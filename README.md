# 🚀 Deploy Orchestrator - Production Ready

> **Modern Environment + Workflow-based Deployment Platform**

[![Production Ready](https://img.shields.io/badge/Status-Production%20Ready-green.svg)](docs/PRODUCTION_DEPLOYMENT.md)
[![Architecture](https://img.shields.io/badge/Architecture-Environment%20%2B%20Workflow-blue.svg)](docs/ENVIRONMENT_WORKFLOW_ARCHITECTURE.md)
[![Security](https://img.shields.io/badge/Security-Enterprise%20Grade-red.svg)](docs/SECURITY.md)

A **production-ready**, enterprise-grade deployment orchestration platform that replaces traditional deployment objects with **environment configurations** and **workflow executions**, providing real-time monitoring, version tracking, and extensible multi-cloud support.

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://github.com/deploy-orchestrator/deploy-orchestrator)
[![Go Version](https://img.shields.io/badge/go-1.21+-blue)](https://golang.org/)
[![Angular Version](https://img.shields.io/badge/angular-17+-red)](https://angular.io/)
[![License](https://img.shields.io/badge/license-MIT-green)](LICENSE)

## ✅ **Production Status**

**Deploy Orchestrator is now PRODUCTION READY!** 🚀

- ✅ **Deployment Service Removed** - Fully migrated to Environment + Workflow architecture
- ✅ **Environment Service** - Production-ready with shared module integration
- ✅ **Workflow Service** - Real-time execution with provider-specific deployment logic
- ✅ **Secrets Service** - Encrypted credential management
- ✅ **Frontend Integration** - Complete Angular UI with environment-workflow components
- ✅ **Docker Compose** - Updated for production deployment
- ✅ **Documentation** - Comprehensive production deployment guide

**Ready to deploy to any environment, anywhere!** 🌍

## ✨ Key Features

- 🌍 **Multi-Cloud Environments** - Deploy to GKE, AKS, EKS, VMs, Serverless, and more
- ⚡ **Real-time Monitoring** - Live logs with automatic secret filtering
- 🔄 **Version Tracking** - Complete deployment lineage with Git integration
- 🔌 **Extensible Providers** - Plugin-based system for easy platform addition
- 🛡️ **Security First** - Automatic secret masking and credential encryption
- 📊 **Workflow Execution** - Step-by-step deployment tracking with retry/skip
- 🎯 **Environment Matrix** - Track versions across all environments
- 📡 **WebSocket Streaming** - Real-time log updates and progress monitoring

## 🏗️ Architecture

The system consists of several microservices with an extensible provider system:

- **Environment Service**: Manages deployment environments and provider configurations
- **Workflow Service**: Handles workflow execution with real-time logging
- **Secrets Service**: Secure secrets management with project-scoped access
- **Provider Registry**: Plugin-based system for deployment targets
- **Real-time Logging**: WebSocket-based log streaming with secret filtering

## 🚀 Quick Start

### **Prerequisites**
- Go 1.21+
- Node.js 18+
- PostgreSQL 14+

### **1. Clone Repository**
```bash
git clone https://github.com/deploy-orchestrator/deploy-orchestrator.git
cd deploy-orchestrator
```

### **2. Start Backend Services**
```bash
# Environment Service
cd backend/environment-service
go run main.go &

# Workflow Service
cd backend/workflow-service
go run main.go &

# Secrets Service
cd backend/secrets-service
go run main.go &
```

### **3. Start Frontend**
```bash
cd frontend/deploy-orchestrator
npm install
npm start
```

### **4. Access Application**
Open http://localhost:4200 and start creating environments!

## 🌍 Supported Providers

| Category | Providers | Status |
|----------|-----------|--------|
| **Kubernetes** | GKE, AKS, EKS, OpenShift, K3s, MicroK8s | ✅ Ready |
| **Cloud VMs** | GCE, EC2, Azure VM, DigitalOcean, Linode | 🔄 Interface Ready |
| **Serverless** | Lambda, Cloud Functions, Azure Functions | 🔄 Interface Ready |
| **Container** | Docker Swarm, Nomad, Mesos | 🔄 Interface Ready |
| **Edge** | Cloudflare Workers, AWS Wavelength | 🔄 Interface Ready |
| **CI/CD** | GitHub Actions, GitLab CI, Jenkins | 🔄 Interface Ready |

## 🔌 Adding New Providers

Adding support for a new platform is incredibly easy:

```go
// 1. Create provider implementation
type MyCloudProvider struct{}

func (p *MyCloudProvider) GetInfo() providers.ProviderInfo {
    return providers.ProviderInfo{
        Type: "mycloud",
        Name: "My Cloud Platform",
        ConfigFields: []providers.ConfigField{
            {
                Name: "apiKey",
                Type: "password",
                Label: "API Key",
                Required: true,
                Sensitive: true,
            },
        },
    }
}

// 2. Register provider
func init() {
    providers.RegisterFactory("mycloud", func() providers.Provider {
        return &MyCloudProvider{}
    })
}
```

**That's it!** Your provider will automatically appear in the UI with a generated configuration form.

## 📡 Real-time Features

### **Live Logging**
- WebSocket-based log streaming
- Automatic secret filtering
- Step-by-step progress tracking
- Export functionality

### **Secret Filtering**
Automatically masks sensitive data:
- `password=secret123` → `password=***REDACTED***`
- `Bearer abc123` → `Bearer ***REDACTED***`
- `api_key=xyz789` → `api_key=***REDACTED***`

### **Version Tracking**
- Git commit integration
- Build artifact linking
- Environment version matrix
- Complete deployment history

## 🌐 API Examples

### **Create Environment**
```bash
curl -X POST /api/v1/environment-service/environments \
  -H "Content-Type: application/json" \
  -d '{
    "projectId": "proj-123",
    "name": "production",
    "type": "kubernetes",
    "provider": {
      "type": "gke",
      "config": {
        "project": "my-gcp-project",
        "cluster": "prod-cluster",
        "zone": "us-central1-a"
      }
    }
  }'
```

### **Start Workflow Execution**
```bash
curl -X POST /api/v1/workflow-service/executions \
  -H "Content-Type: application/json" \
  -d '{
    "workflowId": "workflow-123",
    "environmentId": "env-456",
    "version": {
      "number": "1.2.3",
      "gitCommit": "abc123"
    }
  }'
```

### **Real-time Logs**
```javascript
const socket = io('/workflow-logs');
socket.emit('subscribe-logs', { executionId: 'exec-789' });
socket.on('log-entry', (log) => console.log(log));
```

## 📚 Documentation

- **[Architecture Guide](docs/ARCHITECTURE.md)** - Complete system architecture
- **[Quick Start Guide](docs/QUICK_START.md)** - Get up and running in 15 minutes
- **[Provider Development](docs/providers/DEVELOPMENT.md)** - Create new providers

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Ready to revolutionize your deployments?** [Get started now!](docs/QUICK_START.md) 🚀
