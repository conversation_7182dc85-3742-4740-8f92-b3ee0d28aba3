// Debug script to help identify token malformation issues
// Run this in the browser console to check token storage and format

console.log('🔍 Token Debug Analysis');
console.log('======================');

// Check all possible token storage locations
const tokenSources = {
  'localStorage.accessToken': localStorage.getItem('accessToken'),
  'localStorage.token': localStorage.getItem('token'),
  'localStorage.refreshToken': localStorage.getItem('refreshToken'),
  'currentUser.token': (() => {
    try {
      const user = JSON.parse(localStorage.getItem('currentUser') || '{}');
      return user.token || null;
    } catch (e) {
      return 'ERROR: ' + e.message;
    }
  })()
};

console.log('📦 Token Storage Locations:');
Object.entries(tokenSources).forEach(([key, value]) => {
  if (value) {
    console.log(`✅ ${key}:`, value.substring(0, 50) + '...');
    
    // Check if it's a valid JWT format (3 parts separated by dots)
    const parts = value.split('.');
    if (parts.length === 3) {
      console.log(`   ✅ Valid JWT format (${parts.length} segments)`);
      
      // Try to decode the header and payload (without verification)
      try {
        const header = JSON.parse(atob(parts[0]));
        const payload = JSON.parse(atob(parts[1]));
        console.log(`   📋 Header:`, header);
        console.log(`   📋 Payload:`, payload);
        
        // Check expiration
        if (payload.exp) {
          const expDate = new Date(payload.exp * 1000);
          const now = new Date();
          console.log(`   ⏰ Expires:`, expDate);
          console.log(`   ⏰ Current:`, now);
          console.log(`   ${expDate > now ? '✅' : '❌'} Token ${expDate > now ? 'valid' : 'EXPIRED'}`);
        }
      } catch (e) {
        console.log(`   ❌ Error decoding JWT:`, e.message);
      }
    } else {
      console.log(`   ❌ Invalid JWT format (${parts.length} segments, expected 3)`);
      console.log(`   🔍 Full value:`, value);
    }
  } else {
    console.log(`❌ ${key}: null/empty`);
  }
});

// Check current user object
console.log('\n👤 Current User Object:');
try {
  const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
  console.log('User data:', currentUser);
} catch (e) {
  console.log('❌ Error parsing currentUser:', e.message);
}

// Check what the secrets service would use
console.log('\n🔐 Secrets Service Token Resolution:');
const secretsToken = localStorage.getItem('accessToken') || 
                    localStorage.getItem('token') || 
                    (() => {
                      try {
                        const user = JSON.parse(localStorage.getItem('currentUser') || '{}');
                        return user.token || null;
                      } catch (e) {
                        return null;
                      }
                    })();

if (secretsToken) {
  console.log('✅ Secrets service would use:', secretsToken.substring(0, 50) + '...');
  const parts = secretsToken.split('.');
  console.log(`Token segments: ${parts.length} (${parts.length === 3 ? 'VALID' : 'INVALID'})`);
} else {
  console.log('❌ Secrets service would find no token');
}

// Recommendations
console.log('\n💡 Recommendations:');
if (!secretsToken) {
  console.log('1. ❌ No token found - user needs to log in');
} else if (secretsToken.split('.').length !== 3) {
  console.log('1. ❌ Token is malformed - clear localStorage and re-login');
  console.log('   Run: localStorage.clear(); then login again');
} else {
  console.log('1. ✅ Token format looks correct');
  console.log('2. 🔍 Check network tab for actual request headers');
  console.log('3. 🔍 Check backend logs for token verification details');
}

console.log('\n🛠️ Quick Fixes:');
console.log('- Clear all tokens: localStorage.removeItem("accessToken"); localStorage.removeItem("token"); localStorage.removeItem("refreshToken");');
console.log('- Clear user data: localStorage.removeItem("currentUser");');
console.log('- Clear everything: localStorage.clear();');
