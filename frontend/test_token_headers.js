// Test script to verify token headers are being sent correctly
// Run this in the browser console after the fix

console.log('🔍 Testing Token Headers for Secrets Service');
console.log('=============================================');

// Override fetch to intercept all requests
const originalFetch = window.fetch;
let requestCount = 0;

window.fetch = function(...args) {
  requestCount++;
  const url = args[0];
  const options = args[1] || {};
  
  console.log(`\n📡 Request #${requestCount}:`);
  console.log('URL:', url);
  
  // Check if thi<s is a secrets-service request
  if (url.includes('/api/secrets-service/')) {
    console.log('🔐 This is a SECRETS-SERVICE request');
    
    // Check headers
    if (options.headers) {
      console.log('📋 Request headers:', options.headers);
      
      // Check for Authorization header
      const authHeader = options.headers['Authorization'] || options.headers['authorization'];
      if (authHeader) {
        console.log('✅ Authorization header found:', authHeader.substring(0, 30) + '...');
        
        // Validate JWT format
        const token = authHeader.replace('Bearer ', '');
        const segments = token.split('.');
        console.log(`🔑 Token segments: ${segments.length} (${segments.length === 3 ? 'VALID' : 'INVALID'})`);
      } else {
        console.log('❌ NO Authorization header found!');
        console.log('📋 Available headers:', Object.keys(options.headers));
      }
    } else {
      console.log('❌ NO headers object found!');
    }
  } else {
    console.log('ℹ️  Non-secrets request');
  }
  
  return originalFetch.apply(this, args);
};

// Test function to make a secrets request
window.testSecretsRequest = function() {
  console.log('\n🧪 Making test request to secrets service...');
  
  // Check if user is logged in
  const token = localStorage.getItem('accessToken');
  if (!token) {
    console.log('❌ No token found - please login first');
    return;
  }
  
  console.log('✅ Token found in localStorage:', token.substring(0, 30) + '...');
  
  // Make a test request
  fetch('/api/secrets-service/v1/projects')
    .then(response => {
      console.log('\n📥 Response received:');
      console.log('Status:', response.status);
      console.log('Status Text:', response.statusText);
      
      if (response.status === 200) {
        console.log('✅ SUCCESS: Request completed successfully!');
      } else if (response.status === 401) {
        console.log('❌ UNAUTHORIZED: Token issue detected');
        return response.json().then(data => {
          console.log('Error details:', data);
        });
      } else {
        console.log('⚠️  Unexpected status:', response.status);
      }
    })
    .catch(error => {
      console.log('❌ Request failed:', error);
    });
};

// Instructions
console.log('\n📋 Instructions:');
console.log('1. Make sure you are logged in');
console.log('2. Navigate to a page that calls secrets service');
console.log('3. Watch the console for request details');
console.log('4. Or run: testSecretsRequest()');

console.log('\n🔧 Quick Tests:');
console.log('- Check token: localStorage.getItem("accessToken")');
console.log('- Test request: testSecretsRequest()');
console.log('- Reset interceptor: location.reload()');

// Check current authentication status
const currentToken = localStorage.getItem('accessToken');
const currentUser = localStorage.getItem('currentUser');

console.log('\n📊 Current Status:');
console.log('Token exists:', !!currentToken);
console.log('User exists:', !!currentUser);

if (currentToken) {
  const segments = currentToken.split('.');
  console.log('Token format:', segments.length === 3 ? 'VALID' : 'INVALID');
}

console.log('\n✅ Token header testing is now active!');
console.log('All requests will be logged to the console.');
