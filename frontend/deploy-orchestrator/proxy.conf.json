{"/api/admin": {"target": "http://localhost:8086", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/auth": {"target": "http://localhost:8086", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/applications": {"target": "http://localhost:8089", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/application-groups": {"target": "http://localhost:8089", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/application-service": {"target": "http://localhost:8089", "secure": false, "changeOrigin": true, "logLevel": "debug", "pathRewrite": {"^/api/v1/application-service": "/api/v1"}}, "/api/v1/deployables": {"target": "http://localhost:8088", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/environment-service": {"target": "http://localhost:8088", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/deployments": {"target": "http://localhost:8080", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/workflow-service": {"target": "http://localhost:8085", "secure": false, "changeOrigin": true, "logLevel": "debug", "pathRewrite": {"^/api/v1/workflow-service": "/api/v1"}}, "/api/v1/secrets-service": {"target": "http://localhost:8087", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/auth/check-permission": {"target": "http://localhost:8086", "secure": false, "changeOrigin": true, "logLevel": "debug", "pathRewrite": {"^/api/v1/auth/check-permission": "/api/v1/admin-service/permissions/check"}}, "/api/v1/projects": {"target": "http://localhost:8086", "secure": false, "changeOrigin": true, "logLevel": "debug", "pathRewrite": {"^/api/v1/projects": "/api/v1/admin-service/projects"}}, "/api/v1/schedules": {"target": "http://localhost:8081", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/notifications": {"target": "http://localhost:8082", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/workflows": {"target": "http://localhost:8085", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/projects/*/version-matrix": {"target": "http://localhost:8080", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/promotions": {"target": "http://localhost:8080", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/rollbacks": {"target": "http://localhost:8080", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/environments/*/deployment-history": {"target": "http://localhost:8080", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/versions": {"target": "http://localhost:8080", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/marketplace": {"target": "http://localhost:8085", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/plugins/hot-reload/events": {"target": "http://localhost:8085", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/executions": {"target": "http://localhost:8085", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/templates": {"target": "http://localhost:8085", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/instances": {"target": "http://localhost:8085", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/monitoring": {"target": "http://localhost:8085", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/audit": {"target": "http://localhost:8085", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/integrations": {"target": "http://localhost:8088", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/audit-logs": {"target": "http://localhost:8084", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/identity-providers": {"target": "http://localhost:8086", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/group-projects": {"target": "http://localhost:8086", "secure": false, "changeOrigin": true, "logLevel": "debug", "pathRewrite": {"^/api/v1/group-projects": "/api/v1/admin-service/group-projects"}}, "/api/v1/admin-service": {"target": "http://localhost:8086", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api/v1/users": {"target": "http://localhost:8086", "secure": false, "changeOrigin": true, "logLevel": "debug", "pathRewrite": {"^/api/v1/users": "/api/v1/admin-service/users"}}, "/api/v1/roles": {"target": "http://localhost:8086", "secure": false, "changeOrigin": true, "logLevel": "debug", "pathRewrite": {"^/api/v1/roles": "/api/v1/admin-service/roles"}}, "/api/v1/groups": {"target": "http://localhost:8086", "secure": false, "changeOrigin": true, "logLevel": "debug", "pathRewrite": {"^/api/v1/groups": "/api/v1/admin-service/groups"}}, "/api/v1/permissions": {"target": "http://localhost:8086", "secure": false, "changeOrigin": true, "logLevel": "debug", "pathRewrite": {"^/api/v1/permissions": "/api/v1/admin-service/permissions"}}, "/api/v1/deployment-plugins": {"target": "http://localhost:8085", "secure": false, "changeOrigin": true, "logLevel": "debug", "pathRewrite": {"^/api/v1/deployment-plugins": "/api/v1/deployment-plugins"}}, "/api/v1/plugins": {"target": "http://localhost:8085", "secure": false, "changeOrigin": true, "logLevel": "debug", "pathRewrite": {"^/api/v1/plugins": "/api/v1/plugins"}}, "/api/v1/providers": {"target": "http://localhost:8085", "secure": false, "changeOrigin": true, "logLevel": "debug", "pathRewrite": {"^/api/v1/providers": "/api/v1/providers"}}, "/api/v1": {"target": "http://localhost:8086", "secure": false, "changeOrigin": true, "logLevel": "debug"}, "/api": {"target": "http://localhost:8086", "secure": false, "changeOrigin": true, "logLevel": "debug"}}