<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Environment Config Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold text-center mb-8">Environment Configuration Test</h1>
        
        <!-- Test Icons -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Provider Icons Test</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                
                <!-- Kubernetes -->
                <div class="text-center p-4 border rounded-lg">
                    <div class="w-12 h-12 mx-auto mb-2 text-blue-600">
                        <svg class="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
                        </svg>
                    </div>
                    <p class="text-sm font-medium">Kubernetes</p>
                </div>
                
                <!-- OpenShift -->
                <div class="text-center p-4 border rounded-lg">
                    <div class="w-12 h-12 mx-auto mb-2 text-blue-600">
                        <svg class="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                        </svg>
                    </div>
                    <p class="text-sm font-medium">OpenShift</p>
                </div>
                
                <!-- Docker -->
                <div class="text-center p-4 border rounded-lg">
                    <div class="w-12 h-12 mx-auto mb-2 text-blue-600">
                        <svg class="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M13.983 11.078h2.119a.186.186 0 00.186-.185V9.006a.186.186 0 00-.186-.186h-2.119a.185.185 0 00-.185.185v1.888c0 .102.083.185.185.185m-2.954-5.43h2.118a.186.186 0 00.186-.186V3.574a.186.186 0 00-.186-.185h-2.118a.185.185 0 00-.185.185v1.888c0 .102.082.185.185.186"/>
                        </svg>
                    </div>
                    <p class="text-sm font-medium">Docker</p>
                </div>
                
                <!-- AWS -->
                <div class="text-center p-4 border rounded-lg">
                    <div class="w-12 h-12 mx-auto mb-2 text-blue-600">
                        <svg class="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M6.763 10.036c0 .296.032.535.088.71.064.176.144.368.256.576.04.063.056.127.056.183 0 .08-.048.16-.152.24l-.503.335c-.072.048-.144.072-.2.072-.08 0-.16-.04-.239-.112a2.417 2.417 0 01-.287-.375 6.18 6.18 0 01-.248-.471c-.622.734-1.405 1.101-2.347 1.101-.67 0-1.205-.191-1.596-.574-.391-.383-.591-.894-.591-1.533 0-.678.239-1.23.726-1.644.487-.415 1.133-.623 1.955-.623.27 0 .551.024.846.064.296.04.6.104.918.176v-.583c0-.607-.127-1.030-.375-1.277-.255-.248-.686-.367-1.3-.367-.28 0-.568.032-.863.104-.296.064-.583.16-.863.28-.128.063-.224.104-.279.128-.056.024-.096.04-.128.04-.112 0-.168-.08-.168-.248v-.391c0-.128.016-.224.056-.28.04-.064.112-.128.207-.184.28-.144.615-.264 1.005-.36.391-.096.807-.144 1.246-.144.95 0 1.644.216 2.091.647.439.432.663 1.085.663 1.963v2.586zm-3.24 1.214c.263 0 .535-.048.822-.144.287-.096.543-.271.758-.503.128-.144.224-.304.272-.48.047-.175.08-.384.08-.615v-.295a6.637 6.637 0 00-.735-.136 6.188 6.188 0 00-.751-.048c-.535 0-.926.104-1.19.32-.263.215-.391.518-.391.917 0 .375.095.655.287.846.191.2.47.296.848.296z"/>
                        </svg>
                    </div>
                    <p class="text-sm font-medium">AWS</p>
                </div>
            </div>
        </div>
        
        <!-- Test Form Components -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Form Components Test</h2>
            
            <!-- Text Input -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Environment Name</label>
                <input
                    type="text"
                    placeholder="e.g., production, staging, development"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                >
            </div>
            
            <!-- Select Dropdown -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Authentication Method</label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                    <option value="">Select Authentication Method</option>
                    <option value="token">Service Account Token</option>
                    <option value="kubeconfig">Kubeconfig File</option>
                    <option value="certificate">Client Certificate</option>
                </select>
            </div>
            
            <!-- Toggle Switch -->
            <div class="mb-4 flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div class="flex-1">
                    <label class="text-sm font-medium text-gray-700">TLS Enabled</label>
                    <p class="text-sm text-gray-600">Enable TLS for secure communication</p>
                </div>
                <div class="relative">
                    <input type="checkbox" class="sr-only" id="toggle-test">
                    <label for="toggle-test" class="flex items-center cursor-pointer">
                        <div class="relative">
                            <div class="block bg-gray-300 w-10 h-6 rounded-full transition-colors"></div>
                            <div class="absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform"></div>
                        </div>
                    </label>
                </div>
            </div>
            
            <!-- Buttons -->
            <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                <button type="button" class="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Back</span>
                </button>
                <button type="button" class="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    <span>Next</span>
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <div class="text-center text-gray-600">
            <p>All components are styled with Tailwind CSS and should work without Angular Material.</p>
        </div>
    </div>
    
    <script>
        // Simple toggle functionality for testing
        document.getElementById('toggle-test').addEventListener('change', function() {
            const toggle = this.nextElementSibling.querySelector('div div:first-child');
            const slider = this.nextElementSibling.querySelector('div div:last-child');
            
            if (this.checked) {
                toggle.classList.add('bg-blue-500');
                toggle.classList.remove('bg-gray-300');
                slider.classList.add('translate-x-4');
            } else {
                toggle.classList.remove('bg-blue-500');
                toggle.classList.add('bg-gray-300');
                slider.classList.remove('translate-x-4');
            }
        });
    </script>
</body>
</html>
