/* You can add global styles to this file, and also import other style files */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
body {
  background-color: rgb(249, 250, 251);
  color: rgb(17, 24, 39);
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: background-color 150ms ease-in-out, color 150ms ease-in-out;
}

.btn-primary {
  background-color: rgb(37, 99, 235);
  color: white;
  
  &:hover {
    background-color: rgb(29, 78, 216);
  }
}

.btn-secondary {
  background-color: rgb(229, 231, 235);
  color: rgb(31, 41, 55);
  
  &:hover {
    background-color: rgb(209, 213, 219);
  }
}

.btn-danger {
  background-color: rgb(220, 38, 38);
  color: white;
  
  &:hover {
    background-color: rgb(185, 28, 28);
  }
}

.form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgb(209, 213, 219);
  border-radius: 0.375rem;
  outline: none;
  
  &:focus {
    box-shadow: 0 0 0 2px rgb(59, 130, 246);
  }
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(55, 65, 81);
  margin-bottom: 0.25rem;
}

.form-error {
  font-size: 0.875rem;
  color: rgb(220, 38, 38);
  margin-top: 0.25rem;
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
}

.container {
  max-width: 80rem;
  margin: 0 auto;
  padding: 0 1rem;
  
  @media (min-width: 640px) {
    padding: 0 1.5rem;
  }
  
  @media (min-width: 1024px) {
    padding: 0 2rem;
  }
}

.section {
  padding: 2rem 0;
}
