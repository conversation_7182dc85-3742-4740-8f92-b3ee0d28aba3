import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { CommonModule, DatePipe } from '@angular/common';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { RouterModule } from '@angular/router';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { LoginComponent } from './components/login/login.component';
import { AuthCallbackComponent } from './components/auth-callback/auth-callback.component';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { ProjectSelectorComponent } from './components/project-selector/project-selector.component';
import { NavbarComponent } from './components/navbar/navbar.component';
import { IdpConfigComponent } from './components/idp-config/idp-config.component';
import { OidcConfigComponent } from './components/idp-config/oidc-config/oidc-config.component';
import { SamlConfigComponent } from './components/idp-config/saml-config/saml-config.component';
import { LdapConfigComponent } from './components/idp-config/ldap-config/ldap-config.component';
import { GroupMappingComponent } from './components/group-mapping/group-mapping.component';
import { ProjectsComponent } from './components/projects/projects.component';
import { ProjectFormComponent } from './components/projects/project-form/project-form.component';

import { RolesComponent } from './components/roles/roles.component';

import { SchedulesComponent } from './components/schedules/schedules.component';
import { UsersComponent } from './components/users/users.component';
import { AuditLogsComponent } from './components/audit-logs/audit-logs.component';
import { SettingsComponent } from './components/settings/settings.component';
import { MainLayoutComponent } from './components/layout/main-layout/main-layout.component';
import { SidebarComponent } from './components/layout/sidebar/sidebar.component';
import { AuthInterceptor } from './interceptors/auth.interceptor';
import { ErrorInterceptor } from './interceptors/error.interceptor';
import { ClickOutsideDirective } from './directives/click-outside.directive';
import { WorkflowDesignerComponent } from './components/workflow-designer/workflow-designer.component';
import { WorkflowsComponent } from './components/workflows/workflows.component';
import { StepPaletteComponent } from './components/workflow-designer/step-palette/step-palette.component';
import { StepConfigComponent } from './components/workflow-designer/step-config/step-config.component';
import { TemplateMarketplaceComponent } from './components/template-marketplace/template-marketplace.component';
import { TemplateDetailComponent } from './components/template-detail/template-detail.component';
import { TemplateAnalyticsComponent } from './components/template-analytics/template-analytics.component';
import { WorkflowInstancesComponent } from './components/workflow-instances/workflow-instances.component';
import { ParameterConfigComponent } from './components/parameter-config/parameter-config.component';
import { VariableOverrideComponent } from './components/variable-override/variable-override.component';
import { ExecutionParametersComponent } from './components/execution-parameters/execution-parameters.component';
import { PluginManagementComponent } from './components/plugin-management/plugin-management.component';
import { EnvironmentProviderConfigComponent } from './components/provider-config/provider-config.component';
import { RealTimeLogsComponent } from './components/real-time-logs/real-time-logs.component';
import { EnvironmentConfigComponent } from './components/environment-config/environment-config.component';
import { NotificationComponent } from './components/shared/notification/notification.component';
import { ExecutionMonitoringComponent } from './components/execution-monitoring/execution-monitoring.component';

import { EnvironmentsComponent } from './components/environments/environments.component';
import { ProjectSettingsComponent } from './components/projects/project-settings/project-settings.component';
import { EnvironmentProviderAdminComponent } from './components/environment-provider-config/environment-provider-config.component';
import { SecretProviderAdminComponent } from './components/secret-provider-config/secret-provider-config.component';
import { GroupProjectAssignmentsComponent } from './components/group-project-assignments/group-project-assignments.component';
import { PermissionManagementComponent } from './components/permission-management/permission-management.component';

// New Application-Centric Components
// ApplicationManagementComponent is now standalone
import { ApplicationDetailComponent } from './components/application-detail/application-detail.component';
import { ComponentManagementComponent } from './components/component-management/component-management.component';
import { DeploymentPageComponent } from './components/deployment-page/deployment-page.component';
import { DeploymentsComponent } from './components/deployments/deployments.component';
import { ModalComponent } from './components/shared/modal/modal.component';


// Services
import { PluginPermissionService } from './services/plugin-permission.service';
import { NavigationPermissionService } from './services/navigation-permission.service';
import { WebSocketService } from './services/websocket.service';

// Guards
import { PluginManagementGuard, EnvironmentProviderAccessGuard, TemplateDeployGuard } from './guards/plugin-permission.guard';



@NgModule({
  declarations: [
    AppComponent,
    LoginComponent,
    AuthCallbackComponent,
    DashboardComponent,
    ProjectSelectorComponent,
    NavbarComponent,
    IdpConfigComponent,
    OidcConfigComponent,
    SamlConfigComponent,
    LdapConfigComponent,
    GroupMappingComponent,
    ProjectsComponent,
    ProjectFormComponent,
    RolesComponent,
    SchedulesComponent,
    UsersComponent,
    AuditLogsComponent,
    SettingsComponent,
    MainLayoutComponent,
    SidebarComponent,
    ClickOutsideDirective,
    WorkflowDesignerComponent,
    WorkflowsComponent,
    StepPaletteComponent,
    StepConfigComponent,
    TemplateMarketplaceComponent,
    TemplateDetailComponent,
    TemplateAnalyticsComponent,
    WorkflowInstancesComponent,
    ParameterConfigComponent,
    VariableOverrideComponent,
    ExecutionParametersComponent,
    PluginManagementComponent,
    EnvironmentProviderConfigComponent,
    RealTimeLogsComponent
  ],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    CommonModule,
    AppRoutingModule,
    HttpClientModule,
    FormsModule,
    ReactiveFormsModule,
    DragDropModule,
    RouterModule,
    // Import standalone components
    EnvironmentConfigComponent,
    NotificationComponent,
    ExecutionMonitoringComponent,

    EnvironmentsComponent,
    ProjectSettingsComponent,
    EnvironmentProviderAdminComponent,
    SecretProviderAdminComponent,
    GroupProjectAssignmentsComponent,
    PermissionManagementComponent,
    // ApplicationManagementComponent is now standalone
    ApplicationDetailComponent,
    ComponentManagementComponent,
    DeploymentPageComponent,
    DeploymentsComponent,
    ModalComponent
  ],
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },
    DatePipe,
    // Plugin Services
    PluginPermissionService,
    NavigationPermissionService,
    WebSocketService,
    // Plugin Guards
    PluginManagementGuard,
    EnvironmentProviderAccessGuard,
    TemplateDeployGuard
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }