/**
 * Test file to verify plugin configuration mapping functionality
 * This tests the conversion from plugin manifest schema to frontend config fields
 */

import { DeploymentPlugin, PluginConfigSchema, PluginPropertySchema } from '../models/deployable.model';

// Mock plugin with new configuration structure (based on plugin.yaml)
const mockPluginWithNewStructure: DeploymentPlugin = {
  id: 'helm-openshift-deploy',
  name: 'Helm OpenShift Deploy',
  version: '1.0.0',
  description: 'Helm deployment plugin for OpenShift clusters with Bitbucket integration',
  type: 'helm',
  supportedArtifacts: [],
  isEnabled: true,
  configuration: {
    schema: {
      type: 'object',
      required: ['openshift_api_url', 'openshift_project', 'username', 'password', 'bitbucket_repo_url', 'chart_path', 'values_path'],
      properties: {
        openshift_api_url: {
          type: 'string',
          title: 'OpenShift API URL',
          description: 'OpenShift cluster API endpoint',
          pattern: '^https?://.*',
          examples: ['https://api.cluster.example.com:6443', 'https://openshift.company.com:8443']
        },
        openshift_project: {
          type: 'string',
          title: 'OpenShift Project',
          description: 'Target OpenShift project/namespace',
          pattern: '^[a-z0-9]([-a-z0-9]*[a-z0-9])?$',
          examples: ['my-app-dev', 'production-workloads']
        },
        username: {
          type: 'string',
          title: 'OpenShift Username',
          description: 'Username for OpenShift authentication',
          examples: ['developer', 'service-account']
        },
        password: {
          type: 'password',
          title: 'OpenShift Password',
          description: 'Password for OpenShift authentication',
          sensitive: true,
          examples: ['secret123']
        },
        release_name: {
          type: 'string',
          title: 'Helm Release Name',
          description: 'Name for the Helm release (optional, defaults to chart name)',
          pattern: '^[a-z0-9]([-a-z0-9]*[a-z0-9])?$',
          examples: ['my-application', 'web-frontend']
        },
        helm_timeout: {
          type: 'string',
          title: 'Helm Timeout',
          description: 'Timeout for Helm operations',
          default: '300s',
          pattern: '^[0-9]+(s|m|h)$',
          examples: ['300s', '5m', '1h']
        },
        bitbucket_repo_url: {
          type: 'string',
          title: 'Bitbucket Repository URL',
          description: 'URL of the Bitbucket repository containing Helm charts',
          pattern: '^https://.*\\.git$',
          examples: ['https://bitbucket.org/myorg/helm-charts.git', 'https://company.bitbucket.org/projects/HELM/repos/charts.git']
        },
        chart_path: {
          type: 'string',
          title: 'Chart Path',
          description: 'Path to Helm chart from repository root',
          examples: ['charts/my-application', 'helm/web-frontend', 'deployments/microservice']
        },
        values_path: {
          type: 'string',
          title: 'Values File Path',
          description: 'Path to values file for environment-specific configuration',
          examples: ['values-dev.yaml', 'values-qa.yaml', 'values-prod.yaml', 'environments/dev/values.yaml']
        }
      }
    }
  }
};

// Mock plugin with legacy structure
const mockPluginWithLegacyStructure: DeploymentPlugin = {
  id: 'legacy-plugin',
  name: 'Legacy Plugin',
  version: '1.0.0',
  description: 'A legacy plugin with old configuration schema',
  type: 'generic',
  supportedArtifacts: [],
  isEnabled: true,
  configurationSchema: [
    {
      key: 'legacy_field',
      label: 'Legacy Field',
      type: 'string',
      required: true,
      description: 'A legacy configuration field'
    },
    {
      key: 'legacy_number',
      label: 'Legacy Number',
      type: 'number',
      required: false,
      defaultValue: 42
    }
  ]
};

// Plugin configuration conversion utility functions
// (These should match the ones in step-config.component.ts)
function convertPluginSchemaToConfigFields(schema: any): PluginConfigSchema[] {
  const fields: PluginConfigSchema[] = [];
  const properties = schema.properties || {};
  const required = schema.required || [];

  for (const [key, property] of Object.entries(properties)) {
    const prop = property as any;
    const field: PluginConfigSchema = {
      key,
      label: prop.title || formatLabel(key),
      type: mapPluginTypeToConfigType(prop.type),
      required: required.includes(key),
      description: prop.description,
      defaultValue: prop.default
    };

    // Handle validation patterns
    if (prop.pattern) {
      field.validation = {
        pattern: prop.pattern
      };
    }

    // Handle numeric constraints
    if (prop.minimum !== undefined || prop.maximum !== undefined) {
      if (!field.validation) field.validation = {};
      field.validation.min = prop.minimum;
      field.validation.max = prop.maximum;
    }

    // Handle enum/select options
    if (prop.enum) {
      field.type = 'select';
      field.options = prop.enum.map((value: string) => ({
        label: formatLabel(value),
        value
      }));
    }

    // Handle examples as placeholder
    if (prop.examples && prop.examples.length > 0) {
      field.description = field.description || '';
      if (field.description) field.description += '\n';
      field.description += `Example: ${prop.examples[0]}`;
    }

    fields.push(field);
  }

  return fields;
}

function mapPluginTypeToConfigType(pluginType: string): PluginConfigSchema['type'] {
  switch (pluginType) {
    case 'password':
      return 'password';
    case 'integer':
    case 'number':
      return 'number';
    case 'boolean':
      return 'boolean';
    case 'string':
    default:
      return 'string';
  }
}

function formatLabel(key: string): string {
  return key
    .replace(/[_-]/g, ' ')
    .replace(/\b\w/g, char => char.toUpperCase());
}

function getPluginConfigFields(plugin: DeploymentPlugin): PluginConfigSchema[] {
  // Check if plugin has the new configuration schema structure
  if (plugin?.configuration?.schema?.properties) {
    return convertPluginSchemaToConfigFields(plugin.configuration.schema);
  }
  // Fallback to legacy configurationSchema if available
  return plugin?.configurationSchema || [];
}

// Test functions
function testNewPluginStructureConversion() {
  console.log('=== Testing New Plugin Structure Conversion ===');
  
  const configFields = getPluginConfigFields(mockPluginWithNewStructure);
  
  console.log(`Converted ${configFields.length} configuration fields:`);
  configFields.forEach((field, index) => {
    console.log(`${index + 1}. ${field.label} (${field.key})`);
    console.log(`   Type: ${field.type}, Required: ${field.required}`);
    if (field.description) {
      console.log(`   Description: ${field.description}`);
    }
    if (field.validation) {
      console.log(`   Validation: ${JSON.stringify(field.validation)}`);
    }
    if (field.defaultValue !== undefined) {
      console.log(`   Default: ${field.defaultValue}`);
    }
    console.log('');
  });

  // Verify expected fields are present
  const expectedFields = ['openshift_api_url', 'openshift_project', 'username', 'password', 'bitbucket_repo_url', 'chart_path', 'values_path'];
  const actualFields = configFields.map(f => f.key);
  
  const missingFields = expectedFields.filter(field => !actualFields.includes(field));
  if (missingFields.length > 0) {
    console.error(`❌ Missing fields: ${missingFields.join(', ')}`);
  } else {
    console.log('✅ All expected fields are present');
  }

  // Verify password field type mapping
  const passwordField = configFields.find(f => f.key === 'password');
  if (passwordField && passwordField.type === 'password') {
    console.log('✅ Password field type correctly mapped');
  } else {
    console.error('❌ Password field type mapping failed');
  }

  // Verify required field detection
  const requiredFields = configFields.filter(f => f.required).map(f => f.key);
  const expectedRequiredFields = ['openshift_api_url', 'openshift_project', 'username', 'password', 'bitbucket_repo_url', 'chart_path', 'values_path'];
  
  if (JSON.stringify(requiredFields.sort()) === JSON.stringify(expectedRequiredFields.sort())) {
    console.log('✅ Required fields correctly identified');
  } else {
    console.error(`❌ Required fields mismatch. Expected: ${expectedRequiredFields.join(', ')}, Got: ${requiredFields.join(', ')}`);
  }
}

function testLegacyPluginStructureCompatibility() {
  console.log('=== Testing Legacy Plugin Structure Compatibility ===');
  
  const configFields = getPluginConfigFields(mockPluginWithLegacyStructure);
  
  console.log(`Legacy plugin has ${configFields.length} configuration fields:`);
  configFields.forEach((field, index) => {
    console.log(`${index + 1}. ${field.label} (${field.key})`);
    console.log(`   Type: ${field.type}, Required: ${field.required}`);
    if (field.description) {
      console.log(`   Description: ${field.description}`);
    }
    if (field.defaultValue !== undefined) {
      console.log(`   Default: ${field.defaultValue}`);
    }
    console.log('');
  });

  // Verify legacy fields are preserved
  if (configFields.length === 2 && 
      configFields.some(f => f.key === 'legacy_field') && 
      configFields.some(f => f.key === 'legacy_number')) {
    console.log('✅ Legacy plugin structure preserved');
  } else {
    console.error('❌ Legacy plugin structure not preserved');
  }
}

function testEmptyPluginHandling() {
  console.log('=== Testing Empty Plugin Handling ===');
  
  const emptyPlugin: DeploymentPlugin = {
    id: 'empty-plugin',
    name: 'Empty Plugin',
    version: '1.0.0',
    description: 'A plugin with no configuration',
    type: 'generic',
    supportedArtifacts: [],
    isEnabled: true
  };
  
  const configFields = getPluginConfigFields(emptyPlugin);
  
  if (configFields.length === 0) {
    console.log('✅ Empty plugin handled correctly');
  } else {
    console.error(`❌ Empty plugin should return no fields, got ${configFields.length}`);
  }
}

// Run all tests
function runAllTests() {
  console.log('Plugin Configuration Mapping Tests');
  console.log('==================================\n');
  
  testNewPluginStructureConversion();
  console.log('\n');
  testLegacyPluginStructureCompatibility();
  console.log('\n');
  testEmptyPluginHandling();
  
  console.log('\n✅ All tests completed!');
}

// Export for testing
export {
  mockPluginWithNewStructure,
  mockPluginWithLegacyStructure,
  convertPluginSchemaToConfigFields,
  getPluginConfigFields,
  runAllTests
};

// Run tests if this file is executed directly
if (typeof window !== 'undefined') {
  (window as any).runPluginConfigTests = runAllTests;
  runAllTests();
}
