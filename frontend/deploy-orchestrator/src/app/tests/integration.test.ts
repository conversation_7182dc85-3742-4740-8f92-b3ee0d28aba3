/**
 * Integration test for workflow provider and plugin configuration mapping
 * This test verifies the complete workflow of loading providers and plugins
 * and mapping their configurations correctly.
 */

import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { WorkflowProviderService } from '../services/workflow-provider.service';
import { DeploymentPluginService } from '../services/deployment-plugin.service';
import { environment } from '../../environments/environment';

describe('Workflow Provider and Plugin Integration', () => {
  let workflowProviderService: WorkflowProviderService;
  let pluginService: DeploymentPluginService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        WorkflowProviderService,
        DeploymentPluginService
      ]
    });

    workflowProviderService = TestBed.inject(WorkflowProviderService);
    pluginService = TestBed.inject(DeploymentPluginService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  describe('Workflow Provider Service', () => {
    it('should fetch workflow providers successfully', () => {
      const mockResponse = {
        providers: [
          {
            type: 'kubernetes',
            name: 'Kubernetes Provider',
            description: 'Kubernetes deployment provider',
            capabilities: ['deploy', 'scale', 'monitor'],
            configFields: [
              {
                name: 'cluster_url',
                label: 'Cluster URL',
                type: 'string',
                required: true,
                placeholder: 'https://kubernetes.example.com'
              },
              {
                name: 'namespace',
                label: 'Namespace',
                type: 'string',
                required: true,
                placeholder: 'default'
              }
            ]
          },
          {
            type: 'docker',
            name: 'Docker Provider',
            description: 'Docker container provider',
            capabilities: ['deploy', 'monitor'],
            configFields: [
              {
                name: 'registry_url',
                label: 'Registry URL',
                type: 'string',
                required: true,
                placeholder: 'https://registry.hub.docker.com'
              }
            ]
          }
        ]
      };

      workflowProviderService.getProviders().subscribe(response => {
        expect(response.providers).toBeDefined();
        expect(response.providers.length).toBe(2);
        expect(response.providers[0].type).toBe('kubernetes');
        expect(response.providers[0].configFields.length).toBe(2);
        expect(response.providers[1].type).toBe('docker');
        expect(response.providers[1].configFields.length).toBe(1);
      });

      const req = httpMock.expectOne(`${environment.workflowServiceUrl}/api/v1/providers`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should fetch specific workflow provider successfully', () => {
      const mockProvider = {
        type: 'kubernetes',
        name: 'Kubernetes Provider',
        description: 'Kubernetes deployment provider',
        capabilities: ['deploy', 'scale', 'monitor'],
        configFields: [
          {
            name: 'cluster_url',
            label: 'Cluster URL',
            type: 'string',
            required: true,
            placeholder: 'https://kubernetes.example.com'
          }
        ]
      };

      workflowProviderService.getProvider('kubernetes').subscribe(provider => {
        expect(provider.type).toBe('kubernetes');
        expect(provider.configFields.length).toBe(1);
      });

      const req = httpMock.expectOne(`${environment.workflowServiceUrl}/api/v1/providers/kubernetes`);
      expect(req.request.method).toBe('GET');
      req.flush(mockProvider);
    });
  });

  describe('Plugin Configuration Mapping', () => {
    it('should handle plugins with new configuration structure', () => {
      const mockPluginWithNewStructure = {
        id: 'helm-openshift-deploy',
        name: 'Helm OpenShift Deploy',
        version: '1.0.0',
        description: 'Helm deployment plugin for OpenShift clusters',
        type: 'helm',
        supportedArtifacts: [],
        isEnabled: true,
        configuration: {
          schema: {
            type: 'object',
            required: ['openshift_api_url', 'namespace'],
            properties: {
              openshift_api_url: {
                type: 'string',
                title: 'OpenShift API URL',
                description: 'OpenShift cluster API endpoint',
                pattern: '^https?://.*',
                examples: ['https://api.cluster.example.com:6443']
              },
              namespace: {
                type: 'string',
                title: 'Namespace',
                description: 'Target namespace',
                pattern: '^[a-z0-9]([-a-z0-9]*[a-z0-9])?$',
                examples: ['my-app-dev']
              },
              timeout: {
                type: 'string',
                title: 'Timeout',
                description: 'Operation timeout',
                default: '300s'
              }
            }
          }
        }
      };

      pluginService.getAvailablePlugins().subscribe(plugins => {
        expect(plugins).toBeDefined();
        expect(plugins.length).toBe(1);
        
        const plugin = plugins[0];
        expect(plugin.configuration).toBeDefined();
        expect(plugin.configuration!.schema.properties).toBeDefined();
        
        // Test the configuration field extraction
        const properties = plugin.configuration!.schema.properties;
        expect(Object.keys(properties)).toContain('openshift_api_url');
        expect(Object.keys(properties)).toContain('namespace');
        expect(Object.keys(properties)).toContain('timeout');
        
        expect(plugin.configuration!.schema.required).toContain('openshift_api_url');
        expect(plugin.configuration!.schema.required).toContain('namespace');
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/deployment-plugins`);
      expect(req.request.method).toBe('GET');
      req.flush([mockPluginWithNewStructure]);
    });

    it('should handle plugins with legacy configuration structure', () => {
      const mockPluginWithLegacyStructure = {
        id: 'legacy-plugin',
        name: 'Legacy Plugin',
        version: '1.0.0',
        description: 'A legacy plugin with old configuration schema',
        type: 'generic',
        supportedArtifacts: [],
        isEnabled: true,
        configurationSchema: [
          {
            key: 'legacy_field',
            label: 'Legacy Field',
            type: 'string',
            required: true,
            description: 'A legacy configuration field'
          }
        ]
      };

      pluginService.getAvailablePlugins().subscribe(plugins => {
        expect(plugins).toBeDefined();
        expect(plugins.length).toBe(1);
        
        const plugin = plugins[0];
        expect(plugin.configurationSchema).toBeDefined();
        expect(plugin.configurationSchema!.length).toBe(1);
        expect(plugin.configurationSchema![0].key).toBe('legacy_field');
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/deployment-plugins`);
      expect(req.request.method).toBe('GET');
      req.flush([mockPluginWithLegacyStructure]);
    });
  });

  describe('Error Handling', () => {
    it('should handle workflow provider service errors gracefully', () => {
      workflowProviderService.getProviders().subscribe({
        next: () => fail('Expected an error'),
        error: (error) => {
          expect(error.status).toBe(500);
        }
      });

      const req = httpMock.expectOne(`${environment.workflowServiceUrl}/api/v1/providers`);
      req.flush({ message: 'Internal Server Error' }, { status: 500, statusText: 'Internal Server Error' });
    });

    it('should handle plugin service errors gracefully', () => {
      pluginService.getAvailablePlugins().subscribe({
        next: () => fail('Expected an error'),
        error: (error) => {
          expect(error.status).toBe(404);
        }
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/deployment-plugins`);
      req.flush({ message: 'Not Found' }, { status: 404, statusText: 'Not Found' });
    });
  });
});

// Manual integration test function for browser console
function runManualIntegrationTest() {
  console.log('=== Manual Integration Test ===');
  console.log('This test should be run in the browser console when the app is loaded');
  console.log('');
  
  // Test if services are available
  const workflowProviderService = (window as any).angular?.getTestBed?.()?.inject?.(WorkflowProviderService);
  const pluginService = (window as any).angular?.getTestBed?.()?.inject?.(DeploymentPluginService);
  
  if (!workflowProviderService || !pluginService) {
    console.error('❌ Services not available. Make sure the app is loaded and Angular is accessible.');
    return;
  }
  
  console.log('✅ Services available');
  
  // Test workflow provider service
  workflowProviderService.getProviders().subscribe({
    next: (response: any) => {
      console.log('✅ Workflow providers loaded:', response.providers.length);
      response.providers.forEach((provider: any, index: number) => {
        console.log(`  ${index + 1}. ${provider.name} (${provider.type})`);
        console.log(`     Config fields: ${provider.configFields?.length || 0}`);
      });
    },
    error: (error: any) => {
      console.error('❌ Failed to load workflow providers:', error);
    }
  });
  
  // Test plugin service
  pluginService.getAvailablePlugins().subscribe({
    next: (plugins: any[]) => {
      console.log('✅ Plugins loaded:', plugins.length);
      plugins.forEach((plugin: any, index: number) => {
        console.log(`  ${index + 1}. ${plugin.name} (${plugin.id})`);
        if (plugin.configuration?.schema?.properties) {
          const propCount = Object.keys(plugin.configuration.schema.properties).length;
          console.log(`     New config structure: ${propCount} properties`);
        } else if (plugin.configurationSchema) {
          console.log(`     Legacy config structure: ${plugin.configurationSchema.length} fields`);
        } else {
          console.log(`     No configuration structure`);
        }
      });
    },
    error: (error: any) => {
      console.error('❌ Failed to load plugins:', error);
    }
  });
}

export { runManualIntegrationTest };
