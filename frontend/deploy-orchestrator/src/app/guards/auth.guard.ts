import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  constructor(private router: Router, private authService: AuthService) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {

    console.log('AuthGuard: Checking authentication for route:', state.url);

    if (this.authService.isAuthenticated()) {
      // Check if token is close to expiration and try to refresh
      const token = this.authService.getAccessToken();
      if (token) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          const expirationTime = payload.exp * 1000; // Convert to milliseconds
          const currentTime = Date.now();
          const timeUntilExpiration = expirationTime - currentTime;

          console.log('AuthGuard: Token expiration check:', {
            expirationTime: new Date(expirationTime),
            currentTime: new Date(currentTime),
            timeUntilExpiration: timeUntilExpiration,
            willExpireSoon: timeUntilExpiration < 5 * 60 * 1000 // 5 minutes
          });

          // If token expires in less than 5 minutes, try to refresh
          if (timeUntilExpiration < 5 * 60 * 1000) {
            console.log('AuthGuard: Token expires soon, attempting refresh');
            return this.authService.refreshToken().pipe(
              map(() => {
                console.log('AuthGuard: Token refreshed successfully');
                return true;
              }),
              catchError(error => {
                console.warn('AuthGuard: Token refresh failed, redirecting to login:', error);
                this.authService.logout();
                this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
                return of(false);
              })
            );
          }
        } catch (error) {
          console.warn('AuthGuard: Error parsing token, redirecting to login:', error);
          this.authService.logout();
          this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
          return false;
        }
      }

      console.log('AuthGuard: User authenticated, allowing access');
      return true;
    }

    // Not logged in, so redirect to login page with return url
    console.log('AuthGuard: User not authenticated, redirecting to login');
    this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
    return false;
  }
}
