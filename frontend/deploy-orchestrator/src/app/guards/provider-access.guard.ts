import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class ProviderAccessGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => {
        if (!user) {
          this.router.navigate(['/login']);
          return false;
        }

        // For now, allow all authenticated users to access providers
        // TODO: Implement proper permission checking when admin service is ready
        console.log('Provider access granted for user:', user.username);
        return true;
      }),
      catchError((error) => {
        console.error('Provider access guard error:', error);
        // For now, allow access even if there's an error
        // TODO: Implement proper error handling when admin service is ready
        return of(true);
      })
    );
  }
}
