import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class WorkflowPermissionGuard implements CanActivate {
  constructor(private router: Router, private authService: AuthService) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    const requiredPermission = route.data['permission'];
    
    // If no permission specified, allow access
    if (!requiredPermission) {
      console.log('WorkflowPermissionGuard: No permission specified, allowing access');
      return of(true);
    }
    
    console.log(`WorkflowPermissionGuard: Checking permission: ${requiredPermission} for route: ${state.url}`);
    
    // Check if user is admin
    if (this.authService.isAdmin()) {
      console.log('WorkflowPermissionGuard: User is admin, allowing access');
      return of(true);
    }
    
    // Check permissions with backend
    console.log('WorkflowPermissionGuard: Delegating permission check to AuthService');
    
    // For workflow permissions, we pass an empty project ID (will be handled by the service)
    return this.authService.hasPermission(requiredPermission, '').pipe(
      tap(hasPermission => {
        console.log(`WorkflowPermissionGuard: Permission ${requiredPermission} check result: ${hasPermission}`);
      }),
      map(hasPermission => {
        if (!hasPermission) {
          console.warn(`Access denied: Missing required permission ${requiredPermission}`);
          this.router.navigate(['/dashboard']);
          return false;
        }
        return true;
      })
    );
  }
}
