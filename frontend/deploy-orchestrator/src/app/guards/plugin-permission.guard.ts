import { Injectable } from '@angular/core';
import { CanActivate, CanActivateChild, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { PluginPermissionService } from '../services/plugin-permission.service';
import { AuthService } from '../services/auth.service';
import { PLUGIN_PERMISSIONS, PluginPermissionType } from '../models/plugin-permission.model';

@Injectable({
  providedIn: 'root'
})
export class PluginPermissionGuard implements CanActivate, CanActivateChild {

  constructor(
    private pluginPermissionService: PluginPermissionService,
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkPermission(route, state);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkPermission(childRoute, state);
  }

  private checkPermission(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    // Check if user is authenticated first
    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
      return of(false);
    }

    // Get permission requirements from route data
    const requiredPermission = route.data['permission'] as PluginPermissionType;
    const pluginName = route.data['pluginName'] as string;
    const feature = route.data['feature'] as string;
    const allowAdmin = route.data['allowAdmin'] !== false; // Default to true
    const allowPublic = route.data['allowPublic'] === true; // Default to false

    // If no specific permission required and public access allowed
    if (!requiredPermission && allowPublic) {
      return of(true);
    }

    // Check if user is admin and admin access is allowed
    if (allowAdmin) {
      return this.pluginPermissionService.isPluginAdmin().pipe(
        map(isAdmin => {
          if (isAdmin) return true;
          
          // If not admin, continue with permission checks
          return this.checkSpecificPermission(requiredPermission, pluginName, feature, state.url);
        }),
        catchError(() => {
          this.handleAccessDenied(state.url);
          return of(false);
        })
      );
    }

    // Check specific permission
    return this.checkSpecificPermissionObservable(requiredPermission, pluginName, feature, state.url);
  }

  private checkSpecificPermissionObservable(
    requiredPermission: PluginPermissionType,
    pluginName: string,
    feature: string,
    url: string
  ): Observable<boolean> {
    if (!requiredPermission) {
      return of(true);
    }

    if (pluginName) {
      return this.pluginPermissionService.hasPluginPermission(pluginName, requiredPermission).pipe(
        tap(hasPermission => {
          if (!hasPermission) {
            this.handleAccessDenied(url, `Missing permission: ${requiredPermission} for plugin: ${pluginName}`);
          }
        }),
        catchError(() => {
          this.handleAccessDenied(url);
          return of(false);
        })
      );
    }

    if (feature) {
      return this.pluginPermissionService.hasPluginFeatureAccess(pluginName, feature as any).pipe(
        map(featureAccess => featureAccess.accessible),
        tap(hasAccess => {
          if (!hasAccess) {
            this.handleAccessDenied(url, `Access denied to feature: ${feature}`);
          }
        }),
        catchError(() => {
          this.handleAccessDenied(url);
          return of(false);
        })
      );
    }

    // Fallback to checking if user has any plugin permissions
    return this.pluginPermissionService.getAccessiblePlugins().pipe(
      map(plugins => plugins.length > 0),
      tap(hasAccess => {
        if (!hasAccess) {
          this.handleAccessDenied(url, 'No plugin access permissions');
        }
      }),
      catchError(() => {
        this.handleAccessDenied(url);
        return of(false);
      })
    );
  }

  private checkSpecificPermission(
    requiredPermission: PluginPermissionType,
    pluginName: string,
    feature: string,
    url: string
  ): boolean {
    if (!requiredPermission) {
      return true;
    }

    if (pluginName) {
      const hasPermission = this.pluginPermissionService.hasPluginPermissionSync(pluginName, requiredPermission);
      if (!hasPermission) {
        this.handleAccessDenied(url, `Missing permission: ${requiredPermission} for plugin: ${pluginName}`);
        return false;
      }
      return true;
    }

    if (feature) {
      const featureAccess = this.pluginPermissionService.hasPluginFeatureAccessSync(pluginName, feature as any);
      if (!featureAccess.accessible) {
        this.handleAccessDenied(url, `Access denied to feature: ${feature}`);
        return false;
      }
      return true;
    }

    return true;
  }

  private handleAccessDenied(url: string, reason?: string): void {
    console.warn('Plugin access denied:', { url, reason });
    
    // Log the access attempt for audit purposes
    this.pluginPermissionService.logPluginAction('access_denied', url, {
      reason,
      timestamp: new Date().toISOString()
    }).subscribe({
      error: (error) => console.error('Failed to log access denial:', error)
    });

    // Redirect to access denied page or dashboard
    this.router.navigate(['/access-denied'], { 
      queryParams: { 
        returnUrl: url,
        reason: reason || 'Insufficient permissions'
      }
    });
  }
}

@Injectable({
  providedIn: 'root'
})
export class PluginManagementGuard implements CanActivate {
  constructor(
    private pluginPermissionService: PluginPermissionService,
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
      return of(false);
    }

    // Check if user can view any plugins or is admin
    return this.pluginPermissionService.isPluginAdmin().pipe(
      map(isAdmin => {
        if (isAdmin) return true;
        
        // Check if user has any plugin view permissions
        const matrix = this.pluginPermissionService.getCurrentPermissionMatrix();
        if (!matrix) return false;
        
        return Object.keys(matrix.plugins).some(pluginName => 
          matrix.plugins[pluginName].canView
        );
      }),
      tap(hasAccess => {
        if (!hasAccess) {
          this.router.navigate(['/access-denied'], {
            queryParams: {
              returnUrl: state.url,
              reason: 'No plugin management permissions'
            }
          });
        }
      }),
      catchError(() => {
        this.router.navigate(['/access-denied']);
        return of(false);
      })
    );
  }
}

@Injectable({
  providedIn: 'root'
})
export class EnvironmentProviderAccessGuard implements CanActivate {
  constructor(
    private pluginPermissionService: PluginPermissionService,
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
      return of(false);
    }

    const providerType = route.params['providerType'];
    const action = route.data['action'] || 'view';

    if (providerType) {
      return this.pluginPermissionService.canAccessProvider(providerType, action).pipe(
        tap(hasAccess => {
          if (!hasAccess) {
            this.router.navigate(['/access-denied'], {
              queryParams: {
                returnUrl: state.url,
                reason: `No ${action} access to ${providerType} provider`
              }
            });
          }
        }),
        catchError(() => {
          this.router.navigate(['/access-denied']);
          return of(false);
        })
      );
    }

    // Check if user can access any providers
    return this.pluginPermissionService.getAccessibleProviders().pipe(
      map(providers => providers.length > 0),
      tap(hasAccess => {
        if (!hasAccess) {
          this.router.navigate(['/access-denied'], {
            queryParams: {
              returnUrl: state.url,
              reason: 'No provider access permissions'
            }
          });
        }
      }),
      catchError(() => {
        this.router.navigate(['/access-denied']);
        return of(false);
      })
    );
  }
}

@Injectable({
  providedIn: 'root'
})
export class TemplateDeployGuard implements CanActivate {
  constructor(
    private pluginPermissionService: PluginPermissionService,
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
      return of(false);
    }

    const pluginName = route.params['pluginName'];
    const templateId = route.params['templateId'];
    const projectId = route.queryParams['projectId'];
    const environmentId = route.queryParams['environmentId'];

    if (pluginName && templateId) {
      return this.pluginPermissionService.canDeployTemplate(
        pluginName, 
        templateId, 
        projectId, 
        environmentId
      ).pipe(
        tap(canDeploy => {
          if (!canDeploy) {
            this.router.navigate(['/access-denied'], {
              queryParams: {
                returnUrl: state.url,
                reason: `Cannot deploy template ${templateId} from plugin ${pluginName}`
              }
            });
          }
        }),
        catchError(() => {
          this.router.navigate(['/access-denied']);
          return of(false);
        })
      );
    }

    // Check if user can deploy any templates
    return this.pluginPermissionService.getAccessibleTemplates().pipe(
      map(templates => templates.length > 0),
      tap(hasAccess => {
        if (!hasAccess) {
          this.router.navigate(['/access-denied'], {
            queryParams: {
              returnUrl: state.url,
              reason: 'No template deployment permissions'
            }
          });
        }
      }),
      catchError(() => {
        this.router.navigate(['/access-denied']);
        return of(false);
      })
    );
  }
}
