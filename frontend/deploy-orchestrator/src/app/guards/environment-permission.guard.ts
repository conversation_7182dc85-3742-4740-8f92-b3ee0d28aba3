import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class EnvironmentPermissionGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {

    const requiredPermission = route.data['permission'] as string;

    return this.authService.currentUser$.pipe(
      take(1),
      map(user => {
        if (!user) {
          this.router.navigate(['/login']);
          return false;
        }

        // Admin users can access all environment operations
        if (this.authService.isAdmin()) {
          return true;
        }

        // Check if user has the required environment permission
        if (requiredPermission && !this.authService.isAdmin()) {
          console.warn(`Access denied: User lacks permission '${requiredPermission}'`);
          this.router.navigate(['/dashboard']);
          return false;
        }

        // Check if user has access to any projects (needed for environment access)
        const userProjects = this.authService.getUserProjects();
        // For now, just allow admin access
        if (!this.authService.isAdmin()) {
          console.warn('Access denied: User has no project access');
          this.router.navigate(['/dashboard']);
          return false;
        }

        return true;
      })
    );
  }
}
