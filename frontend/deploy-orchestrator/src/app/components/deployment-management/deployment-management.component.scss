// Custom styles for deployment management component
.deployment-management {
  .step-indicator {
    @apply relative flex items-center justify-center w-8 h-8 rounded-full border-2 bg-white transition-colors;
    
    &.active {
      @apply border-blue-600 text-blue-600;
    }
    
    &.completed {
      @apply border-blue-600 bg-blue-600 text-white;
    }
    
    &.inactive {
      @apply border-gray-300 text-gray-500;
    }
  }

  .step-connector {
    @apply absolute top-4 left-8 w-full h-0.5 bg-gray-200 transition-colors;
    
    &.completed {
      @apply bg-blue-600;
    }
  }

  .selection-card {
    @apply border rounded-lg p-4 cursor-pointer transition-all duration-200;
    
    &:hover {
      @apply shadow-md;
    }
    
    &.selected {
      @apply border-blue-500 bg-blue-50 shadow-sm;
    }
    
    &.unselected {
      @apply border-gray-200 hover:border-gray-300;
    }
  }

  .form-section {
    @apply bg-white shadow rounded-lg p-6 space-y-6;
  }

  .form-field {
    @apply space-y-2;
    
    label {
      @apply block text-sm font-medium text-gray-700;
    }
    
    input, select, textarea {
      @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
      
      &:disabled {
        @apply bg-gray-100 cursor-not-allowed;
      }
      
      &.error {
        @apply border-red-300 focus:ring-red-500;
      }
    }
    
    .error-message {
      @apply text-sm text-red-600;
    }
    
    .help-text {
      @apply text-sm text-gray-500;
    }
  }

  .parameter-field {
    @apply border border-gray-200 rounded-lg p-4 space-y-3;
    
    .parameter-label {
      @apply flex items-center space-x-2;
      
      .required {
        @apply text-red-500;
      }
    }
    
    .parameter-description {
      @apply text-sm text-gray-500;
    }
  }

  .summary-section {
    @apply space-y-6;
    
    .summary-grid {
      @apply grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2;
    }
    
    .summary-item {
      dt {
        @apply text-sm font-medium text-gray-500;
      }
      
      dd {
        @apply mt-1 text-sm text-gray-900;
      }
    }
    
    .item-list {
      @apply space-y-2;
      
      h4 {
        @apply text-base font-medium text-gray-900;
      }
      
      ul {
        @apply list-disc list-inside text-sm text-gray-600 space-y-1;
      }
    }
  }

  .navigation-buttons {
    @apply flex justify-between items-center pt-6 border-t border-gray-200;
    
    .button-group {
      @apply flex space-x-3;
    }
  }

  .btn {
    @apply px-4 py-2 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
    
    &.btn-primary {
      @apply text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500;
      
      &:disabled {
        @apply opacity-50 cursor-not-allowed;
      }
    }
    
    &.btn-secondary {
      @apply text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:ring-blue-500;
    }
    
    &.btn-success {
      @apply text-white bg-green-600 hover:bg-green-700 focus:ring-green-500;
      
      &:disabled {
        @apply opacity-50 cursor-not-allowed;
      }
    }
  }

  .loading-spinner {
    @apply animate-spin h-5 w-5;
  }

  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    
    &.status-active {
      @apply bg-green-100 text-green-800;
    }
    
    &.status-inactive {
      @apply bg-red-100 text-red-800;
    }
    
    &.status-pending {
      @apply bg-yellow-100 text-yellow-800;
    }
  }

  .grid-layout {
    @apply grid gap-4;
    
    &.cols-1 {
      @apply grid-cols-1;
    }
    
    &.cols-2 {
      @apply grid-cols-1 md:grid-cols-2;
    }
    
    &.cols-3 {
      @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
    }
  }

  .loading-overlay {
    @apply fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center;
    
    .loading-content {
      @apply bg-white p-6 rounded-lg shadow-lg;
      
      .loading-text {
        @apply flex items-center text-gray-700;
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 640px) {
    .summary-grid {
      @apply grid-cols-1;
    }
    
    .navigation-buttons {
      @apply flex-col space-y-3;
      
      .button-group {
        @apply w-full justify-center;
      }
    }
    
    .grid-layout {
      &.cols-2, &.cols-3 {
        @apply grid-cols-1;
      }
    }
  }

  // Animation classes
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .slide-in {
    animation: slideIn 0.3s ease-in-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// Custom checkbox and radio styles
.custom-checkbox {
  @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

.custom-radio {
  @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300;
}

// Form validation styles
.form-error {
  @apply border-red-300 focus:ring-red-500 focus:border-red-500;
}

.error-text {
  @apply text-sm text-red-600 mt-1;
}

// Progress bar styles
.progress-bar {
  @apply w-full bg-gray-200 rounded-full h-2;
  
  .progress-fill {
    @apply bg-blue-600 h-2 rounded-full transition-all duration-300;
  }
}
