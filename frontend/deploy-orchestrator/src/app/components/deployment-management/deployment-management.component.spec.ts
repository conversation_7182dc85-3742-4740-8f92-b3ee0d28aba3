import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';

import { DeploymentManagementComponent } from './deployment-management.component';
import { DeploymentManagementService } from '../../services/deployment-management.service';
import { ApplicationService } from '../../services/application.service';
import { EnvironmentService } from '../../services/environment.service';
import { WorkflowService } from '../../services/workflow.service';
import { ProjectService } from '../../services/project.service';
import { NotificationService } from '../../services/notification.service';

describe('DeploymentManagementComponent', () => {
  let component: DeploymentManagementComponent;
  let fixture: ComponentFixture<DeploymentManagementComponent>;
  let mockDeploymentService: jasmine.SpyObj<DeploymentManagementService>;
  let mockApplicationService: jasmine.SpyObj<ApplicationService>;
  let mockEnvironmentService: jasmine.SpyObj<EnvironmentService>;
  let mockWorkflowService: jasmine.SpyObj<WorkflowService>;
  let mockProjectService: jasmine.SpyObj<ProjectService>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockRouter: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    const deploymentServiceSpy = jasmine.createSpyObj('DeploymentManagementService', ['createDeployment']);
    const applicationServiceSpy = jasmine.createSpyObj('ApplicationService', ['getApplications', 'getApplicationComponents']);
    const environmentServiceSpy = jasmine.createSpyObj('EnvironmentService', ['getEnvironments']);
    const workflowServiceSpy = jasmine.createSpyObj('WorkflowService', ['getWorkflows', 'getWorkflow']);
    const projectServiceSpy = jasmine.createSpyObj('ProjectService', ['getProjects']);
    const notificationServiceSpy = jasmine.createSpyObj('NotificationService', ['success', 'error']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [
        ReactiveFormsModule,
        DeploymentManagementComponent
      ],
      providers: [
        { provide: DeploymentManagementService, useValue: deploymentServiceSpy },
        { provide: ApplicationService, useValue: applicationServiceSpy },
        { provide: EnvironmentService, useValue: environmentServiceSpy },
        { provide: WorkflowService, useValue: workflowServiceSpy },
        { provide: ProjectService, useValue: projectServiceSpy },
        { provide: NotificationService, useValue: notificationServiceSpy },
        { provide: Router, useValue: routerSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DeploymentManagementComponent);
    component = fixture.componentInstance;
    
    mockDeploymentService = TestBed.inject(DeploymentManagementService) as jasmine.SpyObj<DeploymentManagementService>;
    mockApplicationService = TestBed.inject(ApplicationService) as jasmine.SpyObj<ApplicationService>;
    mockEnvironmentService = TestBed.inject(EnvironmentService) as jasmine.SpyObj<EnvironmentService>;
    mockWorkflowService = TestBed.inject(WorkflowService) as jasmine.SpyObj<WorkflowService>;
    mockProjectService = TestBed.inject(ProjectService) as jasmine.SpyObj<ProjectService>;
    mockNotificationService = TestBed.inject(NotificationService) as jasmine.SpyObj<NotificationService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  beforeEach(() => {
    // Setup default mock responses
    mockProjectService.getProjects.and.returnValue(of([
      { id: 'project1', name: 'Test Project 1', description: 'Test project 1', isActive: true },
      { id: 'project2', name: 'Test Project 2', description: 'Test project 2', isActive: true }
    ]));

    mockApplicationService.getApplications.and.returnValue(of({
      deployables: [
        { id: 'app1', name: 'Test App 1', description: 'Test application 1', type: 'application', projectId: 'project1', isActive: true, createdAt: new Date(), updatedAt: new Date() },
        { id: 'app2', name: 'Test App 2', description: 'Test application 2', type: 'application', projectId: 'project1', isActive: true, createdAt: new Date(), updatedAt: new Date() }
      ],
      total: 2
    }));

    mockEnvironmentService.getEnvironments.and.returnValue(of({
      environments: [
        { id: 'env1', name: 'Development', description: 'Dev environment', projectId: 'project1', type: 'development', status: 'active', configuration: {}, createdAt: new Date(), updatedAt: new Date() },
        { id: 'env2', name: 'Production', description: 'Prod environment', projectId: 'project1', type: 'production', status: 'active', configuration: {}, createdAt: new Date(), updatedAt: new Date() }
      ],
      total: 2
    }));

    mockWorkflowService.getWorkflows.and.returnValue(of([
      { id: 'workflow1', name: 'Standard Deployment', description: 'Standard deployment workflow', projectId: 'project1', version: '1.0', isActive: true, steps: [], triggers: [], parameters: [], createdAt: new Date(), updatedAt: new Date() },
      { id: 'workflow2', name: 'Blue-Green Deployment', description: 'Blue-green deployment workflow', projectId: 'project1', version: '1.0', isActive: true, steps: [], triggers: [], parameters: [], createdAt: new Date(), updatedAt: new Date() }
    ]));

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.deploymentForm).toBeDefined();
    expect(component.deploymentForm.get('projectId')?.value).toBe('');
    expect(component.deploymentForm.get('version')?.value).toBe('');
    expect(component.deploymentForm.get('strategy')?.value).toBe('rolling_update');
  });

  it('should load projects on init', () => {
    expect(mockProjectService.getProjects).toHaveBeenCalled();
    expect(component.projects.length).toBe(2);
    expect(component.projects[0].name).toBe('Test Project 1');
  });

  it('should load project data when project is selected', () => {
    component.deploymentForm.patchValue({ projectId: 'project1' });
    component.onProjectChange();

    expect(mockApplicationService.getApplications).toHaveBeenCalledWith({ projectId: 'project1' });
    expect(mockEnvironmentService.getEnvironments).toHaveBeenCalledWith({ projectId: 'project1' });
    expect(mockWorkflowService.getWorkflows).toHaveBeenCalledWith('project1');
  });

  it('should handle project data loading error', () => {
    mockApplicationService.getApplications.and.returnValue(throwError(() => new Error('Failed to load')));
    
    component.deploymentForm.patchValue({ projectId: 'project1' });
    component.onProjectChange();

    expect(mockNotificationService.error).toHaveBeenCalledWith('Failed to load project data', 'Please try again');
  });

  describe('Application Selection', () => {
    beforeEach(() => {
      component.applications = [
        { id: 'app1', name: 'Test App 1', description: 'Test application 1', type: 'application', projectId: 'project1', isActive: true, createdAt: new Date(), updatedAt: new Date() },
        { id: 'app2', name: 'Test App 2', description: 'Test application 2', type: 'application', projectId: 'project1', isActive: true, createdAt: new Date(), updatedAt: new Date() }
      ];
    });

    it('should toggle application selection', () => {
      const app = component.applications[0];
      
      expect(component.selectedApplications.length).toBe(0);
      
      component.toggleApplicationSelection(app);
      expect(component.selectedApplications.length).toBe(1);
      expect(component.selectedApplications[0]).toBe(app);
      
      component.toggleApplicationSelection(app);
      expect(component.selectedApplications.length).toBe(0);
    });

    it('should check if application is selected', () => {
      const app = component.applications[0];
      
      expect(component.isApplicationSelected(app.id)).toBe(false);
      
      component.selectedApplications.push(app);
      expect(component.isApplicationSelected(app.id)).toBe(true);
    });
  });

  describe('Environment Selection', () => {
    beforeEach(() => {
      component.environments = [
        { id: 'env1', name: 'Development', status: 'active', type: 'development' },
        { id: 'env2', name: 'Production', status: 'active', type: 'production' }
      ];
    });

    it('should select environment', () => {
      const env = component.environments[0];
      
      component.selectEnvironment(env);
      
      expect(component.deploymentForm.get('environmentId')?.value).toBe(env.id);
      expect(component.selectedEnvironment).toBe(env);
    });

    it('should check if environment is selected', () => {
      const env = component.environments[0];
      
      expect(component.isEnvironmentSelected(env.id)).toBe(false);
      
      component.deploymentForm.patchValue({ environmentId: env.id });
      expect(component.isEnvironmentSelected(env.id)).toBe(true);
    });
  });

  describe('Step Navigation', () => {
    it('should navigate to next step when current step is valid', () => {
      // Setup valid step 1
      component.deploymentForm.patchValue({
        projectId: 'project1',
        version: 'v1.0.0'
      });
      component.selectedApplications = [{ id: 'app1', name: 'Test App' }];
      
      component.nextStep();
      expect(component.currentStep).toBe(2);
    });

    it('should not navigate to next step when current step is invalid', () => {
      // Step 1 is invalid (no project selected)
      component.nextStep();
      expect(component.currentStep).toBe(1);
    });

    it('should navigate to previous step', () => {
      component.currentStep = 2;
      component.previousStep();
      expect(component.currentStep).toBe(1);
    });

    it('should not go below step 1', () => {
      component.currentStep = 1;
      component.previousStep();
      expect(component.currentStep).toBe(1);
    });
  });

  describe('Form Submission', () => {
    beforeEach(() => {
      // Setup valid form
      component.deploymentForm.patchValue({
        projectId: 'project1',
        version: 'v1.0.0',
        environmentId: 'env1',
        workflowId: 'workflow1',
        providerType: 'helm'
      });
      component.selectedApplications = [{ id: 'app1', name: 'Test App' }];
    });

    it('should submit deployment successfully', () => {
      const mockResponse = {
        deploymentId: 'deployment1',
        workflowExecution: 'execution1',
        status: 'pending',
        createdAt: new Date().toISOString()
      };
      
      mockDeploymentService.createDeployment.and.returnValue(of(mockResponse));
      
      component.onSubmit();
      
      expect(mockDeploymentService.createDeployment).toHaveBeenCalled();
      expect(mockNotificationService.success).toHaveBeenCalledWith('Deployment created successfully', 'Redirecting to logs...');
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/execution-monitoring'], {
        queryParams: { deploymentId: 'deployment1' }
      });
    });

    it('should handle deployment submission error', () => {
      mockDeploymentService.createDeployment.and.returnValue(throwError(() => new Error('Failed to create')));
      
      component.onSubmit();
      
      expect(mockNotificationService.error).toHaveBeenCalledWith('Failed to create deployment', 'Please try again');
      expect(component.submitting).toBe(false);
    });

    it('should not submit when form is invalid', () => {
      component.deploymentForm.patchValue({ projectId: '' }); // Make form invalid
      
      component.onSubmit();
      
      expect(mockDeploymentService.createDeployment).not.toHaveBeenCalled();
    });
  });

  describe('Step Validation', () => {
    it('should validate step 1 correctly', () => {
      component.currentStep = 1;
      
      // Invalid - no project
      expect(component.validateCurrentStep()).toBe(false);
      
      // Invalid - project but no applications/components
      component.deploymentForm.patchValue({ projectId: 'project1', version: 'v1.0.0' });
      expect(component.validateCurrentStep()).toBe(false);
      
      // Valid - project, version, and application selected
      component.selectedApplications = [{ id: 'app1', name: 'Test App' }];
      expect(component.validateCurrentStep()).toBe(true);
    });

    it('should validate step 2 correctly', () => {
      component.currentStep = 2;
      
      // Invalid - no environment
      expect(component.validateCurrentStep()).toBe(false);
      
      // Valid - environment selected
      component.deploymentForm.patchValue({ environmentId: 'env1' });
      expect(component.validateCurrentStep()).toBe(true);
    });

    it('should validate step 3 correctly', () => {
      component.currentStep = 3;
      
      // Invalid - no workflow
      expect(component.validateCurrentStep()).toBe(false);
      
      // Invalid - workflow but no provider
      component.deploymentForm.patchValue({ workflowId: 'workflow1' });
      expect(component.validateCurrentStep()).toBe(false);
      
      // Valid - workflow and provider
      component.deploymentForm.patchValue({ providerType: 'helm' });
      expect(component.validateCurrentStep()).toBe(true);
    });
  });

  describe('Utility Methods', () => {
    it('should return correct step titles', () => {
      expect(component.getStepTitle(1)).toBe('Select Applications & Version');
      expect(component.getStepTitle(2)).toBe('Choose Environment');
      expect(component.getStepTitle(3)).toBe('Configure Workflow');
      expect(component.getStepTitle(4)).toBe('Review & Deploy');
      expect(component.getStepTitle(5)).toBe('');
    });

    it('should check step completion correctly', () => {
      component.currentStep = 3;
      
      expect(component.isStepCompleted(1)).toBe(true);
      expect(component.isStepCompleted(2)).toBe(true);
      expect(component.isStepCompleted(3)).toBe(false);
      expect(component.isStepCompleted(4)).toBe(false);
    });

    it('should check active step correctly', () => {
      component.currentStep = 2;
      
      expect(component.isStepActive(1)).toBe(false);
      expect(component.isStepActive(2)).toBe(true);
      expect(component.isStepActive(3)).toBe(false);
    });
  });
});
