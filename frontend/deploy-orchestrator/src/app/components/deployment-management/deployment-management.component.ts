import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Form<PERSON>uilder, FormGroup, Validators, FormArray, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil, forkJoin } from 'rxjs';

import { DeploymentManagementService, BulkDeploymentRequest } from '../../services/deployment-management.service';
import { ApplicationService } from '../../services/application.service';
import { EnvironmentService } from '../../services/environment.service';
import { WorkflowService } from '../../services/workflow.service';
import { ProjectService } from '../../services/project.service';
import { NotificationService } from '../../services/notification.service';

@Component({
  selector: 'app-deployment-management',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule
  ],
  templateUrl: './deployment-management.component.html',
  styleUrls: ['./deployment-management.component.scss']
})
export class DeploymentManagementComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  deploymentForm: FormGroup;
  currentStep = 1;
  totalSteps = 4;
  
  // Data
  projects: any[] = [];
  applications: any[] = [];
  components: any[] = [];
  environments: any[] = [];
  workflows: any[] = [];
  providers: any[] = [];
  
  // Selected data
  selectedProject: any = null;
  selectedApplications: any[] = [];
  selectedComponents: any[] = [];
  selectedEnvironment: any = null;
  selectedWorkflow: any = null;
  
  // Dynamic form fields
  workflowParameters: any[] = [];
  secretMappings: any[] = [];
  
  // UI state
  loading = false;
  submitting = false;
  
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private deploymentService: DeploymentManagementService,
    private applicationService: ApplicationService,
    private environmentService: EnvironmentService,
    private workflowService: WorkflowService,
    private projectService: ProjectService,
    private notificationService: NotificationService
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadProjects();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.deploymentForm = this.fb.group({
      projectId: ['', Validators.required],
      applicationIds: [[]],
      componentIds: [[]],
      version: ['', Validators.required],
      environmentId: ['', Validators.required],
      workflowId: ['', Validators.required],
      providerType: ['', Validators.required],
      strategy: ['rolling_update'],
      parameters: this.fb.array([]),
      secretMappings: this.fb.array([]),
      configuration: this.fb.group({})
    });
  }

  // Step navigation
  nextStep(): void {
    if (this.currentStep < this.totalSteps) {
      if (this.validateCurrentStep()) {
        this.currentStep++;
        if (this.currentStep === 3) {
          this.loadWorkflowParameters();
        }
      }
    }
  }

  previousStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  goToStep(step: number): void {
    if (step <= this.currentStep || this.validateStepsUpTo(step - 1)) {
      this.currentStep = step;
      if (step === 3) {
        this.loadWorkflowParameters();
      }
    }
  }

  private validateCurrentStep(): boolean {
    switch (this.currentStep) {
      case 1:
        return this.deploymentForm.get('projectId')?.valid && 
               (this.selectedApplications.length > 0 || this.selectedComponents.length > 0) &&
               this.deploymentForm.get('version')?.valid;
      case 2:
        return this.deploymentForm.get('environmentId')?.valid;
      case 3:
        return this.deploymentForm.get('workflowId')?.valid && 
               this.deploymentForm.get('providerType')?.valid;
      default:
        return true;
    }
  }

  private validateStepsUpTo(step: number): boolean {
    for (let i = 1; i <= step; i++) {
      const currentStep = this.currentStep;
      this.currentStep = i;
      if (!this.validateCurrentStep()) {
        this.currentStep = currentStep;
        return false;
      }
    }
    this.currentStep = step + 1;
    return true;
  }

  // Data loading
  private loadProjects(): void {
    this.loading = true;
    this.projectService.getProjects()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (projects) => {
          this.projects = projects;
          this.loading = false;
        },
        error: (error) => {
          this.notificationService.showError('Failed to load projects');
          this.loading = false;
        }
      });
  }

  onProjectChange(): void {
    const projectId = this.deploymentForm.get('projectId')?.value;
    if (projectId) {
      this.selectedProject = this.projects.find(p => p.id === projectId);
      this.loadProjectData(projectId);
    }
  }

  private loadProjectData(projectId: string): void {
    this.loading = true;
    
    forkJoin({
      applications: this.applicationService.getApplications(projectId),
      environments: this.environmentService.getEnvironments(projectId),
      workflows: this.workflowService.getWorkflows(projectId)
    }).pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (data) => {
        this.applications = data.applications;
        this.environments = data.environments;
        this.workflows = data.workflows;
        this.loading = false;
      },
      error: (error) => {
        this.notificationService.showError('Failed to load project data');
        this.loading = false;
      }
    });
  }

  // Application/Component selection
  toggleApplicationSelection(application: any): void {
    const index = this.selectedApplications.findIndex(a => a.id === application.id);
    if (index > -1) {
      this.selectedApplications.splice(index, 1);
    } else {
      this.selectedApplications.push(application);
      // Load components for this application
      this.loadApplicationComponents(application.id);
    }
    this.updateFormArrays();
  }

  toggleComponentSelection(component: any): void {
    const index = this.selectedComponents.findIndex(c => c.id === component.id);
    if (index > -1) {
      this.selectedComponents.splice(index, 1);
    } else {
      this.selectedComponents.push(component);
    }
    this.updateFormArrays();
  }

  private loadApplicationComponents(applicationId: string): void {
    this.applicationService.getApplicationComponents(applicationId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (components) => {
          // Add components to the list if not already present
          components.forEach(component => {
            if (!this.components.find(c => c.id === component.id)) {
              this.components.push(component);
            }
          });
        },
        error: (error) => {
          this.notificationService.showError('Failed to load application components');
        }
      });
  }

  private updateFormArrays(): void {
    this.deploymentForm.patchValue({
      applicationIds: this.selectedApplications.map(a => a.id),
      componentIds: this.selectedComponents.map(c => c.id)
    });
  }

  // Environment selection
  onEnvironmentChange(): void {
    const environmentId = this.deploymentForm.get('environmentId')?.value;
    if (environmentId) {
      this.selectedEnvironment = this.environments.find(e => e.id === environmentId);
    }
  }

  // Workflow selection and parameter loading
  onWorkflowChange(): void {
    const workflowId = this.deploymentForm.get('workflowId')?.value;
    if (workflowId) {
      this.selectedWorkflow = this.workflows.find(w => w.id === workflowId);
      this.loadWorkflowParameters();
    }
  }

  private loadWorkflowParameters(): void {
    if (!this.selectedWorkflow) return;
    
    // Load workflow details to get parameters
    this.workflowService.getWorkflow(this.selectedWorkflow.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (workflow) => {
          this.workflowParameters = workflow.parameters || [];
          this.providers = workflow.providers || [];
          this.buildParameterForm();
        },
        error: (error) => {
          this.notificationService.showError('Failed to load workflow parameters');
        }
      });
  }

  private buildParameterForm(): void {
    const parametersArray = this.deploymentForm.get('parameters') as FormArray;
    parametersArray.clear();

    this.workflowParameters.forEach(param => {
      const paramGroup = this.fb.group({
        name: [param.name],
        value: [param.defaultValue || '', param.required ? Validators.required : null],
        type: [param.type],
        description: [param.description]
      });
      parametersArray.push(paramGroup);
    });
  }

  // Form submission
  onSubmit(): void {
    if (this.deploymentForm.valid) {
      this.submitting = true;
      
      const formValue = this.deploymentForm.value;
      const request: BulkDeploymentRequest = {
        projectId: formValue.projectId,
        applicationIds: formValue.applicationIds,
        componentIds: formValue.componentIds,
        version: formValue.version,
        environmentId: formValue.environmentId,
        workflowId: formValue.workflowId,
        providerType: formValue.providerType,
        strategy: formValue.strategy,
        parameters: this.buildParametersObject(),
        secretMappings: this.buildSecretMappingsObject(),
        configuration: formValue.configuration
      };

      this.deploymentService.createDeployment(request)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            this.notificationService.showSuccess('Deployment created successfully');
            // Navigate to real-time logs
            this.router.navigate(['/real-time-logs'], { 
              queryParams: { deploymentId: response.deploymentId }
            });
          },
          error: (error) => {
            this.notificationService.showError('Failed to create deployment');
            this.submitting = false;
          }
        });
    }
  }

  private buildParametersObject(): { [key: string]: any } {
    const parameters: { [key: string]: any } = {};
    const parametersArray = this.deploymentForm.get('parameters') as FormArray;
    
    parametersArray.controls.forEach(control => {
      const param = control.value;
      parameters[param.name] = param.value;
    });
    
    return parameters;
  }

  private buildSecretMappingsObject(): { [key: string]: string } {
    const mappings: { [key: string]: string } = {};
    const mappingsArray = this.deploymentForm.get('secretMappings') as FormArray;
    
    mappingsArray.controls.forEach(control => {
      const mapping = control.value;
      if (mapping.key && mapping.value) {
        mappings[mapping.key] = mapping.value;
      }
    });
    
    return mappings;
  }

  // Utility methods
  getStepTitle(step: number): string {
    switch (step) {
      case 1: return 'Select Applications & Version';
      case 2: return 'Choose Environment';
      case 3: return 'Configure Workflow';
      case 4: return 'Review & Deploy';
      default: return '';
    }
  }

  isStepCompleted(step: number): boolean {
    return step < this.currentStep;
  }

  isStepActive(step: number): boolean {
    return step === this.currentStep;
  }
}
