<div class="min-h-screen bg-gray-50 py-8">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Deploy Applications</h1>
      <p class="mt-2 text-gray-600">Deploy single or multiple applications to your environments using workflows</p>
    </div>

    <!-- Progress Steps -->
    <div class="mb-8">
      <nav aria-label="Progress">
        <ol class="flex items-center">
          <li *ngFor="let step of [1,2,3,4]; let i = index" class="relative">
            <!-- Step connector line -->
            <div *ngIf="i < 3" class="absolute top-4 left-8 w-full h-0.5 bg-gray-200" 
                 [class.bg-blue-600]="isStepCompleted(step + 1)"></div>
            
            <!-- Step circle -->
            <div class="relative flex items-center justify-center w-8 h-8 rounded-full border-2 bg-white"
                 [class.border-blue-600]="isStepActive(step) || isStepCompleted(step)"
                 [class.bg-blue-600]="isStepCompleted(step)"
                 [class.border-gray-300]="!isStepActive(step) && !isStepCompleted(step)">
              <span class="text-sm font-medium"
                    [class.text-white]="isStepCompleted(step)"
                    [class.text-blue-600]="isStepActive(step) && !isStepCompleted(step)"
                    [class.text-gray-500]="!isStepActive(step) && !isStepCompleted(step)">
                {{ step }}
              </span>
            </div>
            
            <!-- Step title -->
            <div class="ml-4 min-w-0 flex-1">
              <span class="text-sm font-medium"
                    [class.text-blue-600]="isStepActive(step)"
                    [class.text-gray-900]="isStepCompleted(step)"
                    [class.text-gray-500]="!isStepActive(step) && !isStepCompleted(step)">
                {{ getStepTitle(step) }}
              </span>
            </div>
          </li>
        </ol>
      </nav>
    </div>

    <!-- Form Content -->
    <form [formGroup]="deploymentForm" (ngSubmit)="onSubmit()" class="space-y-8">
      
      <!-- Step 1: Select Applications & Version -->
      <div *ngIf="currentStep === 1" class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Select Applications & Version</h2>
        
        <!-- Project Selection -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">Project</label>
          <select formControlName="projectId" (change)="onProjectChange()" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Select a project</option>
            <option *ngFor="let project of projects" [value]="project.id">{{ project.name }}</option>
          </select>
        </div>

        <!-- Version Input -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">Version</label>
          <input type="text" formControlName="version" placeholder="e.g., v1.2.3, latest, main"
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <!-- Applications Selection -->
        <div *ngIf="applications.length > 0" class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">Applications</label>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div *ngFor="let app of applications" 
                 class="border rounded-lg p-4 cursor-pointer transition-colors"
                 [class.border-blue-500]="selectedApplications.find(a => a.id === app.id)"
                 [class.bg-blue-50]="selectedApplications.find(a => a.id === app.id)"
                 [class.border-gray-200]="!selectedApplications.find(a => a.id === app.id)"
                 (click)="toggleApplicationSelection(app)">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="font-medium text-gray-900">{{ app.name }}</h3>
                  <p class="text-sm text-gray-500">{{ app.description }}</p>
                </div>
                <div class="ml-4">
                  <input type="checkbox" 
                         [checked]="selectedApplications.find(a => a.id === app.id)"
                         class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Components Selection -->
        <div *ngIf="components.length > 0" class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">Components</label>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div *ngFor="let component of components" 
                 class="border rounded-lg p-4 cursor-pointer transition-colors"
                 [class.border-blue-500]="selectedComponents.find(c => c.id === component.id)"
                 [class.bg-blue-50]="selectedComponents.find(c => c.id === component.id)"
                 [class.border-gray-200]="!selectedComponents.find(c => c.id === component.id)"
                 (click)="toggleComponentSelection(component)">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="font-medium text-gray-900">{{ component.name }}</h3>
                  <p class="text-sm text-gray-500">{{ component.description }}</p>
                  <p class="text-xs text-gray-400">Type: {{ component.type }}</p>
                </div>
                <div class="ml-4">
                  <input type="checkbox" 
                         [checked]="selectedComponents.find(c => c.id === component.id)"
                         class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 2: Choose Environment -->
      <div *ngIf="currentStep === 2" class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Choose Environment</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div *ngFor="let env of environments" 
               class="border rounded-lg p-4 cursor-pointer transition-colors"
               [class.border-blue-500]="deploymentForm.get('environmentId')?.value === env.id"
               [class.bg-blue-50]="deploymentForm.get('environmentId')?.value === env.id"
               [class.border-gray-200]="deploymentForm.get('environmentId')?.value !== env.id"
               (click)="deploymentForm.patchValue({environmentId: env.id}); onEnvironmentChange()">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="font-medium text-gray-900">{{ env.name }}</h3>
                <p class="text-sm text-gray-500">{{ env.description }}</p>
                <div class="mt-2 flex items-center space-x-2">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        [class.bg-green-100]="env.status === 'active'"
                        [class.text-green-800]="env.status === 'active'"
                        [class.bg-red-100]="env.status !== 'active'"
                        [class.text-red-800]="env.status !== 'active'">
                    {{ env.status }}
                  </span>
                  <span class="text-xs text-gray-400">{{ env.type }}</span>
                </div>
              </div>
              <div class="ml-4">
                <input type="radio" 
                       [checked]="deploymentForm.get('environmentId')?.value === env.id"
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 3: Configure Workflow -->
      <div *ngIf="currentStep === 3" class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Configure Workflow</h2>
        
        <!-- Workflow Selection -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">Workflow</label>
          <select formControlName="workflowId" (change)="onWorkflowChange()" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Select a workflow</option>
            <option *ngFor="let workflow of workflows" [value]="workflow.id">{{ workflow.name }}</option>
          </select>
        </div>

        <!-- Provider Selection -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">Provider</label>
          <select formControlName="providerType" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Select a provider</option>
            <option *ngFor="let provider of providers" [value]="provider.type">{{ provider.name }}</option>
          </select>
        </div>

        <!-- Deployment Strategy -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">Deployment Strategy</label>
          <select formControlName="strategy" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="rolling_update">Rolling Update</option>
            <option value="blue_green">Blue/Green</option>
            <option value="canary">Canary</option>
            <option value="recreate">Recreate</option>
          </select>
        </div>

        <!-- Dynamic Parameters -->
        <div *ngIf="workflowParameters.length > 0" class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Workflow Parameters</h3>
          <div formArrayName="parameters" class="space-y-4">
            <div *ngFor="let param of workflowParameters; let i = index" [formGroupName]="i" 
                 class="border border-gray-200 rounded-lg p-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ param.name }}
                <span *ngIf="param.required" class="text-red-500">*</span>
              </label>
              <p *ngIf="param.description" class="text-sm text-gray-500 mb-2">{{ param.description }}</p>
              
              <input *ngIf="param.type === 'string' || param.type === 'number'" 
                     [type]="param.type === 'number' ? 'number' : 'text'"
                     formControlName="value"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              
              <select *ngIf="param.type === 'select'" formControlName="value"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="">Select an option</option>
                <option *ngFor="let option of param.options" [value]="option.value">{{ option.label }}</option>
              </select>
              
              <div *ngIf="param.type === 'boolean'" class="flex items-center">
                <input type="checkbox" formControlName="value" 
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <span class="ml-2 text-sm text-gray-700">{{ param.label || 'Enable' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 4: Review & Deploy -->
      <div *ngIf="currentStep === 4" class="bg-white shadow rounded-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Review & Deploy</h2>
        
        <!-- Deployment Summary -->
        <div class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Deployment Summary</h3>
            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div>
                <dt class="text-sm font-medium text-gray-500">Project</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ selectedProject?.name }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Environment</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ selectedEnvironment?.name }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Version</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ deploymentForm.get('version')?.value }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Workflow</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ selectedWorkflow?.name }}</dd>
              </div>
            </dl>
          </div>

          <div *ngIf="selectedApplications.length > 0">
            <h4 class="text-md font-medium text-gray-900 mb-2">Applications ({{ selectedApplications.length }})</h4>
            <ul class="list-disc list-inside text-sm text-gray-600">
              <li *ngFor="let app of selectedApplications">{{ app.name }}</li>
            </ul>
          </div>

          <div *ngIf="selectedComponents.length > 0">
            <h4 class="text-md font-medium text-gray-900 mb-2">Components ({{ selectedComponents.length }})</h4>
            <ul class="list-disc list-inside text-sm text-gray-600">
              <li *ngFor="let comp of selectedComponents">{{ comp.name }}</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Navigation Buttons -->
      <div class="flex justify-between">
        <button type="button" 
                *ngIf="currentStep > 1"
                (click)="previousStep()"
                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          Previous
        </button>
        
        <div class="flex space-x-3">
          <button type="button" 
                  *ngIf="currentStep < totalSteps"
                  (click)="nextStep()"
                  [disabled]="!validateCurrentStep()"
                  class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
            Next
          </button>
          
          <button type="submit" 
                  *ngIf="currentStep === totalSteps"
                  [disabled]="!deploymentForm.valid || submitting"
                  class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed">
            <span *ngIf="!submitting">Deploy</span>
            <span *ngIf="submitting" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Deploying...
            </span>
          </button>
        </div>
      </div>
    </form>

    <!-- Loading Overlay -->
    <div *ngIf="loading" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
      <div class="bg-white p-6 rounded-lg shadow-lg">
        <div class="flex items-center">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Loading...
        </div>
      </div>
    </div>
  </div>
</div>
