<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow-sm border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Template Marketplace</h1>
          <p class="mt-2 text-gray-600">Discover and use pre-built workflow templates</p>
        </div>
        <div class="flex items-center space-x-4">
          <button
            (click)="toggleViewMode()"
            class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <i class="fas" [ngClass]="viewMode === 'grid' ? 'fa-list' : 'fa-th-large'" class="mr-2"></i>
            {{ viewMode === 'grid' ? 'List View' : 'Grid View' }}
          </button>
          <button
            (click)="toggleFilters()"
            class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <i class="fas fa-filter mr-2"></i>
            Filters
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Featured Templates Section -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" *ngIf="featuredTemplates.length > 0">
    <div class="mb-8">
      <h2 class="text-2xl font-bold text-gray-900 mb-4">Featured Templates</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div
          *ngFor="let template of featuredTemplates"
          class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 cursor-pointer"
          (click)="viewTemplate(template)"
        >
          <div class="p-6">
            <div class="flex items-start justify-between mb-4">
              <div class="flex items-center">
                <div
                  class="w-10 h-10 rounded-lg flex items-center justify-center text-white mr-3"
                  [style.background-color]="getCategoryColor(template.category)"
                >
                  <i class="fas" [ngClass]="'fa-' + getCategoryIcon(template.category)"></i>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">{{ template.name }}</h3>
                  <p class="text-sm text-gray-500">by {{ template.authorName || 'Anonymous' }}</p>
                </div>
              </div>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Featured
              </span>
            </div>
            
            <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ template.description }}</p>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex items-center mr-4">
                  <div class="flex">
                    <i
                      *ngFor="let star of getRatingStars(template.rating)"
                      class="fas fa-star text-sm"
                      [ngClass]="{
                        'text-yellow-400': star === 'full',
                        'text-yellow-200': star === 'half',
                        'text-gray-300': star === 'empty'
                      }"
                    ></i>
                  </div>
                  <span class="ml-1 text-sm text-gray-600">({{ template.ratingCount }})</span>
                </div>
                <span class="text-sm text-gray-500">{{ template.downloadCount }} downloads</span>
              </div>
              <button
                (click)="useTemplate(template); $event.stopPropagation()"
                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700"
              >
                Use Template
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex flex-col lg:flex-row gap-8">
      <!-- Sidebar Filters -->
      <div class="lg:w-64 flex-shrink-0" [ngClass]="{ 'hidden lg:block': !showFilters }">
        <div class="bg-white rounded-lg shadow-sm p-6 sticky top-4">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">Filters</h3>
            <button
              (click)="clearFilters()"
              class="text-sm text-blue-600 hover:text-blue-800"
            >
              Clear All
            </button>
          </div>

          <!-- Search -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <input
              type="text"
              [(ngModel)]="searchQuery"
              (ngModelChange)="onSearchChange()"
              placeholder="Search templates..."
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
          </div>

          <!-- Categories -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
            <select
              [(ngModel)]="selectedCategory"
              (ngModelChange)="onCategoryChange($event)"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Categories</option>
              <option *ngFor="let category of categories" [value]="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>

          <!-- Sort -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
            <select
              (change)="onSortChange(getSortValue($event))"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option *ngFor="let option of sortOptions" [value]="option.value">
                {{ option.label }}
              </option>
            </select>
          </div>

          <!-- Featured Toggle -->
          <div class="mb-6">
            <label class="flex items-center">
              <input
                type="checkbox"
                [(ngModel)]="showFeaturedOnly"
                (ngModelChange)="onFeaturedToggle()"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              >
              <span class="ml-2 text-sm text-gray-700">Featured only</span>
            </label>
          </div>

          <!-- Tags -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
            <div class="space-y-2">
              <label *ngFor="let tag of availableTags" class="flex items-center">
                <input
                  type="checkbox"
                  [checked]="selectedTags.includes(tag)"
                  (change)="onTagToggle(tag)"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                >
                <span class="ml-2 text-sm text-gray-700 capitalize">{{ tag }}</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1">
        <!-- Loading State -->
        <div *ngIf="loading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>

        <!-- Templates Grid/List -->
        <div *ngIf="!loading && templates.length > 0">
          <!-- Grid View -->
          <div *ngIf="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            <div
              *ngFor="let template of templates"
              class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 cursor-pointer"
              (click)="viewTemplate(template)"
            >
              <div class="p-6">
                <div class="flex items-start justify-between mb-4">
                  <div class="flex items-center">
                    <div
                      class="w-10 h-10 rounded-lg flex items-center justify-center text-white mr-3"
                      [style.background-color]="getCategoryColor(template.category)"
                    >
                      <i class="fas" [ngClass]="'fa-' + getCategoryIcon(template.category)"></i>
                    </div>
                    <div>
                      <h3 class="text-lg font-semibold text-gray-900">{{ template.name }}</h3>
                      <p class="text-sm text-gray-500">by {{ template.authorName || 'Anonymous' }}</p>
                    </div>
                  </div>
                  <span
                    *ngIf="template.isFeatured"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
                  >
                    Featured
                  </span>
                </div>
                
                <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ template.description }}</p>
                
                <!-- Tags -->
                <div class="flex flex-wrap gap-1 mb-4" *ngIf="template.tags.length > 0">
                  <span
                    *ngFor="let tag of template.tags.slice(0, 3)"
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                  >
                    {{ tag }}
                  </span>
                  <span
                    *ngIf="template.tags.length > 3"
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                  >
                    +{{ template.tags.length - 3 }}
                  </span>
                </div>
                
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="flex items-center mr-4">
                      <div class="flex">
                        <i
                          *ngFor="let star of getRatingStars(template.rating)"
                          class="fas fa-star text-sm"
                          [ngClass]="{
                            'text-yellow-400': star === 'full',
                            'text-yellow-200': star === 'half',
                            'text-gray-300': star === 'empty'
                          }"
                        ></i>
                      </div>
                      <span class="ml-1 text-sm text-gray-600">({{ template.ratingCount }})</span>
                    </div>
                    <span class="text-sm text-gray-500">{{ template.downloadCount }} downloads</span>
                  </div>
                  <div class="flex space-x-2">
                    <button
                      (click)="downloadTemplate(template); $event.stopPropagation()"
                      class="inline-flex items-center px-2 py-1 border border-gray-300 rounded text-xs font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <i class="fas fa-download mr-1"></i>
                      Download
                    </button>
                    <button
                      (click)="useTemplate(template); $event.stopPropagation()"
                      class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700"
                    >
                      Use
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- List View -->
          <div *ngIf="viewMode === 'list'" class="space-y-4">
            <div
              *ngFor="let template of templates"
              class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 cursor-pointer p-6"
              (click)="viewTemplate(template)"
            >
              <div class="flex items-start justify-between">
                <div class="flex items-start flex-1">
                  <div
                    class="w-12 h-12 rounded-lg flex items-center justify-center text-white mr-4"
                    [style.background-color]="getCategoryColor(template.category)"
                  >
                    <i class="fas" [ngClass]="'fa-' + getCategoryIcon(template.category)"></i>
                  </div>
                  <div class="flex-1">
                    <div class="flex items-center mb-2">
                      <h3 class="text-lg font-semibold text-gray-900 mr-3">{{ template.name }}</h3>
                      <span
                        *ngIf="template.isFeatured"
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
                      >
                        Featured
                      </span>
                    </div>
                    <p class="text-sm text-gray-500 mb-2">by {{ template.authorName || 'Anonymous' }}</p>
                    <p class="text-gray-600 text-sm mb-3">{{ template.description }}</p>
                    
                    <!-- Tags -->
                    <div class="flex flex-wrap gap-1 mb-3" *ngIf="template.tags.length > 0">
                      <span
                        *ngFor="let tag of template.tags"
                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                      >
                        {{ tag }}
                      </span>
                    </div>
                    
                    <div class="flex items-center">
                      <div class="flex items-center mr-6">
                        <div class="flex">
                          <i
                            *ngFor="let star of getRatingStars(template.rating)"
                            class="fas fa-star text-sm"
                            [ngClass]="{
                              'text-yellow-400': star === 'full',
                              'text-yellow-200': star === 'half',
                              'text-gray-300': star === 'empty'
                            }"
                          ></i>
                        </div>
                        <span class="ml-1 text-sm text-gray-600">({{ template.ratingCount }})</span>
                      </div>
                      <span class="text-sm text-gray-500 mr-6">{{ template.downloadCount }} downloads</span>
                      <span class="text-sm text-gray-500">{{ template.usageCount }} uses</span>
                    </div>
                  </div>
                </div>
                <div class="flex space-x-2 ml-4">
                  <button
                    (click)="downloadTemplate(template); $event.stopPropagation()"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <i class="fas fa-download mr-2"></i>
                    Download
                  </button>
                  <button
                    (click)="useTemplate(template); $event.stopPropagation()"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    Use Template
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div *ngIf="!loading && templates.length === 0" class="text-center py-12">
          <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
          <p class="text-gray-600">Try adjusting your search criteria or filters.</p>
        </div>
      </div>
    </div>
  </div>
</div>
