import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { WorkflowService } from '../../services/workflow.service';
import { ProjectService } from '../../services/project.service';
import { AuthService } from '../../services/auth.service';
import { WorkflowTemplate, TemplateCategory } from '../../models/workflow.model';
import { Project } from '../../models/project.model';

@Component({
  selector: 'app-template-marketplace',
  templateUrl: './template-marketplace.component.html',
  styleUrls: ['./template-marketplace.component.css']
})
export class TemplateMarketplaceComponent implements OnInit {
  templates: WorkflowTemplate[] = [];
  featuredTemplates: WorkflowTemplate[] = [];
  categories: TemplateCategory[] = [];
  projects: Project[] = [];

  // Filters
  selectedCategory: string = '';
  searchQuery: string = '';
  selectedTags: string[] = [];
  sortBy: string = 'rating';
  sortOrder: string = 'desc';
  showFeaturedOnly: boolean = false;

  // UI State
  loading: boolean = false;
  showFilters: boolean = false;
  viewMode: 'grid' | 'list' = 'grid';

  // Pagination
  currentPage: number = 1;
  pageSize: number = 12;
  totalTemplates: number = 0;

  // Available sort options
  sortOptions = [
    { value: 'rating', label: 'Rating', order: 'desc' },
    { value: 'download_count', label: 'Downloads', order: 'desc' },
    { value: 'usage_count', label: 'Usage', order: 'desc' },
    { value: 'created_at', label: 'Newest', order: 'desc' },
    { value: 'name', label: 'Name', order: 'asc' }
  ];

  // Available tags
  availableTags: string[] = [
    'deployment', 'ci-cd', 'monitoring', 'backup', 'security', 'testing',
    'docker', 'kubernetes', 'aws', 'azure', 'gcp', 'terraform', 'ansible'
  ];

  isAdmin = false;

  constructor(
    private workflowService: WorkflowService,
    private projectService: ProjectService,
    private authService: AuthService,
    private router: Router
  ) {
    this.isAdmin = this.authService.isAdmin();
  }

  ngOnInit(): void {
    this.loadInitialData();
  }

  private loadInitialData(): void {
    this.loading = true;

    // Load categories
    this.workflowService.getTemplateCategories().subscribe({
      next: (categories) => {
        this.categories = categories;
      },
      error: (error) => {
        console.error('Failed to load categories:', error);
      }
    });

    // Load featured templates
    this.workflowService.getFeaturedTemplates(6).subscribe({
      next: (templates) => {
        this.featuredTemplates = templates;
      },
      error: (error) => {
        console.error('Failed to load featured templates:', error);
      }
    });

    // Load projects for workflow creation
    this.projectService.getProjects().subscribe({
      next: (projects) => {
        this.projects = projects;
      },
      error: (error) => {
        console.error('Failed to load projects:', error);
      }
    });

    // Load templates
    this.loadTemplates();
  }

  loadTemplates(): void {
    this.loading = true;

    const filters: any = {
      category: this.selectedCategory || undefined,
      isFeatured: this.showFeaturedOnly || undefined,
      search: this.searchQuery || undefined,
      tags: this.selectedTags.length > 0 ? this.selectedTags : undefined,
      sortBy: this.sortBy,
      sortOrder: this.sortOrder,
      limit: this.pageSize,
      offset: (this.currentPage - 1) * this.pageSize
    };

    // For non-admin users, only show public templates
    if (!this.isAdmin) {
      filters.isPublic = true;
    }

    this.workflowService.getTemplates(filters).subscribe({
      next: (templates) => {
        this.templates = templates;
        this.loading = false;
      },
      error: (error) => {
        console.error('Failed to load templates:', error);
        this.loading = false;
      }
    });
  }

  onCategoryChange(categoryId: string): void {
    this.selectedCategory = categoryId;
    this.currentPage = 1;
    this.loadTemplates();
  }

  onSearchChange(): void {
    this.currentPage = 1;
    this.loadTemplates();
  }

  onTagToggle(tag: string): void {
    const index = this.selectedTags.indexOf(tag);
    if (index > -1) {
      this.selectedTags.splice(index, 1);
    } else {
      this.selectedTags.push(tag);
    }
    this.currentPage = 1;
    this.loadTemplates();
  }

  onSortChange(sortOption: any): void {
    this.sortBy = sortOption.value;
    this.sortOrder = sortOption.order;
    this.currentPage = 1;
    this.loadTemplates();
  }

  onFeaturedToggle(): void {
    this.showFeaturedOnly = !this.showFeaturedOnly;
    this.currentPage = 1;
    this.loadTemplates();
  }

  clearFilters(): void {
    this.selectedCategory = '';
    this.searchQuery = '';
    this.selectedTags = [];
    this.showFeaturedOnly = false;
    this.sortBy = 'rating';
    this.sortOrder = 'desc';
    this.currentPage = 1;
    this.loadTemplates();
  }

  viewTemplate(template: WorkflowTemplate): void {
    this.router.navigate(['/templates', template.id]);
  }

  downloadTemplate(template: WorkflowTemplate): void {
    this.workflowService.downloadTemplate(template.id).subscribe({
      next: (downloadedTemplate) => {
        // Update the template with new download count
        const index = this.templates.findIndex(t => t.id === template.id);
        if (index > -1) {
          this.templates[index] = downloadedTemplate;
        }

        // Show success message or handle download
        console.log('Template downloaded successfully');
      },
      error: (error) => {
        console.error('Failed to download template:', error);
      }
    });
  }

  useTemplate(template: WorkflowTemplate): void {
    if (this.projects.length === 0) {
      alert('No projects available. Please create a project first.');
      return;
    }

    // For now, use the first project. In a real app, show a project selector
    const projectId = this.projects[0].id;

    const request = {
      name: `${template.name} - Copy`,
      description: `Workflow created from template: ${template.name}`,
      projectId: projectId
    };

    this.workflowService.createWorkflowFromTemplate(template.id, request).subscribe({
      next: (workflow) => {
        console.log('Workflow created successfully:', workflow);
        this.router.navigate(['/workflows', workflow.id]);
      },
      error: (error) => {
        console.error('Failed to create workflow from template:', error);
      }
    });
  }

  getRatingStars(rating: number): string[] {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 0; i < fullStars; i++) {
      stars.push('full');
    }

    if (hasHalfStar) {
      stars.push('half');
    }

    while (stars.length < 5) {
      stars.push('empty');
    }

    return stars;
  }

  getCategoryIcon(categoryId: string): string {
    const category = this.categories.find(c => c.id === categoryId);
    return category?.icon || 'folder';
  }

  getCategoryColor(categoryId: string): string {
    const category = this.categories.find(c => c.id === categoryId);
    return category?.color || '#6B7280';
  }

  getSortValue($event: any): any {
    return this.sortOptions.find(o => o.value === ($event.target).value)
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadTemplates();
  }

  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'grid' ? 'list' : 'grid';
  }

  toggleFilters(): void {
    this.showFilters = !this.showFilters;
  }
}
