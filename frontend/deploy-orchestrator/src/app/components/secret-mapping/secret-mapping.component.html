<div class="secret-mapping-container">
  <!-- Header -->
  <div class="flex items-center justify-between mb-6">
    <div>
      <h3 class="text-lg font-semibold text-gray-900">Secret Mapping</h3>
      <p class="text-sm text-gray-600 mt-1">
        Map template variables to your project's secrets
      </p>
    </div>
    
    <div class="flex items-center space-x-3">
      <button
        type="button"
        (click)="toggleRecommendations()"
        class="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md transition-colors"
        [class]="showRecommendations 
          ? 'bg-blue-100 text-blue-700 hover:bg-blue-200' 
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
      >
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
        </svg>
        {{ showRecommendations ? 'Hide' : 'Show' }} Recommendations
      </button>
      
      <button
        type="button"
        (click)="clearMapping()"
        class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
      >
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        Clear All
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex items-center justify-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <span class="ml-3 text-gray-600">Loading project secrets...</span>
  </div>

  <!-- Secret Mapping Form -->
  <form [formGroup]="secretMappingForm" *ngIf="!isLoading" class="space-y-6">
    
    <!-- No Secrets Required Message -->
    <div *ngIf="secretConfigs.length === 0" class="text-center py-8">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No Secret Mapping Required</h3>
      <p class="mt-1 text-sm text-gray-500">This template doesn't require any secret mappings.</p>
    </div>

    <!-- Secret Mapping Fields -->
    <div *ngFor="let config of secretConfigs" class="secret-mapping-field">
      <div class="bg-white border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors">
        
        <!-- Field Header -->
        <div class="flex items-start justify-between mb-3">
          <div class="flex-1">
            <label [for]="config.templateVariable" class="block text-sm font-medium text-gray-900">
              {{ config.templateVariable }}
              <span *ngIf="config.required" class="text-red-500 ml-1">*</span>
            </label>
            <p *ngIf="config.description" class="text-xs text-gray-600 mt-1">
              {{ config.description }}
            </p>
          </div>
          
          <!-- Status Indicator -->
          <div class="ml-3">
            <span *ngIf="secretMappingForm.get(config.templateVariable)?.value" 
                  class="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-700 rounded-full">
              <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              Mapped
            </span>
            <span *ngIf="!secretMappingForm.get(config.templateVariable)?.value && config.required" 
                  class="inline-flex items-center px-2 py-1 text-xs font-medium bg-red-100 text-red-700 rounded-full">
              <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
              Required
            </span>
          </div>
        </div>

        <!-- Secret Selection Dropdown -->
        <div class="mb-3">
          <select
            [id]="config.templateVariable"
            [formControlName]="config.templateVariable"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            [class.border-red-300]="secretMappingForm.get(config.templateVariable)?.invalid && secretMappingForm.get(config.templateVariable)?.touched"
          >
            <option value="">Select a secret...</option>
            <option *ngFor="let secret of config.userSecrets" [value]="secret.name">
              {{ secret.name }}
              <span *ngIf="secret.description"> - {{ secret.description }}</span>
              <span *ngIf="secret.environment"> ({{ secret.environment }})</span>
            </option>
          </select>
        </div>

        <!-- Recommendation -->
        <div *ngIf="showRecommendations && getRecommendationForVariable(config.templateVariable)" 
             class="mb-3 p-2 bg-blue-50 border border-blue-200 rounded-md">
          <div class="flex items-start">
            <svg class="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            <p class="text-xs text-blue-700">
              {{ getRecommendationForVariable(config.templateVariable) }}
            </p>
          </div>
        </div>

        <!-- Validation Errors -->
        <div *ngIf="getValidationErrorsForVariable(config.templateVariable).length > 0" 
             class="space-y-1">
          <div *ngFor="let error of getValidationErrorsForVariable(config.templateVariable)" 
               class="flex items-start p-2 bg-red-50 border border-red-200 rounded-md">
            <svg class="w-4 h-4 text-red-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <p class="text-xs text-red-700">{{ error }}</p>
          </div>
        </div>

        <!-- Secret Details -->
        <div *ngIf="secretMappingForm.get(config.templateVariable)?.value" class="mt-3 pt-3 border-t border-gray-100">
          <div class="text-xs text-gray-600">
            <div class="grid grid-cols-2 gap-2">
              <div>
                <span class="font-medium">Selected:</span> 
                {{ secretMappingForm.get(config.templateVariable)?.value }}
              </div>
              <div *ngIf="config.userSecrets.find(s => s.name === secretMappingForm.get(config.templateVariable)?.value)?.type">
                <span class="font-medium">Type:</span> 
                {{ config.userSecrets.find(s => s.name === secretMappingForm.get(config.templateVariable)?.value)?.type }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>

  <!-- Validation Summary -->
  <div *ngIf="validation && !isLoading" class="mt-6 p-4 rounded-lg"
       [class]="validation.isValid ? 'bg-green-50 border border-green-200' : 'bg-yellow-50 border border-yellow-200'">
    <div class="flex items-start">
      <svg *ngIf="validation.isValid" class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
      </svg>
      <svg *ngIf="!validation.isValid" class="w-5 h-5 text-yellow-500 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
      </svg>
      <div class="flex-1">
        <h4 class="text-sm font-medium" 
            [class]="validation.isValid ? 'text-green-800' : 'text-yellow-800'">
          {{ validation.isValid ? 'Secret mapping is valid' : 'Secret mapping needs attention' }}
        </h4>
        <div *ngIf="validation.missingMappings.length > 0" class="mt-2">
          <p class="text-sm text-yellow-700">
            Missing mappings for: {{ validation.missingMappings.join(', ') }}
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
