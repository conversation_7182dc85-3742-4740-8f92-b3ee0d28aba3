import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { WorkflowTemplate, WorkflowParameter, SecretMappingConfig, UserSecret, SecretMappingValidation } from '../../models/workflow-execution.interface';
import { SecretsService } from '../../services/secrets.service';
import { NotificationService } from '../../services/notification.service';

@Component({
  selector: 'app-secret-mapping',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './secret-mapping.component.html',
  styleUrls: ['./secret-mapping.component.css']
})
export class SecretMappingComponent implements OnInit, OnChanges {
  @Input() template!: WorkflowTemplate;
  @Input() projectId!: string;
  @Input() initialMapping: { [key: string]: string } = {};
  @Output() mappingChange = new EventEmitter<{ [key: string]: string }>();
  @Output() validationChange = new EventEmitter<boolean>();

  secretMappingForm!: FormGroup;
  secretConfigs: SecretMappingConfig[] = [];
  projectSecrets: UserSecret[] = [];
  validation: SecretMappingValidation | null = null;
  isLoading = false;
  showRecommendations = true;

  constructor(
    private fb: FormBuilder,
    private secretsService: SecretsService,
    private notificationService: NotificationService
  ) {}

  ngOnInit() {
    this.initializeForm();
    this.loadProjectSecrets();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['template'] || changes['projectId']) {
      this.initializeSecretConfigs();
      this.loadProjectSecrets();
    }
    if (changes['initialMapping']) {
      this.updateFormWithMapping();
    }
  }

  private initializeForm() {
    this.secretMappingForm = this.fb.group({});
    this.initializeSecretConfigs();
  }

  private initializeSecretConfigs() {
    if (!this.template) return;

    this.secretConfigs = this.template.parameters
      .filter(param => param.type === 'secret' || param.secretHint)
      .map(param => ({
        templateVariable: param.name,
        description: param.description,
        required: param.required,
        userSecrets: [],
        selectedSecret: this.initialMapping[param.name]
      }));

    // Add form controls for each secret mapping
    const formControls: { [key: string]: any } = {};
    this.secretConfigs.forEach(config => {
      const validators = config.required ? [Validators.required] : [];
      formControls[config.templateVariable] = [config.selectedSecret || '', validators];
    });

    this.secretMappingForm = this.fb.group(formControls);

    // Listen for form changes
    this.secretMappingForm.valueChanges.subscribe(value => {
      this.onMappingChange(value);
    });
  }

  private async loadProjectSecrets() {
    if (!this.projectId) return;

    this.isLoading = true;
    try {
      const response = await this.secretsService.getProjectSecrets(this.projectId).toPromise();
      this.projectSecrets = response?.secrets || [];
      
      // Update secret configs with available secrets
      this.secretConfigs.forEach(config => {
        config.userSecrets = this.projectSecrets.filter(secret => 
          this.isSecretCompatible(secret, config.templateVariable)
        );
      });

      // Validate current mapping
      await this.validateMapping();
      
    } catch (error) {
      console.error('Failed to load project secrets:', error);
      this.notificationService.error('Failed to load project secrets');
    } finally {
      this.isLoading = false;
    }
  }

  private isSecretCompatible(secret: UserSecret, templateVariable: string): boolean {
    // Basic compatibility check based on naming patterns
    const secretName = secret.name.toLowerCase();
    const templateVar = templateVariable.toLowerCase();

    // Direct name match
    if (secretName === templateVar) return true;

    // Pattern matching for common secret types
    if (templateVar.includes('username') && (secretName.includes('user') || secretName.includes('username'))) {
      return true;
    }
    if (templateVar.includes('password') && (secretName.includes('pass') || secretName.includes('password'))) {
      return true;
    }
    if (templateVar.includes('key') && (secretName.includes('key') || secretName.includes('private'))) {
      return true;
    }
    if (templateVar.includes('token') && (secretName.includes('token') || secretName.includes('api'))) {
      return true;
    }

    return true; // Allow all secrets for maximum flexibility
  }

  private updateFormWithMapping() {
    if (!this.secretMappingForm) return;

    Object.keys(this.initialMapping).forEach(key => {
      if (this.secretMappingForm.get(key)) {
        this.secretMappingForm.get(key)?.setValue(this.initialMapping[key]);
      }
    });
  }

  private async validateMapping() {
    if (!this.template || !this.projectId) return;

    try {
      this.validation = await this.secretsService.validateSecretMapping(
        this.template.id,
        this.projectId
      ).toPromise();
      
      this.validationChange.emit(this.validation?.isValid || false);
      
      // Apply recommendations if available
      if (this.validation?.recommendations?.length > 0 && this.showRecommendations) {
        this.applyRecommendations();
      }
      
    } catch (error) {
      console.error('Failed to validate secret mapping:', error);
    }
  }

  private applyRecommendations() {
    if (!this.validation?.recommendations) return;

    this.validation.recommendations.forEach(rec => {
      if (rec.confidence > 0.8 && !this.secretMappingForm.get(rec.templateVariable)?.value) {
        this.secretMappingForm.get(rec.templateVariable)?.setValue(rec.recommendedSecret);
      }
    });
  }

  onMappingChange(mapping: { [key: string]: string }) {
    // Filter out empty values
    const cleanMapping = Object.keys(mapping)
      .filter(key => mapping[key])
      .reduce((obj, key) => {
        obj[key] = mapping[key];
        return obj;
      }, {} as { [key: string]: string });

    this.mappingChange.emit(cleanMapping);
    this.validateMapping();
  }

  onSecretSelect(templateVariable: string, secretName: string) {
    this.secretMappingForm.get(templateVariable)?.setValue(secretName);
  }

  getRecommendationForVariable(templateVariable: string): string {
    if (!this.validation?.recommendations) return '';
    
    const rec = this.validation.recommendations.find(r => r.templateVariable === templateVariable);
    return rec ? `Recommended: ${rec.recommendedSecret} (${Math.round(rec.confidence * 100)}% match)` : '';
  }

  getValidationErrorsForVariable(templateVariable: string): string[] {
    const errors: string[] = [];
    const control = this.secretMappingForm.get(templateVariable);
    
    if (control?.errors) {
      if (control.errors['required']) {
        errors.push('This secret mapping is required');
      }
    }

    if (this.validation?.missingMappings.includes(templateVariable)) {
      errors.push('No compatible secret found in project');
    }

    return errors;
  }

  toggleRecommendations() {
    this.showRecommendations = !this.showRecommendations;
    if (this.showRecommendations && this.validation?.recommendations) {
      this.applyRecommendations();
    }
  }

  clearMapping() {
    this.secretMappingForm.reset();
    this.mappingChange.emit({});
  }

  isFormValid(): boolean {
    return this.secretMappingForm.valid && (this.validation?.isValid ?? false);
  }

  getMappingValue(): { [key: string]: string } {
    return this.secretMappingForm.value;
  }
}
