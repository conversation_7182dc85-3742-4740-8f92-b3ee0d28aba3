.secret-mapping-container {
  @apply max-w-4xl mx-auto;
}

.secret-mapping-field {
  @apply transition-all duration-200;
}

.secret-mapping-field:hover {
  @apply transform translate-y-0;
}

/* Custom select styling */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Focus states */
select:focus {
  @apply ring-2 ring-blue-500 ring-opacity-50 border-blue-500;
}

/* Error states */
select.error {
  @apply border-red-300 focus:border-red-500 focus:ring-red-500;
}

/* Animation for recommendations */
.recommendation-enter {
  @apply opacity-0 transform scale-95;
}

.recommendation-enter-active {
  @apply opacity-100 transform scale-100 transition-all duration-200;
}

.recommendation-exit {
  @apply opacity-100 transform scale-100;
}

.recommendation-exit-active {
  @apply opacity-0 transform scale-95 transition-all duration-200;
}

/* Status indicators */
.status-indicator {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium rounded-full;
}

.status-mapped {
  @apply bg-green-100 text-green-700;
}

.status-required {
  @apply bg-red-100 text-red-700;
}

.status-optional {
  @apply bg-gray-100 text-gray-700;
}

/* Validation messages */
.validation-message {
  @apply flex items-start p-2 rounded-md text-xs;
}

.validation-error {
  @apply bg-red-50 border border-red-200 text-red-700;
}

.validation-warning {
  @apply bg-yellow-50 border border-yellow-200 text-yellow-700;
}

.validation-info {
  @apply bg-blue-50 border border-blue-200 text-blue-700;
}

.validation-success {
  @apply bg-green-50 border border-green-200 text-green-700;
}

/* Loading spinner */
.loading-spinner {
  @apply animate-spin rounded-full border-b-2 border-blue-600;
}

/* Responsive design */
@media (max-width: 640px) {
  .secret-mapping-container {
    @apply px-4;
  }

  .secret-mapping-field {
    @apply mb-4;
  }

  .grid-cols-2 {
    @apply grid-cols-1;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .secret-mapping-container {
    @apply text-gray-100;
  }

  .bg-white {
    @apply bg-gray-800;
  }

  .border-gray-200 {
    @apply border-gray-700;
  }

  .text-gray-900 {
    @apply text-gray-100;
  }

  .text-gray-600 {
    @apply text-gray-400;
  }

  select {
    @apply bg-gray-800 text-gray-100 border-gray-700;
  }
}
