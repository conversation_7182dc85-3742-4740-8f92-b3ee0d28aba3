import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';

import { SecretMappingComponent } from './secret-mapping.component';
import { SecretsService } from '../../services/secrets.service';
import { NotificationService } from '../../services/notification.service';
import { WorkflowTemplate, WorkflowParameter, UserSecret, SecretMappingValidation } from '../../models/workflow-execution.interface';

describe('SecretMappingComponent', () => {
  let component: SecretMappingComponent;
  let fixture: ComponentFixture<SecretMappingComponent>;
  let mockSecretsService: jasmine.SpyObj<SecretsService>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;

  const mockTemplate: WorkflowTemplate = {
    id: 'test-template',
    name: 'Test Template',
    description: 'Test template description',
    category: 'deployment',
    isPublic: true,
    isFeatured: false,
    createdBy: 'test-user',
    authorName: 'Test Author',
    authorEmail: '<EMAIL>',
    version: '1.0.0',
    steps: [],
    parameters: [
      {
        name: 'SSH_USERNAME',
        type: 'secret',
        description: 'SSH username for deployment',
        required: true,
        secretHint: 'SSH_USERNAME'
      },
      {
        name: 'SSH_PRIVATE_KEY',
        type: 'secret',
        description: 'SSH private key for deployment',
        required: true,
        secretHint: 'SSH_PRIVATE_KEY'
      },
      {
        name: 'OPTIONAL_TOKEN',
        type: 'secret',
        description: 'Optional API token',
        required: false,
        secretHint: 'OPTIONAL_TOKEN'
      }
    ],
    variables: {},
    tags: [],
    usageCount: 0,
    downloadCount: 0,
    rating: 0,
    ratingCount: 0,
    documentation: '',
    requirements: [],
    screenshots: [],
    license: 'MIT',
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const mockProjectSecrets: UserSecret[] = [
    {
      id: 'secret-1',
      name: 'DEV_USERNAME',
      description: 'Development SSH username',
      type: 'username',
      environment: 'dev',
      service: 'ssh',
      createdAt: new Date()
    },
    {
      id: 'secret-2',
      name: 'DEV_KEY',
      description: 'Development SSH private key',
      type: 'private_key',
      environment: 'dev',
      service: 'ssh',
      createdAt: new Date()
    },
    {
      id: 'secret-3',
      name: 'QA_USERNAME',
      description: 'QA SSH username',
      type: 'username',
      environment: 'qa',
      service: 'ssh',
      createdAt: new Date()
    },
    {
      id: 'secret-4',
      name: 'API_TOKEN',
      description: 'API token for external service',
      type: 'token',
      environment: '',
      service: 'api',
      createdAt: new Date()
    }
  ];

  beforeEach(async () => {
    const secretsServiceSpy = jasmine.createSpyObj('SecretsService', [
      'getProjectSecrets',
      'validateSecretMapping',
      'isSecretCompatibleWithTemplate'
    ]);
    const notificationServiceSpy = jasmine.createSpyObj('NotificationService', [
      'showError',
      'showSuccess',
      'showWarning'
    ]);

    await TestBed.configureTestingModule({
      imports: [SecretMappingComponent, ReactiveFormsModule, HttpClientTestingModule],
      providers: [
        { provide: SecretsService, useValue: secretsServiceSpy },
        { provide: NotificationService, useValue: notificationServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(SecretMappingComponent);
    component = fixture.componentInstance;
    mockSecretsService = TestBed.inject(SecretsService) as jasmine.SpyObj<SecretsService>;
    mockNotificationService = TestBed.inject(NotificationService) as jasmine.SpyObj<NotificationService>;

    // Setup default mocks
    mockSecretsService.getProjectSecrets.and.returnValue(Promise.resolve(mockProjectSecrets));
    mockSecretsService.isSecretCompatibleWithTemplate.and.returnValue(true);
    mockSecretsService.validateSecretMapping.and.returnValue(Promise.resolve({
      isValid: true,
      missingMappings: [],
      availableSecrets: mockProjectSecrets,
      recommendations: []
    }));

    // Set component inputs
    component.template = mockTemplate;
    component.projectId = 'test-project';
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize secret configs from template parameters', () => {
    component.ngOnInit();

    expect(component.secretConfigs).toHaveSize(3);
    expect(component.secretConfigs[0].templateVariable).toBe('SSH_USERNAME');
    expect(component.secretConfigs[0].required).toBe(true);
    expect(component.secretConfigs[1].templateVariable).toBe('SSH_PRIVATE_KEY');
    expect(component.secretConfigs[1].required).toBe(true);
    expect(component.secretConfigs[2].templateVariable).toBe('OPTIONAL_TOKEN');
    expect(component.secretConfigs[2].required).toBe(false);
  });

  it('should load project secrets on initialization', async () => {
    await component.ngOnInit();

    expect(mockSecretsService.getProjectSecrets).toHaveBeenCalledWith('test-project');
    expect(component.projectSecrets).toEqual(mockProjectSecrets);
  });

  it('should filter compatible secrets for each template variable', async () => {
    mockSecretsService.isSecretCompatibleWithTemplate.and.callFake((secret, templateVar) => {
      if (templateVar === 'SSH_USERNAME') {
        return secret.name.includes('USERNAME');
      }
      if (templateVar === 'SSH_PRIVATE_KEY') {
        return secret.name.includes('KEY');
      }
      return true;
    });

    await component.ngOnInit();

    const usernameConfig = component.secretConfigs.find(c => c.templateVariable === 'SSH_USERNAME');
    const keyConfig = component.secretConfigs.find(c => c.templateVariable === 'SSH_PRIVATE_KEY');

    expect(usernameConfig?.userSecrets).toHaveSize(2); // DEV_USERNAME, QA_USERNAME
    expect(keyConfig?.userSecrets).toHaveSize(1); // DEV_KEY
  });

  it('should emit mapping changes when form values change', () => {
    spyOn(component.mappingChange, 'emit');
    component.ngOnInit();

    component.secretMappingForm.patchValue({
      'SSH_USERNAME': 'DEV_USERNAME',
      'SSH_PRIVATE_KEY': 'DEV_KEY'
    });

    expect(component.mappingChange.emit).toHaveBeenCalledWith({
      'SSH_USERNAME': 'DEV_USERNAME',
      'SSH_PRIVATE_KEY': 'DEV_KEY'
    });
  });

  it('should validate form and emit validation status', async () => {
    spyOn(component.validationChange, 'emit');
    
    const mockValidation: SecretMappingValidation = {
      isValid: true,
      missingMappings: [],
      availableSecrets: mockProjectSecrets,
      recommendations: []
    };
    mockSecretsService.validateSecretMapping.and.returnValue(Promise.resolve(mockValidation));

    await component.ngOnInit();

    expect(component.validationChange.emit).toHaveBeenCalledWith(true);
  });

  it('should handle validation errors gracefully', async () => {
    const mockValidation: SecretMappingValidation = {
      isValid: false,
      missingMappings: ['SSH_USERNAME'],
      availableSecrets: mockProjectSecrets,
      recommendations: []
    };
    mockSecretsService.validateSecretMapping.and.returnValue(Promise.resolve(mockValidation));

    await component.ngOnInit();

    expect(component.validation?.isValid).toBe(false);
    expect(component.validation?.missingMappings).toContain('SSH_USERNAME');
  });

  it('should apply recommendations when available', async () => {
    const mockValidation: SecretMappingValidation = {
      isValid: true,
      missingMappings: [],
      availableSecrets: mockProjectSecrets,
      recommendations: [
        {
          templateVariable: 'SSH_USERNAME',
          recommendedSecret: 'DEV_USERNAME',
          confidence: 0.9,
          reason: 'Username pattern match'
        }
      ]
    };
    mockSecretsService.validateSecretMapping.and.returnValue(Promise.resolve(mockValidation));

    component.showRecommendations = true;
    await component.ngOnInit();

    expect(component.secretMappingForm.get('SSH_USERNAME')?.value).toBe('DEV_USERNAME');
  });

  it('should not apply low confidence recommendations', async () => {
    const mockValidation: SecretMappingValidation = {
      isValid: true,
      missingMappings: [],
      availableSecrets: mockProjectSecrets,
      recommendations: [
        {
          templateVariable: 'SSH_USERNAME',
          recommendedSecret: 'DEV_USERNAME',
          confidence: 0.5, // Low confidence
          reason: 'Partial match'
        }
      ]
    };
    mockSecretsService.validateSecretMapping.and.returnValue(Promise.resolve(mockValidation));

    component.showRecommendations = true;
    await component.ngOnInit();

    expect(component.secretMappingForm.get('SSH_USERNAME')?.value).toBe('');
  });

  it('should handle initial mapping values', () => {
    component.initialMapping = {
      'SSH_USERNAME': 'DEV_USERNAME',
      'SSH_PRIVATE_KEY': 'DEV_KEY'
    };

    component.ngOnInit();

    expect(component.secretMappingForm.get('SSH_USERNAME')?.value).toBe('DEV_USERNAME');
    expect(component.secretMappingForm.get('SSH_PRIVATE_KEY')?.value).toBe('DEV_KEY');
  });

  it('should clear all mappings when clearMapping is called', () => {
    spyOn(component.mappingChange, 'emit');
    component.ngOnInit();

    component.secretMappingForm.patchValue({
      'SSH_USERNAME': 'DEV_USERNAME',
      'SSH_PRIVATE_KEY': 'DEV_KEY'
    });

    component.clearMapping();

    expect(component.secretMappingForm.get('SSH_USERNAME')?.value).toBe('');
    expect(component.secretMappingForm.get('SSH_PRIVATE_KEY')?.value).toBe('');
    expect(component.mappingChange.emit).toHaveBeenCalledWith({});
  });

  it('should validate required fields correctly', () => {
    component.ngOnInit();

    // Required field should be invalid when empty
    expect(component.secretMappingForm.get('SSH_USERNAME')?.valid).toBe(false);
    expect(component.secretMappingForm.get('SSH_PRIVATE_KEY')?.valid).toBe(false);

    // Optional field should be valid when empty
    expect(component.secretMappingForm.get('OPTIONAL_TOKEN')?.valid).toBe(true);

    // Required field should be valid when filled
    component.secretMappingForm.patchValue({
      'SSH_USERNAME': 'DEV_USERNAME',
      'SSH_PRIVATE_KEY': 'DEV_KEY'
    });

    expect(component.secretMappingForm.get('SSH_USERNAME')?.valid).toBe(true);
    expect(component.secretMappingForm.get('SSH_PRIVATE_KEY')?.valid).toBe(true);
  });

  it('should return correct validation errors for variables', () => {
    component.ngOnInit();
    component.validation = {
      isValid: false,
      missingMappings: ['SSH_USERNAME'],
      availableSecrets: mockProjectSecrets,
      recommendations: []
    };

    const errors = component.getValidationErrorsForVariable('SSH_USERNAME');
    expect(errors).toContain('This secret mapping is required');
    expect(errors).toContain('No compatible secret found in project');
  });

  it('should handle service errors gracefully', async () => {
    mockSecretsService.getProjectSecrets.and.returnValue(Promise.reject(new Error('Service error')));

    await component.ngOnInit();

    expect(mockNotificationService.showError).toHaveBeenCalledWith('Failed to load project secrets');
    expect(component.isLoading).toBe(false);
  });

  it('should toggle recommendations visibility', () => {
    component.showRecommendations = true;
    component.toggleRecommendations();
    expect(component.showRecommendations).toBe(false);

    component.toggleRecommendations();
    expect(component.showRecommendations).toBe(true);
  });

  it('should return correct form validity status', () => {
    component.ngOnInit();
    component.validation = { isValid: true, missingMappings: [], availableSecrets: [], recommendations: [] };

    // Form should be invalid when required fields are empty
    expect(component.isFormValid()).toBe(false);

    // Form should be valid when required fields are filled and validation passes
    component.secretMappingForm.patchValue({
      'SSH_USERNAME': 'DEV_USERNAME',
      'SSH_PRIVATE_KEY': 'DEV_KEY'
    });

    expect(component.isFormValid()).toBe(true);
  });

  it('should get correct mapping value', () => {
    component.ngOnInit();
    component.secretMappingForm.patchValue({
      'SSH_USERNAME': 'DEV_USERNAME',
      'SSH_PRIVATE_KEY': 'DEV_KEY',
      'OPTIONAL_TOKEN': ''
    });

    const mapping = component.getMappingValue();
    expect(mapping).toEqual({
      'SSH_USERNAME': 'DEV_USERNAME',
      'SSH_PRIVATE_KEY': 'DEV_KEY',
      'OPTIONAL_TOKEN': ''
    });
  });
});
