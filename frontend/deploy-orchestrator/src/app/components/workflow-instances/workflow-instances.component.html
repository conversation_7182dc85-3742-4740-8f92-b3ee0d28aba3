<div class="p-6">
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-900 mb-2">Workflow Instances</h1>
    <p class="text-gray-600">Manage and label workflow service instances</p>
  </div>

  <!-- Filters and Search -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
      <!-- Search -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (input)="onSearchChange()"
          placeholder="Search instances..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <!-- Status Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
        <select
          [(ngModel)]="statusFilter"
          (change)="onFilterChange()"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">All Statuses</option>
          <option *ngFor="let status of getUniqueValues('status')" [value]="status">{{ status | titlecase }}</option>
        </select>
      </div>

      <!-- OS Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Operating System</label>
        <select
          [(ngModel)]="osFilter"
          (change)="onFilterChange()"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">All OS</option>
          <option *ngFor="let os of getUniqueValues('os')" [value]="os">{{ os | titlecase }}</option>
        </select>
      </div>

      <!-- Clear Filters -->
      <div class="flex items-end">
        <button
          (click)="clearFilters()"
          class="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Clear Filters
        </button>
      </div>
    </div>

    <!-- Label Filters -->
    <div *ngIf="hasAvailableLabels()" class="border-t pt-4">
      <h3 class="text-sm font-medium text-gray-700 mb-2">Filter by Labels</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div *ngFor="let labelKey of getAvailableLabelKeys()">
          <label class="block text-sm font-medium text-gray-600 mb-1">{{ labelKey }}</label>
          <select
            [value]="labelFilters[labelKey] || ''"
            (change)="onLabelFilterChange(labelKey, $event)"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Any {{ labelKey }}</option>
            <option *ngFor="let value of availableLabels[labelKey]" [value]="value">{{ value }}</option>
          </select>
        </div>
      </div>
    </div>
  </div>

  <!-- Bulk Actions -->
  <div *ngIf="selectedInstances.size > 0" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <span class="text-sm font-medium text-blue-900">{{ selectedInstances.size }} instance(s) selected</span>
      </div>
      <div class="flex space-x-2">
        <button
          (click)="openBulkLabelModal()"
          class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Bulk Label
        </button>
        <button
          (click)="clearSelection()"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Clear Selection
        </button>
      </div>
    </div>
  </div>

  <!-- Instance List -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
      <h2 class="text-lg font-medium text-gray-900">Instances ({{ filteredInstances.length }})</h2>
      <div class="flex space-x-2">
        <button
          (click)="selectAllInstances()"
          class="px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Select All
        </button>
        <button
          (click)="loadInstances()"
          class="px-3 py-1 text-sm font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Refresh
        </button>
      </div>
    </div>

    <div *ngIf="loading" class="p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <p class="mt-2 text-gray-600">Loading instances...</p>
    </div>

    <div *ngIf="!loading && filteredInstances.length === 0" class="p-8 text-center text-gray-500">
      No instances found matching your criteria.
    </div>

    <div *ngIf="!loading && filteredInstances.length > 0" class="divide-y divide-gray-200">
      <div *ngFor="let instance of filteredInstances" class="p-4 hover:bg-gray-50">
        <div class="flex items-start space-x-4">
          <!-- Selection Checkbox -->
          <input
            type="checkbox"
            [checked]="selectedInstances.has(instance.id)"
            (change)="toggleInstanceSelection(instance.id)"
            class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />

          <!-- Instance Info -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-3">
                <h3 class="text-lg font-medium text-gray-900 truncate">{{ instance.name }}</h3>
                <span [class]="'px-2 py-1 text-xs font-medium rounded-full ' + getStatusBadgeClass(instance.status)">
                  {{ instance.status | titlecase }}
                </span>
              </div>
              <button
                (click)="openLabelModal(instance)"
                class="px-3 py-1 text-sm font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Manage Labels
              </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-3">
              <div>
                <span class="text-sm text-gray-500">OS:</span>
                <span class="ml-1 text-sm font-medium text-gray-900">{{ instance.os }} ({{ instance.architecture }})</span>
              </div>
              <div>
                <span class="text-sm text-gray-500">CPU:</span>
                <span class="ml-1 text-sm font-medium text-gray-900">{{ instance.resources.cpuCores }} cores</span>
              </div>
              <div>
                <span class="text-sm text-gray-500">Memory:</span>
                <span class="ml-1 text-sm font-medium text-gray-900">{{ (instance.resources.memoryMB / 1024) | number:'1.1-1' }} GB</span>
              </div>
              <div>
                <span class="text-sm text-gray-500">Last Seen:</span>
                <span class="ml-1 text-sm font-medium text-gray-900">{{ formatLastSeen(instance.lastSeen) }}</span>
              </div>
            </div>

            <!-- Resource Usage -->
            <div class="mb-3">
              <div class="flex items-center space-x-4">
                <div class="flex-1">
                  <div class="flex items-center justify-between mb-1">
                    <span class="text-xs text-gray-500">CPU Usage</span>
                    <span class="text-xs text-gray-900">{{ (instance.resources.cpuUsage * 100) | number:'1.0-0' }}%</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div [class]="'h-2 rounded-full ' + getResourceUsageClass(instance.resources.cpuUsage)" [style.width.%]="instance.resources.cpuUsage * 100"></div>
                  </div>
                </div>
                <div class="flex-1">
                  <div class="flex items-center justify-between mb-1">
                    <span class="text-xs text-gray-500">Memory Usage</span>
                    <span class="text-xs text-gray-900">{{ (instance.resources.memoryUsage * 100) | number:'1.0-0' }}%</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div [class]="'h-2 rounded-full ' + getResourceUsageClass(instance.resources.memoryUsage)" [style.width.%]="instance.resources.memoryUsage * 100"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Labels -->
            <div *ngIf="hasLabels(instance.labels)">
              <span class="text-sm text-gray-500 mb-2 block">Labels:</span>
              <div class="flex flex-wrap gap-2">
                <span *ngFor="let label of getObjectKeys(instance.labels)" class="inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-md">
                  {{ label }}: {{ instance.labels[label] }}
                  <button
                    (click)="removeLabel(instance, label)"
                    class="ml-1 text-gray-500 hover:text-red-600 focus:outline-none"
                    [disabled]="labelingLoading"
                  >
                    <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                  </button>
                </span>
              </div>
            </div>

            <!-- Capabilities -->
            <div *ngIf="instance.capabilities.length > 0" class="mt-2">
              <span class="text-sm text-gray-500 mb-2 block">Capabilities:</span>
              <div class="flex flex-wrap gap-1">
                <span *ngFor="let capability of instance.capabilities" class="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-md">
                  {{ capability }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Label Management Modal -->
<div *ngIf="showLabelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Manage Labels</h3>
        <button (click)="closeLabelModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div *ngIf="currentInstance" class="mb-4">
        <p class="text-sm text-gray-600 mb-2">Instance: <span class="font-medium">{{ currentInstance.name }}</span></p>

        <!-- Current Labels -->
        <div *ngIf="hasLabels(currentInstance.labels)" class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2">Current Labels:</h4>
          <div class="space-y-2">
            <div *ngFor="let label of getObjectKeys(currentInstance.labels)" class="flex items-center justify-between p-2 bg-gray-50 rounded">
              <span class="text-sm">{{ label }}: {{ currentInstance.labels[label] }}</span>
              <button
                (click)="removeLabel(currentInstance, label)"
                class="text-red-600 hover:text-red-800 text-sm"
                [disabled]="labelingLoading"
              >
                Remove
              </button>
            </div>
          </div>
        </div>

        <!-- Add New Label -->
        <div class="border-t pt-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2">Add New Label:</h4>
          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Key</label>
              <input
                type="text"
                [(ngModel)]="newLabelKey"
                placeholder="e.g., environment"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Value</label>
              <input
                type="text"
                [(ngModel)]="newLabelValue"
                placeholder="e.g., production"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <button
              (click)="addLabel()"
              [disabled]="!newLabelKey || !newLabelValue || labelingLoading"
              class="w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span *ngIf="labelingLoading" class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
              Add Label
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Bulk Label Modal -->
<div *ngIf="showBulkLabelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Bulk Label Management</h3>
        <button (click)="closeBulkLabelModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div class="mb-4">
        <p class="text-sm text-gray-600 mb-4">{{ selectedInstances.size }} instance(s) selected</p>

        <!-- Operation Type -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Operation</label>
          <select
            [(ngModel)]="bulkOperation"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="add">Add Labels</option>
            <option value="remove">Remove Labels</option>
            <option value="replace">Replace All Labels</option>
          </select>
        </div>

        <!-- Labels to Apply -->
        <div class="mb-4">
          <div class="flex items-center justify-between mb-2">
            <label class="block text-sm font-medium text-gray-700">Labels</label>
            <button
              (click)="addBulkLabel()"
              class="text-sm text-blue-600 hover:text-blue-800"
            >
              + Add Label
            </button>
          </div>

          <div *ngIf="!hasBulkLabels()" class="text-sm text-gray-500 italic">
            No labels defined. Click "Add Label" to add some.
          </div>

          <div *ngIf="hasBulkLabels()" class="space-y-2">
            <div *ngFor="let key of getBulkLabelKeys()" class="flex items-center justify-between p-2 bg-gray-50 rounded">
              <span class="text-sm">{{ key }}: {{ bulkLabels[key] }}</span>
              <button
                (click)="removeBulkLabel(key)"
                class="text-red-600 hover:text-red-800 text-sm"
              >
                Remove
              </button>
            </div>
          </div>
        </div>

        <!-- Apply Button -->
        <button
          (click)="applyBulkLabels()"
          [disabled]="!hasBulkLabels() || labelingLoading"
          class="w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span *ngIf="labelingLoading" class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
          Apply to {{ selectedInstances.size }} Instance(s)
        </button>
      </div>
    </div>
  </div>
</div>
