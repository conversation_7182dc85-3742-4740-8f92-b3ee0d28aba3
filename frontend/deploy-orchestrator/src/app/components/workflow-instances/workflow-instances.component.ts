import { Component, OnInit } from '@angular/core';
import { WorkflowService } from '../../services/workflow.service';
import { forkJoin, of, timer } from 'rxjs';
import { catchError, timeout, takeUntil } from 'rxjs/operators';

interface WorkflowInstance {
  id: string;
  name: string;
  os: string;
  architecture: string;
  status: string;
  lastSeen: string;
  labels: { [key: string]: string };
  capabilities: string[];
  resources: {
    cpuCores: number;
    memoryMB: number;
    diskSpaceGB: number;
    cpuUsage: number;
    memoryUsage: number;
    loadAverage: number;
    maxExecutions: number;
  };
}

interface AvailableLabels {
  [key: string]: string[];
}

@Component({
  selector: 'app-workflow-instances',
  templateUrl: './workflow-instances.component.html',
  styleUrls: ['./workflow-instances.component.scss']
})
export class WorkflowInstancesComponent implements OnInit {
  instances: WorkflowInstance[] = [];
  availableLabels: AvailableLabels = {};
  filteredInstances: WorkflowInstance[] = [];
  selectedInstances: Set<string> = new Set();

  // Filter and search
  searchTerm: string = '';
  statusFilter: string = '';
  osFilter: string = '';
  labelFilters: { [key: string]: string } = {};

  // Labeling
  showLabelModal: boolean = false;
  showBulkLabelModal: boolean = false;
  currentInstance: WorkflowInstance | null = null;
  newLabelKey: string = '';
  newLabelValue: string = '';
  bulkOperation: 'add' | 'remove' | 'replace' = 'add';
  bulkLabels: { [key: string]: string } = {};

  // Loading states
  loading: boolean = false;
  labelingLoading: boolean = false;

  constructor(private workflowService: WorkflowService) {}

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.loading = true;

    // Use forkJoin to load both instances and labels simultaneously
    // If either fails, we still want to show what we can
    const instances$ = this.workflowService.getWorkflowInstances().pipe(
      timeout(10000), // 10 second timeout
      catchError(error => {
        console.error('Error loading instances:', error);
        return of({ instances: [] }); // Return empty array on error
      })
    );

    const labels$ = this.workflowService.getInstanceLabels().pipe(
      timeout(10000), // 10 second timeout
      catchError(error => {
        console.error('Error loading available labels:', error);
        return of({ labels: {} }); // Return empty labels on error
      })
    );

    forkJoin({
      instances: instances$,
      labels: labels$
    }).pipe(
      timeout(15000) // Overall timeout for both requests
    ).subscribe({
      next: (result) => {
        // Check if 'instances' is an array or an object with an 'instances' property
        if (Array.isArray(result.instances)) {
          this.instances = result.instances;
        } else if (result.instances && 'instances' in result.instances) {
          this.instances = result.instances.instances;
        } else {
          this.instances = [];
        }
        this.availableLabels = result.labels?.labels || {};
        this.applyFilters();
        this.loading = false;
        console.log('Data loaded successfully:', {
          instanceCount: this.instances.length,
          labelCount: Object.keys(this.availableLabels).length
        });
      },
      error: (error) => {
        console.error('Timeout or unexpected error loading data:', error);
        this.instances = [];
        this.availableLabels = {};
        this.loading = false;
      }
    });
  }

  loadInstances(): void {
    this.loadData();
  }

  loadAvailableLabels(): void {
    this.workflowService.getInstanceLabels().subscribe({
      next: (response) => {
        this.availableLabels = response.labels || {};
      },
      error: (error) => {
        console.error('Error loading available labels:', error);
        this.availableLabels = {};
      }
    });
  }

  applyFilters(): void {
    this.filteredInstances = this.instances.filter(instance => {
      // Search term filter
      if (this.searchTerm && !instance.name.toLowerCase().includes(this.searchTerm.toLowerCase())) {
        return false;
      }

      // Status filter
      if (this.statusFilter && instance.status !== this.statusFilter) {
        return false;
      }

      // OS filter
      if (this.osFilter && instance.os !== this.osFilter) {
        return false;
      }

      // Label filters
      for (const [key, value] of Object.entries(this.labelFilters)) {
        if (value && (!instance.labels[key] || instance.labels[key] !== value)) {
          return false;
        }
      }

      return true;
    });
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  onFilterChange(): void {
    this.applyFilters();
  }

  onLabelFilterChange(key: string, event: Event): void {
    const target = event.target as HTMLSelectElement;
    const value = target.value;
    if (value) {
      this.labelFilters[key] = value;
    } else {
      delete this.labelFilters[key];
    }
    this.applyFilters();
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.statusFilter = '';
    this.osFilter = '';
    this.labelFilters = {};
    this.applyFilters();
  }

  toggleInstanceSelection(instanceId: string): void {
    if (this.selectedInstances.has(instanceId)) {
      this.selectedInstances.delete(instanceId);
    } else {
      this.selectedInstances.add(instanceId);
    }
  }

  selectAllInstances(): void {
    this.filteredInstances.forEach(instance => {
      this.selectedInstances.add(instance.id);
    });
  }

  clearSelection(): void {
    this.selectedInstances.clear();
  }

  openLabelModal(instance: WorkflowInstance): void {
    this.currentInstance = instance;
    this.newLabelKey = '';
    this.newLabelValue = '';
    this.showLabelModal = true;
  }

  closeLabelModal(): void {
    this.showLabelModal = false;
    this.currentInstance = null;
    this.newLabelKey = '';
    this.newLabelValue = '';
  }

  addLabel(): void {
    if (!this.currentInstance || !this.newLabelKey || !this.newLabelValue) {
      return;
    }

    this.labelingLoading = true;
    const labels = { ...this.currentInstance.labels, [this.newLabelKey]: this.newLabelValue };

    this.workflowService.updateInstanceLabels(this.currentInstance.id, labels).subscribe({
      next: (response) => {
        this.currentInstance!.labels = response.labels;
        this.newLabelKey = '';
        this.newLabelValue = '';
        this.labelingLoading = false;
        this.loadAvailableLabels(); // Refresh available labels
      },
      error: (error) => {
        console.error('Error adding label:', error);
        this.labelingLoading = false;
      }
    });
  }

  removeLabel(instance: WorkflowInstance, labelKey: string): void {
    this.labelingLoading = true;
    this.workflowService.removeInstanceLabel(instance.id, labelKey).subscribe({
      next: (response) => {
        instance.labels = response.labels;
        this.labelingLoading = false;
        this.loadAvailableLabels(); // Refresh available labels
      },
      error: (error) => {
        console.error('Error removing label:', error);
        this.labelingLoading = false;
      }
    });
  }

  openBulkLabelModal(): void {
    if (this.selectedInstances.size === 0) {
      return;
    }
    this.bulkLabels = {};
    this.bulkOperation = 'add';
    this.showBulkLabelModal = true;
  }

  closeBulkLabelModal(): void {
    this.showBulkLabelModal = false;
    this.bulkLabels = {};
  }

  addBulkLabel(): void {
    const key = prompt('Enter label key:');
    const value = prompt('Enter label value:');
    if (key && value) {
      this.bulkLabels[key] = value;
    }
  }

  removeBulkLabel(key: string): void {
    delete this.bulkLabels[key];
  }

  applyBulkLabels(): void {
    if (this.selectedInstances.size === 0 || Object.keys(this.bulkLabels).length === 0) {
      return;
    }

    this.labelingLoading = true;
    const instanceIds = Array.from(this.selectedInstances);

    this.workflowService.bulkUpdateInstanceLabels(instanceIds, this.bulkLabels, this.bulkOperation).subscribe({
      next: (response) => {
        // Update instances with new labels
        for (const [instanceId, result] of Object.entries(response.results)) {
          const instance = this.instances.find(i => i.id === instanceId);
          if (instance && (result as any).success) {
            instance.labels = (result as any).labels;
          }
        }
        this.closeBulkLabelModal();
        this.clearSelection();
        this.labelingLoading = false;
        this.loadAvailableLabels(); // Refresh available labels
      },
      error: (error) => {
        console.error('Error applying bulk labels:', error);
        this.labelingLoading = false;
      }
    });
  }

  getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-red-100 text-red-800';
      case 'busy': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  getResourceUsageClass(usage: number): string {
    if (usage < 0.5) return 'bg-green-500';
    if (usage < 0.8) return 'bg-yellow-500';
    return 'bg-red-500';
  }

  formatLastSeen(lastSeen: string): string {
    const date = new Date(lastSeen);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return `${Math.floor(diffMins / 1440)}d ago`;
  }

  getUniqueValues(field: keyof WorkflowInstance): string[] {
    const values = this.instances.map(instance => instance[field] as string);
    return [...new Set(values)].filter(Boolean);
  }

  // Helper methods for template usage
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  hasLabels(labels: { [key: string]: string }): boolean {
    return Object.keys(labels || {}).length > 0;
  }

  getAvailableLabelKeys(): string[] {
    return Object.keys(this.availableLabels || {});
  }

  getBulkLabelKeys(): string[] {
    return Object.keys(this.bulkLabels || {});
  }

  hasBulkLabels(): boolean {
    return Object.keys(this.bulkLabels || {}).length > 0;
  }

  hasAvailableLabels(): boolean {
    return Object.keys(this.availableLabels || {}).length > 0;
  }
}
