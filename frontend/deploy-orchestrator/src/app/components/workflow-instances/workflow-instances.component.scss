// Workflow Instances Component Styles

.instance-card {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.resource-bar {
  transition: width 0.3s ease-in-out;
}

.label-tag {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: scale(1.05);
  }
}

.status-indicator {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -8px;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }

  &.active::before {
    background-color: #10b981;
    animation: pulse 2s infinite;
  }

  &.inactive::before {
    background-color: #ef4444;
  }

  &.busy::before {
    background-color: #f59e0b;
    animation: pulse 1s infinite;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

// Keep only essential styles for functionality
