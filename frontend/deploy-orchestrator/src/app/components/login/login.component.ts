import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { first } from 'rxjs/operators';
import { AuthService } from '../../services/auth.service';
import { DataRefreshService } from '../../services/data-refresh.service';
import { IdentityProviderService } from '../../services/identity-provider.service';
import { IdentityProvider } from '../../models/identity-provider.model';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  loading = false;
  submitted = false;
  error = '';
  returnUrl: string = '/';
  identityProviders: IdentityProvider[] = [];
  loadingProviders = false;

  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    private dataRefreshService: DataRefreshService,
    private identityProviderService: IdentityProviderService
  ) {
    // Clear any error state and ensure clean login state
    this.error = '';
    this.loading = false;

    // Redirect to home if already logged in
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/']);
    }
  }

  ngOnInit(): void {
    // Reset component state to prevent issues from previous sessions
    this.resetLoginState();

    this.loginForm = this.formBuilder.group({
      username: ['', Validators.required],
      password: ['', Validators.required]
    });

    // Get return url from route parameters or default to '/'
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';

    // Load configured identity providers
    this.loadIdentityProviders();
  }

  private resetLoginState(): void {
    this.loading = false;
    this.submitted = false;
    this.error = '';
    this.loadingProviders = false;

    // Clear any stale form data
    if (this.loginForm) {
      this.loginForm.reset();
    }
  }

  // Convenience getter for easy access to form fields
  get f() { return this.loginForm.controls; }

  loadIdentityProviders(): void {
    this.loadingProviders = true;
    this.identityProviderService.getEnabledProviders()
      .subscribe({
        next: (providers) => {
          this.identityProviders = providers;
          this.loadingProviders = false;
        },
        error: (error) => {
          console.error('Error loading identity providers', error);
          this.loadingProviders = false;
        }
      });
  }

  onSubmit(): void {
    this.submitted = true;

    // Stop here if form is invalid
    if (this.loginForm.invalid) {
      return;
    }

    this.loading = true;
    // Use regular local authentication (no authProvider specified)
    this.authService.login(this.f['username'].value, this.f['password'].value)
      .pipe(first())
      .subscribe({
        next: () => {
          // Refresh all data after successful login using centralized service
          this.dataRefreshService.refreshAllData();
          this.router.navigate([this.returnUrl]);
        },
        error: error => {
          // Display a more detailed error message
          this.error = error.error?.message || error.error?.error || error.error?.details || 'Invalid username or password. Please try again.';
          console.error('Login error:', error); // Log the actual error for debugging
          this.loading = false;
        }
      });
  }

  loginWithProvider(provider: IdentityProvider): void {
    this.loading = true;
    this.error = '';

    if (provider.type === 'ldap') {
      // Use the specific LDAP login method
      this.loginWithLDAP(provider);
      return;
    }

    this.authService.initiateExternalLogin(provider.id, provider.type)
      .subscribe({
        next: (response) => {
          // Redirect to the identity provider's login page
          window.location.href = response.redirectUrl;
        },
        error: (error) => {
          this.error = error.message || `Failed to initiate login with ${provider.name}`;
          this.loading = false;
        }
      });
  }

  loginWithLDAP(provider: IdentityProvider): void {
    // For LDAP, we need to use the credentials from the form
    if (!this.loginForm.valid) {
      this.error = 'Please enter your username and password to login with LDAP';
      this.loading = false;
      return;
    }

    const username = this.f['username'].value;
    const password = this.f['password'].value;

    // Use LDAP-specific authentication with the provider ID
    this.authService.login(username, password, 'ldap', provider.id)
      .pipe(first())
      .subscribe({
        next: () => {
          // Refresh all data after successful LDAP login using centralized service
          this.dataRefreshService.refreshAllData();
          this.router.navigate([this.returnUrl]);
        },
        error: error => {
          this.error = error.error?.message || error.error?.error || error.error?.details || 'LDAP authentication failed. Please check your credentials.';
          console.error('LDAP login error:', error);
          this.loading = false;
        }
      });
  }
}
