<!-- Execution Parameters Modal -->
<div *ngIf="show" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" (keydown)="onKeyDown($event)">
  <div class="relative top-10 mx-auto p-6 border max-w-2xl shadow-lg rounded-md bg-white" (click)="$event.stopPropagation()">
    <div class="mt-3">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6">
        <div>
          <h3 class="text-lg font-medium text-gray-900">Execute Workflow</h3>
          <p class="mt-1 text-sm text-gray-600">{{ workflow?.name }}</p>
        </div>
        <button
          (click)="onCancel()"
          class="text-gray-400 hover:text-gray-600">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div class="space-y-6">
        <!-- Trigger Type Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Execution Type</label>
          <select
            [(ngModel)]="triggerType"
            class="w-full px-4 py-3 text-sm border-2 border-gray-300 rounded-lg transition-all duration-200 ease-in-out focus:outline-none focus:ring-4 focus:ring-blue-100 focus:border-blue-500 hover:border-gray-400 bg-white">
            <option value="manual">Manual Execution</option>
            <option value="scheduled">Scheduled Execution</option>
            <option value="api">API Triggered</option>
            <option value="webhook">Webhook Triggered</option>
          </select>
        </div>

        <!-- Runtime Parameters -->
        <div *ngIf="getVariableEntries().length > 0">
          <div class="flex items-center justify-between mb-4">
            <div>
              <h4 class="text-md font-medium text-gray-900">Runtime Parameters</h4>
              <p class="text-sm text-gray-600">Override workflow variables for this execution</p>
            </div>
          </div>

          <div class="space-y-6">
            <div *ngFor="let variable of getVariableEntries(); trackBy: trackByVariableKey" class="space-y-3 p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors duration-200">
              <div class="flex items-center justify-between">
                <label
                  [for]="'param-' + variable.key"
                  class="block text-sm font-medium text-gray-700 cursor-pointer hover:text-gray-900">
                  {{ variable.key }}
                  <span class="text-xs text-gray-500 ml-2">(click to edit)</span>
                </label>
                <button
                  *ngIf="hasOverride(variable.key)"
                  (click)="resetToDefault(variable.key)"
                  class="text-xs text-blue-600 hover:text-blue-800 px-2 py-1 rounded hover:bg-blue-50 transition-colors duration-200"
                  title="Reset to default value">
                  Reset to default
                </button>
              </div>

              <!-- Parameter Input -->
              <div class="relative">
                <input
                  type="text"
                  [value]="getParameterValue(variable.key)"
                  (input)="onParameterChange(variable.key, $event)"
                  (focus)="onInputFocus(variable.key)"
                  (blur)="onInputBlur(variable.key)"
                  [id]="'param-' + variable.key"
                  [attr.data-parameter]="variable.key"
                  class="w-full px-4 py-3 text-sm border-2 rounded-lg transition-all duration-200 ease-in-out focus:outline-none focus:ring-4 focus:ring-blue-100 focus:border-blue-500 hover:border-gray-400"
                  [class.border-blue-400]="hasOverride(variable.key)"
                  [class.bg-blue-50]="hasOverride(variable.key)"
                  [class.border-gray-300]="!hasOverride(variable.key)"
                  [class.bg-white]="!hasOverride(variable.key)"
                  [placeholder]="'Enter ' + variable.key + ' (default: ' + (variable.value || 'none') + ')'"
                  #paramInput>

                <!-- Override indicator -->
                <div *ngIf="hasOverride(variable.key)"
                     class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                </div>

                <!-- Focus helper -->
                <div class="absolute inset-0 pointer-events-none border-2 border-transparent rounded-lg transition-all duration-200"
                     [class.border-blue-300]="isFocused(variable.key)"
                     [class.shadow-lg]="isFocused(variable.key)">
                </div>
              </div>

              <!-- Default Value Hint -->
              <div class="flex items-center justify-between text-xs text-gray-500">
                <span>Default: {{ variable.value || 'Not set' }}</span>
                <span *ngIf="hasOverride(variable.key)" class="text-blue-600 font-medium">
                  Custom value
                </span>
              </div>

              <!-- Usage Example -->
              <div class="text-xs text-gray-600">
                <span class="font-medium">Used as:</span>
                <code class="bg-gray-200 px-1 rounded ml-1">{{'{{'}}{{'{{'}}{{ variable.key }}{{'}}'}}}}</code>
              </div>
            </div>
          </div>
        </div>

        <!-- No Parameters Message -->
        <div *ngIf="getVariableEntries().length === 0" class="text-center py-8 text-gray-500">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
          <p class="mt-2">No runtime parameters</p>
          <p class="text-sm">This workflow will execute with its default configuration.</p>
        </div>

        <!-- Execution Info -->
        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800">Execution Information</h3>
              <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc pl-5 space-y-1">
                  <li>Parameters override workflow default variables</li>
                  <li>Empty fields will use default values</li>
                  <li>You can monitor execution progress after starting</li>
                  <li>Execution will be logged for audit purposes</li>
                  <li class="text-xs">Press <kbd class="px-1 py-0.5 bg-gray-200 rounded text-xs">Ctrl+Enter</kbd> to execute, <kbd class="px-1 py-0.5 bg-gray-200 rounded text-xs">Esc</kbd> to cancel</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-end space-x-3 mt-8 pt-6 border-t">
        <button
          (click)="onCancel()"
          [disabled]="isExecuting"
          class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
          Cancel
        </button>
        <button
          (click)="onExecute()"
          [disabled]="isExecuting"
          class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 inline-flex items-center">
          <svg *ngIf="!isExecuting" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a3 3 0 016 0v2M7 16a3 3 0 006 0v-2"></path>
          </svg>
          <span *ngIf="isExecuting" class="inline-flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Executing...
          </span>
          <span *ngIf="!isExecuting">Execute Workflow</span>
        </button>
      </div>
    </div>
  </div>
</div>
