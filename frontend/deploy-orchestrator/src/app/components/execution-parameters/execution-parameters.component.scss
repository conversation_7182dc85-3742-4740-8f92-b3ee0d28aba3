// Execution Parameters Component Styles

.execution-parameters {
  .parameter-item {
    transition: all 0.2s ease-in-out;
    
    &:hover {
      background-color: #f9fafb;
    }
  }

  .parameter-label {
    font-weight: 500;
    color: #374151;
  }

  .parameter-input {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    
    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    &.overridden {
      border-color: #3b82f6;
      background-color: #eff6ff;
      
      &:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }
  }

  .trigger-type-select {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    
    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }

  .default-value-hint {
    color: #6b7280;
    font-size: 0.75rem;
  }

  .custom-value-indicator {
    color: #3b82f6;
    font-weight: 500;
    font-size: 0.75rem;
  }

  .usage-example {
    color: #6b7280;
    font-size: 0.75rem;
    
    code {
      background-color: #e5e7eb;
      padding: 0.125rem 0.25rem;
      border-radius: 0.25rem;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }
  }

  .reset-button {
    color: #3b82f6;
    font-size: 0.75rem;
    
    &:hover {
      color: #1d4ed8;
      text-decoration: underline;
    }
  }

  .override-indicator {
    color: #3b82f6;
  }

  .execution-info {
    background-color: #eff6ff;
    border: 1px solid #bfdbfe;
    border-radius: 0.375rem;
    padding: 1rem;
    
    .info-icon {
      color: #60a5fa;
    }
    
    .info-title {
      color: #1e40af;
      font-weight: 500;
    }
    
    .info-content {
      color: #1d4ed8;
      
      ul {
        margin-top: 0.5rem;
        padding-left: 1.25rem;
        
        li {
          margin-bottom: 0.25rem;
        }
      }
    }
  }

  .execute-button {
    background-color: #059669;
    
    &:hover:not(:disabled) {
      background-color: #047857;
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .cancel-button {
    &:hover:not(:disabled) {
      background-color: #f9fafb;
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

// Modal overlay
.modal-overlay {
  backdrop-filter: blur(2px);
}

// Responsive adjustments
@media (max-width: 640px) {
  .execution-parameters {
    .parameter-item {
      padding: 0.75rem;
    }
    
    .execution-info {
      padding: 0.75rem;
    }
    
    .modal-content {
      margin: 0.5rem;
      max-width: calc(100vw - 1rem);
    }
  }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .execution-parameters {
    .parameter-label {
      color: #f3f4f6;
    }
    
    .parameter-input {
      background-color: #374151;
      border-color: #4b5563;
      color: #f3f4f6;
      
      &:focus {
        border-color: #60a5fa;
      }
      
      &.overridden {
        background-color: #1e3a8a;
        border-color: #60a5fa;
      }
    }
    
    .trigger-type-select {
      background-color: #374151;
      border-color: #4b5563;
      color: #f3f4f6;
      
      &:focus {
        border-color: #60a5fa;
      }
    }
    
    .default-value-hint {
      color: #9ca3af;
    }
    
    .custom-value-indicator {
      color: #93c5fd;
    }
    
    .usage-example {
      color: #9ca3af;
      
      code {
        background-color: #4b5563;
        color: #f3f4f6;
      }
    }
    
    .reset-button {
      color: #93c5fd;
      
      &:hover {
        color: #bfdbfe;
      }
    }
    
    .execution-info {
      background-color: #1e3a8a;
      border-color: #3b82f6;
      
      .info-title {
        color: #bfdbfe;
      }
      
      .info-content {
        color: #93c5fd;
      }
    }
  }
}
