import { Component, Input, Output, EventEmitter, OnInit, OnChanges, AfterViewInit, ElementRef } from '@angular/core';
import { WorkflowDefinition } from '../../models/workflow.model';

@Component({
  selector: 'app-execution-parameters',
  templateUrl: './execution-parameters.component.html',
  styleUrls: ['./execution-parameters.component.scss']
})
export class ExecutionParametersComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() workflow: WorkflowDefinition | null = null;
  @Input() show: boolean = false;
  @Output() execute = new EventEmitter<{ parameters: { [key: string]: any }, triggerType: string }>();
  @Output() cancel = new EventEmitter<void>();

  parameters: { [key: string]: any } = {};
  triggerType: string = 'manual';
  isExecuting: boolean = false;
  focusedInput: string | null = null;

  constructor(private elementRef: ElementRef) {}

  ngOnInit(): void {
    this.initializeParameters();
  }

  ngOnChanges(): void {
    if (this.workflow) {
      this.initializeParameters();
    }
    if (!this.show) {
      this.isExecuting = false;
    }
  }

  ngAfterViewInit(): void {
    // Auto-focus first input when modal opens
    if (this.show) {
      this.focusFirstInput();
    }
  }

  private initializeParameters(): void {
    if (!this.workflow || !this.workflow.variables) {
      this.parameters = {};
      return;
    }

    // Initialize with default values from workflow variables
    this.parameters = { ...this.workflow.variables };
  }

  onParameterChange(parameterName: string, event: Event): void {
    const target = event.target as HTMLInputElement;
    const cursorPosition = target.selectionStart || 0;

    this.parameters[parameterName] = target.value;

    // Restore focus and cursor position after Angular updates
    setTimeout(() => {
      const input = document.querySelector(`input[data-parameter="${parameterName}"]`) as HTMLInputElement;
      if (input && document.activeElement === input) {
        input.focus();
        input.setSelectionRange(cursorPosition, cursorPosition);
      }
    }, 0);
  }

  onExecute(): void {
    this.isExecuting = true;
    this.execute.emit({
      parameters: { ...this.parameters },
      triggerType: this.triggerType
    });
  }

  onCancel(): void {
    this.cancel.emit();
  }

  resetToDefault(parameterName: string): void {
    if (this.workflow && this.workflow.variables) {
      this.parameters[parameterName] = this.workflow.variables[parameterName];
    }
  }

  hasOverride(parameterName: string): boolean {
    if (!this.workflow || !this.workflow.variables) return false;
    return this.parameters[parameterName] !== this.workflow.variables[parameterName];
  }

  getVariableEntries(): Array<{key: string, value: any}> {
    if (!this.workflow || !this.workflow.variables) return [];
    return Object.entries(this.workflow.variables).map(([key, value]) => ({
      key,
      value
    }));
  }

  getParameterValue(parameterName: string): any {
    return this.parameters[parameterName] || '';
  }

  setExecuting(executing: boolean): void {
    this.isExecuting = executing;
  }

  onInputFocus(parameterName: string): void {
    this.focusedInput = parameterName;
  }

  onInputBlur(parameterName: string): void {
    this.focusedInput = null;
  }

  isFocused(parameterName: string): boolean {
    return this.focusedInput === parameterName;
  }

  private focusFirstInput(): void {
    setTimeout(() => {
      const firstInput = this.elementRef.nativeElement.querySelector('input[data-parameter]');
      if (firstInput) {
        firstInput.focus();
      }
    }, 100);
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Escape') {
      event.preventDefault();
      this.onCancel();
    } else if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      this.onExecute();
    }
  }

  trackByVariableKey(index: number, item: {key: string, value: any}): string {
    return item.key;
  }
}
