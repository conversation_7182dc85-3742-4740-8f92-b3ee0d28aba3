import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject, interval } from 'rxjs';
import { takeUntil, switchMap } from 'rxjs/operators';
import { WorkflowService } from '../../services/workflow.service';
import { AuthService } from '../../services/auth.service';
import { ProjectService } from '../../services/project.service';
import { Project } from '../../models/project.model';
import { NgForOf, NgIf } from "@angular/common";
import { FormsModule } from '@angular/forms';

interface ExecutionMetrics {
  id: string;
  executionId: string;
  totalSteps: number;
  completedSteps: number;
  failedSteps: number;
  skippedSteps: number;
  averageStepTime: number;
  totalExecutionTime: number;
  resourceUsage: any;
  performanceData: any;
}

interface ExecutionEvent {
  id: string;
  executionId: string;
  stepId?: string;
  eventType: string;
  eventData: any;
  timestamp: string;
}

interface ExecutionDashboard {
  statistics: {
    totalActiveExecutions: number;
    totalCompletedSteps: number;
    totalFailedSteps: number;
    averageExecutionTime: number;
  };
  activeExecutions: { [key: string]: ExecutionMetrics };
  recentExecutions: any[];
}

@Component({
  selector: 'app-execution-monitoring',
  templateUrl: './execution-monitoring.component.html',
  standalone: true,
  imports: [
    NgIf,
    NgForOf,
    FormsModule
  ],
  styleUrls: ['./execution-monitoring.component.css']
})
export class ExecutionMonitoringComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  dashboard: ExecutionDashboard | null = null;
  selectedExecution: string | null = null;
  executionMetrics: ExecutionMetrics | null = null;
  executionEvents: ExecutionEvent[] = [];
  loading = true;
  error: string | null = null;
  projects: Project[] = [];
  selectedProjectId: string = '';
  selectedProject: Project | null = null;
  isAdmin: boolean = false;

  // Chart data
  chartData: any = null;
  chartOptions: any = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Execution Performance'
      }
    }
  };

  constructor(
    private route: ActivatedRoute,
    private workflowService: WorkflowService,
    private authService: AuthService,
    private projectService: ProjectService
  ) {
    this.isAdmin = this.authService.isAdmin();
  }

  ngOnInit(): void {
    // Load projects first
    this.loadProjects();

    // Subscribe to project changes
    this.subscribeToProjectChanges();

    // Check if we have a specific execution ID in the route
    this.route.params.subscribe(params => {
      if (params['executionId']) {
        this.selectedExecution = params['executionId'];
        this.loadExecutionDetails();
      } else {
        this.loadDashboard();
      }
    });

    // Set up auto-refresh
    this.startAutoRefresh();
  }

  loadProjects(): void {
    this.projectService.projects$.subscribe({
      next: (projects) => {
        this.projects = projects;
        if (projects.length > 0 && !this.isAdmin) {
          this.selectedProjectId = projects[0].id;
        }
      },
      error: (error) => {
        console.error('Error loading projects', error);
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private subscribeToProjectChanges(): void {
    this.projectService.selectedProject$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (selectedProject) => {
          this.selectedProject = selectedProject;
        },
        error: (error) => {
          console.error('Error subscribing to project changes:', error);
        }
      });
  }

  loadDashboard(): void {
    this.loading = true;
    this.error = null;

    // For non-admin users, require project ID
    const projectId = !this.isAdmin ? this.selectedProjectId : undefined;

    this.workflowService.getExecutionDashboard(projectId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (dashboard) => {
          console.log('Dashboard data received:', dashboard);
          this.dashboard = dashboard;
          this.updateChartData();
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading dashboard:', error);
          this.error = 'Failed to load execution dashboard';
          this.loading = false;
        }
      });
  }

  onProjectChange(): void {
    this.loadDashboard();
  }

  loadExecutionDetails(): void {
    if (!this.selectedExecution) return;

    this.loading = true;
    this.error = null;

    // Load metrics
    this.workflowService.getExecutionMetrics(this.selectedExecution)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (metrics) => {
          this.executionMetrics = metrics;
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading execution metrics:', error);
          this.error = 'Failed to load execution metrics';
          this.loading = false;
        }
      });

    // Load events
    this.workflowService.getExecutionEvents(this.selectedExecution)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.executionEvents = response.events || [];
        },
        error: (error) => {
          console.error('Error loading execution events:', error);
        }
      });
  }

  startAutoRefresh(): void {
    // Refresh every 5 seconds
    interval(5000)
      .pipe(
        takeUntil(this.destroy$),
        switchMap(() => {
          if (this.selectedExecution) {
            return this.workflowService.getExecutionMetrics(this.selectedExecution);
          } else {
            return this.workflowService.getExecutionDashboard();
          }
        })
      )
      .subscribe({
        next: (data) => {
          if (this.selectedExecution) {
            this.executionMetrics = data as ExecutionMetrics;
          } else {
            this.dashboard = data as ExecutionDashboard;
            this.updateChartData();
          }
        },
        error: (error) => {
          console.error('Error during auto-refresh:', error);
        }
      });
  }

  updateChartData(): void {
    if (!this.dashboard) return;

    const activeExecutions = Object.values(this.dashboard.activeExecutions);

    this.chartData = {
      labels: activeExecutions.map(exec => `Execution ${exec.executionId.substring(0, 8)}`),
      datasets: [
        {
          label: 'Completed Steps',
          data: activeExecutions.map(exec => exec.completedSteps),
          backgroundColor: 'rgba(34, 197, 94, 0.8)',
          borderColor: 'rgba(34, 197, 94, 1)',
          borderWidth: 1
        },
        {
          label: 'Failed Steps',
          data: activeExecutions.map(exec => exec.failedSteps),
          backgroundColor: 'rgba(239, 68, 68, 0.8)',
          borderColor: 'rgba(239, 68, 68, 1)',
          borderWidth: 1
        },
        {
          label: 'Remaining Steps',
          data: activeExecutions.map(exec => exec.totalSteps - exec.completedSteps - exec.failedSteps - exec.skippedSteps),
          backgroundColor: 'rgba(156, 163, 175, 0.8)',
          borderColor: 'rgba(156, 163, 175, 1)',
          borderWidth: 1
        }
      ]
    };
  }



  formatDuration(seconds: number): string {
    if (seconds < 60) {
      return `${seconds}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  }

  formatTimestamp(timestamp: string): string {
    return new Date(timestamp).toLocaleString();
  }

  refreshData(): void {
    if (this.selectedExecution) {
      this.loadExecutionDetails();
    } else {
      this.loadDashboard();
    }
  }

  selectExecution(executionId: string): void {
    this.selectedExecution = executionId;
    this.loadExecutionDetails();
  }

  goBackToDashboard(): void {
    this.selectedExecution = null;
    this.executionMetrics = null;
    this.executionEvents = [];
    this.loadDashboard();
  }

  getProgressPercentage(metrics: ExecutionMetrics): number {
    if (!metrics || metrics.totalSteps === 0) return 0;
    return Math.round((metrics.completedSteps / metrics.totalSteps) * 100);
  }

  formatEventType(eventType: string): string {
    return eventType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  formatEventData(eventData: any): string {
    try {
      return JSON.stringify(eventData, null, 2);
    } catch {
      return String(eventData);
    }
  }

  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
        return 'text-green-600';
      case 'failed':
      case 'error':
        return 'text-red-600';
      case 'running':
      case 'in_progress':
        return 'text-blue-600';
      case 'pending':
      case 'waiting':
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  }

  getActiveExecutionsCount(): number {
    if (!this.dashboard || !this.dashboard.activeExecutions) {
      return 0;
    }
    return Object.keys(this.dashboard.activeExecutions).length;
  }

  getActiveExecutionsArray(): Array<{key: string, value: ExecutionMetrics}> {
    if (!this.dashboard || !this.dashboard.activeExecutions) {
      return [];
    }
    return Object.entries(this.dashboard.activeExecutions).map(([key, value]) => ({key, value}));
  }
}
