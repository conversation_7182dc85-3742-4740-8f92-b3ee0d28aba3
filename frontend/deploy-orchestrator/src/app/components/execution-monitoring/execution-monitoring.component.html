<div class="min-h-screen bg-gray-50 p-6">
  <!-- Header -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">
          {{ selectedExecution ? 'Execution Details' : 'Execution Monitoring' }}
        </h1>
        <p class="text-gray-600 mt-1">
          {{ selectedExecution ? 'Real-time monitoring of workflow execution' : 'Monitor all active workflow executions' }}
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <!-- Project Selector for non-admin users -->
        <div *ngIf="!isAdmin && projects.length > 0" class="flex items-center space-x-2">
          <label for="project-select" class="text-sm font-medium text-gray-700">Project:</label>
          <select
            id="project-select"
            [(ngModel)]="selectedProjectId"
            (ngModelChange)="onProjectChange()"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select Project</option>
            <option *ngFor="let project of projects" [value]="project.id">{{ project.name }}</option>
          </select>
        </div>

        <button
          *ngIf="selectedExecution"
          (click)="goBackToDashboard()"
          class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
          <i class="fas fa-arrow-left mr-2"></i>
          Back to Dashboard
        </button>
        <button
          (click)="refreshData()"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          <i class="fas fa-sync-alt mr-2"></i>
          Refresh
        </button>
      </div>
    </div>
  </div>

  <!-- Project info display -->
  <div *ngIf="selectedProject" class="mb-6">
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h4a1 1 0 011 1v5m-6 0V9a1 1 0 011-1h4a1 1 0 011 1v2"></path>
        </svg>
        <span class="text-sm font-medium text-blue-900">
          Monitoring executions for: <strong>{{ selectedProject.name }}</strong>
        </span>
      </div>
    </div>
  </div>

  <!-- No project selected message -->
  <div *ngIf="!selectedProject" class="mb-6">
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <span class="text-sm font-medium text-yellow-900">
          Please select a project from the header to monitor executions
        </span>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex items-center justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    <span class="ml-3 text-gray-600">Loading monitoring data...</span>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
    <div class="flex">
      <i class="fas fa-exclamation-triangle text-red-400 mr-3 mt-1"></i>
      <div>
        <h3 class="text-sm font-medium text-red-800">Error</h3>
        <p class="text-sm text-red-700 mt-1">{{ error }}</p>
      </div>
    </div>
  </div>

  <!-- Debug Info -->
  <div *ngIf="!loading" class="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
    <p class="text-sm text-yellow-800">
      Debug: selectedExecution={{ selectedExecution }},
      dashboard={{ !!dashboard }},
      loading={{ loading }},
      activeExecutions={{ getActiveExecutionsCount() }}
    </p>
  </div>

  <!-- Dashboard View -->
  <div *ngIf="!selectedExecution && dashboard && !loading">
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 rounded-lg">
            <i class="fas fa-play text-blue-600"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Active Executions</p>
            <p class="text-2xl font-semibold text-gray-900">{{ dashboard.statistics.totalActiveExecutions || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 rounded-lg">
            <i class="fas fa-check text-green-600"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Completed Steps</p>
            <p class="text-2xl font-semibold text-gray-900">{{ dashboard.statistics.totalCompletedSteps || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-red-100 rounded-lg">
            <i class="fas fa-times text-red-600"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Failed Steps</p>
            <p class="text-2xl font-semibold text-gray-900">{{ dashboard.statistics.totalFailedSteps || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-purple-100 rounded-lg">
            <i class="fas fa-clock text-purple-600"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Avg. Execution Time</p>
            <p class="text-2xl font-semibold text-gray-900">{{ formatDuration(dashboard.statistics.averageExecutionTime || 0) }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Active Executions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Active Executions List -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">Active Executions</h2>
        </div>
        <div class="p-6">
          <div *ngIf="getActiveExecutionsCount() === 0" class="text-center py-8 text-gray-500">
            No active executions
          </div>
          <div *ngFor="let execution of getActiveExecutionsArray()"
               class="border rounded-lg p-4 mb-4 hover:bg-gray-50 cursor-pointer transition-colors"
               (click)="selectExecution(execution.key)">
            <div class="flex items-center justify-between mb-2">
              <h3 class="font-medium text-gray-900">Execution {{ execution.key.substring(0, 8) }}</h3>
              <span class="text-sm text-gray-500">{{ getProgressPercentage(execution.value) }}% complete</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div class="bg-blue-600 h-2 rounded-full"
                   [style.width.%]="getProgressPercentage(execution.value)"></div>
            </div>
            <div class="flex justify-between text-sm text-gray-600">
              <span>{{ execution.value.completedSteps }}/{{ execution.value.totalSteps }} steps</span>
              <span *ngIf="execution.value.failedSteps > 0" class="text-red-600">
                {{ execution.value.failedSteps }} failed
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance Chart -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">Execution Progress</h2>
        </div>
        <div class="p-6">
          <div class="flex items-center justify-center h-64 text-gray-500">
            Performance charts will be available soon
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Executions -->
    <div class="mt-8 bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Recent Executions</h2>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Execution ID</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Started</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr *ngFor="let execution of dashboard?.recentExecutions || []">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {{ execution.id.substring(0, 8) }}...
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                      [class.bg-green-100]="execution.status === 'completed'"
                      [class.text-green-800]="execution.status === 'completed'"
                      [class.bg-red-100]="execution.status === 'failed'"
                      [class.text-red-800]="execution.status === 'failed'"
                      [class.bg-blue-100]="execution.status === 'running'"
                      [class.text-blue-800]="execution.status === 'running'"
                      [class.bg-gray-100]="execution.status === 'pending'"
                      [class.text-gray-800]="execution.status === 'pending'">
                  {{ execution.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDuration(execution.duration) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatTimestamp(execution.startedAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button (click)="selectExecution(execution.id)"
                        class="text-blue-600 hover:text-blue-900">
                  View Details
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Execution Details View -->
  <div *ngIf="selectedExecution && executionMetrics && !loading">
    <!-- Execution Overview -->
    <div class="bg-white rounded-lg shadow mb-8">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Execution Overview</h2>
        <p class="text-sm text-gray-600">ID: {{ selectedExecution }}</p>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="text-3xl font-bold text-blue-600">{{ executionMetrics.totalSteps || 0 }}</div>
            <div class="text-sm text-gray-600">Total Steps</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600">{{ executionMetrics.completedSteps || 0 }}</div>
            <div class="text-sm text-gray-600">Completed</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-red-600">{{ executionMetrics.failedSteps || 0 }}</div>
            <div class="text-sm text-gray-600">Failed</div>
          </div>
        </div>

        <!-- Progress Bar -->
        <div class="mt-6">
          <div class="flex justify-between text-sm text-gray-600 mb-2">
            <span>Progress</span>
            <span>{{ executionMetrics ? getProgressPercentage(executionMetrics) : 0 }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-3">
            <div class="bg-blue-600 h-3 rounded-full transition-all duration-300"
                 [style.width.%]="executionMetrics ? getProgressPercentage(executionMetrics) : 0"></div>
          </div>
        </div>

        <!-- Performance Metrics -->
        <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <span class="text-sm text-gray-600">Average Step Time:</span>
            <span class="ml-2 font-medium">{{ formatDuration(executionMetrics.averageStepTime || 0) }}</span>
          </div>
          <div>
            <span class="text-sm text-gray-600">Total Execution Time:</span>
            <span class="ml-2 font-medium">{{ formatDuration(executionMetrics.totalExecutionTime || 0) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Execution Events -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Execution Events</h2>
      </div>
      <div class="p-6">
        <div *ngIf="executionEvents.length === 0" class="text-center py-8 text-gray-500">
          No events recorded
        </div>
        <div *ngFor="let event of executionEvents" class="border-l-4 border-gray-200 pl-4 pb-4 mb-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <span [class]="getStatusColor(event.eventType)" class="font-medium">
                {{ formatEventType(event.eventType) }}
              </span>
              <span *ngIf="event.stepId" class="ml-2 text-sm text-gray-500">
                Step: {{ event.stepId }}
              </span>
            </div>
            <span class="text-sm text-gray-500">{{ formatTimestamp(event.timestamp) }}</span>
          </div>
          <div *ngIf="event.eventData" class="mt-2 text-sm text-gray-600">
            <pre class="whitespace-pre-wrap">{{ formatEventData(event.eventData) }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
