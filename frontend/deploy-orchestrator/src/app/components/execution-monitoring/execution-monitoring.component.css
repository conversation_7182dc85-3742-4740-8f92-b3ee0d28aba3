/* Custom styles for execution monitoring component */

.execution-card {
  transition: all 0.2s ease-in-out;
}

.execution-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.progress-bar {
  transition: width 0.3s ease-in-out;
}

.status-badge {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.event-timeline {
  position: relative;
}

.event-timeline::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #3b82f6, #e5e7eb);
}

.event-item {
  position: relative;
  padding-left: 2rem;
  margin-bottom: 1rem;
}

.event-item::before {
  content: '';
  position: absolute;
  left: -4px;
  top: 0.5rem;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #3b82f6;
}

.event-item.success::before {
  background: #10b981;
}

.event-item.error::before {
  background: #ef4444;
}

.event-item.warning::before {
  background: #f59e0b;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.metric-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  text-align: center;
}

.metric-value {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.metric-label {
  font-size: 0.875rem;
  opacity: 0.9;
}

.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-container {
    height: 250px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .execution-card {
    background-color: #1f2937;
    border-color: #374151;
  }
  
  .metric-card {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  }
}
