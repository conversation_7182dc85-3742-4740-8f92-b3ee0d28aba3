<div class="min-h-screen bg-gradient-to-br from-slate-50 via-indigo-50 to-purple-50">
  <!-- Header -->
  <div class="bg-white/80 backdrop-blur-xl border-b border-white/20 shadow-sm">
    <!-- ... existing code ... -->
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex flex-col items-center justify-center py-16 space-y-4">
    <!-- ... existing code ... -->
  </div>
  <!-- Error State -->
  <div *ngIf="error" class="max-w-7xl mx-auto px-6 py-6">
    <!-- ... existing code ... -->
  </div>
  <!-- Main Content -->
  <div *ngIf="!loading && !error" class="max-w-7xl mx-auto px-6 py-6">
    
    <!-- Component List View (when no specific component selected) -->
    <div *ngIf="!component" class="space-y-6">
      <!-- ... existing code ... -->
    </div>
    <!-- Component Detail View (when specific component selected) -->
    <div *ngIf="component" class="space-y-6">
      <!-- ... existing code ... -->
    </div>
  </div>
</div>
<!-- Create Component Modal -->
<div *ngIf="showCreateModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4" (click)="closeModals()">
  <div class="bg-white/95 backdrop-blur-lg rounded-2xl border border-white/30 shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto" (click)="$event.stopPropagation()">
    <div class="sticky top-0 bg-white/95 backdrop-blur-lg border-b border-slate-200 px-6 py-4 rounded-t-2xl">
      <!-- ... existing code ... -->
    </div>
    <form [formGroup]="componentForm" (ngSubmit)="createComponent()" class="p-6">
      <div class="space-y-8">
        <!-- Basic Information -->
        <div class="space-y-6">
          <!-- ... existing code ... -->
        </div>
        <!-- Repository Configuration -->
        <div formGroupName="repository" class="space-y-6">
          <!-- ... existing code ... -->
        </div>
        <!-- Deployment Strategy -->
        <div class="space-y-6">
          <!-- ... existing code ... -->
        </div>
        <!-- Modal Actions -->
        <div class="sticky bottom-0 bg-white/95 backdrop-blur-lg border-t border-slate-200 px-6 py-4 rounded-b-2xl">
          <!-- ... existing code ... -->
        </div>
      </div>
    </form>
  </div>
</div>

<!-- Deploy Component Modal -->
<div *ngIf="showDeployModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4" (click)="closeModals()">
  <div class="bg-white/95 backdrop-blur-lg rounded-2xl border border-white/30 shadow-2xl w-full max-w-md" (click)="$event.stopPropagation()">
    <div class="bg-white/95 backdrop-blur-lg border-b border-slate-200 px-6 py-4 rounded-t-2xl">
      <!-- ... existing code ... -->
    </div>
    <form [formGroup]="deploymentForm" (ngSubmit)="deployComponent()" class="p-6">
      <div class="space-y-6">
        <!-- ... existing code ... -->
      </div>

      <!-- Modal Actions -->
      <div class="flex space-x-4 justify-end mt-8 pt-6 border-t border-slate-200">
        <!-- ... existing code ... -->
      </div>
    </form>
  </div>
</div>
