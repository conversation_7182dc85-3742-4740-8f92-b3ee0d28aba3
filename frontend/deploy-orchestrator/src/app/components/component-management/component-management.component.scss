/* Component Management - Tailwind CSS Optimized Styles */

/* Custom animations for smooth transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0,-4px,0);
  }
}

.component-management {
  /* Enhanced animations for better UX */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  .animate-pulse-custom {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-spin-custom {
    animation: spin 1s linear infinite;
  }

  .animate-shimmer {
    animation: shimmer 2s linear infinite;
    background: linear-gradient(
      to right,
      #f6f7f8 0%,
      #edeef1 20%,
      #f6f7f8 40%,
      #f6f7f8 100%
    );
    background-size: 200px 100%;
  }

  .animate-bounce {
    animation: bounce 1s infinite;
  }

  /* Glass morphism effect utilities */
  .glass-card {
    background-color: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(0.375rem);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .glass-modal {
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(0.75rem);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  .glass-sidebar {
    background-color: rgba(15, 23, 42, 0.9);
    backdrop-filter: blur(1rem);
    border-right: 1px solid rgba(51, 65, 85, 0.5);
  }

  /* Custom gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .gradient-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  }

  .gradient-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .gradient-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
  }

  /* Enhanced hover effects */
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
  }

  .hover-scale {
    transition: transform 0.2s ease-in-out;

    &:hover {
      transform: scale(1.05);
    }
  }

  .hover-glow {
    transition: all 0.3s ease-in-out;

    &:hover {
      box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
    }
  }

  /* Status indicators with enhanced styling */
  .status-indicator {
    position: relative;
    display: inline-flex;
    align-items: center;

    &::before {
      content: '';
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 8px;
      background-color: currentColor;
      animation: pulse 2s infinite;
    }

    &.status-healthy::before {
      background-color: #10b981;
      box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
    }

    &.status-warning::before {
      background-color: #f59e0b;
      box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
    }

    &.status-error::before {
      background-color: #ef4444;
      box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
    }
  }

  /* Loading skeleton styles */
  .skeleton {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    background-color: #e5e7eb;
    border-radius: 0.25rem;
  }

  .skeleton.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
  }

  .skeleton.skeleton-text:last-child {
    margin-bottom: 0;
  }

  .skeleton.skeleton-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
  }

  .skeleton.skeleton-card {
    height: 8rem;
  }

  /* Enhanced form controls for better UX */
  .form-control-enhanced {
    transition: all 0.2s ease-in-out;

    &:focus {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  /* Tab navigation enhancements */
  .tab-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 1px;
  }

  /* Modal backdrop enhancements */
  .modal-backdrop-enhanced {
    backdrop-filter: blur(8px);
    background: rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease-in-out;
  }

  /* Card hover states with enhanced shadows */
  .card-interactive {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        0 0 0 1px rgba(99, 102, 241, 0.1);
    }
  }

  /* Progress indicators */
  .progress-bar {
    position: relative;
    overflow: hidden;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 9999px;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
      );
      background-size: 1rem 1rem;
      animation: progress-bar-stripes 1s linear infinite;
    }
  }

  @keyframes progress-bar-stripes {
    0% {
      background-position: 1rem 0;
    }
    100% {
      background-position: 0 0;
    }
  }

  /* Custom scrollbar styling */
  .custom-scrollbar {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 3px;

      &:hover {
        background: rgba(0, 0, 0, 0.3);
      }
    }
  }

  /* Responsive breakpoint helpers */
  @media (max-width: 640px) {
    .mobile-stack {
      flex-direction: column;
      gap: 1rem;
    }

    .mobile-full {
      width: 100%;
    }
  }

  @media (max-width: 768px) {
    .tablet-hide {
      display: none;
    }

    .tablet-show {
      display: block;
    }
  }

  /* Component-specific utilities */
  .detail-card {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(0.375rem);
    border-radius: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    transition: all 0.3s ease-in-out;

    &:hover {
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
      transform: translateY(-0.25rem);
    }
  }

  .timeline-item {
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      left: -9px;
      top: 0;
      width: 2px;
      height: 100%;
      background: linear-gradient(to bottom, #667eea, #764ba2);
    }
  }

  .stats-card {
    background: linear-gradient(135deg, #ffffff, #f9fafb);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(229, 231, 235, 0.5);
    transition: all 0.3s ease-in-out;

    &:hover {
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
      border-color: rgb(191, 219, 254);
    }
  }

  .filter-tab {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease-in-out;

    &.active {
      background-color: rgb(219, 234, 254);
      color: rgb(29, 78, 216);
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }

    &:not(.active) {
      color: rgb(75, 85, 99);
      
      &:hover {
        color: rgb(31, 41, 55);
        background-color: rgb(243, 244, 246);
      }
    }
  }

  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .glass-card {
      background-color: rgba(30, 41, 59, 0.7);
      border-color: rgba(51, 65, 85, 0.5);
    }

    .stats-card {
      background: linear-gradient(135deg, rgb(30, 41, 59), rgb(15, 23, 42));
      border-color: rgba(51, 65, 85, 0.5);
    }
  }

  /* Print styles */
  @media print {
    .hover-lift,
    .hover-scale,
    .hover-glow {
      transform: none !important;
      box-shadow: none !important;
    }

    .animate-pulse,
    .animate-spin,
    .animate-bounce {
      animation: none !important;
    }
  }
}