import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { ApplicationService } from '../../services/application.service';
import { 
  Application, 
  Component as AppComponent, 
  ComponentConfiguration,
  DeploymentPipeline,
  HealthStatus,
  DeploymentStatus,
  Environment,
  MetricData
} from '../../models/application.model';

@Component({
  selector: 'app-component-management',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './component-management.component.html',
  styleUrls: ['./component-management.component.scss']
})
export class ComponentManagementComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Route Parameters
  applicationId!: string;
  componentId: string | null = null;

  // Data
  application: Application | null = null;
  component: AppComponent | null = null;
  components: AppComponent[] = [];
  environments: Environment[] = [];
  loading = false;
  error: string | null = null;

  // UI State
  activeTab: 'configuration' | 'deployments' | 'monitoring' | 'dependencies' = 'configuration';
  tabs: ('configuration' | 'deployments' | 'monitoring' | 'dependencies')[] = 
    ['configuration', 'deployments', 'monitoring', 'dependencies'];
  showCreateModal = false;
  showEditModal = false;
  showDeployModal = false;

  // Forms
  componentForm: FormGroup;
  deploymentForm: FormGroup;

  // Component Types and Templates
  componentTypes = [
    { value: 'microservice', label: 'Microservice', icon: 'fas fa-cube' },
    { value: 'frontend', label: 'Frontend Application', icon: 'fas fa-desktop' },
    { value: 'database', label: 'Database', icon: 'fas fa-database' },
    { value: 'queue', label: 'Message Queue', icon: 'fas fa-stream' },
    { value: 'cache', label: 'Cache', icon: 'fas fa-memory' },
    { value: 'cdn', label: 'CDN', icon: 'fas fa-cloud' },
    { value: 'api-gateway', label: 'API Gateway', icon: 'fas fa-gateway' }
  ];

  // Deployment Strategies
  deploymentStrategies = [
    { value: 'rolling', label: 'Rolling Deployment', description: 'Gradual replacement of instances' },
    { value: 'blue-green', label: 'Blue-Green Deployment', description: 'Switch between two environments' },
    { value: 'canary', label: 'Canary Deployment', description: 'Gradual traffic shift to new version' },
    { value: 'recreate', label: 'Recreate', description: 'Stop all instances and create new ones' }
  ];

  constructor(
    private applicationService: ApplicationService,
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder
  ) {
    this.componentForm = this.createComponentForm();
    this.deploymentForm = this.createDeploymentForm();
  }

  ngOnInit(): void {
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      this.applicationId = params['applicationId'];
      this.componentId = params['componentId'] || null;
      
      // Only load application-specific data if we have an applicationId
      if (this.applicationId) {
        this.loadData();
      } else {
        // For general component management, show a message or different UI
        this.loading = false;
      }
    });

    this.loadEnvironments();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createComponentForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      type: ['microservice', Validators.required],
      version: ['1.0.0', Validators.required],
      repository: this.fb.group({
        url: ['', Validators.required],
        branch: ['main', Validators.required],
        buildPath: ['./'],
        dockerfilePath: ['Dockerfile']
      }),
      configuration: this.fb.group({
        resources: this.fb.group({
          cpu: ['500m'],
          memory: ['512Mi'],
          replicas: [1, [Validators.required, Validators.min(1)]]
        }),
        networking: this.fb.group({
          ports: this.fb.array([]),
          ingress: this.fb.group({
            enabled: [false],
            hostname: [''],
            path: ['/'],
            tls: [false]
          })
        }),
        environment: this.fb.group({
          variables: this.fb.array([])
        }),
        healthChecks: this.fb.group({
          enabled: [true],
          readinessProbe: this.fb.group({
            path: ['/health'],
            port: [8080],
            initialDelaySeconds: [30]
          }),
          livenessProbe: this.fb.group({
            path: ['/health'],
            port: [8080],
            initialDelaySeconds: [30]
          })
        })
      }),
      deploymentStrategy: ['rolling'],
      dependencies: this.fb.array([])
    });
  }

  private createDeploymentForm(): FormGroup {
    return this.fb.group({
      environment: ['', Validators.required],
      version: ['', Validators.required],
      strategy: ['rolling'],
      rollbackOnFailure: [true],
      notificationChannels: this.fb.array([])
    });
  }

  private loadData(): void {
    this.loading = true;
    this.error = null;

    // Load application
    this.applicationService.getApplication(this.applicationId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (application) => {
          this.application = application;
          
          // Load components
          this.applicationService.getApplicationComponents(this.applicationId)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
              next: (components) => {
                this.components = components;
                
                // If specific component requested, load it
                if (this.componentId) {
                  this.component = this.components.find(c => c.id === this.componentId) || null;
                  if (this.component) {
                    this.populateComponentForm(this.component);
                  }
                }
                this.loading = false;
              },
              error: (error) => {
                this.error = error.message || 'Failed to load components';
                this.loading = false;
              }
            });
        },
        error: (error) => {
          this.error = error.message || 'Failed to load application';
          this.loading = false;
        }
      });
  }

  private loadEnvironments(): void {
    this.applicationService.getEnvironments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (environments) => {
          this.environments = environments;
        },
        error: (error) => {
          console.error('Failed to load environments:', error);
        }
      });
  }

  private populateComponentForm(component: AppComponent): void {
    this.componentForm.patchValue({
      name: component.name,
      description: component.description,
      type: component.type,
      version: component.version,
      repository: component.repository,
      configuration: component.configuration,
      deploymentStrategy: component.deploymentStrategy
    });
  }

  // Component Management
  createComponent(): void {
    if (!this.componentForm.valid) {
      this.markFormGroupTouched(this.componentForm);
      return;
    }

    const componentData = this.componentForm.value;
    this.applicationService.createComponent(this.applicationId, componentData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (newComponent) => {
          this.components.push(newComponent);
          this.showCreateModal = false;
          this.componentForm.reset();
        },
        error: (error) => {
          this.error = error.message || 'Failed to create component';
        }
      });
  }

  updateComponent(): void {
    if (!this.component || !this.componentForm.valid) {
      this.markFormGroupTouched(this.componentForm);
      return;
    }

    const componentData = this.componentForm.value;
    this.applicationService.updateComponent(
      this.applicationId, 
      this.component.id, 
      componentData
    ).pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (updatedComponent) => {
        const index = this.components.findIndex(c => c.id === this.component!.id);
        if (index >= 0) {
          this.components[index] = updatedComponent;
        }
        
        this.component = updatedComponent;
        this.showEditModal = false;
      },
      error: (error) => {
        this.error = error.message || 'Failed to update component';
      }
    });
  }

  deleteComponent(componentId: string): void {
    if (!confirm('Are you sure you want to delete this component?')) {
      return;
    }

    this.applicationService.deleteComponent(this.applicationId, componentId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.components = this.components.filter(c => c.id !== componentId);
          
          if (this.component?.id === componentId) {
            this.component = null;
            this.router.navigate(['..'], { relativeTo: this.route });
          }
        },
        error: (error) => {
          this.error = error.message || 'Failed to delete component';
        }
      });
  }

  // Deployment Management
  deployComponent(): void {
    if (!this.component || !this.deploymentForm.valid) {
      this.markFormGroupTouched(this.deploymentForm);
      return;
    }

    const deploymentData = this.deploymentForm.value;
    this.applicationService.deployComponent(
      this.component.id,
      deploymentData.environment,
      deploymentData.version
    ).pipe(takeUntil(this.destroy$))
    .subscribe({
      next: () => {
        this.showDeployModal = false;
        this.deploymentForm.reset();
        this.loadData(); // Refresh data
      },
      error: (error) => {
        this.error = error.message || 'Failed to deploy component';
      }
    });
  }

  rollbackComponent(environment: string): void {
    if (!this.component) return;

    if (!confirm(`Are you sure you want to rollback ${this.component.name} in ${environment}?`)) {
      return;
    }

    this.applicationService.rollbackComponent(
      this.component.id,
      environment,
      'previous' // Use previous version as default
    ).pipe(takeUntil(this.destroy$))
    .subscribe({
      next: () => {
        this.loadData(); // Refresh data
      },
      error: (error) => {
        this.error = error.message || 'Failed to rollback component';
      }
    });
  }

  // Navigation
  selectComponent(component: AppComponent): void {
    this.router.navigate(['..', component.id], { relativeTo: this.route });
  }

  navigateToApplication(): void {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }

  // UI Management
  setActiveTab(tab: 'configuration' | 'deployments' | 'monitoring' | 'dependencies'): void {
    this.activeTab = tab;
  }

  // Utility Methods
  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  getComponentTypeIcon(type: string): string {
    const componentType = this.componentTypes.find(t => t.value === type);
    return componentType?.icon || 'fas fa-cube';
  }

  // Enhanced Tailwind CSS utility methods
  getStatusBadgeClasses(status: HealthStatus | undefined): string {
    const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium';
    switch (status) {
      case HealthStatus.HEALTHY: 
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case HealthStatus.WARNING: 
        return `${baseClasses} bg-yellow-100 text-yellow-800 border border-yellow-200`;
      case HealthStatus.ERROR: 
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      default: 
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  }

  getDeploymentStatusBadgeClasses(status: DeploymentStatus): string {
    const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium';
    switch (status) {
      case DeploymentStatus.DEPLOYED: 
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case DeploymentStatus.DEPLOYING: 
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case DeploymentStatus.FAILED: 
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      case DeploymentStatus.ROLLING_BACK: 
        return `${baseClasses} bg-orange-100 text-orange-800 border border-orange-200`;
      default: 
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  }

  getStatusIndicatorClasses(status: HealthStatus | undefined): string {
    const baseClasses = 'flex items-center space-x-2';
    switch (status) {
      case HealthStatus.HEALTHY: 
        return `${baseClasses} text-green-600`;
      case HealthStatus.WARNING: 
        return `${baseClasses} text-yellow-600`;
      case HealthStatus.ERROR: 
        return `${baseClasses} text-red-600`;
      default: 
        return `${baseClasses} text-gray-500`;
    }
  }

  getProgressBarClasses(percentage: number): string {
    const baseClasses = 'h-2 rounded-full transition-all duration-300';
    if (percentage >= 80) {
      return `${baseClasses} bg-green-500`;
    } else if (percentage >= 60) {
      return `${baseClasses} bg-yellow-500`;
    } else if (percentage >= 40) {
      return `${baseClasses} bg-orange-500`;
    } else {
      return `${baseClasses} bg-red-500`;
    }
  }

  getCardHoverClasses(): string {
    return 'transition-all duration-300 ease-in-out hover:shadow-lg hover:-translate-y-1 hover:scale-[1.02] cursor-pointer';
  }

  getButtonClasses(variant: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' = 'primary', size: 'sm' | 'md' | 'lg' = 'md'): string {
    const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
    
    const sizeClasses = {
      sm: 'px-3 py-1.5 text-xs',
      md: 'px-4 py-2 text-sm',
      lg: 'px-6 py-3 text-base'
    };

    const variantClasses = {
      primary: 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white focus:ring-blue-500 shadow-lg hover:shadow-xl',
      secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800 focus:ring-gray-500',
      success: 'bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white focus:ring-green-500 shadow-lg hover:shadow-xl',
      danger: 'bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white focus:ring-red-500 shadow-lg hover:shadow-xl',
      warning: 'bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-white focus:ring-yellow-500 shadow-lg hover:shadow-xl'
    };

    return `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]}`;
  }

  getFormInputClasses(hasError: boolean = false): string {
    const baseClasses = 'block w-full px-3 py-2 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200';
    
    if (hasError) {
      return `${baseClasses} border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500`;
    }
    
    return `${baseClasses} border-gray-300 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-400`;
  }

  getTabClasses(isActive: boolean): string {
    const baseClasses = 'px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 relative';
    
    if (isActive) {
      return `${baseClasses} bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg`;
    }
    
    return `${baseClasses} text-gray-600 hover:text-gray-800 hover:bg-gray-100`;
  }

  getModalClasses(): string {
    return 'fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm';
  }

  getModalContentClasses(): string {
    return 'bg-white/90 backdrop-blur-md rounded-2xl shadow-2xl border border-white/30 max-w-2xl w-full max-h-[90vh] overflow-y-auto';
  }

  // Animation utility methods
  getFadeInClasses(): string {
    return 'animate-fade-in';
  }

  getSlideInClasses(): string {
    return 'animate-slide-in';
  }

  getPulseClasses(): string {
    return 'animate-pulse';
  }

  getSpinClasses(): string {
    return 'animate-spin';
  }

  // Legacy method compatibility (keeping for backward compatibility)
  getStatusColor(status: HealthStatus | undefined): string {
    switch (status) {
      case HealthStatus.HEALTHY: return 'text-green-600';
      case HealthStatus.WARNING: return 'text-yellow-600';
      case HealthStatus.ERROR: return 'text-red-600';
      default: return 'text-gray-500';
    }
  }

  getDeploymentStatusColor(status: DeploymentStatus): string {
    switch (status) {
      case DeploymentStatus.DEPLOYED: return 'text-green-600';
      case DeploymentStatus.DEPLOYING: return 'text-blue-600';
      case DeploymentStatus.FAILED: return 'text-red-600';
      case DeploymentStatus.ROLLING_BACK: return 'text-orange-600';
      default: return 'text-gray-500';
    }
  }

  // Modal Controls
  openCreateModal(): void {
    this.componentForm.reset();
    this.showCreateModal = true;
  }

  openEditModal(): void {
    if (this.component) {
      this.populateComponentForm(this.component);
      this.showEditModal = true;
    }
  }

  openDeployModal(): void {
    this.deploymentForm.reset();
    this.showDeployModal = true;
  }

  closeModals(): void {
    this.showCreateModal = false;
    this.showEditModal = false;
    this.showDeployModal = false;
    this.error = null;
  }
}
