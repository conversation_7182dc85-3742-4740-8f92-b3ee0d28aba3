import { Compo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';

import {
  WorkflowExecutionService,
  WorkflowExecution,
  WorkflowStepExecution,
  LogEntry,
  ExecutionStatus,
  StepStatus
} from '../../services/workflow-execution.service';

import {
  RealtimeLoggingService,
  LogViewerConfig
} from '../../services/realtime-logging.service';

@Component({
  selector: 'app-workflow-execution',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './workflow-execution.component.html',
  styleUrls: ['./workflow-execution.component.css']
})
export class WorkflowExecutionComponent implements OnInit, OnDestroy {
  @Input() executionId?: string;
  @ViewChild('logContainer', { static: false }) logContainer!: ElementRef;

  execution: WorkflowExecution | null = null;
  logs: LogEntry[] = [];
  loading = false;
  error: string | null = null;

  // Real-time logging
  logSubscription: Subscription | null = null;
  isConnectedToLogs = false;

  // Log viewer configuration
  logConfig: LogViewerConfig = {
    autoScroll: true,
    maxLogs: 1000,
    showTimestamps: true,
    showProgress: true,
    highlightDuration: 2000
  };

  // Filtering
  selectedLogLevel = '';
  selectedStep = '';
  logSearchTerm = '';

  // UI state
  showStepDetails = false;
  selectedStepIndex = -1;
  showLogFilters = false;
  showExecutionDetails = false;

  // Refresh interval
  refreshInterval: any;
  refreshRate = 5000; // 5 seconds

  // Log levels for filtering
  readonly logLevels = ['debug', 'info', 'warn', 'error'];

  private subscriptions: Subscription[] = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private executionService: WorkflowExecutionService,
    private loggingService: RealtimeLoggingService
  ) {}

  ngOnInit(): void {
    // Get execution ID from route or input
    if (this.executionId) {
      this.loadExecution(this.executionId);
    } else {
      this.route.params.subscribe(params => {
        if (params['id']) {
          this.loadExecution(params['id']);
        }
      });
    }
  }

  ngOnDestroy(): void {
    this.cleanup();
  }

  private cleanup(): void {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
    
    // Unsubscribe from real-time logs
    if (this.execution) {
      this.loggingService.unsubscribeFromLogs(this.execution.id);
    }
    
    // Clear refresh interval
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }
  }

  // Data loading
  async loadExecution(id: string): Promise<void> {
    this.loading = true;
    this.error = null;

    try {
      this.execution = await this.executionService.getExecution(id).toPromise();
      
      if (this.execution) {
        // Load historical logs
        await this.loadHistoricalLogs();
        
        // Subscribe to real-time logs if execution is running
        if (this.isExecutionRunning()) {
          this.subscribeToRealtimeLogs();
        }
        
        // Start periodic refresh for execution status
        this.startPeriodicRefresh();
      }
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to load execution';
      console.error('Error loading execution:', error);
    } finally {
      this.loading = false;
    }
  }

  async loadHistoricalLogs(): Promise<void> {
    if (!this.execution) return;

    try {
      const response = await this.executionService.getExecutionLogs(this.execution.id, {
        limit: this.logConfig.maxLogs
      }).toPromise();
      
      this.logs = response?.logs || [];
      this.applyLogFilters();
    } catch (error) {
      console.error('Error loading historical logs:', error);
    }
  }

  subscribeToRealtimeLogs(): void {
    if (!this.execution || this.logSubscription) return;

    this.logSubscription = this.loggingService.subscribeToLogs(this.execution.id).subscribe({
      next: (logEntry: LogEntry) => {
        this.addLogEntry(logEntry);
      },
      error: (error) => {
        console.error('Real-time log subscription error:', error);
        this.isConnectedToLogs = false;
      }
    });

    // Check connection status
    setTimeout(() => {
      this.isConnectedToLogs = this.loggingService.isConnected(this.execution!.id);
    }, 1000);
  }

  addLogEntry(logEntry: LogEntry): void {
    this.logs.push(logEntry);
    
    // Limit logs in memory
    if (this.logs.length > this.logConfig.maxLogs) {
      this.logs = this.logs.slice(-this.logConfig.maxLogs);
    }
    
    this.applyLogFilters();
    
    // Auto-scroll if enabled
    if (this.logConfig.autoScroll) {
      this.scrollToBottom();
    }
  }

  startPeriodicRefresh(): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }

    this.refreshInterval = setInterval(async () => {
      if (this.execution && this.isExecutionRunning()) {
        try {
          const updated = await this.executionService.getExecution(this.execution.id).toPromise();
          if (updated) {
            this.execution = updated;
            
            // Stop real-time logging if execution completed
            if (!this.isExecutionRunning() && this.logSubscription) {
              this.logSubscription.unsubscribe();
              this.logSubscription = null;
              this.isConnectedToLogs = false;
            }
          }
        } catch (error) {
          console.error('Error refreshing execution:', error);
        }
      } else {
        // Stop refresh if execution is complete
        clearInterval(this.refreshInterval);
        this.refreshInterval = null;
      }
    }, this.refreshRate);
  }

  // Execution operations
  async cancelExecution(): Promise<void> {
    if (!this.execution || !this.isExecutionRunning()) return;

    const reason = prompt('Please provide a reason for cancellation:');
    if (!reason) return;

    try {
      await this.executionService.cancelExecution(this.execution.id, reason).toPromise();
      await this.loadExecution(this.execution.id);
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to cancel execution';
    }
  }

  async retryExecution(): Promise<void> {
    if (!this.execution) return;

    try {
      const newExecution = await this.executionService.retryExecution(this.execution.id).toPromise();
      if (newExecution) {
        this.router.navigate(['/executions', newExecution.id]);
      }
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to retry execution';
    }
  }

  // Step operations
  async retryStep(step: WorkflowStepExecution): Promise<void> {
    if (!this.execution) return;

    try {
      await this.executionService.retryStep(this.execution.id, step.id).toPromise();
      await this.loadExecution(this.execution.id);
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to retry step';
    }
  }

  async skipStep(step: WorkflowStepExecution): Promise<void> {
    if (!this.execution) return;

    const reason = prompt('Please provide a reason for skipping this step:');
    if (!reason) return;

    try {
      await this.executionService.skipStep(this.execution.id, step.id, reason).toPromise();
      await this.loadExecution(this.execution.id);
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to skip step';
    }
  }

  // Log management
  applyLogFilters(): void {
    // This would be called to filter logs based on current filter settings
    // Implementation depends on how you want to handle filtering
  }

  clearLogs(): void {
    this.logs = [];
  }

  exportLogs(): void {
    const filename = `execution-${this.execution?.id}-logs-${new Date().toISOString().slice(0, 19)}.txt`;
    this.loggingService.exportLogs(this.logs, filename);
  }

  toggleAutoScroll(): void {
    this.logConfig.autoScroll = !this.logConfig.autoScroll;
    if (this.logConfig.autoScroll) {
      this.scrollToBottom();
    }
  }

  scrollToBottom(): void {
    setTimeout(() => {
      if (this.logContainer) {
        const container = this.logContainer.nativeElement;
        container.scrollTop = container.scrollHeight;
      }
    });
  }

  // UI helpers
  selectStep(index: number): void {
    this.selectedStepIndex = index;
    this.showStepDetails = true;
  }

  closeStepDetails(): void {
    this.showStepDetails = false;
    this.selectedStepIndex = -1;
  }

  toggleLogFilters(): void {
    this.showLogFilters = !this.showLogFilters;
  }

  toggleExecutionDetails(): void {
    this.showExecutionDetails = !this.showExecutionDetails;
  }

  // Status helpers
  isExecutionRunning(): boolean {
    return this.execution ? this.executionService.isExecutionRunning(this.execution) : false;
  }

  isExecutionComplete(): boolean {
    return this.execution ? this.executionService.isExecutionComplete(this.execution) : false;
  }

  getExecutionProgress(): number {
    return this.execution ? this.executionService.getExecutionProgress(this.execution) : 0;
  }

  getStepProgress(step: WorkflowStepExecution): number {
    return this.executionService.getStepProgress(step);
  }

  // CSS classes
  getExecutionStatusClass(): string {
    if (!this.execution) return '';
    
    switch (this.execution.status) {
      case 'pending': return 'bg-gray-100 text-gray-800';
      case 'running': return 'bg-blue-100 text-blue-800';
      case 'success': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  getStepStatusClass(status: StepStatus): string {
    switch (status) {
      case 'pending': return 'bg-gray-100 text-gray-800';
      case 'running': return 'bg-blue-100 text-blue-800';
      case 'success': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'skipped': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  getLogLevelClass(level: string): string {
    return this.loggingService.getLogLevelClass(level);
  }

  // Getters for template
  get selectedStep(): WorkflowStepExecution | null {
    if (!this.execution || this.selectedStepIndex < 0) return null;
    return this.execution.steps[this.selectedStepIndex] || null;
  }

  get filteredLogs(): LogEntry[] {
    return this.loggingService.filterLogs(this.logs, {
      filterLevel: this.selectedLogLevel || undefined,
      filterStep: this.selectedStep || undefined
    });
  }

  get availableSteps(): string[] {
    if (!this.execution) return [];
    return [...new Set(this.execution.steps.map(step => step.stepName))];
  }

  get logStatistics() {
    return this.loggingService.getLogStatistics(this.logs);
  }

  formatDuration(milliseconds?: number): string {
    if (!milliseconds) return 'N/A';
    
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  formatLogEntry(log: LogEntry): string {
    return this.loggingService.formatLogEntry(log, this.logConfig);
  }
}
