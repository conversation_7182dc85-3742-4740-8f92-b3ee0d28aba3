<div class="real-time-logs h-full flex flex-col">
  <!-- Header with Controls -->
  <div class="logs-header bg-gray-800 text-white p-4 flex items-center justify-between">
    <div class="flex items-center space-x-4">
      <h3 class="text-lg font-semibold">Real-time Logs</h3>
      
      <!-- Connection Status -->
      <div class="flex items-center space-x-2">
        <i [class]="getConnectionStatusIcon()" [ngClass]="getConnectionStatusClass()"></i>
        <span class="text-sm" [ngClass]="getConnectionStatusClass()">
          {{ connectionStatus | titlecase }}
        </span>
        <button *ngIf="connectionStatus === 'error' || connectionStatus === 'disconnected'"
                (click)="reconnect()"
                class="text-blue-400 hover:text-blue-300 text-sm">
          Reconnect
        </button>
      </div>

      <!-- Statistics -->
      <div class="flex items-center space-x-4 text-sm">
        <span class="bg-gray-700 px-2 py-1 rounded">
          Total: {{ stats.total }}
        </span>
        <span *ngIf="stats.error > 0" class="bg-red-600 px-2 py-1 rounded">
          Errors: {{ stats.error }}
        </span>
        <span *ngIf="stats.warn > 0" class="bg-yellow-600 px-2 py-1 rounded">
          Warnings: {{ stats.warn }}
        </span>
      </div>
    </div>

    <!-- Controls -->
    <div class="flex items-center space-x-2">
      <button (click)="togglePause()"
              [class]="isPaused ? 'bg-green-600 hover:bg-green-700' : 'bg-yellow-600 hover:bg-yellow-700'"
              class="px-3 py-1 rounded text-sm">
        <i [class]="isPaused ? 'fas fa-play' : 'fas fa-pause'" class="mr-1"></i>
        {{ isPaused ? 'Resume' : 'Pause' }}
      </button>
      
      <button (click)="toggleFollow()"
              [class]="isFollowing ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-600 hover:bg-gray-700'"
              class="px-3 py-1 rounded text-sm">
        <i class="fas fa-arrow-down mr-1"></i>
        {{ isFollowing ? 'Following' : 'Follow' }}
      </button>
      
      <button (click)="clearLogs()"
              class="bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm">
        <i class="fas fa-trash mr-1"></i>
        Clear
      </button>
      
      <button (click)="downloadLogs()"
              class="bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-sm">
        <i class="fas fa-download mr-1"></i>
        Download
      </button>
    </div>
  </div>

  <!-- Filters -->
  <div *ngIf="showFilters" class="filters bg-gray-100 p-3 border-b border-gray-200">
    <div class="flex flex-wrap items-center gap-4">
      <!-- Search -->
      <div *ngIf="showSearch" class="flex-1 min-w-64">
        <input type="text"
               [(ngModel)]="searchQuery"
               (input)="onFilterChange()"
               placeholder="Search logs..."
               class="w-full px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
      </div>

      <!-- Level Filter -->
      <div class="flex items-center space-x-2">
        <label class="text-sm font-medium text-gray-700">Level:</label>
        <select [(ngModel)]="levelFilter" (change)="onFilterChange()"
                class="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option value="">All</option>
          <option *ngFor="let level of getLogLevels()" [value]="level">
            {{ level | titlecase }}
          </option>
        </select>
      </div>

      <!-- Source Filter -->
      <div class="flex items-center space-x-2">
        <label class="text-sm font-medium text-gray-700">Source:</label>
        <select [(ngModel)]="sourceFilter" (change)="onFilterChange()"
                class="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option value="">All</option>
          <option *ngFor="let source of getUniqueSources()" [value]="source">
            {{ source }}
          </option>
        </select>
      </div>

      <!-- Date Filter -->
      <div class="flex items-center space-x-2">
        <label class="text-sm font-medium text-gray-700">Date:</label>
        <input type="date"
               [(ngModel)]="dateFilter"
               (change)="onFilterChange()"
               class="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
      </div>

      <!-- Options -->
      <div class="flex items-center space-x-4">
        <label class="flex items-center text-sm">
          <input type="checkbox" [(ngModel)]="showTimestamps" class="mr-1">
          Timestamps
        </label>
        <label class="flex items-center text-sm">
          <input type="checkbox" [(ngModel)]="showMetadata" class="mr-1">
          Metadata
        </label>
      </div>

      <!-- Clear Filters -->
      <button (click)="clearFilters()"
              class="text-blue-600 hover:text-blue-800 text-sm">
        Clear Filters
      </button>
    </div>
  </div>

  <!-- Logs Container -->
  <div #logsContainer 
       class="logs-container flex-1 bg-black text-green-400 font-mono text-sm overflow-y-auto p-4 space-y-1">
    
    <!-- No Logs Message -->
    <div *ngIf="filteredLogs.length === 0" class="text-center text-gray-500 py-8">
      <i class="fas fa-file-alt text-4xl mb-4"></i>
      <p>No logs available</p>
      <p *ngIf="connectionStatus === 'disconnected'" class="text-sm mt-2">
        Connect to start receiving logs
      </p>
    </div>

    <!-- Log Entries -->
    <div *ngFor="let log of filteredLogs; trackBy: trackByLogId" 
         class="log-entry flex items-start space-x-2 hover:bg-gray-900 px-2 py-1 rounded">
      
      <!-- Timestamp -->
      <span *ngIf="showTimestamps" class="timestamp text-gray-500 text-xs whitespace-nowrap">
        {{ formatTimestamp(log.timestamp) }}
      </span>

      <!-- Level Icon and Badge -->
      <div class="flex items-center space-x-1 whitespace-nowrap">
        <i [class]="getLevelIcon(log.level)" [ngClass]="getLevelClass(log.level)"></i>
        <span [ngClass]="getLevelClass(log.level)" class="text-xs font-bold uppercase">
          {{ log.level }}
        </span>
      </div>

      <!-- Source -->
      <span class="source text-blue-400 text-xs whitespace-nowrap">
        [{{ log.source }}]
      </span>

      <!-- Step Name (if available) -->
      <span *ngIf="log.stepName" class="step-name text-purple-400 text-xs whitespace-nowrap">
        [{{ log.stepName }}]
      </span>

      <!-- Message -->
      <span class="message flex-1 break-words">
        {{ log.message }}
      </span>

      <!-- Metadata Toggle -->
      <button *ngIf="log.metadata && showMetadata"
              (click)="toggleLogMetadata(log)"
              class="text-gray-500 hover:text-gray-300 text-xs">
        <i class="fas fa-info-circle"></i>
      </button>
    </div>

    <!-- Metadata Expansion -->
    <div *ngFor="let log of filteredLogs" class="metadata-container">
      <div *ngIf="log.metadata && showMetadata && log.showMetadata" 
           class="metadata bg-gray-800 border border-gray-600 rounded p-2 ml-8 mt-1 text-xs">
        <div class="text-gray-400 mb-1">Metadata:</div>
        <pre class="text-gray-300 whitespace-pre-wrap">{{ log.metadata | json }}</pre>
      </div>
    </div>
  </div>

  <!-- Footer with Scroll Controls -->
  <div class="logs-footer bg-gray-100 border-t border-gray-200 p-2 flex items-center justify-between">
    <div class="flex items-center space-x-4 text-sm text-gray-600">
      <span>Showing {{ filteredLogs.length }} of {{ logs.length }} logs</span>
      <span *ngIf="maxLines">Max: {{ maxLines }} lines</span>
    </div>
    
    <div class="flex items-center space-x-2">
      <button (click)="scrollToTop()"
              class="text-gray-600 hover:text-gray-800 text-sm">
        <i class="fas fa-arrow-up mr-1"></i>
        Top
      </button>
      <button (click)="scrollToBottom()"
              class="text-gray-600 hover:text-gray-800 text-sm">
        <i class="fas fa-arrow-down mr-1"></i>
        Bottom
      </button>
    </div>
  </div>
</div>
