.real-time-logs {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #000;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.logs-header {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  border-bottom: 1px solid #374151;
  
  .connection-status {
    &.connected {
      color: #10b981;
    }
    
    &.connecting {
      color: #f59e0b;
    }
    
    &.disconnected {
      color: #6b7280;
    }
    
    &.error {
      color: #ef4444;
    }
  }
  
  .stats-badge {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    
    &.error {
      background: #dc2626;
    }
    
    &.warning {
      background: #d97706;
    }
  }
  
  .control-button {
    transition: all 0.2s ease-in-out;
    
    &:hover {
      transform: translateY(-1px);
    }
  }
}

.filters {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  
  .filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    
    label {
      font-weight: 500;
      color: #374151;
      white-space: nowrap;
    }
    
    input, select {
      transition: all 0.2s ease-in-out;
      
      &:focus {
        transform: scale(1.02);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }
  }
}

.logs-container {
  background: #000;
  color: #00ff00;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 0.875rem;
  line-height: 1.4;
  overflow-y: auto;
  overflow-x: hidden;
  
  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #1a1a1a;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #404040;
    border-radius: 4px;
    
    &:hover {
      background: #606060;
    }
  }
  
  .log-entry {
    transition: background-color 0.1s ease-in-out;
    border-radius: 0.25rem;
    margin: 0.125rem 0;
    
    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }
    
    .timestamp {
      color: #6b7280;
      font-size: 0.75rem;
      min-width: 80px;
    }
    
    .level {
      min-width: 50px;
      font-weight: 600;
      
      &.debug {
        color: #60a5fa;
      }
      
      &.info {
        color: #34d399;
      }
      
      &.warn {
        color: #fbbf24;
      }
      
      &.error {
        color: #f87171;
      }
    }
    
    .source {
      color: #93c5fd;
      min-width: 100px;
    }
    
    .step-name {
      color: #c084fc;
      min-width: 120px;
    }
    
    .message {
      color: #e5e7eb;
      word-break: break-word;
      white-space: pre-wrap;
    }
  }
  
  .metadata-container {
    .metadata {
      background: #1f2937;
      border: 1px solid #374151;
      border-radius: 0.375rem;
      padding: 0.75rem;
      margin: 0.25rem 0 0.25rem 2rem;
      font-size: 0.75rem;
      
      pre {
        color: #d1d5db;
        white-space: pre-wrap;
        word-break: break-word;
      }
    }
  }
  
  .no-logs {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6b7280;
    
    .icon {
      font-size: 3rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }
  }
}

.logs-footer {
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  
  .footer-info {
    color: #6b7280;
    font-size: 0.875rem;
  }
  
  .scroll-controls {
    button {
      color: #6b7280;
      transition: color 0.2s ease-in-out;
      
      &:hover {
        color: #374151;
      }
    }
  }
}

// Level-specific styling
.level-debug {
  color: #60a5fa !important;
}

.level-info {
  color: #34d399 !important;
}

.level-warn {
  color: #fbbf24 !important;
}

.level-error {
  color: #f87171 !important;
}

// Animation for new log entries
.log-entry {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Connection status indicator
.connection-indicator {
  position: relative;
  
  &.connected::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
  }
  
  &.connecting::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #f59e0b;
    border-radius: 50%;
    animation: blink 1s infinite;
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

// Responsive design
@media (max-width: 768px) {
  .logs-header {
    flex-direction: column;
    gap: 1rem;
    
    .header-left,
    .header-right {
      width: 100%;
      justify-content: space-between;
    }
  }
  
  .filters {
    .filter-group {
      flex-direction: column;
      align-items: stretch;
      gap: 0.5rem;
    }
  }
  
  .logs-container {
    font-size: 0.75rem;
    
    .log-entry {
      flex-direction: column;
      align-items: stretch;
      gap: 0.25rem;
      
      .timestamp,
      .level,
      .source,
      .step-name {
        min-width: auto;
      }
    }
  }
}

// Dark mode enhancements
@media (prefers-color-scheme: dark) {
  .filters {
    background: #1f2937;
    border-color: #374151;
    
    input, select {
      background: #111827;
      border-color: #374151;
      color: #f9fafb;
    }
  }
  
  .logs-footer {
    background: #1f2937;
    border-color: #374151;
    color: #d1d5db;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .logs-container {
    .log-entry {
      .level {
        &.debug {
          color: #3b82f6;
        }
        
        &.info {
          color: #059669;
        }
        
        &.warn {
          color: #d97706;
        }
        
        &.error {
          color: #dc2626;
        }
      }
    }
  }
}

// Print styles
@media print {
  .logs-header,
  .filters,
  .logs-footer {
    display: none;
  }
  
  .logs-container {
    background: white;
    color: black;
    height: auto;
    overflow: visible;
    
    .log-entry {
      break-inside: avoid;
      
      .level {
        &.debug,
        &.info,
        &.warn,
        &.error {
          color: black;
        }
      }
      
      .source,
      .step-name,
      .message {
        color: black;
      }
    }
  }
}
