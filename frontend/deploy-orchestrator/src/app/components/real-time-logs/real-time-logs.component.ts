import { <PERSON>mpo<PERSON>, OnInit, On<PERSON><PERSON>roy, Input, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { WebSocketService } from '../../services/websocket.service';
import { PluginService } from '../../services/plugin.service';
import { DeploymentManagementService } from '../../services/deployment-management.service';

export interface LogEntry {
  id: string;
  timestamp: Date;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  source: string;
  workflowId?: string;
  stepName?: string;
  pluginName?: string;
  deploymentId?: string;
  applicationId?: string;
  componentId?: string;
  environmentId?: string;
  metadata?: { [key: string]: any };
  showMetadata?: boolean;
}

@Component({
  selector: 'app-real-time-logs',
  templateUrl: './real-time-logs.component.html',
  styleUrls: ['./real-time-logs.component.scss']
})
export class RealTimeLogsComponent implements OnInit, OnDestroy, AfterViewChecked {
  @Input() workflowId?: string;
  @Input() pluginName?: string;
  @Input() deploymentId?: string;
  @Input() autoScroll = true;
  @Input() maxLines = 1000;
  @Input() showFilters = true;
  @Input() showSearch = true;

  @ViewChild('logsContainer', { static: true }) logsContainer!: ElementRef;

  private destroy$ = new Subject<void>();
  private shouldScrollToBottom = false;

  // Data
  logs: LogEntry[] = [];
  filteredLogs: LogEntry[] = [];
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error' = 'disconnected';

  // Filters
  levelFilter: string = '';
  sourceFilter: string = '';
  searchQuery: string = '';
  dateFilter: string = '';

  // UI State
  isFollowing = true;
  showTimestamps = true;
  showMetadata = false;
  isPaused = false;

  // Statistics
  stats = {
    total: 0,
    debug: 0,
    info: 0,
    warn: 0,
    error: 0
  };

  constructor(
    private webSocketService: WebSocketService,
    private pluginService: PluginService,
    private deploymentService: DeploymentManagementService,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    // Check for deployment ID in query params
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {
      if (params['deploymentId']) {
        this.deploymentId = params['deploymentId'];
      }
      if (params['workflowId']) {
        this.workflowId = params['workflowId'];
      }
    });

    this.connectToLogs();
    this.setupFilters();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.webSocketService.disconnect();
  }

  ngAfterViewChecked(): void {
    if (this.shouldScrollToBottom && this.autoScroll && this.isFollowing) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  // WebSocket Connection
  connectToLogs(): void {
    this.connectionStatus = 'connecting';

    const params: any = {};
    if (this.workflowId) {
      params.workflowId = this.workflowId;
    }
    if (this.pluginName) {
      params.pluginName = this.pluginName;
    }
    if (this.deploymentId) {
      params.deploymentId = this.deploymentId;
    }

    this.webSocketService.connect('/ws/logs', params)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (message) => {
          this.handleLogMessage(message);
        },
        error: (error) => {
          console.error('WebSocket error:', error);
          this.connectionStatus = 'error';
        }
      });

    this.webSocketService.connectionStatus$
      .pipe(takeUntil(this.destroy$))
      .subscribe(status => {
        this.connectionStatus = status;
      });
  }

  handleLogMessage(message: any): void {
    if (this.isPaused) return;

    try {
      const logEntry: LogEntry = {
        id: message.id || this.generateLogId(),
        timestamp: new Date(message.timestamp),
        level: message.level || 'info',
        message: this.maskSecrets(message.message || ''),
        source: message.source || 'unknown',
        workflowId: message.workflowId,
        stepName: message.stepName,
        pluginName: message.pluginName,
        deploymentId: message.deploymentId,
        applicationId: message.applicationId,
        componentId: message.componentId,
        environmentId: message.environmentId,
        metadata: message.metadata
      };

      this.addLogEntry(logEntry);
    } catch (error) {
      console.error('Error parsing log message:', error);
    }
  }

  addLogEntry(logEntry: LogEntry): void {
    this.logs.push(logEntry);
    this.updateStats(logEntry);

    // Limit the number of logs to prevent memory issues
    if (this.logs.length > this.maxLines) {
      const removed = this.logs.splice(0, this.logs.length - this.maxLines);
      removed.forEach(log => this.updateStats(log, true));
    }

    this.applyFilters();
    this.shouldScrollToBottom = true;
  }

  // Secret Masking
  maskSecrets(message: string): string {
    const secretPatterns = [
      /password["\s]*[:=]["\s]*([^"\s,}]+)/gi,
      /token["\s]*[:=]["\s]*([^"\s,}]+)/gi,
      /key["\s]*[:=]["\s]*([^"\s,}]+)/gi,
      /secret["\s]*[:=]["\s]*([^"\s,}]+)/gi,
      /apikey["\s]*[:=]["\s]*([^"\s,}]+)/gi,
      /authorization["\s]*[:=]["\s]*([^"\s,}]+)/gi,
    ];

    let maskedMessage = message;
    secretPatterns.forEach(pattern => {
      maskedMessage = maskedMessage.replace(pattern, (match, value) => {
        return match.replace(value, '***');
      });
    });

    return maskedMessage;
  }

  // Filtering and Search
  setupFilters(): void {
    // Apply filters whenever any filter changes
    this.applyFilters();
  }

  applyFilters(): void {
    this.filteredLogs = this.logs.filter(log => {
      // Level filter
      if (this.levelFilter && log.level !== this.levelFilter) {
        return false;
      }

      // Source filter
      if (this.sourceFilter && log.source !== this.sourceFilter) {
        return false;
      }

      // Search query
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        if (!log.message.toLowerCase().includes(query) &&
            !log.source.toLowerCase().includes(query) &&
            !(log.stepName && log.stepName.toLowerCase().includes(query))) {
          return false;
        }
      }

      // Date filter
      if (this.dateFilter) {
        const filterDate = new Date(this.dateFilter);
        const logDate = new Date(log.timestamp);
        if (logDate.toDateString() !== filterDate.toDateString()) {
          return false;
        }
      }

      return true;
    });
  }

  onFilterChange(): void {
    this.applyFilters();
  }

  clearFilters(): void {
    this.levelFilter = '';
    this.sourceFilter = '';
    this.searchQuery = '';
    this.dateFilter = '';
    this.applyFilters();
  }

  // Statistics
  updateStats(logEntry: LogEntry, remove = false): void {
    const increment = remove ? -1 : 1;

    this.stats.total += increment;

    switch (logEntry.level) {
      case 'debug':
        this.stats.debug += increment;
        break;
      case 'info':
        this.stats.info += increment;
        break;
      case 'warn':
        this.stats.warn += increment;
        break;
      case 'error':
        this.stats.error += increment;
        break;
    }
  }

  // UI Actions
  toggleFollow(): void {
    this.isFollowing = !this.isFollowing;
    if (this.isFollowing) {
      this.scrollToBottom();
    }
  }

  togglePause(): void {
    this.isPaused = !this.isPaused;
  }

  clearLogs(): void {
    this.logs = [];
    this.filteredLogs = [];
    this.stats = {
      total: 0,
      debug: 0,
      info: 0,
      warn: 0,
      error: 0
    };
  }

  downloadLogs(): void {
    const logsText = this.filteredLogs.map(log =>
      `[${log.timestamp.toISOString()}] [${log.level.toUpperCase()}] [${log.source}] ${log.message}`
    ).join('\n');

    const blob = new Blob([logsText], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `logs-${new Date().toISOString().split('T')[0]}.txt`;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  scrollToBottom(): void {
    try {
      const container = this.logsContainer.nativeElement;
      container.scrollTop = container.scrollHeight;
    } catch (error) {
      console.error('Error scrolling to bottom:', error);
    }
  }

  scrollToTop(): void {
    try {
      const container = this.logsContainer.nativeElement;
      container.scrollTop = 0;
    } catch (error) {
      console.error('Error scrolling to top:', error);
    }
  }

  // Utility Methods
  generateLogId(): string {
    return `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  getLevelClass(level: string): string {
    switch (level) {
      case 'debug':
        return 'text-blue-400';
      case 'info':
        return 'text-green-400';
      case 'warn':
        return 'text-yellow-400';
      case 'error':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  }

  getLevelIcon(level: string): string {
    switch (level) {
      case 'debug':
        return 'fas fa-bug';
      case 'info':
        return 'fas fa-info-circle';
      case 'warn':
        return 'fas fa-exclamation-triangle';
      case 'error':
        return 'fas fa-times-circle';
      default:
        return 'fas fa-circle';
    }
  }

  getConnectionStatusClass(): string {
    switch (this.connectionStatus) {
      case 'connected':
        return 'text-green-500';
      case 'connecting':
        return 'text-yellow-500';
      case 'disconnected':
        return 'text-gray-500';
      case 'error':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  }

  getConnectionStatusIcon(): string {
    switch (this.connectionStatus) {
      case 'connected':
        return 'fas fa-circle';
      case 'connecting':
        return 'fas fa-spinner fa-spin';
      case 'disconnected':
        return 'fas fa-circle';
      case 'error':
        return 'fas fa-exclamation-circle';
      default:
        return 'fas fa-circle';
    }
  }

  formatTimestamp(timestamp: Date): string {
    return timestamp.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    });
  }

  getUniqueSources(): string[] {
    return [...new Set(this.logs.map(log => log.source))].sort();
  }

  getLogLevels(): string[] {
    return ['debug', 'info', 'warn', 'error'];
  }

  reconnect(): void {
    this.webSocketService.disconnect();
    setTimeout(() => {
      this.connectToLogs();
    }, 1000);
  }

  trackByLogId(index: number, log: LogEntry): string {
    return log.id;
  }

  toggleLogMetadata(log: LogEntry): void {
    (log as any).showMetadata = !(log as any).showMetadata;
  }
}
