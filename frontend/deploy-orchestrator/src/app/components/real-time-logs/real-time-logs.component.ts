import { Component, OnInit, OnDestroy, Input, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject, of } from 'rxjs';
import { takeUntil, catchError } from 'rxjs/operators';
import { WebSocketService } from '../../services/websocket.service';
import { PluginService } from '../../services/plugin.service';
import { DeploymentManagementService } from '../../services/deployment-management.service';
import { WorkflowExecutionService } from '../../services/workflow-execution.service';

export interface LogEntry {
  id: string;
  timestamp: Date;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  source: string;
  workflowId?: string;
  stepName?: string;
  pluginName?: string;
  deploymentId?: string;
  applicationId?: string;
  componentId?: string;
  environmentId?: string;
  metadata?: { [key: string]: any };
  showMetadata?: boolean;
}

@Component({
  selector: 'app-real-time-logs',
  templateUrl: './real-time-logs.component.html',
  styleUrls: ['./real-time-logs.component.scss']
})
export class RealTimeLogsComponent implements OnInit, OnDestroy, AfterViewChecked {
  @Input() workflowId?: string;
  @Input() pluginName?: string;
  @Input() deploymentId?: string;
  @Input() autoScroll = true;
  @Input() maxLines = 1000;
  @Input() showFilters = true;
  @Input() showSearch = true;

  @ViewChild('logsContainer', { static: true }) logsContainer!: ElementRef;

  private destroy$ = new Subject<void>();
  private shouldScrollToBottom = false;

  // Data
  logs: LogEntry[] = [];
  filteredLogs: LogEntry[] = [];
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error' = 'disconnected';

  // Filters
  levelFilter: string = '';
  sourceFilter: string = '';
  searchQuery: string = '';
  dateFilter: string = '';

  // UI State
  isFollowing = true;
  showTimestamps = true;
  showMetadata = false;
  isPaused = false;

  // Statistics
  stats = {
    total: 0,
    debug: 0,
    info: 0,
    warn: 0,
    error: 0
  };

  constructor(
    private webSocketService: WebSocketService,
    private pluginService: PluginService,
    private deploymentService: DeploymentManagementService,
    private workflowExecutionService: WorkflowExecutionService,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    // Check for deployment ID in query params
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {
      if (params['deploymentId']) {
        this.deploymentId = params['deploymentId'];
      }
      if (params['workflowId']) {
        this.workflowId = params['workflowId'];
      }
    });

    this.connectToLogs();
    this.setupFilters();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.webSocketService.disconnect();
  }

  ngAfterViewChecked(): void {
    if (this.shouldScrollToBottom && this.autoScroll && this.isFollowing) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  // WebSocket Connection
  connectToLogs(): void {
    this.connectionStatus = 'connecting';
    console.log('🔌 Connecting to logs WebSocket...', {
      workflowId: this.workflowId,
      deploymentId: this.deploymentId,
      pluginName: this.pluginName
    });

    const params: any = {};
    if (this.workflowId) {
      params.workflowId = this.workflowId;
    }
    if (this.pluginName) {
      params.pluginName = this.pluginName;
    }
    if (this.deploymentId) {
      params.deploymentId = this.deploymentId;
    }

    // Connect to real workflow logs
    this.connectToWorkflowLogs(params);

    this.webSocketService.connectionStatus$
      .pipe(takeUntil(this.destroy$))
      .subscribe(status => {
        console.log('🔌 WebSocket status changed:', status);
        this.connectionStatus = status;
      });
  }

  handleLogMessage(message: any): void {
    if (this.isPaused) return;

    try {
      const logEntry: LogEntry = {
        id: message.id || this.generateLogId(),
        timestamp: new Date(message.timestamp),
        level: message.level || 'info',
        message: this.maskSecrets(message.message || ''),
        source: message.source || 'unknown',
        workflowId: message.workflowId,
        stepName: message.stepName,
        pluginName: message.pluginName,
        deploymentId: message.deploymentId,
        applicationId: message.applicationId,
        componentId: message.componentId,
        environmentId: message.environmentId,
        metadata: message.metadata
      };

      this.addLogEntry(logEntry);
    } catch (error) {
      console.error('Error parsing log message:', error);
    }
  }

  addLogEntry(logEntry: LogEntry): void {
    this.logs.push(logEntry);
    this.updateStats(logEntry);

    // Limit the number of logs to prevent memory issues
    if (this.logs.length > this.maxLines) {
      const removed = this.logs.splice(0, this.logs.length - this.maxLines);
      removed.forEach(log => this.updateStats(log, true));
    }

    this.applyFilters();
    this.shouldScrollToBottom = true;
  }

  // Secret Masking
  maskSecrets(message: string): string {
    const secretPatterns = [
      /password["\s]*[:=]["\s]*([^"\s,}]+)/gi,
      /token["\s]*[:=]["\s]*([^"\s,}]+)/gi,
      /key["\s]*[:=]["\s]*([^"\s,}]+)/gi,
      /secret["\s]*[:=]["\s]*([^"\s,}]+)/gi,
      /apikey["\s]*[:=]["\s]*([^"\s,}]+)/gi,
      /authorization["\s]*[:=]["\s]*([^"\s,}]+)/gi,
    ];

    let maskedMessage = message;
    secretPatterns.forEach(pattern => {
      maskedMessage = maskedMessage.replace(pattern, (match, value) => {
        return match.replace(value, '***');
      });
    });

    return maskedMessage;
  }

  // Filtering and Search
  setupFilters(): void {
    // Apply filters whenever any filter changes
    this.applyFilters();
  }

  applyFilters(): void {
    this.filteredLogs = this.logs.filter(log => {
      // Level filter
      if (this.levelFilter && log.level !== this.levelFilter) {
        return false;
      }

      // Source filter
      if (this.sourceFilter && log.source !== this.sourceFilter) {
        return false;
      }

      // Search query
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        if (!log.message.toLowerCase().includes(query) &&
            !log.source.toLowerCase().includes(query) &&
            !(log.stepName && log.stepName.toLowerCase().includes(query))) {
          return false;
        }
      }

      // Date filter
      if (this.dateFilter) {
        const filterDate = new Date(this.dateFilter);
        const logDate = new Date(log.timestamp);
        if (logDate.toDateString() !== filterDate.toDateString()) {
          return false;
        }
      }

      return true;
    });
  }

  onFilterChange(): void {
    this.applyFilters();
  }

  clearFilters(): void {
    this.levelFilter = '';
    this.sourceFilter = '';
    this.searchQuery = '';
    this.dateFilter = '';
    this.applyFilters();
  }

  // Statistics
  updateStats(logEntry: LogEntry, remove = false): void {
    const increment = remove ? -1 : 1;

    this.stats.total += increment;

    switch (logEntry.level) {
      case 'debug':
        this.stats.debug += increment;
        break;
      case 'info':
        this.stats.info += increment;
        break;
      case 'warn':
        this.stats.warn += increment;
        break;
      case 'error':
        this.stats.error += increment;
        break;
    }
  }

  // UI Actions
  toggleFollow(): void {
    this.isFollowing = !this.isFollowing;
    if (this.isFollowing) {
      this.scrollToBottom();
    }
  }

  togglePause(): void {
    this.isPaused = !this.isPaused;
  }

  clearLogs(): void {
    this.logs = [];
    this.filteredLogs = [];
    this.stats = {
      total: 0,
      debug: 0,
      info: 0,
      warn: 0,
      error: 0
    };
  }

  downloadLogs(): void {
    const logsText = this.filteredLogs.map(log =>
      `[${log.timestamp.toISOString()}] [${log.level.toUpperCase()}] [${log.source}] ${log.message}`
    ).join('\n');

    const blob = new Blob([logsText], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `logs-${new Date().toISOString().split('T')[0]}.txt`;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  scrollToBottom(): void {
    try {
      const container = this.logsContainer.nativeElement;
      container.scrollTop = container.scrollHeight;
    } catch (error) {
      console.error('Error scrolling to bottom:', error);
    }
  }

  scrollToTop(): void {
    try {
      const container = this.logsContainer.nativeElement;
      container.scrollTop = 0;
    } catch (error) {
      console.error('Error scrolling to top:', error);
    }
  }

  // Connect to real workflow logs
  private connectToWorkflowLogs(params: any): void {
    console.log('🔌 Connecting to real workflow logs...', params);

    // Try WebSocket connection first
    this.webSocketService.connect('/ws/logs', params)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (message) => {
          console.log('📨 Received WebSocket message:', message);
          this.handleLogMessage(message);
        },
        error: (error) => {
          console.error('❌ WebSocket error:', error);
          this.connectionStatus = 'error';
          // Fallback to polling workflow execution logs
          this.pollWorkflowLogs();
        }
      });

    // If no immediate connection, start polling as backup
    setTimeout(() => {
      if (this.connectionStatus !== 'connected') {
        console.log('🔄 WebSocket not connected, starting log polling...');
        this.pollWorkflowLogs();
      }
    }, 2000);
  }

  // Poll workflow execution logs from the backend
  private pollWorkflowLogs(): void {
    if (!this.workflowId && !this.deploymentId) {
      console.log('⚠️ No workflowId or deploymentId provided for polling');
      return;
    }

    console.log('📊 Starting workflow log polling...');
    this.connectionStatus = 'connected';

    // Poll for workflow execution logs
    const pollInterval = setInterval(() => {
      if (this.isPaused) return;

      // Use workflow execution service for workflow logs, deployment service for deployment logs
      const logService = this.workflowId ?
        this.workflowExecutionService.getExecutionLogs(this.workflowId) :
        this.deploymentService.getDeploymentLogs(this.deploymentId!);

      logService
        .pipe(
          takeUntil(this.destroy$),
          catchError(error => {
            console.error('Failed to fetch logs:', error);
            this.connectionStatus = 'error';
            return of({ logs: [] });
          })
        )
        .subscribe(response => {
          this.connectionStatus = 'connected';

          if (response.logs && response.logs.length > 0) {
            response.logs.forEach((log: any) => {
              const logEntry: LogEntry = {
                id: log.id || this.generateLogId(),
                timestamp: new Date(log.timestamp || Date.now()),
                level: log.level || 'info',
                message: log.message || '',
                source: log.source || 'workflow',
                workflowId: this.workflowId,
                deploymentId: this.deploymentId,
                stepName: log.stepName,
                pluginName: log.pluginName,
                metadata: log.metadata
              };

              // Only add if we don't already have this log
              if (!this.logs.find(existingLog => existingLog.id === logEntry.id)) {
                this.addLogEntry(logEntry);
              }
            });
          }
        });
    }, 2000); // Poll every 2 seconds

    // Store interval reference to clear it on destroy
    this.destroy$.subscribe(() => {
      clearInterval(pollInterval);
    });
  }

  // Simulate logs for demo/fallback purposes (keeping for emergency fallback)
  private simulateLogs(): void {
    console.log('🎭 Starting log simulation...');
    this.connectionStatus = 'connected';

    const logMessages = [
      { level: 'info', message: 'Deployment started', source: 'deployment-service' },
      { level: 'info', message: 'Validating deployment configuration...', source: 'deployment-service' },
      { level: 'info', message: 'Configuration validation passed', source: 'deployment-service' },
      { level: 'info', message: 'Connecting to Kubernetes cluster...', source: 'helm-provider' },
      { level: 'info', message: 'Successfully connected to cluster', source: 'helm-provider' },
      { level: 'info', message: 'Preparing Helm chart...', source: 'helm-provider' },
      { level: 'info', message: 'Chart preparation completed', source: 'helm-provider' },
      { level: 'info', message: 'Starting rolling update deployment...', source: 'kubernetes' },
      { level: 'info', message: 'Creating new ReplicaSet...', source: 'kubernetes' },
      { level: 'info', message: 'Scaling up new ReplicaSet (1/3 replicas)', source: 'kubernetes' },
      { level: 'info', message: 'Scaling up new ReplicaSet (2/3 replicas)', source: 'kubernetes' },
      { level: 'info', message: 'Scaling up new ReplicaSet (3/3 replicas)', source: 'kubernetes' },
      { level: 'info', message: 'Scaling down old ReplicaSet (2/3 replicas)', source: 'kubernetes' },
      { level: 'info', message: 'Scaling down old ReplicaSet (1/3 replicas)', source: 'kubernetes' },
      { level: 'info', message: 'Scaling down old ReplicaSet (0/3 replicas)', source: 'kubernetes' },
      { level: 'info', message: 'Running health checks...', source: 'health-checker' },
      { level: 'info', message: 'Health checks passed', source: 'health-checker' },
      { level: 'info', message: 'Deployment completed successfully', source: 'deployment-service' }
    ];

    let index = 0;
    const interval = setInterval(() => {
      if (index >= logMessages.length || this.isPaused) {
        if (index >= logMessages.length) {
          clearInterval(interval);
          console.log('✅ Log simulation completed');
        }
        return;
      }

      const logMessage = logMessages[index];
      const logEntry: LogEntry = {
        id: this.generateLogId(),
        timestamp: new Date(),
        level: logMessage.level as any,
        message: logMessage.message,
        source: logMessage.source,
        workflowId: this.workflowId,
        deploymentId: this.deploymentId,
        stepName: `step-${index + 1}`,
        pluginName: this.pluginName
      };

      this.addLogEntry(logEntry);
      index++;
    }, 1000); // Add a log every second

    // Store interval reference to clear it on destroy
    this.destroy$.subscribe(() => {
      clearInterval(interval);
    });
  }

  // Utility Methods
  generateLogId(): string {
    return `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  getLevelClass(level: string): string {
    switch (level) {
      case 'debug':
        return 'text-blue-400';
      case 'info':
        return 'text-green-400';
      case 'warn':
        return 'text-yellow-400';
      case 'error':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  }

  getLevelIcon(level: string): string {
    switch (level) {
      case 'debug':
        return 'fas fa-bug';
      case 'info':
        return 'fas fa-info-circle';
      case 'warn':
        return 'fas fa-exclamation-triangle';
      case 'error':
        return 'fas fa-times-circle';
      default:
        return 'fas fa-circle';
    }
  }

  getConnectionStatusClass(): string {
    switch (this.connectionStatus) {
      case 'connected':
        return 'text-green-500';
      case 'connecting':
        return 'text-yellow-500';
      case 'disconnected':
        return 'text-gray-500';
      case 'error':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  }

  getConnectionStatusIcon(): string {
    switch (this.connectionStatus) {
      case 'connected':
        return 'fas fa-circle';
      case 'connecting':
        return 'fas fa-spinner fa-spin';
      case 'disconnected':
        return 'fas fa-circle';
      case 'error':
        return 'fas fa-exclamation-circle';
      default:
        return 'fas fa-circle';
    }
  }

  formatTimestamp(timestamp: Date): string {
    return timestamp.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    });
  }

  getUniqueSources(): string[] {
    return [...new Set(this.logs.map(log => log.source))].sort();
  }

  getLogLevels(): string[] {
    return ['debug', 'info', 'warn', 'error'];
  }

  reconnect(): void {
    this.webSocketService.disconnect();
    setTimeout(() => {
      this.connectToLogs();
    }, 1000);
  }

  trackByLogId(index: number, log: LogEntry): string {
    return log.id;
  }

  toggleLogMetadata(log: LogEntry): void {
    (log as any).showMetadata = !(log as any).showMetadata;
  }
}
