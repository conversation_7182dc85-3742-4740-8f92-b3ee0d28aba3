<div class="environment-promotion-container p-6">
  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-3xl font-bold text-gray-900">Environment Promotion</h1>
      <p class="text-gray-600 mt-1">Promote applications between environments with version tracking</p>
    </div>
    <div class="flex items-center space-x-3">
      <button (click)="refreshData()"
              class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
        <i class="fas fa-sync-alt mr-2"></i>Refresh
      </button>
    </div>
  </div>

  <!-- Project info display -->
  <div *ngIf="selectedProject" class="mb-6">
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h4a1 1 0 011 1v5m-6 0V9a1 1 0 011-1h4a1 1 0 011 1v2"></path>
        </svg>
        <span class="text-sm font-medium text-blue-900">
          Managing promotions for: <strong>{{ selectedProject.name }}</strong>
        </span>
      </div>
    </div>
  </div>

  <!-- No project selected message -->
  <div *ngIf="!selectedProject" class="mb-6">
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <span class="text-sm font-medium text-yellow-900">
          Please select a project from the header to manage environment promotions
        </span>
      </div>
    </div>
  </div>



  <!-- View Mode Tabs -->
  <div class="bg-white rounded-lg shadow mb-6">
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8 px-6">
        <button (click)="onViewModeChange('matrix')"
                [class.border-blue-500]="viewMode === 'matrix'"
                [class.text-blue-600]="viewMode === 'matrix'"
                [class.border-transparent]="viewMode !== 'matrix'"
                [class.text-gray-500]="viewMode !== 'matrix'"
                class="py-4 px-1 border-b-2 font-medium text-sm hover:text-gray-700 hover:border-gray-300">
          <i class="fas fa-table mr-2"></i>Version Matrix
        </button>
        <button (click)="onViewModeChange('promotion')"
                [class.border-blue-500]="viewMode === 'promotion'"
                [class.text-blue-600]="viewMode === 'promotion'"
                [class.border-transparent]="viewMode !== 'promotion'"
                [class.text-gray-500]="viewMode !== 'promotion'"
                class="py-4 px-1 border-b-2 font-medium text-sm hover:text-gray-700 hover:border-gray-300">
          <i class="fas fa-arrow-right mr-2"></i>Promote
        </button>
        <button (click)="onViewModeChange('history')"
                [class.border-blue-500]="viewMode === 'history'"
                [class.text-blue-600]="viewMode === 'history'"
                [class.border-transparent]="viewMode !== 'history'"
                [class.text-gray-500]="viewMode !== 'history'"
                class="py-4 px-1 border-b-2 font-medium text-sm hover:text-gray-700 hover:border-gray-300">
          <i class="fas fa-history mr-2"></i>History
        </button>
      </nav>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex items-center justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    <span class="ml-3 text-gray-600">Loading...</span>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
    <div class="flex">
      <i class="fas fa-exclamation-triangle text-red-400 mr-3 mt-1"></i>
      <div>
        <h3 class="text-sm font-medium text-red-800">Error</h3>
        <p class="text-sm text-red-700 mt-1">{{ error }}</p>
      </div>
    </div>
  </div>

  <!-- Version Matrix View -->
  <div *ngIf="viewMode === 'matrix' && !loading && selectedProject" class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
      <h2 class="text-lg font-semibold text-gray-900">Version Matrix</h2>
      <p class="text-sm text-gray-600">Current versions deployed across environments</p>
    </div>

    <div class="p-6">
      <div *ngIf="environmentVersions.length === 0" class="text-center py-8 text-gray-500">
        <i class="fas fa-cube text-4xl mb-4"></i>
        <p>No environments found for this project</p>
      </div>

      <div *ngIf="environmentVersions.length > 0" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Environment</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Version</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deployed At</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Health</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr *ngFor="let envVersion of environmentVersions">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-3 w-3 rounded-full mr-3" [ngClass]="getStatusClass(envVersion.status)"></div>
                  <div class="text-sm font-medium text-gray-900">{{ envVersion.environmentName }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 font-mono">{{ envVersion.currentVersion || 'Not deployed' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ envVersion.deployedAt ? formatDate(envVersion.deployedAt) : 'Never' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" [ngClass]="getHealthClass(envVersion.health)">
                  {{ envVersion.health }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <ng-container *ngFor="let targetEnv of environmentVersions">
                    <button *ngIf="canPromote(envVersion.environmentId, targetEnv.environmentId) && envVersion.currentVersion"
                            (click)="quickPromote(envVersion.environmentId, targetEnv.environmentId, envVersion.currentVersion)"
                            class="text-blue-600 hover:text-blue-900 text-xs px-2 py-1 border border-blue-200 rounded hover:bg-blue-50">
                      → {{ targetEnv.environmentName }}
                    </button>
                  </ng-container>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Promotion Form View -->
  <div *ngIf="viewMode === 'promotion' && !loading && selectedProject" class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
      <h2 class="text-lg font-semibold text-gray-900">Promote Version</h2>
      <p class="text-sm text-gray-600">Deploy a version from one environment to another</p>
    </div>

    <form [formGroup]="promotionForm" (ngSubmit)="promoteVersion()" class="p-6 space-y-6">
      <!-- Workflow Selection -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label for="workflow" class="block text-sm font-medium text-gray-700 mb-2">Workflow</label>
          <select id="workflow" formControlName="workflowId"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Select Workflow</option>
            <option *ngFor="let workflow of workflows" [value]="workflow.id">{{ workflow.name }}</option>
          </select>
        </div>
      </div>

      <!-- Environment Selection -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label for="source-env" class="block text-sm font-medium text-gray-700 mb-2">Source Environment</label>
          <select id="source-env" formControlName="sourceEnvironment"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Select Source</option>
            <option *ngFor="let env of environments" [value]="env.id">{{ env.name }}</option>
          </select>
        </div>

        <div>
          <label for="target-env" class="block text-sm font-medium text-gray-700 mb-2">Target Environment</label>
          <select id="target-env" formControlName="targetEnvironment"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Select Target</option>
            <option *ngFor="let env of environments" [value]="env.id">{{ env.name }}</option>
          </select>
        </div>
      </div>

      <!-- Version Information -->
      <div formGroupName="version" class="space-y-4">
        <h3 class="text-lg font-medium text-gray-900">Version Information</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label for="version-number" class="block text-sm font-medium text-gray-700 mb-1">Version *</label>
            <input id="version-number" type="text" formControlName="number" placeholder="1.0.1"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div>
            <label for="git-branch" class="block text-sm font-medium text-gray-700 mb-1">Git Branch</label>
            <input id="git-branch" type="text" formControlName="gitBranch" placeholder="main"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div>
            <label for="git-commit" class="block text-sm font-medium text-gray-700 mb-1">Git Commit</label>
            <input id="git-commit" type="text" formControlName="gitCommit" placeholder="abc123..."
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div>
            <label for="git-tag" class="block text-sm font-medium text-gray-700 mb-1">Git Tag</label>
            <input id="git-tag" type="text" formControlName="gitTag" placeholder="v1.0.1"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
        </div>
      </div>

      <!-- Promotion Options -->
      <div class="space-y-4">
        <h3 class="text-lg font-medium text-gray-900">Promotion Options</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="promotion-type" class="block text-sm font-medium text-gray-700 mb-2">Promotion Type</label>
            <select id="promotion-type" formControlName="promotionType"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="manual">Manual</option>
              <option value="automatic">Automatic</option>
              <option value="scheduled">Scheduled</option>
            </select>
          </div>

          <div class="flex items-center">
            <input id="require-approval" type="checkbox" formControlName="requireApproval"
                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="require-approval" class="ml-2 block text-sm text-gray-900">
              Require approval before deployment
            </label>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
        <button type="button" (click)="onViewModeChange('matrix')"
                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
          Cancel
        </button>
        <button type="submit" [disabled]="promotionForm.invalid || promotionInProgress"
                class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed">
          <i *ngIf="promotionInProgress" class="fas fa-spinner fa-spin mr-2"></i>
          <i *ngIf="!promotionInProgress" class="fas fa-rocket mr-2"></i>
          {{ promotionInProgress ? 'Starting Promotion...' : 'Start Promotion' }}
        </button>
      </div>
    </form>
  </div>

  <!-- History View -->
  <div *ngIf="viewMode === 'history' && !loading && selectedProject" class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
      <h2 class="text-lg font-semibold text-gray-900">Promotion History</h2>
      <p class="text-sm text-gray-600">Recent promotion activities across environments</p>
    </div>

    <div class="p-6">
      <div class="text-center py-8 text-gray-500">
        <i class="fas fa-history text-4xl mb-4"></i>
        <p>Promotion history will be available soon</p>
        <p class="text-sm">Track all promotion activities and their outcomes</p>
      </div>
    </div>
  </div>

  <!-- No Project Selected -->
  <div *ngIf="!selectedProject && !loading" class="bg-white rounded-lg shadow p-8 text-center">
    <i class="fas fa-project-diagram text-4xl text-gray-400 mb-4"></i>
    <h3 class="text-lg font-medium text-gray-900 mb-2">Select a Project</h3>
    <p class="text-gray-600">Choose a project from the header selector to view environment versions and manage promotions</p>
  </div>
</div>
