import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, combineLatest } from 'rxjs';
import { takeUntil, map } from 'rxjs/operators';

import { EnvironmentService, EnvironmentConfig, VersionMatrix } from '../../services/environment.service';
import { WorkflowService } from '../../services/workflow.service';
import { WorkflowExecutionService, VersionInfo } from '../../services/workflow-execution.service';
import { ProjectService } from '../../services/project.service';
import { Project } from '../../models/project.model';

export interface PromotionRequest {
  workflowId: string;
  projectId: string;
  sourceEnvironment: string;
  targetEnvironment: string;
  version: VersionInfo;
  promotionType: 'automatic' | 'manual' | 'scheduled';
  requireApproval: boolean;
  approvalUsers?: string[];
}

export interface EnvironmentVersion {
  environmentId: string;
  environmentName: string;
  currentVersion: string;
  deployedAt: string;
  health: string;
  status: string;
  services: DeployedService[];
}

export interface DeployedService {
  name: string;
  version: string;
  status: string;
  health: string;
  updatedAt: string;
}

@Component({
  selector: 'app-environment-promotion',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './environment-promotion.component.html',
  styleUrls: ['./environment-promotion.component.css']
})
export class EnvironmentPromotionComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Data
  environments: EnvironmentConfig[] = [];
  workflows: any[] = [];
  versionMatrix: VersionMatrix | null = null;
  environmentVersions: EnvironmentVersion[] = [];
  selectedProject: Project | null = null;

  // UI State
  loading = false;
  error: string | null = null;
  selectedSourceEnv = '';
  selectedTargetEnv = '';
  selectedVersion = '';

  // Forms
  promotionForm: FormGroup;

  // View modes
  viewMode: 'matrix' | 'promotion' | 'history' = 'matrix';

  // Promotion state
  promotionInProgress = false;
  promotionResult: any = null;

  constructor(
    private environmentService: EnvironmentService,
    private workflowService: WorkflowService,
    private workflowExecutionService: WorkflowExecutionService,
    private projectService: ProjectService,
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.promotionForm = this.createPromotionForm();
  }

  ngOnInit(): void {
    this.loadInitialData();
    this.setupFormSubscriptions();
    this.subscribeToProjectChanges();

    // Check for query parameters
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {
      if (params['view']) {
        this.viewMode = params['view'];
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createPromotionForm(): FormGroup {
    return this.formBuilder.group({
      workflowId: ['', Validators.required],
      sourceEnvironment: ['', Validators.required],
      targetEnvironment: ['', Validators.required],
      version: this.formBuilder.group({
        number: ['', Validators.required],
        gitCommit: [''],
        gitBranch: [''],
        gitTag: ['']
      }),
      promotionType: ['manual', Validators.required],
      requireApproval: [false],
      approvalUsers: [[]]
    });
  }

  private setupFormSubscriptions(): void {
    // Watch for source environment changes to load available versions
    this.promotionForm.get('sourceEnvironment')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(sourceEnvId => {
        if (sourceEnvId) {
          this.loadEnvironmentVersions(sourceEnvId);
        }
      });

    // Watch for project changes
    this.promotionForm.get('projectId')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(projectId => {
        if (projectId) {
          this.loadProjectEnvironments(projectId);
        }
      });
  }

  private async loadInitialData(): Promise<void> {
    this.loading = true;
    console.log('🔍 Environment-Promotion: Loading initial data...');

    try {
      // Load workflows with error handling
      try {
        const workflowsResponse = await this.workflowService.getWorkflows().toPromise();
        this.workflows = workflowsResponse || [];
        console.log('✅ Environment-Promotion: Loaded workflows:', this.workflows.length);
      } catch (error) {
        console.warn('⚠️ Environment-Promotion: Failed to load workflows, using mock data:', error);
        this.loadMockWorkflows();
      }

    } catch (error) {
      this.error = 'Failed to load initial data. Using mock data for development.';
      console.error('❌ Environment-Promotion: Error loading initial data:', error);
      this.loadMockWorkflows();
    } finally {
      this.loading = false;
    }
  }

  private async loadProjectData(): Promise<void> {
    if (!this.selectedProject?.id) return;

    this.loading = true;
    try {
      // Load environments for project
      await this.loadProjectEnvironments(this.selectedProject.id);

      // Load version matrix
      await this.loadVersionMatrix();

    } catch (error) {
      this.error = 'Failed to load project data';
      console.error('Error loading project data:', error);
    } finally {
      this.loading = false;
    }
  }

  private async loadProjectEnvironments(projectId: string): Promise<void> {
    console.log('🔍 Environment-Promotion: Loading environments for project:', projectId);
    try {
      const response = await this.environmentService.getEnvironments({ projectId }).toPromise();
      this.environments = response?.environments || [];
      console.log('✅ Environment-Promotion: Loaded environments:', this.environments.length);
    } catch (error) {
      console.error('❌ Environment-Promotion: Error loading environments, using mock data:', error);
      this.loadMockEnvironments(projectId);
    }
  }

  private async loadVersionMatrix(): Promise<void> {
    if (!this.selectedProject?.id) return;

    try {
      const matrix = await this.environmentService.getVersionMatrix(this.selectedProject.id).toPromise();
      this.versionMatrix = matrix || null;
      this.processVersionMatrix();
    } catch (error) {
      console.warn('Error loading version matrix:', error);
      // Create mock version matrix for demo purposes
      this.versionMatrix = {
        projectId: this.selectedProject.id,
        environments: {
          'development': {
            currentVersion: 'v1.2.3',
            deployedAt: new Date().toISOString(),
            health: 'healthy',
            metrics: { cpu: 45, memory: 60 },
            services: [
              { name: 'web-app', version: 'v1.2.3', status: 'running', health: 'healthy', updatedAt: new Date().toISOString() }
            ]
          },
          'staging': {
            currentVersion: 'v1.2.2',
            deployedAt: new Date(Date.now() - 86400000).toISOString(),
            health: 'healthy',
            metrics: { cpu: 35, memory: 50 },
            services: [
              { name: 'web-app', version: 'v1.2.2', status: 'running', health: 'healthy', updatedAt: new Date(Date.now() - 86400000).toISOString() }
            ]
          },
          'production': {
            currentVersion: 'v1.2.1',
            deployedAt: new Date(Date.now() - 172800000).toISOString(),
            health: 'healthy',
            metrics: { cpu: 25, memory: 40 },
            services: [
              { name: 'web-app', version: 'v1.2.1', status: 'running', health: 'healthy', updatedAt: new Date(Date.now() - 172800000).toISOString() }
            ]
          }
        },
        generatedAt: new Date().toISOString()
      };
      this.processVersionMatrix();
    }
  }

  private processVersionMatrix(): void {
    if (!this.versionMatrix) return;

    this.environmentVersions = Object.entries(this.versionMatrix.environments).map(([envName, envVersion]) => {
      const environment = this.environments.find(env => env.name === envName);
      return {
        environmentId: environment?.id || '',
        environmentName: envName,
        currentVersion: envVersion.currentVersion,
        deployedAt: envVersion.deployedAt,
        health: envVersion.health,
        status: this.getEnvironmentStatus(envVersion),
        services: envVersion.services || []
      };
    });
  }

  private async loadEnvironmentVersions(environmentId: string): Promise<void> {
    try {
      const history = await this.environmentService.getDeploymentHistory(environmentId).toPromise();
      // Process deployment history to get available versions
      // This would populate version dropdown
    } catch (error) {
      console.error('Error loading environment versions:', error);
    }
  }

  // UI Actions
  onProjectChange(): void {
    if (this.selectedProject?.id) {
      this.loadProjectData();
    }
  }

  onViewModeChange(mode: 'matrix' | 'promotion' | 'history'): void {
    this.viewMode = mode;
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { view: mode },
      queryParamsHandling: 'merge'
    });
  }

  async promoteVersion(): Promise<void> {
    if (this.promotionForm.invalid) {
      this.markFormGroupTouched(this.promotionForm);
      return;
    }

    this.promotionInProgress = true;
    this.error = null;

    try {
      const formValue = this.promotionForm.value;

      const promotionRequest: PromotionRequest = {
        workflowId: formValue.workflowId,
        projectId: this.selectedProject?.id || '',
        sourceEnvironment: formValue.sourceEnvironment,
        targetEnvironment: formValue.targetEnvironment,
        version: formValue.version,
        promotionType: formValue.promotionType,
        requireApproval: formValue.requireApproval,
        approvalUsers: formValue.approvalUsers
      };

      // Start promotion workflow
      const result = await this.workflowExecutionService.startPromotion(promotionRequest).toPromise();

      this.promotionResult = result;

      // Refresh version matrix
      await this.loadVersionMatrix();

      // Navigate to execution monitoring
      this.router.navigate(['/execution-monitoring'], {
        queryParams: { executionId: result.executionId }
      });

    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to start promotion';
      console.error('Error starting promotion:', error);
    } finally {
      this.promotionInProgress = false;
    }
  }

  quickPromote(sourceEnv: string, targetEnv: string, version: string): void {
    // Quick promotion without form
    this.selectedSourceEnv = sourceEnv;
    this.selectedTargetEnv = targetEnv;
    this.selectedVersion = version;

    this.promotionForm.patchValue({
      sourceEnvironment: sourceEnv,
      targetEnvironment: targetEnv,
      version: { number: version }
    });

    this.viewMode = 'promotion';
  }

  // Helper methods
  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  private getEnvironmentStatus(envVersion: any): string {
    if (envVersion.health === 'healthy') {
      return 'active';
    } else if (envVersion.health === 'degraded') {
      return 'warning';
    } else {
      return 'error';
    }
  }

  getStatusClass(status: string): string {
    return `status-${status.toLowerCase()}`;
  }

  getHealthClass(health: string): string {
    return `health-${health.toLowerCase()}`;
  }

  canPromote(sourceEnv: string, targetEnv: string): boolean {
    // Add business logic for promotion rules
    // e.g., can only promote from dev to staging, staging to prod
    return sourceEnv !== targetEnv;
  }

  getPromotionPath(): string[] {
    // Define promotion pipeline: dev -> staging -> production
    return ['development', 'staging', 'production'];
  }

  isVersionNewer(version1: string, version2: string): boolean {
    // Simple version comparison - in real app, use semver
    return version1 > version2;
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  refreshData(): void {
    this.loadProjectData();
  }

  private subscribeToProjectChanges(): void {
    // Subscribe to the shared project selection state
    this.projectService.selectedProject$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (selectedProject) => {
          console.log('🔄 Environment-Promotion: Project changed:', selectedProject);
          this.selectedProject = selectedProject;
          if (selectedProject) {
            this.loadProjectData();
          } else {
            // Clear data when no project is selected, but load mock data for development
            console.log('⚠️ Environment-Promotion: No project selected, using mock data');
            this.loadMockEnvironments('mock-project-id');
            this.loadMockWorkflows();
            this.environmentVersions = [];
            this.versionMatrix = null;
          }
        },
        error: (error) => {
          console.error('❌ Environment-Promotion: Error subscribing to project changes:', error);
        }
      });
  }

  private loadMockEnvironments(projectId: string): void {
    console.log('🎭 Environment-Promotion: Loading mock environments');
    this.environments = [
      {
        id: 'env-dev-001',
        projectId: projectId,
        name: 'Development',
        type: 'kubernetes',
        provider: {
          type: 'gke',
          config: {
            region: 'us-central1',
            cluster: 'dev-cluster',
            authMethod: 'service-account'
          }
        },
        resources: { cpu: '1000m', memory: '1Gi', storage: '10Gi', replicas: 1 },
        networking: { loadBalancer: false, ssl: false },
        variables: {},
        secretMappings: [],
        healthCheck: { enabled: true, endpoint: '/health', interval: 30, timeout: 10 },
        deploymentStrategy: 'rolling',
        status: 'active',
        description: 'Development environment',
        tags: ['dev'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'env-staging-001',
        projectId: projectId,
        name: 'Staging',
        type: 'kubernetes',
        provider: {
          type: 'aks',
          config: {
            region: 'eastus',
            cluster: 'staging-cluster',
            authMethod: 'service-account'
          }
        },
        resources: { cpu: '2000m', memory: '2Gi', storage: '20Gi', replicas: 2 },
        networking: { loadBalancer: true, ssl: true },
        variables: {},
        secretMappings: [],
        healthCheck: { enabled: true, endpoint: '/health', interval: 30, timeout: 10 },
        deploymentStrategy: 'blue-green',
        status: 'active',
        description: 'Staging environment',
        tags: ['staging'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'env-prod-001',
        projectId: projectId,
        name: 'Production',
        type: 'kubernetes',
        provider: {
          type: 'eks',
          config: {
            region: 'us-west-2',
            cluster: 'prod-cluster',
            authMethod: 'service-account'
          }
        },
        resources: { cpu: '4000m', memory: '4Gi', storage: '50Gi', replicas: 3 },
        networking: { loadBalancer: true, ssl: true },
        variables: {},
        secretMappings: [],
        healthCheck: { enabled: true, endpoint: '/health', interval: 30, timeout: 10 },
        deploymentStrategy: 'blue-green',
        status: 'active',
        description: 'Production environment',
        tags: ['prod'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
  }

  private loadMockWorkflows(): void {
    console.log('🎭 Environment-Promotion: Loading mock workflows');
    this.workflows = [
      {
        id: 'wf-deploy-001',
        name: 'Standard Deployment',
        description: 'Standard application deployment workflow',
        projectId: this.selectedProject?.id || 'mock-project-id',
        version: '1.0.0',
        steps: [],
        parameters: [
          { name: 'image_tag', label: 'Image Tag', type: 'text', required: true, default: 'latest' },
          { name: 'replicas', label: 'Replicas', type: 'number', required: false, default: '1' }
        ],
        variables: [
          { name: 'ENVIRONMENT', default: 'development' },
          { name: 'LOG_LEVEL', default: 'info' }
        ],
        triggers: [],
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'system'
      },
      {
        id: 'wf-promotion-001',
        name: 'Environment Promotion',
        description: 'Promote application between environments',
        projectId: this.selectedProject?.id || 'mock-project-id',
        version: '1.0.0',
        steps: [],
        parameters: [
          { name: 'source_env', label: 'Source Environment', type: 'text', required: true },
          { name: 'target_env', label: 'Target Environment', type: 'text', required: true },
          { name: 'version', label: 'Version', type: 'text', required: true }
        ],
        variables: [],
        triggers: [],
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'system'
      }
    ];
  }
}
