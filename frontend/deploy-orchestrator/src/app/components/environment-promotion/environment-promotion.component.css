.environment-promotion-container {
  min-height: 100vh;
  background-color: #f9fafb;
}

/* Status indicators */
.status-active {
  background-color: #10b981;
}

.status-warning {
  background-color: #f59e0b;
}

.status-error {
  background-color: #ef4444;
}

.status-inactive {
  background-color: #6b7280;
}

/* Health indicators */
.health-healthy {
  background-color: #dcfce7;
  color: #166534;
}

.health-degraded {
  background-color: #fef3c7;
  color: #92400e;
}

.health-unhealthy {
  background-color: #fee2e2;
  color: #991b1b;
}

.health-unknown {
  background-color: #f3f4f6;
  color: #374151;
}

/* Version matrix table */
.version-matrix-table {
  border-collapse: separate;
  border-spacing: 0;
}

.version-matrix-table th {
  position: sticky;
  top: 0;
  background-color: #f9fafb;
  z-index: 10;
}

.version-matrix-table td {
  border-bottom: 1px solid #e5e7eb;
}

.version-matrix-table tr:hover {
  background-color: #f9fafb;
}

/* Promotion form */
.promotion-form {
  max-width: 800px;
}

.form-section {
  margin-bottom: 2rem;
}

.form-section h3 {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

/* Version comparison */
.version-comparison {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background-color: #f9fafb;
}

.version-box {
  text-align: center;
  padding: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: white;
}

.version-box h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.version-number {
  font-size: 1.25rem;
  font-weight: 700;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #1f2937;
}

.promotion-arrow {
  font-size: 1.5rem;
  color: #3b82f6;
}

/* Quick promotion buttons */
.quick-promotion-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.quick-promotion-card {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  background-color: white;
  transition: all 0.2s;
}

.quick-promotion-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.promotion-path {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.environment-step {
  flex: 1;
  text-align: center;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #f9fafb;
}

.environment-step.active {
  background-color: #dbeafe;
  border-color: #3b82f6;
  color: #1e40af;
}

.path-arrow {
  margin: 0 0.5rem;
  color: #6b7280;
}

/* Promotion history */
.promotion-history-item {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
  background-color: white;
}

.promotion-history-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.promotion-status {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
}

.promotion-status.success {
  background-color: #dcfce7;
  color: #166534;
}

.promotion-status.failed {
  background-color: #fee2e2;
  color: #991b1b;
}

.promotion-status.running {
  background-color: #dbeafe;
  color: #1e40af;
}

.promotion-status.pending {
  background-color: #f3f4f6;
  color: #374151;
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .version-comparison {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .promotion-arrow {
    transform: rotate(90deg);
  }
  
  .quick-promotion-grid {
    grid-template-columns: 1fr;
  }
  
  .promotion-path {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .path-arrow {
    transform: rotate(90deg);
    margin: 0.5rem 0;
  }
}

/* Animation for promotion progress */
.promotion-progress {
  position: relative;
  overflow: hidden;
}

.promotion-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Version tags */
.version-tag {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  color: #374151;
}

.version-tag.current {
  background-color: #dcfce7;
  border-color: #16a34a;
  color: #166534;
}

.version-tag.outdated {
  background-color: #fef3c7;
  border-color: #d97706;
  color: #92400e;
}

/* Environment health indicators */
.health-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.health-indicator i {
  font-size: 0.625rem;
}

/* Promotion timeline */
.promotion-timeline {
  position: relative;
  padding-left: 2rem;
}

.promotion-timeline::before {
  content: '';
  position: absolute;
  left: 0.75rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #e5e7eb;
}

.timeline-item {
  position: relative;
  margin-bottom: 1rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -1.75rem;
  top: 0.5rem;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background-color: #3b82f6;
  border: 2px solid white;
  box-shadow: 0 0 0 2px #3b82f6;
}

.timeline-item.completed::before {
  background-color: #10b981;
  box-shadow: 0 0 0 2px #10b981;
}

.timeline-item.failed::before {
  background-color: #ef4444;
  box-shadow: 0 0 0 2px #ef4444;
}
