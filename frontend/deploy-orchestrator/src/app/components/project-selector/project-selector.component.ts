import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ProjectService } from '../../services/project.service';
import { Project } from '../../models/project.model';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-project-selector',
  templateUrl: './project-selector.component.html',
  styleUrls: ['./project-selector.component.scss']
})
export class ProjectSelectorComponent implements OnInit, OnDestroy {
  projects: Project[] = [];
  selectedProject: Project | null = null;
  isDropdownOpen = false;
  isAdmin = false;
  private subscriptions: Subscription = new Subscription();

  constructor(
    private projectService: ProjectService,
    private authService: AuthService,
    private router: Router
  ) {
    this.isAdmin = this.authService.isAdmin();
  }

  ngOnInit(): void {
    // Subscribe to the projects observable
    this.subscriptions.add(
      this.projectService.projects$.subscribe({
        next: (projects) => {
          this.projects = projects;
        },
        error: (error) => {
          console.error('Error loading projects', error);
        }
      })
    );

    // Subscribe to the selected project observable
    this.subscriptions.add(
      this.projectService.selectedProject$.subscribe({
        next: (selectedProject) => {
          this.selectedProject = selectedProject;
        },
        error: (error) => {
          console.error('Error loading selected project', error);
        }
      })
    );
  }

  ngOnDestroy(): void {
    // Clean up subscriptions when component is destroyed
    this.subscriptions.unsubscribe();
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  closeDropdown(): void {
    this.isDropdownOpen = false;
  }

  selectProject(project: Project): void {
    this.projectService.setSelectedProject(project);
    this.isDropdownOpen = false;
  }

  createNewProject(): void {
    this.router.navigate(['/projects/new']);
    this.isDropdownOpen = false;
  }
}
