<div class="relative inline-block text-left" clickOutside (clickOutside)="closeDropdown()">
  <div>
    <button (click)="toggleDropdown()" type="button"
            [class]="selectedProject ? 'inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500' : 'inline-flex justify-center w-full rounded-md border border-blue-300 shadow-sm px-4 py-2 bg-blue-50 text-sm font-medium text-blue-700 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'"
            id="project-menu-button" aria-expanded="true" aria-haspopup="true">
      {{ selectedProject?.name || 'Select Project' }}
      <svg class="-mr-1 ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
      </svg>
    </button>
  </div>

  <div *ngIf="isDropdownOpen" class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-40" role="menu" aria-orientation="vertical" aria-labelledby="project-menu-button" tabindex="-1">
    <div class="py-1" role="none">
      <div *ngIf="projects.length === 0" class="px-4 py-2 text-sm text-gray-500">
        No projects available
      </div>
      <a *ngFor="let project of projects" (click)="selectProject(project)" class="text-gray-700 block px-4 py-2 text-sm hover:bg-gray-100 cursor-pointer" role="menuitem" tabindex="-1">
        {{ project.name }}
      </a>
      <div *ngIf="isAdmin" class="border-t border-gray-100 my-1"></div>
      <a *ngIf="isAdmin" (click)="createNewProject()" class="text-blue-600 block px-4 py-2 text-sm hover:bg-gray-100 cursor-pointer" role="menuitem" tabindex="-1">
        + Create New Project
      </a>
    </div>
  </div>
</div>
