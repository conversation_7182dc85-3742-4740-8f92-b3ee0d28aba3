import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { EnvironmentWorkflowComponent } from '../environment-workflow/environment-workflow.component';
import { EnvironmentConfigComponent } from '../environment-config/environment-config.component';
import { Subscription, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import {
  EnvironmentService,
  EnvironmentConfig,
  CreateEnvironmentRequest,
  UpdateEnvironmentRequest,
  EnvironmentFilter,
  ConnectionTestResult,
  EnvironmentHealth,
  ProviderType,
  EnvironmentType,
  EnvironmentStatus
} from '../../services/environment.service';

import { ProjectService } from '../../services/project.service';
import { Project } from '../../models/project.model';

@Component({
  selector: 'app-environments',
  standalone: true,
  imports: [CommonModule, FormsModule, EnvironmentWorkflowComponent, EnvironmentConfigComponent],
  templateUrl: './environments.component.html',
  styleUrls: ['./environments.component.scss']
})
export class EnvironmentsComponent implements OnInit, OnDestroy {
  environments: EnvironmentConfig[] = [];
  projects: any[] = [];
  loading = false;
  error: string | null = null;

  // Project management
  selectedProject: Project | null = null;
  currentProjectId: string = '';
  private destroy$ = new Subject<void>();

  // Stats
  stats = {
    totalEnvironments: 0,
    activeEnvironments: 0,
    recentDeployments: 0,
    uniqueProviders: 0
  };

  // Activity tracking
  recentActivity: any[] = [];
  selectedProjectId = '';

  // Navigation methods
  navigateToEnvironmentConfig(): void {
    this.router.navigate(['/environment-config']);
  }

  navigateToWorkflows(): void {
    this.router.navigate(['/workflows']);
  }

  navigateToMonitoring(): void {
    this.router.navigate(['/execution-monitoring']);
  }

  refreshData(): void {
    this.loadEnvironments();
  }

  // Event handlers
  onEnvironmentSelected(event: any): void {
    console.log('Environment selected:', event);
  }

  onWorkflowExecuted(event: any): void {
    console.log('Workflow executed:', event);
  }

  // Activity helpers
  getActivityIcon(type: string): string {
    return `activity-icon-${type}`;
  }

  getActivityIconClass(type: string): string {
    return `fa fa-${type}`;
  }

  getActivityStatusClass(status: string): string {
    return `activity-status-${status}`;
  }

  // Pagination
  currentPage = 1;
  pageSize = 20;
  totalEnvironments = 0;

  // Filtering
  filter: EnvironmentFilter = {};
  searchTerm = '';

  // Modal states
  showCreateModal = false;
  showEditModal = false;
  showHealthModal = false;
  showSimpleCreateModal = false;
  selectedEnvironment: EnvironmentConfig | null = null;

  // Forms
  createForm: CreateEnvironmentRequest = {
    projectId: '',
    name: '',
    type: 'kubernetes',
    provider: {
      type: 'gke',
      config: {
        authMethod: 'service-account'
      }
    },
    resources: {
      cpu: '1000m',
      memory: '1Gi',
      storage: '10Gi',
      replicas: 1
    },
    networking: {
      loadBalancer: false,
      ssl: false
    },
    healthCheck: {
      enabled: true,
      interval: 30,
      timeout: 10
    },
    deploymentStrategy: 'rolling'
  };

  editForm: UpdateEnvironmentRequest = {};

  // Health monitoring
  environmentHealth: { [id: string]: EnvironmentHealth } = {};
  healthRefreshInterval: any;

  // Connection testing
  connectionTests: { [id: string]: ConnectionTestResult } = {};
  testingConnections = new Set<string>();

  // Constants
  private subscriptions: Subscription[] = [];

  constructor(
    private environmentService: EnvironmentService,
    private projectService: ProjectService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.subscribeToProjectChanges();
    this.initializeProject();
    this.loadProjects();
    this.loadEnvironments();
    this.startHealthMonitoring();
  }

  private initializeProject(): void {
    // Initialize project from shared service if available
    const selectedProject = this.projectService.getSelectedProject();
    if (selectedProject?.id) {
      console.log('🎯 Environments: Initializing with project from service:', selectedProject.id);
      this.selectedProject = selectedProject;
      this.currentProjectId = selectedProject.id;
      this.selectedProjectId = selectedProject.id;
    } else {
      console.log('⚠️ Environments: No project selected at initialization');
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.subscriptions.forEach(sub => sub.unsubscribe());
    if (this.healthRefreshInterval) {
      clearInterval(this.healthRefreshInterval);
    }
  }

  // Data loading
  async loadEnvironments(): Promise<void> {
    this.loading = true;
    this.error = null;

    try {
      const filterWithPagination = {
        ...this.filter,
        limit: this.pageSize,
        offset: (this.currentPage - 1) * this.pageSize
      };

      // Add project filter if a project is selected
      if (this.currentProjectId) {
        filterWithPagination.projectId = this.currentProjectId;
      }

      const response = await this.environmentService.getEnvironments(filterWithPagination).toPromise();
      this.environments = response?.environments || [];
      this.totalEnvironments = response?.total || 0;

      // Update selectedProjectId for the environment-workflow component
      this.selectedProjectId = this.currentProjectId;

      // Calculate real statistics
      this.calculateStats();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to load environments';
      console.error('Error loading environments:', error);
    } finally {
      this.loading = false;
    }
  }

  // Calculate real statistics from loaded data
  private calculateStats(): void {
    this.stats.totalEnvironments = this.totalEnvironments;
    this.stats.activeEnvironments = this.environments.filter(env => env.status === 'active').length;

    // Calculate unique providers
    const uniqueProviders = new Set(this.environments.map(env => env.provider.type));
    this.stats.uniqueProviders = uniqueProviders.size;

    // Load recent deployments count
    this.loadRecentDeployments();
  }

  // Load recent deployments count
  private async loadRecentDeployments(): Promise<void> {
    try {
      // Get recent deployments from the last 7 days
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      // For now, we'll use a placeholder. In a real implementation,
      // this would call a deployment history API
      this.stats.recentDeployments = 0;

      // TODO: Implement actual deployment history API call
      // const deployments = await this.deploymentService.getRecentDeployments(sevenDaysAgo).toPromise();
      // this.stats.recentDeployments = deployments?.length || 0;
    } catch (error) {
      console.error('Error loading recent deployments:', error);
      this.stats.recentDeployments = 0;
    }
  }

  async loadProjects(): Promise<void> {
    try {
      const response = await this.projectService.getProjects().toPromise();
      this.projects = response || [];
    } catch (error) {
      console.error('Error loading projects:', error);
    }
  }

  // Health monitoring
  startHealthMonitoring(): void {
    this.loadAllEnvironmentHealth();

    // Refresh health every 30 seconds
    this.healthRefreshInterval = setInterval(() => {
      this.loadAllEnvironmentHealth();
    }, 30000);
  }

  async loadAllEnvironmentHealth(): Promise<void> {
    try {
      const response = await this.environmentService.getAllEnvironmentHealth().toPromise();
      const healthArray = response?.health || [];

      this.environmentHealth = {};
      healthArray.forEach(health => {
        this.environmentHealth[health.environmentId] = health;
      });
    } catch (error) {
      console.error('Error loading environment health:', error);
    }
  }

  async loadEnvironmentHealth(id: string): Promise<void> {
    try {
      const health = await this.environmentService.getEnvironmentHealth(id).toPromise();
      if (health) {
        this.environmentHealth[id] = health;
      }
    } catch (error) {
      console.error(`Error loading health for environment ${id}:`, error);
    }
  }

  // Environment operations
  async createEnvironment(): Promise<void> {
    try {
      await this.environmentService.createEnvironment(this.createForm).toPromise();
      this.closeCreateModal();
      this.loadEnvironments();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to create environment';
    }
  }

  async updateEnvironment(): Promise<void> {
    if (!this.selectedEnvironment) return;

    try {
      await this.environmentService.updateEnvironment(this.selectedEnvironment.id, this.editForm).toPromise();
      this.closeEditModal();
      this.loadEnvironments();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to update environment';
    }
  }

  async deleteEnvironment(environment: EnvironmentConfig): Promise<void> {
    if (!confirm(`Are you sure you want to delete environment "${environment.name}"?`)) {
      return;
    }

    try {
      await this.environmentService.deleteEnvironment(environment.id).toPromise();
      this.loadEnvironments();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to delete environment';
    }
  }

  // Connection testing
  async testConnection(environment: EnvironmentConfig): Promise<void> {
    this.testingConnections.add(environment.id);

    try {
      const result = await this.environmentService.testConnection(environment.id).toPromise();
      if (result) {
        this.connectionTests[environment.id] = result;
      }
    } catch (error) {
      this.connectionTests[environment.id] = {
        environmentId: environment.id,
        success: false,
        message: error instanceof Error ? error.message : 'Connection test failed',
        testedAt: new Date().toISOString()
      };
    } finally {
      this.testingConnections.delete(environment.id);
    }
  }

  // Health operations
  async triggerHealthCheck(environment: EnvironmentConfig): Promise<void> {
    try {
      const health = await this.environmentService.triggerHealthCheck(environment.id).toPromise();
      if (health) {
        this.environmentHealth[environment.id] = health;
      }
    } catch (error) {
      console.error(`Error triggering health check for ${environment.id}:`, error);
    }
  }

  // Modal management
  openCreateModal(): void {
    this.createForm = {
      projectId: '',
      name: '',
      type: 'kubernetes',
      provider: {
        type: 'gke',
        config: {
          authMethod: 'service-account'
        }
      },
      resources: {
        cpu: '1000m',
        memory: '1Gi',
        storage: '10Gi',
        replicas: 1
      },
      networking: {
        loadBalancer: false,
        ssl: false
      },
      healthCheck: {
        enabled: true,
        interval: 30,
        timeout: 10
      },
      deploymentStrategy: 'rolling'
    };
    this.showCreateModal = true;
  }

  openSimpleCreateModal(): void {
    this.showSimpleCreateModal = true;
  }

  closeCreateModal(): void {
    this.showCreateModal = false;
    this.error = null;
  }

  closeSimpleCreateModal(): void {
    this.showSimpleCreateModal = false;
    this.error = null;
  }

  onEnvironmentCreated(environmentData: any): void {
    console.log('Environment created:', environmentData);
    this.closeSimpleCreateModal();
    this.loadEnvironments();
  }

  onCreateCancelled(): void {
    this.closeSimpleCreateModal();
  }

  openEditModal(environment: EnvironmentConfig): void {
    this.selectedEnvironment = environment;
    this.editForm = {
      name: environment.name,
      description: environment.description,
      status: environment.status,
      tags: [...environment.tags]
    };
    this.showEditModal = true;
  }

  closeEditModal(): void {
    this.showEditModal = false;
    this.selectedEnvironment = null;
    this.error = null;
  }

  openHealthModal(environment: EnvironmentConfig): void {
    this.selectedEnvironment = environment;
    this.loadEnvironmentHealth(environment.id);
    this.showHealthModal = true;
  }

  closeHealthModal(): void {
    this.showHealthModal = false;
    this.selectedEnvironment = null;
  }

  // Filtering and pagination
  applyFilter(): void {
    this.currentPage = 1;
    this.loadEnvironments();
  }

  clearFilter(): void {
    this.filter = {};
    this.searchTerm = '';
    this.currentPage = 1;
    this.loadEnvironments();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadEnvironments();
  }

  // Utility methods
  getStatusBadgeClass(status: EnvironmentStatus): string {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  getProviderBadgeClass(provider: ProviderType): string {
    switch (provider) {
      case 'gke': return 'bg-blue-100 text-blue-800';
      case 'aks': return 'bg-cyan-100 text-cyan-800';
      case 'eks': return 'bg-orange-100 text-orange-800';
      case 'openshift': return 'bg-red-100 text-red-800';
      case 'bare-metal': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  getHealthStatusClass(health?: EnvironmentHealth): string {
    if (!health) return 'bg-gray-100 text-gray-800';

    switch (health.status) {
      case 'healthy': return 'bg-green-100 text-green-800';
      case 'degraded': return 'bg-yellow-100 text-yellow-800';
      case 'unhealthy': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  getConnectionTestClass(test?: ConnectionTestResult): string {
    if (!test) return '';
    return test.success ? 'text-green-600' : 'text-red-600';
  }

  isConnectionTesting(environmentId: string): boolean {
    return this.testingConnections.has(environmentId);
  }

  getProjectName(projectId: string): string {
    const project = this.projects.find(p => p.id === projectId);
    return project ? project.name : 'Unknown Project';
  }

  get totalPages(): number {
    return Math.ceil(this.totalEnvironments / this.pageSize);
  }

  get filteredEnvironments(): EnvironmentConfig[] {
    if (!this.searchTerm) return this.environments;

    const term = this.searchTerm.toLowerCase();
    return this.environments.filter(env =>
      env.name.toLowerCase().includes(term) ||
      env.description.toLowerCase().includes(term) ||
      env.provider.type.toLowerCase().includes(term)
    );
  }

  // Navigation
  viewEnvironment(environment: EnvironmentConfig): void {
    this.router.navigate(['/environments', environment.id]);
  }

  viewExecutions(environment: EnvironmentConfig): void {
    this.router.navigate(['/executions'], {
      queryParams: { environmentId: environment.id }
    });
  }

  // Project management
  private subscribeToProjectChanges(): void {
    this.projectService.selectedProject$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (selectedProject) => {
          const newProjectId = selectedProject?.id || '';

          console.log('🔄 Environments: Project changed:', {
            previous: this.currentProjectId,
            new: newProjectId,
            project: selectedProject
          });

          if (newProjectId !== this.currentProjectId) {
            this.selectedProject = selectedProject;
            this.currentProjectId = newProjectId;
            this.selectedProjectId = newProjectId; // Set immediately for environment-workflow component

            // Reload environments when project changes
            this.loadEnvironments();
          }
        },
        error: (error) => {
          console.error('Error subscribing to project changes:', error);
        }
      });
  }
}
