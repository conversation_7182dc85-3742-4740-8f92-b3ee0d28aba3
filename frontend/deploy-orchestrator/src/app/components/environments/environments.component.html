<div class="environments-container p-6">
  <!-- Header with Migration Notice -->
  <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
    <div class="flex items-start">
      <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
      <div>
        <h3 class="text-lg font-semibold text-blue-900 mb-2">Environment-Based Deployments</h3>
        <p class="text-blue-800 mb-3">
          We've upgraded to a more powerful environment-based deployment system.
          Configure your deployment environments and use workflows for better control and monitoring.
        </p>
        <div class="flex gap-3">
          <button (click)="navigateToEnvironmentConfig()"
                  class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
            <i class="fas fa-cog mr-2"></i>Configure Environments
          </button>
          <button (click)="navigateToWorkflows()"
                  class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
            <i class="fas fa-play mr-2"></i>Create Workflow
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Project info display -->
  <div *ngIf="selectedProject" class="mb-6">
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h4a1 1 0 011 1v5m-6 0V9a1 1 0 011-1h4a1 1 0 011 1v2"></path>
        </svg>
        <span class="text-sm font-medium text-blue-900">
          Showing environments for: <strong>{{ selectedProject.name }}</strong>
        </span>
      </div>
    </div>
  </div>

  <!-- No project selected message -->
  <div *ngIf="!selectedProject" class="mb-6">
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <span class="text-sm font-medium text-yellow-900">
          Please select a project from the header to view environments
        </span>
      </div>
    </div>
  </div>

  <!-- Quick Stats -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-blue-100">
          <i class="fas fa-server text-blue-600 text-xl"></i>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Total Environments</p>
          <p class="text-2xl font-semibold text-gray-900">{{stats.totalEnvironments}}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-green-100">
          <i class="fas fa-check-circle text-green-600 text-xl"></i>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Active</p>
          <p class="text-2xl font-semibold text-gray-900">{{stats.activeEnvironments}}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-yellow-100">
          <i class="fas fa-rocket text-yellow-600 text-xl"></i>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Recent Deployments</p>
          <p class="text-2xl font-semibold text-gray-900">{{stats.recentDeployments}}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex items-center">
        <div class="p-3 rounded-full bg-purple-100">
          <i class="fas fa-cloud text-purple-600 text-xl"></i>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600">Providers</p>
          <p class="text-2xl font-semibold text-gray-900">{{stats.uniqueProviders}}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Environment-Workflow Integration -->
  <div class="bg-white rounded-lg shadow-lg">
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-xl font-semibold text-gray-900">Environment & Workflow Management</h2>
          <p class="text-gray-600 mt-1">Deploy applications using environment-specific workflows</p>
        </div>
        <div class="flex gap-3">
          <button (click)="refreshData()"
                  class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
            <i class="fas fa-sync-alt mr-2"></i>Refresh
          </button>
          <button (click)="openSimpleCreateModal()"
                  class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
            <i class="fas fa-plus mr-2"></i>New Environment
          </button>
          <button (click)="navigateToEnvironmentConfig()"
                  class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
            <i class="fas fa-cog mr-2"></i>Advanced Config
          </button>
        </div>
      </div>
    </div>

    <div class="p-6">
      <!-- Environment-Workflow Component - Only show when project is selected -->
      <div *ngIf="selectedProjectId; else noProjectSelected">
        <app-environment-workflow
          [selectedProjectId]="selectedProjectId"
          (environmentSelected)="onEnvironmentSelected($event)"
          (workflowExecuted)="onWorkflowExecuted($event)">
        </app-environment-workflow>
      </div>

      <!-- Show message when no project is selected -->
      <ng-template #noProjectSelected>
        <div class="text-center py-12">
          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-md mx-auto">
            <div class="flex items-center justify-center mb-4">
              <svg class="w-12 h-12 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-medium text-yellow-900 mb-2">No Project Selected</h3>
            <p class="text-sm text-yellow-700 mb-4">
              Please select a project from the header dropdown to view and manage environments and workflows.
            </p>
            <div class="flex items-center justify-center">
              <svg class="w-4 h-4 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
              </svg>
              <span class="text-sm font-medium text-yellow-800">Select a project above</span>
            </div>
          </div>
        </div>
      </ng-template>
    </div>
  </div>

  <!-- Recent Activity -->
  <div class="mt-8 bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
    </div>
    <div class="p-6">
      <div *ngIf="recentActivity.length === 0" class="text-center py-8">
        <i class="fas fa-history text-gray-400 text-3xl mb-3"></i>
        <p class="text-gray-600">No recent activity</p>
      </div>

      <div *ngFor="let activity of recentActivity" class="flex items-center py-3 border-b border-gray-100 last:border-b-0">
        <div class="flex-shrink-0">
          <div [ngClass]="getActivityIcon(activity.type)" class="w-8 h-8 rounded-full flex items-center justify-center">
            <i [ngClass]="getActivityIconClass(activity.type)" class="text-sm"></i>
          </div>
        </div>
        <div class="ml-4 flex-1">
          <p class="text-sm font-medium text-gray-900">{{activity.message}}</p>
          <p class="text-sm text-gray-600">{{activity.environment}} • {{activity.timestamp | date:'short'}}</p>
        </div>
        <div class="flex-shrink-0">
          <span [ngClass]="getActivityStatusClass(activity.status)"
                class="px-2 py-1 rounded-full text-xs font-medium">
            {{activity.status | titlecase}}
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-semibold mb-2">Environment Setup</h3>
          <p class="text-blue-100 mb-4">Configure your deployment environments</p>
          <button (click)="navigateToEnvironmentConfig()"
                  class="bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-blue-50 transition-colors">
            Get Started
          </button>
        </div>
        <i class="fas fa-server text-4xl text-blue-200"></i>
      </div>
    </div>

    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-semibold mb-2">Workflow Designer</h3>
          <p class="text-green-100 mb-4">Create deployment workflows</p>
          <button (click)="navigateToWorkflows()"
                  class="bg-white text-green-600 px-4 py-2 rounded-lg font-medium hover:bg-green-50 transition-colors">
            Create Workflow
          </button>
        </div>
        <i class="fas fa-project-diagram text-4xl text-green-200"></i>
      </div>
    </div>

    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-semibold mb-2">Monitoring</h3>
          <p class="text-purple-100 mb-4">Monitor environment health</p>
          <button (click)="navigateToMonitoring()"
                  class="bg-white text-purple-600 px-4 py-2 rounded-lg font-medium hover:bg-purple-50 transition-colors">
            View Metrics
          </button>
        </div>
        <i class="fas fa-chart-line text-4xl text-purple-200"></i>
      </div>
    </div>
  </div>
</div>

<!-- Simple Environment Creation Modal -->
<div *ngIf="showSimpleCreateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Create New Environment</h3>
        <button (click)="closeSimpleCreateModal()" class="text-gray-400 hover:text-gray-600">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>

      <app-environment-config
        [projectId]="selectedProjectId"
        (environmentCreated)="onEnvironmentCreated($event)"
        (cancelled)="onCreateCancelled()">
      </app-environment-config>
    </div>
  </div>
</div>
