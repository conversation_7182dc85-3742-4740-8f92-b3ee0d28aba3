.environments-container {
  min-height: calc(100vh - 200px);
  background: #f8fafc;
}

.migration-notice {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-left: 4px solid #3b82f6;
}

.stats-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }
}

.activity-icon-deployment {
  background-color: rgb(219, 234, 254);
  color: rgb(37, 99, 235);
}

.activity-icon-environment {
  background-color: rgb(220, 252, 231);
  color: rgb(22, 163, 74);
}

.activity-icon-workflow {
  background-color: rgb(237, 233, 254);
  color: rgb(147, 51, 234);
}

.activity-icon-error {
  background-color: rgb(254, 226, 226);
  color: rgb(220, 38, 38);
}

.activity-status-success {
  background-color: rgb(220, 252, 231);
  color: rgb(22, 101, 52);
}

.activity-status-pending {
  background-color: rgb(254, 249, 195);
  color: rgb(146, 64, 14);
}

.activity-status-failed {
  background-color: rgb(254, 226, 226);
  color: rgb(153, 27, 27);
}

.activity-status-running {
  background-color: rgb(219, 234, 254);
  color: rgb(30, 64, 175);
}

.quick-action-card {
  transition: all 0.3s ease-in-out;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }
}

.gradient-blue {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-green {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-purple {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stagger-animation {
  animation-delay: var(--animation-delay, 0s);
}

.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.card-hover {
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.btn-gradient {
  background: linear-gradient(45deg, #667eea, #764ba2);
  transition: all 0.3s ease;
  
  &:hover {
    background: linear-gradient(45deg, #764ba2, #667eea);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }
}

.icon-bounce {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.notification-badge {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #ef4444;
    border-radius: 50%;
    animation: pulse 2s infinite;
  }
}

.progress-bar {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  height: 4px;
  border-radius: 2px;
  overflow: hidden;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: progress-shine 2s infinite;
  }
}

@keyframes progress-shine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.tooltip-container {
  position: relative;
  
  .tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    z-index: 1000;
    
    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 4px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.8);
    }
  }
  
  &:hover .tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-4px);
  }
}

.responsive-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (max-width: 768px) {
  .environments-container {
    padding: 1rem;
  }
  
  .responsive-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-action-card {
    text-align: center;
  }
}
