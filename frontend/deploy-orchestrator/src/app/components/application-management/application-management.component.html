<div class="min-h-screen bg-gradient-to-br from-slate-50 via-indigo-50 to-purple-50">
  <!-- Header Section -->
  <div class="bg-white/80 backdrop-blur-xl border-b border-white/20 shadow-sm">
    <div class="max-w-7xl mx-auto px-6 py-8">
      <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-6 lg:space-y-0">
        <div class="space-y-2">
          <h1 class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
            Application Management
          </h1>
          <p class="text-slate-600" *ngIf="selectedProject">
            Managing applications for {{ selectedProject.name }}
          </p>
        </div>
        
        <div class="flex flex-wrap gap-3">
          <button 
            class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200 space-x-2"
            (click)="createApplicationGroup()">
            <i class="icon-plus text-sm"></i>
            <span>Create Application Group</span>
          </button>
          <button 
            class="inline-flex items-center px-4 py-2 bg-white/80 hover:bg-white text-slate-700 hover:text-slate-900 font-medium rounded-lg shadow-md hover:shadow-lg border border-slate-200 hover:border-slate-300 transform hover:-translate-y-0.5 transition-all duration-200 space-x-2"
            (click)="navigateToComponentManagement()">
            <i class="icon-grid text-sm"></i>
            <span>Manage Components</span>
          </button>
        </div>
      </div>

      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-8">
        <div class="bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-white/30 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300">
          <div class="flex items-center space-x-4">
            <div class="bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg p-3 shadow-lg">
              <i class="icon-check-circle text-white text-xl"></i>
            </div>
            <div>
              <div class="text-2xl font-bold text-slate-900">{{ stats.healthyApplications }}</div>
              <div class="text-sm text-slate-600">Healthy Apps</div>
            </div>
          </div>
        </div>
        
        <div class="bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-white/30 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300">
          <div class="flex items-center space-x-4">
            <div class="bg-gradient-to-br from-amber-500 to-orange-600 rounded-lg p-3 shadow-lg">
              <i class="icon-alert-triangle text-white text-xl"></i>
            </div>
            <div>
              <div class="text-2xl font-bold text-slate-900">{{ stats.warningApplications }}</div>
              <div class="text-sm text-slate-600">Warning Apps</div>
            </div>
          </div>
        </div>
        
        <div class="bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-white/30 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300">
          <div class="flex items-center space-x-4">
            <div class="bg-gradient-to-br from-red-500 to-rose-600 rounded-lg p-3 shadow-lg">
              <i class="icon-x-circle text-white text-xl"></i>
            </div>
            <div>
              <div class="text-2xl font-bold text-slate-900">{{ stats.errorApplications }}</div>
              <div class="text-sm text-slate-600">Error Apps</div>
            </div>
          </div>
        </div>
        
        <div class="bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-white/30 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300">
          <div class="flex items-center space-x-4">
            <div class="bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg p-3 shadow-lg">
              <i class="icon-upload text-white text-xl"></i>
            </div>
            <div>
              <div class="text-2xl font-bold text-slate-900">{{ stats.activeDeployments }}</div>
              <div class="text-sm text-slate-600">Active Deployments</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters and Controls -->
  <div class="max-w-7xl mx-auto px-6 py-6">
    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-white/30 shadow-lg">
      <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-6">
        <div class="relative">
          <i class="icon-search absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
          <input 
            type="text" 
            placeholder="Search applications..." 
            [(ngModel)]="searchTerm"
            class="pl-10 pr-4 py-2 w-64 border border-slate-200 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white/80 backdrop-blur-sm transition-all duration-200">
        </div>
        
        <div class="flex items-center space-x-3">
          <label class="text-sm font-medium text-slate-700">Status:</label>
          <select [(ngModel)]="filterStatus" class="px-3 py-2 border border-slate-200 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white/80 backdrop-blur-sm transition-all duration-200">
            <option value="all">All Status</option>
            <option value="healthy">Healthy</option>
            <option value="warning">Warning</option>
            <option value="error">Error</option>
          </select>
        </div>
      </div>

      <div class="flex items-center bg-white/80 backdrop-blur-sm rounded-lg p-1 shadow-inner border border-slate-200">
        <button 
          class="flex items-center justify-center w-10 h-10 rounded-md transition-all duration-200"
          [class]="viewMode === 'grid' ? 'bg-indigo-100 text-indigo-600 shadow-sm' : 'text-slate-500 hover:text-slate-700 hover:bg-slate-100'"
          (click)="setViewMode('grid')">
          <i class="icon-grid text-lg"></i>
        </button>
        <button 
          class="flex items-center justify-center w-10 h-10 rounded-md transition-all duration-200"
          [class]="viewMode === 'list' ? 'bg-indigo-100 text-indigo-600 shadow-sm' : 'text-slate-500 hover:text-slate-700 hover:bg-slate-100'"
          (click)="setViewMode('list')">
          <i class="icon-list text-lg"></i>
        </button>
        <button 
          class="flex items-center justify-center w-10 h-10 rounded-md transition-all duration-200"
          [class]="viewMode === 'topology' ? 'bg-indigo-100 text-indigo-600 shadow-sm' : 'text-slate-500 hover:text-slate-700 hover:bg-slate-100'"
          (click)="setViewMode('topology')">
          <i class="icon-share-2 text-lg"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-6 pb-8">
    <div *ngIf="loading" class="flex flex-col items-center justify-center py-16 space-y-4">
      <div class="relative">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        <div class="absolute inset-0 animate-ping rounded-full h-12 w-12 border border-indigo-400 opacity-20"></div>
      </div>
      <p class="text-slate-600 font-medium">Loading application groups...</p>
    </div>

    <div *ngIf="error" class="flex flex-col items-center justify-center py-16 space-y-6">
      <div class="bg-red-100 rounded-full p-4">
        <i class="icon-alert-circle text-red-600 text-2xl"></i>
      </div>
      <div class="text-center space-y-2">
        <h3 class="text-lg font-semibold text-slate-900">Something went wrong</h3>
        <p class="text-slate-600">{{ error }}</p>
      </div>
      <button 
        class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200"
        (click)="loadApplicationGroups()">
        Try Again
      </button>
    </div>

    <!-- Grid View -->
    <div *ngIf="!loading && !error && viewMode === 'grid'" class="space-y-6">
      <div class="grid gap-6">
        <div 
          *ngFor="let group of filteredApplicationGroups" 
          class="bg-white/70 backdrop-blur-sm rounded-xl border border-white/30 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
          [class.ring-2]="selectedGroup?.id === group.id"
          [class.ring-indigo-500]="selectedGroup?.id === group.id">
          
          <div class="cursor-pointer p-6 border-b border-slate-100" (click)="selectGroup(group)">
            <div class="flex justify-between items-start">
              <div class="space-y-2">
                <h3 class="text-xl font-semibold text-slate-900">{{ group.name }}</h3>
                <p class="text-slate-600">{{ group.description }}</p>
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-slate-500">{{ getApplicationCount(group) }} applications</span>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        [class]="getStatusBadgeClass(getGroupHealthStatus(group))">
                    {{ getGroupHealthStatus(group) }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div 
                *ngFor="let app of group.applications" 
                class="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-slate-200 hover:border-indigo-300 hover:shadow-md transition-all duration-200 cursor-pointer"
                (click)="selectApplication(app)">
                
                <div class="flex items-start justify-between mb-3">
                  <div class="flex items-center space-x-3">
                    <div class="bg-indigo-100 rounded-lg p-2">
                      <i class="icon-package text-indigo-600"></i>
                    </div>
                    <div>
                      <h4 class="font-medium text-slate-900">{{ app.name }}</h4>
                      <p class="text-sm text-slate-600 line-clamp-2">{{ app.description }}</p>
                    </div>
                  </div>
                  <div class="w-3 h-3 rounded-full" [class]="getStatusIndicatorClass(app.overallStatus)"></div>
                </div>

                <div class="space-y-2 mb-4">
                  <div 
                    *ngFor="let env of app.environments" 
                    class="flex items-center justify-between p-2 rounded-md"
                    [class]="getEnvironmentBgClass(env.status)">
                    <div class="flex items-center space-x-2">
                      <span class="text-sm font-medium">{{ env.name }}</span>
                      <span class="text-xs text-slate-500">{{ env.currentVersion }}</span>
                    </div>
                    
                    <div class="flex items-center space-x-2" *ngIf="env.status === 'healthy'">
                      <button 
                        class="p-1 rounded hover:bg-white/50 transition-colors"
                        (click)="deployApplication(app, env.name); $event.stopPropagation()"
                        title="Redeploy">
                        <i class="icon-refresh-cw text-sm text-slate-600"></i>
                      </button>
                    </div>
                  </div>
                </div>

                <div class="flex space-x-2">
                  <button 
                    class="flex-1 px-3 py-2 bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-medium rounded-md transition-colors duration-200"
                    (click)="navigateToApplicationDetail(app); $event.stopPropagation()">
                    Details
                  </button>
                  <button 
                    class="flex-1 px-3 py-2 bg-white hover:bg-slate-50 text-slate-700 text-sm font-medium rounded-md border border-slate-200 hover:border-slate-300 transition-colors duration-200"
                    (click)="navigateToApplicationDeployments(app); $event.stopPropagation()">
                    Deployments
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- List View -->
    <div *ngIf="!loading && !error && viewMode === 'list'" class="bg-white/70 backdrop-blur-sm rounded-xl border border-white/30 shadow-lg overflow-hidden">
      <div class="bg-slate-50/80 backdrop-blur-sm px-6 py-4 border-b border-slate-200">
        <div class="grid grid-cols-12 gap-4 text-sm font-medium text-slate-700">
          <div class="col-span-3">Application</div>
          <div class="col-span-2">Group</div>
          <div class="col-span-3">Environments</div>
          <div class="col-span-2">Status</div>
          <div class="col-span-2">Actions</div>
        </div>
      </div>
      
      <div class="divide-y divide-slate-100">
        <div *ngFor="let group of filteredApplicationGroups">
          <div 
            *ngFor="let app of group.applications"
            class="grid grid-cols-12 gap-4 px-6 py-4 hover:bg-indigo-50/50 transition-colors duration-200">
            
            <div class="col-span-3 flex items-center space-x-3">
              <div class="bg-indigo-100 rounded-lg p-2 flex-shrink-0">
                <i class="icon-package text-indigo-600"></i>
              </div>
              <div class="min-w-0">
                <div class="font-medium text-slate-900 truncate">{{ app.name }}</div>
                <div class="text-sm text-slate-600 truncate">{{ app.description }}</div>
              </div>
            </div>
            
            <div class="col-span-2 flex items-center">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800">
                {{ group.name }}
              </span>
            </div>
            
            <div class="col-span-3 flex items-center">
              <div class="flex flex-wrap gap-1">
                <span 
                  *ngFor="let env of app.environments"
                  class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium"
                  [class]="getEnvironmentPillClass(env.status)">
                  {{ env.name }}: {{ env.currentVersion }}
                </span>
              </div>
            </div>
            
            <div class="col-span-2 flex items-center">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    [class]="getStatusBadgeClass(app.overallStatus)">
                {{ app.overallStatus }}
              </span>
            </div>
            
            <div class="col-span-2 flex items-center space-x-2">
              <button 
                class="p-2 text-slate-500 hover:text-indigo-600 hover:bg-indigo-100 rounded-md transition-colors duration-200"
                (click)="navigateToApplicationDetail(app)"
                title="View Details">
                <i class="icon-eye"></i>
              </button>
              <button 
                class="p-2 text-slate-500 hover:text-indigo-600 hover:bg-indigo-100 rounded-md transition-colors duration-200"
                (click)="navigateToApplicationDeployments(app)"
                title="View Deployments">
                <i class="icon-activity"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Topology View -->
    <div *ngIf="!loading && !error && viewMode === 'topology'" class="bg-white/70 backdrop-blur-sm rounded-xl border border-white/30 shadow-lg overflow-hidden">
      <app-topology-viewer 
        [applications]="getAllApplications()"
        [selectedApplication]="selectedApplication"
        [autoLayout]="true"
        [animated]="true">
      </app-topology-viewer>
    </div>

    <!-- Empty State -->
    <div *ngIf="!loading && !error && filteredApplicationGroups.length === 0" class="flex flex-col items-center justify-center py-16 space-y-6">
      <div class="bg-slate-100 rounded-full p-6">
        <i class="icon-package text-slate-400 text-4xl"></i>
      </div>
      <div class="text-center space-y-2">
        <h3 class="text-xl font-semibold text-slate-900">No applications found</h3>
        <p class="text-slate-600" *ngIf="searchTerm || filterStatus !== 'all'">
          Try adjusting your search or filter criteria
        </p>
        <p class="text-slate-600" *ngIf="!searchTerm && filterStatus === 'all'">
          Create your first application group to get started
        </p>
      </div>
      <button 
        *ngIf="!searchTerm && filterStatus === 'all'"
        class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200"
        (click)="createApplicationGroup()">
        Create Application Group
      </button>
    </div>
  </div>
</div>

<!-- Group Details Panel -->
<div class="fixed inset-y-0 right-0 w-96 bg-white/90 backdrop-blur-xl border-l border-white/20 shadow-xl transform transition-transform duration-300 z-40" 
     *ngIf="showGroupDetailsPanel && selectedGroup" 
     [class.translate-x-0]="showGroupDetailsPanel"
     [class.translate-x-full]="!showGroupDetailsPanel">
     
  <div class="flex flex-col h-full">
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 p-6 text-white">
      <div class="flex justify-between items-start">
        <div class="space-y-1">
          <h3 class="text-xl font-semibold">{{ selectedGroup.name }}</h3>
          <p class="text-indigo-100 text-sm">{{ selectedGroup.description }}</p>
        </div>
        <button 
          class="p-2 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-colors duration-200" 
          (click)="closeGroupDetailsPanel()" 
          title="Close panel">
          <i class="icon-x text-lg"></i>
        </button>
      </div>
    </div>

    <div class="flex-1 overflow-y-auto p-6 space-y-6">
      <div class="space-y-4">
        <div class="flex justify-between items-center">
          <h4 class="font-semibold text-slate-900">Applications in Group ({{ getGroupApplicationCount(selectedGroup) }})</h4>
          <button 
            class="inline-flex items-center px-3 py-2 bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-medium rounded-lg transition-colors duration-200 space-x-2"
            (click)="createApplication()">
            <i class="icon-plus text-sm"></i>
            <span>Add Application</span>
          </button>
        </div>

        <!-- Applications in Group -->
        <div class="space-y-3" *ngIf="selectedGroup.applications && selectedGroup.applications.length > 0">
          <div 
            *ngFor="let app of selectedGroup.applications" 
            class="bg-slate-50/80 backdrop-blur-sm rounded-lg p-4 border border-slate-200 hover:border-indigo-300 transition-colors duration-200">
            <div class="flex items-start justify-between">
              <div class="flex items-center space-x-3">
                <div class="bg-indigo-100 rounded-lg p-2 flex-shrink-0">
                  <i class="icon-package text-indigo-600"></i>
                </div>
                <div class="min-w-0">
                  <h5 class="font-medium text-slate-900 truncate">{{ app.name }}</h5>
                  <p class="text-sm text-slate-600 line-clamp-2">{{ app.description }}</p>
                  <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium mt-1"
                        [class]="getStatusBadgeClass(app.overallStatus)">
                    {{ app.overallStatus }}
                  </span>
                </div>
              </div>
              <div class="flex space-x-1 flex-shrink-0">
                <button 
                  class="p-2 text-slate-500 hover:text-indigo-600 hover:bg-indigo-100 rounded-md transition-colors duration-200"
                  (click)="navigateToApplicationDetail(app)"
                  title="View">
                  <i class="icon-eye text-sm"></i>
                </button>
                <button 
                  class="p-2 text-slate-500 hover:text-red-600 hover:bg-red-100 rounded-md transition-colors duration-200"
                  (click)="removeApplicationFromGroup(app)"
                  title="Remove from group">
                  <i class="icon-trash-2 text-sm"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty state for applications -->
        <div class="text-center py-8" *ngIf="!selectedGroup.applications || selectedGroup.applications.length === 0">
          <div class="bg-slate-100 rounded-full p-4 w-16 h-16 mx-auto mb-4">
            <i class="icon-package text-slate-400 text-2xl"></i>
          </div>
          <p class="text-slate-600 mb-4">No applications in this group yet</p>
          <button 
            class="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg transition-colors duration-200 space-x-2"
            (click)="createApplication()">
            <i class="icon-plus text-sm"></i>
            <span>Create Application</span>
          </button>
        </div>
      </div>

      <!-- Available Applications to Add -->
      <div class="space-y-4" *ngIf="getApplicationsNotInGroups().length > 0">
        <div class="space-y-2">
          <h4 class="font-semibold text-slate-900">Available Applications</h4>
          <p class="text-sm text-slate-600">Applications not assigned to any group</p>
        </div>

        <div class="space-y-3">
          <div 
            *ngFor="let app of getApplicationsNotInGroups()" 
            class="bg-amber-50/80 backdrop-blur-sm rounded-lg p-4 border border-amber-200 hover:border-amber-300 transition-colors duration-200">
            <div class="flex items-start justify-between">
              <div class="flex items-center space-x-3">
                <div class="bg-amber-100 rounded-lg p-2 flex-shrink-0">
                  <i class="icon-package text-amber-600"></i>
                </div>
                <div class="min-w-0">
                  <h5 class="font-medium text-slate-900 truncate">{{ app.name }}</h5>
                  <p class="text-sm text-slate-600 line-clamp-2">{{ app.description }}</p>
                  <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium mt-1"
                        [class]="getStatusBadgeClass(app.overallStatus)">
                    {{ app.overallStatus }}
                  </span>
                </div>
              </div>
              <button 
                class="p-2 text-amber-600 hover:text-amber-700 hover:bg-amber-200 rounded-md transition-colors duration-200 flex-shrink-0"
                (click)="addApplicationToGroup(app)"
                title="Add to group">
                <i class="icon-plus text-sm"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Group Statistics -->
      <div class="space-y-4">
        <h4 class="font-semibold text-slate-900">Group Statistics</h4>
        
        <div class="grid grid-cols-2 gap-4">
          <div class="bg-slate-50/80 backdrop-blur-sm rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-slate-900">{{ getGroupApplicationCount(selectedGroup) }}</div>
            <div class="text-sm text-slate-600">Total Applications</div>
          </div>
          <div class="bg-green-50/80 backdrop-blur-sm rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-green-600">{{ getGroupHealthyCount(selectedGroup) }}</div>
            <div class="text-sm text-slate-600">Healthy</div>
          </div>
          <div class="bg-amber-50/80 backdrop-blur-sm rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-amber-600">{{ getGroupWarningCount(selectedGroup) }}</div>
            <div class="text-sm text-slate-600">Warning</div>
          </div>
          <div class="bg-red-50/80 backdrop-blur-sm rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-red-600">{{ getGroupErrorCount(selectedGroup) }}</div>
            <div class="text-sm text-slate-600">Error</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Create Application Group Modal -->
<app-create-application-group-modal
  [isOpen]="showCreateGroupModal"
  [projectId]="selectedProject?.id || ''"
  (modalClose)="onCloseCreateGroupModal()"
  (groupCreated)="onGroupCreated($event)">
</app-create-application-group-modal>

<!-- Create Application Modal -->
<app-create-application-modal
  [isOpen]="showCreateApplicationModal"
  [groupId]="selectedGroup?.id || ''"
  (modalClose)="onCloseCreateApplicationModal()"
  (applicationCreated)="onApplicationCreated($event)">
</app-create-application-modal>
