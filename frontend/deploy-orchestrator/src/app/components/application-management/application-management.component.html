<div class="p-6">
  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Applications</h1>
      <p class="text-gray-600 mt-1">Manage applications for the selected project</p>
    </div>
    <button 
      *ngIf="currentProjectId"
      (click)="openCreateModal()" 
      class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
      </svg>
      Add Application
    </button>
  </div>

  <!-- No Project Selected -->
  <div *ngIf="!currentProjectId" class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900">No Project Selected</h3>
    <p class="mt-1 text-sm text-gray-500">Please select a project from the header to manage applications.</p>
  </div>

  <!-- Loading -->
  <div *ngIf="loading && currentProjectId" class="text-center py-12">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
    <p class="mt-2 text-sm text-gray-500">Loading applications...</p>
  </div>

  <!-- Applications List -->
  <div *ngIf="!loading && currentProjectId" class="space-y-4">
    <!-- Search and Filter -->
    <div class="flex gap-4 mb-6">
      <div class="flex-1">
        <input 
          type="text" 
          [(ngModel)]="searchTerm"
          (input)="filterApplications()"
          placeholder="Search applications..." 
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
      </div>
      <select 
        [(ngModel)]="selectedType"
        (change)="filterApplications()"
        class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
        <option value="">All Types</option>
        <option value="web_application">Web Application</option>
        <option value="microservice">Microservice</option>
        <option value="api_gateway">API Gateway</option>
        <option value="database">Database</option>
        <option value="other">Other</option>
      </select>
    </div>

    <!-- Applications Grid -->
    <div *ngIf="filteredApplications.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div *ngFor="let app of filteredApplications" class="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900">{{ app.name }}</h3>
            <p class="text-sm text-gray-600 mt-1">{{ app.description || 'No description' }}</p>
            <div class="mt-3 flex items-center gap-2">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    [ngClass]="{
                      'bg-green-100 text-green-800': app.type === 'web_application',
                      'bg-purple-100 text-purple-800': app.type === 'microservice',
                      'bg-orange-100 text-orange-800': app.type === 'api_gateway',
                      'bg-blue-100 text-blue-800': app.type === 'database',
                      'bg-yellow-100 text-yellow-800': app.type === 'other'
                    }">
                {{ app.type | titlecase }}
              </span>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Active
              </span>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <button 
              (click)="editApplication(app)"
              class="text-gray-400 hover:text-blue-600 p-1">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
            </button>
            <button 
              (click)="deleteApplication(app)"
              class="text-gray-400 hover:text-red-600 p-1">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
            </button>
          </div>
        </div>
        
        <div class="mt-4 text-sm text-gray-500">
          <div class="flex items-center gap-4">
            <span *ngIf="app.repository">
              <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
              </svg>
              {{ app.repository }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- No Applications -->
    <div *ngIf="filteredApplications.length === 0 && !loading" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No Applications Found</h3>
      <p class="mt-1 text-sm text-gray-500">
        {{ searchTerm || selectedType ? 'No applications match your filters.' : 'Get started by creating your first application.' }}
      </p>
      <button 
        *ngIf="!searchTerm && !selectedType"
        (click)="openCreateModal()" 
        class="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
        Add Application
      </button>
    </div>
  </div>
</div>

<!-- Create/Edit Modal -->
<div *ngIf="showModal" class="fixed inset-0 z-50 overflow-y-auto">
  <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" (click)="closeModal()"></div>

    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
      <form [formGroup]="applicationForm" (ngSubmit)="saveApplication()">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                {{ editingApplication ? 'Edit Application' : 'Create Application' }}
              </h3>

              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Name *</label>
                  <input
                    type="text"
                    formControlName="name"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700">Description</label>
                  <textarea
                    formControlName="description"
                    rows="3"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700">Type *</label>
                  <select
                    formControlName="type"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    <option value="web_application">Web Application</option>
                    <option value="microservice">Microservice</option>
                    <option value="api_gateway">API Gateway</option>
                    <option value="database">Database</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700">Repository</label>
                  <input
                    type="text"
                    formControlName="repository"
                    placeholder="https://github.com/user/repo"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700">Branch</label>
                  <input
                    type="text"
                    formControlName="branch"
                    placeholder="main"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            type="submit"
            [disabled]="!applicationForm.valid || saving"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50">
            {{ saving ? 'Saving...' : (editingApplication ? 'Update' : 'Create') }}
          </button>
          <button
            type="button"
            (click)="closeModal()"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
