import { Component, <PERSON><PERSON>nit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { trigger, state, style, transition, animate } from '@angular/animations';

import { ProjectService } from '../../services/project.service';
import { ApplicationService, UpdateApplicationRequest } from '../../services/application.service';
import { NotificationService } from '../../services/notification.service';
import { Application, CreateApplicationRequest } from '../../models/application.model';

@Component({
  selector: 'app-application-management',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './application-management.component.html',
  styleUrls: ['./application-management.component.scss'],
  animations: [
    trigger('fadeInOut', [
      state('in', style({ opacity: 1 })),
      transition(':enter', [
        style({ opacity: 0 }),
        animate('200ms ease-in')
      ]),
      transition(':leave', [
        animate('200ms ease-out', style({ opacity: 0 }))
      ])
    ]),
    trigger('slideInOut', [
      state('in', style({ transform: 'translateY(0)', opacity: 1 })),
      transition(':enter', [
        style({ transform: 'translateY(-20px)', opacity: 0 }),
        animate('300ms ease-out')
      ]),
      transition(':leave', [
        animate('200ms ease-in', style({ transform: 'translateY(-20px)', opacity: 0 }))
      ])
    ])
  ]
})
export class ApplicationManagementComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Data
  currentProjectId = '';
  applications: Application[] = [];
  filteredApplications: Application[] = [];
  loading = false;

  // UI State
  searchTerm = '';
  selectedType = '';
  showModal = false;
  editingApplication: Application | null = null;
  saving = false;

  // Form
  applicationForm: FormGroup;

  constructor(
    private projectService: ProjectService,
    private applicationService: ApplicationService,
    private notificationService: NotificationService,
    private fb: FormBuilder
  ) {
    this.applicationForm = this.fb.group({
      name: ['', Validators.required],
      description: [''],
      type: ['web_application', Validators.required],
      repository: [''],
      branch: ['main']
    });
  }

  ngOnInit(): void {
    // Listen to project changes
    this.projectService.selectedProject$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(project => {
      if (project?.id) {
        this.currentProjectId = project.id;
        this.loadApplications();
      } else {
        this.currentProjectId = '';
        this.applications = [];
        this.filteredApplications = [];
      }
    });

    // Get current project immediately
    const currentProject = this.projectService.getSelectedProject();
    if (currentProject?.id) {
      this.currentProjectId = currentProject.id;
      this.loadApplications();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadApplications(): void {
    if (!this.currentProjectId) return;

    this.loading = true;
    this.applicationService.getApplications({ projectId: this.currentProjectId }).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (response) => {
        this.applications = response?.deployables || [];
        this.filterApplications();
        this.loading = false;
      },
      error: (error) => {
        console.error('Failed to load applications:', error);
        this.applications = [];
        this.filteredApplications = [];
        this.loading = false;
      }
    });
  }

  filterApplications(): void {
    let filtered = [...this.applications];

    if (this.searchTerm) {
      filtered = filtered.filter(app =>
        app.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        app.description?.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }

    if (this.selectedType) {
      filtered = filtered.filter(app => app.type === this.selectedType);
    }

    this.filteredApplications = filtered;
  }

  // Modal Management
  openCreateModal(): void {
    this.editingApplication = null;
    this.applicationForm.reset({
      name: '',
      description: '',
      type: 'web_application',
      repository: '',
      branch: 'main'
    });
    this.showModal = true;
  }

  closeModal(): void {
    this.showModal = false;
    this.editingApplication = null;
    this.saving = false;
  }

  editApplication(app: Application): void {
    this.editingApplication = app;
    this.applicationForm.patchValue({
      name: app.name,
      description: app.description,
      type: app.type,
      repository: app.repository?.url || '',
      branch: app.repository?.branch || 'main'
    });
    this.showModal = true;
  }

  saveApplication(): void {
    console.log('🔄 saveApplication called');
    console.log('📋 Form valid:', this.applicationForm.valid);
    console.log('📋 Current project ID:', this.currentProjectId);
    console.log('📋 Form errors:', this.applicationForm.errors);

    if (!this.applicationForm.valid) {
      console.log('❌ Form is invalid');
      this.notificationService.error('Please fill all required fields');
      return;
    }

    if (!this.currentProjectId) {
      console.log('❌ No project ID available');
      this.notificationService.error('No project selected. Please select a project first.');
      return;
    }

    this.saving = true;
    const formValue = this.applicationForm.value;
    console.log('📋 Form value:', formValue);

    if (this.editingApplication) {
      // Update existing application
      const updateRequest: UpdateApplicationRequest = {
        name: formValue.name,
        description: formValue.description,
        projectId: this.currentProjectId, // Add projectId here
        repository: {
          url: formValue.repository,
          branch: formValue.branch,
          provider: ''
        }
      };

      this.applicationService.updateApplication(this.editingApplication.id, updateRequest).pipe(
        takeUntil(this.destroy$)
      ).subscribe({
        next: () => {
          this.notificationService.success('Application updated successfully');
          this.closeModal();
          this.loadApplications();
        },
        error: (error) => {
          console.error('Failed to update application:', error);
          this.notificationService.error('Failed to update application');
          this.saving = false;
        }
      });
    } else {
      // Create new application - Note: This will need to be adapted based on actual backend API
      // For now, using a simplified approach that matches the deployables API
      // Create request with backend expected format
      const createRequest: CreateApplicationRequest = {
        name: formValue.name,
        description: formValue.description,
        type: formValue.type,
        projectId: this.currentProjectId, // Add projectId here
        groupId: 'default',
        repository: formValue.repository ? {
          url: formValue.repository,
          branch: formValue.branch || 'main',
          provider: 'github'
        } : undefined,
        tags: [],
        configuration: {
          defaultReplicas: 1,
          defaultResources: {
            cpu: { requests: '100m', limits: '500m' },
            memory: { requests: '128Mi', limits: '512Mi' }
          },
          globalEnvironmentVariables: [],
          globalSecrets: [],
          networkPolicies: []
        }
      };

      console.log('📤 Sending create request:', createRequest);

      this.applicationService.createApplication(createRequest).pipe(
        takeUntil(this.destroy$)
      ).subscribe({
        next: () => {
          this.notificationService.success('Application created successfully');
          this.closeModal();
          this.loadApplications();
        },
        error: (error) => {
          console.error('Failed to create application:', error);
          this.notificationService.error('Failed to create application');
          this.saving = false;
        }
      });
    }
  }

  deleteApplication(app: Application): void {
    if (!confirm(`Are you sure you want to delete "${app.name}"?`)) return;

    this.applicationService.deleteApplication(app.id).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: () => {
        this.notificationService.success('Application deleted successfully');
        this.loadApplications();
      },
      error: (error) => {
        console.error('Failed to delete application:', error);
        this.notificationService.error('Failed to delete application');
      }
    });
  }

}
