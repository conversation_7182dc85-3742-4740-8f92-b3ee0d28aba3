import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, firstValueFrom } from 'rxjs';
import {map, takeUntil} from 'rxjs/operators';

import { ProjectService } from '../../services/project.service';
import { ApplicationService } from '../../services/application.service';
import { Project } from '../../models/project.model';
import { ApplicationGroup, Application, Component as AppComponent, DeploymentStatus, CreateApplicationRequest, ApplicationType, RepositoryInfo, ApplicationConfiguration } from '../../models/application.model';
import { CreateApplicationGroupModalComponent, CreateApplicationGroupResult, CreateApplicationModalComponent, CreateApplicationResult, SimpleCreateApplicationRequest } from '../shared/modal/modal.component';
import { TopologyViewerComponent } from '../shared/topology-viewer/topology-viewer.component';

@Component({
  selector: 'app-application-management',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, CreateApplicationGroupModalComponent, CreateApplicationModalComponent, TopologyViewerComponent],
  templateUrl: './application-management.component.html',
  styleUrls: ['./application-management.component.scss']
})
export class ApplicationManagementComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Data
  selectedProject: Project | null = null;
  applicationGroups: ApplicationGroup[] = [];
  loading = false;
  error: string | null = null;

  // UI State
  selectedGroup: ApplicationGroup | null = null;
  selectedApplication: Application | null = null;
  viewMode: 'grid' | 'list' | 'topology' = 'grid';
  searchTerm = '';
  filterStatus: 'all' | 'healthy' | 'warning' | 'error' = 'all';
  showCreateGroupModal = false;
  showCreateApplicationModal = false;
  showGroupDetailsPanel = false;
  availableApplications: Application[] = [];

  // Stats
  stats = {
    totalApplications: 0,
    healthyApplications: 0,
    warningApplications: 0,
    errorApplications: 0,
    activeDeployments: 0
  };

  constructor(
    private projectService: ProjectService,
    private applicationService: ApplicationService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.loadSelectedProject();
    this.loadApplicationGroups();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadSelectedProject(): void {
    this.projectService.selectedProject$
      .pipe(takeUntil(this.destroy$))
      .subscribe(project => {
        this.selectedProject = project;
        if (project) {
          this.loadApplicationGroups();
        }
      });
  }

  async loadApplicationGroups(): Promise<void> {
    if (!this.selectedProject?.id) return;

    this.loading = true;
    this.error = null;

    try {
      this.applicationGroups = await this.applicationService.getApplicationGroups(this.selectedProject.id);
      await this.loadAvailableApplications();
      this.calculateStats();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to load application groups';
      console.error('Error loading application groups:', error);
    } finally {
      this.loading = false;
    }
  }

  private async loadAvailableApplications(): Promise<void> {
    try {
      this.availableApplications = await firstValueFrom(
        this.applicationService.getApplications({ projectId: this.selectedProject?.id }).pipe(map((response: { deployables: Application[], total: number }) => response.deployables),)
      );
    } catch (error) {
      console.error('Error loading available applications:', error);
    }
  }

  private calculateStats(): void {
    const allApps = this.applicationGroups.flatMap(group => group.applications);
    
    this.stats = {
      totalApplications: allApps.length,
      healthyApplications: allApps.filter(app => app.overallStatus === 'healthy').length,
      warningApplications: allApps.filter(app => app.overallStatus === 'warning').length,
      errorApplications: allApps.filter(app => app.overallStatus === 'error').length,
      activeDeployments: allApps.filter(app => 
        app.environments.some(env => env.deploymentStatus === DeploymentStatus.DEPLOYING)
      ).length
    };
  }

  // Application Group Management
  createApplicationGroup(): void {
    if (!this.selectedProject?.id) return;
    this.showCreateGroupModal = true;
  }

  onCloseCreateGroupModal(): void {
    this.showCreateGroupModal = false;
  }

  async onGroupCreated(result: CreateApplicationGroupResult): Promise<void> {
    if (!this.selectedProject?.id) return;
    
    try {
      const newGroup = {
        name: result.name,
        description: result.description,
        projectId: this.selectedProject.id,
        applications: []
      };

      const createdGroup = await this.applicationService.createApplicationGroup(newGroup);
      this.applicationGroups.push(createdGroup);
      this.selectGroup(createdGroup);
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to create application group';
    }
  }

  selectGroup(group: ApplicationGroup): void {
    this.selectedGroup = group;
    this.selectedApplication = null;
    this.showGroupDetailsPanel = true;
  }

  closeGroupDetailsPanel(): void {
    this.showGroupDetailsPanel = false;
    this.selectedGroup = null;
  }

  // Application Management in Groups
  createApplication(): void {
    if (!this.selectedGroup) return;
    this.showCreateApplicationModal = true;
  }

  onCloseCreateApplicationModal(): void {
    this.showCreateApplicationModal = false;
  }

  async onApplicationCreated(result: CreateApplicationResult): Promise<void> {
    if (!this.selectedGroup || !this.selectedProject?.id) return;
    
    try {
      // Create a simplified request that matches the backend Go struct
      const newApplication: SimpleCreateApplicationRequest = {
        name: result.name,
        description: result.description || '',
        groupId: this.selectedGroup.id,
        repository: result.repository?.url || '',
        branch: result.repository?.branch || 'main',
        buildCommand: '',
        startCommand: '',
        healthEndpoint: '',
        port: 8080
      };

      const createdApp = await firstValueFrom(
        this.applicationService.createApplication(newApplication as any)
      );
      
      // Add to the group's applications
      if (this.selectedGroup.applications) {
        this.selectedGroup.applications.push(createdApp);
      } else {
        this.selectedGroup.applications = [createdApp];
      }
      
      // Close the modal
      this.showCreateApplicationModal = false;
      
      // Refresh available applications
      await this.loadAvailableApplications();
      this.calculateStats();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to create application';
    }
  }

  getApplicationsNotInGroups(): Application[] {
    const applicationsInGroups = this.applicationGroups.flatMap(group => 
      group.applications?.map(app => app.id) || []
    );
    return this.availableApplications.filter(app => 
      !applicationsInGroups.includes(app.id)
    );
  }

  async addApplicationToGroup(application: Application): Promise<void> {
    if (!this.selectedGroup) return;
    
    try {
      // Update application to belong to this group
      await firstValueFrom(
        this.applicationService.updateApplication(application.id, { 
          groupId: this.selectedGroup.id 
        } as any)
      );
      
      // Add to the group's applications locally
      if (this.selectedGroup.applications) {
        this.selectedGroup.applications.push(application);
      } else {
        this.selectedGroup.applications = [application];
      }
      
      // Refresh data
      await this.loadAvailableApplications();
      this.calculateStats();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to add application to group';
    }
  }

  async removeApplicationFromGroup(application: Application): Promise<void> {
    if (!this.selectedGroup) return;
    
    try {
      // Update application to remove from group
      await firstValueFrom(
        this.applicationService.updateApplication(application.id, { 
          groupId: null 
        } as any)
      );
      
      // Remove from the group's applications locally
      if (this.selectedGroup.applications) {
        this.selectedGroup.applications = this.selectedGroup.applications.filter(
          app => app.id !== application.id
        );
      }
      
      // Refresh data
      await this.loadAvailableApplications();
      this.calculateStats();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to remove application from group';
    }
  }

  selectApplication(app: Application): void {
    this.selectedApplication = app;
    // Navigate to application detail view
    this.router.navigate(['applications', app.id], { relativeTo: this.route });
  }

  // Quick Actions
  async deployApplication(app: Application, environment: string): Promise<void> {
    try {
      await this.applicationService.deployApplication(app.id, environment);
      await this.loadApplicationGroups(); // Refresh data
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to deploy application';
    }
  }

  async promoteApplication(app: Application, fromEnv: string, toEnv: string): Promise<void> {
    try {
      await this.applicationService.promoteApplication(app.id, fromEnv, toEnv);
      await this.loadApplicationGroups(); // Refresh data
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to promote application';
    }
  }

  // Filter and Search
  get filteredApplicationGroups(): ApplicationGroup[] {
    let filtered = this.applicationGroups;

    // Apply search filter
    if (this.searchTerm) {
      filtered = filtered.map(group => ({
        ...group,
        applications: group.applications.filter(app =>
          app.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
          app.description?.toLowerCase().includes(this.searchTerm.toLowerCase())
        )
      })).filter(group => group.applications.length > 0);
    }

    // Apply status filter
    if (this.filterStatus !== 'all') {
      filtered = filtered.map(group => ({
        ...group,
        applications: group.applications.filter(app => app.overallStatus === this.filterStatus)
      })).filter(group => group.applications.length > 0);
    }

    return filtered;
  }

  // View Management
  setViewMode(mode: 'grid' | 'list' | 'topology'): void {
    this.viewMode = mode;
  }

  // Helper Methods
  getAllApplications(): Application[] {
    return this.applicationGroups.flatMap(group => group.applications);
  }

  // Utility Methods
  getStatusClass(status: string): string {
    return `status-${status}`;
  }

  getStatusBadgeClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'healthy':
      case 'running':
      case 'deployed':
        return 'bg-green-100 text-green-800';
      case 'warning':
      case 'degraded':
        return 'bg-amber-100 text-amber-800';
      case 'error':
      case 'failed':
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'deploying':
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      case 'stopped':
      case 'offline':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getStatusIndicatorClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'healthy':
      case 'running':
      case 'deployed':
        return 'bg-green-500';
      case 'warning':
      case 'degraded':
        return 'bg-amber-500';
      case 'error':
      case 'failed':
      case 'critical':
        return 'bg-red-500';
      case 'deploying':
      case 'pending':
        return 'bg-blue-500 animate-pulse';
      case 'stopped':
      case 'offline':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  }

  getEnvironmentBgClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'healthy':
      case 'running':
      case 'deployed':
        return 'bg-green-50 border border-green-200';
      case 'warning':
      case 'degraded':
        return 'bg-amber-50 border border-amber-200';
      case 'error':
      case 'failed':
      case 'critical':
        return 'bg-red-50 border border-red-200';
      case 'deploying':
      case 'pending':
        return 'bg-blue-50 border border-blue-200';
      case 'stopped':
      case 'offline':
        return 'bg-gray-50 border border-gray-200';
      default:
        return 'bg-gray-50 border border-gray-200';
    }
  }

  getEnvironmentPillClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'healthy':
      case 'running':
      case 'deployed':
        return 'bg-green-100 text-green-800';
      case 'warning':
      case 'degraded':
        return 'bg-amber-100 text-amber-800';
      case 'error':
      case 'failed':
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'deploying':
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      case 'stopped':
      case 'offline':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getApplicationCount(group: ApplicationGroup): number {
    return group.applications.length;
  }

  getGroupHealthStatus(group: ApplicationGroup): string {
    const apps = group.applications;
    if (apps.some(app => app.overallStatus === 'error')) return 'error';
    if (apps.some(app => app.overallStatus === 'warning')) return 'warning';
    return 'healthy';
  }

  // Navigation
  navigateToApplicationDetail(app: Application): void {
    this.router.navigate(['/applications', app.id]);
  }

  navigateToApplicationDeployments(app: Application): void {
    this.router.navigate(['/applications', app.id, 'deployments']);
  }

  navigateToComponentManagement(): void {
    // If we have a selected application, navigate to application-specific component management
    if (this.selectedApplication) {
      this.router.navigate(['/component-management', this.selectedApplication.id]);
    } else {
      // Otherwise, navigate to general component management
      this.router.navigate(['/component-management']);
    }
  }

  // Group Statistics Helper Methods
  getGroupApplicationCount(group: ApplicationGroup): number {
    return group.applications?.length || 0;
  }

  getGroupHealthyCount(group: ApplicationGroup): number {
    return (group.applications || []).filter(app => app.overallStatus === 'healthy').length;
  }

  getGroupWarningCount(group: ApplicationGroup): number {
    return (group.applications || []).filter(app => app.overallStatus === 'warning').length;
  }

  getGroupErrorCount(group: ApplicationGroup): number {
    return (group.applications || []).filter(app => app.overallStatus === 'error').length;
  }
}
