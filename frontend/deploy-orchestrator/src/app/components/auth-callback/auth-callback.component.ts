import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { DataRefreshService } from '../../services/data-refresh.service';

@Component({
  selector: 'app-auth-callback',
  templateUrl: './auth-callback.component.html',
  styleUrls: ['./auth-callback.component.scss']
})
export class AuthCallbackComponent implements OnInit {
  error = '';
  loading = true;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    private dataRefreshService: DataRefreshService
  ) { }

  ngOnInit(): void {
    // Get code and state from URL
    const code = this.route.snapshot.queryParamMap.get('code');
    const state = this.route.snapshot.queryParamMap.get('state');
    const error = this.route.snapshot.queryParamMap.get('error');
    const errorDescription = this.route.snapshot.queryParamMap.get('error_description');

    if (error) {
      this.error = errorDescription || 'Authentication failed';
      this.loading = false;
      return;
    }

    if (!code || !state) {
      this.error = 'Invalid callback parameters';
      this.loading = false;
      return;
    }

    // Process the callback
    this.authService.handleExternalLoginCallback(code, state)
      .subscribe({
        next: () => {
          // Refresh all data after successful external login using centralized service
          this.dataRefreshService.refreshAllData();

          // Redirect to the home page or return URL
          const returnUrl = localStorage.getItem('returnUrl') || '/';
          localStorage.removeItem('returnUrl');
          this.router.navigate([returnUrl]);
        },
        error: (err) => {
          this.error = err.message || 'Authentication failed';
          this.loading = false;
        }
      });
  }
}
