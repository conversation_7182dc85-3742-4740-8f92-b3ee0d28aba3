<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8 text-center">
    <div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Processing Login
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        Please wait while we complete your authentication...
      </p>
    </div>
    
    <div *ngIf="loading" class="flex justify-center">
      <svg class="animate-spin h-10 w-10 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>
    
    <div *ngIf="error" class="text-red-500 text-sm">
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong class="font-bold">Error: </strong>
        <span class="block sm:inline">{{ error }}</span>
      </div>
      <div class="mt-4">
        <a routerLink="/login" class="text-blue-600 hover:text-blue-800">Return to login</a>
      </div>
    </div>
  </div>
</div>
