<!-- Modern Deployable Management UI -->
<div class="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50">
  <!-- Header Section -->
  <div class="bg-white/70 backdrop-blur-md border-b border-gray-200/50 sticky top-0">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
        <div class="flex items-center space-x-3">
          <div class="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl">
            <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M2 12C2 6.48 6.48 2 12 2s10 4.48 10 10-4.48 10-10 10S2 17.52 2 12zm4.64-1.96l3.54 3.54c.78.78 2.05.78 2.83 0l7.07-7.07c.78-.78.78-2.05 0-2.83-.78-.78-2.05-.78-2.83 0L12 9.93 6.75 4.68c-.78-.78-2.05-.78-2.83 0s-.78 2.05 0 2.83l3.54 3.54z"/>
            </svg>
          </div>
          <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
              Deployment Hub
            </h1>
            <p class="text-gray-600 mt-1">Deploy and manage your applications with confidence</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-4">
          <!-- Project ID debug display -->
          <div class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
            {{ getCurrentProjectInfo() }}
          </div>
          
          <!-- Debug button -->
          <button
            class="px-4 py-2 rounded-lg text-sm font-medium text-white bg-gray-600 hover:bg-gray-700"
            (click)="debugEnvironmentService()">
            Debug Env API
          </button>
          
          <div class="flex bg-white rounded-xl shadow-sm border border-gray-200 p-1">
            <button 
              class="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200"
              [class]="currentView === 'overview' ? 'bg-indigo-600 text-white shadow-sm' : 'text-gray-600 hover:text-indigo-600 hover:bg-gray-50'"
              (click)="setView('overview')">
              <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
              </svg>
              Overview
            </button>
            <button 
              class="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200"
              [class]="currentView === 'deploy' ? 'bg-indigo-600 text-white shadow-sm' : 'text-gray-600 hover:text-indigo-600 hover:bg-gray-50'"
              (click)="setView('deploy')">
              <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M5 4v3h5.5v12h3V7H19V4z"/>
              </svg>
              Deploy
            </button>
            <button 
              class="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200"
              [class]="currentView === 'manage' ? 'bg-indigo-600 text-white shadow-sm' : 'text-gray-600 hover:text-indigo-600 hover:bg-gray-50'"
              (click)="setView('manage')">
              <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              Manage
            </button>
          </div>
          
          <button class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-2.5 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105" (click)="openQuickDeployModal()">
            <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            Quick Deploy
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex items-center justify-center min-h-96">
    <div class="text-center">
      <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-indigo-200 border-t-indigo-600 mb-4"></div>
      <p class="text-gray-600 text-lg">Loading your deployables...</p>
    </div>
  </div>

  <!-- Main Content -->
  <div *ngIf="!loading" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Overview View -->
    <div *ngIf="currentView === 'overview'" class="space-y-8">
      <!-- Quick Stats -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white/70 backdrop-blur-md rounded-2xl p-6 border border-gray-200/50 hover:shadow-lg transition-all duration-200">
          <div class="flex items-center">
            <div class="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z"/>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-2xl font-bold text-gray-900">{{ getApplicationCount() }}</h3>
              <p class="text-gray-600">Applications</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white/70 backdrop-blur-md rounded-2xl p-6 border border-gray-200/50 hover:shadow-lg transition-all duration-200">
          <div class="flex items-center">
            <div class="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-2xl font-bold text-gray-900">{{ getServiceCount() }}</h3>
              <p class="text-gray-600">Services</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white/70 backdrop-blur-md rounded-2xl p-6 border border-gray-200/50 hover:shadow-lg transition-all duration-200">
          <div class="flex items-center">
            <div class="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-2xl font-bold text-gray-900">{{ environments.length }}</h3>
              <p class="text-gray-600">Environments</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white/70 backdrop-blur-md rounded-2xl p-6 border border-gray-200/50 hover:shadow-lg transition-all duration-200">
          <div class="flex items-center">
            <div class="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
              </svg>
            </div>
            <div class="ml-4">
              <h3 class="text-2xl font-bold text-gray-900">{{ getActiveDeployments() }}</h3>
              <p class="text-gray-600">Active Deployments</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="bg-white/70 backdrop-blur-md rounded-2xl p-6 border border-gray-200/50">
        <h2 class="text-xl font-bold text-gray-900 mb-6">Recent Activity</h2>
        <div class="space-y-4">
          <div *ngFor="let activity of getRecentActivity()" class="flex items-start space-x-4 p-4 bg-gray-50/50 rounded-xl hover:bg-gray-100/50 transition-colors duration-200">
            <div class="flex items-center justify-center w-10 h-10 rounded-full" 
                 [class]="activity.type === 'deployment' ? 'bg-blue-100 text-blue-600' : 
                          activity.type === 'promotion' ? 'bg-green-100 text-green-600' : 
                          'bg-purple-100 text-purple-600'">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" 
                   *ngIf="activity.type === 'deployment'">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" 
                   *ngIf="activity.type === 'promotion'">
                <path d="M5 4v3h5.5v12h3V7H19V4z"/>
              </svg>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" 
                   *ngIf="activity.type === 'creation'">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <h4 class="text-sm font-medium text-gray-900">{{ activity.title }}</h4>
              <p class="text-sm text-gray-600 mt-1">{{ activity.description }}</p>
              <span class="text-xs text-gray-500 mt-2 block">{{ formatDate(activity.timestamp) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Environment Health -->
      <div class="bg-white/70 backdrop-blur-md rounded-2xl p-6 border border-gray-200/50">
        <h2 class="text-xl font-bold text-gray-900 mb-6">Environment Status</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div *ngFor="let env of environments" class="bg-gray-50/50 rounded-xl p-4 border border-gray-200/30 hover:shadow-md transition-all duration-200">
            <div class="flex items-center justify-between mb-4">
              <h3 class="font-semibold text-gray-900">{{ env.name }}</h3>
              <div class="flex items-center space-x-2" 
                   [class]="getEnvironmentStatusClass(env) === 'healthy' ? 'text-green-600' : 
                            getEnvironmentStatusClass(env) === 'warning' ? 'text-yellow-600' : 
                            getEnvironmentStatusClass(env) === 'critical' ? 'text-red-600' : 'text-gray-400'">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" 
                     *ngIf="getEnvironmentStatusClass(env) === 'healthy'">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" 
                     *ngIf="getEnvironmentStatusClass(env) === 'warning'">
                  <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
                </svg>
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" 
                     *ngIf="getEnvironmentStatusClass(env) === 'critical'">
                  <path d="M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/>
                </svg>
                <span class="text-xs font-medium">{{ getEnvironmentStatusText(env) }}</span>
              </div>
            </div>
            <div class="space-y-2">
              <div *ngFor="let deployable of getDeployablesInEnvironment(env.id)" class="flex items-center justify-between p-2 bg-white/50 rounded-lg">
                <div class="flex items-center space-x-2">
                  <span class="text-sm font-medium text-gray-900">{{ deployable.name }}</span>
                  <span class="text-xs text-gray-500">v{{ deployable.version }}</span>
                </div>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium" 
                      [class]="getStatusClass(deployable.status) === 'success' ? 'bg-green-100 text-green-800' : 
                               getStatusClass(deployable.status) === 'warning' ? 'bg-yellow-100 text-yellow-800' : 
                               getStatusClass(deployable.status) === 'danger' ? 'bg-red-100 text-red-800' : 
                               'bg-gray-100 text-gray-800'">
                  {{ deployable.status }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Deploy View -->
    <div *ngIf="currentView === 'deploy'" class="space-y-8">
      <!-- Deployment Wizard -->
      <div class="bg-white/70 backdrop-blur-md rounded-2xl border border-gray-200/50 overflow-hidden">
        <!-- Wizard Header -->
        <div class="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-xl font-bold text-white">{{ getWizardStepTitle() }}</h2>
              <p class="text-indigo-100 mt-1">{{ getWizardStepDescription() }}</p>
            </div>
            <button class="text-white hover:text-indigo-200 transition-colors duration-200" 
                    (click)="startDeploymentWizard()">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- Step Progress Indicator -->
        <div class="px-6 py-4 bg-gray-50/50 border-b border-gray-200/50">
          <div class="flex items-center space-x-4">
            <div *ngFor="let step of [1, 2, 3]; let i = index" 
                 class="flex items-center"
                 [class.flex-1]="i < 2">
              <div class="flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-200"
                   [class]="deploymentWizard.step === step ? 'bg-indigo-600 border-indigo-600 text-white' : 
                           deploymentWizard.step > step ? 'bg-green-500 border-green-500 text-white' : 
                           'bg-white border-gray-300 text-gray-500'">
                <svg *ngIf="deploymentWizard.step > step" class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
                </svg>
                <span *ngIf="deploymentWizard.step <= step">{{ step }}</span>
              </div>
              <div *ngIf="i < 2" class="flex-1 h-1 mx-4 rounded-full"
                   [class]="deploymentWizard.step > step + 1 ? 'bg-green-500' : 'bg-gray-200'"></div>
            </div>
          </div>
          <div class="flex justify-between mt-2 text-sm">
            <span [class]="deploymentWizard.step >= 1 ? 'text-indigo-600 font-medium' : 'text-gray-500'">
              Select Applications
            </span>
            <span [class]="deploymentWizard.step >= 2 ? 'text-indigo-600 font-medium' : 'text-gray-500'">
              Choose Environment
            </span>
            <span [class]="deploymentWizard.step >= 3 ? 'text-indigo-600 font-medium' : 'text-gray-500'">
              Configure & Deploy
            </span>
          </div>
        </div>

        <!-- Step Content -->
        <div class="p-6">
          <!-- Step 1: Select Applications -->
          <div *ngIf="deploymentWizard.step === 1" class="space-y-6">
            <!-- Header with Add New Application Button -->
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-xl font-semibold text-gray-900">Select Applications to Deploy</h3>
              <button 
                class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 bg-green-600 text-white hover:bg-green-700"
                (click)="openCreateModal()">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                </svg>
                Add New Application
              </button>
            </div>

            <!-- Type Filter -->
            <div class="mb-2">
              <h4 class="text-sm font-medium text-gray-700 mb-2">Filter by Application Type</h4>
              <div class="flex flex-wrap gap-3">
                <button *ngFor="let type of getUniqueDeployableTypes()" 
                        class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 border"
                        [class]="activeFilters.includes(type) ? 'bg-indigo-600 border-indigo-600 text-white' : 'bg-white border-gray-300 text-gray-700 hover:border-indigo-300'"
                        (click)="toggleFilter(type)">
                  <i [class]="getArtifactIcon(type) + ' mr-2'"></i>
                  {{ getApplicationTypeLabel(type) }}
                </button>
              </div>
            </div>

            <!-- Search Box -->
            <div class="relative w-full mb-4">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <input
                type="text"
                class="block w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Search for applications by name or type..."
                [(ngModel)]="wizardSearchQuery"
                (input)="filterWizardApplications()"
                name="wizardSearch">
            </div>

            <!-- No Applications Message -->
            <div *ngIf="!wizardLoading.applications && !wizardErrors.applications && getFilteredWizardApplications().length === 0" class="text-center p-8 bg-gray-50 rounded-xl border border-gray-200">
              <svg class="w-16 h-16 mx-auto text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"/>
                <path d="M14 17H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
              </svg>
              <h4 class="mt-2 text-lg font-medium text-gray-900">
                {{ wizardSearchQuery ? 'No applications match your search' : 'No applications available' }}
              </h4>
              <p class="mt-1 text-sm text-gray-600">
                {{ wizardSearchQuery ? 'Try a different search term or clear the search' : 'Click "Add New Application" button to create a new deployable application' }}
              </p>
              <div *ngIf="wizardSearchQuery" class="mt-3">
                <button 
                  class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium bg-indigo-600 text-white hover:bg-indigo-700 transition-all duration-200"
                  (click)="wizardSearchQuery = ''; filterWizardApplications()">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                  </svg>
                  Clear Search
                </button>
              </div>
              <div class="mt-3">
                <button 
                  class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium bg-green-600 text-white hover:bg-green-700 transition-all duration-200"
                  (click)="openCreateModal()">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                  </svg>
                  Create New Application
                </button>
              </div>
            </div>

            <!-- Applications Grid -->
            <!-- Loading State -->
            <div *ngIf="wizardLoading.applications" class="flex items-center justify-center py-10">
              <div class="animate-spin rounded-full h-8 w-8 border-4 border-indigo-200 border-t-indigo-600"></div>
              <span class="ml-3 text-gray-600">Loading applications...</span>
            </div>
            
            <!-- Error State -->
            <div *ngIf="wizardErrors.applications && !wizardLoading.applications" 
                 class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              <p class="font-medium">{{ wizardErrors.applications }}</p>
              <button class="text-sm underline hover:text-red-800" (click)="loadPluginData()">Try again</button>
            </div>
            
            <!-- Applications Grid -->
            <div *ngIf="!wizardLoading.applications && !wizardErrors.applications && getFilteredWizardApplications().length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div *ngFor="let deployable of getFilteredWizardApplications()" 
                   class="relative border rounded-xl p-4 cursor-pointer transition-all duration-200 hover:shadow-md"
                   [class]="isDeployableSelected(deployable) ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 bg-white'"
                   (click)="toggleDeployableSelection(deployable)">
                
                <!-- Selection Indicator -->
                <div class="absolute top-3 right-3">
                  <div class="w-5 h-5 rounded-full border-2 flex items-center justify-center"
                       [class]="isDeployableSelected(deployable) ? 'border-indigo-500 bg-indigo-500' : 'border-gray-300'">
                    <svg *ngIf="isDeployableSelected(deployable)" class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
                    </svg>
                  </div>
                </div>

                <!-- Artifact Icon -->
                <div class="flex items-center mb-3">
                  <div class="w-10 h-10 rounded-lg flex items-center justify-center text-white mr-3"
                       [class]="'bg-gradient-to-r ' + getArtifactColor(deployable.type)">
                    <i [class]="getArtifactIcon(deployable.type)"></i>
                  </div>
                  <div class="flex-1 min-w-0">
                    <h3 class="font-semibold text-gray-900 truncate">{{ deployable.name }}</h3>
                    <p class="text-sm text-gray-500">v{{ deployable.version }}</p>
                  </div>
                </div>

                <p class="text-sm text-gray-600 mb-3">{{ deployable.description || 'No description available' }}</p>

                <!-- Type & Status -->
                <div class="flex items-center justify-between">
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    {{ deployable.type }}
                  </span>
                  <div class="w-2 h-2 rounded-full"
                       [class]="deployable.status === DeploymentStatus.DEPLOYED ? 'bg-green-500' : 
                               deployable.status === DeploymentStatus.DEPLOYING ? 'bg-yellow-500' : 
                               deployable.status === DeploymentStatus.FAILED ? 'bg-red-500' : 'bg-gray-400'"></div>
                </div>
              </div>
            </div>

            <!-- Selection Summary -->
            <div *ngIf="deploymentWizard.selectedDeployables.length > 0" 
                 class="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
              <h4 class="font-medium text-indigo-900">Selected Applications for Deployment</h4>
              <div class="flex flex-wrap gap-2 mt-2">
                <span *ngFor="let deployable of deploymentWizard.selectedDeployables" 
                      class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-indigo-600 text-white">
                  {{ deployable.name }}
                  <button class="ml-2 hover:text-indigo-200" (click)="toggleDeployableSelection(deployable); $event.stopPropagation();">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/>
                    </svg>
                  </button>
                </span>
              </div>
            </div>
          </div>

          <!-- Step 2: Choose Environment -->
          <div *ngIf="deploymentWizard.step === 2" class="space-y-6">
            <!-- Header with Environment Explanation -->
            <div class="mb-4">
              <h3 class="text-xl font-semibold text-gray-900">Select Target Environment</h3>
              <p class="text-gray-600">Choose where to deploy the selected applications</p>
            </div>
            
            <!-- Loading State -->
            <div *ngIf="wizardLoading.environments" class="flex items-center justify-center py-10">
              <div class="animate-spin rounded-full h-8 w-8 border-4 border-indigo-200 border-t-indigo-600"></div>
              <span class="ml-3 text-gray-600">Loading environments...</span>
            </div>
            
            <!-- Error State -->
            <div *ngIf="wizardErrors.environments && !wizardLoading.environments" 
                 class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              <p class="font-medium">{{ wizardErrors.environments }}</p>
              <button class="text-sm underline hover:text-red-800" (click)="loadPluginData()">Try again</button>
            </div>
            
            <!-- No Environments Message -->
            <div *ngIf="!wizardLoading.environments && !wizardErrors.environments && environments.length === 0"
                 class="text-center p-8 bg-gray-50 rounded-xl border border-gray-200">
              <svg class="w-16 h-16 mx-auto text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"/>
                <path d="M12 14.17L8.83 11l-1.42 1.41L12 17 20 9l-1.41-1.41z"/>
              </svg>
              <h4 class="mt-2 text-lg font-medium text-gray-900">No environments available</h4>
              <p class="mt-1 text-sm text-gray-600">Please create an environment before deployment</p>
            </div>
            
            <!-- Environments Grid -->
            <div *ngIf="!wizardLoading.environments && !wizardErrors.environments && environments.length > 0"
                 class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div *ngFor="let env of environments" 
                   class="border rounded-xl p-6 cursor-pointer transition-all duration-200 hover:shadow-md relative overflow-hidden"
                   [class]="deploymentWizard.selectedEnvironment?.id === env.id ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 bg-white'"
                   (click)="selectEnvironment(env)">
                
                <!-- Environment Type Badge -->
                <div class="absolute top-0 right-0 bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs font-bold px-3 py-1 rounded-bl-lg">
                  ENVIRONMENT
                </div>
                
                <!-- Environment Header -->
                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center">
                    <div class="w-10 h-10 rounded-lg bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center text-white mr-3">
                      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M3.5,18.5L9.5,12.5L13.5,16.5L22,6.92L20.59,5.5L13.5,13.5L9.5,9.5L2,17L3.5,18.5Z"/>
                      </svg>
                    </div>
                    <div>
                      <h3 class="font-semibold text-gray-900">{{ env.name }}</h3>
                      <p class="text-sm text-gray-500">{{ env.type || 'Deployment Environment' }}</p>
                    </div>
                  </div>
                  <div *ngIf="deploymentWizard.selectedEnvironment?.id === env.id" 
                       class="w-5 h-5 rounded-full bg-indigo-500 flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
                    </svg>
                  </div>
                </div>

                <p class="text-sm text-gray-600 mb-4">{{ env.description || 'No description available' }}</p>

                <!-- Environment Details -->
                <div class="bg-white/50 border border-gray-100 rounded-lg p-3 mb-4">
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-xs font-medium text-gray-500">Provider:</span>
                    <span class="text-xs font-semibold">{{ env.provider?.type || 'Not specified' }}</span>
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-xs font-medium text-gray-500">Region:</span>
                    <span class="text-xs font-semibold">{{ env.provider?.region || 'Not specified' }}</span>
                  </div>
                </div>

                <!-- Environment Status -->
                <div class="flex items-center justify-between">
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <div class="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                    Available
                  </span>
                </div>
              </div>
            </div>
            
            <!-- Selected Environment Summary -->
            <div *ngIf="deploymentWizard.selectedEnvironment" 
                 class="bg-indigo-50 border border-indigo-200 rounded-lg p-4 mt-4">
              <h4 class="font-medium text-indigo-900">Selected Target Environment</h4>
              <div class="mt-2 flex items-center">
                <div class="w-6 h-6 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center text-white mr-2">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M3.5,18.5L9.5,12.5L13.5,16.5L22,6.92L20.59,5.5L13.5,13.5L9.5,9.5L2,17L3.5,18.5Z"/>
                  </svg>
                </div>
                <span class="font-semibold">{{ deploymentWizard.selectedEnvironment.name }}</span>
              </div>
            </div>
          </div>

          <!-- Step 3: Configure & Deploy -->
          <div *ngIf="deploymentWizard.step === 3" class="space-y-6">
            <!-- Deployment Summary -->
            <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
              <h3 class="text-xl font-semibold text-gray-900 mb-4">Deployment Summary</h3>
              
              <div class="space-y-6">
                <!-- Selected Applications -->
                <div>
                  <h4 class="text-sm font-medium text-gray-500 mb-2">Selected Applications</h4>
                  <div class="flex flex-wrap gap-2">
                    <span *ngFor="let deployable of deploymentWizard.selectedDeployables" 
                          class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-semibold bg-indigo-100 text-indigo-800">
                      <span class="w-2 h-2 rounded-full bg-indigo-500 mr-2"></span>
                      {{ deployable.name }}
                    </span>
                  </div>
                </div>
                
                <!-- Target Environment -->
                <div>
                  <h4 class="text-sm font-medium text-gray-500 mb-2">Target Environment</h4>
                  <div class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-semibold bg-green-100 text-green-800">
                    <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                    {{ deploymentWizard.selectedEnvironment?.name }}
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Plugin Selection -->
            <div class="space-y-4">
              <h3 class="text-lg font-semibold text-gray-900">Select Deployment Tool</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div *ngFor="let plugin of compatiblePlugins" 
                     class="border rounded-lg p-4 cursor-pointer transition-all duration-200"
                     [class]="deploymentWizard.selectedPlugin?.id === plugin.id ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:border-gray-300'"
                     (click)="selectPlugin(plugin)">
                  <div class="flex items-center justify-between mb-2">
                    <h4 class="font-medium text-gray-900">{{ plugin.name }}</h4>
                    <div *ngIf="deploymentWizard.selectedPlugin?.id === plugin.id" 
                         class="w-5 h-5 rounded-full bg-indigo-500 flex items-center justify-center">
                      <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
                      </svg>
                    </div>
                  </div>
                  <p class="text-sm text-gray-600">{{ plugin.description }}</p>
                  <div class="flex items-center mt-2">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {{ plugin.type }}
                    </span>
                    <span *ngIf="plugin.isDefault" class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Default
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Plugin Configuration -->
            <div *ngIf="deploymentWizard.selectedPlugin" class="space-y-4">
              <h3 class="text-lg font-semibold text-gray-900">Configuration</h3>
              <div class="bg-gray-50 rounded-lg p-4 space-y-4">
                <div *ngFor="let schema of deploymentWizard.selectedPlugin.configurationSchema" class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">
                    {{ schema.label }}
                    <span *ngIf="schema.required" class="text-red-500">*</span>
                  </label>
                  
                  <!-- String Input -->
                  <input *ngIf="schema.type === 'string'" 
                         type="text"
                         class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                         [value]="deploymentWizard.pluginConfiguration[schema.key] || ''"
                         [placeholder]="schema.defaultValue || ''"
                         (input)="updatePluginConfiguration(schema.key, $event)">
                  
                  <!-- Number Input -->
                  <input *ngIf="schema.type === 'number'" 
                         type="number"
                         class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                         [value]="deploymentWizard.pluginConfiguration[schema.key] || ''"
                         [min]="schema.validation?.min"
                         [max]="schema.validation?.max"
                         (input)="updatePluginConfiguration(schema.key, +$event)">
                  
                  <!-- Boolean Input -->
                  <label *ngIf="schema.type === 'boolean'" class="flex items-center">
                    <input type="checkbox"
                           class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                           [checked]="deploymentWizard.pluginConfiguration[schema.key] || false"
                           (change)="updatePluginConfiguration(schema.key, $event)">
                    <span class="ml-2 text-sm text-gray-600">{{ schema.description }}</span>
                  </label>
                  
                  <!-- Select Input -->
                  <select *ngIf="schema.type === 'select'" 
                          class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                          [value]="deploymentWizard.pluginConfiguration[schema.key] || schema.defaultValue"
                          (change)="updatePluginConfiguration(schema.key, $event)">
                    <option *ngFor="let option of schema.options" [value]="option.value">
                      {{ option.label }}
                    </option>
                  </select>
                  
                  <!-- Textarea Input -->
                  <textarea *ngIf="schema.type === 'textarea'"
                            class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                            rows="3"
                            [value]="deploymentWizard.pluginConfiguration[schema.key] || ''"
                            [placeholder]="schema.description"
                            (input)="updatePluginConfiguration(schema.key, $event)"></textarea>
                  
                  <p *ngIf="schema.description && schema.type !== 'boolean'" class="text-xs text-gray-500">{{ schema.description }}</p>
                </div>
              </div>
            </div>

            <!-- Deployment Summary -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 class="font-medium text-blue-900 mb-2">Deployment Summary</h4>
              <div class="space-y-1 text-sm text-blue-800">
                <p><strong>Applications:</strong> {{ deploymentWizard.selectedDeployables.length }} selected</p>
                <p><strong>Environment:</strong> {{ deploymentWizard.selectedEnvironment?.name }}</p>
                <p><strong>Plugin:</strong> {{ deploymentWizard.selectedPlugin?.name }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Wizard Navigation -->
        <div class="px-6 py-4 bg-gray-50/50 border-t border-gray-200/50 flex items-center justify-between">
          <button *ngIf="deploymentWizard.step > 1" 
                  class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                  (click)="previousWizardStep()">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            Previous
          </button>
          <div *ngIf="deploymentWizard.step === 1"></div>

          <div class="flex space-x-3">
            <button *ngIf="deploymentWizard.step < 3" 
                    class="inline-flex items-center px-6 py-2 text-sm font-medium text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    [disabled]="!canProceedToNextStep()"
                    (click)="nextWizardStep()">
              Next
              <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
              </svg>
            </button>

            <button *ngIf="deploymentWizard.step === 3" 
                    class="inline-flex items-center px-6 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    [disabled]="!canExecuteDeployment() || deploymentWizard.isDeploying"
                    (click)="executeWizardDeployment()">
              <svg *ngIf="deploymentWizard.isDeploying" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
              </svg>
              <svg *ngIf="!deploymentWizard.isDeploying" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
              </svg>
              {{ deploymentWizard.isDeploying ? 'Deploying...' : 'Deploy Now' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Legacy Pipeline View -->
        <div class="bg-white/70 backdrop-blur-md rounded-2xl p-6 border border-gray-200/50">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Pipeline View</h3>
          <p class="text-gray-600 mb-4">View current deployments across environments</p>
          <button class="inline-flex items-center px-4 py-2 text-sm font-medium text-indigo-600 bg-indigo-50 rounded-lg hover:bg-indigo-100 transition-colors duration-200" 
                  (click)="setView('manage')">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z"/>
            </svg>
            View Pipeline
          </button>
        </div>

        <!-- Quick Deploy -->
        <div class="bg-white/70 backdrop-blur-md rounded-2xl p-6 border border-gray-200/50">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Deploy</h3>
          <p class="text-gray-600 mb-4">Deploy using default settings</p>
          <button class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 transition-colors duration-200" 
                  (click)="openQuickDeployModal()">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
            </svg>
            Quick Deploy
          </button>
        </div>
      </div>
    </div>

    <!-- Manage View -->
    <div *ngIf="currentView === 'manage'" class="space-y-6">
      <!-- Search and Filters -->
      <div class="bg-white/70 backdrop-blur-md rounded-2xl p-6 border border-gray-200/50">
        <div class="flex flex-col lg:flex-row space-y-4 lg:space-y-0 lg:space-x-6">
          <div class="flex-1">
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
              </div>
              <input type="text" 
                     class="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 focus:border-transparent sm:text-sm"
                     placeholder="Search deployables, environments, or workflows..."
                     [(ngModel)]="searchQuery"
                     (input)="onSearch()">
            </div>
          </div>
          
          <div class="flex flex-wrap gap-2">
            <button class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200" 
                    [class]="activeFilters.includes('applications') ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
                    (click)="toggleFilter('applications')">
              <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z"/>
              </svg>
              Applications
            </button>
            <button class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200" 
                    [class]="activeFilters.includes('services') ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
                    (click)="toggleFilter('services')">
              <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              Services
            </button>
            <button class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200" 
                    [class]="activeFilters.includes('components') ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
                    (click)="toggleFilter('components')">
              <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.5 11H19V7c0-1.1-.9-2-2-2h-4V3.5C13 2.12 11.88 1 10.5 1S8 2.12 8 3.5V5H4c-1.1 0-1.99.9-1.99 2v3.8H3.5c1.49 0 2.7 1.21 2.7 2.7s-1.21 2.7-2.7 2.7H2V20c0 1.1.9 2 2 2h3.8v-1.5c0-1.49 1.21-2.7 2.7-2.7 1.49 0 2.7 1.21 2.7 2.7V22H17c1.1 0 2-.9 2-2v-4h1.5c1.38 0 2.5-1.12 2.5-2.5S21.88 11 20.5 11z"/>
              </svg>
              Components
            </button>
            <button class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200" 
                    [class]="activeFilters.includes('infrastructure') ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
                    (click)="toggleFilter('infrastructure')">
              <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              Infrastructure
            </button>
          </div>
          
          <div class="flex-shrink-0">
            <select class="block w-full px-3 py-2.5 border border-gray-300 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent" 
                    [(ngModel)]="sortBy" (change)="onSort()">
              <option value="name">Sort by Name</option>
              <option value="type">Sort by Type</option>
              <option value="status">Sort by Status</option>
              <option value="lastDeployed">Sort by Last Deployed</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Deployables Management Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <div *ngFor="let deployable of filteredDeployables" class="bg-white/70 backdrop-blur-md rounded-2xl border border-gray-200/50 hover:shadow-lg transition-all duration-200">
          <div class="p-6">
            <div class="flex items-start justify-between mb-4">
              <div class="flex items-start space-x-3 flex-1 min-w-0">
                <div class="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex-shrink-0">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24" 
                       *ngIf="deployable.type === DeployableType.APPLICATION">
                    <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z"/>
                  </svg>
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24" 
                       *ngIf="deployable.type === DeployableType.SERVICE">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24" 
                       *ngIf="deployable.type !== DeployableType.APPLICATION && deployable.type !== DeployableType.SERVICE">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                
                <div class="flex-1 min-w-0">
                  <h3 class="text-lg font-semibold text-gray-900 truncate">{{ deployable.name }}</h3>
                  <p class="text-sm text-gray-600 mt-1 line-clamp-2">{{ deployable.description }}</p>
                  <div class="flex items-center space-x-2 mt-2">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium" 
                          [class]="deployable.type.toLowerCase() === 'application' ? 'bg-blue-100 text-blue-800' : 
                                   deployable.type.toLowerCase() === 'service' ? 'bg-green-100 text-green-800' : 
                                   deployable.type.toLowerCase() === 'component' ? 'bg-purple-100 text-purple-800' : 
                                   'bg-gray-100 text-gray-800'">
                      {{ deployable.type }}
                    </span>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                      v{{ deployable.version }}
                    </span>
                    <span *ngIf="deployable.owner" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {{ deployable.owner }}
                    </span>
                  </div>
                </div>
              </div>
              
              <div class="relative">
                <button class="flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors duration-200" (click)="toggleCardMenu(deployable.id)">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                  </svg>
                </button>
                <div *ngIf="activeCardMenu === deployable.id" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                  <div class="py-1">
                    <button class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" (click)="editDeployable(deployable)">
                      <svg class="w-4 h-4 mr-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                      </svg>
                      Edit
                    </button>
                    <button class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" (click)="duplicateDeployable(deployable)">
                      <svg class="w-4 h-4 mr-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                      </svg>
                      Duplicate
                    </button>
                    <button class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" (click)="viewLogs(deployable)">
                      <svg class="w-4 h-4 mr-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zM11 3.5L18.5 9H13z"/>
                      </svg>
                      View Logs
                    </button>
                    <hr class="my-1">
                    <button class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50" (click)="deleteDeployable(deployable)">
                      <svg class="w-4 h-4 mr-3" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                      </svg>
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Environment Status -->
            <div class="mb-4">
              <h4 class="text-sm font-medium text-gray-900 mb-2">Environment Status</h4>
              <div class="space-y-2">
                <div *ngFor="let envStatus of deployable.environments" 
                     class="flex items-center justify-between p-2 bg-gray-50/50 rounded-lg">
                  <span class="text-sm text-gray-700">{{ getEnvironmentName(envStatus.name) }}</span>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 rounded-full" 
                         [class]="getEnvironmentDeploymentStatus(envStatus) === 'success' ? 'bg-green-500' : 
                                  getEnvironmentDeploymentStatus(envStatus) === 'warning' ? 'bg-yellow-500' : 
                                  getEnvironmentDeploymentStatus(envStatus) === 'danger' ? 'bg-red-500' : 'bg-gray-400'"></div>
                    <span class="text-xs text-gray-600">{{ envStatus.deploymentStatus }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-2">
              <button class="flex items-center justify-center px-3 py-2 text-xs font-medium text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 transition-colors duration-200" (click)="quickDeploy(deployable)">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                Deploy
              </button>
              <button class="flex items-center justify-center px-3 py-2 text-xs font-medium text-green-600 bg-green-100 rounded-lg hover:bg-green-200 transition-colors duration-200" (click)="promoteDeployable(deployable)">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M5 4v3h5.5v12h3V7H19V4z"/>
                </svg>
                Promote
              </button>
              <button class="flex items-center justify-center px-3 py-2 text-xs font-medium text-orange-600 bg-orange-100 rounded-lg hover:bg-orange-200 transition-colors duration-200" (click)="rollbackDeployableFromCard(deployable)">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9z"/>
                </svg>
                Rollback
              </button>
              <button class="flex items-center justify-center px-3 py-2 text-xs font-medium text-purple-600 bg-purple-100 rounded-lg hover:bg-purple-200 transition-colors duration-200" (click)="viewMetrics(deployable)">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
                </svg>
                Monitor
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Deploy Modal -->
  <div *ngIf="showQuickDeployModal" class="fixed inset-0 z-50 overflow-y-auto" (click)="closeQuickDeployModal()">
    <div class="flex items-center justify-center min-h-full p-4 text-center sm:p-0">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
      
      <div class="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg" (click)="$event.stopPropagation()">
        <div class="bg-white px-6 pt-6 pb-4">
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
              <div class="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Quick Deploy</h3>
                <p class="text-sm text-gray-600">Deploy to an environment quickly</p>
              </div>
            </div>
            <button class="text-gray-400 hover:text-gray-600 transition-colors duration-200" (click)="closeQuickDeployModal()">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/>
              </svg>
            </button>
          </div>
          
          <!-- Quick Deploy Form -->
          <form (ngSubmit)="executeQuickDeploy()" class="space-y-4">
            <!-- Environment Selection -->
            <div>
              <label class="block text-sm font-medium text-gray-900 mb-2">Target Environment</label>
              <select 
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                [(ngModel)]="quickDeployData.environmentId"
                name="environmentId"
                required>
                <option value="">Select an environment...</option>
                <option *ngFor="let env of environments" [value]="env.id">
                  {{ env.name }} ({{ env.provider || 'Unknown' }})
                </option>
              </select>
            </div>
            
            <!-- Deployment Options -->
            <div class="space-y-3">
              <div class="flex items-center">
                <input 
                  type="checkbox" 
                  id="runHealthChecks"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  [(ngModel)]="quickDeployData.runHealthChecks"
                  name="runHealthChecks">
                <label for="runHealthChecks" class="ml-2 block text-sm text-gray-900">
                  Run health checks after deployment
                </label>
              </div>
              
              <div class="flex items-center">
                <input 
                  type="checkbox" 
                  id="notifyOnComplete"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  [(ngModel)]="quickDeployData.notifyOnComplete"
                  name="notifyOnComplete">
                <label for="notifyOnComplete" class="ml-2 block text-sm text-gray-900">
                  Send notification when complete
                </label>
              </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <button 
                type="button"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                (click)="closeQuickDeployModal()">
                Cancel
              </button>
              <button 
                type="submit"
                class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                [disabled]="!quickDeployData.environmentId || loading">
                <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                {{ loading ? 'Deploying...' : 'Deploy Now' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Create Deployable Modal -->
  <div *ngIf="showCreateModal" class="fixed inset-0 z-50 overflow-y-auto" (click)="closeCreateModal()">
    <div class="flex items-center justify-center min-h-full p-4 text-center sm:p-0">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
      
      <div class="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl" (click)="$event.stopPropagation()">
        <div class="bg-white px-6 pt-6 pb-4">
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
              <div class="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-green-600 to-blue-600 rounded-xl">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Create New Application</h3>
                <p class="text-sm text-gray-600">Add a new deployable application that can be deployed to environments</p>
              </div>
            </div>
            <button class="text-gray-400 hover:text-gray-600 transition-colors duration-200" (click)="closeCreateModal()">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/>
              </svg>
            </button>
          </div>
          
          <!-- Create Form -->
          <form [formGroup]="createForm" (ngSubmit)="createDeployable()" class="space-y-6">
            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-900 mb-2">Name *</label>
                <input 
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  formControlName="name"
                  placeholder="Enter deployable name...">
                <div *ngIf="createForm.get('name')?.invalid && createForm.get('name')?.touched" class="mt-1 text-sm text-red-600">
                  Name is required (minimum 3 characters)
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-900 mb-2">Type *</label>
                <select 
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  formControlName="type">
                  <option value="">Select application type...</option>
                  <option [value]="DeployableType.APPLICATION">Standard Application</option>
                  <option [value]="DeployableType.WEB_APPLICATION">Web Application</option>
                  <option [value]="DeployableType.MICROSERVICE">Microservice</option>
                  <option [value]="DeployableType.SERVICE">Service</option>
                  <option [value]="DeployableType.API_GATEWAY">API Gateway</option>
                  <option [value]="DeployableType.MOBILE_APP">Mobile Application</option>
                  <option [value]="DeployableType.DESKTOP_APP">Desktop Application</option>
                </select>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-900 mb-2">Description</label>
              <textarea 
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                rows="3"
                formControlName="description"
                placeholder="Enter description..."></textarea>
            </div>
            
            <!-- Repository Information -->
            <div formGroupName="repository" class="space-y-4">
              <h4 class="text-md font-medium text-gray-900">Repository Information</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-900 mb-2">Repository URL *</label>
                  <input 
                    type="url"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    formControlName="url"
                    placeholder="https://github.com/user/repo.git">
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-900 mb-2">Branch</label>
                  <input 
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    formControlName="branch"
                    placeholder="main">
                </div>
              </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <button 
                type="button"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                (click)="closeCreateModal()">
                Cancel
              </button>
              <button 
                type="submit"
                class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                [disabled]="createForm.invalid || loading">
                <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
                </svg>
                {{ loading ? 'Creating...' : 'Create Deployable' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
