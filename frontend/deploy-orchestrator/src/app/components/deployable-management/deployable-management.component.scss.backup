// Modern Deployment Hub - Tailwind CSS Component
// This component uses Tailwind CSS utility classes for styling
// Custom styles are minimal as most styling is handled via Tailwind utilities

// Component-specific overrides and custom styles only
.deployable-management {
  // Any component-specific styles that cannot be achieved with Tailwind utilities
  // Most styling is now handled by Tailwind CSS classes in the template
}

// Custom animations and transitions that might not be available in Tailwind
@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse-slow {
  animation: pulse-slow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

// Custom backdrop blur for browsers that need additional support
.custom-backdrop-blur {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}
  .main-content {
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;

    // Overview Tab Styles
    .overview-content {
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;

        .stat-card {
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(20px);
          border-radius: 1rem;
          padding: 2rem;
          text-align: center;
          border: 1px solid rgba(255, 255, 255, 0.2);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
          }

          .stat-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }

          .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
          }

          .stat-label {
            color: #64748b;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.5px;
          }
        }
      }

      .overview-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 2rem;

        .activity-section, .environments-section {
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(20px);
          border-radius: 1rem;
          padding: 2rem;
          border: 1px solid rgba(255, 255, 255, 0.2);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

          .section-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f1f5f9;

            .material-icons {
              font-size: 1.5rem;
              color: #667eea;
            }

            h3 {
              margin: 0;
              font-size: 1.25rem;
              font-weight: 600;
              color: #1e293b;
            }
          }
        }

        .activity-timeline {
          .activity-item {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid #f1f5f9;

            &:last-child {
              margin-bottom: 0;
              padding-bottom: 0;
              border-bottom: none;
            }

            .activity-icon {
              width: 2.5rem;
              height: 2.5rem;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              font-size: 1rem;
            }

            .activity-content {
              flex: 1;

              .activity-title {
                font-weight: 600;
                color: #1e293b;
                margin-bottom: 0.25rem;
                font-size: 0.9rem;
              }

              .activity-description {
                color: #64748b;
                font-size: 0.8rem;
                margin-bottom: 0.5rem;
              }

              .activity-time {
                color: #94a3b8;
                font-size: 0.75rem;
                font-weight: 500;
              }
            }
          }
        }

        .environment-grid {
          display: grid;
          gap: 1rem;

          .environment-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 0.75rem;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            }

            &.healthy {
              border-left: 4px solid #10b981;
            }

            &.warning {
              border-left: 4px solid #f59e0b;
            }

            &.critical {
              border-left: 4px solid #ef4444;
            }

            &.empty {
              border-left: 4px solid #94a3b8;
            }

            .env-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 0.75rem;

              .env-name {
                font-weight: 600;
                color: #1e293b;
                font-size: 1rem;
              }

              .env-status {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.8rem;
                font-weight: 500;

                &.healthy { color: #10b981; }
                &.warning { color: #f59e0b; }
                &.critical { color: #ef4444; }
                &.empty { color: #94a3b8; }

                .material-icons {
                  font-size: 1rem;
                }
              }
            }

            .env-details {
              color: #64748b;
              font-size: 0.85rem;
            }
          }
        }
      }
    }

    // Deploy Tab Styles
    .deploy-content {
      .deployment-pipeline {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

        .pipeline-header {
          text-align: center;
          margin-bottom: 2rem;

          h3 {
            color: #1e293b;
            font-weight: 600;
            margin-bottom: 0.5rem;
          }

          p {
            color: #64748b;
            margin: 0;
          }
        }

        .pipeline-flow {
          display: flex;
          justify-content: space-between;
          align-items: stretch;
          gap: 2rem;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 10%;
            right: 10%;
            height: 3px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            z-index: 0;
            border-radius: 3px;
            opacity: 0.3;
          }

          .pipeline-stage {
            flex: 1;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 1rem;
            padding: 1.5rem;
            text-align: center;
            border: 2px dashed #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;

            &.drag-over {
              border-color: #667eea;
              background: rgba(102, 126, 234, 0.05);
              transform: scale(1.02);
            }

            &.has-deployments {
              border-style: solid;
              border-color: #667eea;
              background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            }

            .stage-header {
              .stage-icon {
                font-size: 2.5rem;
                color: #667eea;
                margin-bottom: 1rem;
              }

              .stage-title {
                font-weight: 600;
                color: #1e293b;
                margin-bottom: 0.5rem;
                font-size: 1.1rem;
              }

              .stage-subtitle {
                color: #64748b;
                font-size: 0.85rem;
                margin-bottom: 1rem;
              }
            }

            .stage-deployables {
              .deployable-chip {
                display: inline-block;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 2rem;
                font-size: 0.8rem;
                font-weight: 500;
                margin: 0.25rem;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
                }

                .material-icons {
                  font-size: 0.9rem;
                  margin-right: 0.5rem;
                  vertical-align: middle;
                }
              }
            }

            .stage-actions {
              margin-top: auto;
              padding-top: 1rem;

              .btn {
                border-radius: 0.5rem;
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
                font-weight: 500;
                border: none;
                background: #f8fafc;
                color: #64748b;
                transition: all 0.3s ease;

                &:hover {
                  background: #667eea;
                  color: white;
                  transform: translateY(-1px);
                }
              }
            }
          }
        }
      }

      .undeployed-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 1rem;
        padding: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

        .section-header {
          margin-bottom: 1.5rem;

          h4 {
            color: #1e293b;
            font-weight: 600;
            margin-bottom: 0.5rem;
          }

          p {
            color: #64748b;
            margin: 0;
          }
        }

        .deployables-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
          gap: 1rem;

          .deployable-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 0.75rem;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
            cursor: grab;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-4px);
              box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }

            &:active {
              cursor: grabbing;
            }

            .deployable-header {
              display: flex;
              align-items: center;
              gap: 0.75rem;
              margin-bottom: 1rem;

              .deployable-icon {
                width: 2.5rem;
                height: 2.5rem;
                border-radius: 0.5rem;
                display: flex;
                align-items: center;
                justify-content: center;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-size: 1.2rem;
              }

              .deployable-info {
                flex: 1;

                .deployable-name {
                  font-weight: 600;
                  color: #1e293b;
                  margin-bottom: 0.25rem;
                  font-size: 1rem;
                }

                .deployable-type {
                  background: #f1f5f9;
                  color: #64748b;
                  padding: 0.25rem 0.5rem;
                  border-radius: 0.25rem;
                  font-size: 0.7rem;
                  font-weight: 500;
                  text-transform: uppercase;
                }
              }
            }

            .deployable-description {
              color: #64748b;
              font-size: 0.85rem;
              margin-bottom: 1rem;
              line-height: 1.4;
            }

            .deployable-actions {
              display: flex;
              gap: 0.5rem;

              .btn {
                flex: 1;
                border-radius: 0.5rem;
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
                font-weight: 500;
                border: 1px solid #e2e8f0;
                background: white;
                color: #64748b;
                transition: all 0.3s ease;

                &:hover {
                  background: #667eea;
                  color: white;
                  border-color: #667eea;
                }
              }
            }
          }
        }
      }
    }

    // Manage Tab Styles
    .manage-content {
      .search-and-filters {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

        .search-bar {
          position: relative;
          margin-bottom: 1.5rem;

          .form-control {
            padding: 1rem 1rem 1rem 3rem;
            border-radius: 0.75rem;
            border: 2px solid #e2e8f0;
            background: rgba(255, 255, 255, 0.9);
            font-size: 1rem;
            transition: all 0.3s ease;

            &:focus {
              border-color: #667eea;
              box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
              outline: none;
            }
          }

          .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #94a3b8;
            font-size: 1.2rem;
            pointer-events: none;
          }
        }

        .filter-chips {
          display: flex;
          flex-wrap: wrap;
          gap: 0.75rem;
          margin-bottom: 1rem;

          .filter-chip {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;

            &:hover {
              background: rgba(102, 126, 234, 0.2);
              transform: translateY(-1px);
            }

            &.active {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              border-color: #667eea;
            }

            .material-icons {
              font-size: 1rem;
              margin-right: 0.5rem;
              vertical-align: middle;
            }
          }
        }

        .sort-controls {
          display: flex;
          align-items: center;
          gap: 1rem;

          label {
            color: #64748b;
            font-weight: 500;
            font-size: 0.9rem;
          }

          .form-select {
            border-radius: 0.5rem;
            border: 1px solid #e2e8f0;
            padding: 0.5rem 1rem;
            background: white;
            color: #1e293b;
            font-size: 0.9rem;

            &:focus {
              border-color: #667eea;
              box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
              outline: none;
            }
          }
        }
      }

      .deployables-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;

        .deployable-card {
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(20px);
          border-radius: 1rem;
          padding: 0;
          border: 1px solid rgba(255, 255, 255, 0.2);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          overflow: hidden;
          position: relative;

          &:hover {
            transform: translateY(-6px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
          }

          .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            position: relative;

            .menu-button {
              position: absolute;
              top: 1rem;
              right: 1rem;
              background: rgba(255, 255, 255, 0.9);
              border: 1px solid #e2e8f0;
              border-radius: 50%;
              width: 2.5rem;
              height: 2.5rem;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                background: #667eea;
                color: white;
                border-color: #667eea;
              }
            }

            .card-menu {
              position: absolute;
              top: 3.5rem;
              right: 1rem;
              background: white;
              border-radius: 0.75rem;
              padding: 0.5rem;
              box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
              border: 1px solid #e2e8f0;
              z-index: 10;
              min-width: 180px;

              .menu-item {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                padding: 0.75rem 1rem;
                border-radius: 0.5rem;
                cursor: pointer;
                transition: all 0.2s ease;
                color: #64748b;
                font-size: 0.9rem;

                &:hover {
                  background: #f8fafc;
                  color: #1e293b;
                }

                &.delete {
                  color: #ef4444;
                  
                  &:hover {
                    background: rgba(239, 68, 68, 0.1);
                  }
                }

                .material-icons {
                  font-size: 1.1rem;
                }
              }
            }

            .deployable-title {
              display: flex;
              align-items: center;
              gap: 1rem;
              margin-bottom: 1rem;

              .deployable-icon {
                width: 3rem;
                height: 3rem;
                border-radius: 0.75rem;
                display: flex;
                align-items: center;
                justify-content: center;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-size: 1.5rem;
              }

              .title-info {
                flex: 1;

                h4 {
                  margin: 0;
                  font-size: 1.2rem;
                  font-weight: 600;
                  color: #1e293b;
                  margin-bottom: 0.25rem;
                }

                .deployable-type {
                  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
                  color: #667eea;
                  padding: 0.25rem 0.75rem;
                  border-radius: 1rem;
                  font-size: 0.75rem;
                  font-weight: 600;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;
                }
              }
            }

            .deployable-description {
              color: #64748b;
              font-size: 0.9rem;
              line-height: 1.5;
              margin: 0;
            }
          }

          .card-body {
            padding: 1.5rem;

            .environments-section {
              margin-bottom: 1.5rem;

              .section-label {
                font-size: 0.8rem;
                font-weight: 600;
                color: #64748b;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                margin-bottom: 0.75rem;
              }

              .environment-badges {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;

                .env-badge {
                  display: flex;
                  align-items: center;
                  gap: 0.5rem;
                  padding: 0.5rem 0.75rem;
                  border-radius: 0.5rem;
                  font-size: 0.8rem;
                  font-weight: 500;
                  border: 1px solid;

                  &.deployed {
                    background: rgba(16, 185, 129, 0.1);
                    color: #10b981;
                    border-color: rgba(16, 185, 129, 0.2);
                  }

                  &.deploying {
                    background: rgba(245, 158, 11, 0.1);
                    color: #f59e0b;
                    border-color: rgba(245, 158, 11, 0.2);
                  }

                  &.failed {
                    background: rgba(239, 68, 68, 0.1);
                    color: #ef4444;
                    border-color: rgba(239, 68, 68, 0.2);
                  }

                  &.pending {
                    background: rgba(59, 130, 246, 0.1);
                    color: #3b82f6;
                    border-color: rgba(59, 130, 246, 0.2);
                  }

                  .material-icons {
                    font-size: 0.9rem;
                  }
                }

                .no-deployments {
                  color: #94a3b8;
                  font-size: 0.85rem;
                  font-style: italic;
                }
              }
            }

            .card-actions {
              display: flex;
              gap: 0.75rem;

              .btn {
                flex: 1;
                border-radius: 0.5rem;
                padding: 0.75rem 1rem;
                font-size: 0.85rem;
                font-weight: 500;
                border: none;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;

                &.btn-primary {
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  color: white;

                  &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
                  }
                }

                &.btn-outline-primary {
                  border: 1px solid #667eea;
                  color: #667eea;
                  background: transparent;

                  &:hover {
                    background: #667eea;
                    color: white;
                    transform: translateY(-2px);
                  }
                }

                .material-icons {
                  font-size: 1rem;
                }
              }
            }
          }
        }
      }
    }
  }

  // Quick Deploy Modal
  .modal {
    .modal-dialog {
      .modal-content {
        border-radius: 1rem;
        border: none;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(20px);

        .modal-header {
          border-bottom: 1px solid #f1f5f9;
          padding: 2rem 2rem 1rem;

          .modal-title {
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 0.75rem;

            .material-icons {
              color: #667eea;
              font-size: 1.5rem;
            }
          }

          .btn-close {
            border-radius: 0.5rem;
            padding: 0.5rem;
          }
        }

        .modal-body {
          padding: 1rem 2rem 2rem;

          .form-group {
            margin-bottom: 1.5rem;

            label {
              font-weight: 500;
              color: #374151;
              margin-bottom: 0.5rem;
              display: block;
            }

            .form-control, .form-select {
              border-radius: 0.5rem;
              border: 1px solid #e2e8f0;
              padding: 0.75rem 1rem;
              font-size: 0.9rem;
              transition: all 0.3s ease;

              &:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                outline: none;
              }
            }
          }

          .form-check {
            margin-bottom: 1rem;

            .form-check-input {
              &:checked {
                background-color: #667eea;
                border-color: #667eea;
              }

              &:focus {
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.2);
              }
            }

            .form-check-label {
              color: #374151;
              font-size: 0.9rem;
            }
          }
        }

        .modal-footer {
          border-top: 1px solid #f1f5f9;
          padding: 1rem 2rem 2rem;

          .btn {
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;

            &.btn-primary {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              border: none;

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
              }
            }

            &.btn-secondary {
              background: #f8fafc;
              color: #64748b;
              border: 1px solid #e2e8f0;

              &:hover {
                background: #f1f5f9;
                color: #374151;
              }
            }
          }
        }
      }
    }
  }

  // Loading States
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;

    .spinner-border {
      color: #667eea;
      width: 3rem;
      height: 3rem;
    }
  }

  // Empty States
  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    .empty-icon {
      font-size: 4rem;
      color: #cbd5e1;
      margin-bottom: 1.5rem;
    }

    h3 {
      color: #64748b;
      margin-bottom: 1rem;
      font-weight: 500;
    }

    p {
      color: #94a3b8;
      margin-bottom: 2rem;
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    .main-header .header-content {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }

    .main-content {
      padding: 1rem;
    }

    .overview-content .overview-grid {
      grid-template-columns: 1fr;
    }

    .deploy-content .pipeline-flow {
      flex-direction: column;
      
      &::before {
        left: 50%;
        right: auto;
        top: 0;
        bottom: 0;
        width: 3px;
        height: auto;
        transform: translateX(-50%);
      }
    }

    .deployables-grid {
      grid-template-columns: 1fr !important;
    }

    .view-navigation .nav-container .nav-tabs .nav-link {
      padding: 0.75rem 1rem;
      font-size: 0.9rem;
    }
  }

  @media (max-width: 480px) {
    .stats-grid {
      grid-template-columns: 1fr !important;
    }

    .filter-chips {
      justify-content: center;
    }

    .sort-controls {
      flex-direction: column;
      align-items: stretch;
      gap: 0.5rem;
    }
  }
}

// Animation Classes
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease forwards;
}

.slide-in-right {
  animation: slideInRight 0.4s ease forwards;
}

.pulse {
  animation: pulse 2s infinite;
}

// Utility Classes
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-soft {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.shadow-strong {
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.backdrop-blur {
  backdrop-filter: blur(20px);
}
