// Modern Deployment Hub - Tailwind CSS Component
// This component uses Tailwind CSS utility classes for styling
// Custom styles are minimal as most styling is handled via Tailwind utilities

// Component-specific overrides and custom styles only
// Since we're using Tailwind CSS, most styling is handled via utility classes
// This section is reserved for any future component-specific styles that cannot be achieved with Tailwind utilities

// Custom animations and transitions that might not be available in Tailwind
@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse-slow {
  animation: pulse-slow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

// Custom backdrop blur for browsers that need additional support
.custom-backdrop-blur {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}
