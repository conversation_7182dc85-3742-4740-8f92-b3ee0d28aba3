import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Subject, combineLatest, interval } from 'rxjs';
import { takeUntil, debounceTime, distinctUntilChanged, startWith } from 'rxjs/operators';

import { DeployableService } from '../../services/deployable.service';
import { WorkflowService } from '../../services/workflow.service';
import { EnvironmentService } from '../../services/environment.service';
import { ProjectService } from '../../services/project.service';
import { DeploymentPluginService } from '../../services/deployment-plugin.service';
import { 
  Deployable, 
  DeployableType, 
  DeploymentStatus,
  DeployableCreateRequest,
  WorkflowReference,
  RepositoryInfo,
  isApplication,
  isComponent,
  isService,
  isInfrastructure,
  DeploymentWizardState,
  DeploymentPlugin,
  PluginConfigSchema,
  EnvironmentPluginMapping,
  DeploymentRequest,
  DeployableGroup,
  getArtifactIcon,
  getArtifactColor
} from '../../models/deployable.model';

interface QuickDeployData {
  deployableId: string;
  environmentId: string;
  runHealthChecks: boolean;
  notifyOnComplete: boolean;
}

interface ActivityItem {
  id: string;
  type: 'deployment' | 'promotion' | 'rollback' | 'creation';
  icon: string;
  title: string;
  description: string;
  timestamp: Date;
}

@Component({
  selector: 'app-deployable-management',
  templateUrl: './deployable-management.component.html',
  styleUrls: ['./deployable-management.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    DatePipe
  ]
})
export class DeployableManagementComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Expose enums to template
  DeployableType = DeployableType;
  DeploymentStatus = DeploymentStatus;

  // Expose helper functions to template
  getArtifactIcon = getArtifactIcon;
  getArtifactColor = getArtifactColor;

  // Project filtering
  currentProjectId: string | null = null;

  // Data
  deployables: Deployable[] = [];
  filteredDeployables: Deployable[] = [];
  selectedDeployable: Deployable | null = null;
  parentDeployable: Deployable | null = null;
  environments: any[] = [];
  workflows: WorkflowReference[] = [];
  recentActivity: ActivityItem[] = [];

  // Deployment Wizard State
  deploymentWizard: DeploymentWizardState = {
    step: 1,
    selectedDeployables: [],
    selectedGroups: [],
    selectedEnvironment: null,
    selectedPlugin: null,
    pluginConfiguration: {},
    isDeploying: false
  };

  // Plugin Management
  availablePlugins: DeploymentPlugin[] = [];
  environmentPluginMappings: EnvironmentPluginMapping[] = [];
  compatiblePlugins: DeploymentPlugin[] = [];

  // Loading and Error States
  wizardLoading = {
    plugins: false,
    environments: false,
    applications: false,
    deploying: false
  };
  
  wizardErrors = {
    plugins: null as string | null,
    environments: null as string | null,
    applications: null as string | null,
    deployment: null as string | null
  };
  
  // Application data specifically for the wizard to separate from environments
  wizardApplications: Deployable[] = [];

  // UI State
  loading = false;
  currentView: 'overview' | 'deploy' | 'manage' = 'overview';
  showQuickDeployModal = false;
  showCreateModal = false;
  showDetailsPanel = false;
  showDeployMenu = false;
  showActionsMenu = false;
  activeCardMenu: string | null = null;
  isDragOver: string | null = null;
  viewMode: 'list' | 'grid' | 'hierarchy' = 'list';

  // Search and Filtering
  searchQuery = '';
  activeFilters: string[] = [];
  sortBy = 'name';

  // Quick Deploy
  quickDeployData: QuickDeployData = {
    deployableId: '',
    environmentId: '',
    runHealthChecks: true,
    notifyOnComplete: false
  };

  // Forms
  filterForm!: FormGroup;
  createForm!: FormGroup;

  // Search text for the wizard application filtering
  wizardSearchQuery: string = '';
  
  // Filtered applications for the wizard
  filteredWizardApplications: Deployable[] = [];
  
  constructor(
    private deployableService: DeployableService,
    private workflowService: WorkflowService,
    private environmentService: EnvironmentService,
    private projectService: ProjectService,
    private deploymentPluginService: DeploymentPluginService,
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder
  ) {
    this.initializeForms();
  }

  ngOnInit(): void {
    this.setupProjectSubscription();
    this.setupRouteParams();
    this.setupFormSubscriptions();
    this.setupRealtimeUpdates();
    this.loadPluginData();
    this.startDeploymentWizard(); // Initialize wizard state
  }
  
  private setupProjectSubscription(): void {
    // Subscribe to the selected project changes
    this.projectService.selectedProject$
      .pipe(takeUntil(this.destroy$))
      .subscribe(project => {
        this.currentProjectId = project?.id || null;
        this.loadInitialData();
      });
  }

  // View Management
  setView(view: 'overview' | 'deploy' | 'manage'): void {
    this.currentView = view;
    this.activeCardMenu = null;
  }

  // Stats Methods
  getApplicationCount(): number {
    return this.deployables.filter(d => d.type === DeployableType.APPLICATION).length;
  }

  getServiceCount(): number {
    return this.deployables.filter(d => d.type === DeployableType.SERVICE).length;
  }

  getActiveDeployments(): number {
    return this.deployables.reduce((count, d) => {
      return count + (d.environments?.filter(e => 
        e.deploymentStatus === DeploymentStatus.DEPLOYED || e.deploymentStatus === DeploymentStatus.DEPLOYING
      ).length || 0);
    }, 0);
  }

  // Activity Methods
  getRecentActivity(): ActivityItem[] {
    // Use real activity data from the service
    return this.recentActivity;
  }

  /**
   * Load recent activity for deployables (environments)
   */
  loadRecentActivity(): void {
    if (!this.currentProjectId) {
      this.recentActivity = [];
      return;
    }

    this.deployableService.getDeployables({ projectId: this.currentProjectId })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          const deployables = response.deployables || [];
          const activities: ActivityItem[] = [];

          // Process deployables to create activity items
          deployables.forEach(deployable => {
            if (deployable.environments && deployable.environments.length > 0) {
              deployable.environments.forEach(env => {
                if (env.lastDeployedAt) {
                  activities.push({
                    id: `${deployable.id}-${env.name}`,
                    type: 'deployment',
                    icon: 'rocket_launch',
                    title: `Deployed ${deployable.name} to ${env.name}`,
                    description: `Successfully deployed version ${deployable.version} to ${env.name} environment`,
                    timestamp: new Date(env.lastDeployedAt)
                  });
                }
              });
            }

            // Add creation activity
            activities.push({
              id: deployable.id,
              type: 'creation',
              icon: 'add_circle',
              title: `Created new ${this.getDeployableTypeLabel(deployable.type)}`,
              description: `${deployable.name} has been added to the system`,
              timestamp: new Date(deployable.createdAt)
            });
          });

          // Sort activities by timestamp (newest first)
          this.recentActivity = activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()).slice(0, 5);
        },
        error: (error) => {
          console.error('Error loading recent activity:', error);
          this.recentActivity = [];
        }
      });
  }

  private getDeployableTypeLabel(type: DeployableType): string {
    switch (type) {
      case DeployableType.APPLICATION:
        return 'application';
      case DeployableType.MICROSERVICE:
        return 'microservice';
      case DeployableType.DATABASE:
        return 'database';
      case DeployableType.SERVICE:
        return 'service';
      default:
        return type.toString().toLowerCase().replace('_', ' ');
    }
  }
  

  // Environment Methods
  getEnvironmentStatusClass(env: any): string {
    const healthyDeployments = this.getDeployablesInEnvironment(env.id)
      .filter(d => d.status === DeploymentStatus.DEPLOYED).length;
    const totalDeployments = this.getDeployablesInEnvironment(env.id).length;
    
    if (totalDeployments === 0) return 'empty';
    if (healthyDeployments === totalDeployments) return 'healthy';
    if (healthyDeployments > totalDeployments / 2) return 'warning';
    return 'critical';
  }

  getEnvironmentStatusIcon(env: any): string {
    const statusClass = this.getEnvironmentStatusClass(env);
    switch (statusClass) {
      case 'healthy': return 'check_circle';
      case 'warning': return 'warning';
      case 'critical': return 'error';
      default: return 'remove_circle_outline';
    }
  }

  getEnvironmentStatusText(env: any): string {
    const statusClass = this.getEnvironmentStatusClass(env);
    switch (statusClass) {
      case 'healthy': return 'All services healthy';
      case 'warning': return 'Some issues detected';
      case 'critical': return 'Critical issues';
      default: return 'No deployments';
    }
  }

  getDeployablesInEnvironment(environmentId: string): Deployable[] {
    return this.deployables.filter(d => 
      d.environments?.some(e => e.name === environmentId) || false
    );
  }

  getUndeployedDeployables(): Deployable[] {
    return this.deployables.filter(d => !d.environments || d.environments.length === 0);
  }

  getEnvironmentName(environmentId: string): string {
    const env = this.environments.find(e => e.id === environmentId);
    return env ? env.name : 'Unknown';
  }

  // Drag and Drop Methods
  onDragStart(event: DragEvent, deployable: Deployable): void {
    if (event.dataTransfer) {
      event.dataTransfer.setData('text/plain', JSON.stringify({
        id: deployable.id,
        name: deployable.name,
        type: deployable.type
      }));
      event.dataTransfer.effectAllowed = 'move';
    }
  }

  onDragOver(event: DragEvent, environmentId: string): void {
    event.preventDefault();
    event.dataTransfer!.dropEffect = 'move';
    this.isDragOver = environmentId;
  }

  onDrop(event: DragEvent, environmentId: string): void {
    event.preventDefault();
    this.isDragOver = null;
    
    const data = event.dataTransfer?.getData('text/plain');
    if (data) {
      const deployableInfo = JSON.parse(data);
      const deployable = this.deployables.find(d => d.id === deployableInfo.id);
      if (deployable) {
        this.deployToEnvironment(deployable, environmentId);
      }
    }
  }

  // Deployment Methods
  quickDeploy(deployable: Deployable): void {
    this.quickDeployData.deployableId = deployable.id;
    this.showQuickDeployModal = true;
  }

  configureDeployment(deployable: Deployable): void {
    // Navigate to detailed deployment configuration
    this.router.navigate(['/workflows/deploy'], {
      queryParams: { deployableId: deployable.id }
    });
  }

  executeQuickDeploy(): void {
    const deployable = this.deployables.find(d => d.id === this.quickDeployData.deployableId);
    if (deployable && this.quickDeployData.environmentId) {
      this.executeDeployment(deployable, this.quickDeployData.environmentId);
      this.closeQuickDeployModal();
    }
  }

  private executeDeployment(deployable: Deployable, environmentId: string): void {
    if (!deployable || !environmentId) {
      console.error('Invalid parameters for deployment');
      return;
    }

    // Find the target environment
    const targetEnvironment = this.environments.find(env => env.id === environmentId);
    if (!targetEnvironment) {
      console.error('Target environment not found:', environmentId);
      alert('Target environment not found');
      return;
    }

    this.loading = true;

    // Select appropriate plugin and configuration based on environment
    const pluginConfig = this.getPluginConfiguration(targetEnvironment, deployable);
    
    console.log(`Deploying ${deployable.name} to ${targetEnvironment.name} using plugin: ${pluginConfig.plugin}`, pluginConfig);

    // Use the actual deployment service with plugin-specific configuration
    this.deployableService.deployToEnvironment(
      deployable.id, 
      environmentId, 
      this.currentProjectId || undefined,
      {
        runHealthChecks: this.quickDeployData.runHealthChecks,
        notifyOnComplete: this.quickDeployData.notifyOnComplete,
        pluginConfig: pluginConfig  // Include plugin configuration
      } as any
    ).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (result) => {
        console.log(`Deployment initiated for ${deployable.name} to environment ${environmentId}`, result);
        
        // Update local deployment status
        const envIndex = deployable.environments.findIndex(e => e.name === environmentId);
        if (envIndex >= 0) {
          deployable.environments[envIndex].deploymentStatus = DeploymentStatus.DEPLOYING;
        } else {
          deployable.environments.push({
            name: environmentId,
            status: 'unknown' as any,
            currentVersion: deployable.version,
            deploymentStatus: DeploymentStatus.DEPLOYING,
            lastDeployedAt: new Date().toISOString(),
            instances: [],
            healthScore: 0
          });
        }
        
        // Add to recent activity with plugin information
        this.recentActivity.unshift({
          id: `activity-${Date.now()}`,
          type: 'deployment',
          icon: 'fas fa-rocket',
          title: 'Deployment Started',
          description: `${deployable.name} deployment to ${targetEnvironment.name} initiated using ${pluginConfig.plugin}`,
          timestamp: new Date()
        });
        
        this.refreshDeploymentStatus();
        this.loading = false;
      },
      error: (error) => {
        console.error('Error executing deployment:', error);
        this.loading = false;
        
        // Show error message with plugin information
        alert(`Failed to deploy ${deployable.name} using ${pluginConfig.plugin}: ${error.message || 'Unknown error'}`);
      }
    });
  }

  promoteToNext(environmentId: string): void {
    const currentIndex = this.environments.findIndex(e => e.id === environmentId);
    if (currentIndex < this.environments.length - 1) {
      const nextEnvironment = this.environments[currentIndex + 1];
      const deployablesToPromote = this.getDeployablesInEnvironment(environmentId);
      
      deployablesToPromote.forEach(deployable => {
        this.executeDeployment(deployable, nextEnvironment.id);
      });
    }
  }

  promoteDeployable(deployable: Deployable): void {
    // Find the highest environment this deployable is deployed to
    const deployedEnvs = deployable.environments.map(e => e.name);
    const envIndexes = deployedEnvs.map(id => 
      this.environments.findIndex(env => env.id === id)
    ).filter(idx => idx >= 0);
    
    if (envIndexes.length > 0) {
      const highestIndex = Math.max(...envIndexes);
      if (highestIndex < this.environments.length - 1) {
        const nextEnv = this.environments[highestIndex + 1];
        this.executeDeployment(deployable, nextEnv.id);
      }
    }
  }

  // Modal Methods
  openQuickDeployModal(): void {
    this.showQuickDeployModal = true;
  }

  closeQuickDeployModal(): void {
    this.showQuickDeployModal = false;
    this.quickDeployData = {
      deployableId: '',
      environmentId: '',
      runHealthChecks: true,
      notifyOnComplete: false
    };
  }

  // Modal and Panel Management
  openCreateModal(): void {
    this.showCreateModal = true;
  }

  closeCreateModal(): void {
    this.showCreateModal = false;
    this.createForm.reset();
  }

  openDetailsPanel(deployable: Deployable): void {
    this.selectedDeployable = deployable;
    this.showDetailsPanel = true;
  }

  closeDetailsPanel(): void {
    this.showDetailsPanel = false;
    this.selectedDeployable = null;
  }

  // Card Menu Methods
  toggleCardMenu(deployableId: string): void {
    this.activeCardMenu = this.activeCardMenu === deployableId ? null : deployableId;
  }

  editDeployable(deployable: Deployable): void {
    this.router.navigate(['/deployables', deployable.id, 'edit']);
    this.activeCardMenu = null;
  }

  duplicateDeployable(deployable: Deployable): void {
    console.log('Duplicating deployable:', deployable.name);
    this.activeCardMenu = null;
  }

  viewLogs(deployable: Deployable): void {
    this.router.navigate(['/logs'], { queryParams: { deployableId: deployable.id } });
    this.activeCardMenu = null;
  }

  viewMetrics(deployable: Deployable): void {
    this.router.navigate(['/monitoring'], { queryParams: { deployableId: deployable.id } });
    this.activeCardMenu = null;
  }

  deleteDeployable(deployable: Deployable): void {
    if (confirm(`Are you sure you want to delete ${deployable.name}?`)) {
      console.log('Deleting deployable:', deployable.name);
      this.activeCardMenu = null;
    }
  }

  // Search and Filter Methods
  onSearch(): void {
    this.applyFilters();
  }

  toggleFilter(filter: string): void {
    const index = this.activeFilters.indexOf(filter);
    if (index >= 0) {
      this.activeFilters.splice(index, 1);
    } else {
      this.activeFilters.push(filter);
    }
    this.applyFilters();
  }

  onSort(): void {
    this.applyFilters();
  }

  private applyFilters(): void {
    let filtered = this.deployables;

    // Apply search
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(d => 
        d.name.toLowerCase().includes(query) ||
        d.description?.toLowerCase().includes(query) ||
        d.type.toLowerCase().includes(query)
      );
    }

    // Apply type filters
    if (this.activeFilters.length > 0) {
      filtered = filtered.filter(d => {
        const typeFilter = d.type.toLowerCase();
        return this.activeFilters.some(filter => {
          switch (filter) {
            case 'applications': return typeFilter === 'application';
            case 'services': return typeFilter === 'service';
            case 'components': return typeFilter === 'component';
            case 'infrastructure': return typeFilter === 'infrastructure';
            default: return false;
          }
        });
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (this.sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'type':
          return a.type.localeCompare(b.type);
        case 'status':
          return a.status.localeCompare(b.status);
        case 'lastDeployed':
          // Sort by most recent deployment
          const aLastDeploy = Math.max(...a.environments.map(e => 
            e.lastDeployedAt ? new Date(e.lastDeployedAt).getTime() : 0
          ));
          const bLastDeploy = Math.max(...b.environments.map(e => 
            e.lastDeployedAt ? new Date(e.lastDeployedAt).getTime() : 0
          ));
          return bLastDeploy - aLastDeploy;
        default:
          return 0;
      }
    });

    this.filteredDeployables = filtered;
  }

  // Status and Icon Methods
  getStatusClass(status: DeploymentStatus): string {
    switch (status) {
      case DeploymentStatus.DEPLOYED: return 'success';
      case DeploymentStatus.DEPLOYING: return 'warning';
      case DeploymentStatus.FAILED: return 'danger';
      case DeploymentStatus.PENDING: return 'info';
      default: return 'secondary';
    }
  }

  getStatusIcon(status: DeploymentStatus): string {
    switch (status) {
      case DeploymentStatus.DEPLOYED: return 'check_circle';
      case DeploymentStatus.DEPLOYING: return 'sync';
      case DeploymentStatus.FAILED: return 'error';
      case DeploymentStatus.PENDING: return 'schedule';
      default: return 'help_outline';
    }
  }

  getEnvironmentDeploymentStatus(envStatus: any): string {
    return this.getStatusClass(envStatus.status);
  }

  // Create deployable method
  createDeployable(): void {
    if (this.createForm.valid) {
      const formValue = this.createForm.value;
      this.loading = true;

      // Build the create request from form data
      const createRequest: DeployableCreateRequest = {
        name: formValue.name,
        description: formValue.description || '',
        type: formValue.type,
        parentId: formValue.parentId || undefined,
        projectId: this.currentProjectId || '',
        repository: formValue.repository.url ? {
          url: formValue.repository.url,
          branch: formValue.repository.branch || 'main',
          provider: formValue.repository.provider || 'github',
          buildPath: formValue.repository.buildPath || ''
        } : undefined,
        buildConfig: {
          dockerfile: formValue.buildConfig?.dockerfile || 'Dockerfile',
          buildArgs: formValue.buildConfig?.buildArgs || {},
          buildContext: formValue.buildConfig?.buildContext || '.'
        },
        deploymentConfig: {
          resources: {
            cpu: formValue.deploymentConfig?.resources?.limits?.cpu || '500m',
            memory: formValue.deploymentConfig?.resources?.limits?.memory || '512Mi',
            replicas: formValue.deploymentConfig?.replicas || 1
          },
          strategy: {
            type: 'rolling' as any
          },
          networking: {
            ports: []
          },
          environment: {}
        },
        tags: formValue.tags || [],
        version: '1.0.0' // Default version for new deployables
      };

      this.deployableService.createDeployable(createRequest).pipe(
        takeUntil(this.destroy$)
      ).subscribe({
        next: (newDeployable) => {
          console.log('Deployable created successfully:', newDeployable);
          
          // Add to local deployables list
          this.deployables.push(newDeployable);
          this.applyFilters();
          
          // Add to recent activity
          this.recentActivity.unshift({
            id: `activity-${Date.now()}`,
            type: 'creation',
            icon: 'fas fa-plus',
            title: 'Deployable Created',
            description: `${newDeployable.name} was created successfully`,
            timestamp: new Date()
          });
          
          // Close modal and reset form
          this.closeCreateModal();
          this.loading = false;
          
          // Show success message (you could add a toast notification here)
          console.log(`Successfully created deployable: ${newDeployable.name}`);
        },
        error: (error) => {
          console.error('Error creating deployable:', error);
          this.loading = false;
          
          // You could add error handling UI here
          alert(`Failed to create deployable: ${error.message || 'Unknown error'}`);
        }
      });
    } else {
      // Mark all fields as touched to show validation errors
      this.markFormGroupTouched(this.createForm);
    }
  }

  // Helper method to mark all form fields as touched
  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
      
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  // View Mode Management
  setViewMode(mode: 'list' | 'grid' | 'hierarchy'): void {
    this.viewMode = mode;
  }

  // Utility method for date formatting with relative time
  formatDate(date: Date | string): string {
    if (!date) return 'N/A';
    
    const d = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffMs = now.getTime() - d.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffMins < 60) {
      return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    } else if (diffDays < 30) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else {
      return d.toLocaleDateString();
    }
  }

  // Utility method for checking if deployable can have children
  canHaveChildren(deployable: Deployable): boolean {
    return deployable.type === DeployableType.APPLICATION || 
           deployable.type === DeployableType.INFRASTRUCTURE;
  }

  private initializeForms(): void {
    this.filterForm = this.fb.group({
      search: [''],
      type: [''],
      status: [''],
      environment: [''],
      parentId: ['']
    });

    this.createForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      type: [DeployableType.APPLICATION, Validators.required],
      parentId: [''],
      repository: this.fb.group({
        url: ['', Validators.required],
        branch: ['main', Validators.required],
        provider: ['github', Validators.required],
        buildPath: ['']
      }),
      buildConfig: this.fb.group({
        dockerfile: ['Dockerfile'],
        buildArgs: this.fb.array([]),
        buildContext: ['.']
      }),
      deploymentConfig: this.fb.group({
        replicas: [1, [Validators.min(0)]],
        resources: this.fb.group({
          requests: this.fb.group({
            cpu: ['100m'],
            memory: ['128Mi']
          }),
          limits: this.fb.group({
            cpu: ['500m'],
            memory: ['512Mi']
          })
        })
      }),
      environmentOverrides: this.fb.array([]),
      tags: [[]]
    });
  }

  private loadInitialData(): void {
    this.loading = true;

    // Get deployables, environments, and workflows filtered by projectId
    combineLatest([
      this.deployableService.getDeployables({ projectId: this.currentProjectId || undefined }),
      this.environmentService.getEnvironments({ projectId: this.currentProjectId || undefined }),
      this.workflowService.getWorkflows(this.currentProjectId || undefined)
    ]).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: ([deployables, environments, workflows]) => {
        this.deployables = deployables.deployables || deployables;
        this.filteredDeployables = this.deployables;
        this.environments = environments.environments || environments;
        this.workflows = workflows.map((w: any) => ({
          workflowId: w.id,
          workflowName: w.name
        }));
        this.applyFilters();
        
        // Load recent activity data
        this.loadRecentActivity();
        
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading data:', error);
        this.loading = false;
      }
    });
  }

  private setupRouteParams(): void {
    this.route.params.pipe(
      takeUntil(this.destroy$)
    ).subscribe(params => {
      if (params['parentId']) {
        this.loadParentDeployable(params['parentId']);
        this.filterForm.patchValue({ parentId: params['parentId'] });
      }
      
      if (params['deployableId']) {
        this.loadDeployableDetails(params['deployableId']);
      }
    });
  }

  private setupFormSubscriptions(): void {
    this.filterForm.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(filters => {
      this.applyFilters();
    });
  }

  private setupRealtimeUpdates(): void {
    // Setup periodic refresh for real-time updates
    interval(30000).pipe( // Refresh every 30 seconds
      startWith(0),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      if (!this.loading) {
        this.refreshDeploymentStatus();
      }
    });
  }
  
  // Debug/info method to display current project
  getCurrentProjectInfo(): string {
    return this.currentProjectId 
      ? `Filtering by project: ${this.currentProjectId}` 
      : 'No project filter applied';
  }
  
  // Debug method for logging environment service data
  debugEnvironmentService(): void {
    console.log('Current Project ID:', this.currentProjectId);
    
    // Try direct API call with the specific project ID
    const specificProjectId = 'f0768aa2-aec0-4864-87bc-62e8f39921de';
    console.log(`Attempting to fetch environments for specific projectId: ${specificProjectId}`);
    
    // Test environment service API
    this.environmentService.getEnvironments({ projectId: specificProjectId }).subscribe({
      next: (response) => {
        console.log('Environment API Response:', response);
        console.log('Environments count:', response.environments ? response.environments.length : 0);
        console.log('Total environments reported by API:', response.total);
      },
      error: (error) => {
        console.error('Environment API Error:', error);
      }
    });
    
    // Test deployable service API
    console.log('Testing deployable service API...');
    this.deployableService.getDeployables({ projectId: specificProjectId }).subscribe({
      next: (response) => {
        console.log('Deployables API Response:', response);
        console.log('Deployables count:', response.deployables?.length || 0);
        console.log('Total deployables reported by API:', response.total);
        alert(`API Test: Found ${response.deployables?.length || 0} deployables via /api/v1/deployables endpoint`);
      },
      error: (error) => {
        console.error('Deployables API Error:', error);
        alert(`Deployables API Error: ${error.message || JSON.stringify(error)}`);
      }
    });
  }

  private refreshDeploymentStatus(): void {
    // Refresh deployment status without showing loading state
    this.deployableService.getDeployables({ projectId: this.currentProjectId || undefined }).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (result) => {
        const deployables = result.deployables || result;
        // Update deployment status while preserving UI state
        deployables.forEach((updated: Deployable) => {
          const existing = this.deployables.find(d => d.id === updated.id);
          if (existing) {
            existing.status = updated.status;
            existing.environments = updated.environments;
          }
        });
        this.applyFilters();
        
        // Refresh recent activity
        this.loadRecentActivity();
      },
      error: (error) => {
        console.error('Error refreshing status:', error);
      }
    });
  }

  private loadParentDeployable(parentId: string): void {
    this.deployableService.getDeployable(parentId, this.currentProjectId || undefined).pipe(
      takeUntil(this.destroy$)
    ).subscribe(parent => {
      this.parentDeployable = parent;
    });
  }

  private loadDeployableDetails(deployableId: string): void {
    this.deployableService.getDeployable(deployableId, this.currentProjectId || undefined).pipe(
      takeUntil(this.destroy$)
    ).subscribe(deployable => {
      this.selectedDeployable = deployable;
    });
  }

  // Missing Methods

  selectDeployable(deployable: Deployable): void {
    this.selectedDeployable = deployable;
    this.showDetailsPanel = true;
  }

  deployToEnvironment(deployable: Deployable, environmentId: string): void {
    if (!deployable || !environmentId) {
      console.error('Invalid parameters for deployment');
      return;
    }

    this.loading = true;
    this.deployableService.deployToEnvironment(deployable.id, environmentId, this.currentProjectId || undefined).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: () => {
        console.log(`Deployed ${deployable.name} to environment ${environmentId}`);
        this.refreshDeploymentStatus();
        this.loading = false;
      },
      error: (error) => {
        console.error('Deployment failed:', error);
        this.loading = false;
      }
    });
  }

  rollbackDeployable(deployable: Deployable, environmentId: string): void {
    if (!deployable || !environmentId) {
      console.error('Invalid deployable or environment for rollback');
      return;
    }

    this.loading = true;
    this.deployableService.rollbackDeployable(deployable.id, environmentId, this.currentProjectId || undefined).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: () => {
        console.log(`Rolled back ${deployable.name} in environment ${environmentId}`);
        this.refreshDeploymentStatus();
        this.loading = false;
      },
      error: (error) => {
        console.error('Rollback failed:', error);
        this.loading = false;
      }
    });
  }

  navigateToDeployables(deployableId: string): void {
    this.router.navigate(['/deployables', deployableId]);
  }

  navigateToWorkflowExecution(): void {
    this.router.navigate(['/workflows', 'execution']);
  }

  // Helper method for environment deployment
  openEnvironmentDeployDialog(environmentId: string): void {
    // Open dialog or navigate to deployment configuration
    console.log('Deploy to environment:', environmentId);
    // In a real implementation, this would open a dialog to select deployables
    // or navigate to a deployment configuration page
  }

  // Helper method for rollback with environment selection
  rollbackDeployableFromCard(deployable: Deployable): void {
    // In a real implementation, this would open a dialog to select environment
    // For now, rollback from the first deployed environment
    const deployedEnv = deployable.environments.find(env => 
      env.deploymentStatus === DeploymentStatus.DEPLOYED
    );
    
    if (deployedEnv) {
      this.rollbackDeployable(deployable, deployedEnv.name);
    } else {
      console.warn('No deployed environment found for rollback');
    }
  }

  // Environment-specific plugin selection logic
  private selectPluginForEnvironment(environment: any): string {
    if (!environment?.provider?.type) {
      console.warn('Environment missing provider type, using default plugin');
      return 'default-deploy';
    }

    const providerType = environment.provider.type.toLowerCase();
    
    // Map provider types to appropriate plugins
    switch (providerType) {
      case 'openshift':
        return 'helm-openshift-deploy';
      case 'kubernetes':
      case 'gke':
      case 'aks':
      case 'eks':
        return 'helm-kubernetes-deploy';
      case 'vm':
      case 'bare-metal':
        return 'ansible-deploy';
      case 'serverless':
        return 'serverless-deploy';
      case 'container':
        return 'docker-deploy';
      default:
        console.warn(`Unknown provider type: ${providerType}, using default plugin`);
        return 'default-deploy';
    }
  }

  private getPluginConfiguration(environment: any, deployable: Deployable): any {
    const plugin = this.selectPluginForEnvironment(environment);
    
    switch (plugin) {
      case 'helm-openshift-deploy':
        return this.getHelmOpenShiftConfig(environment, deployable);
      case 'helm-kubernetes-deploy':
        return this.getHelmKubernetesConfig(environment, deployable);
      case 'ansible-deploy':
        return this.getAnsibleConfig(environment, deployable);
      case 'serverless-deploy':
        return this.getServerlessConfig(environment, deployable);
      case 'docker-deploy':
        return this.getDockerConfig(environment, deployable);
      default:
        return this.getDefaultConfig(environment, deployable);
    }
  }

  private getHelmOpenShiftConfig(environment: any, deployable: Deployable): any {
    const config = environment.provider?.config || {};
    return {
      plugin: 'helm-openshift-deploy',
      operation: 'deploy:helm',
      parameters: {
        openshift_api_url: config.endpoint || config.cluster_url,
        openshift_project: config.namespace || environment.name,
        username: config.credentials?.username,
        password: config.credentials?.password,
        bitbucket_repo_url: deployable.repository?.url,
        chart_path: deployable.repository?.buildPath || 'charts/' + deployable.name,
        values_path: `values-${environment.name}.yaml`,
        release_name: `${deployable.name}-${environment.name}`,
        helm_timeout: '300s',
        dry_run: false,
        extra_values: {
          'image.tag': deployable.version,
          'environment': environment.name,
          'replicas': deployable.deploymentConfig?.resources?.replicas || 1
        }
      }
    };
  }

  private getHelmKubernetesConfig(environment: any, deployable: Deployable): any {
    const config = environment.provider?.config || {};
    return {
      plugin: 'helm-kubernetes-deploy',
      operation: 'deploy:helm',
      parameters: {
        kubeconfig: config.kubeconfig,
        namespace: config.namespace || environment.name,
        cluster: config.cluster,
        repo_url: deployable.repository?.url,
        chart_path: deployable.repository?.buildPath || 'charts/' + deployable.name,
        values_path: `values-${environment.name}.yaml`,
        release_name: `${deployable.name}-${environment.name}`,
        helm_timeout: '300s'
      }
    };
  }

  private getAnsibleConfig(environment: any, deployable: Deployable): any {
    const config = environment.provider?.config || {};
    return {
      plugin: 'ansible-deploy',
      operation: 'deploy:playbook',
      parameters: {
        inventory: config.inventory,
        playbook: 'deploy.yml',
        extra_vars: {
          app_name: deployable.name,
          app_version: deployable.version,
          environment: environment.name,
          repository_url: deployable.repository?.url
        }
      }
    };
  }

  private getServerlessConfig(environment: any, deployable: Deployable): any {
    const config = environment.provider?.config || {};
    return {
      plugin: 'serverless-deploy',
      operation: 'deploy:function',
      parameters: {
        provider: config.provider || 'aws',
        region: config.region,
        function_name: `${deployable.name}-${environment.name}`,
        runtime: (deployable.buildConfig as any)?.runtime || 'nodejs18.x',
        handler: (deployable.buildConfig as any)?.handler || 'index.handler',
        source_code: deployable.repository?.url
      }
    };
  }

  private getDockerConfig(environment: any, deployable: Deployable): any {
    const config = environment.provider?.config || {};
    return {
      plugin: 'docker-deploy',
      operation: 'deploy:container',
      parameters: {
        docker_host: config.endpoint,
        image: `${deployable.name}:${deployable.version}`,
        container_name: `${deployable.name}-${environment.name}`,
        ports: deployable.deploymentConfig?.networking?.ports || [],
        environment_vars: deployable.deploymentConfig?.environment || {}
      }
    };
  }

  private getDefaultConfig(environment: any, deployable: Deployable): any {
    return {
      plugin: 'default-deploy',
      operation: 'deploy:generic',
      parameters: {
        deployable_id: deployable.id,
        environment_id: environment.id,
        version: deployable.version
      }
    };
  }

  // Deployment Wizard Methods

  /**
   * Initialize deployment wizard
   */
  startDeploymentWizard(): void {
    this.resetDeploymentWizard();
    this.loadPluginData();
  }

  /**
   * Reset deployment wizard state
   */
  resetDeploymentWizard(): void {
    this.deploymentWizard = {
      step: 1,
      selectedDeployables: [],
      selectedGroups: [],
      selectedEnvironment: null,
      selectedPlugin: null,
      pluginConfiguration: {},
      isDeploying: false
    };
  }

  /**
   * Load plugin and environment mapping data for wizard with better separation
   * between applications and environments
   */
  protected loadPluginData(): void {
    // Load plugins for deployment
    this.wizardLoading.plugins = true;
    this.deploymentPluginService.getAvailablePlugins().pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: plugins => {
        this.availablePlugins = plugins;
        this.wizardLoading.plugins = false;
        this.wizardErrors.plugins = null;
        console.log('Successfully loaded', plugins.length, 'deployment plugins for the wizard');
      },
      error: error => {
        console.error('Failed to load deployment plugins:', error);
        this.wizardLoading.plugins = false;
        this.wizardErrors.plugins = 'Failed to load deployment plugins. Please try again.';
      }
    });

    // Load environment-plugin mappings
    this.deploymentPluginService.getEnvironmentPluginMappings().pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: mappings => {
        this.environmentPluginMappings = mappings;
        console.log('Successfully loaded', mappings.length, 'environment-plugin mappings');
      },
      error: error => {
        console.error('Failed to load environment plugin mappings:', error);
      }
    });

    // Load applications specifically for the wizard
    // We filter for application type deployables to properly differentiate from environments
    this.wizardLoading.applications = true;
    this.deployableService.getDeployables({ 
      projectId: this.currentProjectId || undefined,
      type: DeployableType.APPLICATION // Base filter - we'll further filter in the frontend
    }).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: response => {
        const apps = response.deployables || response;
        // Update the filtered deployables for the wizard only
        this.wizardApplications = apps;
        this.wizardLoading.applications = false;
        this.wizardErrors.applications = null;
        console.log('Loaded applications for wizard:', this.wizardApplications.length);
      },
      error: error => {
        console.error('Failed to load applications for deployment wizard:', error);
        this.wizardLoading.applications = false;
        this.wizardErrors.applications = 'Failed to load applications. Please try again.';
      }
    });

    // Load environments specifically for the wizard
    this.wizardLoading.environments = true;
    this.environmentService.getEnvironments({ projectId: this.currentProjectId || undefined }).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: response => {
        this.environments = response.environments || response;
        this.wizardLoading.environments = false;
        this.wizardErrors.environments = null;
        console.log('Loaded environments for wizard:', this.environments.length);
      },
      error: error => {
        console.error('Failed to load environments for deployment wizard:', error);
        this.wizardLoading.environments = false;
        this.wizardErrors.environments = 'Failed to load environments. Please try again.';
      }
    });
  }

  /**
   * Navigate to next step in wizard
   */
  nextWizardStep(): void {
    if (this.deploymentWizard.step < 3) {
      this.deploymentWizard.step = (this.deploymentWizard.step + 1) as 1 | 2 | 3;
      
      if (this.deploymentWizard.step === 3) {
        this.updateCompatiblePlugins();
        this.setDefaultPlugin();
      }
    }
  }

  /**
   * Navigate to previous step in wizard
   */
  previousWizardStep(): void {
    if (this.deploymentWizard.step > 1) {
      this.deploymentWizard.step = (this.deploymentWizard.step - 1) as 1 | 2 | 3;
    }
  }

  /**
   * Go to specific step in wizard
   */
  goToWizardStep(step: 1 | 2 | 3): void {
    this.deploymentWizard.step = step;
  }

  /**
   * Toggle deployable selection in wizard
   */
  toggleDeployableSelection(deployable: Deployable): void {
    const index = this.deploymentWizard.selectedDeployables.findIndex(d => d.id === deployable.id);
    if (index > -1) {
      this.deploymentWizard.selectedDeployables.splice(index, 1);
    } else {
      this.deploymentWizard.selectedDeployables.push(deployable);
    }
  }

  /**
   * Check if deployable is selected
   */
  isDeployableSelected(deployable: Deployable): boolean {
    return this.deploymentWizard.selectedDeployables.some(d => d.id === deployable.id);
  }

  /**
   * Select environment in wizard
   */
  selectEnvironment(environment: any): void {
    this.deploymentWizard.selectedEnvironment = environment;
  }

  /**
   * Update compatible plugins based on selected deployables
   */
  private updateCompatiblePlugins(): void {
    if (this.deploymentWizard.selectedDeployables.length === 0) {
      this.compatiblePlugins = [];
      return;
    }

    const selectedTypes = [...new Set(this.deploymentWizard.selectedDeployables.map(d => d.type))];
    this.deploymentPluginService.getCompatiblePlugins(selectedTypes).pipe(
      takeUntil(this.destroy$)
    ).subscribe(plugins => {
      this.compatiblePlugins = plugins;
    });
  }

  /**
   * Set default plugin based on environment mapping
   */
  private setDefaultPlugin(): void {
    if (!this.deploymentWizard.selectedEnvironment) return;

    this.deploymentPluginService.getDefaultPluginForEnvironment(
      this.deploymentWizard.selectedEnvironment.id
    ).pipe(
      takeUntil(this.destroy$)
    ).subscribe(plugin => {
      if (plugin && this.compatiblePlugins.some(p => p.id === plugin.id)) {
        this.selectPlugin(plugin);
      }
    });
  }

  /**
   * Select deployment plugin
   */
  selectPlugin(plugin: DeploymentPlugin): void {
    this.deploymentWizard.selectedPlugin = plugin;
    this.initializePluginConfiguration(plugin);
  }

  /**
   * Initialize plugin configuration with defaults
   */
  private initializePluginConfiguration(plugin: DeploymentPlugin): void {
    const config: { [key: string]: any } = {};
    
    // Handle both old and new plugin configuration formats
    const configFields = this.getPluginConfigurationFields(plugin);
    configFields.forEach((schema: PluginConfigSchema) => {
      if (schema.defaultValue !== undefined) {
        config[schema.key] = schema.defaultValue;
      }
    });

    // Apply admin configuration overrides if they exist
    const mapping = this.environmentPluginMappings.find(
      m => m.environmentId === this.deploymentWizard.selectedEnvironment?.id
    );
    if (mapping && mapping.adminConfiguration) {
      Object.assign(config, mapping.adminConfiguration);
    }

    this.deploymentWizard.pluginConfiguration = config;
  }

  /**
   * Update plugin configuration value
   */
  updatePluginConfiguration(key: string, $event: any): void {
    let value = ($event.target as HTMLInputElement).value
    this.deploymentWizard.pluginConfiguration[key] = value;
  }

  /**
   * Check if wizard can proceed to next step
   */
  canProceedToNextStep(): boolean {
    switch (this.deploymentWizard.step) {
      case 1:
        return this.deploymentWizard.selectedDeployables.length > 0;
      case 2:
        return this.deploymentWizard.selectedEnvironment !== null;
      case 3:
        return this.deploymentWizard.selectedPlugin !== null;
      default:
        return false;
    }
  }

  /**
   * Execute deployment
   */
  executeWizardDeployment(): void {
    if (!this.canExecuteDeployment()) return;

    const request: DeploymentRequest = {
      deployables: this.deploymentWizard.selectedDeployables.map(d => d.id),
      groups: this.deploymentWizard.selectedGroups.map(g => g.id),
      environmentId: this.deploymentWizard.selectedEnvironment!.id,
      pluginId: this.deploymentWizard.selectedPlugin!.id,
      configuration: this.deploymentWizard.pluginConfiguration,
      runHealthChecks: true,
      notifyOnComplete: true
    };

    this.deploymentWizard.isDeploying = true;

    this.deploymentPluginService.executeDeployment(request).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (result) => {
        console.log('Deployment started successfully:', result);
        this.deploymentWizard.isDeploying = false;
        this.resetDeploymentWizard();
        this.refreshDeploymentStatus();
        // Could show success notification here
      },
      error: (error) => {
        console.error('Deployment failed:', error);
        this.deploymentWizard.isDeploying = false;
        // Could show error notification here
      }
    });
  }

  /**
   * Check if deployment can be executed
   */
  canExecuteDeployment(): boolean {
    return this.deploymentWizard.selectedDeployables.length > 0 &&
           this.deploymentWizard.selectedEnvironment !== null &&
           this.deploymentWizard.selectedPlugin !== null &&
           !this.deploymentWizard.isDeploying;
  }

  /**
   * Get filtered deployables by type for step 1
   */
  getFilteredDeployablesByType(type?: DeployableType): Deployable[] {
    if (!type) return this.filteredDeployables;
    return this.filteredDeployables.filter(d => d.type === type);
  }

  /**
   * Get unique deployable types for filtering, focused on application types for the wizard
   */
  getUniqueDeployableTypes(): DeployableType[] {
    // Filter to only include application-related types for the wizard
    const applicationTypes = [
      DeployableType.APPLICATION,
      DeployableType.WEB_APPLICATION,
      DeployableType.MICROSERVICE,
      DeployableType.MOBILE_APP,
      DeployableType.DESKTOP_APP,
      DeployableType.SERVICE,
      DeployableType.API_GATEWAY
    ];
    
    // Get the unique types that exist in the deployables and are application types
    const types = [...new Set(this.deployables
      .filter(d => applicationTypes.includes(d.type))
      .map(d => d.type))];
      
    return types.sort();
  }

  /**
   * Get step title for wizard
   */
  getWizardStepTitle(): string {
    switch (this.deploymentWizard.step) {
      case 1:
        return 'Select Applications';
      case 2:
        return 'Choose Environment';
      case 3:
        return 'Configure Deployment';
      default:
        return 'Deploy';
    }
  }

  /**
   * Get step description for wizard
   */
  getWizardStepDescription(): string {
    switch (this.deploymentWizard.step) {
      case 1:
        return 'Choose the applications and services you want to deploy';
      case 2:
        return 'Select the target environment for your deployment';
      case 3:
        return 'Configure deployment settings and review your selections';
      default:
        return '';
    }
  }

  /**
   * Get deployables that are appropriate for the deployment wizard (Step 1)
   * This helps differentiate between applications and environments
   */
  getWizardApplications(): Deployable[] {
    // If we've loaded specific application types for the wizard, use those
    if (this.wizardApplications.length > 0) {
      return this.wizardApplications;
    }
    
    // Otherwise, filter from all deployables
    return this.filteredDeployables.filter(d => this.isApplicationType(d.type));
  }

  /**
   * Check if a deployable type is an application that can be deployed
   * This helps differentiate applications from infrastructure or other types
   */
  isApplicationType(type: DeployableType): boolean {
    return [
      DeployableType.APPLICATION,
      DeployableType.WEB_APPLICATION,
      DeployableType.MICROSERVICE,
      DeployableType.MOBILE_APP,
      DeployableType.DESKTOP_APP,
      DeployableType.SERVICE
    ].includes(type);
  }

  /**
   * Get a user-friendly label for application types
   * Used to make the UI more descriptive for different application types
   */
  getApplicationTypeLabel(type: DeployableType): string {
    switch (type) {
      case DeployableType.APPLICATION:
        return 'Standard Application';
      case DeployableType.WEB_APPLICATION:
        return 'Web Application';
      case DeployableType.MICROSERVICE:
        return 'Microservice';
      case DeployableType.MOBILE_APP:
        return 'Mobile Application';
      case DeployableType.DESKTOP_APP:
        return 'Desktop Application';
      case DeployableType.SERVICE:
        return 'Service';
      case DeployableType.API_GATEWAY:
        return 'API Gateway';
      default:
        return type.replace('_', ' ').toLowerCase();
    }
  }

  /**
   * Filter applications in the wizard based on search query
   */
  filterWizardApplications(): void {
    if (!this.wizardSearchQuery?.trim()) {
      this.filteredWizardApplications = [];
      return;
    }
    
    const query = this.wizardSearchQuery.toLowerCase().trim();
    this.filteredWizardApplications = this.wizardApplications.filter(app => 
      app.name.toLowerCase().includes(query) || 
      app.description?.toLowerCase().includes(query) ||
      this.getApplicationTypeLabel(app.type).toLowerCase().includes(query)
    );
  }
  
  /**
   * Get applications for the deployment wizard with search applied
   */
  getFilteredWizardApplications(): Deployable[] {
    if (this.wizardSearchQuery?.trim()) {
      return this.filteredWizardApplications;
    }
    return this.getWizardApplications();
  }

  private getPluginConfigurationFields(plugin: DeploymentPlugin): PluginConfigSchema[] {
    // Check if plugin has the new configuration schema structure
    if (plugin?.configuration?.schema?.properties) {
      return this.convertPluginSchemaToConfigFields(plugin.configuration.schema);
    }
    // Fallback to legacy configurationSchema if available
    return plugin?.configurationSchema || [];
  }

  private convertPluginSchemaToConfigFields(schema: any): PluginConfigSchema[] {
    const fields: PluginConfigSchema[] = [];
    const properties = schema.properties || {};
    const required = schema.required || [];

    for (const [key, property] of Object.entries(properties)) {
      const prop = property as any;
      const field: PluginConfigSchema = {
        key,
        label: prop.title || this.formatLabel(key),
        type: this.mapPluginTypeToConfigType(prop.type),
        required: required.includes(key),
        description: prop.description,
        defaultValue: prop.default
      };

      // Handle validation patterns
      if (prop.pattern) {
        field.validation = {
          pattern: prop.pattern
        };
      }

      // Handle numeric constraints
      if (prop.minimum !== undefined || prop.maximum !== undefined) {
        if (!field.validation) field.validation = {};
        field.validation.min = prop.minimum;
        field.validation.max = prop.maximum;
      }

      // Handle enum/select options
      if (prop.enum) {
        field.type = 'select';
        field.options = prop.enum.map((value: string) => ({
          label: this.formatLabel(value),
          value
        }));
      }

      // Handle examples as placeholder
      if (prop.examples && prop.examples.length > 0) {
        field.description = field.description || '';
        if (field.description) field.description += '\n';
        field.description += `Example: ${prop.examples[0]}`;
      }

      fields.push(field);
    }

    return fields;
  }

  private mapPluginTypeToConfigType(pluginType: string): PluginConfigSchema['type'] {
    switch (pluginType) {
      case 'password':
        return 'password';
      case 'integer':
      case 'number':
        return 'number';
      case 'boolean':
        return 'boolean';
      case 'string':
      default:
        return 'string';
    }
  }

  private formatLabel(key: string): string {
    return key
      .replace(/[_-]/g, ' ')
      .replace(/\b\w/g, char => char.toUpperCase());
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}