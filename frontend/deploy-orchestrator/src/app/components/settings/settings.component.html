<div class="px-4 py-5 sm:px-6">
  <div class="flex items-center justify-between">
    <div>
      <h3 class="text-lg leading-6 font-medium text-gray-900">System Settings</h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500">Configure system-wide settings and preferences</p>
    </div>
    <div class="flex space-x-3">
      <input type="file" id="importFile" (change)="importSettings($event)" accept=".json" class="hidden">
      <label for="importFile" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer">
        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
        </svg>
        Import
      </label>
      <button (click)="exportSettings()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
        Export
      </button>
      <button (click)="openCreateModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        New Setting
      </button>
    </div>
  </div>
</div>

<div *ngIf="error" class="bg-red-50 border-l-4 border-red-400 p-4 mx-4 my-2">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
      </svg>
    </div>
    <div class="ml-3">
      <p class="text-sm text-red-700">{{ error }}</p>
    </div>
  </div>
</div>

<div *ngIf="success" class="bg-green-50 border-l-4 border-green-400 p-4 mx-4 my-2">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
      </svg>
    </div>
    <div class="ml-3">
      <p class="text-sm text-green-700">{{ success }}</p>
    </div>
  </div>
</div>

<div class="border-t border-gray-200">
  <div class="flex">
    <!-- Category Sidebar -->
    <div class="w-64 bg-gray-50 border-r border-gray-200">
      <nav class="mt-5 px-2">
        <div class="space-y-1">
          <button *ngFor="let category of categories"
                  (click)="setActiveCategory(category.key)"
                  [class]="activeCategory === category.key ? 'bg-gray-200 text-gray-900' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'"
                  class="group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full text-left">
            <svg [class]="activeCategory === category.key ? 'text-gray-500' : 'text-gray-400 group-hover:text-gray-500'"
                 class="mr-3 flex-shrink-0 h-6 w-6"
                 xmlns="http://www.w3.org/2000/svg"
                 fill="none"
                 viewBox="0 0 24 24"
                 stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="getCategoryIcon(category.icon)" />
            </svg>
            {{ category.name }}
          </button>
        </div>
      </nav>
    </div>

    <!-- Settings Content -->
    <div class="flex-1">
      <div *ngIf="loading" class="flex justify-center py-6">
        <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>

      <div *ngIf="!loading && (!settingsByCategory[activeCategory] || settingsByCategory[activeCategory].length === 0)" class="text-center py-10">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No settings</h3>
        <p class="mt-1 text-sm text-gray-500">No settings found in this category.</p>
        <div class="mt-6">
          <button (click)="openCreateModal()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            New Setting
          </button>
        </div>
      </div>

      <div *ngIf="!loading && settingsByCategory[activeCategory] && settingsByCategory[activeCategory].length > 0" class="divide-y divide-gray-200">
        <div *ngFor="let setting of settingsByCategory[activeCategory]" class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div class="flex-1 min-w-0">
              <div class="flex items-center">
                <h4 class="text-sm font-medium text-gray-900">{{ setting.key }}</h4>
                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  {{ setting.type }}
                </span>
                <span *ngIf="setting.isSecret" class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  Secret
                </span>
              </div>
              <p *ngIf="setting.description" class="mt-1 text-sm text-gray-500">{{ setting.description }}</p>
              <div class="mt-2">
                <input
                  [type]="getInputType(setting)"
                  [value]="formatValue(setting)"
                  #settingInput
                  (blur)="updateSetting(setting, settingInput.value)"
                  class="block w-full max-w-lg px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              </div>
            </div>
            <div class="ml-4 flex-shrink-0">
              <button (click)="deleteSetting(setting)" class="text-red-600 hover:text-red-900 text-sm">
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Danger Zone -->
      <div *ngIf="activeCategory === 'general'" class="border-t border-gray-200 mt-8">
        <div class="px-6 py-4 bg-red-50">
          <h3 class="text-lg font-medium text-red-900">Danger Zone</h3>
          <p class="mt-1 text-sm text-red-700">These actions are irreversible. Please be careful.</p>
          <div class="mt-4">
            <button (click)="resetToDefaults()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
              Reset All Settings to Defaults
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Create Setting Modal -->
<div *ngIf="showCreateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Create New Setting</h3>
        <button (click)="closeCreateModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <form [formGroup]="settingForm" (ngSubmit)="createSetting()">
        <div class="grid grid-cols-1 gap-6">
          <div>
            <label for="key" class="block text-sm font-medium text-gray-700">Setting Key</label>
            <input type="text" id="key" formControlName="key" placeholder="e.g., app.name" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            <div *ngIf="settingForm.get('key')?.invalid && (settingForm.get('key')?.dirty || settingForm.get('key')?.touched)" class="text-red-500 text-xs mt-1">
              <div *ngIf="settingForm.get('key')?.errors?.['required']">Setting key is required.</div>
            </div>
          </div>

          <div>
            <label for="value" class="block text-sm font-medium text-gray-700">Value</label>
            <textarea id="value" formControlName="value" rows="3" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
            <div *ngIf="settingForm.get('value')?.invalid && (settingForm.get('value')?.dirty || settingForm.get('value')?.touched)" class="text-red-500 text-xs mt-1">
              <div *ngIf="settingForm.get('value')?.errors?.['required']">Value is required.</div>
            </div>
          </div>

          <div>
            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
            <input type="text" id="description" formControlName="description" placeholder="Brief description of this setting" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label for="type" class="block text-sm font-medium text-gray-700">Type</label>
              <select id="type" formControlName="type" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                <option value="string">String</option>
                <option value="number">Number</option>
                <option value="boolean">Boolean</option>
                <option value="json">JSON</option>
              </select>
            </div>
            <div>
              <label for="category" class="block text-sm font-medium text-gray-700">Category</label>
              <select id="category" formControlName="category" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                <option *ngFor="let category of categories" [value]="category.key">{{ category.name }}</option>
              </select>
            </div>
          </div>

          <div class="flex items-center">
            <input id="isSecret" type="checkbox" formControlName="isSecret" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="isSecret" class="ml-2 block text-sm text-gray-900">
              This is a secret value (will be masked in the UI)
            </label>
          </div>
        </div>

        <div class="mt-6 flex justify-end space-x-3">
          <button type="button" (click)="closeCreateModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
            Cancel
          </button>
          <button type="submit" [disabled]="settingForm.invalid || saving" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
            <span *ngIf="saving" class="mr-2">
              <svg class="animate-spin h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            Create Setting
          </button>
        </div>
      </form>
    </div>
  </div>
</div>