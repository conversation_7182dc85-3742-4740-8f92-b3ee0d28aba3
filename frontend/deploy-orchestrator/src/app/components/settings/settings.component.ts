import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { SettingsService } from '../../services/settings.service';
import { AuthService } from '../../services/auth.service';

interface SystemSetting {
  key: string;
  value: string;
  description: string;
  type: 'string' | 'number' | 'boolean' | 'json';
  category: string;
  isSecret?: boolean;
}

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss']
})
export class SettingsComponent implements OnInit {
  settings: SystemSetting[] = [];
  settingsByCategory: { [key: string]: SystemSetting[] } = {};
  settingForm!: FormGroup;
  loading = false;
  saving = false;
  error = '';
  success = '';
  activeCategory = 'general';
  showCreateModal = false;

  categories = [
    { key: 'general', name: 'General', icon: 'cog' },
    { key: 'security', name: 'Security', icon: 'shield' },
    { key: 'notifications', name: 'Notifications', icon: 'bell' },
    { key: 'deployment', name: 'Deployment', icon: 'rocket' },
    { key: 'audit', name: 'Audit & Logging', icon: 'document' },
    { key: 'integration', name: 'Integrations', icon: 'link' }
  ];

  constructor(
    private settingsService: SettingsService,
    private authService: AuthService,
    private formBuilder: FormBuilder
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.loadSettings();
  }

  initForm(): void {
    this.settingForm = this.formBuilder.group({
      key: ['', Validators.required],
      value: ['', Validators.required],
      description: [''],
      type: ['string', Validators.required],
      category: ['general', Validators.required],
      isSecret: [false]
    });
  }

  loadSettings(): void {
    this.loading = true;
    this.settingsService.getSettings().subscribe({
      next: (response: any) => {
        // Handle the response structure from backend
        if (response && response.settings) {
          // Convert the settings object to an array of SystemSetting objects
          this.settings = this.convertSettingsObjectToArray(response.settings);
        } else {
          this.settings = [];
        }
        this.groupSettingsByCategory();
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load settings. Please try again.';
        console.error('Error loading settings', error);
        this.loading = false;
      }
    });
  }

  convertSettingsObjectToArray(settingsObj: any): SystemSetting[] {
    const settingsArray: SystemSetting[] = [];

    // Convert the flat settings object to SystemSetting array
    for (const [key, value] of Object.entries(settingsObj)) {
      const setting: SystemSetting = {
        key: key,
        value: String(value),
        description: this.getSettingDescription(key),
        type: this.getSettingType(value),
        category: this.getSettingCategory(key)
      };
      settingsArray.push(setting);
    }

    return settingsArray;
  }

  getSettingDescription(key: string): string {
    const descriptions: { [key: string]: string } = {
      'systemName': 'Name of the system',
      'version': 'Current system version',
      'environment': 'Current environment (development, staging, production)',
      'maintenance': 'Maintenance mode status',
      'features.audit': 'Enable audit logging',
      'features.monitoring': 'Enable system monitoring',
      'features.notifications': 'Enable notifications',
      'features.multiTenant': 'Enable multi-tenant support'
    };
    return descriptions[key] || 'System setting';
  }

  getSettingType(value: any): 'string' | 'number' | 'boolean' | 'json' {
    if (typeof value === 'boolean') return 'boolean';
    if (typeof value === 'number') return 'number';
    if (typeof value === 'object') return 'json';
    return 'string';
  }

  getSettingCategory(key: string): string {
    if (key.startsWith('features.')) return 'general';
    if (key.includes('security') || key.includes('auth')) return 'security';
    if (key.includes('notification')) return 'notifications';
    if (key.includes('deployment') || key.includes('deploy')) return 'deployment';
    if (key.includes('audit') || key.includes('log')) return 'audit';
    if (key.includes('integration')) return 'integration';
    return 'general';
  }

  groupSettingsByCategory(): void {
    this.settingsByCategory = {};
    this.settings.forEach(setting => {
      if (!this.settingsByCategory[setting.category]) {
        this.settingsByCategory[setting.category] = [];
      }
      this.settingsByCategory[setting.category].push(setting);
    });
  }

  openCreateModal(): void {
    this.showCreateModal = true;
    this.settingForm.reset();
    this.settingForm.patchValue({
      type: 'string',
      category: this.activeCategory,
      isSecret: false
    });
    this.error = '';
    this.success = '';
  }

  closeCreateModal(): void {
    this.showCreateModal = false;
    this.settingForm.reset();
  }

  createSetting(): void {
    if (this.settingForm.invalid) {
      return;
    }

    this.saving = true;
    const settingData = this.settingForm.value;

    this.settingsService.createSetting(settingData).subscribe({
      next: (setting) => {
        this.success = 'Setting created successfully.';
        this.error = '';
        this.saving = false;
        this.closeCreateModal();
        this.loadSettings();
      },
      error: (error) => {
        this.error = 'Failed to create setting. Please try again.';
        console.error('Error creating setting', error);
        this.saving = false;
      }
    });
  }

  updateSetting(setting: SystemSetting, newValue: string): void {
    this.settingsService.updateSetting(setting.key, newValue).subscribe({
      next: () => {
        this.success = 'Setting updated successfully.';
        this.error = '';
        setting.value = newValue;
      },
      error: (error) => {
        this.error = 'Failed to update setting. Please try again.';
        console.error('Error updating setting', error);
      }
    });
  }

  deleteSetting(setting: SystemSetting): void {
    if (confirm(`Are you sure you want to delete the setting "${setting.key}"?`)) {
      this.settingsService.deleteSetting(setting.key).subscribe({
        next: () => {
          this.success = 'Setting deleted successfully.';
          this.error = '';
          this.loadSettings();
        },
        error: (error) => {
          this.error = 'Failed to delete setting. Please try again.';
          console.error('Error deleting setting', error);
        }
      });
    }
  }

  resetToDefaults(): void {
    if (confirm('Are you sure you want to reset all settings to their default values? This action cannot be undone.')) {
      this.settingsService.resetToDefaults().subscribe({
        next: () => {
          this.success = 'Settings reset to defaults successfully.';
          this.error = '';
          this.loadSettings();
        },
        error: (error) => {
          this.error = 'Failed to reset settings. Please try again.';
          console.error('Error resetting settings', error);
        }
      });
    }
  }

  exportSettings(): void {
    this.settingsService.exportSettings().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `settings-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.error = 'Failed to export settings. Please try again.';
        console.error('Error exporting settings', error);
      }
    });
  }

  importSettings(event: any): void {
    const file = event.target.files[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('file', file);

    this.settingsService.importSettings(formData).subscribe({
      next: () => {
        this.success = 'Settings imported successfully.';
        this.error = '';
        this.loadSettings();
        event.target.value = ''; // Reset file input
      },
      error: (error) => {
        this.error = 'Failed to import settings. Please check the file format.';
        console.error('Error importing settings', error);
        event.target.value = ''; // Reset file input
      }
    });
  }

  setActiveCategory(category: string): void {
    this.activeCategory = category;
  }

  getCategoryIcon(iconName: string): string {
    const icons: { [key: string]: string } = {
      'cog': 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z',
      'shield': 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
      'bell': 'M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9',
      'rocket': 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z',
      'document': 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
      'link': 'M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1'
    };
    return icons[iconName] || icons['cog'];
  }

  formatValue(setting: SystemSetting): string {
    if (setting.isSecret) {
      return '••••••••';
    }

    if (setting.type === 'json') {
      try {
        return JSON.stringify(JSON.parse(setting.value), null, 2);
      } catch {
        return setting.value;
      }
    }

    return setting.value;
  }

  getInputType(setting: SystemSetting): string {
    if (setting.isSecret) return 'password';
    if (setting.type === 'number') return 'number';
    return 'text';
  }
}
