<div class="min-h-screen bg-gray-50" *ngIf="template">
  <!-- Header -->
  <div class="bg-white shadow-sm border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <button
            (click)="goBack()"
            class="mr-4 inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Templates
          </button>
          <div>
            <h1 class="text-3xl font-bold text-gray-900">{{ template.name }}</h1>
            <p class="mt-2 text-gray-600">by {{ template.authorName || 'Anonymous' }}</p>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <button
            (click)="toggleFavorite()"
            class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            [ngClass]="{ 'text-red-600 border-red-300': isFavorite }"
          >
            <i class="fas" [ngClass]="isFavorite ? 'fa-heart' : 'fa-heart-o'" class="mr-2"></i>
            {{ isFavorite ? 'Remove from Favorites' : 'Add to Favorites' }}
          </button>
          <button
            (click)="downloadTemplate()"
            class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <i class="fas fa-download mr-2"></i>
            Download
          </button>
          <button
            (click)="showCreateWorkflowModal = true"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <i class="fas fa-plus mr-2"></i>
            Use Template
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Content -->
      <div class="lg:col-span-2">
        <!-- Template Info Card -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div class="flex items-start justify-between mb-4">
            <div class="flex items-center">
              <div class="w-12 h-12 rounded-lg bg-blue-600 flex items-center justify-center text-white mr-4">
                <i class="fas fa-project-diagram"></i>
              </div>
              <div>
                <h2 class="text-xl font-semibold text-gray-900">{{ template.name }}</h2>
                <p class="text-sm text-gray-500">Version {{ template.version }}</p>
              </div>
            </div>
            <span
              *ngIf="template.isFeatured"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
            >
              Featured
            </span>
          </div>

          <p class="text-gray-600 mb-4">{{ template.description }}</p>

          <!-- Stats -->
          <div class="flex items-center space-x-6 mb-4">
            <div class="flex items-center">
              <div class="flex">
                <i
                  *ngFor="let star of getRatingStars(template.rating)"
                  class="fas fa-star text-sm"
                  [ngClass]="{
                    'text-yellow-400': star === 'full',
                    'text-yellow-200': star === 'half',
                    'text-gray-300': star === 'empty'
                  }"
                ></i>
              </div>
              <span class="ml-2 text-sm text-gray-600">{{ template.rating.toFixed(1) }} ({{ template.ratingCount }} reviews)</span>
            </div>
            <span class="text-sm text-gray-500">{{ template.downloadCount }} downloads</span>
            <span class="text-sm text-gray-500">{{ template.usageCount }} uses</span>
          </div>

          <!-- Tags -->
          <div class="flex flex-wrap gap-2" *ngIf="template.tags.length > 0">
            <span
              *ngFor="let tag of template.tags"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
            >
              {{ tag }}
            </span>
          </div>
        </div>

        <!-- Tabs -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6">
              <button
                *ngFor="let tab of ['overview', 'reviews', 'versions']"
                (click)="setActiveTab(tab)"
                class="py-4 px-1 border-b-2 font-medium text-sm capitalize"
                [ngClass]="{
                  'border-blue-500 text-blue-600': activeTab === tab,
                  'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== tab
                }"
              >
                {{ tab }}
              </button>
            </nav>
          </div>

          <div class="p-6">
            <!-- Overview Tab -->
            <div *ngIf="activeTab === 'overview'">
              <div class="space-y-6">
                <div>
                  <h3 class="text-lg font-medium text-gray-900 mb-3">Documentation</h3>
                  <p class="text-gray-600">{{ template.documentation || 'No documentation available.' }}</p>
                </div>

                <div *ngIf="template.requirements.length > 0">
                  <h3 class="text-lg font-medium text-gray-900 mb-3">Requirements</h3>
                  <ul class="list-disc list-inside space-y-1">
                    <li *ngFor="let requirement of template.requirements" class="text-gray-600">{{ requirement }}</li>
                  </ul>
                </div>

                <!-- Template Variables Section -->
                <div *ngIf="template.variables && getVariableEntries(template.variables).length > 0">
                  <h3 class="text-lg font-medium text-gray-900 mb-3">Customizable Variables</h3>
                  <div class="space-y-3">
                    <div
                      *ngFor="let variable of getVariableEntries(template.variables)"
                      class="flex items-start p-3 border border-gray-200 rounded-lg bg-gray-50"
                    >
                      <div class="flex-1">
                        <div class="flex items-center">
                          <h4 class="font-medium text-gray-900">{{ variable.key }}</h4>
                          <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            Variable
                          </span>
                        </div>
                        <div class="flex items-center mt-2 text-xs text-gray-500">
                          <span>Default: {{ variable.value || 'Not set' }}</span>
                        </div>
                        <div class="mt-2 text-xs text-gray-600">
                          <code class="bg-gray-200 px-1 rounded">{{'{{'}}{{'{{'}}{{ variable.key }}{{'}}'}}}}</code> in step configurations
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <p class="text-sm text-blue-700">
                      <strong>Note:</strong> You can customize these variable values when creating a workflow from this template or during execution.
                    </p>
                  </div>
                </div>

                <div>
                  <h3 class="text-lg font-medium text-gray-900 mb-3">Workflow Steps</h3>
                  <div class="space-y-3">
                    <div
                      *ngFor="let step of template.steps; let i = index"
                      class="flex items-start p-3 border border-gray-200 rounded-lg"
                    >
                      <div class="w-8 h-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-sm font-medium mr-3">
                        {{ i + 1 }}
                      </div>
                      <div>
                        <h4 class="font-medium text-gray-900">{{ step.name }}</h4>
                        <p class="text-sm text-gray-500">Type: {{ step.type }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Reviews Tab -->
            <div *ngIf="activeTab === 'reviews'">
              <div class="space-y-6">
                <!-- Add Review Button -->
                <div class="flex justify-between items-center">
                  <h3 class="text-lg font-medium text-gray-900">Reviews</h3>
                  <button
                    (click)="showReviewForm = !showReviewForm"
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <i class="fas fa-plus mr-2"></i>
                    Write Review
                  </button>
                </div>

                <!-- Review Form -->
                <div *ngIf="showReviewForm" class="bg-gray-50 rounded-lg p-4">
                  <h4 class="font-medium text-gray-900 mb-3">Write a Review</h4>

                  <!-- Rating -->
                  <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Rating</label>
                    <div class="flex space-x-1">
                      <button
                        *ngFor="let i of [1,2,3,4,5]"
                        (click)="setUserRating(i)"
                        class="text-2xl"
                        [ngClass]="i <= userRating ? 'text-yellow-400' : 'text-gray-300'"
                      >
                        <i class="fas fa-star"></i>
                      </button>
                    </div>
                  </div>

                  <!-- Review Text -->
                  <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Review</label>
                    <textarea
                      [(ngModel)]="userReview"
                      rows="4"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Share your experience with this template..."
                    ></textarea>
                  </div>

                  <!-- Submit Buttons -->
                  <div class="flex space-x-3">
                    <button
                      (click)="submitReview()"
                      [disabled]="userRating === 0"
                      class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                    >
                      Submit Review
                    </button>
                    <button
                      (click)="showReviewForm = false; userRating = 0; userReview = ''"
                      class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                  </div>
                </div>

                <!-- Reviews List -->
                <div *ngIf="reviewsLoading" class="flex justify-center py-8">
                  <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>

                <div *ngIf="!reviewsLoading && reviews.length === 0" class="text-center py-8">
                  <p class="text-gray-500">No reviews yet. Be the first to review this template!</p>
                </div>

                <div *ngIf="!reviewsLoading && reviews.length > 0" class="space-y-4">
                  <div
                    *ngFor="let review of reviews"
                    class="border border-gray-200 rounded-lg p-4"
                  >
                    <div class="flex items-start justify-between mb-3">
                      <div>
                        <h4 class="font-medium text-gray-900">{{ review.userName || 'Anonymous' }}</h4>
                        <div class="flex items-center mt-1">
                          <div class="flex">
                            <i
                              *ngFor="let star of getRatingStars(review.rating)"
                              class="fas fa-star text-sm"
                              [ngClass]="{
                                'text-yellow-400': star === 'full',
                                'text-yellow-200': star === 'half',
                                'text-gray-300': star === 'empty'
                              }"
                            ></i>
                          </div>
                          <span class="ml-2 text-sm text-gray-500">{{ review.createdAt | date:'short' }}</span>
                        </div>
                      </div>
                      <button
                        (click)="markReviewHelpful(review.id)"
                        class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-500 hover:text-gray-700"
                      >
                        <i class="fas fa-thumbs-up mr-1"></i>
                        Helpful ({{ review.helpfulCount }})
                      </button>
                    </div>
                    <p class="text-gray-600">{{ review.content }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Versions Tab -->
            <div *ngIf="activeTab === 'versions'">
              <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900">Version History</h3>
                <div class="bg-gray-50 rounded-lg p-4">
                  <div class="flex items-center justify-between">
                    <div>
                      <h4 class="font-medium text-gray-900">{{ template.version }}</h4>
                      <p class="text-sm text-gray-500">Current version</p>
                    </div>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Latest
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm p-6 sticky top-4">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Template Info</h3>

          <dl class="space-y-3">
            <div>
              <dt class="text-sm font-medium text-gray-500">Category</dt>
              <dd class="text-sm text-gray-900 capitalize">{{ template.category }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Variables</dt>
              <dd class="text-sm text-gray-900">
                <span *ngIf="template.variables && getVariableEntries(template.variables).length > 0"
                      class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                  {{ getVariableEntries(template.variables).length }} variable{{ getVariableEntries(template.variables).length !== 1 ? 's' : '' }}
                </span>
                <span *ngIf="!template.variables || getVariableEntries(template.variables).length === 0" class="text-gray-500">
                  No variables
                </span>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">License</dt>
              <dd class="text-sm text-gray-900">{{ template.license }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Created</dt>
              <dd class="text-sm text-gray-900">{{ template.createdAt | date:'mediumDate' }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
              <dd class="text-sm text-gray-900">{{ template.updatedAt | date:'mediumDate' }}</dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <!-- Create Workflow Modal -->
  <div *ngIf="showCreateWorkflowModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-10 mx-auto p-6 border max-w-2xl shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Create Workflow from Template</h3>

        <div class="space-y-6">
          <!-- Basic Information -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Workflow Name</label>
              <input
                type="text"
                [(ngModel)]="workflowName"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter workflow name">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Project</label>
              <select
                [(ngModel)]="selectedProjectId"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="">Select a project</option>
                <option *ngFor="let project of projects" [value]="project.id">{{ project.name }}</option>
              </select>
            </div>
          </div>

          <!-- Variable Customization -->
          <div *ngIf="template && template.variables && getVariableEntries(template.variables).length > 0" class="border-t pt-6">
            <app-variable-override
              [variables]="template.variables"
              title="Customize Template Variables"
              description="Override default variable values for your specific workflow needs"
              (variablesChange)="onVariablesChange($event)">
            </app-variable-override>
          </div>

          <!-- No Variables Message -->
          <div *ngIf="template && (!template.variables || getVariableEntries(template.variables).length === 0)" class="border-t pt-6">
            <div class="text-center py-4 text-gray-500">
              <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <p class="mt-2 text-sm">This template doesn't have any customizable variables.</p>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3 mt-8 pt-6 border-t">
          <button
            (click)="showCreateWorkflowModal = false; resetCreateWorkflowForm()"
            class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            Cancel
          </button>
          <button
            (click)="createWorkflow()"
            [disabled]="!workflowName || !selectedProjectId"
            class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed">
            Create Workflow
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Loading State -->
<div *ngIf="loading" class="min-h-screen bg-gray-50 flex items-center justify-center">
  <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
</div>
