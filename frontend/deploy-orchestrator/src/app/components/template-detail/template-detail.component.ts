import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { WorkflowService } from '../../services/workflow.service';
import { ProjectService } from '../../services/project.service';
import { WorkflowTemplate, TemplateReview } from '../../models/workflow.model';
import { Project } from '../../models/project.model';

@Component({
  selector: 'app-template-detail',
  templateUrl: './template-detail.component.html',
  styleUrls: ['./template-detail.component.css']
})
export class TemplateDetailComponent implements OnInit {
  template: WorkflowTemplate | null = null;
  reviews: TemplateReview[] = [];
  projects: Project[] = [];

  // UI State
  loading: boolean = false;
  reviewsLoading: boolean = false;
  activeTab: string = 'overview';

  // Rating & Review
  userRating: number = 0;
  userReview: string = '';
  showReviewForm: boolean = false;

  // Favorites
  isFavorite: boolean = false;

  // Create Workflow
  showCreateWorkflowModal: boolean = false;
  workflowName: string = '';
  selectedProjectId: string = '';
  variableOverrides: { [key: string]: any } = {};

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private workflowService: WorkflowService,
    private projectService: ProjectService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      const templateId = params['id'];
      if (templateId) {
        this.loadTemplate(templateId);
        this.loadReviews(templateId);
      }
    });

    this.loadProjects();
  }

  private loadTemplate(templateId: string): void {
    this.loading = true;

    this.workflowService.getTemplate(templateId).subscribe({
      next: (template) => {
        this.template = template;
        this.workflowName = `${template.name} - Copy`;
        this.loading = false;
      },
      error: (error) => {
        console.error('Failed to load template:', error);
        this.loading = false;
      }
    });
  }

  private loadReviews(templateId: string): void {
    this.reviewsLoading = true;

    this.workflowService.getTemplateReviews(templateId).subscribe({
      next: (reviews) => {
        this.reviews = reviews;
        this.reviewsLoading = false;
      },
      error: (error) => {
        console.error('Failed to load reviews:', error);
        this.reviewsLoading = false;
      }
    });
  }

  private loadProjects(): void {
    this.projectService.getProjects().subscribe({
      next: (projects) => {
        this.projects = projects;
        if (projects.length > 0) {
          this.selectedProjectId = projects[0].id;
        }
      },
      error: (error) => {
        console.error('Failed to load projects:', error);
      }
    });
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  downloadTemplate(): void {
    if (!this.template) return;

    this.workflowService.downloadTemplate(this.template.id).subscribe({
      next: (downloadedTemplate) => {
        this.template = downloadedTemplate;
        // Trigger download
        const blob = new Blob([JSON.stringify(downloadedTemplate, null, 2)], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${downloadedTemplate.name}.json`;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Failed to download template:', error);
      }
    });
  }

  toggleFavorite(): void {
    if (!this.template) return;

    if (this.isFavorite) {
      this.workflowService.removeFromFavorites(this.template.id).subscribe({
        next: () => {
          this.isFavorite = false;
        },
        error: (error) => {
          console.error('Failed to remove from favorites:', error);
        }
      });
    } else {
      this.workflowService.addToFavorites(this.template.id).subscribe({
        next: () => {
          this.isFavorite = true;
        },
        error: (error) => {
          console.error('Failed to add to favorites:', error);
        }
      });
    }
  }

  submitRating(): void {
    if (!this.template || this.userRating === 0) return;

    const ratingData = {
      rating: this.userRating,
      review: this.userReview
    };

    this.workflowService.rateTemplate(this.template.id, ratingData).subscribe({
      next: () => {
        this.loadTemplate(this.template!.id);
        this.loadReviews(this.template!.id);
        this.userRating = 0;
        this.userReview = '';
        this.showReviewForm = false;
      },
      error: (error) => {
        console.error('Failed to submit rating:', error);
      }
    });
  }

  submitReview(): void {
    if (!this.template || this.userRating === 0) return;

    const reviewData = {
      title: `Review for ${this.template.name}`,
      content: this.userReview,
      rating: this.userRating,
      pros: [],
      cons: []
    };

    this.workflowService.createTemplateReview(this.template.id, reviewData).subscribe({
      next: () => {
        this.loadTemplate(this.template!.id);
        this.loadReviews(this.template!.id);
        this.userRating = 0;
        this.userReview = '';
        this.showReviewForm = false;
      },
      error: (error) => {
        console.error('Failed to submit review:', error);
      }
    });
  }

  createWorkflow(): void {
    if (!this.template || !this.workflowName || !this.selectedProjectId) return;

    const request = {
      name: this.workflowName,
      description: `Workflow created from template: ${this.template.name}`,
      projectId: this.selectedProjectId,
      variableOverrides: this.variableOverrides
    };

    this.workflowService.createWorkflowFromTemplate(this.template.id, request).subscribe({
      next: (workflow) => {
        this.showCreateWorkflowModal = false;
        this.resetCreateWorkflowForm();
        this.router.navigate(['/workflows', workflow.id]);
      },
      error: (error) => {
        console.error('Failed to create workflow:', error);
      }
    });
  }

  onVariablesChange(variables: { [key: string]: any }): void {
    this.variableOverrides = variables;
  }

  resetCreateWorkflowForm(): void {
    this.workflowName = this.template ? `${this.template.name} - Copy` : '';
    this.variableOverrides = {};
  }

  getVariableEntries(variables: { [key: string]: any }): Array<{key: string, value: any}> {
    if (!variables) return [];
    return Object.entries(variables).map(([key, value]) => ({
      key,
      value
    }));
  }

  markReviewHelpful(reviewId: string): void {
    this.workflowService.markReviewHelpful(reviewId).subscribe({
      next: () => {
        this.loadReviews(this.template!.id);
      },
      error: (error) => {
        console.error('Failed to mark review as helpful:', error);
      }
    });
  }

  getRatingStars(rating: number): string[] {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 0; i < fullStars; i++) {
      stars.push('full');
    }

    if (hasHalfStar) {
      stars.push('half');
    }

    while (stars.length < 5) {
      stars.push('empty');
    }

    return stars;
  }

  setUserRating(rating: number): void {
    this.userRating = rating;
  }

  goBack(): void {
    this.router.navigate(['/templates']);
  }
}
