/* Essential styles for template detail */
.template-info-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); }
.rating-star { transition: all 0.2s ease-in-out; cursor: pointer; }
.rating-star:hover { transform: scale(1.1); }
.review-card:hover { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }
.step-card:hover { background-color: #f9fafb; border-color: #3b82f6; }
.favorite-btn:hover { transform: translateY(-1px); }
.download-btn:hover { transform: translateY(-1px); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }

@keyframes modalSlideIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}
.modal-content { animation: modalSlideIn 0.3s ease-out; }

@keyframes spin { to { transform: rotate(360deg); } }
.loading-spinner { animation: spin 1s linear infinite; }

@media (max-width: 768px) {
  .template-actions { flex-direction: column; gap: 0.5rem; }
  .template-actions button { width: 100%; }
  .modal-content { margin: 1rem; width: calc(100% - 2rem); }
}
