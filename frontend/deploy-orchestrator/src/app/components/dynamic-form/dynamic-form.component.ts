import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup } from '@angular/forms';
import { DynamicFormService, DynamicFormSchema, DynamicFormField } from '../../services/dynamic-form.service';

@Component({
  selector: 'app-dynamic-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  template: `
    <div class="dynamic-form" *ngIf="schema && form">
      <!-- Form Header -->
      <div class="form-header mb-6" *ngIf="schema.title || schema.description">
        <h3 class="text-lg font-medium text-gray-900 mb-2" *ngIf="schema.title">
          {{ schema.title }}
        </h3>
        <p class="text-sm text-gray-600" *ngIf="schema.description">
          {{ schema.description }}
        </p>
      </div>

      <!-- Form Fields -->
      <form [formGroup]="form" (ngSubmit)="onSubmit()" class="space-y-6">
        <div *ngFor="let field of visibleFields" class="form-field">
          <!-- Field Label -->
          <label [for]="field.name" class="block text-sm font-medium text-gray-700 mb-1">
            {{ field.label }}
            <span class="text-red-500 ml-1" *ngIf="field.required">*</span>
          </label>

          <!-- Field Description -->
          <p class="text-xs text-gray-500 mb-2" *ngIf="field.description">
            {{ field.description }}
          </p>

          <!-- String Input -->
          <input
            *ngIf="field.type === 'string'"
            [id]="field.name"
            [formControlName]="field.name"
            type="text"
            [placeholder]="field.placeholder || ''"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            [class.border-red-300]="hasFieldError(field.name)"
          />

          <!-- Email Input -->
          <input
            *ngIf="field.type === 'email'"
            [id]="field.name"
            [formControlName]="field.name"
            type="email"
            [placeholder]="field.placeholder || '<EMAIL>'"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            [class.border-red-300]="hasFieldError(field.name)"
          />

          <!-- URL Input -->
          <input
            *ngIf="field.type === 'url'"
            [id]="field.name"
            [formControlName]="field.name"
            type="url"
            [placeholder]="field.placeholder || 'https://example.com'"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            [class.border-red-300]="hasFieldError(field.name)"
          />

          <!-- Password Input -->
          <div *ngIf="field.type === 'password'" class="relative">
            <input
              [id]="field.name"
              [formControlName]="field.name"
              [type]="showPassword[field.name] ? 'text' : 'password'"
              [placeholder]="field.placeholder || ''"
              class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              [class.border-red-300]="hasFieldError(field.name)"
            />
            <button
              type="button"
              (click)="togglePasswordVisibility(field.name)"
              class="absolute inset-y-0 right-0 pr-3 flex items-center">
              <svg class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path *ngIf="!showPassword[field.name]" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path *ngIf="!showPassword[field.name]" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                <path *ngIf="showPassword[field.name]" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
              </svg>
            </button>
          </div>

          <!-- Number Input -->
          <input
            *ngIf="field.type === 'number'"
            [id]="field.name"
            [formControlName]="field.name"
            type="number"
            [placeholder]="field.placeholder || '0'"
            [min]="field.validation?.min || null"
            [max]="field.validation?.max || null"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            [class.border-red-300]="hasFieldError(field.name)"
          />

          <!-- Boolean Input -->
          <div *ngIf="field.type === 'boolean'" class="flex items-center">
            <input
              [id]="field.name"
              [formControlName]="field.name"
              type="checkbox"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label [for]="field.name" class="ml-2 text-sm text-gray-700">
              Enable {{ field.label }}
            </label>
          </div>

          <!-- Select Input -->
          <select
            *ngIf="field.type === 'select'"
            [id]="field.name"
            [formControlName]="field.name"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            [class.border-red-300]="hasFieldError(field.name)"
          >
            <option value="">Select an option...</option>
            <option 
              *ngFor="let option of getSelectOptions(field)" 
              [value]="getOptionValue(option)">
              {{ getOptionLabel(option) }}
            </option>
          </select>

          <!-- Textarea Input -->
          <textarea
            *ngIf="field.type === 'textarea'"
            [id]="field.name"
            [formControlName]="field.name"
            [placeholder]="field.placeholder || ''"
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            [class.border-red-300]="hasFieldError(field.name)"
          ></textarea>

          <!-- Object Input (JSON Editor) -->
          <div *ngIf="field.type === 'object'" class="space-y-4 border border-gray-200 rounded-md p-4">
            <div class="text-sm font-medium text-gray-700">{{ field.label }} Properties</div>
            <div *ngFor="let subField of field.properties" class="ml-4">
              <!-- Recursive field rendering would go here -->
              <div class="text-sm text-gray-500">{{ subField.name }}: {{ subField.type }}</div>
            </div>
          </div>

          <!-- Array Input -->
          <div *ngIf="field.type === 'array'" class="space-y-2">
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-gray-700">Items</span>
              <button
                type="button"
                (click)="addArrayItem(field.name)"
                class="text-sm text-blue-600 hover:text-blue-800">
                Add Item
              </button>
            </div>
            <div *ngFor="let item of getArrayItems(field.name); let i = index" class="flex items-center space-x-2">
              <input
                type="text"
                [value]="item"
                (input)="updateArrayItem(field.name, i, $event)"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
              <button
                type="button"
                (click)="removeArrayItem(field.name, i)"
                class="text-red-600 hover:text-red-800">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
          </div>

          <!-- Field Errors -->
          <div *ngIf="hasFieldError(field.name)" class="mt-1">
            <p class="text-sm text-red-600" *ngFor="let error of getFieldErrors(field.name)">
              {{ error }}
            </p>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200" *ngIf="showActions">
          <button
            type="button"
            (click)="onCancel()"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            Cancel
          </button>
          <button
            type="submit"
            [disabled]="form.invalid || loading"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
            {{ loading ? 'Saving...' : 'Save' }}
          </button>
        </div>
      </form>
    </div>
  `,
  styleUrls: ['./dynamic-form.component.scss']
})
export class DynamicFormComponent implements OnInit, OnChanges {
  @Input() schema!: DynamicFormSchema;
  @Input() initialValues: any = {};
  @Input() loading = false;
  @Input() showActions = true;

  @Output() formChange = new EventEmitter<any>();
  @Output() formSubmit = new EventEmitter<any>();
  @Output() formCancel = new EventEmitter<void>();

  form!: FormGroup;
  showPassword: { [key: string]: boolean } = {};
  visibleFields: DynamicFormField[] = [];

  constructor(private dynamicFormService: DynamicFormService) {}

  ngOnInit(): void {
    this.buildForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['schema'] || changes['initialValues']) {
      this.buildForm();
    }
  }

  private buildForm(): void {
    if (!this.schema) return;

    this.form = this.dynamicFormService.buildFormFromSchema(this.schema, this.initialValues);
    this.updateVisibleFields();

    // Initialize password visibility
    this.schema.fields.forEach(field => {
      if (field.type === 'password') {
        this.showPassword[field.name] = false;
      }
    });

    // Subscribe to form changes
    this.form.valueChanges.subscribe(value => {
      this.updateVisibleFields();
      this.formChange.emit(value);
    });
  }

  private updateVisibleFields(): void {
    if (!this.schema || !this.form) return;

    const formValues = this.form.value;
    this.visibleFields = this.schema.fields.filter(field => 
      this.dynamicFormService.isFieldVisible(field, formValues)
    );
  }

  togglePasswordVisibility(fieldName: string): void {
    this.showPassword[fieldName] = !this.showPassword[fieldName];
  }

  hasFieldError(fieldName: string): boolean {
    const control = this.form.get(fieldName);
    return !!(control && control.invalid && (control.dirty || control.touched));
  }

  getFieldErrors(fieldName: string): string[] {
    const control = this.form.get(fieldName);
    if (!control || !control.errors) return [];

    const errors: string[] = [];
    const fieldErrors = control.errors;

    if (fieldErrors['required']) errors.push('This field is required');
    if (fieldErrors['email']) errors.push('Please enter a valid email address');
    if (fieldErrors['invalidUrl']) errors.push('Please enter a valid URL');
    if (fieldErrors['pattern']) errors.push('Please enter a valid format');
    if (fieldErrors['min']) errors.push(`Value must be at least ${fieldErrors['min'].min}`);
    if (fieldErrors['max']) errors.push(`Value must be at most ${fieldErrors['max'].max}`);
    if (fieldErrors['minlength']) errors.push(`Must be at least ${fieldErrors['minlength'].requiredLength} characters`);
    if (fieldErrors['maxlength']) errors.push(`Must be at most ${fieldErrors['maxlength'].requiredLength} characters`);

    return errors;
  }

  getSelectOptions(field: DynamicFormField): any[] {
    return field.options || [];
  }

  getOptionValue(option: any): string {
    return typeof option === 'object' ? option.value : option;
  }

  getOptionLabel(option: any): string {
    return typeof option === 'object' ? option.label : option;
  }

  getArrayItems(fieldName: string): any[] {
    const value = this.form.get(fieldName)?.value;
    return Array.isArray(value) ? value : [];
  }

  addArrayItem(fieldName: string): void {
    const currentValue = this.getArrayItems(fieldName);
    const newValue = [...currentValue, ''];
    this.form.get(fieldName)?.setValue(newValue);
  }

  updateArrayItem(fieldName: string, index: number, event: any): void {
    const currentValue = this.getArrayItems(fieldName);
    const newValue = [...currentValue];
    newValue[index] = event.target.value;
    this.form.get(fieldName)?.setValue(newValue);
  }

  removeArrayItem(fieldName: string, index: number): void {
    const currentValue = this.getArrayItems(fieldName);
    const newValue = currentValue.filter((_, i) => i !== index);
    this.form.get(fieldName)?.setValue(newValue);
  }

  onSubmit(): void {
    if (this.form.valid) {
      this.formSubmit.emit(this.form.value);
    }
  }

  onCancel(): void {
    this.formCancel.emit();
  }
}
