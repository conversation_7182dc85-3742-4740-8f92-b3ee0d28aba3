.dynamic-form {
  .form-field {
    &.form-field-full {
      grid-column: 1 / -1;
    }
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
  }

  .field-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
  }

  .required-indicator {
    color: #ef4444;
    margin-left: 0.25rem;
  }

  .form-input,
  .form-select,
  .form-textarea {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    font-size: 0.875rem;
    line-height: 1.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    &.error {
      border-color: #ef4444;
    }

    &:disabled {
      background-color: #f9fafb;
      color: #6b7280;
      cursor: not-allowed;
    }
  }

  .form-checkbox {
    height: 1rem;
    width: 1rem;
    color: #3b82f6;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;

    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }

  .field-description {
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
  }

  .field-error {
    font-size: 0.75rem;
    color: #ef4444;
    margin-top: 0.25rem;
  }

  .array-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;

    .array-item-input {
      flex: 1;
    }

    .array-item-remove {
      color: #ef4444;
      cursor: pointer;
      padding: 0.25rem;
      border-radius: 0.25rem;
      transition: background-color 0.15s ease-in-out;

      &:hover {
        background-color: #fef2f2;
      }
    }
  }

  .array-add-button {
    color: #3b82f6;
    font-size: 0.875rem;
    cursor: pointer;
    padding: 0.5rem;
    border: 1px dashed #d1d5db;
    border-radius: 0.375rem;
    text-align: center;
    transition: border-color 0.15s ease-in-out, background-color 0.15s ease-in-out;

    &:hover {
      border-color: #3b82f6;
      background-color: #eff6ff;
    }
  }

  .object-field {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    background-color: #f9fafb;

    .object-field-title {
      font-size: 0.875rem;
      font-weight: 500;
      color: #374151;
      margin-bottom: 0.75rem;
    }

    .object-field-content {
      display: grid;
      gap: 0.75rem;
    }
  }

  .conditional-field {
    opacity: 0.6;
    transition: opacity 0.2s ease-in-out;

    &.visible {
      opacity: 1;
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
    margin-top: 1.5rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    border: 1px solid transparent;

    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    &.btn-primary {
      background-color: #3b82f6;
      color: white;

      &:hover:not(:disabled) {
        background-color: #2563eb;
      }
    }

    &.btn-secondary {
      background-color: white;
      color: #374151;
      border-color: #d1d5db;

      &:hover:not(:disabled) {
        background-color: #f9fafb;
      }
    }
  }

  .loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  // Responsive design
  @media (max-width: 640px) {
    .form-grid {
      grid-template-columns: 1fr;
    }

    .form-actions {
      flex-direction: column-reverse;
    }

    .btn {
      width: 100%;
      justify-content: center;
    }
  }
}
