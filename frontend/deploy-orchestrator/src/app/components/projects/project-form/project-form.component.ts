import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ProjectService } from '../../../services/project.service';
import { Project } from '../../../models/project.model';

@Component({
  selector: 'app-project-form',
  templateUrl: './project-form.component.html',
  styleUrls: ['./project-form.component.scss']
})
export class ProjectFormComponent implements OnInit {
  projectForm!: FormGroup;
  isEditMode = false;
  projectId: string | null = null;
  loading = false;
  submitting = false;
  error = '';

  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private projectService: ProjectService
  ) { }

  ngOnInit(): void {
    this.initForm();

    this.projectId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.projectId;

    if (this.isEditMode && this.projectId) {
      this.loadProject(this.projectId);
    }
  }

  initForm(): void {
    this.projectForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      description: ['', Validators.maxLength(500)],
      isActive: [true]
    });
  }

  loadProject(id: string): void {
    this.loading = true;
    this.projectService.getProject(id).subscribe({
      next: (project) => {
        this.projectForm.patchValue({
          name: project.name,
          description: project.description,
          isActive: project.isActive
        });
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load project. Please try again.';
        console.error('Error loading project', error);
        this.loading = false;
      }
    });
  }

  onSubmit(): void {
    if (this.projectForm.invalid) {
      return;
    }

    this.submitting = true;
    const projectData: Project = {
      id: this.projectId || '',
      name: this.projectForm.value.name,
      description: this.projectForm.value.description,
      isActive: this.projectForm.value.isActive
    };

    const request = this.isEditMode
      ? this.projectService.updateProject(projectData)
      : this.projectService.createProject(projectData);

    request.subscribe({
      next: (project) => {
        // The project service will automatically refresh the projects list
        // which will update the project selector in the header
        this.router.navigate(['/projects']);
      },
      error: (error) => {
        this.error = `Failed to ${this.isEditMode ? 'update' : 'create'} project. Please try again.`;
        console.error(`Error ${this.isEditMode ? 'updating' : 'creating'} project`, error);
        this.submitting = false;
      }
    });
  }

  cancel(): void {
    this.router.navigate(['/projects']);
  }
}
