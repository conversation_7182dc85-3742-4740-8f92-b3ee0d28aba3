<div class="px-4 py-5 sm:px-6">
  <h3 class="text-lg leading-6 font-medium text-gray-900">{{ isEditMode ? 'Edit' : 'Create' }} Project</h3>
  <p class="mt-1 max-w-2xl text-sm text-gray-500">{{ isEditMode ? 'Update project details' : 'Enter project details' }}</p>
</div>

<div *ngIf="loading" class="flex justify-center py-6">
  <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
  </svg>
</div>

<div *ngIf="error" class="bg-red-50 border-l-4 border-red-400 p-4 mx-4 my-2">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
      </svg>
    </div>
    <div class="ml-3">
      <p class="text-sm text-red-700">{{ error }}</p>
    </div>
  </div>
</div>

<div *ngIf="!loading" class="border-t border-gray-200">
  <form [formGroup]="projectForm" (ngSubmit)="onSubmit()">
    <div class="px-4 py-5 bg-white sm:p-6">
      <div class="grid grid-cols-6 gap-6">
        <div class="col-span-6 sm:col-span-4">
          <label for="name" class="block text-sm font-medium text-gray-700">Project Name</label>
          <input type="text" id="name" formControlName="name" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
          <div *ngIf="projectForm.get('name')?.invalid && (projectForm.get('name')?.dirty || projectForm.get('name')?.touched)" class="text-red-500 text-xs mt-1">
            <div *ngIf="projectForm.get('name')?.errors?.['required']">Project name is required.</div>
            <div *ngIf="projectForm.get('name')?.errors?.['maxlength']">Project name cannot exceed 100 characters.</div>
          </div>
        </div>

        <div class="col-span-6">
          <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
          <textarea id="description" formControlName="description" rows="3" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"></textarea>
          <div *ngIf="projectForm.get('description')?.invalid && (projectForm.get('description')?.dirty || projectForm.get('description')?.touched)" class="text-red-500 text-xs mt-1">
            <div *ngIf="projectForm.get('description')?.errors?.['maxlength']">Description cannot exceed 500 characters.</div>
          </div>
        </div>

        <div class="col-span-6">
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <input id="isActive" formControlName="isActive" type="checkbox" class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
            </div>
            <div class="ml-3 text-sm">
              <label for="isActive" class="font-medium text-gray-700">Active</label>
              <p class="text-gray-500">Inactive projects are not accessible to users.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
      <button type="button" (click)="cancel()" class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-3">
        Cancel
      </button>
      <button type="submit" [disabled]="projectForm.invalid || submitting" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <span *ngIf="submitting" class="mr-2">
          <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </span>
        {{ isEditMode ? 'Update' : 'Create' }}
      </button>
    </div>
  </form>
</div>
