<div class="px-4 py-5 sm:px-6">
  <div class="flex justify-between items-center">
    <h3 class="text-lg leading-6 font-medium text-gray-900">Projects</h3>
    <button *ngIf="isAdmin" (click)="createProject()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
      Create Project
    </button>
  </div>
  <p class="mt-1 max-w-2xl text-sm text-gray-500">
    <span *ngIf="isAdmin">Manage your projects</span>
    <span *ngIf="!isAdmin">View your accessible projects</span>
  </p>
</div>

<div *ngIf="loading" class="flex justify-center py-6">
  <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
  </svg>
</div>

<div *ngIf="error" class="bg-red-50 border-l-4 border-red-400 p-4 mx-4 my-2">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
      </svg>
    </div>
    <div class="ml-3">
      <p class="text-sm text-red-700">{{ error }}</p>
    </div>
  </div>
</div>

<div *ngIf="!loading && !error" class="overflow-x-auto">
  <table *ngIf="projects.length > 0" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
      <tr>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
        <th scope="col" class="relative px-6 py-3">
          <span class="sr-only">Actions</span>
        </th>
      </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
      <tr *ngFor="let project of projects">
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm font-medium text-gray-900">{{ project.name }}</div>
        </td>
        <td class="px-6 py-4">
          <div class="text-sm text-gray-500">{{ project.description }}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span *ngIf="project.isActive" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
            Active
          </span>
          <span *ngIf="!project.isActive" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
            Inactive
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          {{ project.createdAt | date:'medium' }}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <button (click)="viewProjectSettings(project)" class="text-gray-600 hover:text-gray-900 mr-4">Settings</button>
          <button *ngIf="isAdmin" (click)="editProject(project)" class="text-blue-600 hover:text-blue-900 mr-4">Edit</button>
          <button *ngIf="isAdmin" (click)="deleteProject(project)" class="text-red-600 hover:text-red-900">Delete</button>
        </td>
      </tr>
    </tbody>
  </table>

  <div *ngIf="projects.length === 0" class="text-center py-10">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900">No projects</h3>
    <p *ngIf="isAdmin" class="mt-1 text-sm text-gray-500">Get started by creating a new project.</p>
    <p *ngIf="!isAdmin" class="mt-1 text-sm text-gray-500">You don't have access to any projects. Please contact an administrator.</p>
    <div *ngIf="isAdmin" class="mt-6">
      <button (click)="createProject()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
        </svg>
        Create Project
      </button>
    </div>
  </div>
</div>
