import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ProjectService } from '../../../services/project.service';
import { AuthService } from '../../../services/auth.service';
import { Project } from '../../../models/project.model';
import { ProjectSecretsComponent } from '../../project-secrets/project-secrets.component';
import { ProjectPermissionsTabComponent } from './project-permissions-tab/project-permissions-tab.component';
import { ProjectIntegrationsTabComponent } from './project-integrations-tab/project-integrations-tab.component';
import { ProjectAuditTabComponent } from './project-audit-tab/project-audit-tab.component';

@Component({
  selector: 'app-project-settings',
  standalone: true,
  imports: [CommonModule, ProjectSecretsComponent, ProjectPermissionsTabComponent, ProjectIntegrationsTabComponent, ProjectAuditTabComponent],
  templateUrl: './project-settings.component.html',
  styleUrls: ['./project-settings.component.css']
})
export class ProjectSettingsComponent implements OnInit {
  project: Project | null = null;
  projectId: string = '';
  loading = false;
  error = '';
  activeTab = 'general';

  tabs = [
    { id: 'general', name: 'General', icon: 'cog' },
    { id: 'secrets', name: 'Secrets', icon: 'key' },
    { id: 'permissions', name: 'Permissions', icon: 'shield' },
    { id: 'integrations', name: 'Integrations', icon: 'link' },
    { id: 'audit', name: 'Audit', icon: 'document' }
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private projectService: ProjectService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.projectId = this.route.snapshot.paramMap.get('id') || '';
    if (this.projectId) {
      this.loadProject();
    }
  }

  loadProject(): void {
    this.loading = true;
    this.error = '';

    this.projectService.getProject(this.projectId).subscribe({
      next: (project) => {
        this.project = project;
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load project details';
        this.loading = false;
        console.error('Error loading project:', error);
      }
    });
  }

  setActiveTab(tabId: string): void {
    this.activeTab = tabId;
  }

  isAdmin(): boolean {
    return this.authService.isAdmin();
  }

  canEditProject(): boolean {
    return this.isAdmin() || this.hasProjectPermission('write');
  }

  hasProjectPermission(permission: string): boolean {
    // This would check if the user has specific permissions for this project
    // For now, we'll use a simple admin check
    return this.isAdmin();
  }

  goBack(): void {
    this.router.navigate(['/projects']);
  }

  editProject(): void {
    if (this.canEditProject()) {
      this.router.navigate([`/projects/${this.projectId}/edit`]);
    }
  }

  getTabIcon(iconName: string): string {
    const icons: { [key: string]: string } = {
      'cog': 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z',
      'key': 'M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z',
      'shield': 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
      'link': 'M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1',
      'document': 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
    };
    return icons[iconName] || icons['cog'];
  }
}
