<div class="p-6">
  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <div>
      <h2 class="text-xl font-semibold text-gray-900">Project Integrations</h2>
      <p class="text-gray-600">Manage external integrations for this project</p>
    </div>
    <button
      (click)="openAddIntegrationModal()"
      class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
      </svg>
      Add Integration
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
  </div>

  <!-- Error <PERSON> -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
    {{ error }}
    <button (click)="error = ''" class="float-right text-red-500 hover:text-red-700">×</button>
  </div>

  <!-- Success Alert -->
  <div *ngIf="success" class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-4">
    {{ success }}
    <button (click)="success = ''" class="float-right text-green-500 hover:text-green-700">×</button>
  </div>

  <!-- Integrations List -->
  <div *ngIf="!loading" class="space-y-4">
    <div *ngFor="let integration of integrations" class="bg-white border border-gray-200 rounded-lg p-6">
      <div class="flex justify-between items-start">
        <div class="flex items-start space-x-4">
          <!-- Integration Icon -->
          <div class="flex-shrink-0">
            <div [class]="'w-12 h-12 rounded-lg flex items-center justify-center ' + getIntegrationIconClass(integration.type)">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path [attr.d]="getIntegrationIcon(integration.type)"></path>
              </svg>
            </div>
          </div>

          <!-- Integration Details -->
          <div class="flex-1">
            <div class="flex items-center space-x-2">
              <h3 class="text-lg font-medium text-gray-900">{{ integration.name }}</h3>
              <span [class]="'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ' + getStatusBadgeClass(integration.status)">
                {{ integration.status }}
              </span>
            </div>
            <p class="text-gray-600 mt-1">{{ integration.description }}</p>
            <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
              <span>Type: {{ formatIntegrationType(integration.type) }}</span>
              <span>•</span>
              <span>Added: {{ formatDate(integration.createdAt) }}</span>
              <span *ngIf="integration.lastSync">•</span>
              <span *ngIf="integration.lastSync">Last sync: {{ formatDate(integration.lastSync) }}</span>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center space-x-2">
          <button
            (click)="testIntegration(integration)"
            [disabled]="integration.status === 'testing'"
            class="text-blue-600 hover:text-blue-900 disabled:opacity-50">
            <span *ngIf="integration.status === 'testing'">Testing...</span>
            <span *ngIf="integration.status !== 'testing'">Test</span>
          </button>
          <button
            (click)="configureIntegration(integration)"
            class="text-gray-600 hover:text-gray-900">
            Configure
          </button>
          <button
            (click)="removeIntegration(integration)"
            class="text-red-600 hover:text-red-900">
            Remove
          </button>
        </div>
      </div>

      <!-- Integration Configuration Preview -->
      <div *ngIf="integration.showConfig" class="mt-4 pt-4 border-t border-gray-200">
        <h4 class="text-sm font-medium text-gray-900 mb-2">Configuration</h4>
        <div class="bg-gray-50 rounded-lg p-3">
          <pre class="text-xs text-gray-700 whitespace-pre-wrap">{{ formatConfig(integration.config) }}</pre>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="integrations.length === 0" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No integrations configured</h3>
      <p class="mt-1 text-sm text-gray-500">Get started by adding your first integration.</p>
      <div class="mt-6">
        <button
          (click)="openAddIntegrationModal()"
          class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
          Add Integration
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Add Integration Modal -->
<div *ngIf="showAddModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Add Integration</h3>
        <button (click)="closeAddModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <form (ngSubmit)="addIntegration()" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Integration Type *</label>
          <select
            [(ngModel)]="newIntegration.type"
            name="type"
            required
            (change)="onIntegrationTypeChange()"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Select integration type...</option>
            <option *ngFor="let type of availableIntegrationTypes" [value]="type.value">
              {{ type.label }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Name *</label>
          <input
            type="text"
            [(ngModel)]="newIntegration.name"
            name="name"
            required
            placeholder="e.g., Production Git Repository"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea
            [(ngModel)]="newIntegration.description"
            name="description"
            rows="2"
            placeholder="Describe this integration..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
        </div>

        <!-- Dynamic Configuration Fields -->
        <div *ngIf="newIntegration.type" class="space-y-4">
          <h4 class="text-sm font-medium text-gray-900">Configuration</h4>
          <div *ngFor="let field of getConfigFields(newIntegration.type)" class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">
              {{ field.label }}
              <span *ngIf="field.required" class="text-red-500">*</span>
            </label>
            
            <input
              *ngIf="field.type === 'text' || field.type === 'url'"
              [type]="field.type"
              [(ngModel)]="newIntegration.config[field.key]"
              [name]="field.key"
              [required]="field.required"
              [placeholder]="field.placeholder"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            
            <input
              *ngIf="field.type === 'password'"
              type="password"
              [(ngModel)]="newIntegration.config[field.key]"
              [name]="field.key"
              [required]="field.required"
              [placeholder]="field.placeholder"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            
            <textarea
              *ngIf="field.type === 'textarea'"
              [(ngModel)]="newIntegration.config[field.key]"
              [name]="field.key"
              [required]="field.required"
              [placeholder]="field.placeholder"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
            
            <select
              *ngIf="field.type === 'select'"
              [(ngModel)]="newIntegration.config[field.key]"
              [name]="field.key"
              [required]="field.required"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">{{ field.placeholder || 'Select...' }}</option>
              <option *ngFor="let option of field.options" [value]="option.value">
                {{ option.label }}
              </option>
            </select>
            
            <p *ngIf="field.description" class="text-xs text-gray-500">{{ field.description }}</p>
          </div>
        </div>

        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            (click)="closeAddModal()"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md">
            Cancel
          </button>
          <button
            type="submit"
            [disabled]="loading"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md disabled:opacity-50">
            <span *ngIf="loading" class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
            Add Integration
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
