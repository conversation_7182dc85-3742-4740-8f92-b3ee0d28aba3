import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ProjectIntegrationService } from '../../../../services/project-integration.service';
import { AuthService } from '../../../../services/auth.service';
import {
  ProjectIntegration,
  IntegrationTemplate,
  CreateIntegrationRequest,
  IntegrationType,
  IntegrationStatus
} from '../../../../models/project-integration.model';

@Component({
  selector: 'app-project-integrations-tab',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './project-integrations-tab.component.html',
  styleUrls: ['./project-integrations-tab.component.css']
})
export class ProjectIntegrationsTabComponent implements OnInit {
  @Input() projectId!: string;

  integrations: ProjectIntegration[] = [];
  templates: IntegrationTemplate[] = [];

  loading = false;
  error = '';
  success = '';

  // UI State
  showAddModal = false;
  showEditModal = false;
  selectedIntegration: ProjectIntegration | null = null;
  selectedTemplate: IntegrationTemplate | null = null;

  // Forms
  integrationForm: FormGroup;

  // Filters
  filterStatus: IntegrationStatus | 'all' = 'all';
  filterType: IntegrationType | 'all' = 'all';
  searchTerm = '';

  constructor(
    private integrationService: ProjectIntegrationService,
    private authService: AuthService,
    private formBuilder: FormBuilder
  ) {
    this.integrationForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: ['', Validators.required],
      type: ['', Validators.required],
      config: this.formBuilder.group({})
    });
  }

  ngOnInit(): void {
    this.loadData();
  }

  async loadData(): Promise<void> {
    this.loading = true;
    this.error = '';

    try {
      await Promise.all([
        this.loadIntegrations(),
        this.loadTemplates()
      ]);
    } catch (error) {
      this.error = 'Failed to load integration data';
      console.error('Error loading integration data:', error);
    } finally {
      this.loading = false;
    }
  }

  async loadIntegrations(): Promise<void> {
    try {
      this.integrations = await this.integrationService.getProjectIntegrations(this.projectId).toPromise() || [];
    } catch (error) {
      console.error('Error loading integrations:', error);
    }
  }

  async loadTemplates(): Promise<void> {
    try {
      this.templates = await this.integrationService.getIntegrationTemplates().toPromise() || [];
    } catch (error) {
      console.error('Error loading templates:', error);
    }
  }

  // Integration Management
  openAddModal(): void {
    this.showAddModal = true;
    this.selectedTemplate = null;
    this.integrationForm.reset();
    this.error = '';
    this.success = '';
  }

  openAddIntegrationModal(): void {
    this.openAddModal();
  }

  closeAddModal(): void {
    this.showAddModal = false;
    this.selectedTemplate = null;
    this.integrationForm.reset();
  }

  selectTemplate(template: IntegrationTemplate): void {
    this.selectedTemplate = template;
    this.integrationForm.patchValue({
      type: template.type,
      name: template.name,
      description: template.description
    });

    // Build dynamic config form based on template schema
    this.buildConfigForm(template);
  }

  buildConfigForm(template: IntegrationTemplate): void {
    const configGroup = this.formBuilder.group({});

    template.configSchema.forEach(field => {
      const validators = [];
      if (field.required) validators.push(Validators.required);
      if (field.validation?.minLength) validators.push(Validators.minLength(field.validation.minLength));
      if (field.validation?.maxLength) validators.push(Validators.maxLength(field.validation.maxLength));
      if (field.validation?.pattern) validators.push(Validators.pattern(field.validation.pattern));

      configGroup.addControl(field.name, this.formBuilder.control('', validators));
    });

    this.integrationForm.setControl('config', configGroup);
  }

  async createIntegration(): Promise<void> {
    if (this.integrationForm.invalid) {
      this.error = 'Please fill in all required fields';
      return;
    }

    const formValue = this.integrationForm.value;
    const request: CreateIntegrationRequest = {
      type: formValue.type,
      name: formValue.name,
      description: formValue.description,
      config: formValue.config,
      isActive: true
    };

    try {
      await this.integrationService.createIntegration(this.projectId, request).toPromise();
      this.success = 'Integration created successfully';
      this.closeAddModal();
      this.loadIntegrations();
    } catch (error) {
      this.error = 'Failed to create integration';
      console.error('Error creating integration:', error);
    }
  }

  async toggleIntegration(integration: ProjectIntegration): Promise<void> {
    try {
      await this.integrationService.toggleIntegration(this.projectId, integration.id, !integration.isActive).toPromise();
      this.success = `Integration ${integration.isActive ? 'disabled' : 'enabled'} successfully`;
      this.loadIntegrations();
    } catch (error) {
      this.error = 'Failed to toggle integration';
      console.error('Error toggling integration:', error);
    }
  }

  async deleteIntegration(integration: ProjectIntegration): Promise<void> {
    if (!confirm(`Are you sure you want to delete the integration "${integration.name}"?`)) {
      return;
    }

    try {
      await this.integrationService.deleteIntegration(this.projectId, integration.id).toPromise();
      this.success = 'Integration deleted successfully';
      this.loadIntegrations();
    } catch (error) {
      this.error = 'Failed to delete integration';
      console.error('Error deleting integration:', error);
    }
  }

  async testIntegration(integration: ProjectIntegration): Promise<void> {
    try {
      const result = await this.integrationService.testIntegration(this.projectId, integration.id).toPromise();
      if (result?.success) {
        this.success = 'Integration test successful';
      } else {
        this.error = `Integration test failed: ${result?.message}`;
      }
    } catch (error) {
      this.error = 'Failed to test integration';
      console.error('Error testing integration:', error);
    }
  }

  async syncIntegration(integration: ProjectIntegration): Promise<void> {
    try {
      const result = await this.integrationService.syncIntegration(this.projectId, integration.id).toPromise();
      if (result?.success) {
        this.success = 'Integration synced successfully';
        this.loadIntegrations();
      } else {
        this.error = `Integration sync failed: ${result?.message}`;
      }
    } catch (error) {
      this.error = 'Failed to sync integration';
      console.error('Error syncing integration:', error);
    }
  }

  // Utility methods
  get filteredIntegrations(): ProjectIntegration[] {
    let filtered = this.integrations;

    if (this.filterStatus !== 'all') {
      filtered = filtered.filter(i => i.status === this.filterStatus);
    }

    if (this.filterType !== 'all') {
      filtered = filtered.filter(i => i.type === this.filterType);
    }

    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(i =>
        i.name.toLowerCase().includes(term) ||
        i.description.toLowerCase().includes(term) ||
        i.type.toLowerCase().includes(term)
      );
    }

    return filtered;
  }

  get popularTemplates(): IntegrationTemplate[] {
    return this.templates.filter(t => t.isPopular);
  }

  get allTemplates(): IntegrationTemplate[] {
    return this.templates;
  }

  getStatusColor(status: IntegrationStatus): string {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'syncing': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  getTypeIcon(type: IntegrationType): string {
    switch (type) {
      case 'git': return 'M12 0C5.374 0 0 5.373 0 12c0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z';
      case 'ci_cd': return 'M12 2L2 7v10l10 5 10-5V7l-10-5z';
      case 'monitoring': return 'M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z';
      case 'notification': return 'M18 8A6 6 0 006 8c0 7-3 9-3 9h18s-3-2-3-9zM13.73 21a2 2 0 01-3.46 0';
      default: return 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z';
    }
  }

  canManageIntegrations(): boolean {
    return this.authService.isAdmin(); // Add more specific permission checks here
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  // Additional methods for template compatibility
  getIntegrationIconClass(type: IntegrationType): string {
    switch (type) {
      case 'git': return 'bg-gray-100 text-gray-600';
      case 'ci_cd': return 'bg-blue-100 text-blue-600';
      case 'monitoring': return 'bg-green-100 text-green-600';
      case 'notification': return 'bg-yellow-100 text-yellow-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  }

  getIntegrationIcon(type: IntegrationType): string {
    return this.getTypeIcon(type);
  }

  getStatusBadgeClass(status: IntegrationStatus): string {
    return this.getStatusColor(status);
  }

  formatIntegrationType(type: IntegrationType): string {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  formatConfig(config: any): string {
    return JSON.stringify(config, null, 2);
  }

  configureIntegration(integration: ProjectIntegration): void {
    this.selectedIntegration = integration;
    this.showEditModal = true;
  }

  removeIntegration(integration: ProjectIntegration): void {
    this.deleteIntegration(integration);
  }

  testIntegrationAction(integration: ProjectIntegration): void {
    integration.status = 'syncing';
    // Call the service method instead of recursive call
    this.testIntegration(integration).then(() => {
      integration.status = 'active';
    }).catch(() => {
      integration.status = 'error';
    });
  }

  // Template form properties
  newIntegration: any = {
    type: '',
    name: '',
    description: '',
    config: {}
  };

  availableIntegrationTypes = [
    { value: 'git', label: 'Git Repository' },
    { value: 'ci_cd', label: 'CI/CD Pipeline' },
    { value: 'monitoring', label: 'Monitoring System' },
    { value: 'notification', label: 'Notification Service' }
  ];

  onIntegrationTypeChange(): void {
    this.newIntegration.config = {};
  }

  getConfigFields(type: string): any[] {
    switch (type) {
      case 'git':
        return [
          { key: 'repositoryUrl', label: 'Repository URL', type: 'url', required: true, placeholder: 'https://github.com/user/repo.git' },
          { key: 'branch', label: 'Branch', type: 'text', required: false, placeholder: 'main' },
          { key: 'accessToken', label: 'Access Token', type: 'password', required: true, placeholder: 'ghp_...' }
        ];
      case 'ci_cd':
        return [
          { key: 'pipelineUrl', label: 'Pipeline URL', type: 'url', required: true, placeholder: 'https://ci.example.com/pipeline' },
          { key: 'apiKey', label: 'API Key', type: 'password', required: true, placeholder: 'api_key_...' }
        ];
      default:
        return [];
    }
  }

  addIntegration(): void {
    this.createIntegration();
  }
}
