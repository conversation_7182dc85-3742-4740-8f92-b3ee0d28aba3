/* Project Integrations Tab Styles */

.integration-card {
  @apply bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow;
}

.integration-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center;
}

.integration-status {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.integration-actions {
  @apply flex items-center space-x-2;
}

.integration-config {
  @apply mt-4 pt-4 border-t border-gray-200;
}

.config-preview {
  @apply bg-gray-50 rounded-lg p-3;
}

.modal-overlay {
  @apply fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50;
}

.modal-content {
  @apply relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white;
}

.form-field {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg;
}

.empty-state {
  @apply text-center py-12;
}

.empty-state-icon {
  @apply mx-auto h-12 w-12 text-gray-400;
}
