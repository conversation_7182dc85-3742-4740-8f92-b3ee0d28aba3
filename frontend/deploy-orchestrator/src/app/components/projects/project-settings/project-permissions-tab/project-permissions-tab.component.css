/* Optimized styles for project permissions tab */
.permission-item { @apply transition-colors hover:bg-gray-50; }
.permission-badge { @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium; }
.permission-badge.user { @apply bg-blue-100 text-blue-800; }
.permission-badge.group { @apply bg-green-100 text-green-800; }
.permission-badge.expired { @apply bg-red-100 text-red-800; }
.permission-avatar { @apply w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center; }
.permission-actions { @apply flex items-center space-x-2; }
.btn-revoke { @apply inline-flex items-center px-3 py-1 border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors; }

.search-input { @apply block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm; }
.filter-select { @apply block w-32 pl-3 pr-10 py-2 border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm rounded-md; }

.loading-spinner { @apply inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600; }
.empty-state { @apply text-center py-12; }
.empty-state-icon { @apply mx-auto h-12 w-12 text-gray-400; }
.empty-state-title { @apply mt-2 text-sm font-medium text-gray-900; }
.empty-state-description { @apply mt-1 text-sm text-gray-500; }

.alert { @apply border rounded-md p-4; }
.alert.error { @apply bg-red-50 border-red-200; }
.alert.success { @apply bg-green-50 border-green-200; }
.alert-icon { @apply w-5 h-5; }
.alert-icon.error { @apply text-red-400; }
.alert-icon.success { @apply text-green-400; }
.alert-message { @apply text-sm; }
.alert-message.error { @apply text-red-800; }
.alert-message.success { @apply text-green-800; }

.modal-overlay { @apply fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50; }
.modal-container { @apply relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white; }
.modal-header { @apply flex items-center justify-between pb-3; }
.modal-title { @apply text-lg font-medium text-gray-900; }
.modal-close { @apply text-gray-400 hover:text-gray-600 focus:outline-none; }
.modal-body { @apply py-4; }
.modal-footer { @apply flex items-center justify-end space-x-3 pt-4 border-t border-gray-200; }

.form-group { @apply mb-4; }
.form-label { @apply block text-sm font-medium text-gray-700 mb-2; }
.form-input, .form-select { @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm; }
.form-error { @apply mt-1 text-sm text-red-600; }

.btn { @apply inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors; }
.btn-primary { @apply border-transparent text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500; }
.btn-secondary { @apply border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500; }
.btn-danger { @apply border-transparent text-white bg-red-600 hover:bg-red-700 focus:ring-red-500; }
.btn-icon { @apply w-4 h-4 mr-2; }
