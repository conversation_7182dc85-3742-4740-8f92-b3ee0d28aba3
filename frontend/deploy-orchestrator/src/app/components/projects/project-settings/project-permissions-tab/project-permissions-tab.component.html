<div class="space-y-6">
  <!-- Header -->
  <div class="flex justify-between items-center">
    <div>
      <h3 class="text-lg font-medium text-gray-900">Project Permissions</h3>
      <p class="text-sm text-gray-500">Manage user and group access to this project</p>
    </div>
    <div class="flex space-x-3" *ngIf="canManagePermissions()">
      <button (click)="openCreateRoleModal()" 
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Create Role
      </button>
      <button (click)="openAssignModal()" 
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Assign Permission
      </button>
    </div>
  </div>

  <!-- Alerts -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 rounded-md p-4">
    <div class="flex">
      <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
      </svg>
      <div class="ml-3">
        <p class="text-sm text-red-800">{{ error }}</p>
      </div>
    </div>
  </div>

  <div *ngIf="success" class="bg-green-50 border border-green-200 rounded-md p-4">
    <div class="flex">
      <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
      </svg>
      <div class="ml-3">
        <p class="text-sm text-green-800">{{ success }}</p>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0 sm:space-x-4">
    <div class="flex space-x-4">
      <select [(ngModel)]="filterType" class="block w-32 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
        <option value="all">All</option>
        <option value="users">Users</option>
        <option value="groups">Groups</option>
      </select>
    </div>
    <div class="flex-1 max-w-lg">
      <input type="text" 
             [(ngModel)]="searchTerm"
             placeholder="Search permissions..."
             class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="text-center py-12">
    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <p class="mt-2 text-sm text-gray-500">Loading permissions...</p>
  </div>

  <!-- Permissions List -->
  <div *ngIf="!loading" class="bg-white shadow overflow-hidden sm:rounded-md">
    <ul class="divide-y divide-gray-200" *ngIf="filteredPermissions.length > 0; else noPermissions">
      <li *ngFor="let permission of filteredPermissions" class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                <svg *ngIf="permission.userId" class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <svg *ngIf="permission.groupId" class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <div class="flex items-center">
                <p class="text-sm font-medium text-gray-900">
                  {{ permission.user?.fullName || permission.group?.name }}
                </p>
                <span *ngIf="permission.userId" class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  User
                </span>
                <span *ngIf="permission.groupId" class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Group
                </span>
                <span *ngIf="isExpired(permission.expiresAt)" class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  Expired
                </span>
              </div>
              <p class="text-sm text-gray-500">{{ permission.user?.email || permission.group?.description }}</p>
              <div class="mt-1 flex items-center text-sm text-gray-500">
                <span class="font-medium">Role:</span>
                <span class="ml-1">{{ getRoleName(permission.roleId) }}</span>
                <span *ngIf="permission.expiresAt" class="ml-4">
                  <span class="font-medium">Expires:</span>
                  <span class="ml-1">{{ formatDate(permission.expiresAt) }}</span>
                </span>
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-2" *ngIf="canManagePermissions()">
            <button (click)="revokePermission(permission)"
                    class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
              Revoke
            </button>
          </div>
        </div>
      </li>
    </ul>

    <ng-template #noPermissions>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No permissions assigned</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by assigning permissions to users or groups.</p>
        <div class="mt-6" *ngIf="canManagePermissions()">
          <button (click)="openAssignModal()" 
                  class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Assign Permission
          </button>
        </div>
      </div>
    </ng-template>
  </div>
</div>
