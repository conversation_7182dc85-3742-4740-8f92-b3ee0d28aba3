import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ProjectPermissionService } from '../../../../services/project-permission.service';
import { AuthService } from '../../../../services/auth.service';
import { 
  ProjectPermission, 
  ProjectRole, 
  ProjectGroup, 
  PermissionAssignmentRequest 
} from '../../../../models/project-permission.model';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-project-permissions-tab',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './project-permissions-tab.component.html',
  styleUrls: ['./project-permissions-tab.component.css']
})
export class ProjectPermissionsTabComponent implements OnInit {
  @Input() projectId!: string;

  permissions: ProjectPermission[] = [];
  roles: ProjectRole[] = [];
  groups: ProjectGroup[] = [];
  availableUsers: any[] = [];
  availableGroups: ProjectGroup[] = [];

  loading = false;
  error = '';
  success = '';

  // UI State
  showAssignModal = false;
  showCreateRoleModal = false;
  assignmentType: 'user' | 'group' = 'user';
  selectedPermission: ProjectPermission | null = null;

  // Forms
  assignmentForm: FormGroup;
  roleForm: FormGroup;

  // Filters
  filterType: 'all' | 'users' | 'groups' = 'all';
  searchTerm = '';

  constructor(
    private permissionService: ProjectPermissionService,
    private authService: AuthService,
    private formBuilder: FormBuilder
  ) {
    this.assignmentForm = this.formBuilder.group({
      type: ['user', Validators.required],
      userId: [''],
      groupId: [''],
      roleId: ['', Validators.required],
      expiresAt: ['']
    });

    this.roleForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: ['', Validators.required],
      permissions: [[], Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadData();
  }

  async loadData(): Promise<void> {
    this.loading = true;
    this.error = '';

    try {
      await Promise.all([
        this.loadPermissions(),
        this.loadRoles(),
        this.loadGroups()
      ]);
    } catch (error) {
      this.error = 'Failed to load permission data';
      console.error('Error loading permission data:', error);
    } finally {
      this.loading = false;
    }
  }

  async loadPermissions(): Promise<void> {
    try {
      this.permissions = await this.permissionService.getProjectPermissions(this.projectId).toPromise() || [];
    } catch (error) {
      console.error('Error loading permissions:', error);
    }
  }

  async loadRoles(): Promise<void> {
    try {
      const [projectRoles, globalRoles] = await Promise.all([
        firstValueFrom(this.permissionService.getProjectRoles(this.projectId)),
        firstValueFrom(this.permissionService.getGlobalRoles())
      ]);
      this.roles = [...(projectRoles || []), ...(globalRoles || [])];
    } catch (error) {
      console.error('Error loading roles:', error);
    }
  }

  async loadGroups(): Promise<void> {
    try {
      this.groups = await this.permissionService.getProjectGroups(this.projectId).toPromise() || [];
    } catch (error) {
      console.error('Error loading groups:', error);
    }
  }

  // Permission Management
  openAssignModal(): void {
    this.showAssignModal = true;
    this.assignmentForm.reset({ type: 'user' });
    this.loadAvailableUsers();
    this.error = '';
    this.success = '';
  }

  closeAssignModal(): void {
    this.showAssignModal = false;
    this.assignmentForm.reset();
  }

  async loadAvailableUsers(): Promise<void> {
    try {
      this.availableUsers = await this.permissionService.getAvailableUsers(this.projectId).toPromise() || [];
    } catch (error) {
      console.error('Error loading available users:', error);
    }
  }

  async loadAvailableGroups(): Promise<void> {
    try {
      this.availableGroups = await this.permissionService.getAvailableGroups(this.projectId).toPromise() || [];
    } catch (error) {
      console.error('Error loading available groups:', error);
    }
  }

  onAssignmentTypeChange(): void {
    const type = this.assignmentForm.get('type')?.value;
    if (type === 'group' && this.availableGroups.length === 0) {
      this.loadAvailableGroups();
    }
  }

  async assignPermission(): Promise<void> {
    if (this.assignmentForm.invalid) {
      this.error = 'Please fill in all required fields';
      return;
    }

    const formValue = this.assignmentForm.value;
    const request: PermissionAssignmentRequest = {
      roleId: formValue.roleId,
      expiresAt: formValue.expiresAt || undefined
    };

    if (formValue.type === 'user') {
      request.userId = formValue.userId;
    } else {
      request.groupId = formValue.groupId;
    }

    try {
      await this.permissionService.assignPermission(this.projectId, request).toPromise();
      this.success = 'Permission assigned successfully';
      this.closeAssignModal();
      this.loadPermissions();
    } catch (error) {
      this.error = 'Failed to assign permission';
      console.error('Error assigning permission:', error);
    }
  }

  async revokePermission(permission: ProjectPermission): Promise<void> {
    if (!confirm('Are you sure you want to revoke this permission?')) {
      return;
    }

    try {
      await this.permissionService.revokePermission(this.projectId, permission.id).toPromise();
      this.success = 'Permission revoked successfully';
      this.loadPermissions();
    } catch (error) {
      this.error = 'Failed to revoke permission';
      console.error('Error revoking permission:', error);
    }
  }

  // Role Management
  openCreateRoleModal(): void {
    this.showCreateRoleModal = true;
    this.roleForm.reset();
    this.error = '';
    this.success = '';
  }

  closeCreateRoleModal(): void {
    this.showCreateRoleModal = false;
    this.roleForm.reset();
  }

  async createRole(): Promise<void> {
    if (this.roleForm.invalid) {
      this.error = 'Please fill in all required fields';
      return;
    }

    try {
      await this.permissionService.createProjectRole(this.projectId, this.roleForm.value).toPromise();
      this.success = 'Role created successfully';
      this.closeCreateRoleModal();
      this.loadRoles();
    } catch (error) {
      this.error = 'Failed to create role';
      console.error('Error creating role:', error);
    }
  }

  // Utility methods
  get filteredPermissions(): ProjectPermission[] {
    let filtered = this.permissions;

    if (this.filterType !== 'all') {
      filtered = filtered.filter(p => 
        this.filterType === 'users' ? p.userId : p.groupId
      );
    }

    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(p => 
        p.user?.username?.toLowerCase().includes(term) ||
        p.user?.fullName?.toLowerCase().includes(term) ||
        p.group?.name?.toLowerCase().includes(term) ||
        p.role?.name?.toLowerCase().includes(term)
      );
    }

    return filtered;
  }

  getRoleName(roleId: string): string {
    const role = this.roles.find(r => r.id === roleId);
    return role?.name || 'Unknown Role';
  }

  isAdmin(): boolean {
    return this.authService.isAdmin();
  }

  canManagePermissions(): boolean {
    return this.isAdmin(); // Add more specific permission checks here
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  isExpired(expiresAt?: string): boolean {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
  }
}
