/* Optimized styles for project audit tab */
.audit-log-item {
  @apply transition-colors hover:bg-gray-50;
}

.action-badge, .status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.action-badge.create, .status-badge.success { @apply bg-green-100 text-green-800; }
.action-badge.update { @apply bg-blue-100 text-blue-800; }
.action-badge.delete, .status-badge.failed { @apply bg-red-100 text-red-800; }
.action-badge.view { @apply bg-gray-100 text-gray-800; }
.action-badge.default { @apply bg-yellow-100 text-yellow-800; }

.summary-card { @apply bg-white shadow rounded-lg overflow-hidden; }
.summary-card-content { @apply p-5; }
.summary-icon { @apply h-6 w-6; }
.summary-icon.total { @apply text-gray-400; }
.summary-icon.success { @apply text-green-400; }
.summary-icon.failed { @apply text-red-400; }
.summary-icon.users { @apply text-blue-400; }

.filter-form { @apply bg-gray-50 p-4 rounded-lg; }
.filter-input { @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm; }
.filter-label { @apply block text-sm font-medium text-gray-700 mb-1; }

.btn { @apply inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors; }
.btn-primary { @apply border-transparent text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500; }
.btn-secondary { @apply border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500; }
.btn-icon { @apply w-4 h-4 mr-2; }

.pagination-btn { @apply relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed; }
.pagination-btn.active { @apply border-blue-500 bg-blue-50 text-blue-600; }

.loading-spinner { @apply inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600; }
.empty-state { @apply text-center py-12; }
.empty-state-icon { @apply mx-auto h-12 w-12 text-gray-400; }
.empty-state-title { @apply mt-2 text-sm font-medium text-gray-900; }
.empty-state-description { @apply mt-1 text-sm text-gray-500; }

.alert { @apply border rounded-md p-4; }
.alert.error { @apply bg-red-50 border-red-200; }
.alert.success { @apply bg-green-50 border-green-200; }
.alert-icon { @apply w-5 h-5; }
.alert-icon.error { @apply text-red-400; }
.alert-icon.success { @apply text-green-400; }
.alert-message { @apply text-sm; }
.alert-message.error { @apply text-red-800; }
.alert-message.success { @apply text-green-800; }

.resource-icon { @apply w-8 h-8 text-gray-400; }
.log-timestamp { @apply text-xs text-gray-400 mt-1; }
.log-user { @apply text-sm font-medium text-gray-900; }
.log-resource { @apply text-sm text-gray-500; }
.log-error { @apply mt-2 text-sm text-red-600; }
.log-ip { @apply text-sm text-gray-500; }

@media (max-width: 640px) {
  .summary-card { @apply mb-4; }
  .filter-form { @apply grid-cols-1; }
  .pagination-btn { @apply px-3 py-2; }
}
