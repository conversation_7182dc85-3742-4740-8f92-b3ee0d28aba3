import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { ProjectAuditService } from '../../../../services/project-audit.service';
import { AuthService } from '../../../../services/auth.service';
import {
  ProjectAuditLog,
  ProjectAuditFilter,
  ProjectAuditResponse,
  ProjectAuditSummary,
  ProjectAuditAction,
  ProjectAuditResource
} from '../../../../models/project-audit.model';

@Component({
  selector: 'app-project-audit-tab',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './project-audit-tab.component.html',
  styleUrls: ['./project-audit-tab.component.css']
})
export class ProjectAuditTabComponent implements OnInit {
  @Input() projectId!: string;

  auditLogs: ProjectAuditLog[] = [];
  summary: ProjectAuditSummary | null = null;

  loading = false;
  error = '';
  success = '';

  // Pagination
  currentPage = 1;
  totalPages = 1;
  totalLogs = 0;
  pageSize = 20;

  // Filters
  filterForm: FormGroup;
  showFilters = false;

  // Available filter options
  actions: ProjectAuditAction[] = [
    'CREATE', 'UPDATE', 'DELETE', 'VIEW', 'DEPLOY', 'ROLLBACK', 'SCALE', 'RESTART',
    'PERMISSION_GRANT', 'PERMISSION_REVOKE', 'INTEGRATION_ADD', 'INTEGRATION_REMOVE',
    'SECRET_CREATE', 'SECRET_UPDATE', 'SECRET_DELETE', 'ENVIRONMENT_CREATE',
    'WORKFLOW_EXECUTE', 'SETTINGS_UPDATE'
  ];

  resources: ProjectAuditResource[] = [
    'PROJECT', 'ENVIRONMENT', 'DEPLOYMENT', 'WORKFLOW', 'SECRET', 'INTEGRATION',
    'PERMISSION', 'ROLE', 'GROUP', 'USER', 'SETTINGS'
  ];

  // Make Math available to template
  Math = Math;

  constructor(
    private auditService: ProjectAuditService,
    private authService: AuthService,
    private formBuilder: FormBuilder
  ) {
    this.filterForm = this.formBuilder.group({
      startDate: [''],
      endDate: [''],
      userId: [''],
      action: [''],
      resource: [''],
      success: [''],
      searchTerm: ['']
    });
  }

  ngOnInit(): void {
    this.loadData();
  }

  async loadData(): Promise<void> {
    this.loading = true;
    this.error = '';

    try {
      await Promise.all([
        this.loadAuditLogs(),
        this.loadSummary()
      ]);
    } catch (error) {
      this.error = 'Failed to load audit data';
      console.error('Error loading audit data:', error);
    } finally {
      this.loading = false;
    }
  }

  async loadAuditLogs(): Promise<void> {
    try {
      const filter = this.buildFilter();
      const response = await this.auditService.getProjectAuditLogs(this.projectId, filter).toPromise();

      if (response) {
        this.auditLogs = response.logs;
        this.totalLogs = response.total;
        this.currentPage = response.page;
        this.totalPages = response.totalPages;
      }
    } catch (error) {
      console.error('Error loading audit logs:', error);
    }
  }

  async loadSummary(): Promise<void> {
    try {
      const endDate = new Date().toISOString();
      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(); // Last 30 days

      this.summary = await this.auditService.getProjectAuditSummary(this.projectId, startDate, endDate).toPromise() || null;
    } catch (error) {
      console.error('Error loading audit summary:', error);
    }
  }

  buildFilter(): ProjectAuditFilter {
    const formValue = this.filterForm.value;
    const filter: ProjectAuditFilter = {
      page: this.currentPage,
      limit: this.pageSize
    };

    if (formValue.startDate) filter.startDate = formValue.startDate;
    if (formValue.endDate) filter.endDate = formValue.endDate;
    if (formValue.userId) filter.userId = formValue.userId;
    if (formValue.action) filter.action = formValue.action;
    if (formValue.resource) filter.resource = formValue.resource;
    if (formValue.success !== '') filter.success = formValue.success === 'true';
    if (formValue.searchTerm) filter.searchTerm = formValue.searchTerm;

    return filter;
  }

  async applyFilters(): Promise<void> {
    this.currentPage = 1;
    await this.loadAuditLogs();
  }

  async clearFilters(): Promise<void> {
    this.filterForm.reset();
    this.currentPage = 1;
    await this.loadAuditLogs();
  }

  async changePage(page: number): Promise<void> {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      await this.loadAuditLogs();
    }
  }

  async exportLogs(): Promise<void> {
    try {
      const filter = this.buildFilter();
      const blob = await this.auditService.exportProjectAuditLogs(this.projectId, filter).toPromise();

      if (blob) {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `project-${this.projectId}-audit-logs.csv`;
        link.click();
        window.URL.revokeObjectURL(url);

        this.success = 'Audit logs exported successfully';
      }
    } catch (error) {
      this.error = 'Failed to export audit logs';
      console.error('Error exporting audit logs:', error);
    }
  }

  // Utility methods
  getActionColor(action: ProjectAuditAction): string {
    switch (action) {
      case 'CREATE':
      case 'DEPLOY':
        return 'bg-green-100 text-green-800';
      case 'UPDATE':
      case 'SCALE':
        return 'bg-blue-100 text-blue-800';
      case 'DELETE':
      case 'PERMISSION_REVOKE':
        return 'bg-red-100 text-red-800';
      case 'VIEW':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  }

  getResourceIcon(resource: ProjectAuditResource): string {
    switch (resource) {
      case 'PROJECT': return 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4';
      case 'ENVIRONMENT': return 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z';
      case 'DEPLOYMENT': return 'M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10';
      case 'SECRET': return 'M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z';
      case 'USER': return 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z';
      default: return 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z';
    }
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  formatAction(action: ProjectAuditAction): string {
    return action.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  }

  formatResource(resource: ProjectAuditResource): string {
    return resource.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  }

  canViewAuditLogs(): boolean {
    return this.authService.isAdmin(); // Add more specific permission checks here
  }

  canExportAuditLogs(): boolean {
    return this.authService.isAdmin(); // Add more specific permission checks here
  }

  get pageNumbers(): number[] {
    const pages: number[] = [];
    const start = Math.max(1, this.currentPage - 2);
    const end = Math.min(this.totalPages, this.currentPage + 2);

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    return pages;
  }
}
