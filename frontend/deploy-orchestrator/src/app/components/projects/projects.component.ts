import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ProjectService } from '../../services/project.service';
import { Project } from '../../models/project.model';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-projects',
  templateUrl: './projects.component.html',
  styleUrls: ['./projects.component.scss']
})
export class ProjectsComponent implements OnInit, OnDestroy {
  projects: Project[] = [];
  loading = false;
  error = '';
  isAdmin = false;
  private subscription: Subscription = new Subscription();

  constructor(
    private projectService: ProjectService,
    private authService: AuthService,
    private router: Router
  ) {
    this.isAdmin = this.authService.isAdmin();
  }

  ngOnInit(): void {
    this.loadProjects();
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  loadProjects(): void {
    this.loading = true;
    this.subscription = this.projectService.projects$.subscribe({
      next: (projects) => {
        this.projects = projects;
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load projects. Please try again.';
        console.error('Error loading projects', error);
        this.loading = false;
      }
    });
  }

  createProject(): void {
    this.router.navigate(['/projects/new']);
  }

  editProject(project: Project): void {
    this.router.navigate([`/projects/${project.id}/edit`]);
  }

  viewProjectSettings(project: Project): void {
    this.router.navigate([`/projects/${project.id}/settings`]);
  }

  deleteProject(project: Project): void {
    if (confirm(`Are you sure you want to delete project "${project.name}"?`)) {
      this.projectService.deleteProject(project.id).subscribe({
        next: () => {
          // The project service will automatically refresh the projects list
          // which will update the project selector in the header
        },
        error: (error) => {
          console.error('Error deleting project', error);
          alert('Failed to delete project. Please try again.');
        }
      });
    }
  }
}
