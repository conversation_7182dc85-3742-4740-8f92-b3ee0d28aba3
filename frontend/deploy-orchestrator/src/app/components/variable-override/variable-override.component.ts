import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';

@Component({
  selector: 'app-variable-override',
  templateUrl: './variable-override.component.html',
  styleUrls: ['./variable-override.component.scss']
})
export class VariableOverrideComponent implements OnInit {
  @Input() variables: { [key: string]: any } = {};
  @Input() title: string = 'Customize Variables';
  @Input() description: string = 'Override default variable values for your specific needs';
  @Output() variablesChange = new EventEmitter<{ [key: string]: any }>();

  overrideValues: { [key: string]: any } = {};
  focusedInput: string | null = null;

  ngOnInit(): void {
    this.initializeOverrideValues();
  }

  private initializeOverrideValues(): void {
    // Initialize with default values from template
    this.overrideValues = { ...this.variables };
  }

  onVariableChange(variableName: string, event: Event): void {
    const target = event.target as HTMLInputElement;
    this.overrideValues[variableName] = target.value;
    this.variablesChange.emit({ ...this.overrideValues });
  }

  getVariableEntries(): Array<{key: string, value: any}> {
    return Object.entries(this.variables).map(([key, value]) => ({
      key,
      value
    }));
  }

  getOverrideValue(variableName: string): any {
    return this.overrideValues[variableName] || '';
  }

  resetToDefault(variableName: string): void {
    this.overrideValues[variableName] = this.variables[variableName];
    this.variablesChange.emit({ ...this.overrideValues });
  }

  hasOverride(variableName: string): boolean {
    return this.overrideValues[variableName] !== this.variables[variableName];
  }

  onInputFocus(variableName: string): void {
    this.focusedInput = variableName;
  }

  onInputBlur(variableName: string): void {
    this.focusedInput = null;
  }

  isFocused(variableName: string): boolean {
    return this.focusedInput === variableName;
  }
}
