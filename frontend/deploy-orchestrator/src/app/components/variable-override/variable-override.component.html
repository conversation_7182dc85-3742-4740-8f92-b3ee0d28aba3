<div class="space-y-6">
  <!-- Header -->
  <div>
    <h3 class="text-lg font-medium text-gray-900">{{ title }}</h3>
    <p class="mt-1 text-sm text-gray-600">{{ description }}</p>
  </div>

  <!-- No Variables Message -->
  <div *ngIf="getVariableEntries().length === 0" class="text-center py-8 text-gray-500">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
    </svg>
    <p class="mt-2">No variables to customize</p>
    <p class="text-sm">This template doesn't have any customizable variables.</p>
  </div>

  <!-- Variables Form -->
  <div *ngIf="getVariableEntries().length > 0" class="space-y-4">
    <div *ngFor="let variable of getVariableEntries()" class="space-y-2">
      <div class="flex items-center justify-between">
        <label class="block text-sm font-medium text-gray-700">
          {{ variable.key }}
        </label>
        <button
          *ngIf="hasOverride(variable.key)"
          (click)="resetToDefault(variable.key)"
          class="text-xs text-blue-600 hover:text-blue-800"
          title="Reset to default value">
          Reset to default
        </button>
      </div>

      <!-- Variable Input -->
      <div class="relative">
        <input
          type="text"
          [value]="getOverrideValue(variable.key)"
          (input)="onVariableChange(variable.key, $event)"
          (focus)="onInputFocus(variable.key)"
          (blur)="onInputBlur(variable.key)"
          [id]="'var-' + variable.key"
          [attr.data-variable]="variable.key"
          class="w-full px-4 py-3 text-sm border-2 rounded-lg transition-all duration-200 ease-in-out focus:outline-none focus:ring-4 focus:ring-blue-100 focus:border-blue-500 hover:border-gray-400"
          [class.border-blue-400]="hasOverride(variable.key)"
          [class.bg-blue-50]="hasOverride(variable.key)"
          [class.border-gray-300]="!hasOverride(variable.key)"
          [class.bg-white]="!hasOverride(variable.key)"
          [placeholder]="'Enter ' + variable.key + ' (default: ' + (variable.value || 'none') + ')'">

        <!-- Override indicator -->
        <div *ngIf="hasOverride(variable.key)"
             class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
        </div>

        <!-- Focus helper -->
        <div class="absolute inset-0 pointer-events-none border-2 border-transparent rounded-lg transition-all duration-200"
             [class.border-blue-300]="isFocused(variable.key)"
             [class.shadow-lg]="isFocused(variable.key)">
        </div>
      </div>

      <!-- Default Value Hint -->
      <div class="flex items-center justify-between text-xs text-gray-500">
        <span>Default: {{ variable.value || 'Not set' }}</span>
        <span *ngIf="hasOverride(variable.key)" class="text-blue-600 font-medium">
          Custom value
        </span>
      </div>

      <!-- Usage Example -->
      <div class="text-xs text-gray-600">
        <span class="font-medium">Usage:</span>
        <code class="bg-gray-200 px-1 rounded ml-1">{{'{{'}}{{'{{'}}{{ variable.key }}{{'}}'}}}}</code>
      </div>
    </div>

    <!-- Summary -->
    <div class="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-gray-800">Variable Customization</h3>
          <div class="mt-2 text-sm text-gray-600">
            <ul class="list-disc pl-5 space-y-1">
              <li>Leave fields empty to use default values</li>
              <li>Custom values will override defaults in the workflow</li>
              <li>You can also provide values during workflow execution</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
