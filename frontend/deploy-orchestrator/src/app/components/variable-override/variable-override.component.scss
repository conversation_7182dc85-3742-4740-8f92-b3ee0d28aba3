// Variable Override Component Styles

.variable-override {
  .variable-item {
    transition: all 0.2s ease-in-out;
    
    &:hover {
      background-color: #f9fafb;
    }
  }

  .variable-label {
    font-weight: 500;
    color: #374151;
  }

  .variable-input {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    
    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    &.overridden {
      border-color: #3b82f6;
      background-color: #eff6ff;
      
      &:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }
  }

  .default-value-hint {
    color: #6b7280;
    font-size: 0.75rem;
  }

  .custom-value-indicator {
    color: #3b82f6;
    font-weight: 500;
    font-size: 0.75rem;
  }

  .usage-example {
    color: #6b7280;
    font-size: 0.75rem;
    
    code {
      background-color: #e5e7eb;
      padding: 0.125rem 0.25rem;
      border-radius: 0.25rem;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }
  }

  .reset-button {
    color: #3b82f6;
    font-size: 0.75rem;
    
    &:hover {
      color: #1d4ed8;
      text-decoration: underline;
    }
  }

  .override-indicator {
    color: #3b82f6;
  }

  .summary-section {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    
    .summary-icon {
      color: #9ca3af;
    }
    
    .summary-title {
      color: #1f2937;
      font-weight: 500;
    }
    
    .summary-content {
      color: #6b7280;
      
      ul {
        margin-top: 0.5rem;
        padding-left: 1.25rem;
        
        li {
          margin-bottom: 0.25rem;
        }
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 640px) {
  .variable-override {
    .variable-item {
      padding: 0.75rem;
    }
    
    .summary-section {
      padding: 0.75rem;
    }
  }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .variable-override {
    .variable-label {
      color: #f3f4f6;
    }
    
    .variable-input {
      background-color: #374151;
      border-color: #4b5563;
      color: #f3f4f6;
      
      &:focus {
        border-color: #60a5fa;
      }
      
      &.overridden {
        background-color: #1e3a8a;
        border-color: #60a5fa;
      }
    }
    
    .default-value-hint {
      color: #9ca3af;
    }
    
    .custom-value-indicator {
      color: #93c5fd;
    }
    
    .usage-example {
      color: #9ca3af;
      
      code {
        background-color: #4b5563;
        color: #f3f4f6;
      }
    }
    
    .reset-button {
      color: #93c5fd;
      
      &:hover {
        color: #bfdbfe;
      }
    }
    
    .summary-section {
      background-color: #1f2937;
      border-color: #374151;
      
      .summary-title {
        color: #f3f4f6;
      }
      
      .summary-content {
        color: #d1d5db;
      }
    }
  }
}
