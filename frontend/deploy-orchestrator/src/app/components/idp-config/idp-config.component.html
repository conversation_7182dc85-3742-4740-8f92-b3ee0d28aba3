<div class="px-4 py-5 sm:px-6">
  <h3 class="text-lg leading-6 font-medium text-gray-900">Identity Provider Configuration</h3>
  <p class="mt-1 max-w-2xl text-sm text-gray-500">Configure authentication providers for your organization</p>
</div>

<div *ngIf="loading" class="flex justify-center py-6">
  <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
  </svg>
</div>

<div *ngIf="error" class="bg-red-50 border-l-4 border-red-400 p-4 mx-4 my-2">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
      </svg>
    </div>
    <div class="ml-3">
      <p class="text-sm text-red-700">{{ error }}</p>
    </div>
  </div>
</div>

<div *ngIf="!loading" class="border-t border-gray-200">
  <div class="border-b border-gray-200">
    <nav class="-mb-px flex" aria-label="Tabs">
      <button (click)="setActiveTab(IdentityProviderType.OIDC)" class="w-1/3 py-4 px-1 text-center border-b-2 font-medium text-sm" [ngClass]="activeTab === IdentityProviderType.OIDC ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
        OpenID Connect
        <span class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full"
              [ngClass]="activeTab === IdentityProviderType.OIDC ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'">
          {{ oidcProvidersCount }}
        </span>
      </button>
      <button (click)="setActiveTab(IdentityProviderType.SAML)" class="w-1/3 py-4 px-1 text-center border-b-2 font-medium text-sm" [ngClass]="activeTab === IdentityProviderType.SAML ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
        SAML
        <span class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full"
              [ngClass]="activeTab === IdentityProviderType.SAML ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'">
          {{ samlProvidersCount }}
        </span>
      </button>
      <button (click)="setActiveTab(IdentityProviderType.LDAP)" class="w-1/3 py-4 px-1 text-center border-b-2 font-medium text-sm" [ngClass]="activeTab === IdentityProviderType.LDAP ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
        LDAP
        <span class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full"
              [ngClass]="activeTab === IdentityProviderType.LDAP ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'">
          {{ ldapProvidersCount }}
        </span>
      </button>
    </nav>
  </div>

  <div class="p-4">
    <div *ngIf="activeTab === IdentityProviderType.OIDC">
      <app-oidc-config [providers]="getOIDCProviders()" (providersChanged)="loadProviders()"></app-oidc-config>
    </div>
    <div *ngIf="activeTab === IdentityProviderType.SAML">
      <app-saml-config [providers]="getSAMLProviders()" (providersChanged)="loadProviders()"></app-saml-config>
    </div>
    <div *ngIf="activeTab === IdentityProviderType.LDAP">
      <app-ldap-config [providers]="getLDAPProviders()" (providersChanged)="loadProviders()"></app-ldap-config>
    </div>
  </div>
</div>
