import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {IdentityProviderService} from '../../../services/identity-provider.service';
import {IdentityProvider, IdentityProviderType, LDAPProvider} from '../../../models/identity-provider.model';

@Component({
    selector: 'app-ldap-config',
    templateUrl: './ldap-config.component.html',
    styleUrls: ['./ldap-config.component.scss']
})
export class LdapConfigComponent implements OnInit {
    @Input() providers: LDAPProvider[] = [];
    @Output() providersChanged = new EventEmitter<void>();

    ldapForm!: FormGroup;
    isEditMode = false;
    editingProvider: LDAPProvider | null = null;
    showForm = false;
    loading = false;
    error = '';
    success = '';

    constructor(
        private formBuilder: FormBuilder,
        private identityProviderService: IdentityProviderService
    ) {
    }

    ngOnInit(): void {
        this.initForm();
    }

    initForm(): void {
        this.ldapForm = this.formBuilder.group({
            name: ['', [Validators.required, Validators.maxLength(100)]],
            description: ['', Validators.maxLength(500)],
            enabled: [true],
            isDefault: [false],
            url: ['', [Validators.required, Validators.maxLength(255)]],
            bindDN: ['', [Validators.required, Validators.maxLength(255)]],
            bindPassword: ['', [Validators.required, Validators.maxLength(255)]],
            baseDN: ['', [Validators.required, Validators.maxLength(255)]],
            userFilter: ['(uid=%s)', [Validators.required, Validators.maxLength(255)]],
            groupFilter: ['(objectClass=groupOfNames)', [Validators.required, Validators.maxLength(255)]],
            groupsAttr: ['memberOf', [Validators.required, Validators.maxLength(100)]],
            useSSL: [true],
            startTLS: [false],
            insecureSkip: [false]
        });
    }

    showAddForm(): void {
        this.isEditMode = false;
        this.editingProvider = null;
        this.ldapForm.reset({
            enabled: true,
            isDefault: false,
            userFilter: '(uid=%s)',
            groupFilter: '(objectClass=groupOfNames)',
            groupsAttr: 'memberOf',
            useSSL: true,
            startTLS: false,
            insecureSkip: false
        });
        this.showForm = true;
    }

    showEditForm(provider: LDAPProvider): void {
        this.isEditMode = true;
        this.editingProvider = provider;
        this.ldapForm.patchValue({
            name: provider.name,
            description: provider.description,
            enabled: provider.enabled,
            isDefault: provider.isDefault,
            url: provider.url,
            bindDN: provider.bindDN,
            bindPassword: provider.bindPassword,
            baseDN: provider.baseDN,
            userFilter: provider.userFilter,
            groupFilter: provider.groupFilter,
            groupsAttr: provider.groupsAttr,
            useSSL: provider.useSSL,
            startTLS: provider.startTLS,
            insecureSkip: provider.insecureSkip
        });
        this.showForm = true;
    }

    cancelForm(): void {
        this.showForm = false;
        this.error = '';
        this.success = '';
    }

    onSubmit(): void {
        if (this.ldapForm.invalid) {
            return;
        }

        this.loading = true;
        this.error = '';
        this.success = '';

        const providerData: LDAPProvider = {
            id: this.isEditMode && this.editingProvider ? this.editingProvider.id : '',
            name: this.ldapForm.value.name,
            description: this.ldapForm.value.description,
            type: IdentityProviderType.LDAP,
            enabled: this.ldapForm.value.enabled,
            isDefault: this.ldapForm.value.isDefault,
            url: this.ldapForm.value.url,
            bindDN: this.ldapForm.value.bindDN,
            bindPassword: this.ldapForm.value.bindPassword,
            baseDN: this.ldapForm.value.baseDN,
            userFilter: this.ldapForm.value.userFilter,
            groupFilter: this.ldapForm.value.groupFilter,
            groupsAttr: this.ldapForm.value.groupsAttr,
            useSSL: this.ldapForm.value.useSSL,
            startTLS: this.ldapForm.value.startTLS,
            insecureSkip: this.ldapForm.value.insecureSkip
        };

        const request = this.isEditMode
            ? this.identityProviderService.updateLDAPProvider(providerData)
            : this.identityProviderService.createLDAPProvider(providerData);

        request.subscribe({
            next: () => {
                this.success = `Provider ${this.isEditMode ? 'updated' : 'created'} successfully.`;
                this.loading = false;
                this.providersChanged.emit();
                this.showForm = false;
            },
            error: (error) => {
                this.error = `Failed to ${this.isEditMode ? 'update' : 'create'} provider. Please try again.`;
                console.error(`Error ${this.isEditMode ? 'updating' : 'creating'} provider`, error);
                this.loading = false;
            }
        });
    }

    deleteProvider(provider: LDAPProvider): void {
        if (confirm(`Are you sure you want to delete the provider "${provider.name}"?`)) {
            this.identityProviderService.deleteLDAPProvider(provider.id).subscribe({
                next: () => {
                    this.success = 'Provider deleted successfully.';
                    this.providersChanged.emit();
                },
                error: (error) => {
                    this.error = 'Failed to delete provider. Please try again.';
                    console.error('Error deleting provider', error);
                }
            });
        }
    }

    setDefaultProvider(provider: LDAPProvider): void {
        this.identityProviderService.setDefaultProvider(provider.id, IdentityProviderType.LDAP).subscribe({
            next: () => {
                this.success = `"${provider.name}" set as default provider.`;
                this.providersChanged.emit();
            },
            error: (error: any) => {
                this.error = 'Failed to set default provider. Please try again.';
                console.error('Error setting default provider', error);
            }
        });
    }

    testConnection(provider: LDAPProvider): void {
        this.identityProviderService.testConnection(provider.id, IdentityProviderType.LDAP).subscribe({
            next: (result: any) => {
                if (result.success) {
                    this.success = `Connection test successful: ${result.message}`;
                } else {
                    this.error = `Connection test failed: ${result.message}`;
                }
            },
            error: (error: any) => {
                this.error = 'Connection test failed. Please try again.';
                console.error('Error testing connection', error);
            }
        });
    }
}
