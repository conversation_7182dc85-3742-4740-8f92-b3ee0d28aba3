import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {IdentityProviderService} from '../../../services/identity-provider.service';
import {IdentityProvider, IdentityProviderType, SAMLProvider} from '../../../models/identity-provider.model';

@Component({
    selector: 'app-saml-config',
    templateUrl: './saml-config.component.html',
    styleUrls: ['./saml-config.component.scss']
})
export class SamlConfigComponent implements OnInit {
    @Input() providers: SAMLProvider[] = [];
    @Output() providersChanged = new EventEmitter<void>();

    samlForm!: FormGroup;
    isEditMode = false;
    editingProvider: SAMLProvider | null = null;
    showForm = false;
    loading = false;
    error = '';
    success = '';

    constructor(
        private formBuilder: FormBuilder,
        private identityProviderService: IdentityProviderService
    ) {
    }

    ngOnInit(): void {
        this.initForm();
    }

    initForm(): void {
        this.samlForm = this.formBuilder.group({
            name: ['', [Validators.required, Validators.maxLength(100)]],
            description: ['', Validators.maxLength(500)],
            enabled: [true],
            isDefault: [false],
            entityID: ['', [Validators.required, Validators.maxLength(255)]],
            metadataURL: ['', [Validators.required, Validators.maxLength(255)]],
            acsURL: ['', [Validators.required, Validators.maxLength(255)]],
            spCertificate: ['', [Validators.required]],
            spPrivateKey: ['', [Validators.required]],
            groupsAttribute: ['groups', [Validators.required, Validators.maxLength(100)]]
        });
    }

    showAddForm(): void {
        this.isEditMode = false;
        this.editingProvider = null;
        this.samlForm.reset({
            enabled: true,
            isDefault: false,
            groupsAttribute: 'groups'
        });
        this.showForm = true;
    }

    showEditForm(provider: SAMLProvider): void {
        this.isEditMode = true;
        this.editingProvider = provider;
        this.samlForm.patchValue({
            name: provider.name,
            description: provider.description,
            enabled: provider.enabled,
            isDefault: provider.isDefault,
            entityID: provider.entityID,
            metadataURL: provider.metadataURL,
            acsURL: provider.acsURL,
            spCertificate: provider.spCertificate,
            spPrivateKey: provider.spPrivateKey,
            groupsAttribute: provider.groupsAttribute
        });
        this.showForm = true;
    }

    cancelForm(): void {
        this.showForm = false;
        this.error = '';
        this.success = '';
    }

    onSubmit(): void {
        if (this.samlForm.invalid) {
            return;
        }

        this.loading = true;
        this.error = '';
        this.success = '';

        const providerData: SAMLProvider = {
            id: this.isEditMode && this.editingProvider ? this.editingProvider.id : '',
            name: this.samlForm.value.name,
            description: this.samlForm.value.description,
            type: IdentityProviderType.SAML,
            enabled: this.samlForm.value.enabled,
            isDefault: this.samlForm.value.isDefault,
            entityID: this.samlForm.value.entityID,
            metadataURL: this.samlForm.value.metadataURL,
            acsURL: this.samlForm.value.acsURL,
            spCertificate: this.samlForm.value.spCertificate,
            spPrivateKey: this.samlForm.value.spPrivateKey,
            groupsAttribute: this.samlForm.value.groupsAttribute
        };

        const request = this.isEditMode
            ? this.identityProviderService.updateSAMLProvider(providerData)
            : this.identityProviderService.createSAMLProvider(providerData);

        request.subscribe({
            next: () => {
                this.success = `Provider ${this.isEditMode ? 'updated' : 'created'} successfully.`;
                this.loading = false;
                this.providersChanged.emit();
                this.showForm = false;
            },
            error: (error) => {
                this.error = `Failed to ${this.isEditMode ? 'update' : 'create'} provider. Please try again.`;
                console.error(`Error ${this.isEditMode ? 'updating' : 'creating'} provider`, error);
                this.loading = false;
            }
        });
    }

    deleteProvider(provider: SAMLProvider): void {
        if (confirm(`Are you sure you want to delete the provider "${provider.name}"?`)) {
            this.identityProviderService.deleteSAMLProvider(provider.id).subscribe({
                next: () => {
                    this.success = 'Provider deleted successfully.';
                    this.providersChanged.emit();
                },
                error: (error) => {
                    this.error = 'Failed to delete provider. Please try again.';
                    console.error('Error deleting provider', error);
                }
            });
        }
    }

    setDefaultProvider(provider: SAMLProvider): void {
        this.identityProviderService.setDefaultProvider(provider.id, IdentityProviderType.SAML).subscribe({
            next: () => {
                this.success = `"${provider.name}" set as default provider.`;
                this.providersChanged.emit();
            },
            error: (error:any) => {
                this.error = 'Failed to set default provider. Please try again.';
                console.error('Error setting default provider', error);
            }
        });
    }

    testConnection(provider: SAMLProvider): void {
        this.identityProviderService.testConnection(provider.id, IdentityProviderType.SAML).subscribe({
            next: (result: any) => {
                if (result.success) {
                    this.success = `Connection test successful: ${result.message}`;
                } else {
                    this.error = `Connection test failed: ${result.message}`;
                }
            },
            error: (error: any) => {
                this.error = 'Connection test failed. Please try again.';
                console.error('Error testing connection', error);
            }
        });
    }
}
