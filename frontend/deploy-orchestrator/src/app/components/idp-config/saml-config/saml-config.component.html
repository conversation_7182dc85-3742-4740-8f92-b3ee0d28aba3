<div class="mb-4 flex justify-between items-center">
  <h3 class="text-lg font-medium text-gray-900">SAML Providers</h3>
  <button (click)="showAddForm()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
    Add Provider
  </button>
</div>

<div *ngIf="error" class="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
      </svg>
    </div>
    <div class="ml-3">
      <p class="text-sm text-red-700">{{ error }}</p>
    </div>
  </div>
</div>

<div *ngIf="success" class="bg-green-50 border-l-4 border-green-400 p-4 mb-4">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
      </svg>
    </div>
    <div class="ml-3">
      <p class="text-sm text-green-700">{{ success }}</p>
    </div>
  </div>
</div>

<div *ngIf="showForm" class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
  <div class="px-4 py-5 sm:px-6 bg-gray-50">
    <h3 class="text-lg leading-6 font-medium text-gray-900">{{ isEditMode ? 'Edit' : 'Add' }} SAML Provider</h3>
  </div>
  <div class="border-t border-gray-200">
    <form [formGroup]="samlForm" (ngSubmit)="onSubmit()">
      <div class="px-4 py-5 bg-white sm:p-6">
        <div class="grid grid-cols-6 gap-6">
          <div class="col-span-6 sm:col-span-3">
            <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
            <input type="text" id="name" formControlName="name" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
            <div *ngIf="samlForm.get('name')?.invalid && (samlForm.get('name')?.dirty || samlForm.get('name')?.touched)" class="text-red-500 text-xs mt-1">
              <div *ngIf="samlForm.get('name')?.errors?.['required']">Name is required.</div>
              <div *ngIf="samlForm.get('name')?.errors?.['maxlength']">Name cannot exceed 100 characters.</div>
            </div>
          </div>

          <div class="col-span-6 sm:col-span-3">
            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
            <input type="text" id="description" formControlName="description" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
            <div *ngIf="samlForm.get('description')?.invalid && (samlForm.get('description')?.dirty || samlForm.get('description')?.touched)" class="text-red-500 text-xs mt-1">
              <div *ngIf="samlForm.get('description')?.errors?.['maxlength']">Description cannot exceed 500 characters.</div>
            </div>
          </div>

          <div class="col-span-6 sm:col-span-3">
            <label for="entityID" class="block text-sm font-medium text-gray-700">Entity ID</label>
            <input type="text" id="entityID" formControlName="entityID" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
            <div *ngIf="samlForm.get('entityID')?.invalid && (samlForm.get('entityID')?.dirty || samlForm.get('entityID')?.touched)" class="text-red-500 text-xs mt-1">
              <div *ngIf="samlForm.get('entityID')?.errors?.['required']">Entity ID is required.</div>
              <div *ngIf="samlForm.get('entityID')?.errors?.['maxlength']">Entity ID cannot exceed 255 characters.</div>
            </div>
          </div>

          <div class="col-span-6 sm:col-span-3">
            <label for="metadataURL" class="block text-sm font-medium text-gray-700">Metadata URL</label>
            <input type="text" id="metadataURL" formControlName="metadataURL" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
            <div *ngIf="samlForm.get('metadataURL')?.invalid && (samlForm.get('metadataURL')?.dirty || samlForm.get('metadataURL')?.touched)" class="text-red-500 text-xs mt-1">
              <div *ngIf="samlForm.get('metadataURL')?.errors?.['required']">Metadata URL is required.</div>
              <div *ngIf="samlForm.get('metadataURL')?.errors?.['maxlength']">Metadata URL cannot exceed 255 characters.</div>
            </div>
          </div>

          <div class="col-span-6 sm:col-span-3">
            <label for="acsURL" class="block text-sm font-medium text-gray-700">ACS URL</label>
            <input type="text" id="acsURL" formControlName="acsURL" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
            <div *ngIf="samlForm.get('acsURL')?.invalid && (samlForm.get('acsURL')?.dirty || samlForm.get('acsURL')?.touched)" class="text-red-500 text-xs mt-1">
              <div *ngIf="samlForm.get('acsURL')?.errors?.['required']">ACS URL is required.</div>
              <div *ngIf="samlForm.get('acsURL')?.errors?.['maxlength']">ACS URL cannot exceed 255 characters.</div>
            </div>
          </div>

          <div class="col-span-6 sm:col-span-3">
            <label for="groupsAttribute" class="block text-sm font-medium text-gray-700">Groups Attribute</label>
            <input type="text" id="groupsAttribute" formControlName="groupsAttribute" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
            <div *ngIf="samlForm.get('groupsAttribute')?.invalid && (samlForm.get('groupsAttribute')?.dirty || samlForm.get('groupsAttribute')?.touched)" class="text-red-500 text-xs mt-1">
              <div *ngIf="samlForm.get('groupsAttribute')?.errors?.['required']">Groups Attribute is required.</div>
              <div *ngIf="samlForm.get('groupsAttribute')?.errors?.['maxlength']">Groups Attribute cannot exceed 100 characters.</div>
            </div>
          </div>

          <div class="col-span-6">
            <label for="spCertificate" class="block text-sm font-medium text-gray-700">Service Provider Certificate</label>
            <textarea id="spCertificate" formControlName="spCertificate" rows="5" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"></textarea>
            <div *ngIf="samlForm.get('spCertificate')?.invalid && (samlForm.get('spCertificate')?.dirty || samlForm.get('spCertificate')?.touched)" class="text-red-500 text-xs mt-1">
              <div *ngIf="samlForm.get('spCertificate')?.errors?.['required']">Service Provider Certificate is required.</div>
            </div>
          </div>

          <div class="col-span-6">
            <label for="spPrivateKey" class="block text-sm font-medium text-gray-700">Service Provider Private Key</label>
            <textarea id="spPrivateKey" formControlName="spPrivateKey" rows="5" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"></textarea>
            <div *ngIf="samlForm.get('spPrivateKey')?.invalid && (samlForm.get('spPrivateKey')?.dirty || samlForm.get('spPrivateKey')?.touched)" class="text-red-500 text-xs mt-1">
              <div *ngIf="samlForm.get('spPrivateKey')?.errors?.['required']">Service Provider Private Key is required.</div>
            </div>
          </div>

          <div class="col-span-6 sm:col-span-3">
            <div class="flex items-start">
              <div class="flex items-center h-5">
                <input id="enabled" formControlName="enabled" type="checkbox" class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
              </div>
              <div class="ml-3 text-sm">
                <label for="enabled" class="font-medium text-gray-700">Enabled</label>
                <p class="text-gray-500">Disabled providers cannot be used for authentication.</p>
              </div>
            </div>
          </div>

          <div class="col-span-6 sm:col-span-3">
            <div class="flex items-start">
              <div class="flex items-center h-5">
                <input id="isDefault" formControlName="isDefault" type="checkbox" class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
              </div>
              <div class="ml-3 text-sm">
                <label for="isDefault" class="font-medium text-gray-700">Default Provider</label>
                <p class="text-gray-500">The default provider will be used when no specific provider is selected.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
        <button type="button" (click)="cancelForm()" class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-3">
          Cancel
        </button>
        <button type="submit" [disabled]="samlForm.invalid || loading" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <span *ngIf="loading" class="mr-2">
            <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          {{ isEditMode ? 'Update' : 'Add' }} Provider
        </button>
      </div>
    </form>
  </div>
</div>

<div *ngIf="providers.length === 0 && !showForm" class="text-center py-10">
  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
  </svg>
  <h3 class="mt-2 text-sm font-medium text-gray-900">No SAML providers</h3>
  <p class="mt-1 text-sm text-gray-500">Get started by adding a new provider.</p>
  <div class="mt-6">
    <button (click)="showAddForm()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
      <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
      </svg>
      Add Provider
    </button>
  </div>
</div>

<div *ngIf="providers.length > 0 && !showForm" class="overflow-x-auto">
  <table class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
      <tr>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Default</th>
        <th scope="col" class="relative px-6 py-3">
          <span class="sr-only">Actions</span>
        </th>
      </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
      <tr *ngFor="let provider of providers">
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm font-medium text-gray-900">{{ provider.name }}</div>
        </td>
        <td class="px-6 py-4">
          <div class="text-sm text-gray-500">{{ provider.description }}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span *ngIf="provider.enabled" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
            Enabled
          </span>
          <span *ngIf="!provider.enabled" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
            Disabled
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span *ngIf="provider.isDefault" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
            Default
          </span>
          <button *ngIf="!provider.isDefault" (click)="setDefaultProvider(provider)" class="text-xs text-blue-600 hover:text-blue-900">
            Set as Default
          </button>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <button (click)="testConnection(provider)" class="text-blue-600 hover:text-blue-900 mr-4">Test</button>
          <button (click)="showEditForm(provider)" class="text-blue-600 hover:text-blue-900 mr-4">Edit</button>
          <button (click)="deleteProvider(provider)" class="text-red-600 hover:text-red-900">Delete</button>
        </td>
      </tr>
    </tbody>
  </table>
</div>
