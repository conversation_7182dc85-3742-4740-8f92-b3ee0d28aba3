import { Component, OnInit } from '@angular/core';
import { IdentityProviderService } from '../../services/identity-provider.service';
import { IdentityProvider, IdentityProviderType, OIDCProvider, SAMLProvider, LDAPProvider } from '../../models/identity-provider.model';

@Component({
  selector: 'app-idp-config',
  templateUrl: './idp-config.component.html',
  styleUrls: ['./idp-config.component.scss']
})
export class IdpConfigComponent implements OnInit {
  providers: IdentityProvider[] = [];
  loading = false;
  error = '';
  activeTab: IdentityProviderType = IdentityProviderType.OIDC;
  IdentityProviderType = IdentityProviderType; // Expose enum to template

  // Properties to store provider counts
  oidcProvidersCount = 0;
  samlProvidersCount = 0;
  ldapProvidersCount = 0;

  constructor(private identityProviderService: IdentityProviderService) { }

  ngOnInit(): void {
    this.loadProviders();
  }

  loadProviders(): void {
    this.loading = true;
    // Instead of calling getIdentityProviders, let's load each type separately
    this.loadOIDCProviders();
  }

  loadOIDCProviders(): void {
    this.identityProviderService.getOIDCProviders().subscribe({
      next: (oidcProviders) => {
        const oidcList = oidcProviders.map(p => ({...p, type: IdentityProviderType.OIDC}));
        this.oidcProvidersCount = oidcList.length;
        this.loadSAMLProviders(oidcList);
      },
      error: (error) => {
        this.handleError(error);
      }
    });
  }

  loadSAMLProviders(currentProviders: IdentityProvider[] = []): void {
    this.identityProviderService.getSAMLProviders().subscribe({
      next: (samlProviders) => {
        const samlList = samlProviders.map(p => ({...p, type: IdentityProviderType.SAML}));
        this.samlProvidersCount = samlList.length;
        this.loadLDAPProviders([...currentProviders, ...samlList]);
      },
      error: (error) => {
        this.handleError(error);
      }
    });
  }

  loadLDAPProviders(currentProviders: IdentityProvider[] = []): void {
    this.identityProviderService.getLDAPProviders().subscribe({
      next: (ldapProviders) => {
        const ldapList = ldapProviders.map(p => ({...p, type: IdentityProviderType.LDAP}));
        this.ldapProvidersCount = ldapList.length;
        this.providers = [...currentProviders, ...ldapList];
        this.loading = false;
      },
      error: (error) => {
        this.handleError(error);
      }
    });
  }

  handleError(error: any): void {
        this.error = 'Failed to load identity providers. Please try again.';
        console.error('Error loading identity providers', error);
        this.loading = false;
      }

  setActiveTab(tab: IdentityProviderType): void {
    this.activeTab = tab;
  }

  getProvidersByType(type: IdentityProviderType): IdentityProvider[] {
    return this.providers.filter(provider => provider.type === type);
  }

  getOIDCProviders(): OIDCProvider[] {
    return this.providers.filter(provider => provider.type === IdentityProviderType.OIDC) as OIDCProvider[];
  }

  getSAMLProviders(): SAMLProvider[] {
    return this.providers.filter(provider => provider.type === IdentityProviderType.SAML) as SAMLProvider[];
  }

  getLDAPProviders(): LDAPProvider[] {
    return this.providers.filter(provider => provider.type === IdentityProviderType.LDAP) as LDAPProvider[];
  }
}
