import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {IdentityProviderService} from '../../../services/identity-provider.service';
import {IdentityProvider, IdentityProviderType, OIDCProvider} from '../../../models/identity-provider.model';

@Component({
    selector: 'app-oidc-config',
    templateUrl: './oidc-config.component.html',
    styleUrls: ['./oidc-config.component.scss']
})
export class OidcConfigComponent implements OnInit {
    @Input() providers: OIDCProvider[] = [];
    @Output() providersChanged = new EventEmitter<void>();

    oidcForm!: FormGroup;
    isEditMode = false;
    editingProvider: OIDCProvider | null = null;
    showForm = false;
    loading = false;
    error = '';
    success = '';

    constructor(
        private formBuilder: FormBuilder,
        private identityProviderService: IdentityProviderService
    ) {
    }

    ngOnInit(): void {
        this.initForm();
    }

    initForm(): void {
        this.oidcForm = this.formBuilder.group({
            name: ['', [Validators.required, Validators.maxLength(100)]],
            description: ['', Validators.maxLength(500)],
            enabled: [true],
            isDefault: [false],
            issuerURL: ['', [Validators.required, Validators.maxLength(255)]],
            clientID: ['', [Validators.required, Validators.maxLength(255)]],
            clientSecret: ['', [Validators.required, Validators.maxLength(255)]],
            redirectURL: ['', [Validators.required, Validators.maxLength(255)]],
            scopes: ['openid profile email', [Validators.required, Validators.maxLength(255)]],
            groupsClaim: ['groups', [Validators.required, Validators.maxLength(100)]],
            usernameClaim: ['preferred_username', [Validators.required, Validators.maxLength(100)]],
            emailClaim: ['email', [Validators.required, Validators.maxLength(100)]]
        });
    }

    showAddForm(): void {
        this.isEditMode = false;
        this.editingProvider = null;
        this.oidcForm.reset({
            enabled: true,
            isDefault: false,
            scopes: 'openid profile email',
            groupsClaim: 'groups',
            usernameClaim: 'preferred_username',
            emailClaim: 'email'
        });
        this.showForm = true;
    }

    showEditForm(provider: OIDCProvider): void {
        this.isEditMode = true;
        this.editingProvider = provider;
        this.oidcForm.patchValue({
            name: provider.name,
            description: provider.description,
            enabled: provider.enabled,
            isDefault: provider.isDefault,
            issuerURL: provider.issuerURL,
            clientID: provider.clientID,
            clientSecret: provider.clientSecret,
            redirectURL: provider.redirectURL,
            scopes: provider.scopes,
            groupsClaim: provider.groupsClaim,
            usernameClaim: provider.usernameClaim,
            emailClaim: provider.emailClaim
        });
        this.showForm = true;
    }

    cancelForm(): void {
        this.showForm = false;
        this.error = '';
        this.success = '';
    }

    onSubmit(): void {
        if (this.oidcForm.invalid) {
            return;
        }

        this.loading = true;
        this.error = '';
        this.success = '';

        const providerData: OIDCProvider = {
            id: this.isEditMode && this.editingProvider ? this.editingProvider.id : '',
            name: this.oidcForm.value.name,
            description: this.oidcForm.value.description,
            type: IdentityProviderType.OIDC,
            enabled: this.oidcForm.value.enabled,
            isDefault: this.oidcForm.value.isDefault,
            issuerURL: this.oidcForm.value.issuerURL,
            clientID: this.oidcForm.value.clientID,
            clientSecret: this.oidcForm.value.clientSecret,
            redirectURL: this.oidcForm.value.redirectURL,
            scopes: this.oidcForm.value.scopes,
            groupsClaim: this.oidcForm.value.groupsClaim,
            usernameClaim: this.oidcForm.value.usernameClaim,
            emailClaim: this.oidcForm.value.emailClaim
        };

        const request = this.isEditMode
            ? this.identityProviderService.updateOIDCProvider(providerData)
            : this.identityProviderService.createOIDCProvider(providerData);

        request.subscribe({
            next: () => {
                this.success = `Provider ${this.isEditMode ? 'updated' : 'created'} successfully.`;
                this.loading = false;
                this.providersChanged.emit();
                this.showForm = false;
            },
            error: (error) => {
                this.error = `Failed to ${this.isEditMode ? 'update' : 'create'} provider. Please try again.`;
                console.error(`Error ${this.isEditMode ? 'updating' : 'creating'} provider`, error);
                this.loading = false;
            }
        });
    }

    deleteProvider(provider: OIDCProvider): void {
        if (confirm(`Are you sure you want to delete the provider "${provider.name}"?`)) {
            this.identityProviderService.deleteOIDCProvider(provider.id).subscribe({
                next: () => {
                    this.success = 'Provider deleted successfully.';
                    this.providersChanged.emit();
                },
                error: (error) => {
                    this.error = 'Failed to delete provider. Please try again.';
                    console.error('Error deleting provider', error);
                }
            });
        }
    }

    setDefaultProvider(provider: OIDCProvider): void {
        this.identityProviderService.setDefaultProvider(provider.id, IdentityProviderType.OIDC).subscribe({
            next: () => {
                this.success = `"${provider.name}" set as default provider.`;
                this.providersChanged.emit();
            },
            error: (error:any) => {
                this.error = 'Failed to set default provider. Please try again.';
                console.error('Error setting default provider', error);
            }
        });
    }

    testConnection(provider: OIDCProvider): void {
        this.identityProviderService.testConnection(provider.id, IdentityProviderType.OIDC).subscribe({
            next: (result: any) => {
                if (result.success) {
                    this.success = `Connection test successful: ${result.message}`;
                } else {
                    this.error = `Connection test failed: ${result.message}`;
                }
            },
            error: (error: any) => {
                this.error = 'Connection test failed. Please try again.';
                console.error('Error testing connection', error);
            }
        });
    }
}
