// Permission Management Component Styles

.permission-container {
  min-height: calc(100vh - 200px);
  background: #f8fafc;
}

.permission-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.role-card {
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  transition: all 0.2s ease-in-out;

  &:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
  }

  &.selected {
    border-color: #3b82f6;
    background: #eff6ff;
  }
}

.permission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.permission-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  padding: 0.75rem;
  transition: all 0.2s ease-in-out;

  &:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  }

  &.assigned {
    background: #eff6ff;
    border-color: #3b82f6;
  }
}

.permission-category {
  background: #f8fafc;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #6b7280;
}

.permission-name {
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.25rem;
}

.permission-description {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

.role-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.role-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.role-description {
  color: #6b7280;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.permission-count {
  background: #3b82f6;
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
}

.search-input {
  transition: all 0.2s ease-in-out;

  &:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    border-color: #3b82f6;
  }
}

.filter-tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1rem;
}

.filter-tab {
  padding: 0.75rem 1rem;
  border-bottom: 2px solid transparent;
  color: #6b7280;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;

  &:hover {
    color: #3b82f6;
  }

  &.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
  }
}

.action-button {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
  }

  &.primary {
    background: #3b82f6;
    color: white;

    &:hover {
      background: #2563eb;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
  }

  &.secondary {
    background: #f3f4f6;
    color: #374151;

    &:hover {
      background: #e5e7eb;
    }
  }

  &.danger {
    background: #ef4444;
    color: white;

    &:hover {
      background: #dc2626;
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    }
  }
}

.modal-overlay {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-height: 90vh;
  overflow-y: auto;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 0.75rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

.success-message {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #166534;
  padding: 0.75rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

// Responsive design
@media (max-width: 768px) {
  .permission-container {
    padding: 1rem;
  }

  .permission-grid {
    grid-template-columns: 1fr;
  }

  .role-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .filter-tabs {
    overflow-x: auto;
    white-space: nowrap;
  }

  .modal-content {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }
}
