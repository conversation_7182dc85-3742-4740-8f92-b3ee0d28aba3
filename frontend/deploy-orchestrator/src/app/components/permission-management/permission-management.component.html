<div class="permission-management">
  <!-- Header -->
  <div class="bg-white shadow">
    <div class="px-4 sm:px-6 lg:px-8">
      <div class="py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Permission Management</h1>
            <p class="mt-2 text-sm text-gray-700">
              Manage system permissions and their categories. Permissions define what actions users can perform.
            </p>
          </div>
          <div class="flex space-x-3">
            <button
              (click)="refreshData()"
              class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <svg class="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
            <button
              (click)="openCreateDialog()"
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <svg class="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Create Permission
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="bg-gray-50 px-4 sm:px-6 lg:px-8 py-4">
    <div class="flex flex-wrap items-center gap-4">
      <div class="flex-1 min-w-0">
        <label for="search" class="block text-sm font-medium text-gray-700">Search Permissions</label>
        <input
          type="text"
          id="search"
          [(ngModel)]="searchTerm"
          placeholder="Search by name or description..."
          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
      </div>
      <div class="flex-1 min-w-0">
        <label for="category-filter" class="block text-sm font-medium text-gray-700">Filter by Category</label>
        <select
          id="category-filter"
          [(ngModel)]="selectedCategory"
          class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
          <option value="">All Categories</option>
          <option *ngFor="let category of categories" [value]="category">{{ category | titlecase }}</option>
        </select>
      </div>
      <div class="flex items-end">
        <button
          (click)="clearFilters()"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
          Clear Filters
        </button>
      </div>
    </div>
  </div>

  <!-- Success/Error Messages -->
  <div *ngIf="success" class="bg-green-50 border border-green-200 rounded-md p-4 mx-4 sm:mx-6 lg:mx-8 mt-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <p class="text-sm text-green-800">{{ success }}</p>
      </div>
      <div class="ml-auto pl-3">
        <button (click)="success = null" class="text-green-400 hover:text-green-600">
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <div *ngIf="error" class="bg-red-50 border border-red-200 rounded-md p-4 mx-4 sm:mx-6 lg:mx-8 mt-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <p class="text-sm text-red-800">{{ error }}</p>
      </div>
      <div class="ml-auto pl-3">
        <button (click)="error = null" class="text-red-400 hover:text-red-600">
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center items-center py-12">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
    <span class="ml-3 text-gray-600">Loading permissions...</span>
  </div>

  <!-- Permissions Grid -->
  <div *ngIf="!loading" class="mx-4 sm:mx-6 lg:mx-8 mt-6">
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
      <div *ngFor="let permission of paginatedPermissions" class="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow duration-200">
        <div class="p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-10 w-10 rounded-lg flex items-center justify-center" [ngClass]="getCategoryColor(permission.category)">
                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="getPermissionIcon(permission.category)" />
                </svg>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 truncate">{{ permission.name }}</h3>
                <div class="relative">
                  <button
                    type="button"
                    class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
                    (click)="$event.stopPropagation()">
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                    </svg>
                  </button>
                  <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden">
                    <div class="py-1">
                      <button
                        (click)="openEditDialog(permission)"
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Edit Permission
                      </button>
                      <button
                        (click)="deletePermission(permission)"
                        class="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50">
                        Delete Permission
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <p class="text-sm text-gray-500 mt-1">{{ permission.description }}</p>
              <div class="mt-3 flex items-center justify-between">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" [ngClass]="getCategoryColor(permission.category)">
                  {{ permission.category | titlecase }}
                </span>
                <div class="flex space-x-2">
                  <button
                    (click)="openEditDialog(permission)"
                    class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                    Edit
                  </button>
                  <button
                    (click)="deletePermission(permission)"
                    class="text-red-600 hover:text-red-900 text-sm font-medium">
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="filteredPermissions.length === 0" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No permissions found</h3>
      <p class="mt-1 text-sm text-gray-500">
        <span *ngIf="searchTerm || selectedCategory">Try adjusting your filters or </span>
        <span>get started by creating a new permission.</span>
      </p>
      <div class="mt-6">
        <button
          (click)="openCreateDialog()"
          class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
          <svg class="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Create Permission
        </button>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="!loading && totalPages > 1" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mx-4 sm:mx-6 lg:mx-8 mt-6">
    <div class="flex-1 flex justify-between sm:hidden">
      <button
        [disabled]="currentPage === 1"
        (click)="onPageChange(currentPage - 1)"
        class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
        Previous
      </button>
      <button
        [disabled]="currentPage === totalPages"
        (click)="onPageChange(currentPage + 1)"
        class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
        Next
      </button>
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
      <div>
        <p class="text-sm text-gray-700">
          Showing
          <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span>
          to
          <span class="font-medium">{{ Math.min(currentPage * pageSize, filteredPermissions.length) }}</span>
          of
          <span class="font-medium">{{ filteredPermissions.length }}</span>
          results
        </p>
      </div>
      <div>
        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
          <button
            [disabled]="currentPage === 1"
            (click)="onPageChange(currentPage - 1)"
            class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            <span class="sr-only">Previous</span>
            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
          </button>
          <button
            *ngFor="let page of paginationPages"
            (click)="onPageChange(page)"
            [class]="page === currentPage ? 
              'z-10 bg-indigo-50 border-indigo-500 text-indigo-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium' :
              'bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium'">
            {{ page }}
          </button>
          <button
            [disabled]="currentPage === totalPages"
            (click)="onPageChange(currentPage + 1)"
            class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            <span class="sr-only">Next</span>
            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </button>
        </nav>
      </div>
    </div>
  </div>
</div>

<!-- Create/Edit Permission Dialog -->
<div *ngIf="showCreateDialog" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
  <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" (click)="closeDialog()"></div>
    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
      <form [formGroup]="permissionForm" (ngSubmit)="savePermission()">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                {{ editingPermission ? 'Edit Permission' : 'Create Permission' }}
              </h3>
              <div class="mt-4 space-y-4">
                <div>
                  <label for="permission-name" class="block text-sm font-medium text-gray-700">Permission Name</label>
                  <input
                    type="text"
                    id="permission-name"
                    formControlName="name"
                    placeholder="e.g., user:create, project:read"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    [class.border-red-300]="nameError">
                  <p *ngIf="nameError" class="mt-1 text-sm text-red-600">{{ nameError }}</p>
                  <p class="mt-1 text-xs text-gray-500">Format: category:action (lowercase, use underscores for multi-word actions)</p>
                </div>

                <div>
                  <label for="permission-description" class="block text-sm font-medium text-gray-700">Description</label>
                  <textarea
                    id="permission-description"
                    formControlName="description"
                    rows="3"
                    placeholder="Describe what this permission allows users to do"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    [class.border-red-300]="descriptionError"></textarea>
                  <p *ngIf="descriptionError" class="mt-1 text-sm text-red-600">{{ descriptionError }}</p>
                </div>

                <div>
                  <label for="permission-category" class="block text-sm font-medium text-gray-700">Category</label>
                  <select
                    id="permission-category"
                    formControlName="category"
                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                    [class.border-red-300]="categoryError">
                    <option value="">Select a category</option>
                    <option value="user">User</option>
                    <option value="project">Project</option>
                    <option value="environment">Environment</option>
                    <option value="workflow">Workflow</option>
                    <option value="template">Template</option>
                    <option value="secret">Secret</option>
                    <option value="audit">Audit</option>
                    <option value="monitoring">Monitoring</option>
                    <option value="admin">Admin</option>
                  </select>
                  <p *ngIf="categoryError" class="mt-1 text-sm text-red-600">{{ categoryError }}</p>
                </div>

                <!-- Error in Dialog -->
                <div *ngIf="error" class="bg-red-50 border border-red-200 rounded-md p-3">
                  <p class="text-sm text-red-800">{{ error }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            type="submit"
            [disabled]="permissionForm.invalid"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed">
            {{ editingPermission ? 'Update' : 'Create' }} Permission
          </button>
          <button
            type="button"
            (click)="closeDialog()"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
