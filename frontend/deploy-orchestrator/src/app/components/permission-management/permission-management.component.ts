import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';

import { AuthService } from '../../services/auth.service';
import { AdminService } from '../../services/admin.service';

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  createdAt: string;
  updatedAt: string;
}

interface PermissionsByCategory {
  [category: string]: Permission[];
}

@Component({
  selector: 'app-permission-management',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './permission-management.component.html',
  styleUrls: ['./permission-management.component.scss']
})
export class PermissionManagementComponent implements OnInit {
  permissions: Permission[] = [];
  permissionsByCategory: PermissionsByCategory = {};
  categories: string[] = [];

  // UI state
  loading = false;
  error: string | null = null;
  success: string | null = null;
  showCreateDialog = false;
  editingPermission: Permission | null = null;

  // Form
  permissionForm: FormGroup;

  // Filters
  selectedCategory = '';
  searchTerm = '';

  // Pagination
  currentPage = 1;
  pageSize = 20;
  totalPermissions = 0;

  constructor(
    private authService: AuthService,
    private adminService: AdminService,
    private formBuilder: FormBuilder,
    private router: Router
  ) {
    this.permissionForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.pattern(/^[a-z]+:[a-z_]+$/)]],
      description: ['', [Validators.required, Validators.minLength(3)]],
      category: ['', [Validators.required]]
    });
  }

  ngOnInit(): void {
    this.checkPermissions();
    this.loadPermissions();
  }

  private checkPermissions(): void {
    if (!this.authService.isAdmin()) {
      this.router.navigate(['/dashboard']);
      return;
    }
  }

  private async loadPermissions(): Promise<void> {
    this.loading = true;
    this.error = null;

    try {
      const response = await this.adminService.getPermissions().toPromise();
      // Map the response to match our interface
      this.permissions = (response || []).map((p: any) => ({
        id: p.id,
        name: p.name,
        description: p.description,
        category: p.category || 'general',
        createdAt: p.createdAt || new Date().toISOString(),
        updatedAt: p.updatedAt || new Date().toISOString()
      }));
      this.organizePermissionsByCategory();
      this.totalPermissions = this.permissions.length;
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to load permissions';
    } finally {
      this.loading = false;
    }
  }

  private organizePermissionsByCategory(): void {
    this.permissionsByCategory = {};
    this.categories = [];

    this.permissions.forEach(permission => {
      const category = permission.category || 'other';
      if (!this.permissionsByCategory[category]) {
        this.permissionsByCategory[category] = [];
        this.categories.push(category);
      }
      this.permissionsByCategory[category].push(permission);
    });

    // Sort categories
    this.categories.sort();
  }

  // Permission Management
  openCreateDialog(): void {
    this.editingPermission = null;
    this.permissionForm.reset();
    this.showCreateDialog = true;
    this.error = null;
    this.success = null;
  }

  openEditDialog(permission: Permission): void {
    this.editingPermission = permission;
    this.permissionForm.patchValue({
      name: permission.name,
      description: permission.description,
      category: permission.category
    });
    this.showCreateDialog = true;
    this.error = null;
    this.success = null;
  }

  closeDialog(): void {
    this.showCreateDialog = false;
    this.editingPermission = null;
    this.permissionForm.reset();
    this.error = null;
    this.success = null;
  }

  async savePermission(): Promise<void> {
    if (this.permissionForm.invalid) {
      this.error = 'Please fill in all required fields correctly';
      return;
    }

    const formValue = this.permissionForm.value;

    try {
      if (this.editingPermission) {
        // Update existing permission
        await this.adminService.updatePermission({
          ...this.editingPermission,
          ...formValue
        }).toPromise();
        this.success = 'Permission updated successfully';
      } else {
        // Create new permission
        await this.adminService.createPermission(formValue).toPromise();
        this.success = 'Permission created successfully';
      }

      this.closeDialog();
      this.loadPermissions();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to save permission';
    }
  }

  async deletePermission(permission: Permission): Promise<void> {
    if (!confirm(`Are you sure you want to delete permission "${permission.name}"?`)) {
      return;
    }

    try {
      await this.adminService.deletePermission(permission.id).toPromise();
      this.success = 'Permission deleted successfully';
      this.loadPermissions();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to delete permission';
    }
  }

  // Filtering and Search
  get filteredPermissions(): Permission[] {
    let filtered = this.permissions;

    // Filter by category
    if (this.selectedCategory) {
      filtered = filtered.filter(p => p.category === this.selectedCategory);
    }

    // Filter by search term
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(p =>
        p.name.toLowerCase().includes(term) ||
        p.description.toLowerCase().includes(term)
      );
    }

    return filtered;
  }

  get paginatedPermissions(): Permission[] {
    const filtered = this.filteredPermissions;
    const start = (this.currentPage - 1) * this.pageSize;
    const end = start + this.pageSize;
    return filtered.slice(start, end);
  }

  get totalPages(): number {
    return Math.ceil(this.filteredPermissions.length / this.pageSize);
  }

  get paginationPages(): number[] {
    const pages = [];
    const start = Math.max(1, this.currentPage - 2);
    const end = Math.min(this.totalPages, this.currentPage + 2);

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    return pages;
  }

  onPageChange(page: number): void {
    this.currentPage = page;
  }

  clearFilters(): void {
    this.selectedCategory = '';
    this.searchTerm = '';
    this.currentPage = 1;
  }

  // UI Helpers
  getPermissionIcon(category: string): string {
    const icons: { [key: string]: string } = {
      'user': 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
      'project': 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4',
      'environment': 'M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01',
      'workflow': 'M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h2m0-8h2m-2 0V3m0 4v6m2-6h2a2 2 0 012 2v6a2 2 0 01-2 2h-2m0-8v8',
      'template': 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
      'secret': 'M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z',
      'audit': 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
      'monitoring': 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
      'admin': 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z'
    };
    return icons[category] || icons['admin'];
  }

  getCategoryColor(category: string): string {
    const colors: { [key: string]: string } = {
      'user': 'bg-blue-100 text-blue-800',
      'project': 'bg-green-100 text-green-800',
      'environment': 'bg-purple-100 text-purple-800',
      'workflow': 'bg-yellow-100 text-yellow-800',
      'template': 'bg-pink-100 text-pink-800',
      'secret': 'bg-red-100 text-red-800',
      'audit': 'bg-gray-100 text-gray-800',
      'monitoring': 'bg-indigo-100 text-indigo-800',
      'admin': 'bg-orange-100 text-orange-800'
    };
    return colors[category] || colors['admin'];
  }

  refreshData(): void {
    this.loadPermissions();
  }

  // Math helper for templates
  Math = Math;

  // Form validation helpers
  get nameError(): string | null {
    const control = this.permissionForm.get('name');
    if (control?.errors && control.touched) {
      if (control.errors['required']) return 'Permission name is required';
      if (control.errors['pattern']) return 'Permission name must follow format: category:action (e.g., user:create)';
    }
    return null;
  }

  get descriptionError(): string | null {
    const control = this.permissionForm.get('description');
    if (control?.errors && control.touched) {
      if (control.errors['required']) return 'Description is required';
      if (control.errors['minlength']) return 'Description must be at least 3 characters';
    }
    return null;
  }

  get categoryError(): string | null {
    const control = this.permissionForm.get('category');
    if (control?.errors && control.touched) {
      if (control.errors['required']) return 'Category is required';
    }
    return null;
  }
}
