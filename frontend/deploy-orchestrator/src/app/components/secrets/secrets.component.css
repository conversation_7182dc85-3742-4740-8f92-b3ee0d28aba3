/* Custom styles for secrets component */

.secret-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow;
}

.secret-header {
  @apply flex justify-between items-start mb-3;
}

.secret-title {
  @apply text-lg font-semibold text-gray-900;
}

.secret-description {
  @apply text-sm text-gray-600 mt-1;
}

.secret-meta {
  @apply flex flex-wrap gap-2 mt-3;
}

.secret-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.secret-actions {
  @apply flex space-x-2;
}

.action-button {
  @apply px-3 py-1 text-sm font-medium rounded-md transition-colors;
}

.action-button-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.action-button-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

.action-button-danger {
  @apply bg-red-600 text-white hover:bg-red-700;
}

.action-button-success {
  @apply bg-green-600 text-white hover:bg-green-700;
}

/* Modal styles */
.modal-overlay {
  @apply fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50;
}

.modal-container {
  @apply relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white;
}

.modal-header {
  @apply flex justify-between items-center mb-4;
}

.modal-title {
  @apply text-lg font-medium text-gray-900;
}

.modal-close {
  @apply text-gray-400 hover:text-gray-600 transition-colors;
}

.modal-body {
  @apply space-y-4;
}

.modal-footer {
  @apply flex justify-end space-x-3 pt-4 border-t border-gray-200;
}

/* Form styles */
.form-group {
  @apply space-y-1;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.form-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical;
}

.form-checkbox {
  @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

/* Tag styles */
.tag-container {
  @apply flex flex-wrap gap-2 mb-2;
}

.tag {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800;
}

.tag-remove {
  @apply ml-1 text-blue-600 hover:text-blue-800 cursor-pointer;
}

.tag-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

/* Status badges */
.status-active {
  @apply bg-green-100 text-green-800;
}

.status-inactive {
  @apply bg-gray-100 text-gray-800;
}

.status-expired {
  @apply bg-red-100 text-red-800;
}

.status-rotating {
  @apply bg-yellow-100 text-yellow-800;
}

.status-pending {
  @apply bg-blue-100 text-blue-800;
}

/* Provider badges */
.provider-internal {
  @apply bg-blue-100 text-blue-800;
}

.provider-vault {
  @apply bg-purple-100 text-purple-800;
}

.provider-conjur {
  @apply bg-indigo-100 text-indigo-800;
}

.provider-aws {
  @apply bg-orange-100 text-orange-800;
}

.provider-azure {
  @apply bg-cyan-100 text-cyan-800;
}

.provider-gcp {
  @apply bg-green-100 text-green-800;
}

/* Type badges */
.type-generic {
  @apply bg-gray-100 text-gray-800;
}

.type-password {
  @apply bg-red-100 text-red-800;
}

.type-api-key {
  @apply bg-yellow-100 text-yellow-800;
}

.type-certificate {
  @apply bg-purple-100 text-purple-800;
}

.type-ssh-key {
  @apply bg-green-100 text-green-800;
}

.type-database {
  @apply bg-blue-100 text-blue-800;
}

.type-oauth {
  @apply bg-indigo-100 text-indigo-800;
}

.type-jwt {
  @apply bg-pink-100 text-pink-800;
}

/* Loading states */
.loading-spinner {
  @apply inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

.loading-text {
  @apply mt-2 text-gray-600;
}

/* Empty states */
.empty-state {
  @apply text-center py-8;
}

.empty-state-icon {
  @apply mx-auto h-12 w-12 text-gray-400;
}

.empty-state-title {
  @apply mt-2 text-sm font-medium text-gray-900;
}

.empty-state-description {
  @apply mt-1 text-sm text-gray-500;
}

/* Pagination */
.pagination {
  @apply flex items-center justify-between mt-6;
}

.pagination-info {
  @apply text-sm text-gray-700;
}

.pagination-controls {
  @apply flex space-x-2;
}

.pagination-button {
  @apply px-3 py-2 text-sm font-medium rounded-md transition-colors;
}

.pagination-button-active {
  @apply bg-blue-600 text-white;
}

.pagination-button-inactive {
  @apply text-gray-500 bg-white border border-gray-300 hover:bg-gray-50;
}

.pagination-button-disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-container {
    @apply w-11/12 top-10;
  }
  
  .secret-actions {
    @apply flex-col space-x-0 space-y-2;
  }
  
  .action-button {
    @apply w-full text-center;
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .secret-card {
    @apply bg-gray-800 border-gray-700;
  }
  
  .secret-title {
    @apply text-gray-100;
  }
  
  .secret-description {
    @apply text-gray-300;
  }
  
  .modal-container {
    @apply bg-gray-800 border-gray-700;
  }
  
  .modal-title {
    @apply text-gray-100;
  }
  
  .form-input,
  .form-select,
  .form-textarea {
    @apply bg-gray-700 border-gray-600 text-gray-100;
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Focus styles for accessibility */
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  @apply ring-2 ring-blue-500 ring-opacity-50;
}

.action-button:focus {
  @apply outline-none ring-2 ring-offset-2 ring-blue-500;
}

/* Hover effects */
.secret-card:hover {
  @apply shadow-lg transform translate-y-[-1px] transition-all duration-200;
}

.action-button:hover {
  @apply transform translate-y-[-1px] transition-all duration-200;
}

/* Custom scrollbar for modals */
.modal-container {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.modal-container::-webkit-scrollbar {
  width: 6px;
}

.modal-container::-webkit-scrollbar-track {
  background: #f7fafc;
}

.modal-container::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.modal-container::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}
