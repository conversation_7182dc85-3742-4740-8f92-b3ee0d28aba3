import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { SecretsService, Secret, ProjectSecretBinding, SecretVariable, SecretScope, Provider } from '../../services/secrets.service';
import { ProjectService } from '../../services/project.service';
import { Project } from '../../models/project.model';

interface RotationPolicy {
  id: string;
  name: string;
  description: string;
  interval: string;
  strategy: string;
}

@Component({
  selector: 'app-secrets',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './secrets.component.html',
  styleUrls: ['./secrets.component.css']
})
export class SecretsComponent implements OnInit {
  secrets: Secret[] = [];
  scopes: SecretScope[] = [];
  providers: Provider[] = [];
  rotationPolicies: RotationPolicy[] = [];
  projects: Project[] = [];

  loading = false;
  error: string | null = null;

  // Filters
  selectedScope = '';
  selectedProvider = '';
  selectedType = '';
  selectedProject = '';
  searchTerm = '';

  // Modal states
  showCreateModal = false;
  showEditModal = false;
  showViewModal = false;
  showRotateModal = false;
  showBindModal = false;
  showVariableModal = false;
  selectedSecret: Secret | null = null;

  // Project binding
  bindingForm = {
    projectId: '',
    variableName: '',
    variableType: 'env',
    accessLevel: 'read',
    environments: [] as string[],
    services: [] as string[],
    description: ''
  };

  // Variable creation
  variableForm = {
    projectId: '',
    name: '',
    type: 'env',
    path: '',
    format: 'plain',
    environment: '',
    service: '',
    namespace: '',
    transform: {}
  };

  // Form data
  newSecret = {
    name: '',
    description: '',
    scopeId: '',
    type: 'generic',
    provider: 'internal',
    value: '',
    tags: [] as string[],
    metadata: {},
    rotationPolicyId: '',
    requiresApproval: false,
    expiresAt: ''
  };

  editSecret = {
    description: '',
    value: '',
    tags: [] as string[],
    metadata: {},
    rotationPolicyId: '',
    requiresApproval: false,
    expiresAt: ''
  };

  secretTypes = [
    { value: 'generic', label: 'Generic' },
    { value: 'password', label: 'Password' },
    { value: 'api_key', label: 'API Key' },
    { value: 'certificate', label: 'Certificate' },
    { value: 'ssh_key', label: 'SSH Key' },
    { value: 'database', label: 'Database' },
    { value: 'oauth', label: 'OAuth' },
    { value: 'jwt', label: 'JWT' }
  ];

  variableTypes = [
    { value: 'env', label: 'Environment Variable' },
    { value: 'file', label: 'Configuration File' },
    { value: 'config', label: 'Configuration Object' },
    { value: 'mount', label: 'Volume Mount' }
  ];

  accessLevels = [
    { value: 'read', label: 'Read Only' },
    { value: 'write', label: 'Read/Write' },
    { value: 'admin', label: 'Administrator' }
  ];

  formatTypes = [
    { value: 'plain', label: 'Plain Text' },
    { value: 'json', label: 'JSON' },
    { value: 'yaml', label: 'YAML' },
    { value: 'base64', label: 'Base64' },
    { value: 'xml', label: 'XML' }
  ];

  // Pagination
  currentPage = 1;
  pageSize = 20;
  totalSecrets = 0;

  constructor(
    private router: Router,
    private secretsService: SecretsService,
    private projectService: ProjectService
  ) {}

  ngOnInit() {
    this.loadSecrets();
    this.loadScopes();
    this.loadProviders();
    this.loadRotationPolicies();
    this.loadProjects();
  }

  async loadSecrets() {
    this.loading = true;
    this.error = null;

    try {
      const params: any = {
        limit: this.pageSize,
        offset: (this.currentPage - 1) * this.pageSize
      };

      if (this.selectedScope) params.scopeId = this.selectedScope;

      const data = await this.secretsService.getSecrets(params).toPromise();
      this.secrets = data?.secrets || [];
      this.totalSecrets = data?.total || 0;
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to load secrets';
      console.error('Error loading secrets:', error);
    } finally {
      this.loading = false;
    }
  }

  async loadScopes() {
    try {
      const data = await this.secretsService.getScopes().toPromise();
      this.scopes = data?.scopes || [];
    } catch (error) {
      console.error('Error loading scopes:', error);
    }
  }

  async loadProviders() {
    try {
      const data = await this.secretsService.getProviders().toPromise();
      this.providers = data?.providers || [];
    } catch (error) {
      console.error('Error loading providers:', error);
    }
  }

  async loadRotationPolicies() {
    try {
      const data = await this.secretsService.getRotationPolicies().toPromise();
      this.rotationPolicies = data?.policies || [];
    } catch (error) {
      console.error('Error loading rotation policies:', error);
    }
  }

  async loadProjects() {
    try {
      const projects = await this.projectService.getProjects().toPromise();
      this.projects = projects || [];
    } catch (error) {
      console.error('Error loading projects:', error);
    }
  }

  get filteredSecrets() {
    return this.secrets.filter(secret => {
      const matchesSearch = !this.searchTerm ||
        secret.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        secret.description.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesProvider = !this.selectedProvider || secret.provider === this.selectedProvider;
      const matchesType = !this.selectedType || secret.type === this.selectedType;

      return matchesSearch && matchesProvider && matchesType;
    });
  }

  openCreateModal() {
    this.newSecret = {
      name: '',
      description: '',
      scopeId: this.scopes.length > 0 ? this.scopes[0].id : '',
      type: 'generic',
      provider: 'internal',
      value: '',
      tags: [],
      metadata: {},
      rotationPolicyId: '',
      requiresApproval: false,
      expiresAt: ''
    };
    this.showCreateModal = true;
  }

  closeCreateModal() {
    this.showCreateModal = false;
  }

  async createSecret() {
    try {
      const secretData = {
        ...this.newSecret,
        rotationPolicyId: this.newSecret.rotationPolicyId || null,
        expiresAt: this.newSecret.expiresAt || null
      };

      await this.secretsService.createSecret(secretData).toPromise();
      this.closeCreateModal();
      this.loadSecrets();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to create secret';
    }
  }

  async viewSecret(secret: Secret) {
    try {
      this.selectedSecret = await this.secretsService.getSecret(secret.id).toPromise();
      this.showViewModal = true;
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to load secret';
    }
  }

  openEditModal(secret: Secret) {
    this.selectedSecret = secret;
    this.editSecret = {
      description: secret.description,
      value: '',
      tags: [...secret.tags],
      metadata: { ...secret.metadata },
      rotationPolicyId: secret.rotationPolicy?.id || '',
      requiresApproval: secret.requiresApproval,
      expiresAt: secret.expiresAt || ''
    };
    this.showEditModal = true;
  }

  async updateSecret() {
    if (!this.selectedSecret) return;

    try {
      const updateData: any = {
        description: this.editSecret.description,
        tags: this.editSecret.tags,
        metadata: this.editSecret.metadata,
        rotationPolicyId: this.editSecret.rotationPolicyId || null,
        requiresApproval: this.editSecret.requiresApproval,
        expiresAt: this.editSecret.expiresAt || null
      };

      if (this.editSecret.value) {
        updateData.value = this.editSecret.value;
      }

      await this.secretsService.updateSecret(this.selectedSecret.id, updateData).toPromise();
      this.showEditModal = false;
      this.loadSecrets();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to update secret';
    }
  }

  async rotateSecret(secret: Secret) {
    try {
      await this.secretsService.rotateSecret(secret.id).toPromise();
      this.loadSecrets();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to rotate secret';
    }
  }

  async deleteSecret(secret: Secret) {
    if (!confirm(`Are you sure you want to delete the secret "${secret.name}"?`)) {
      return;
    }

    try {
      await this.secretsService.deleteSecret(secret.id).toPromise();
      this.loadSecrets();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to delete secret';
    }
  }

  onFilterChange() {
    this.currentPage = 1;
    this.loadSecrets();
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.loadSecrets();
  }

  get totalPages() {
    return Math.ceil(this.totalSecrets / this.pageSize);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'expired': return 'bg-red-100 text-red-800';
      case 'rotating': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  getProviderBadgeClass(provider: string): string {
    switch (provider) {
      case 'internal': return 'bg-blue-100 text-blue-800';
      case 'vault': return 'bg-purple-100 text-purple-800';
      case 'conjur': return 'bg-indigo-100 text-indigo-800';
      case 'aws': return 'bg-orange-100 text-orange-800';
      case 'azure': return 'bg-cyan-100 text-cyan-800';
      case 'gcp': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  addTag(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault();
      const input = event.target as HTMLInputElement;
      const tag = input.value.trim();
      if (tag && !this.newSecret.tags.includes(tag)) {
        this.newSecret.tags.push(tag);
        input.value = '';
      }
    }
  }

  removeTag(index: number) {
    this.newSecret.tags.splice(index, 1);
  }

  addEditTag(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault();
      const input = event.target as HTMLInputElement;
      const tag = input.value.trim();
      if (tag && !this.editSecret.tags.includes(tag)) {
        this.editSecret.tags.push(tag);
        input.value = '';
      }
    }
  }

  removeEditTag(index: number) {
    this.editSecret.tags.splice(index, 1);
  }

  // Project binding methods
  openBindModal(secret: Secret) {
    this.selectedSecret = secret;
    this.bindingForm = {
      projectId: this.projects.length > 0 ? this.projects[0].id : '',
      variableName: secret.name.toUpperCase().replace(/[^A-Z0-9]/g, '_'),
      variableType: 'env',
      accessLevel: 'read',
      environments: [],
      services: [],
      description: ''
    };
    this.showBindModal = true;
  }

  async bindSecretToProject() {
    if (!this.selectedSecret) return;

    try {
      const bindingData = {
        secretId: this.selectedSecret.id,
        variableName: this.bindingForm.variableName,
        variableType: this.bindingForm.variableType,
        accessLevel: this.bindingForm.accessLevel,
        environments: this.bindingForm.environments,
        services: this.bindingForm.services,
        description: this.bindingForm.description
      };

      await this.secretsService.bindSecretToProject(this.bindingForm.projectId, bindingData).toPromise();
      this.showBindModal = false;
      this.loadSecrets();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to bind secret to project';
    }
  }

  // Variable management methods
  openVariableModal(secret: Secret) {
    this.selectedSecret = secret;
    this.variableForm = {
      projectId: this.projects.length > 0 ? this.projects[0].id : '',
      name: secret.name.toUpperCase().replace(/[^A-Z0-9]/g, '_'),
      type: 'env',
      path: '',
      format: 'plain',
      environment: '',
      service: '',
      namespace: '',
      transform: {}
    };
    this.showVariableModal = true;
  }

  async createSecretVariable() {
    if (!this.selectedSecret) return;

    try {
      const variableData = {
        secretId: this.selectedSecret.id,
        name: this.variableForm.name,
        type: this.variableForm.type,
        path: this.variableForm.path,
        format: this.variableForm.format,
        environment: this.variableForm.environment,
        service: this.variableForm.service,
        namespace: this.variableForm.namespace,
        transform: this.variableForm.transform
      };

      await this.secretsService.createSecretVariable(this.variableForm.projectId, variableData).toPromise();
      this.showVariableModal = false;
      this.loadSecrets();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to create secret variable';
    }
  }

  async getProjectSecrets(projectId: string) {
    try {
      const data = await this.secretsService.getProjectSecrets(projectId).toPromise();
      return data?.secrets || [];
    } catch (error) {
      console.error('Error loading project secrets:', error);
      return [];
    }
  }

  // Environment and service management
  addEnvironment(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault();
      const input = event.target as HTMLInputElement;
      const env = input.value.trim();
      if (env && !this.bindingForm.environments.includes(env)) {
        this.bindingForm.environments.push(env);
        input.value = '';
      }
    }
  }

  removeEnvironment(index: number) {
    this.bindingForm.environments.splice(index, 1);
  }

  addService(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault();
      const input = event.target as HTMLInputElement;
      const service = input.value.trim();
      if (service && !this.bindingForm.services.includes(service)) {
        this.bindingForm.services.push(service);
        input.value = '';
      }
    }
  }

  removeService(index: number) {
    this.bindingForm.services.splice(index, 1);
  }

  // Utility methods
  getVariableTypeBadgeClass(type: string): string {
    switch (type) {
      case 'env': return 'bg-blue-100 text-blue-800';
      case 'file': return 'bg-green-100 text-green-800';
      case 'config': return 'bg-purple-100 text-purple-800';
      case 'mount': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  getAccessLevelBadgeClass(level: string): string {
    switch (level) {
      case 'read': return 'bg-green-100 text-green-800';
      case 'write': return 'bg-yellow-100 text-yellow-800';
      case 'admin': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }
}
