<div class="p-6">
  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Secrets Management</h1>
      <p class="text-gray-600">Manage and secure your application secrets</p>
    </div>
    <button
      (click)="openCreateModal()"
      class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
      </svg>
      Create Secret
    </button>
  </div>

  <!-- Error Alert -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
    {{ error }}
    <button (click)="error = null" class="float-right text-red-500 hover:text-red-700">×</button>
  </div>

  <!-- Filters -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (input)="onFilterChange()"
          placeholder="Search secrets..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Scope</label>
        <select
          [(ngModel)]="selectedScope"
          (change)="onFilterChange()"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option value="">All Scopes</option>
          <option *ngFor="let scope of scopes" [value]="scope.id">{{ scope.name }}</option>
        </select>
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Provider</label>
        <select
          [(ngModel)]="selectedProvider"
          (change)="onFilterChange()"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option value="">All Providers</option>
          <option *ngFor="let provider of providers" [value]="provider.type">{{ provider.name }}</option>
        </select>
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
        <select
          [(ngModel)]="selectedType"
          (change)="onFilterChange()"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option value="">All Types</option>
          <option *ngFor="let type of secretTypes" [value]="type.value">{{ type.label }}</option>
        </select>
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Project</label>
        <select
          [(ngModel)]="selectedProject"
          (change)="onFilterChange()"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option value="">All Projects</option>
          <option *ngFor="let project of projects" [value]="project.id">{{ project.name }}</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="text-center py-8">
    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <p class="mt-2 text-gray-600">Loading secrets...</p>
  </div>

  <!-- Secrets Table -->
  <div *ngIf="!loading" class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Provider</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scope</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Rotated</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngFor="let secret of filteredSecrets" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
              <div>
                <div class="text-sm font-medium text-gray-900">{{ secret.name }}</div>
                <div class="text-sm text-gray-500">{{ secret.description }}</div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                {{ secret.type }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span [class]="'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ' + getProviderBadgeClass(secret.provider)">
                {{ secret.provider }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ secret.scope?.name || 'Unknown' }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span [class]="'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ' + getStatusBadgeClass(secret.status)">
                {{ secret.status }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ secret.lastRotated ? formatDate(secret.lastRotated) : 'Never' }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex flex-wrap gap-2">
                <button
                  (click)="viewSecret(secret)"
                  class="text-blue-600 hover:text-blue-900">
                  View
                </button>
                <button
                  (click)="openEditModal(secret)"
                  class="text-indigo-600 hover:text-indigo-900">
                  Edit
                </button>
                <button
                  (click)="openBindModal(secret)"
                  class="text-purple-600 hover:text-purple-900">
                  Bind to Project
                </button>
                <button
                  (click)="openVariableModal(secret)"
                  class="text-teal-600 hover:text-teal-900">
                  Create Variable
                </button>
                <button
                  (click)="rotateSecret(secret)"
                  class="text-green-600 hover:text-green-900">
                  Rotate
                </button>
                <button
                  (click)="deleteSecret(secret)"
                  class="text-red-600 hover:text-red-900">
                  Delete
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <div *ngIf="filteredSecrets.length === 0" class="text-center py-8">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No secrets found</h3>
      <p class="mt-1 text-sm text-gray-500">Get started by creating a new secret.</p>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="totalPages > 1" class="flex items-center justify-between mt-6">
    <div class="text-sm text-gray-700">
      Showing {{ (currentPage - 1) * pageSize + 1 }} to {{ Math.min(currentPage * pageSize, totalSecrets) }} of {{ totalSecrets }} secrets
    </div>
    <div class="flex space-x-2">
      <button
        (click)="onPageChange(currentPage - 1)"
        [disabled]="currentPage === 1"
        class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
        Previous
      </button>
      <button
        *ngFor="let page of [].constructor(totalPages); let i = index"
        (click)="onPageChange(i + 1)"
        [class]="'px-3 py-2 text-sm font-medium rounded-md ' + (currentPage === i + 1 ? 'bg-blue-600 text-white' : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50')">
        {{ i + 1 }}
      </button>
      <button
        (click)="onPageChange(currentPage + 1)"
        [disabled]="currentPage === totalPages"
        class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
        Next
      </button>
    </div>
  </div>
</div>

<!-- Create Secret Modal -->
<div *ngIf="showCreateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Create New Secret</h3>
        <button (click)="closeCreateModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <form (ngSubmit)="createSecret()" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Name *</label>
            <input
              type="text"
              [(ngModel)]="newSecret.name"
              name="name"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Type *</label>
            <select
              [(ngModel)]="newSecret.type"
              name="type"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option *ngFor="let type of secretTypes" [value]="type.value">{{ type.label }}</option>
            </select>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Scope *</label>
            <select
              [(ngModel)]="newSecret.scopeId"
              name="scopeId"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option *ngFor="let scope of scopes" [value]="scope.id">{{ scope.name }}</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Provider</label>
            <select
              [(ngModel)]="newSecret.provider"
              name="provider"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="internal">Internal</option>
              <option *ngFor="let provider of providers" [value]="provider.type">{{ provider.name }}</option>
            </select>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea
            [(ngModel)]="newSecret.description"
            name="description"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Secret Value *</label>
          <textarea
            [(ngModel)]="newSecret.value"
            name="value"
            required
            rows="4"
            placeholder="Enter the secret value..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Tags</label>
          <div class="flex flex-wrap gap-2 mb-2">
            <span *ngFor="let tag of newSecret.tags; let i = index"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {{ tag }}
              <button type="button" (click)="removeTag(i)" class="ml-1 text-blue-600 hover:text-blue-800">×</button>
            </span>
          </div>
          <input
            type="text"
            (keydown)="addTag($event)"
            placeholder="Press Enter to add tags..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Rotation Policy</label>
            <select
              [(ngModel)]="newSecret.rotationPolicyId"
              name="rotationPolicyId"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">No rotation policy</option>
              <option *ngFor="let policy of rotationPolicies" [value]="policy.id">{{ policy.name }}</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Expires At</label>
            <input
              type="datetime-local"
              [(ngModel)]="newSecret.expiresAt"
              name="expiresAt"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
        </div>

        <div class="flex items-center">
          <input
            type="checkbox"
            [(ngModel)]="newSecret.requiresApproval"
            name="requiresApproval"
            id="requiresApproval"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
          <label for="requiresApproval" class="ml-2 block text-sm text-gray-900">
            Requires approval for access
          </label>
        </div>

        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            (click)="closeCreateModal()"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md">
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md">
            Create Secret
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Bind Secret to Project Modal -->
<div *ngIf="showBindModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Bind Secret to Project</h3>
        <button (click)="showBindModal = false" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <form (ngSubmit)="bindSecretToProject()" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Project *</label>
            <select
              [(ngModel)]="bindingForm.projectId"
              name="projectId"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option *ngFor="let project of projects" [value]="project.id">{{ project.name }}</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Variable Name *</label>
            <input
              type="text"
              [(ngModel)]="bindingForm.variableName"
              name="variableName"
              required
              placeholder="e.g., DATABASE_PASSWORD"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Variable Type *</label>
            <select
              [(ngModel)]="bindingForm.variableType"
              name="variableType"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option *ngFor="let type of variableTypes" [value]="type.value">{{ type.label }}</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Access Level *</label>
            <select
              [(ngModel)]="bindingForm.accessLevel"
              name="accessLevel"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option *ngFor="let level of accessLevels" [value]="level.value">{{ level.label }}</option>
            </select>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea
            [(ngModel)]="bindingForm.description"
            name="description"
            rows="3"
            placeholder="Describe how this secret will be used in the project..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Environments</label>
          <div class="flex flex-wrap gap-2 mb-2">
            <span *ngFor="let env of bindingForm.environments; let i = index"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              {{ env }}
              <button type="button" (click)="removeEnvironment(i)" class="ml-1 text-green-600 hover:text-green-800">×</button>
            </span>
          </div>
          <input
            type="text"
            (keydown)="addEnvironment($event)"
            placeholder="Press Enter to add environments (e.g., production, staging)..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Services</label>
          <div class="flex flex-wrap gap-2 mb-2">
            <span *ngFor="let service of bindingForm.services; let i = index"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {{ service }}
              <button type="button" (click)="removeService(i)" class="ml-1 text-blue-600 hover:text-blue-800">×</button>
            </span>
          </div>
          <input
            type="text"
            (keydown)="addService($event)"
            placeholder="Press Enter to add services (e.g., web-service, api-service)..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            (click)="showBindModal = false"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md">
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md">
            Bind to Project
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Create Secret Variable Modal -->
<div *ngIf="showVariableModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Create Secret Variable</h3>
        <button (click)="showVariableModal = false" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <form (ngSubmit)="createSecretVariable()" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Project *</label>
            <select
              [(ngModel)]="variableForm.projectId"
              name="projectId"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option *ngFor="let project of projects" [value]="project.id">{{ project.name }}</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Variable Name *</label>
            <input
              type="text"
              [(ngModel)]="variableForm.name"
              name="name"
              required
              placeholder="e.g., DATABASE_PASSWORD"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Variable Type *</label>
            <select
              [(ngModel)]="variableForm.type"
              name="type"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option *ngFor="let type of variableTypes" [value]="type.value">{{ type.label }}</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Format</label>
            <select
              [(ngModel)]="variableForm.format"
              name="format"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option *ngFor="let format of formatTypes" [value]="format.value">{{ format.label }}</option>
            </select>
          </div>
        </div>

        <div *ngIf="variableForm.type === 'file' || variableForm.type === 'mount'">
          <label class="block text-sm font-medium text-gray-700 mb-1">File Path *</label>
          <input
            type="text"
            [(ngModel)]="variableForm.path"
            name="path"
            placeholder="e.g., /etc/app/config.json"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Environment</label>
            <input
              type="text"
              [(ngModel)]="variableForm.environment"
              name="environment"
              placeholder="e.g., production"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Service</label>
            <input
              type="text"
              [(ngModel)]="variableForm.service"
              name="service"
              placeholder="e.g., web-service"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Namespace</label>
            <input
              type="text"
              [(ngModel)]="variableForm.namespace"
              name="namespace"
              placeholder="e.g., default"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
        </div>

        <div class="bg-gray-50 p-4 rounded-lg">
          <h4 class="text-sm font-medium text-gray-700 mb-2">Variable Context</h4>
          <p class="text-xs text-gray-600 mb-2">
            Leave fields empty to make the variable available globally within the project.
            Specify environment, service, or namespace to restrict access.
          </p>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-2 text-xs">
            <div class="bg-white p-2 rounded border">
              <strong>Environment:</strong> {{ variableForm.environment || 'All environments' }}
            </div>
            <div class="bg-white p-2 rounded border">
              <strong>Service:</strong> {{ variableForm.service || 'All services' }}
            </div>
            <div class="bg-white p-2 rounded border">
              <strong>Namespace:</strong> {{ variableForm.namespace || 'All namespaces' }}
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            (click)="showVariableModal = false"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md">
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 text-sm font-medium text-white bg-teal-600 hover:bg-teal-700 rounded-md">
            Create Variable
          </button>
        </div>
      </form>
    </div>
  </div>
</div>