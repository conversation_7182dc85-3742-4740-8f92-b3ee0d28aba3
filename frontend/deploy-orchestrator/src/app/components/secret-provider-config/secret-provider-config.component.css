/* Secret Provider Configuration Styles */
.secret-provider-config {
  @apply p-6;
}

/* Provider Card Styles */
.provider-card {
  @apply bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow;
}

.provider-header {
  @apply flex justify-between items-start mb-4;
}

.provider-icon {
  @apply w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center;
}

.provider-info h3 {
  @apply text-lg font-semibold text-gray-900;
}

.provider-info p {
  @apply text-sm text-gray-600;
}

/* Status Badge Styles */
.status-badge {
  @apply px-2 py-1 text-xs font-medium rounded-full;
}

.status-active {
  @apply bg-green-100 text-green-800;
}

.status-inactive {
  @apply bg-red-100 text-red-800;
}

.status-testing {
  @apply bg-yellow-100 text-yellow-800;
}

/* Actions Menu */
.actions-menu {
  @apply absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10;
}

.actions-menu button {
  @apply w-full text-left px-4 py-2 text-sm hover:bg-gray-50 flex items-center gap-2;
}

.actions-menu button:first-child {
  @apply rounded-t-lg;
}

.actions-menu button:last-child {
  @apply rounded-b-lg;
}

/* Form Styles */
.form-group {
  @apply space-y-1;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.form-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.form-checkbox {
  @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

/* Button Styles */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg;
}

.btn-disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* Modal Styles */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-content {
  @apply bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto;
}

.modal-header {
  @apply flex justify-between items-center mb-6;
}

.modal-title {
  @apply text-xl font-semibold;
}

.modal-close {
  @apply text-gray-500 hover:text-gray-700;
}

/* Loading Spinner */
.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

/* Alert Styles */
.alert {
  @apply px-4 py-3 rounded-lg mb-4;
}

.alert-error {
  @apply bg-red-50 border border-red-200 text-red-700;
}

.alert-success {
  @apply bg-green-50 border border-green-200 text-green-700;
}

.alert-warning {
  @apply bg-yellow-50 border border-yellow-200 text-yellow-700;
}

.alert-info {
  @apply bg-blue-50 border border-blue-200 text-blue-700;
}

/* Empty State */
.empty-state {
  @apply text-center py-12;
}

.empty-state-icon {
  @apply w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4;
}

.empty-state-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.empty-state-description {
  @apply text-gray-600 mb-4;
}

/* Configuration Section */
.config-section {
  @apply space-y-4 mb-6;
}

.config-section-title {
  @apply text-lg font-medium text-gray-900;
}

.config-fields {
  @apply space-y-4;
}

/* Responsive Design */
@media (max-width: 768px) {
  .secret-provider-config {
    @apply p-4;
  }
  
  .modal-content {
    @apply max-w-full mx-4;
  }
  
  .provider-header {
    @apply flex-col items-start gap-3;
  }
  
  .actions-menu {
    @apply relative w-full mt-2;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Focus States */
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  @apply outline-none ring-2 ring-blue-500 border-blue-500;
}

/* Hover Effects */
.provider-card:hover {
  @apply shadow-lg;
}

.btn-primary:hover {
  @apply bg-blue-700;
}

.btn-secondary:hover {
  @apply bg-gray-200;
}

.btn-danger:hover {
  @apply bg-red-700;
}
