import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { SecretsService, Provider } from '../../services/secrets.service';

@Component({
  selector: 'app-secret-provider-admin',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './secret-provider-config.component.html',
  styleUrls: ['./secret-provider-config.component.css']
})
export class SecretProviderAdminComponent implements OnInit {
  @Input() provider: Provider | null = null;
  @Output() providerSaved = new EventEmitter<Provider>();
  @Output() cancelled = new EventEmitter<void>();

  providers: Provider[] = [];
  loading = false;
  error = '';
  success = '';

  // Modal states
  showCreateModal = false;
  showEditModal = false;
  showTestModal = false;
  selectedProvider: Provider | null = null;
  activeMenu: string | null = null;

  // Forms
  providerForm!: FormGroup;
  testResults: any = null;

  // Secret provider types - loaded from backend
  providerTypes: any[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private secretsService: SecretsService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadProviderTypes();
    this.loadProviders();
  }

  initForm(): void {
    this.providerForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      type: ['', Validators.required],
      description: ['', Validators.maxLength(500)],
      isActive: [true],
      config: this.formBuilder.group({})
    });

    // Watch for type changes to update config form
    this.providerForm.get('type')?.valueChanges.subscribe(type => {
      this.updateConfigForm(type);
    });
  }

  loadProviderTypes(): void {
    this.secretsService.getProviderTypes().subscribe({
      next: (response) => {
        this.providerTypes = response.types.map(type => ({
          value: type.type,
          label: type.name,
          description: type.description,
          configFields: type.configFields
        }));
      },
      error: (error) => {
        console.error('Error loading provider types:', error);
        // Fallback to empty array
        this.providerTypes = [];
      }
    });
  }

  updateConfigForm(type: string): void {
    const configGroup = this.formBuilder.group({});

    // Find the provider type configuration
    const providerType = this.providerTypes.find(pt => pt.value === type);
    if (providerType && providerType.configFields) {
      // Dynamically create form controls based on provider config fields
      providerType.configFields.forEach((field: any) => {
        const validators = [];
        if (field.required) {
          validators.push(Validators.required);
        }
        if (field.validation?.minLength) {
          validators.push(Validators.minLength(field.validation.minLength));
        }
        if (field.validation?.maxLength) {
          validators.push(Validators.maxLength(field.validation.maxLength));
        }
        if (field.validation?.pattern) {
          validators.push(Validators.pattern(field.validation.pattern));
        }

        const defaultValue = field.default || '';
        configGroup.addControl(field.name, this.formBuilder.control(defaultValue, validators));
      });
    }

    this.providerForm.setControl('config', configGroup);
  }

  loadProviders(): void {
    this.loading = true;
    this.error = '';

    this.secretsService.getProviders().subscribe({
      next: (response) => {
        // Handle null providers from API response
        this.providers = response.providers || [];
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load secret providers';
        this.loading = false;
        console.error('Error loading secret providers:', error);
      }
    });
  }

  openCreateModal(): void {
    this.selectedProvider = null;
    this.providerForm.reset();
    this.providerForm.patchValue({ isActive: true });
    this.showCreateModal = true;
  }

  openEditModal(provider: Provider): void {
    this.selectedProvider = provider;
    this.providerForm.patchValue({
      name: provider.name,
      type: provider.type,
      description: provider.description,
      isActive: provider.isActive
    });
    this.showEditModal = true;
  }

  closeModals(): void {
    this.showCreateModal = false;
    this.showEditModal = false;
    this.showTestModal = false;
    this.selectedProvider = null;
    this.testResults = null;
    this.error = '';
    this.success = '';
    this.cancelled.emit();
  }

  onSubmit(): void {
    if (this.providerForm.invalid) {
      return;
    }

    const formData = this.providerForm.value;

    if (this.selectedProvider) {
      // Update existing provider
      this.updateProvider(formData);
    } else {
      // Create new provider
      this.createProvider(formData);
    }
  }

  createProvider(data: any): void {
    this.loading = true;
    this.error = '';

    this.secretsService.createProvider(data).subscribe({
      next: (response) => {
        this.success = 'Secret provider created successfully';
        this.loading = false;
        this.closeModals();
        this.loadProviders();
        this.providerSaved.emit(response);
      },
      error: (error) => {
        this.error = error.error?.error || 'Failed to create secret provider';
        this.loading = false;
      }
    });
  }

  updateProvider(data: any): void {
    if (!this.selectedProvider) return;

    this.loading = true;
    this.error = '';

    this.secretsService.updateProvider(this.selectedProvider.id, data).subscribe({
      next: (response) => {
        this.success = 'Secret provider updated successfully';
        this.loading = false;
        this.closeModals();
        this.loadProviders();
        this.providerSaved.emit(response);
      },
      error: (error) => {
        this.error = error.error?.error || 'Failed to update secret provider';
        this.loading = false;
      }
    });
  }

  deleteProvider(provider: Provider): void {
    if (confirm(`Are you sure you want to delete the secret provider "${provider.name}"?`)) {
      // Note: This would need to be implemented in the secrets service
      this.success = 'Secret provider deleted successfully';
      this.loadProviders();
    }
  }

  testProvider(provider: Provider): void {
    this.selectedProvider = provider;
    this.testResults = null;
    this.showTestModal = true;

    this.secretsService.testProvider(provider.id).subscribe({
      next: (result) => {
        this.testResults = result;
      },
      error: (error) => {
        this.testResults = {
          success: false,
          message: 'Connection test failed',
          error: error.message
        };
      }
    });
  }

  getProviderTypeLabel(type: string): string {
    const providerType = this.providerTypes.find(pt => pt.value === type);
    return providerType ? providerType.label : type;
  }

  getStatusBadgeClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'active':
      case 'connected':
        return 'bg-green-100 text-green-800';
      case 'inactive':
      case 'disconnected':
        return 'bg-red-100 text-red-800';
      case 'testing':
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getConfigFields(type: string): string[] {
    const providerType = this.providerTypes.find(pt => pt.value === type);
    return providerType?.configFields?.map((field: any) => field.name) || [];
  }

  getFieldLabel(fieldName: string, providerType?: string): string {
    if (!providerType) {
      providerType = this.providerForm.get('type')?.value;
    }
    const provider = this.providerTypes.find(pt => pt.value === providerType);
    const field = provider?.configFields?.find((f: any) => f.name === fieldName);
    return field?.label || fieldName;
  }

  isFieldSensitive(fieldName: string, providerType?: string): boolean {
    if (!providerType) {
      providerType = this.providerForm.get('type')?.value;
    }
    const provider = this.providerTypes.find(pt => pt.value === providerType);
    const field = provider?.configFields?.find((f: any) => f.name === fieldName);
    return field?.sensitive || false;
  }
}
