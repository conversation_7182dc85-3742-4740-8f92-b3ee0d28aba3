<!-- Secret Provider Configuration Component -->
<div class="secret-provider-config">
  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <div>
      <h2 class="text-2xl font-bold text-gray-900">Secret Providers</h2>
      <p class="text-gray-600">Configure secret management providers for secure credential storage</p>
    </div>
    <button
      (click)="openCreateModal()"
      class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
      <i class="fas fa-plus"></i>
      Add Secret Provider
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <span class="ml-2 text-gray-600">Loading secret providers...</span>
  </div>

  <!-- Error Message -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
    <div class="flex items-center">
      <i class="fas fa-exclamation-circle mr-2"></i>
      {{ error }}
    </div>
  </div>

  <!-- Success Message -->
  <div *ngIf="success" class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-4">
    <div class="flex items-center">
      <i class="fas fa-check-circle mr-2"></i>
      {{ success }}
    </div>
  </div>

  <!-- Providers List -->
  <div *ngIf="!loading" class="grid gap-4">
    <div *ngFor="let provider of providers"
         class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">

      <!-- Provider Header -->
      <div class="flex justify-between items-start mb-4">
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <i class="fas fa-key text-blue-600"></i>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">{{ provider.name }}</h3>
            <p class="text-sm text-gray-600">{{ getProviderTypeLabel(provider.type) }}</p>
          </div>
        </div>

        <div class="flex items-center gap-2">
          <span [class]="getStatusBadgeClass(provider.isActive ? 'active' : 'inactive')"
                class="px-2 py-1 text-xs font-medium rounded-full">
            {{ provider.isActive ? 'Active' : 'Inactive' }}
          </span>

          <!-- Actions Menu -->
          <div class="relative">
            <button (click)="activeMenu = activeMenu === provider.id ? null : provider.id"
                    class="p-2 text-gray-400 hover:text-gray-600 rounded-lg">
              <i class="fas fa-ellipsis-v"></i>
            </button>

            <div *ngIf="activeMenu === provider.id"
                 class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
              <button (click)="testProvider(provider)"
                      class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2">
                <i class="fas fa-plug text-green-600"></i>
                Test Connection
              </button>
              <button (click)="openEditModal(provider)"
                      class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2">
                <i class="fas fa-edit text-blue-600"></i>
                Edit
              </button>
              <button (click)="deleteProvider(provider)"
                      class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center gap-2">
                <i class="fas fa-trash"></i>
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Provider Details -->
      <div class="text-sm text-gray-600 mb-3">
        {{ provider.description }}
      </div>

      <!-- Configuration Summary -->
      <div class="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span class="font-medium text-gray-700">Type:</span>
          <span class="ml-2 text-gray-600">{{ getProviderTypeLabel(provider.type) }}</span>
        </div>
        <div>
          <span class="font-medium text-gray-700">Status:</span>
          <span class="ml-2 text-gray-600">{{ provider.isActive ? 'Active' : 'Inactive' }}</span>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="providers.length === 0" class="text-center py-12">
      <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <i class="fas fa-key text-gray-400 text-2xl"></i>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No Secret Providers</h3>
      <p class="text-gray-600 mb-4">Configure secret providers to securely manage credentials and sensitive data.</p>
      <button (click)="openCreateModal()"
              class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
        Add Your First Secret Provider
      </button>
    </div>
  </div>
</div>

<!-- Create/Edit Modal -->
<div *ngIf="showCreateModal || showEditModal"
     class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
    <div class="flex justify-between items-center mb-6">
      <h3 class="text-xl font-semibold">
        {{ selectedProvider ? 'Edit' : 'Create' }} Secret Provider
      </h3>
      <button (click)="closeModals()" class="text-gray-500 hover:text-gray-700">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <form [formGroup]="providerForm" (ngSubmit)="onSubmit()">
      <!-- Basic Information -->
      <div class="space-y-4 mb-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
          <input type="text" formControlName="name"
                 class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                 placeholder="Enter provider name">
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
          <select formControlName="type"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="">Select provider type</option>
            <option *ngFor="let type of providerTypes" [value]="type.value">
              {{ type.label }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea formControlName="description" rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter provider description"></textarea>
        </div>

        <div class="flex items-center">
          <input type="checkbox" formControlName="isActive" id="isActive"
                 class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
          <label for="isActive" class="ml-2 block text-sm text-gray-700">
            Active
          </label>
        </div>
      </div>

      <!-- Configuration Fields -->
      <div *ngIf="providerForm.get('type')?.value" class="space-y-4 mb-6">
        <h4 class="text-lg font-medium text-gray-900">Configuration</h4>

        <div formGroupName="config" class="space-y-4">
          <div *ngFor="let field of getConfigFields(providerForm.get('type')?.value)" class="space-y-1">
            <label class="block text-sm font-medium text-gray-700">
              {{ getFieldLabel(field) }}
            </label>
            <input
              [type]="isFieldSensitive(field) ? 'password' : 'text'"
              [formControlName]="field"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              [placeholder]="'Enter ' + getFieldLabel(field).toLowerCase()">
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end gap-3">
        <button type="button" (click)="closeModals()"
                class="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg">
          Cancel
        </button>
        <button type="submit" [disabled]="providerForm.invalid"
                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed">
          {{ selectedProvider ? 'Update' : 'Create' }} Provider
        </button>
      </div>
    </form>
  </div>
</div>

<!-- Test Results Modal -->
<div *ngIf="showTestModal"
     class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-white rounded-lg p-6 w-full max-w-lg">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-xl font-semibold">Connection Test Results</h3>
      <button (click)="closeModals()" class="text-gray-500 hover:text-gray-700">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div *ngIf="testResults" class="space-y-4">
      <div [class]="testResults.success ? 'bg-green-50 border-green-200 text-green-700' : 'bg-red-50 border-red-200 text-red-700'"
           class="border px-4 py-3 rounded-lg">
        <div class="flex items-center">
          <i [class]="testResults.success ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'" class="mr-2"></i>
          {{ testResults.message }}
        </div>
        <div *ngIf="testResults.error" class="mt-2 text-sm">
          {{ testResults.error }}
        </div>
      </div>
    </div>

    <div *ngIf="!testResults" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span class="ml-2 text-gray-600">Testing connection...</span>
    </div>

    <div class="flex justify-end mt-6">
      <button (click)="closeModals()"
              class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg">
        Close
      </button>
    </div>
  </div>
</div>
