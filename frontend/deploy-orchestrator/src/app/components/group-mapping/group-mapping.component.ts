import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { GroupService } from '../../services/group.service';
import { IdentityProviderService } from '../../services/identity-provider.service';
import { AdminService } from '../../services/admin.service';
import { ProjectService } from '../../services/project.service';
import { Group, Role } from '../../models/user.model';
import { Project } from '../../models/project.model';
import { IdentityProvider, IdentityProviderType } from '../../models/identity-provider.model';

@Component({
  selector: 'app-group-mapping',
  templateUrl: './group-mapping.component.html',
  styleUrls: ['./group-mapping.component.scss']
})
export class GroupMappingComponent implements OnInit {
  groups: Group[] = [];
  providers: IdentityProvider[] = [];
  availableRoles: Role[] = [];
  availableProjects: Project[] = [];
  syncForm!: FormGroup;
  roleAssignmentForm!: FormGroup;
  projectAssignmentForm!: FormGroup;
  loading = false;
  syncLoading = false;
  assignmentLoading = false;
  error = '';
  success = '';
  syncResults: any = null;

  // Modal state
  showAssignmentModal = false;
  selectedGroup: Group | null = null;
  activeTab: 'roles' | 'projects' = 'roles';
  groupRoleAssignments: any[] = [];
  groupProjectAssignments: any[] = [];

  constructor(
    private groupService: GroupService,
    private identityProviderService: IdentityProviderService,
    private adminService: AdminService,
    private projectService: ProjectService,
    private formBuilder: FormBuilder
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.loadGroups();
    this.loadProviders();
    this.loadRoles();
    this.loadProjects();
  }

  initForm(): void {
    this.syncForm = this.formBuilder.group({
      providerId: ['', Validators.required],
      providerType: ['', Validators.required],
      username: [''],
      dryRun: [true]
    });

    this.roleAssignmentForm = this.formBuilder.group({
      roleId: ['', Validators.required],
      projectId: ['']
    });

    this.projectAssignmentForm = this.formBuilder.group({
      projectId: ['', Validators.required]
    });
  }

  loadGroups(): void {
    this.loading = true;
    this.groupService.getGroups().subscribe({
      next: (groups) => {
        this.groups = groups;
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load groups. Please try again.';
        console.error('Error loading groups', error);
        this.loading = false;
      }
    });
  }

  loadProviders(): void {
    // Load providers of each type and combine them
    this.loadOIDCProviders();
  }

  loadOIDCProviders(): void {
    this.identityProviderService.getOIDCProviders().subscribe({
      next: (oidcProviders) => {
        const oidcList = oidcProviders.map(p => ({...p, type: IdentityProviderType.OIDC}));
        this.loadSAMLProviders(oidcList);
      },
      error: (error) => {
        this.handleError(error);
      }
    });
  }

  loadSAMLProviders(currentProviders: IdentityProvider[] = []): void {
    this.identityProviderService.getSAMLProviders().subscribe({
      next: (samlProviders) => {
        const samlList = samlProviders.map(p => ({...p, type: IdentityProviderType.SAML}));
        this.loadLDAPProviders([...currentProviders, ...samlList]);
      },
      error: (error) => {
        this.handleError(error);
      }
    });
  }

  loadLDAPProviders(currentProviders: IdentityProvider[] = []): void {
    this.identityProviderService.getLDAPProviders().subscribe({
      next: (ldapProviders) => {
        const ldapList = ldapProviders.map(p => ({...p, type: IdentityProviderType.LDAP}));
        this.providers = [...currentProviders, ...ldapList].filter(p => p.enabled);
        this.loading = false;
      },
      error: (error) => {
        this.handleError(error);
      }
    });
  }

  handleError(error: any): void {
        this.error = 'Failed to load identity providers. Please try again.';
        console.error('Error loading identity providers', error);
      }

  getProviderTypeLabel(type: IdentityProviderType): string {
    switch (type) {
      case IdentityProviderType.OIDC:
        return 'OpenID Connect';
      case IdentityProviderType.SAML:
        return 'SAML';
      case IdentityProviderType.LDAP:
        return 'LDAP';
      default:
        return type;
    }
  }

  syncGroups(): void {
    if (this.syncForm.invalid) {
      return;
    }

    this.syncLoading = true;
    this.error = '';
    this.success = '';
    this.syncResults = null;

    const { providerId, providerType, username, dryRun } = this.syncForm.value;

    let request;
    if (username) {
      request = this.groupService.syncUserByUsername(username, providerId, providerType, dryRun);
    } else {
      request = this.groupService.syncAllUsers(providerId, providerType, dryRun);
    }

    request.subscribe({
      next: (results) => {
        this.syncResults = results.result;
        this.success = `Group synchronization ${dryRun ? 'simulation' : ''} completed successfully.`;
        this.syncLoading = false;
        if (!dryRun) {
          this.loadGroups();
        }
      },
      error: (error) => {
        this.error = 'Failed to synchronize groups. Please try again.';
        console.error('Error synchronizing groups', error);
        this.syncLoading = false;
      }
    });
  }

  loadRoles(): void {
    this.adminService.getRoles().subscribe({
      next: (roles) => {
        this.availableRoles = roles;
      },
      error: (error) => {
        console.error('Error loading roles', error);
      }
    });
  }

  loadProjects(): void {
    this.projectService.getProjects().subscribe({
      next: (projects) => {
        this.availableProjects = projects;
      },
      error: (error) => {
        console.error('Error loading projects', error);
      }
    });
  }

  openGroupAssignmentModal(group: Group): void {
    this.selectedGroup = group;
    this.showAssignmentModal = true;
    this.activeTab = 'roles';
    this.loadGroupAssignments();
  }

  closeAssignmentModal(): void {
    this.showAssignmentModal = false;
    this.selectedGroup = null;
    this.groupRoleAssignments = [];
    this.groupProjectAssignments = [];
    this.roleAssignmentForm.reset();
    this.projectAssignmentForm.reset();
  }

  loadGroupAssignments(): void {
    if (!this.selectedGroup) return;

    // Load role assignments for the group
    this.loadGroupRoleAssignments();
    this.loadGroupProjectAssignments();
  }

  loadGroupRoleAssignments(): void {
    if (!this.selectedGroup) return;

    this.adminService.getGroupRoles(this.selectedGroup.id).subscribe({
      next: (roles) => {
        // Transform roles to include display information
        this.groupRoleAssignments = roles.map(role => ({
          roleId: role.id,
          roleName: role.name,
          projectId: null, // For now, we don't have project-specific assignments
          projectName: null
        }));
      },
      error: (error) => {
        console.error('Error loading group role assignments', error);
        this.groupRoleAssignments = [];
      }
    });
  }

  loadGroupProjectAssignments(): void {
    if (!this.selectedGroup) return;

    // Clear existing assignments
    this.groupProjectAssignments = [];

    // Use the more efficient method to get projects for this group directly
    this.adminService.getProjectsForGroup(this.selectedGroup.id).subscribe({
      next: (projects) => {
        // Transform projects to the expected format
        this.groupProjectAssignments = projects.map(project => ({
          projectId: project.id,
          projectName: project.name
        })).sort((a, b) => a.projectName.localeCompare(b.projectName));
      },
      error: (error) => {
        console.error('Error loading group project assignments', error);
        this.groupProjectAssignments = [];

        // Fallback to the old method if the new API doesn't exist
        this.loadGroupProjectAssignmentsFallback();
      }
    });
  }

  // Fallback method using the old approach
  private loadGroupProjectAssignmentsFallback(): void {
    if (!this.selectedGroup) return;

    this.projectService.getProjects().subscribe({
      next: (projects) => {
        // Check each project to see if this group is assigned
        let projectsChecked = 0;
        const totalProjects = projects.length;
        const assignedProjects: { projectId: string; projectName: string }[] = [];

        if (totalProjects === 0) {
          return;
        }

        projects.forEach(project => {
          this.projectService.getGroupsForProject(project.id).subscribe({
            next: (groups) => {
              projectsChecked++;
              // Check if our selected group is in this project's groups
              const isAssigned = groups.some(group => group.id === this.selectedGroup?.id);
              if (isAssigned) {
                // Check for duplicates before adding
                const alreadyExists = assignedProjects.some(ap => ap.projectId === project.id);
                if (!alreadyExists) {
                  assignedProjects.push({
                    projectId: project.id,
                    projectName: project.name
                  });
                }
              }

              // If we've checked all projects, update the assignments
              if (projectsChecked === totalProjects) {
                // Sort by project name for better UX and assign to component property
                this.groupProjectAssignments = assignedProjects.sort((a, b) => a.projectName.localeCompare(b.projectName));
              }
            },
            error: (error) => {
              projectsChecked++;
              console.error(`Error loading groups for project ${project.name}`, error);

              // Still update assignments if we've checked all projects
              if (projectsChecked === totalProjects) {
                this.groupProjectAssignments = assignedProjects.sort((a, b) => a.projectName.localeCompare(b.projectName));
              }
            }
          });
        });
      },
      error: (error) => {
        console.error('Error loading projects', error);
        this.groupProjectAssignments = [];
      }
    });
  }

  assignRoleToGroup(): void {
    if (this.roleAssignmentForm.invalid || !this.selectedGroup) {
      return;
    }

    this.assignmentLoading = true;
    const { roleId, projectId } = this.roleAssignmentForm.value;

    this.adminService.assignRoleToGroup(this.selectedGroup.id, roleId, projectId || undefined).subscribe({
      next: () => {
        this.success = 'Role assigned to group successfully.';
        this.error = '';
        this.assignmentLoading = false;
        this.roleAssignmentForm.reset();
        this.loadGroupRoleAssignments();
      },
      error: (error) => {
        this.error = 'Failed to assign role to group. Please try again.';
        console.error('Error assigning role to group', error);
        this.assignmentLoading = false;
      }
    });
  }

  removeRoleFromGroup(assignment: any): void {
    if (!this.selectedGroup) return;

    this.adminService.removeRoleFromGroup(this.selectedGroup.id, assignment.roleId, assignment.projectId).subscribe({
      next: () => {
        this.success = 'Role removed from group successfully.';
        this.error = '';
        this.loadGroupRoleAssignments();
      },
      error: (error) => {
        this.error = 'Failed to remove role from group. Please try again.';
        console.error('Error removing role from group', error);
      }
    });
  }

  assignGroupToProject(): void {
    if (this.projectAssignmentForm.invalid || !this.selectedGroup) {
      return;
    }

    this.assignmentLoading = true;
    const { projectId } = this.projectAssignmentForm.value;

    // Use the project service to assign group to project
    this.projectService.assignGroupToProject(projectId, this.selectedGroup.id).subscribe({
      next: () => {
        this.success = 'Group assigned to project successfully.';
        this.error = '';
        this.assignmentLoading = false;
        this.projectAssignmentForm.reset();
        this.loadGroupProjectAssignments();
      },
      error: (error) => {
        this.error = 'Failed to assign group to project. Please try again.';
        console.error('Error assigning group to project', error);
        this.assignmentLoading = false;
      }
    });
  }

  removeGroupFromProject(assignment: any): void {
    if (!this.selectedGroup) return;

    this.projectService.removeGroupFromProject(assignment.projectId, this.selectedGroup.id).subscribe({
      next: () => {
        this.success = 'Group removed from project successfully.';
        this.error = '';
        this.loadGroupProjectAssignments();
      },
      error: (error) => {
        this.error = 'Failed to remove group from project. Please try again.';
        console.error('Error removing group from project', error);
      }
    });
  }
}
