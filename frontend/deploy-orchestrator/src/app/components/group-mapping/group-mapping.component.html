<div class="px-4 py-5 sm:px-6">
  <h3 class="text-lg leading-6 font-medium text-gray-900">Group Mapping</h3>
  <p class="mt-1 max-w-2xl text-sm text-gray-500">Manage group mappings and synchronize groups from identity providers</p>
</div>

<div *ngIf="error" class="bg-red-50 border-l-4 border-red-400 p-4 mx-4 my-2">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
      </svg>
    </div>
    <div class="ml-3">
      <p class="text-sm text-red-700">{{ error }}</p>
    </div>
  </div>
</div>

<div *ngIf="success" class="bg-green-50 border-l-4 border-green-400 p-4 mx-4 my-2">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
      </svg>
    </div>
    <div class="ml-3">
      <p class="text-sm text-green-700">{{ success }}</p>
    </div>
  </div>
</div>

<div class="border-t border-gray-200">
  <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
    <div class="px-4 py-5 sm:px-6 bg-gray-50">
      <h3 class="text-lg leading-6 font-medium text-gray-900">Synchronize Groups</h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500">Synchronize groups from an identity provider</p>
    </div>
    <div class="border-t border-gray-200">
      <form [formGroup]="syncForm" (ngSubmit)="syncGroups()">
        <div class="px-4 py-5 bg-white sm:p-6">
          <div class="grid grid-cols-6 gap-6">
            <div class="col-span-6 sm:col-span-3">
              <label for="providerType" class="block text-sm font-medium text-gray-700">Provider Type</label>
              <select id="providerType" formControlName="providerType" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                <option value="">Select Provider Type</option>
                <option value="oidc">OpenID Connect</option>
                <option value="saml">SAML</option>
                <option value="ldap">LDAP</option>
              </select>
              <div *ngIf="syncForm.get('providerType')?.invalid && (syncForm.get('providerType')?.dirty || syncForm.get('providerType')?.touched)" class="text-red-500 text-xs mt-1">
                <div *ngIf="syncForm.get('providerType')?.errors?.['required']">Provider Type is required.</div>
              </div>
            </div>

            <div class="col-span-6 sm:col-span-3">
              <label for="providerId" class="block text-sm font-medium text-gray-700">Provider</label>
              <select id="providerId" formControlName="providerId" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                <option value="">Select Provider</option>
                <option *ngFor="let provider of providers" [value]="provider.id" [disabled]="provider.type !== syncForm.get('providerType')?.value">
                  {{ provider.name }} ({{ getProviderTypeLabel(provider.type) }})
                </option>
              </select>
              <div *ngIf="syncForm.get('providerId')?.invalid && (syncForm.get('providerId')?.dirty || syncForm.get('providerId')?.touched)" class="text-red-500 text-xs mt-1">
                <div *ngIf="syncForm.get('providerId')?.errors?.['required']">Provider is required.</div>
              </div>
            </div>

            <div class="col-span-6 sm:col-span-3">
              <label for="username" class="block text-sm font-medium text-gray-700">Username (Optional)</label>
              <input type="text" id="username" formControlName="username" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
              <p class="mt-1 text-xs text-gray-500">Leave empty to synchronize all users</p>
            </div>

            <div class="col-span-6 sm:col-span-3">
              <div class="flex items-start mt-6">
                <div class="flex items-center h-5">
                  <input id="dryRun" formControlName="dryRun" type="checkbox" class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                </div>
                <div class="ml-3 text-sm">
                  <label for="dryRun" class="font-medium text-gray-700">Dry Run</label>
                  <p class="text-gray-500">Simulate the synchronization without making any changes</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
          <button type="submit" [disabled]="syncForm.invalid || syncLoading" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <span *ngIf="syncLoading" class="mr-2">
              <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            Synchronize Groups
          </button>
        </div>
      </form>
    </div>
  </div>

  <div *ngIf="syncResults" class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
    <div class="px-4 py-5 sm:px-6 bg-gray-50">
      <h3 class="text-lg leading-6 font-medium text-gray-900">Synchronization Results</h3>
    </div>
    <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
      <div *ngIf="syncForm.value.dryRun" class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-yellow-700">This was a dry run. No changes were made.</p>
          </div>
        </div>
      </div>

      <div class="mt-4">
        <h4 class="text-md font-medium text-gray-900">Summary</h4>
        <dl class="mt-2 grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500">Users Processed</dt>
            <dd class="mt-1 text-sm text-gray-900">{{ syncResults.usersProcessed || 0 }}</dd>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500">Groups Added</dt>
            <dd class="mt-1 text-sm text-gray-900">{{ syncResults.groupsAdded?.length || 0 }}</dd>
          </div>
        </dl>
      </div>

      <div *ngIf="syncResults.groupsAdded?.length > 0" class="mt-6">
        <h4 class="text-md font-medium text-gray-900">Groups Added</h4>
        <ul class="mt-2 divide-y divide-gray-200">
          <li *ngFor="let group of syncResults.groupsAdded" class="py-2">
            <div class="flex items-center space-x-4">
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate">{{ group.name }}</p>
                <p class="text-sm text-gray-500 truncate">Source: {{ group.source }}</p>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <div *ngIf="loading" class="flex justify-center py-6">
    <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
  </div>

  <div *ngIf="!loading" class="px-4 py-5 sm:px-6 bg-gray-50">
    <h3 class="text-lg leading-6 font-medium text-gray-900">Current Groups</h3>
  </div>

  <div *ngIf="!loading && groups.length === 0" class="text-center py-10">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900">No groups</h3>
    <p class="mt-1 text-sm text-gray-500">Synchronize groups from an identity provider to get started.</p>
  </div>

  <div *ngIf="!loading && groups.length > 0" class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">External ID</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr *ngFor="let group of groups">
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-gray-900">{{ group.name }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-500">{{ group.source }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-500">{{ group.externalId || 'N/A' }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
            <button (click)="openGroupAssignmentModal(group)" class="text-blue-600 hover:text-blue-900 mr-3">
              Manage Assignments
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Group Assignment Modal -->
  <div *ngIf="showAssignmentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">
            Manage Assignments for {{ selectedGroup?.name }}
          </h3>
          <button (click)="closeAssignmentModal()" class="text-gray-400 hover:text-gray-600">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Tab Navigation -->
        <div class="border-b border-gray-200 mb-6">
          <nav class="-mb-px flex space-x-8">
            <button
              (click)="activeTab = 'roles'"
              [class]="activeTab === 'roles' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
              class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
              Role Assignments
            </button>
            <button
              (click)="activeTab = 'projects'"
              [class]="activeTab === 'projects' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
              class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
              Project Assignments
            </button>
          </nav>
        </div>

        <!-- Role Assignments Tab -->
        <div *ngIf="activeTab === 'roles'">
          <div class="mb-6">
            <h4 class="text-md font-medium text-gray-900 mb-3">Assign Role to Group</h4>
            <form [formGroup]="roleAssignmentForm" (ngSubmit)="assignRoleToGroup()">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label for="roleSelect" class="block text-sm font-medium text-gray-700">Role</label>
                  <select id="roleSelect" formControlName="roleId" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">Select Role</option>
                    <option *ngFor="let role of availableRoles" [value]="role.id">{{ role.name }}</option>
                  </select>
                </div>
                <div>
                  <label for="projectSelect" class="block text-sm font-medium text-gray-700">Project (Optional)</label>
                  <select id="projectSelect" formControlName="projectId" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">Global Role</option>
                    <option *ngFor="let project of availableProjects" [value]="project.id">{{ project.name }}</option>
                  </select>
                </div>
                <div class="flex items-end">
                  <button type="submit" [disabled]="roleAssignmentForm.invalid || assignmentLoading" class="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <span *ngIf="assignmentLoading" class="mr-2">
                      <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </span>
                    Assign Role
                  </button>
                </div>
              </div>
            </form>
          </div>

          <!-- Current Role Assignments -->
          <div>
            <h4 class="text-md font-medium text-gray-900 mb-3">Current Role Assignments</h4>
            <div *ngIf="groupRoleAssignments.length === 0" class="text-sm text-gray-500 italic">
              No role assignments found for this group.
            </div>
            <div *ngIf="groupRoleAssignments.length > 0" class="space-y-2">
              <div *ngFor="let assignment of groupRoleAssignments" class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                <div>
                  <span class="font-medium">{{ assignment.roleName }}</span>
                  <span *ngIf="assignment.projectName" class="text-sm text-gray-500 ml-2">
                    (Project: {{ assignment.projectName }})
                  </span>
                  <span *ngIf="!assignment.projectName" class="text-sm text-gray-500 ml-2">
                    (Global)
                  </span>
                </div>
                <button (click)="removeRoleFromGroup(assignment)" class="text-red-600 hover:text-red-900 text-sm">
                  Remove
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Project Assignments Tab -->
        <div *ngIf="activeTab === 'projects'">
          <div class="mb-6">
            <h4 class="text-md font-medium text-gray-900 mb-3">Assign Group to Project</h4>
            <form [formGroup]="projectAssignmentForm" (ngSubmit)="assignGroupToProject()">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="projectAssignSelect" class="block text-sm font-medium text-gray-700">Project</label>
                  <select id="projectAssignSelect" formControlName="projectId" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">Select Project</option>
                    <option *ngFor="let project of availableProjects" [value]="project.id">{{ project.name }}</option>
                  </select>
                </div>
                <div class="flex items-end">
                  <button type="submit" [disabled]="projectAssignmentForm.invalid || assignmentLoading" class="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    <span *ngIf="assignmentLoading" class="mr-2">
                      <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </span>
                    Assign to Project
                  </button>
                </div>
              </div>
            </form>
          </div>

          <!-- Current Project Assignments -->
          <div>
            <h4 class="text-md font-medium text-gray-900 mb-3">Current Project Assignments</h4>
            <div *ngIf="groupProjectAssignments.length === 0" class="text-sm text-gray-500 italic">
              No project assignments found for this group.
            </div>
            <div *ngIf="groupProjectAssignments.length > 0" class="space-y-2">
              <div *ngFor="let assignment of groupProjectAssignments" class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                <div>
                  <span class="font-medium">{{ assignment.projectName }}</span>
                </div>
                <button (click)="removeGroupFromProject(assignment)" class="text-red-600 hover:text-red-900 text-sm">
                  Remove
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="mt-6 flex justify-end">
          <button (click)="closeAssignmentModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
