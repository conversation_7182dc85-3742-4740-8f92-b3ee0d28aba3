import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../services/auth.service';
import { AdminService } from '../../services/admin.service';
import { ProjectService } from '../../services/project.service';
import { WorkflowExecutionService } from '../../services/workflow-execution.service';
import { ScheduleService } from '../../services/schedule.service';
import { User } from '../../models/user.model';

interface DashboardStats {
  totalProjects: number;
  activeDeployments: number;
  scheduledJobs: number;
  totalUsers: number;
}

interface ActivityItem {
  type: 'deployment' | 'schedule' | 'user';
  title: string;
  description: string;
  timestamp: Date;
}

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  currentUser: User | null = null;
  loading = true;
  isAdmin = false;

  stats: DashboardStats = {
    totalProjects: 0,
    activeDeployments: 0,
    scheduledJobs: 0,
    totalUsers: 0
  };

  recentActivity: ActivityItem[] = [];

  constructor(
    private authService: AuthService,
    private adminService: AdminService,
    private projectService: ProjectService,
    private workflowExecutionService: WorkflowExecutionService,
    private scheduleService: ScheduleService
  ) { }

  ngOnInit(): void {
    this.authService.currentUser.subscribe(user => {
      this.currentUser = user;
      this.isAdmin = this.authService.isAdmin();
      this.loadDashboardData();
    });
  }

  loadDashboardData(): void {
    // Only load data if user is authenticated
    if (!this.authService.isAuthenticated()) {
      console.warn('Cannot load dashboard data: User not authenticated');
      this.loading = false;
      return;
    }

    this.loading = true;

    // Load statistics
    this.loadStats();

    // Load recent activity
    this.loadRecentActivity();
  }

  loadStats(): void {
    // Only load stats if user is authenticated
    if (!this.authService.isAuthenticated()) {
      console.warn('Cannot load dashboard stats: User not authenticated');
      this.loading = false;
      return;
    }

    // Load projects count
    this.projectService.getProjects().subscribe({
      next: (projects) => {
        this.stats.totalProjects = projects.length;
      },
      error: (error) => {
        console.error('Error loading projects', error);
        // Don't retry automatically to prevent infinite loops
        if (error.status === 401) {
          console.warn('Authentication failed while loading projects. User may need to log in again.');
        }
      }
    });

    // Load workflow executions count
    // Only make request if authenticated to prevent infinite 401 loops
    if (this.authService.isAuthenticated()) {
      this.workflowExecutionService.getExecutions().subscribe({
        next: (response) => {
          const executions = response.executions || [];
          this.stats.activeDeployments = executions.filter(e => e.status === 'running' || e.status === 'pending').length;
        },
        error: (error) => {
          console.error('Error loading executions', error);
          // Don't retry automatically to prevent infinite loops
          if (error.status === 401) {
            console.warn('Authentication failed while loading workflow executions. User may need to log in again.');
          }
        }
      });
    }

    // Load schedules count (if service exists)
    if (this.authService.isAuthenticated()) {
      this.scheduleService.getSchedules().subscribe({
        next: (schedules) => {
          this.stats.scheduledJobs = schedules.filter(s => s.enabled).length;
        },
        error: (error) => {
          console.error('Error loading schedules', error);
          // Don't retry automatically to prevent infinite loops
          if (error.status === 401) {
            console.warn('Authentication failed while loading schedules. User may need to log in again.');
          }
        }
      });
    }

    // Load users count (admin only)
    if (this.isAdmin && this.authService.isAuthenticated()) {
      this.adminService.getUsers().subscribe({
        next: (users) => {
          this.stats.totalUsers = users.length;
        },
        error: (error) => {
          console.error('Error loading users', error);
          // Don't retry automatically to prevent infinite loops
          if (error.status === 401) {
            console.warn('Authentication failed while loading users. User may need to log in again.');
          }
        }
      });
    }
  }

  loadRecentActivity(): void {
    // Only load activity if user is authenticated
    if (!this.authService.isAuthenticated()) {
      console.warn('Cannot load recent activity: User not authenticated');
      this.loading = false;
      return;
    }

    // This would typically load from an activity/audit log service
    // For now, we'll create some mock data
    this.recentActivity = [
      {
        type: 'deployment',
        title: 'Deployment Started',
        description: 'Production deployment for Project Alpha',
        timestamp: new Date(Date.now() - 1000 * 60 * 30) // 30 minutes ago
      },
      {
        type: 'schedule',
        title: 'Schedule Created',
        description: 'Daily backup job scheduled',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2 hours ago
      },
      {
        type: 'user',
        title: 'User Login',
        description: 'Admin user logged in',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4) // 4 hours ago
      }
    ];

    this.loading = false;
  }

  getActivityIcon(type: string): string {
    switch (type) {
      case 'deployment':
        return 'bg-blue-500';
      case 'schedule':
        return 'bg-green-500';
      case 'user':
        return 'bg-purple-500';
      default:
        return 'bg-gray-500';
    }
  }
}
