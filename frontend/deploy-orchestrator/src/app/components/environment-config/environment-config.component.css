/* Custom styles that complement Tailwind CSS */

/* Card styles using Tailwind classes */
.card {
  @apply shadow-lg border border-gray-200 rounded-lg bg-white;
}

.card-header {
  @apply border-b border-gray-100 pb-4 mb-4 px-6 pt-6;
}

.form-field {
  @apply w-full mb-4;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md bg-white;
}

.btn {
  @apply font-medium px-4 py-2 rounded-md transition-colors;
}

/* Custom animations and transitions */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Secret selector custom styles */
.secret-selector {
  @apply relative;
}

.secret-option {
  @apply flex items-center justify-between p-2 hover:bg-gray-50;
}

.secret-icon {
  @apply text-green-600 mr-2;
}


