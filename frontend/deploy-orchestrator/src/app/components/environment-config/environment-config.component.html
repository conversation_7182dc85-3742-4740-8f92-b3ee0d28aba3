<div class="max-w-6xl mx-auto p-6 bg-gray-50 min-h-screen">
  <!-- Header -->
  <div class="text-center mb-8">
    <h2 class="text-3xl font-bold text-blue-600 mb-2">Create Environment</h2>
    <p class="text-gray-600 text-sm">Configure a new deployment environment for your project</p>
  </div>

  <!-- Project info display -->
  <div *ngIf="selectedProject" class="mb-6">
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h4a1 1 0 011 1v5m-6 0V9a1 1 0 011-1h4a1 1 0 011 1v2"></path>
        </svg>
        <span class="text-sm font-medium text-blue-900">
          Creating environment for: <strong>{{ selectedProject.name }}</strong>
        </span>
      </div>
    </div>
  </div>

  <!-- No project selected message -->
  <div *ngIf="!selectedProject" class="mb-6">
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <span class="text-sm font-medium text-yellow-900">
          Please select a project from the header to create an environment
        </span>
      </div>
    </div>
  </div>

  <!-- Progress Steps -->
  <div class="flex items-center justify-center mb-8 px-4">
    <div class="flex flex-col items-center">
      <div class="w-10 h-10 rounded-full flex items-center justify-center font-medium mb-2 transition-all duration-300"
           [class]="step == 1 ? 'bg-blue-600 text-white' : step > 1 ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-600'">
        <span *ngIf="step <= 1">1</span>
        <svg *ngIf="step > 1" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
        </svg>
      </div>
      <div class="text-xs text-center min-w-20"
           [class]="step == 1 ? 'text-blue-600 font-medium' : 'text-gray-600'">Select Provider</div>
    </div>
    <div class="w-16 h-0.5 mx-4 mb-6 transition-all duration-300"
         [class]="step > 1 ? 'bg-green-500' : 'bg-gray-300'"></div>
    <div class="flex flex-col items-center">
      <div class="w-10 h-10 rounded-full flex items-center justify-center font-medium mb-2 transition-all duration-300"
           [class]="step == 2 ? 'bg-blue-600 text-white' : step > 2 ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-600'">
        <span *ngIf="step <= 2">2</span>
        <svg *ngIf="step > 2" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
        </svg>
      </div>
      <div class="text-xs text-center min-w-20"
           [class]="step == 2 ? 'text-blue-600 font-medium' : 'text-gray-600'">Configure</div>
    </div>
    <div class="w-16 h-0.5 mx-4 mb-6 transition-all duration-300"
         [class]="step > 2 ? 'bg-green-500' : 'bg-gray-300'"></div>
    <div class="flex flex-col items-center">
      <div class="w-10 h-10 rounded-full flex items-center justify-center font-medium mb-2 transition-all duration-300"
           [class]="step == 3 ? 'bg-blue-600 text-white' : step > 3 ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-600'">
        <span *ngIf="step <= 3">3</span>
        <svg *ngIf="step > 3" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
        </svg>
      </div>
      <div class="text-xs text-center min-w-20"
           [class]="step == 3 ? 'text-blue-600 font-medium' : 'text-gray-600'">Review</div>
    </div>
  </div>

  <!-- Step 1: Select Provider -->
  <div *ngIf="step == 1" class="bg-white rounded-lg shadow-lg p-6 mb-6 fade-in">
      <div class="border-b border-gray-100 pb-4 mb-6">
        <h3 class="text-xl font-semibold text-gray-800 mb-2">Choose Environment Provider</h3>
        <p class="text-gray-600 text-sm">Select the platform where you want to deploy your applications</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div
          *ngFor="let provider of availableProviders"
          class="relative border-2 rounded-lg p-5 cursor-pointer transition-all duration-300 hover:border-blue-500 hover:shadow-md hover:-translate-y-1 bg-white"
          [class]="selectedProvider?.metadata?.name === provider.metadata.name ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200'"
          (click)="selectProvider(provider)"
          tabindex="0"
          (keydown.enter)="selectProvider(provider)"
          (keydown.space)="selectProvider(provider)"
        >
          <div class="text-center mb-3">
            <!-- Custom SVG icons for providers -->
            <div class="w-12 h-12 mx-auto mb-2 text-blue-600">
              <!-- Kubernetes icon -->
              <svg *ngIf="provider.metadata.icon === 'kubernetes'" class="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
              </svg>
              <!-- OpenShift icon -->
              <svg *ngIf="provider.metadata.icon === 'openshift'" class="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
              </svg>
              <!-- Docker icon -->
              <svg *ngIf="provider.metadata.icon === 'docker'" class="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
                <path d="M13.983 11.078h2.119a.186.186 0 00.186-.185V9.006a.186.186 0 00-.186-.186h-2.119a.185.185 0 00-.185.185v1.888c0 .102.083.185.185.185m-2.954-5.43h2.118a.186.186 0 00.186-.186V3.574a.186.186 0 00-.186-.185h-2.118a.185.185 0 00-.185.185v1.888c0 .102.082.185.185.186"/>
              </svg>
              <!-- AWS icon -->
              <svg *ngIf="provider.metadata.icon === 'aws'" class="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
                <path d="M6.763 10.036c0 .296.032.535.088.71.064.176.144.368.256.576.04.063.056.127.056.183 0 .08-.048.16-.152.24l-.503.335c-.072.048-.144.072-.2.072-.08 0-.16-.04-.239-.112a2.417 2.417 0 01-.287-.375 6.18 6.18 0 01-.248-.471c-.622.734-1.405 1.101-2.347 1.101-.67 0-1.205-.191-1.596-.574-.391-.383-.591-.894-.591-1.533 0-.678.239-1.23.726-1.644.487-.415 1.133-.623 1.955-.623.27 0 .551.024.846.064.296.04.6.104.918.176v-.583c0-.607-.127-1.030-.375-1.277-.255-.248-.686-.367-1.3-.367-.28 0-.568.032-.863.104-.296.064-.583.16-.863.28-.128.063-.224.104-.279.128-.056.024-.096.04-.128.04-.112 0-.168-.08-.168-.248v-.391c0-.128.016-.224.056-.28.04-.064.112-.128.207-.184.28-.144.615-.264 1.005-.36.391-.096.807-.144 1.246-.144.95 0 1.644.216 2.091.647.439.432.663 1.085.663 1.963v2.586zm-3.24 1.214c.263 0 .535-.048.822-.144.287-.096.543-.271.758-.503.128-.144.224-.304.272-.48.047-.175.08-.384.08-.615v-.295a6.637 6.637 0 00-.735-.136 6.188 6.188 0 00-.751-.048c-.535 0-.926.104-1.19.32-.263.215-.391.518-.391.917 0 .375.095.655.287.846.191.2.47.296.848.296z"/>
              </svg>
              <!-- Default cloud icon -->
              <svg *ngIf="!provider.metadata.icon || (provider.metadata.icon !== 'kubernetes' && provider.metadata.icon !== 'openshift' && provider.metadata.icon !== 'docker' && provider.metadata.icon !== 'aws')" class="w-full h-full" fill="currentColor" viewBox="0 0 20 20">
                <path d="M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z"></path>
              </svg>
            </div>
          </div>

          <div class="text-center">
            <h4 class="text-lg font-medium text-gray-800 mb-2">{{ provider.metadata.name | titlecase }}</h4>
            <p class="text-sm text-gray-600 mb-3 line-clamp-2">{{ provider.metadata.description }}</p>

            <div class="flex flex-wrap justify-center gap-1 mb-3">
              <span *ngFor="let tag of provider.metadata.tags?.slice(0, 3)"
                    class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                {{ tag }}
              </span>
            </div>

            <div class="text-xs text-gray-500">
              <strong>Capabilities:</strong>
              {{ provider.metadata.capabilities.join(', ') }}
            </div>
          </div>

          <div class="absolute top-3 right-3" *ngIf="provider.enabled">
            <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
          </div>
      </div>
    </div>
  </div>



  <form [formGroup]="environmentForm" (ngSubmit)="createEnvironment()">
    <!-- Step 2: Configure Provider -->
    <div *ngIf="isStep('configure')" class="bg-white rounded-lg shadow-lg p-6 mb-6 slide-in-up">
      <div class="border-b border-gray-100 pb-4 mb-6">
        <h3 class="text-xl font-semibold text-gray-800 mb-2">Configure {{ selectedProvider?.metadata?.name | titlecase }}</h3>
        <p class="text-gray-600 text-sm">{{ selectedProvider?.metadata?.description }}</p>
      </div>

      <!-- Basic Environment Info -->
      <div class="mb-8">
        <h4 class="text-lg font-medium text-gray-800 mb-4 border-b border-gray-200 pb-2">Environment Details</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Environment Name -->
          <div class="w-full">
            <label class="block text-sm font-medium text-gray-700 mb-2">Environment Name</label>
            <input
              type="text"
              formControlName="name"
              placeholder="e.g., production, staging, development"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              [class.border-red-500]="environmentForm.get('name')?.invalid && environmentForm.get('name')?.touched"
            >
            <div *ngIf="environmentForm.get('name')?.hasError('required') && environmentForm.get('name')?.touched"
                 class="mt-1 text-sm text-red-600">
              Environment name is required
            </div>
            <div *ngIf="environmentForm.get('name')?.hasError('minlength') && environmentForm.get('name')?.touched"
                 class="mt-1 text-sm text-red-600">
              Environment name must be at least 3 characters
            </div>
          </div>

          <!-- Description -->
          <div class="w-full">
            <label class="block text-sm font-medium text-gray-700 mb-2">Description (Optional)</label>
            <textarea
              formControlName="description"
              rows="2"
              placeholder="Describe this environment..."
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- Provider Configuration -->
      <div class="mb-8" formGroupName="config">
        <h4 class="text-lg font-medium text-gray-800 mb-4 border-b border-gray-200 pb-2">Provider Configuration</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <ng-container *ngFor="let field of selectedProvider?.configSchema">
            <div *ngIf="shouldShowField(field)" class="w-full">

            <!-- Text/Password/Number inputs with Secret Integration -->
            <div *ngIf="field.type === 'text' || field.type === 'string' || field.type === 'password' || field.type === 'number' || field.type === 'email'"
                 class="relative">

              <!-- Secret Reference Display -->
              <div *ngIf="hasSecretReference(field.name)"
                   class="mb-2 p-3 bg-green-50 border border-green-200 rounded-lg flex items-center justify-between">
                <div class="flex items-center">
                  <svg class="w-4 h-4 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-sm text-green-800">{{ getSecretDisplayName(field.name) }}</span>
                </div>
                <button type="button"
                        (click)="removeSecretReference(field.name)"
                        class="p-1 text-red-600 hover:bg-red-100 rounded-full transition-colors">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                  </svg>
                </button>
              </div>

              <!-- Form Field -->
              <div class="w-full">
                <label class="block text-sm font-medium text-gray-700 mb-2">{{ field.label }}</label>
                <div class="relative">
                  <input
                    [formControlName]="field.name"
                    [type]="getFieldType(field)"
                    [placeholder]="field.description || ''"
                    [readonly]="hasSecretReference(field.name)"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    [class.border-red-500]="environmentForm.get('config')?.get(field.name)?.invalid && environmentForm.get('config')?.get(field.name)?.touched"
                    [class.bg-gray-50]="hasSecretReference(field.name)"
                    [class.pr-10]="isSecretField(field.name) && !hasSecretReference(field.name)"
                  >

                  <!-- Secret Selector Button -->
                  <button *ngIf="isSecretField(field.name) && !hasSecretReference(field.name)"
                          type="button"
                          (click)="toggleSecretMenu(field.name)"
                          title="Select from secrets"
                          class="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-blue-600 hover:bg-blue-100 rounded transition-colors">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clip-rule="evenodd"></path>
                    </svg>
                  </button>
                </div>

                <div *ngIf="field.description && !hasSecretReference(field.name)" class="mt-1 text-sm text-gray-500">
                  {{ field.description }}
                </div>
                <div *ngIf="environmentForm.get('config')?.get(field.name)?.hasError('required') && environmentForm.get('config')?.get(field.name)?.touched"
                     class="mt-1 text-sm text-red-600">
                  {{ field.label }} is required
                </div>
              </div>

              <!-- Secret Selection Dropdown -->
              <div *ngIf="showSecretMenu[field.name]"
                   class="absolute z-10 mt-1 w-80 bg-white border border-gray-300 rounded-md shadow-lg">
                <div class="p-3 border-b border-gray-200">
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Select Secret</span>
                    <button type="button" (click)="loadAvailableSecrets()"
                            class="text-xs text-blue-600 hover:text-blue-800 flex items-center space-x-1">
                      <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                      </svg>
                      <span>Refresh</span>
                    </button>
                  </div>
                  <div class="text-xs text-gray-500">
                    Project: {{ currentProjectId || projectId || 'Not set' }} | Secrets: {{ availableSecrets.length }}
                  </div>
                </div>
                <div class="max-h-48 overflow-y-auto">
                  <button *ngFor="let secret of availableSecrets"
                          type="button"
                          (click)="selectSecret(field.name, secret)"
                          class="w-full text-left px-3 py-2 hover:bg-gray-50 flex items-center space-x-2">
                    <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z" clip-rule="evenodd"></path>
                    </svg>
                    <div class="flex-1">
                      <div class="font-medium text-sm">{{ secret.name }}</div>
                      <div class="text-xs text-gray-500">{{ secret.description }}</div>
                      <div class="text-xs text-gray-400">{{ secret.provider }} • {{ secret.type }}</div>
                    </div>
                  </button>
                  <div *ngIf="availableSecrets.length === 0" class="p-4 text-center">
                    <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                    </svg>
                    <div class="text-sm text-gray-500 mb-1">No secrets available</div>
                    <div class="text-xs text-gray-400">
                      {{ (currentProjectId || projectId) ? 'No secrets configured for this project' : 'No project selected' }}
                    </div>
                    <button *ngIf="currentProjectId || projectId" type="button" (click)="loadAvailableSecrets()"
                            class="mt-2 text-xs text-blue-600 hover:text-blue-800">
                      Try again
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Textarea -->
            <div *ngIf="field.type === 'textarea'" class="w-full">
              <label class="block text-sm font-medium text-gray-700 mb-2">{{ field.label }}</label>
              <textarea
                [formControlName]="field.name"
                rows="4"
                [placeholder]="field.description || ''"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
                [class.border-red-500]="environmentForm.get('config')?.get(field.name)?.invalid && environmentForm.get('config')?.get(field.name)?.touched"
              ></textarea>
              <div *ngIf="field.description" class="mt-1 text-sm text-gray-500">{{ field.description }}</div>
              <div *ngIf="environmentForm.get('config')?.get(field.name)?.hasError('required') && environmentForm.get('config')?.get(field.name)?.touched"
                   class="mt-1 text-sm text-red-600">
                {{ field.label }} is required
              </div>
            </div>

            <!-- Select dropdown -->
            <div *ngIf="field.type === 'select'" class="w-full">
              <label class="block text-sm font-medium text-gray-700 mb-2">{{ field.label }}</label>
              <select
                [formControlName]="field.name"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                [class.border-red-500]="environmentForm.get('config')?.get(field.name)?.invalid && environmentForm.get('config')?.get(field.name)?.touched"
              >
                <option value="">Select {{ field.label }}</option>
                <option *ngFor="let option of field.options" [value]="option.value">
                  {{ option.label }}
                </option>
              </select>
              <div *ngIf="field.description" class="mt-1 text-sm text-gray-500">{{ field.description }}</div>
              <div *ngIf="environmentForm.get('config')?.get(field.name)?.hasError('required') && environmentForm.get('config')?.get(field.name)?.touched"
                   class="mt-1 text-sm text-red-600">
                {{ field.label }} is required
              </div>
            </div>

            <!-- Boolean toggle -->
            <div *ngIf="field.type === 'boolean' || field.type === 'checkbox'" class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div class="flex-1">
                <label class="text-sm font-medium text-gray-700">{{ field.label }}</label>
                <p class="text-sm text-gray-600" *ngIf="field.description">{{ field.description }}</p>
              </div>
              <div class="relative">
                <input
                  type="checkbox"
                  [formControlName]="field.name"
                  class="sr-only"
                  [id]="'toggle-' + field.name"
                >
                <label
                  [for]="'toggle-' + field.name"
                  class="flex items-center cursor-pointer"
                >
                  <div class="relative">
                    <div class="block bg-gray-300 w-10 h-6 rounded-full transition-colors"
                         [class.bg-blue-500]="environmentForm.get('config')?.get(field.name)?.value"></div>
                    <div class="absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform"
                         [class.transform]="environmentForm.get('config')?.get(field.name)?.value"
                         [class.translate-x-4]="environmentForm.get('config')?.get(field.name)?.value"></div>
                  </div>
                </label>
              </div>
            </div>

            </div>
          </ng-container>
        </div>
      </div>

      <!-- Additional Secrets Section -->
      <div class="mt-8 bg-gray-50 rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <div>
            <h4 class="text-lg font-medium text-gray-800">Additional Secrets</h4>
            <p class="text-sm text-gray-600">Map additional secrets to environment variables</p>
          </div>
          <button type="button" (click)="toggleAddSecretForm()"
                  class="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
            </svg>
            <span>Add Secret</span>
          </button>
        </div>

        <!-- Add Secret Form -->
        <div *ngIf="showAddSecretForm" class="mb-6 p-4 bg-white rounded-lg border border-gray-200">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Secret</label>
              <select [(ngModel)]="newSecretMapping.secretId"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">Select a secret</option>
                <option *ngFor="let secret of availableSecrets" [value]="secret.id">
                  {{ secret.name }} ({{ secret.provider }})
                </option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Variable Name</label>
              <input type="text" [(ngModel)]="newSecretMapping.variableName"
                     placeholder="e.g., DATABASE_PASSWORD"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Format</label>
              <select [(ngModel)]="newSecretMapping.format"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="env">Environment Variable</option>
                <option value="file">File Mount</option>
                <option value="volume">Volume Mount</option>
              </select>
            </div>
            <div *ngIf="newSecretMapping.format === 'file' || newSecretMapping.format === 'volume'">
              <label class="block text-sm font-medium text-gray-700 mb-2">Mount Path</label>
              <input type="text" [(ngModel)]="newSecretMapping.mountPath"
                     placeholder="e.g., /etc/secrets/db-password"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
          </div>
          <div class="flex justify-end space-x-3 mt-4">
            <button type="button" (click)="toggleAddSecretForm()"
                    class="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors">
              Cancel
            </button>
            <button type="button" (click)="addSecretMapping()"
                    [disabled]="!newSecretMapping.secretId || !newSecretMapping.variableName"
                    class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors">
              Add Secret
            </button>
          </div>
        </div>

        <!-- Existing Secrets List -->
        <div *ngIf="additionalSecrets.length > 0" class="space-y-3">
          <div *ngFor="let secret of additionalSecrets; let i = index"
               class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200">
            <div class="flex items-center space-x-3">
              <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z" clip-rule="evenodd"></path>
              </svg>
              <div>
                <div class="font-medium text-gray-900">{{ getSecretName(secret.secretId) }}</div>
                <div class="text-sm text-gray-500">
                  Variable: <span class="font-mono">{{ secret.variableName }}</span>
                  <span *ngIf="secret.format"> • Format: {{ secret.format }}</span>
                  <span *ngIf="secret.mountPath"> • Path: {{ secret.mountPath }}</span>
                </div>
              </div>
            </div>
            <button type="button" (click)="removeSecretMapping(i)"
                    class="p-1 text-red-600 hover:bg-red-100 rounded transition-colors">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>
        </div>

        <div *ngIf="additionalSecrets.length === 0 && !showAddSecretForm"
             class="text-center py-8 text-gray-500">
          <svg class="w-12 h-12 text-gray-400 mx-auto mb-3" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z" clip-rule="evenodd"></path>
          </svg>
          <p class="text-sm">No additional secrets configured</p>
          <p class="text-xs text-gray-400">Click "Add Secret" to map secrets to environment variables</p>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-between items-center pt-6 border-t border-gray-200">
        <button type="button" (click)="previousStep()"
                class="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors">
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
          </svg>
          <span>Back</span>
        </button>
        <button type="button" (click)="nextStep()"
                [disabled]="!environmentForm.valid"
                class="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors">
          <span>Next</span>
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Step 3: Review -->
    <div *ngIf="isStep('review')" class="bg-white rounded-lg shadow-lg p-6 mb-6 fade-in">
      <div class="border-b border-gray-100 pb-4 mb-6">
        <h3 class="text-xl font-semibold text-gray-800 mb-2">Review Configuration</h3>
        <p class="text-gray-600 text-sm">Please review your environment configuration before creating</p>
      </div>

      <!-- Environment Details -->
      <div class="mb-6">
        <h4 class="text-lg font-medium text-gray-800 mb-4 border-b border-gray-200 pb-2">Environment Details</h4>
        <div class="space-y-3">
          <div class="flex justify-between items-center py-2 border-b border-gray-100">
            <span class="font-medium text-gray-700 min-w-32">Name:</span>
            <span class="text-gray-900">{{ environmentForm.get('name')?.value }}</span>
          </div>
          <div *ngIf="environmentForm.get('description')?.value"
               class="flex justify-between items-center py-2 border-b border-gray-100">
            <span class="font-medium text-gray-700 min-w-32">Description:</span>
            <span class="text-gray-900">{{ environmentForm.get('description')?.value }}</span>
          </div>
          <div class="flex justify-between items-center py-2 border-b border-gray-100">
            <span class="font-medium text-gray-700 min-w-32">Provider:</span>
            <span class="text-gray-900">{{ selectedProvider?.metadata?.name | titlecase }}</span>
          </div>
        </div>
      </div>

      <!-- Provider Configuration -->
      <div class="mb-6">
        <h4 class="text-lg font-medium text-gray-800 mb-4 border-b border-gray-200 pb-2">Provider Configuration</h4>
        <div class="space-y-3">
          <ng-container *ngFor="let field of selectedProvider?.configSchema">
            <div *ngIf="shouldShowField(field)"
                 class="flex justify-between items-center py-2 border-b border-gray-100">
            <span class="font-medium text-gray-700 min-w-32">{{ field.label }}:</span>
            <span *ngIf="!field.sensitive && !hasSecretReference(field.name)" class="text-gray-900">
              {{ environmentForm.get('config')?.get(field.name)?.value || 'Not set' }}
            </span>
            <span *ngIf="field.sensitive || hasSecretReference(field.name)"
                  class="text-gray-500 font-mono flex items-center">
              <svg *ngIf="hasSecretReference(field.name)" class="w-4 h-4 text-green-600 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
              </svg>
              <span *ngIf="hasSecretReference(field.name)">{{ getSecretDisplayName(field.name) }}</span>
              <span *ngIf="!hasSecretReference(field.name)">••••••••</span>
            </span>
            </div>
          </ng-container>
        </div>
      </div>

      <!-- Additional Secrets Review -->
      <div *ngIf="additionalSecrets.length > 0" class="mb-6">
        <h4 class="text-lg font-medium text-gray-800 mb-4 border-b border-gray-200 pb-2">Additional Secrets</h4>
        <div class="space-y-3">
          <div *ngFor="let secret of additionalSecrets"
               class="flex justify-between items-center py-2 border-b border-gray-100">
            <span class="font-medium text-gray-700 min-w-32">{{ getSecretName(secret.secretId) }}:</span>
            <span class="text-gray-900 flex items-center">
              <svg class="w-4 h-4 text-green-600 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z" clip-rule="evenodd"></path>
              </svg>
              <span class="font-mono">{{ secret.variableName }}</span>
              <span *ngIf="secret.format" class="text-sm text-gray-500 ml-2">({{ secret.format }})</span>
            </span>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-between items-center pt-6 border-t border-gray-200">
        <button type="button" (click)="previousStep()"
                class="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors">
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
          </svg>
          <span>Back</span>
        </button>
        <button type="submit" [disabled]="loading"
                class="flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors">
          <!-- Loading Spinner -->
          <svg *ngIf="loading" class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span *ngIf="!loading">Create Environment</span>
          <span *ngIf="loading">Creating...</span>
        </button>
      </div>
    </div>

  </form>

  <!-- Action Buttons -->
  <div class="flex justify-between items-center mt-6" *ngIf="step == 1">
    <button type="button" (click)="cancel()"
            class="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors">
      Cancel
    </button>
    <button type="button" (click)="reset()" *ngIf="selectedProvider"
            class="px-4 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-md transition-colors">
      Reset
    </button>
  </div>

</div>
