import {Component, <PERSON><PERSON>nit, On<PERSON><PERSON>roy, Input, Output, EventEmitter, ChangeDetectorRef} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule} from '@angular/forms';

import {Router} from '@angular/router';
import {Observable, of, Subject} from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { EnvironmentService, CreateEnvironmentRequest } from '../../services/environment.service';
import { SecretsService } from '../../services/secrets.service';
import { ProjectService } from '../../services/project.service';
import { NotificationService } from '../../services/notification.service';

// Secret integration interfaces
interface Secret {
    id: string;
    name: string;
    type: string;
    provider: string;
    description?: string;
}

interface SecretReference {
    secretId: string;
    key: string;
    displayName: string;
}

// Simplified provider interfaces
interface ProviderMetadata {
    name: string;
    version: string;
    description: string;
    type: string;
    category: string;
    capabilities: string[];
    icon?: string;
    tags?: string[];
}

// Define types for conditional logic
type ShowWhenCondition = string | string[] | {
    equals?: any;
    not_equals?: any;
    in?: any[];
    not_in?: any[];
};

interface ProviderConfigField {
    name: string;
    type: string;
    label: string;
    description?: string;
    required: boolean;
    sensitive?: boolean;
    default?: any;
    options?: any[];
    validation?: any;
    dependsOn?: string;
    showWhen?: ShowWhenCondition;
}

interface EnvironmentProvider {
    metadata: ProviderMetadata;
    configSchema: ProviderConfigField[];
    enabled: boolean;
}

@Component({
    selector: 'app-environment-config',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        FormsModule
    ],
    templateUrl: './environment-config.component.html',
    styleUrls: ['./environment-config.component.css']
})
export class EnvironmentConfigComponent implements OnInit, OnDestroy {

    @Input() projectId: string = ''; // Keep for backward compatibility
    @Output() environmentCreated = new EventEmitter<any>();
    @Output() cancelled = new EventEmitter<void>();

    // Available providers (loaded dynamically from backend)
    availableProviders: EnvironmentProvider[] = [];

    // Form state
    environmentForm: FormGroup;
    selectedProvider: EnvironmentProvider | null = null;
    loading = false;

    // Secret integration properties
    availableSecrets: Secret[] = [];
    secretReferences: { [fieldName: string]: SecretReference } = {};
    showSecretMenu: { [fieldName: string]: boolean } = {};

    // Multiple secrets support
    additionalSecrets: Array<{ secretId: string; variableName: string; mountPath?: string; format?: string }> = [];
    showAddSecretForm = false;
    newSecretMapping = { secretId: '', variableName: '', mountPath: '', format: 'env' };
    public currentStep: 'select' | 'configure' | 'review' = 'select';
    numberOfSteps: number = 3;

    // Project management
    private destroy$ = new Subject<void>();
    currentProjectId: string = '';
    selectedProject: any = null;

    constructor(
        private fb: FormBuilder,
        private router: Router,
        private cdr: ChangeDetectorRef,
        private environmentService: EnvironmentService,
        private secretsService: SecretsService,
        private projectService: ProjectService,
        private notificationService: NotificationService
    ) {
        this.environmentForm = this.fb.group({
            name: ['', [Validators.required, Validators.minLength(3)]],
            description: [''],
            provider: ['', Validators.required],
            config: this.fb.group({})
        });
    }

    ngOnInit(): void {
        // Load providers from backend
        this.loadProviders();

        // Subscribe to the shared project selection state
        this.subscribeToProjectChanges();

        // Set initial project ID from input or shared service
        this.initializeProjectId();
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    private subscribeToProjectChanges(): void {
        this.projectService.selectedProject$
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: (selectedProject) => {
                    const newProjectId = selectedProject?.id || '';

                    if (newProjectId !== this.currentProjectId) {
                        this.selectedProject = selectedProject;
                        this.currentProjectId = newProjectId;

                        if (this.currentProjectId) {
                            this.loadAvailableSecrets();
                        } else {
                            this.availableSecrets = [];
                        }
                    }
                },
                error: (error) => {
                    console.error('Error subscribing to project changes:', error);
                }
            });
    }

    private initializeProjectId(): void {
        // Use project ID from input if provided, otherwise use shared service
        if (this.projectId) {
            this.currentProjectId = this.projectId;
        } else {
            const selectedProject = this.projectService.getSelectedProject();
            this.selectedProject = selectedProject;
            this.currentProjectId = selectedProject?.id || '';
        }

        // Load secrets if we have a project ID
        if (this.currentProjectId) {
            this.loadAvailableSecrets();
        }
    }

    isStep(stepName: string): boolean {
        return this.currentStep === stepName;
    }

    get step(): number {
        switch (this.currentStep) {
            case 'select': return 1;
            case 'configure': return 2;
            case 'review': return 3;
            default: return 1;
        }
    }

    loadProviders(): void {
        this.loading = true;
        this.environmentService.getProviderTypes().subscribe({
            next: (response) => {
                // Transform backend provider types to our format and filter out mock providers
                this.availableProviders = response.types
                    .filter(type => {
                        // Filter out mock providers (identified by category "mock" or name containing "mock")
                        const isMock = type.category === 'mock' ||
                                      type.name?.toLowerCase().includes('mock') ||
                                      type.type?.toLowerCase().includes('mock') ||
                                      type.description?.toLowerCase().includes('mock');
                        return !isMock;
                    })
                    .map(type => ({
                        metadata: {
                            name: type.name || type.type,
                            version: type.version || '1.0.0',
                            description: type.description || `${type.name} environment provider`,
                            type: type.type || 'container-orchestration',
                            category: type.category || type.type,
                            capabilities: type.capabilities || ['deploy', 'scale', 'monitor'],
                            icon: type.icon || type.name?.toLowerCase(),
                            tags: type.tags || [type.name?.toLowerCase()]
                        },
                        configSchema: [], // Will be loaded when provider is selected
                        enabled: type.enabled !== false
                    }));
                this.loading = false;
            },
            error: (error) => {
                console.error('Failed to load providers:', error);
                this.availableProviders = []; // Clear providers on error
                this.loading = false;
                // Show error message to user
                this.notificationService.error(
                    'Failed to load environment providers',
                    'Please check your connection and try again.'
                );
            }
        });
    }

    selectProvider(provider: EnvironmentProvider): void {
        this.selectedProvider = provider;
        this.environmentForm.patchValue({provider: provider.metadata.name});

        // Load provider schema if not already loaded
        if (!provider.configSchema || provider.configSchema.length === 0) {
            this.environmentService.getProviderSchema(provider.metadata.name).subscribe({
                next: (response) => {
                    // Convert JSON Schema format to ProviderConfigField array
                    provider.configSchema = this.convertJsonSchemaToConfigFields(response.schema);
                    this.buildProviderConfigForm();
                    this.currentStep = 'configure';
                    this.cdr.detectChanges();
                },
                error: (error) => {
                    console.error('Failed to load provider schema:', error);
                    // Use fallback schema or show error
                    provider.configSchema = [];
                    this.buildProviderConfigForm();
                    this.currentStep = 'configure';
                    this.cdr.detectChanges();
                }
            });
        } else {
            // Build dynamic form for provider configuration
            this.buildProviderConfigForm();

            // Move to configuration step
            this.currentStep = 'configure';

            // Trigger change detection to ensure view updates
            this.cdr.detectChanges();
        }
    }

    /**
     * Converts JSON Schema format to ProviderConfigField array
     * @param jsonSchema - The JSON Schema object from the API
     * @returns Array of ProviderConfigField objects
     */
    convertJsonSchemaToConfigFields(jsonSchema: any): ProviderConfigField[] {
        if (!jsonSchema || !jsonSchema.properties) {
            return [];
        }

        const fields: ProviderConfigField[] = [];
        const requiredFields = jsonSchema.required || [];

        for (const [fieldName, fieldDef] of Object.entries(jsonSchema.properties)) {
            const field = fieldDef as any;

            const configField: ProviderConfigField = {
                name: fieldName,
                type: this.mapJsonSchemaTypeToFieldType(field.type, field),
                label: field.title || this.formatFieldName(fieldName),
                description: field.description || '',
                required: requiredFields.includes(fieldName),
                sensitive: field.sensitive || false,
                default: field.default,
                options: field.enum ? field.enum.map((value: any) => ({ value, label: value })) : undefined,
                validation: this.extractValidationFromJsonSchema(field),
                dependsOn: field.dependsOn,
                showWhen: field.showWhen
            };

            fields.push(configField);
        }

        return fields;
    }

    /**
     * Maps JSON Schema types to form field types
     */
    private mapJsonSchemaTypeToFieldType(schemaType: string, field: any): string {
        if (field.sensitive) {
            return 'password';
        }

        switch (schemaType) {
            case 'string':
                if (field.enum) {
                    return 'select';
                }
                if (field.format === 'email') {
                    return 'email';
                }
                if (field.format === 'uri') {
                    return 'url';
                }
                return 'text';
            case 'number':
            case 'integer':
                return 'number';
            case 'boolean':
                return 'checkbox';
            default:
                return 'text';
        }
    }

    /**
     * Formats field name for display
     */
    private formatFieldName(fieldName: string): string {
        return fieldName
            .replace(/_/g, ' ')
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase())
            .trim();
    }

    /**
     * Extracts validation rules from JSON Schema field
     */
    private extractValidationFromJsonSchema(field: any): any {
        const validation: any = {};

        if (field.pattern) {
            validation.pattern = field.pattern;
        }
        if (field.minLength !== undefined) {
            validation.minLength = field.minLength;
        }
        if (field.maxLength !== undefined) {
            validation.maxLength = field.maxLength;
        }
        if (field.minimum !== undefined) {
            validation.min = field.minimum;
        }
        if (field.maximum !== undefined) {
            validation.max = field.maximum;
        }

        return Object.keys(validation).length > 0 ? validation : undefined;
    }

    buildProviderConfigForm(): void {
        if (!this.selectedProvider) {
            return;
        }

        const configGroup = this.fb.group({});

        this.selectedProvider.configSchema.forEach(field => {
            const validators = [];
            if (field.required) {
                validators.push(Validators.required);
            }

            // Add validation based on field type
            if (field.validation) {
                if (field.validation.pattern) {
                    validators.push(Validators.pattern(field.validation.pattern));
                }
                if (field.validation.minLength) {
                    validators.push(Validators.minLength(field.validation.minLength));
                }
                if (field.validation.maxLength) {
                    validators.push(Validators.maxLength(field.validation.maxLength));
                }
                if (field.validation.min !== undefined) {
                    validators.push(Validators.min(field.validation.min));
                }
                if (field.validation.max !== undefined) {
                    validators.push(Validators.max(field.validation.max));
                }
            }

            // Set proper default value, especially for enum fields
            let defaultValue = field.default || '';
            if (field.options && field.options.length > 0 && !defaultValue) {
                defaultValue = field.options[0].value;
            }

            configGroup.addControl(
                field.name,
                this.fb.control(defaultValue, validators)
            );
        });

        this.environmentForm.setControl('config', configGroup);

        // Subscribe to form changes to trigger conditional field updates
        this.environmentForm.get('config')?.valueChanges.subscribe(() => {
            this.cdr.detectChanges();
        });
    }

    getFieldType(field: ProviderConfigField): string {
        switch (field.type) {
            case 'password':
                return 'password';
            case 'number':
                return 'number';
            case 'email':
                return 'email';
            default:
                return 'text';
        }
    }

    /**
     * Determines if a field should be shown based on its dependencies
     */
    shouldShowField(field: ProviderConfigField): boolean {
        // Always show fields without dependencies
        if (!field.dependsOn || !field.showWhen) {
            return true;
        }

        // Get the current value of the dependent field
        const dependentFieldValue = this.environmentForm.get(['config', field.dependsOn])?.value;

        // Handle different types of showWhen conditions
        if (typeof field.showWhen === 'string') {
            return dependentFieldValue === field.showWhen;
        }

        // Handle array of values (field should show when dependent field matches any value)
        if (Array.isArray(field.showWhen)) {
            return field.showWhen.includes(dependentFieldValue);
        }

        // Handle object-based conditions (more complex logic)
        if (typeof field.showWhen === 'object' && field.showWhen !== null && !Array.isArray(field.showWhen)) {
            const condition = field.showWhen as { equals?: any; not_equals?: any; in?: any[]; not_in?: any[] };

            // Support for operators like equals, not_equals, contains, etc.
            if (condition.equals !== undefined) {
                return dependentFieldValue === condition.equals;
            }
            if (condition.not_equals !== undefined) {
                return dependentFieldValue !== condition.not_equals;
            }
            if (condition.in !== undefined && Array.isArray(condition.in)) {
                return condition.in.includes(dependentFieldValue);
            }
            if (condition.not_in !== undefined && Array.isArray(condition.not_in)) {
                return !condition.not_in.includes(dependentFieldValue);
            }
        }

        // Default to showing the field if condition is unclear
        return true;
    }

    nextStep(): void {
        switch (this.currentStep) {
            case 'select':
                this.currentStep = 'configure';
                break;
            case 'configure':
                if (this.environmentForm.valid) {
                    this.currentStep = 'review';
                }
                break;
        }

    }

    previousStep(): void {
        switch (this.currentStep) {
            case 'review':
                this.currentStep = 'configure';
                break;
            case 'configure':
                this.currentStep = 'select';
                break;
        }

    }

    createEnvironment(): void {
        if (!this.environmentForm.valid) {
            this.notificationService.warning('Form Validation', 'Please fill in all required fields');
            return;
        }

        this.loading = true;

        // Check if project ID is valid
        const finalProjectId = this.currentProjectId || this.projectId;
        if (!finalProjectId) {
            this.notificationService.error('No Project Selected', 'Please select a project first.');
            this.loading = false;
            return;
        }

        // Validate project exists before creating environment
        this.validateProjectExists(finalProjectId).then(exists => {
            if (!exists) {
                this.notificationService.error(
                    'Invalid Project',
                    `Project with ID "${finalProjectId}" does not exist. Please select a valid project.`
                );
                this.loading = false;
                return;
            }

            // Continue with environment creation
            this.proceedWithEnvironmentCreation(finalProjectId);
        }).catch(error => {
            console.error('Error validating project:', error);
            // Continue anyway, let the backend handle the validation
            this.proceedWithEnvironmentCreation(finalProjectId);
        });
    }

    /**
     * Validates that a project exists and user has access
     */
    private async validateProjectExists(projectId: string): Promise<boolean> {
        try {
            // First check if it's the currently selected project
            const selectedProject = this.projectService.getSelectedProject();
            if (selectedProject && selectedProject.id === projectId) {
                return true;
            }

            // If not in shared service, make API calls to validate
            // Check if project exists and user has access
            const hasAccess = await this.projectService.checkProjectAccess(projectId).toPromise();
            if (!hasAccess) {
                return false;
            }

            // Try to get project details to confirm it exists
            const project = await this.projectService.getProject(projectId).toPromise();
            if (project) {
                return true;
            } else {
                return false;
            }
        } catch (error: any) {
            console.error('Error validating project:', error);

            // If it's a 404, the project doesn't exist
            if (error?.status === 404) {
                return false;
            }

            // If it's a 403, user doesn't have access
            if (error?.status === 403) {
                return false;
            }

            // For other errors, assume project exists but there's a network issue
            return true;
        }
    }

    /**
     * Proceeds with environment creation after project validation
     */
    private proceedWithEnvironmentCreation(projectId: string): void {
        // Clean up config by removing empty string values and processing secret references
        const cleanConfig = this.cleanProviderConfig(this.environmentForm.value.config);

        // Structure config according to backend ProviderConfig model
        const providerConfig = this.buildProviderConfig(cleanConfig);

        const createRequest: CreateEnvironmentRequest = {
            projectId: projectId,
            name: this.environmentForm.value.name,
            type: 'kubernetes', // Default type, could be made configurable
            provider: {
                type: this.selectedProvider?.metadata.name as any,
                config: providerConfig
            },
            resources: {
                cpu: '1000m',
                memory: '1Gi',
                storage: '10Gi',
                replicas: 1
            },
            networking: {
                loadBalancer: false,
                ssl: false
            },
            variables: {},
            secretMappings: [
                // Field-based secret references
                ...Object.keys(this.secretReferences).map(fieldName => ({
                    secretId: this.secretReferences[fieldName].secretId,
                    variableName: fieldName,
                    format: 'env'
                })),
                // Additional secrets
                ...this.additionalSecrets
            ],
            healthCheck: {
                enabled: true,
                endpoint: '/health',
                interval: 30,
                timeout: 10
            },
            deploymentStrategy: 'rolling',
            description: this.environmentForm.value.description || '',
            tags: []
        };

        this.environmentService.createEnvironment(createRequest).subscribe({
            next: (environment) => {
                this.loading = false;
                this.notificationService.success(
                    'Environment Created Successfully!',
                    `Environment "${environment.name}" has been created and is ready for use.`
                );
                this.environmentCreated.emit(environment);

                // Navigate to environments list
                this.router.navigate(['/environments']);
            },
            error: (error) => {
                this.loading = false;
                console.error('Failed to create environment:', error);

                let errorMessage = 'Failed to create environment. ';
                if (error.error && error.error.error) {
                    errorMessage += error.error.error;
                } else if (error.message) {
                    errorMessage += error.message;
                } else {
                    errorMessage += 'Please try again.';
                }

                this.notificationService.error('Environment Creation Failed', errorMessage);
            }
        });
    }

    /**
     * Cleans provider config by removing empty values and processing secret references
     */
    private cleanProviderConfig(config: any): any {
        const cleanedConfig: any = {};

        for (const [key, value] of Object.entries(config)) {
            // Skip empty string values (but keep false, 0, etc.)
            if (value === '' || value === null || value === undefined) {
                continue;
            }

            // Process secret references - convert to actual secret values for backend
            if (typeof value === 'string' && value.startsWith('{{secret:')) {
                // For now, keep the secret reference format as the backend should handle it
                // In the future, this could be resolved to actual secret values
                cleanedConfig[key] = value;
            } else {
                cleanedConfig[key] = value;
            }
        }

        return cleanedConfig;
    }

    /**
     * Builds provider config according to backend ProviderConfig model
     */
    private buildProviderConfig(config: any): any {
        const providerConfig: any = {
            authMethod: config.auth_method || 'username_password'
        };

        // Separate credentials from other config
        const credentials: any = {};
        const extra: any = {};

        // Define which fields should go into credentials vs extra
        const credentialFields = ['username', 'password', 'token', 'kubeconfig', 'oauth_client_id', 'oauth_client_secret'];
        const standardFields = ['cluster', 'zone', 'region', 'project', 'resourceGroup', 'subscriptionId', 'endpoint'];

        for (const [key, value] of Object.entries(config)) {
            if (credentialFields.includes(key)) {
                credentials[key] = value;
            } else if (standardFields.includes(key)) {
                // Map to standard ProviderConfig fields
                switch (key) {
                    case 'project':
                        providerConfig.project = value;
                        break;
                    case 'region':
                        providerConfig.region = value;
                        break;
                    case 'zone':
                        providerConfig.zone = value;
                        break;
                    case 'cluster':
                        providerConfig.cluster = value;
                        break;
                    case 'resourceGroup':
                        providerConfig.resourceGroup = value;
                        break;
                    case 'subscriptionId':
                        providerConfig.subscriptionId = value;
                        break;
                    case 'endpoint':
                        providerConfig.endpoint = value;
                        break;
                }
            } else {
                // Everything else goes to extra
                extra[key] = value;
            }
        }

        // Add credentials if any
        if (Object.keys(credentials).length > 0) {
            providerConfig.credentials = credentials;
        }

        // Add extra fields if any
        if (Object.keys(extra).length > 0) {
            providerConfig.extra = extra;
        }

        return providerConfig;
    }

    cancel(): void {
        this.cancelled.emit();
    }

    reset(): void {
        this.currentStep = 'select';
        this.selectedProvider = null;
        this.environmentForm.reset();
        this.secretReferences = {};
    }

    // Secret integration methods
    loadAvailableSecrets(): void {
        const projectId = this.currentProjectId || this.projectId;

        if (!projectId) {
            this.availableSecrets = [];
            return;
        }

        this.secretsService.getProjectSecrets(projectId).subscribe({
            next: (response) => {
                if (response && response.secrets && Array.isArray(response.secrets)) {
                    // Transform ProjectSecretBinding[] to Secret[] format
                    this.availableSecrets = response.secrets
                        .filter(binding => binding.secret) // Only include bindings with valid secret data
                        .map(binding => ({
                            id: binding.secret!.id,
                            name: binding.secret!.name,
                            type: binding.secret!.type || 'generic',
                            provider: binding.secret!.provider || 'internal',
                            description: binding.secret!.description || binding.description || 'No description available'
                        }));
                } else {
                    this.availableSecrets = [];
                }
            },
            error: (error) => {
                console.error('Failed to load project secrets:', error);
                // Keep empty array as fallback
                this.availableSecrets = [];
            }
        });
    }

    toggleSecretMenu(fieldName: string): void {
        // Close all other menus
        Object.keys(this.showSecretMenu).forEach(key => {
            this.showSecretMenu[key] = false;
        });

        // Toggle current menu
        const wasOpen = this.showSecretMenu[fieldName];
        this.showSecretMenu[fieldName] = !wasOpen;

        // If opening the menu and no secrets are loaded, try to reload them
        if (!wasOpen && this.availableSecrets.length === 0 && this.projectId) {
            this.loadAvailableSecrets();
        }
    }

    selectSecret(fieldName: string, secret: Secret, key: string = 'value'): void {
        this.secretReferences[fieldName] = {
            secretId: secret.id,
            key: key,
            displayName: `${secret.name} (${key})`
        };

        // Update form control to show secret reference
        const control = this.environmentForm.get(['config', fieldName]);
        if (control) {
            control.setValue(`{{secret:${secret.id}:${key}}}`);
            control.markAsTouched();
        }

        // Close the menu
        this.showSecretMenu[fieldName] = false;
    }

    removeSecretReference(fieldName: string): void {
        delete this.secretReferences[fieldName];

        // Clear form control
        const control = this.environmentForm.get(['config', fieldName]);
        if (control) {
            control.setValue('');
        }
    }

    isSecretField(fieldName: string): boolean {
        return fieldName.toLowerCase().includes('password') ||
            fieldName.toLowerCase().includes('secret') ||
            fieldName.toLowerCase().includes('key') ||
            fieldName.toLowerCase().includes('token');
    }

    hasSecretReference(fieldName: string): boolean {
        return !!this.secretReferences[fieldName];
    }

    getSecretDisplayName(fieldName: string): string {
        return this.secretReferences[fieldName]?.displayName || '';
    }

    // Multiple secrets management
    addSecretMapping(): void {
        if (this.newSecretMapping.secretId && this.newSecretMapping.variableName) {
            this.additionalSecrets.push({
                secretId: this.newSecretMapping.secretId,
                variableName: this.newSecretMapping.variableName,
                mountPath: this.newSecretMapping.mountPath || undefined,
                format: this.newSecretMapping.format || 'env'
            });

            // Reset form
            this.newSecretMapping = { secretId: '', variableName: '', mountPath: '', format: 'env' };
            this.showAddSecretForm = false;
        }
    }

    removeSecretMapping(index: number): void {
        this.additionalSecrets.splice(index, 1);
    }

    getSecretName(secretId: string): string {
        const secret = this.availableSecrets.find(s => s.id === secretId);
        return secret ? secret.name : secretId;
    }

    toggleAddSecretForm(): void {
        this.showAddSecretForm = !this.showAddSecretForm;
        if (!this.showAddSecretForm) {
            // Reset form when closing
            this.newSecretMapping = { secretId: '', variableName: '', mountPath: '', format: 'env' };
        }
    }
}
