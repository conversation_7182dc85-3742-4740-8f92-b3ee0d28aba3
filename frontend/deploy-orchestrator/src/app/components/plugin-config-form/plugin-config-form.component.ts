import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';

export interface PluginConfigField {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'password' | 'select' | 'textarea' | 'url' | 'email';
  label: string;
  description?: string;
  required?: boolean;
  sensitive?: boolean;
  defaultValue?: any;
  options?: { value: any; label: string }[];
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
  };
  placeholder?: string;
  helpText?: string;
}

export interface PluginConfigSchema {
  title: string;
  description?: string;
  fields: PluginConfigField[];
}

@Component({
  selector: 'app-plugin-config-form',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  template: `
    <div class="plugin-config-form">
      <div class="form-header" *ngIf="schema.title || schema.description">
        <h3 class="form-title" *ngIf="schema.title">{{ schema.title }}</h3>
        <p class="form-description" *ngIf="schema.description">{{ schema.description }}</p>
      </div>

      <form [formGroup]="configForm" (ngSubmit)="onSubmit()" class="config-form">
        <div class="form-grid">
          <div
            *ngFor="let field of schema.fields"
            class="form-field"
            [class.form-field-full]="field.type === 'textarea'"
          >
            <label [for]="field.name" class="field-label">
              {{ field.label }}
              <span class="required-indicator" *ngIf="field.required">*</span>
            </label>

            <!-- String/URL/Email Input -->
            <input
              *ngIf="field.type === 'string' || field.type === 'url' || field.type === 'email'"
              [id]="field.name"
              [formControlName]="field.name"
              [type]="getInputType(field.type)"
              [placeholder]="field.placeholder || ''"
              class="form-input"
              [class.error]="hasError(field.name)"
            />

            <!-- Password Input -->
            <div *ngIf="field.type === 'password'" class="password-field">
              <input
                [id]="field.name"
                [formControlName]="field.name"
                [type]="showPassword[field.name] ? 'text' : 'password'"
                [placeholder]="field.placeholder || ''"
                class="form-input"
                [class.error]="hasError(field.name)"
              />
              <button
                type="button"
                class="password-toggle"
                (click)="togglePassword(field.name)"
              >
                <i [class]="showPassword[field.name] ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>

            <!-- Number Input -->
            <input
              *ngIf="field.type === 'number'"
              [id]="field.name"
              [formControlName]="field.name"
              type="number"
              [placeholder]="field.placeholder || ''"
              [min]="field.validation?.min || null"
              [max]="field.validation?.max || null"
              class="form-input"
              [class.error]="hasError(field.name)"
            />

            <!-- Boolean Checkbox -->
            <div *ngIf="field.type === 'boolean'" class="checkbox-field">
              <input
                [id]="field.name"
                [formControlName]="field.name"
                type="checkbox"
                class="form-checkbox"
              />
              <span class="checkbox-label">{{ field.description || 'Enable this option' }}</span>
            </div>

            <!-- Select Dropdown -->
            <div *ngIf="field.type === 'select'" class="select-container">
              <select
                [id]="field.name"
                [formControlName]="field.name"
                class="form-select"
                [class.error]="hasError(field.name)"
                (change)="onSelectChange(field.name, $event)"
              >
                <option value="">Select {{ field.label }}</option>
                <option
                  *ngFor="let option of field.options"
                  [value]="option.value"
                  [disabled]="option.value === '__secret_divider__'"
                  [class.divider]="option.value === '__secret_divider__'"
                >
                  {{ option.label }}
                </option>
              </select>

              <!-- Manual input field for secrets -->
              <div *ngIf="showManualInput[field.name]" class="manual-input-container">
                <input
                  [id]="field.name + '_manual'"
                  type="password"
                  [placeholder]="'Enter ' + field.label.toLowerCase()"
                  class="form-input manual-input"
                  [value]="manualValues[field.name] || ''"
                  (input)="onManualInput(field.name, $event)"
                />
                <button
                  type="button"
                  class="toggle-manual-visibility"
                  (click)="toggleManualVisibility(field.name)"
                >
                  <i [class]="showManualPassword[field.name] ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                </button>
              </div>
            </div>

            <!-- Textarea -->
            <textarea
              *ngIf="field.type === 'textarea'"
              [id]="field.name"
              [formControlName]="field.name"
              [placeholder]="field.placeholder || ''"
              rows="4"
              class="form-textarea"
              [class.error]="hasError(field.name)"
            ></textarea>

            <!-- Field Description -->
            <p *ngIf="field.description && field.type !== 'boolean'" class="field-description">
              {{ field.description }}
            </p>

            <!-- Help Text -->
            <p *ngIf="field.helpText" class="field-help">
              <i class="fas fa-info-circle"></i>
              {{ field.helpText }}
            </p>

            <!-- Error Messages -->
            <div *ngIf="hasError(field.name)" class="field-error">
              <span *ngIf="configForm.get(field.name)?.errors?.['required']">
                {{ field.label }} is required
              </span>
              <span *ngIf="configForm.get(field.name)?.errors?.['pattern']">
                {{ field.label }} format is invalid
              </span>
              <span *ngIf="configForm.get(field.name)?.errors?.['email']">
                Please enter a valid email address
              </span>
              <span *ngIf="configForm.get(field.name)?.errors?.['url']">
                Please enter a valid URL
              </span>
              <span *ngIf="configForm.get(field.name)?.errors?.['min']">
                Minimum value is {{ field.validation?.min }}
              </span>
              <span *ngIf="configForm.get(field.name)?.errors?.['max']">
                Maximum value is {{ field.validation?.max }}
              </span>
              <span *ngIf="configForm.get(field.name)?.errors?.['minlength']">
                Minimum length is {{ field.validation?.minLength }} characters
              </span>
              <span *ngIf="configForm.get(field.name)?.errors?.['maxlength']">
                Maximum length is {{ field.validation?.maxLength }} characters
              </span>
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button
            type="button"
            class="btn btn-secondary"
            (click)="onCancel()"
            [disabled]="loading"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="configForm.invalid || loading"
          >
            <i *ngIf="loading" class="fas fa-spinner fa-spin"></i>
            {{ loading ? 'Saving...' : 'Save Configuration' }}
          </button>
        </div>
      </form>

      <!-- JSON Preview (for debugging) -->
      <div *ngIf="showJsonPreview" class="json-preview">
        <h4>Configuration Preview:</h4>
        <pre>{{ getConfigJson() }}</pre>
      </div>
    </div>
  `,
  styleUrls: ['./plugin-config-form.component.css']
})
export class PluginConfigFormComponent implements OnInit, OnChanges {
  @Input() schema!: PluginConfigSchema;
  @Input() initialConfig: any = {};
  @Input() loading = false;
  @Input() showJsonPreview = false;

  @Output() configChange = new EventEmitter<any>();
  @Output() submit = new EventEmitter<any>();
  @Output() cancel = new EventEmitter<void>();

  configForm!: FormGroup;
  showPassword: { [key: string]: boolean } = {};
  showManualInput: { [key: string]: boolean } = {};
  showManualPassword: { [key: string]: boolean } = {};
  manualValues: { [key: string]: string } = {};

  constructor(private formBuilder: FormBuilder) {}

  ngOnInit(): void {
    this.buildForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['schema'] && !changes['schema'].firstChange) {
      this.buildForm();
    }
    if (changes['initialConfig'] && !changes['initialConfig'].firstChange) {
      this.updateFormValues();
    }
  }

  private buildForm(): void {
    if (!this.schema?.fields) return;

    const formControls: { [key: string]: FormControl } = {};

    this.schema.fields.forEach(field => {
      const validators = this.buildValidators(field);
      const defaultValue = this.getFieldDefaultValue(field);

      formControls[field.name] = new FormControl(defaultValue, validators);

      // Initialize password visibility
      if (field.type === 'password') {
        this.showPassword[field.name] = false;
      }
    });

    this.configForm = this.formBuilder.group(formControls);

    // Emit changes
    this.configForm.valueChanges.subscribe(value => {
      this.configChange.emit(this.processFormValue(value));
    });
  }

  private buildValidators(field: PluginConfigField): any[] {
    const validators = [];

    if (field.required) {
      validators.push(Validators.required);
    }

    if (field.type === 'email') {
      validators.push(Validators.email);
    }

    if (field.type === 'url') {
      validators.push(Validators.pattern(/^https?:\/\/.+/));
    }

    if (field.validation) {
      if (field.validation.pattern) {
        validators.push(Validators.pattern(field.validation.pattern));
      }
      if (field.validation.min !== undefined) {
        validators.push(Validators.min(field.validation.min));
      }
      if (field.validation.max !== undefined) {
        validators.push(Validators.max(field.validation.max));
      }
      if (field.validation.minLength !== undefined) {
        validators.push(Validators.minLength(field.validation.minLength));
      }
      if (field.validation.maxLength !== undefined) {
        validators.push(Validators.maxLength(field.validation.maxLength));
      }
    }

    return validators;
  }

  private getFieldDefaultValue(field: PluginConfigField): any {
    // Check initial config first
    if (this.initialConfig && this.initialConfig[field.name] !== undefined) {
      return this.initialConfig[field.name];
    }

    // Then check field default
    if (field.defaultValue !== undefined) {
      return field.defaultValue;
    }

    // Type-specific defaults
    switch (field.type) {
      case 'boolean':
        return false;
      case 'number':
        return null;
      default:
        return '';
    }
  }

  private updateFormValues(): void {
    if (!this.configForm || !this.initialConfig) return;

    Object.keys(this.initialConfig).forEach(key => {
      if (this.configForm.get(key)) {
        this.configForm.get(key)?.setValue(this.initialConfig[key]);
      }
    });
  }

  private processFormValue(value: any): any {
    // Process form values (e.g., convert strings to numbers, handle empty values)
    const processed: any = {};

    this.schema.fields.forEach(field => {
      let fieldValue = value[field.name];

      // Handle empty values
      if (fieldValue === '' || fieldValue === null) {
        if (field.required) {
          processed[field.name] = fieldValue;
        } else {
          // Don't include optional empty fields
          return;
        }
      } else {
        // Type conversion
        if (field.type === 'number' && typeof fieldValue === 'string') {
          fieldValue = parseFloat(fieldValue);
        }
        processed[field.name] = fieldValue;
      }
    });

    return processed;
  }

  getInputType(fieldType: string): string {
    switch (fieldType) {
      case 'email':
        return 'email';
      case 'url':
        return 'url';
      default:
        return 'text';
    }
  }

  hasError(fieldName: string): boolean {
    const control = this.configForm.get(fieldName);
    return !!(control && control.invalid && (control.dirty || control.touched));
  }

  togglePassword(fieldName: string): void {
    this.showPassword[fieldName] = !this.showPassword[fieldName];
  }

  onSelectChange(fieldName: string, event: any): void {
    const value = event.target.value;

    // Check if user selected "Enter manually"
    if (value === '') {
      this.showManualInput[fieldName] = true;
      this.showManualPassword[fieldName] = false;
      // Clear the select value and use manual input
      this.configForm.get(fieldName)?.setValue('');
    } else {
      this.showManualInput[fieldName] = false;
      this.manualValues[fieldName] = '';
      // Use the selected secret reference
      this.configForm.get(fieldName)?.setValue(value);
    }
  }

  onManualInput(fieldName: string, event: any): void {
    const value = event.target.value;
    this.manualValues[fieldName] = value;
    this.configForm.get(fieldName)?.setValue(value);
  }

  toggleManualVisibility(fieldName: string): void {
    this.showManualPassword[fieldName] = !this.showManualPassword[fieldName];
    const inputElement = document.getElementById(fieldName + '_manual') as HTMLInputElement;
    if (inputElement) {
      inputElement.type = this.showManualPassword[fieldName] ? 'text' : 'password';
    }
  }

  onSubmit(): void {
    if (this.configForm.valid) {
      const config = this.processFormValue(this.configForm.value);
      this.submit.emit(config);
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.configForm.controls).forEach(key => {
        this.configForm.get(key)?.markAsTouched();
      });
    }
  }

  onCancel(): void {
    this.cancel.emit();
  }

  getConfigJson(): string {
    return JSON.stringify(this.processFormValue(this.configForm.value), null, 2);
  }

  resetForm(): void {
    this.configForm.reset();
    this.buildForm();
  }

  validateForm(): boolean {
    return this.configForm.valid;
  }

  getFormErrors(): { [key: string]: any } {
    const errors: { [key: string]: any } = {};
    Object.keys(this.configForm.controls).forEach(key => {
      const control = this.configForm.get(key);
      if (control && control.errors) {
        errors[key] = control.errors;
      }
    });
    return errors;
  }
}
