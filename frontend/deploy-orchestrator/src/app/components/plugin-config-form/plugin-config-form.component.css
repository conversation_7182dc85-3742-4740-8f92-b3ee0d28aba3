.plugin-config-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-header {
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.form-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.form-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-field-full {
  grid-column: 1 / -1;
}

.field-label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.required-indicator {
  color: #ef4444;
  font-weight: bold;
}

.form-input,
.form-select,
.form-textarea {
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
  background: white;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.password-field {
  position: relative;
  display: flex;
  align-items: center;
}

.password-field .form-input {
  padding-right: 48px;
  flex: 1;
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s;
}

.password-toggle:hover {
  color: #374151;
}

.checkbox-field {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
}

.form-checkbox {
  margin-top: 2px;
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

.checkbox-label {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
  flex: 1;
}

.field-description {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.field-help {
  margin: 0;
  font-size: 12px;
  color: #3b82f6;
  line-height: 1.4;
  display: flex;
  align-items: flex-start;
  gap: 6px;
}

.field-help i {
  margin-top: 2px;
  flex-shrink: 0;
}

.field-error {
  margin: 0;
  font-size: 12px;
  color: #ef4444;
  line-height: 1.4;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
  margin-top: 8px;
}

.btn {
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
  border-color: #2563eb;
}

.btn-secondary {
  background: white;
  color: #374151;
  border-color: #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.json-preview {
  margin-top: 32px;
  padding: 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.json-preview h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.json-preview pre {
  margin: 0;
  font-size: 12px;
  color: #1f2937;
  background: white;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Responsive Design */
@media (max-width: 768px) {
  .plugin-config-form {
    padding: 16px;
    margin: 16px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .form-actions {
    flex-direction: column-reverse;
  }

  .btn {
    width: 100%;
  }
}

/* Loading State */
.fa-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Field Type Specific Styles */
.form-textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.form-select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}

/* Secret Support Styles */
.select-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.manual-input-container {
  position: relative;
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.manual-input {
  flex: 1;
  padding-right: 48px;
}

.toggle-manual-visibility {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s;
}

.toggle-manual-visibility:hover {
  color: #374151;
}

.form-select option.divider {
  background: #f3f4f6;
  color: #6b7280;
  font-style: italic;
  font-weight: 500;
}

.form-select option[disabled] {
  color: #9ca3af;
}

/* Secret option styling */
.form-select option[value^="{{secret:"] {
  background: #f0f9ff;
  color: #0369a1;
  font-weight: 500;
}

.form-select option[value^="{{secret:"]:before {
  content: "🔐 ";
}

/* Focus States */
.form-input:focus,
.form-select:focus,
.form-textarea:focus,
.form-checkbox:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

/* Validation States */
.field-error {
  display: flex;
  align-items: center;
  gap: 6px;
}

.field-error::before {
  content: "⚠";
  color: #ef4444;
  font-size: 12px;
}
