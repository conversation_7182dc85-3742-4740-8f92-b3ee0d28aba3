import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { SecretsService, ProjectSecretBinding, SecretVariable, Secret, CreateSecretRequest, BindSecretToProjectRequest } from '../../services/secrets.service';



interface DeploymentSecretsRequest {
  deploymentId: string;
  projectId: string;
  environment: string;
  service?: string;
  namespace?: string;
}

interface WorkflowSecretsRequest {
  workflowId: string;
  executionId: string;
  projectId: string;
  environment?: string;
  service?: string;
  stepName?: string;
}

@Component({
  selector: 'app-project-secrets',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './project-secrets.component.html',
  styleUrls: ['./project-secrets.component.css']
})
export class ProjectSecretsComponent implements OnInit {
  @Input() projectId: string = '';
  @Input() projectName: string = '';

  projectSecrets: ProjectSecretBinding[] = [];
  secretVariables: SecretVariable[] = [];
  availableSecrets: Secret[] = [];

  loading = false;
  error: string | null = null;

  // Filters
  selectedEnvironment = '';
  selectedService = '';
  selectedType = '';
  searchTerm = '';

  // Modal states
  showVariablesModal = false;
  showTestModal = false;
  showCreateSecretModal = false;
  showBindSecretModal = false;
  selectedSecret: ProjectSecretBinding | null = null;

  // Create secret form
  newSecret: CreateSecretRequest = {
    name: '',
    description: '',
    scopeId: '',
    type: 'generic',
    provider: 'internal',
    value: '',
    tags: [],
    metadata: {},
    requiresApproval: false
  };
  newSecretTags = '';

  // Bind secret form
  bindForm: BindSecretToProjectRequest = {
    secretId: '',
    variableName: '',
    variableType: 'env',
    accessLevel: 'read',
    environments: [],
    services: [],
    description: ''
  };
  bindFormEnvironments = '';
  bindFormServices = '';

  // Test deployment/workflow
  testForm = {
    type: 'deployment', // deployment or workflow
    deploymentId: '',
    workflowId: '',
    executionId: '',
    environment: 'production',
    service: '',
    namespace: 'default',
    stepName: ''
  };

  testResults: any = null;

  variableTypes = [
    { value: 'env', label: 'Environment Variable', icon: '🔧' },
    { value: 'file', label: 'Configuration File', icon: '📄' },
    { value: 'config', label: 'Configuration Object', icon: '⚙️' },
    { value: 'mount', label: 'Volume Mount', icon: '💾' }
  ];

  accessLevels = [
    { value: 'read', label: 'Read Only', color: 'green' },
    { value: 'write', label: 'Read/Write', color: 'yellow' },
    { value: 'admin', label: 'Administrator', color: 'red' }
  ];

  constructor(private secretsService: SecretsService) {}

  ngOnInit() {
    if (this.projectId) {
      this.loadProjectSecrets();
      this.loadSecretVariables();
      this.loadAvailableSecrets();
    }
  }

  async loadProjectSecrets() {
    this.loading = true;
    this.error = null;

    try {
      const params: any = {};
      if (this.selectedEnvironment) params.environment = this.selectedEnvironment;
      if (this.selectedService) params.service = this.selectedService;

      const data = await this.secretsService.getProjectSecrets(this.projectId, params).toPromise();
      this.projectSecrets = data?.secrets || [];
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to load project secrets';
      console.error('Error loading project secrets:', error);
    } finally {
      this.loading = false;
    }
  }

  async loadSecretVariables() {
    try {
      const params: any = {};
      if (this.selectedEnvironment) params.environment = this.selectedEnvironment;
      if (this.selectedService) params.service = this.selectedService;
      if (this.selectedType) params.type = this.selectedType;

      const data = await this.secretsService.getProjectSecretVariables(this.projectId, params).toPromise();
      this.secretVariables = data?.variables || [];
    } catch (error) {
      console.error('Error loading secret variables:', error);
    }
  }

  get filteredSecrets() {
    return this.projectSecrets.filter(secret => {
      const matchesSearch = !this.searchTerm ||
        (secret.secret?.name || '').toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        secret.variableName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        secret.description.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesType = !this.selectedType || secret.variableType === this.selectedType;

      return matchesSearch && matchesType;
    });
  }

  onFilterChange() {
    this.loadProjectSecrets();
    this.loadSecretVariables();
  }

  showVariables(secret: ProjectSecretBinding) {
    this.selectedSecret = secret;
    this.showVariablesModal = true;
  }

  getVariablesForSecret(secretId: string): SecretVariable[] {
    return this.secretVariables.filter(v => v.secretId === secretId);
  }

  async unbindSecret(secret: ProjectSecretBinding) {
    if (!confirm(`Are you sure you want to unbind "${secret.secret?.name || 'this secret'}" from this project?`)) {
      return;
    }

    try {
      await this.secretsService.unbindSecretFromProject(this.projectId, secret.id).toPromise();
      this.loadProjectSecrets();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to unbind secret';
    }
  }

  openTestModal() {
    this.testForm = {
      type: 'deployment',
      deploymentId: `deploy_${Date.now()}`,
      workflowId: '',
      executionId: '',
      environment: 'production',
      service: '',
      namespace: 'default',
      stepName: ''
    };
    this.testResults = null;
    this.showTestModal = true;
  }

  async testSecretRetrieval() {
    this.loading = true;
    this.error = null;
    this.testResults = null;

    try {
      if (this.testForm.type === 'deployment') {
        const requestBody = {
          deploymentId: this.testForm.deploymentId,
          projectId: this.projectId,
          environment: this.testForm.environment,
          service: this.testForm.service,
          namespace: this.testForm.namespace
        };
        this.testResults = await this.secretsService.getDeploymentSecrets(requestBody).toPromise();
      } else {
        const requestBody = {
          workflowId: this.testForm.workflowId,
          executionId: this.testForm.executionId,
          projectId: this.projectId,
          environment: this.testForm.environment,
          service: this.testForm.service,
          stepName: this.testForm.stepName
        };
        this.testResults = await this.secretsService.getWorkflowSecrets(requestBody).toPromise();
      }
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to test secret retrieval';
    } finally {
      this.loading = false;
    }
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  getVariableTypeBadgeClass(type: string): string {
    switch (type) {
      case 'env': return 'bg-blue-100 text-blue-800';
      case 'file': return 'bg-green-100 text-green-800';
      case 'config': return 'bg-purple-100 text-purple-800';
      case 'mount': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  getAccessLevelBadgeClass(level: string): string {
    switch (level) {
      case 'read': return 'bg-green-100 text-green-800';
      case 'write': return 'bg-yellow-100 text-yellow-800';
      case 'admin': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'expired': return 'bg-red-100 text-red-800';
      case 'rotating': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  getProviderBadgeClass(provider: string): string {
    switch (provider) {
      case 'internal': return 'bg-blue-100 text-blue-800';
      case 'vault': return 'bg-purple-100 text-purple-800';
      case 'conjur': return 'bg-indigo-100 text-indigo-800';
      case 'aws': return 'bg-orange-100 text-orange-800';
      case 'azure': return 'bg-cyan-100 text-cyan-800';
      case 'gcp': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  getVariableTypeIcon(variableType: string): string {
    const type = this.variableTypes.find(t => t.value === variableType);
    return type ? type.icon : '';
  }

  getVariableTypeLabel(variableType: string): string {
    const type = this.variableTypes.find(t => t.value === variableType);
    return type ? type.label : variableType;
  }

  getAccessLevelLabel(accessLevel: string): string {
    const level = this.accessLevels.find(l => l.value === accessLevel);
    return level ? level.label : accessLevel;
  }

  getVariableCountByType(typeValue: string): number {
    return this.secretVariables.filter(v => v.type === typeValue).length;
  }

  // Load available secrets for binding
  async loadAvailableSecrets() {
    try {
      const data = await this.secretsService.getSecrets().toPromise();
      // Filter out secrets already bound to this project
      const boundSecretIds = this.projectSecrets.map(ps => ps.secretId);
      this.availableSecrets = (data?.secrets || []).filter(secret =>
        !boundSecretIds.includes(secret.id)
      );
    } catch (error) {
      console.error('Error loading available secrets:', error);
    }
  }

  // Create Secret Modal Methods
  async openCreateSecretModal() {
    // Ensure we have a project scope for this project
    const projectScopeId = await this.ensureProjectScope();

    this.newSecret = {
      name: '',
      description: '',
      scopeId: projectScopeId,
      type: 'generic',
      provider: 'internal',
      value: '',
      tags: [],
      metadata: {},
      requiresApproval: false
    };
    this.newSecretTags = '';
    this.showCreateSecretModal = true;
  }

  private async ensureProjectScope(): Promise<string> {
    try {
      // First, try to find an existing project scope for this project
      const scopes = await this.secretsService.getScopes().toPromise();
      const projectScope = scopes?.scopes?.find(scope =>
        scope.type === 'project' &&
        scope.name === `project-${this.projectId}`
      );

      if (projectScope) {
        return projectScope.id;
      }

      // If no project scope exists, create one
      const createScopeRequest = {
        name: `project-${this.projectId}`,
        type: 'project',
        description: `Scope for project ${this.projectName || this.projectId}`,
        config: {
          projectId: this.projectId,
          projectName: this.projectName
        }
      };

      const newScope = await this.secretsService.createScope(createScopeRequest).toPromise();
      return newScope.id;
    } catch (error) {
      console.error('Error ensuring project scope:', error);
      // Fallback to empty string - the backend should handle this gracefully
      return '';
    }
  }

  closeCreateSecretModal() {
    this.showCreateSecretModal = false;
  }

  async createSecret() {
    this.loading = true;
    this.error = null;

    try {
      // Parse tags
      this.newSecret.tags = this.newSecretTags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      // Create the secret
      const createdSecret = await this.secretsService.createSecret(this.newSecret).toPromise();

      if (!createdSecret) {
        throw new Error('Failed to create secret');
      }

      // Automatically bind the new secret to this project
      const bindRequest: BindSecretToProjectRequest = {
        secretId: createdSecret.id,
        variableName: this.newSecret.name.toUpperCase().replace(/[^A-Z0-9]/g, '_'),
        variableType: 'env',
        accessLevel: 'read',
        environments: [],
        services: [],
        description: `Auto-bound secret: ${this.newSecret.description}`
      };

      await this.secretsService.bindSecretToProject(this.projectId, bindRequest).toPromise();

      this.closeCreateSecretModal();
      this.loadProjectSecrets();
      this.loadAvailableSecrets();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to create secret';
    } finally {
      this.loading = false;
    }
  }

  // Bind Secret Modal Methods
  openBindSecretModal() {
    this.bindForm = {
      secretId: '',
      variableName: '',
      variableType: 'env',
      accessLevel: 'read',
      environments: [],
      services: [],
      description: ''
    };
    this.bindFormEnvironments = '';
    this.bindFormServices = '';
    this.loadAvailableSecrets();
    this.showBindSecretModal = true;
  }

  closeBindSecretModal() {
    this.showBindSecretModal = false;
  }

  onSecretSelect() {
    const selectedSecret = this.availableSecrets.find(s => s.id === this.bindForm.secretId);
    if (selectedSecret) {
      // Auto-suggest variable name based on secret name
      this.bindForm.variableName = selectedSecret.name.toUpperCase().replace(/[^A-Z0-9]/g, '_');
      this.bindForm.description = `Bound secret: ${selectedSecret.description}`;
    }
  }

  async bindSecret() {
    this.loading = true;
    this.error = null;

    try {
      // Parse environments and services
      this.bindForm.environments = this.bindFormEnvironments
        .split(',')
        .map(env => env.trim())
        .filter(env => env.length > 0);

      this.bindForm.services = this.bindFormServices
        .split(',')
        .map(service => service.trim())
        .filter(service => service.length > 0);

      await this.secretsService.bindSecretToProject(this.projectId, this.bindForm).toPromise();

      this.closeBindSecretModal();
      this.loadProjectSecrets();
      this.loadAvailableSecrets();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to bind secret';
    } finally {
      this.loading = false;
    }
  }

  getSecretDisplayText(secret: any): string {
    return `${secret.name} (${secret.type})`;
  }
}
