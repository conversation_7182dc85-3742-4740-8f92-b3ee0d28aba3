<div class="p-6">
  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Project Secrets</h1>
      <p class="text-gray-600">Manage secrets for {{ projectName || 'this project' }}</p>
    </div>
    <div class="flex gap-3">
      <button
        (click)="openCreateSecretModal()"
        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
        Create Secret
      </button>
      <button
        (click)="openBindSecretModal()"
        class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
        </svg>
        Bind Existing Secret
      </button>
      <button
        (click)="openTestModal()"
        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Test Secret Retrieval
      </button>
    </div>
  </div>

  <!-- Error Alert -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
    {{ error }}
    <button (click)="error = null" class="float-right text-red-500 hover:text-red-700">×</button>
  </div>

  <!-- Filters -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (input)="onFilterChange()"
          placeholder="Search secrets..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Environment</label>
        <input
          type="text"
          [(ngModel)]="selectedEnvironment"
          (input)="onFilterChange()"
          placeholder="e.g., production"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Service</label>
        <input
          type="text"
          [(ngModel)]="selectedService"
          (input)="onFilterChange()"
          placeholder="e.g., web-service"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Variable Type</label>
        <select
          [(ngModel)]="selectedType"
          (change)="onFilterChange()"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option value="">All Types</option>
          <option *ngFor="let type of variableTypes" [value]="type.value">{{ type.label }}</option>
        </select>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="text-center py-8">
    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <p class="mt-2 text-gray-600">Loading project secrets...</p>
  </div>

  <!-- Project Secrets Table -->
  <div *ngIf="!loading" class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Secret</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Variable</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Access</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Context</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngFor="let secret of filteredSecrets" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
              <div>
                <div class="text-sm font-medium text-gray-900">{{ secret.secret?.name || 'Unknown Secret' }}</div>
                <div class="text-sm text-gray-500">{{ secret.secret?.description || 'No description' }}</div>
                <div class="flex items-center gap-2 mt-1">
                  <span [class]="'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ' + getProviderBadgeClass(secret.secret?.provider || 'unknown')">
                    {{ secret.secret?.provider || 'Unknown' }}
                  </span>
                  <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                    {{ secret.secret?.type || 'Unknown' }}
                  </span>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div>
                <div class="text-sm font-medium text-gray-900 font-mono">{{ secret.variableName }}</div>
                <div class="text-sm text-gray-500">{{ secret.description }}</div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span [class]="'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ' + getVariableTypeBadgeClass(secret.variableType)">
                {{ getVariableTypeIcon(secret.variableType) }} {{ getVariableTypeLabel(secret.variableType) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span [class]="'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ' + getAccessLevelBadgeClass(secret.accessLevel)">
                {{ getAccessLevelLabel(secret.accessLevel) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">
                <div *ngIf="secret.environments && secret.environments.length > 0" class="mb-1">
                  <span class="text-xs text-gray-500">Envs:</span>
                  <span *ngFor="let env of secret.environments; let last = last" class="text-xs">
                    {{ env }}<span *ngIf="!last">, </span>
                  </span>
                </div>
                <div *ngIf="secret.services && secret.services.length > 0">
                  <span class="text-xs text-gray-500">Services:</span>
                  <span *ngFor="let service of secret.services; let last = last" class="text-xs">
                    {{ service }}<span *ngIf="!last">, </span>
                  </span>
                </div>
                <div *ngIf="(!secret.environments || secret.environments.length === 0) && (!secret.services || secret.services.length === 0)" class="text-xs text-gray-500">
                  Global access
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span [class]="'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ' + getStatusBadgeClass(secret.secret?.status || 'unknown')">
                {{ secret.secret?.status || 'Unknown' }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button
                  (click)="showVariables(secret)"
                  class="text-blue-600 hover:text-blue-900">
                  Variables
                </button>
                <button
                  (click)="unbindSecret(secret)"
                  class="text-red-600 hover:text-red-900">
                  Unbind
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <div *ngIf="filteredSecrets.length === 0" class="text-center py-8">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No secrets bound to this project</h3>
      <p class="mt-1 text-sm text-gray-500">Bind secrets to this project to use them in deployments and workflows.</p>
    </div>
  </div>

  <!-- Secret Variables Summary -->
  <div *ngIf="secretVariables.length > 0" class="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Secret Variables Summary</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div *ngFor="let type of variableTypes" class="bg-gray-50 p-3 rounded-lg">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <span class="text-lg mr-2">{{ type.icon }}</span>
            <span class="text-sm font-medium text-gray-700">{{ type.label }}</span>
          </div>
          <span class="text-lg font-bold text-gray-900">
            {{ getVariableCountByType(type.value) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Variables Modal -->
<div *ngIf="showVariablesModal && selectedSecret" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Variables for {{ selectedSecret.secret?.name || 'Unknown Secret' }}</h3>
        <button (click)="showVariablesModal = false" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div class="space-y-4">
        <div *ngFor="let variable of getVariablesForSecret(selectedSecret.secretId)"
             class="border border-gray-200 rounded-lg p-4">
          <div class="flex justify-between items-start mb-2">
            <div>
              <h4 class="text-sm font-medium text-gray-900 font-mono">{{ variable.name }}</h4>
              <span [class]="'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ' + getVariableTypeBadgeClass(variable.type)">
                {{ getVariableTypeLabel(variable.type) }}
              </span>
            </div>
            <span class="text-xs text-gray-500">{{ formatDate(variable.createdAt) }}</span>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div *ngIf="variable.environment">
              <span class="text-gray-500">Environment:</span>
              <span class="ml-1 font-medium">{{ variable.environment }}</span>
            </div>
            <div *ngIf="variable.service">
              <span class="text-gray-500">Service:</span>
              <span class="ml-1 font-medium">{{ variable.service }}</span>
            </div>
            <div *ngIf="variable.namespace">
              <span class="text-gray-500">Namespace:</span>
              <span class="ml-1 font-medium">{{ variable.namespace }}</span>
            </div>
            <div *ngIf="variable.path">
              <span class="text-gray-500">Path:</span>
              <span class="ml-1 font-medium font-mono">{{ variable.path }}</span>
            </div>
            <div *ngIf="variable.format">
              <span class="text-gray-500">Format:</span>
              <span class="ml-1 font-medium">{{ variable.format }}</span>
            </div>
          </div>
        </div>

        <div *ngIf="getVariablesForSecret(selectedSecret.secretId).length === 0"
             class="text-center py-4 text-gray-500">
          No variables configured for this secret.
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Test Secret Retrieval Modal -->
<div *ngIf="showTestModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Test Secret Retrieval</h3>
        <button (click)="showTestModal = false" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <form (ngSubmit)="testSecretRetrieval()" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Test Type</label>
            <select
              [(ngModel)]="testForm.type"
              name="type"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="deployment">Deployment</option>
              <option value="workflow">Workflow</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Environment</label>
            <input
              type="text"
              [(ngModel)]="testForm.environment"
              name="environment"
              required
              placeholder="e.g., production"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
        </div>

        <div *ngIf="testForm.type === 'deployment'" class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Deployment ID</label>
            <input
              type="text"
              [(ngModel)]="testForm.deploymentId"
              name="deploymentId"
              placeholder="e.g., deploy_123456"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Service</label>
            <input
              type="text"
              [(ngModel)]="testForm.service"
              name="service"
              placeholder="e.g., web-service"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Namespace</label>
            <input
              type="text"
              [(ngModel)]="testForm.namespace"
              name="namespace"
              placeholder="e.g., default"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
        </div>

        <div *ngIf="testForm.type === 'workflow'" class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Workflow ID</label>
            <input
              type="text"
              [(ngModel)]="testForm.workflowId"
              name="workflowId"
              placeholder="e.g., workflow_123456"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Execution ID</label>
            <input
              type="text"
              [(ngModel)]="testForm.executionId"
              name="executionId"
              placeholder="e.g., exec_123456"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Service</label>
            <input
              type="text"
              [(ngModel)]="testForm.service"
              name="service"
              placeholder="e.g., deployment-service"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Step Name</label>
            <input
              type="text"
              [(ngModel)]="testForm.stepName"
              name="stepName"
              placeholder="e.g., deploy-step"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
        </div>

        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            (click)="showTestModal = false"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md">
            Cancel
          </button>
          <button
            type="submit"
            [disabled]="loading"
            class="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md disabled:opacity-50">
            <span *ngIf="loading" class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
            Test Retrieval
          </button>
        </div>
      </form>

      <!-- Test Results -->
      <div *ngIf="testResults" class="mt-6 border-t pt-4">
        <h4 class="text-md font-medium text-gray-900 mb-3">Test Results</h4>

        <div class="bg-gray-50 rounded-lg p-4">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 text-sm">
            <div>
              <span class="text-gray-500">Variables Retrieved:</span>
              <span class="ml-1 font-bold text-green-600">{{ testResults.variables?.length || 0 }}</span>
            </div>
            <div>
              <span class="text-gray-500">Errors:</span>
              <span class="ml-1 font-bold" [class]="testResults.errors?.length > 0 ? 'text-red-600' : 'text-green-600'">
                {{ testResults.errors?.length || 0 }}
              </span>
            </div>
            <div>
              <span class="text-gray-500">Retrieved At:</span>
              <span class="ml-1 font-medium">{{ formatDate(testResults.metadata?.retrievedAt) }}</span>
            </div>
          </div>

          <!-- Variables -->
          <div *ngIf="testResults.variables?.length > 0" class="mb-4">
            <h5 class="text-sm font-medium text-gray-700 mb-2">Variables:</h5>
            <div class="space-y-2">
              <div *ngFor="let variable of testResults.variables"
                   class="bg-white border border-gray-200 rounded p-3">
                <div class="flex justify-between items-start mb-2">
                  <div class="flex items-center gap-2">
                    <span class="font-mono text-sm font-medium">{{ variable.name }}</span>
                    <span [class]="'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ' + getVariableTypeBadgeClass(variable.type)">
                      {{ variable.type }}
                    </span>
                  </div>
                  <span class="text-xs text-gray-500">{{ variable.secretId }}</span>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-2 text-xs text-gray-600">
                  <div *ngIf="variable.environment">
                    <strong>Environment:</strong> {{ variable.environment }}
                  </div>
                  <div *ngIf="variable.service">
                    <strong>Service:</strong> {{ variable.service }}
                  </div>
                  <div *ngIf="variable.path">
                    <strong>Path:</strong> {{ variable.path }}
                  </div>
                </div>
                <div *ngIf="variable.value && variable.type === 'env'" class="mt-2 p-2 bg-gray-100 rounded font-mono text-xs">
                  <strong>Value:</strong> {{ variable.value.substring(0, 20) }}{{ variable.value.length > 20 ? '...' : '' }}
                </div>
              </div>
            </div>
          </div>

          <!-- Errors -->
          <div *ngIf="testResults.errors?.length > 0" class="mb-4">
            <h5 class="text-sm font-medium text-red-700 mb-2">Errors:</h5>
            <div class="space-y-1">
              <div *ngFor="let error of testResults.errors"
                   class="bg-red-50 border border-red-200 text-red-700 px-3 py-2 rounded text-sm">
                {{ error }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Create Secret Modal -->
<div *ngIf="showCreateSecretModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Create New Secret</h3>
        <button (click)="closeCreateSecretModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <form (ngSubmit)="createSecret()" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Name *</label>
            <input
              type="text"
              [(ngModel)]="newSecret.name"
              name="name"
              required
              placeholder="e.g., database-credentials"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Type *</label>
            <select
              [(ngModel)]="newSecret.type"
              name="type"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="generic">Generic</option>
              <option value="database">Database</option>
              <option value="api">API Key</option>
              <option value="certificate">Certificate</option>
              <option value="ssh">SSH Key</option>
            </select>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea
            [(ngModel)]="newSecret.description"
            name="description"
            rows="2"
            placeholder="Describe what this secret is used for..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Secret Value *</label>
          <textarea
            [(ngModel)]="newSecret.value"
            name="value"
            rows="4"
            required
            placeholder="Enter the secret value (JSON, plain text, etc.)"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none font-mono"></textarea>
          <p class="mt-1 text-sm text-gray-500">For JSON secrets, use format: {{ '{' }}"key": "value", "another": "secret"{{ '}' }}</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Provider</label>
            <select
              [(ngModel)]="newSecret.provider"
              name="provider"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="internal">Internal</option>
              <option value="vault">HashiCorp Vault</option>
              <option value="aws">AWS Secrets Manager</option>
              <option value="azure">Azure Key Vault</option>
              <option value="gcp">Google Secret Manager</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Tags (comma-separated)</label>
            <input
              type="text"
              [(ngModel)]="newSecretTags"
              name="tags"
              placeholder="e.g., production, database, critical"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
        </div>

        <div class="flex items-center">
          <input
            type="checkbox"
            [(ngModel)]="newSecret.requiresApproval"
            name="requiresApproval"
            id="requiresApproval"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
          <label for="requiresApproval" class="ml-2 block text-sm text-gray-900">
            Requires approval for access
          </label>
        </div>

        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            (click)="closeCreateSecretModal()"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md">
            Cancel
          </button>
          <button
            type="submit"
            [disabled]="loading"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md disabled:opacity-50">
            <span *ngIf="loading" class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
            Create Secret
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Bind Existing Secret Modal -->
<div *ngIf="showBindSecretModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Bind Existing Secret to Project</h3>
        <button (click)="closeBindSecretModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <form (ngSubmit)="bindSecret()" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Select Secret *</label>
          <select
            [(ngModel)]="bindForm.secretId"
            name="secretId"
            required
            (change)="onSecretSelect()"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Choose a secret...</option>
            <option *ngFor="let secret of availableSecrets" [value]="secret.id">{{ getSecretDisplayText(secret) }}</option>
          </select>
          <p class="mt-1 text-sm text-gray-500">Only secrets not already bound to this project are shown</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Variable Name *</label>
            <input
              type="text"
              [(ngModel)]="bindForm.variableName"
              name="variableName"
              required
              placeholder="e.g., DB_PASSWORD"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono">
            <p class="mt-1 text-sm text-gray-500">How this secret will be referenced in deployments</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Variable Type *</label>
            <select
              [(ngModel)]="bindForm.variableType"
              name="variableType"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="env">Environment Variable</option>
              <option value="file">File Mount</option>
              <option value="volume">Volume Mount</option>
            </select>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea
            [(ngModel)]="bindForm.description"
            name="description"
            rows="2"
            placeholder="Describe how this secret is used in this project..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Access Level *</label>
            <select
              [(ngModel)]="bindForm.accessLevel"
              name="accessLevel"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="read">Read Only</option>
              <option value="write">Read/Write</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Environments (comma-separated)</label>
            <input
              type="text"
              [(ngModel)]="bindFormEnvironments"
              name="environments"
              placeholder="e.g., production, staging (leave empty for all)"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Services (comma-separated)</label>
          <input
            type="text"
            [(ngModel)]="bindFormServices"
            name="services"
            placeholder="e.g., web-service, api-service (leave empty for all)"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          <p class="mt-1 text-sm text-gray-500">Restrict access to specific services within the project</p>
        </div>

        <div class="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            (click)="closeBindSecretModal()"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md">
            Cancel
          </button>
          <button
            type="submit"
            [disabled]="loading || !bindForm.secretId || !bindForm.variableName"
            class="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md disabled:opacity-50">
            <span *ngIf="loading" class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
            Bind Secret
          </button>
        </div>
      </form>
    </div>
  </div>
</div>