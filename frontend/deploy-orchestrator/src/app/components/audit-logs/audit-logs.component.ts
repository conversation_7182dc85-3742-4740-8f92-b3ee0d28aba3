import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { AuditService } from '../../services/audit.service';
import { AuthService } from '../../services/auth.service';

interface AuditLog {
  id: string;
  timestamp: Date;
  userId: string;
  username: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: string;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
}

@Component({
  selector: 'app-audit-logs',
  templateUrl: './audit-logs.component.html',
  styleUrls: ['./audit-logs.component.scss']
})
export class AuditLogsComponent implements OnInit {
  auditLogs: AuditLog[] = [];
  filteredLogs: AuditLog[] = [];
  filterForm!: FormGroup;
  loading = false;
  error = '';

  // Pagination
  currentPage = 1;
  pageSize = 50;
  totalLogs = 0;
  totalPages = 0;

  // Make Math available in template
  Math = Math;

  // Filter options
  actionTypes = [
    'LOGIN',
    'LOGOUT',
    'CREATE',
    'UPDATE',
    'DELETE',
    'DEPLOY',
    'SCHEDULE',
    'PERMISSION_CHANGE',
    'ROLE_ASSIGNMENT',
    'PROJECT_ACCESS'
  ];

  resourceTypes = [
    'USER',
    'PROJECT',
    'DEPLOYMENT',
    'SCHEDULE',
    'ROLE',
    'PERMISSION',
    'GROUP',
    'IDENTITY_PROVIDER'
  ];

  constructor(
    private auditService: AuditService,
    private authService: AuthService,
    private formBuilder: FormBuilder
  ) { }

  ngOnInit(): void {
    this.initFilterForm();
    this.loadAuditLogs();
  }

  initFilterForm(): void {
    this.filterForm = this.formBuilder.group({
      startDate: [''],
      endDate: [''],
      userId: [''],
      action: [''],
      resource: [''],
      success: [''],
      searchTerm: ['']
    });

    // Subscribe to form changes for real-time filtering
    this.filterForm.valueChanges.subscribe(() => {
      this.applyFilters();
    });
  }

  loadAuditLogs(): void {
    this.loading = true;
    this.error = '';

    const filters = {
      page: this.currentPage,
      limit: this.pageSize,
      ...this.filterForm.value
    };

    this.auditService.getAuditLogs(filters).subscribe({
      next: (response) => {
        this.auditLogs = response.logs || [];
        this.totalLogs = response.total || 0;
        this.totalPages = Math.ceil(this.totalLogs / this.pageSize);
        this.applyFilters();
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load audit logs. Please try again.';
        console.error('Error loading audit logs', error);
        this.loading = false;
      }
    });
  }

  applyFilters(): void {
    let filtered = [...this.auditLogs];
    const filters = this.filterForm.value;

    // Apply date range filter
    if (filters.startDate) {
      const startDate = new Date(filters.startDate);
      filtered = filtered.filter(log => new Date(log.timestamp) >= startDate);
    }

    if (filters.endDate) {
      const endDate = new Date(filters.endDate);
      endDate.setHours(23, 59, 59, 999); // End of day
      filtered = filtered.filter(log => new Date(log.timestamp) <= endDate);
    }

    // Apply action filter
    if (filters.action) {
      filtered = filtered.filter(log => log.action === filters.action);
    }

    // Apply resource filter
    if (filters.resource) {
      filtered = filtered.filter(log => log.resource === filters.resource);
    }

    // Apply success filter
    if (filters.success !== '') {
      const successValue = filters.success === 'true';
      filtered = filtered.filter(log => log.success === successValue);
    }

    // Apply search term filter
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(log =>
        log.username.toLowerCase().includes(searchTerm) ||
        log.action.toLowerCase().includes(searchTerm) ||
        log.resource.toLowerCase().includes(searchTerm) ||
        (log.details && log.details.toLowerCase().includes(searchTerm))
      );
    }

    this.filteredLogs = filtered;
  }

  clearFilters(): void {
    this.filterForm.reset();
    this.currentPage = 1;
    this.loadAuditLogs();
  }

  exportLogs(): void {
    const filters = {
      ...this.filterForm.value,
      export: true
    };

    this.auditService.exportAuditLogs(filters).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.error = 'Failed to export audit logs. Please try again.';
        console.error('Error exporting audit logs', error);
      }
    });
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.loadAuditLogs();
    }
  }

  getActionColor(action: string): string {
    switch (action) {
      case 'LOGIN':
        return 'bg-green-100 text-green-800';
      case 'LOGOUT':
        return 'bg-gray-100 text-gray-800';
      case 'CREATE':
        return 'bg-blue-100 text-blue-800';
      case 'UPDATE':
        return 'bg-yellow-100 text-yellow-800';
      case 'DELETE':
        return 'bg-red-100 text-red-800';
      case 'DEPLOY':
        return 'bg-purple-100 text-purple-800';
      case 'PERMISSION_CHANGE':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getSuccessColor(success: boolean): string {
    return success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  }

  getSuccessText(success: boolean): string {
    return success ? 'Success' : 'Failed';
  }

  formatDetails(details?: string): string {
    if (!details) return 'N/A';
    try {
      const parsed = JSON.parse(details);
      return JSON.stringify(parsed, null, 2);
    } catch {
      return details;
    }
  }
}
