import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

import { DeploymentManagementService, PromotionRequest } from '../../services/deployment-management.service';
import { WorkflowService } from '../../services/workflow.service';
import { NotificationService } from '../../services/notification.service';

export interface PromotionDialogData {
  entityName: string;
  entityType: 'application' | 'component';
  entityId: string;
  version: string;
  sourceEnvironmentId: string;
  sourceEnvironmentName: string;
  targetEnvironmentId: string;
  targetEnvironmentName: string;
  projectId: string;
}

@Component({
  selector: 'app-promotion-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule
  ],
  templateUrl: './promotion-dialog.component.html',
  styleUrls: ['./promotion-dialog.component.scss']
})
export class PromotionDialogComponent implements OnInit, OnDestroy {
  @Input() data!: PromotionDialogData;
  @Input() isVisible = false;
  @Output() close = new EventEmitter<any>();
  @Output() submit = new EventEmitter<PromotionRequest>();

  private destroy$ = new Subject<void>();

  promotionForm!: FormGroup;
  workflows: any[] = [];
  providers: any[] = [];
  loading = false;
  submitting = false;

  constructor(
    private fb: FormBuilder,
    private deploymentService: DeploymentManagementService,
    private workflowService: WorkflowService,
    private notificationService: NotificationService
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    if (this.data?.projectId) {
      this.loadWorkflows();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.promotionForm = this.fb.group({
      workflowId: ['', Validators.required],
      providerType: ['', Validators.required],
      approvalRequired: [true],
      parameters: this.fb.group({}),
      secretMappings: this.fb.group({}),
      configuration: this.fb.group({})
    });
  }

  private loadWorkflows(): void {
    this.loading = true;
    this.workflowService.getWorkflows(this.data.projectId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (workflows) => {
          this.workflows = workflows;
          this.loading = false;
        },
        error: (error) => {
          this.notificationService.error('Failed to load workflows', 'Please try again');
          this.loading = false;
        }
      });
  }

  onWorkflowChange(): void {
    const workflowId = this.promotionForm.get('workflowId')?.value;
    if (workflowId) {
      const selectedWorkflow = this.workflows.find(w => w.id === workflowId);
      if (selectedWorkflow) {
        this.providers = (selectedWorkflow as any).providers || [];
      }
    }
  }

  onSubmit(): void {
    if (this.promotionForm.valid) {
      this.submitting = true;
      
      const formValue = this.promotionForm.value;
      const request: PromotionRequest = {
        projectId: this.data.projectId,
        sourceEnvironmentId: this.data.sourceEnvironmentId,
        targetEnvironmentId: this.data.targetEnvironmentId,
        version: this.data.version,
        workflowId: formValue.workflowId,
        providerType: formValue.providerType,
        parameters: formValue.parameters || {},
        secretMappings: formValue.secretMappings || {},
        configuration: formValue.configuration || {}
      };

      if (this.data.entityType === 'application') {
        request.applicationId = this.data.entityId;
      } else {
        request.componentId = this.data.entityId;
      }

      this.deploymentService.promoteVersion(request)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            this.notificationService.success('Promotion created successfully', 'Promotion started');
            this.submit.emit(request);
            this.close.emit(response);
          },
          error: (error) => {
            this.notificationService.error('Failed to create promotion', 'Please try again');
            this.submitting = false;
          }
        });
    }
  }

  onCancel(): void {
    this.close.emit();
  }

  updateFormField(fieldName: string, event: any): void {
    try {
      const value = JSON.parse(event.target.value);
      this.promotionForm.get(fieldName)?.setValue(value);
    } catch (e) {
      // Invalid JSON, ignore
    }
  }
}
