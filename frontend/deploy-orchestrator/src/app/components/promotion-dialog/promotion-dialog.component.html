<!-- Overlay -->
<div *ngIf="isVisible" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
  <!-- Dialog -->
  <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <h2 class="text-xl font-semibold text-gray-900">
        Promote {{ data?.entityName }}
      </h2>
    </div>

    <!-- Content -->
    <div class="px-6 py-4 space-y-6">
      <!-- Promotion Summary -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 class="text-sm font-medium text-blue-900 mb-2">Promotion Details</h3>
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-gray-600">{{ data?.entityType | titlecase }}:</span>
            <span class="font-medium text-gray-900 ml-2">{{ data?.entityName }}</span>
          </div>
          <div>
            <span class="text-gray-600">Version:</span>
            <span class="font-medium text-gray-900 ml-2">{{ data?.version }}</span>
          </div>
          <div>
            <span class="text-gray-600">From:</span>
            <span class="font-medium text-gray-900 ml-2">{{ data?.sourceEnvironmentName }}</span>
          </div>
          <div>
            <span class="text-gray-600">To:</span>
            <span class="font-medium text-gray-900 ml-2">{{ data?.targetEnvironmentName }}</span>
          </div>
        </div>
      </div>

      <!-- Promotion Form -->
      <form [formGroup]="promotionForm" class="space-y-4">
        <!-- Workflow Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Workflow</label>
          <select formControlName="workflowId" (change)="onWorkflowChange()"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Select a workflow</option>
            <option *ngFor="let workflow of workflows" [value]="workflow.id">
              {{ workflow.name }}
            </option>
          </select>
          <div *ngIf="promotionForm.get('workflowId')?.hasError('required') && promotionForm.get('workflowId')?.touched"
               class="text-red-600 text-sm mt-1">
            Workflow is required
          </div>
        </div>

        <!-- Provider Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Provider</label>
          <select formControlName="providerType"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Select a provider</option>
            <option *ngFor="let provider of providers" [value]="provider.type">
              {{ provider.name }}
            </option>
          </select>
          <div *ngIf="promotionForm.get('providerType')?.hasError('required') && promotionForm.get('providerType')?.touched"
               class="text-red-600 text-sm mt-1">
            Provider is required
          </div>
        </div>

        <!-- Approval Required -->
        <div class="flex items-center">
          <input type="checkbox" formControlName="approvalRequired"
                 class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
          <label class="ml-2 text-sm text-gray-700">
            Require approval before promotion
          </label>
        </div>

        <!-- Advanced Configuration (Expandable) -->
        <details class="border border-gray-200 rounded-lg">
          <summary class="px-4 py-3 cursor-pointer text-sm font-medium text-gray-700 hover:bg-gray-50">
            Advanced Configuration
          </summary>
          <div class="px-4 pb-4 space-y-4">
          <!-- Parameters -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Parameters</label>
            <div class="text-sm text-gray-500 mb-2">
              Additional parameters for the promotion workflow
            </div>
            <textarea 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="3"
              placeholder='{"key": "value"}'
              (blur)="updateFormField('parameters', $event)">
            </textarea>
          </div>

          <!-- Secret Mappings -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Secret Mappings</label>
            <div class="text-sm text-gray-500 mb-2">
              Map secrets for the target environment
            </div>
            <textarea 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="3"
              placeholder='{"SECRET_NAME": "target-secret-name"}'
              (blur)="updateFormField('secretMappings', $event)">
            </textarea>
          </div>

          <!-- Configuration -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Configuration</label>
            <div class="text-sm text-gray-500 mb-2">
              Additional configuration for the promotion
            </div>
            <textarea 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="3"
              placeholder='{"strategy": "blue-green"}'
              (blur)="updateFormField('configuration', $event)">
            </textarea>
          </div>
          </div>
        </details>
      </form>

      <!-- Loading State -->
      <div *ngIf="loading" class="flex items-center justify-center py-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span class="ml-3 text-gray-600">Loading workflows...</span>
      </div>
    </div>

    <!-- Footer -->
    <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
      <button type="button" (click)="onCancel()" [disabled]="submitting"
              class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
        Cancel
      </button>
      <button type="button" (click)="onSubmit()" [disabled]="!promotionForm.valid || submitting"
              class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
        <span *ngIf="!submitting">Create Promotion</span>
        <span *ngIf="submitting" class="flex items-center">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          Creating...
        </span>
      </button>
    </div>
  </div>
</div>
