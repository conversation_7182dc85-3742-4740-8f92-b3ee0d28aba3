<div class="promotion-dialog">
  <h2 mat-dialog-title class="text-xl font-semibold text-gray-900 mb-4">
    Promote {{ data.entityName }}
  </h2>

  <mat-dialog-content class="space-y-6">
    <!-- Promotion Summary -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <h3 class="text-sm font-medium text-blue-900 mb-2">Promotion Details</h3>
      <div class="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span class="text-gray-600">{{ data.entityType | titlecase }}:</span>
          <span class="font-medium text-gray-900 ml-2">{{ data.entityName }}</span>
        </div>
        <div>
          <span class="text-gray-600">Version:</span>
          <span class="font-medium text-gray-900 ml-2">{{ data.version }}</span>
        </div>
        <div>
          <span class="text-gray-600">From:</span>
          <span class="font-medium text-gray-900 ml-2">{{ data.sourceEnvironmentName }}</span>
        </div>
        <div>
          <span class="text-gray-600">To:</span>
          <span class="font-medium text-gray-900 ml-2">{{ data.targetEnvironmentName }}</span>
        </div>
      </div>
    </div>

    <!-- Promotion Form -->
    <form [formGroup]="promotionForm" class="space-y-4">
      <!-- Workflow Selection -->
      <mat-form-field appearance="outline" class="w-full">
        <mat-label>Workflow</mat-label>
        <mat-select formControlName="workflowId" (selectionChange)="onWorkflowChange()">
          <mat-option *ngFor="let workflow of workflows" [value]="workflow.id">
            {{ workflow.name }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="promotionForm.get('workflowId')?.hasError('required')">
          Workflow is required
        </mat-error>
      </mat-form-field>

      <!-- Provider Selection -->
      <mat-form-field appearance="outline" class="w-full">
        <mat-label>Provider</mat-label>
        <mat-select formControlName="providerType">
          <mat-option *ngFor="let provider of providers" [value]="provider.type">
            {{ provider.name }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="promotionForm.get('providerType')?.hasError('required')">
          Provider is required
        </mat-error>
      </mat-form-field>

      <!-- Approval Required -->
      <div class="flex items-center">
        <mat-checkbox formControlName="approvalRequired">
          Require approval before promotion
        </mat-checkbox>
      </div>

      <!-- Advanced Configuration (Expandable) -->
      <details class="border border-gray-200 rounded-lg">
        <summary class="px-4 py-3 cursor-pointer text-sm font-medium text-gray-700 hover:bg-gray-50">
          Advanced Configuration
        </summary>
        <div class="px-4 pb-4 space-y-4">
          <!-- Parameters -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Parameters</label>
            <div class="text-sm text-gray-500 mb-2">
              Additional parameters for the promotion workflow
            </div>
            <textarea 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="3"
              placeholder='{"key": "value"}'
              (blur)="updateFormField('parameters', $event)">
            </textarea>
          </div>

          <!-- Secret Mappings -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Secret Mappings</label>
            <div class="text-sm text-gray-500 mb-2">
              Map secrets for the target environment
            </div>
            <textarea 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="3"
              placeholder='{"SECRET_NAME": "target-secret-name"}'
              (blur)="updateFormField('secretMappings', $event)">
            </textarea>
          </div>

          <!-- Configuration -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Configuration</label>
            <div class="text-sm text-gray-500 mb-2">
              Additional configuration for the promotion
            </div>
            <textarea 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="3"
              placeholder='{"strategy": "blue-green"}'
              (blur)="updateFormField('configuration', $event)">
            </textarea>
          </div>
        </div>
      </details>
    </form>

    <!-- Loading State -->
    <div *ngIf="loading" class="flex items-center justify-center py-4">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span class="ml-3 text-gray-600">Loading workflows...</span>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end" class="pt-4 border-t border-gray-200">
    <button mat-button (click)="onCancel()" [disabled]="submitting">
      Cancel
    </button>
    <button 
      mat-raised-button 
      color="primary" 
      (click)="onSubmit()" 
      [disabled]="!promotionForm.valid || submitting">
      <span *ngIf="!submitting">Create Promotion</span>
      <span *ngIf="submitting" class="flex items-center">
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
        Creating...
      </span>
    </button>
  </mat-dialog-actions>
</div>
