.promotion-dialog {
  min-width: 500px;
  max-width: 600px;

  .mat-mdc-dialog-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .mat-mdc-form-field {
    width: 100%;
  }

  details {
    summary {
      &::-webkit-details-marker {
        display: none;
      }
      
      &::before {
        content: '▶';
        display: inline-block;
        margin-right: 8px;
        transition: transform 0.2s;
      }
    }

    &[open] summary::before {
      transform: rotate(90deg);
    }
  }

  .promotion-summary {
    background-color: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 16px;

    h3 {
      color: #1e40af;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .summary-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      font-size: 14px;

      .summary-item {
        .label {
          color: #6b7280;
        }

        .value {
          color: #111827;
          font-weight: 500;
          margin-left: 8px;
        }
      }
    }
  }

  .advanced-config {
    border: 1px solid #e5e7eb;
    border-radius: 8px;

    summary {
      padding: 12px 16px;
      cursor: pointer;
      font-weight: 500;
      color: #374151;
      background-color: #f9fafb;
      border-radius: 8px 8px 0 0;

      &:hover {
        background-color: #f3f4f6;
      }
    }

    .config-content {
      padding: 16px;
      border-top: 1px solid #e5e7eb;

      .config-field {
        margin-bottom: 16px;

        label {
          display: block;
          font-weight: 500;
          color: #374151;
          margin-bottom: 8px;
        }

        .field-description {
          font-size: 12px;
          color: #6b7280;
          margin-bottom: 8px;
        }

        textarea {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
          resize: vertical;

          &:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }

          &::placeholder {
            color: #9ca3af;
          }
        }
      }
    }
  }

  .loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px;

    .spinner {
      width: 32px;
      height: 32px;
      border: 2px solid #e5e7eb;
      border-top: 2px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      margin-left: 12px;
      color: #6b7280;
    }
  }

  .dialog-actions {
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;

    .mat-mdc-button {
      margin-left: 8px;
    }

    .submit-button {
      .spinner-small {
        width: 16px;
        height: 16px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 8px;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive adjustments
@media (max-width: 640px) {
  .promotion-dialog {
    min-width: 90vw;
    max-width: 90vw;

    .promotion-summary .summary-grid {
      grid-template-columns: 1fr;
      gap: 8px;
    }
  }
}
