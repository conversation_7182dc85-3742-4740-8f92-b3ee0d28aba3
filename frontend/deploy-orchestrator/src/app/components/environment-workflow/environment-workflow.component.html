<div class="environment-workflow-container">
  <!-- Header -->
  <div class="header">
    <h2 class="title">Deploy to Environment</h2>
    <p class="subtitle">Select an environment and workflow to start a deployment</p>
  </div>

  <!-- Error Display -->
  <div *ngIf="error" class="error-banner">
    <i class="icon-alert"></i>
    <span>{{ error }}</span>
    <button type="button" class="close-btn" (click)="error = null">×</button>
  </div>

  <!-- Main Content -->
  <div class="content-grid">
    <!-- Deployment Form -->
    <div class="deployment-form-section">
      <div class="card">
        <div class="card-header">
          <h3>Deployment Configuration</h3>
        </div>

        <form [formGroup]="deploymentForm" (ngSubmit)="startExecution()" class="deployment-form">
          <!-- Environment Selection -->
          <div class="form-group">
            <label for="environmentId" class="form-label">
              Target Environment *
            </label>
            <select
              id="environmentId"
              formControlName="environmentId"
              class="form-select"
              [class.invalid]="isFieldInvalid('environmentId')">
              <option value="">Select an environment...</option>
              <option *ngFor="let env of environments" [value]="env.id">
                {{ env.name }} ({{ env.provider.type | uppercase }})
                <span class="status-indicator" [class]="getEnvironmentStatusClass(env)">
                  {{ env.status }}
                </span>
              </option>
            </select>
            <div *ngIf="isFieldInvalid('environmentId')" class="field-error">
              {{ getFieldError('environmentId') }}
            </div>
          </div>

          <!-- Environment Info -->
          <div *ngIf="selectedEnvironment" class="environment-info">
            <div class="info-card">
              <div class="info-header">
                <h4>{{ selectedEnvironment.name }}</h4>
                <span class="status-badge" [class]="getEnvironmentStatusClass(selectedEnvironment)">
                  {{ selectedEnvironment.status }}
                </span>
              </div>
              <div class="info-details">
                <div class="detail-item">
                  <span class="label">Provider:</span>
                  <span class="value">{{ selectedEnvironment.provider.type | uppercase }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">Region:</span>
                  <span class="value">{{ selectedEnvironment.provider.config.region || 'N/A' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">Type:</span>
                  <span class="value">{{ selectedEnvironment.type }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Workflow Selection -->
          <div class="form-group">
            <label for="workflowId" class="form-label">
              Workflow *
            </label>
            <select
              id="workflowId"
              formControlName="workflowId"
              class="form-select"
              [class.invalid]="isFieldInvalid('workflowId')">
              <option value="">Select a workflow...</option>
              <option *ngFor="let workflow of workflows" [value]="workflow.id">
                {{ workflow.name }} - {{ workflow.description }}
              </option>
            </select>
            <div *ngIf="isFieldInvalid('workflowId')" class="field-error">
              {{ getFieldError('workflowId') }}
            </div>
          </div>

          <!-- Version Information -->
          <div class="form-group" formGroupName="version">
            <label class="form-label">Version Information *</label>
            <div class="version-grid">
              <div class="version-field">
                <label for="version-number">Version</label>
                <input
                  id="version-number"
                  type="text"
                  formControlName="number"
                  placeholder="1.0.0"
                  class="form-input">
              </div>
              <div class="version-field">
                <label for="git-branch">Git Branch</label>
                <input
                  id="git-branch"
                  type="text"
                  formControlName="gitBranch"
                  placeholder="main"
                  class="form-input">
              </div>
              <div class="version-field">
                <label for="git-commit">Git Commit</label>
                <input
                  id="git-commit"
                  type="text"
                  formControlName="gitCommit"
                  placeholder="abc123..."
                  class="form-input">
              </div>
              <div class="version-field">
                <label for="build-id">Build ID</label>
                <input
                  id="build-id"
                  type="text"
                  formControlName="buildId"
                  placeholder="build-456"
                  class="form-input">
              </div>
            </div>
          </div>

          <!-- Workflow Parameters -->
          <div *ngIf="selectedWorkflow && selectedWorkflow.parameters?.length > 0" class="form-group">
            <label class="form-label">Workflow Parameters</label>
            <div formGroupName="parameters" class="parameters-grid">
              <div *ngFor="let param of selectedWorkflow.parameters" class="parameter-field">
                <label [for]="'param-' + param.name">
                  {{ param.label || param.name }}
                  <span *ngIf="param.required" class="required">*</span>
                </label>
                <input
                  [id]="'param-' + param.name"
                  [type]="param.type || 'text'"
                  [formControlName]="param.name"
                  [placeholder]="param.placeholder || param.description"
                  class="form-input">
                <small *ngIf="param.description" class="field-help">
                  {{ param.description }}
                </small>
              </div>
            </div>
          </div>

          <!-- Advanced Options -->
          <div class="advanced-section">
            <button
              type="button"
              class="advanced-toggle"
              (click)="showAdvanced = !showAdvanced">
              <i class="icon-chevron" [class.rotated]="showAdvanced"></i>
              Advanced Options
            </button>

            <div *ngIf="showAdvanced" class="advanced-content">
              <!-- Workflow Variables -->
              <div *ngIf="selectedWorkflow && selectedWorkflow.variables?.length > 0" class="form-group">
                <label class="form-label">Workflow Variables</label>
                <div formGroupName="variables" class="variables-grid">
                  <div *ngFor="let variable of selectedWorkflow.variables" class="variable-field">
                    <label [for]="'var-' + variable.name">{{ variable.name }}</label>
                    <input
                      [id]="'var-' + variable.name"
                      type="text"
                      [formControlName]="variable.name"
                      [placeholder]="variable.default"
                      class="form-input">
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="form-actions">
            <button
              type="button"
              class="btn btn-secondary"
              (click)="resetForm()"
              [disabled]="loading">
              Reset
            </button>
            <button
              type="submit"
              class="btn btn-primary"
              [disabled]="deploymentForm.invalid || loading">
              <i *ngIf="loading" class="icon-spinner spinning"></i>
              <i *ngIf="!loading" class="icon-deploy"></i>
              {{ loading ? 'Starting...' : 'Start Deployment' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Execution History -->
    <div class="execution-history-section">
      <div class="card">
        <div class="card-header">
          <h3>Recent Executions</h3>
          <button class="btn btn-sm btn-outline" (click)="loadExecutionHistory()">
            <i class="icon-refresh"></i>
            Refresh
          </button>
        </div>

        <div class="execution-list">
          <div *ngIf="executionHistory.length === 0" class="empty-state">
            <i class="icon-history"></i>
            <p>No recent executions</p>
          </div>

          <div *ngFor="let execution of executionHistory"
               class="execution-item"
               (click)="viewExecution(execution)">
            <div class="execution-header">
              <div class="execution-info">
                <span class="execution-id">{{ execution.id.substring(0, 8) }}</span>
                <span class="execution-workflow">{{ execution.workflow?.name || 'Unknown Workflow' }}</span>
              </div>
              <span class="status-badge" [class]="getExecutionStatusClass(execution)">
                {{ execution.status }}
              </span>
            </div>

            <div class="execution-details">
              <div class="detail-row">
                <span class="label">Version:</span>
                <span class="value">{{ execution.version.number }}</span>
              </div>
              <div class="detail-row">
                <span class="label">Started:</span>
                <span class="value">{{ execution.startedAt | date:'short' }}</span>
              </div>
              <div class="detail-row">
                <span class="label">Duration:</span>
                <span class="value">{{ formatDuration(execution.duration) }}</span>
              </div>
            </div>

            <div *ngIf="execution.status === 'running'" class="execution-progress">
              <div class="progress-bar">
                <div class="progress-fill"
                     [style.width.%]="getExecutionProgress(execution)"></div>
              </div>
              <span class="progress-text">{{ getExecutionProgress(execution) }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Current Execution Monitor -->
  <div *ngIf="currentExecution" class="current-execution-monitor">
    <div class="monitor-card">
      <div class="monitor-header">
        <h3>Current Execution</h3>
        <span class="status-badge" [class]="getExecutionStatusClass(currentExecution)">
          {{ currentExecution.status }}
        </span>
      </div>

      <div class="monitor-content">
        <div class="execution-summary">
          <div class="summary-item">
            <span class="label">Execution ID:</span>
            <span class="value">{{ currentExecution.id }}</span>
          </div>
          <div class="summary-item">
            <span class="label">Environment:</span>
            <span class="value">{{ selectedEnvironment?.name }}</span>
          </div>
          <div class="summary-item">
            <span class="label">Progress:</span>
            <span class="value">{{ getExecutionProgress(currentExecution) }}%</span>
          </div>
        </div>

        <div class="monitor-actions">
          <button class="btn btn-sm btn-outline" (click)="viewExecution(currentExecution)">
            <i class="icon-eye"></i>
            View Details
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
