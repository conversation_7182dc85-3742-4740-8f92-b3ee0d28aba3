import { <PERSON>mponent, <PERSON>Init, OnD<PERSON>roy, OnChanges, SimpleChanges, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Observable, firstValueFrom, Subject } from 'rxjs';
import { map, startWith, takeUntil, take, filter } from 'rxjs/operators';

import { EnvironmentService, EnvironmentConfig } from '../../services/environment.service';
import { WorkflowService } from '../../services/workflow.service';
import { WorkflowExecutionService, StartExecutionRequest, WorkflowExecution } from '../../services/workflow-execution.service';
import { ProjectService } from '../../services/project.service';
import { Project } from '../../models/project.model';

@Component({
  selector: 'app-environment-workflow',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './environment-workflow.component.html',
  styleUrls: ['./environment-workflow.component.css']
})
export class EnvironmentWorkflowComponent implements OnInit, OnDestroy, OnChanges {
  @Input() projectId!: string;
  @Input() selectedProjectId?: string;
  @Input() workflowId?: string;
  @Input() environmentId?: string;
  @Output() executionStarted = new EventEmitter<WorkflowExecution>();

  // Project management
  selectedProject: Project | null = null;
  currentProjectId: string = '';
  private destroy$ = new Subject<void>();

  // Form and data
  deploymentForm: FormGroup;
  environments: EnvironmentConfig[] = [];
  workflows: any[] = [];
  selectedEnvironment?: EnvironmentConfig;
  selectedWorkflow?: any;

  // UI state
  loading = false;
  error: string | null = null;
  showAdvanced = false;

  // Execution tracking
  currentExecution?: WorkflowExecution;
  executionHistory: WorkflowExecution[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private environmentService: EnvironmentService,
    private workflowService: WorkflowService,
    private workflowExecutionService: WorkflowExecutionService,
    private projectService: ProjectService
  ) {
    this.deploymentForm = this.createForm();
  }

  ngOnInit(): void {
    console.log('🚀 Environment-Workflow: Component initializing...', {
      projectId: this.projectId,
      selectedProjectId: this.selectedProjectId,
      currentProjectId: this.currentProjectId
    });

    this.subscribeToProjectChanges();
    this.initializeProjectId();
    this.setupFormSubscriptions();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Handle changes to input properties
    if (changes['selectedProjectId'] && !changes['selectedProjectId'].firstChange) {
      const newProjectId = changes['selectedProjectId'].currentValue;
      console.log('🔄 Environment-Workflow: selectedProjectId input changed:', {
        previous: changes['selectedProjectId'].previousValue,
        current: newProjectId
      });

      if (newProjectId && newProjectId !== this.currentProjectId) {
        this.currentProjectId = newProjectId;
        this.loadData();
      }
    }

    if (changes['projectId'] && !changes['projectId'].firstChange) {
      const newProjectId = changes['projectId'].currentValue;
      console.log('🔄 Environment-Workflow: projectId input changed:', {
        previous: changes['projectId'].previousValue,
        current: newProjectId
      });

      if (newProjectId && newProjectId !== this.currentProjectId) {
        this.currentProjectId = newProjectId;
        this.loadData();
      }
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createForm(): FormGroup {
    return this.formBuilder.group({
      environmentId: ['', Validators.required],
      workflowId: ['', Validators.required],
      version: this.formBuilder.group({
        number: ['', Validators.required],
        gitCommit: [''],
        gitBranch: ['main'],
        gitTag: [''],
        buildId: ['']
      }),
      parameters: this.formBuilder.group({}),
      variables: this.formBuilder.group({})
    });
  }

  private setupFormSubscriptions(): void {
    // Watch environment selection
    this.deploymentForm.get('environmentId')?.valueChanges.subscribe(environmentId => {
      if (environmentId) {
        this.selectedEnvironment = this.environments.find(env => env.id === environmentId);
        this.validateEnvironmentHealth();
      }
    });

    // Watch workflow selection
    this.deploymentForm.get('workflowId')?.valueChanges.subscribe(workflowId => {
      if (workflowId) {
        this.selectedWorkflow = this.workflows.find(wf => wf.id === workflowId);
        this.updateWorkflowParameters();
      }
    });
  }

  private async loadData(): Promise<void> {
    // Don't load data if no project is selected
    const projectId = this.currentProjectId || this.projectId || this.selectedProjectId;
    console.log('🔍 Environment-Workflow: Loading data for project:', {
      projectId,
      currentProjectId: this.currentProjectId,
      inputProjectId: this.projectId,
      selectedProjectId: this.selectedProjectId
    });

    if (!projectId) {
      console.warn('⚠️ Environment-Workflow: No project selected, using mock data for development');
      this.environments = [];
      this.workflows = [];
      this.error = 'No project selected. Using mock data for development.';
      this.loadMockData();
      return;
    }

    this.loading = true;
    this.error = null;

    try {
      console.log('📡 Environment-Workflow: Making API calls for project:', projectId);

      //// Load environments and workflows in parallel
      //const [environmentsResponse, workflowsResponse] = await Promise.all([
      //  this.environmentService.getEnvironments({ projectId }).toPromise(),
      //  this.workflowService.getWorkflows(projectId).firstValueFrom()
      //]);

      const [environmentsResponse, workflowsResponse] = await Promise.all([
        firstValueFrom(this.environmentService.getEnvironments({ projectId })),
        firstValueFrom(this.workflowService.getWorkflows(projectId))
      ]);

      // Log the raw API responses to understand the data structure
      console.log('🔍 Environment-Workflow: Raw API responses:', {
        environmentsResponse,
        workflowsResponse,
        environmentsType: typeof environmentsResponse,
        workflowsType: typeof workflowsResponse,
        environmentsKeys: environmentsResponse ? Object.keys(environmentsResponse) : 'null',
        workflowsKeys: workflowsResponse ? Object.keys(workflowsResponse) : 'null'
      });

      // Handle different possible response structures
      let environments: any[] = [];
      let workflows: any[] = [];

      // Try different ways to extract environments data
      if (Array.isArray(environmentsResponse)) {
        environments = environmentsResponse;
        console.log('✅ Environment-Workflow: environmentsResponse is array');
      } else if (environmentsResponse?.environments && Array.isArray(environmentsResponse.environments)) {
        environments = environmentsResponse.environments;
        console.log('✅ Environment-Workflow: environmentsResponse.environments is array');
      } else if ((environmentsResponse as any)?.data && Array.isArray((environmentsResponse as any).data)) {
        environments = (environmentsResponse as any).data;
        console.log('✅ Environment-Workflow: environmentsResponse.data is array');
      } else {
        console.warn('⚠️ Environment-Workflow: Unexpected environments response structure');
        environments = [];
      }

      // Try different ways to extract workflows data
      if (Array.isArray(workflowsResponse)) {
        workflows = workflowsResponse;
        console.log('✅ Environment-Workflow: workflowsResponse is array');
      } else if ((workflowsResponse as any)?.workflows && Array.isArray((workflowsResponse as any).workflows)) {
        workflows = (workflowsResponse as any).workflows;
        console.log('✅ Environment-Workflow: workflowsResponse.workflows is array');
      } else if ((workflowsResponse as any)?.data && Array.isArray((workflowsResponse as any).data)) {
        workflows = (workflowsResponse as any).data;
        console.log('✅ Environment-Workflow: workflowsResponse.data is array');
      } else {
        console.warn('⚠️ Environment-Workflow: Unexpected workflows response structure');
        workflows = [];
      }

      this.environments = environments;
      this.workflows = workflows;

      console.log('✅ Environment-Workflow: Processed data:', {
        environments: this.environments.length,
        workflows: this.workflows.length,
        environmentsData: this.environments,
        workflowsData: this.workflows
      });

      // Pre-select if provided
      if (this.environmentId) {
        this.deploymentForm.patchValue({ environmentId: this.environmentId });
      }
      if (this.workflowId) {
        this.deploymentForm.patchValue({ workflowId: this.workflowId });
      }

      // Load execution history
      await this.loadExecutionHistory();

    } catch (error) {
      console.error('❌ Environment-Workflow: Failed to load data:', error);

      // Provide detailed error information
      const errorDetails = {
        error,
        message: error instanceof Error ? error.message : 'Unknown error',
        status: (error as any)?.status,
        statusText: (error as any)?.statusText,
        url: (error as any)?.url,
        responseText: (error as any)?.error
      };
      console.error('❌ Environment-Workflow: Error details:', errorDetails);

      this.error = `Failed to load data: ${errorDetails.message}`;

      // Add more specific error messages and provide fallback data for development
      if (error instanceof Error) {
        if (error.message.includes('404') ||
            error.message.includes('Failed to load') ||
            error.message.includes('Connection refused') ||
            error.message.includes('Network Error')) {
          this.error = 'Backend services not available. Using mock data for development.';
          this.loadMockData();
        } else if (error.message.includes('401') || error.message.includes('403')) {
          this.error = 'Authentication required. Please log in again.';
        }
      } else {
        // Handle HTTP error responses
        const httpError = error as any;
        if (httpError.status === 404 ||
            httpError.status === 0 ||
            httpError.status >= 500) {
          this.error = 'Backend services not available. Using mock data for development.';
          this.loadMockData();
        } else if (httpError.status === 401 || httpError.status === 403) {
          this.error = 'Authentication required. Please log in again.';
        }
      }
    } finally {
      this.loading = false;
    }
  }

  async loadExecutionHistory(): Promise<void> {
    if (!this.environmentId) return;

    try {
      const response = await firstValueFrom(this.workflowExecutionService.getEnvironmentExecutions(
        this.environmentId,
        { limit: 10 }
      ));

      this.executionHistory = response?.executions || [];
    } catch (error) {
      console.warn('Failed to load execution history:', error);
    }
  }

  private validateEnvironmentHealth(): void {
    if (!this.selectedEnvironment) return;

    this.environmentService.getEnvironmentHealth(this.selectedEnvironment.id).subscribe({
      next: (health) => {
        if (health.status !== 'healthy') {
          this.error = `Environment "${this.selectedEnvironment?.name}" is not healthy: ${health.status}`;
        } else {
          this.error = null;
        }
      },
      error: (error) => {
        this.error = `Failed to check environment health: ${error.message}`;
      }
    });
  }

  private updateWorkflowParameters(): void {
    if (!this.selectedWorkflow) return;

    // Clear existing parameters and variables
    const parametersGroup = this.formBuilder.group({});
    const variablesGroup = this.formBuilder.group({});

    // Add workflow parameters
    if (this.selectedWorkflow.parameters) {
      this.selectedWorkflow.parameters.forEach((param: any) => {
        const validators = param.required ? [Validators.required] : [];
        parametersGroup.addControl(param.name, this.formBuilder.control(param.default || '', validators));
      });
    }

    // Add workflow variables
    if (this.selectedWorkflow.variables) {
      this.selectedWorkflow.variables.forEach((variable: any) => {
        variablesGroup.addControl(variable.name, this.formBuilder.control(variable.default || ''));
      });
    }

    // Update form
    this.deploymentForm.setControl('parameters', parametersGroup);
    this.deploymentForm.setControl('variables', variablesGroup);
  }

  async startExecution(): Promise<void> {
    if (this.deploymentForm.invalid) {
      this.markFormGroupTouched(this.deploymentForm);
      return;
    }

    this.loading = true;
    this.error = null;

    try {
      const formValue = this.deploymentForm.value;

      const request: StartExecutionRequest = {
        workflowId: formValue.workflowId,
        projectId: this.currentProjectId || this.projectId,
        environmentId: formValue.environmentId,
        version: formValue.version,
        triggerType: 'manual',
        triggerBy: 'user', // This would come from auth context
        parameters: formValue.parameters || {},
        variables: formValue.variables || {}
      };

      const execution = await this.workflowExecutionService.startExecution(request).toPromise();

      if (execution) {
        this.currentExecution = execution;
        this.executionStarted.emit(execution);

        // Refresh execution history
        await this.loadExecutionHistory();

        // Reset form for next execution
        this.resetForm();
      }

    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to start execution';
    } finally {
      this.loading = false;
    }
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  resetForm(): void {
    this.deploymentForm.reset();
    this.deploymentForm.patchValue({
      environmentId: this.environmentId || '',
      workflowId: this.workflowId || '',
      version: {
        gitBranch: 'main'
      }
    });
  }

  // UI Helper Methods
  getEnvironmentStatusClass(environment: EnvironmentConfig): string {
    switch (environment.status) {
      case 'active': return 'status-active';
      case 'inactive': return 'status-inactive';
      case 'maintenance': return 'status-maintenance';
      case 'error': return 'status-error';
      default: return 'status-unknown';
    }
  }

  getExecutionStatusClass(execution: WorkflowExecution): string {
    switch (execution.status) {
      case 'success': return 'status-success';
      case 'failed': return 'status-failed';
      case 'running': return 'status-running';
      case 'pending': return 'status-pending';
      case 'cancelled': return 'status-cancelled';
      default: return 'status-unknown';
    }
  }

  getExecutionProgress(execution: WorkflowExecution): number {
    return this.workflowExecutionService.getExecutionProgress(execution);
  }

  formatDuration(milliseconds?: number): string {
    if (!milliseconds) return 'N/A';

    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  // Form validation helpers
  isFieldInvalid(fieldName: string): boolean {
    const field = this.deploymentForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.deploymentForm.get(fieldName);
    if (field && field.errors) {
      if (field.errors['required']) {
        return `${fieldName} is required`;
      }
      // Add more validation error messages as needed
    }
    return '';
  }

  // Navigation helpers
  viewExecution(execution: WorkflowExecution): void {
    // Navigate to execution details
    // This would typically use Angular Router
    console.log('Navigate to execution:', execution.id);
  }

  viewEnvironment(environment: EnvironmentConfig): void {
    // Navigate to environment details
    console.log('Navigate to environment:', environment.id);
  }

  // Project management methods
  private subscribeToProjectChanges(): void {
    this.projectService.selectedProject$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (selectedProject) => {
          const newProjectId = selectedProject?.id || '';
          console.log('🔄 Environment-Workflow: Project changed:', {
            previous: this.currentProjectId,
            new: newProjectId,
            project: selectedProject
          });

          if (newProjectId !== this.currentProjectId) {
            this.selectedProject = selectedProject;
            this.currentProjectId = newProjectId;

            // Reload data when project changes
            this.loadData();
          }
        },
        error: (error) => {
          console.error('❌ Environment-Workflow: Error subscribing to project changes:', error);
        }
      });
  }

  private initializeProjectId(): void {
    console.log('🎯 Environment-Workflow: Initializing project ID...', {
      projectId: this.projectId,
      selectedProjectId: this.selectedProjectId,
      hasCurrentProjectId: !!this.currentProjectId
    });

    // Priority order: explicit projectId input, selectedProjectId input, then shared service
    if (this.projectId) {
      console.log('✅ Environment-Workflow: Using explicit projectId input:', this.projectId);
      this.currentProjectId = this.projectId;
      this.loadData();
      return;
    }

    if (this.selectedProjectId) {
      console.log('✅ Environment-Workflow: Using selectedProjectId input:', this.selectedProjectId);
      this.currentProjectId = this.selectedProjectId;
      this.loadData();
      return;
    }

    console.log('⚠️ Environment-Workflow: No input projectId provided, checking shared service...');
    // Check if project service already has a selected project
    const selectedProject = this.projectService.getSelectedProject();
    if (selectedProject?.id) {
      console.log('✅ Environment-Workflow: Found project in shared service:', selectedProject.id);
      this.currentProjectId = selectedProject.id;
      this.selectedProject = selectedProject;
      this.loadData();
      return;
    }

    console.log('⏳ Environment-Workflow: No project available, waiting for selection...');
    // If no project is selected yet, wait for the first non-null project to be auto-selected
    // This handles the case where projects are still loading
    this.projectService.selectedProject$
      .pipe(
        filter(project => project !== null), // Only proceed when a project is selected
        take(1), // Only take the first value to avoid duplicate subscriptions
        takeUntil(this.destroy$)
      )
      .subscribe(project => {
        if (project?.id && !this.currentProjectId) {
          console.log('✅ Environment-Workflow: Received project from subscription:', project.id);
          this.currentProjectId = project.id;
          this.selectedProject = project;
          this.loadData();
        }
      });
  }

  private loadMockData(): void {
    console.log('🎭 Environment-Workflow: Loading mock data for development');

    // Mock environments
    this.environments = [
      {
        id: 'env-dev-001',
        projectId: this.currentProjectId,
        name: 'Development',
        type: 'kubernetes',
        provider: {
          type: 'gke',
          config: {
            region: 'us-central1',
            cluster: 'dev-cluster',
            authMethod: 'service-account'
          }
        },
        resources: { cpu: '1000m', memory: '1Gi', storage: '10Gi', replicas: 1 },
        networking: { loadBalancer: false, ssl: false },
        variables: {},
        secretMappings: [],
        healthCheck: { enabled: true, endpoint: '/health', interval: 30, timeout: 10 },
        deploymentStrategy: 'rolling',
        status: 'active',
        description: 'Development environment',
        tags: ['dev'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'env-staging-001',
        projectId: this.currentProjectId,
        name: 'Staging',
        type: 'kubernetes',
        provider: {
          type: 'aks',
          config: {
            region: 'eastus',
            cluster: 'staging-cluster',
            authMethod: 'service-account'
          }
        },
        resources: { cpu: '2000m', memory: '2Gi', storage: '20Gi', replicas: 2 },
        networking: { loadBalancer: true, ssl: true },
        variables: {},
        secretMappings: [],
        healthCheck: { enabled: true, endpoint: '/health', interval: 30, timeout: 10 },
        deploymentStrategy: 'blue-green',
        status: 'active',
        description: 'Staging environment',
        tags: ['staging'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    // Mock workflows
    this.workflows = [
      {
        id: 'wf-deploy-001',
        name: 'Standard Deployment',
        description: 'Standard application deployment workflow',
        projectId: this.currentProjectId,
        version: '1.0.0',
        steps: [],
        parameters: [
          { name: 'image_tag', label: 'Image Tag', type: 'text', required: true, default: 'latest' },
          { name: 'replicas', label: 'Replicas', type: 'number', required: false, default: '1' }
        ],
        variables: [
          { name: 'ENVIRONMENT', default: 'development' },
          { name: 'LOG_LEVEL', default: 'info' }
        ],
        triggers: [],
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'system'
      },
      {
        id: 'wf-rollback-001',
        name: 'Rollback Deployment',
        description: 'Rollback to previous version',
        projectId: this.currentProjectId,
        version: '1.0.0',
        steps: [],
        parameters: [
          { name: 'target_version', label: 'Target Version', type: 'text', required: true }
        ],
        variables: [],
        triggers: [],
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'system'
      }
    ];

    console.log('✅ Environment-Workflow: Mock data loaded:', {
      environments: this.environments.length,
      workflows: this.workflows.length
    });
  }
}
