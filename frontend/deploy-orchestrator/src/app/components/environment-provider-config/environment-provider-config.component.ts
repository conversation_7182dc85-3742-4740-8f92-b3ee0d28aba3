import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ProviderService, ProviderInfo } from '../../services/provider.service';
import { EnvironmentConfig, EnvironmentService } from '../../services/environment.service';

@Component({
  selector: 'app-environment-provider-admin',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './environment-provider-config.component.html',
  styleUrls: ['./environment-provider-config.component.css']
})
export class EnvironmentProviderAdminComponent implements OnInit {
  @Input() environment: EnvironmentConfig | null = null;
  @Output() environmentSaved = new EventEmitter<EnvironmentConfig>();
  @Output() cancelled = new EventEmitter<void>();

  providers: ProviderInfo[] = [];
  loading = false;
  error = '';
  success = '';

  // Modal states
  showCreateModal = false;
  showEditModal = false;
  showTestModal = false;
  selectedProvider: ProviderInfo | null = null;

  // Forms
  providerForm!: FormGroup;
  testResults: any = null;

  // Provider types - these will be loaded dynamically from the provider service
  providerTypes: any[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private providerService: ProviderService,
    private environmentService: EnvironmentService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadProviderTypes();
    this.loadProviders();
  }

  initForm(): void {
    this.providerForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      type: ['', Validators.required],
      description: ['', Validators.maxLength(500)],
      isActive: [true],
      config: this.formBuilder.group({})
    });

    // Watch for type changes to update config form
    this.providerForm.get('type')?.valueChanges.subscribe(type => {
      this.updateConfigForm(type);
    });
  }

  loadProviderTypes(): void {
    this.environmentService.getProviderTypes().subscribe({
      next: (response) => {
        // Filter out mock providers and transform to provider types
        this.providerTypes = response.types
          .filter(type => {
            // Filter out mock providers (identified by category "mock" or name containing "mock")
            const isMock = type.category === 'mock' ||
                          type.name?.toLowerCase().includes('mock') ||
                          type.type?.toLowerCase().includes('mock') ||
                          type.description?.toLowerCase().includes('mock');
            return !isMock;
          })
          .map(type => ({
            value: type.type,
            label: type.name,
            description: type.description,
            configFields: type.configFields
          }));
      },
      error: (error) => {
        console.error('Error loading provider types:', error);
        // Fallback to empty array
        this.providerTypes = [];
      }
    });
  }

  updateConfigForm(type: string): void {
    const configGroup = this.formBuilder.group({});

    // Find the provider type configuration
    const providerType = this.providerTypes.find(pt => pt.value === type);
    if (providerType && providerType.configFields) {
      // Dynamically create form controls based on provider config fields
      providerType.configFields.forEach((field: any) => {
        const validators = [];
        if (field.required) {
          validators.push(Validators.required);
        }
        if (field.validation?.minLength) {
          validators.push(Validators.minLength(field.validation.minLength));
        }
        if (field.validation?.maxLength) {
          validators.push(Validators.maxLength(field.validation.maxLength));
        }
        if (field.validation?.pattern) {
          validators.push(Validators.pattern(field.validation.pattern));
        }

        const defaultValue = field.default || '';
        configGroup.addControl(field.name, this.formBuilder.control(defaultValue, validators));
      });
    }

    this.providerForm.setControl('config', configGroup);
  }

  loadProviders(): void {
    this.loading = true;
    this.error = '';

    this.providerService.getProviders().subscribe({
      next: (response) => {
        // Handle null providers from API response and filter out mock providers
        const allProviders = response.providers || [];
        this.providers = allProviders.filter(provider => {
          // Filter out mock providers (identified by category "mock" or name containing "mock")
          const isMock = provider.category === 'mock' ||
                        provider.name?.toLowerCase().includes('mock') ||
                        provider.type?.toLowerCase().includes('mock') ||
                        provider.description?.toLowerCase().includes('mock');
          return !isMock;
        });

        // Convert filtered providers to providerTypes format for the form
        this.providerTypes = this.providers.map(provider => ({
          value: provider.type,
          label: provider.name,
          description: provider.description
        }));
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load providers';
        this.loading = false;
        console.error('Error loading providers:', error);
      }
    });
  }

  openCreateModal(): void {
    this.selectedProvider = null;
    this.providerForm.reset();
    this.providerForm.patchValue({ isActive: true });
    this.showCreateModal = true;
  }

  openEditModal(provider: ProviderInfo): void {
    this.selectedProvider = provider;
    this.providerForm.patchValue({
      name: provider.name,
      type: provider.type,
      description: provider.description,
      isActive: true // ProviderInfo doesn't have isActive, default to true
    });
    this.showEditModal = true;
  }

  closeModals(): void {
    this.showCreateModal = false;
    this.showEditModal = false;
    this.showTestModal = false;
    this.selectedProvider = null;
    this.testResults = null;
    this.error = '';
    this.success = '';
    this.cancelled.emit();
  }

  onSubmit(): void {
    if (this.providerForm.invalid) {
      return;
    }

    const formData = this.providerForm.value;

    if (this.selectedProvider) {
      // Update existing provider
      this.updateProvider(formData);
    } else {
      // Create new provider
      this.createProvider(formData);
    }
  }

  createProvider(data: any): void {
    // Note: This would need to be implemented in the secrets service
    // For now, we'll show a success message
    this.success = 'Provider configuration saved successfully';
    this.closeModals();
    this.loadProviders();

    // If we have an environment, emit it as saved
    if (this.environment) {
      this.environmentSaved.emit(this.environment);
    }
  }

  updateProvider(data: any): void {
    // Note: This would need to be implemented in the secrets service
    // For now, we'll show a success message
    this.success = 'Provider configuration updated successfully';
    this.closeModals();
    this.loadProviders();
  }

  deleteProvider(provider: ProviderInfo): void {
    if (confirm(`Are you sure you want to delete the provider "${provider.name}"?`)) {
      // Note: This would need to be implemented in the provider service
      this.success = 'Provider deleted successfully';
      this.loadProviders();
    }
  }

  testProvider(provider: ProviderInfo): void {
    this.selectedProvider = provider;
    this.testResults = null;
    this.showTestModal = true;

    // Note: ProviderInfo doesn't have an id field, using type instead
    this.providerService.testProviderConnection(provider.type, {}).subscribe({
      next: (result) => {
        this.testResults = result;
      },
      error: (error) => {
        this.testResults = {
          success: false,
          message: 'Connection test failed',
          error: error.message
        };
      }
    });
  }

  getStatusBadgeClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'active':
      case 'connected':
        return 'bg-green-100 text-green-800';
      case 'inactive':
      case 'disconnected':
        return 'bg-red-100 text-red-800';
      case 'testing':
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getConfigFields(type: string): string[] {
    const providerType = this.providerTypes.find(pt => pt.value === type);
    return providerType?.configFields?.map((field: any) => field.name) || [];
  }

  getFieldLabel(fieldName: string, providerType?: string): string {
    if (!providerType) {
      providerType = this.providerForm.get('type')?.value;
    }
    const provider = this.providerTypes.find(pt => pt.value === providerType);
    const field = provider?.configFields?.find((f: any) => f.name === fieldName);
    return field?.label || fieldName;
  }

  isFieldSensitive(fieldName: string, providerType?: string): boolean {
    if (!providerType) {
      providerType = this.providerForm.get('type')?.value;
    }
    const provider = this.providerTypes.find(pt => pt.value === providerType);
    const field = provider?.configFields?.find((f: any) => f.name === fieldName);
    return field?.sensitive || false;
  }

  getProviderTypeLabel(type: string): string {
    const providerType = this.providerTypes.find(pt => pt.value === type);
    return providerType ? providerType.label : type;
  }
}
