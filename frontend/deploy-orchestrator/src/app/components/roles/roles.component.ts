import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Role, Permission } from '../../models/user.model';
import { AdminService } from '../../services/admin.service';
import { ProjectService } from '../../services/project.service';
import { Project } from '../../models/project.model';

@Component({
  selector: 'app-roles',
  templateUrl: './roles.component.html',
  styleUrls: ['./roles.component.scss']
})
export class RolesComponent implements OnInit {
  roles: Role[] = [];
  permissions: Permission[] = [];
  projects: Project[] = [];
  selectedRole: Role | null = null;

  roleForm!: FormGroup;
  isEditMode = false;
  loading = false;
  submitting = false;
  error = '';
  success = '';

  showRoleModal = false;

  constructor(
    private adminService: AdminService,
    private projectService: ProjectService,
    private formBuilder: FormBuilder
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.loadRoles();
    this.loadPermissions();
    this.loadProjects();
  }

  initForm(): void {
    this.roleForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.maxLength(50)]],
      description: ['', Validators.maxLength(200)],
      projectId: [''],
      permissions: [[]],
    });
  }

  loadRoles(): void {
    this.loading = true;
    this.adminService.getRoles().subscribe({
      next: (roles) => {
        this.roles = roles;
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load roles';
        console.error('Error loading roles', error);
        this.loading = false;
      }
    });
  }

  loadPermissions(): void {
    this.adminService.getPermissions().subscribe({
      next: (permissions) => {
        this.permissions = permissions;
      },
      error: (error) => {
        console.error('Error loading permissions', error);
      }
    });
  }

  loadProjects(): void {
    this.projectService.getProjects().subscribe({
      next: (projects) => {
        this.projects = projects;
      },
      error: (error) => {
        console.error('Error loading projects', error);
      }
    });
  }

  openCreateModal(): void {
    this.isEditMode = false;
    this.selectedRole = null;
    this.roleForm.reset({
      permissions: []
    });
    this.showRoleModal = true;
  }

  openEditModal(role: Role): void {
    this.isEditMode = true;
    this.selectedRole = role;

    // Get the full role details with permissions
    this.adminService.getRole(role.id).subscribe({
      next: (fullRole) => {
        this.roleForm.patchValue({
          name: fullRole.name,
          description: fullRole.description,
          projectId: fullRole.projectId || '',
          permissions: fullRole.permissions?.map(p => p.id) || []
        });
        this.showRoleModal = true;
      },
      error: (error) => {
        this.error = 'Failed to load role details';
        console.error('Error loading role details', error);
      }
    });
  }

  closeModal(): void {
    this.showRoleModal = false;
    this.error = '';
    this.success = '';
  }

  onSubmit(): void {
    if (this.roleForm.invalid) {
      return;
    }

    this.submitting = true;
    this.error = '';
    this.success = '';

    const roleData = {
      ...this.roleForm.value,
      id: this.selectedRole?.id
    };

    // If no project is selected, set projectId to null
    if (!roleData.projectId) {
      roleData.projectId = null;
    }

    const request = this.isEditMode
      ? this.adminService.updateRole(roleData)
      : this.adminService.createRole(roleData);

    request.subscribe({
      next: () => {
        this.success = `Role ${this.isEditMode ? 'updated' : 'created'} successfully`;
        this.loadRoles();
        this.submitting = false;
        setTimeout(() => this.closeModal(), 1500);
      },
      error: (error) => {
        this.error = `Failed to ${this.isEditMode ? 'update' : 'create'} role`;
        console.error(`Error ${this.isEditMode ? 'updating' : 'creating'} role`, error);
        this.submitting = false;
      }
    });
  }

  deleteRole(role: Role): void {
    if (!confirm(`Are you sure you want to delete the role "${role.name}"?`)) {
      return;
    }

    this.adminService.deleteRole(role.id).subscribe({
      next: () => {
        this.loadRoles();
        this.success = 'Role deleted successfully';
      },
      error: (error) => {
        this.error = 'Failed to delete role';
        console.error('Error deleting role', error);
      }
    });
  }

  /**
   * Handle permission checkbox change
   */
  onPermissionChange(event: Event, permissionId: string): void {
    const checkbox = event.target as HTMLInputElement;
    const currentPermissions = this.roleForm.get('permissions')?.value || [];

    if (checkbox.checked) {
      // Add permission if checked
      this.roleForm.get('permissions')?.setValue([...currentPermissions, permissionId]);
    } else {
      // Remove permission if unchecked
      this.roleForm.get('permissions')?.setValue(
        currentPermissions.filter((id: string) => id !== permissionId)
      );
    }
  }

  /**
   * Check if a permission is selected
   */
  isPermissionSelected(permissionId: string): boolean {
    const permissions = this.roleForm.get('permissions')?.value;
    return Array.isArray(permissions) && permissions.includes(permissionId);
  }

  /**
   * Get project name by ID
   */
  getProjectName(projectId: string | null | undefined): string {
    if (!projectId) {
      return 'Global';
    }
    const project = this.projects.find(p => p.id === projectId);
    return project ? project.name : 'Unknown project';
  }

  /**
   * Check if role has permissions
   */
  hasPermissions(role: Role): boolean {
    return !!role.permissions && role.permissions.length > 0;
  }

  /**
   * Get permissions count text
   */
  getPermissionsCountText(role: Role): string {
    if (!this.hasPermissions(role)) {
      return 'No permissions';
    }
    return `${role.permissions!.length} permission(s)`;
  }
}
