<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow-sm border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Template Analytics</h1>
          <p class="mt-2 text-gray-600">Insights and metrics for workflow templates</p>
        </div>
        <div class="flex items-center space-x-4">
          <!-- Project Selector for non-admin users -->
          <select
            *ngIf="!isAdmin && projects.length > 0"
            [(ngModel)]="selectedProjectId"
            (ngModelChange)="onProjectChange()"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Projects</option>
            <option *ngFor="let project of projects" [value]="project.id">{{ project.name }}</option>
          </select>

          <select
            [(ngModel)]="selectedTimeRange"
            (ngModelChange)="onTimeRangeChange()"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option *ngFor="let range of timeRanges" [value]="range.value">{{ range.label }}</option>
          </select>
          <button
            (click)="exportAnalytics()"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <i class="fas fa-download mr-2"></i>
            Export Data
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Loading State -->
    <div *ngIf="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>

    <!-- Analytics Content -->
    <div *ngIf="!loading">
      <!-- Key Metrics -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center mr-4">
              <i class="fas fa-layer-group text-blue-600 text-xl"></i>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-500">Total Templates</p>
              <p class="text-2xl font-bold text-gray-900">{{ formatNumber(analytics.totalTemplates) }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center mr-4">
              <i class="fas fa-download text-green-600 text-xl"></i>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-500">Total Downloads</p>
              <p class="text-2xl font-bold text-gray-900">{{ formatNumber(analytics.totalDownloads) }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center mr-4">
              <i class="fas fa-play text-purple-600 text-xl"></i>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-500">Total Usage</p>
              <p class="text-2xl font-bold text-gray-900">{{ formatNumber(analytics.totalUsage) }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 rounded-lg bg-yellow-100 flex items-center justify-center mr-4">
              <i class="fas fa-star text-yellow-600 text-xl"></i>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-500">Average Rating</p>
              <p class="text-2xl font-bold text-gray-900">{{ analytics.averageRating.toFixed(1) }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Charts Row -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Download Trends Chart -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Download Trends</h3>
          <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div class="text-center">
              <i class="fas fa-chart-line text-4xl text-gray-400 mb-2"></i>
              <p class="text-gray-500">Chart visualization would go here</p>
              <p class="text-sm text-gray-400">Integration with charting library needed</p>
            </div>
          </div>
        </div>

        <!-- Category Distribution -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Templates by Category</h3>
          <div class="space-y-3">
            <div *ngFor="let category of getCategoryChartData()" class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-4 h-4 rounded-full mr-3" [style.background-color]="'#' + (category.name.charCodeAt(0) * 123456).toString(16).slice(-6)"></div>
                <span class="text-sm font-medium text-gray-700">{{ category.name }}</span>
              </div>
              <div class="flex items-center">
                <div class="w-24 bg-gray-200 rounded-full h-2 mr-3">
                  <div
                    class="bg-blue-600 h-2 rounded-full"
                    [style.width.%]="(category.value / analytics.totalTemplates) * 100"
                  ></div>
                </div>
                <span class="text-sm text-gray-500 w-8 text-right">{{ category.value }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Rating Distribution and Top Templates -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Rating Distribution -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Rating Distribution</h3>
          <div class="space-y-3">
            <div *ngFor="let rating of getRatingChartData()" class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex mr-3">
                  <i *ngFor="let i of [1,2,3,4,5]"
                     class="fas fa-star text-sm"
                     [ngClass]="i <= rating.rating ? 'text-yellow-400' : 'text-gray-300'">
                  </i>
                </div>
                <span class="text-sm font-medium text-gray-700">{{ rating.name }}</span>
              </div>
              <div class="flex items-center">
                <div class="w-24 bg-gray-200 rounded-full h-2 mr-3">
                  <div
                    class="bg-yellow-500 h-2 rounded-full"
                    [style.width.%]="getRatingBarWidth(rating.value)"
                  ></div>
                </div>
                <span class="text-sm text-gray-500 w-8 text-right">{{ rating.value }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Top Templates -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Most Popular Templates</h3>
          <div class="space-y-3">
            <div *ngFor="let template of analytics.topTemplates.slice(0, 5); let i = index"
                 class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center">
                <div class="w-8 h-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-sm font-medium mr-3">
                  {{ i + 1 }}
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ template.name }}</p>
                  <p class="text-xs text-gray-500">{{ template.category }}</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-gray-900">{{ template.downloadCount }}</p>
                <p class="text-xs text-gray-500">downloads</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Insights Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center mr-3">
              <i class="fas fa-trophy text-green-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900">Top Category</h3>
          </div>
          <p class="text-2xl font-bold text-gray-900 mb-2">{{ getTopCategory() }}</p>
          <p class="text-sm text-gray-500">Most popular template category</p>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center mr-3">
              <i class="fas fa-fire text-blue-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900">Most Popular</h3>
          </div>
          <p class="text-lg font-bold text-gray-900 mb-2" *ngIf="getMostPopularTemplate()">
            {{ getMostPopularTemplate()!.name }}
          </p>
          <p class="text-sm text-gray-500" *ngIf="getMostPopularTemplate()">
            {{ getMostPopularTemplate()!.downloadCount }} downloads
          </p>
          <p class="text-sm text-gray-500" *ngIf="!getMostPopularTemplate()">
            No templates available
          </p>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 rounded-lg bg-purple-100 flex items-center justify-center mr-3">
              <i class="fas fa-chart-line text-purple-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900">Engagement</h3>
          </div>
          <p class="text-2xl font-bold text-gray-900 mb-2">
            {{ analytics.totalTemplates > 0 ? (analytics.totalUsage / analytics.totalTemplates).toFixed(1) : '0' }}
          </p>
          <p class="text-sm text-gray-500">Average uses per template</p>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="mt-8 bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
        <div class="space-y-3">
          <div class="flex items-center justify-between py-2 border-b border-gray-100">
            <div class="flex items-center">
              <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                <i class="fas fa-download text-blue-600 text-sm"></i>
              </div>
              <span class="text-sm text-gray-700">Template downloaded</span>
            </div>
            <span class="text-sm text-gray-500">2 minutes ago</span>
          </div>
          <div class="flex items-center justify-between py-2 border-b border-gray-100">
            <div class="flex items-center">
              <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                <i class="fas fa-star text-green-600 text-sm"></i>
              </div>
              <span class="text-sm text-gray-700">New template rating</span>
            </div>
            <span class="text-sm text-gray-500">5 minutes ago</span>
          </div>
          <div class="flex items-center justify-between py-2 border-b border-gray-100">
            <div class="flex items-center">
              <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                <i class="fas fa-plus text-purple-600 text-sm"></i>
              </div>
              <span class="text-sm text-gray-700">New template published</span>
            </div>
            <span class="text-sm text-gray-500">1 hour ago</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
