/* Essential styles only for template analytics */
.metric-card { transition: all 0.2s ease-in-out; }
.metric-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); }
.chart-container:hover { box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); }
.template-item:hover { background-color: #f3f4f6; transform: translateX(4px); }
.insight-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); }
.activity-item:hover { background-color: #f9fafb; }

@keyframes spin { to { transform: rotate(360deg); } }
.loading-spinner { animation: spin 1s linear infinite; }

@media (max-width: 768px) {
  .metric-cards { grid-template-columns: repeat(2, 1fr); }
  .chart-grid, .insight-cards { grid-template-columns: 1fr; }
}

@media (max-width: 480px) {
  .metric-cards { grid-template-columns: 1fr; }
}
