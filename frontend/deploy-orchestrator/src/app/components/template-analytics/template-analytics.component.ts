import { Component, OnInit } from '@angular/core';
import { WorkflowService } from '../../services/workflow.service';
import { AuthService } from '../../services/auth.service';
import { ProjectService } from '../../services/project.service';
import { WorkflowTemplate } from '../../models/workflow.model';
import { Project } from '../../models/project.model';

interface AnalyticsData {
  totalTemplates: number;
  totalDownloads: number;
  totalUsage: number;
  averageRating: number;
  topTemplates: WorkflowTemplate[];
  categoryStats: { [key: string]: number };
  downloadTrends: { date: string; downloads: number }[];
  ratingDistribution: { rating: number; count: number }[];
}

@Component({
  selector: 'app-template-analytics',
  templateUrl: './template-analytics.component.html',
  styleUrls: ['./template-analytics.component.css']
})
export class TemplateAnalyticsComponent implements OnInit {
  analytics: AnalyticsData = {
    totalTemplates: 0,
    totalDownloads: 0,
    totalUsage: 0,
    averageRating: 0,
    topTemplates: [],
    categoryStats: {},
    downloadTrends: [],
    ratingDistribution: []
  };

  loading: boolean = false;
  selectedTimeRange: string = '30d';
  projects: Project[] = [];
  selectedProjectId: string = '';
  isAdmin: boolean = false;

  timeRanges = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 3 months' },
    { value: '1y', label: 'Last year' }
  ];

  constructor(
    private workflowService: WorkflowService,
    private authService: AuthService,
    private projectService: ProjectService
  ) {
    this.isAdmin = this.authService.isAdmin();
  }

  ngOnInit(): void {
    this.loadProjects();
  }

  loadProjects(): void {
    this.projectService.projects$.subscribe({
      next: (projects) => {
        this.projects = projects;
        if (projects.length > 0 && !this.isAdmin) {
          this.selectedProjectId = projects[0].id;
        }
        this.loadAnalytics();
      },
      error: (error) => {
        console.error('Error loading projects', error);
        this.loadAnalytics();
      }
    });
  }

  loadAnalytics(): void {
    this.loading = true;

    // For non-admin users, only show analytics for their accessible projects
    const filters: any = { limit: 1000 };

    // Add project filtering for non-admin users
    if (!this.isAdmin && this.selectedProjectId) {
      // Note: This would require backend support for project-based template filtering
      // For now, we'll load all public templates and filter client-side
      filters.isPublic = true;
    }

    // Load templates to calculate analytics
    this.workflowService.getTemplates(filters).subscribe({
      next: (templates) => {
        this.calculateAnalytics(templates);
        this.loading = false;
      },
      error: (error) => {
        console.error('Failed to load analytics:', error);
        this.loading = false;
      }
    });
  }

  onProjectChange(): void {
    this.loadAnalytics();
  }

  private calculateAnalytics(templates: WorkflowTemplate[]): void {
    // Calculate basic stats
    this.analytics.totalTemplates = templates.length;
    this.analytics.totalDownloads = templates.reduce((sum, t) => sum + t.downloadCount, 0);
    this.analytics.totalUsage = templates.reduce((sum, t) => sum + t.usageCount, 0);

    // Calculate average rating
    const ratedTemplates = templates.filter(t => t.ratingCount > 0);
    if (ratedTemplates.length > 0) {
      const totalRating = ratedTemplates.reduce((sum, t) => sum + (t.rating * t.ratingCount), 0);
      const totalRatingCount = ratedTemplates.reduce((sum, t) => sum + t.ratingCount, 0);
      this.analytics.averageRating = totalRating / totalRatingCount;
    }

    // Get top templates by downloads
    this.analytics.topTemplates = templates
      .sort((a, b) => b.downloadCount - a.downloadCount)
      .slice(0, 10);

    // Calculate category stats
    this.analytics.categoryStats = {};
    templates.forEach(template => {
      const category = template.category || 'uncategorized';
      this.analytics.categoryStats[category] = (this.analytics.categoryStats[category] || 0) + 1;
    });

    // Generate mock download trends (in a real app, this would come from the backend)
    this.generateMockDownloadTrends();

    // Calculate rating distribution
    this.calculateRatingDistribution(templates);
  }

  private generateMockDownloadTrends(): void {
    const days = this.selectedTimeRange === '7d' ? 7 :
                 this.selectedTimeRange === '30d' ? 30 :
                 this.selectedTimeRange === '90d' ? 90 : 365;

    this.analytics.downloadTrends = [];
    const today = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      // Generate random download count (in a real app, this would be actual data)
      const downloads = Math.floor(Math.random() * 50) + 10;

      this.analytics.downloadTrends.push({
        date: date.toISOString().split('T')[0],
        downloads: downloads
      });
    }
  }

  private calculateRatingDistribution(templates: WorkflowTemplate[]): void {
    const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };

    templates.forEach(template => {
      if (template.ratingCount > 0) {
        const roundedRating = Math.round(template.rating);
        distribution[roundedRating as keyof typeof distribution] += template.ratingCount;
      }
    });

    this.analytics.ratingDistribution = Object.entries(distribution).map(([rating, count]) => ({
      rating: parseInt(rating),
      count: count
    }));
  }

  onTimeRangeChange(): void {
    this.generateMockDownloadTrends();
  }

  getCategoryChartData(): any[] {
    return Object.entries(this.analytics.categoryStats).map(([category, count]) => ({
      name: category.charAt(0).toUpperCase() + category.slice(1),
      value: count
    }));
  }

  getDownloadChartData(): any[] {
    return this.analytics.downloadTrends.map(item => ({
      name: new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      value: item.downloads
    }));
  }

  getRatingChartData(): any[] {
    return this.analytics.ratingDistribution.map(item => ({
      name: `${item.rating} Star${item.rating !== 1 ? 's' : ''}`,
      value: item.count
    }));
  }

  exportAnalytics(): void {
    const data = {
      analytics: this.analytics,
      exportedAt: new Date().toISOString(),
      timeRange: this.selectedTimeRange
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `template-analytics-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  getGrowthPercentage(current: number, previous: number): number {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  }

  formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  getTopCategory(): string {
    const categories = Object.entries(this.analytics.categoryStats);
    if (categories.length === 0) return 'N/A';

    const topCategory = categories.reduce((max, current) =>
      current[1] > max[1] ? current : max
    );

    return topCategory[0].charAt(0).toUpperCase() + topCategory[0].slice(1);
  }

  getMostPopularTemplate(): WorkflowTemplate | null {
    if (this.analytics.topTemplates.length === 0) return null;
    return this.analytics.topTemplates[0];
  }

  getRatingBarWidth(ratingValue: number): number {
    if (this.analytics.ratingDistribution.length === 0) return 0;

    const maxCount = Math.max(...this.analytics.ratingDistribution.map(r => r.count));
    if (maxCount === 0) return 0;

    return (ratingValue / maxCount) * 100;
  }
}
