<div class="min-h-screen bg-gray-50 p-6">
  <!-- Header Section -->
  <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 flex items-center gap-3 mb-2">
          <i class="fas fa-rocket text-blue-600"></i>
          Deployment Management
        </h1>
        <p class="text-gray-600">Manage and monitor application deployments across environments</p>
      </div>
      <div class="flex items-center gap-3">
        <!-- Management Navigation -->
        <div class="flex items-center gap-2 mr-4">
          <button
            (click)="navigateToApplications()"
            class="inline-flex items-center px-3 py-2 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors">
            <i class="fas fa-cubes mr-2"></i>
            Manage Apps
          </button>
          <button
            (click)="navigateToPlugins()"
            class="inline-flex items-center px-3 py-2 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors">
            <i class="fas fa-puzzle-piece mr-2"></i>
            Plugins
          </button>
          <button
            (click)="navigateToEnvironments()"
            class="inline-flex items-center px-3 py-2 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors">
            <i class="fas fa-server mr-2"></i>
            Environments
          </button>
        </div>
        
        <!-- Action Buttons -->
        <button
          (click)="openDeploymentWizard()"
          [disabled]="isLoading"
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
          <i class="fas fa-plus mr-2"></i>
          New Deployment
        </button>
        <button
          (click)="openBatchDeploymentWizard()"
          [disabled]="isLoading"
          class="inline-flex items-center px-4 py-2 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
          <i class="fas fa-layer-group mr-2"></i>
          Multi-App Deploy
        </button>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div *ngIf="statistics" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex items-center justify-center w-12 h-12 bg-green-100 rounded-xl">
            <i class="fas fa-check-circle text-green-600 text-xl"></i>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-2xl font-bold text-gray-900">{{ statistics.successfulDeployments }}</p>
          <p class="text-sm text-gray-600">Successful</p>
        </div>
      </div>
    </div>
    
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex items-center justify-center w-12 h-12 bg-red-100 rounded-xl">
            <i class="fas fa-times-circle text-red-600 text-xl"></i>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-2xl font-bold text-gray-900">{{ statistics.failedDeployments }}</p>
          <p class="text-sm text-gray-600">Failed</p>
        </div>
      </div>
    </div>
    
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-xl">
            <i class="fas fa-clock text-yellow-600 text-xl"></i>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-2xl font-bold text-gray-900">{{ pendingDeployments }}</p>
          <p class="text-sm text-gray-600">Pending</p>
        </div>
      </div>
    </div>
    
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl">
            <i class="fas fa-play text-blue-600 text-xl"></i>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-2xl font-bold text-gray-900">{{ runningDeployments }}</p>
          <p class="text-sm text-gray-600">Running</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters and Search -->
  <div class="filters-section">
    <div class="search-box">
      <i class="fas fa-search"></i>
      <input
        type="text"
        placeholder="Search deployments..."
        [(ngModel)]="searchTerm"
        (ngModelChange)="onSearchChange()">
    </div>
    
    <div class="filters">
      <select [(ngModel)]="selectedStatus" (ngModelChange)="applyFilters()" class="filter-select">
        <option value="">All Status</option>
        <option value="PENDING">Pending</option>
        <option value="RUNNING">Running</option>
        <option value="COMPLETED">Completed</option>
        <option value="FAILED">Failed</option>
        <option value="CANCELLED">Cancelled</option>
      </select>

      <select [(ngModel)]="selectedEnvironment" (ngModelChange)="applyFilters()" class="filter-select">
        <option value="">All Environments</option>
        <option *ngFor="let env of environments" [value]="env.id">{{ env.name }}</option>
      </select>

      <select [(ngModel)]="selectedApplication" (ngModelChange)="applyFilters()" class="filter-select">
        <option value="">All Applications</option>
        <option *ngFor="let app of applications" [value]="app.id">{{ app.name }}</option>
      </select>
    </div>

    <div class="view-controls">
      <button
        class="btn btn-outline"
        [class.active]="viewMode === 'table'"
        (click)="setViewMode('table')">
        <i class="fas fa-table"></i>
        Table
      </button>
      <button
        class="btn btn-outline"
        [class.active]="viewMode === 'cards'"
        (click)="setViewMode('cards')">
        <i class="fas fa-th-large"></i>
        Cards
      </button>
    </div>
  </div>

  <!-- Deployment List -->
  <div class="deployments-container" *ngIf="!isLoading">
    <!-- Table View -->
    <div class="deployments-table" *ngIf="viewMode === 'table'">
      <div class="table-header">
        <div class="table-actions">
          <button
            class="btn btn-outline btn-sm"
            (click)="toggleBatchMode()"
            [class.active]="batchMode">
            <i class="fas fa-check-square"></i>
            Batch Actions
          </button>
          <button
            *ngIf="batchMode && selectedDeployments.size > 0"
            class="btn btn-danger btn-sm"
            (click)="batchCancelDeployments()">
            <i class="fas fa-stop"></i>
            Cancel Selected
          </button>
          <button
            *ngIf="batchMode && selectedDeployments.size > 0"
            class="btn btn-warning btn-sm"
            (click)="batchRetryDeployments()">
            <i class="fas fa-redo"></i>
            Retry Selected
          </button>
        </div>
      </div>

      <table class="deployment-table">
        <thead>
          <tr>
            <th *ngIf="batchMode" class="checkbox-column">
              <input
                type="checkbox"
                [checked]="isAllSelected()"
                [indeterminate]="isSomeSelected()"
                (change)="toggleAllSelection()">
            </th>
            <th (click)="sortBy('application')" class="sortable">
              Application
              <i class="fas fa-sort" [class.fa-sort-up]="sortField === 'application' && sortDirection === 'asc'"
                 [class.fa-sort-down]="sortField === 'application' && sortDirection === 'desc'"></i>
            </th>
            <th (click)="sortBy('environment')" class="sortable">
              Environment
              <i class="fas fa-sort" [class.fa-sort-up]="sortField === 'environment' && sortDirection === 'asc'"
                 [class.fa-sort-down]="sortField === 'environment' && sortDirection === 'desc'"></i>
            </th>
            <th (click)="sortBy('version')" class="sortable">
              Version
              <i class="fas fa-sort" [class.fa-sort-up]="sortField === 'version' && sortDirection === 'asc'"
                 [class.fa-sort-down]="sortField === 'version' && sortDirection === 'desc'"></i>
            </th>
            <th (click)="sortBy('status')" class="sortable">
              Status
              <i class="fas fa-sort" [class.fa-sort-up]="sortField === 'status' && sortDirection === 'asc'"
                 [class.fa-sort-down]="sortField === 'status' && sortDirection === 'desc'"></i>
            </th>
            <th (click)="sortBy('startedAt')" class="sortable">
              Started
              <i class="fas fa-sort" [class.fa-sort-up]="sortField === 'startedAt' && sortDirection === 'asc'"
                 [class.fa-sort-down]="sortField === 'startedAt' && sortDirection === 'desc'"></i>
            </th>
            <th>Duration</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let deployment of pagedDeployments; trackBy: trackByDeploymentId">
            <td *ngIf="batchMode" class="checkbox-column">
              <input
                type="checkbox"
                [checked]="selectedDeployments.has(deployment.id)"
                (change)="toggleDeploymentSelection(deployment.id)">
            </td>
            <td>
              <div class="application-info">
                <i class="fas fa-cube"></i>
                <span>{{ deployment.applicationName }}</span>
              </div>
            </td>
            <td>
              <span class="environment-badge" [attr.data-environment]="deployment.environment?.type || deployment.environmentName">
                {{ deployment.environmentName }}
              </span>
            </td>
            <td>
              <span class="version-tag">{{ deployment.version }}</span>
            </td>
            <td>
              <span class="status-badge" [attr.data-status]="deployment.status">
                <i [class]="getStatusIcon(deployment.status)"></i>
                {{ deployment.status }}
              </span>
            </td>
            <td>
              <span class="timestamp">{{ deployment.startedAt | date:'MMM dd, HH:mm' }}</span>
            </td>
            <td>
              <span class="duration">{{ getDuration(deployment) }}</span>
            </td>
            <td>
              <div class="action-buttons">
                <button
                  class="btn btn-icon"
                  (click)="openDeploymentDetails(deployment)"
                  title="View Details">
                  <i class="fas fa-eye"></i>
                </button>
                <button
                  class="btn btn-icon"
                  (click)="openDeploymentLogs(deployment)"
                  title="View Logs">
                  <i class="fas fa-file-alt"></i>
                </button>
                <button
                  *ngIf="canPromote(deployment)"
                  class="btn btn-icon btn-success"
                  (click)="openPromotionDialog(deployment)"
                  title="Promote">
                  <i class="fas fa-arrow-up"></i>
                </button>
                <button
                  *ngIf="canRetry(deployment)"
                  class="btn btn-icon btn-warning"
                  (click)="retryDeployment(deployment)"
                  title="Retry">
                  <i class="fas fa-redo"></i>
                </button>
                <button
                  *ngIf="canCancel(deployment)"
                  class="btn btn-icon btn-danger"
                  (click)="cancelDeployment(deployment)"
                  title="Cancel">
                  <i class="fas fa-stop"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Cards View -->
    <div class="deployments-cards" *ngIf="viewMode === 'cards'">
      <div class="deployment-card" *ngFor="let deployment of pagedDeployments; trackBy: trackByDeploymentId">
        <div class="card-header">
          <div class="card-title">
            <i class="fas fa-cube"></i>
            {{ deployment.applicationName }}
          </div>
          <div class="card-status">
            <span class="status-badge" [attr.data-status]="deployment.status">
              <i [class]="getStatusIcon(deployment.status)"></i>
              {{ deployment.status }}
            </span>
          </div>
        </div>
        <div class="card-content">
          <div class="card-info">
            <div class="info-item">
              <label>Environment:</label>
              <span class="environment-badge" [attr.data-environment]="deployment.environment?.type || deployment.environmentName">
                {{ deployment.environmentName }}
              </span>
            </div>
            <div class="info-item">
              <label>Version:</label>
              <span class="version-tag">{{ deployment.version }}</span>
            </div>
            <div class="info-item">
              <label>Started:</label>
              <span class="timestamp">{{ deployment.startedAt | date:'MMM dd, HH:mm' }}</span>
            </div>
            <div class="info-item">
              <label>Duration:</label>
              <span class="duration">{{ getDuration(deployment) }}</span>
            </div>
          </div>
        </div>
        <div class="card-actions">
          <button
            class="btn btn-outline btn-sm"
            (click)="openDeploymentDetails(deployment)">
            <i class="fas fa-eye"></i>
            Details
          </button>
          <button
            class="btn btn-outline btn-sm"
            (click)="openDeploymentLogs(deployment)">
            <i class="fas fa-file-alt"></i>
            Logs
          </button>
          <button
            *ngIf="canPromote(deployment)"
            class="btn btn-success btn-sm"
            (click)="openPromotionDialog(deployment)">
            <i class="fas fa-arrow-up"></i>
            Promote
          </button>
          <button
            *ngIf="canRetry(deployment)"
            class="btn btn-warning btn-sm"
            (click)="retryDeployment(deployment)">
            <i class="fas fa-redo"></i>
            Retry
          </button>
          <button
            *ngIf="canCancel(deployment)"
            class="btn btn-danger btn-sm"
            (click)="cancelDeployment(deployment)">
            <i class="fas fa-stop"></i>
            Cancel
          </button>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div class="empty-state" *ngIf="filteredDeployments.length === 0">
      <i class="fas fa-rocket empty-icon"></i>
      <h3>No Deployments Found</h3>
      <p>Create your first deployment to get started</p>
      <button class="btn btn-primary" (click)="openDeploymentWizard()">
        <i class="fas fa-plus"></i>
        New Deployment
      </button>
    </div>
  </div>

  <!-- Pagination -->
  <div class="pagination-section" *ngIf="filteredDeployments.length > pageSize">
    <div class="pagination-info">
      Showing {{ getStartIndex() + 1 }} to {{ getEndIndex() }} of {{ totalDeployments }} deployments
    </div>
    <div class="pagination-controls">
      <button
        class="btn btn-outline"
        (click)="previousPage()"
        [disabled]="currentPage === 1">
        <i class="fas fa-chevron-left"></i>
        Previous
      </button>
      <span class="page-info">
        Page {{ currentPage }} of {{ getTotalPages() }}
        ({{ totalDeployments }} total)
      </span>
      <button
        class="btn btn-outline"
        (click)="nextPage()"
        [disabled]="currentPage === getTotalPages()">
        Next
        <i class="fas fa-chevron-right"></i>
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="loading-spinner">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Loading deployments...</p>
    </div>
  </div>
</div>

<!-- Deployment Wizard Modal -->
<div *ngIf="deploymentWizard.isOpen" class="fixed inset-0 z-50 overflow-y-auto">
  <!-- Backdrop -->
  <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" (click)="closeDeploymentWizard()"></div>
  
  <!-- Modal Content -->
  <div class="flex min-h-screen items-center justify-center p-4">
    <div class="relative w-full max-w-4xl bg-white rounded-xl shadow-2xl transform transition-all">
      
      <!-- Modal Header -->
      <div class="border-b border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-2xl font-bold text-gray-900">New Deployment</h2>
            <p class="mt-1 text-sm text-gray-600">Deploy applications to your environments</p>
          </div>
          <button 
            (click)="closeDeploymentWizard()"
            class="rounded-lg p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600 transition-colors">
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>
        
        <!-- Progress Steps -->
        <div class="mt-6">
          <nav class="flex items-center justify-center">
            <ol class="flex items-center space-x-4">
              <li class="flex items-center">
                <div class="flex items-center">
                  <div [class]="wizardState.currentStep >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'" 
                       class="flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium">
                    1
                  </div>
                  <span [class]="wizardState.currentStep >= 1 ? 'text-blue-600' : 'text-gray-500'" 
                        class="ml-2 text-sm font-medium">Application</span>
                </div>
                <i class="fas fa-chevron-right ml-4 text-gray-300"></i>
              </li>
              
              <li class="flex items-center">
                <div class="flex items-center">
                  <div [class]="wizardState.currentStep >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'" 
                       class="flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium">
                    2
                  </div>
                  <span [class]="wizardState.currentStep >= 2 ? 'text-blue-600' : 'text-gray-500'" 
                        class="ml-2 text-sm font-medium">Environment</span>
                </div>
                <i class="fas fa-chevron-right ml-4 text-gray-300"></i>
              </li>
              
              <li class="flex items-center">
                <div class="flex items-center">
                  <div [class]="wizardState.currentStep >= 3 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'" 
                       class="flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium">
                    3
                  </div>
                  <span [class]="wizardState.currentStep >= 3 ? 'text-blue-600' : 'text-gray-500'" 
                        class="ml-2 text-sm font-medium">Method</span>
                </div>
                <i class="fas fa-chevron-right ml-4 text-gray-300"></i>
              </li>
              
              <li class="flex items-center">
                <div class="flex items-center">
                  <div [class]="wizardState.currentStep >= 4 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'" 
                       class="flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium">
                    4
                  </div>
                  <span [class]="wizardState.currentStep >= 4 ? 'text-blue-600' : 'text-gray-500'" 
                        class="ml-2 text-sm font-medium">Configuration</span>
                </div>
                <i class="fas fa-chevron-right ml-4 text-gray-300"></i>
              </li>
              
              <li class="flex items-center">
                <div class="flex items-center">
                  <div [class]="wizardState.currentStep >= 5 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'" 
                       class="flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium">
                    5
                  </div>
                  <span [class]="wizardState.currentStep >= 5 ? 'text-blue-600' : 'text-gray-500'" 
                        class="ml-2 text-sm font-medium">Plugin</span>
                </div>
                <i class="fas fa-chevron-right ml-4 text-gray-300"></i>
              </li>
              
              <li class="flex items-center">
                <div class="flex items-center">
                  <div [class]="wizardState.currentStep >= 6 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'" 
                       class="flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium">
                    6
                  </div>
                  <span [class]="wizardState.currentStep >= 6 ? 'text-blue-600' : 'text-gray-500'" 
                        class="ml-2 text-sm font-medium">Review</span>
                </div>
              </li>
            </ol>
          </nav>
        </div>
      </div>

      <!-- Modal Body -->
      <div class="px-6 py-6 max-h-96 overflow-y-auto">
        
        <!-- Step 1: Application Selection -->
        <div *ngIf="wizardState.currentStep === 1" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Select Application</h3>
            
            <!-- Deployment Type -->
            <div class="mb-6">
              <label class="block text-sm font-medium text-gray-700 mb-3">Deployment Type</label>
              <div class="grid grid-cols-2 gap-4">
                <label class="flex items-center p-4 border-2 rounded-lg cursor-pointer transition-colors"
                       [class]="wizardState.deploymentType === 'single' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                  <input type="radio" 
                         [(ngModel)]="wizardState.deploymentType" 
                         value="single" 
                         class="sr-only"
                         (change)="validateWizardStep()">
                  <div class="flex flex-col items-center text-center">
                    <i class="fas fa-rocket text-2xl mb-2" [class]="wizardState.deploymentType === 'single' ? 'text-blue-600' : 'text-gray-400'"></i>
                    <span class="font-medium text-sm">Single App</span>
                    <span class="text-xs text-gray-500">Deploy one application</span>
                  </div>
                </label>
                
                <label class="flex items-center p-4 border-2 rounded-lg cursor-pointer transition-colors"
                       [class]="wizardState.deploymentType === 'multi-app' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                  <input type="radio" 
                         [(ngModel)]="wizardState.deploymentType" 
                         value="multi-app" 
                         class="sr-only"
                         (change)="validateWizardStep()">
                  <div class="flex flex-col items-center text-center">
                    <i class="fas fa-sitemap text-2xl mb-2" [class]="wizardState.deploymentType === 'multi-app' ? 'text-blue-600' : 'text-gray-400'"></i>
                    <span class="font-medium text-sm">Multi-App</span>
                    <span class="text-xs text-gray-500">Coordinated deployment</span>
                  </div>
                </label>
              </div>
            </div>

            <!-- Application Selection -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-3">
                Choose Application{{ wizardState.deploymentType !== 'single' ? 's' : '' }}
              </label>
              
              <div *ngIf="wizardState.deploymentType === 'single'" class="space-y-2">
                <div *ngFor="let app of applications" 
                     class="flex items-center p-3 border rounded-lg cursor-pointer transition-colors hover:bg-gray-50"
                     [class]="isApplicationSelected(app) ? 'border-blue-500 bg-blue-50' : 'border-gray-200'"
                     (click)="selectSingleApplication(app)">
                  <div class="flex-1">
                    <div class="flex items-center">
                      <i class="fas fa-cube text-blue-600 mr-3"></i>
                      <div>
                        <h4 class="font-medium text-gray-900">{{ app.name }}</h4>
                        <p class="text-sm text-gray-500">{{ app.description || 'No description' }}</p>
                        <div *ngIf="app.version" class="text-xs text-gray-400 mt-1">
                          <i class="fas fa-tag mr-1"></i>
                          <span>v{{ app.version }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div *ngIf="isApplicationSelected(app)" class="text-blue-600">
                    <i class="fas fa-check-circle"></i>
                  </div>
                </div>
              </div>
              
              <div *ngIf="wizardState.deploymentType !== 'single'" class="space-y-2 max-h-64 overflow-y-auto">
                <div *ngFor="let app of applications" 
                     class="flex items-center p-3 border rounded-lg">
                  <label class="flex items-center cursor-pointer flex-1">
                    <input type="checkbox" 
                           [checked]="isApplicationSelected(app)"
                           (change)="toggleApplicationSelection(app)"
                           class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                    <div class="ml-3 flex-1">
                      <div class="flex items-center">
                        <i class="fas fa-cube text-blue-600 mr-3"></i>
                        <div>
                          <h4 class="font-medium text-gray-900">{{ app.name }}</h4>
                          <p class="text-sm text-gray-500">{{ app.description || 'No description' }}</p>
                        </div>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 2: Environment Selection -->
        <div *ngIf="wizardState.currentStep === 2" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Select Environment</h3>
            
            <div class="grid grid-cols-1 gap-4">
              <div *ngFor="let env of environments" 
                   class="flex items-center p-4 border-2 rounded-lg cursor-pointer transition-colors"
                   [class]="wizardState.selectedEnvironment?.id === env.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'"
                   (click)="selectEnvironment(env)">
                <div class="flex-1">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <div class="flex h-10 w-10 items-center justify-center rounded-full"
                           [class]="getEnvironmentColorClass(env.type)">
                        <i class="fas fa-server text-white"></i>
                      </div>
                      <div class="ml-4">
                        <h4 class="font-medium text-gray-900">{{ env.name }}</h4>
                        <p class="text-sm text-gray-500">{{ env.description || env.type }}</p>
                      </div>
                    </div>
                    <div class="flex items-center space-x-3">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                            [class]="env.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                        {{ env.isActive ? 'Active' : 'Inactive' }}
                      </span>
                      <div *ngIf="wizardState.selectedEnvironment?.id === env.id" class="text-blue-600">
                        <i class="fas fa-check-circle"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 3: Deployment Method Selection -->
        <div *ngIf="wizardState.currentStep === 3" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Select Deployment Method</h3>
            <p class="text-sm text-gray-600 mb-6">Choose how you want to deploy your applications</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <label class="flex items-center p-6 border-2 rounded-lg cursor-pointer transition-colors"
                     [class]="wizardState.deploymentMethod === 'workflow' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                <input type="radio" 
                       [(ngModel)]="wizardState.deploymentMethod" 
                       value="workflow" 
                       class="sr-only"
                       (change)="selectDeploymentMethod('workflow')">
                <div class="flex flex-col items-center text-center w-full">
                  <div class="flex h-12 w-12 items-center justify-center rounded-full mb-4"
                       [class]="wizardState.deploymentMethod === 'workflow' ? 'bg-blue-100' : 'bg-gray-100'">
                    <i class="fas fa-sitemap text-2xl" [class]="wizardState.deploymentMethod === 'workflow' ? 'text-blue-600' : 'text-gray-400'"></i>
                  </div>
                  <h4 class="font-medium text-gray-900 mb-2">Workflow-Based</h4>
                  <p class="text-sm text-gray-500">Use predefined workflows for deployment automation with complex steps and conditions</p>
                  <div class="mt-3 text-xs text-gray-400">
                    <i class="fas fa-check mr-1"></i> Complex automation
                    <br>
                    <i class="fas fa-check mr-1"></i> Multi-step processes
                    <br>
                    <i class="fas fa-check mr-1"></i> Conditional logic
                  </div>
                </div>
              </label>
              
              <label class="flex items-center p-6 border-2 rounded-lg cursor-pointer transition-colors"
                     [class]="wizardState.deploymentMethod === 'provider' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                <input type="radio" 
                       [(ngModel)]="wizardState.deploymentMethod" 
                       value="provider" 
                       class="sr-only"
                       (change)="selectDeploymentMethod('provider')">
                <div class="flex flex-col items-center text-center w-full">
                  <div class="flex h-12 w-12 items-center justify-center rounded-full mb-4"
                       [class]="wizardState.deploymentMethod === 'provider' ? 'bg-blue-100' : 'bg-gray-100'">
                    <i class="fas fa-plug text-2xl" [class]="wizardState.deploymentMethod === 'provider' ? 'text-blue-600' : 'text-gray-400'"></i>
                  </div>
                  <h4 class="font-medium text-gray-900 mb-2">Deploy Providers</h4>
                  <p class="text-sm text-gray-500">Use specialized deployment providers (Internal + Plugins: Helm, Kubernetes, Docker) for direct deployment</p>
                  <div class="mt-3 text-xs text-gray-400">
                    <i class="fas fa-check mr-1"></i> Tool-specific deployment
                    <br>
                    <i class="fas fa-check mr-1"></i> Direct integration
                    <br>
                    <i class="fas fa-check mr-1"></i> Simplified configuration
                  </div>
                </div>
              </label>
            </div>
          </div>
        </div>

        <!-- Step 4: Configuration -->
        <div *ngIf="wizardState.currentStep === 4" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Deployment Configuration</h3>
            
            <!-- Workflow Selection (if workflow method selected) -->
            <div *ngIf="wizardState.deploymentMethod === 'workflow'" class="mb-6">
              <label class="block text-sm font-medium text-gray-700 mb-3">
                Select Workflow
                <span class="text-red-500">*</span>
              </label>
              <div class="grid grid-cols-1 gap-3">
                <label *ngFor="let workflow of getAvailableWorkflows()" 
                       class="flex items-center p-4 border-2 rounded-lg cursor-pointer transition-colors"
                       [class]="isWorkflowSelected(workflow) ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                  <input type="radio" 
                         [checked]="isWorkflowSelected(workflow)"
                         (change)="selectWorkflow(workflow)"
                         class="sr-only">
                  <div class="flex flex-col flex-1">
                    <div class="flex items-center justify-between mb-2">
                      <span class="font-medium text-gray-900">{{ workflow.name }}</span>
                      <span class="text-xs px-2 py-1 rounded-full" 
                            [class]="workflow.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'">
                        {{ workflow.isActive ? 'Active' : 'Inactive' }}
                      </span>
                    </div>
                    <p *ngIf="workflow.description" class="text-sm text-gray-500 mb-2">{{ workflow.description }}</p>
                    <div class="flex flex-wrap gap-1">
                      <span *ngFor="let step of workflow.steps?.slice(0, 3)" 
                            class="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded">
                        {{ step.name }}
                      </span>
                      <span *ngIf="workflow.steps && workflow.steps.length > 3" 
                            class="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded">
                        +{{ workflow.steps.length - 3 }} more
                      </span>
                    </div>
                  </div>
                </label>
                <div *ngIf="getAvailableWorkflows().length === 0" 
                     class="text-center py-8 text-gray-500">
                  <i class="fas fa-exclamation-circle text-2xl mb-2"></i>
                  <p>No workflows available for this project</p>
                  <p class="text-sm">Please create a workflow first or select Deploy Providers method</p>
                </div>
              </div>
            </div>
            
            <!-- Workflow Parameters (if workflow selected and has parameters) -->
            <div *ngIf="wizardState.deploymentMethod === 'workflow' && wizardState.selectedWorkflow && wizardState.selectedWorkflow.parameters && wizardState.selectedWorkflow.parameters.length > 0" class="mb-6">
              <label class="block text-sm font-medium text-gray-700 mb-3">
                Workflow Parameters
                <span class="text-red-500">*</span>
              </label>
              <div class="space-y-4 bg-gray-50 rounded-lg p-4">
                <div *ngFor="let param of wizardState.selectedWorkflow.parameters" class="space-y-2">
                  <label [for]="'param-' + param.name" class="block text-sm font-medium text-gray-700">
                    {{ param.name }}
                    <span *ngIf="param.required" class="text-red-500">*</span>
                    <span *ngIf="param.description" class="text-gray-500 font-normal">- {{ param.description }}</span>
                  </label>
                  
                  <!-- String/Text Parameter -->
                  <div *ngIf="param.type === 'string' || param.type === 'text'; else otherTypes">
                    <input
                      [id]="'param-' + param.name"
                      type="text"
                      [(ngModel)]="wizardState.workflowParameters[param.name]"
                      [placeholder]="param.defaultValue || 'Enter ' + param.name"
                      [required]="param.required"
                      (ngModelChange)="validateWizardStep()"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  </div>
                  
                  <ng-template #otherTypes>
                    <!-- Number Parameter -->
                    <div *ngIf="param.type === 'number'">
                      <input
                        [id]="'param-' + param.name"
                        type="number"
                        [(ngModel)]="wizardState.workflowParameters[param.name]"
                        [placeholder]="param.defaultValue || 'Enter ' + param.name"
                        [required]="param.required"
                        (ngModelChange)="validateWizardStep()"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <!-- Boolean Parameter -->
                    <div *ngIf="param.type === 'boolean'">
                      <label class="flex items-center">
                        <input
                          [id]="'param-' + param.name"
                          type="checkbox"
                          [(ngModel)]="wizardState.workflowParameters[param.name]"
                          (ngModelChange)="validateWizardStep()"
                          class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">Enable {{ param.name }}</span>
                      </label>
                    </div>
                    
                    <!-- Select Parameter -->
                    <div *ngIf="param.type === 'select' && param.options">
                      <select
                        [id]="'param-' + param.name"
                        [(ngModel)]="wizardState.workflowParameters[param.name]"
                        [required]="param.required"
                        (ngModelChange)="validateWizardStep()"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select {{ param.name }}</option>
                        <option *ngFor="let option of param.options" [value]="option">{{ option }}</option>
                      </select>
                    </div>
                    
                    <!-- Textarea for long text -->
                    <div *ngIf="param.type === 'textarea'">
                      <textarea
                        [id]="'param-' + param.name"
                        [(ngModel)]="wizardState.workflowParameters[param.name]"
                        [placeholder]="param.defaultValue || 'Enter ' + param.name"
                        [required]="param.required"
                        (ngModelChange)="validateWizardStep()"
                        rows="3"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                    </div>
                  </ng-template>
                </div>
              </div>
            </div>
            
            <!-- Deployment Strategy -->
            <div class="mb-6">
              <label class="block text-sm font-medium text-gray-700 mb-3">
                Deployment Strategy
                <span class="text-gray-500 text-xs font-normal">(Optional - leave unselected for manual deployment)</span>
              </label>
              <div class="grid grid-cols-2 lg:grid-cols-3 gap-4">
                <label class="flex items-center p-4 border-2 rounded-lg cursor-pointer transition-colors"
                       [class]="wizardState.deploymentStrategy === null ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                  <input type="radio" 
                         [(ngModel)]="wizardState.deploymentStrategy" 
                         [value]="null" 
                         class="sr-only"
                         (change)="validateWizardStep()">
                  <div class="flex flex-col">
                    <div class="flex items-center mb-2">
                      <i class="fas fa-hand-paper mr-2" [class]="wizardState.deploymentStrategy === null ? 'text-blue-600' : 'text-gray-400'"></i>
                      <span class="font-medium">Manual</span>
                    </div>
                    <span class="text-sm text-gray-500">No automated strategy</span>
                  </div>
                </label>
                
                <label class="flex items-center p-4 border-2 rounded-lg cursor-pointer transition-colors"
                       [class]="wizardState.deploymentStrategy === 'rolling_update' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                  <input type="radio" 
                         [(ngModel)]="wizardState.deploymentStrategy" 
                         value="rolling_update" 
                         class="sr-only"
                         (change)="validateWizardStep()">
                  <div class="flex flex-col">
                    <div class="flex items-center mb-2">
                      <i class="fas fa-sync-alt mr-2" [class]="wizardState.deploymentStrategy === 'rolling_update' ? 'text-blue-600' : 'text-gray-400'"></i>
                      <span class="font-medium">Rolling Update</span>
                    </div>
                    <span class="text-sm text-gray-500">Gradually replace instances</span>
                  </div>
                </label>
                
                <label class="flex items-center p-4 border-2 rounded-lg cursor-pointer transition-colors"
                       [class]="wizardState.deploymentStrategy === 'blue_green' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                  <input type="radio" 
                         [(ngModel)]="wizardState.deploymentStrategy" 
                         value="blue_green" 
                         class="sr-only"
                         (change)="validateWizardStep()">
                  <div class="flex flex-col">
                    <div class="flex items-center mb-2">
                      <i class="fas fa-exchange-alt mr-2" [class]="wizardState.deploymentStrategy === 'blue_green' ? 'text-blue-600' : 'text-gray-400'"></i>
                      <span class="font-medium">Blue-Green</span>
                    </div>
                    <span class="text-sm text-gray-500">Switch between environments</span>
                  </div>
                </label>
                
                <label class="flex items-center p-4 border-2 rounded-lg cursor-pointer transition-colors"
                       [class]="wizardState.deploymentStrategy === 'canary' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                  <input type="radio" 
                         [(ngModel)]="wizardState.deploymentStrategy" 
                         value="canary" 
                         class="sr-only"
                         (change)="validateWizardStep()">
                  <div class="flex flex-col">
                    <div class="flex items-center mb-2">
                      <i class="fas fa-feather mr-2" [class]="wizardState.deploymentStrategy === 'canary' ? 'text-blue-600' : 'text-gray-400'"></i>
                      <span class="font-medium">Canary</span>
                    </div>
                    <span class="text-sm text-gray-500">Gradual traffic shifting</span>
                  </div>
                </label>
                
                <label class="flex items-center p-4 border-2 rounded-lg cursor-pointer transition-colors"
                       [class]="wizardState.deploymentStrategy === 'recreate' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                  <input type="radio" 
                         [(ngModel)]="wizardState.deploymentStrategy" 
                         value="recreate" 
                         class="sr-only"
                         (change)="validateWizardStep()">
                  <div class="flex flex-col">
                    <div class="flex items-center mb-2">
                      <i class="fas fa-redo mr-2" [class]="wizardState.deploymentStrategy === 'recreate' ? 'text-blue-600' : 'text-gray-400'"></i>
                      <span class="font-medium">Recreate</span>
                    </div>
                    <span class="text-sm text-gray-500">Stop then start</span>
                  </div>
                </label>
              </div>
            </div>

            <!-- Configuration Options -->
            <div class="space-y-4">
              <div>
                <label for="version" class="block text-sm font-medium text-gray-700 mb-2">Version (optional)</label>
                <input type="text" 
                       id="version"
                       [(ngModel)]="wizardState.configuration.version"
                       placeholder="e.g., v1.2.3, latest"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              </div>
              
              <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea id="description"
                          [(ngModel)]="wizardState.configuration.description"
                          rows="3"
                          placeholder="Describe this deployment..."
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
              </div>
              
              <!-- Options -->
              <div class="space-y-3">
                <label class="flex items-center">
                  <input type="checkbox" 
                         [(ngModel)]="wizardState.configuration.rollbackOnFailure"
                         class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                  <span class="ml-2 text-sm text-gray-700">Auto-rollback on failure</span>
                </label>
                
                <label class="flex items-center">
                  <input type="checkbox" 
                         [(ngModel)]="wizardState.configuration.runHealthChecks"
                         class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                  <span class="ml-2 text-sm text-gray-700">Run health checks after deployment</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 5: Plugin & Provider Configuration (only for provider method) -->
        <div *ngIf="wizardState.currentStep === 5 && wizardState.deploymentMethod === 'provider'" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Deployment Method Selection</h3>
            <p class="text-sm text-gray-500 mb-6">Choose your deployment approach: use a plugin for automated tooling or select a provider for direct infrastructure deployment.</p>
            
            <!-- Deployment Method Choice -->
            <div class="mb-8">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Plugin Option -->
                <label class="flex flex-col p-6 border-2 rounded-lg cursor-pointer transition-colors"
                       [class]="wizardState.selectedPlugin ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                  <input type="radio" 
                         name="deploymentType"
                         [checked]="wizardState.selectedPlugin !== null"
                         (change)="selectDeploymentType('plugin')"
                         class="sr-only">
                  <div class="flex items-center justify-between mb-3">
                    <div class="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                      <i class="fas fa-plug text-blue-600"></i>
                    </div>
                    <div *ngIf="wizardState.selectedPlugin" class="text-blue-600">
                      <i class="fas fa-check-circle"></i>
                    </div>
                  </div>
                  <h4 class="font-medium text-gray-900 mb-2">Deployment Plugin</h4>
                  <p class="text-sm text-gray-500">Use automated deployment tools like Helm, Kubernetes, Docker, etc.</p>
                </label>

                <!-- Provider Option -->
                <label class="flex flex-col p-6 border-2 rounded-lg cursor-pointer transition-colors"
                       [class]="wizardState.selectedProvider ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                  <input type="radio" 
                         name="deploymentType"
                         [checked]="wizardState.selectedProvider !== null"
                         (change)="selectDeploymentType('provider')"
                         class="sr-only">
                  <div class="flex items-center justify-between mb-3">
                    <div class="flex h-10 w-10 items-center justify-center rounded-full bg-green-100">
                      <i class="fas fa-cloud text-green-600"></i>
                    </div>
                    <div *ngIf="wizardState.selectedProvider" class="text-blue-600">
                      <i class="fas fa-check-circle"></i>
                    </div>
                  </div>
                  <h4 class="font-medium text-gray-900 mb-2">Cloud Provider</h4>
                  <p class="text-sm text-gray-500">Deploy directly to cloud platforms like AWS, GCP, Azure, etc.</p>
                </label>
              </div>
            </div>

            <!-- Plugin Selection Section -->
            <div *ngIf="wizardState.selectedPlugin || (!wizardState.selectedProvider && !wizardState.selectedPlugin)" class="mb-6">
              <label class="block text-sm font-medium text-gray-700 mb-3">
                Select Deployment Plugin
                <span class="text-gray-500 text-xs font-normal">(Choose the tool that will handle the deployment)</span>
              </label>
              <div class="space-y-3">
                <!-- Dynamic Plugin Options from Backend -->
                <div *ngIf="compatiblePlugins.length === 0 && availablePlugins.length === 0" class="text-center py-8">
                  <div class="text-gray-400 mb-2">
                    <i class="fas fa-plug text-3xl"></i>
                  </div>
                  <p class="text-gray-500">Loading plugins...</p>
                </div>
                
                <div *ngIf="compatiblePlugins.length === 0 && availablePlugins.length > 0" class="text-center py-8">
                  <div class="text-gray-400 mb-2">
                    <i class="fas fa-exclamation-triangle text-3xl"></i>
                  </div>
                  <p class="text-gray-500">No compatible plugins found for selected applications</p>
                  <p class="text-xs text-gray-400 mt-1">Showing all available plugins instead</p>
                </div>
                
                <!-- Show compatible plugins if available, otherwise show all plugins -->
                <label *ngFor="let plugin of (compatiblePlugins.length > 0 ? compatiblePlugins : availablePlugins)" 
                       class="flex items-center p-4 border-2 rounded-lg cursor-pointer transition-colors"
                       [class]="isPluginSelected(plugin) ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                  <input type="radio" 
                         [value]="plugin"
                         [checked]="isPluginSelected(plugin)"
                         (change)="selectPlugin(plugin)"
                         class="sr-only">
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <div class="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                        <i class="fas fa-plug text-blue-600"></i>
                      </div>
                      <div class="ml-4">
                        <h4 class="font-medium text-gray-900">{{ plugin.name }}</h4>
                        <p class="text-sm text-gray-500">{{ plugin.description }}</p>
                        <div class="text-xs text-gray-400 mt-1">
                          <span *ngFor="let type of plugin.supportedArtifacts; let last = last">
                            {{ type }}<span *ngIf="!last">, </span>
                          </span>
                        </div>
                      </div>
                    </div>
                    <div *ngIf="isPluginSelected(plugin)" class="text-blue-600">
                      <i class="fas fa-check-circle"></i>
                    </div>
                  </div>
                </label>

                <!-- Manual/No Plugin Option -->
                <label class="flex items-center p-4 border-2 rounded-lg cursor-pointer transition-colors"
                       [class]="wizardState.selectedPlugin === null ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                  <input type="radio" 
                         [(ngModel)]="wizardState.selectedPlugin" 
                         [value]="null" 
                         class="sr-only"
                         (change)="selectPlugin(null)">
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <div class="flex h-10 w-10 items-center justify-center rounded-full bg-gray-100">
                        <i class="fas fa-hand-paper text-gray-600"></i>
                      </div>
                      <div class="ml-4">
                        <h4 class="font-medium text-gray-900">No Plugin (Manual)</h4>
                        <p class="text-sm text-gray-500">Manual deployment without automated tooling</p>
                      </div>
                    </div>
                    <div *ngIf="wizardState.selectedPlugin === null" class="text-blue-600">
                      <i class="fas fa-check-circle"></i>
                    </div>
                  </div>
                </label>
              </div>
            </div>

            <!-- Provider Selection Section -->
            <div *ngIf="wizardState.selectedProvider || (!wizardState.selectedProvider && !wizardState.selectedPlugin)" class="mb-6">
              <label class="block text-sm font-medium text-gray-700 mb-3">
                Select Cloud Provider
                <span class="text-gray-500 text-xs font-normal">(Choose the platform where you want to deploy)</span>
              </label>
              <div class="space-y-3">
                <!-- Loading State -->
                <div *ngIf="availableProviders.length === 0" class="text-center py-8">
                  <div class="text-gray-400 mb-2">
                    <i class="fas fa-cloud text-3xl"></i>
                  </div>
                  <p class="text-gray-500">Loading providers...</p>
                </div>
                
                <!-- Provider Options -->
                <label *ngFor="let provider of availableProviders" 
                       class="flex items-center p-4 border-2 rounded-lg cursor-pointer transition-colors"
                       [class]="isProviderSelected(provider) ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
                  <input type="radio" 
                         [value]="provider"
                         [checked]="isProviderSelected(provider)"
                         (change)="selectProvider(provider)"
                         class="sr-only">
                  <div class="flex items-center justify-between w-full">
                    <div class="flex items-center">
                      <div class="flex h-10 w-10 items-center justify-center rounded-full"
                           [class]="getProviderIconClass(provider.type)">
                        <i [class]="getProviderIcon(provider.type)"></i>
                      </div>
                      <div class="ml-4">
                        <h4 class="font-medium text-gray-900">{{ provider.name }}</h4>
                        <p class="text-sm text-gray-500">{{ provider.description }}</p>
                        <div class="text-xs text-gray-400 mt-1">
                          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-2">
                            {{ provider.category }}
                          </span>
                          <span *ngFor="let capability of provider.capabilities.slice(0, 3); let last = last">
                            {{ capability }}<span *ngIf="!last">, </span>
                          </span>
                          <span *ngIf="provider.capabilities.length > 3">...</span>
                        </div>
                      </div>
                    </div>
                    <div *ngIf="isProviderSelected(provider)" class="text-blue-600">
                      <i class="fas fa-check-circle"></i>
                    </div>
                  </div>
                </label>
              </div>
            </div>

            <!-- Provider Configuration -->
            <div *ngIf="wizardState.selectedProvider" class="space-y-6">
              <h4 class="text-md font-semibold text-gray-900 border-b border-gray-200 pb-2">Provider Configuration</h4>
              <p class="text-sm text-gray-500 mb-4">Configure {{ wizardState.selectedProvider.name }} ({{ wizardState.selectedProvider.type }})</p>
              
              <!-- Dynamic Provider Configuration -->
              <div class="space-y-4">
                <div *ngFor="let field of wizardState.selectedProvider.configFields" class="form-field">
                  
                  <!-- String Input -->
                  <div *ngIf="field.type === 'string' || field.type === 'password'">
                    <label [for]="field.name" class="block text-sm font-medium text-gray-700 mb-2">
                      {{ field.label }}
                      <span *ngIf="field.required" class="text-red-500">*</span>
                    </label>
                    <input 
                      [type]="field.type === 'password' ? 'password' : 'text'"
                      [id]="field.name"
                      [(ngModel)]="wizardState.providerConfig[field.name]"
                      [placeholder]="field.placeholder || field.description"
                      [required]="field.required"
                      (ngModelChange)="onProviderConfigChange(field.name, $event)"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <p *ngIf="field.description" class="text-xs text-gray-500 mt-1">{{ field.description }}</p>
                  </div>

                  <!-- Number Input -->
                  <div *ngIf="field.type === 'number'">
                    <label [for]="field.name" class="block text-sm font-medium text-gray-700 mb-2">
                      {{ field.label }}
                      <span *ngIf="field.required" class="text-red-500">*</span>
                    </label>
                    <input 
                      type="number"
                      [id]="field.name"
                      [(ngModel)]="wizardState.providerConfig[field.name]"
                      [placeholder]="field.placeholder || field.description"
                      [required]="field.required"
                      [min]="field.validation?.min || null"
                      [max]="field.validation?.max || null"
                      (ngModelChange)="onProviderConfigChange(field.name, $event)"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <p *ngIf="field.description" class="text-xs text-gray-500 mt-1">{{ field.description }}</p>
                  </div>

                  <!-- Boolean Checkbox -->
                  <div *ngIf="field.type === 'boolean'">
                    <label class="flex items-center">
                      <input 
                        type="checkbox"
                        [id]="field.name"
                        [(ngModel)]="wizardState.providerConfig[field.name]"
                        (ngModelChange)="onProviderConfigChange(field.name, $event)"
                        class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                      <span class="ml-2 text-sm text-gray-700">
                        {{ field.label }}
                        <span *ngIf="field.required" class="text-red-500">*</span>
                      </span>
                    </label>
                    <p *ngIf="field.description" class="text-xs text-gray-500 mt-1 ml-6">{{ field.description }}</p>
                  </div>

                  <!-- Select Dropdown -->
                  <div *ngIf="field.type === 'select'">
                    <label [for]="field.name" class="block text-sm font-medium text-gray-700 mb-2">
                      {{ field.label }}
                      <span *ngIf="field.required" class="text-red-500">*</span>
                    </label>
                    <select 
                      [id]="field.name"
                      [(ngModel)]="wizardState.providerConfig[field.name]"
                      [required]="field.required"
                      (ngModelChange)="onProviderConfigChange(field.name, $event)"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                      <option value="">Select {{ field.label.toLowerCase() }}</option>
                      <option *ngFor="let option of field.options" [value]="option.value">{{ option.label }}</option>
                    </select>
                    <p *ngIf="field.description" class="text-xs text-gray-500 mt-1">{{ field.description }}</p>
                  </div>

                  <!-- Multi-select -->
                  <div *ngIf="field.type === 'multiselect'">
                    <label [for]="field.name" class="block text-sm font-medium text-gray-700 mb-2">
                      {{ field.label }}
                      <span *ngIf="field.required" class="text-red-500">*</span>
                    </label>
                    <div class="space-y-2">
                      <label *ngFor="let option of field.options" class="flex items-center">
                        <input 
                          type="checkbox"
                          [value]="option.value"
                          [checked]="isProviderOptionSelected(field.name, option.value)"
                          (change)="toggleProviderOption(field.name, option.value, $event)"
                          class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">{{ option.label }}</span>
                      </label>
                    </div>
                    <p *ngIf="field.description" class="text-xs text-gray-500 mt-1">{{ field.description }}</p>
                  </div>

                  <!-- File Input -->
                  <div *ngIf="field.type === 'file'">
                    <label [for]="field.name" class="block text-sm font-medium text-gray-700 mb-2">
                      {{ field.label }}
                      <span *ngIf="field.required" class="text-red-500">*</span>
                    </label>
                    <input 
                      type="file"
                      [id]="field.name"
                      [required]="field.required"
                      (change)="onProviderFileChange(field.name, $event)"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <p *ngIf="field.description" class="text-xs text-gray-500 mt-1">{{ field.description }}</p>
                  </div>

                </div>
              </div>
            </div>

            <!-- Plugin Configuration -->
            <div *ngIf="wizardState.selectedPlugin" class="space-y-6">
              <h4 class="text-md font-semibold text-gray-900 border-b border-gray-200 pb-2">Plugin Configuration</h4>
              <p class="text-sm text-gray-500 mb-4">Configure {{ wizardState.selectedPlugin.name }} ({{ wizardState.selectedPlugin.type }})</p>
              
              <!-- Dynamic Plugin Configuration -->
              <div class="space-y-4">
                <div *ngFor="let field of wizardState.selectedPlugin.configurationSchema" class="form-field">
                  
                  <!-- String Input -->
                  <div *ngIf="field.type === 'string' || field.type === 'password'">
                    <label [for]="field.key" class="block text-sm font-medium text-gray-700 mb-2">
                      {{ field.label }}
                      <span *ngIf="field.required" class="text-red-500">*</span>
                    </label>
                    <input 
                      [type]="field.type === 'password' ? 'password' : 'text'"
                      [id]="field.key"
                      [(ngModel)]="wizardState.pluginConfig[field.key]"
                      [placeholder]="field.description || ''"
                      [required]="field.required"
                      (ngModelChange)="onPluginConfigChange(field.key, $event)"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <p *ngIf="field.description" class="text-xs text-gray-500 mt-1">{{ field.description }}</p>
                  </div>

                  <!-- Textarea -->
                  <div *ngIf="field.type === 'textarea'">
                    <label [for]="field.key" class="block text-sm font-medium text-gray-700 mb-2">
                      {{ field.label }}
                      <span *ngIf="field.required" class="text-red-500">*</span>
                    </label>
                    <textarea 
                      [id]="field.key"
                      [(ngModel)]="wizardState.pluginConfig[field.key]"
                      [placeholder]="field.description || ''"
                      [required]="field.required"
                      (ngModelChange)="onPluginConfigChange(field.key, $event)"
                      rows="4"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"></textarea>
                    <p *ngIf="field.description" class="text-xs text-gray-500 mt-1">{{ field.description }}</p>
                  </div>

                  <!-- Number Input -->
                  <div *ngIf="field.type === 'number'">
                    <label [for]="field.key" class="block text-sm font-medium text-gray-700 mb-2">
                      {{ field.label }}
                      <span *ngIf="field.required" class="text-red-500">*</span>
                    </label>
                    <input 
                      type="number"
                      [id]="field.key"
                      [(ngModel)]="wizardState.pluginConfig[field.key]"
                      [placeholder]="field.description || ''"
                      [required]="field.required"
                      [min]="field.validation?.min || null"
                      [max]="field.validation?.max || null"
                      (ngModelChange)="onPluginConfigChange(field.key, $event)"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <p *ngIf="field.description" class="text-xs text-gray-500 mt-1">{{ field.description }}</p>
                  </div>

                  <!-- Boolean Checkbox -->
                  <div *ngIf="field.type === 'boolean'">
                    <label class="flex items-center">
                      <input 
                        type="checkbox"
                        [id]="field.key"
                        [(ngModel)]="wizardState.pluginConfig[field.key]"
                        (ngModelChange)="onPluginConfigChange(field.key, $event)"
                        class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                      <span class="ml-2 text-sm text-gray-700">
                        {{ field.label }}
                        <span *ngIf="field.required" class="text-red-500">*</span>
                      </span>
                    </label>
                    <p *ngIf="field.description" class="text-xs text-gray-500 mt-1 ml-6">{{ field.description }}</p>
                  </div>

                  <!-- Select Dropdown -->
                  <div *ngIf="field.type === 'select'">
                    <label [for]="field.key" class="block text-sm font-medium text-gray-700 mb-2">
                      {{ field.label }}
                      <span *ngIf="field.required" class="text-red-500">*</span>
                    </label>
                    <select 
                      [id]="field.key"
                      [(ngModel)]="wizardState.pluginConfig[field.key]"
                      [required]="field.required"
                      (ngModelChange)="onPluginConfigChange(field.key, $event)"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                      <option value="">Select {{ field.label.toLowerCase() }}</option>
                      <option *ngFor="let option of field.options" [value]="option.value">{{ option.label }}</option>
                    </select>
                    <p *ngIf="field.description" class="text-xs text-gray-500 mt-1">{{ field.description }}</p>
                  </div>

                  <!-- File Input -->
                  <div *ngIf="field.type === 'file'">
                    <label [for]="field.key" class="block text-sm font-medium text-gray-700 mb-2">
                      {{ field.label }}
                      <span *ngIf="field.required" class="text-red-500">*</span>
                    </label>
                    <input 
                      type="file"
                      [id]="field.key"
                      [required]="field.required"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <p *ngIf="field.description" class="text-xs text-gray-500 mt-1">{{ field.description }}</p>
                  </div>

                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 6: Review -->
        <div *ngIf="wizardState.currentStep === 6" class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Review Deployment</h3>
            
            <div class="bg-gray-50 rounded-lg p-6 space-y-4">
              <!-- Applications -->
              <div>
                <h4 class="font-medium text-gray-900 mb-2">Applications</h4>
                <div class="space-y-1">
                  <div *ngFor="let app of wizardState.selectedApplications" class="flex items-center text-sm">
                    <i class="fas fa-cube text-blue-600 mr-2"></i>
                    <span>{{ app.name }}</span>
                  </div>
                </div>
              </div>
              
              <!-- Environment -->
              <div>
                <h4 class="font-medium text-gray-900 mb-2">Environment</h4>
                <div class="flex items-center text-sm">
                  <i class="fas fa-server text-green-600 mr-2"></i>
                  <span>{{ wizardState.selectedEnvironment?.name }}</span>
                </div>
              </div>
              
              <!-- Deployment Method -->
              <div>
                <h4 class="font-medium text-gray-900 mb-2">Deployment Method</h4>
                <div class="flex items-center text-sm">
                  <i [class]="wizardState.deploymentMethod === 'workflow' ? 'fas fa-project-diagram' : 'fas fa-plug'" 
                     class="text-purple-600 mr-2"></i>
                  <span>{{ wizardState.deploymentMethod === 'workflow' ? 'Workflow-Based' : 'Provider-Based' }}</span>
                </div>
              </div>

              <!-- Strategy -->
              <div>
                <h4 class="font-medium text-gray-900 mb-2">Strategy</h4>
                <div class="flex items-center text-sm">
                  <i class="fas fa-cog text-purple-600 mr-2"></i>
                  <span>{{ wizardState.deploymentStrategy || 'Manual/None' }}</span>
                </div>
              </div>

              <!-- Selected Plugin -->
              <div *ngIf="wizardState.deploymentMethod === 'provider' && wizardState.selectedPlugin">
                <h4 class="font-medium text-gray-900 mb-2">Selected Plugin</h4>
                <div class="flex items-center text-sm">
                  <i class="fas fa-plug text-blue-600 mr-2"></i>
                  <span>{{ wizardState.selectedPlugin.name }}</span>
                  <span class="ml-2 text-gray-500">({{ wizardState.selectedPlugin.type }})</span>
                </div>
              </div>

              <!-- Selected Provider -->
              <div *ngIf="wizardState.deploymentMethod === 'provider' && wizardState.selectedProvider">
                <h4 class="font-medium text-gray-900 mb-2">Selected Provider</h4>
                <div class="flex items-center text-sm">
                  <i [class]="getProviderIcon(wizardState.selectedProvider.type)" class="mr-2"></i>
                  <span>{{ wizardState.selectedProvider.name }}</span>
                  <span class="ml-2 text-gray-500">({{ wizardState.selectedProvider.type }})</span>
                </div>
              </div>

              <!-- Manual Deployment -->
              <div *ngIf="wizardState.deploymentMethod === 'provider' && !wizardState.selectedPlugin && !wizardState.selectedProvider">
                <h4 class="font-medium text-gray-900 mb-2">Deployment Type</h4>
                <div class="flex items-center text-sm">
                  <i class="fas fa-hand-paper text-gray-600 mr-2"></i>
                  <span>Manual Deployment</span>
                  <span class="ml-2 text-gray-500">(No automated tooling)</span>
                </div>
              </div>
              
              <!-- Selected Workflow -->
              <div *ngIf="wizardState.deploymentMethod === 'workflow' && wizardState.selectedWorkflow">
                <h4 class="font-medium text-gray-900 mb-2">Selected Workflow</h4>
                <div class="flex items-center text-sm">
                  <i class="fas fa-project-diagram text-blue-600 mr-2"></i>
                  <span>{{ wizardState.selectedWorkflow.name }}</span>
                  <span class="ml-2 text-gray-500">({{ wizardState.selectedWorkflow.version }})</span>
                </div>
              </div>
              
              <!-- Plugin Configuration -->
              <div *ngIf="wizardState.selectedPlugin && wizardState.pluginConfig && getObjectKeys(wizardState.pluginConfig).length > 0">
                <h4 class="font-medium text-gray-900 mb-2">Plugin Configuration</h4>
                <div class="space-y-1 text-sm bg-white rounded border p-3">
                  <div *ngFor="let key of getObjectKeys(wizardState.pluginConfig)" class="flex">
                    <span class="text-gray-600 font-medium w-32">{{ key }}:</span>
                    <span class="text-gray-900">{{ wizardState.pluginConfig[key] || 'Not set' }}</span>
                  </div>
                </div>
              </div>

              <!-- Provider Configuration -->
              <div *ngIf="wizardState.selectedProvider && wizardState.providerConfig && getObjectKeys(wizardState.providerConfig).length > 0">
                <h4 class="font-medium text-gray-900 mb-2">Provider Configuration</h4>
                <div class="space-y-1 text-sm bg-white rounded border p-3">
                  <div *ngFor="let key of getObjectKeys(wizardState.providerConfig)" class="flex">
                    <span class="text-gray-600 font-medium w-32">{{ key }}:</span>
                    <span class="text-gray-900">
                      <span *ngIf="isProviderFieldSensitive(key); else normalValue">••••••••</span>
                      <ng-template #normalValue>{{ wizardState.providerConfig[key] || 'Not set' }}</ng-template>
                    </span>
                  </div>
                </div>
              </div>

              <!-- General Configuration -->
              <div *ngIf="wizardState.configuration.version || wizardState.configuration.description">
                <h4 class="font-medium text-gray-900 mb-2">Additional Configuration</h4>
                <div class="space-y-1 text-sm">
                  <div *ngIf="wizardState.configuration.version">
                    <span class="text-gray-600">Version:</span> {{ wizardState.configuration.version }}
                  </div>
                  <div *ngIf="wizardState.configuration.description">
                    <span class="text-gray-600">Description:</span> {{ wizardState.configuration.description }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div *ngIf="wizardState.isDeploying" class="flex flex-col items-center justify-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
          <p class="text-gray-600">Creating deployment...</p>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="border-t border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between">
          <button *ngIf="wizardState.currentStep > 1" 
                  (click)="previousWizardStep()"
                  [disabled]="wizardState.isDeploying"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
            <i class="fas fa-chevron-left mr-2"></i>
            Previous
          </button>
          <div *ngIf="wizardState.currentStep === 1"></div>
          
          <div class="flex space-x-3">
            <button (click)="closeDeploymentWizard()"
                    [disabled]="wizardState.isDeploying"
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
              Cancel
            </button>
            
            <button *ngIf="wizardState.currentStep < wizardState.totalSteps"
                    (click)="nextWizardStep()"
                    [disabled]="!wizardState.isValid || wizardState.isDeploying"
                    class="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
              Next
              <i class="fas fa-chevron-right ml-2"></i>
            </button>
            
            <button *ngIf="wizardState.currentStep === wizardState.totalSteps"
                    (click)="submitDeployment()"
                    [disabled]="!wizardState.isValid || wizardState.isDeploying"
                    class="px-6 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed">
              <i class="fas fa-rocket mr-2"></i>
              Deploy
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Deployment Details Modal -->
<div class="modal-overlay" *ngIf="detailsDialog.isOpen" (click)="closeDetailsDialog()">
  <div class="modal-container details-modal" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>
        <i class="fas fa-info-circle"></i>
        Deployment Details
      </h2>
      <button class="modal-close" (click)="closeDetailsDialog()">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="modal-content" *ngIf="detailsDialog.deployment">
      <div class="details-grid">
        <div class="details-section">
          <h4>Basic Information</h4>
          <div class="detail-item">
            <label>ID:</label>
            <span>{{ detailsDialog.deployment.id }}</span>
          </div>
          <div class="detail-item">
            <label>Application:</label>
            <span>{{ detailsDialog.deployment.applicationName }}</span>
          </div>
          <div class="detail-item">
            <label>Environment:</label>
            <span>{{ detailsDialog.deployment.environmentName }}</span>
          </div>
          <div class="detail-item">
            <label>Version:</label>
            <span>{{ detailsDialog.deployment.version }}</span>
          </div>
          <div class="detail-item">
            <label>Strategy:</label>
            <span>{{ detailsDialog.deployment.strategy }}</span>
          </div>
          <div class="detail-item">
            <label>Status:</label>
            <span class="status-badge" [attr.data-status]="detailsDialog.deployment.status">
              <i [class]="getStatusIcon(detailsDialog.deployment.status)"></i>
              {{ detailsDialog.deployment.status }}
            </span>
          </div>
        </div>

        <div class="details-section">
          <h4>Timeline</h4>
          <div class="detail-item">
            <label>Started:</label>
            <span>{{ detailsDialog.deployment.startedAt | date:'full' }}</span>
          </div>
          <div class="detail-item" *ngIf="detailsDialog.deployment.completedAt">
            <label>Completed:</label>
            <span>{{ detailsDialog.deployment.completedAt | date:'full' }}</span>
          </div>
          <div class="detail-item">
            <label>Duration:</label>
            <span>{{ getDuration(detailsDialog.deployment) }}</span>
          </div>
        </div>

        <div class="details-section" *ngIf="detailsDialog.deployment.description">
          <h4>Description</h4>
          <p>{{ detailsDialog.deployment.description }}</p>
        </div>

        <div class="details-section" *ngIf="detailsDialog.deployment.metadata">
          <h4>Metadata</h4>
          <div class="metadata-grid">
            <div *ngFor="let item of getMetadataEntries(detailsDialog.deployment.metadata)" class="metadata-item">
              <label>{{ item.key }}:</label>
              <span>{{ item.value }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button class="btn btn-outline" (click)="closeDetailsDialog()">Close</button>
      <button
        class="btn btn-outline"
        (click)="openDeploymentLogs(detailsDialog.deployment)">
        <i class="fas fa-file-alt"></i>
        View Logs
      </button>
      <button
        *ngIf="canPromote(detailsDialog.deployment)"
        class="btn btn-success"
        (click)="openPromotionDialog(detailsDialog.deployment)">
        <i class="fas fa-arrow-up"></i>
        Promote
      </button>
    </div>
  </div>
</div>

<!-- Promotion Dialog -->
<div class="modal-overlay" *ngIf="promotionDialog.isOpen" (click)="closePromotionDialog()">
  <div class="modal-container promotion-modal" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>
        <i class="fas fa-arrow-up"></i>
        Promote Deployment
      </h2>
      <button class="modal-close" (click)="closePromotionDialog()">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="modal-content">
      <div class="promotion-info">
        <p>Promote <strong>{{ promotionDialog.deployment?.applicationName }}</strong> 
           from <strong>{{ promotionDialog.deployment?.environmentName }}</strong></p>
      </div>

      <div class="form-group">
        <label>Target Environment</label>
        <select [(ngModel)]="promotionDialog.targetEnvironmentId" class="form-control">
          <option value="">Select target environment</option>
          <option *ngFor="let env of getPromotionTargets()" [value]="env.id">
            {{ env.name }}
          </option>
        </select>
      </div>

      <div class="form-group">
        <label>Description</label>
        <textarea
          [(ngModel)]="promotionDialog.description"
          class="form-control"
          rows="3"
          placeholder="Promotion description..."></textarea>
      </div>

      <div class="form-group">
        <label>
          <input
            type="checkbox"
            [(ngModel)]="promotionDialog.runTests">
          Run tests after promotion
        </label>
      </div>
    </div>

    <div class="modal-footer">
      <button class="btn btn-outline" (click)="closePromotionDialog()">Cancel</button>
      <button
        class="btn btn-success"
        (click)="startPromotion()"
        [disabled]="!promotionDialog.targetEnvironmentId || promotionDialog.isPromoting">
        <i class="fas fa-arrow-up"></i>
        <span *ngIf="!promotionDialog.isPromoting">Promote</span>
        <span *ngIf="promotionDialog.isPromoting">Promoting...</span>
      </button>
    </div>
  </div>
</div>

<!-- Logs Dialog -->
<div class="modal-overlay" *ngIf="logsDialog.isOpen" (click)="closeLogsDialog()">
  <div class="modal-container logs-modal" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>
        <i class="fas fa-file-alt"></i>
        Deployment Logs
      </h2>
      <div class="logs-controls">
        <button
          class="btn btn-outline btn-sm"
          (click)="toggleAutoScroll()"
          [class.active]="logsDialog.autoScroll">
          <i class="fas fa-arrow-down"></i>
          Auto Scroll
        </button>
        <button class="btn btn-outline btn-sm" (click)="refreshLogs()">
          <i class="fas fa-sync"></i>
          Refresh
        </button>
      </div>
      <button class="modal-close" (click)="closeLogsDialog()">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="modal-content">
      <div class="logs-container" #logsContainer>
        <div class="log-entry" *ngFor="let log of logsDialog.logs; trackBy: trackByLogId">
          <span class="log-timestamp">{{ log.timestamp | date:'HH:mm:ss.SSS' }}</span>
          <span class="log-level" [attr.data-level]="log.level">{{ log.level }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button class="btn btn-outline" (click)="closeLogsDialog()">Close</button>
      <button class="btn btn-outline" (click)="downloadLogs()">
        <i class="fas fa-download"></i>
        Download
      </button>
    </div>
  </div>
</div>
