import { Compo<PERSON>, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, Subscription, interval, combineLatest, BehaviorSubject } from 'rxjs';
import { takeUntil, debounceTime, distinctUntilChanged, switchMap, startWith, map } from 'rxjs/operators';

import {
  DeploymentService,
  Deployment,
  BatchDeployment,
  MultiAppDeployment,
  DeploymentStatistics,
  DeploymentMatrix,
  PromotionHistory,
  DeploymentAuditLog,
  DeploymentTemplate,
  CreateDeploymentRequest,
  CreateBatchDeploymentRequest,
  CreateMultiAppDeploymentRequest,
  PromotionRequest,
  DeploymentFilter,
  DeploymentStrategy,
  BatchDeploymentStatus,
  PromotionStatus,
  DeploymentEvent,
  DeploymentLog
} from '../../services/deployment.service';

import { ApplicationService } from '../../services/application.service';
import { EnvironmentService, EnvironmentConfig } from '../../services/environment.service';
import { WorkflowService } from '../../services/workflow.service';
import { ProjectService } from '../../services/project.service';
import { RealtimeLoggingService } from '../../services/realtime-logging.service';

// Import models and enums
import { Application, DeploymentStatus } from '../../models/application.model';
import { Project } from '../../models/project.model';
import { WorkflowDefinition } from '../../models/workflow.model';

interface DeploymentWizardState {
  currentStep: number;
  totalSteps: number;
  selectedApplications: Application[];
  selectedEnvironment: EnvironmentConfig | null;
  selectedWorkflow: WorkflowDefinition | null;
  deploymentType: 'single' | 'batch' | 'multi-app';
  deploymentStrategy: DeploymentStrategy;
  configuration: any;
  isValid: boolean;
  isDeploying: boolean;
}

interface FilterState {
  search: string;
  status: DeploymentStatus[];
  environmentId: string;
  applicationId: string;
  dateRange: string;
  showAdvanced: boolean;
}

interface PromotionDialogState {
  isOpen: boolean;
  isPromoting: boolean;
  sourceDeployment: Deployment | null;
  availableEnvironments: EnvironmentConfig[];
  selectedEnvironment: EnvironmentConfig | null;
  promotionForm: FormGroup | null;
}

interface StatisticsViewState {
  selectedPeriod: string;
  selectedView: 'overview' | 'trends' | 'matrix' | 'audit';
  refreshInterval: number;
}

@Component({
  selector: 'app-deployment-page',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './deployment-page.component.html',
  styleUrls: ['./deployment-page.component.css']
})
export class DeploymentPageComponent implements OnInit, OnDestroy {
  @ViewChild('logContainer', { static: false }) logContainer!: ElementRef;

  private destroy$ = new Subject<void>();
  private logSubscription: Subscription | null = null;
  private statusSubscription: Subscription | null = null;

  // Core Data
  deployments: Deployment[] = [];
  batchDeployments: BatchDeployment[] = [];
  multiAppDeployments: MultiAppDeployment[] = [];
  filteredDeployments: Deployment[] = [];
  pagedDeployments: Deployment[] = [];
  statistics: DeploymentStatistics | null = null;
  deploymentMatrix: DeploymentMatrix | null = null;
  promotionHistory: PromotionHistory[] = [];
  auditLogs: DeploymentAuditLog[] = [];

  // Reference Data
  applications: Application[] = [];
  environments: EnvironmentConfig[] = [];
  workflows: WorkflowDefinition[] = [];
  projects: Project[] = [];

  // State Management
  isLoading = false;
  error: string | null = null;
  searchQuery = '';
  selectedFilters: FilterState = {
    search: '',
    status: [],
    environmentId: '',
    applicationId: '',
    dateRange: '',
    showAdvanced: false
  };

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalDeployments = 0;

  // Sorting
  sortField = 'createdAt';
  sortDirection: 'asc' | 'desc' = 'desc';

  // Forms
  deploymentForm: FormGroup;
  filterForm: FormGroup;
  batchDeploymentForm: FormGroup;
  multiAppDeploymentForm: FormGroup;

  // Dialog States
  deploymentWizard = {
    isOpen: false,
    currentStep: 1,
    totalSteps: 4
  };

  wizardState: DeploymentWizardState = {
    currentStep: 1,
    totalSteps: 4,
    selectedApplications: [],
    selectedEnvironment: null,
    selectedWorkflow: null,
    deploymentType: 'single',
    deploymentStrategy: DeploymentStrategy.ROLLING,
    configuration: {},
    isValid: false,
    isDeploying: false
  };

  detailsDialog = {
    isOpen: false,
    deployment: null as Deployment | null,
    activeTab: 'overview'
  };

  logsDialog = {
    isOpen: false,
    deployment: null as Deployment | null,
    logs: [] as string[],
    autoScroll: true
  };

  promotionDialog: PromotionDialogState = {
    isOpen: false,
    isPromoting: false,
    sourceDeployment: null,
    availableEnvironments: [],
    selectedEnvironment: null,
    promotionForm: null
  };

  // View States
  showDeploymentWizard = false;
  currentStep = 1;
  statisticsView: StatisticsViewState = {
    selectedPeriod: '7d',
    selectedView: 'overview',
    refreshInterval: 30000
  };

  constructor(
    private deploymentService: DeploymentService,
    private applicationService: ApplicationService,
    private environmentService: EnvironmentService,
    private workflowService: WorkflowService,
    private projectService: ProjectService,
    private realtimeLoggingService: RealtimeLoggingService,
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.deploymentForm = this.fb.group({
      applicationId: ['', Validators.required],
      environmentId: ['', Validators.required],
      workflowId: [''],
      strategy: [DeploymentStrategy.ROLLING],
      description: [''],
      parameters: this.fb.group({})
    });

    this.filterForm = this.fb.group({
      search: [''],
      status: [[]],
      environmentId: [''],
      applicationId: [''],
      dateRange: [''],
      showAdvanced: [false]
    });

    this.batchDeploymentForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      strategy: [DeploymentStrategy.ROLLING],
      deployments: this.fb.array([])
    });

    this.multiAppDeploymentForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      strategy: [DeploymentStrategy.ROLLING],
      environmentId: ['', Validators.required],
      applications: this.fb.array([])
    });
  }

  ngOnInit(): void {
    this.initializeData();
    this.setupRealtimeUpdates();
    this.handleRouteParams();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    
    if (this.logSubscription) {
      this.logSubscription.unsubscribe();
    }
    
    if (this.statusSubscription) {
      this.statusSubscription.unsubscribe();
    }
  }

  // ========== Initialization ==========
  private initializeData(): void {
    this.isLoading = true;
    
    // Load all required data
    combineLatest([
      this.deploymentService.getDeployments(),
      this.applicationService.getApplications(),
      this.environmentService.getEnvironments().pipe(map((response: { environments: EnvironmentConfig[], total: number }) => response.environments)),
      this.workflowService.getWorkflows(),
      this.projectService.getProjects(),
      this.deploymentService.getDeploymentStatistics(),
      this.deploymentService.getDeploymentTemplates()
    ]).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: ([deployments, applications, environments, workflows, projects, statistics, templates]) => {
        this.deployments = deployments;
        this.applications = applications;
        this.environments = environments;
        this.workflows = workflows;
        this.projects = projects;
        this.statistics = statistics;
        this.applyFilters();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Failed to load deployment data:', error);
        this.error = 'Failed to load deployment data';
        this.isLoading = false;
      }
    });
  }

  private setupRealtimeUpdates(): void {
    // Setup real-time updates for deployment status
    this.statusSubscription = interval(5000).pipe(
      takeUntil(this.destroy$),
      switchMap(() => this.deploymentService.getDeployments())
    ).subscribe({
      next: (deployments) => {
        this.deployments = deployments;
        this.applyFilters();
      },
      error: (error) => {
        console.error('Failed to refresh deployments:', error);
      }
    });
  }

  private handleRouteParams(): void {
    this.route.params.pipe(
      takeUntil(this.destroy$)
    ).subscribe(params => {
      if (params['deploymentId']) {
        this.selectDeployment(params['deploymentId']);
      }
    });
  }

  private selectDeployment(deploymentId: string): void {
    const deployment = this.deployments.find(d => d.id === deploymentId);
    if (deployment) {
      this.viewDeploymentDetails(deployment);
    }
  }

  // ========== Form Creation ==========
  private createDeploymentForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      applicationId: ['', Validators.required],
      environmentId: ['', Validators.required],
      workflowId: [''],
      strategy: [DeploymentStrategy.ROLLING],
      configuration: this.fb.group({})
    });
  }

  // ========== Search and Filter ==========
  onSearchChange(query: string): void {
    this.searchQuery = query;
    this.applyFilters();
  }

  applyFilters(): void {
    let filtered = [...this.deployments];

    // Apply search filter
    if (this.searchQuery) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(deployment =>
        deployment.name.toLowerCase().includes(query) ||
        deployment.applicationName?.toLowerCase().includes(query) ||
        deployment.environmentName?.toLowerCase().includes(query)
      );
    }

    // Apply status filter
    if (this.selectedFilters.status.length > 0) {
      filtered = filtered.filter(deployment =>
        this.selectedFilters.status.includes(deployment.status)
      );
    }

    // Apply environment filter
    if (this.selectedFilters.environmentId) {
      filtered = filtered.filter(deployment =>
        deployment.environmentId === this.selectedFilters.environmentId
      );
    }

    // Apply application filter
    if (this.selectedFilters.applicationId) {
      filtered = filtered.filter(deployment =>
        deployment.applicationId === this.selectedFilters.applicationId
      );
    }

    this.filteredDeployments = filtered;
    this.totalDeployments = filtered.length;
    this.updatePagedDeployments();
  }

  private updatePagedDeployments(): void {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.pagedDeployments = this.filteredDeployments.slice(startIndex, endIndex);
  }

  // ========== Deployment Management ==========
  deployApplication(): void {
    if (this.deploymentForm.valid) {
      this.isLoading = true;
      
      const request: CreateDeploymentRequest = {
        ...this.deploymentForm.value,
        strategy: this.deploymentForm.value.strategy || DeploymentStrategy.ROLLING
      };

      this.deploymentService.createDeployment(request).pipe(
        takeUntil(this.destroy$)
      ).subscribe({
        next: (deployment) => {
          this.closeDeploymentWizard();
          this.loadDeployments();
          this.router.navigate(['/deployments', deployment.id]);
        },
        error: (error) => {
          console.error('Failed to create deployment:', error);
          this.error = 'Failed to create deployment';
          this.isLoading = false;
        }
      });
    }
  }

  private executeWizardDeployment(): void {
    if (this.wizardState.deploymentType === 'batch') {
      this.executeBatchDeployment();
    } else if (this.wizardState.deploymentType === 'multi-app') {
      this.executeMultiAppDeployment();
    } else {
      this.executeSingleDeployment();
    }
  }

  private executeSingleDeployment(): void {
    if (this.wizardState.selectedApplications.length > 0 && this.wizardState.selectedEnvironment) {
      const app = this.wizardState.selectedApplications[0];
      const request: CreateDeploymentRequest = {
        name: `${app.name} to ${this.wizardState.selectedEnvironment.name}`,
        applicationId: app.id,
        environmentId: this.wizardState.selectedEnvironment.id,
        strategy: this.wizardState.deploymentStrategy,
        workflowId: this.wizardState.selectedWorkflow?.id,
        configuration: this.wizardState.configuration
      };

      this.deploymentService.createDeployment(request).subscribe({
        next: (deployment) => {
          this.closeDeploymentWizard();
          this.loadDeployments();
        },
        error: (error) => {
          console.error('Failed to create deployment:', error);
          this.error = 'Failed to create deployment';
        }
      });
    }
  }

  private executeBatchDeployment(): void {
    const request: CreateBatchDeploymentRequest = {
      name: `Batch deployment to ${this.wizardState.selectedEnvironment!.name}`,
      description: `Deploy ${this.wizardState.selectedApplications.length} applications`,
      strategy: this.wizardState.deploymentStrategy,
      deployments: this.wizardState.selectedApplications.map(app => ({
        name: `${app.name} to ${this.wizardState.selectedEnvironment!.name}`,
        applicationId: app.id,
        environmentId: this.wizardState.selectedEnvironment!.id,
        strategy: this.wizardState.deploymentStrategy,
        workflowId: this.wizardState.selectedWorkflow?.id,
        configuration: this.wizardState.configuration
      }))
    };

    this.deploymentService.createBatchDeployment(request).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: () => {
        this.closeDeploymentWizard();
        this.loadDeployments();
      },
      error: (error) => {
        console.error('Failed to create batch deployment:', error);
        this.error = 'Failed to create batch deployment';
      }
    });
  }

  private executeMultiAppDeployment(): void {
    // Similar implementation for multi-app deployment
    // Implementation would be similar to batch deployment
  }

  // ========== Dialog Management ==========
  openDeploymentWizard(): void {
    this.deploymentWizard.isOpen = true;
    this.resetWizard();
  }

  openBatchDeploymentWizard(): void {
    this.wizardState.deploymentType = 'batch';
    this.openDeploymentWizard();
  }

  closeDeploymentWizard(): void {
    this.deploymentWizard.isOpen = false;
    this.resetWizard();
  }

  private resetWizard(): void {
    this.wizardState = {
      currentStep: 1,
      totalSteps: 4,
      selectedApplications: [],
      selectedEnvironment: null,
      selectedWorkflow: null,
      deploymentType: 'single',
      deploymentStrategy: DeploymentStrategy.ROLLING,
      configuration: {},
      isValid: false,
      isDeploying: false
    };
  }

  nextWizardStep(): void {
    if (this.wizardState.currentStep < this.wizardState.totalSteps) {
      this.wizardState.currentStep++;
      this.validateWizardStep();
    }
  }

  previousWizardStep(): void {
    if (this.wizardState.currentStep > 1) {
      this.wizardState.currentStep--;
      this.validateWizardStep();
    }
  }

  private validateWizardStep(): void {
    switch (this.wizardState.currentStep) {
      case 1:
        this.wizardState.isValid = this.wizardState.selectedApplications.length > 0;
        break;
      case 2:
        this.wizardState.isValid = this.wizardState.selectedEnvironment !== null;
        break;
      case 3:
        this.wizardState.isValid = true; // Configuration is optional
        break;
      case 4:
        this.wizardState.isValid = true; // Review step
        break;
      default:
        this.wizardState.isValid = false;
    }
  }

  viewDeploymentDetails(deployment: Deployment): void {
    this.detailsDialog.deployment = deployment;
    this.detailsDialog.isOpen = true;
    this.detailsDialog.activeTab = 'overview';
  }

  viewDeploymentLogs(deployment: Deployment): void {
    this.logsDialog.isOpen = true;
    this.logsDialog.deployment = deployment;
    this.loadDeploymentLogs(deployment.id);
  }

  promoteDeployment(deployment: Deployment): void {
    this.openPromotionDialog(deployment);
  }

  closeDetailsDialog(): void {
    this.detailsDialog.isOpen = false;
    this.detailsDialog.deployment = null;
  }

  closeLogsDialog(): void {
    this.logsDialog.isOpen = false;
    this.logsDialog.deployment = null;
    this.logsDialog.logs = [];
  }

  // ========== Pagination ==========
  previousPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updatePagedDeployments();
    }
  }

  nextPage(): void {
    if (this.currentPage < this.getTotalPages()) {
      this.currentPage++;
      this.updatePagedDeployments();
    }
  }

  getTotalPages(): number {
    return Math.ceil(this.totalDeployments / this.pageSize);
  }

  getStartIndex(): number {
    return (this.currentPage - 1) * this.pageSize;
  }

  getEndIndex(): number {
    return Math.min(this.getStartIndex() + this.pageSize, this.totalDeployments);
  }

  // ========== Wizard Methods ==========
  isApplicationSelected(app: Application): boolean {
    return this.wizardState.selectedApplications.some(selected => selected.id === app.id);
  }

  toggleApplicationSelection(app: Application): void {
    const index = this.wizardState.selectedApplications.findIndex(a => a.id === app.id);
    if (index === -1) {
      this.wizardState.selectedApplications.push(app);
    } else {
      this.wizardState.selectedApplications.splice(index, 1);
    }
    this.validateWizardStep();
  }

  selectEnvironment(env: EnvironmentConfig): void {
    this.wizardState.selectedEnvironment = env;
    this.validateWizardStep();
  }

  getSelectedWorkflowName(): string {
    return this.wizardState.selectedWorkflow?.name || 'Default';
  }

  deployApplications(): void {
    this.executeWizardDeployment();
  }

  // ========== Log Management ==========
  toggleAutoScroll(): void {
    this.logsDialog.autoScroll = !this.logsDialog.autoScroll;
  }

  clearLogs(): void {
    this.logsDialog.logs = [];
  }

  trackByLogId(index: number, log: any): string {
    return `${index}-${log}`;
  }

  downloadLogs(): void {
    const logText = this.logsDialog.logs.join('\n');
    this.downloadText(logText, 'deployment-logs.txt');
  }

  // ========== Utility Methods ==========
  private convertToCSV(data: any[]): string {
    if (data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row =>
        headers.map(header => `"${row[header] || ''}"`).join(',')
      )
    ].join('\n');
    
    return csvContent;
  }

  private downloadCSV(csv: string, filename: string): void {
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  private downloadText(text: string, filename: string): void {
    const blob = new Blob([text], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  private loadPage(): void {
    this.updatePagedDeployments();
  }

  private loadDeploymentLogs(deploymentId: string): void {
    this.deploymentService.getDeploymentLogs(deploymentId).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (logs) => {
        this.logsDialog.logs = logs.map(log =>
          `[${log.timestamp}] ${log.level}: ${log.message}`
        );
      },
      error: (error) => {
        console.error('Failed to load logs:', error);
        this.logsDialog.logs = ['Failed to load logs'];
      }
    });
  }

  // ========== Sorting ==========
  sortBy(field: string): void {
    if (this.sortField === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortField = field;
      this.sortDirection = 'asc';
    }
    this.applyFilters();
  }

  // ========== Helper Methods ==========
  getStatusIcon(status: DeploymentStatus): string {
    switch (status) {
      case DeploymentStatus.PENDING:
        return 'fas fa-clock text-warning';
      case 'RUNNING' as DeploymentStatus:
        return 'fas fa-spinner fa-spin text-primary';
      case 'SUCCESS' as DeploymentStatus:
        return 'fas fa-check-circle text-success';
      case 'FAILED' as DeploymentStatus:
        return 'fas fa-times-circle text-danger';
      case 'CANCELLED' as DeploymentStatus:
        return 'fas fa-ban text-secondary';
      default:
        return 'fas fa-question-circle text-muted';
    }
  }

  getEnvironmentStatusIcon(status: string): string {
    switch (status) {
      case 'active':
        return 'fas fa-check-circle text-success';
      case 'inactive':
        return 'fas fa-times-circle text-danger';
      case 'maintenance':
        return 'fas fa-wrench text-warning';
      default:
        return 'fas fa-question-circle text-muted';
    }
  }

  getDuration(deployment: Deployment): string {
    if (!deployment.startTime) return 'N/A';
    
    const start = new Date(deployment.startTime);
    const end = deployment.endTime ? new Date(deployment.endTime) : new Date();
    const diffMs = end.getTime() - start.getTime();
    
    return this.formatDuration(diffMs);
  }

  private formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  getMetadataEntries(metadata: any): Array<{key: string, value: any}> {
    if (!metadata) return [];
    return Object.entries(metadata).map(([key, value]) => ({ key, value }));
  }

  // ========== Capability Checks ==========
  canPromote(deployment: Deployment): boolean {
    return deployment.status === 'SUCCESS' as DeploymentStatus;
  }

  canRetry(deployment: Deployment): boolean {
    return deployment.status === 'FAILED' as DeploymentStatus;
  }

  canCancel(deployment: Deployment): boolean {
    return deployment.status === 'RUNNING' as DeploymentStatus || 
           deployment.status === DeploymentStatus.PENDING;
  }

  // ========== Form Methods ==========
  selectApplication(app: Application): void {
    this.deploymentForm.patchValue({ applicationId: app.id });
  }

  selectEnvironmentForForm(env: EnvironmentConfig): void {
    this.deploymentForm.patchValue({ environmentId: env.id });
  }

  getSelectedApplication(): Application | null {
    const appId = this.deploymentForm.get('applicationId')?.value;
    return this.applications.find(app => app.id === appId) || null;
  }

  getSelectedEnvironment(): EnvironmentConfig | null {
    const envId = this.deploymentForm.get('environmentId')?.value;
    return this.environments.find(env => env.id === envId) || null;
  }

  // ========== Step Navigation ==========
  nextStep(): void {
    if (this.currentStep < 4) {
      this.currentStep++;
    }
  }

  previousStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  // ========== Promotion Dialog ==========
  openPromotionDialog(deployment: Deployment): void {
    this.promotionDialog.isOpen = true;
    this.promotionDialog.sourceDeployment = deployment;
    this.promotionDialog.availableEnvironments = this.environments.filter(env =>
      env.id !== deployment.environmentId
    );
    this.promotionDialog.promotionForm = this.fb.group({
      targetEnvironmentId: ['', Validators.required],
      description: [''],
      runTests: [true]
    });
  }

  closePromotionDialog(): void {
    this.promotionDialog.isOpen = false;
    this.promotionDialog.sourceDeployment = null;
    this.promotionDialog.selectedEnvironment = null;
    this.promotionDialog.promotionForm = null;
  }

  getPromotionTargets(): EnvironmentConfig[] {
    if (!this.promotionDialog.sourceDeployment) return [];
    return this.environments.filter(env => 
      env.id !== this.promotionDialog.sourceDeployment!.environmentId
    );
  }

  startPromotion(): void {
    if (this.promotionDialog.promotionForm?.valid && this.promotionDialog.sourceDeployment) {
      this.promotionDialog.isPromoting = true;
      
      const formValue = this.promotionDialog.promotionForm.value;
      const request: PromotionRequest = {
        applicationId: this.promotionDialog.sourceDeployment.applicationId,
        fromEnvironmentId: this.promotionDialog.sourceDeployment.environmentId,
        toEnvironmentId: formValue.targetEnvironmentId,
        description: formValue.description || '',
        runTests: formValue.runTests || false
      };

      this.deploymentService.promoteDeployment(request).subscribe({
        next: () => {
          this.closePromotionDialog();
          this.loadDeployments();
        },
        error: (error) => {
          console.error('Failed to start promotion:', error);
          this.error = 'Failed to start promotion';
          this.promotionDialog.isPromoting = false;
        }
      });
    }
  }

  // ========== Event Handlers ==========
  onFilterChange(): void {
    this.applyFilters();
  }

  onSearchInput(event: any): void {
    this.searchQuery = event.target.value;
    this.onSearchChange(this.searchQuery);
  }

  refreshDeployments(): void {
    this.loadDeployments();
  }

  exportDeployments(): void {
    const csvData = this.deployments.map(d => ({
      name: d.name,
      application: d.applicationName,
      environment: d.environmentName,
      status: d.status,
      startTime: d.startTime,
      endTime: d.endTime,
      duration: this.getDuration(d)
    }));
    
    const csv = this.convertToCSV(csvData);
    this.downloadCSV(csv, 'deployments.csv');
  }

  trackByDeploymentId(index: number, deployment: Deployment): string {
    return deployment.id;
  }

  // ========== Private Methods ==========
  private loadDeployments(): void {
    this.deploymentService.getDeployments().subscribe({
      next: (deployments) => {
        this.deployments = deployments;
        this.applyFilters();
      },
      error: (error) => {
        console.error('Failed to load deployments:', error);
        this.error = 'Failed to load deployments';
      }
    });
  }
}
