.deployment-page {
  padding: 24px;
  background-color: #f8fafc;
  min-height: 100vh;
}

/* Header Section */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content h1.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-content .page-title i {
  color: #3b82f6;
}

.header-content .page-description {
  margin: 8px 0 0 0;
  color: #64748b;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border-radius: 8px;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  font-size: 14px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-secondary {
  background: #64748b;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #475569;
}

.btn-outline {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-outline:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-success {
  background: #10b981;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #059669;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 13px;
}

.btn-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
}

.btn-close:hover {
  color: #374151;
}

/* Statistics Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.stat-icon.success {
  background: #dcfce7;
  color: #16a34a;
}

.stat-icon.danger {
  background: #fee2e2;
  color: #dc2626;
}

.stat-icon.warning {
  background: #fef3c7;
  color: #d97706;
}

.stat-icon.info {
  background: #dbeafe;
  color: #2563eb;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
  color: #1e293b;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
  margin-top: 4px;
}

/* Filters Section */
.filters-section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  display: flex;
  gap: 20px;
  align-items: flex-end;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 160px;
}

.filter-group label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.filter-group select,
.filter-group input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Deployments Table */
.deployments-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.table-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.table-wrapper {
  overflow-x: auto;
}

.deployments-table {
  width: 100%;
  border-collapse: collapse;
}

.deployments-table th {
  background: #f8fafc;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  white-space: nowrap;
}

.deployments-table td {
  padding: 16px;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
}

.deployments-table tr:hover {
  background: #f8fafc;
}

.deployment-name strong {
  color: #1e293b;
  font-weight: 600;
}

.deployment-name small {
  display: block;
  color: #64748b;
  font-size: 12px;
  margin-top: 2px;
}

/* Badges */
.environment-badge {
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.environment-badge[data-env="production"] {
  background: #fee2e2;
  color: #dc2626;
}

.environment-badge[data-env="staging"] {
  background: #fef3c7;
  color: #d97706;
}

.environment-badge[data-env="development"] {
  background: #dcfce7;
  color: #16a34a;
}

.environment-badge[data-env="test"] {
  background: #dbeafe;
  color: #2563eb;
}

.version-badge {
  background: #f1f5f9;
  color: #475569;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-family: 'Monaco', 'Consolas', monospace;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge[data-status="successful"] {
  background: #dcfce7;
  color: #16a34a;
}

.status-badge[data-status="failed"] {
  background: #fee2e2;
  color: #dc2626;
}

.status-badge[data-status="pending"] {
  background: #fef3c7;
  color: #d97706;
}

.status-badge[data-status="running"] {
  background: #dbeafe;
  color: #2563eb;
}

.status-badge[data-status="cancelled"] {
  background: #f3f4f6;
  color: #6b7280;
}

.strategy-badge {
  background: #f3f4f6;
  color: #374151;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: capitalize;
}

.running-duration {
  color: #2563eb;
  font-weight: 500;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 4px;
}

.action-buttons .btn {
  min-width: 32px;
  height: 32px;
  padding: 0;
  justify-content: center;
}

/* Loading and Empty States */
.loading-state,
.empty-state {
  padding: 64px 24px;
  text-align: center;
  color: #6b7280;
}

.loading-spinner {
  font-size: 24px;
  margin-bottom: 16px;
}

.empty-icon {
  font-size: 48px;
  color: #d1d5db;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: #374151;
}

.empty-state p {
  margin: 0 0 24px 0;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 24px;
  border-top: 1px solid #e5e7eb;
}

.page-info {
  color: #6b7280;
  font-size: 14px;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-height: 90vh;
  overflow-y: auto;
  width: 100%;
  max-width: 600px;
}

.modal-content.deployment-wizard {
  max-width: 800px;
}

.modal-content.details-dialog {
  max-width: 900px;
}

.modal-content.logs-dialog {
  max-width: 1000px;
  height: 80vh;
  max-height: none;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-body {
  padding: 24px;
  flex: 1;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #e5e7eb;
}

/* Deployment Wizard */
.wizard-steps {
  display: flex;
  justify-content: center;
  margin: 24px 0;
  padding: 0 24px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 20px;
  left: 50%;
  width: 100%;
  height: 2px;
  background: #e5e7eb;
  z-index: 1;
}

.step.completed:not(:last-child)::after {
  background: #10b981;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: white;
  border: 2px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #6b7280;
  position: relative;
  z-index: 2;
  margin-bottom: 8px;
}

.step.active .step-number {
  border-color: #3b82f6;
  color: #3b82f6;
}

.step.completed .step-number {
  background: #10b981;
  border-color: #10b981;
  color: white;
}

.step-label {
  font-size: 14px;
  color: #6b7280;
  text-align: center;
}

.step.active .step-label {
  color: #3b82f6;
  font-weight: 500;
}

.wizard-content {
  padding: 0 24px;
  min-height: 400px;
}

.wizard-step h3 {
  margin: 0 0 24px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.wizard-footer {
  border-top: 1px solid #e5e7eb;
  padding: 24px;
}

.wizard-actions {
  display: flex;
  justify-content: space-between;
}

/* Application Selection */
.app-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.app-card {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.app-card:hover {
  border-color: #3b82f6;
}

.app-card.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.app-card .app-icon {
  font-size: 24px;
  color: #3b82f6;
  margin-bottom: 12px;
}

.app-card h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.app-card p {
  margin: 0 0 12px 0;
  color: #6b7280;
  font-size: 14px;
}

.app-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.project-name {
  color: #6b7280;
}

.version {
  background: #f1f5f9;
  color: #475569;
  padding: 2px 6px;
  border-radius: 4px;
}

.app-status {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

.app-status[data-status="active"] {
  background: #dcfce7;
  color: #16a34a;
}

/* Environment Selection */
.env-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

.env-card {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.env-card:hover {
  border-color: #3b82f6;
}

.env-card.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.env-icon {
  font-size: 24px;
  margin-bottom: 12px;
}

.env-icon[data-env="production"] {
  color: #dc2626;
}

.env-icon[data-env="staging"] {
  color: #d97706;
}

.env-icon[data-env="development"] {
  color: #16a34a;
}

.env-icon[data-env="test"] {
  color: #2563eb;
}

.env-card h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.env-card p {
  margin: 0 0 12px 0;
  color: #6b7280;
  font-size: 14px;
}

.env-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #6b7280;
}

/* Form Styles */
.deployment-config-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input[type="checkbox"] {
  width: auto;
}

/* Deployment Summary */
.deployment-summary {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.summary-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.app-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.app-list li {
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
  color: #374151;
}

.env-summary {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.env-summary strong {
  color: #1e293b;
}

.env-summary span {
  font-size: 14px;
  color: #6b7280;
}

.config-summary {
  display: grid;
  gap: 8px;
}

.config-summary div {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

/* Promotion Dialog */
.promotion-dialog .form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* Details Dialog */
.details-tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.tab-button {
  background: none;
  border: none;
  padding: 12px 16px;
  cursor: pointer;
  font-weight: 500;
  color: #6b7280;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-button:hover {
  color: #374151;
}

.tab-button.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.tab-content {
  min-height: 300px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.overview-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.overview-item label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.overview-item span {
  color: #1e293b;
  font-weight: 500;
}

.components-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.component-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.component-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.component-details {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #6b7280;
}

.events-timeline {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.event-item {
  display: flex;
  gap: 16px;
  padding: 12px;
  border: 1px solid #f1f5f9;
  border-radius: 8px;
}

.event-time {
  font-size: 12px;
  color: #6b7280;
  min-width: 100px;
}

.event-content {
  flex: 1;
}

.event-type {
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 4px;
}

.event-type[data-type="info"] {
  color: #2563eb;
}

.event-type[data-type="warning"] {
  color: #d97706;
}

.event-type[data-type="error"] {
  color: #dc2626;
}

.event-message {
  color: #374151;
  font-size: 14px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.metric-card {
  background: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
}

.metric-card h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
}

/* Logs Dialog */
.logs-dialog .modal-header {
  flex-shrink: 0;
}

.log-controls {
  display: flex;
  gap: 8px;
  margin-left: auto;
  margin-right: 16px;
}

.logs-container {
  background: #1e293b;
  color: #e2e8f0;
  padding: 16px;
  border-radius: 8px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 13px;
  line-height: 1.4;
  height: 400px;
  overflow-y: auto;
}

.log-entry {
  display: flex;
  gap: 12px;
  margin-bottom: 2px;
  padding: 2px 0;
}

.log-entry[data-level="error"] {
  background: rgba(239, 68, 68, 0.1);
  color: #fca5a5;
}

.log-entry[data-level="warning"] {
  background: rgba(245, 158, 11, 0.1);
  color: #fcd34d;
}

.log-entry[data-level="info"] {
  color: #93c5fd;
}

.log-timestamp {
  color: #64748b;
  min-width: 80px;
}

.log-level {
  min-width: 50px;
  font-weight: 500;
  text-transform: uppercase;
}

.log-level[data-level="error"] {
  color: #f87171;
}

.log-level[data-level="warning"] {
  color: #fbbf24;
}

.log-level[data-level="info"] {
  color: #60a5fa;
}

.log-message {
  flex: 1;
  word-break: break-word;
}

/* Responsive Design */
@media (max-width: 768px) {
  .deployment-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: stretch;
  }

  .header-actions .btn {
    flex: 1;
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .filters-section {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-actions {
    margin-left: 0;
    justify-content: flex-start;
  }

  .deployments-table {
    font-size: 14px;
  }

  .deployments-table th,
  .deployments-table td {
    padding: 12px 8px;
  }

  .modal-overlay {
    padding: 16px;
  }

  .modal-content {
    max-height: 95vh;
  }

  .app-grid,
  .env-grid {
    grid-template-columns: 1fr;
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .wizard-actions {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .deployment-name small {
    display: none;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-buttons .btn {
    width: 100%;
  }
}
