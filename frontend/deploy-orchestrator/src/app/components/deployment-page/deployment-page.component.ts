import {Component, On<PERSON>nit, On<PERSON><PERSON>roy, ViewChild, ElementRef} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators} from '@angular/forms';
import {Router, ActivatedRoute} from '@angular/router';
import {Subject, Subscription, interval, combineLatest, BehaviorSubject, of} from 'rxjs';
import {takeUntil, debounceTime, distinctUntilChanged, switchMap, startWith, map, catchError} from 'rxjs/operators';
import {environment} from '../../../environments/environment';
import {
    DeploymentService,
    Deployment,
    BatchDeployment,
    MultiAppDeployment,
    DeploymentStatistics,
    DeploymentMatrix,
    PromotionHistory,
    DeploymentAuditLog,
    DeploymentTemplate,
    CreateDeploymentRequest,
    CreateBatchDeploymentRequest,
    CreateMultiAppDeploymentRequest,
    PromotionRequest,
    DeploymentFilter,
    DeploymentStrategy,
    BatchDeploymentStatus,
    PromotionStatus,
    DeploymentEvent,
    DeploymentLog,
    MultiAppDeploymentConfig
} from '../../services/deployment.service';

// Import the DeploymentStatus from application.model
import {DeploymentStatus} from '../../models/application.model';

import {ApplicationService} from '../../services/application.service';
import {ProviderService, ProviderInfo} from '../../services/provider.service';
import {EnvironmentService, EnvironmentConfig, ProviderCapabilities} from '../../services/environment.service';
import {WorkflowService} from '../../services/workflow.service';
import {ProjectService} from '../../services/project.service';
import {RealtimeLoggingService} from '../../services/realtime-logging.service';

// Import models and enums
import {Application, ApplicationType} from '../../models/application.model';
import {Project} from '../../models/project.model';
import {WorkflowDefinition} from '../../models/workflow.model';
import {DeploymentPluginService} from '../../services/deployment-plugin.service';
import {DeploymentPlugin, DeployableType} from '../../models/deployable.model';

interface DeploymentWizardState {
    currentStep: number;
    totalSteps: number;
    selectedApplications: Application[];
    selectedEnvironment: EnvironmentConfig | null;
    selectedWorkflow: WorkflowDefinition | null;
    workflowParameters: { [key: string]: any }; // Added for workflow parameter values
    deploymentType: 'single' | 'multi-app'; // Removed 'batch' - same as multi-app
    deploymentMethod: 'workflow' | 'provider' | null; // Changed from 'plugin' to 'provider'
    deploymentStrategy: DeploymentStrategy | null;
    selectedPlugin: DeploymentPlugin | null;
    pluginConfig: PluginConfiguration;
    selectedProvider: ProviderInfo | null; // Added for provider selection
    providerConfig: { [key: string]: any }; // Added for provider configuration
    configuration: any;
    isValid: boolean;
    isDeploying: boolean;
}

interface PluginConfiguration {
    // Helm OpenShift Deploy
    releaseName?: string;
    namespace?: string;
    chartRepository?: string;
    chartPath?: string;
    chartVersion?: string;
    valuesFile?: string;
    customValues?: string;
    createNamespace?: boolean;
    
    // Kubernetes Deploy
    manifestsPath?: string;
    
    // Docker Deploy
    dockerImage?: string;
    containerName?: string;
    ports?: string;
    
    // Generic plugin config
    [key: string]: any;
}

interface FilterState {
    search: string;
    status: DeploymentStatus[];
    environmentId: string;
    applicationId: string;
    dateRange: string;
    showAdvanced: boolean;
}

interface PromotionDialogState {
    isOpen: boolean;
    isPromoting: boolean;
    sourceDeployment: Deployment | null;
    availableEnvironments: EnvironmentConfig[];
    selectedEnvironment: EnvironmentConfig | null;
    promotionForm: FormGroup | null;
    targetEnvironmentId?: string;
    description?: string;
    runTests?: boolean;
    deployment?: Deployment; // For backward compatibility with template
}

interface StatisticsViewState {
    selectedPeriod: string;
    selectedView: 'overview' | 'trends' | 'matrix' | 'audit';
    refreshInterval: number;
}

@Component({
    selector: 'app-deployment-page',
    standalone: true,
    imports: [CommonModule, FormsModule, ReactiveFormsModule],
    templateUrl: './deployment-page.component.html',
    styleUrls: ['./deployment-page.component.css']
})
export class DeploymentPageComponent implements OnInit, OnDestroy {
    @ViewChild('logContainer', {static: false}) logContainer!: ElementRef;

    private destroy$ = new Subject<void>();
    private logSubscription: Subscription | null = null;
    private statusSubscription: Subscription | null = null;

    // Core Data
    deployments: Deployment[] = [];
    batchDeployments: BatchDeployment[] = [];
    multiAppDeployments: MultiAppDeployment[] = [];
    filteredDeployments: Deployment[] = [];
    pagedDeployments: Deployment[] = [];
    statistics: DeploymentStatistics | null = null;
    deploymentMatrix: DeploymentMatrix | null = null;
    promotionHistory: PromotionHistory[] = [];
    auditLogs: DeploymentAuditLog[] = [];

    // Message properties
    errorMessage: string | null = null;
    successMessage: string | null = null;

    // Add pending and running properties to match template
    get pendingDeployments(): number {
        return this.deployments.filter(d => d.status === DeploymentStatus.PENDING).length;
    }

    get runningDeployments(): number {
        return this.deployments.filter(d => d.status === DeploymentStatus.DEPLOYING).length;
    }

    // Reference Data
    applications: Application[] = [];
    environments: EnvironmentConfig[] = [];
    workflows: WorkflowDefinition[] = [];
    projects: Project[] = [];
    availablePlugins: DeploymentPlugin[] = [];
    compatiblePlugins: DeploymentPlugin[] = [];
    availableProviders: ProviderInfo[] = [];
    compatibleProviders: ProviderInfo[] = [];

    // State Management
    isLoading = false;
    error: string | null = null;
    searchQuery = '';
    isCreatingDeployment = false;

    // Template-expected properties
    searchTerm = '';
    selectedStatus = '';
    selectedEnvironment = '';
    selectedApplication = '';
    viewMode: 'table' | 'cards' = 'table';
    batchMode = false;
    selectedDeployments = new Set<string>();

    // State Management
    selectedFilters: FilterState = {
        search: '',
        status: [],
        environmentId: '',
        applicationId: '',
        dateRange: '',
        showAdvanced: false
    };

    // Pagination
    currentPage = 1;
    pageSize = 10;
    totalDeployments = 0;

    // Sorting
    sortField = 'createdAt';
    sortDirection: 'asc' | 'desc' = 'desc';

    // Forms
    deploymentForm: FormGroup & {
        applicationId?: string;
        environmentId?: string;
        workflowId?: string;
        strategy?: DeploymentStrategy;
        version?: string;
        description?: string;
        rollbackOnFailure?: boolean;
        runHealthChecks?: boolean;
    };
    filterForm: FormGroup;
    batchDeploymentForm: FormGroup;
    multiAppDeploymentForm: FormGroup;

    // Dialog States
    deploymentWizard = {
        isOpen: false,
        currentStep: 1,
        totalSteps: 4
    };

    wizardState: DeploymentWizardState = {
        currentStep: 1,
        totalSteps: 6, // Increased to accommodate method selection
        selectedApplications: [],
        selectedEnvironment: null,
        selectedWorkflow: null,
        workflowParameters: {},
        deploymentType: 'single',
        deploymentMethod: null,
        deploymentStrategy: null,
        selectedPlugin: null,
        pluginConfig: {},
        selectedProvider: null, // Added for provider selection
        providerConfig: {}, // Added for provider configuration
        configuration: {},
        isValid: false,
        isDeploying: false
    };

    detailsDialog = {
        isOpen: false,
        deployment: null as Deployment | null,
        activeTab: 'overview'
    };

    logsDialog = {
        isOpen: false,
        deployment: null as Deployment | null,
        logs: [] as DeploymentLog[],
        autoScroll: true
    };

    promotionDialog: PromotionDialogState = {
        isOpen: false,
        isPromoting: false,
        sourceDeployment: null,
        availableEnvironments: [],
        selectedEnvironment: null,
        promotionForm: null
    };

    // View States
    // Removed showDeploymentWizard variable - using deploymentWizard.isOpen instead
    currentStep = 1;
    statisticsView: StatisticsViewState = {
        selectedPeriod: '7d',
        selectedView: 'overview',
        refreshInterval: 30000
    };

    constructor(
        private deploymentService: DeploymentService,
        private applicationService: ApplicationService,
        private environmentService: EnvironmentService,
        private workflowService: WorkflowService,
        private projectService: ProjectService,
        private realtimeLoggingService: RealtimeLoggingService,
        private deploymentPluginService: DeploymentPluginService,
        private providerService: ProviderService,
        private fb: FormBuilder,
        private router: Router,
        private route: ActivatedRoute
    ) {
        this.deploymentForm = this.fb.group({
            applicationId: ['', Validators.required],
            environmentId: ['', Validators.required],
            workflowId: [''],
            strategy: [DeploymentStrategy.ROLLING],
            description: [''],
            parameters: this.fb.group({})
        });

        this.filterForm = this.fb.group({
            search: [''],
            status: [[]],
            environmentId: [''],
            applicationId: [''],
            dateRange: [''],
            showAdvanced: [false]
        });

        this.batchDeploymentForm = this.fb.group({
            name: ['', [Validators.required, Validators.minLength(3)]],
            description: [''],
            strategy: [DeploymentStrategy.ROLLING],
            deployments: this.fb.array([])
        });

        this.multiAppDeploymentForm = this.fb.group({
            name: ['', [Validators.required, Validators.minLength(3)]],
            description: [''],
            strategy: [DeploymentStrategy.ROLLING],
            environmentId: ['', Validators.required],
            applications: this.fb.array([])
        });
    }

    ngOnInit(): void {
        // Subscribe to project changes first
        this.subscribeToProjectChanges();
        this.handleRouteParams();
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();

        if (this.logSubscription) {
            this.logSubscription.unsubscribe();
        }

        if (this.statusSubscription) {
            this.statusSubscription.unsubscribe();
        }
    }

    // ========== Project Subscription ==========
    private subscribeToProjectChanges(): void {
        // Subscribe to selected project changes
        this.projectService.selectedProject$
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: (project) => {
                    if (project) {
                        console.log('Selected project changed:', project.name);
                        // Load data whenever project changes
                        this.initializeData(project.id);

                        // Explicitly load workflows with proper project filtering
                        this.loadWorkflows(project.id);
                    }
                },
                error: (error) => {
                    console.error('Error in project subscription:', error);
                }
            });
    }

    // ========== Initialization ==========
    private initializeData(projectId: string): void {
        this.isLoading = true;

        // Create filter with projectId for deployments
        const deploymentFilter: DeploymentFilter = {
            projectId: projectId,
            limit: 100  // Set a reasonable limit
        };

        // Create filter for applications
        const applicationFilter = {projectId: projectId};

        // Setup real-time updates with the project ID
        this.setupRealtimeUpdates(projectId);

        // Debug application filter
        console.log('Application filter:', applicationFilter);

        // Debug the API URL for applications
        console.log('Application service base URL:', `${environment.apiUrl}/applications/`);

        // Load all required data
        combineLatest([
            this.deploymentService.getDeployments(deploymentFilter),
            this.applicationService.getApplications(applicationFilter).pipe(
                map((response: { deployables: Application[], total: number }) => response.deployables),
                catchError(error => {
                    console.error('Failed to fetch applications:', error);
                    return of([]);  // Return empty array on error
                })
            ),
            this.environmentService.getEnvironments().pipe(
                map((response: { environments: EnvironmentConfig[], total: number }) => response.environments),
                catchError(error => {
                    console.error('Failed to fetch environments:', error);
                    return of([]);
                })
            ),
            this.workflowService.getWorkflows(projectId).pipe(
                catchError(error => {
                    console.error('Failed to fetch workflows:', error);
                    return of([]);
                })
            ),
            this.projectService.getProjects().pipe(
                catchError(error => {
                    console.error('Failed to fetch projects:', error);
                    return of([]);
                })
            ),
            this.deploymentPluginService.getAvailablePlugins().pipe(
                catchError(error => {
                    console.error('Failed to fetch deployment plugins:', error);
                    return of([]);
                })
            ),
            this.providerService.getProviders().pipe(
                map((response: { providers: ProviderInfo[] }) => response.providers),
                catchError(error => {
                    console.error('Failed to fetch deployment providers:', error);
                    return of([]);
                })
            ),
            this.deploymentService.getDeploymentStatistics().pipe(
                catchError(error => {
                    console.error('Failed to fetch deployment statistics:', error);
                    return of(null);
                })
            )
            // Removed template call as it's not implemented (501 Not Implemented)
        ]).pipe(
            takeUntil(this.destroy$)
        ).subscribe({
            next: ([deployments, applications, environments, workflows, projects, plugins, providers, statistics]) => {
                this.deployments = deployments;
                this.applications = applications;
                this.environments = environments;
                this.workflows = workflows;
                this.projects = projects;
                this.availablePlugins = plugins;
                this.availableProviders = providers;
                this.statistics = statistics;
                this.applyFilters();
                this.isLoading = false;
                console.log(`Loaded ${deployments.length} deployments for project ${projectId}`);
                console.log(`Loaded ${plugins.length} deployment plugins`);
                console.log(`Loaded ${providers.length} deployment providers`);
            },
            error: (error) => {
                console.error('Failed to load deployment data:', error);
                this.error = 'Failed to load deployment data';
                this.isLoading = false;
            }
        });
    }

    private setupRealtimeUpdates(projectId: string): void {
        // Setup real-time updates for deployment status with project filter
        this.statusSubscription?.unsubscribe();

        // Create filter with projectId
        const deploymentFilter: DeploymentFilter = {
            projectId: projectId,
            limit: 100  // Set a reasonable limit
        };

        this.statusSubscription = interval(10000).pipe(  // Increased interval to reduce server load
            takeUntil(this.destroy$),
            switchMap(() => this.deploymentService.getDeployments(deploymentFilter))
        ).subscribe({
            next: (deployments) => {
                this.deployments = deployments;
                this.applyFilters();
            },
            error: (error) => {
                console.error('Failed to refresh deployments:', error);
            }
        });
    }

    private handleRouteParams(): void {
        this.route.params.pipe(
            takeUntil(this.destroy$)
        ).subscribe(params => {
            if (params['deploymentId']) {
                this.selectDeployment(params['deploymentId']);
            }
        });
    }

    private selectDeployment(deploymentId: string): void {
        const deployment = this.deployments.find(d => d.id === deploymentId);
        if (deployment) {
            this.viewDeploymentDetails(deployment);
        }
    }

    // ========== Form Creation ==========
    private createDeploymentForm(): FormGroup {
        return this.fb.group({
            name: ['', [Validators.required, Validators.minLength(3)]],
            description: [''],
            applicationId: ['', Validators.required],
            environmentId: ['', Validators.required],
            workflowId: [''],
            strategy: [DeploymentStrategy.ROLLING],
            configuration: this.fb.group({})
        });
    }

    // ========== Search and Filter ==========
    onSearchChange(query?: string): void {
        if (query !== undefined) {
            this.searchTerm = query;
            this.searchQuery = query;
        }
        this.applyFilters();
    }

    /**
     * Applies filters to the deployments
     */
    applyFilters(): void {
        // Ensure deployments is an array
        if (!Array.isArray(this.deployments)) {
            console.warn('Expected deployments to be an array but got:', typeof this.deployments);
            this.deployments = [];
        }

        let filtered = this.deployments;

        // Apply search term filter
        if (this.searchTerm) {
            const term = this.searchTerm.toLowerCase();
            filtered = filtered.filter(deployment =>
                deployment.name?.toLowerCase().includes(term) ||
                deployment.applicationName?.toLowerCase().includes(term) ||
                deployment.environmentName?.toLowerCase().includes(term) ||
                (deployment.description && deployment.description.toLowerCase().includes(term))
            );
        }

        // Apply status filter
        if (this.selectedStatus) {
            filtered = filtered.filter(deployment => deployment.status === this.selectedStatus);
        }

        // Apply environment filter
        if (this.selectedEnvironment) {
            filtered = filtered.filter(deployment => deployment.environmentId === this.selectedEnvironment);
        }

        // Apply application filter
        if (this.selectedApplication) {
            filtered = filtered.filter(deployment => deployment.applicationId === this.selectedApplication);
        }

        // Ensure filteredDeployments is always an array
        this.filteredDeployments = Array.isArray(filtered) ? filtered : [];
        this.totalDeployments = this.filteredDeployments.length;
        this.updatePagedDeployments();
    }

    // ========== View Management ==========
    setViewMode(mode: 'table' | 'cards'): void {
        this.viewMode = mode;
    }

    toggleBatchMode(): void {
        this.batchMode = !this.batchMode;
        if (!this.batchMode) {
            // Clear selections when exiting batch mode
            this.selectedDeployments.clear();
        }
    }

    toggleDeploymentSelection(deploymentId: string): void {
        if (this.selectedDeployments.has(deploymentId)) {
            this.selectedDeployments.delete(deploymentId);
        } else {
            this.selectedDeployments.add(deploymentId);
        }
    }

    // ========== Deployment Actions ==========
    openDeploymentDetails(deployment: Deployment): void {
        this.viewDeploymentDetails(deployment);
    }

    openDeploymentLogs(deployment: Deployment | null): void {
        if (!deployment) {
            console.error('No deployment provided for logs');
            return;
        }
        this.viewDeploymentLogs(deployment);
    }

    retryDeployment(deployment: Deployment): void {
        this.deploymentService.retryDeployment(deployment.id).pipe(
            takeUntil(this.destroy$)
        ).subscribe({
            next: () => {
                this.loadDeployments();
            },
            error: (error) => {
                console.error('Failed to retry deployment:', error);
                this.error = 'Failed to retry deployment';
            }
        });
    }

    cancelDeployment(deployment: Deployment): void {
        this.deploymentService.cancelDeployment(deployment.id).pipe(
            takeUntil(this.destroy$)
        ).subscribe({
            next: () => {
                this.loadDeployments();
            },
            error: (error) => {
                console.error('Failed to cancel deployment:', error);
                this.error = 'Failed to cancel deployment';
            }
        });
    }

    // ========== Batch Actions ==========
    batchCancelDeployments(): void {
        if (!confirm(`Are you sure you want to cancel ${this.selectedDeployments.size} deployments?`)) {
            return;
        }

        const cancellations = Array.from(this.selectedDeployments).map(id =>
            this.deploymentService.cancelDeployment(id).toPromise()
        );

        Promise.all(cancellations)
            .then(() => {
                this.selectedDeployments.clear();
                this.loadDeployments();
            })
            .catch(error => {
                console.error('Failed to cancel deployments:', error);
                this.error = 'Failed to cancel selected deployments';
            });
    }

    batchRetryDeployments(): void {
        if (!confirm(`Are you sure you want to retry ${this.selectedDeployments.size} deployments?`)) {
            return;
        }

        const retries = Array.from(this.selectedDeployments).map(id =>
            this.deploymentService.retryDeployment(id).toPromise()
        );

        Promise.all(retries)
            .then(() => {
                this.selectedDeployments.clear();
                this.loadDeployments();
            })
            .catch(error => {
                console.error('Failed to retry deployments:', error);
                this.error = 'Failed to retry selected deployments';
            });
    }

    // ========== Dialog Management ==========
    openDeploymentWizard(): void {
        console.log('Opening deployment wizard');
        this.deploymentWizard.isOpen = true;
        this.deploymentWizard.currentStep = 1;
        this.resetWizard();

        // Make sure the form is reset and valid for step 1
        this.deploymentForm = this.createDeploymentForm() as any;
        this.currentStep = 1;
        this.validateWizardStep();

        // For debugging
        console.log('Wizard state:', {
            isOpen: this.deploymentWizard.isOpen,
            currentStep: this.wizardState.currentStep,
            isValid: this.wizardState.isValid
        });
    }

    openBatchDeploymentWizard(): void {
        this.wizardState.deploymentType = 'multi-app';
        this.openDeploymentWizard();
    }

    closeDeploymentWizard(): void {
        this.deploymentWizard.isOpen = false;
        this.resetWizard();
    }

    private resetWizard(): void {
        this.wizardState = {
            currentStep: 1,
            totalSteps: 6,
            selectedApplications: [],
            selectedEnvironment: null,
            selectedWorkflow: null,
            workflowParameters: {},
            deploymentType: 'single',
            deploymentMethod: null,
            deploymentStrategy: null,
            selectedPlugin: null,
            pluginConfig: {},
            selectedProvider: null, // Added for provider selection
            providerConfig: {}, // Added for provider configuration
            configuration: {},
            isValid: false,
            isDeploying: false
        };
    }

    nextWizardStep(): void {
        if (this.wizardState.currentStep < this.wizardState.totalSteps) {
            this.wizardState.currentStep++;
            this.validateWizardStep();
        }
    }

    previousWizardStep(): void {
        if (this.wizardState.currentStep > 1) {
            this.wizardState.currentStep--;
            this.validateWizardStep();
        }
    }

    viewDeploymentDetails(deployment: Deployment): void {
        this.detailsDialog.deployment = deployment;
        this.detailsDialog.isOpen = true;
        this.detailsDialog.activeTab = 'overview';
    }

    viewDeploymentLogs(deployment: Deployment): void {
        this.logsDialog.isOpen = true;
        this.logsDialog.deployment = deployment;
        this.loadDeploymentLogs(deployment.id);
    }

    promoteDeployment(deployment: Deployment): void {
        this.openPromotionDialog(deployment);
    }

    closeDetailsDialog(): void {
        this.detailsDialog.isOpen = false;
        this.detailsDialog.deployment = null;
    }

    closeLogsDialog(): void {
        this.logsDialog.isOpen = false;
        this.logsDialog.deployment = null;
        this.logsDialog.logs = [];
    }

    // ========== Pagination ==========
    previousPage(): void {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.updatePagedDeployments();
        }
    }

    nextPage(): void {
        if (this.currentPage < this.getTotalPages()) {
            this.currentPage++;
            this.updatePagedDeployments();
        }
    }

    getTotalPages(): number {
        return Math.ceil(this.totalDeployments / this.pageSize);
    }

    getStartIndex(): number {
        return (this.currentPage - 1) * this.pageSize;
    }

    getEndIndex(): number {
        return Math.min(this.getStartIndex() + this.pageSize, this.totalDeployments);
    }

    // ========== Wizard Methods ==========
    isApplicationSelected(app: Application): boolean {
        return this.wizardState.selectedApplications.some(selected => selected.id === app.id);
    }

    getSelectedWorkflowName(): string {
        return this.wizardState.selectedWorkflow?.name || 'Default';
    }

    // This is called from the template to execute the wizard deployment
    deployApplications(): void {
        this.executeWizardDeployment();
    }

    // Helper method to load workflows specifically
    loadWorkflows(projectId: string): void {
        if (!projectId) {
            console.error('Cannot load workflows: No project ID provided');
            return;
        }

        this.workflowService.getWorkflows(projectId).pipe(
            takeUntil(this.destroy$)
        ).subscribe({
            next: (workflows) => {
                console.log(`Loaded ${workflows.length} workflows for project ${projectId}`);
                this.workflows = workflows;
            },
            error: (error) => {
                console.error('Failed to load workflows:', error);
                this.error = 'Failed to load workflows';
            }
        });
    }

    // ========== Log Management ==========
    toggleAutoScroll(): void {
        this.logsDialog.autoScroll = !this.logsDialog.autoScroll;
    }

    clearLogs(): void {
        this.logsDialog.logs = [];
    }

    refreshLogs(): void {
        if (this.logsDialog.deployment) {
            this.loadDeploymentLogs(this.logsDialog.deployment.id);
        }
    }

    downloadLogs(): void {
        if (this.logsDialog.logs.length > 0 && this.logsDialog.deployment) {
            const logsText = this.logsDialog.logs.map(log =>
                `${log.timestamp} [${log.level}] ${log.message}`
            ).join('\n');

            const blob = new Blob([logsText], {type: 'text/plain'});
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `deployment-${this.logsDialog.deployment.id}-logs.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }
    }

    trackByLogId(index: number, log: DeploymentLog): any {
        return log.id || index;
    }

    private loadDeploymentLogs(deploymentId: string): void {
        this.deploymentService.getDeploymentLogs(deploymentId).pipe(
            takeUntil(this.destroy$)
        ).subscribe({
            next: (logs) => {
                this.logsDialog.logs = logs.map((log, index) =>
                    typeof log === 'string' ? {
                        id: `log-${index}`,
                        deploymentId: deploymentId,
                        message: log,
                        timestamp: new Date().toISOString(),
                        level: 'INFO',
                        source: 'deployment'
                    } : log
                );
            },
            error: (error: any) => {
                console.error('Failed to load deployment logs:', error);
                this.error = 'Failed to load deployment logs';
            }
        });
    }

    /**
     * Loads deployments from the service
     */
    private loadDeployments(): void {
        this.isLoading = true;
        this.deploymentService.getDeployments().subscribe({
            next: (deployments) => {
                this.deployments = deployments;
                this.applyFilters();
                this.isLoading = false;
            },
            error: (error) => {
                console.error('Error loading deployments', error);
                this.errorMessage = 'Failed to load deployments. Please try again.';
                this.isLoading = false;
            }
        });
    }

    /**
     * Updates the paged deployments based on current page and page size
     */
    private updatePagedDeployments(): void {
        // Ensure filteredDeployments is an array before calling slice
        if (!Array.isArray(this.filteredDeployments)) {
            console.warn('Expected filteredDeployments to be an array but got:', typeof this.filteredDeployments);

            // Convert to array if it's an object with deployments property
            if (this.filteredDeployments && typeof this.filteredDeployments === 'object' &&
                this.filteredDeployments['deployments'] &&
                Array.isArray(this.filteredDeployments['deployments'])) {
                this.filteredDeployments = this.filteredDeployments['deployments'];
            } else {
                // Last resort: convert to empty array if it's not usable
                this.filteredDeployments = [];
                this.totalDeployments = 0;
            }
        }

        // Additional safeguard - verify filteredDeployments is an array
        if (!Array.isArray(this.filteredDeployments)) {
            this.filteredDeployments = [];
            this.totalDeployments = 0;
            this.pagedDeployments = [];
            return;
        }

        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = Math.min(startIndex + this.pageSize, this.filteredDeployments.length);

        try {
            this.pagedDeployments = this.filteredDeployments.slice(startIndex, endIndex);
        } catch (error) {
            console.error('Error slicing filteredDeployments:', error);
            this.pagedDeployments = [];
        }
    }

    /**
     * Executes the deployment based on the current wizard state
     */
    private executeWizardDeployment(): void {
        if (!this.wizardState.selectedEnvironment || this.wizardState.selectedApplications.length === 0) {
            this.errorMessage = 'Please select at least one application and an environment';
            return;
        }

        const environmentId = this.wizardState.selectedEnvironment.id;
        this.wizardState.isDeploying = true;

        if (this.wizardState.deploymentType === 'multi-app') {
            // Get the first project ID from selected applications
            const projectId = this.wizardState.selectedApplications[0]?.projectId || this.projects[0]?.id || '';

            // Handle multi-app deployment
            const config: MultiAppDeploymentConfig = {
                applications: this.wizardState.selectedApplications.map(app => ({
                    applicationId: app.id,
                    version: app.version || '1.0.0',
                    strategy: this.wizardState.deploymentStrategy || DeploymentStrategy.ROLLING
                })),
                coordination: {
                    mode: 'sequential',
                    rollbackOnFailure: this.wizardState.configuration.rollbackOnFailure || false
                }
            };

            const request: CreateMultiAppDeploymentRequest = {
                name: `Multi-App Deployment ${new Date().toISOString().slice(0, 10)}`,
                description: 'Created from deployment wizard',
                projectId: projectId,
                environmentId: environmentId,
                config: config,
                applications: this.wizardState.selectedApplications.map(app => ({
                    applicationId: app.id,
                    version: app.version || '1.0.0',
                    strategy: this.wizardState.deploymentStrategy || undefined
                }))
            };

            this.isLoading = true;
            this.deploymentService.createMultiAppDeployment(request).subscribe({
                next: () => {
                    this.closeDeploymentWizard();
                    this.loadDeployments();
                    this.successMessage = 'Multi-app deployment created successfully';
                    this.isLoading = false;
                    this.wizardState.isDeploying = false;
                },
                error: (error: any) => {
                    console.error('Error creating multi-app deployment', error);
                    this.errorMessage = 'Failed to create multi-app deployment';
                    this.isLoading = false;
                    this.wizardState.isDeploying = false;
                }
            });
        } else {
            // Handle single application deployment
            const app = this.wizardState.selectedApplications[0];

            const request: CreateDeploymentRequest = {
                name: `${app.name} Deployment`,
                applicationId: app.id,
                environmentId: environmentId,
                workflowId: this.wizardState.selectedWorkflow?.id || '', // Provide default empty string for undefined
                strategy: this.wizardState.deploymentStrategy || DeploymentStrategy.ROLLING,
                configuration: this.wizardState.configuration || {}
            };

            this.isLoading = true;
            this.deploymentService.createDeployment(request).subscribe({
                next: () => {
                    this.closeDeploymentWizard();
                    this.loadDeployments();
                    this.successMessage = 'Deployment created successfully';
                    this.isLoading = false;
                    this.wizardState.isDeploying = false;
                },
                error: (error: any) => {
                    console.error('Error creating deployment', error);
                    this.errorMessage = 'Failed to create deployment';
                    this.isLoading = false;
                    this.wizardState.isDeploying = false;
                }
            });
        }
    }

    /**
     * Opens the promotion dialog for the given deployment
     */
    openPromotionDialog(deployment: Deployment | null): void {
        if (!deployment) {
            console.error('No deployment provided for promotion');
            return;
        }
        this.promotionDialog.isOpen = true;
        this.promotionDialog.sourceDeployment = deployment;
        this.promotionDialog.isPromoting = false;

        // Get available environments for promotion (environments other than the current one)
        const currentEnvId = deployment.environmentId;
        this.promotionDialog.availableEnvironments = this.environments.filter(env =>
            env.id !== currentEnvId
        );

        // Create the promotion form
        this.promotionDialog.promotionForm = this.fb.group({
            targetEnvironmentId: ['', Validators.required],
            keepConfiguration: [true],
            notes: [''],
            validateFirst: [true]
        });
    }

    closePromotionDialog(): void {
        this.promotionDialog.isOpen = false;
        this.promotionDialog.sourceDeployment = null;
        this.promotionDialog.selectedEnvironment = null;
    }

    executePromotion(): void {
        if (!this.promotionDialog.sourceDeployment || !this.promotionDialog.promotionForm || !this.promotionDialog.promotionForm.valid) {
            return;
        }

        this.promotionDialog.isPromoting = true;
        const formValue = this.promotionDialog.promotionForm.value;

        const sourceDeploymentId = this.promotionDialog.sourceDeployment.id;
        const targetEnvironmentId = formValue.targetEnvironmentId;

        // Create a deployment request based on the source deployment but with new environment
        const request: CreateDeploymentRequest = {
            name: `Promotion-${new Date().toISOString().slice(0, 10)}`,
            applicationId: this.promotionDialog.sourceDeployment.applicationId,
            environmentId: targetEnvironmentId,
            workflowId: this.promotionDialog.sourceDeployment.workflowId,
            strategy: this.promotionDialog.sourceDeployment.strategy,
            configuration: this.promotionDialog.sourceDeployment.configuration,
            description: `Promoted from deployment ${sourceDeploymentId}`
        };

        this.deploymentService.createDeployment(request).subscribe({
            next: () => {
                this.promotionDialog.isPromoting = false;
                this.closePromotionDialog();
                this.loadDeployments();
                this.successMessage = 'Deployment promoted successfully';
            },
            error: (error: any) => {
                console.error('Failed to promote deployment:', error);
                this.promotionDialog.isPromoting = false;
                this.errorMessage = 'Failed to promote deployment';
            }
        });
    }

    // ========== Table Selection and Sorting ==========

    /**
     * Sorts deployments by the specified field
     */
    sortBy(field: string): void {
        if (this.sortField === field) {
            // Toggle sort direction if clicking on the same field
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortDirection = 'asc';
        }

        this.filteredDeployments.sort((a, b) => {
            let valueA: any, valueB: any;

            // Handle special cases
            switch (field) {
                case 'application':
                    valueA = a.applicationName;
                    valueB = b.applicationName;
                    break;
                case 'environment':
                    valueA = a.environmentName;
                    valueB = b.environmentName;
                    break;
                case 'startedAt':
                    valueA = a.startedAt || '';
                    valueB = b.startedAt || '';
                    break;
                case 'version':
                    valueA = a.version || '';
                    valueB = b.version || '';
                    break;
                case 'status':
                    valueA = a.status;
                    valueB = b.status;
                    break;
                default:
                    valueA = (a as any)[field];
                    valueB = (b as any)[field];
            }

            // Handle null/undefined values
            if (valueA === undefined || valueA === null) valueA = '';
            if (valueB === undefined || valueB === null) valueB = '';

            // Sort based on direction
            const result = typeof valueA === 'string'
                ? valueA.localeCompare(valueB)
                : valueA - valueB;

            return this.sortDirection === 'asc' ? result : -result;
        });

        this.updatePagedDeployments();
    }

    /**
     * Tracks deployment by ID for ngFor optimization
     */
    trackByDeploymentId(index: number, deployment: Deployment): string {
        return deployment.id;
    }

    /**
     * Checks if all deployments are selected
     */
    isAllSelected(): boolean {
        return this.pagedDeployments.length > 0 &&
            this.pagedDeployments.every(d => this.selectedDeployments.has(d.id));
    }

    /**
     * Checks if some but not all deployments are selected
     */
    isSomeSelected(): boolean {
        const selectedCount = this.pagedDeployments.filter(d => this.selectedDeployments.has(d.id)).length;
        return selectedCount > 0 && selectedCount < this.pagedDeployments.length;
    }

    /**
     * Toggles selection of all deployments
     */
    toggleAllSelection(): void {
        if (this.isAllSelected()) {
            // Deselect all if all are currently selected
            this.pagedDeployments.forEach(d => this.selectedDeployments.delete(d.id));
        } else {
            // Select all if not all are currently selected
            this.pagedDeployments.forEach(d => this.selectedDeployments.add(d.id));
        }
    }

    // ========== Status and Utility Methods ==========

    /**
     * Gets the appropriate icon class for a deployment status
     */
    getStatusIcon(status: DeploymentStatus): string {
        switch (status) {
            case DeploymentStatus.PENDING:
                return 'fas fa-clock status-pending';
            case DeploymentStatus.DEPLOYING:
                return 'fas fa-spinner fa-spin status-deploying';
            case DeploymentStatus.DEPLOYED:
                return 'fas fa-check-circle status-success';
            case DeploymentStatus.FAILED:
                return 'fas fa-times-circle status-failed';
            case DeploymentStatus.ROLLING_BACK:
                return 'fas fa-undo fa-spin status-warning';
            case DeploymentStatus.ROLLED_BACK:
                return 'fas fa-undo status-warning';
            case DeploymentStatus.CANCELLED:
                return 'fas fa-ban status-cancelled';
            default:
                return 'fas fa-question-circle status-unknown';
        }
    }

    /**
     * Gets the appropriate icon class for an environment status
     */
    getEnvironmentStatusIcon(status: string): string {
        switch (status) {
            case 'healthy':
                return 'fas fa-check-circle status-success';
            case 'degraded':
                return 'fas fa-exclamation-triangle status-warning';
            case 'down':
                return 'fas fa-times-circle status-failed';
            default:
                return 'fas fa-question-circle status-unknown';
        }
    }

    /**
     * Calculates the duration of a deployment
     */
    getDuration(deployment: Deployment): string {
        if (!deployment) return '';

        const start = deployment.startedAt ? new Date(deployment.startedAt) : null;
        const end = deployment.completedAt ? new Date(deployment.completedAt) : null;

        if (!start) return 'Not started';

        const duration = end ? end.getTime() - start.getTime() : new Date().getTime() - start.getTime();

        // Format the duration
        const seconds = Math.floor(duration / 1000) % 60;
        const minutes = Math.floor(duration / (1000 * 60)) % 60;
        const hours = Math.floor(duration / (1000 * 60 * 60));

        if (hours > 0) {
            return `${hours}h ${minutes}m ${seconds}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds}s`;
        } else {
            return `${seconds}s`;
        }
    }

    /**
     * Checks if a deployment can be promoted
     */
    canPromote(deployment: Deployment | null): boolean {
        if (!deployment) return false;
        return deployment.status === DeploymentStatus.DEPLOYED;
    }

    /**
     * Checks if a deployment can be retried
     */
    canRetry(deployment: Deployment | null): boolean {
        if (!deployment) return false;
        return deployment.status === DeploymentStatus.FAILED || deployment.status === DeploymentStatus.ROLLED_BACK;
    }

    /**
     * Checks if a deployment can be cancelled
     */
    canCancel(deployment: Deployment | null): boolean {
        if (!deployment) return false;
        return deployment.status === DeploymentStatus.PENDING || deployment.status === DeploymentStatus.DEPLOYING;
    }

    // ========== Wizard Navigation Methods ==========

    /**
     * Checks if proceeding to the next step is allowed
     */
    canProceedToNextStep(): boolean {
        // Add validation logic based on current step
        switch (this.currentStep) {
            case 1: // Application selection
                return this.deploymentForm.get('applicationId')?.value !== '';
            case 2: // Environment selection
                return this.deploymentForm.get('environmentId')?.value !== '';
            case 3: // Configuration
                return true; // Configuration is optional
            default:
                return true;
        }
    }

    /**
     * Creates a new deployment
     */
    createDeployment(): void {
        if (!this.deploymentForm.valid) {
            return;
        }

        const formValue = this.deploymentForm.value;
        const request: CreateDeploymentRequest = {
            name: `Deployment - ${new Date().toISOString().slice(0, 10)}`,
            applicationId: formValue.applicationId,
            environmentId: formValue.environmentId,
            workflowId: formValue.workflowId || '',
            strategy: formValue.strategy,
            description: formValue.description || '',
            configuration: formValue.parameters || {}
        };

        this.isLoading = true;
        this.deploymentService.createDeployment(request).subscribe({
            next: () => {
                this.isLoading = false;
                this.closeDeploymentWizard();
                this.loadDeployments();
                this.successMessage = 'Deployment created successfully';
            },
            error: (error: any) => {
                this.isLoading = false;
                console.error('Error creating deployment:', error);
                this.errorMessage = 'Failed to create deployment';
            }
        });
    }

    // ========== Deployment Form Methods ==========

    /**
     * Gets the selected application
     */
    getSelectedApplication(): Application | undefined {
        const applicationId = this.deploymentForm.get('applicationId')?.value;
        return this.applications.find(app => app.id === applicationId);
    }

    /**
     * Gets the selected environment
     */
    getSelectedEnvironment(): EnvironmentConfig | undefined {
        const environmentId = this.deploymentForm.get('environmentId')?.value;
        return this.environments.find(env => env.id === environmentId);
    }

    /**
     * Selects an application for deployment in the form
     */
    selectApplication(app: Application): void {
        this.deploymentForm.patchValue({
            applicationId: app.id
        });
    }

    /**
     * Selects a workflow for deployment
     */
    selectWorkflow(workflow: WorkflowDefinition): void {
        this.wizardState.selectedWorkflow = workflow;
        
        // Initialize workflow parameters with default values
        this.wizardState.workflowParameters = {};
        if (workflow.parameters && workflow.parameters.length > 0) {
            workflow.parameters.forEach(param => {
                this.wizardState.workflowParameters[param.name] = param.defaultValue || '';
            });
        }
        
        this.validateWizardStep();
    }

    /**
     * Checks if a workflow is selected
     */
    isWorkflowSelected(workflow: WorkflowDefinition): boolean {
        return this.wizardState.selectedWorkflow?.id === workflow.id;
    }

    /**
     * Gets available workflows for the current project
     */
    getAvailableWorkflows(): WorkflowDefinition[] {
        return this.workflows.filter(workflow => workflow.isActive !== false);
    }

    /**
     * Updates plugin configuration when form values change
     */
    onPluginConfigChange(key: string, value: any): void {
        this.wizardState.pluginConfig[key] = value;
        this.validateWizardStep();
    }

    // ========== Deployment Method Selection Methods ==========

    /**
     * Validates the current wizard step
     */
    validateWizardStep(): void {
        this.wizardState.isValid = false;

        switch (this.wizardState.currentStep) {
            case 1: // Application selection
                this.wizardState.isValid = this.wizardState.selectedApplications.length > 0;
                break;
            case 2: // Environment selection
                this.wizardState.isValid = this.wizardState.selectedEnvironment !== null;
                break;
            case 3: // Deployment method selection
                this.wizardState.isValid = this.wizardState.deploymentMethod !== null;
                break;
            case 4: // Workflow/Strategy selection
                if (this.wizardState.deploymentMethod === 'workflow') {
                    // Check if workflow is selected
                    if (!this.wizardState.selectedWorkflow) {
                        this.wizardState.isValid = false;
                        break;
                    }
                    
                    // Check if required workflow parameters are filled
                    if (this.wizardState.selectedWorkflow.parameters && this.wizardState.selectedWorkflow.parameters.length > 0) {
                        const hasAllRequiredParams = this.wizardState.selectedWorkflow.parameters.every(param => {
                            if (!param.required) return true;
                            const value = this.wizardState.workflowParameters[param.name];
                            return value !== undefined && value !== null && value !== '';
                        });
                        this.wizardState.isValid = hasAllRequiredParams;
                    } else {
                        this.wizardState.isValid = true;
                    }
                } else {
                    this.wizardState.isValid = true; // Strategy is optional
                }
                break;
            case 5: // Plugin & Provider selection (only for provider method)
                if (this.wizardState.deploymentMethod === 'provider') {
                    // At least one must be selected: plugin OR provider
                    const hasPlugin = this.wizardState.selectedPlugin !== null;
                    const hasProvider = this.wizardState.selectedProvider !== null;
                    
                    if (hasPlugin && hasProvider) {
                        // Both selected - invalid, user must choose one
                        this.wizardState.isValid = false;
                    } else if (hasPlugin) {
                        // Plugin selected - validate plugin configuration
                        this.wizardState.isValid = this.validatePluginConfiguration();
                    } else if (hasProvider) {
                        // Provider selected - validate provider configuration
                        this.wizardState.isValid = this.validateProviderConfiguration();
                    } else {
                        // Neither selected - invalid
                        this.wizardState.isValid = false;
                    }
                } else if (this.wizardState.deploymentMethod === 'workflow') {
                    // Skip this step for workflow method - workflow already selected in step 4
                    this.wizardState.isValid = true;
                } else {
                    this.wizardState.isValid = false;
                }
                break;
            case 6: // Review
                this.wizardState.isValid = true;
                break;
            default:
                this.wizardState.isValid = false;
        }
    }

    /**
     * Validates plugin configuration
     */
    validatePluginConfiguration(): boolean {
        if (!this.wizardState.selectedPlugin) {
            return false;
        }

        const plugin = this.wizardState.selectedPlugin;
        const config = this.wizardState.pluginConfig;

        switch (plugin.name) {
            case 'helm-openshift-deploy':
                return !!(config['releaseName'] && config['chartRepository']);
            case 'kubernetes-deploy':
                return !!(config['manifestPath']);
            case 'docker-deploy':
                return !!(config['imageName'] && config['containerName']);
            default:
                return true; // For manual/no plugin
        }
    }

    /**
     * Selects deployment method (workflow or provider)
     */
    selectDeploymentMethod(method: 'workflow' | 'provider'): void {
        this.wizardState.deploymentMethod = method;
        
        // Load compatible plugins and providers when provider method is selected
        if (method === 'provider') {
            this.loadCompatibleDeploymentOptions();
        }
        
        this.validateWizardStep();
    }

    /**
     * Selects a deployment plugin
     */
    selectPlugin(plugin: DeploymentPlugin | null): void {
        this.wizardState.selectedPlugin = plugin;
        this.wizardState.pluginConfig = {};
        this.validateWizardStep();
    }

    /**
     * Checks if a plugin is selected
     */
    isPluginSelected(plugin: DeploymentPlugin): boolean {
        return this.wizardState.selectedPlugin?.id === plugin.id;
    }

    // ========== Provider Selection Methods ==========

    selectProvider(provider: ProviderInfo | null): void {
        this.wizardState.selectedProvider = provider;
        this.wizardState.providerConfig = {};
        
        // Initialize provider configuration with defaults
        if (provider) {
            this.initializeProviderConfiguration(provider);
        }
        
        this.validateWizardStep();
    }

    /**
     * Checks if a provider is selected
     */
    isProviderSelected(provider: ProviderInfo): boolean {
        return this.wizardState.selectedProvider?.type === provider.type;
    }

    /**
     * Initializes provider configuration with default values
     */
    private initializeProviderConfiguration(provider: ProviderInfo): void {
        const config: { [key: string]: any } = {};
        
        // Set default values from provider config fields
        provider.configFields.forEach(field => {
            if (field.default !== undefined) {
                config[field.name] = field.default;
            }
        });
        
        this.wizardState.providerConfig = config;
    }

    /**
     * Updates provider configuration when form values change
     */
    onProviderConfigChange(key: string, value: any): void {
        if (!this.wizardState.providerConfig) {
            this.wizardState.providerConfig = {};
        }
        this.wizardState.providerConfig[key] = value;
        this.validateWizardStep();
    }

    /**
     * Validates provider configuration
     */
    validateProviderConfiguration(): boolean {
        if (!this.wizardState.selectedProvider) {
            return false;
        }

        const provider = this.wizardState.selectedProvider;
        const config = this.wizardState.providerConfig || {};

        // Check all required fields are filled
        for (const field of provider.configFields) {
            if (field.required) {
                const value = config[field.name];
                if (value === undefined || value === null || value === '') {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Loads compatible plugins and providers for the selected applications and environment
     */
    loadCompatibleDeploymentOptions(): void {
        if (this.wizardState.selectedApplications.length === 0) {
            this.compatiblePlugins = [];
            this.compatibleProviders = [];
            return;
        }

        // Get application types from selected applications and convert to deployable types
        const deployableTypes = this.wizardState.selectedApplications.map(app => 
            this.convertApplicationTypeToDeployableType(app.type)
        );
        
        // Filter plugins that support the selected application types
        this.compatiblePlugins = this.availablePlugins.filter(plugin => {
            // If plugin doesn't specify supported artifacts, assume it supports all
            if (!plugin.supportedArtifacts || plugin.supportedArtifacts.length === 0) {
                return true;
            }
            
            // Check if plugin supports any of the selected application types
            return deployableTypes.some(deployableType => 
                plugin.supportedArtifacts.includes(deployableType)
            );
        });

        // Filter providers based on environment and application compatibility
        this.compatibleProviders = this.availableProviders.filter(provider => {
            // Check if provider supports the selected environment type
            if (this.wizardState.selectedEnvironment) {
                const envType = this.wizardState.selectedEnvironment.type;
                
                // Map environment types to provider capabilities
                const requiredCapabilities = this.getRequiredCapabilitiesForEnvironment(envType);
                
                // Check if provider has required capabilities
                return requiredCapabilities.every(capability => 
                    provider.capabilities.includes(capability)
                );
            }
            
            return true; // If no environment selected, show all providers
        });

        console.log(`Found ${this.compatiblePlugins.length} compatible plugins and ${this.compatibleProviders.length} compatible providers for applications:`, deployableTypes);
    }

    /**
     * Legacy method name for backward compatibility
     */
    loadCompatiblePlugins(): void {
        this.loadCompatibleDeploymentOptions();
    }

    /**
     * Converts ApplicationType to DeployableType
     */
    private convertApplicationTypeToDeployableType(appType: ApplicationType): DeployableType {
        switch (appType) {
            case ApplicationType.WEB_APPLICATION:
                return DeployableType.WEB_APPLICATION;
            case ApplicationType.MICROSERVICE:
                return DeployableType.MICROSERVICE;
            case ApplicationType.DATABASE:
                return DeployableType.DATABASE;
            case ApplicationType.MESSAGE_QUEUE:
                return DeployableType.MESSAGE_QUEUE;
            case ApplicationType.CACHE:
                return DeployableType.CACHE;
            default:
                return DeployableType.APPLICATION; // Default fallback
        }
    }

    /**
     * Navigation methods for header buttons
     */
    navigateToApplications(): void {
        // Navigate to deployables management page (unified applications)
        this.router.navigate(['/deployables']);
    }

    navigateToPlugins(): void {
        // Navigate to plugins management page
        this.router.navigate(['/plugins']);
    }

    navigateToEnvironments(): void {
        // Navigate to environments management page
        this.router.navigate(['/environments']);
    }

    // Deployment method selection methods

    /**
     * Utility method to expose Object.keys to the template
     * Used for displaying plugin configuration in the review step
     */
    getObjectKeys(obj: any): string[] {
        return obj ? Object.keys(obj) : [];
    }

    // ========== Missing Template Methods ==========

    /**
     * Select a single application for deployment
     */
    selectSingleApplication(app: Application): void {
        this.wizardState.selectedApplications = [app];
        this.wizardState.deploymentType = 'single';
        this.loadCompatibleDeploymentOptions(); // Load compatible plugins and providers when applications change
        this.validateWizardStep();
    }

    /**
     * Toggle application selection for multi-app deployments
     */
    toggleApplicationSelection(app: Application): void {
        const index = this.wizardState.selectedApplications.findIndex(selected => selected.id === app.id);
        if (index >= 0) {
            this.wizardState.selectedApplications.splice(index, 1);
        } else {
            this.wizardState.selectedApplications.push(app);
        }
        
        // Update deployment type based on selection count
        if (this.wizardState.selectedApplications.length > 1) {
            this.wizardState.deploymentType = 'multi-app';
        } else if (this.wizardState.selectedApplications.length === 1) {
            this.wizardState.deploymentType = 'single';
        }
        
        this.loadCompatibleDeploymentOptions(); // Load compatible plugins and providers when applications change
        this.validateWizardStep();
    }

    /**
     * Select an environment for deployment
     */
    selectEnvironment(env: EnvironmentConfig): void {
        this.wizardState.selectedEnvironment = env;
        
        // Reload compatible deployment options when environment changes
        if (this.wizardState.deploymentMethod === 'provider') {
            this.loadCompatibleDeploymentOptions();
        }
        
        this.validateWizardStep();
    }

    /**
     * Get CSS class for environment type styling
     */
    getEnvironmentColorClass(envType: string): string {
        const colorMap: { [key: string]: string } = {
            'development': 'env-dev',
            'testing': 'env-test',
            'staging': 'env-staging',
            'production': 'env-prod',
            'dev': 'env-dev',
            'test': 'env-test',
            'stage': 'env-staging',
            'prod': 'env-prod'
        };
        return colorMap[envType?.toLowerCase()] || 'env-default';
    }

    /**
     * Submit the final deployment
     */
    submitDeployment(): void {
        if (!this.wizardState.isValid || this.wizardState.isDeploying) {
            return;
        }

        this.wizardState.isDeploying = true;
        this.executeWizardDeployment();
    }

    /**
     * Get metadata entries as key-value pairs for display
     */
    getMetadataEntries(metadata: any): Array<{key: string, value: any}> {
        if (!metadata || typeof metadata !== 'object') {
            return [];
        }
        return Object.keys(metadata).map(key => ({
            key,
            value: metadata[key]
        }));
    }

    /**
     * Get available promotion target environments
     */
    getPromotionTargets(): EnvironmentConfig[] {
        if (!this.promotionDialog.sourceDeployment) {
            return [];
        }
        
        const currentEnvId = this.promotionDialog.sourceDeployment.environmentId;
        return this.environments.filter(env => env.id !== currentEnvId);
    }

    /**
     * Start the deployment promotion process
     */
    startPromotion(): void {
        if (!this.promotionDialog.selectedEnvironment || 
            !this.promotionDialog.sourceDeployment ||
            this.promotionDialog.isPromoting) {
            return;
        }

        this.promotionDialog.isPromoting = true;
        
        const promotionRequest: PromotionRequest = {
            sourceDeploymentId: this.promotionDialog.sourceDeployment.id,
            targetEnvironmentId: this.promotionDialog.selectedEnvironment.id,
            description: this.promotionDialog.description || '',
            runTests: this.promotionDialog.runTests || false
        };

        this.deploymentService.promoteDeployment(promotionRequest).pipe(
            takeUntil(this.destroy$)
        ).subscribe({
            next: (promotion) => {
                this.successMessage = `Deployment promoted successfully to ${this.promotionDialog.selectedEnvironment?.name}`;
                this.closePromotionDialog();
                this.loadDeployments(); // Refresh the deployments list
            },
            error: (error) => {
                console.error('Failed to promote deployment:', error);
                this.errorMessage = 'Failed to promote deployment';
                this.promotionDialog.isPromoting = false;
            }
        });
    }

    /**
     * Gets required provider capabilities based on environment type
     */
    private getRequiredCapabilitiesForEnvironment(envType: string): import('../../services/provider.service').ProviderCapability[] {
        switch (envType) {
            case 'kubernetes':
                return ['containers', 'networking', 'persistent-storage'];
            case 'vm':
                return ['networking'];
            case 'serverless':
                return ['auto-scaling'];
            case 'container':
                return ['containers', 'networking'];
            default:
                return [];
        }
    }

    /**
     * Select deployment type (plugin or provider)
     */
    selectDeploymentType(type: 'plugin' | 'provider'): void {
        if (type === 'plugin') {
            // Clear provider selection and show plugin selection
            this.wizardState.selectedProvider = null;
            this.wizardState.providerConfig = {};
            // Don't clear plugin selection, let user choose
        } else if (type === 'provider') {
            // Clear plugin selection and show provider selection
            this.wizardState.selectedPlugin = null;
            this.wizardState.pluginConfig = {};
            // Don't clear provider selection, let user choose
        }
        this.validateWizardStep();
    }

    /**
     * Get provider icon based on provider type
     */
    getProviderIcon(providerType: string): string {
        const iconMap: { [key: string]: string } = {
            // Kubernetes providers
            'gke': 'fab fa-google text-blue-600',
            'aks': 'fab fa-microsoft text-blue-600',
            'eks': 'fab fa-aws text-orange-600',
            'openshift': 'fas fa-cube text-red-600',
            'k3s': 'fas fa-dharmachakra text-blue-600',
            'microk8s': 'fas fa-dharmachakra text-orange-600',
            'rancher': 'fas fa-cow text-blue-600',
            
            // Cloud VM providers
            'gce': 'fab fa-google text-blue-600',
            'ec2': 'fab fa-aws text-orange-600',
            'azure-vm': 'fab fa-microsoft text-blue-600',
            'digitalocean': 'fas fa-droplet text-blue-600',
            'linode': 'fas fa-server text-green-600',
            'vultr': 'fas fa-server text-blue-600',
            
            // Container platforms
            'docker-swarm': 'fab fa-docker text-blue-600',
            'nomad': 'fas fa-rocket text-purple-600',
            'mesos': 'fas fa-layer-group text-gray-600',
            
            // Serverless platforms
            'lambda': 'fab fa-aws text-orange-600',
            'cloud-functions': 'fab fa-google text-blue-600',
            'azure-functions': 'fab fa-microsoft text-blue-600',
            'cloudflare-workers': 'fas fa-cloud text-orange-600',
            'vercel': 'fas fa-bolt text-black',
            'netlify': 'fas fa-globe text-green-600',
            
            // Edge computing
            'cloudflare-edge': 'fas fa-cloud text-orange-600',
            'aws-wavelength': 'fab fa-aws text-orange-600',
            'azure-edge': 'fab fa-microsoft text-blue-600',
            
            // On-premise/Hybrid
            'bare-metal': 'fas fa-server text-gray-600',
            'vmware': 'fas fa-server text-blue-600',
            'hyper-v': 'fab fa-microsoft text-blue-600',
            'proxmox': 'fas fa-server text-orange-600',
            'openstack': 'fas fa-cloud text-red-600',
            
            // CI/CD platforms
            'github-actions': 'fab fa-github text-gray-800',
            'gitlab-ci': 'fab fa-gitlab text-orange-600',
            'jenkins': 'fas fa-tools text-blue-600',
            'circleci': 'fas fa-circle text-green-600',
            'travis-ci': 'fas fa-check text-green-600'
        };
        
        return iconMap[providerType] || 'fas fa-cloud text-gray-600';
    }

    /**
     * Get provider icon background class
     */
    getProviderIconClass(providerType: string): string {
        const classMap: { [key: string]: string } = {
            // Kubernetes providers
            'gke': 'bg-blue-100',
            'aks': 'bg-blue-100',
            'eks': 'bg-orange-100',
            'openshift': 'bg-red-100',
            'k3s': 'bg-blue-100',
            'microk8s': 'bg-orange-100',
            'rancher': 'bg-blue-100',
            
            // Cloud VM providers
            'gce': 'bg-blue-100',
            'ec2': 'bg-orange-100',
            'azure-vm': 'bg-blue-100',
            'digitalocean': 'bg-blue-100',
            'linode': 'bg-green-100',
            'vultr': 'bg-blue-100',
            
            // Container platforms
            'docker-swarm': 'bg-blue-100',
            'nomad': 'bg-purple-100',
            'mesos': 'bg-gray-100',
            
            // Serverless platforms
            'lambda': 'bg-orange-100',
            'cloud-functions': 'bg-blue-100',
            'azure-functions': 'bg-blue-100',
            'cloudflare-workers': 'bg-orange-100',
            'vercel': 'bg-gray-100',
            'netlify': 'bg-green-100',
            
            // Edge computing
            'cloudflare-edge': 'bg-orange-100',
            'aws-wavelength': 'bg-orange-100',
            'azure-edge': 'bg-blue-100',
            
            // On-premise/Hybrid
            'bare-metal': 'bg-gray-100',
            'vmware': 'bg-blue-100',
            'hyper-v': 'bg-blue-100',
            'proxmox': 'bg-orange-100',
            'openstack': 'bg-red-100',
            
            // CI/CD platforms
            'github-actions': 'bg-gray-100',
            'gitlab-ci': 'bg-orange-100',
            'jenkins': 'bg-blue-100',
            'circleci': 'bg-green-100',
            'travis-ci': 'bg-green-100'
        };
        
        return classMap[providerType] || 'bg-gray-100';
    }

    /**
     * Check if a provider option is selected (for multi-select fields)
     */
    isProviderOptionSelected(fieldName: string, optionValue: string): boolean {
        const fieldValue = this.wizardState.providerConfig[fieldName];
        if (Array.isArray(fieldValue)) {
            return fieldValue.includes(optionValue);
        }
        return false;
    }

    /**
     * Toggle provider option selection (for multi-select fields)
     */
    toggleProviderOption(fieldName: string, optionValue: string, event: Event): void {
        const target = event.target as HTMLInputElement;
        let currentValue = this.wizardState.providerConfig[fieldName];
        
        if (!Array.isArray(currentValue)) {
            currentValue = [];
        }
        
        if (target.checked) {
            if (!currentValue.includes(optionValue)) {
                currentValue.push(optionValue);
            }
        } else {
            const index = currentValue.indexOf(optionValue);
            if (index > -1) {
                currentValue.splice(index, 1);
            }
        }
        
        this.wizardState.providerConfig[fieldName] = currentValue;
        this.validateWizardStep();
    }

    /**
     * Handle provider file input changes
     */
    onProviderFileChange(fieldName: string, event: Event): void {
        const target = event.target as HTMLInputElement;
        const file = target.files?.[0];
        
        if (file) {
            // For now, just store the file name
            // In a real implementation, you'd want to upload the file or read its contents
            this.wizardState.providerConfig[fieldName] = file.name;
            this.validateWizardStep();
        }
    }

    /**
     * Check if a provider field is sensitive (should be masked in review)
     */
    isProviderFieldSensitive(fieldName: string): boolean {
        if (!this.wizardState.selectedProvider) return false;
        
        const field = this.wizardState.selectedProvider.configFields.find(f => f.name === fieldName);
        return field?.sensitive === true || field?.type === 'password';
    }
}