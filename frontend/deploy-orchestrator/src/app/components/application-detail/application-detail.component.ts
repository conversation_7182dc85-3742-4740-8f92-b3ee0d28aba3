import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, interval } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { ApplicationService } from '../../services/application.service';
import { 
  Application, 
  Component as AppComponent, 
  DeploymentHistory,
  MetricData,
  HealthStatus,
  DeploymentStatus,
  Environment,
  ApplicationMetrics
} from '../../models/application.model';

@Component({
  selector: 'app-application-detail',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './application-detail.component.html',
  styleUrls: ['./application-detail.component.scss']
})
export class ApplicationDetailComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Route Parameters
  applicationId!: string;

  // Data
  application: Application | null = null;
  components: AppComponent[] = [];
  deploymentHistory: DeploymentHistory[] = [];
  environments: Environment[] = [];
  metrics: ApplicationMetrics | null = null;
  loading = false;
  error: string | null = null;

  // UI State
  activeTab: 'overview' | 'components' | 'deployments' | 'monitoring' | 'topology' = 'overview';
  tabs: ('overview' | 'components' | 'deployments' | 'monitoring' | 'topology')[] = 
    ['overview', 'components', 'deployments', 'monitoring', 'topology'];
  selectedEnvironment = 'all';
  selectedTimeRange = '24h';
  autoRefresh = true;

  // Chart Data
  performanceData: any = null;
  deploymentTrends: any = null;

  // Time Ranges
  timeRanges = [
    { value: '1h', label: 'Last Hour' },
    { value: '24h', label: 'Last 24 Hours' },
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' }
  ];

  // Real-time Stats
  realTimeStats = {
    totalRequests: 0,
    errorRate: 0,
    averageResponseTime: 0,
    activeUsers: 0,
    throughput: 0
  };

  constructor(
    private applicationService: ApplicationService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      this.applicationId = params['applicationId'];
      this.loadApplicationData();
    });

    // Set up auto-refresh for real-time data
    if (this.autoRefresh) {
      interval(30000).pipe(takeUntil(this.destroy$)).subscribe(() => {
        this.refreshMetrics();
      });
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private async loadApplicationData(): Promise<void> {
    this.loading = true;
    this.error = null;

    try {
      // Load application details
      this.applicationService.getApplication(this.applicationId).subscribe(app => {
        this.application = app;
      });
      
      // Load components
      this.applicationService.getApplicationComponents(this.applicationId).subscribe(components => {
        this.components = components;
      });
      
      // Load deployment history
      this.applicationService.getDeploymentHistory(this.applicationId).subscribe(history => {
        this.deploymentHistory = history;
      });
      
      // Load environments
      this.applicationService.getEnvironments().subscribe(environments => {
        this.environments = environments;
      });
      
      // Load metrics
      await this.loadMetrics();
      
    } catch (error: any) {
      this.error = error.message || 'Failed to load application data';
    } finally {
      this.loading = false;
    }
  }

  private async loadMetrics(): Promise<void> {
    try {
      this.applicationService.getApplicationMetrics(
        this.applicationId, 
        this.selectedEnvironment || 'production',
        this.selectedTimeRange
      ).subscribe(metrics => {
        this.metrics = metrics;
        this.updateChartData();
        this.updateRealTimeStats();
      });
      
    } catch (error) {
      console.error('Failed to load metrics:', error);
    }
  }

  private async refreshMetrics(): Promise<void> {
    if (this.activeTab === 'monitoring') {
      await this.loadMetrics();
    }
  }

  private updateChartData(): void {
    if (!this.metrics) return;

    // Performance Chart Data
    this.performanceData = {
      labels: this.metrics.timeSeriesData.map(d => new Date(d.timestamp).toLocaleTimeString()),
      datasets: [
        {
          label: 'Response Time (ms)',
          data: this.metrics.timeSeriesData.map(d => d.responseTime),
          borderColor: '#007bff',
          backgroundColor: 'rgba(0, 123, 255, 0.1)',
          tension: 0.4
        },
        {
          label: 'CPU Usage (%)',
          data: this.metrics.timeSeriesData.map(d => d.cpuUsage),
          borderColor: '#28a745',
          backgroundColor: 'rgba(40, 167, 69, 0.1)',
          tension: 0.4,
          yAxisID: 'y1'
        }
      ]
    };

    // Deployment Trends
    this.deploymentTrends = {
      labels: this.deploymentHistory.slice(-10).map(d => new Date(d.timestamp).toLocaleDateString()),
      datasets: [
        {
          label: 'Successful Deployments',
          data: this.getDeploymentTrendData('success'),
          backgroundColor: '#28a745'
        },
        {
          label: 'Failed Deployments',
          data: this.getDeploymentTrendData('failed'),
          backgroundColor: '#dc3545'
        }
      ]
    };
  }

  private updateRealTimeStats(): void {
    if (!this.metrics) return;

    const latest = this.metrics.timeSeriesData[this.metrics.timeSeriesData.length - 1];
    if (latest) {
      this.realTimeStats = {
        totalRequests: this.metrics.totalRequests,
        errorRate: latest.errorRate,
        averageResponseTime: latest.responseTime,
        activeUsers: latest.activeUsers || 0,
        throughput: latest.throughput || 0
      };
    }
  }

  private getDeploymentTrendData(status: 'success' | 'failed'): number[] {
    const data: number[] = [];
    const groupedByDate = this.groupDeploymentsByDate();
    
    Object.keys(groupedByDate).forEach(date => {
      const count = groupedByDate[date].filter(d => 
        status === 'success' ? d.status === DeploymentStatus.DEPLOYED : d.status === DeploymentStatus.FAILED
      ).length;
      data.push(count);
    });
    
    return data;
  }

  private groupDeploymentsByDate(): { [date: string]: DeploymentHistory[] } {
    return this.deploymentHistory.reduce((groups, deployment) => {
      const date = new Date(deployment.timestamp).toDateString();
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(deployment);
      return groups;
    }, {} as { [date: string]: DeploymentHistory[] });
  }

  // Navigation Methods
  navigateToComponent(componentId: string): void {
    this.router.navigate(['../components', componentId], { relativeTo: this.route });
  }

  navigateToApplications(): void {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }

  // Deployment Actions
  async deployApplication(environment: string): Promise<void> {
    if (!this.application) return;

    try {
      await this.applicationService.deployApplication(this.applicationId, environment);
      await this.loadApplicationData(); // Refresh data
    } catch (error: any) {
      this.error = error.message || 'Failed to deploy application';
    }
  }

  async rollbackApplication(environment: string): Promise<void> {
    if (!this.application) return;

    if (!confirm(`Are you sure you want to rollback ${this.application.name} in ${environment}?`)) {
      return;
    }

    try {
      await this.applicationService.rollbackApplication(this.applicationId, environment);
      await this.loadApplicationData(); // Refresh data
    } catch (error: any) {
      this.error = error.message || 'Failed to rollback application';
    }
  }

  // Filtering and Display Methods
  getFilteredComponents(): AppComponent[] {
    if (this.selectedEnvironment === 'all') {
      return this.components;
    }
    
    return this.components.filter(component => 
      component.environments?.some(env => env.name === this.selectedEnvironment)
    );
  }

  getFilteredDeployments(): DeploymentHistory[] {
    if (this.selectedEnvironment === 'all') {
      return this.deploymentHistory;
    }
    
    return this.deploymentHistory.filter(deployment => 
      deployment.environment === this.selectedEnvironment
    );
  }

  getApplicationHealthStatus(): HealthStatus {
    if (!this.components.length) return HealthStatus.UNKNOWN;
    
    const hasError = this.components.some(c => c.healthStatus === HealthStatus.ERROR);
    const hasWarning = this.components.some(c => c.healthStatus === HealthStatus.WARNING);
    
    if (hasError) return HealthStatus.ERROR;
    if (hasWarning) return HealthStatus.WARNING;
    return HealthStatus.HEALTHY;
  }

  getEnvironmentStatus(environmentName: string): DeploymentStatus {
    const envComponents = this.components.filter(c => 
      c.environments?.some(e => e.name === environmentName)
    );
    
    if (!envComponents.length) return DeploymentStatus.PENDING;
    
    const hasDeploying = envComponents.some(c => 
      c.environments?.some(e => e.name === environmentName && e.deploymentStatus === DeploymentStatus.DEPLOYING)
    );
    const hasFailed = envComponents.some(c => 
      c.environments?.some(e => e.name === environmentName && e.deploymentStatus === DeploymentStatus.FAILED)
    );
    
    if (hasDeploying) return DeploymentStatus.DEPLOYING;
    if (hasFailed) return DeploymentStatus.FAILED;
    return DeploymentStatus.DEPLOYED;
  }

  // Utility Methods
  getDeploymentStatusBadgeClasses(status: DeploymentStatus): string {
    const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium';
    switch (status) {
      case DeploymentStatus.DEPLOYED: 
        return `${baseClasses} bg-green-100 text-green-800 border border-green-200`;
      case DeploymentStatus.DEPLOYING: 
        return `${baseClasses} bg-blue-100 text-blue-800 border border-blue-200`;
      case DeploymentStatus.FAILED: 
        return `${baseClasses} bg-red-100 text-red-800 border border-red-200`;
      case DeploymentStatus.ROLLING_BACK: 
        return `${baseClasses} bg-orange-100 text-orange-800 border border-orange-200`;
      default: 
        return `${baseClasses} bg-gray-100 text-gray-800 border border-gray-200`;
    }
  }

  getStatusColor(status: HealthStatus | undefined): string {
    switch (status) {
      case HealthStatus.HEALTHY: return 'text-green-500';
      case HealthStatus.WARNING: return 'text-yellow-500';
      case HealthStatus.ERROR: return 'text-red-500';
      default: return 'text-gray-400';
    }
  }

  getComponentTypeIcon(type: string): string {
    const iconMap: { [key: string]: string } = {
      'microservice': 'fas fa-cube',
      'frontend': 'fas fa-desktop',
      'database': 'fas fa-database',
      'queue': 'fas fa-stream',
      'cache': 'fas fa-memory',
      'cdn': 'fas fa-cloud',
      'api-gateway': 'fas fa-gateway'
    };
    return iconMap[type] || 'fas fa-cube';
  }

  getCardHoverClasses(): string {
    return 'transition-all duration-300 ease-in-out hover:shadow-lg hover:-translate-y-1 hover:scale-[1.02] cursor-pointer';
  }

  getButtonClasses(variant: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' = 'primary'): string {
    const baseClasses = 'inline-flex items-center px-4 py-2 font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
    
    const variantClasses = {
      primary: 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white focus:ring-blue-500 shadow-lg hover:shadow-xl',
      secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800 focus:ring-gray-500',
      success: 'bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white focus:ring-green-500 shadow-lg hover:shadow-xl',
      danger: 'bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white focus:ring-red-500 shadow-lg hover:shadow-xl',
      warning: 'bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-white focus:ring-yellow-500 shadow-lg hover:shadow-xl'
    };
    
    return `${baseClasses} ${variantClasses[variant]}`;
  }

  // Tab Management
  setActiveTab(tab: 'overview' | 'components' | 'deployments' | 'monitoring' | 'topology'): void {
    this.activeTab = tab;
    if (tab === 'monitoring') {
      this.loadMetrics();
    }
  }

  // Auto Refresh Toggle
  toggleAutoRefresh(): void {
    this.autoRefresh = !this.autoRefresh;
    
    if (this.autoRefresh) {
      // Restart auto-refresh interval
      interval(30000).pipe(takeUntil(this.destroy$)).subscribe(() => {
        this.refreshMetrics();
      });
    }
  }

  // Time Range and Filter Handlers
  onTimeRangeChange(): void {
    this.loadMetrics();
  }

  onEnvironmentFilterChange(): void {
    this.loadMetrics();
  }

  // Status and Color Utilities
  getDeploymentStatusColor(status: DeploymentStatus): string {
    switch (status) {
      case DeploymentStatus.DEPLOYED: 
        return 'bg-green-100 text-green-800';
      case DeploymentStatus.DEPLOYING: 
        return 'bg-blue-100 text-blue-800';
      case DeploymentStatus.FAILED: 
        return 'bg-red-100 text-red-800';
      case DeploymentStatus.ROLLING_BACK: 
        return 'bg-orange-100 text-orange-800';
      default: 
        return 'bg-gray-100 text-gray-800';
    }
  }

  // Duration Formatter
  formatDuration(durationMs: number): string {
    if (durationMs < 1000) {
      return `${durationMs}ms`;
    }
    
    const seconds = Math.floor(durationMs / 1000);
    if (seconds < 60) {
      return `${seconds}s`;
    }
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  }

  // Export Metrics
  exportMetrics(): void {
    if (!this.metrics) {
      alert('No metrics data available to export');
      return;
    }

    try {
      const dataToExport = {
        application: this.application?.name,
        environment: this.selectedEnvironment,
        timeRange: this.selectedTimeRange,
        exportedAt: new Date().toISOString(),
        metrics: this.metrics,
        realTimeStats: this.realTimeStats
      };

      const dataStr = JSON.stringify(dataToExport, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      const link = document.createElement('a');
      link.href = URL.createObjectURL(dataBlob);
      link.download = `${this.application?.name || 'app'}-metrics-${new Date().toISOString().slice(0, 10)}.json`;
      link.click();
      
      URL.revokeObjectURL(link.href);
    } catch (error) {
      console.error('Failed to export metrics:', error);
      alert('Failed to export metrics');
    }
  }
}
