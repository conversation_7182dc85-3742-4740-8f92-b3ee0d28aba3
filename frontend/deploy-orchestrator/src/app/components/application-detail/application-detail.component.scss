// Application Detail Component - Tailwind CSS Optimized Styles
// Streamlined SCSS focused on custom utilities and animations

// =============================================================================
// ANIMATIONS
// =============================================================================

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: 200px 0; }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

// =============================================================================
// GLASS MORPHISM EFFECTS
// =============================================================================

.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-modal {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.glass-sidebar {
  background: rgba(249, 250, 251, 0.8);
  backdrop-filter: blur(12px);
  border-right: 1px solid rgba(229, 231, 235, 0.8);
}

// =============================================================================
// GRADIENT BACKGROUNDS
// =============================================================================

.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-danger {
  background: linear-gradient(135deg, #ff758c 0%, #ff7eb3 100%);
}

// =============================================================================
// ENHANCED HOVER EFFECTS
// =============================================================================

.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}

.hover-scale {
  transition: transform 0.2s ease-in-out;
  
  &:hover {
    transform: scale(1.05);
  }
}

.hover-glow {
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
}

// =============================================================================
// STATUS INDICATORS
// =============================================================================

.status-indicator {
  position: relative;
  display: inline-block;
  
  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    animation: pulse 2s infinite;
  }
  
  &.status-healthy::before {
    background-color: rgba(34, 197, 94, 0.4);
  }
  
  &.status-warning::before {
    background-color: rgba(245, 158, 11, 0.4);
  }
  
  &.status-error::before {
    background-color: rgba(239, 68, 68, 0.4);
  }
}

// =============================================================================
// LOADING STATES
// =============================================================================

.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

.loading-dots {
  display: inline-flex;
  gap: 4px;
  
  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: currentColor;
    animation: bounce 1.4s infinite ease-in-out both;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
  }
}

// =============================================================================
// PROGRESS BARS
// =============================================================================

.progress-animated {
  .progress-bar {
    background: linear-gradient(45deg, rgba(255,255,255,.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,.15) 50%, rgba(255,255,255,.15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
  }
}

@keyframes progress-bar-stripes {
  0% { background-position: 1rem 0; }
  100% { background-position: 0 0; }
}

// =============================================================================
// CUSTOM SCROLLBAR
// =============================================================================

.custom-scrollbar {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
    
    &:hover {
      background: #94a3b8;
    }
  }
}

// =============================================================================
// RESPONSIVE BREAKPOINT HELPERS
// =============================================================================

@media (max-width: 640px) {
  .mobile-hidden { display: none !important; }
  .mobile-full { width: 100% !important; }
}

@media (max-width: 768px) {
  .tablet-hidden { display: none !important; }
  .tablet-stack { flex-direction: column !important; }
}

@media (max-width: 1024px) {
  .desktop-hidden { display: none !important; }
}

// =============================================================================
// COMPONENT-SPECIFIC UTILITIES
// =============================================================================

.application-detail {
  // Enhanced card styles for application details
  .detail-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
      border-color: #3b82f6;
      transform: translateY(-2px);
    }
  }
  
  // Environment status indicators
  .env-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    
    .status-dot {
      width: 0.5rem;
      height: 0.5rem;
      border-radius: 50%;
    }
  }
  
  // Component grid enhancements
  .component-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
  
  // Deployment timeline
  .timeline-item {
    position: relative;
    padding-left: 2rem;
    padding-bottom: 2rem;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0.5rem;
      width: 1rem;
      height: 1rem;
      background-color: #3b82f6;
      border-radius: 50%;
      border: 4px solid white;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    &::after {
      content: '';
      position: absolute;
      left: 0.5rem;
      top: 1.5rem;
      width: 0.125rem;
      height: 100%;
      background-color: #e5e7eb;
    }
    
    &:last-child::after {
      display: none;
    }
  }
  
  // Quick stats animations
  .stat-card {
    transition: all 0.3s ease-in-out;
    
    &:hover {
      transform: scale(1.05);
    }
    
    .stat-icon {
      transition: transform 0.3s ease;
    }
    
    &:hover .stat-icon {
      transform: scale(1.1);
    }
  }
  
  // Filter tabs
  .filter-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    
    .tab-button {
      padding: 0.5rem 1rem;
      border-radius: 0.5rem;
      font-weight: 500;
      font-size: 0.875rem;
      transition: all 0.2s ease;
      
      &.active {
        background-color: #3b82f6;
        color: white;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }
      
      &:not(.active) {
        background-color: #f3f4f6;
        color: #4b5563;
        
        &:hover {
          background-color: #e5e7eb;
        }
      }
    }
  }
}

// =============================================================================
// ANIMATION UTILITIES
// =============================================================================

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.animate-pulse-slow {
  animation: pulse 3s infinite;
}

.animate-bounce-slow {
  animation: bounce 2s infinite;
}

// =============================================================================
// DARK MODE SUPPORT (for future implementation)
// =============================================================================

@media (prefers-color-scheme: dark) {
  .glass-card {
    background: rgba(17, 24, 39, 0.8);
    border: 1px solid rgba(75, 85, 99, 0.3);
  }
  
  .glass-modal {
    background: rgba(17, 24, 39, 0.95);
    border: 1px solid rgba(75, 85, 99, 0.3);
  }
}

// =============================================================================
// PRINT STYLES
// =============================================================================

@media print {
  .no-print {
    display: none !important;
  }
  
  .application-detail {
    height: auto !important;
    overflow: visible !important;
  }
}
