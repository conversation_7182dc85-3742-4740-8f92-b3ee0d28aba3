<!-- Application Detail - Tailwind CSS Optimized -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
      <nav class="flex items-center space-x-3 text-sm">
        <button 
          class="inline-flex items-center px-3 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors duration-200"
          (click)="navigateToApplications()">
          <i class="fas fa-arrow-left mr-2"></i>
          Back to Applications
        </button>
        <span class="text-gray-400">/</span>
        <span class="font-medium text-gray-900">{{ application?.name }}</span>
      </nav>
      
      <div class="flex items-center space-x-3">
        <button 
          class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5"
          (click)="exportMetrics()" 
          [disabled]="!metrics">
          <i class="fas fa-download mr-2"></i>
          Export Metrics
        </button>
        <label class="flex items-center space-x-2 text-sm">
          <input 
            type="checkbox" 
            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
            [(ngModel)]="autoRefresh"
            (change)="toggleAutoRefresh()">
          <span class="text-gray-700 font-medium">Auto Refresh</span>
        </label>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex flex-col items-center justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    <p class="mt-4 text-gray-600 font-medium">Loading application details...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-start justify-between">
    <div class="flex items-start space-x-3">
      <i class="fas fa-exclamation-triangle text-red-500 mt-0.5"></i>
      <span class="text-red-800">{{ error }}</span>
    </div>
    <button 
      type="button" 
      class="text-red-500 hover:text-red-700 transition-colors duration-200"
      (click)="error = null">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <!-- Main Content -->
  <div *ngIf="!loading && !error && application" class="space-y-8">
    
    <!-- Application Header -->
    <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-8 border border-white/30 shadow-lg">
      <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-6 lg:space-y-0">
        <div class="flex items-center space-x-6">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
            <i class="fas fa-layer-group text-white text-2xl"></i>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-gray-900">{{ application.name }}</h1>
            <p class="text-gray-600 mt-1">{{ application.description || 'No description available' }}</p>
            <div class="flex flex-wrap items-center gap-4 mt-3 text-sm">
              <span class="flex items-center space-x-1 text-gray-600">
                <i class="fas fa-cubes text-blue-500"></i>
                <span>{{ components.length }} Components</span>
              </span>
              <span class="flex items-center space-x-1 text-gray-600">
                <i class="fas fa-code-branch text-green-500"></i>
                <span>v{{ application.version }}</span>
              </span>
              <span class="flex items-center space-x-1">
                <i [class]="'fas fa-circle ' + getStatusColor(getApplicationHealthStatus())"></i>
                <span class="font-medium">{{ getApplicationHealthStatus() | titlecase }}</span>
              </span>
              <span class="flex items-center space-x-1 text-gray-600">
                <i class="fas fa-calendar text-orange-500"></i>
                <span>{{ application.lastDeployment?.timestamp | date:'medium' }}</span>
              </span>
            </div>
          </div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div class="bg-white/50 backdrop-blur-sm rounded-xl p-4 text-center border border-white/30">
            <div class="text-2xl font-bold text-blue-600">{{ realTimeStats.totalRequests | number }}</div>
            <div class="text-xs text-gray-600 mt-1">Total Requests</div>
          </div>
          <div class="bg-white/50 backdrop-blur-sm rounded-xl p-4 text-center border border-white/30">
            <div class="text-2xl font-bold text-red-600">{{ realTimeStats.errorRate | number:'1.2-2' }}%</div>
            <div class="text-xs text-gray-600 mt-1">Error Rate</div>
          </div>
          <div class="bg-white/50 backdrop-blur-sm rounded-xl p-4 text-center border border-white/30">
            <div class="text-2xl font-bold text-yellow-600">{{ realTimeStats.averageResponseTime | number }}ms</div>
            <div class="text-xs text-gray-600 mt-1">Avg Response Time</div>
          </div>
          <div class="bg-white/50 backdrop-blur-sm rounded-xl p-4 text-center border border-white/30">
            <div class="text-2xl font-bold text-green-600">{{ realTimeStats.activeUsers | number }}</div>
            <div class="text-xs text-gray-600 mt-1">Active Users</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Environment Status Bar -->
    <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-6 border border-white/30 shadow-lg">
      <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <i class="fas fa-server text-blue-500 mr-2"></i>
        Environment Status
      </h4>
      <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <div *ngFor="let env of environments" class="flex items-center justify-between p-4 bg-white/50 rounded-xl border border-white/30">
          <div>
            <div class="font-medium text-gray-900">{{ env.name }}</div>
            <div class="text-sm text-gray-600">{{ env.type }}</div>
          </div>
          <div class="flex items-center space-x-3">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  [ngClass]="getDeploymentStatusBadgeClasses(getEnvironmentStatus(env.name))">
              {{ getEnvironmentStatus(env.name) }}
            </span>
            <div class="flex items-center space-x-2">
              <button 
                class="inline-flex items-center p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                (click)="deployApplication(env.name)"
                [disabled]="getEnvironmentStatus(env.name) === 'deploying'">
                <i class="fas fa-rocket"></i>
              </button>
              <button 
                *ngIf="getEnvironmentStatus(env.name) === 'deployed'"
                class="inline-flex items-center p-2 text-orange-600 hover:text-orange-800 hover:bg-orange-50 rounded-lg transition-colors duration-200"
                (click)="rollbackApplication(env.name)">
                <i class="fas fa-undo"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <button 
        *ngFor="let tab of tabs"
        class="tab-button"
        [class.active]="activeTab === tab"
        (click)="setActiveTab(tab)">
        <i [class]="'fas ' + (tab === 'overview' ? 'fa-home' : 
                           tab === 'components' ? 'fa-cubes' : 
                           tab === 'deployments' ? 'fa-rocket' : 
                           tab === 'monitoring' ? 'fa-chart-line' : 'fa-project-diagram')"></i>
        {{ tab | titlecase }}
      </button>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      
      <!-- Overview Tab -->
      <div *ngIf="activeTab === 'overview'" class="tab-pane">
        <div class="overview-grid">
          
          <!-- Application Info -->
          <div class="info-section">
            <h4><i class="fas fa-info-circle"></i> Application Information</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>Name</label>
                <span>{{ application.name }}</span>
              </div>
              <div class="info-item">
                <label>Version</label>
                <span>{{ application.version }}</span>
              </div>
              <div class="info-item">
                <label>Group</label>
                <span>{{ application.group?.name }}</span>
              </div>
              <div class="info-item">
                <label>Owner</label>
                <span>{{ application.owner }}</span>
              </div>
              <div class="info-item">
                <label>Repository</label>
                <span>{{ application.repository?.url }}</span>
              </div>
              <div class="info-item">
                <label>Last Deployment</label>
                <span>{{ application.lastDeployment?.timestamp | date:'medium' }}</span>
              </div>
            </div>
          </div>

          <!-- Recent Deployments -->
          <div class="recent-deployments">
            <h4><i class="fas fa-history"></i> Recent Deployments</h4>
            <div class="deployment-list">
              <div *ngFor="let deployment of deploymentHistory.slice(0, 5)" class="deployment-item">
                <div class="deployment-info">
                  <div class="deployment-env">{{ deployment.environment }}</div>
                  <div class="deployment-version">v{{ deployment.version }}</div>
                  <div class="deployment-time">{{ deployment.timestamp | date:'short' }}</div>
                </div>
                <div class="deployment-status">
                  <span [class]="'badge ' + getDeploymentStatusColor(deployment.status)">
                    {{ deployment.status }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Component Health Overview -->
          <div class="component-health">
            <h4><i class="fas fa-heartbeat"></i> Component Health</h4>
            <div class="health-summary">
              <div *ngFor="let component of components" class="component-health-item">
                <div class="component-info">
                  <i [class]="getComponentTypeIcon(component.type)"></i>
                  <span class="component-name">{{ component.name }}</span>
                </div>
                <div class="health-indicators">
                  <span *ngFor="let env of component.environments" 
                        [class]="'health-dot ' + getStatusColor(env.healthStatus)"
                        [title]="env.name + ': ' + env.healthStatus">
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Performance Summary -->
          <div class="performance-summary" *ngIf="metrics">
            <h4><i class="fas fa-tachometer-alt"></i> Performance Summary</h4>
            <div class="performance-grid">
              <div class="perf-metric">
                <div class="metric-value">{{ metrics.averageResponseTime | number }}ms</div>
                <div class="metric-label">Avg Response Time</div>
                <div class="metric-trend positive">
                  <i class="fas fa-arrow-up"></i> 5.2%
                </div>
              </div>
              <div class="perf-metric">
                <div class="metric-value">{{ metrics.totalRequests | number }}</div>
                <div class="metric-label">Total Requests</div>
                <div class="metric-trend positive">
                  <i class="fas fa-arrow-up"></i> 12.4%
                </div>
              </div>
              <div class="perf-metric">
                <div class="metric-value">{{ metrics.errorRate | number:'1.2-2' }}%</div>
                <div class="metric-label">Error Rate</div>
                <div class="metric-trend negative">
                  <i class="fas fa-arrow-down"></i> 2.1%
                </div>
              </div>
              <div class="perf-metric">
                <div class="metric-value">99.9%</div>
                <div class="metric-label">Uptime</div>
                <div class="metric-trend stable">
                  <i class="fas fa-minus"></i> 0%
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Components Tab -->
      <div *ngIf="activeTab === 'components'" class="tab-pane">
        <div class="components-section">
          <div class="section-header">
            <h4><i class="fas fa-cubes"></i> Application Components</h4>
            <div class="filters">
              <select 
                class="block w-48 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white text-sm"
                [(ngModel)]="selectedEnvironment"
                (change)="onEnvironmentFilterChange()">
                <option value="all">All Environments</option>
                <option *ngFor="let env of environments" [value]="env.name">
                  {{ env.name }}
                </option>
              </select>
            </div>
          </div>

          <div class="components-grid">
            <div *ngFor="let component of getFilteredComponents()" 
                 class="component-card"
                 (click)="navigateToComponent(component.id)">
              <div class="component-header">
                <div class="component-icon">
                  <i [class]="getComponentTypeIcon(component.type)"></i>
                </div>
                <div class="component-info">
                  <h5>{{ component.name }}</h5>
                  <span class="component-type">{{ component.type }}</span>
                </div>
                <div class="component-status">
                  <span [class]="'status-badge ' + getStatusColor(component.healthStatus)">
                    {{ component.healthStatus }}
                  </span>
                </div>
              </div>

              <div class="component-body">
                <p class="component-description">{{ component.description || 'No description' }}</p>
                
                <div class="component-stats">
                  <div class="stat">
                    <span class="stat-label">Version</span>
                    <span class="stat-value">{{ component.version }}</span>
                  </div>
                  <div class="stat">
                    <span class="stat-label">Environments</span>
                    <span class="stat-value">{{ component.environments?.length || 0 }}</span>
                  </div>
                </div>
              </div>

              <div class="component-footer">
                <div class="deployment-status">
                  <div *ngFor="let env of component.environments" class="env-status">
                    <span class="env-name">{{ env.name }}</span>
                    <span [class]="'badge ' + getDeploymentStatusColor(env.deploymentStatus)">
                      {{ env.deploymentStatus }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Deployments Tab -->
      <div *ngIf="activeTab === 'deployments'" class="tab-pane">
        <div class="deployments-section">
          <div class="section-header">
            <h4><i class="fas fa-rocket"></i> Deployment History</h4>
            <div class="filters">
              <select 
                class="block w-48 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white text-sm"
                [(ngModel)]="selectedEnvironment"
                (change)="onEnvironmentFilterChange()">
                <option value="all">All Environments</option>
                <option *ngFor="let env of environments" [value]="env.name">
                  {{ env.name }}
                </option>
              </select>
            </div>
          </div>

          <div class="deployments-table">
            <table class="table">
              <thead>
                <tr>
                  <th>Environment</th>
                  <th>Component</th>
                  <th>Version</th>
                  <th>Status</th>
                  <th>Deployed By</th>
                  <th>Duration</th>
                  <th>Timestamp</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let deployment of getFilteredDeployments()">
                  <td>{{ deployment.environment }}</td>
                  <td>{{ deployment.componentName }}</td>
                  <td>{{ deployment.version }}</td>
                  <td>
                    <span [class]="'badge ' + getDeploymentStatusColor(deployment.status)">
                      {{ deployment.status }}
                    </span>
                  </td>
                  <td>{{ deployment.deployedBy }}</td>
                  <td>{{ formatDuration(deployment.duration || 0) }}</td>
                  <td>{{ deployment.timestamp | date:'medium' }}</td>
                  <td>
                    <button 
                      *ngIf="deployment.status === 'deployed'"
                      class="inline-flex items-center p-2 text-orange-600 hover:text-orange-800 hover:bg-orange-50 rounded-lg transition-colors duration-200"
                      (click)="rollbackApplication(deployment.environment)">
                      <i class="fas fa-undo"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Monitoring Tab -->
      <div *ngIf="activeTab === 'monitoring'" class="tab-pane">
        <div class="monitoring-section">
          <div class="section-header">
            <h4><i class="fas fa-chart-line"></i> Application Monitoring</h4>
            <div class="monitoring-controls">
              <select 
                class="block w-48 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white text-sm mr-2"
                [(ngModel)]="selectedTimeRange"
                (change)="onTimeRangeChange()">
                <option *ngFor="let range of timeRanges" [value]="range.value">
                  {{ range.label }}
                </option>
              </select>
              <select 
                class="block w-48 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-white text-sm"
                [(ngModel)]="selectedEnvironment">
                <option value="all">All Environments</option>
                <option *ngFor="let env of environments" [value]="env.name">
                  {{ env.name }}
                </option>
              </select>
            </div>
          </div>

          <!-- Real-time Metrics Dashboard -->
          <div class="metrics-dashboard">
            <div class="metrics-row">
              <div class="metric-card">
                <div class="metric-header">
                  <h5><i class="fas fa-tachometer-alt"></i> Response Time</h5>
                </div>
                <div class="metric-value">{{ realTimeStats.averageResponseTime }}ms</div>
                <div class="metric-chart">
                  <!-- Chart placeholder - would integrate with Chart.js or similar -->
                  <div class="chart-placeholder">Performance Chart</div>
                </div>
              </div>

              <div class="metric-card">
                <div class="metric-header">
                  <h5><i class="fas fa-exclamation-triangle"></i> Error Rate</h5>
                </div>
                <div class="metric-value">{{ realTimeStats.errorRate | number:'1.2-2' }}%</div>
                <div class="metric-chart">
                  <div class="chart-placeholder">Error Rate Chart</div>
                </div>
              </div>
            </div>

            <div class="metrics-row">
              <div class="metric-card">
                <div class="metric-header">
                  <h5><i class="fas fa-server"></i> Throughput</h5>
                </div>
                <div class="metric-value">{{ realTimeStats.throughput | number }} req/s</div>
                <div class="metric-chart">
                  <div class="chart-placeholder">Throughput Chart</div>
                </div>
              </div>

              <div class="metric-card">
                <div class="metric-header">
                  <h5><i class="fas fa-users"></i> Active Users</h5>
                </div>
                <div class="metric-value">{{ realTimeStats.activeUsers | number }}</div>
                <div class="metric-chart">
                  <div class="chart-placeholder">Users Chart</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Component Health Status -->
          <div class="health-status-grid">
            <div *ngFor="let component of components" class="health-status-card">
              <div class="health-header">
                <h5>{{ component.name }}</h5>
                <span [class]="'status-indicator ' + getStatusColor(component.healthStatus)">
                  <i class="fas fa-circle"></i> {{ component.healthStatus }}
                </span>
              </div>
              
              <div class="health-metrics">
                <div *ngFor="let env of component.environments" class="env-metrics">
                  <div class="env-name">{{ env.name }}</div>
                  <div class="metrics-list">
                    <div class="metric">
                      <span class="metric-label">CPU</span>
                      <div class="metric-bar">
                        <div class="metric-fill" [style.width.%]="env.metrics?.cpu || 0"></div>
                      </div>
                      <span class="metric-value">{{ env.metrics?.cpu || 0 }}%</span>
                    </div>
                    <div class="metric">
                      <span class="metric-label">Memory</span>
                      <div class="metric-bar">
                        <div class="metric-fill" [style.width.%]="env.metrics?.memory || 0"></div>
                      </div>
                      <span class="metric-value">{{ env.metrics?.memory || 0 }}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Topology Tab -->
      <div *ngIf="activeTab === 'topology'" class="tab-pane">
        <div class="topology-section">
          <h4><i class="fas fa-project-diagram"></i> Application Topology</h4>
          
          <div class="topology-view">
            <div class="topology-canvas">
              <!-- This would be a visual topology diagram -->
              <div class="topology-placeholder">
                <i class="fas fa-project-diagram"></i>
                <p>Interactive topology view would be implemented here</p>
                <p>Showing component relationships and dependencies</p>
              </div>
            </div>
            
            <div class="topology-legend">
              <h5>Legend</h5>
              <div class="legend-items">
                <div class="legend-item">
                  <i class="fas fa-cube text-primary"></i>
                  <span>Microservice</span>
                </div>
                <div class="legend-item">
                  <i class="fas fa-database text-success"></i>
                  <span>Database</span>
                </div>
                <div class="legend-item">
                  <i class="fas fa-desktop text-warning"></i>
                  <span>Frontend</span>
                </div>
                <div class="legend-item">
                  <i class="fas fa-stream text-info"></i>
                  <span>Message Queue</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
