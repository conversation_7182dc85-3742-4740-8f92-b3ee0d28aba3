import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

import { AuthService } from '../../services/auth.service';
import { ProjectService } from '../../services/project.service';
import { GroupService } from '../../services/group.service';
import {firstValueFrom} from "rxjs";

interface GroupProjectAssignment {
  id: string;
  groupId: string;
  groupName: string;
  projectId: string;
  projectName: string;
  createdAt: string;
  createdBy: string;
}

interface Group {
  id: string;
  name: string;
  description?: string;
}

interface Project {
  id: string;
  name: string;
  description?: string;
}

@Component({
  selector: 'app-group-project-assignments',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './group-project-assignments.component.html',
  styleUrls: ['./group-project-assignments.component.scss']
})
export class GroupProjectAssignmentsComponent implements OnInit {
  assignments: GroupProjectAssignment[] = [];
  groups: Group[] = [];
  projects: Project[] = [];

  // UI state
  loading = false;
  error: string | null = null;
  showAssignDialog = false;
  showBulkAssignDialog = false;

  // Assignment form
  selectedGroupId = '';
  selectedProjectId = '';
  selectedGroupIds: string[] = [];
  selectedProjectIds: string[] = [];
  assignmentMode: 'single' | 'bulk-groups' | 'bulk-projects' = 'single';

  // Filters
  filterGroupId = '';
  filterProjectId = '';

  // Pagination
  currentPage = 1;
  pageSize = 20;
  totalAssignments = 0;

  constructor(
    private authService: AuthService,
    private projectService: ProjectService,
    private groupService: GroupService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.checkPermissions();
    this.loadData();
  }

  private checkPermissions(): void {
    if (!this.authService.isAdmin()) {
      this.router.navigate(['/dashboard']);
      return;
    }
  }

  private async loadData(): Promise<void> {
    this.loading = true;
    try {
      await Promise.all([
        this.loadAssignments(),
        this.loadGroups(),
        this.loadProjects()
      ]);
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to load data';
    } finally {
      this.loading = false;
    }
  }

  private async loadAssignments(): Promise<void> {
    try {
      const params = {
        page: this.currentPage,
        limit: this.pageSize,
        ...(this.filterGroupId && { groupId: this.filterGroupId }),
        ...(this.filterProjectId && { projectId: this.filterProjectId })
      };

      //const response = await this.groupService.getGroupProjectAssignments(params).toPromise();
      const response = await firstValueFrom(this.groupService.getGroupProjectAssignments(params));
      this.assignments = response?.assignments || [];
      this.totalAssignments = response?.pagination?.total || 0;
    } catch (error) {
      throw new Error('Failed to load assignments');
    }
  }

  private async loadGroups(): Promise<void> {
    try {
      //const response = await this.groupService.getGroups().toPromise();
      const response =  await firstValueFrom(this.groupService.getGroups());
      this.groups = response || [];
    } catch (error) {
      throw new Error('Failed to load groups');
    }
  }

  private async loadProjects(): Promise<void> {
    try {
      //const response = await this.projectService.getProjects().toPromise();
      const response = await firstValueFrom(this.projectService.getProjects());
      this.projects = response || [];
    } catch (error) {
      throw new Error('Failed to load projects');
    }
  }

  // Assignment Management
  openAssignDialog(mode: 'single' | 'bulk-groups' | 'bulk-projects' = 'single'): void {
    this.assignmentMode = mode;
    this.selectedGroupId = '';
    this.selectedProjectId = '';
    this.selectedGroupIds = [];
    this.selectedProjectIds = [];
    this.showAssignDialog = true;
    this.error = null;
  }

  closeAssignDialog(): void {
    this.showAssignDialog = false;
    this.assignmentMode = 'single';
  }

  async createAssignment(): Promise<void> {
    if (this.assignmentMode === 'single') {
      await this.createSingleAssignment();
    } else if (this.assignmentMode === 'bulk-groups') {
      await this.createBulkGroupAssignments();
    } else if (this.assignmentMode === 'bulk-projects') {
      await this.createBulkProjectAssignments();
    }
  }

  private async createSingleAssignment(): Promise<void> {
    if (!this.selectedGroupId || !this.selectedProjectId) {
      this.error = 'Please select both a group and a project';
      return;
    }

    try {
      await this.groupService.assignGroupToProject(this.selectedGroupId, this.selectedProjectId).toPromise();
      this.closeAssignDialog();
      this.loadAssignments();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to create assignment';
    }
  }

  private async createBulkGroupAssignments(): Promise<void> {
    if (this.selectedGroupIds.length === 0 || !this.selectedProjectId) {
      this.error = 'Please select groups and a project';
      return;
    }

    try {
      await this.groupService.bulkAssignGroupsToProject(this.selectedGroupIds, this.selectedProjectId).toPromise();
      this.closeAssignDialog();
      this.loadAssignments();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to create bulk assignments';
    }
  }

  private async createBulkProjectAssignments(): Promise<void> {
    if (!this.selectedGroupId || this.selectedProjectIds.length === 0) {
      this.error = 'Please select a group and projects';
      return;
    }

    try {
      await this.groupService.bulkAssignProjectsToGroup(this.selectedProjectIds, this.selectedGroupId).toPromise();
      this.closeAssignDialog();
      this.loadAssignments();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to create bulk assignments';
    }
  }

  async removeAssignment(assignment: GroupProjectAssignment): Promise<void> {
    if (!confirm(`Remove access for group "${assignment.groupName}" from project "${assignment.projectName}"?`)) {
      return;
    }

    try {
      await this.groupService.removeGroupFromProject(assignment.groupId, assignment.projectId).toPromise();
      this.loadAssignments();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to remove assignment';
    }
  }

  // Filtering and Pagination
  applyFilters(): void {
    this.currentPage = 1;
    this.loadAssignments();
  }

  clearFilters(): void {
    this.filterGroupId = '';
    this.filterProjectId = '';
    this.currentPage = 1;
    this.loadAssignments();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadAssignments();
  }

  // Multi-select helpers
  toggleGroupSelection(groupId: string): void {
    const index = this.selectedGroupIds.indexOf(groupId);
    if (index > -1) {
      this.selectedGroupIds.splice(index, 1);
    } else {
      this.selectedGroupIds.push(groupId);
    }
  }

  toggleProjectSelection(projectId: string): void {
    const index = this.selectedProjectIds.indexOf(projectId);
    if (index > -1) {
      this.selectedProjectIds.splice(index, 1);
    } else {
      this.selectedProjectIds.push(projectId);
    }
  }

  isGroupSelected(groupId: string): boolean {
    return this.selectedGroupIds.includes(groupId);
  }

  isProjectSelected(projectId: string): boolean {
    return this.selectedProjectIds.includes(projectId);
  }

  // UI Helpers
  getGroupName(groupId: string): string {
    const group = this.groups.find(g => g.id === groupId);
    return group ? group.name : groupId;
  }

  getProjectName(projectId: string): string {
    const project = this.projects.find(p => p.id === projectId);
    return project ? project.name : projectId;
  }

  get totalPages(): number {
    return Math.ceil(this.totalAssignments / this.pageSize);
  }

  get paginationPages(): number[] {
    const pages = [];
    const start = Math.max(1, this.currentPage - 2);
    const end = Math.min(this.totalPages, this.currentPage + 2);

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    return pages;
  }

  refreshData(): void {
    this.loadData();
  }

  // Math helper for templates
  Math = Math;
}
