<nav class="bg-blue-600">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex items-center justify-between h-16">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <span class="text-white font-bold text-xl">Admin Portal</span>
        </div>
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-4">
            <a routerLink="/projects" routerLinkActive="bg-blue-700" class="text-white px-3 py-2 rounded-md text-sm font-medium">Projects</a>
            <a routerLink="/workflows" routerLinkActive="bg-blue-700" class="text-white px-3 py-2 rounded-md text-sm font-medium">Workflows</a>
            <ng-container *ngIf="isAdmin()">
              <a routerLink="/admin/idp-config" routerLinkActive="bg-blue-700" class="text-white px-3 py-2 rounded-md text-sm font-medium">Identity Providers</a>
              <a routerLink="/admin/group-mapping" routerLinkActive="bg-blue-700" class="text-white px-3 py-2 rounded-md text-sm font-medium">Group Mapping</a>
            </ng-container>
          </div>
        </div>
      </div>
      <div class="hidden md:block">
        <div class="ml-4 flex items-center md:ml-6">
          <div class="ml-3 relative" clickOutside (clickOutside)="closeProfileMenu()">
            <div>
              <button (click)="toggleProfileMenu()" type="button" class="max-w-xs bg-blue-600 rounded-full flex items-center text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-blue-600 focus:ring-white" id="user-menu-button" aria-expanded="false" aria-haspopup="true">
                <span class="sr-only">Open user menu</span>
                <span class="h-8 w-8 rounded-full bg-blue-700 flex items-center justify-center text-white">
                  {{ currentUser?.username ? (currentUser?.username?.[0] || '').toUpperCase() : 'U' }}
                </span>
              </button>
            </div>
            <div *ngIf="isProfileMenuOpen" class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1">
              <div class="block px-4 py-2 text-sm text-gray-700">
                {{ currentUser?.username }}
              </div>
              <a (click)="logout()" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer" role="menuitem" tabindex="-1">Sign out</a>
            </div>
          </div>
        </div>
      </div>
      <div class="-mr-2 flex md:hidden">
        <button (click)="toggleMenu()" type="button" class="bg-blue-600 inline-flex items-center justify-center p-2 rounded-md text-white hover:text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-blue-600 focus:ring-white" aria-controls="mobile-menu" aria-expanded="false">
          <span class="sr-only">Open main menu</span>
          <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
          <svg class="hidden h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <div *ngIf="isMenuOpen" class="md:hidden" id="mobile-menu" clickOutside (clickOutside)="closeMenu()">
    <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
      <a routerLink="/projects" routerLinkActive="bg-blue-700" class="text-white block px-3 py-2 rounded-md text-base font-medium">Projects</a>
      <a routerLink="/workflows" routerLinkActive="bg-blue-700" class="text-white block px-3 py-2 rounded-md text-base font-medium">Workflows</a>
      <ng-container *ngIf="isAdmin()">
        <a routerLink="/admin/idp-config" routerLinkActive="bg-blue-700" class="text-white block px-3 py-2 rounded-md text-base font-medium">Identity Providers</a>
        <a routerLink="/admin/group-mapping" routerLinkActive="bg-blue-700" class="text-white block px-3 py-2 rounded-md text-base font-medium">Group Mapping</a>
      </ng-container>
    </div>
    <div class="pt-4 pb-3 border-t border-blue-700">
      <div class="flex items-center px-5">
        <div class="flex-shrink-0">
          <span class="h-10 w-10 rounded-full bg-blue-700 flex items-center justify-center text-white">
            {{ currentUser?.username ? (currentUser?.username?.[0] || '').toUpperCase() : 'U' }}
          </span>
        </div>
        <div class="ml-3">
          <div class="text-base font-medium leading-none text-white">{{ currentUser?.username }}</div>
        </div>
      </div>
      <div class="mt-3 px-2 space-y-1">
        <a (click)="logout()" class="block px-3 py-2 rounded-md text-base font-medium text-white hover:bg-blue-700 cursor-pointer">Sign out</a>
      </div>
    </div>
  </div>
</nav>
