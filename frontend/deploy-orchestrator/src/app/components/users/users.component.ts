import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AdminService } from '../../services/admin.service';
import { ProjectService } from '../../services/project.service';
import { AuthService } from '../../services/auth.service';
import { User, Role } from '../../models/user.model';
import { Project } from '../../models/project.model';

@Component({
  selector: 'app-users',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.scss']
})
export class UsersComponent implements OnInit {
  users: User[] = [];
  roles: Role[] = [];
  projects: Project[] = [];
  userForm!: FormGroup;
  userRoleForm!: FormGroup;
  loading = false;
  creating = false;
  assigningRole = false;
  error = '';
  success = '';
  showCreateModal = false;
  showRoleAssignmentModal = false;
  selectedUser: User | null = null;
  userRoles: any[] = [];

  constructor(
    private adminService: AdminService,
    private projectService: ProjectService,
    private authService: AuthService,
    private formBuilder: FormBuilder
  ) { }

  ngOnInit(): void {
    this.initForms();
    this.loadUsers();
    this.loadRoles();
    this.loadProjects();
  }

  initForms(): void {
    this.userForm = this.formBuilder.group({
      username: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      firstName: [''],
      lastName: [''],
      isAdmin: [false]
    });

    this.userRoleForm = this.formBuilder.group({
      roleId: ['', Validators.required],
      projectId: [''] // Optional for global roles
    });
  }

  loadUsers(): void {
    this.loading = true;
    this.adminService.getUsers().subscribe({
      next: (users) => {
        this.users = users;
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load users. Please try again.';
        console.error('Error loading users', error);
        this.loading = false;
      }
    });
  }

  loadRoles(): void {
    this.adminService.getRoles().subscribe({
      next: (roles) => {
        this.roles = roles;
      },
      error: (error) => {
        console.error('Error loading roles', error);
      }
    });
  }

  loadProjects(): void {
    this.projectService.getProjects().subscribe({
      next: (projects) => {
        this.projects = projects;
      },
      error: (error) => {
        console.error('Error loading projects', error);
      }
    });
  }

  openCreateModal(): void {
    this.showCreateModal = true;
    this.userForm.reset();
    this.userForm.patchValue({ isAdmin: false });
    this.error = '';
    this.success = '';
  }

  closeCreateModal(): void {
    this.showCreateModal = false;
    this.userForm.reset();
  }

  createUser(): void {
    if (this.userForm.invalid) {
      return;
    }

    this.creating = true;
    const userData = this.userForm.value;

    this.adminService.createUser(userData).subscribe({
      next: (user) => {
        this.success = 'User created successfully.';
        this.error = '';
        this.creating = false;
        this.closeCreateModal();
        this.loadUsers();
      },
      error: (error) => {
        this.error = 'Failed to create user. Please try again.';
        console.error('Error creating user', error);
        this.creating = false;
      }
    });
  }

  openRoleAssignmentModal(user: User): void {
    this.selectedUser = user;
    this.showRoleAssignmentModal = true;
    this.userRoleForm.reset();
    this.loadUserRoles();
    this.error = '';
    this.success = '';
  }

  closeRoleAssignmentModal(): void {
    this.showRoleAssignmentModal = false;
    this.selectedUser = null;
    this.userRoles = [];
    this.userRoleForm.reset();
  }

  loadUserRoles(): void {
    if (!this.selectedUser) return;

    this.adminService.getUserRoles(this.selectedUser.id).subscribe({
      next: (roles) => {
        this.userRoles = roles.map(role => ({
          roleId: role.id,
          roleName: role.name,
          projectId: null, // This would need to be enhanced for project-specific roles
          projectName: null
        }));
      },
      error: (error) => {
        console.error('Error loading user roles', error);
        this.userRoles = [];
      }
    });
  }

  assignRoleToUser(): void {
    if (this.userRoleForm.invalid || !this.selectedUser) {
      return;
    }

    this.assigningRole = true;
    const { roleId, projectId } = this.userRoleForm.value;

    this.adminService.assignRoleToUser(this.selectedUser.id, roleId, projectId || undefined).subscribe({
      next: () => {
        this.success = 'Role assigned to user successfully.';
        this.error = '';
        this.assigningRole = false;
        this.userRoleForm.reset();
        this.loadUserRoles();
      },
      error: (error) => {
        this.error = 'Failed to assign role to user. Please try again.';
        console.error('Error assigning role to user', error);
        this.assigningRole = false;
      }
    });
  }

  removeRoleFromUser(assignment: any): void {
    if (!this.selectedUser) return;

    this.adminService.removeRoleFromUser(this.selectedUser.id, assignment.roleId, assignment.projectId).subscribe({
      next: () => {
        this.success = 'Role removed from user successfully.';
        this.error = '';
        this.loadUserRoles();
      },
      error: (error) => {
        this.error = 'Failed to remove role from user. Please try again.';
        console.error('Error removing role from user', error);
      }
    });
  }

  toggleUserStatus(user: User): void {
    const newStatus = !user.isActive;
    const action = newStatus ? 'activate' : 'deactivate';

    this.adminService.updateUser(user.id, { isActive: newStatus }).subscribe({
      next: () => {
        this.success = `User ${action}d successfully.`;
        this.error = '';
        this.loadUsers();
      },
      error: (error) => {
        this.error = `Failed to ${action} user. Please try again.`;
        console.error(`Error ${action}ing user`, error);
      }
    });
  }

  deleteUser(user: User): void {
    if (confirm(`Are you sure you want to delete the user "${user.username}"?`)) {
      this.adminService.deleteUser(user.id).subscribe({
        next: () => {
          this.success = 'User deleted successfully.';
          this.error = '';
          this.loadUsers();
        },
        error: (error) => {
          this.error = 'Failed to delete user. Please try again.';
          console.error('Error deleting user', error);
        }
      });
    }
  }

  getStatusColor(isActive: boolean): string {
    return isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  }

  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  getRoleName(roleId: string): string {
    const role = this.roles.find(r => r.id === roleId);
    return role ? role.name : 'Unknown Role';
  }

  getProjectName(projectId?: string): string {
    if (!projectId) return 'Global';
    const project = this.projects.find(p => p.id === projectId);
    return project ? project.name : 'Unknown Project';
  }
}
