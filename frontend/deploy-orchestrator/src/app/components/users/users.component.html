<div class="px-4 py-5 sm:px-6">
  <div class="flex items-center justify-between">
    <div>
      <h3 class="text-lg leading-6 font-medium text-gray-900">Users</h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500">Manage system users and their permissions</p>
    </div>
    <button (click)="openCreateModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
      <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
      </svg>
      New User
    </button>
  </div>
</div>

<div *ngIf="error" class="bg-red-50 border-l-4 border-red-400 p-4 mx-4 my-2">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
      </svg>
    </div>
    <div class="ml-3">
      <p class="text-sm text-red-700">{{ error }}</p>
    </div>
  </div>
</div>

<div *ngIf="success" class="bg-green-50 border-l-4 border-green-400 p-4 mx-4 my-2">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
      </svg>
    </div>
    <div class="ml-3">
      <p class="text-sm text-green-700">{{ success }}</p>
    </div>
  </div>
</div>

<div class="border-t border-gray-200">
  <div *ngIf="loading" class="flex justify-center py-6">
    <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
  </div>

  <div *ngIf="!loading && users.length === 0" class="text-center py-10">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900">No users</h3>
    <p class="mt-1 text-sm text-gray-500">Get started by creating a new user.</p>
    <div class="mt-6">
      <button (click)="openCreateModal()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        New User
      </button>
    </div>
  </div>

  <div *ngIf="!loading && users.length > 0" class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr *ngFor="let user of users">
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <div class="flex-shrink-0 h-10 w-10">
                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                  <span class="text-sm font-medium text-gray-700">{{ (user.firstName || user.username).charAt(0).toUpperCase() }}</span>
                </div>
              </div>
              <div class="ml-4">
                <div class="text-sm font-medium text-gray-900">{{ user.firstName }} {{ user.lastName }}</div>
                <div class="text-sm text-gray-500">{{ user.username }}</div>
              </div>
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900">{{ user.email }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span *ngIf="user.isAdmin" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
              Admin
            </span>
            <span *ngIf="!user.isAdmin" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
              User
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span [ngClass]="getStatusColor(user.isActive)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
              {{ getStatusText(user.isActive) }}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            {{ user.lastLogin ? (user.lastLogin | date:'short') : 'Never' }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
            <button (click)="openRoleAssignmentModal(user)" class="text-blue-600 hover:text-blue-900 mr-3">
              Manage Roles
            </button>
            <button (click)="toggleUserStatus(user)"
                    [class]="user.isActive ? 'text-yellow-600 hover:text-yellow-900' : 'text-green-600 hover:text-green-900'"
                    class="mr-3">
              {{ user.isActive ? 'Deactivate' : 'Activate' }}
            </button>
            <button (click)="deleteUser(user)" class="text-red-600 hover:text-red-900">
              Delete
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<!-- Create User Modal -->
<div *ngIf="showCreateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Create New User</h3>
        <button (click)="closeCreateModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <form [formGroup]="userForm" (ngSubmit)="createUser()">
        <div class="grid grid-cols-1 gap-6">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label for="firstName" class="block text-sm font-medium text-gray-700">First Name</label>
              <input type="text" id="firstName" formControlName="firstName" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            </div>
            <div>
              <label for="lastName" class="block text-sm font-medium text-gray-700">Last Name</label>
              <input type="text" id="lastName" formControlName="lastName" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            </div>
          </div>

          <div>
            <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
            <input type="text" id="username" formControlName="username" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            <div *ngIf="userForm.get('username')?.invalid && (userForm.get('username')?.dirty || userForm.get('username')?.touched)" class="text-red-500 text-xs mt-1">
              <div *ngIf="userForm.get('username')?.errors?.['required']">Username is required.</div>
            </div>
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
            <input type="email" id="email" formControlName="email" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            <div *ngIf="userForm.get('email')?.invalid && (userForm.get('email')?.dirty || userForm.get('email')?.touched)" class="text-red-500 text-xs mt-1">
              <div *ngIf="userForm.get('email')?.errors?.['required']">Email is required.</div>
              <div *ngIf="userForm.get('email')?.errors?.['email']">Please enter a valid email address.</div>
            </div>
          </div>

          <div class="flex items-center">
            <input id="isAdmin" type="checkbox" formControlName="isAdmin" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="isAdmin" class="ml-2 block text-sm text-gray-900">
              Administrator privileges
            </label>
          </div>
        </div>

        <div class="mt-6 flex justify-end space-x-3">
          <button type="button" (click)="closeCreateModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
            Cancel
          </button>
          <button type="submit" [disabled]="userForm.invalid || creating" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
            <span *ngIf="creating" class="mr-2">
              <svg class="animate-spin h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            Create User
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Role Assignment Modal -->
<div *ngIf="showRoleAssignmentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">
          Manage Roles for {{ selectedUser?.username }}
        </h3>
        <button (click)="closeRoleAssignmentModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Assign New Role -->
      <div class="mb-6">
        <h4 class="text-md font-medium text-gray-900 mb-3">Assign Role</h4>
        <form [formGroup]="userRoleForm" (ngSubmit)="assignRoleToUser()">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label for="roleSelect" class="block text-sm font-medium text-gray-700">Role</label>
              <select id="roleSelect" formControlName="roleId" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                <option value="">Select Role</option>
                <option *ngFor="let role of roles" [value]="role.id">{{ role.name }}</option>
              </select>
            </div>
            <div>
              <label for="projectSelect" class="block text-sm font-medium text-gray-700">Project (Optional)</label>
              <select id="projectSelect" formControlName="projectId" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                <option value="">Global Role</option>
                <option *ngFor="let project of projects" [value]="project.id">{{ project.name }}</option>
              </select>
            </div>
            <div class="flex items-end">
              <button type="submit" [disabled]="userRoleForm.invalid || assigningRole" class="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <span *ngIf="assigningRole" class="mr-2">
                  <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </span>
                Assign Role
              </button>
            </div>
          </div>
        </form>
      </div>

      <!-- Current Role Assignments -->
      <div>
        <h4 class="text-md font-medium text-gray-900 mb-3">Current Role Assignments</h4>
        <div *ngIf="userRoles.length === 0" class="text-sm text-gray-500 italic">
          No role assignments found for this user.
        </div>
        <div *ngIf="userRoles.length > 0" class="space-y-2">
          <div *ngFor="let assignment of userRoles" class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
            <div>
              <span class="font-medium">{{ assignment.roleName }}</span>
              <span *ngIf="assignment.projectName" class="text-sm text-gray-500 ml-2">
                (Project: {{ assignment.projectName }})
              </span>
              <span *ngIf="!assignment.projectName" class="text-sm text-gray-500 ml-2">
                (Global)
              </span>
            </div>
            <button (click)="removeRoleFromUser(assignment)" class="text-red-600 hover:text-red-900 text-sm">
              Remove
            </button>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="mt-6 flex justify-end">
        <button (click)="closeRoleAssignmentModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
          Close
        </button>
      </div>
    </div>
  </div>
</div>