import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil, combineLatest, forkJoin, of } from 'rxjs';
import {catchError, take, timeout, tap} from 'rxjs/operators';

import { ProjectService } from '../../services/project.service';
import { ApplicationService } from '../../services/application.service';
import { EnvironmentService } from '../../services/environment.service';
import { WorkflowService } from '../../services/workflow.service';
import { DeploymentManagementService } from '../../services/deployment-management.service';
import { NotificationService } from '../../services/notification.service';

@Component({
  selector: 'app-deployments',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule
  ],
  templateUrl: './deployments.component.html',
  styleUrls: ['./deployments.component.scss']
})
export class DeploymentsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Current project from header selector
  currentProjectId: string = '';
  
  // Active tab
  activeTab: 'deploy' | 'history' | 'matrix' | 'promotions' = 'deploy';
  
  // Loading states
  loading = false;
  deploying = false;
  
  // Data
  applications: any[] = [];
  environments: any[] = [];
  workflows: any[] = [];
  deployments: any[] = [];
  versionMatrix: any = {};
  
  // Forms
  deploymentForm!: FormGroup;
  
  // Selected items
  selectedApplications: any[] = [];
  selectedEnvironment: any = null;
  selectedWorkflow: any = null;

  // Filtering
  applicationSearchTerm = '';
  selectedApplicationType = '';
  filteredApplications: any[] = [];

  environmentSearchTerm = '';
  selectedEnvironmentType = '';
  filteredEnvironments: any[] = [];

  // Workflow parameters
  workflowParameters: { [key: string]: any } = {};
  
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private projectService: ProjectService,
    private applicationService: ApplicationService,
    private environmentService: EnvironmentService,
    private workflowService: WorkflowService,
    private deploymentService: DeploymentManagementService,
    private notificationService: NotificationService
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.initializeForm();

    // Check for tab parameter in URL
    this.route.queryParams.pipe(
      takeUntil(this.destroy$)
    ).subscribe(params => {
      if (params['tab'] && ['deploy', 'history', 'matrix', 'promotions'].includes(params['tab'])) {
        this.activeTab = params['tab'];
      }
    });

    // Listen to project changes from header
    console.log('🔗 Setting up project subscription in deployments component...');
    this.projectService.selectedProject$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(project => {
      console.log('📡 Deployments component received project:', project);
      if (project?.id) {
        console.log('✅ Project selected, loading data for:', project.name, '(ID:', project.id, ')');
        this.currentProjectId = project.id;
        this.loadProjectData();
      } else {
        console.log('⚠️ No project selected, clearing data');
        // No project selected, clear data
        this.currentProjectId = '';
        this.applications = [];
        this.environments = [];
        this.workflows = [];
        this.deployments = [];
        this.versionMatrix = {};
        this.loading = false;
      }
    });

    // Also try to get the current project immediately
    const currentProject = this.projectService.getSelectedProject();
    if (currentProject?.id) {
      this.currentProjectId = currentProject.id;
      this.loadProjectData();
    }
    // Note: If no project is selected initially, we let the projects$ subscription handle the loading state

    // Also check if projects are available to stop loading when projects are loaded but none selected
    this.projectService.projects$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(projects => {
      console.log('📋 Available projects:', projects.length, 'projects');
      console.log('📋 Projects list:', projects.map(p => ({ id: p.id, name: p.name })));
      // If projects are loaded but no project is selected, stop loading
      if (projects.length > 0 && !this.currentProjectId) {
        console.log('🔄 Projects loaded but none selected, stopping loading');
        this.loading = false;
      } else if (projects.length === 0) {
        console.log('⚠️ No projects available for user');
        this.loading = false;
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.deploymentForm = this.fb.group({
      version: ['', Validators.required],
      workflowId: ['', Validators.required],
      providerType: ['helm', Validators.required],
      strategy: ['rolling_update'],
      parameters: this.fb.group({}),
      secretMappings: this.fb.group({}),
      configuration: this.fb.group({})
    });
  }

  private loadProjectData(): void {
    if (!this.currentProjectId) {
      console.log('No project ID, skipping data load');
      return;
    }

    console.log('🔄 Loading project data for:', this.currentProjectId);
    this.loading = true;

    // Load all data in parallel using forkJoin
    console.log('🔄 Creating applications$ observable...');
    const applications$ = this.applicationService.getApplications({ projectId: this.currentProjectId }).pipe(
      take(1),
      tap(() => console.log('✅ Applications request completed')),
      catchError(error => {
        console.error('❌ Failed to load applications:', error);
        return of({ deployables: [], page: 1, pageSize: 50, total: 0 });
      })
    );

    console.log('🔄 Creating environments$ observable...');
    const environments$ = this.environmentService.getEnvironments({ projectId: this.currentProjectId }).pipe(
      take(1),
      tap(() => console.log('✅ Environments request completed')),
      catchError(error => {
        console.error('❌ Failed to load environments:', error);
        return of({ environments: [], page: 1, pageSize: 50, total: 0 });
      })
    );

    console.log('🔄 Creating workflows$ observable...');
    const workflows$ = this.workflowService.getWorkflows(this.currentProjectId).pipe(
      take(1),
      tap(() => console.log('✅ Workflows request completed')),
      catchError(error => {
        console.error('❌ Failed to load workflows:', error);
        return of([]);
      })
    );

    console.log('🔄 Starting parallel data loading...');

    console.log('🔄 Creating forkJoin with all observables...');
    forkJoin({
      applications: applications$,
      environments: environments$,
      workflows: workflows$
    }).pipe(
      tap(() => console.log('🎯 ForkJoin about to emit result')),
      timeout(10000), // 10 second timeout (reduced for faster feedback)
      takeUntil(this.destroy$)
    ).subscribe({
      next: (results) => {
        console.log('✅ All data loaded successfully');
        this.applications = results.applications?.deployables || [];
        this.environments = results.environments?.environments || [];
        this.workflows = results.workflows || [];

        // Initialize filtered arrays
        this.filteredApplications = [...this.applications];
        this.filteredEnvironments = [...this.environments];

        console.log('📊 Data summary:');
        console.log('  - Applications:', this.applications.length);
        console.log('  - Environments:', this.environments.length);
        console.log('  - Workflows:', this.workflows.length);

        this.loading = false;
        console.log('🔄 Loading completed, loading set to false');
      },
      error: (error) => {
        console.error('❌ Failed to load project data:', error);
        console.error('❌ Error details:', {
          message: error.message,
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          name: error.name
        });
        this.loading = false;
        console.log('🔄 Loading failed, loading set to false');
      }
    });

    // Load additional data based on active tab
    if (this.activeTab === 'history') {
      this.loadDeploymentHistory();
    } else if (this.activeTab === 'matrix') {
      this.loadVersionMatrix();
    }
  }

  private loadDeploymentHistory(): void {
    if (!this.currentProjectId) return;

    console.log('🔄 Loading deployment history...');
    this.deploymentService.getEnvironmentVersions(
      this.currentProjectId,
      this.environments.map(env => env.id)
    ).pipe(
      catchError(error => {
        console.error('❌ Failed to load deployment history:', error);
        return of({ versions: [] });
      }),
      takeUntil(this.destroy$)
    ).subscribe(response => {
      this.deployments = response?.versions || [];
      console.log('✅ Deployment history loaded:', this.deployments.length);
    });
  }

  private loadVersionMatrix(): void {
    if (!this.currentProjectId) return;

    console.log('🔄 Loading version matrix...');
    this.deploymentService.getVersionMatrix(this.currentProjectId).pipe(
      catchError(error => {
        console.error('❌ Failed to load version matrix:', error);
        return of({ matrix: {} });
      }),
      takeUntil(this.destroy$)
    ).subscribe(response => {
      this.versionMatrix = response?.matrix || {};
      console.log('✅ Version matrix loaded');
    });
  }

  // Tab management
  switchTab(tab: 'deploy' | 'history' | 'matrix' | 'promotions'): void {
    this.activeTab = tab;
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { tab },
      queryParamsHandling: 'merge'
    });

    // Load data for the new tab
    if (tab === 'history') {
      this.loadDeploymentHistory();
    } else if (tab === 'matrix') {
      this.loadVersionMatrix();
    }
  }

  // Application selection
  toggleApplicationSelection(app: any): void {
    const index = this.selectedApplications.findIndex(a => a.id === app.id);
    if (index > -1) {
      this.selectedApplications.splice(index, 1);
    } else {
      this.selectedApplications.push(app);
    }
  }

  isApplicationSelected(appId: string): boolean {
    return this.selectedApplications.some(app => app.id === appId);
  }

  // Environment selection
  selectEnvironment(env: any): void {
    this.selectedEnvironment = env;
  }

  isEnvironmentSelected(envId: string): boolean {
    return this.selectedEnvironment?.id === envId;
  }

  // Deployment
  async onDeploy(): Promise<void> {
    if (!this.deploymentForm.valid || this.selectedApplications.length === 0 || !this.selectedEnvironment) {
      this.notificationService.error('Please fill all required fields and select applications and environment');
      return;
    }

    this.deploying = true;
    try {
      const formValue = this.deploymentForm.value;

      // Create bulk deployment request (works for single or multiple applications)
      const deploymentRequest = {
        applicationIds: this.selectedApplications.map(app => app.id),
        projectId: this.currentProjectId,
        environmentId: this.selectedEnvironment.id,
        version: formValue.version || 'latest',
        providerType: formValue.providerType || 'helm',
        workflowId: formValue.workflowId,
        parameters: this.workflowParameters,
        configuration: {
          strategy: formValue.strategy || 'rolling_update',
          rollbackOnFailure: true,
          runHealthChecks: true,
          ...this.workflowParameters
        },
        description: `Deploying ${this.selectedApplications.length} application(s) to ${this.selectedEnvironment.name}`
      };

      console.log('Creating deployment:', deploymentRequest);
      const response = await this.deploymentService.createDeployment(deploymentRequest).toPromise();

      this.notificationService.success('Deployment created successfully', 'Redirecting to monitoring...');

      // Navigate to execution monitoring
      this.router.navigate(['/execution-monitoring'], {
        queryParams: { deploymentId: response?.deploymentId }
      });

    } catch (error) {
      console.error('Failed to create deployment:', error);
      this.notificationService.error('Failed to create deployment', 'Please try again');
    } finally {
      this.deploying = false;
    }
  }

  // Utility methods
  getApplicationsByType(type: string): any[] {
    return this.applications.filter(app => app.type === type);
  }

  getEnvironmentsByType(type: string): any[] {
    return this.environments.filter(env => env.type === type);
  }

  getDeploymentStatusClass(status: string): string {
    const statusClasses: { [key: string]: string } = {
      'pending': 'bg-yellow-100 text-yellow-800',
      'in_progress': 'bg-blue-100 text-blue-800',
      'succeeded': 'bg-green-100 text-green-800',
      'failed': 'bg-red-100 text-red-800',
      'rolled_back': 'bg-gray-100 text-gray-800'
    };
    return statusClasses[status] || 'bg-gray-100 text-gray-800';
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  formatTime(dateString: string): string {
    return new Date(dateString).toLocaleTimeString();
  }

  // Version matrix helpers
  getMatrixApplications(): string[] {
    return Object.keys(this.versionMatrix);
  }

  getMatrixEnvironments(): string[] {
    const envs = new Set<string>();
    Object.values(this.versionMatrix).forEach((appVersions: any) => {
      Object.keys(appVersions).forEach(env => envs.add(env));
    });
    return Array.from(envs);
  }

  getVersionForAppInEnv(app: string, env: string): string {
    return this.versionMatrix[app]?.[env] || '-';
  }

  canPromoteVersion(app: string, fromEnv: string, toEnv: string): boolean {
    const fromVersion = this.getVersionForAppInEnv(app, fromEnv);
    const toVersion = this.getVersionForAppInEnv(app, toEnv);
    return fromVersion !== '-' && fromVersion !== toVersion;
  }

  promoteVersion(app: string, fromEnv: string, toEnv: string): void {
    // Implementation for promotion
    console.log(`Promoting ${app} from ${fromEnv} to ${toEnv}`);
  }

  // Filtering methods
  filterApplications(): void {
    this.filteredApplications = this.applications.filter(app => {
      const matchesSearch = !this.applicationSearchTerm ||
        app.name.toLowerCase().includes(this.applicationSearchTerm.toLowerCase()) ||
        (app.description && app.description.toLowerCase().includes(this.applicationSearchTerm.toLowerCase()));

      const matchesType = !this.selectedApplicationType || app.type === this.selectedApplicationType;

      return matchesSearch && matchesType;
    });
  }

  filterEnvironments(): void {
    this.filteredEnvironments = this.environments.filter(env => {
      const matchesSearch = !this.environmentSearchTerm ||
        env.name.toLowerCase().includes(this.environmentSearchTerm.toLowerCase()) ||
        (env.description && env.description.toLowerCase().includes(this.environmentSearchTerm.toLowerCase()));

      const matchesType = !this.selectedEnvironmentType || env.type === this.selectedEnvironmentType;

      return matchesSearch && matchesType;
    });
  }

  // Workflow methods
  onWorkflowChange(event: any): void {
    const workflowId = event.target.value;
    this.selectedWorkflow = this.workflows.find(w => w.id === workflowId) || null;
    this.workflowParameters = {};
  }

  getWorkflowParameterValue(paramName: string): any {
    return this.workflowParameters[paramName] || '';
  }

  setWorkflowParameterValue(paramName: string, event: any): void {
    const value = event.target ? event.target.value : event.target.checked;
    this.workflowParameters[paramName] = value;
  }

  // Validation
  canDeploy(): boolean {
    return this.selectedApplications.length > 0 &&
           this.selectedEnvironment &&
           this.deploymentForm.valid &&
           !this.deploying &&
           !this.hasInvalidWorkflowParameters();
  }

  // Workflow parameter validation methods
  isParameterInvalid(param: any): boolean {
    if (!param.required) return false;
    const value = this.getWorkflowParameterValue(param.name);
    return !value || (typeof value === 'string' && value.trim() === '');
  }

  hasInvalidWorkflowParameters(): boolean {
    if (!this.selectedWorkflow?.parameters) return false;
    return this.selectedWorkflow.parameters.some((param: any) => this.isParameterInvalid(param));
  }

  getParameterInputClass(param: any): string {
    if (this.isParameterInvalid(param)) {
      return 'border-red-300 focus:ring-red-500 focus:border-red-500';
    }
    return 'border-gray-300 focus:ring-blue-500 focus:border-blue-500';
  }

  getValidParametersCount(): number {
    if (!this.selectedWorkflow?.parameters) return 0;
    return this.selectedWorkflow.parameters.filter((param: any) => {
      const value = this.getWorkflowParameterValue(param.name);
      return value && (typeof value !== 'string' || value.trim() !== '');
    }).length;
  }

  getRequiredParametersCount(): number {
    if (!this.selectedWorkflow?.parameters) return 0;
    return this.selectedWorkflow.parameters.filter((param: any) => param.required).length;
  }

  trackByParameterName(index: number, param: any): string {
    return param.name;
  }
}
