.deployments-container {
  @apply h-full flex flex-col bg-gray-50;

  /* Tab styling */
  .tab-button {
    @apply py-2 px-1 border-b-2 font-medium text-sm transition-colors;
    
    &.active {
      @apply border-blue-500 text-blue-600;
    }
    
    &.inactive {
      @apply border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300;
    }
  }

  /* Selection cards */
  .selection-card {
    @apply border rounded-lg p-4 cursor-pointer transition-all duration-200;
    
    &:hover {
      @apply shadow-md;
    }
    
    &.selected {
      @apply border-blue-500 bg-blue-50 shadow-sm;
    }
    
    &.unselected {
      @apply border-gray-200 hover:border-gray-300;
    }
  }

  /* Form styling */
  .form-field {
    @apply space-y-2;
    
    label {
      @apply block text-sm font-medium text-gray-700;
    }
    
    input, select, textarea {
      @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
      
      &:disabled {
        @apply bg-gray-100 cursor-not-allowed;
      }
    }
  }

  /* Status badges */
  .status-badge {
    @apply inline-flex px-2 py-1 text-xs font-semibold rounded-full;
    
    &.pending {
      @apply bg-yellow-100 text-yellow-800;
    }
    
    &.in-progress {
      @apply bg-blue-100 text-blue-800;
    }
    
    &.succeeded {
      @apply bg-green-100 text-green-800;
    }
    
    &.failed {
      @apply bg-red-100 text-red-800;
    }
    
    &.rolled-back {
      @apply bg-gray-100 text-gray-800;
    }
  }

  /* Loading states */
  .loading-spinner {
    @apply animate-spin h-8 w-8 text-blue-600;
  }

  .loading-container {
    @apply flex items-center justify-center h-64;
  }

  /* Empty states */
  .empty-state {
    @apply text-center py-8;
    
    .icon {
      @apply mx-auto h-12 w-12 text-gray-400;
    }
    
    .title {
      @apply mt-2 text-sm font-medium text-gray-900;
    }
    
    .description {
      @apply mt-1 text-sm text-gray-500;
    }
  }

  // Table styling
  .data-table {
    @apply min-w-full divide-y divide-gray-200;
    
    thead {
      @apply bg-gray-50;
      
      th {
        @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
      }
    }
    
    tbody {
      @apply bg-white divide-y divide-gray-200;
      
      td {
        @apply px-6 py-4 whitespace-nowrap text-sm;
        
        &.primary {
          @apply font-medium text-gray-900;
        }
        
        &.secondary {
          @apply text-gray-500;
        }
      }
    }
  }

  // Button styling
  .btn {
    @apply px-4 py-2 rounded-md font-medium text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
    
    &.primary {
      @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
      
      &:disabled {
        @apply opacity-50 cursor-not-allowed;
      }
    }
    
    &.secondary {
      @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-blue-500;
    }
    
    &.danger {
      @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
    }
  }

  // Panel layout
  .panel {
    @apply bg-white border-r border-gray-200;
    
    .panel-header {
      @apply p-6 border-b border-gray-200;
      
      h3 {
        @apply text-lg font-medium text-gray-900;
      }
    }
    
    .panel-content {
      @apply p-6;
    }
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .flex-container {
      @apply flex-col;

      .panel-third {
        @apply w-full;
      }
    }

    .data-table {
      @apply text-xs;

      th, td {
        @apply px-3 py-2;
      }
    }
  }

  // Animation classes
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .slide-in {
    animation: slideIn 0.3s ease-in-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// Custom scrollbar
.deployments-container {
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}
