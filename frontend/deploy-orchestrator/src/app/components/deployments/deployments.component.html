<div class="deployments-container">
  <!-- Header -->
  <div class="bg-white shadow-sm border-b border-gray-200">
    <div class="px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Deployments</h1>
          <p class="text-sm text-gray-600 mt-1" *ngIf="currentProjectId">
            Managing deployments for current project
          </p>
          <p class="text-sm text-yellow-600 mt-1" *ngIf="!currentProjectId">
            Please select a project from the header to continue
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <button 
            *ngIf="activeTab === 'deploy'"
            (click)="onDeploy()"
            [disabled]="!currentProjectId || deploying || selectedApplications.length === 0 || !selectedEnvironment"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed">
            <span *ngIf="!deploying">Deploy Selected</span>
            <span *ngIf="deploying" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Deploying...
            </span>
          </button>
        </div>
      </div>
    </div>

    <!-- Tabs -->
    <div class="border-b border-gray-200">
      <nav class="px-6 -mb-px flex space-x-8">
        <button
          (click)="switchTab('deploy')"
          [class]="activeTab === 'deploy' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          class="py-2 px-1 border-b-2 font-medium text-sm">
          Deploy
        </button>
        <button
          (click)="switchTab('history')"
          [class]="activeTab === 'history' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          class="py-2 px-1 border-b-2 font-medium text-sm">
          History
        </button>
        <button
          (click)="switchTab('matrix')"
          [class]="activeTab === 'matrix' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          class="py-2 px-1 border-b-2 font-medium text-sm">
          Version Matrix
        </button>
        <button
          (click)="switchTab('promotions')"
          [class]="activeTab === 'promotions' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          class="py-2 px-1 border-b-2 font-medium text-sm">
          Promotions
        </button>
      </nav>
    </div>
  </div>

  <!-- Content -->
  <div class="flex-1 overflow-hidden" *ngIf="currentProjectId">
    <!-- Loading State -->
    <div *ngIf="loading" class="flex items-center justify-center h-64">
      <div class="text-center">
        <svg class="animate-spin h-8 w-8 text-blue-600 mx-auto" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <p class="text-gray-600 mt-2">Loading project data...</p>
      </div>
    </div>

    <!-- Deploy Tab -->
    <div *ngIf="!loading && activeTab === 'deploy'" class="h-full flex">
      <!-- Left Panel - Applications -->
      <div class="w-1/3 border-r border-gray-200 bg-white">
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Select Applications</h3>
          <div class="space-y-3">
            <div *ngFor="let app of applications" 
                 (click)="toggleApplicationSelection(app)"
                 [class]="isApplicationSelected(app.id) ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'"
                 class="border rounded-lg p-4 cursor-pointer transition-colors">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <h4 class="font-medium text-gray-900">{{ app.name }}</h4>
                  <p class="text-sm text-gray-600">{{ app.description }}</p>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mt-2">
                    {{ app.type }}
                  </span>
                </div>
                <div class="ml-3">
                  <input type="checkbox" 
                         [checked]="isApplicationSelected(app.id)"
                         class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Middle Panel - Configuration -->
      <div class="w-1/3 border-r border-gray-200 bg-white">
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Deployment Configuration</h3>
          <form [formGroup]="deploymentForm" class="space-y-4">
            <!-- Version -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Version</label>
              <input type="text" formControlName="version" placeholder="e.g., v1.0.0"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>

            <!-- Workflow -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Workflow</label>
              <select formControlName="workflowId"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="">Select workflow</option>
                <option *ngFor="let workflow of workflows" [value]="workflow.id">
                  {{ workflow.name }}
                </option>
              </select>
            </div>

            <!-- Provider -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Provider</label>
              <select formControlName="providerType"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="helm">Helm</option>
                <option value="kubernetes">Kubernetes</option>
                <option value="openshift">OpenShift</option>
                <option value="docker">Docker</option>
              </select>
            </div>

            <!-- Strategy -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Strategy</label>
              <select formControlName="strategy"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="rolling_update">Rolling Update</option>
                <option value="blue_green">Blue-Green</option>
                <option value="canary">Canary</option>
                <option value="recreate">Recreate</option>
              </select>
            </div>
          </form>
        </div>
      </div>

      <!-- Right Panel - Environments -->
      <div class="w-1/3 bg-white">
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Select Environment</h3>
          <div class="space-y-3">
            <div *ngFor="let env of environments" 
                 (click)="selectEnvironment(env)"
                 [class]="isEnvironmentSelected(env.id) ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'"
                 class="border rounded-lg p-4 cursor-pointer transition-colors">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <h4 class="font-medium text-gray-900">{{ env.name }}</h4>
                  <p class="text-sm text-gray-600">{{ env.description }}</p>
                  <div class="flex items-center mt-2 space-x-2">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {{ env.type }}
                    </span>
                    <span [class]="env.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium">
                      {{ env.status }}
                    </span>
                  </div>
                </div>
                <div class="ml-3">
                  <input type="radio" 
                         [checked]="isEnvironmentSelected(env.id)"
                         class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- History Tab -->
    <div *ngIf="!loading && activeTab === 'history'" class="p-6">
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Deployment History</h3>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Application</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Environment</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Version</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deployed</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">By</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr *ngFor="let deployment of deployments">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ deployment.application?.name || deployment.component?.name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ deployment.environmentId }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ deployment.version }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span [class]="getDeploymentStatusClass(deployment.status)"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                    {{ deployment.status }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(deployment.deployedAt) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ deployment.deployedBy }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Version Matrix Tab -->
    <div *ngIf="!loading && activeTab === 'matrix'" class="p-6">
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Version Matrix</h3>
          <p class="text-sm text-gray-600 mt-1">Current versions deployed across environments</p>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Application</th>
                <th *ngFor="let env of getMatrixEnvironments()" 
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ env }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr *ngFor="let app of getMatrixApplications()">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ app }}
                </td>
                <td *ngFor="let env of getMatrixEnvironments()" 
                    class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                    {{ getVersionForAppInEnv(app, env) }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Promotions Tab -->
    <div *ngIf="!loading && activeTab === 'promotions'" class="p-6">
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Environment Promotions</h3>
          <p class="text-sm text-gray-600 mt-1">Promote versions between environments</p>
        </div>
        <div class="p-6">
          <p class="text-gray-500 text-center py-8">Promotion functionality coming soon...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- No Project Selected -->
  <div *ngIf="!currentProjectId" class="flex items-center justify-center h-64">
    <div class="text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No Project Selected</h3>
      <p class="mt-1 text-sm text-gray-500">Please select a project from the header to manage deployments.</p>
    </div>
  </div>
</div>
