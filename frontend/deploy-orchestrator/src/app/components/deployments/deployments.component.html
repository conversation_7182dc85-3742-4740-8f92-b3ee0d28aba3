<div class="deployments-container">
  <!-- Header -->
  <div class="bg-white shadow-sm border-b border-gray-200">
    <div class="px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Deployments</h1>
          <p class="text-sm text-gray-600 mt-1" *ngIf="currentProjectId">
            Managing deployments for current project
          </p>
          <p class="text-sm text-yellow-600 mt-1" *ngIf="!currentProjectId">
            Please select a project from the header to continue
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <button 
            *ngIf="activeTab === 'deploy'"
            (click)="onDeploy()"
            [disabled]="!currentProjectId || deploying || selectedApplications.length === 0 || !selectedEnvironment"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed">
            <span *ngIf="!deploying">Deploy Selected</span>
            <span *ngIf="deploying" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Deploying...
            </span>
          </button>
        </div>
      </div>
    </div>

    <!-- Tabs -->
    <div class="border-b border-gray-200">
      <nav class="px-6 -mb-px flex space-x-8">
        <button
          (click)="switchTab('deploy')"
          [class]="activeTab === 'deploy' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          class="py-2 px-1 border-b-2 font-medium text-sm">
          Deploy
        </button>
        <button
          (click)="switchTab('history')"
          [class]="activeTab === 'history' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          class="py-2 px-1 border-b-2 font-medium text-sm">
          History
        </button>
        <button
          (click)="switchTab('matrix')"
          [class]="activeTab === 'matrix' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          class="py-2 px-1 border-b-2 font-medium text-sm">
          Version Matrix
        </button>
        <button
          (click)="switchTab('promotions')"
          [class]="activeTab === 'promotions' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          class="py-2 px-1 border-b-2 font-medium text-sm">
          Promotions
        </button>
      </nav>
    </div>
  </div>

  <!-- Loading Projects State -->
  <div *ngIf="!currentProjectId && loading" class="flex items-center justify-center h-64">
    <div class="text-center">
      <svg class="animate-spin h-8 w-8 text-blue-600 mx-auto" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <p class="text-gray-600 mt-2">Loading projects...</p>
    </div>
  </div>

  <!-- Content -->
  <div class="flex-1 overflow-hidden" *ngIf="currentProjectId">
    <!-- Loading Project Data State -->
    <div *ngIf="loading" class="flex items-center justify-center h-64">
      <div class="text-center">
        <svg class="animate-spin h-8 w-8 text-blue-600 mx-auto" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <p class="text-gray-600 mt-2">Loading project data...</p>
      </div>
    </div>

    <!-- Deploy Tab -->
    <div *ngIf="!loading && activeTab === 'deploy'" class="p-6 space-y-8">
      <!-- Step 1: Select Applications -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-semibold">1</span>
                </div>
              </div>
              <div class="ml-3">
                <h3 class="text-lg font-semibold text-gray-900">Select Applications</h3>
                <p class="text-sm text-gray-600">Choose which applications to deploy</p>
              </div>
            </div>
            <div class="text-sm text-gray-500">
              {{ selectedApplications.length }} of {{ applications.length }} selected
            </div>
          </div>
        </div>

        <div class="p-6">
          <!-- Search and Filter -->
          <div class="mb-4 flex gap-4">
            <div class="flex-1">
              <input
                type="text"
                [(ngModel)]="applicationSearchTerm"
                (input)="filterApplications()"
                placeholder="Search applications..."
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <select
              [(ngModel)]="selectedApplicationType"
              (change)="filterApplications()"
              class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">All Types</option>
              <option value="web_application">Web Application</option>
              <option value="microservice">Microservice</option>
              <option value="api_gateway">API Gateway</option>
              <option value="database">Database</option>
              <option value="other">Other</option>
            </select>
          </div>

          <!-- Applications Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
            <div *ngFor="let app of filteredApplications"
                 (click)="toggleApplicationSelection(app)"
                 [class]="isApplicationSelected(app.id) ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200' : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'"
                 class="border rounded-lg p-4 cursor-pointer transition-all duration-200">
              <div class="flex items-start justify-between">
                <div class="flex-1 min-w-0">
                  <div class="flex items-center">
                    <input type="checkbox"
                           [checked]="isApplicationSelected(app.id)"
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-3">
                    <h4 class="font-medium text-gray-900 truncate">{{ app.name }}</h4>
                  </div>
                  <p class="text-sm text-gray-600 mt-1 line-clamp-2">{{ app.description || 'No description' }}</p>
                  <div class="mt-2">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                          [ngClass]="{
                            'bg-green-100 text-green-800': app.type === 'web_application',
                            'bg-purple-100 text-purple-800': app.type === 'microservice',
                            'bg-orange-100 text-orange-800': app.type === 'api_gateway',
                            'bg-blue-100 text-blue-800': app.type === 'database',
                            'bg-yellow-100 text-yellow-800': app.type === 'other'
                          }">
                      {{ app.type | titlecase }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- No Applications Found -->
          <div *ngIf="filteredApplications.length === 0" class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No Applications Found</h3>
            <p class="mt-1 text-sm text-gray-500">
              {{ applicationSearchTerm || selectedApplicationType ? 'No applications match your filters.' : 'No applications available for deployment.' }}
            </p>
          </div>
        </div>
      </div>

      <!-- Step 2: Deployment Configuration -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-semibold">2</span>
                </div>
              </div>
              <div class="ml-3">
                <h3 class="text-lg font-semibold text-gray-900">Deployment Configuration</h3>
                <p class="text-sm text-gray-600">Configure deployment settings and workflow</p>
              </div>
            </div>
          </div>
        </div>

        <div class="p-6">
          <form [formGroup]="deploymentForm">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- Basic Configuration -->
              <div class="space-y-4">
                <h4 class="text-sm font-semibold text-gray-900 flex items-center">
                  <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                  </svg>
                  Basic Settings
                </h4>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Version Tag *
                  </label>
                  <input type="text" formControlName="version"
                         placeholder="e.g., v1.2.3, latest, develop"
                         class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <p class="mt-1 text-xs text-gray-500">Specify the version/tag to deploy</p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Deployment Strategy
                  </label>
                  <select formControlName="strategy"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="rolling_update">🔄 Rolling Update</option>
                    <option value="blue_green">🔵 Blue-Green</option>
                    <option value="canary">🐤 Canary</option>
                    <option value="recreate">♻️ Recreate</option>
                  </select>
                  <p class="mt-1 text-xs text-gray-500">Choose deployment strategy</p>
                </div>
              </div>

              <!-- Workflow Configuration -->
              <div class="space-y-4">
                <h4 class="text-sm font-semibold text-gray-900 flex items-center">
                  <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                  Workflow & Execution
                </h4>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Deployment Workflow *
                  </label>
                  <select formControlName="workflowId"
                          (change)="onWorkflowChange($event)"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Select a workflow...</option>
                    <option *ngFor="let workflow of workflows" [value]="workflow.id">
                      {{ workflow.name }} - {{ workflow.description }}
                    </option>
                  </select>
                  <p class="mt-1 text-xs text-gray-500">Choose the workflow to execute for deployment</p>
                </div>

                <!-- Dynamic Workflow Parameters -->
                <div *ngIf="selectedWorkflow && selectedWorkflow.parameters?.length > 0"
                     class="bg-gray-50 rounded-lg p-4">
                  <h5 class="text-sm font-medium text-gray-900 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                    </svg>
                    Workflow Parameters ({{ selectedWorkflow.parameters.length }})
                  </h5>

                  <!-- Scrollable container for many parameters -->
                  <div class="max-h-96 overflow-y-auto space-y-4 pr-2">
                    <div *ngFor="let param of selectedWorkflow.parameters; trackBy: trackByParameterName"
                         class="bg-white rounded-lg p-3 border border-gray-200">
                      <div class="flex items-start justify-between mb-2">
                        <label class="block text-sm font-medium text-gray-700">
                          {{ param.name }}
                          <span *ngIf="param.required" class="text-red-500 ml-1">*</span>
                        </label>
                        <span *ngIf="param.type"
                              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {{ param.type }}
                        </span>
                      </div>

                      <!-- Text Input -->
                      <input *ngIf="param.type === 'string' || param.type === 'text'"
                             type="text"
                             [value]="getWorkflowParameterValue(param.name)"
                             (input)="setWorkflowParameterValue(param.name, $event)"
                             [placeholder]="param.defaultValue || param.description || 'Enter value...'"
                             [class]="getParameterInputClass(param)"
                             class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors">

                      <!-- Number Input -->
                      <input *ngIf="param.type === 'number'"
                             type="number"
                             [value]="getWorkflowParameterValue(param.name)"
                             (input)="setWorkflowParameterValue(param.name, $event)"
                             [placeholder]="param.defaultValue || param.description || 'Enter number...'"
                             [class]="getParameterInputClass(param)"
                             class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors">

                      <!-- Boolean Input -->
                      <div *ngIf="param.type === 'boolean'" class="flex items-center">
                        <input type="checkbox"
                               [checked]="getWorkflowParameterValue(param.name)"
                               (change)="setWorkflowParameterValue(param.name, $event)"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">{{ param.description || 'Enable this option' }}</span>
                      </div>

                      <!-- Select Input -->
                      <select *ngIf="param.type === 'select' || param.type === 'enum'"
                              [value]="getWorkflowParameterValue(param.name)"
                              (change)="setWorkflowParameterValue(param.name, $event)"
                              [class]="getParameterInputClass(param)"
                              class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors">
                        <option value="">Select an option...</option>
                        <option *ngFor="let option of getParameterOptions(param)" [value]="option">{{ option }}</option>
                      </select>

                      <!-- Textarea for long text -->
                      <textarea *ngIf="param.type === 'textarea'"
                                [value]="getWorkflowParameterValue(param.name)"
                                (input)="setWorkflowParameterValue(param.name, $event)"
                                [placeholder]="param.defaultValue || param.description || 'Enter text...'"
                                [class]="getParameterInputClass(param)"
                                rows="3"
                                class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors resize-none"></textarea>

                      <!-- Description and validation -->
                      <div class="mt-2">
                        <p *ngIf="param.description" class="text-xs text-gray-500">{{ param.description }}</p>
                        <p *ngIf="param.defaultValue" class="text-xs text-gray-400 mt-1">Default: {{ param.defaultValue }}</p>
                        <p *ngIf="isParameterInvalid(param)" class="text-xs text-red-600 mt-1 flex items-center">
                          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                          This field is required
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- Parameters Summary -->
                  <div class="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div class="flex items-center text-sm text-blue-800">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <span>{{ getValidParametersCount() }} of {{ selectedWorkflow.parameters.length }} parameters configured</span>
                      <span *ngIf="getRequiredParametersCount() > 0" class="ml-2">
                        ({{ getRequiredParametersCount() }} required)
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <!-- Step 3: Select Environment -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-orange-50 to-amber-50">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-semibold">3</span>
                </div>
              </div>
              <div class="ml-3">
                <h3 class="text-lg font-semibold text-gray-900">Select Target Environment</h3>
                <p class="text-sm text-gray-600">Choose where to deploy your applications</p>
              </div>
            </div>
            <div class="text-sm text-gray-500" *ngIf="selectedEnvironment">
              Selected: {{ selectedEnvironment.name }}
            </div>
          </div>
        </div>

        <div class="p-6">
          <!-- Search and Filter -->
          <div class="mb-4 flex gap-4">
            <div class="flex-1">
              <input
                type="text"
                [(ngModel)]="environmentSearchTerm"
                (input)="filterEnvironments()"
                placeholder="Search environments..."
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <select
              [(ngModel)]="selectedEnvironmentType"
              (change)="filterEnvironments()"
              class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">All Types</option>
              <option value="development">Development</option>
              <option value="staging">Staging</option>
              <option value="production">Production</option>
              <option value="testing">Testing</option>
            </select>
          </div>

          <!-- Environments Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
            <div *ngFor="let env of filteredEnvironments"
                 (click)="selectEnvironment(env)"
                 [class]="isEnvironmentSelected(env.id) ? 'border-orange-500 bg-orange-50 ring-2 ring-orange-200' : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'"
                 class="border rounded-lg p-4 cursor-pointer transition-all duration-200">
              <div class="flex items-start justify-between">
                <div class="flex-1 min-w-0">
                  <div class="flex items-center">
                    <input type="radio"
                           [checked]="isEnvironmentSelected(env.id)"
                           class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 mr-3">
                    <h4 class="font-medium text-gray-900 truncate">{{ env.name }}</h4>
                  </div>
                  <p class="text-sm text-gray-600 mt-1 line-clamp-2">{{ env.description || 'No description' }}</p>
                  <div class="mt-3 flex items-center space-x-2">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                          [ngClass]="{
                            'bg-blue-100 text-blue-800': env.type === 'development',
                            'bg-yellow-100 text-yellow-800': env.type === 'staging',
                            'bg-red-100 text-red-800': env.type === 'production',
                            'bg-purple-100 text-purple-800': env.type === 'testing',
                            'bg-gray-100 text-gray-800': !['development', 'staging', 'production', 'testing'].includes(env.type)
                          }">
                      {{ env.type | titlecase }}
                    </span>
                    <span [class]="env.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium">
                      <div class="w-1.5 h-1.5 rounded-full mr-1"
                           [class]="env.status === 'active' ? 'bg-green-400' : 'bg-red-400'"></div>
                      {{ env.status | titlecase }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- No Environments Found -->
          <div *ngIf="filteredEnvironments.length === 0" class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No Environments Found</h3>
            <p class="mt-1 text-sm text-gray-500">
              {{ environmentSearchTerm || selectedEnvironmentType ? 'No environments match your filters.' : 'No environments available for deployment.' }}
            </p>
          </div>
        </div>
      </div>

      <!-- Deploy Action -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Ready to Deploy?</h3>
              <p class="text-sm text-gray-600 mt-1">
                Review your selections and start the deployment process
              </p>
            </div>
            <div class="flex flex-col items-end space-y-2">
              <!-- Validation Messages -->
              <div *ngIf="!canDeploy() && !deploying" class="text-sm text-red-600 text-right">
                <div *ngIf="selectedApplications.length === 0" class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  Please select at least one application
                </div>
                <div *ngIf="!selectedEnvironment" class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  Please select an environment
                </div>
                <div *ngIf="!deploymentForm.valid" class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  Please fill all required fields
                </div>
                <div *ngIf="hasInvalidWorkflowParameters()" class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  Please fill all required workflow parameters
                </div>
              </div>

              <button
                (click)="onDeploy()"
                [disabled]="!canDeploy()"
                class="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center space-x-2">
                <svg *ngIf="!deploying" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                <svg *ngIf="deploying" class="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>{{ deploying ? 'Deploying...' : 'Start Deployment' }}</span>
              </button>
            </div>
          </div>

          <!-- Deployment Summary -->
          <div class="mt-4 p-4 bg-gray-50 rounded-lg">
            <h4 class="text-sm font-medium text-gray-900 mb-2">Deployment Summary</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span class="text-gray-600">Applications:</span>
                <span class="ml-1 font-medium">{{ selectedApplications.length }} selected</span>
              </div>
              <div>
                <span class="text-gray-600">Environment:</span>
                <span class="ml-1 font-medium">{{ selectedEnvironment?.name || 'None selected' }}</span>
              </div>
              <div>
                <span class="text-gray-600">Workflow:</span>
                <span class="ml-1 font-medium">{{ selectedWorkflow?.name || 'None selected' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- History Tab -->
    <div *ngIf="!loading && activeTab === 'history'" class="p-6">
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Deployment History</h3>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Application</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Environment</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Version</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deployed</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">By</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr *ngFor="let deployment of deployments">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ deployment.application?.name || deployment.component?.name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ deployment.environmentId }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ deployment.version }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span [class]="getDeploymentStatusClass(deployment.status)"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                    {{ deployment.status }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(deployment.deployedAt) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ deployment.deployedBy }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Version Matrix Tab -->
    <div *ngIf="!loading && activeTab === 'matrix'" class="p-6">
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Version Matrix</h3>
          <p class="text-sm text-gray-600 mt-1">Current versions deployed across environments</p>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Application</th>
                <th *ngFor="let env of getMatrixEnvironments()" 
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ env }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr *ngFor="let app of getMatrixApplications()">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ app }}
                </td>
                <td *ngFor="let env of getMatrixEnvironments()" 
                    class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                    {{ getVersionForAppInEnv(app, env) }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Promotions Tab -->
    <div *ngIf="!loading && activeTab === 'promotions'" class="p-6">
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Environment Promotions</h3>
          <p class="text-sm text-gray-600 mt-1">Promote versions between environments</p>
        </div>
        <div class="p-6">
          <p class="text-gray-500 text-center py-8">Promotion functionality coming soon...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- No Project Selected -->
  <div *ngIf="!currentProjectId && !loading" class="flex items-center justify-center h-64">
    <div class="text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No Project Selected</h3>
      <p class="mt-1 text-sm text-gray-500">Please select a project from the header to manage deployments.</p>
    </div>
  </div>
</div>
