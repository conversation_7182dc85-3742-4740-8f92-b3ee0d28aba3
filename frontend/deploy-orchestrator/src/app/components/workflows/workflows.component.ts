import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { WorkflowService } from '../../services/workflow.service';
import { ProjectService } from '../../services/project.service';
import { AuthService } from '../../services/auth.service';
import { NotificationService } from '../../services/notification.service';
import { WorkflowDefinition, WorkflowExecution } from '../../models/workflow.model';
import { Project } from '../../models/project.model';

@Component({
  selector: 'app-workflows',
  templateUrl: './workflows.component.html',
  styleUrls: ['./workflows.component.scss']
})
export class WorkflowsComponent implements OnInit, OnDestroy {
  workflows: WorkflowDefinition[] = [];
  recentExecutions: WorkflowExecution[] = [];
  projects: Project[] = [];
  selectedProjectId: string = '';
  loading = false;
  error: string | null = null;

  // Project management
  selectedProject: Project | null = null;
  currentProjectId: string = '';
  private destroy$ = new Subject<void>();

  // Execution modal
  showExecutionModal = false;
  workflowToExecute: WorkflowDefinition | null = null;

  constructor(
    private workflowService: WorkflowService,
    private projectService: ProjectService,
    private authService: AuthService,
    private notificationService: NotificationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.subscribeToProjectChanges();
    this.loadProjects();
    this.loadWorkflows();
    this.loadRecentExecutions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadProjects(): void {
    this.projectService.getProjects().subscribe({
      next: (projects) => {
        this.projects = projects || [];
      },
      error: (error) => {
        console.error('Error loading projects:', error);
        this.projects = []; // Ensure it's always an array
      }
    });
  }

  loadWorkflows(): void {
    this.loading = true;
    this.error = null;

    // Use currentProjectId from shared project service
    const projectId = this.currentProjectId || undefined;

    this.workflowService.getWorkflows(projectId).subscribe({
      next: (workflows) => {
        this.workflows = workflows || [];
        this.loading = false;

        // Update selectedProjectId for backward compatibility with template
        this.selectedProjectId = this.currentProjectId;
      },
      error: (error) => {
        this.error = 'Failed to load workflows';
        this.loading = false;
        this.workflows = []; // Ensure it's always an array
        console.error('Error loading workflows:', error);
      }
    });
  }

  loadRecentExecutions(): void {
    // Use currentProjectId from shared project service
    const projectId = this.currentProjectId || undefined;

    this.workflowService.getExecutions(undefined, projectId, undefined, 10).subscribe({
      next: (executions) => {
        this.recentExecutions = executions || [];
      },
      error: (error) => {
        console.error('Error loading recent executions:', error);
        this.recentExecutions = []; // Ensure it's always an array
      }
    });
  }

  onProjectChange(): void {
    this.loadWorkflows();
    this.loadRecentExecutions();
  }

  createWorkflow(): void {
    this.router.navigate(['/workflows/designer']);
  }

  editWorkflow(workflow: WorkflowDefinition): void {
    this.router.navigate(['/workflows/designer', workflow.id]);
  }

  executeWorkflow(workflow: WorkflowDefinition): void {
    this.workflowToExecute = workflow;
    this.showExecutionModal = true;
  }

  onExecuteWorkflow(data: { parameters: { [key: string]: any }, triggerType: string }): void {
    if (!this.workflowToExecute) return;

    this.workflowService.executeWorkflow(
      this.workflowToExecute.id,
      data.parameters,
      data.triggerType
    ).subscribe({
      next: (execution) => {
        console.log('Workflow execution started:', execution);
        const workflowName = this.workflowToExecute?.name;
        this.showExecutionModal = false;
        this.workflowToExecute = null;
        this.loadRecentExecutions();

        // Show success notification
        this.notificationService.success(
          'Workflow Execution Started',
          `Workflow "${workflowName}" execution started successfully!`
        );
      },
      error: (error) => {
        console.error('Error executing workflow:', error);
        this.notificationService.error(
          'Workflow Execution Failed',
          'Failed to execute workflow. Please try again.'
        );
        // Reset execution state on error
        this.showExecutionModal = false;
        this.workflowToExecute = null;
      }
    });
  }

  onCancelExecution(): void {
    this.showExecutionModal = false;
    this.workflowToExecute = null;
  }

  deleteWorkflow(workflow: WorkflowDefinition): void {
    if (confirm(`Are you sure you want to delete workflow "${workflow.name}"?`)) {
      this.workflowService.deleteWorkflow(workflow.id).subscribe({
        next: () => {
          this.loadWorkflows();
          this.notificationService.success(
            'Workflow Deleted',
            `Workflow "${workflow.name}" has been deleted successfully.`
          );
        },
        error: (error) => {
          console.error('Error deleting workflow:', error);
          this.notificationService.error(
            'Delete Failed',
            'Failed to delete workflow. Please try again.'
          );
        }
      });
    }
  }

  viewExecution(execution: WorkflowExecution): void {
    this.router.navigate(['/workflows/executions', execution.id]);
  }

  viewExecutionMonitoring(execution: WorkflowExecution): void {
    this.router.navigate(['/execution-monitoring', execution.id]);
  }

  isAdmin(): boolean {
    return this.authService.isAdmin();
  }

  getStatusColor(status: string): string {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'success':
        return 'text-green-600';
      case 'failed':
      case 'error':
        return 'text-red-600';
      case 'running':
      case 'in_progress':
        return 'text-blue-600';
      case 'pending':
      case 'queued':
        return 'text-yellow-600';
      case 'cancelled':
      case 'canceled':
        return 'text-gray-600';
      default:
        return 'text-gray-600';
    }
  }

  // Project management
  private subscribeToProjectChanges(): void {
    this.projectService.selectedProject$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (selectedProject) => {
          const newProjectId = selectedProject?.id || '';

          if (newProjectId !== this.currentProjectId) {
            this.selectedProject = selectedProject;
            this.currentProjectId = newProjectId;

            // Reload workflows and executions when project changes
            this.loadWorkflows();
            this.loadRecentExecutions();
          }
        },
        error: (error) => {
          console.error('Error subscribing to project changes:', error);
        }
      });
  }
}
