<div class="p-6">
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Workflows</h1>
      <p class="text-gray-600">Manage and execute your automation workflows</p>
    </div>
    <button
      (click)="createWorkflow()"
      class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center">
      <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
      </svg>
      Create Workflow
    </button>
  </div>

  <!-- Project info display -->
  <div *ngIf="selectedProject" class="mb-6">
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h4a1 1 0 011 1v5m-6 0V9a1 1 0 011-1h4a1 1 0 011 1v2"></path>
        </svg>
        <span class="text-sm font-medium text-blue-900">
          Showing workflows for: <strong>{{ selectedProject.name }}</strong>
        </span>
      </div>
    </div>
  </div>

  <!-- No project selected message -->
  <div *ngIf="!selectedProject" class="mb-6">
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <span class="text-sm font-medium text-yellow-900">
          Please select a project from the header to view workflows
        </span>
      </div>
    </div>
  </div>

  <!-- Error Message -->
  <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
    {{ error }}
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center items-center py-12">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <span class="ml-2 text-gray-600">Loading workflows...</span>
  </div>

  <!-- Workflows Grid -->
  <div *ngIf="!loading" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Workflows List -->
    <div class="lg:col-span-2">
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">Workflows</h2>
        </div>
        <div class="divide-y divide-gray-200">
          <div *ngIf="!workflows || workflows.length === 0" class="px-6 py-8 text-center text-gray-500">
            No workflows found. <a (click)="createWorkflow()" class="text-blue-600 hover:text-blue-800 cursor-pointer">Create your first workflow</a>
          </div>
          <div *ngFor="let workflow of workflows" class="px-6 py-4 hover:bg-gray-50">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center">
                  <h3 class="text-sm font-medium text-gray-900">{{ workflow.name }}</h3>
                  <span *ngIf="!workflow.isActive" class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    Inactive
                  </span>
                </div>
                <p class="text-sm text-gray-500 mt-1">{{ workflow.description || 'No description' }}</p>
                <div class="flex items-center mt-2 text-xs text-gray-500">
                  <span>{{ (workflow.steps || []).length }} steps</span>
                  <span class="mx-2">•</span>
                  <span>v{{ workflow.version }}</span>
                  <span class="mx-2">•</span>
                  <span>{{ workflow.updatedAt | date:'short' }}</span>
                </div>
                <div *ngIf="workflow.tags && workflow.tags.length > 0" class="flex flex-wrap gap-1 mt-2">
                  <span *ngFor="let tag of workflow.tags" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {{ tag }}
                  </span>
                </div>
              </div>
              <div class="flex items-center space-x-2 ml-4">
                <button
                  (click)="executeWorkflow(workflow)"
                  class="text-green-600 hover:text-green-800 p-1"
                  title="Execute Workflow">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h1m4 0h1M9 6h6"></path>
                  </svg>
                </button>
                <button
                  (click)="editWorkflow(workflow)"
                  class="text-blue-600 hover:text-blue-800 p-1"
                  title="Edit Workflow">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                </button>
                <button
                  *ngIf="isAdmin()"
                  (click)="deleteWorkflow(workflow)"
                  class="text-red-600 hover:text-red-800 p-1"
                  title="Delete Workflow">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Executions -->
    <div class="lg:col-span-1">
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">Recent Executions</h2>
        </div>
        <div class="divide-y divide-gray-200">
          <div *ngIf="!recentExecutions || recentExecutions.length === 0" class="px-6 py-8 text-center text-gray-500">
            No recent executions
          </div>
          <div *ngFor="let execution of recentExecutions" class="px-6 py-4 hover:bg-gray-50">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <h4 class="text-sm font-medium text-gray-900">{{ execution.workflowName }}</h4>
                <p class="text-xs text-gray-500 mt-1">{{ execution.startedAt | date:'short' }}</p>
                <div class="flex items-center mt-1">
                  <span [class]="getStatusColor(execution.status)" class="text-xs font-medium capitalize">
                    {{ execution.status }}
                  </span>
                  <span *ngIf="execution.duration" class="text-xs text-gray-500 ml-2">
                    {{ execution.duration }}s
                  </span>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <button
                  (click)="viewExecution(execution)"
                  class="text-blue-600 hover:text-blue-800 text-xs font-medium">
                  View Details
                </button>
                <button
                  (click)="viewExecutionMonitoring(execution)"
                  class="text-green-600 hover:text-green-800 text-xs font-medium">
                  Monitor
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Execution Parameters Modal -->
<app-execution-parameters
  [workflow]="workflowToExecute"
  [show]="showExecutionModal"
  (execute)="onExecuteWorkflow($event)"
  (cancel)="onCancelExecution()">
</app-execution-parameters>
