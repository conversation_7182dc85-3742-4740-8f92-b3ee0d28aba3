import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { ParameterConfigComponent } from './parameter-config.component';
import { WorkflowParameter } from '../../models/workflow.model';

describe('ParameterConfigComponent', () => {
  let component: ParameterConfigComponent;
  let fixture: ComponentFixture<ParameterConfigComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ParameterConfigComponent],
      imports: [FormsModule]
    }).compileComponents();

    fixture = TestBed.createComponent(ParameterConfigComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize parameter values with defaults', () => {
    const parameters: WorkflowParameter[] = [
      {
        name: 'testParam',
        type: 'string',
        description: 'Test parameter',
        required: true,
        defaultValue: 'default-value'
      }
    ];

    component.parameters = parameters;
    component.ngOnInit();

    expect(component.parameterValues['testParam']).toBe('default-value');
  });

  it('should validate required parameters', () => {
    const parameters: WorkflowParameter[] = [
      {
        name: 'requiredParam',
        type: 'string',
        description: 'Required parameter',
        required: true
      }
    ];

    component.parameters = parameters;
    component.ngOnInit();

    // Should have validation error for empty required parameter
    expect(component.hasValidationError('requiredParam')).toBe(true);
    expect(component.getValidationError('requiredParam')).toContain('required');
  });

  it('should emit parameter changes', () => {
    spyOn(component.parametersChange, 'emit');
    
    component.onParameterChange('testParam', 'new-value');
    
    expect(component.parametersChange.emit).toHaveBeenCalledWith(
      jasmine.objectContaining({ testParam: 'new-value' })
    );
  });

  it('should emit validation status changes', () => {
    spyOn(component.validationChange, 'emit');
    
    const parameters: WorkflowParameter[] = [
      {
        name: 'testParam',
        type: 'string',
        description: 'Test parameter',
        required: false,
        defaultValue: 'default'
      }
    ];

    component.parameters = parameters;
    component.ngOnInit();

    expect(component.validationChange.emit).toHaveBeenCalledWith(true);
  });

  it('should handle different parameter types', () => {
    const parameters: WorkflowParameter[] = [
      {
        name: 'stringParam',
        type: 'string',
        description: 'String parameter',
        required: false
      },
      {
        name: 'numberParam',
        type: 'number',
        description: 'Number parameter',
        required: false
      },
      {
        name: 'booleanParam',
        type: 'boolean',
        description: 'Boolean parameter',
        required: false
      }
    ];

    component.parameters = parameters;
    component.ngOnInit();

    expect(component.parameterValues['stringParam']).toBe('');
    expect(component.parameterValues['numberParam']).toBe(0);
    expect(component.parameterValues['booleanParam']).toBe(false);
  });

  it('should validate enum parameters', () => {
    const parameters: WorkflowParameter[] = [
      {
        name: 'enumParam',
        type: 'enum',
        description: 'Enum parameter',
        required: true,
        options: ['option1', 'option2', 'option3']
      }
    ];

    component.parameters = parameters;
    component.ngOnInit();

    // Set invalid value
    component.onParameterChange('enumParam', 'invalid-option');
    
    expect(component.hasValidationError('enumParam')).toBe(true);
    expect(component.getValidationError('enumParam')).toContain('must be one of');

    // Set valid value
    component.onParameterChange('enumParam', 'option1');
    
    expect(component.hasValidationError('enumParam')).toBe(false);
  });

  it('should handle array parameters', () => {
    const mockEvent = {
      target: { value: 'item1, item2, item3' }
    } as any;

    component.onInputChange('arrayParam', mockEvent);

    expect(component.parameterValues['arrayParam']).toEqual(['item1', 'item2', 'item3']);
  });

  it('should return correct parameter metadata', () => {
    const parameters: WorkflowParameter[] = [
      {
        name: 'testParam',
        type: 'string',
        description: 'Test description',
        required: true,
        options: ['opt1', 'opt2']
      }
    ];

    component.parameters = parameters;

    expect(component.getParameterType('testParam')).toBe('string');
    expect(component.getParameterDescription('testParam')).toBe('Test description');
    expect(component.isRequired('testParam')).toBe(true);
    expect(component.getParameterOptions('testParam')).toEqual(['opt1', 'opt2']);
  });
});
