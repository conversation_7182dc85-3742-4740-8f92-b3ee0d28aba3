// Parameter Configuration Component Styles

.parameter-config {
  .parameter-item {
    transition: all 0.2s ease-in-out;
    
    &:hover {
      background-color: #f9fafb;
    }
  }

  .parameter-label {
    font-weight: 500;
    color: #374151;
    
    .required-indicator {
      color: #ef4444;
      font-weight: bold;
    }
  }

  .parameter-description {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
  }

  .parameter-input {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    
    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    &.error {
      border-color: #ef4444;
      
      &:focus {
        border-color: #ef4444;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
      }
    }
  }

  .validation-error {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
  }

  .default-value-hint {
    color: #6b7280;
    font-size: 0.75rem;
    margin-top: 0.25rem;
  }

  .parameter-tips {
    background-color: #eff6ff;
    border: 1px solid #bfdbfe;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 1.5rem;
    
    .tips-icon {
      color: #60a5fa;
    }
    
    .tips-title {
      color: #1e40af;
      font-weight: 500;
    }
    
    .tips-content {
      color: #1d4ed8;
      
      ul {
        margin-top: 0.5rem;
        padding-left: 1.25rem;
        
        li {
          margin-bottom: 0.25rem;
        }
      }
    }
  }

  .checkbox-container {
    display: flex;
    align-items: center;
    
    input[type="checkbox"] {
      margin-right: 0.5rem;
      color: #3b82f6;
      
      &:focus {
        ring-color: #3b82f6;
      }
    }
    
    label {
      color: #111827;
      font-size: 0.875rem;
    }
  }

  .array-input-hint {
    color: #6b7280;
    font-size: 0.75rem;
    margin-top: 0.25rem;
  }
}

// Responsive adjustments
@media (max-width: 640px) {
  .parameter-config {
    .parameter-item {
      padding: 0.75rem;
    }
    
    .parameter-tips {
      padding: 0.75rem;
    }
  }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .parameter-config {
    .parameter-label {
      color: #f3f4f6;
    }
    
    .parameter-description {
      color: #9ca3af;
    }
    
    .parameter-input {
      background-color: #374151;
      border-color: #4b5563;
      color: #f3f4f6;
      
      &:focus {
        border-color: #60a5fa;
      }
      
      &.error {
        border-color: #f87171;
      }
    }
    
    .validation-error {
      color: #f87171;
    }
    
    .default-value-hint {
      color: #9ca3af;
    }
    
    .parameter-tips {
      background-color: #1e3a8a;
      border-color: #3b82f6;
      
      .tips-title {
        color: #bfdbfe;
      }
      
      .tips-content {
        color: #93c5fd;
      }
    }
  }
}
