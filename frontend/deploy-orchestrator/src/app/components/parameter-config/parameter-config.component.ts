import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { WorkflowParameter } from '../../models/workflow.model';

@Component({
  selector: 'app-parameter-config',
  templateUrl: './parameter-config.component.html',
  styleUrls: ['./parameter-config.component.scss']
})
export class ParameterConfigComponent implements OnInit {
  @Input() parameters: WorkflowParameter[] = [];
  @Input() title: string = 'Configure Parameters';
  @Input() description: string = 'Provide values for the template parameters';
  @Output() parametersChange = new EventEmitter<{ [key: string]: any }>();
  @Output() validationChange = new EventEmitter<boolean>();

  parameterValues: { [key: string]: any } = {};
  validationErrors: { [key: string]: string } = {};

  ngOnInit(): void {
    this.initializeParameterValues();
    this.validateParameters();
  }

  private initializeParameterValues(): void {
    this.parameters.forEach(param => {
      if (param.defaultValue !== undefined && param.defaultValue !== null) {
        this.parameterValues[param.name] = param.defaultValue;
      } else {
        // Initialize with appropriate default based on type
        switch (param.type) {
          case 'boolean':
            this.parameterValues[param.name] = false;
            break;
          case 'number':
            this.parameterValues[param.name] = 0;
            break;
          case 'array':
            this.parameterValues[param.name] = [];
            break;
          default:
            this.parameterValues[param.name] = '';
        }
      }
    });
  }

  onParameterChange(parameterName: string, value: any): void {
    this.parameterValues[parameterName] = value;
    this.validateParameter(parameterName);
    this.parametersChange.emit({ ...this.parameterValues });
    this.emitValidationStatus();
  }

  onInputChange(parameterName: string, event: Event): void {
    const target = event.target as HTMLInputElement;
    let value: any = target.value;

    // Convert value based on parameter type
    const parameter = this.parameters.find(p => p.name === parameterName);
    if (parameter) {
      switch (parameter.type) {
        case 'number':
          value = target.value ? parseFloat(target.value) : 0;
          break;
        case 'boolean':
          value = target.checked;
          break;
        case 'array':
          value = target.value.split(',').map(item => item.trim()).filter(item => item.length > 0);
          break;
        default:
          value = target.value;
      }
    }

    this.onParameterChange(parameterName, value);
  }

  private validateParameter(parameterName: string): void {
    const parameter = this.parameters.find(p => p.name === parameterName);
    if (!parameter) return;

    const value = this.parameterValues[parameterName];
    let error = '';

    // Required validation
    if (parameter.required) {
      if (value === undefined || value === null || value === '' || 
          (Array.isArray(value) && value.length === 0)) {
        error = `${parameter.name} is required`;
      }
    }

    // Type-specific validation
    if (!error && value !== '' && value !== null && value !== undefined) {
      switch (parameter.type) {
        case 'number':
          if (isNaN(value)) {
            error = `${parameter.name} must be a valid number`;
          }
          break;
        case 'enum':
          if (parameter.options && !parameter.options.includes(value)) {
            error = `${parameter.name} must be one of: ${parameter.options.join(', ')}`;
          }
          break;
      }
    }

    if (error) {
      this.validationErrors[parameterName] = error;
    } else {
      delete this.validationErrors[parameterName];
    }
  }

  private validateParameters(): void {
    this.parameters.forEach(param => {
      this.validateParameter(param.name);
    });
    this.emitValidationStatus();
  }

  private emitValidationStatus(): void {
    const isValid = Object.keys(this.validationErrors).length === 0;
    this.validationChange.emit(isValid);
  }

  getParameterValue(parameterName: string): any {
    return this.parameterValues[parameterName];
  }

  getValidationError(parameterName: string): string {
    return this.validationErrors[parameterName] || '';
  }

  hasValidationError(parameterName: string): boolean {
    return !!this.validationErrors[parameterName];
  }

  getArrayValue(parameterName: string): string {
    const value = this.parameterValues[parameterName];
    return Array.isArray(value) ? value.join(', ') : '';
  }

  isRequired(parameterName: string): boolean {
    const parameter = this.parameters.find(p => p.name === parameterName);
    return parameter?.required || false;
  }

  getParameterDescription(parameterName: string): string {
    const parameter = this.parameters.find(p => p.name === parameterName);
    return parameter?.description || '';
  }

  getParameterOptions(parameterName: string): string[] {
    const parameter = this.parameters.find(p => p.name === parameterName);
    return parameter?.options || [];
  }

  getParameterType(parameterName: string): string {
    const parameter = this.parameters.find(p => p.name === parameterName);
    return parameter?.type || 'string';
  }
}
