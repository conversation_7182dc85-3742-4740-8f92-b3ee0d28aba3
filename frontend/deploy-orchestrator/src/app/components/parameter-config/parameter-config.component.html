<div class="space-y-6">
  <!-- Header -->
  <div>
    <h3 class="text-lg font-medium text-gray-900">{{ title }}</h3>
    <p class="mt-1 text-sm text-gray-600">{{ description }}</p>
  </div>

  <!-- No Parameters Message -->
  <div *ngIf="parameters.length === 0" class="text-center py-8 text-gray-500">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
    </svg>
    <p class="mt-2">No parameters to configure</p>
    <p class="text-sm">This template doesn't require any parameter configuration.</p>
  </div>

  <!-- Parameters Form -->
  <div *ngIf="parameters.length > 0" class="space-y-6">
    <div *ngFor="let parameter of parameters" class="space-y-2">
      <label class="block text-sm font-medium text-gray-700">
        {{ parameter.name }}
        <span *ngIf="isRequired(parameter.name)" class="text-red-500 ml-1">*</span>
      </label>
      
      <!-- Description -->
      <p *ngIf="getParameterDescription(parameter.name)" class="text-sm text-gray-500">
        {{ getParameterDescription(parameter.name) }}
      </p>

      <!-- String Input -->
      <input
        *ngIf="getParameterType(parameter.name) === 'string'"
        type="text"
        [value]="getParameterValue(parameter.name)"
        (input)="onInputChange(parameter.name, $event)"
        [class]="hasValidationError(parameter.name) ? 
          'w-full px-3 py-2 border border-red-300 rounded-md focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500' :
          'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500'"
        [placeholder]="parameter.defaultValue || 'Enter ' + parameter.name">

      <!-- Number Input -->
      <input
        *ngIf="getParameterType(parameter.name) === 'number'"
        type="number"
        [value]="getParameterValue(parameter.name)"
        (input)="onInputChange(parameter.name, $event)"
        [class]="hasValidationError(parameter.name) ? 
          'w-full px-3 py-2 border border-red-300 rounded-md focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500' :
          'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500'"
        [placeholder]="parameter.defaultValue || '0'">

      <!-- Boolean Checkbox -->
      <div *ngIf="getParameterType(parameter.name) === 'boolean'" class="flex items-center">
        <input
          type="checkbox"
          [checked]="getParameterValue(parameter.name)"
          (change)="onInputChange(parameter.name, $event)"
          [id]="'param-' + parameter.name"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
        <label [for]="'param-' + parameter.name" class="ml-2 block text-sm text-gray-900">
          Enable {{ parameter.name }}
        </label>
      </div>

      <!-- Enum/Select Dropdown -->
      <select
        *ngIf="getParameterType(parameter.name) === 'enum'"
        [value]="getParameterValue(parameter.name)"
        (change)="onInputChange(parameter.name, $event)"
        [class]="hasValidationError(parameter.name) ? 
          'w-full px-3 py-2 border border-red-300 rounded-md focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500' :
          'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500'">
        <option value="">Select {{ parameter.name }}</option>
        <option *ngFor="let option of getParameterOptions(parameter.name)" [value]="option">
          {{ option }}
        </option>
      </select>

      <!-- Array Input -->
      <div *ngIf="getParameterType(parameter.name) === 'array'">
        <input
          type="text"
          [value]="getArrayValue(parameter.name)"
          (input)="onInputChange(parameter.name, $event)"
          [class]="hasValidationError(parameter.name) ? 
            'w-full px-3 py-2 border border-red-300 rounded-md focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500' :
            'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500'"
          placeholder="Enter comma-separated values">
        <p class="mt-1 text-xs text-gray-500">Enter multiple values separated by commas</p>
      </div>

      <!-- Validation Error -->
      <p *ngIf="hasValidationError(parameter.name)" class="text-sm text-red-600">
        {{ getValidationError(parameter.name) }}
      </p>

      <!-- Default Value Hint -->
      <p *ngIf="parameter.defaultValue && !hasValidationError(parameter.name)" class="text-xs text-gray-500">
        Default: {{ parameter.defaultValue }}
      </p>
    </div>

    <!-- Parameter Usage Tips -->
    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-blue-800">Parameter Tips</h3>
          <div class="mt-2 text-sm text-blue-700">
            <ul class="list-disc pl-5 space-y-1">
              <li>Required parameters must be provided before creating the workflow</li>
              <li>Default values will be used if no value is specified</li>
              <li>Parameters customize the workflow behavior for your specific needs</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
