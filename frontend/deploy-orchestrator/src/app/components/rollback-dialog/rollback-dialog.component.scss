// Rollback Dialog Styles using Tailwind CSS
.rollback-dialog {
  // Custom details/summary styling
  details {
    summary {
      &::-webkit-details-marker {
        display: none;
      }
      
      &::before {
        content: '▶';
        display: inline-block;
        margin-right: 8px;
        transition: transform 0.2s;
      }
    }

    &[open] summary::before {
      transform: rotate(90deg);
    }
  }

  // Version selection cards
  .version-option {
    @apply border rounded-lg p-4 cursor-pointer transition-all duration-200;
    
    &:hover {
      @apply shadow-md;
    }
    
    &.selected {
      @apply border-blue-500 bg-blue-50 shadow-sm;
    }
    
    &.unselected {
      @apply border-gray-200 hover:border-gray-300;
    }

    .version-info {
      @apply flex items-center space-x-3;
      
      .version-number {
        @apply font-medium text-gray-900;
      }
      
      .status-badge {
        @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
        
        &.status-succeeded {
          @apply bg-green-100 text-green-800;
        }
        
        &.status-failed {
          @apply bg-red-100 text-red-800;
        }
      }
    }

    .deployment-meta {
      @apply mt-1 text-sm text-gray-500;
    }
  }

  // Form styling
  .form-field {
    @apply space-y-2;
    
    label {
      @apply block text-sm font-medium text-gray-700;
      
      .required {
        @apply text-red-500;
      }
    }
    
    input, select, textarea {
      @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
      
      &:disabled {
        @apply bg-gray-100 cursor-not-allowed;
      }
      
      &.error {
        @apply border-red-300 focus:ring-red-500;
      }
    }
    
    .error-message {
      @apply text-sm text-red-600;
    }
    
    .help-text {
      @apply text-sm text-gray-500;
    }
  }

  // Advanced configuration section
  .advanced-config {
    @apply border border-gray-200 rounded-lg;

    summary {
      @apply px-4 py-3 cursor-pointer text-sm font-medium text-gray-700 hover:bg-gray-50;
    }

    .config-content {
      @apply px-4 pb-4 space-y-4;

      .config-field {
        label {
          @apply block text-sm font-medium text-gray-700 mb-2;
        }

        .field-description {
          @apply text-sm text-gray-500 mb-2;
        }

        textarea {
          @apply font-mono text-xs;
          
          &::placeholder {
            @apply text-gray-400;
          }
        }
      }
    }
  }

  // Warning section
  .warning-section {
    @apply bg-red-50 border border-red-200 rounded-lg p-4;

    .warning-icon {
      @apply h-5 w-5 text-red-400;
    }

    .warning-title {
      @apply text-sm font-medium text-red-800;
    }

    .warning-content {
      @apply mt-2 text-sm text-red-700;
    }
  }

  // Summary sections
  .summary-section {
    @apply rounded-lg p-4;
    
    &.rollback-summary {
      @apply bg-yellow-50 border border-yellow-200;
      
      h3 {
        @apply text-sm font-medium text-yellow-900 mb-2;
      }
    }

    .summary-grid {
      @apply grid grid-cols-2 gap-4 text-sm;

      .summary-item {
        .label {
          @apply text-gray-600;
        }

        .value {
          @apply font-medium text-gray-900 ml-2;
        }
      }
    }
  }

  // Loading state
  .loading-state {
    @apply flex items-center justify-center py-8;

    .spinner {
      @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
    }

    .loading-text {
      @apply ml-3 text-gray-600;
    }
  }

  // Empty state
  .empty-state {
    @apply text-center py-8 text-gray-500;
  }

  // Footer buttons
  .dialog-footer {
    @apply px-6 py-4 border-t border-gray-200 flex justify-end space-x-3;

    .btn {
      @apply px-4 py-2 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
      
      &.btn-secondary {
        @apply border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500;
      }
      
      &.btn-danger {
        @apply border border-transparent text-white bg-red-600 hover:bg-red-700 focus:ring-red-500;
      }
      
      &:disabled {
        @apply opacity-50 cursor-not-allowed;
      }

      .spinner-small {
        @apply animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2;
      }
    }
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// Responsive adjustments
@media (max-width: 640px) {
  .rollback-dialog {
    .summary-grid {
      @apply grid-cols-1 gap-2;
    }
    
    .dialog-footer {
      @apply flex-col space-y-2 space-x-0;
      
      .btn {
        @apply w-full justify-center;
      }
    }
  }
}
