<!-- Overlay -->
<div *ngIf="isVisible" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
  <!-- Dialog -->
  <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <h2 class="text-xl font-semibold text-gray-900">
        Rollback {{ data?.entityName }}
      </h2>
    </div>

    <!-- Content -->
    <div class="px-6 py-4 space-y-6">
      <!-- Rollback Summary -->
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 class="text-sm font-medium text-yellow-900 mb-2">Rollback Details</h3>
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-gray-600">{{ data?.entityType | titlecase }}:</span>
            <span class="font-medium text-gray-900 ml-2">{{ data?.entityName }}</span>
          </div>
          <div>
            <span class="text-gray-600">Current Version:</span>
            <span class="font-medium text-gray-900 ml-2">{{ data?.currentVersion }}</span>
          </div>
          <div>
            <span class="text-gray-600">Environment:</span>
            <span class="font-medium text-gray-900 ml-2">{{ data?.environmentName }}</span>
          </div>
          <div *ngIf="getSelectedOption()">
            <span class="text-gray-600">Target Version:</span>
            <span class="font-medium text-gray-900 ml-2">{{ getSelectedOption()?.version }}</span>
          </div>
        </div>
      </div>

      <!-- Version Selection -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 mb-4">Select Version to Rollback To</h3>
        <div *ngIf="loading" class="flex items-center justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span class="ml-3 text-gray-600">Loading rollback options...</span>
        </div>
        
        <div *ngIf="!loading && rollbackOptions.length === 0" class="text-center py-8 text-gray-500">
          No previous versions available for rollback
        </div>

        <div *ngIf="!loading && rollbackOptions.length > 0" class="space-y-3">
          <div *ngFor="let option of rollbackOptions" 
               class="border rounded-lg p-4 cursor-pointer transition-colors"
               [class.border-blue-500]="isVersionSelected(option.version)"
               [class.bg-blue-50]="isVersionSelected(option.version)"
               [class.border-gray-200]="!isVersionSelected(option.version)"
               (click)="onVersionSelect(option)">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-3">
                  <span class="font-medium text-gray-900">{{ option.version }}</span>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {{ option.status }}
                  </span>
                </div>
                <div class="mt-1 text-sm text-gray-500">
                  Deployed {{ formatDate(option.deployedAt) }} by {{ option.deployedBy }}
                </div>
              </div>
              <div class="ml-4">
                <input type="radio" 
                       [checked]="isVersionSelected(option.version)"
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Rollback Form -->
      <form [formGroup]="rollbackForm" class="space-y-4" *ngIf="rollbackOptions.length > 0">
        <!-- Reason -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Reason for Rollback *</label>
          <textarea formControlName="reason" rows="3" 
                    placeholder="Explain why this rollback is necessary..."
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
          <div *ngIf="rollbackForm.get('reason')?.hasError('required') && rollbackForm.get('reason')?.touched" 
               class="text-red-600 text-sm mt-1">
            Reason is required
          </div>
        </div>

        <!-- Workflow Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Workflow *</label>
          <select formControlName="workflowId" (change)="onWorkflowChange()" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Select a workflow</option>
            <option *ngFor="let workflow of workflows" [value]="workflow.id">
              {{ workflow.name }}
            </option>
          </select>
          <div *ngIf="rollbackForm.get('workflowId')?.hasError('required') && rollbackForm.get('workflowId')?.touched" 
               class="text-red-600 text-sm mt-1">
            Workflow is required
          </div>
        </div>

        <!-- Provider Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Provider *</label>
          <select formControlName="providerType" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Select a provider</option>
            <option *ngFor="let provider of providers" [value]="provider.type">
              {{ provider.name }}
            </option>
          </select>
          <div *ngIf="rollbackForm.get('providerType')?.hasError('required') && rollbackForm.get('providerType')?.touched" 
               class="text-red-600 text-sm mt-1">
            Provider is required
          </div>
        </div>

        <!-- Advanced Configuration (Expandable) -->
        <details class="border border-gray-200 rounded-lg">
          <summary class="px-4 py-3 cursor-pointer text-sm font-medium text-gray-700 hover:bg-gray-50">
            Advanced Configuration
          </summary>
          <div class="px-4 pb-4 space-y-4">
            <!-- Parameters -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Parameters</label>
              <div class="text-sm text-gray-500 mb-2">
                Additional parameters for the rollback workflow
              </div>
              <textarea 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows="3"
                placeholder='{"key": "value"}'
                (blur)="updateFormField('parameters', $event)">
              </textarea>
            </div>

            <!-- Configuration -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Configuration</label>
              <div class="text-sm text-gray-500 mb-2">
                Additional configuration for the rollback
              </div>
              <textarea 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows="3"
                placeholder='{"strategy": "immediate"}'
                (blur)="updateFormField('configuration', $event)">
              </textarea>
            </div>
          </div>
        </details>
      </form>

      <!-- Warning -->
      <div *ngIf="getSelectedOption()" class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">
              Warning: Rollback Operation
            </h3>
            <div class="mt-2 text-sm text-red-700">
              <p>
                This will rollback {{ data?.entityName }} from version {{ data?.currentVersion }} 
                to version {{ getSelectedOption()?.version }} in the {{ data?.environmentName }} environment.
                This action cannot be undone automatically.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
      <button type="button" (click)="onCancel()" [disabled]="submitting"
              class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
        Cancel
      </button>
      <button type="button" (click)="onSubmit()" 
              [disabled]="!rollbackForm.valid || !rollbackForm.get('toVersion')?.value || submitting"
              class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50">
        <span *ngIf="!submitting">Confirm Rollback</span>
        <span *ngIf="submitting" class="flex items-center">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          Rolling back...
        </span>
      </button>
    </div>
  </div>
</div>
