import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

import { DeploymentManagementService } from '../../services/deployment-management.service';
import { WorkflowService } from '../../services/workflow.service';
import { NotificationService } from '../../services/notification.service';

export interface RollbackDialogData {
  entityName: string;
  entityType: 'application' | 'component';
  entityId: string;
  currentVersion: string;
  environmentId: string;
  environmentName: string;
  projectId: string;
}

export interface RollbackOption {
  version: string;
  deploymentId: string;
  deployedAt: string;
  deployedBy: string;
  status: string;
}

export interface RollbackRequest {
  projectId: string;
  environmentId: string;
  applicationId?: string;
  componentId?: string;
  toVersion: string;
  workflowId: string;
  providerType: string;
  requestedBy: string;
  reason: string;
  parameters?: { [key: string]: any };
  configuration?: { [key: string]: any };
}

@Component({
  selector: 'app-rollback-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule
  ],
  templateUrl: './rollback-dialog.component.html',
  styleUrls: ['./rollback-dialog.component.scss']
})
export class RollbackDialogComponent implements OnInit, OnDestroy {
  @Input() data!: RollbackDialogData;
  @Input() isVisible = false;
  @Output() close = new EventEmitter<any>();
  @Output() submit = new EventEmitter<RollbackRequest>();

  private destroy$ = new Subject<void>();
  
  rollbackForm!: FormGroup;
  workflows: any[] = [];
  providers: any[] = [];
  rollbackOptions: RollbackOption[] = [];
  loading = false;
  submitting = false;

  constructor(
    private fb: FormBuilder,
    private deploymentService: DeploymentManagementService,
    private workflowService: WorkflowService,
    private notificationService: NotificationService
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    if (this.data?.projectId) {
      this.loadData();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.rollbackForm = this.fb.group({
      toVersion: ['', Validators.required],
      workflowId: ['', Validators.required],
      providerType: ['', Validators.required],
      reason: ['', Validators.required],
      parameters: this.fb.group({}),
      configuration: this.fb.group({})
    });
  }

  private loadData(): void {
    this.loading = true;
    
    // Load workflows and rollback options in parallel
    Promise.all([
      this.loadWorkflows(),
      this.loadRollbackOptions()
    ]).finally(() => {
      this.loading = false;
    });
  }

  private loadWorkflows(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.workflowService.getWorkflows(this.data.projectId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (workflows) => {
            this.workflows = workflows;
            resolve();
          },
          error: (error) => {
            this.notificationService.error('Failed to load workflows', 'Please try again');
            reject(error);
          }
        });
    });
  }

  private loadRollbackOptions(): Promise<void> {
    return new Promise((resolve, reject) => {
      const params = {
        projectId: this.data.projectId,
        environmentId: this.data.environmentId,
        ...(this.data.entityType === 'application' 
          ? { applicationId: this.data.entityId }
          : { componentId: this.data.entityId })
      };

      // This would call the rollback service to get available versions
      // For now, we'll create mock data
      this.rollbackOptions = [
        {
          version: 'v1.0.0',
          deploymentId: 'dep1',
          deployedAt: new Date(Date.now() - 86400000).toISOString(),
          deployedBy: 'user1',
          status: 'succeeded'
        },
        {
          version: 'v0.9.0',
          deploymentId: 'dep2',
          deployedAt: new Date(Date.now() - 172800000).toISOString(),
          deployedBy: 'user2',
          status: 'succeeded'
        }
      ].filter(option => option.version !== this.data.currentVersion);

      resolve();
    });
  }

  onWorkflowChange(): void {
    const workflowId = this.rollbackForm.get('workflowId')?.value;
    if (workflowId) {
      const selectedWorkflow = this.workflows.find(w => w.id === workflowId);
      if (selectedWorkflow) {
        this.providers = (selectedWorkflow as any).providers || [];
      }
    }
  }

  onVersionSelect(option: RollbackOption): void {
    this.rollbackForm.patchValue({ toVersion: option.version });
  }

  isVersionSelected(version: string): boolean {
    return this.rollbackForm.get('toVersion')?.value === version;
  }

  onSubmit(): void {
    if (this.rollbackForm.valid) {
      this.submitting = true;
      
      const formValue = this.rollbackForm.value;
      const request: RollbackRequest = {
        projectId: this.data.projectId,
        environmentId: this.data.environmentId,
        toVersion: formValue.toVersion,
        workflowId: formValue.workflowId,
        providerType: formValue.providerType,
        requestedBy: 'current-user', // This should come from auth service
        reason: formValue.reason,
        parameters: formValue.parameters || {},
        configuration: formValue.configuration || {}
      };

      if (this.data.entityType === 'application') {
        request.applicationId = this.data.entityId;
      } else {
        request.componentId = this.data.entityId;
      }

      // For now, just emit the request
      this.submit.emit(request);
      this.close.emit(request);
      
      this.submitting = false;
    }
  }

  onCancel(): void {
    this.close.emit();
  }

  updateFormField(fieldName: string, event: any): void {
    try {
      const value = JSON.parse(event.target.value);
      this.rollbackForm.get(fieldName)?.setValue(value);
    } catch (e) {
      // Invalid JSON, ignore
    }
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  getSelectedOption(): RollbackOption | null {
    const selectedVersion = this.rollbackForm.get('toVersion')?.value;
    return this.rollbackOptions.find(option => option.version === selectedVersion) || null;
  }
}
