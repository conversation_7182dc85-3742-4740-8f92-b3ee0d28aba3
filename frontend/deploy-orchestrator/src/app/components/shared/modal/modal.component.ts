import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';

export interface CreateApplicationGroupResult {
  name: string;
  description: string;
}

@Component({
  selector: 'app-modal',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div *ngIf="isOpen" class="fixed inset-0 z-50 flex items-center justify-center overflow-x-hidden overflow-y-auto outline-none focus:outline-none" (click)="onBackdropClick($event)">
      <div class="fixed inset-0 bg-black bg-opacity-50"></div>
      
      <div class="relative w-auto max-w-3xl mx-auto my-6">
        <div class="relative flex flex-col w-full bg-white border-0 rounded-lg shadow-lg outline-none focus:outline-none">
          <!-- Header -->
          <div class="flex items-start justify-between p-5 border-b border-solid border-gray-200 rounded-t">
            <div class="flex items-center space-x-3">
              <ng-content select="[slot=icon]"></ng-content>
              <div>
                <h3 class="text-lg font-semibold text-gray-900">
                  <ng-content select="[slot=title]"></ng-content>
                </h3>
                <p class="text-sm text-gray-500 mt-1">
                  <ng-content select="[slot=subtitle]"></ng-content>
                </p>
              </div>
            </div>
            <button
              class="p-1 ml-auto bg-transparent border-0 text-gray-400 hover:text-gray-600 text-3xl leading-none font-semibold outline-none focus:outline-none"
              type="button"
              (click)="close()"
            >
              <span class="bg-transparent text-gray-400 hover:text-gray-600 h-6 w-6 text-2xl block outline-none focus:outline-none">
                ×
              </span>
            </button>
          </div>
          
          <!-- Body -->
          <div class="relative p-6 flex-auto">
            <ng-content></ng-content>
          </div>
          
          <!-- Footer -->
          <div class="flex items-center justify-end p-6 border-t border-solid border-gray-200 rounded-b">
            <ng-content select="[slot=footer]"></ng-content>
          </div>
        </div>
      </div>
    </div>
  `
})
export class ModalComponent {
  @Input() isOpen = false;
  @Output() modalClose = new EventEmitter<void>();

  close(): void {
    this.modalClose.emit();
  }

  onBackdropClick(event: Event): void {
    if (event.target === event.currentTarget) {
      this.close();
    }
  }
}

@Component({
  selector: 'app-create-application-group-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, ModalComponent],
  template: `
    <app-modal [isOpen]="isOpen" (modalClose)="close()">
      <!-- Icon -->
      <div slot="icon" class="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
        </svg>
      </div>

      <!-- Title -->
      <span slot="title">Create Application Group</span>
      
      <!-- Subtitle -->
      <span slot="subtitle">Create a new group to organize your applications</span>

      <!-- Form Content -->
      <form [formGroup]="applicationGroupForm" class="space-y-6">
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
            Group Name *
          </label>
          <div class="relative">
            <input
              type="text"
              id="name"
              formControlName="name"
              placeholder="Enter application group name"
              maxlength="100"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              [class.border-red-500]="applicationGroupForm.get('name')?.invalid && applicationGroupForm.get('name')?.touched"
            >
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
              </svg>
            </div>
          </div>
          
          <!-- Error Messages -->
          <div *ngIf="applicationGroupForm.get('name')?.invalid && applicationGroupForm.get('name')?.touched" class="mt-1">
            <p *ngIf="applicationGroupForm.get('name')?.hasError('required')" class="text-sm text-red-600">
              Group name is required
            </p>
            <p *ngIf="applicationGroupForm.get('name')?.hasError('minlength')" class="text-sm text-red-600">
              Group name must be at least 3 characters long
            </p>
          </div>
          
          <!-- Character count -->
          <p class="mt-1 text-sm text-gray-500">
            {{ applicationGroupForm.get('name')?.value?.length || 0 }}/100
          </p>
        </div>

        <div>
          <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
            Description (Optional)
          </label>
          <div class="relative">
            <textarea
              id="description"
              formControlName="description"
              placeholder="Enter a description for this application group"
              rows="3"
              maxlength="500"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
            ></textarea>
            <div class="absolute top-2 right-2 pointer-events-none">
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
          </div>
          
          <!-- Character count -->
          <p class="mt-1 text-sm text-gray-500">
            {{ applicationGroupForm.get('description')?.value?.length || 0 }}/500
          </p>
        </div>
      </form>

      <!-- Footer buttons -->
      <div slot="footer" class="flex space-x-3">
        <button
          type="button"
          (click)="close()"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="button"
          [disabled]="!applicationGroupForm.valid"
          (click)="createGroup()"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Create Group
        </button>
      </div>
    </app-modal>
  `
})
export class CreateApplicationGroupModalComponent implements OnInit {
  @Input() isOpen = false;
  @Input() projectId!: string;
  @Output() modalClose = new EventEmitter<void>();
  @Output() groupCreated = new EventEmitter<CreateApplicationGroupResult>();

  applicationGroupForm!: FormGroup;

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.applicationGroupForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(100)]],
      description: ['', [Validators.maxLength(500)]]
    });
  }

  close(): void {
    this.modalClose.emit();
  }

  createGroup(): void {
    if (this.applicationGroupForm.valid) {
      const result: CreateApplicationGroupResult = this.applicationGroupForm.value;
      this.groupCreated.emit(result);
      this.close();
    }
  }
}

export interface CreateApplicationResult {
  name: string;
  description: string;
  type: string;
  repository?: {
    url: string;
    branch: string;
  };
  tags: string[];
}

// Simple interface that matches the backend Go struct
export interface SimpleCreateApplicationRequest {
  name: string;
  description: string;
  groupId: string;
  repository: string;
  branch: string;
  buildCommand: string;
  startCommand: string;
  healthEndpoint: string;
  port: number;
}

@Component({
  selector: 'app-create-application-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  template: `
    <div *ngIf="isOpen" class="fixed inset-0 z-50 flex items-center justify-center overflow-x-hidden overflow-y-auto outline-none focus:outline-none bg-black/50 backdrop-blur-sm" (click)="onBackdropClick($event)">
      <div class="relative w-full max-w-2xl mx-auto my-6 max-h-[90vh] overflow-y-auto">
        <div class="relative flex flex-col w-full bg-white border-0 rounded-xl shadow-2xl outline-none focus:outline-none">
          <!-- Header -->
          <div class="flex items-center justify-between p-6 border-b border-gray-200 rounded-t-xl">
            <h2 class="text-xl font-semibold text-gray-900">Create New Application</h2>
            <button 
              type="button" 
              class="p-2 ml-auto bg-transparent border-0 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200" 
              (click)="close()">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Body -->
          <div class="relative p-6 flex-auto">
            <form [formGroup]="applicationForm" (ngSubmit)="createApplication()" class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                  <label for="name" class="block text-sm font-medium text-gray-700">Application Name *</label>
                  <input
                    type="text"
                    id="name"
                    formControlName="name"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    [class.border-red-500]="applicationForm.get('name')?.invalid && applicationForm.get('name')?.touched"
                    [class.focus:ring-red-500]="applicationForm.get('name')?.invalid && applicationForm.get('name')?.touched"
                    placeholder="Enter application name">
                  <div *ngIf="applicationForm.get('name')?.invalid && applicationForm.get('name')?.touched" class="mt-1 space-y-1">
                    <p *ngIf="applicationForm.get('name')?.errors?.['required']" class="text-sm text-red-600">Application name is required</p>
                    <p *ngIf="applicationForm.get('name')?.errors?.['minlength']" class="text-sm text-red-600">Name must be at least 3 characters</p>
                    <p *ngIf="applicationForm.get('name')?.errors?.['maxlength']" class="text-sm text-red-600">Name cannot exceed 100 characters</p>
                  </div>
                </div>

                <div class="space-y-2">
                  <label for="type" class="block text-sm font-medium text-gray-700">Application Type *</label>
                  <select 
                    id="type" 
                    formControlName="type" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    [class.border-red-500]="applicationForm.get('type')?.invalid && applicationForm.get('type')?.touched"
                    [class.focus:ring-red-500]="applicationForm.get('type')?.invalid && applicationForm.get('type')?.touched">
                    <option value="">Select application type</option>
                    <option value="web_application">Web Application</option>
                    <option value="microservice">Microservice</option>
                    <option value="database">Database</option>
                    <option value="message_queue">Message Queue</option>
                    <option value="cache">Cache</option>
                    <option value="api_gateway">API Gateway</option>
                    <option value="load_balancer">Load Balancer</option>
                    <option value="monitoring">Monitoring</option>
                    <option value="logging">Logging</option>
                    <option value="other">Other</option>
                  </select>
                  <div *ngIf="applicationForm.get('type')?.invalid && applicationForm.get('type')?.touched" class="mt-1">
                    <p *ngIf="applicationForm.get('type')?.errors?.['required']" class="text-sm text-red-600">Application type is required</p>
                  </div>
                </div>
              </div>

              <div class="space-y-2">
                <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                <textarea
                  id="description"
                  formControlName="description"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-all duration-200"
                  [class.border-red-500]="applicationForm.get('description')?.invalid && applicationForm.get('description')?.touched"
                  placeholder="Enter application description (optional)">
                </textarea>
                <div *ngIf="applicationForm.get('description')?.invalid && applicationForm.get('description')?.touched" class="mt-1">
                  <p *ngIf="applicationForm.get('description')?.errors?.['maxlength']" class="text-sm text-red-600">Description cannot exceed 500 characters</p>
                </div>
              </div>

              <!-- Repository Information -->
              <div class="bg-gray-50 p-4 rounded-lg space-y-4">
                <h4 class="text-base font-medium text-gray-900">Repository Information (Optional)</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="space-y-2">
                    <label for="repositoryUrl" class="block text-sm font-medium text-gray-700">Repository URL</label>
                    <input
                      type="url"
                      id="repositoryUrl"
                      formControlName="repositoryUrl"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      placeholder="https://github.com/user/repo.git">
                  </div>
                  <div class="space-y-2">
                    <label for="repositoryBranch" class="block text-sm font-medium text-gray-700">Branch</label>
                    <input
                      type="text"
                      id="repositoryBranch"
                      formControlName="repositoryBranch"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      placeholder="main">
                  </div>
                </div>
              </div>

              <!-- Tags -->
              <div class="space-y-2">
                <label for="tags" class="block text-sm font-medium text-gray-700">Tags (Optional)</label>
                <input
                  type="text"
                  id="tags"
                  formControlName="tags"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  placeholder="Enter tags separated by commas (e.g., frontend, react, api)">
                <p class="text-xs text-gray-500">Separate multiple tags with commas</p>
              </div>
            </form>
          </div>

          <!-- Footer -->
          <div class="flex items-center justify-end p-6 border-t border-gray-200 rounded-b-xl space-x-3">
            <button 
              type="button" 
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200" 
              (click)="close()">
              Cancel
            </button>
            <button 
              type="button" 
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200" 
              (click)="createApplication()"
              [disabled]="applicationForm.invalid || isLoading">
              <svg *ngIf="isLoading" class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg *ngIf="!isLoading" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              {{ isLoading ? 'Creating...' : 'Create Application' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: []
})
export class CreateApplicationModalComponent implements OnInit {
  @Input() isOpen = false;
  @Input() groupId!: string;
  @Output() modalClose = new EventEmitter<void>();
  @Output() applicationCreated = new EventEmitter<CreateApplicationResult>();

  applicationForm!: FormGroup;
  isLoading = false;

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.applicationForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(100)]],
      description: ['', [Validators.maxLength(500)]],
      type: ['', [Validators.required]],
      repositoryUrl: [''],
      repositoryBranch: ['main'],
      tags: ['']
    });
  }

  onBackdropClick(event: Event): void {
    if (event.target === event.currentTarget) {
      this.close();
    }
  }

  close(): void {
    this.modalClose.emit();
    this.resetForm();
  }

  createApplication(): void {
    if (this.applicationForm.valid && !this.isLoading) {
      this.isLoading = true;
      const formValue = this.applicationForm.value;
      
      const result: CreateApplicationResult = {
        name: formValue.name,
        description: formValue.description || '',
        type: formValue.type,
        tags: formValue.tags ? formValue.tags.split(',').map((tag: string) => tag.trim()).filter((tag: string) => tag) : []
      };

      // Add repository info if provided
      if (formValue.repositoryUrl) {
        result.repository = {
          url: formValue.repositoryUrl,
          branch: formValue.repositoryBranch || 'main'
        };
      }

      this.applicationCreated.emit(result);
      this.resetForm();
      this.isLoading = false;
    }
  }

  private resetForm(): void {
    this.applicationForm.reset({
      repositoryBranch: 'main'
    });
    this.isLoading = false;
  }
}
