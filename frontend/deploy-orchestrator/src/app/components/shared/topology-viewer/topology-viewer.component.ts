import { Component, Input, OnInit, OnDestroy, ElementRef, ViewChild, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Application, Component as AppComponent } from '../../../models/application.model';

interface Node {
  id: string;
  label: string;
  type: 'application' | 'component' | 'database' | 'external' | 'load-balancer';
  status: 'healthy' | 'warning' | 'error' | 'unknown';
  x: number;
  y: number;
  metadata?: any;
}

interface Edge {
  id: string;
  source: string;
  target: string;
  type: 'dependency' | 'communication' | 'data-flow';
  strength: number; // 1-3 (weak to strong dependency)
}

interface TopologyData {
  nodes: Node[];
  edges: Edge[];
}

@Component({
  selector: 'app-topology-viewer',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './topology-viewer.component.html',
  styleUrls: ['./topology-viewer.component.scss']
})
export class TopologyViewerComponent implements OnInit, OnDestroy, OnChanges {
  @Input() applications: Application[] = [];
  @Input() selectedApplication: Application | null = null;
  @Input() autoLayout = true;
  @Input() showEdgeLabels = false;
  @Input() animated = true;

  @ViewChild('topologyCanvas', { static: true }) svgRef!: ElementRef<SVGElement>;

  // Canvas properties
  canvasWidth = 800;
  canvasHeight = 600;

  // Topology data
  topology: TopologyData = { nodes: [], edges: [] };
  visibleNodes: Node[] = [];
  visibleEdges: Edge[] = [];

  // UI state
  loading = false;
  selectedNode: Node | null = null;
  selectedEdge: Edge | null = null;
  layoutType = 'force';
  filterType = 'all';
  animationRunning = false;

  // Mouse interaction state
  private isDragging = false;
  private dragNode: Node | null = null;
  private mousePos = { x: 0, y: 0 };

  // Tooltip state
  tooltip = {
    visible: false,
    x: 0,
    y: 0,
    node: null as Node | null
  };

  // Layout and animation
  private animationId: number | null = null;
  private simulation: any = null;

  // Legend data
  nodeTypes = [
    { type: 'application', label: 'Application', icon: '📱', color: '#3b82f6' },
    { type: 'component', label: 'Component', icon: '🔧', color: '#10b981' },
    { type: 'database', label: 'Database', icon: '🗄️', color: '#f59e0b' },
    { type: 'external', label: 'External Service', icon: '🌐', color: '#8b5cf6' },
    { type: 'load-balancer', label: 'Load Balancer', icon: '⚖️', color: '#ef4444' }
  ];

  statusTypes = [
    { status: 'healthy', label: 'Healthy', color: '#10b981' },
    { status: 'warning', label: 'Warning', color: '#f59e0b' },
    { status: 'error', label: 'Error', color: '#ef4444' },
    { status: 'unknown', label: 'Unknown', color: '#6b7280' }
  ];

  edgeTypes = [
    { type: 'dependency', label: 'Dependency', color: '#64748b' },
    { type: 'communication', label: 'Communication', color: '#3b82f6' },
    { type: 'data-flow', label: 'Data Flow', color: '#10b981' }
  ];

  ngOnInit(): void {
    this.updateCanvasSize();
    this.generateTopologyData();
    this.applyLayout();
    this.startAnimation();

    // Listen for window resize
    window.addEventListener('resize', this.updateCanvasSize.bind(this));
  }

  ngOnDestroy(): void {
    this.stopAnimation();
    window.removeEventListener('resize', this.updateCanvasSize.bind(this));
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['applications'] || changes['selectedApplication']) {
      this.generateTopologyData();
      this.applyLayout();
    }
  }

  private updateCanvasSize(): void {
    if (this.svgRef) {
      const rect = this.svgRef.nativeElement.getBoundingClientRect();
      this.canvasWidth = rect.width || 800;
      this.canvasHeight = Math.max(600, rect.height || 600);
    }
  }

  private generateTopologyData(): void {
    this.loading = true;
    
    const nodes: Node[] = [];
    const edges: Edge[] = [];

    // Generate nodes for applications
    this.applications.forEach((app, appIndex) => {
      // Add application node
      nodes.push({
        id: `app-${app.id}`,
        label: app.name,
        type: 'application',
        status: app.overallStatus as any,
        x: Math.random() * (this.canvasWidth - 100) + 50,
        y: Math.random() * (this.canvasHeight - 100) + 50,
        metadata: {
          description: app.description,
          version: app.version,
          environments: app.environments?.length || 0
        }
      });

      // Add component nodes
      app.components?.forEach((component, compIndex) => {
        const nodeId = `comp-${app.id}-${component.id}`;
        nodes.push({
          id: nodeId,
          label: component.name,
          type: 'component',
          status: component.healthStatus as any || 'unknown',
          x: Math.random() * (this.canvasWidth - 100) + 50,
          y: Math.random() * (this.canvasHeight - 100) + 50,
          metadata: {
            type: component.type,
            version: component.version,
            replicas: component.deploymentConfig?.replicas || 1
          }
        });

        // Add edge from application to component
        edges.push({
          id: `edge-app-${app.id}-comp-${component.id}`,
          source: `app-${app.id}`,
          target: nodeId,
          type: 'dependency',
          strength: 3
        });

        // Add dependencies between components
        component.dependencies?.forEach(dep => {
          const depNodeId = `comp-${app.id}-${dep.componentId}`;
          if (nodes.find(n => n.id === depNodeId)) {
            edges.push({
              id: `edge-${nodeId}-${depNodeId}`,
              source: nodeId,
              target: depNodeId,
              type: 'dependency',
              strength: 2
            });
          }
        });
      });
    });

    // Add some sample external dependencies
    if (nodes.length > 0) {
      // Database node
      nodes.push({
        id: 'db-main',
        label: 'Main Database',
        type: 'database',
        status: 'healthy',
        x: this.canvasWidth * 0.8,
        y: this.canvasHeight * 0.3,
        metadata: {
          type: 'PostgreSQL',
          version: '13.4',
          connections: 50
        }
      });

      // Load balancer
      nodes.push({
        id: 'lb-main',
        label: 'Load Balancer',
        type: 'load-balancer',
        status: 'healthy',
        x: this.canvasWidth * 0.2,
        y: this.canvasHeight * 0.2,
        metadata: {
          type: 'NGINX',
          requests_per_sec: 1500
        }
      });

      // External service
      nodes.push({
        id: 'ext-auth',
        label: 'Auth Service',
        type: 'external',
        status: 'warning',
        x: this.canvasWidth * 0.5,
        y: this.canvasHeight * 0.8,
        metadata: {
          provider: 'OAuth2',
          latency: '120ms'
        }
      });

      // Add connections to external services
      const appNodes = nodes.filter(n => n.type === 'application');
      appNodes.forEach(appNode => {
        edges.push({
          id: `edge-lb-${appNode.id}`,
          source: 'lb-main',
          target: appNode.id,
          type: 'communication',
          strength: 3
        });

        edges.push({
          id: `edge-${appNode.id}-db`,
          source: appNode.id,
          target: 'db-main',
          type: 'data-flow',
          strength: 2
        });

        edges.push({
          id: `edge-${appNode.id}-auth`,
          source: appNode.id,
          target: 'ext-auth',
          type: 'dependency',
          strength: 1
        });
      });
    }

    this.topology = { nodes, edges };
    this.applyFilter();
    this.loading = false;
  }

  applyLayout(): void {
    switch (this.layoutType) {
      case 'force':
        this.applyForceLayout();
        break;
      case 'hierarchical':
        this.applyHierarchicalLayout();
        break;
      case 'circular':
        this.applyCircularLayout();
        break;
      case 'grid':
        this.applyGridLayout();
        break;
    }
  }

  private applyForceLayout(): void {
    // Simple force-directed layout
    const nodes = [...this.visibleNodes];
    const edges = this.visibleEdges;

    // Simulate force-directed positioning
    for (let iteration = 0; iteration < 100; iteration++) {
      // Repulsion between nodes
      for (let i = 0; i < nodes.length; i++) {
        for (let j = i + 1; j < nodes.length; j++) {
          const dx = nodes[j].x - nodes[i].x;
          const dy = nodes[j].y - nodes[i].y;
          const distance = Math.sqrt(dx * dx + dy * dy) || 1;
          const force = 1000 / (distance * distance);
          
          const fx = (dx / distance) * force;
          const fy = (dy / distance) * force;
          
          nodes[i].x -= fx;
          nodes[i].y -= fy;
          nodes[j].x += fx;
          nodes[j].y += fy;
        }
      }

      // Attraction along edges
      edges.forEach(edge => {
        const source = nodes.find(n => n.id === edge.source);
        const target = nodes.find(n => n.id === edge.target);
        
        if (source && target) {
          const dx = target.x - source.x;
          const dy = target.y - source.y;
          const distance = Math.sqrt(dx * dx + dy * dy) || 1;
          const desiredDistance = 100;
          const force = (distance - desiredDistance) * 0.01;
          
          const fx = (dx / distance) * force;
          const fy = (dy / distance) * force;
          
          source.x += fx;
          source.y += fy;
          target.x -= fx;
          target.y -= fy;
        }
      });

      // Keep nodes within bounds
      nodes.forEach(node => {
        node.x = Math.max(50, Math.min(this.canvasWidth - 50, node.x));
        node.y = Math.max(50, Math.min(this.canvasHeight - 50, node.y));
      });
    }

    this.visibleNodes = nodes;
  }

  private applyHierarchicalLayout(): void {
    const nodes = [...this.visibleNodes];
    const levels: { [key: number]: Node[] } = {};
    
    // Assign levels based on node type
    nodes.forEach(node => {
      let level = 0;
      switch (node.type) {
        case 'external':
        case 'load-balancer':
          level = 0;
          break;
        case 'application':
          level = 1;
          break;
        case 'component':
          level = 2;
          break;
        case 'database':
          level = 3;
          break;
      }
      
      if (!levels[level]) levels[level] = [];
      levels[level].push(node);
    });

    // Position nodes in levels
    Object.keys(levels).forEach(levelStr => {
      const level = parseInt(levelStr);
      const levelNodes = levels[level];
      const y = (this.canvasHeight / (Object.keys(levels).length + 1)) * (level + 1);
      
      levelNodes.forEach((node, index) => {
        node.x = (this.canvasWidth / (levelNodes.length + 1)) * (index + 1);
        node.y = y;
      });
    });

    this.visibleNodes = nodes;
  }

  private applyCircularLayout(): void {
    const nodes = [...this.visibleNodes];
    const centerX = this.canvasWidth / 2;
    const centerY = this.canvasHeight / 2;
    const radius = Math.min(centerX, centerY) * 0.8;

    nodes.forEach((node, index) => {
      const angle = (2 * Math.PI * index) / nodes.length;
      node.x = centerX + radius * Math.cos(angle);
      node.y = centerY + radius * Math.sin(angle);
    });

    this.visibleNodes = nodes;
  }

  private applyGridLayout(): void {
    const nodes = [...this.visibleNodes];
    const cols = Math.ceil(Math.sqrt(nodes.length));
    const cellWidth = this.canvasWidth / cols;
    const cellHeight = this.canvasHeight / Math.ceil(nodes.length / cols);

    nodes.forEach((node, index) => {
      const row = Math.floor(index / cols);
      const col = index % cols;
      
      node.x = (col + 0.5) * cellWidth;
      node.y = (row + 0.5) * cellHeight;
    });

    this.visibleNodes = nodes;
  }

  applyFilter(): void {
    let filteredNodes = [...this.topology.nodes];

    switch (this.filterType) {
      case 'healthy':
        filteredNodes = filteredNodes.filter(n => n.status === 'healthy');
        break;
      case 'issues':
        filteredNodes = filteredNodes.filter(n => n.status === 'warning' || n.status === 'error');
        break;
      case 'critical':
        // Show only nodes in critical path (simplified)
        filteredNodes = filteredNodes.filter(n => n.type === 'application' || n.type === 'database');
        break;
    }

    this.visibleNodes = filteredNodes;
    
    // Filter edges to only show connections between visible nodes
    this.visibleEdges = this.topology.edges.filter(edge => 
      filteredNodes.some(n => n.id === edge.source) && 
      filteredNodes.some(n => n.id === edge.target)
    );
  }

  resetView(): void {
    this.generateTopologyData();
    this.applyLayout();
  }

  toggleAnimation(): void {
    if (this.animationRunning) {
      this.stopAnimation();
    } else {
      this.startAnimation();
    }
  }

  private startAnimation(): void {
    if (!this.animated) return;
    
    this.animationRunning = true;
    
    const animate = () => {
      if (this.animationRunning && this.layoutType === 'force') {
        this.applyForceLayout();
        this.animationId = requestAnimationFrame(animate);
      }
    };
    
    animate();
  }

  private stopAnimation(): void {
    this.animationRunning = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  // UI Event handlers
  selectNode(node: Node): void {
    this.selectedNode = node;
  }

  selectNodeById(nodeId: string): void {
    const node = this.getNodeById(nodeId);
    if (node) {
      this.selectNode(node);
    }
  }

  closeDetails(): void {
    this.selectedNode = null;
  }

  onNodeHover(node: Node, event: MouseEvent): void {
    // Could show tooltip or highlight connected nodes
  }

  onNodeLeave(node: Node): void {
    // Clean up hover effects
  }

  // Helper methods for rendering
  getNodeRadius(node: Node): number {
    switch (node.type) {
      case 'application': return 25;
      case 'database': return 20;
      case 'load-balancer': return 18;
      default: return 15;
    }
  }

  getNodeColor(node: Node): string {
    const nodeType = this.nodeTypes.find(t => t.type === node.type);
    return nodeType?.color || '#6b7280';
  }

  getNodeBorderColor(node: Node): string {
    return this.selectedNode?.id === node.id ? '#2563eb' : this.getStatusColor(node.status);
  }

  getNodeIcon(node: Node): string {
    const nodeType = this.nodeTypes.find(t => t.type === node.type);
    return nodeType?.icon || '⚪';
  }

  getNodeIconSize(node: Node): number {
    return this.getNodeRadius(node) * 0.8;
  }

  getNodeIconOffset(node: Node): number {
    return this.getNodeIconSize(node) * 0.3;
  }

  getStatusColor(status: string): string {
    const statusType = this.statusTypes.find(s => s.status === status);
    return statusType?.color || '#6b7280';
  }

  getNodeClass(node: Node): string {
    return `node-${node.type} status-${node.status} ${this.selectedNode?.id === node.id ? 'selected' : ''}`;
  }

  getEdgePath(edge: Edge): string {
    const source = this.visibleNodes.find(n => n.id === edge.source);
    const target = this.visibleNodes.find(n => n.id === edge.target);
    
    if (!source || !target) return '';
    
    return `M ${source.x} ${source.y} L ${target.x} ${target.y}`;
  }

  getEdgeStrokeWidth(edge: Edge): number {
    return edge.strength;
  }

  getEdgeClass(edge: Edge): string {
    return `edge-${edge.type}`;
  }

  getEdgeLabel(edge: Edge): string {
    return edge.type;
  }

  getEdgeLabelX(edge: Edge): number {
    const source = this.visibleNodes.find(n => n.id === edge.source);
    const target = this.visibleNodes.find(n => n.id === edge.target);
    return source && target ? (source.x + target.x) / 2 : 0;
  }

  getEdgeLabelY(edge: Edge): number {
    const source = this.visibleNodes.find(n => n.id === edge.source);
    const target = this.visibleNodes.find(n => n.id === edge.target);
    return source && target ? (source.y + target.y) / 2 : 0;
  }

  getNodeById(nodeId: string): Node | undefined {
    return this.visibleNodes.find(n => n.id === nodeId);
  }

  getConnectedNodes(nodeId: string): string[] {
    const connected: string[] = [];
    
    this.visibleEdges.forEach(edge => {
      if (edge.source === nodeId) {
        connected.push(edge.target);
      } else if (edge.target === nodeId) {
        connected.push(edge.source);
      }
    });
    
    return connected;
  }

  getMetadataArray(metadata: any): { key: string; value: any }[] {
    if (!metadata) return [];
    
    return Object.keys(metadata).map(key => ({
      key: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
      value: metadata[key]
    }));
  }

  // TrackBy functions for performance
  trackByNodeId(index: number, node: Node): string {
    return node.id;
  }

  trackByEdgeId(index: number, edge: Edge): string {
    return edge.id;
  }

  // Additional methods for template compatibility
  selectEdge(edge: Edge): void {
    this.selectedEdge = edge;
  }

  startDragging(node: Node, event: MouseEvent): void {
    this.isDragging = true;
    this.dragNode = node;
    this.mousePos = { x: event.clientX, y: event.clientY };
    event.preventDefault();
  }

  showNodeTooltip(node: Node, event: MouseEvent): void {
    this.tooltip = {
      visible: true,
      x: event.clientX + 10,
      y: event.clientY - 10,
      node: node
    };
  }

  hideNodeTooltip(): void {
    this.tooltip = {
      visible: false,
      x: 0,
      y: 0,
      node: null
    };
  }

  // Canvas interaction methods
  onCanvasMouseDown(event: MouseEvent): void {
    // Handle canvas mouse down
  }

  onCanvasMouseMove(event: MouseEvent): void {
    if (this.isDragging && this.dragNode) {
      const deltaX = event.clientX - this.mousePos.x;
      const deltaY = event.clientY - this.mousePos.y;
      
      this.dragNode.x += deltaX;
      this.dragNode.y += deltaY;
      
      this.mousePos = { x: event.clientX, y: event.clientY };
    }
  }

  onCanvasMouseUp(event: MouseEvent): void {
    this.isDragging = false;
    this.dragNode = null;
  }

  onCanvasWheel(event: WheelEvent): void {
    // Handle zoom functionality if needed
    event.preventDefault();
  }

  // Utility methods for template
  objectKeys(obj: any): string[] {
    return obj ? Object.keys(obj) : [];
  }

  formatLabel(key: string): string {
    return key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1');
  }

  navigateToNode(node: Node): void {
    // TODO: Implement navigation to node details
    console.log('Navigate to node:', node);
  }

  focusOnNode(node: Node): void {
    // Center the view on the selected node
    const centerX = this.canvasWidth / 2;
    const centerY = this.canvasHeight / 2;
    
    const deltaX = centerX - node.x;
    const deltaY = centerY - node.y;
    
    // Move all nodes to center the selected node
    this.visibleNodes.forEach(n => {
      n.x += deltaX;
      n.y += deltaY;
    });
  }
}
