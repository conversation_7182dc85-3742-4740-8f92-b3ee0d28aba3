<div class="topology-viewer">
  <div class="topology-controls">
    <div class="control-group">
      <label>Layout:</label>
      <select [(ngModel)]="layoutType" (change)="applyLayout()" class="control-select">
        <option value="force">Force Layout</option>
        <option value="hierarchical">Hierarchical</option>
        <option value="circular">Circular</option>
        <option value="grid">Grid</option>
      </select>
    </div>
    
    <div class="control-group">
      <label>Filter:</label>
      <select [(ngModel)]="filterType" (change)="applyFilter()" class="control-select">
        <option value="all">All Components</option>
        <option value="healthy">Healthy Only</option>
        <option value="issues">With Issues</option>
        <option value="critical">Critical Path</option>
      </select>
    </div>
    
    <div class="control-group">
      <button (click)="toggleAnimation()" class="control-button">
        <i class="fas" [class.fa-pause]="animationRunning" [class.fa-play]="!animationRunning"></i>
        {{ animationRunning ? 'Pause' : 'Resume' }}
      </button>
    </div>
    
    <div class="control-group">
      <button (click)="resetView()" class="control-button">
        <i class="fas fa-sync-alt"></i>
        Reset View
      </button>
    </div>
  </div>

  <div class="topology-main">
    <div class="topology-canvas-container">
      <svg #topologyCanvas class="topology-canvas" 
           [attr.width]="canvasWidth" 
           [attr.height]="canvasHeight"
           (mousedown)="onCanvasMouseDown($event)"
           (mousemove)="onCanvasMouseMove($event)"
           (mouseup)="onCanvasMouseUp($event)"
           (wheel)="onCanvasWheel($event)">
        
        <!-- Background Grid -->
        <defs>
          <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f0f0f0" stroke-width="1"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
        
        <!-- Edges -->
        <g class="edges-group">
          <line *ngFor="let edge of visibleEdges"
                [attr.x1]="getNodeById(edge.source)?.x"
                [attr.y1]="getNodeById(edge.source)?.y"
                [attr.x2]="getNodeById(edge.target)?.x"
                [attr.y2]="getNodeById(edge.target)?.y"
                [class]="'edge edge-' + edge.type"
                [attr.stroke-width]="edge.strength * 2"
                (click)="selectEdge(edge)">
          </line>
        </g>
        
        <!-- Nodes -->
        <g class="nodes-group">
          <g *ngFor="let node of visibleNodes; trackBy: trackByNodeId"
             [attr.transform]="'translate(' + node.x + ',' + node.y + ')'"
             [class]="'node node-' + node.type + ' node-' + node.status"
             (click)="selectNode(node)"
             (mousedown)="startDragging(node, $event)"
             (mouseenter)="showNodeTooltip(node, $event)"
             (mouseleave)="hideNodeTooltip()">
            
            <!-- Node Circle -->
            <circle [attr.r]="getNodeRadius(node)"
                    [class]="'node-circle node-' + node.status"
                    [attr.fill]="getNodeColor(node)">
            </circle>
            
            <!-- Node Icon -->
            <text [attr.dy]="4" 
                  text-anchor="middle" 
                  class="node-icon"
                  [attr.font-size]="getNodeRadius(node) * 0.8">
              {{ getNodeIcon(node) }}
            </text>
            
            <!-- Node Label -->
            <text [attr.dy]="getNodeRadius(node) + 15" 
                  text-anchor="middle" 
                  class="node-label">
              {{ node.label }}
            </text>
          </g>
        </g>
      </svg>
      
      <!-- Loading overlay -->
      <div *ngIf="loading" class="topology-loading">
        <div class="loading-spinner"></div>
        <p>Loading topology...</p>
      </div>
      
      <!-- Empty state -->
      <div *ngIf="!loading && visibleNodes.length === 0" class="topology-empty">
        <div class="empty-icon">
          <i class="fas fa-project-diagram"></i>
        </div>
        <h3>No topology data available</h3>
        <p>Add some applications and components to see the topology visualization.</p>
      </div>
    </div>
    
    <!-- Legend -->
    <div class="topology-legend">
      <h4>Legend</h4>
      
      <div class="legend-section">
        <h5>Node Types</h5>
        <div class="legend-item">
          <div class="legend-node node-application"></div>
          <span>Application</span>
        </div>
        <div class="legend-item">
          <div class="legend-node node-component"></div>
          <span>Component</span>
        </div>
        <div class="legend-item">
          <div class="legend-node node-database"></div>
          <span>Database</span>
        </div>
        <div class="legend-item">
          <div class="legend-node node-external"></div>
          <span>External Service</span>
        </div>
      </div>
      
      <div class="legend-section">
        <h5>Health Status</h5>
        <div class="legend-item">
          <div class="legend-status status-healthy"></div>
          <span>Healthy</span>
        </div>
        <div class="legend-item">
          <div class="legend-status status-warning"></div>
          <span>Warning</span>
        </div>
        <div class="legend-item">
          <div class="legend-status status-error"></div>
          <span>Error</span>
        </div>
        <div class="legend-item">
          <div class="legend-status status-unknown"></div>
          <span>Unknown</span>
        </div>
      </div>
      
      <div class="legend-section">
        <h5>Connections</h5>
        <div class="legend-item">
          <div class="legend-edge edge-dependency"></div>
          <span>Dependency</span>
        </div>
        <div class="legend-item">
          <div class="legend-edge edge-communication"></div>
          <span>Communication</span>
        </div>
        <div class="legend-item">
          <div class="legend-edge edge-data-flow"></div>
          <span>Data Flow</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Node Detail Panel -->
  <div *ngIf="selectedNode" class="node-detail-panel">
    <div class="panel-header">
      <h4>{{ selectedNode.label }}</h4>
      <button (click)="selectedNode = null" class="close-button">
        <i class="fas fa-times"></i>
      </button>
    </div>
    
    <div class="panel-content">
      <div class="detail-section">
        <h5>Basic Information</h5>
        <div class="detail-row">
          <span class="detail-label">Type:</span>
          <span class="detail-value">{{ selectedNode.type }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Status:</span>
          <span class="detail-value">
            <span [class]="'status-badge status-' + selectedNode.status">
              {{ selectedNode.status }}
            </span>
          </span>
        </div>
      </div>
      
      <div *ngIf="selectedNode.metadata" class="detail-section">
        <h5>Metadata</h5>
        <div *ngFor="let item of objectKeys(selectedNode.metadata)" class="detail-row">
          <span class="detail-label">{{ formatLabel(item) }}:</span>
          <span class="detail-value">{{ selectedNode.metadata[item] }}</span>
        </div>
      </div>
      
      <div class="detail-section">
        <h5>Actions</h5>
        <button (click)="navigateToNode(selectedNode)" class="action-button">
          <i class="fas fa-external-link-alt"></i>
          View Details
        </button>
        <button (click)="focusOnNode(selectedNode)" class="action-button">
          <i class="fas fa-crosshairs"></i>
          Focus on Node
        </button>
      </div>
    </div>
  </div>

  <!-- Tooltip -->
  <div *ngIf="tooltip.visible" 
       class="topology-tooltip"
       [style.left.px]="tooltip.x"
       [style.top.px]="tooltip.y">
    <div class="tooltip-content">
      <strong>{{ tooltip.node?.label }}</strong>
      <div>Type: {{ tooltip.node?.type }}</div>
      <div>Status: {{ tooltip.node?.status }}</div>
      <div *ngIf="tooltip.node?.metadata?.version">Version: {{ tooltip.node?.metadata?.version }}</div>
    </div>
  </div>
</div>
