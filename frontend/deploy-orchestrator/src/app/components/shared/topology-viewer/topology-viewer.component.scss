.topology-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8fafc;
  border-radius: 12px;
  overflow: hidden;

  .topology-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: white;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .control-group {
      display: flex;
      align-items: center;
      gap: 8px;

      label {
        font-size: 0.875rem;
        color: #374151;
        font-weight: 500;
        min-width: fit-content;
      }

      .control-select {
        padding: 6px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 0.875rem;
        background: white;
        min-width: 140px;

        &:focus {
          outline: none;
          border-color: #2563eb;
          box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
      }
    }

    .control-actions {
      display: flex;
      gap: 8px;

      .btn {
        padding: 6px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 0.875rem;
        background: white;
        color: #374151;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background: #f9fafb;
          border-color: #9ca3af;
        }

        &.btn-outline-primary {
          color: #2563eb;
          border-color: #2563eb;

          &:hover {
            background: #eff6ff;
          }
        }

        i {
          margin-right: 4px;
        }
      }
    }
  }

  .topology-canvas {
    flex: 1;
    position: relative;
    background: white;
    overflow: hidden;

    .topology-svg {
      width: 100%;
      height: 100%;
      cursor: grab;

      &:active {
        cursor: grabbing;
      }

      // Node styles
      .nodes-group {
        .node {
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            transform: scale(1.1);
          }

          &.selected {
            filter: drop-shadow(0 0 8px rgba(37, 99, 235, 0.5));
          }

          .node-icon {
            fill: white;
            font-weight: 600;
            font-family: system-ui, -apple-system, sans-serif;
          }

          .node-label {
            fill: #374151;
            font-size: 12px;
            font-weight: 500;
            font-family: system-ui, -apple-system, sans-serif;
          }

          // Status-based styles
          &.status-healthy circle {
            filter: drop-shadow(0 2px 4px rgba(16, 185, 129, 0.3));
          }

          &.status-warning circle {
            filter: drop-shadow(0 2px 4px rgba(245, 158, 11, 0.3));
          }

          &.status-error circle {
            filter: drop-shadow(0 2px 4px rgba(239, 68, 68, 0.3));
            animation: pulse 2s infinite;
          }

          &.status-unknown circle {
            filter: drop-shadow(0 2px 4px rgba(107, 114, 128, 0.3));
          }
        }
      }

      // Edge styles
      .edges-group {
        .edge {
          path {
            fill: none;
            stroke: #64748b;
            stroke-opacity: 0.6;
            transition: all 0.2s ease;
          }

          &.edge-dependency path {
            stroke: #64748b;
            stroke-dasharray: none;
          }

          &.edge-communication path {
            stroke: #3b82f6;
            stroke-dasharray: 5,5;
          }

          &.edge-data-flow path {
            stroke: #10b981;
            stroke-dasharray: 10,5;
          }

          &:hover path {
            stroke-opacity: 1;
            stroke-width: 3 !important;
          }

          .edge-label {
            fill: #6b7280;
            font-size: 10px;
            font-family: system-ui, -apple-system, sans-serif;
            text-anchor: middle;
            opacity: 0;
            transition: opacity 0.2s ease;
          }
        }

        &:hover .edge-label {
          opacity: 1;
        }
      }
    }

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.9);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 10;

      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #2563eb;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
      }

      p {
        color: #6b7280;
        font-size: 0.875rem;
      }
    }
  }

  .topology-legend {
    width: 250px;
    background: white;
    border-left: 1px solid #e5e7eb;
    padding: 24px;
    overflow-y: auto;

    h5 {
      font-size: 1rem;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 16px;
    }

    .legend-section {
      margin-bottom: 24px;

      h6 {
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 12px;
      }

      .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        font-size: 0.875rem;
        color: #6b7280;

        .legend-icon {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          color: white;
          font-weight: 600;
        }

        .legend-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
        }

        .legend-line {
          width: 20px;
          height: 0;
          border-top: 2px solid;
        }
      }
    }
  }

  .node-details-panel {
    position: absolute;
    top: 80px;
    right: 24px;
    width: 300px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 20;
    max-height: calc(100% - 120px);
    overflow: hidden;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #e5e7eb;
      background: #f8fafc;

      h5 {
        font-size: 1rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }

      .btn-close {
        background: none;
        border: none;
        color: #6b7280;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;

        &:hover {
          background: #e5e7eb;
          color: #374151;
        }
      }
    }

    .panel-content {
      padding: 20px;
      overflow-y: auto;

      .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        font-size: 0.875rem;

        strong {
          color: #374151;
          font-weight: 600;
        }

        .type-badge, .status-badge {
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 0.75rem;
          font-weight: 500;
          text-transform: uppercase;

          &.type-application {
            background: #dbeafe;
            color: #1d4ed8;
          }

          &.type-component {
            background: #dcfce7;
            color: #166534;
          }

          &.type-database {
            background: #fef3c7;
            color: #92400e;
          }

          &.type-external {
            background: #e0e7ff;
            color: #5b21b6;
          }

          &.type-load-balancer {
            background: #fee2e2;
            color: #991b1b;
          }

          &.status-healthy {
            background: #dcfce7;
            color: #166534;
          }

          &.status-warning {
            background: #fef3c7;
            color: #92400e;
          }

          &.status-error {
            background: #fee2e2;
            color: #991b1b;
          }

          &.status-unknown {
            background: #f3f4f6;
            color: #374151;
          }
        }
      }

      .metadata-section {
        margin-top: 16px;
        
        strong {
          color: #374151;
          font-weight: 600;
          font-size: 0.875rem;
        }

        .metadata-content {
          margin-top: 8px;
          background: #f8fafc;
          border-radius: 6px;
          padding: 12px;

          .metadata-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 6px;
            font-size: 0.8rem;

            &:last-child {
              margin-bottom: 0;
            }

            .metadata-key {
              color: #6b7280;
              font-weight: 500;
            }

            .metadata-value {
              color: #374151;
              font-weight: 600;
            }
          }
        }
      }

      .connected-nodes {
        margin-top: 16px;

        strong {
          color: #374151;
          font-weight: 600;
          font-size: 0.875rem;
        }

        .connected-list {
          margin-top: 8px;

          .connected-item {
            padding: 6px 12px;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            margin-bottom: 4px;
            font-size: 0.8rem;
            color: #374151;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
              background: #eff6ff;
              border-color: #2563eb;
              color: #1d4ed8;
            }

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

// Animations
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

// Responsive design
@media (max-width: 1200px) {
  .topology-viewer {
    .topology-legend {
      width: 200px;
      padding: 16px;
    }

    .node-details-panel {
      width: 250px;
    }
  }
}

@media (max-width: 768px) {
  .topology-viewer {
    .topology-controls {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .control-group {
        justify-content: space-between;
      }

      .control-actions {
        justify-content: center;
      }
    }

    .topology-legend {
      display: none; // Hide legend on mobile
    }

    .node-details-panel {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 90vw;
      max-width: 400px;
      max-height: 80vh;
    }
  }
}
