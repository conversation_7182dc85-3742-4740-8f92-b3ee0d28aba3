<!-- Desktop sidebar -->
<div class="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
  <div class="flex-1 flex flex-col min-h-0 bg-gray-800">
    <div class="flex items-center h-16 flex-shrink-0 px-4 bg-gray-900">
      <div class="text-white font-bold text-xl">Deploy Orchestrator</div>
    </div>
    <div class="flex-1 flex flex-col overflow-y-auto">
      <nav class="flex-1 px-2 py-4 space-y-1">
        <!-- Dynamic Navigation Items -->
        <ng-container *ngFor="let item of navigationItems$ | async">
          <div *ngIf="item.visible" class="pb-3">
            <!-- Section Header (if it's a top-level item with children) -->
            <p *ngIf="hasChildren(item)" class="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">
              {{ item.label }}
            </p>
            
            <!-- Single Navigation Item -->
            <a *ngIf="!hasChildren(item)" 
               (click)="navigateTo(item.route)"
               [class]="isActiveRoute(item.route) ? 'bg-gray-900 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'"
               class="group flex items-center px-2 py-2 text-sm font-medium rounded-md cursor-pointer">
              <i [class]="item.icon" class="text-gray-400 group-hover:text-gray-300 mr-3 flex-shrink-0 h-6 w-6"></i>
              <span class="flex-1">{{ item.label }}</span>
              <span *ngIf="item.badge" [class]="getBadgeClass(item.badge.color)" 
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-2">
                {{ item.badge.text }}
              </span>
            </a>
            
            <!-- Navigation Items with Children -->
            <ng-container *ngIf="hasChildren(item)">
              <div *ngFor="let child of getVisibleChildren(item)">
                <a (click)="navigateTo(child.route)"
                   [class]="isActiveRoute(child.route) ? 'bg-gray-900 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'"
                   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md cursor-pointer">
                  <i [class]="child.icon" class="text-gray-400 group-hover:text-gray-300 mr-3 flex-shrink-0 h-6 w-6"></i>
                  <span class="flex-1">{{ child.label }}</span>
                  <span *ngIf="child.badge" [class]="getBadgeClass(child.badge.color)" 
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-2">
                    {{ child.badge.text }}
                  </span>
                  <i *ngIf="child.adminOnly" class="fas fa-crown text-yellow-400 ml-2 text-xs"></i>
                </a>
              </div>
            </ng-container>
          </div>
        </ng-container>
      </nav>
    </div>
    
    <!-- User profile section -->
    <div class="flex-shrink-0 flex bg-gray-700 p-4">
      <div class="flex-shrink-0 w-full group block">
        <div class="flex items-center">
          <div>
            <div class="inline-block h-9 w-9 rounded-full bg-gray-500 text-white flex items-center justify-center">
              {{ username.charAt(0).toUpperCase() }}
            </div>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-white">{{ username }}</p>
            <div class="flex items-center space-x-2">
              <button (click)="logout()" class="text-xs font-medium text-gray-300 hover:text-white">
                Logout
              </button>
              <span *ngIf="isAdmin" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                <i class="fas fa-crown mr-1"></i>Admin
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Mobile menu -->
<div class="md:hidden">
  <div class="fixed top-0 inset-x-0 z-40 bg-gray-800 p-2">
    <div class="flex items-center justify-between">
      <div class="text-white font-bold text-xl">Deploy Orchestrator</div>
      <button (click)="toggleMobileMenu()" class="text-gray-400 hover:text-white">
        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path *ngIf="!isMobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          <path *ngIf="isMobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
  </div>

  <!-- Mobile menu, show/hide based on menu state -->
  <div *ngIf="isMobileMenuOpen" class="fixed inset-0 z-30 flex">
    <div class="fixed inset-0 bg-gray-600 bg-opacity-75" (click)="toggleMobileMenu()"></div>
    <div class="relative flex-1 flex flex-col max-w-xs w-full bg-gray-800">
      <div class="flex-1 h-0 pt-16 pb-4 overflow-y-auto">
        <nav class="mt-5 px-2 space-y-1">
          <!-- Mobile Dynamic Navigation Items -->
          <ng-container *ngFor="let item of navigationItems$ | async">
            <div *ngIf="item.visible" class="pb-3">
              <!-- Section Header (if it's a top-level item with children) -->
              <p *ngIf="hasChildren(item)" class="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                {{ item.label }}
              </p>
              
              <!-- Single Navigation Item -->
              <a *ngIf="!hasChildren(item)" 
                 (click)="navigateTo(item.route)"
                 [class]="isActiveRoute(item.route) ? 'bg-gray-900 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'"
                 class="group flex items-center px-2 py-2 text-sm font-medium rounded-md cursor-pointer">
                <i [class]="item.icon" class="text-gray-400 group-hover:text-gray-300 mr-3 flex-shrink-0 h-6 w-6"></i>
                <span class="flex-1">{{ item.label }}</span>
                <span *ngIf="item.badge" [class]="getBadgeClass(item.badge.color)" 
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-2">
                  {{ item.badge.text }}
                </span>
              </a>
              
              <!-- Navigation Items with Children -->
              <ng-container *ngIf="hasChildren(item)">
                <div *ngFor="let child of getVisibleChildren(item)">
                  <a (click)="navigateTo(child.route)"
                     [class]="isActiveRoute(child.route) ? 'bg-gray-900 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'"
                     class="group flex items-center px-2 py-2 text-sm font-medium rounded-md cursor-pointer">
                    <i [class]="child.icon" class="text-gray-400 group-hover:text-gray-300 mr-3 flex-shrink-0 h-6 w-6"></i>
                    <span class="flex-1">{{ child.label }}</span>
                    <span *ngIf="child.badge" [class]="getBadgeClass(child.badge.color)" 
                          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-2">
                      {{ child.badge.text }}
                    </span>
                    <i *ngIf="child.adminOnly" class="fas fa-crown text-yellow-400 ml-2 text-xs"></i>
                  </a>
                </div>
              </ng-container>
            </div>
          </ng-container>
        </nav>
      </div>
      
      <!-- Mobile User Profile -->
      <div class="flex-shrink-0 flex bg-gray-700 p-4">
        <div class="flex-shrink-0 w-full group block">
          <div class="flex items-center">
            <div>
              <div class="inline-block h-9 w-9 rounded-full bg-gray-500 text-white flex items-center justify-center">
                {{ username.charAt(0).toUpperCase() }}
              </div>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-white">{{ username }}</p>
              <div class="flex items-center space-x-2">
                <button (click)="logout()" class="text-xs font-medium text-gray-300 hover:text-white">
                  Logout
                </button>
                <span *ngIf="isAdmin" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                  <i class="fas fa-crown mr-1"></i>Admin
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Permission Loading State -->
<div *ngIf="!(navigationItems$ | async)" class="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
  <div class="flex-1 flex flex-col min-h-0 bg-gray-800">
    <div class="flex items-center h-16 flex-shrink-0 px-4 bg-gray-900">
      <div class="text-white font-bold text-xl">Deploy Orchestrator</div>
    </div>
    <div class="flex-1 flex items-center justify-center">
      <div class="text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        <p class="mt-2 text-sm text-gray-400">Loading permissions...</p>
      </div>
    </div>
  </div>
</div>

<!-- No Access State -->
<div *ngIf="(navigationItems$ | async)?.length === 0" class="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
  <div class="flex-1 flex flex-col min-h-0 bg-gray-800">
    <div class="flex items-center h-16 flex-shrink-0 px-4 bg-gray-900">
      <div class="text-white font-bold text-xl">Deploy Orchestrator</div>
    </div>
    <div class="flex-1 flex items-center justify-center p-4">
      <div class="text-center">
        <i class="fas fa-lock text-gray-400 text-4xl mb-4"></i>
        <h3 class="text-lg font-medium text-white mb-2">Access Restricted</h3>
        <p class="text-sm text-gray-400">You don't have permission to access any features. Contact your administrator.</p>
      </div>
    </div>
    
    <!-- User profile section for no access state -->
    <div class="flex-shrink-0 flex bg-gray-700 p-4">
      <div class="flex-shrink-0 w-full group block">
        <div class="flex items-center">
          <div>
            <div class="inline-block h-9 w-9 rounded-full bg-gray-500 text-white flex items-center justify-center">
              {{ username.charAt(0).toUpperCase() }}
            </div>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-white">{{ username }}</p>
            <button (click)="logout()" class="text-xs font-medium text-gray-300 hover:text-white">
              Logout
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
