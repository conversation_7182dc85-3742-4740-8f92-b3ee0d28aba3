.sidebar {
  width: 250px;
  background-color: #1a202c;
  color: white;
  height: 100vh;
  overflow-y: auto;
  position: sticky;
  top: 0;
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-menu {
  padding: 1rem 0;
}

.menu-item {
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.2s;
}

.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.menu-item.active {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border-left: 3px solid #3182ce;
}

.menu-icon {
  margin-right: 0.75rem;
}

@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
  }
}
