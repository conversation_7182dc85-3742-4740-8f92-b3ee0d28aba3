import { Component, OnInit, OnDestroy } from '@angular/core';
import { AuthService } from '../../../services/auth.service';
import { EnvironmentService } from '../../../services/environment.service';
import { NavigationPermissionService, NavigationItem } from '../../../services/navigation-permission.service';
import { PluginPermissionService } from '../../../services/plugin-permission.service';
import { Router } from '@angular/router';
import { Observable, of, Subject } from 'rxjs';
import { map, catchError, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  isAdmin = false;
  username = '';
  isMobileMenuOpen = false;

  // Navigation items with permissions
  navigationItems$: Observable<NavigationItem[]>;

  // Permission observables for template
  canViewEnvironments$: Observable<boolean>;
  canManageEnvironments$: Observable<boolean>;
  canDeployToEnvironments$: Observable<boolean>;
  canViewWorkflows$: Observable<boolean>;
  canViewTemplates$: Observable<boolean>;
  canViewTemplateAnalytics$: Observable<boolean>;
  canViewExecutionMonitoring$: Observable<boolean>;

  // Plugin permission observables
  canViewPlugins$: Observable<boolean>;
  canManagePlugins$: Observable<boolean>;
  canAccessProviders$: Observable<boolean>;

  constructor(
    private authService: AuthService,
    private environmentService: EnvironmentService,
    private navigationPermissionService: NavigationPermissionService,
    private pluginPermissionService: PluginPermissionService,
    private router: Router
  ) {
    this.navigationItems$ = this.navigationPermissionService.getNavigationItems();

    // Initialize permission observables
    this.canViewEnvironments$ = this.navigationPermissionService.canViewEnvironments();
    this.canManageEnvironments$ = this.navigationPermissionService.canManageEnvironments();
    this.canDeployToEnvironments$ = this.navigationPermissionService.canDeployToEnvironments();
    this.canViewWorkflows$ = this.navigationPermissionService.canViewWorkflows();
    this.canViewTemplates$ = this.navigationPermissionService.canViewTemplates();
    this.canViewTemplateAnalytics$ = this.navigationPermissionService.canViewTemplateAnalytics();
    this.canViewExecutionMonitoring$ = this.navigationPermissionService.canViewExecutionMonitoring();

    // Initialize plugin permission observables
    this.canViewPlugins$ = this.pluginPermissionService.canViewPlugins();
    this.canManagePlugins$ = this.pluginPermissionService.canManagePlugins();
    this.canAccessProviders$ = this.pluginPermissionService.canAccessProviders();
  }

  ngOnInit(): void {
    this.isAdmin = this.authService.isAdmin();
    const user = this.authService.currentUserValue;
    if (user) {
      this.username = user.username;
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleMobileMenu(): void {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  navigateTo(route: string): void {
    this.router.navigate([route]);
    this.isMobileMenuOpen = false; // Close mobile menu after navigation
  }

  isActiveRoute(route: string): boolean {
    return this.router.url === route || this.router.url.startsWith(route + '/');
  }

  hasChildren(item: NavigationItem): boolean {
    return !!(item.children && item.children.length > 0);
  }

  getVisibleChildren(item: NavigationItem): NavigationItem[] {
    return item.children?.filter(child => child.visible) || [];
  }

  getBadgeClass(color: string): string {
    switch (color) {
      case 'blue':
        return 'bg-blue-100 text-blue-800';
      case 'green':
        return 'bg-green-100 text-green-800';
      case 'yellow':
        return 'bg-yellow-100 text-yellow-800';
      case 'red':
        return 'bg-red-100 text-red-800';
      case 'purple':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
  }
}
