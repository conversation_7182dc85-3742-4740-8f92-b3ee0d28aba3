.workflow-canvas {
  position: relative;
  overflow: hidden;
  user-select: none;
}

.workflow-step {
  position: absolute;
  transition: all 0.2s ease;
}

.workflow-step:hover {
  transform: translateY(-2px);
}

.workflow-step.selected {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.connection-line {
  pointer-events: none;
}

.connection-point {
  cursor: pointer;
  transition: all 0.2s ease;
}

.connection-point:hover {
  transform: scale(1.2);
}

.step-palette-item {
  transition: all 0.2s ease;
}

.step-palette-item:hover {
  transform: translateX(4px);
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  opacity: 0.9;
  transform: rotate(5deg);
  background: white;
  border: 2px solid #3b82f6;
  padding: 8px;
  min-width: 120px;
  z-index: 1000;
}

.cdk-drag-placeholder {
  opacity: 0.2;
  background: #f3f4f6;
  border: 2px dashed #d1d5db;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging .cdk-drag {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-receiving {
  background-color: rgba(59, 130, 246, 0.05);
  border: 2px dashed #3b82f6;
  border-radius: 8px;
}

.canvas-grid {
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}