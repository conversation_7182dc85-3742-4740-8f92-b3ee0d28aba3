import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { Subject, takeUntil } from 'rxjs';
import { WorkflowService } from '../../services/workflow.service';
import { ProjectService } from '../../services/project.service';
import {
  WorkflowDefinition,
  WorkflowStep,
  StepType,
  WorkflowConnection,
  WorkflowDesignerState,
  Position
} from '../../models/workflow.model';
import { Project } from '../../models/project.model';

@Component({
  selector: 'app-workflow-designer',
  templateUrl: './workflow-designer.component.html',
  styleUrls: ['./workflow-designer.component.scss']
})
export class WorkflowDesignerComponent implements OnInit, OnD<PERSON>roy {
  @ViewChild('canvas', { static: true }) canvasRef!: ElementRef<HTMLDivElement>;

  private destroy$ = new Subject<void>();

  // Designer state
  designerState: WorkflowDesignerState = {
    workflow: this.createEmptyWorkflow(),
    connections: [],
    canvasPosition: { x: 0, y: 0 },
    canvasZoom: 1,
    isDirty: false
  };

  // UI state
  projects: Project[] = [];
  selectedProjectId: string = '';
  showStepConfig = false;
  showWorkflowProperties = false;
  workflowPropertiesTab: 'basic' | 'variables' = 'basic';
  isLoading = false;
  error: string | null = null;
  successMessage: string | null = null;
  showPublishTemplateModal = false;
  publishingTemplate = false;

  // Template publishing data
  templateData = {
    name: '',
    description: '',
    category: '',
    tagsString: '',
    isPublic: false
  };

  // Canvas interaction
  isDragging = false;
  dragStart: Position = { x: 0, y: 0 };
  canvasOffset: Position = { x: 0, y: 0 };

  // Step dragging
  isDraggingStep = false;
  draggedStep: WorkflowStep | null = null;
  stepDragStart: Position = { x: 0, y: 0 };

  // Connection creation
  isCreatingConnection = false;
  connectionStart: WorkflowStep | null = null;
  connectionType: 'success' | 'failure' | 'dependency' = 'success';
  tempConnectionEnd: Position = { x: 0, y: 0 };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private workflowService: WorkflowService,
    private projectService: ProjectService
  ) {}

  ngOnInit(): void {
    this.loadProjects();
    this.loadWorkflow();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createEmptyWorkflow(): WorkflowDefinition {
    return {
      id: '',
      name: 'New Workflow',
      description: '',
      projectId: '',
      version: '1.0.0',
      isActive: true,
      steps: [],
      parameters: [],
      triggers: [],
      variables: {},
      tags: []
    };
  }

  private loadProjects(): void {
    this.projectService.getProjects()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (projects) => {
          this.projects = projects;
          if (projects.length > 0 && !this.selectedProjectId) {
            this.selectedProjectId = projects[0].id;
            this.designerState.workflow.projectId = this.selectedProjectId;
          }
        },
        error: (error) => {
          console.error('Error loading projects:', error);
        }
      });
  }

  private loadWorkflow(): void {
    const workflowId = this.route.snapshot.paramMap.get('id');
    if (workflowId) {
      this.isLoading = true;
      this.workflowService.getWorkflow(workflowId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (workflow) => {
            // Ensure variables property exists
            if (!workflow.variables) {
              workflow.variables = {};
            }
            this.designerState.workflow = workflow;
            this.selectedProjectId = workflow.projectId;
            this.generateConnections();
            this.isLoading = false;
          },
          error: (error) => {
            this.error = 'Failed to load workflow';
            this.isLoading = false;
            console.error('Error loading workflow:', error);
          }
        });
    }
  }

  private generateConnections(): void {
    const connections: WorkflowConnection[] = [];

    this.designerState.workflow.steps.forEach(step => {
      // Create dependency connections
      step.dependencies.forEach(depId => {
        connections.push({
          id: this.workflowService.generateConnectionId(),
          sourceStepId: depId,
          targetStepId: step.id,
          type: 'dependency'
        });
      });

      // Create success connections
      step.onSuccess.forEach(successId => {
        connections.push({
          id: this.workflowService.generateConnectionId(),
          sourceStepId: step.id,
          targetStepId: successId,
          type: 'success'
        });
      });

      // Create failure connections
      step.onFailure.forEach(failureId => {
        connections.push({
          id: this.workflowService.generateConnectionId(),
          sourceStepId: step.id,
          targetStepId: failureId,
          type: 'failure'
        });
      });
    });

    this.designerState.connections = connections;
  }

  // Step management
  onStepSelected(stepType: StepType): void {
    this.addStep(stepType);
  }

  addStep(stepType: StepType, position?: Position): void {
    const step: WorkflowStep = {
      id: this.workflowService.generateStepId(),
      name: stepType.name,
      type: stepType.type,
      description: stepType.description,
      config: { ...stepType.defaultConfig },
      dependencies: [],
      conditions: [],
      onSuccess: [],
      onFailure: [],
      timeout: 30,
      retryPolicy: {
        maxAttempts: 1,
        delaySeconds: 0,
        backoffMultiplier: 1
      },
      position: position || this.getNextStepPosition()
    };

    this.designerState.workflow.steps.push(step);
    this.designerState.selectedStep = step;
    this.showStepConfig = true;
    this.markDirty();
  }

  private getNextStepPosition(): Position {
    const steps = this.designerState.workflow.steps;
    if (steps.length === 0) {
      return { x: 100, y: 100 };
    }

    // Find the rightmost step and place the new step to its right
    const rightmostStep = steps.reduce((max, step) =>
      step.position.x > max.position.x ? step : max
    );

    return {
      x: rightmostStep.position.x + 200,
      y: rightmostStep.position.y
    };
  }

  selectStep(step: WorkflowStep): void {
    this.designerState.selectedStep = step;
    this.showStepConfig = true;
    this.showWorkflowProperties = false;
  }

  deleteStep(step: WorkflowStep): void {
    if (confirm(`Are you sure you want to delete step "${step.name}"?`)) {
      // Remove the step
      this.designerState.workflow.steps = this.designerState.workflow.steps.filter(s => s.id !== step.id);

      // Remove connections involving this step
      this.designerState.connections = this.designerState.connections.filter(
        conn => conn.sourceStepId !== step.id && conn.targetStepId !== step.id
      );

      // Remove references from other steps
      this.designerState.workflow.steps.forEach(s => {
        s.dependencies = s.dependencies.filter(id => id !== step.id);
        s.onSuccess = s.onSuccess.filter(id => id !== step.id);
        s.onFailure = s.onFailure.filter(id => id !== step.id);
      });

      // Clear selection if this step was selected
      if (this.designerState.selectedStep?.id === step.id) {
        this.designerState.selectedStep = undefined;
        this.showStepConfig = false;
      }

      this.markDirty();
    }
  }

  // Canvas interaction
  onCanvasDrop(event: CdkDragDrop<any>): void {
    if (event.previousContainer !== event.container) {
      // This is a drag from the step palette to the canvas
      const stepType = event.item.data as StepType;
      const rect = this.canvasRef.nativeElement.getBoundingClientRect();

      // Calculate position relative to canvas, accounting for canvas offset
      const position: Position = {
        x: Math.max(20, event.dropPoint.x - rect.left - this.canvasOffset.x),
        y: Math.max(20, event.dropPoint.y - rect.top - this.canvasOffset.y)
      };

      this.addStep(stepType, position);
    } else {
      // This would be for reordering steps within the canvas (future feature)
      // For now, we don't handle reordering of existing steps
    }
  }

  onCanvasMouseDown(event: MouseEvent): void {
    // Only start canvas panning if clicking directly on the canvas (not on steps or other elements)
    if (event.target === this.canvasRef.nativeElement ||
        (event.target as HTMLElement).closest('svg')) {
      this.isDragging = true;
      this.dragStart = { x: event.clientX, y: event.clientY };
      this.designerState.selectedStep = undefined;
      this.showStepConfig = false;
      this.showWorkflowProperties = false;
      event.preventDefault(); // Prevent text selection
    }
  }

  onCanvasMouseMove(event: MouseEvent): void {
    if (this.isDragging) {
      event.preventDefault(); // Prevent text selection during drag
      const deltaX = event.clientX - this.dragStart.x;
      const deltaY = event.clientY - this.dragStart.y;

      this.canvasOffset.x += deltaX;
      this.canvasOffset.y += deltaY;

      this.dragStart = { x: event.clientX, y: event.clientY };
    }
  }

  onCanvasMouseUp(): void {
    this.isDragging = false;
    this.isDraggingStep = false;
    this.draggedStep = null;
    this.isCreatingConnection = false;
    this.connectionStart = null;
  }

  // Enhanced step dragging
  onStepMouseDown(event: MouseEvent, step: WorkflowStep): void {
    event.stopPropagation();

    if (event.button === 0) { // Left click
      this.isDraggingStep = true;
      this.draggedStep = step;
      this.stepDragStart = { x: event.clientX, y: event.clientY };
      this.selectStep(step);
    }
  }

  onStepMouseMove(event: MouseEvent): void {
    if (this.isDraggingStep && this.draggedStep) {
      event.preventDefault();

      const deltaX = event.clientX - this.stepDragStart.x;
      const deltaY = event.clientY - this.stepDragStart.y;

      this.draggedStep.position.x += deltaX;
      this.draggedStep.position.y += deltaY;

      // Ensure step doesn't go outside canvas bounds
      this.draggedStep.position.x = Math.max(20, this.draggedStep.position.x);
      this.draggedStep.position.y = Math.max(20, this.draggedStep.position.y);

      this.stepDragStart = { x: event.clientX, y: event.clientY };
      this.markDirty();
    }
  }

  onStepMouseUp(event: MouseEvent): void {
    if (this.isDraggingStep) {
      this.isDraggingStep = false;
      this.draggedStep = null;
    }
  }

  // Connection creation
  startConnection(event: MouseEvent, step: WorkflowStep, type: 'success' | 'failure' | 'dependency'): void {
    event.stopPropagation();
    this.isCreatingConnection = true;
    this.connectionStart = step;
    this.connectionType = type;
    this.tempConnectionEnd = { x: event.clientX, y: event.clientY };
  }

  updateTempConnection(event: MouseEvent): void {
    if (this.isCreatingConnection) {
      const rect = this.canvasRef.nativeElement.getBoundingClientRect();
      this.tempConnectionEnd = {
        x: event.clientX - rect.left - this.canvasOffset.x,
        y: event.clientY - rect.top - this.canvasOffset.y
      };
    }
  }

  completeConnection(targetStep: WorkflowStep): void {
    if (this.isCreatingConnection && this.connectionStart && this.connectionStart.id !== targetStep.id) {
      this.createConnection(this.connectionStart, targetStep, this.connectionType);
      this.isCreatingConnection = false;
      this.connectionStart = null;
    }
  }

  private createConnection(sourceStep: WorkflowStep, targetStep: WorkflowStep, type: 'success' | 'failure' | 'dependency'): void {
    // Prevent duplicate connections
    const existingConnection = this.designerState.connections.find(conn =>
      conn.sourceStepId === sourceStep.id &&
      conn.targetStepId === targetStep.id &&
      conn.type === type
    );

    if (existingConnection) {
      return;
    }

    // Add connection to the appropriate step property
    switch (type) {
      case 'success':
        if (!sourceStep.onSuccess.includes(targetStep.id)) {
          sourceStep.onSuccess.push(targetStep.id);
        }
        break;
      case 'failure':
        if (!sourceStep.onFailure.includes(targetStep.id)) {
          sourceStep.onFailure.push(targetStep.id);
        }
        break;
      case 'dependency':
        if (!targetStep.dependencies.includes(sourceStep.id)) {
          targetStep.dependencies.push(sourceStep.id);
        }
        break;
    }

    // Add visual connection
    this.designerState.connections.push({
      id: this.workflowService.generateConnectionId(),
      sourceStepId: sourceStep.id,
      targetStepId: targetStep.id,
      type: type
    });

    this.markDirty();
  }

  // Connection management
  deleteConnection(connection: WorkflowConnection): void {
    // Remove from visual connections
    this.designerState.connections = this.designerState.connections.filter(conn => conn.id !== connection.id);

    // Remove from step properties
    const sourceStep = this.getStepById(connection.sourceStepId);
    const targetStep = this.getStepById(connection.targetStepId);

    if (sourceStep && targetStep) {
      switch (connection.type) {
        case 'success':
          sourceStep.onSuccess = sourceStep.onSuccess.filter(id => id !== targetStep.id);
          break;
        case 'failure':
          sourceStep.onFailure = sourceStep.onFailure.filter(id => id !== targetStep.id);
          break;
        case 'dependency':
          targetStep.dependencies = targetStep.dependencies.filter(id => id !== sourceStep.id);
          break;
      }
    }

    this.markDirty();
  }

  // Auto-layout functionality
  autoLayout(): void {
    const steps = this.designerState.workflow.steps;
    if (steps.length === 0) return;

    // Simple grid layout
    const gridSize = Math.ceil(Math.sqrt(steps.length));
    const stepWidth = 200;
    const stepHeight = 120;
    const padding = 50;

    steps.forEach((step, index) => {
      const row = Math.floor(index / gridSize);
      const col = index % gridSize;

      step.position = {
        x: padding + col * (stepWidth + padding),
        y: padding + row * (stepHeight + padding)
      };
    });

    this.markDirty();
  }

  // Smart layout based on dependencies
  smartLayout(): void {
    const steps = this.designerState.workflow.steps;
    if (steps.length === 0) return;

    // Create a dependency graph
    const graph = new Map<string, string[]>();
    const inDegree = new Map<string, number>();

    // Initialize
    steps.forEach(step => {
      graph.set(step.id, []);
      inDegree.set(step.id, 0);
    });

    // Build graph
    steps.forEach(step => {
      step.dependencies.forEach(depId => {
        if (graph.has(depId)) {
          graph.get(depId)!.push(step.id);
          inDegree.set(step.id, (inDegree.get(step.id) || 0) + 1);
        }
      });
    });

    // Topological sort to determine levels
    const levels: string[][] = [];
    const queue: string[] = [];

    // Find steps with no dependencies (level 0)
    inDegree.forEach((degree, stepId) => {
      if (degree === 0) {
        queue.push(stepId);
      }
    });

    while (queue.length > 0) {
      const currentLevel: string[] = [...queue];
      levels.push(currentLevel);
      queue.length = 0;

      currentLevel.forEach(stepId => {
        const neighbors = graph.get(stepId) || [];
        neighbors.forEach(neighborId => {
          const newDegree = (inDegree.get(neighborId) || 0) - 1;
          inDegree.set(neighborId, newDegree);
          if (newDegree === 0) {
            queue.push(neighborId);
          }
        });
      });
    }

    // Position steps based on levels
    const stepWidth = 200;
    const stepHeight = 120;
    const levelSpacing = 300;
    const stepSpacing = 50;
    const startX = 100;
    const startY = 100;

    levels.forEach((level, levelIndex) => {
      const levelY = startY + levelIndex * levelSpacing;
      const totalWidth = level.length * stepWidth + (level.length - 1) * stepSpacing;
      const startXForLevel = startX + Math.max(0, (800 - totalWidth) / 2); // Center the level

      level.forEach((stepId, stepIndex) => {
        const step = steps.find(s => s.id === stepId);
        if (step) {
          step.position = {
            x: startXForLevel + stepIndex * (stepWidth + stepSpacing),
            y: levelY
          };
        }
      });
    });

    this.markDirty();
  }

  // Workflow management
  saveWorkflow(): void {
    if (!this.designerState.workflow.name.trim()) {
      alert('Please enter a workflow name');
      return;
    }

    if (!this.designerState.workflow.projectId) {
      alert('Please select a project');
      return;
    }

    this.isLoading = true;
    this.error = null;

    const saveOperation = this.designerState.workflow.id
      ? this.workflowService.updateWorkflow(this.designerState.workflow.id, this.designerState.workflow)
      : this.workflowService.createWorkflow(this.designerState.workflow);

    saveOperation
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (workflow) => {
          this.designerState.workflow = workflow;
          this.designerState.isDirty = false;
          this.isLoading = false;
          this.successMessage = 'Workflow saved successfully!';
          setTimeout(() => this.successMessage = null, 3000);
        },
        error: (error) => {
          this.error = 'Failed to save workflow';
          this.isLoading = false;
          console.error('Error saving workflow:', error);
        }
      });
  }

  markDirty(): void {
    this.designerState.isDirty = true;
  }

  showProperties(): void {
    this.showWorkflowProperties = true;
    this.showStepConfig = false;
    this.designerState.selectedStep = undefined;
  }

  publishTemplate(): void {
    if (!this.templateData.name || !this.templateData.category) {
      return;
    }

    this.publishingTemplate = true;

    // Prepare template data
    const templateRequest = {
      name: this.templateData.name,
      description: this.templateData.description,
      category: this.templateData.category,
      isPublic: this.templateData.isPublic,
      tags: this.templateData.tagsString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
      workflowId: this.designerState.workflow.id
    };

    this.workflowService.createTemplateFromWorkflow(templateRequest)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (template) => {
          this.publishingTemplate = false;
          this.showPublishTemplateModal = false;
          this.successMessage = 'Template published successfully!';
          setTimeout(() => this.successMessage = null, 3000);

          // Reset template data
          this.templateData = {
            name: '',
            description: '',
            category: '',
            tagsString: '',
            isPublic: false
          };
        },
        error: (error) => {
          this.publishingTemplate = false;
          this.error = 'Failed to publish template';
          console.error('Error publishing template:', error);
        }
      });
  }

  goBack(): void {
    if (this.designerState.isDirty) {
      if (confirm('You have unsaved changes. Are you sure you want to leave?')) {
        this.router.navigate(['/workflows']);
      }
    } else {
      this.router.navigate(['/workflows']);
    }
  }

  // Helper methods for template
  trackByStepId(index: number, step: WorkflowStep): string {
    return step.id;
  }

  getStepById(stepId: string): WorkflowStep | undefined {
    return this.designerState.workflow.steps.find(step => step.id === stepId);
  }

  getConnectionColor(type: string): string {
    switch (type) {
      case 'success': return '#10b981'; // green
      case 'failure': return '#ef4444'; // red
      case 'dependency': return '#6b7280'; // gray
      default: return '#6b7280';
    }
  }



  // Variable management methods
  addVariable(): void {
    if (!this.designerState.workflow.variables) {
      this.designerState.workflow.variables = {};
    }
    const variableName = `variable_${Object.keys(this.designerState.workflow.variables).length + 1}`;
    this.designerState.workflow.variables[variableName] = '';
    this.markDirty();
  }

  removeVariable(key: string): void {
    if (!this.designerState.workflow.variables) {
      this.designerState.workflow.variables = {};
      return;
    }
    delete this.designerState.workflow.variables[key];
    this.markDirty();
  }

  updateVariableKey(oldKey: string, event: Event): void {
    const target = event.target as HTMLInputElement;
    const newKey = target.value;
    const cursorPosition = target.selectionStart || 0;

    if (!this.designerState.workflow.variables) {
      this.designerState.workflow.variables = {};
      return;
    }

    if (newKey !== oldKey) {
      const oldValue = this.designerState.workflow.variables[oldKey];
      delete this.designerState.workflow.variables[oldKey];
      this.designerState.workflow.variables[newKey] = oldValue;
      this.markDirty();

      // Restore focus and cursor position after Angular updates
      setTimeout(() => {
        const input = document.querySelector(`input[data-variable-key="${newKey}"]`) as HTMLInputElement;
        if (input) {
          input.focus();
          input.setSelectionRange(cursorPosition, cursorPosition);
        }
      }, 0);
    }
  }

  updateVariableValue(key: string, event: Event): void {
    const target = event.target as HTMLInputElement;
    const newValue = target.value;
    const cursorPosition = target.selectionStart || 0;

    if (!this.designerState.workflow.variables) {
      this.designerState.workflow.variables = {};
    }

    this.designerState.workflow.variables[key] = newValue;
    this.markDirty();

    // Restore focus and cursor position after Angular updates
    setTimeout(() => {
      const input = document.querySelector(`input[data-variable-value="${key}"]`) as HTMLInputElement;
      if (input) {
        input.focus();
        input.setSelectionRange(cursorPosition, cursorPosition);
      }
    }, 0);
  }

  getVariableCount(): number {
    if (!this.designerState.workflow.variables) {
      this.designerState.workflow.variables = {};
    }
    return Object.keys(this.designerState.workflow.variables).length;
  }

  getVariableEntries(): Array<{key: string, value: any}> {
    if (!this.designerState.workflow.variables) {
      this.designerState.workflow.variables = {};
    }
    return Object.entries(this.designerState.workflow.variables).map(([key, value]) => ({
      key,
      value
    }));
  }

  trackByVariableKey(index: number, item: {key: string, value: any}): string {
    return item.key;
  }
}
