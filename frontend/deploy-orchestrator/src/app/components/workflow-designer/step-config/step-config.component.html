<div class="h-full flex flex-col">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-gray-200">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-medium text-gray-900">Step Configuration</h3>
      <button
        (click)="onClose()"
        class="text-gray-400 hover:text-gray-600">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Tabs -->
  <div class="border-b border-gray-200">
    <nav class="-mb-px flex">
      <button
        (click)="activeTab = 'basic'"
        [class]="'py-2 px-4 text-sm font-medium border-b-2 ' +
                 (activeTab === 'basic' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300')">
        Basic
      </button>
      <button
        (click)="activeTab = 'config'"
        [class]="'py-2 px-4 text-sm font-medium border-b-2 ' +
                 (activeTab === 'config' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300')">
        Configuration
      </button>
      <button
        (click)="activeTab = 'flow'"
        [class]="'py-2 px-4 text-sm font-medium border-b-2 ' +
                 (activeTab === 'flow' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300')">
        Flow
      </button>
      <button
        (click)="activeTab = 'advanced'"
        [class]="'py-2 px-4 text-sm font-medium border-b-2 ' +
                 (activeTab === 'advanced' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300')">
        Advanced
      </button>
    </nav>
  </div>

  <!-- Content -->
  <div class="flex-1 overflow-y-auto p-6">
    <!-- Basic Tab -->
    <div *ngIf="activeTab === 'basic'" class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
        <input
          type="text"
          [(ngModel)]="step.name"
          (ngModelChange)="onStepChange()"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
        <textarea
          [(ngModel)]="step.description"
          (ngModelChange)="onStepChange()"
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
        </textarea>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
        <input
          type="text"
          [value]="step.type"
          readonly
          class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500">
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Timeout (minutes)</label>
        <input
          type="number"
          [(ngModel)]="step.timeout"
          (ngModelChange)="onStepChange()"
          min="1"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
      </div>
    </div>

    <!-- Configuration Tab -->
    <div *ngIf="activeTab === 'config'" class="space-y-4">
      <!-- Debug Information -->
      <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
        <div class="text-xs text-yellow-800">
          <strong>Debug Info:</strong> 
          Step Type: "{{ step.type }}" | 
          Selected Plugin: {{ selectedPlugin?.name || 'None' }} | 
          Config Keys: {{ getConfigKeys().length }} | 
          Plugin Config Fields: {{ getPluginConfigFields().length }}
        </div>
      </div>
      
      <!-- Provider Step Configuration -->
      <div *ngIf="step.type === 'provider'" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Provider Type <span class="text-red-500">*</span>
          </label>
          <select
            [value]="step.config['providerType'] || ''"
            (change)="onProviderChange($any($event.target).value)"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
            <option value="">Select a provider...</option>
            <option *ngFor="let provider of availableWorkflowProviders" [value]="provider.type">
              {{ provider.name }} ({{ provider.type }})
            </option>
          </select>
          <div *ngIf="loadingProviders" class="text-sm text-gray-500 mt-1">Loading providers...</div>
        </div>

        <!-- Provider Configuration Fields -->
        <div *ngIf="selectedWorkflowProvider" class="space-y-4 border-t pt-4">
          <h4 class="text-sm font-medium text-gray-900">Provider Configuration</h4>
          <div *ngFor="let field of getProviderConfigFields()" class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">
              {{ field.label || field.name }}
              <span *ngIf="field.required" class="text-red-500">*</span>
            </label>
            <p *ngIf="field.description" class="text-xs text-gray-500 mb-1">{{ field.description }}</p>

            <!-- Text Input -->
            <input
              *ngIf="field.type === 'string'"
              type="text"
              [value]="step.config['providerConfig']?.[field.name] || ''"
              (input)="onProviderConfigChange(field.name, $any($event.target).value)"
              [required]="field.required"
              [placeholder]="field.placeholder || ''"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">

            <!-- Password Input -->
            <input
              *ngIf="field.type === 'password'"
              type="password"
              [value]="step.config['providerConfig']?.[field.name] || ''"
              (input)="onProviderConfigChange(field.name, $any($event.target).value)"
              [required]="field.required"
              [placeholder]="field.placeholder || ''"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">

            <!-- Number Input -->
            <input
              *ngIf="field.type === 'number'"
              type="number"
              [value]="step.config['providerConfig']?.[field.name] || ''"
              (input)="onProviderConfigChange(field.name, +($any($event.target).value))"
              [required]="field.required"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">

            <!-- Boolean Input -->
            <div *ngIf="field.type === 'boolean'" class="flex items-center">
              <input
                type="checkbox"
                [checked]="step.config['providerConfig']?.[field.name] || false"
                (change)="onProviderConfigChange(field.name, $any($event.target).checked)"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
              <span class="ml-2 text-sm text-gray-700">Enable</span>
            </div>

            <!-- Select Input -->
            <select
              *ngIf="field.type === 'select'"
              [value]="step.config['providerConfig']?.[field.key || field.name] || ''"
              (change)="onProviderConfigChange(field.key || field.name, $any($event.target).value)"
              [required]="field.required"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
              <option value="">Select an option</option>
              <option 
                *ngFor="let option of getIterableOptions(field.options)" 
                [value]="getOptionValue(option)">
                {{ getOptionLabel(option) }}
              </option>
            </select>
          </div>
        </div>
      </div>

      <!-- Plugin Step Configuration -->
      <div *ngIf="step.type === 'plugin'" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Plugin <span class="text-red-500">*</span>
          </label>
          <select
            [value]="step.config['pluginId'] || ''"
            (change)="onPluginChange($any($event.target).value)"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
            <option value="">Select a plugin...</option>
            <option *ngFor="let plugin of availablePlugins" [value]="plugin.id">
              {{ plugin.name }} ({{ plugin.type }})
            </option>
          </select>
          <div *ngIf="loadingPlugins" class="text-sm text-gray-500 mt-1">Loading plugins...</div>
        </div>

        <!-- Plugin Configuration Fields -->
        <div *ngIf="selectedPlugin" class="space-y-4 border-t pt-4">
          <h4 class="text-sm font-medium text-gray-900">Plugin Configuration</h4>

          <!-- Loading Plugin Schema -->
          <div *ngIf="loadingPluginSchema" class="text-center py-4">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
            <p class="text-sm text-gray-500 mt-2">Loading plugin configuration...</p>
          </div>

          <!-- Dynamic Form for Plugin Configuration -->
          <div *ngIf="pluginConfigSchema && !loadingPluginSchema">
            <app-dynamic-form
              [schema]="pluginConfigSchema"
              [initialValues]="step.config['pluginConfig'] || {}"
              [showActions]="false"
              (formChange)="onDynamicFormChange($event)"
              (formSubmit)="onDynamicFormSubmit($event)">
            </app-dynamic-form>
          </div>

          <!-- Fallback to Legacy Configuration -->
          <div *ngIf="!pluginConfigSchema && !loadingPluginSchema" class="space-y-4">
            <div *ngFor="let field of getPluginConfigFields()" class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                {{ field.label }}
                <span *ngIf="field.required" class="text-red-500">*</span>
              </label>
              <p *ngIf="field.description" class="text-xs text-gray-500 mb-1">{{ field.description }}</p>

              <!-- Text Input -->
              <input
                *ngIf="field.type === 'string'"
                type="text"
                [value]="step.config['pluginConfig']?.[field.key] || ''"
                (input)="onPluginConfigChange(field.key, $any($event.target).value)"
                [required]="field.required"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">

              <!-- Password Input -->
              <input
                *ngIf="field.type === 'password'"
                type="password"
                [value]="step.config['pluginConfig']?.[field.key] || ''"
                (input)="onPluginConfigChange(field.key, $any($event.target).value)"
                [required]="field.required"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">

              <!-- Number Input -->
              <input
                *ngIf="field.type === 'number'"
                type="number"
                [value]="step.config['pluginConfig']?.[field.key] || ''"
                (input)="onPluginConfigChange(field.key, +($any($event.target).value))"
                [required]="field.required"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">

              <!-- Boolean Input -->
              <div *ngIf="field.type === 'boolean'" class="flex items-center">
                <input
                  type="checkbox"
                  [checked]="step.config['pluginConfig']?.[field.key] || false"
                  (change)="onPluginConfigChange(field.key, $any($event.target).checked)"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <span class="ml-2 text-sm text-gray-700">Enable</span>
              </div>

              <!-- Select Input -->
              <select
                *ngIf="field.type === 'select'"
                [value]="step.config['pluginConfig']?.[field.key] || ''"
                (change)="onPluginConfigChange(field.key, $any($event.target).value)"
                [required]="field.required"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                <option value="">Select an option</option>
                <option *ngFor="let option of getIterableOptions(field.options)" [value]="getOptionValue(option)">{{ getOptionLabel(option) }}</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <!-- Standard Step Configuration -->
      <div *ngIf="step.type !== 'provider' && step.type !== 'plugin'">
        <div *ngIf="getConfigKeys().length === 0" class="text-center py-8 text-gray-500">
          No configuration options available for this step type.
        </div>

        <div *ngFor="let key of getConfigKeys()" class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            {{ key }}
            <span *ngIf="isConfigRequired(key)" class="text-red-500">*</span>
          </label>

          <!-- Text Input -->
        <input
          *ngIf="getConfigType(key) === 'text'"
          type="text"
          [value]="step.config[key] || ''"
          (input)="onInputChange(key, $event)"
          [required]="isConfigRequired(key)"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">

        <!-- Number Input -->
        <input
          *ngIf="getConfigType(key) === 'number'"
          type="number"
          [value]="step.config[key] || 0"
          (input)="onNumberChange(key, $event)"
          [required]="isConfigRequired(key)"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">

        <!-- Textarea -->
        <textarea
          *ngIf="getConfigType(key) === 'textarea'"
          [value]="step.config[key] || ''"
          (input)="onTextareaChange(key, $event)"
          [required]="isConfigRequired(key)"
          rows="4"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
        </textarea>

        <!-- Select -->
        <select
          *ngIf="getConfigType(key) === 'select'"
          [value]="step.config[key] || ''"
          (change)="onSelectChange(key, $event)"
          [required]="isConfigRequired(key)"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
          <option value="">Select an option</option>
          <option *ngFor="let option of getConfigOptions(key)" [value]="option">{{ option }}</option>
        </select>

        <!-- Boolean -->
        <div *ngIf="getConfigType(key) === 'boolean'" class="flex items-center">
          <input
            type="checkbox"
            [checked]="step.config[key] || false"
            (change)="onCheckboxChange(key, $event)"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
          <span class="ml-2 text-sm text-gray-700">Enable</span>
        </div>
        </div>
      </div>
    </div>

    <!-- Flow Tab -->
    <div *ngIf="activeTab === 'flow'" class="space-y-6">
      <!-- Dependencies -->
      <div>
        <h4 class="text-sm font-medium text-gray-900 mb-3">Dependencies</h4>
        <p class="text-xs text-gray-500 mb-3">Steps that must complete before this step runs</p>

        <div *ngIf="step.dependencies.length === 0" class="text-sm text-gray-500 mb-3">
          No dependencies
        </div>

        <div *ngFor="let depId of step.dependencies" class="flex items-center justify-between bg-gray-50 px-3 py-2 rounded-md mb-2">
          <span class="text-sm">{{ getStepName(depId) }}</span>
          <button
            (click)="removeDependency(depId)"
            class="text-red-600 hover:text-red-800">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <select
          (change)="onDependencySelect($event)"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
          <option value="">Add dependency...</option>
          <option *ngFor="let step of getAvailableDependencies()" [value]="step.id">{{ step.name }}</option>
        </select>
      </div>

      <!-- Success Flow -->
      <div>
        <h4 class="text-sm font-medium text-gray-900 mb-3">On Success</h4>
        <p class="text-xs text-gray-500 mb-3">Steps to run when this step succeeds</p>

        <div *ngIf="step.onSuccess.length === 0" class="text-sm text-gray-500 mb-3">
          No success steps
        </div>

        <div *ngFor="let successId of step.onSuccess" class="flex items-center justify-between bg-green-50 px-3 py-2 rounded-md mb-2">
          <span class="text-sm">{{ getStepName(successId) }}</span>
          <button
            (click)="removeSuccessStep(successId)"
            class="text-red-600 hover:text-red-800">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <select
          (change)="onSuccessStepSelect($event)"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
          <option value="">Add success step...</option>
          <option *ngFor="let step of getAvailableSuccessSteps()" [value]="step.id">{{ step.name }}</option>
        </select>
      </div>

      <!-- Failure Flow -->
      <div>
        <h4 class="text-sm font-medium text-gray-900 mb-3">On Failure</h4>
        <p class="text-xs text-gray-500 mb-3">Steps to run when this step fails</p>

        <div *ngIf="step.onFailure.length === 0" class="text-sm text-gray-500 mb-3">
          No failure steps
        </div>

        <div *ngFor="let failureId of step.onFailure" class="flex items-center justify-between bg-red-50 px-3 py-2 rounded-md mb-2">
          <span class="text-sm">{{ getStepName(failureId) }}</span>
          <button
            (click)="removeFailureStep(failureId)"
            class="text-red-600 hover:text-red-800">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <select
          (change)="onFailureStepSelect($event)"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
          <option value="">Add failure step...</option>
          <option *ngFor="let step of getAvailableFailureSteps()" [value]="step.id">{{ step.name }}</option>
        </select>
      </div>
    </div>

    <!-- Advanced Tab -->
    <div *ngIf="activeTab === 'advanced'" class="space-y-4">
      <!-- Retry Policy -->
      <div>
        <h4 class="text-sm font-medium text-gray-900 mb-3">Retry Policy</h4>

        <div class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Max Attempts</label>
            <input
              type="number"
              [(ngModel)]="step.retryPolicy.maxAttempts"
              (ngModelChange)="onStepChange()"
              min="1"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Delay (seconds)</label>
            <input
              type="number"
              [(ngModel)]="step.retryPolicy.delaySeconds"
              (ngModelChange)="onStepChange()"
              min="0"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Backoff Multiplier</label>
            <input
              type="number"
              [(ngModel)]="step.retryPolicy.backoffMultiplier"
              (ngModelChange)="onStepChange()"
              min="1"
              step="0.1"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
