import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { WorkflowStep, WorkflowDefinition, STEP_TYPES, StepType } from '../../../models/workflow.model';
import { ProviderService, ProviderInfo, ConfigField } from '../../../services/provider.service';
import { WorkflowProviderService, WorkflowProvider, WorkflowProviderConfigField } from '../../../services/workflow-provider.service';
import { DeploymentPluginService } from '../../../services/deployment-plugin.service';
import { DeploymentPlugin, PluginConfigSchema, PluginPropertySchema } from '../../../models/deployable.model';
import { DynamicFormService, DynamicFormSchema, PluginConfigurationSchema } from '../../../services/dynamic-form.service';
import { DynamicFormComponent } from '../../dynamic-form/dynamic-form.component';

@Component({
  selector: 'app-step-config',
  templateUrl: './step-config.component.html',
  styleUrls: ['./step-config.component.scss']
})
export class StepConfigComponent implements OnInit {
  @Input() step!: WorkflowStep;
  @Input() workflow!: WorkflowDefinition;
  @Output() stepUpdated = new EventEmitter<void>();
  @Output() close = new EventEmitter<void>();

  stepType: StepType | undefined;
  availableSteps: WorkflowStep[] = [];
  activeTab: string = 'basic';

  // Provider and Plugin specific data
  availableProviders: ProviderInfo[] = []; // For environment providers
  availableWorkflowProviders: WorkflowProvider[] = []; // For workflow providers
  availablePlugins: DeploymentPlugin[] = [];
  selectedProvider: ProviderInfo | null = null;
  selectedWorkflowProvider: WorkflowProvider | null = null;
  selectedPlugin: DeploymentPlugin | null = null;
  loadingProviders = false;
  loadingPlugins = false;

  // Dynamic form properties
  pluginConfigSchema: DynamicFormSchema | null = null;
  loadingPluginSchema = false;

  constructor(
    private providerService: ProviderService,
    private workflowProviderService: WorkflowProviderService,
    private pluginService: DeploymentPluginService,
    private dynamicFormService: DynamicFormService
  ) {}

  ngOnInit(): void {
    this.stepType = STEP_TYPES.find(type => type.type === this.step.type);
    this.availableSteps = this.workflow.steps.filter(s => s.id !== this.step.id);
    
    // Load providers and plugins for provider/plugin step types
    if (this.step.type === 'provider') {
      this.loadProviders();
    } else if (this.step.type === 'plugin') {
      this.loadPlugins();
    }
  }

  private loadProviders(): void {
    this.loadingProviders = true;
    
    // Load workflow providers for workflow-specific steps
    this.workflowProviderService.getProviders().subscribe({
      next: (response) => {
        this.availableWorkflowProviders = response.providers;
        // Set selected workflow provider if already configured
        if (this.step.config['providerType']) {
          this.selectedWorkflowProvider = this.availableWorkflowProviders.find(p => p.type === this.step.config['providerType']) || null;
        }
        this.loadingProviders = false;
      },
      error: (error: any) => {
        console.error('Failed to load workflow providers:', error);
        this.loadingProviders = false;
      }
    });
  }

  private loadPlugins(): void {
    this.loadingPlugins = true;
    this.pluginService.getAvailablePlugins().subscribe({
      next: (plugins: DeploymentPlugin[]) => {
        this.availablePlugins = plugins;
        // Set selected plugin if already configured
        if (this.step.config['pluginId']) {
          this.selectedPlugin = this.availablePlugins.find(p => p.id === this.step.config['pluginId']) || null;
        }
        this.loadingPlugins = false;
      },
      error: (error: any) => {
        console.error('Failed to load plugins:', error);
        this.loadingPlugins = false;
      }
    });
  }

  onProviderChange(providerType: string): void {
    this.selectedWorkflowProvider = this.availableWorkflowProviders.find(p => p.type === providerType) || null;
    this.step.config['providerType'] = providerType;
    // Reset provider config when provider changes
    this.step.config['providerConfig'] = {};
    this.onStepChange();
  }

  onPluginChange(pluginId: string): void {
    this.selectedPlugin = this.availablePlugins.find(p => p.id === pluginId) || null;
    this.step.config['pluginId'] = pluginId;
    // Reset plugin config when plugin changes
    this.step.config['pluginConfig'] = {};

    // Load dynamic plugin configuration schema
    if (this.selectedPlugin) {
      this.loadPluginConfigSchema(this.selectedPlugin.name);
    } else {
      this.pluginConfigSchema = null;
    }

    this.onStepChange();
  }

  private loadPluginConfigSchema(pluginName: string): void {
    this.loadingPluginSchema = true;
    this.dynamicFormService.getPluginConfigSchema(pluginName).subscribe({
      next: (schema: PluginConfigurationSchema) => {
        this.pluginConfigSchema = this.dynamicFormService.convertPluginSchemaToFormSchema(schema);
        this.loadingPluginSchema = false;
      },
      error: (error) => {
        console.error('Failed to load plugin config schema:', error);
        this.pluginConfigSchema = null;
        this.loadingPluginSchema = false;
      }
    });
  }

  onProviderConfigChange(fieldName: string, value: any): void {
    if (!this.step.config['providerConfig']) {
      this.step.config['providerConfig'] = {};
    }
    this.step.config['providerConfig'][fieldName] = value;
    this.onStepChange();
  }

  onPluginConfigChange(fieldName: string, value: any): void {
    if (!this.step.config['pluginConfig']) {
      this.step.config['pluginConfig'] = {};
    }
    this.step.config['pluginConfig'][fieldName] = value;
    this.onStepChange();
  }

  onDynamicFormChange(formValues: any): void {
    this.step.config['pluginConfig'] = formValues;
    this.onStepChange();
  }

  onDynamicFormSubmit(formValues: any): void {
    this.step.config['pluginConfig'] = formValues;
    this.onStepChange();
  }

  getProviderConfigFields(): WorkflowProviderConfigField[] {
    return this.selectedWorkflowProvider?.configFields || [];
  }

  getPluginConfigFields(): PluginConfigSchema[] {
    // Check if plugin has the new configuration schema structure
    if (this.selectedPlugin?.configuration?.schema?.properties) {
      return this.convertPluginSchemaToConfigFields(this.selectedPlugin.configuration.schema);
    }
    // Fallback to legacy configurationSchema if available
    return this.selectedPlugin?.configurationSchema || [];
  }

  private convertPluginSchemaToConfigFields(schema: any): PluginConfigSchema[] {
    const fields: PluginConfigSchema[] = [];
    const properties = schema.properties || {};
    const required = schema.required || [];

    for (const [key, property] of Object.entries(properties)) {
      const prop = property as any;
      const field: PluginConfigSchema = {
        key,
        label: prop.title || this.formatLabel(key),
        type: this.mapPluginTypeToConfigType(prop.type),
        required: required.includes(key),
        description: prop.description,
        defaultValue: prop.default
      };

      // Handle validation patterns
      if (prop.pattern) {
        field.validation = {
          pattern: prop.pattern
        };
      }

      // Handle numeric constraints
      if (prop.minimum !== undefined || prop.maximum !== undefined) {
        if (!field.validation) field.validation = {};
        field.validation.min = prop.minimum;
        field.validation.max = prop.maximum;
      }

      // Handle enum/select options
      if (prop.enum) {
        field.type = 'select';
        field.options = prop.enum.map((value: string) => ({
          label: this.formatLabel(value),
          value
        }));
      }

      // Handle examples as placeholder
      if (prop.examples && prop.examples.length > 0) {
        field.description = field.description || '';
        if (field.description) field.description += '\n';
        field.description += `Example: ${prop.examples[0]}`;
      }

      fields.push(field);
    }

    return fields;
  }

  private mapPluginTypeToConfigType(pluginType: string): PluginConfigSchema['type'] {
    switch (pluginType) {
      case 'password':
        return 'password';
      case 'integer':
      case 'number':
        return 'number';
      case 'boolean':
        return 'boolean';
      case 'string':
      default:
        return 'string';
    }
  }

  private formatLabel(key: string): string {
    return key
      .replace(/[_-]/g, ' ')
      .replace(/\b\w/g, char => char.toUpperCase());
  }

  onStepChange(): void {
    this.stepUpdated.emit();
  }

  onClose(): void {
    this.close.emit();
  }

  addDependency(stepId: string): void {
    if (stepId && !this.step.dependencies.includes(stepId)) {
      this.step.dependencies.push(stepId);
      this.onStepChange();
    }
  }

  removeDependency(stepId: string): void {
    this.step.dependencies = this.step.dependencies.filter(id => id !== stepId);
    this.onStepChange();
  }

  addSuccessStep(stepId: string): void {
    if (stepId && !this.step.onSuccess.includes(stepId)) {
      this.step.onSuccess.push(stepId);
      this.onStepChange();
    }
  }

  removeSuccessStep(stepId: string): void {
    this.step.onSuccess = this.step.onSuccess.filter(id => id !== stepId);
    this.onStepChange();
  }

  addFailureStep(stepId: string): void {
    if (stepId && !this.step.onFailure.includes(stepId)) {
      this.step.onFailure.push(stepId);
      this.onStepChange();
    }
  }

  removeFailureStep(stepId: string): void {
    this.step.onFailure = this.step.onFailure.filter(id => id !== stepId);
    this.onStepChange();
  }

  getStepName(stepId: string): string {
    const step = this.workflow.steps.find(s => s.id === stepId);
    return step ? step.name : stepId;
  }

  getAvailableDependencies(): WorkflowStep[] {
    return this.availableSteps.filter(s => !this.step.dependencies.includes(s.id));
  }

  getAvailableSuccessSteps(): WorkflowStep[] {
    return this.availableSteps.filter(s => !this.step.onSuccess.includes(s.id));
  }

  getAvailableFailureSteps(): WorkflowStep[] {
    return this.availableSteps.filter(s => !this.step.onFailure.includes(s.id));
  }

  updateConfigValue(key: string, value: any): void {
    this.step.config[key] = value;
    this.onStepChange();
  }

  onInputChange(key: string, event: Event): void {
    const target = event.target as HTMLInputElement;
    this.updateConfigValue(key, target.value);
  }

  onNumberChange(key: string, event: Event): void {
    const target = event.target as HTMLInputElement;
    this.updateConfigValue(key, +target.value);
  }

  onTextareaChange(key: string, event: Event): void {
    const target = event.target as HTMLTextAreaElement;
    this.updateConfigValue(key, target.value);
  }

  onSelectChange(key: string, event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.updateConfigValue(key, target.value);
  }

  onCheckboxChange(key: string, event: Event): void {
    const target = event.target as HTMLInputElement;
    this.updateConfigValue(key, target.checked);
  }

  onDependencySelect(event: Event): void {
    const target = event.target as HTMLSelectElement;
    if (target.value) {
      this.addDependency(target.value);
      target.value = '';
    }
  }

  onSuccessStepSelect(event: Event): void {
    const target = event.target as HTMLSelectElement;
    if (target.value) {
      this.addSuccessStep(target.value);
      target.value = '';
    }
  }

  onFailureStepSelect(event: Event): void {
    const target = event.target as HTMLSelectElement;
    if (target.value) {
      this.addFailureStep(target.value);
      target.value = '';
    }
  }

  getConfigKeys(): string[] {
    return Object.keys(this.stepType?.configSchema || {});
  }

  getConfigType(key: string): string {
    return this.stepType?.configSchema[key]?.type || 'text';
  }

  getConfigOptions(key: string): string[] {
    return this.stepType?.configSchema[key]?.options || [];
  }

  isConfigRequired(key: string): boolean {
    return this.stepType?.configSchema[key]?.required || false;
  }

  // Utility methods for option handling in templates
  isOptionString(option: any): boolean {
    return typeof option === 'string';
  }

  getOptionValue(option: any): any {
    return typeof option === 'string' ? option : option?.value;
  }

  getOptionLabel(option: any): string {
    return typeof option === 'string' ? option : option?.label || option?.value || '';
  }

  // Safe method to get iterable options
  getIterableOptions(options: any): any[] {
    return Array.isArray(options) ? options : [];
  }
}
