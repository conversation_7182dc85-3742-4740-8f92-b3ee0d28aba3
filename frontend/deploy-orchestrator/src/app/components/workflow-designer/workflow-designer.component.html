<div class="h-screen flex flex-col bg-gray-100">
  <!-- Header -->
  <div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <button
          (click)="goBack()"
          class="mr-4 p-2 text-gray-400 hover:text-gray-600">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </button>
        <div>
          <h1 class="text-xl font-semibold text-gray-900">
            {{ designerState.workflow.name }}
            <span *ngIf="designerState.isDirty" class="text-orange-500 ml-2">*</span>
          </h1>
          <p class="text-sm text-gray-500">{{ designerState.workflow.description || 'No description' }}</p>
        </div>
      </div>

      <div class="flex items-center space-x-3">
        <!-- Workflow Configuration Button -->
        <button
          (click)="showProperties()"
          [class]="showWorkflowProperties ?
            'px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 border border-blue-600' :
            'px-4 py-2 bg-white text-blue-600 text-sm rounded-md hover:bg-blue-50 border border-blue-600'"
          class="inline-flex items-center"
          title="Configure workflow properties, parameters, and variables">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
          <span>Configure Workflow</span>
          <!-- Variable Count Badge -->
          <div class="ml-2 flex items-center space-x-1">
            <span *ngIf="getVariableCount() > 0"
                  class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  title="{{getVariableCount()}} template variable{{getVariableCount() !== 1 ? 's' : ''}} defined">
              {{getVariableCount()}} variable{{getVariableCount() !== 1 ? 's' : ''}}
            </span>
          </div>
        </button>

        <!-- Save Button -->
        <button
          (click)="saveWorkflow()"
          [disabled]="isLoading"
          class="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 disabled:opacity-50 inline-flex items-center">
          <svg *ngIf="!isLoading" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
          </svg>
          <span *ngIf="isLoading" class="inline-flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Saving...
          </span>
          <span *ngIf="!isLoading">Save Workflow</span>
        </button>

        <!-- Publish Template Button -->
        <button
          *ngIf="designerState.workflow.id && !isLoading"
          (click)="showPublishTemplateModal = true"
          class="px-4 py-2 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-50 inline-flex items-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
          </svg>
          Publish as Template
        </button>
      </div>
    </div>
  </div>

  <!-- Error Message -->
  <div *ngIf="error" class="bg-red-50 border-l-4 border-red-400 p-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>
      </div>
      <div class="ml-3">
        <p class="text-sm text-red-700">{{ error }}</p>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="flex-1 flex overflow-hidden">
    <!-- Step Palette -->
    <div class="w-80 bg-white border-r border-gray-200">
      <app-step-palette (stepSelected)="onStepSelected($event)"></app-step-palette>
    </div>

    <!-- Canvas Area -->
    <div class="flex-1 relative overflow-hidden flex flex-col">
      <!-- Canvas Toolbar -->
      <div class="bg-white border-b border-gray-200 px-4 py-2 flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <span class="text-sm font-medium text-gray-700">Layout:</span>
          <button
            (click)="autoLayout()"
            class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm rounded-md text-gray-700 bg-white hover:bg-gray-50">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
            </svg>
            Grid Layout
          </button>
          <button
            (click)="smartLayout()"
            class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm rounded-md text-gray-700 bg-white hover:bg-gray-50">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            Smart Layout
          </button>
        </div>

        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-500">
            {{ designerState.workflow.steps.length }} steps
          </span>
          <span class="text-sm text-gray-500">
            {{ designerState.connections.length }} connections
          </span>
        </div>
      </div>

      <div
        #canvas
        class="flex-1 bg-gray-50 relative cursor-grab"
        [class.cursor-grabbing]="isDragging"
        [class.cursor-crosshair]="isCreatingConnection"
        cdkDropList
        cdkDropListId="canvas-drop-list"
        [cdkDropListData]="designerState.workflow.steps"
        (cdkDropListDropped)="onCanvasDrop($event)"
        (mousedown)="onCanvasMouseDown($event)"
        (mousemove)="onCanvasMouseMove($event); updateTempConnection($event)"
        (mouseup)="onCanvasMouseUp()"
        (mouseleave)="onCanvasMouseUp()">

        <!-- Grid Pattern -->
        <svg class="absolute inset-0 w-full h-full pointer-events-none" style="z-index: 0;">
          <defs>
            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" stroke-width="1" opacity="0.5"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>

        <!-- Workflow Steps -->
        <div
          *ngFor="let step of designerState.workflow.steps; trackBy: trackByStepId"
          class="absolute"
          [style.left.px]="step.position.x + canvasOffset.x"
          [style.top.px]="step.position.y + canvasOffset.y"
          [style.z-index]="10">

          <div
            (click)="selectStep(step)"
            (mousedown)="onStepMouseDown($event, step)"
            (mousemove)="onStepMouseMove($event)"
            (mouseup)="onStepMouseUp($event)"
            (drop)="completeConnection(step)"
            (dragover)="$event.preventDefault()"
            [class]="'bg-white border-2 rounded-lg p-4 cursor-pointer shadow-sm hover:shadow-md transition-shadow min-w-[160px] ' +
                     (designerState.selectedStep?.id === step.id ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200 hover:border-gray-300') +
                     (isDraggingStep && draggedStep?.id === step.id ? ' opacity-75 transform scale-105' : '')">

            <!-- Step Header -->
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                  </svg>
                </div>
                <div>
                  <h4 class="text-sm font-medium text-gray-900">{{ step.name }}</h4>
                  <p class="text-xs text-gray-500">{{ step.type }}</p>
                </div>
              </div>

              <button
                (click)="deleteStep(step); $event.stopPropagation()"
                class="text-gray-400 hover:text-red-600 p-1">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
              </button>
            </div>

            <!-- Step Description -->
            <p *ngIf="step.description" class="text-xs text-gray-600 mb-2">{{ step.description }}</p>

            <!-- Step Status Indicators -->
            <div class="flex items-center justify-between text-xs">
              <div class="flex items-center space-x-2">
                <span *ngIf="step.dependencies.length > 0" class="text-orange-600">
                  {{ step.dependencies.length }} deps
                </span>
                <span *ngIf="step.timeout" class="text-gray-500">
                  {{ step.timeout }}m timeout
                </span>
              </div>
              <div class="flex items-center space-x-1">
                <span *ngIf="step.onSuccess.length > 0" class="w-2 h-2 bg-green-400 rounded-full" title="Has success flow"></span>
                <span *ngIf="step.onFailure.length > 0" class="w-2 h-2 bg-red-400 rounded-full" title="Has failure flow"></span>
              </div>
            </div>

            <!-- Connection Points -->
            <div
              class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow-sm cursor-crosshair hover:scale-125 transition-transform"
              title="Input - Drop connections here"
              (drop)="completeConnection(step)"
              (dragover)="$event.preventDefault()"></div>

            <div
              class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow-sm cursor-crosshair hover:scale-125 transition-transform"
              title="Success Output - Drag to create success connection"
              (mousedown)="startConnection($event, step, 'success')"
              draggable="true"></div>

            <div
              class="absolute -bottom-2 right-4 w-4 h-4 bg-red-500 rounded-full border-2 border-white shadow-sm cursor-crosshair hover:scale-125 transition-transform"
              title="Failure Output - Drag to create failure connection"
              (mousedown)="startConnection($event, step, 'failure')"
              draggable="true"></div>

            <div
              class="absolute -left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-orange-500 rounded-full border-2 border-white shadow-sm cursor-crosshair hover:scale-125 transition-transform"
              title="Dependency Output - Drag to create dependency"
              (mousedown)="startConnection($event, step, 'dependency')"
              draggable="true"></div>
          </div>
        </div>

        <!-- Connection Lines -->
        <svg class="absolute inset-0 w-full h-full pointer-events-none" style="z-index: 5;">
          <!-- Existing connections -->
          <g *ngFor="let connection of designerState.connections">
            <ng-container *ngIf="getStepById(connection.sourceStepId) as sourceStep">
              <ng-container *ngIf="getStepById(connection.targetStepId) as targetStep">
                <line
                  [attr.x1]="sourceStep.position.x + canvasOffset.x + 80"
                  [attr.y1]="sourceStep.position.y + canvasOffset.y + 80"
                  [attr.x2]="targetStep.position.x + canvasOffset.x + 80"
                  [attr.y2]="targetStep.position.y + canvasOffset.y"
                  [attr.stroke]="getConnectionColor(connection.type)"
                  stroke-width="2"
                  marker-end="url(#arrowhead)"
                  class="cursor-pointer hover:stroke-width-3"
                  (click)="deleteConnection(connection)"/>
              </ng-container>
            </ng-container>
          </g>

          <!-- Temporary connection line during creation -->
          <g *ngIf="isCreatingConnection && connectionStart">
            <line
              [attr.x1]="connectionStart.position.x + canvasOffset.x + 80"
              [attr.y1]="connectionStart.position.y + canvasOffset.y + 80"
              [attr.x2]="tempConnectionEnd.x"
              [attr.y2]="tempConnectionEnd.y"
              [attr.stroke]="getConnectionColor(connectionType)"
              stroke-width="2"
              stroke-dasharray="5,5"
              opacity="0.7"
              marker-end="url(#arrowhead)"/>
          </g>

          <!-- Arrow marker definition -->
          <defs>
            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
              <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
            </marker>
          </defs>
        </svg>

        <!-- Empty State -->
        <div *ngIf="designerState.workflow.steps.length === 0" class="absolute inset-0 flex items-center justify-center">
          <div class="text-center max-w-md">
            <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
            </svg>
            <h3 class="mt-4 text-lg font-medium text-gray-900">Start Building Your Workflow</h3>
            <p class="mt-2 text-sm text-gray-500">Create a powerful automation workflow by following these steps:</p>

            <div class="mt-6 space-y-3 text-left">
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <span class="text-xs font-medium text-blue-600">1</span>
                </div>
                <p class="text-sm text-gray-600">Drag steps from the left palette to the canvas</p>
              </div>
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <span class="text-xs font-medium text-blue-600">2</span>
                </div>
                <p class="text-sm text-gray-600">Click steps to configure their settings</p>
              </div>
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <span class="text-xs font-medium text-blue-600">3</span>
                </div>
                <p class="text-sm text-gray-600">
                  Click
                  <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 mx-1">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Configure Workflow
                  </span>
                  to set parameters and variables
                </p>
              </div>
            </div>

            <div class="mt-6">
              <button
                (click)="showProperties()"
                class="inline-flex items-center px-4 py-2 border border-blue-300 text-blue-700 text-sm rounded-md hover:bg-blue-50">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                Configure Workflow Properties
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Configuration Panel -->
    <div *ngIf="showStepConfig || showWorkflowProperties" class="w-96 bg-white border-l border-gray-200">
      <!-- Step Configuration -->
      <div *ngIf="showStepConfig && designerState.selectedStep">
        <app-step-config
          [step]="designerState.selectedStep"
          [workflow]="designerState.workflow"
          (stepUpdated)="markDirty()"
          (close)="showStepConfig = false">
        </app-step-config>
      </div>

      <!-- Workflow Properties -->
      <div *ngIf="showWorkflowProperties" class="h-full flex flex-col">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Workflow Properties</h3>
            <button
              (click)="showWorkflowProperties = false"
              class="text-gray-400 hover:text-gray-600">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>

        <div class="flex-1 overflow-y-auto p-6">
          <!-- Workflow Properties Tabs -->
          <div class="border-b border-gray-200 mb-6">
            <nav class="-mb-px flex space-x-8">
              <button
                (click)="workflowPropertiesTab = 'basic'"
                [class]="workflowPropertiesTab === 'basic' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Basic
              </button>
              <button
                (click)="workflowPropertiesTab = 'variables'"
                [class]="workflowPropertiesTab === 'variables' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Template Variables
                <span *ngIf="getVariableCount() > 0"
                      class="ml-2 bg-blue-100 text-blue-600 py-0.5 px-2 rounded-full text-xs">
                  {{getVariableCount()}}
                </span>
              </button>
            </nav>
          </div>

          <!-- Basic Properties Tab -->
          <div *ngIf="workflowPropertiesTab === 'basic'" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
              <input
                type="text"
                [(ngModel)]="designerState.workflow.name"
                (ngModelChange)="markDirty()"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                [(ngModel)]="designerState.workflow.description"
                (ngModelChange)="markDirty()"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
              </textarea>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Project</label>
              <select
                [(ngModel)]="designerState.workflow.projectId"
                (ngModelChange)="markDirty()"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                <option value="">Select a project</option>
                <option *ngFor="let project of projects" [value]="project.id">{{ project.name }}</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Version</label>
              <input
                type="text"
                [(ngModel)]="designerState.workflow.version"
                (ngModelChange)="markDirty()"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <div class="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                [(ngModel)]="designerState.workflow.isActive"
                (ngModelChange)="markDirty()"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
              <label for="isActive" class="ml-2 block text-sm text-gray-900">Active</label>
            </div>
          </div>

          <!-- Template Variables Tab -->
          <div *ngIf="workflowPropertiesTab === 'variables'" class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-medium text-gray-900">Template Variables</h3>
                <p class="text-sm text-gray-600 mt-1">Define variables that users can customize when using this template</p>
              </div>
              <button
                (click)="addVariable()"
                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Variable
              </button>
            </div>

            <div *ngIf="getVariableCount() === 0" class="text-center py-8 text-gray-500">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4m-6-4a2 2 0 01-2-2V9a2 2 0 012-2h2m0 0V5a2 2 0 012-2h4a2 2 0 012 2v2M7 7h10"></path>
              </svg>
              <p class="mt-2">No template variables defined</p>
              <p class="text-sm">Variables allow users to customize values when creating workflows from this template.</p>
              <p class="text-sm mt-2">Use variables in step configurations with <code class="bg-gray-200 px-1 rounded">{{'{{'}}{{'{{'}}variableName{{'}}'}}}}</code> syntax.</p>
            </div>

            <div *ngFor="let variable of getVariableEntries(); let i = index; trackBy: trackByVariableKey"
                 class="border border-gray-200 rounded-lg p-4 space-y-3">
              <div class="flex items-center justify-between">
                <h4 class="font-medium text-gray-900">Variable {{i + 1}}</h4>
                <button
                  (click)="removeVariable(variable.key)"
                  class="text-red-600 hover:text-red-800">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                </button>
              </div>

              <div class="space-y-3">
                <!-- Variable Name and Type Row -->
                <div class="grid grid-cols-2 gap-3">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Variable Name *</label>
                    <input
                      type="text"
                      [value]="variable.key"
                      (input)="updateVariableKey(variable.key, $event)"
                      [attr.data-variable-key]="variable.key"
                      placeholder="variable_name"
                      class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                    <select
                      [value]="getVariableType(variable.key)"
                      (change)="updateVariableType(variable.key, $event)"
                      class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                      <option value="string">String</option>
                      <option value="number">Number</option>
                      <option value="boolean">Boolean</option>
                      <option value="select">Select</option>
                      <option value="textarea">Text Area</option>
                      <option value="enum">Enum</option>
                      <option value="secret">Secret</option>
                    </select>
                  </div>
                </div>

                <!-- Default Value Row -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Default Value</label>
                  <input
                    *ngIf="getVariableType(variable.key) !== 'textarea'"
                    type="text"
                    [value]="variable.value"
                    (input)="updateVariableValue(variable.key, $event)"
                    [attr.data-variable-value]="variable.key"
                    placeholder="Default value"
                    class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <textarea
                    *ngIf="getVariableType(variable.key) === 'textarea'"
                    [value]="variable.value"
                    (input)="updateVariableValue(variable.key, $event)"
                    [attr.data-variable-value]="variable.key"
                    placeholder="Default value"
                    rows="3"
                    class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"></textarea>
                </div>

                <!-- Description Row -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <textarea
                    [value]="getVariableDescription(variable.key)"
                    (input)="updateVariableDescription(variable.key, $event)"
                    placeholder="Describe what this variable is used for..."
                    rows="2"
                    class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"></textarea>
                </div>

                <!-- Options for Select/Enum Types -->
                <div *ngIf="getVariableType(variable.key) === 'select' || getVariableType(variable.key) === 'enum'">
                  <label class="block text-sm font-medium text-gray-700 mb-1">Options (one per line)</label>
                  <textarea
                    [value]="getVariableOptions(variable.key)"
                    (input)="updateVariableOptions(variable.key, $event)"
                    placeholder="option1&#10;option2&#10;option3"
                    rows="3"
                    class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"></textarea>
                  <p class="text-xs text-gray-500 mt-1">Enter each option on a new line</p>
                </div>

                <!-- Secret Selection for Secret Type -->
                <div *ngIf="getVariableType(variable.key) === 'secret'">
                  <div class="bg-blue-50 border border-blue-200 rounded-md p-3 mb-3">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                        </svg>
                      </div>
                      <div class="ml-3">
                        <h4 class="text-sm font-medium text-blue-800">Secret Variable</h4>
                        <div class="mt-1 text-sm text-blue-700">
                          <p>This variable will be populated with project secrets during deployment.</p>
                          <p class="mt-1">Users will be able to choose specific secrets when creating deployments.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Required Checkbox -->
                <div class="flex items-center">
                  <input
                    type="checkbox"
                    [checked]="getVariableRequired(variable.key)"
                    (change)="updateVariableRequired(variable.key, $event)"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                  <label class="ml-2 block text-sm text-gray-900">Required field</label>
                </div>
              </div>

              <div class="bg-gray-50 p-3 rounded-md">
                <p class="text-sm text-gray-600">
                  <strong>Usage:</strong> Reference this variable in step configurations using
                  <code class="bg-gray-200 px-1 rounded">{{'{{'}}{{'{{'}}{{variable.key}}{{'}}'}}{{'}}'}}}</code>
                </p>
              </div>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-blue-800">Template Variable Best Practices</h3>
                  <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc pl-5 space-y-1">
                      <li>Define variables for values users might want to customize (image names, environments, etc.)</li>
                      <li>Provide sensible default values that work out-of-the-box</li>
                      <li>Use descriptive variable names like <code>imageName</code>, <code>environment</code>, <code>replicas</code></li>
                      <li>Users can override these values when creating workflows or during execution</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Success/Error Messages -->
<div *ngIf="successMessage" class="fixed top-4 right-4 z-50 bg-green-50 border border-green-200 rounded-md p-4 shadow-lg">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
      </svg>
    </div>
    <div class="ml-3">
      <p class="text-sm text-green-700">{{ successMessage }}</p>
    </div>
    <div class="ml-auto pl-3">
      <button (click)="successMessage = null" class="text-green-400 hover:text-green-600">
        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
  </div>
</div>

<!-- Publish Template Modal -->
<div *ngIf="showPublishTemplateModal" class="fixed inset-0 z-50 overflow-y-auto">
  <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" (click)="showPublishTemplateModal = false"></div>

    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
      <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
        <div class="sm:flex sm:items-start">
          <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
            </svg>
          </div>
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Publish as Template
            </h3>
            <div class="mt-4 space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Template Name</label>
                <input
                  type="text"
                  [(ngModel)]="templateData.name"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter template name">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  [(ngModel)]="templateData.description"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Describe what this template does">
                </textarea>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select
                  [(ngModel)]="templateData.category"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                  <option value="">Select a category</option>
                  <option value="deployment">Deployment</option>
                  <option value="testing">Testing</option>
                  <option value="monitoring">Monitoring</option>
                  <option value="integration">Integration</option>
                  <option value="automation">Automation</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Tags (comma-separated)</label>
                <input
                  type="text"
                  [(ngModel)]="templateData.tagsString"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., docker, kubernetes, ci/cd">
              </div>
              <div class="flex items-center">
                <input
                  type="checkbox"
                  id="isPublic"
                  [(ngModel)]="templateData.isPublic"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="isPublic" class="ml-2 block text-sm text-gray-900">Make template public</label>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
        <button
          (click)="publishTemplate()"
          [disabled]="publishingTemplate || !templateData.name || !templateData.category"
          class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50">
          <span *ngIf="publishingTemplate" class="inline-flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Publishing...
          </span>
          <span *ngIf="!publishingTemplate">Publish Template</span>
        </button>
        <button
          (click)="showPublishTemplateModal = false"
          class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>
