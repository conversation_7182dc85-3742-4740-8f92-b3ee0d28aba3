<div class="bg-white border-r border-gray-200 h-full flex flex-col">
  <!-- Header -->
  <div class="px-4 py-3 border-b border-gray-200">
    <h3 class="text-sm font-medium text-gray-900">Step Palette</h3>
  </div>

  <!-- Search and Filter -->
  <div class="p-4 border-b border-gray-200">
    <!-- Search -->
    <div class="relative mb-3">
      <input
        type="text"
        [(ngModel)]="searchTerm"
        placeholder="Search steps..."
        class="w-full pl-8 pr-8 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
      <svg class="absolute left-2 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
      </svg>
      <button
        *ngIf="searchTerm"
        (click)="clearSearch()"
        class="absolute right-2 top-2.5 h-4 w-4 text-gray-400 hover:text-gray-600">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Category Filter -->
    <div class="relative">
      <select
        [(ngModel)]="selectedCategory"
        class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
        <option value="">All Categories</option>
        <option *ngFor="let category of categories" [value]="category">{{ category }}</option>
      </select>
      <button
        *ngIf="selectedCategory"
        (click)="clearCategory()"
        class="absolute right-8 top-2.5 h-4 w-4 text-gray-400 hover:text-gray-600">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Step Types List -->
  <div class="flex-1 overflow-y-auto" cdkDropList [cdkDropListData]="filteredStepTypes" [cdkDropListConnectedTo]="['canvas-drop-list']">
    <div class="p-2">
      <div *ngIf="filteredStepTypes.length === 0" class="text-center py-8 text-gray-500 text-sm">
        No steps found
      </div>

      <div
        *ngFor="let stepType of filteredStepTypes"
        class="mb-2 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group relative">

        <!-- Main clickable area -->
        <div
          (click)="selectStep(stepType)"
          class="p-3 cursor-pointer">
          <div class="flex items-start">
            <div class="flex-shrink-0 mr-3">
              <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200">
                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="getStepIcon(stepType.icon)"></path>
                </svg>
              </div>
            </div>

            <div class="flex-1 min-w-0">
              <h4 class="text-sm font-medium text-gray-900 group-hover:text-blue-900">{{ stepType.name }}</h4>
              <p class="text-xs text-gray-500 mt-1 line-clamp-2">{{ stepType.description }}</p>
              <div class="mt-2">
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 group-hover:bg-blue-100 group-hover:text-blue-800">
                  {{ stepType.category }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Separate drag handle -->
        <div
          cdkDrag
          [cdkDragData]="stepType"
          (cdkDragStarted)="onDragStart($event, stepType)"
          (cdkDragEnded)="onDragEnd($event)"
          class="absolute top-2 right-2 w-6 h-6 opacity-0 group-hover:opacity-100 transition-opacity cursor-move bg-gray-100 hover:bg-gray-200 rounded flex items-center justify-center"
          title="Drag to canvas">
          <svg class="w-3 h-3 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
          </svg>
        </div>
      </div>
    </div>
  </div>

  <!-- Help Text -->
  <div class="px-4 py-3 border-t border-gray-200 bg-gray-50">
    <p class="text-xs text-gray-600">
      <strong>Click</strong> to add steps instantly, or <strong>drag</strong> the handle (⋮) to position them precisely on the canvas.
    </p>
  </div>
</div>
