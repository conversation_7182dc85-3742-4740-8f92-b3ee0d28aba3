import { Component, Output, EventEmitter } from '@angular/core';
import { CdkDragStart, CdkDragEnd } from '@angular/cdk/drag-drop';
import { StepType, STEP_TYPES } from '../../../models/workflow.model';

@Component({
  selector: 'app-step-palette',
  templateUrl: './step-palette.component.html',
  styleUrls: ['./step-palette.component.scss']
})
export class StepPaletteComponent {
  @Output() stepSelected = new EventEmitter<StepType>();

  stepTypes = STEP_TYPES;
  categories: string[] = [];
  selectedCategory: string = '';
  searchTerm: string = '';

  constructor() {
    this.categories = [...new Set(this.stepTypes.map(step => step.category))];
  }

  get filteredStepTypes(): StepType[] {
    let filtered = this.stepTypes;

    if (this.selectedCategory) {
      filtered = filtered.filter(step => step.category === this.selectedCategory);
    }

    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(step =>
        step.name.toLowerCase().includes(term) ||
        step.description.toLowerCase().includes(term) ||
        step.type.toLowerCase().includes(term)
      );
    }

    return filtered;
  }

  onDragStart(event: CdkDragStart, stepType: StepType): void {
    // Store the step type data for the drop handler
    event.source.data = stepType;

    // Add visual feedback to the original element
    const dragElement = event.source.element.nativeElement;
    dragElement.style.opacity = '0.5';
  }

  onDragEnd(event: CdkDragEnd): void {
    // Reset visual feedback
    const dragElement = event.source.element.nativeElement;
    dragElement.style.opacity = '1';
  }

  selectStep(stepType: StepType): void {
    this.stepSelected.emit(stepType);
  }

  getStepIcon(icon: string): string {
    const iconMap: { [key: string]: string } = {
      'code': 'M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4',
      'globe': 'M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9',
      'rocket': 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
      'branch': 'M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z',
      'parallel': 'M4 6h16M4 12h16M4 18h16',
      'sequential': 'M9 5l7 7-7 7'
    };
    return iconMap[icon] || iconMap['code'];
  }

  clearSearch(): void {
    this.searchTerm = '';
  }

  clearCategory(): void {
    this.selectedCategory = '';
  }
}
