import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { Subject, takeUntil, forkJoin } from 'rxjs';

import { DeploymentManagementService, VersionMatrix, EnvironmentVersion } from '../../services/deployment-management.service';
import { EnvironmentService } from '../../services/environment.service';
import { ProjectService } from '../../services/project.service';
import { NotificationService } from '../../services/notification.service';
import { PromotionDialogComponent, PromotionDialogData } from '../promotion-dialog/promotion-dialog.component';

@Component({
  selector: 'app-version-matrix',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule
  ],
  templateUrl: './version-matrix.component.html',
  styleUrls: ['./version-matrix.component.scss']
})
export class VersionMatrixComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  // Data
  projects: any[] = [];
  environments: any[] = [];
  versionMatrix: VersionMatrix = {};
  environmentVersions: EnvironmentVersion[] = [];
  
  // Selected data
  selectedProjectId: string = '';
  
  // UI state
  loading = false;
  
  // Matrix display
  entityNames: string[] = [];
  environmentIds: string[] = [];

  constructor(
    private deploymentService: DeploymentManagementService,
    private environmentService: EnvironmentService,
    private projectService: ProjectService,
    private notificationService: NotificationService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadProjects();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadProjects(): void {
    this.loading = true;
    this.projectService.getProjects()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (projects) => {
          this.projects = projects;
          if (projects.length > 0) {
            this.selectedProjectId = projects[0].id;
            this.onProjectChange();
          }
          this.loading = false;
        },
        error: (error) => {
          this.notificationService.error('Failed to load projects', 'Please try again');
          this.loading = false;
        }
      });
  }

  onProjectChange(): void {
    if (this.selectedProjectId) {
      this.loadProjectData();
    }
  }

  private loadProjectData(): void {
    this.loading = true;
    
    forkJoin({
      environments: this.environmentService.getEnvironments({ projectId: this.selectedProjectId }),
      versionMatrix: this.deploymentService.getVersionMatrix(this.selectedProjectId),
      environmentVersions: this.deploymentService.getEnvironmentVersions(this.selectedProjectId)
    }).pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (data) => {
        this.environments = data.environments.environments || [];
        this.versionMatrix = data.versionMatrix.matrix;
        this.environmentVersions = data.environmentVersions.versions;
        this.processMatrixData();
        this.loading = false;
      },
      error: (error) => {
        this.notificationService.error('Failed to load project data', 'Please try again');
        this.loading = false;
      }
    });
  }

  private processMatrixData(): void {
    // Extract unique entity names and environment IDs
    this.entityNames = Object.keys(this.versionMatrix);
    this.environmentIds = this.environments.map(env => env.id);
  }

  getVersion(entityName: string, environmentId: string): string {
    return this.versionMatrix[entityName]?.[environmentId] || '-';
  }

  hasVersion(entityName: string, environmentId: string): boolean {
    return !!this.versionMatrix[entityName]?.[environmentId];
  }

  getEnvironmentName(environmentId: string): string {
    const env = this.environments.find(e => e.id === environmentId);
    return env ? env.name : environmentId;
  }

  getVersionStatus(entityName: string, environmentId: string): string {
    const version = this.getVersion(entityName, environmentId);
    if (version === '-') return 'not-deployed';
    
    // Find the environment version record to get status
    const envVersion = this.environmentVersions.find(ev => 
      ev.environmentId === environmentId && 
      ev.version === version &&
      ((ev.application && ev.application.name === entityName) ||
       (ev.component && ev.component.name === entityName))
    );
    
    return envVersion ? envVersion.status : 'unknown';
  }

  getStatusColorClass(status: string): string {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'deploying': return 'bg-blue-100 text-blue-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'rolled_back': return 'bg-yellow-100 text-yellow-800';
      case 'not-deployed': return 'bg-gray-100 text-gray-500';
      default: return 'bg-gray-100 text-gray-600';
    }
  }

  canPromote(entityName: string, sourceEnvId: string, targetEnvId: string): boolean {
    const sourceVersion = this.getVersion(entityName, sourceEnvId);
    const targetVersion = this.getVersion(entityName, targetEnvId);
    
    return sourceVersion !== '-' && 
           sourceVersion !== targetVersion &&
           this.getVersionStatus(entityName, sourceEnvId) === 'active';
  }

  promoteVersion(entityName: string, sourceEnvId: string, targetEnvId: string): void {
    const sourceVersion = this.getVersion(entityName, sourceEnvId);

    if (!this.canPromote(entityName, sourceEnvId, targetEnvId)) {
      this.notificationService.error('Cannot promote this version', 'Check version status');
      return;
    }

    // Find the entity (application or component)
    const envVersion = this.environmentVersions.find(ev =>
      ev.environmentId === sourceEnvId &&
      ev.version === sourceVersion &&
      ((ev.application && ev.application.name === entityName) ||
       (ev.component && ev.component.name === entityName))
    );

    if (!envVersion) {
      this.notificationService.error('Version information not found', 'Please refresh and try again');
      return;
    }

    // Open promotion dialog
    const dialogData: PromotionDialogData = {
      entityName: entityName,
      entityType: envVersion.application ? 'application' : 'component',
      entityId: envVersion.application?.id || envVersion.component?.id || '',
      version: sourceVersion,
      sourceEnvironmentId: sourceEnvId,
      sourceEnvironmentName: this.getEnvironmentName(sourceEnvId),
      targetEnvironmentId: targetEnvId,
      targetEnvironmentName: this.getEnvironmentName(targetEnvId),
      projectId: this.selectedProjectId
    };

    const dialogRef = this.dialog.open(PromotionDialogComponent, {
      width: '600px',
      data: dialogData
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.notificationService.success('Promotion created successfully', 'Check promotions page for status');
        this.refreshData();
      }
    });
  }

  refreshData(): void {
    if (this.selectedProjectId) {
      this.loadProjectData();
    }
  }

  exportMatrix(): void {
    // Create CSV content
    const headers = ['Entity', ...this.environmentIds.map(id => this.getEnvironmentName(id))];
    const rows = this.entityNames.map(entityName => [
      entityName,
      ...this.environmentIds.map(envId => this.getVersion(entityName, envId))
    ]);
    
    const csvContent = [headers, ...rows]
      .map(row => row.join(','))
      .join('\n');
    
    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `version-matrix-${this.selectedProjectId}-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  getDeploymentInfo(entityName: string, environmentId: string): any {
    const version = this.getVersion(entityName, environmentId);
    if (version === '-') return null;
    
    return this.environmentVersions.find(ev => 
      ev.environmentId === environmentId && 
      ev.version === version &&
      ((ev.application && ev.application.name === entityName) ||
       (ev.component && ev.component.name === entityName))
    );
  }

  formatDeploymentDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  getEntityType(entityName: string): string {
    // Check if this entity appears in any environment version as application or component
    const envVersion = this.environmentVersions.find(ev => 
      (ev.application && ev.application.name === entityName) ||
      (ev.component && ev.component.name === entityName)
    );
    
    if (envVersion?.application) return 'Application';
    if (envVersion?.component) return 'Component';
    return 'Unknown';
  }
}
