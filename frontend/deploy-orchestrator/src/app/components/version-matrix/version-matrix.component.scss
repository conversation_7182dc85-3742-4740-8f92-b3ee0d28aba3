// Version Matrix Component Styles
.version-matrix {
  .matrix-table {
    @apply min-w-full divide-y divide-gray-200;
    
    thead {
      @apply bg-gray-50;
      
      th {
        @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
        
        &.sticky-column {
          @apply sticky left-0 bg-gray-50 z-10;
        }
        
        &.environment-header {
          @apply text-center min-w-32;
        }
      }
    }
    
    tbody {
      @apply bg-white divide-y divide-gray-200;
      
      tr {
        &:nth-child(even) {
          @apply bg-gray-50;
        }
        
        td {
          @apply px-6 py-4 whitespace-nowrap;
          
          &.entity-name {
            @apply text-sm font-medium text-gray-900 sticky left-0 bg-inherit z-10;
          }
          
          &.entity-type {
            @apply text-sm text-gray-500;
          }
          
          &.version-cell {
            @apply text-center;
          }
        }
      }
    }
  }

  .version-info {
    @apply flex flex-col items-center space-y-2;
    
    .version-number {
      @apply text-sm font-medium text-gray-900;
    }
    
    .version-status {
      @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
      
      &.status-active {
        @apply bg-green-100 text-green-800;
      }
      
      &.status-deploying {
        @apply bg-blue-100 text-blue-800;
      }
      
      &.status-failed {
        @apply bg-red-100 text-red-800;
      }
      
      &.status-rolled-back {
        @apply bg-yellow-100 text-yellow-800;
      }
      
      &.status-unknown {
        @apply bg-gray-100 text-gray-600;
      }
    }
    
    .deployment-meta {
      @apply text-xs text-gray-500 text-center;
    }
  }

  .not-deployed {
    @apply text-sm text-gray-400 text-center;
  }

  .promotion-actions {
    @apply flex space-x-1 mt-2;
    
    .promote-btn {
      @apply inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors;
      
      &:hover {
        @apply shadow-sm;
      }
      
      svg {
        @apply w-3 h-3;
      }
    }
  }

  .entity-type-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    
    &.type-application {
      @apply bg-blue-100 text-blue-800;
    }
    
    &.type-component {
      @apply bg-purple-100 text-purple-800;
    }
  }

  .empty-state {
    @apply bg-white shadow rounded-lg p-12 text-center;
    
    .empty-icon {
      @apply mx-auto h-12 w-12 text-gray-400;
    }
    
    .empty-title {
      @apply mt-4 text-lg font-medium text-gray-900;
    }
    
    .empty-description {
      @apply mt-2 text-gray-500;
    }
    
    .empty-action {
      @apply mt-6;
      
      .action-btn {
        @apply inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
      }
    }
  }

  .loading-state {
    @apply bg-white shadow rounded-lg p-12 text-center;
    
    .loading-content {
      @apply flex items-center justify-center;
      
      .loading-spinner {
        @apply animate-spin h-8 w-8 text-blue-600;
      }
      
      .loading-text {
        @apply ml-3 text-lg text-gray-600;
      }
    }
  }

  .legend {
    @apply mt-8 bg-white shadow rounded-lg p-6;
    
    .legend-title {
      @apply text-lg font-medium text-gray-900 mb-4;
    }
    
    .legend-grid {
      @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
    }
    
    .legend-item {
      @apply flex items-center space-x-2;
      
      .legend-badge {
        @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
      }
      
      .legend-description {
        @apply text-sm text-gray-600;
      }
    }
  }

  .header-actions {
    @apply flex space-x-3;
    
    .action-btn {
      @apply inline-flex items-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
      
      &.btn-secondary {
        @apply border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500;
      }
      
      &.btn-primary {
        @apply border-transparent text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500;
      }
      
      &:disabled {
        @apply opacity-50 cursor-not-allowed;
      }
      
      svg {
        @apply w-4 h-4 mr-2;
      }
    }
  }

  .project-selector {
    @apply mb-6;
    
    label {
      @apply block text-sm font-medium text-gray-700 mb-2;
    }
    
    select {
      @apply w-full max-w-md px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500;
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .matrix-table {
      .sticky-column {
        @apply relative;
      }
    }
    
    .header-actions {
      @apply flex-col space-y-2 space-x-0;
    }
    
    .legend-grid {
      @apply grid-cols-1;
    }
  }

  // Hover effects
  .matrix-table tbody tr:hover {
    @apply bg-blue-50;
  }

  // Scrollbar styling for horizontal scroll
  .overflow-x-auto {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 #f7fafc;
    
    &::-webkit-scrollbar {
      height: 8px;
    }
    
    &::-webkit-scrollbar-track {
      @apply bg-gray-100;
    }
    
    &::-webkit-scrollbar-thumb {
      @apply bg-gray-300 rounded;
      
      &:hover {
        @apply bg-gray-400;
      }
    }
  }

  // Animation for status changes
  .version-status {
    transition: all 0.3s ease-in-out;
  }

  // Tooltip styles (if using a tooltip library)
  .tooltip {
    @apply absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg;
  }
}

// Print styles
@media print {
  .version-matrix {
    .header-actions {
      @apply hidden;
    }
    
    .matrix-table {
      @apply text-xs;
      
      .sticky-column {
        @apply relative;
      }
    }
  }
}
