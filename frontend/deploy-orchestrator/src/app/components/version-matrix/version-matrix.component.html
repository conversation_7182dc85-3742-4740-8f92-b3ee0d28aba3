<div class="min-h-screen bg-gray-50 py-8">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Version Matrix</h1>
          <p class="mt-2 text-gray-600">Track which versions are deployed across environments</p>
        </div>
        <div class="flex space-x-3">
          <button (click)="refreshData()" 
                  [disabled]="loading"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Refresh
          </button>
          <button (click)="exportMatrix()" 
                  [disabled]="loading || entityNames.length === 0"
                  class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Export CSV
          </button>
        </div>
      </div>
    </div>

    <!-- Project Selection -->
    <div class="mb-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">Project</label>
      <select [(ngModel)]="selectedProjectId" (change)="onProjectChange()" 
              class="w-full max-w-md px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        <option value="">Select a project</option>
        <option *ngFor="let project of projects" [value]="project.id">{{ project.name }}</option>
      </select>
    </div>

    <!-- Version Matrix Table -->
    <div *ngIf="!loading && entityNames.length > 0" class="bg-white shadow rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Deployment Matrix</h2>
        <p class="mt-1 text-sm text-gray-500">Current versions deployed across environments</p>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky left-0 bg-gray-50 z-10">
                Application/Component
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th *ngFor="let envId of environmentIds" 
                  scope="col" 
                  class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider min-w-32">
                {{ getEnvironmentName(envId) }}
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr *ngFor="let entityName of entityNames; let i = index" 
                [class.bg-gray-50]="i % 2 === 1">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 sticky left-0 bg-inherit z-10">
                {{ entityName }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      [class.bg-blue-100]="getEntityType(entityName) === 'Application'"
                      [class.text-blue-800]="getEntityType(entityName) === 'Application'"
                      [class.bg-purple-100]="getEntityType(entityName) === 'Component'"
                      [class.text-purple-800]="getEntityType(entityName) === 'Component'">
                  {{ getEntityType(entityName) }}
                </span>
              </td>
              <td *ngFor="let envId of environmentIds" 
                  class="px-6 py-4 whitespace-nowrap text-center">
                <div class="flex flex-col items-center space-y-2">
                  <!-- Version Display -->
                  <div *ngIf="hasVersion(entityName, envId)" 
                       class="flex flex-col items-center space-y-1">
                    <span class="text-sm font-medium text-gray-900">
                      {{ getVersion(entityName, envId) }}
                    </span>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                          [ngClass]="getStatusColorClass(getVersionStatus(entityName, envId))">
                      {{ getVersionStatus(entityName, envId) }}
                    </span>
                    
                    <!-- Deployment Info -->
                    <div *ngIf="getDeploymentInfo(entityName, envId) as deployInfo" 
                         class="text-xs text-gray-500 text-center">
                      <div>{{ formatDeploymentDate(deployInfo.deployedAt) }}</div>
                      <div>by {{ deployInfo.deployedBy }}</div>
                    </div>
                  </div>
                  
                  <!-- Not Deployed -->
                  <div *ngIf="!hasVersion(entityName, envId)" 
                       class="text-sm text-gray-400">
                    Not deployed
                  </div>
                  
                  <!-- Promotion Actions -->
                  <div class="flex space-x-1 mt-2">
                    <button *ngFor="let sourceEnvId of environmentIds" 
                            *ngIf="sourceEnvId !== envId && canPromote(entityName, sourceEnvId, envId)"
                            (click)="promoteVersion(entityName, sourceEnvId, envId)"
                            class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            [title]="'Promote from ' + getEnvironmentName(sourceEnvId)">
                      <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
                      </svg>
                    </button>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="!loading && entityNames.length === 0 && selectedProjectId" 
         class="bg-white shadow rounded-lg p-12 text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
      </svg>
      <h3 class="mt-4 text-lg font-medium text-gray-900">No deployments found</h3>
      <p class="mt-2 text-gray-500">No applications or components have been deployed to any environment in this project.</p>
      <div class="mt-6">
        <a routerLink="/deployment-management" 
           class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Create Deployment
        </a>
      </div>
    </div>

    <!-- No Project Selected -->
    <div *ngIf="!loading && !selectedProjectId" 
         class="bg-white shadow rounded-lg p-12 text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
      </svg>
      <h3 class="mt-4 text-lg font-medium text-gray-900">Select a project</h3>
      <p class="mt-2 text-gray-500">Choose a project to view its deployment matrix.</p>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="bg-white shadow rounded-lg p-12 text-center">
      <div class="flex items-center justify-center">
        <svg class="animate-spin h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="ml-3 text-lg text-gray-600">Loading version matrix...</span>
      </div>
    </div>

    <!-- Legend -->
    <div *ngIf="!loading && entityNames.length > 0" class="mt-8 bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Legend</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div class="flex items-center space-x-2">
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            active
          </span>
          <span class="text-sm text-gray-600">Successfully deployed and running</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            deploying
          </span>
          <span class="text-sm text-gray-600">Deployment in progress</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
            failed
          </span>
          <span class="text-sm text-gray-600">Deployment failed</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            rolled_back
          </span>
          <span class="text-sm text-gray-600">Deployment was rolled back</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            Application
          </span>
          <span class="text-sm text-gray-600">Application entity</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            Component
          </span>
          <span class="text-sm text-gray-600">Component entity</span>
        </div>
      </div>
    </div>
  </div>
</div>
