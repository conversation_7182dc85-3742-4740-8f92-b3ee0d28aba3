import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { WorkflowTemplate, WorkflowExecutionRequest, WorkflowExecution } from '../../models/workflow-execution.interface';
import { WorkflowService } from '../../services/workflow.service';
import { ProjectService } from '../../services/project.service';
import { NotificationService } from '../../services/notification.service';
import { SecretMappingComponent } from '../secret-mapping/secret-mapping.component';

interface WizardStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  valid: boolean;
}

@Component({
  selector: 'app-template-execution-wizard',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, SecretMappingComponent],
  templateUrl: './template-execution-wizard.component.html',
  styleUrls: ['./template-execution-wizard.component.css']
})
export class TemplateExecutionWizardComponent implements OnInit {
  @Input() template!: WorkflowTemplate;
  @Input() projectId!: string;
  @Output() executionStarted = new EventEmitter<WorkflowExecution>();
  @Output() wizardClosed = new EventEmitter<void>();

  currentStepIndex = 0;
  isExecuting = false;
  executionResult: WorkflowExecution | null = null;

  // Form groups for each step
  projectForm!: FormGroup;
  parametersForm!: FormGroup;
  secretMapping: { [key: string]: string } = {};
  secretMappingValid = false;

  // Available projects
  availableProjects: any[] = [];

  steps: WizardStep[] = [
    {
      id: 'project',
      title: 'Select Project',
      description: 'Choose the project where this workflow will execute',
      completed: false,
      valid: false
    },
    {
      id: 'parameters',
      title: 'Configure Parameters',
      description: 'Set values for workflow parameters',
      completed: false,
      valid: false
    },
    {
      id: 'secrets',
      title: 'Map Secrets',
      description: 'Map template variables to your project secrets',
      completed: false,
      valid: false
    },
    {
      id: 'review',
      title: 'Review & Execute',
      description: 'Review configuration and start execution',
      completed: false,
      valid: false
    }
  ];

  constructor(
    private fb: FormBuilder,
    private workflowService: WorkflowService,
    private projectService: ProjectService,
    private notificationService: NotificationService,
    private router: Router
  ) {}

  ngOnInit() {
    this.initializeForms();
    this.loadProjects();
    this.updateStepValidation();
  }

  private initializeForms() {
    // Project selection form
    this.projectForm = this.fb.group({
      projectId: [this.projectId || '', Validators.required]
    });

    // Parameters form
    const parameterControls: { [key: string]: any } = {};
    this.template.parameters
      .filter(param => param.type !== 'secret') // Exclude secret parameters
      .forEach(param => {
        const validators = param.required ? [Validators.required] : [];
        parameterControls[param.name] = [param.defaultValue || '', validators];
      });

    this.parametersForm = this.fb.group(parameterControls);

    // Listen for form changes
    this.projectForm.valueChanges.subscribe(() => this.updateStepValidation());
    this.parametersForm.valueChanges.subscribe(() => this.updateStepValidation());
  }

  private async loadProjects() {
    try {
      this.availableProjects = await this.projectService.getUserProjects();
      
      // If projectId is provided, validate it exists
      if (this.projectId) {
        const projectExists = this.availableProjects.some(p => p.id === this.projectId);
        if (!projectExists) {
          this.notificationService.showWarning('Selected project not found');
          this.projectForm.get('projectId')?.setValue('');
        }
      }
    } catch (error) {
      console.error('Failed to load projects:', error);
      this.notificationService.showError('Failed to load projects');
    }
  }

  private updateStepValidation() {
    // Project step
    this.steps[0].valid = this.projectForm.valid;
    this.steps[0].completed = this.steps[0].valid;

    // Parameters step
    this.steps[1].valid = this.parametersForm.valid;
    this.steps[1].completed = this.steps[1].valid;

    // Secrets step
    this.steps[2].valid = this.secretMappingValid;
    this.steps[2].completed = this.steps[2].valid;

    // Review step
    this.steps[3].valid = this.steps[0].valid && this.steps[1].valid && this.steps[2].valid;
    this.steps[3].completed = false; // Never auto-completed
  }

  // Navigation methods
  nextStep() {
    if (this.currentStepIndex < this.steps.length - 1 && this.canProceedToNext()) {
      this.currentStepIndex++;
    }
  }

  previousStep() {
    if (this.currentStepIndex > 0) {
      this.currentStepIndex--;
    }
  }

  goToStep(index: number) {
    if (index >= 0 && index < this.steps.length) {
      this.currentStepIndex = index;
    }
  }

  canProceedToNext(): boolean {
    return this.steps[this.currentStepIndex].valid;
  }

  canProceedToPrevious(): boolean {
    return this.currentStepIndex > 0;
  }

  // Event handlers
  onProjectChange() {
    this.projectId = this.projectForm.get('projectId')?.value;
    this.updateStepValidation();
  }

  onSecretMappingChange(mapping: { [key: string]: string }) {
    this.secretMapping = mapping;
    this.updateStepValidation();
  }

  onSecretMappingValidationChange(isValid: boolean) {
    this.secretMappingValid = isValid;
    this.updateStepValidation();
  }

  // Execution methods
  async executeWorkflow() {
    if (!this.canExecute()) {
      this.notificationService.showError('Please complete all required steps');
      return;
    }

    this.isExecuting = true;

    try {
      const executionRequest: WorkflowExecutionRequest = {
        projectId: this.projectForm.get('projectId')?.value,
        parameters: this.parametersForm.value,
        secretMapping: this.secretMapping,
        startedBy: 'current-user', // This should come from auth service
        triggerType: 'manual'
      };

      this.executionResult = await this.workflowService.executeTemplate(
        this.template.id,
        executionRequest
      );

      this.notificationService.showSuccess(
        `Workflow execution started: ${this.executionResult.id}`
      );

      this.executionStarted.emit(this.executionResult);

      // Navigate to execution monitoring
      this.router.navigate(['/workflows/executions', this.executionResult.id]);

    } catch (error) {
      console.error('Failed to execute workflow:', error);
      this.notificationService.showError('Failed to start workflow execution');
    } finally {
      this.isExecuting = false;
    }
  }

  canExecute(): boolean {
    return this.steps.every(step => step.valid) && !this.isExecuting;
  }

  // Utility methods
  getCurrentStep(): WizardStep {
    return this.steps[this.currentStepIndex];
  }

  getStepProgress(): number {
    const completedSteps = this.steps.filter(step => step.completed).length;
    return Math.round((completedSteps / this.steps.length) * 100);
  }

  getParameterDisplayValue(paramName: string): string {
    const value = this.parametersForm.get(paramName)?.value;
    if (value === null || value === undefined || value === '') {
      return 'Not set';
    }
    return String(value);
  }

  getSecretMappingDisplayValue(templateVar: string): string {
    return this.secretMapping[templateVar] || 'Not mapped';
  }

  closeWizard() {
    this.wizardClosed.emit();
  }

  // Template helper methods
  hasRequiredParameters(): boolean {
    return this.template.parameters.some(param => 
      param.required && param.type !== 'secret'
    );
  }

  hasSecretParameters(): boolean {
    return this.template.parameters.some(param => 
      param.type === 'secret' || param.secretHint
    );
  }

  getRequiredSecretParameters() {
    return this.template.parameters.filter(param => 
      (param.type === 'secret' || param.secretHint) && param.required
    );
  }

  getOptionalSecretParameters() {
    return this.template.parameters.filter(param => 
      (param.type === 'secret' || param.secretHint) && !param.required
    );
  }
}
