<div class="template-execution-wizard">
  <!-- Wizard Header -->
  <div class="wizard-header bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-xl font-semibold text-gray-900">Execute Template</h2>
        <p class="text-sm text-gray-600 mt-1">{{ template.name }}</p>
      </div>
      <button
        type="button"
        (click)="closeWizard()"
        class="text-gray-400 hover:text-gray-600 transition-colors"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Progress Bar -->
    <div class="mt-4">
      <div class="flex items-center justify-between text-xs text-gray-600 mb-2">
        <span>Progress</span>
        <span>{{ getStepProgress() }}%</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div 
          class="bg-blue-600 h-2 rounded-full transition-all duration-300"
          [style.width.%]="getStepProgress()"
        ></div>
      </div>
    </div>

    <!-- Step Navigation -->
    <nav class="mt-6">
      <ol class="flex items-center justify-between">
        <li *ngFor="let step of steps; let i = index" 
            class="flex items-center"
            [class.flex-1]="i < steps.length - 1">
          
          <button
            type="button"
            (click)="goToStep(i)"
            class="flex items-center text-sm font-medium transition-colors"
            [class]="i === currentStepIndex 
              ? 'text-blue-600' 
              : step.completed 
                ? 'text-green-600 hover:text-green-700' 
                : 'text-gray-500 hover:text-gray-700'"
          >
            <!-- Step Icon -->
            <span class="flex items-center justify-center w-8 h-8 rounded-full border-2 mr-3 transition-colors"
                  [class]="i === currentStepIndex 
                    ? 'border-blue-600 bg-blue-600 text-white' 
                    : step.completed 
                      ? 'border-green-600 bg-green-600 text-white' 
                      : 'border-gray-300 bg-white text-gray-500'">
              <svg *ngIf="step.completed && i !== currentStepIndex" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span *ngIf="!step.completed || i === currentStepIndex">{{ i + 1 }}</span>
            </span>
            
            <!-- Step Title -->
            <span class="hidden sm:block">{{ step.title }}</span>
          </button>

          <!-- Connector Line -->
          <div *ngIf="i < steps.length - 1" 
               class="flex-1 h-0.5 mx-4 transition-colors"
               [class]="step.completed ? 'bg-green-600' : 'bg-gray-300'">
          </div>
        </li>
      </ol>
    </nav>
  </div>

  <!-- Wizard Content -->
  <div class="wizard-content flex-1 overflow-y-auto">
    
    <!-- Step 1: Project Selection -->
    <div *ngIf="currentStepIndex === 0" class="step-content p-6">
      <div class="max-w-2xl mx-auto">
        <h3 class="text-lg font-medium text-gray-900 mb-2">Select Project</h3>
        <p class="text-sm text-gray-600 mb-6">
          Choose the project where this workflow will execute. You can only select projects you have access to.
        </p>

        <form [formGroup]="projectForm" class="space-y-4">
          <div>
            <label for="projectId" class="block text-sm font-medium text-gray-700 mb-2">
              Project <span class="text-red-500">*</span>
            </label>
            <select
              id="projectId"
              formControlName="projectId"
              (change)="onProjectChange()"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a project...</option>
              <option *ngFor="let project of availableProjects" [value]="project.id">
                {{ project.name }}
                <span *ngIf="project.description"> - {{ project.description }}</span>
              </option>
            </select>
          </div>

          <div *ngIf="projectForm.get('projectId')?.value" class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h4 class="text-sm font-medium text-blue-900 mb-2">Selected Project Details</h4>
            <div class="text-sm text-blue-700">
              <p><strong>Name:</strong> {{ availableProjects.find(p => p.id === projectForm.get('projectId')?.value)?.name }}</p>
              <p *ngIf="availableProjects.find(p => p.id === projectForm.get('projectId')?.value)?.description">
                <strong>Description:</strong> {{ availableProjects.find(p => p.id === projectForm.get('projectId')?.value)?.description }}
              </p>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Step 2: Parameters Configuration -->
    <div *ngIf="currentStepIndex === 1" class="step-content p-6">
      <div class="max-w-2xl mx-auto">
        <h3 class="text-lg font-medium text-gray-900 mb-2">Configure Parameters</h3>
        <p class="text-sm text-gray-600 mb-6">
          Set values for the workflow parameters. Required parameters must be provided.
        </p>

        <div *ngIf="!hasRequiredParameters()" class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No Parameters Required</h3>
          <p class="mt-1 text-sm text-gray-500">This template doesn't require any parameter configuration.</p>
        </div>

        <form [formGroup]="parametersForm" *ngIf="hasRequiredParameters()" class="space-y-6">
          <div *ngFor="let param of template.parameters" class="parameter-field">
            <div *ngIf="param.type !== 'secret'" class="space-y-2">
              <label [for]="param.name" class="block text-sm font-medium text-gray-700">
                {{ param.name }}
                <span *ngIf="param.required" class="text-red-500 ml-1">*</span>
              </label>
              
              <p *ngIf="param.description" class="text-xs text-gray-600">
                {{ param.description }}
              </p>

              <!-- String/Text Input -->
              <input
                *ngIf="param.type === 'string'"
                [id]="param.name"
                [formControlName]="param.name"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                [placeholder]="param.defaultValue || 'Enter ' + param.name"
              />

              <!-- Number Input -->
              <input
                *ngIf="param.type === 'number'"
                [id]="param.name"
                [formControlName]="param.name"
                type="number"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                [placeholder]="param.defaultValue || 'Enter ' + param.name"
              />

              <!-- Boolean Checkbox -->
              <div *ngIf="param.type === 'boolean'" class="flex items-center">
                <input
                  [id]="param.name"
                  [formControlName]="param.name"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label [for]="param.name" class="ml-2 text-sm text-gray-700">
                  Enable {{ param.name }}
                </label>
              </div>

              <!-- Select Dropdown for Options -->
              <select
                *ngIf="param.options && param.options.length > 0"
                [id]="param.name"
                [formControlName]="param.name"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select {{ param.name }}...</option>
                <option *ngFor="let option of param.options" [value]="option">
                  {{ option }}
                </option>
              </select>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Step 3: Secret Mapping -->
    <div *ngIf="currentStepIndex === 2" class="step-content p-6">
      <div class="max-w-4xl mx-auto">
        <h3 class="text-lg font-medium text-gray-900 mb-2">Map Secrets</h3>
        <p class="text-sm text-gray-600 mb-6">
          Map template secret variables to your project's secrets. This allows you to use your existing secrets with this template.
        </p>

        <app-secret-mapping
          [template]="template"
          [projectId]="projectForm.get('projectId')?.value"
          [initialMapping]="secretMapping"
          (mappingChange)="onSecretMappingChange($event)"
          (validationChange)="onSecretMappingValidationChange($event)"
        ></app-secret-mapping>
      </div>
    </div>

    <!-- Step 4: Review & Execute -->
    <div *ngIf="currentStepIndex === 3" class="step-content p-6">
      <div class="max-w-2xl mx-auto">
        <h3 class="text-lg font-medium text-gray-900 mb-2">Review & Execute</h3>
        <p class="text-sm text-gray-600 mb-6">
          Review your configuration and start the workflow execution.
        </p>

        <!-- Configuration Summary -->
        <div class="space-y-6">
          
          <!-- Template Info -->
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Template</h4>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600">Name:</span>
                <span class="text-gray-900">{{ template.name }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Version:</span>
                <span class="text-gray-900">{{ template.version }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Author:</span>
                <span class="text-gray-900">{{ template.authorName }}</span>
              </div>
            </div>
          </div>

          <!-- Project Info -->
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Project</h4>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600">Project:</span>
                <span class="text-gray-900">
                  {{ availableProjects.find(p => p.id === projectForm.get('projectId')?.value)?.name }}
                </span>
              </div>
            </div>
          </div>

          <!-- Parameters -->
          <div *ngIf="hasRequiredParameters()" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Parameters</h4>
            <div class="space-y-2 text-sm">
              <div *ngFor="let param of template.parameters" class="flex justify-between">
                <div *ngIf="param.type !== 'secret'">
                  <span class="text-gray-600">{{ param.name }}:</span>
                  <span class="text-gray-900 ml-2">{{ getParameterDisplayValue(param.name) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Secret Mappings -->
          <div *ngIf="hasSecretParameters()" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Secret Mappings</h4>
            <div class="space-y-2 text-sm">
              <div *ngFor="let param of template.parameters" class="flex justify-between">
                <div *ngIf="param.type === 'secret' || param.secretHint">
                  <span class="text-gray-600">{{ param.name }}:</span>
                  <span class="text-gray-900 ml-2">{{ getSecretMappingDisplayValue(param.name) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Execution Button -->
        <div class="mt-8 pt-6 border-t border-gray-200">
          <button
            type="button"
            (click)="executeWorkflow()"
            [disabled]="!canExecute()"
            class="w-full flex items-center justify-center px-6 py-3 text-base font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <svg *ngIf="isExecuting" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isExecuting ? 'Starting Execution...' : 'Execute Workflow' }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Wizard Footer -->
  <div class="wizard-footer bg-gray-50 border-t border-gray-200 px-6 py-4">
    <div class="flex items-center justify-between">
      <button
        type="button"
        (click)="previousStep()"
        [disabled]="!canProceedToPrevious()"
        class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        Previous
      </button>

      <div class="text-sm text-gray-600">
        Step {{ currentStepIndex + 1 }} of {{ steps.length }}
      </div>

      <button
        *ngIf="currentStepIndex < steps.length - 1"
        type="button"
        (click)="nextStep()"
        [disabled]="!canProceedToNext()"
        class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        Next
        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>

      <div *ngIf="currentStepIndex === steps.length - 1" class="w-20"></div>
    </div>
  </div>
</div>
