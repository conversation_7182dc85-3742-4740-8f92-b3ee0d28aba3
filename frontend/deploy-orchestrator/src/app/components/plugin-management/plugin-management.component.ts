import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil, combineLatest } from 'rxjs/operators';
import { PluginService } from '../../services/plugin.service';
import { PluginPermissionService } from '../../services/plugin-permission.service';
import {
  Plugin,
  PluginInstallRequest,
  PluginStatus,
  HotReloadEvent,
  PluginMarketplace,
  MarketplacePlugin
} from '../../models/plugin.model';
import {
  PluginPermissionMatrix,
  PluginFeatureAccess,
  PLUGIN_PERMISSIONS,
  PLUGIN_FEATURES
} from '../../models/plugin-permission.model';

@Component({
  selector: 'app-plugin-management',
  templateUrl: './plugin-management.component.html',
  styleUrls: ['./plugin-management.component.scss']
})
export class PluginManagementComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Data
  plugins: Plugin[] = [];
  filteredPlugins: Plugin[] = [];
  selectedPlugin: Plugin | null = null;
  pluginStatus: { [key: string]: PluginStatus } = {};
  hotReloadEvents: HotReloadEvent[] = [];
  marketplace: PluginMarketplace | null = null;

  // Permissions
  permissionMatrix: PluginPermissionMatrix | null = null;
  accessiblePlugins: string[] = [];
  isPluginAdmin = false;

  // UI State
  activeTab: 'installed' | 'marketplace' | 'hot-reload' = 'installed';
  loading = false;
  error: string | null = null;
  searchQuery = '';
  filterType = '';
  filterStatus = '';
  showInstallModal = false;
  showConfigModal = false;
  showLogsModal = false;

  // Install form
  installForm = {
    name: '',
    source: '',
    config: {},
    enabled: true
  };

  // Config form
  configForm: { [key: string]: any } = {};

  // Logs
  pluginLogs: any[] = [];
  logsLoading = false;

  constructor(
    private pluginService: PluginService,
    private pluginPermissionService: PluginPermissionService
  ) {}

  ngOnInit(): void {
    this.loadPermissions();
    this.loadPlugins();
    this.loadMarketplace();
    this.subscribeToHotReloadEvents();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Permission Loading
  loadPermissions(): void {
    // Load permission matrix
    this.pluginPermissionService.permissionMatrix$
      .pipe(takeUntil(this.destroy$))
      .subscribe(matrix => {
        this.permissionMatrix = matrix;
        this.applyPermissionFilters();
      });

    // Load accessible plugins
    this.pluginPermissionService.getAccessiblePlugins()
      .pipe(takeUntil(this.destroy$))
      .subscribe(plugins => {
        this.accessiblePlugins = plugins;
        this.applyPermissionFilters();
      });

    // Check if user is plugin admin
    this.pluginPermissionService.isPluginAdmin()
      .pipe(takeUntil(this.destroy$))
      .subscribe(isAdmin => {
        this.isPluginAdmin = isAdmin;
      });
  }

  // Data Loading
  loadPlugins(): void {
    this.loading = true;
    this.error = null;

    this.pluginService.getPlugins()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (plugins) => {
          this.plugins = plugins;
          this.applyFilters();
          this.loadPluginStatuses();
          this.loading = false;
        },
        error: (error) => {
          this.error = 'Failed to load plugins';
          this.loading = false;
          console.error('Error loading plugins:', error);
        }
      });
  }

  loadPluginStatuses(): void {
    this.plugins.forEach(plugin => {
      this.pluginService.getPluginStatus(plugin.name)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (status) => {
            this.pluginStatus[plugin.name] = status;
          },
          error: (error) => {
            console.error(`Error loading status for plugin ${plugin.name}:`, error);
          }
        });
    });
  }

  loadMarketplace(): void {
    this.pluginService.getMarketplace()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (marketplace) => {
          this.marketplace = marketplace;
        },
        error: (error) => {
          console.error('Error loading marketplace:', error);
        }
      });
  }

  subscribeToHotReloadEvents(): void {
    this.pluginService.subscribeToHotReloadEvents();
    this.pluginService.hotReloadEvents$
      .pipe(takeUntil(this.destroy$))
      .subscribe(events => {
        this.hotReloadEvents = events;
      });
  }

  // Permission Filtering
  applyPermissionFilters(): void {
    if (!this.plugins.length) return;

    // Filter plugins based on permissions
    const permissionFilteredPlugins = this.plugins.filter(plugin => {
      // Admin can see all plugins
      if (this.isPluginAdmin) return true;

      // Check if user has access to this plugin
      return this.accessiblePlugins.includes(plugin.name);
    });

    this.plugins = permissionFilteredPlugins;
    this.applyFilters();
  }

  // Filtering and Search
  applyFilters(): void {
    this.filteredPlugins = this.plugins.filter(plugin => {
      const matchesSearch = !this.searchQuery ||
        plugin.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        plugin.description.toLowerCase().includes(this.searchQuery.toLowerCase());

      const matchesType = !this.filterType || plugin.type === this.filterType;
      const matchesStatus = !this.filterStatus || plugin.status === this.filterStatus;

      return matchesSearch && matchesType && matchesStatus;
    });
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  onFilterChange(): void {
    this.applyFilters();
  }

  // Permission Checking Methods
  canInstallPlugins(): boolean {
    return this.isPluginAdmin || this.hasPermission(PLUGIN_PERMISSIONS.PLUGIN_INSTALL);
  }

  canManagePlugin(plugin: Plugin): boolean {
    return this.isPluginAdmin || this.hasPluginPermission(plugin.name, PLUGIN_PERMISSIONS.PLUGIN_MANAGE);
  }

  canConfigurePlugin(plugin: Plugin): boolean {
    return this.isPluginAdmin || this.hasPluginPermission(plugin.name, PLUGIN_PERMISSIONS.PLUGIN_CONFIGURE);
  }

  canViewPluginLogs(plugin: Plugin): boolean {
    return this.isPluginAdmin || this.hasPluginPermission(plugin.name, PLUGIN_PERMISSIONS.PLUGIN_LOGS);
  }

  canReloadPlugin(plugin: Plugin): boolean {
    return this.isPluginAdmin || this.hasPluginPermission(plugin.name, PLUGIN_PERMISSIONS.PLUGIN_RELOAD);
  }

  canUninstallPlugin(plugin: Plugin): boolean {
    return this.isPluginAdmin || this.hasPluginPermission(plugin.name, PLUGIN_PERMISSIONS.PLUGIN_UNINSTALL);
  }

  hasPermission(permission: string): boolean {
    if (!this.permissionMatrix) return false;
    // Check global permissions or admin status
    return this.isPluginAdmin;
  }

  hasPluginPermission(pluginName: string, permission: string): boolean {
    if (!this.permissionMatrix || !this.permissionMatrix.plugins[pluginName]) return false;

    const pluginAccess = this.permissionMatrix.plugins[pluginName];

    switch (permission) {
      case PLUGIN_PERMISSIONS.PLUGIN_VIEW:
        return pluginAccess.canView;
      case PLUGIN_PERMISSIONS.PLUGIN_INSTALL:
        return pluginAccess.canInstall;
      case PLUGIN_PERMISSIONS.PLUGIN_CONFIGURE:
        return pluginAccess.canConfigure;
      case PLUGIN_PERMISSIONS.PLUGIN_MANAGE:
        return pluginAccess.canManage;
      case PLUGIN_PERMISSIONS.PLUGIN_RELOAD:
        return pluginAccess.canManage; // Reload requires manage permission
      case PLUGIN_PERMISSIONS.PLUGIN_UNINSTALL:
        return pluginAccess.canManage; // Uninstall requires manage permission
      case PLUGIN_PERMISSIONS.PLUGIN_LOGS:
        return pluginAccess.canView; // Logs require view permission
      default:
        return false;
    }
  }

  // Plugin Actions
  selectPlugin(plugin: Plugin): void {
    if (!this.hasPluginPermission(plugin.name, PLUGIN_PERMISSIONS.PLUGIN_VIEW)) {
      this.error = `You don't have permission to view plugin: ${plugin.name}`;
      return;
    }
    this.selectedPlugin = plugin;
  }

  enablePlugin(plugin: Plugin): void {
    if (!this.canManagePlugin(plugin)) {
      this.error = `You don't have permission to manage plugin: ${plugin.name}`;
      return;
    }

    this.pluginService.enablePlugin(plugin.name)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedPlugin) => {
          this.updatePluginInList(updatedPlugin);
          this.logPluginAction('enable', plugin.name);
        },
        error: (error) => {
          console.error('Error enabling plugin:', error);
          this.error = `Failed to enable plugin: ${error.message}`;
        }
      });
  }

  disablePlugin(plugin: Plugin): void {
    if (!this.canManagePlugin(plugin)) {
      this.error = `You don't have permission to manage plugin: ${plugin.name}`;
      return;
    }

    this.pluginService.disablePlugin(plugin.name)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedPlugin) => {
          this.updatePluginInList(updatedPlugin);
          this.logPluginAction('disable', plugin.name);
        },
        error: (error) => {
          console.error('Error disabling plugin:', error);
          this.error = `Failed to disable plugin: ${error.message}`;
        }
      });
  }

  reloadPlugin(plugin: Plugin): void {
    if (!this.canReloadPlugin(plugin)) {
      this.error = `You don't have permission to reload plugin: ${plugin.name}`;
      return;
    }

    this.pluginService.reloadPlugin(plugin.name)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedPlugin) => {
          this.updatePluginInList(updatedPlugin);
          this.logPluginAction('reload', plugin.name);
        },
        error: (error) => {
          console.error('Error reloading plugin:', error);
          this.error = `Failed to reload plugin: ${error.message}`;
        }
      });
  }

  uninstallPlugin(plugin: Plugin): void {
    if (!this.canUninstallPlugin(plugin)) {
      this.error = `You don't have permission to uninstall plugin: ${plugin.name}`;
      return;
    }

    if (confirm(`Are you sure you want to uninstall plugin "${plugin.name}"?`)) {
      this.pluginService.uninstallPlugin(plugin.name)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.plugins = this.plugins.filter(p => p.name !== plugin.name);
            this.applyFilters();
            if (this.selectedPlugin?.name === plugin.name) {
              this.selectedPlugin = null;
            }
            this.logPluginAction('uninstall', plugin.name);
          },
          error: (error) => {
            console.error('Error uninstalling plugin:', error);
            this.error = `Failed to uninstall plugin: ${error.message}`;
          }
        });
    }
  }

  // Install Plugin
  openInstallModal(): void {
    if (!this.canInstallPlugins()) {
      this.error = "You don't have permission to install plugins";
      return;
    }

    this.showInstallModal = true;
    this.installForm = {
      name: '',
      source: '',
      config: {},
      enabled: true
    };
  }

  closeInstallModal(): void {
    this.showInstallModal = false;
  }

  installPlugin(): void {
    if (!this.canInstallPlugins()) {
      this.error = "You don't have permission to install plugins";
      return;
    }

    const request: PluginInstallRequest = {
      name: this.installForm.name,
      source: this.installForm.source,
      config: this.installForm.config,
      enabled: this.installForm.enabled
    };

    this.pluginService.installPlugin(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (plugin) => {
          this.plugins.push(plugin);
          this.applyFilters();
          this.closeInstallModal();
          this.logPluginAction('install', plugin.name, { source: request.source });
        },
        error: (error) => {
          console.error('Error installing plugin:', error);
          this.error = `Failed to install plugin: ${error.message}`;
        }
      });
  }

  installFromMarketplace(marketplacePlugin: MarketplacePlugin): void {
    this.pluginService.installFromMarketplace(marketplacePlugin.name)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (plugin) => {
          this.plugins.push(plugin);
          this.applyFilters();
          marketplacePlugin.installed = true;
        },
        error: (error) => {
          console.error('Error installing from marketplace:', error);
        }
      });
  }

  // Configure Plugin
  openConfigModal(plugin: Plugin): void {
    if (!this.canConfigurePlugin(plugin)) {
      this.error = `You don't have permission to configure plugin: ${plugin.name}`;
      return;
    }

    this.selectedPlugin = plugin;
    this.configForm = { ...plugin.config };
    this.showConfigModal = true;
  }

  closeConfigModal(): void {
    this.showConfigModal = false;
    this.selectedPlugin = null;
    this.error = null;
  }

  getConfigFormJson(): string {
    try {
      return JSON.stringify(this.configForm, null, 2);
    } catch (error) {
      return '{}';
    }
  }

  updateConfigFromJson(event: any): void {
    const jsonString = event.target.value;
    try {
      this.configForm = JSON.parse(jsonString);
      this.error = null;
    } catch (error) {
      this.error = 'Invalid JSON format';
    }
  }

  updatePluginConfig(): void {
    if (!this.selectedPlugin) return;

    this.pluginService.updatePluginConfig(this.selectedPlugin.name, { config: this.configForm })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedPlugin) => {
          this.updatePluginInList(updatedPlugin);
          this.logPluginAction('configure', this.selectedPlugin!.name, { config: this.configForm });
          this.closeConfigModal();
        },
        error: (error) => {
          console.error('Error updating plugin config:', error);
          this.error = `Failed to update plugin configuration: ${error.message}`;
        }
      });
  }

  // View Logs
  openLogsModal(plugin: Plugin): void {
    if (!this.canViewPluginLogs(plugin)) {
      this.error = `You don't have permission to view logs for plugin: ${plugin.name}`;
      return;
    }

    this.selectedPlugin = plugin;
    this.showLogsModal = true;
    this.loadPluginLogs(plugin.name);
  }

  closeLogsModal(): void {
    this.showLogsModal = false;
    this.selectedPlugin = null;
    this.pluginLogs = [];
  }

  loadPluginLogs(pluginName: string): void {
    this.logsLoading = true;
    this.pluginService.getPluginLogs(pluginName, 100)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.pluginLogs = response.logs;
          this.logsLoading = false;
        },
        error: (error) => {
          console.error('Error loading plugin logs:', error);
          this.logsLoading = false;
        }
      });
  }

  // Utility Methods
  updatePluginInList(updatedPlugin: Plugin): void {
    const index = this.plugins.findIndex(p => p.name === updatedPlugin.name);
    if (index !== -1) {
      this.plugins[index] = updatedPlugin;
      this.applyFilters();
    }
  }

  getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'running':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'loading':
        return 'bg-yellow-100 text-yellow-800';
      case 'disabled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getHealthBadgeClass(health: string): string {
    switch (health) {
      case 'healthy':
        return 'bg-green-100 text-green-800';
      case 'unhealthy':
        return 'bg-red-100 text-red-800';
      case 'unknown':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  getUniqueTypes(): string[] {
    return [...new Set(this.plugins.map(p => p.type))];
  }

  getUniqueStatuses(): string[] {
    return [...new Set(this.plugins.map(p => p.status))];
  }

  // Audit Logging
  private logPluginAction(action: string, pluginName: string, details?: any): void {
    this.pluginPermissionService.logPluginAction(action, pluginName, {
      ...details,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    }).subscribe({
      error: (error) => console.error('Failed to log plugin action:', error)
    });
  }

  // Permission-based UI helpers
  showInstallButton(): boolean {
    return this.canInstallPlugins();
  }

  showPluginActions(plugin: Plugin): boolean {
    return this.canManagePlugin(plugin) || this.canConfigurePlugin(plugin) || this.canViewPluginLogs(plugin);
  }

  getVisiblePluginActions(plugin: Plugin): string[] {
    const actions: string[] = [];

    if (this.canManagePlugin(plugin)) {
      if (plugin.enabled) {
        actions.push('disable');
      } else {
        actions.push('enable');
      }
      actions.push('reload', 'uninstall');
    }

    if (this.canConfigurePlugin(plugin)) {
      actions.push('configure');
    }

    if (this.canViewPluginLogs(plugin)) {
      actions.push('logs');
    }

    return actions;
  }

  // Error handling
  clearError(): void {
    this.error = null;
  }

  // Permission status helpers
  getPermissionStatus(plugin: Plugin): string {
    if (this.isPluginAdmin) return 'admin';
    if (!this.permissionMatrix || !this.permissionMatrix.plugins[plugin.name]) return 'no-access';

    const access = this.permissionMatrix.plugins[plugin.name];
    if (access.canManage) return 'manage';
    if (access.canConfigure) return 'configure';
    if (access.canView) return 'view';

    return 'no-access';
  }

  getPermissionBadgeClass(plugin: Plugin): string {
    const status = this.getPermissionStatus(plugin);
    switch (status) {
      case 'admin':
        return 'bg-purple-100 text-purple-800';
      case 'manage':
        return 'bg-green-100 text-green-800';
      case 'configure':
        return 'bg-blue-100 text-blue-800';
      case 'view':
        return 'bg-gray-100 text-gray-800';
      case 'no-access':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }
}
