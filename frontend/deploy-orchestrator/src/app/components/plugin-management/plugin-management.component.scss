.plugin-card {
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }
}

.status-badge {
  &.running {
    background-color: rgb(220, 252, 231);
    color: rgb(22, 101, 52);
  }
  
  &.error {
    background-color: rgb(254, 226, 226);
    color: rgb(153, 27, 27);
  }
  
  &.loading {
    background-color: rgb(254, 249, 195);
    color: rgb(146, 64, 14);
  }
  
  &.disabled {
    background-color: rgb(243, 244, 246);
    color: rgb(31, 41, 55);
  }
}

.health-badge {
  &.healthy {
    background-color: rgb(220, 252, 231);
    color: rgb(22, 101, 52);
  }
  
  &.unhealthy {
    background-color: rgb(254, 226, 226);
    color: rgb(153, 27, 27);
  }
  
  &.unknown {
    background-color: rgb(243, 244, 246);
    color: rgb(31, 41, 55);
  }
}

.plugin-actions {
  button {
    transition: all 0.2s ease-in-out;
    
    &:hover {
      transform: translateY(-1px);
    }
  }
}

.hot-reload-event {
  &.reload-completed {
    border-left: 4px solid #10b981;
  }
  
  &.reload-failed {
    border-left: 4px solid #ef4444;
  }
  
  &.reload-started {
    border-left: 4px solid #3b82f6;
  }
  
  &.file-changed {
    border-left: 4px solid #f59e0b;
  }
}

.logs-container {
  background: #1a1a1a;
  color: #00ff00;
  font-family: 'Courier New', monospace;
  
  .log-entry {
    &.error {
      color: #ff6b6b;
    }
    
    &.warn {
      color: #ffd93d;
    }
    
    &.info {
      color: #6bcf7f;
    }
    
    &.debug {
      color: #74c0fc;
    }
  }
}

.marketplace-card {
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  }
  
  .rating {
    display: flex;
    align-items: center;
    
    .star {
      color: #fbbf24;
    }
  }
  
  .tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    
    .tag {
      background-color: rgb(243, 244, 246);
      color: rgb(31, 41, 55);
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      font-size: 0.75rem;
    }
  }
}

.modal-overlay {
  backdrop-filter: blur(4px);
}

.config-editor {
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  
  &:focus {
    background: #ffffff;
  }
}

.search-filters {
  .filter-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    
    label {
      font-weight: 500;
      color: #374151;
    }
    
    select, input {
      transition: all 0.2s ease-in-out;
      
      &:focus {
        transform: scale(1.02);
      }
    }
  }
}

.empty-state {
  .icon {
    font-size: 4rem;
    color: #d1d5db;
    margin-bottom: 1rem;
  }
  
  h3 {
    font-size: 1.125rem;
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #6b7280;
    margin-bottom: 1rem;
  }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.plugin-metrics {
  .metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e5e7eb;
    
    &:last-child {
      border-bottom: none;
    }
    
    .label {
      font-weight: 500;
      color: #374151;
    }
    
    .value {
      color: #111827;
      font-weight: 600;
    }
  }
}

.hot-reload-indicator {
  position: relative;
  
  &.active::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

.plugin-capabilities {
  .capability {
    background-color: rgb(239, 246, 255);
    color: rgb(30, 64, 175);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
    display: inline-block;
  }
}

.plugin-dependencies {
  .dependency {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    
    .name {
      font-weight: 500;
    }
    
    .version {
      color: #6b7280;
      font-size: 0.875rem;
    }
    
    .status {
      &.installed {
        color: #10b981;
      }
      
      &.missing {
        color: #ef4444;
      }
    }
  }
}

.plugin-updates {
  .update-available {
    background-color: rgb(254, 252, 232);
    border: 1px solid rgb(254, 240, 138);
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 1rem;
    
    .update-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .version-info {
        .current {
          color: #6b7280;
        }
        
        .latest {
          color: #059669;
          font-weight: 600;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .plugin-card {
    .plugin-actions {
      flex-direction: column;
      gap: 0.5rem;
      
      button {
        width: 100%;
      }
    }
  }
  
  .search-filters {
    flex-direction: column;
    gap: 1rem;
    
    .filter-group {
      width: 100%;
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .plugin-card {
    background: #1f2937;
    border-color: #374151;
    
    h3 {
      color: #f9fafb;
    }
    
    p {
      color: #d1d5db;
    }
  }
  
  .logs-container {
    background: #111827;
    border-color: #374151;
  }
}
