import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PluginConfigFormComponent, PluginConfigSchema } from '../plugin-config-form/plugin-config-form.component';
import { PluginConfigService } from '../../services/plugin-config.service';
import { PluginService } from '../../services/plugin.service';
import { Plugin } from '../../models/plugin.model';

export interface PluginConfigData {
  plugin: Plugin;
  currentConfig?: any;
}

@Component({
  selector: 'app-plugin-config-modal',
  standalone: true,
  imports: [
    CommonModule,
    PluginConfigFormComponent
  ],
  template: `
    <div *ngIf="isOpen" class="fixed inset-0 z-50 flex items-center justify-center overflow-x-hidden overflow-y-auto outline-none focus:outline-none" (click)="onBackdropClick($event)">
      <div class="fixed inset-0 bg-black bg-opacity-50"></div>
      
      <div class="relative w-full max-w-4xl mx-auto my-6">
        <div class="relative flex flex-col w-full bg-white border-0 rounded-lg shadow-lg outline-none focus:outline-none">
          <!-- Header -->
          <div class="flex items-start justify-between p-6 border-b border-solid border-gray-200 rounded-t">
            <div class="flex items-center space-x-4">
              <div class="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg">
                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 011-1h1a2 2 0 100-4H7a1 1 0 01-1-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"></path>
                </svg>
              </div>
              <div>
                <h2 class="text-xl font-semibold text-gray-900">Configure {{ data?.plugin?.name }}</h2>
                <p class="text-sm text-gray-500 mt-1">Version {{ data?.plugin?.version }}</p>
              </div>
            </div>
            <button
              class="p-1 ml-auto bg-transparent border-0 text-gray-400 hover:text-gray-600 text-3xl leading-none font-semibold outline-none focus:outline-none"
              type="button"
              (click)="close()"
            >
              <span class="bg-transparent text-gray-400 hover:text-gray-600 h-6 w-6 text-2xl block outline-none focus:outline-none">
                ×
              </span>
            </button>
          </div>
          
          <!-- Body -->
          <div class="relative p-6 flex-auto max-h-96 overflow-y-auto">
            <!-- Loading state -->
            <div *ngIf="loading" class="flex flex-col items-center justify-center py-12">
              <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-purple-600 mb-4"></div>
              <p class="text-gray-600">Loading plugin configuration schema...</p>
            </div>

            <!-- Error state -->
            <div *ngIf="error" class="flex items-start space-x-3 p-4 bg-red-50 rounded-lg border border-red-200">
              <div class="flex-shrink-0">
                <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div class="flex-1">
                <h3 class="text-sm font-medium text-red-800">Configuration Error</h3>
                <p class="text-sm text-red-700 mt-1">{{ error }}</p>
                <button
                  type="button"
                  (click)="loadSchema()"
                  class="mt-3 inline-flex items-center px-3 py-1.5 text-sm font-medium text-red-700 bg-red-100 border border-red-300 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  Retry
                </button>
              </div>
            </div>

            <!-- Form -->
            <div *ngIf="!loading && !error && schema" class="space-y-6">
              <app-plugin-config-form
                [schema]="schema"
                [initialConfig]="data?.currentConfig"
                [loading]="saving"
                [showJsonPreview]="showJsonPreview"
                (configChange)="onConfigChange($event)"
                (submit)="onSave($event)"
                (cancel)="onCancel()"
              ></app-plugin-config-form>
            </div>
          </div>
          
          <!-- Footer -->
          <div *ngIf="!loading && !error" class="flex items-center justify-between p-6 border-t border-solid border-gray-200 rounded-b">
            <!-- Left side actions -->
            <button
              type="button"
              (click)="toggleJsonPreview()"
              class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
            >
              <svg *ngIf="!showJsonPreview" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
              </svg>
              <svg *ngIf="showJsonPreview" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
              </svg>
              {{ showJsonPreview ? 'Hide JSON' : 'Show JSON' }}
            </button>

            <!-- Right side actions -->
            <div class="flex space-x-3">
              <button
                type="button"
                (click)="onCancel()"
                [disabled]="saving"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
              <button
                type="button"
                (click)="onSave(currentConfig)"
                [disabled]="!isConfigValid || saving"
                class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md shadow-sm hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                <svg *ngIf="saving" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <svg *ngIf="!saving" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                {{ saving ? 'Saving...' : 'Save Configuration' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .plugin-config-modal {
      width: 100%;
      max-width: 800px;
      min-height: 400px;
    }
  `]
})
export class PluginConfigModalComponent implements OnInit, OnChanges {
  @Input() isOpen = false;
  @Input() data: PluginConfigData | null = null;
  @Output() modalClose = new EventEmitter<void>();
  @Output() configSaved = new EventEmitter<any>();

  // Component state
  loading = false;
  saving = false;
  error: string | null = null;
  schema: PluginConfigSchema | null = null;
  currentConfig: any = null;
  isConfigValid = false;
  showJsonPreview = false;

  constructor(
    private pluginConfigService: PluginConfigService,
    private pluginService: PluginService
  ) {}

  ngOnInit(): void {
    if (this.isOpen && this.data?.plugin) {
      this.loadSchema();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isOpen'] && this.isOpen && this.data?.plugin) {
      this.loadSchema();
    }
  }

  async loadSchema(): Promise<void> {
    if (!this.data?.plugin) return;

    this.loading = true;
    this.error = null;

    try {
      // Convert plugin to metadata format that the service expects
      const pluginMetadata = {
        name: this.data.plugin.name,
        version: this.data.plugin.version,
        description: this.data.plugin.description
      };

      // Use the correct method from PluginConfigService
      const schemaResult = await this.pluginConfigService.convertPluginToFormSchema(pluginMetadata).toPromise();
      this.schema = schemaResult || null;
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to load plugin configuration schema';
      console.error('Error loading plugin schema:', error);
    } finally {
      this.loading = false;
    }
  }

  onConfigChange(config: any): void {
    this.currentConfig = config;
    this.isConfigValid = this.validateConfig(config);
  }

  private validateConfig(config: any): boolean {
    // Basic validation - could be enhanced based on schema
    return config && Object.keys(config).length > 0;
  }

  async onSave(config: any): Promise<void> {
    if (!this.data?.plugin || !this.isConfigValid) return;

    this.saving = true;
    this.error = null;

    try {
      // Use PluginService to update plugin configuration
      const updateRequest = { config: config };
      const updatedPlugin = await this.pluginService.updatePluginConfig(this.data.plugin.name, updateRequest).toPromise();
      this.configSaved.emit(updatedPlugin);
      this.close();
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to save plugin configuration';
      console.error('Error saving plugin config:', error);
    } finally {
      this.saving = false;
    }
  }

  onCancel(): void {
    this.close();
  }

  toggleJsonPreview(): void {
    this.showJsonPreview = !this.showJsonPreview;
  }

  close(): void {
    this.modalClose.emit();
  }

  onBackdropClick(event: Event): void {
    if (event.target === event.currentTarget) {
      this.close();
    }
  }
}
