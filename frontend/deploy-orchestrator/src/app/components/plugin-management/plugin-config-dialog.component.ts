import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PluginConfigFormComponent, PluginConfigSchema } from '../plugin-config-form/plugin-config-form.component';
import { PluginConfigService } from '../../services/plugin-config.service';
import { PluginService } from '../../services/plugin.service';
import { Plugin } from '../../models/plugin.model';

export interface PluginConfigDialogData {
  plugin: Plugin;
  currentConfig?: any;
  isOpen?: boolean;
}

@Component({
  selector: 'app-plugin-config-dialog',
  standalone: true,
  imports: [
    CommonModule,
    PluginConfigFormComponent
  ],
  template: `
    <div class="plugin-config-dialog">
      <div mat-dialog-title class="dialog-header">
        <div class="header-content">
          <mat-icon class="plugin-icon">extension</mat-icon>
          <div class="header-text">
            <h2>Configure {{ data.plugin.name }}</h2>
            <p class="plugin-version">Version {{ data.plugin.version }}</p>
          </div>
        </div>
        <button mat-icon-button mat-dialog-close class="close-button">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <div mat-dialog-content class="dialog-content">
        <div *ngIf="loading" class="loading-container">
          <mat-spinner diameter="40"></mat-spinner>
          <p>Loading plugin configuration schema...</p>
        </div>

        <div *ngIf="error" class="error-container">
          <mat-icon class="error-icon">error</mat-icon>
          <div class="error-content">
            <h3>Configuration Error</h3>
            <p>{{ error }}</p>
            <button mat-button color="primary" (click)="loadSchema()">
              <mat-icon>refresh</mat-icon>
              Retry
            </button>
          </div>
        </div>

        <div *ngIf="!loading && !error && schema" class="form-container">
          <app-plugin-config-form
            [schema]="schema"
            [initialConfig]="data.currentConfig"
            [loading]="saving"
            [showJsonPreview]="showJsonPreview"
            (configChange)="onConfigChange($event)"
            (submit)="onSave($event)"
            (cancel)="onCancel()"
          ></app-plugin-config-form>
        </div>
      </div>

      <div mat-dialog-actions class="dialog-actions" *ngIf="!loading && !error">
        <div class="actions-left">
          <button
            mat-button
            type="button"
            (click)="toggleJsonPreview()"
            class="preview-button"
          >
            <mat-icon>{{ showJsonPreview ? 'visibility_off' : 'code' }}</mat-icon>
            {{ showJsonPreview ? 'Hide JSON' : 'Show JSON' }}
          </button>
        </div>

        <div class="actions-right">
          <button
            mat-button
            type="button"
            (click)="onCancel()"
            [disabled]="saving"
          >
            Cancel
          </button>
          <button
            mat-raised-button
            color="primary"
            type="button"
            (click)="onSave(currentConfig)"
            [disabled]="!isConfigValid || saving"
          >
            <mat-icon *ngIf="saving">hourglass_empty</mat-icon>
            <mat-icon *ngIf="!saving">save</mat-icon>
            {{ saving ? 'Saving...' : 'Save Configuration' }}
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .plugin-config-dialog {
      width: 100%;
      max-width: 800px;
      min-height: 400px;
    }

    .dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0;
      margin: 0;
    }

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .plugin-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
      color: #1976d2;
    }

    .header-text h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }

    .plugin-version {
      margin: 0;
      font-size: 14px;
      color: #666;
    }

    .close-button {
      margin-left: auto;
    }

    .dialog-content {
      padding: 24px 0;
      min-height: 300px;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 48px;
      gap: 16px;
    }

    .loading-container p {
      margin: 0;
      color: #666;
    }

    .error-container {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      padding: 24px;
      background: #ffebee;
      border-radius: 8px;
      border-left: 4px solid #f44336;
    }

    .error-icon {
      color: #f44336;
      margin-top: 4px;
    }

    .error-content h3 {
      margin: 0 0 8px 0;
      color: #d32f2f;
    }

    .error-content p {
      margin: 0 0 16px 0;
      color: #666;
    }

    .form-container {
      padding: 0;
    }

    .dialog-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0 0 0;
      margin: 0;
      border-top: 1px solid #e0e0e0;
    }

    .actions-left,
    .actions-right {
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .preview-button {
      color: #666;
    }

    .preview-button:hover {
      background: #f5f5f5;
    }

    /* Responsive */
    @media (max-width: 600px) {
      .plugin-config-dialog {
        max-width: 100%;
        margin: 0;
      }

      .dialog-actions {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
      }

      .actions-left,
      .actions-right {
        justify-content: center;
      }
    }
  `]
})
export class PluginConfigDialogComponent implements OnInit {
  schema: PluginConfigSchema | null = null;
  loading = true;
  saving = false;
  error: string | null = null;
  currentConfig: any = {};
  isConfigValid = false;
  showJsonPreview = false;

  constructor(
    public dialogRef: MatDialogRef<PluginConfigDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: PluginConfigDialogData,
    private pluginConfigService: PluginConfigService,
    private pluginService: PluginService
  ) {}

  ngOnInit(): void {
    this.loadSchema();
  }

  async loadSchema(): Promise<void> {
    this.loading = true;
    this.error = null;

    try {
      // Try to get schema from plugin metadata
      if (this.data.plugin.name === 'helm-openshift-deploy') {
        // Use predefined schema for Helm OpenShift plugin with secrets support
        this.pluginConfigService.createHelmOpenShiftSchema().subscribe({
          next: (schema) => {
            this.schema = schema;
            this.currentConfig = this.data.currentConfig || {};
            this.validateConfig();
            this.loading = false;
          },
          error: (error) => {
            console.warn('Failed to load schema with secrets, using basic schema:', error);
            this.schema = this.pluginConfigService.createHelmOpenShiftSchemaSync();
            this.currentConfig = this.data.currentConfig || {};
            this.validateConfig();
            this.loading = false;
          }
        });
        return; // Exit early since we're handling async
      } else {
        // Try to get schema from plugin service
        try {
          const pluginDetails = await this.pluginService.getPlugin(this.data.plugin.name).toPromise();
          if (pluginDetails?.config) {
            // Try to create schema from existing config structure
            const schema = await this.pluginConfigService.convertPluginToFormSchema(pluginDetails).toPromise();
            this.schema = schema || this.createGenericSchema();
          } else {
            throw new Error('No configuration schema available');
          }
        } catch (error) {
          // Fallback to generic schema
          this.schema = this.createGenericSchema();
        }
      }

      // Set initial config
      this.currentConfig = this.data.currentConfig || {};
      this.validateConfig();

    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to load configuration schema';
      console.error('Error loading plugin schema:', error);
    } finally {
      this.loading = false;
    }
  }

  private createGenericSchema(): PluginConfigSchema {
    return {
      title: `Configure ${this.data.plugin.name}`,
      description: `Configuration for ${this.data.plugin.name} plugin`,
      fields: [
        {
          name: 'config',
          type: 'textarea',
          label: 'Configuration JSON',
          description: 'Plugin configuration in JSON format',
          required: false,
          placeholder: '{\n  "key": "value"\n}'
        }
      ]
    };
  }

  onConfigChange(config: any): void {
    this.currentConfig = config;
    this.validateConfig();
  }

  private validateConfig(): void {
    if (!this.schema) {
      this.isConfigValid = false;
      return;
    }

    const validation = this.pluginConfigService.validateConfiguration(this.currentConfig, this.schema);
    this.isConfigValid = validation.valid;
  }

  async onSave(config: any): Promise<void> {
    if (!this.isConfigValid) {
      return;
    }

    this.saving = true;
    try {
      // Save configuration via plugin service
      await this.pluginService.updatePluginConfig(this.data.plugin.name, config).toPromise();

      // Close dialog with success result
      this.dialogRef.close({
        success: true,
        config: config
      });
    } catch (error) {
      this.error = error instanceof Error ? error.message : 'Failed to save configuration';
      console.error('Error saving plugin configuration:', error);
    } finally {
      this.saving = false;
    }
  }

  onCancel(): void {
    this.dialogRef.close({
      success: false
    });
  }

  toggleJsonPreview(): void {
    this.showJsonPreview = !this.showJsonPreview;
  }
}
