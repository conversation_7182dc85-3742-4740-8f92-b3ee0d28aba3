<div class="min-h-screen bg-gray-50 py-6">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Plugin Management</h1>
      <p class="mt-2 text-gray-600">Manage plugins, providers, and hot reload functionality</p>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
      <nav class="flex space-x-8" aria-label="Tabs">
        <button
          (click)="activeTab = 'installed'"
          [class]="activeTab === 'installed' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
          Installed Plugins
          <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">{{ plugins.length }}</span>
        </button>
        <button
          (click)="activeTab = 'marketplace'"
          [class]="activeTab === 'marketplace' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
          Marketplace
          <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">{{ marketplace?.total || 0 }}</span>
        </button>
        <button
          (click)="activeTab = 'hot-reload'"
          [class]="activeTab === 'hot-reload' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
          Hot Reload Events
          <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">{{ hotReloadEvents.length }}</span>
        </button>
      </nav>
    </div>

    <!-- Installed Plugins Tab -->
    <div *ngIf="activeTab === 'installed'">
      <!-- Controls -->
      <div class="mb-6 flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <input
            type="text"
            [(ngModel)]="searchQuery"
            (input)="onSearchChange()"
            placeholder="Search plugins..."
            class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
        </div>
        <div class="flex gap-2">
          <select
            [(ngModel)]="filterType"
            (change)="onFilterChange()"
            class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option value="">All Types</option>
            <option *ngFor="let type of getUniqueTypes()" [value]="type">{{ type | titlecase }}</option>
          </select>
          <select
            [(ngModel)]="filterStatus"
            (change)="onFilterChange()"
            class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option value="">All Statuses</option>
            <option *ngFor="let status of getUniqueStatuses()" [value]="status">{{ status | titlecase }}</option>
          </select>
          <button
            *ngIf="showInstallButton()"
            (click)="openInstallModal()"
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <i class="fas fa-plus mr-2"></i>Install Plugin
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-gray-600">Loading plugins...</p>
      </div>

      <!-- Error State -->
      <div *ngIf="error" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
        <div class="flex">
          <i class="fas fa-exclamation-circle text-red-400 mr-3 mt-0.5"></i>
          <div class="flex-1">
            <h3 class="text-sm font-medium text-red-800">Error</h3>
            <p class="mt-1 text-sm text-red-700">{{ error }}</p>
          </div>
          <button (click)="clearError()" class="text-red-400 hover:text-red-600">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <!-- Plugins Grid -->
      <div *ngIf="!loading && !error" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div *ngFor="let plugin of filteredPlugins" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <!-- Plugin Header -->
          <div class="flex items-start justify-between mb-4">
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900">{{ plugin.name }}</h3>
              <p class="text-sm text-gray-600">v{{ plugin.version }}</p>
            </div>
            <div class="flex flex-col gap-2">
              <span [class]="getStatusBadgeClass(plugin.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                {{ plugin.status | titlecase }}
              </span>
              <span *ngIf="plugin.health" [class]="getHealthBadgeClass(plugin.health)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                {{ plugin.health | titlecase }}
              </span>
              <span [class]="getPermissionBadgeClass(plugin)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                {{ getPermissionStatus(plugin) | titlecase }}
              </span>
            </div>
          </div>

          <!-- Plugin Info -->
          <p class="text-sm text-gray-600 mb-4">{{ plugin.description }}</p>

          <div class="space-y-2 text-xs text-gray-500 mb-4">
            <div class="flex justify-between">
              <span>Type:</span>
              <span class="font-medium">{{ plugin.type | titlecase }}</span>
            </div>
            <div class="flex justify-between">
              <span>Hot Reload:</span>
              <span class="font-medium">
                <i [class]="plugin.hotReload ? 'fas fa-check text-green-500' : 'fas fa-times text-red-500'"></i>
                {{ plugin.hotReload ? 'Enabled' : 'Disabled' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span>Reloads:</span>
              <span class="font-medium">{{ plugin.reloadCount }}</span>
            </div>
            <div class="flex justify-between">
              <span>Last Reload:</span>
              <span class="font-medium">{{ formatDate(plugin.lastReload) }}</span>
            </div>
          </div>

          <!-- Plugin Actions -->
          <div *ngIf="showPluginActions(plugin)" class="flex flex-wrap gap-2">
            <button
              *ngIf="!plugin.enabled && canManagePlugin(plugin)"
              (click)="enablePlugin(plugin)"
              class="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700">
              <i class="fas fa-play mr-1"></i>Enable
            </button>
            <button
              *ngIf="plugin.enabled && canManagePlugin(plugin)"
              (click)="disablePlugin(plugin)"
              class="bg-yellow-600 text-white px-3 py-1 rounded text-xs hover:bg-yellow-700">
              <i class="fas fa-pause mr-1"></i>Disable
            </button>
            <button
              *ngIf="canReloadPlugin(plugin)"
              (click)="reloadPlugin(plugin)"
              class="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700">
              <i class="fas fa-sync mr-1"></i>Reload
            </button>
            <button
              *ngIf="canConfigurePlugin(plugin)"
              (click)="openConfigModal(plugin)"
              class="bg-gray-600 text-white px-3 py-1 rounded text-xs hover:bg-gray-700">
              <i class="fas fa-cog mr-1"></i>Config
            </button>
            <button
              *ngIf="canViewPluginLogs(plugin)"
              (click)="openLogsModal(plugin)"
              class="bg-purple-600 text-white px-3 py-1 rounded text-xs hover:bg-purple-700">
              <i class="fas fa-file-alt mr-1"></i>Logs
            </button>
            <button
              *ngIf="canUninstallPlugin(plugin)"
              (click)="uninstallPlugin(plugin)"
              class="bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-700">
              <i class="fas fa-trash mr-1"></i>Uninstall
            </button>
          </div>

          <!-- No Actions Available -->
          <div *ngIf="!showPluginActions(plugin)" class="text-center py-2">
            <span class="text-sm text-gray-500">No actions available</span>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="!loading && !error && filteredPlugins.length === 0" class="text-center py-12">
        <i class="fas fa-puzzle-piece text-gray-400 text-4xl mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No plugins found</h3>
        <p *ngIf="isPluginAdmin" class="text-gray-600 mb-4">Get started by installing your first plugin</p>
        <p *ngIf="!isPluginAdmin && accessiblePlugins.length === 0" class="text-gray-600 mb-4">
          You don't have access to any plugins. Contact your administrator to get plugin permissions.
        </p>
        <p *ngIf="!isPluginAdmin && accessiblePlugins.length > 0" class="text-gray-600 mb-4">
          No plugins match your current filters. Try adjusting your search criteria.
        </p>
        <button
          *ngIf="showInstallButton()"
          (click)="openInstallModal()"
          class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
          Install Plugin
        </button>
      </div>
    </div>

    <!-- Marketplace Tab -->
    <div *ngIf="activeTab === 'marketplace'">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div *ngFor="let plugin of marketplace?.plugins" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-start justify-between mb-4">
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900">{{ plugin.name }}</h3>
              <p class="text-sm text-gray-600">v{{ plugin.version }} by {{ plugin.author }}</p>
            </div>
            <div class="flex items-center">
              <i class="fas fa-star text-yellow-400 mr-1"></i>
              <span class="text-sm text-gray-600">{{ plugin.rating }}</span>
            </div>
          </div>

          <p class="text-sm text-gray-600 mb-4">{{ plugin.description }}</p>

          <div class="flex flex-wrap gap-1 mb-4">
            <span *ngFor="let tag of plugin.tags" class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">
              {{ tag }}
            </span>
          </div>

          <div class="flex justify-between items-center">
            <div class="text-xs text-gray-500">
              <div>{{ plugin.downloads }} downloads</div>
              <div>Updated {{ formatDate(plugin.lastUpdated) }}</div>
            </div>
            <button
              *ngIf="!plugin.installed"
              (click)="installFromMarketplace(plugin)"
              class="bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700">
              Install
            </button>
            <span *ngIf="plugin.installed" class="bg-green-100 text-green-800 px-3 py-1 rounded text-sm">
              Installed
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Hot Reload Events Tab -->
    <div *ngIf="activeTab === 'hot-reload'">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Hot Reload Events</h3>
          <p class="text-sm text-gray-600">Real-time plugin reload events and file changes</p>
        </div>
        <div class="divide-y divide-gray-200">
          <div *ngFor="let event of hotReloadEvents" class="px-6 py-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <i [class]="event.event === 'reload-completed' ? 'fas fa-check-circle text-green-500' :
                           event.event === 'reload-failed' ? 'fas fa-times-circle text-red-500' :
                           event.event === 'reload-started' ? 'fas fa-sync text-blue-500' :
                           'fas fa-file text-gray-500'" class="mr-3"></i>
                <div>
                  <div class="font-medium text-gray-900">{{ event.pluginName }}</div>
                  <div class="text-sm text-gray-600">{{ event.event | titlecase }}</div>
                </div>
              </div>
              <div class="text-right">
                <div class="text-sm text-gray-900">{{ formatDate(event.timestamp) }}</div>
                <div *ngIf="event.details" class="text-xs text-gray-500">{{ event.details }}</div>
              </div>
            </div>
            <div *ngIf="event.error" class="mt-2 text-sm text-red-600 bg-red-50 p-2 rounded">
              {{ event.error }}
            </div>
          </div>
        </div>
        <div *ngIf="hotReloadEvents.length === 0" class="px-6 py-8 text-center text-gray-500">
          No hot reload events yet
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Install Plugin Modal -->
<div *ngIf="showInstallModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Install Plugin</h3>
      <form (ngSubmit)="installPlugin()">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Plugin Name</label>
          <input
            type="text"
            [(ngModel)]="installForm.name"
            name="name"
            required
            class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
        </div>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Source</label>
          <input
            type="text"
            [(ngModel)]="installForm.source"
            name="source"
            required
            placeholder="./plugins/my-plugin or git://github.com/user/plugin.git"
            class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
        </div>
        <div class="mb-4">
          <label class="flex items-center">
            <input
              type="checkbox"
              [(ngModel)]="installForm.enabled"
              name="enabled"
              class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <span class="ml-2 text-sm text-gray-700">Enable after installation</span>
          </label>
        </div>
        <div class="flex justify-end gap-2">
          <button
            type="button"
            (click)="closeInstallModal()"
            class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
            Cancel
          </button>
          <button
            type="submit"
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            Install
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Config Modal -->
<div *ngIf="showConfigModal && selectedPlugin" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-2/3 max-w-2xl shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Configure {{ selectedPlugin.name }}</h3>
        <button
          type="button"
          (click)="closeConfigModal()"
          class="text-gray-400 hover:text-gray-600">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <form (ngSubmit)="updatePluginConfig()">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Configuration (JSON)</label>
          <textarea
            [value]="getConfigFormJson()"
            (input)="updateConfigFromJson($event)"
            name="config"
            rows="12"
            class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 font-mono text-sm"
            placeholder='{"key": "value"}'></textarea>
          <p class="text-xs text-gray-500 mt-1">Enter valid JSON configuration for the plugin</p>
        </div>
        <div *ngIf="error" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p class="text-sm text-red-600">{{ error }}</p>
        </div>
        <div class="flex justify-end gap-2">
          <button
            type="button"
            (click)="closeConfigModal()"
            class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
            Cancel
          </button>
          <button
            type="submit"
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            Update Configuration
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Logs Modal -->
<div *ngIf="showLogsModal && selectedPlugin" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-10 mx-auto p-5 border w-4/5 max-w-4xl shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Logs for {{ selectedPlugin.name }}</h3>
      <div class="bg-black text-green-400 p-4 rounded-md h-96 overflow-y-auto font-mono text-sm">
        <div *ngIf="logsLoading" class="text-center text-gray-400">Loading logs...</div>
        <div *ngFor="let log of pluginLogs" class="mb-1">
          <span class="text-gray-500">[{{ formatDate(log.timestamp) }}]</span>
          <span [class]="log.level === 'error' ? 'text-red-400' :
                        log.level === 'warn' ? 'text-yellow-400' :
                        log.level === 'debug' ? 'text-blue-400' : 'text-green-400'">
            [{{ log.level.toUpperCase() }}]
          </span>
          {{ log.message }}
        </div>
        <div *ngIf="!logsLoading && pluginLogs.length === 0" class="text-center text-gray-400">
          No logs available
        </div>
      </div>
      <div class="flex justify-end mt-4">
        <button
          (click)="closeLogsModal()"
          class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
          Close
        </button>
      </div>
    </div>
  </div>
</div>
