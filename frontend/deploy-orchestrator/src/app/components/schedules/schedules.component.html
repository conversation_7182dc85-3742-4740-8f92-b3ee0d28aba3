<div class="px-4 py-5 sm:px-6">
  <div class="flex items-center justify-between">
    <div>
      <h3 class="text-lg leading-6 font-medium text-gray-900">Schedules</h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500">Manage scheduled deployments and automated tasks</p>
    </div>
    <button (click)="openCreateModal()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
      <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
      </svg>
      New Schedule
    </button>
  </div>
</div>

<!-- Project info display -->
<div *ngIf="selectedProject" class="mx-4 mb-6">
  <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
    <div class="flex items-center">
      <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h4a1 1 0 011 1v5m-6 0V9a1 1 0 011-1h4a1 1 0 011 1v2"></path>
      </svg>
      <span class="text-sm font-medium text-blue-900">
        Showing schedules for: <strong>{{ selectedProject.name }}</strong>
      </span>
    </div>
  </div>
</div>

<!-- No project selected message -->
<div *ngIf="!selectedProject" class="mx-4 mb-6">
  <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
    <div class="flex items-center">
      <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
      </svg>
      <span class="text-sm font-medium text-yellow-900">
        Please select a project from the header to view schedules
      </span>
    </div>
  </div>
</div>

<div *ngIf="error" class="bg-red-50 border-l-4 border-red-400 p-4 mx-4 my-2">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
      </svg>
    </div>
    <div class="ml-3">
      <p class="text-sm text-red-700">{{ error }}</p>
    </div>
  </div>
</div>

<div *ngIf="success" class="bg-green-50 border-l-4 border-green-400 p-4 mx-4 my-2">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
      </svg>
    </div>
    <div class="ml-3">
      <p class="text-sm text-green-700">{{ success }}</p>
    </div>
  </div>
</div>

<div class="border-t border-gray-200">
  <div *ngIf="loading" class="flex justify-center py-6">
    <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
  </div>

  <div *ngIf="!loading && schedules.length === 0" class="text-center py-10">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900">No schedules</h3>
    <p class="mt-1 text-sm text-gray-500">Get started by creating a new schedule.</p>
    <div class="mt-6">
      <button (click)="openCreateModal()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        New Schedule
      </button>
    </div>
  </div>

  <div *ngIf="!loading && schedules.length > 0" class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deployment</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cron Expression</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Next Run</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr *ngFor="let schedule of schedules">
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-gray-900">{{ schedule.name }}</div>
            <div *ngIf="schedule.description" class="text-sm text-gray-500">{{ schedule.description }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-500">{{ getProjectName(schedule.projectId) }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-500">{{ getWorkflowName(schedule.workflowId || schedule.deploymentId) }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-500 font-mono">{{ schedule.cronExpression }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span [ngClass]="getStatusColor(schedule.enabled)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
              {{ getStatusText(schedule.enabled) }}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            {{ schedule.nextRun ? (schedule.nextRun | date:'short') : 'N/A' }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
            <button (click)="toggleSchedule(schedule)"
                    [class]="schedule.enabled ? 'text-yellow-600 hover:text-yellow-900' : 'text-green-600 hover:text-green-900'"
                    class="mr-3">
              {{ schedule.enabled ? 'Disable' : 'Enable' }}
            </button>
            <button (click)="triggerSchedule(schedule)"
                    class="text-blue-600 hover:text-blue-900 mr-3">
              Trigger
            </button>
            <button *ngIf="isAdmin"
                    (click)="deleteSchedule(schedule)"
                    class="text-red-600 hover:text-red-900">
              Delete
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<!-- Create Schedule Modal -->
<div *ngIf="showCreateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Create New Schedule</h3>
        <button (click)="closeCreateModal()" class="text-gray-400 hover:text-gray-600">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <form [formGroup]="scheduleForm" (ngSubmit)="createSchedule()">
        <div class="grid grid-cols-1 gap-6">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700">Schedule Name</label>
            <input type="text" id="name" formControlName="name" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            <div *ngIf="scheduleForm.get('name')?.invalid && (scheduleForm.get('name')?.dirty || scheduleForm.get('name')?.touched)" class="text-red-500 text-xs mt-1">
              <div *ngIf="scheduleForm.get('name')?.errors?.['required']">Name is required.</div>
            </div>
          </div>

          <div>
            <label for="description" class="block text-sm font-medium text-gray-700">Description (Optional)</label>
            <textarea id="description" formControlName="description" rows="2" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
          </div>

          <div>
            <label for="projectId" class="block text-sm font-medium text-gray-700">Project</label>
            <select id="projectId" formControlName="projectId" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              <option value="">Select Project</option>
              <option *ngFor="let project of projects" [value]="project.id">{{ project.name }}</option>
            </select>
            <div *ngIf="scheduleForm.get('projectId')?.invalid && (scheduleForm.get('projectId')?.dirty || scheduleForm.get('projectId')?.touched)" class="text-red-500 text-xs mt-1">
              <div *ngIf="scheduleForm.get('projectId')?.errors?.['required']">Project is required.</div>
            </div>
          </div>

          <div>
            <label for="workflowId" class="block text-sm font-medium text-gray-700">Workflow (Optional)</label>
            <select id="workflowId" formControlName="workflowId" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              <option value="">Manual Trigger</option>
              <option *ngFor="let execution of getFilteredExecutions()" [value]="execution.workflowId">{{ execution.workflow?.name || 'Unknown Workflow' }}</option>
            </select>
          </div>

          <div>
            <label for="cronExpression" class="block text-sm font-medium text-gray-700">Cron Expression</label>
            <input type="text" id="cronExpression" formControlName="cronExpression" placeholder="* * * * *" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm font-mono">
            <div *ngIf="scheduleForm.get('cronExpression')?.invalid && (scheduleForm.get('cronExpression')?.dirty || scheduleForm.get('cronExpression')?.touched)" class="text-red-500 text-xs mt-1">
              <div *ngIf="scheduleForm.get('cronExpression')?.errors?.['required']">Cron expression is required.</div>
            </div>

            <!-- Common Cron Expressions -->
            <div class="mt-2">
              <label class="block text-xs font-medium text-gray-600 mb-1">Common Expressions:</label>
              <div class="grid grid-cols-2 gap-2">
                <button *ngFor="let expr of commonCronExpressions"
                        type="button"
                        (click)="onCronExpressionChange(expr.value)"
                        class="text-left text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded border">
                  <div class="font-medium">{{ expr.label }}</div>
                  <div class="text-gray-500 font-mono">{{ expr.value }}</div>
                </button>
              </div>
            </div>
          </div>

          <div class="flex items-center">
            <input id="enabled" type="checkbox" formControlName="enabled" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="enabled" class="ml-2 block text-sm text-gray-900">
              Enable schedule immediately
            </label>
          </div>
        </div>

        <div class="mt-6 flex justify-end space-x-3">
          <button type="button" (click)="closeCreateModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
            Cancel
          </button>
          <button type="submit" [disabled]="scheduleForm.invalid || creating" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
            <span *ngIf="creating" class="mr-2">
              <svg class="animate-spin h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            Create Schedule
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
