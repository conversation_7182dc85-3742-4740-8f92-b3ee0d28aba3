import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ScheduleService, Schedule } from '../../services/schedule.service';
import { ProjectService } from '../../services/project.service';
import { WorkflowExecutionService, WorkflowExecution } from '../../services/workflow-execution.service';
import { AuthService } from '../../services/auth.service';
import { Project } from '../../models/project.model';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-schedules',
  templateUrl: './schedules.component.html',
  styleUrls: ['./schedules.component.scss']
})
export class SchedulesComponent implements OnInit, OnDestroy {
  schedules: Schedule[] = [];
  projects: Project[] = [];
  executions: WorkflowExecution[] = [];
  scheduleForm!: FormGroup;
  loading = false;
  creating = false;
  error = '';
  success = '';
  showCreateModal = false;
  isAdmin = false;

  // Project management
  selectedProject: Project | null = null;
  private destroy$ = new Subject<void>();

  // Common cron expressions
  commonCronExpressions = [
    { label: 'Every minute', value: '* * * * *' },
    { label: 'Every 5 minutes', value: '*/5 * * * *' },
    { label: 'Every hour', value: '0 * * * *' },
    { label: 'Every day at midnight', value: '0 0 * * *' },
    { label: 'Every day at 6 AM', value: '0 6 * * *' },
    { label: 'Every Monday at 9 AM', value: '0 9 * * 1' },
    { label: 'Every weekday at 9 AM', value: '0 9 * * 1-5' },
    { label: 'Every month on the 1st at midnight', value: '0 0 1 * *' }
  ];

  constructor(
    private scheduleService: ScheduleService,
    private projectService: ProjectService,
    private workflowExecutionService: WorkflowExecutionService,
    private authService: AuthService,
    private formBuilder: FormBuilder
  ) { }

  ngOnInit(): void {
    this.isAdmin = this.authService.isAdmin();
    this.initForm();
    this.subscribeToProjectChanges();
    this.loadSchedules();
    this.loadProjects();
    this.loadExecutions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private subscribeToProjectChanges(): void {
    this.projectService.selectedProject$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (selectedProject) => {
          this.selectedProject = selectedProject;
        },
        error: (error) => {
          console.error('Error subscribing to project changes:', error);
        }
      });
  }

  initForm(): void {
    this.scheduleForm = this.formBuilder.group({
      name: ['', Validators.required],
      description: [''],
      projectId: ['', Validators.required],
      workflowId: [''],
      cronExpression: ['', Validators.required],
      enabled: [true]
    });
  }

  loadSchedules(): void {
    this.loading = true;
    this.scheduleService.getSchedules().subscribe({
      next: (schedules) => {
        this.schedules = schedules.map(schedule => ({
          ...schedule,
          projectName: this.getProjectName(schedule.projectId),
          workflowName: this.getWorkflowName(schedule.workflowId || schedule.deploymentId)
        }));
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load schedules. Please try again.';
        console.error('Error loading schedules', error);
        this.loading = false;
      }
    });
  }

  loadProjects(): void {
    this.projectService.getProjects().subscribe({
      next: (projects) => {
        this.projects = projects;
      },
      error: (error) => {
        console.error('Error loading projects', error);
      }
    });
  }

  loadExecutions(): void {
    this.workflowExecutionService.getExecutions().subscribe({
      next: (response) => {
        this.executions = response.executions || [];
      },
      error: (error) => {
        console.error('Error loading executions', error);
      }
    });
  }

  getProjectName(projectId: string): string {
    const project = this.projects.find(p => p.id === projectId);
    return project ? project.name : 'Unknown Project';
  }

  getWorkflowName(workflowId?: string): string {
    if (!workflowId) return 'Manual Trigger';
    const execution = this.executions.find(e => e.workflowId === workflowId);
    return execution ? (execution.workflow?.name || 'Unknown Workflow') : 'Unknown Workflow';
  }

  openCreateModal(): void {
    this.showCreateModal = true;
    this.scheduleForm.reset();
    this.scheduleForm.patchValue({ enabled: true });
    this.error = '';
    this.success = '';
  }

  closeCreateModal(): void {
    this.showCreateModal = false;
    this.scheduleForm.reset();
  }

  createSchedule(): void {
    if (this.scheduleForm.invalid) {
      return;
    }

    this.creating = true;
    const scheduleData = this.scheduleForm.value;

    this.scheduleService.createSchedule(scheduleData).subscribe({
      next: (schedule) => {
        this.success = 'Schedule created successfully.';
        this.error = '';
        this.creating = false;
        this.closeCreateModal();
        this.loadSchedules();
      },
      error: (error) => {
        this.error = 'Failed to create schedule. Please try again.';
        console.error('Error creating schedule', error);
        this.creating = false;
      }
    });
  }

  toggleSchedule(schedule: Schedule): void {
    const action = schedule.enabled ? 'disable' : 'enable';
    const serviceCall = schedule.enabled ?
      this.scheduleService.disableSchedule(schedule.id) :
      this.scheduleService.enableSchedule(schedule.id);

    serviceCall.subscribe({
      next: () => {
        this.success = `Schedule ${action}d successfully.`;
        this.error = '';
        this.loadSchedules();
      },
      error: (error) => {
        this.error = `Failed to ${action} schedule. Please try again.`;
        console.error(`Error ${action}ing schedule`, error);
      }
    });
  }

  triggerSchedule(schedule: Schedule): void {
    this.scheduleService.triggerSchedule(schedule.id).subscribe({
      next: () => {
        this.success = 'Schedule triggered successfully.';
        this.error = '';
      },
      error: (error) => {
        this.error = 'Failed to trigger schedule. Please try again.';
        console.error('Error triggering schedule', error);
      }
    });
  }

  deleteSchedule(schedule: Schedule): void {
    if (confirm(`Are you sure you want to delete the schedule "${schedule.name}"?`)) {
      this.scheduleService.deleteSchedule(schedule.id).subscribe({
        next: () => {
          this.success = 'Schedule deleted successfully.';
          this.error = '';
          this.loadSchedules();
        },
        error: (error) => {
          this.error = 'Failed to delete schedule. Please try again.';
          console.error('Error deleting schedule', error);
        }
      });
    }
  }

  getStatusColor(enabled: boolean): string {
    return enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
  }

  getStatusText(enabled: boolean): string {
    return enabled ? 'Enabled' : 'Disabled';
  }

  onCronExpressionChange(expression: string): void {
    this.scheduleForm.patchValue({ cronExpression: expression });
  }

  getFilteredExecutions(): WorkflowExecution[] {
    const selectedProjectId = this.scheduleForm.get('projectId')?.value;
    if (!selectedProjectId) {
      return this.executions;
    }
    return this.executions.filter(e => e.projectId === selectedProjectId);
  }
}
