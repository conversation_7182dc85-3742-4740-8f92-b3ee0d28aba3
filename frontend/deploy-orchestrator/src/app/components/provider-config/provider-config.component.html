<div class="min-h-screen bg-gray-50 py-6">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Provider Configuration</h1>
      <p class="mt-2 text-gray-600">Manage deployment providers, environments, and templates</p>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
      <nav class="flex space-x-8" aria-label="Tabs">
        <button
          (click)="activeTab = 'providers'"
          [class]="activeTab === 'providers' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
          Providers
          <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">{{ providers.length }}</span>
        </button>
        <button
          (click)="activeTab = 'environments'"
          [class]="activeTab === 'environments' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
          Environments
          <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">{{ environments.length }}</span>
        </button>
        <button
          (click)="activeTab = 'templates'"
          [class]="activeTab === 'templates' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
          Templates
          <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">{{ providerTemplates.length }}</span>
        </button>
      </nav>
    </div>

    <!-- Providers Tab -->
    <div *ngIf="activeTab === 'providers'" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Providers List -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Available Providers</h3>
          </div>
          <div class="divide-y divide-gray-200">
            <div *ngFor="let provider of providers" 
                 (click)="loadProviderDetails(provider)"
                 [class]="selectedProvider?.type === provider.type ? 'bg-blue-50' : 'hover:bg-gray-50'"
                 class="px-6 py-4 cursor-pointer transition-colors">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <h4 class="font-medium text-gray-900">{{ provider.type | titlecase }}</h4>
                  <p class="text-sm text-gray-600">{{ provider.capabilities.length }} capabilities</p>
                </div>
                <div class="flex flex-col items-end gap-2">
                  <span [class]="getProviderStatusClass(provider)" 
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                    {{ provider.status | titlecase }}
                  </span>
                  <button *ngIf="!isProviderInstalled(provider)"
                          (click)="installProvider(provider); $event.stopPropagation()"
                          class="text-blue-600 hover:text-blue-800 text-xs">
                    Install
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Provider Details -->
      <div class="lg:col-span-2">
        <div *ngIf="selectedProvider" class="space-y-6">
          <!-- Provider Info -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-xl font-semibold text-gray-900">{{ selectedProvider.type | titlecase }} Provider</h3>
              <span [class]="getProviderStatusClass(selectedProvider)" 
                    class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium">
                {{ selectedProvider.status | titlecase }}
              </span>
            </div>

            <!-- Plugin Info -->
            <div *ngIf="selectedProvider.plugin" class="mb-6 p-4 bg-gray-50 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2">Plugin Information</h4>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-gray-600">Name:</span>
                  <span class="ml-2 font-medium">{{ selectedProvider.plugin.name }}</span>
                </div>
                <div>
                  <span class="text-gray-600">Version:</span>
                  <span class="ml-2 font-medium">{{ selectedProvider.plugin.version }}</span>
                </div>
                <div>
                  <span class="text-gray-600">Status:</span>
                  <span class="ml-2 font-medium">{{ selectedProvider.plugin.status }}</span>
                </div>
                <div>
                  <span class="text-gray-600">Hot Reload:</span>
                  <span class="ml-2 font-medium">
                    <i [class]="selectedProvider.plugin.hotReload ? 'fas fa-check text-green-500' : 'fas fa-times text-red-500'"></i>
                    {{ selectedProvider.plugin.hotReload ? 'Enabled' : 'Disabled' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Capabilities -->
            <div class="mb-6">
              <h4 class="font-medium text-gray-900 mb-3">Capabilities</h4>
              <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                <div *ngFor="let capability of selectedProvider.capabilities" 
                     class="flex items-center p-3 bg-blue-50 rounded-lg">
                  <i [class]="getCapabilityIcon(capability)" class="text-blue-600 mr-3"></i>
                  <span class="text-sm font-medium text-blue-900">{{ capability | titlecase }}</span>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex gap-3">
              <button (click)="openCreateEnvironmentModal(selectedProvider)"
                      class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                <i class="fas fa-plus mr-2"></i>Create Environment
              </button>
              <button *ngIf="selectedProvider.templates.length > 0"
                      class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                <i class="fas fa-template mr-2"></i>View Templates
              </button>
            </div>
          </div>

          <!-- Environments for this Provider -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-semibold text-gray-900">Environments</h3>
            </div>
            <div class="p-6">
              <div *ngIf="getEnvironmentsByProvider(selectedProvider.type).length === 0" 
                   class="text-center py-8 text-gray-500">
                No environments configured for this provider
              </div>
              <div *ngIf="getEnvironmentsByProvider(selectedProvider.type).length > 0" 
                   class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div *ngFor="let env of getEnvironmentsByProvider(selectedProvider.type)" 
                     class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                  <div class="flex items-center justify-between mb-2">
                    <h4 class="font-medium text-gray-900">{{ env.name }}</h4>
                    <div class="flex gap-2">
                      <button (click)="selectEnvironment(env)"
                              class="text-blue-600 hover:text-blue-800 text-sm">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button (click)="deleteEnvironment(env)"
                              class="text-red-600 hover:text-red-800 text-sm">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                  <p class="text-sm text-gray-600 mb-2">{{ env.description }}</p>
                  <div class="text-xs text-gray-500">
                    <div>Cluster: {{ env.provider.config.cluster }}</div>
                    <div>Region: {{ env.provider.config.region }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- No Provider Selected -->
        <div *ngIf="!selectedProvider" class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
          <i class="fas fa-cloud text-gray-400 text-4xl mb-4"></i>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Select a Provider</h3>
          <p class="text-gray-600">Choose a provider from the list to view details and manage environments</p>
        </div>
      </div>
    </div>

    <!-- Environments Tab -->
    <div *ngIf="activeTab === 'environments'">
      <div class="mb-6 flex justify-between items-center">
        <h2 class="text-xl font-semibold text-gray-900">All Environments</h2>
        <button (click)="openCreateEnvironmentModal()"
                class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
          <i class="fas fa-plus mr-2"></i>Create Environment
        </button>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div *ngFor="let env of environments" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">{{ env.name }}</h3>
            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">
              {{ env.provider.type | titlecase }}
            </span>
          </div>
          <p class="text-sm text-gray-600 mb-4">{{ env.description }}</p>
          
          <div class="space-y-2 text-sm text-gray-500 mb-4">
            <div class="flex justify-between">
              <span>Cluster:</span>
              <span class="font-medium">{{ env.provider.config.cluster }}</span>
            </div>
            <div class="flex justify-between">
              <span>Region:</span>
              <span class="font-medium">{{ env.provider.config.region }}</span>
            </div>
            <div class="flex justify-between">
              <span>Namespace:</span>
              <span class="font-medium">{{ env.provider.config.namespace }}</span>
            </div>
          </div>

          <div class="flex gap-2">
            <button (click)="selectEnvironment(env)"
                    class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
              <i class="fas fa-edit mr-1"></i>Edit
            </button>
            <button (click)="deleteEnvironment(env)"
                    class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">
              <i class="fas fa-trash mr-1"></i>Delete
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Templates Tab -->
    <div *ngIf="activeTab === 'templates'">
      <div class="mb-6">
        <h2 class="text-xl font-semibold text-gray-900">Deployment Templates</h2>
        <p class="text-gray-600">Pre-configured deployment templates from installed providers</p>
      </div>

      <div *ngFor="let category of getUniqueCategories()" class="mb-8">
        <h3 class="text-lg font-medium text-gray-900 mb-4">{{ category | titlecase }}</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div *ngFor="let template of getTemplatesByCategory(category)" 
               class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-start justify-between mb-4">
              <div class="flex-1">
                <h4 class="text-lg font-semibold text-gray-900">{{ template.name }}</h4>
                <p class="text-sm text-gray-600">{{ template.provider | titlecase }}</p>
              </div>
              <div class="flex flex-wrap gap-1">
                <span *ngFor="let tag of template.tags" 
                      class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">
                  {{ tag }}
                </span>
              </div>
            </div>

            <p class="text-sm text-gray-600 mb-4">{{ template.description }}</p>

            <div class="mb-4">
              <h5 class="text-sm font-medium text-gray-900 mb-2">Parameters ({{ template.parameters.length }})</h5>
              <div class="space-y-1">
                <div *ngFor="let param of template.parameters.slice(0, 3)" 
                     class="text-xs text-gray-500 flex justify-between">
                  <span>{{ param.name }}</span>
                  <span [class]="param.required ? 'text-red-500' : 'text-gray-400'">
                    {{ param.required ? 'Required' : 'Optional' }}
                  </span>
                </div>
                <div *ngIf="template.parameters.length > 3" class="text-xs text-gray-400">
                  +{{ template.parameters.length - 3 }} more...
                </div>
              </div>
            </div>

            <button (click)="openTemplateModal(template)"
                    class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
              <i class="fas fa-rocket mr-2"></i>Deploy
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Create Environment Modal -->
<div *ngIf="showCreateEnvironmentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Create Environment</h3>
      <form (ngSubmit)="createEnvironment()">
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Name</label>
            <input type="text" [(ngModel)]="environmentForm.name" name="name" required
                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
            <textarea [(ngModel)]="environmentForm.description" name="description" rows="3"
                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Provider Type</label>
            <select [(ngModel)]="environmentForm.provider!.type" name="providerType" required
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
              <option value="">Select Provider</option>
              <option *ngFor="let provider of providers" [value]="provider.type">
                {{ provider.type | titlecase }}
              </option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Cluster</label>
            <input type="text" [(ngModel)]="environmentForm.provider!.config.cluster" name="cluster" required
                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Region</label>
            <input type="text" [(ngModel)]="environmentForm.provider!.config.region" name="region" required
                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Namespace</label>
            <input type="text" [(ngModel)]="environmentForm.provider!.config.namespace" name="namespace" required
                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
          </div>
        </div>
        <div class="flex justify-end gap-2 mt-6">
          <button type="button" (click)="closeCreateEnvironmentModal()"
                  class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
            Cancel
          </button>
          <button type="submit"
                  class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            Create
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Template Deployment Modal -->
<div *ngIf="showTemplateModal && selectedTemplate" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div class="relative top-10 mx-auto p-5 border w-4/5 max-w-2xl shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Deploy {{ selectedTemplate.name }}</h3>
      <form (ngSubmit)="deployWithTemplate()">
        <div class="space-y-4 max-h-96 overflow-y-auto">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Target Environment</label>
            <select [(ngModel)]="selectedEnvironment" name="environment" required
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
              <option [ngValue]="null">Select Environment</option>
              <option *ngFor="let env of environments" [ngValue]="env">
                {{ env.name }} ({{ env.provider.type | titlecase }})
              </option>
            </select>
          </div>
          
          <div *ngFor="let param of selectedTemplate.parameters" class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">
              {{ param.name }}
              <span *ngIf="isParameterRequired(param)" class="text-red-500">*</span>
            </label>
            <p *ngIf="param.description" class="text-xs text-gray-500 mb-1">{{ param.description }}</p>
            
            <input *ngIf="getParameterInputType(param.type) !== 'checkbox'"
                   [type]="getParameterInputType(param.type)"
                   [(ngModel)]="templateParameters[param.name]"
                   [name]="param.name"
                   [required]="isParameterRequired(param)"
                   [placeholder]="getParameterPlaceholder(param)"
                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            
            <label *ngIf="getParameterInputType(param.type) === 'checkbox'" class="flex items-center">
              <input type="checkbox"
                     [(ngModel)]="templateParameters[param.name]"
                     [name]="param.name"
                     class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
              <span class="ml-2 text-sm text-gray-700">{{ param.description || param.name }}</span>
            </label>
          </div>
        </div>
        
        <div class="flex justify-end gap-2 mt-6">
          <button type="button" (click)="closeTemplateModal()"
                  class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
            Cancel
          </button>
          <button type="submit" [disabled]="!selectedEnvironment"
                  class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400">
            Deploy
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
