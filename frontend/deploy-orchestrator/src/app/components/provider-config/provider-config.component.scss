.provider-card {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  &.selected {
    border-color: #3b82f6;
    background-color: #eff6ff;
  }
}

.capability-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.capability-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background: #f0f9ff;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background: #e0f2fe;
    transform: translateY(-1px);
  }
  
  .icon {
    color: #0ea5e9;
    margin-right: 0.75rem;
    font-size: 1.125rem;
  }
  
  .name {
    font-weight: 500;
    color: #0c4a6e;
  }
}

.environment-card {
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
  }
}

.template-card {
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  }
  
  .template-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
  }
  
  .template-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    
    .tag {
      background: #f3f4f6;
      color: #374151;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      font-size: 0.75rem;
      font-weight: 500;
    }
  }
  
  .parameters-preview {
    background: #f9fafb;
    border-radius: 0.375rem;
    padding: 0.75rem;
    
    .parameter-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .required {
        color: #dc2626;
        font-weight: 600;
      }
      
      .optional {
        color: #9ca3af;
      }
    }
  }
}

.provider-status {
  &.installed {
    background: #dcfce7;
    color: #166534;
  }
  
  &.available {
    background: #dbeafe;
    color: #1e40af;
  }
  
  &.error {
    background: #fee2e2;
    color: #dc2626;
  }
}

.plugin-info {
  background: #f8fafc;
  border-radius: 0.5rem;
  padding: 1rem;
  
  .info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    
    .info-item {
      display: flex;
      justify-content: space-between;
      
      .label {
        color: #6b7280;
      }
      
      .value {
        font-weight: 500;
        color: #111827;
      }
    }
  }
}

.environment-form {
  .form-group {
    margin-bottom: 1rem;
    
    label {
      display: block;
      font-weight: 500;
      color: #374151;
      margin-bottom: 0.5rem;
    }
    
    input, select, textarea {
      width: 100%;
      border-radius: 0.375rem;
      border: 1px solid #d1d5db;
      padding: 0.5rem 0.75rem;
      transition: all 0.2s ease-in-out;
      
      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }
  }
}

.template-deployment-form {
  .parameter-group {
    margin-bottom: 1rem;
    
    .parameter-label {
      display: flex;
      align-items: center;
      margin-bottom: 0.5rem;
      
      .required-indicator {
        color: #dc2626;
        margin-left: 0.25rem;
      }
    }
    
    .parameter-description {
      color: #6b7280;
      font-size: 0.75rem;
      margin-bottom: 0.5rem;
    }
    
    .checkbox-group {
      display: flex;
      align-items: center;
      
      input[type="checkbox"] {
        margin-right: 0.5rem;
      }
    }
  }
}

.modal-overlay {
  backdrop-filter: blur(4px);
}

.modal-content {
  max-height: 90vh;
  overflow-y: auto;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  
  button {
    transition: all 0.2s ease-in-out;
    
    &:hover {
      transform: translateY(-1px);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  
  .icon {
    font-size: 3rem;
    color: #d1d5db;
    margin-bottom: 1rem;
  }
  
  .title {
    font-size: 1.125rem;
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.5rem;
  }
  
  .description {
    color: #6b7280;
  }
}

.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.provider-list {
  .provider-item {
    transition: all 0.2s ease-in-out;
    
    &:hover {
      background-color: #f9fafb;
    }
    
    &.selected {
      background-color: #eff6ff;
      border-left: 4px solid #3b82f6;
    }
  }
}

.capabilities-showcase {
  .capability-category {
    margin-bottom: 2rem;
    
    .category-title {
      font-weight: 600;
      color: #111827;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #e5e7eb;
    }
  }
}

.environment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

// Responsive design
@media (max-width: 768px) {
  .provider-details {
    .capability-grid {
      grid-template-columns: 1fr;
    }
  }
  
  .environment-grid,
  .template-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    width: 95%;
    margin: 1rem auto;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .provider-card,
  .environment-card,
  .template-card {
    background: #1f2937;
    border-color: #374151;
    
    &:hover {
      background: #111827;
    }
  }
  
  .plugin-info {
    background: #111827;
    border-color: #374151;
  }
  
  .capability-item {
    background: #1e293b;
    
    &:hover {
      background: #0f172a;
    }
  }
}
