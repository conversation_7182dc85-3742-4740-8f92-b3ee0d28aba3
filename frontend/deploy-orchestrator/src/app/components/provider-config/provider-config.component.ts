import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { PluginService } from '../../services/plugin.service';
import { EnvironmentService } from '../../services/environment.service';
import {
  ProviderInfo,
  PluginTemplate,
  PluginCapability,
  Plugin
} from '../../models/plugin.model';
import { Environment, ProviderConfig } from '../../models/environment.model';
import { EnvironmentConfig, CreateEnvironmentRequest, UpdateEnvironmentRequest, ProviderType } from '../../services/environment.service';

@Component({
  selector: 'app-environment-provider-config',
  templateUrl: './provider-config.component.html',
  styleUrls: ['./provider-config.component.scss']
})
export class EnvironmentProviderConfigComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Data
  providers: ProviderInfo[] = [];
  environments: EnvironmentConfig[] = [];
  selectedProvider: ProviderInfo | null = null;
  selectedEnvironment: EnvironmentConfig | null = null;
  providerTemplates: PluginTemplate[] = [];
  providerCapabilities: PluginCapability[] = [];

  // UI State
  activeTab: 'providers' | 'environments' | 'templates' = 'providers';
  loading = false;
  error: string | null = null;
  showCreateEnvironmentModal = false;
  showTemplateModal = false;
  selectedTemplate: PluginTemplate | null = null;

  // Forms
  environmentForm: Partial<CreateEnvironmentRequest> = {
    name: '',
    description: '',
    projectId: '',
    type: 'kubernetes',
    provider: {
      type: 'gke' as ProviderType,
      config: {
        cluster: '',
        region: '',
        zone: '',
        namespace: '',
        endpoint: '',
        project: '',
        authMethod: 'service-account',
        extra: {}
      }
    },
    resources: {
      cpu: '1',
      memory: '1Gi',
      storage: '10Gi'
    },
    networking: {},
    healthCheck: {
      enabled: false
    },
    deploymentStrategy: 'rolling'
  };

  templateParameters: { [key: string]: any } = {};

  constructor(
    private pluginService: PluginService,
    private environmentService: EnvironmentService
  ) {}

  ngOnInit(): void {
    this.loadProviders();
    this.loadEnvironments();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Data Loading
  loadProviders(): void {
    this.loading = true;
    this.error = null;

    this.pluginService.getProviders()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (providers) => {
          this.providers = providers;
          this.loading = false;
        },
        error: (error) => {
          this.error = 'Failed to load providers';
          this.loading = false;
          console.error('Error loading providers:', error);
        }
      });
  }

  loadEnvironments(): void {
    this.environmentService.getEnvironments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.environments = response.environments;
        },
        error: (error) => {
          console.error('Error loading environments:', error);
        }
      });
  }

  loadProviderDetails(provider: ProviderInfo): void {
    this.selectedProvider = provider;

    // Load templates for this provider
    this.pluginService.getPluginTemplates(provider.type)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (templates) => {
          this.providerTemplates = templates;
        },
        error: (error) => {
          console.error('Error loading provider templates:', error);
        }
      });

    // Load capabilities for this provider
    this.pluginService.getProviderCapabilities(provider.type)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (capabilities) => {
          this.providerCapabilities = capabilities;
        },
        error: (error) => {
          console.error('Error loading provider capabilities:', error);
        }
      });
  }

  // Environment Management
  openCreateEnvironmentModal(provider?: ProviderInfo): void {
    this.showCreateEnvironmentModal = true;
    if (provider) {
      this.environmentForm.provider!.type = provider.type as ProviderType;
    }
  }

  closeCreateEnvironmentModal(): void {
    this.showCreateEnvironmentModal = false;
    this.environmentForm = {
      name: '',
      description: '',
      projectId: '',
      type: 'kubernetes',
      provider: {
        type: 'gke' as ProviderType,
        config: {
          cluster: '',
          region: '',
          zone: '',
          namespace: '',
          endpoint: '',
          project: '',
          authMethod: 'service-account',
          extra: {}
        }
      },
      resources: {
        cpu: '1',
        memory: '1Gi',
        storage: '10Gi'
      },
      networking: {},
      healthCheck: {
        enabled: false
      },
      deploymentStrategy: 'rolling'
    };
  }

  createEnvironment(): void {
    this.environmentService.createEnvironment(this.environmentForm as CreateEnvironmentRequest)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (environment) => {
          this.environments.push(environment);
          this.closeCreateEnvironmentModal();
        },
        error: (error) => {
          console.error('Error creating environment:', error);
        }
      });
  }

  selectEnvironment(environment: EnvironmentConfig): void {
    this.selectedEnvironment = environment;
  }

  updateEnvironment(environment: EnvironmentConfig): void {
    const updateRequest: UpdateEnvironmentRequest = {
      name: environment.name,
      provider: environment.provider,
      resources: environment.resources,
      networking: environment.networking,
      variables: environment.variables,
      secretMappings: environment.secretMappings,
      healthCheck: environment.healthCheck,
      deploymentStrategy: environment.deploymentStrategy,
      status: environment.status,
      description: environment.description,
      tags: environment.tags
    };

    this.environmentService.updateEnvironment(environment.id!, updateRequest)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedEnvironment) => {
          const index = this.environments.findIndex(e => e.id === environment.id);
          if (index !== -1) {
            this.environments[index] = updatedEnvironment;
          }
        },
        error: (error) => {
          console.error('Error updating environment:', error);
        }
      });
  }

  deleteEnvironment(environment: EnvironmentConfig): void {
    if (confirm(`Are you sure you want to delete environment "${environment.name}"?`)) {
      this.environmentService.deleteEnvironment(environment.id!)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.environments = this.environments.filter(e => e.id !== environment.id);
            if (this.selectedEnvironment?.id === environment.id) {
              this.selectedEnvironment = null;
            }
          },
          error: (error) => {
            console.error('Error deleting environment:', error);
          }
        });
    }
  }

  // Template Management
  openTemplateModal(template: PluginTemplate): void {
    this.selectedTemplate = template;
    this.showTemplateModal = true;

    // Initialize template parameters
    this.templateParameters = {};
    template.parameters.forEach(param => {
      this.templateParameters[param.name] = param.default || '';
    });
  }

  closeTemplateModal(): void {
    this.showTemplateModal = false;
    this.selectedTemplate = null;
    this.templateParameters = {};
  }

  deployWithTemplate(): void {
    if (!this.selectedTemplate || !this.selectedEnvironment) return;

    const deploymentRequest = {
      pluginName: this.selectedProvider?.plugin?.name || '',
      templateId: this.selectedTemplate.id,
      parameters: this.templateParameters,
      environmentId: this.selectedEnvironment.id!,
      projectId: this.selectedEnvironment.projectId
    };

    this.pluginService.deployWithPlugin(deploymentRequest)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result) => {
          console.log('Deployment result:', result);
          this.closeTemplateModal();
          // Show success message or navigate to deployment details
        },
        error: (error) => {
          console.error('Error deploying with template:', error);
        }
      });
  }

  // Utility Methods
  getProviderStatusClass(provider: ProviderInfo): string {
    switch (provider.status) {
      case 'installed':
        return 'bg-green-100 text-green-800';
      case 'available':
        return 'bg-blue-100 text-blue-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getCapabilityIcon(capability: string): string {
    const iconMap: { [key: string]: string } = {
      'deploy': 'fas fa-rocket',
      'rollback': 'fas fa-undo',
      'scaling': 'fas fa-expand-arrows-alt',
      'health-checks': 'fas fa-heartbeat',
      'logs': 'fas fa-file-alt',
      'metrics': 'fas fa-chart-line',
      'secrets': 'fas fa-key',
      'config-maps': 'fas fa-cog',
      'load-balancing': 'fas fa-balance-scale',
      'auto-scaling': 'fas fa-arrows-alt-h',
      'blue-green': 'fas fa-exchange-alt',
      'canary': 'fas fa-feather-alt',
      'routes': 'fas fa-route',
      'builds': 'fas fa-hammer',
      'image-streams': 'fas fa-stream',
      's2i': 'fas fa-code-branch'
    };
    return iconMap[capability] || 'fas fa-puzzle-piece';
  }

  getEnvironmentsByProvider(providerType: string): EnvironmentConfig[] {
    return this.environments.filter(env => env.provider.type === providerType);
  }

  getTemplatesByCategory(category: string): PluginTemplate[] {
    return this.providerTemplates.filter(template => template.category === category);
  }

  getUniqueCategories(): string[] {
    return [...new Set(this.providerTemplates.map(t => t.category))];
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  isProviderInstalled(provider: ProviderInfo): boolean {
    return provider.status === 'installed' && provider.plugin !== undefined;
  }

  getProviderPlugin(provider: ProviderInfo): Plugin | null {
    return provider.plugin || null;
  }

  installProvider(provider: ProviderInfo): void {
    // This would trigger plugin installation for the provider
    console.log('Installing provider:', provider.type);
    // Implementation would depend on how providers are packaged as plugins
  }

  getParameterInputType(paramType: string): string {
    switch (paramType) {
      case 'number':
      case 'integer':
        return 'number';
      case 'boolean':
        return 'checkbox';
      case 'password':
        return 'password';
      default:
        return 'text';
    }
  }

  isParameterRequired(param: any): boolean {
    return param.required === true;
  }

  getParameterPlaceholder(param: any): string {
    if (param.default !== undefined) {
      return `Default: ${param.default}`;
    }
    return param.description || '';
  }
}
