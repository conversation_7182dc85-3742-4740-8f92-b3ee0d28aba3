import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface EnvironmentTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  provider: string;
  version: string;
  tags: string[];
  is_public: boolean;
  created_by: string;
  config: any;
  variables: TemplateVariable[];
  presets: EnvironmentPreset[];
  usage_count: number;
  rating: number;
  created_at: string;
  updated_at: string;
}

export interface TemplateVariable {
  name: string;
  type: string;
  description: string;
  default_value?: any;
  required: boolean;
  options?: string[];
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    options?: string[];
  };
}

export interface EnvironmentPreset {
  name: string;
  description: string;
  values: { [key: string]: any };
  tags: string[];
}

export interface CreateTemplateRequest {
  name: string;
  description: string;
  category: string;
  provider: string;
  version?: string;
  tags: string[];
  is_public: boolean;
  created_by: string;
  config: any;
  variables: TemplateVariable[];
  presets: EnvironmentPreset[];
}

export interface UpdateTemplateRequest {
  name?: string;
  description?: string;
  category?: string;
  version?: string;
  tags?: string[];
  is_public?: boolean;
  config?: any;
  variables?: TemplateVariable[];
  presets?: EnvironmentPreset[];
}

export interface ListTemplatesRequest {
  category?: string;
  provider?: string;
  created_by?: string;
  is_public?: boolean;
  search?: string;
  sort_by?: string;
  limit?: number;
  offset?: number;
}

export interface CreateFromTemplateRequest {
  template_id: string;
  project_id: string;
  name: string;
  description?: string;
  preset_name?: string;
  variables: { [key: string]: any };
  tags?: string[];
}

export interface TemplateValidationResult {
  valid: boolean;
  missing_variables: string[];
  invalid_variables: string[];
  template_id: string;
}

@Injectable({
  providedIn: 'root'
})
export class EnvironmentTemplateService {
  private apiUrl = `${environment.apiUrl}/environment-service/v1/templates`;

  constructor(private http: HttpClient) {}

  // List templates with optional filtering
  listTemplates(request?: ListTemplatesRequest): Observable<{ templates: EnvironmentTemplate[], total: number }> {
    let params = new HttpParams();
    
    if (request) {
      if (request.category) params = params.set('category', request.category);
      if (request.provider) params = params.set('provider', request.provider);
      if (request.created_by) params = params.set('created_by', request.created_by);
      if (request.is_public !== undefined) params = params.set('is_public', request.is_public.toString());
      if (request.search) params = params.set('search', request.search);
      if (request.sort_by) params = params.set('sort_by', request.sort_by);
      if (request.limit) params = params.set('limit', request.limit.toString());
      if (request.offset) params = params.set('offset', request.offset.toString());
    }

    return this.http.get<{ templates: EnvironmentTemplate[], total: number }>(this.apiUrl, { params });
  }

  // Get a specific template
  getTemplate(id: string): Observable<EnvironmentTemplate> {
    return this.http.get<EnvironmentTemplate>(`${this.apiUrl}/${id}`);
  }

  // Create a new template
  createTemplate(request: CreateTemplateRequest): Observable<EnvironmentTemplate> {
    return this.http.post<EnvironmentTemplate>(this.apiUrl, request);
  }

  // Update an existing template
  updateTemplate(id: string, request: UpdateTemplateRequest): Observable<EnvironmentTemplate> {
    return this.http.put<EnvironmentTemplate>(`${this.apiUrl}/${id}`, request);
  }

  // Delete a template
  deleteTemplate(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  // Create environment from template
  createEnvironmentFromTemplate(id: string, request: CreateFromTemplateRequest): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/${id}/create-environment`, request);
  }

  // Get template presets
  getTemplatePresets(id: string): Observable<{ template_id: string, presets: EnvironmentPreset[], total: number }> {
    return this.http.get<{ template_id: string, presets: EnvironmentPreset[], total: number }>(`${this.apiUrl}/${id}/presets`);
  }

  // Get template variables
  getTemplateVariables(id: string): Observable<{ template_id: string, variables: TemplateVariable[], total: number }> {
    return this.http.get<{ template_id: string, variables: TemplateVariable[], total: number }>(`${this.apiUrl}/${id}/variables`);
  }

  // Validate template configuration
  validateTemplateConfig(id: string, presetName?: string, variables?: { [key: string]: any }): Observable<TemplateValidationResult> {
    const request = {
      preset_name: presetName,
      variables: variables || {}
    };
    return this.http.post<TemplateValidationResult>(`${this.apiUrl}/${id}/validate`, request);
  }

  // Get template usage statistics
  getTemplateStats(id: string): Observable<{ template_id: string, usage_count: number, rating: number, created_at: string, updated_at: string }> {
    return this.http.get<{ template_id: string, usage_count: number, rating: number, created_at: string, updated_at: string }>(`${this.apiUrl}/${id}/stats`);
  }

  // Get popular templates
  getPopularTemplates(limit: number = 10): Observable<{ templates: EnvironmentTemplate[], total: number }> {
    return this.listTemplates({
      sort_by: 'usage_count',
      limit: limit,
      is_public: true
    });
  }

  // Get templates by category
  getTemplatesByCategory(category: string, limit?: number): Observable<{ templates: EnvironmentTemplate[], total: number }> {
    return this.listTemplates({
      category: category,
      limit: limit,
      is_public: true
    });
  }

  // Get templates by provider
  getTemplatesByProvider(provider: string, limit?: number): Observable<{ templates: EnvironmentTemplate[], total: number }> {
    return this.listTemplates({
      provider: provider,
      limit: limit,
      is_public: true
    });
  }

  // Search templates
  searchTemplates(query: string, limit?: number): Observable<{ templates: EnvironmentTemplate[], total: number }> {
    return this.listTemplates({
      search: query,
      limit: limit,
      is_public: true
    });
  }

  // Get user's templates
  getUserTemplates(userId: string, limit?: number): Observable<{ templates: EnvironmentTemplate[], total: number }> {
    return this.listTemplates({
      created_by: userId,
      limit: limit
    });
  }

  // Get template categories
  getTemplateCategories(): Observable<string[]> {
    // This could be enhanced to get categories from the backend
    return new Observable(observer => {
      observer.next([
        'web-application',
        'microservice',
        'database',
        'cache',
        'message-queue',
        'monitoring',
        'ci-cd',
        'development',
        'testing',
        'production'
      ]);
      observer.complete();
    });
  }

  // Get supported providers
  getSupportedProviders(): Observable<string[]> {
    // This could be enhanced to get providers from the backend
    return new Observable(observer => {
      observer.next([
        'kubernetes',
        'openshift',
        'docker-swarm',
        'aws-ecs',
        'azure-aci',
        'cloud-run',
        'apigee'
      ]);
      observer.complete();
    });
  }
}
