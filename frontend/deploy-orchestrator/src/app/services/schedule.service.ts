import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, tap } from 'rxjs';
import { environment } from '../../environments/environment';
import { AuthService } from './auth.service';
import { DataRefreshService } from './data-refresh.service';

export interface Schedule {
  id: string;
  name: string;
  description?: string;
  projectId: string;
  workflowId?: string;
  deploymentId?: string; // Keep for backward compatibility
  cronExpression: string;
  enabled: boolean;
  active?: boolean; // Keep for backward compatibility
  nextRun?: Date;
  lastRun?: Date;
  createdAt?: Date;
  updatedAt?: Date;
  createdBy?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ScheduleService {
  private apiUrl = `${environment.apiV1Url}/schedules`;

  // BehaviorSubject to store and share the schedules list
  private schedulesSubject = new BehaviorSubject<Schedule[]>([]);

  // Observable that components can subscribe to
  public schedules$ = this.schedulesSubject.asObservable();

  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private dataRefreshService: DataRefreshService
  ) {
    // Register this service's refresh function with the centralized data refresh service
    this.dataRefreshService.registerRefreshFunction(() => {
      if (this.authService.isAuthenticated()) {
        this.refreshSchedules();
      }
    });

    // Only load schedules if user is authenticated
    if (this.authService.isAuthenticated()) {
      this.refreshSchedules();
    }
  }

  // Method to refresh the schedules list
  refreshSchedules(deploymentId?: string): void {
    // Only make the request if user is authenticated
    if (!this.authService.isAuthenticated()) {
      console.warn('Cannot load schedules: User not authenticated');
      return;
    }

    let url = this.apiUrl;
    if (deploymentId) {
      url += `?deploymentId=${deploymentId}`;
    }

    this.http.get<Schedule[]>(url)
      .subscribe({
        next: (schedules) => {
          this.schedulesSubject.next(schedules);
        },
        error: (error) => {
          console.error('Error loading schedules', error);
          // Error handling (including 401 redirects) is now handled by HTTP interceptors
          if (error.status === 401) {
            console.warn('Authentication failed while loading schedules. Interceptor will handle redirect.');
          }
        }
      });
  }

  getSchedules(deploymentId?: string): Observable<Schedule[]> {
    // Return the cached schedules and refresh in the background only if authenticated
    if (this.authService.isAuthenticated()) {
      this.refreshSchedules(deploymentId);
    }
    return this.schedules$;
  }

  getSchedule(id: string): Observable<Schedule> {
    return this.http.get<Schedule>(`${this.apiUrl}/${id}`);
  }

  createSchedule(schedule: Schedule): Observable<Schedule> {
    return this.http.post<Schedule>(this.apiUrl, schedule)
      .pipe(
        tap(() => {
          // Refresh the schedules list after creating a new schedule
          this.refreshSchedules(schedule.deploymentId);
        })
      );
  }

  updateSchedule(schedule: Schedule): Observable<Schedule> {
    return this.http.put<Schedule>(`${this.apiUrl}/${schedule.id}`, schedule)
      .pipe(
        tap(() => {
          // Refresh the schedules list after updating a schedule
          this.refreshSchedules(schedule.deploymentId);
        })
      );
  }

  deleteSchedule(id: string, deploymentId?: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`)
      .pipe(
        tap(() => {
          // Refresh the schedules list after deleting a schedule
          this.refreshSchedules(deploymentId);
        })
      );
  }

  activateSchedule(id: string, deploymentId?: string): Observable<Schedule> {
    return this.http.post<Schedule>(`${this.apiUrl}/${id}/activate`, {})
      .pipe(
        tap(() => {
          // Refresh the schedules list after activating a schedule
          this.refreshSchedules(deploymentId);
        })
      );
  }

  deactivateSchedule(id: string, deploymentId?: string): Observable<Schedule> {
    return this.http.post<Schedule>(`${this.apiUrl}/${id}/deactivate`, {})
      .pipe(
        tap(() => {
          // Refresh the schedules list after deactivating a schedule
          this.refreshSchedules(deploymentId);
        })
      );
  }

  toggleSchedule(id: string, deploymentId?: string): Observable<Schedule> {
    return this.http.put<Schedule>(`${this.apiUrl}/${id}/toggle`, {})
      .pipe(
        tap(() => {
          // Refresh the schedules list after toggling a schedule
          this.refreshSchedules(deploymentId);
        })
      );
  }

  enableSchedule(id: string): Observable<Schedule> {
    return this.http.post<Schedule>(`${this.apiUrl}/${id}/enable`, {})
      .pipe(
        tap(() => {
          this.refreshSchedules();
        })
      );
  }

  disableSchedule(id: string): Observable<Schedule> {
    return this.http.post<Schedule>(`${this.apiUrl}/${id}/disable`, {})
      .pipe(
        tap(() => {
          this.refreshSchedules();
        })
      );
  }

  getSchedulesByProject(projectId: string): Observable<Schedule[]> {
    return this.http.get<Schedule[]>(`${this.apiUrl}?projectId=${projectId}`);
  }

  triggerSchedule(id: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${id}/trigger`, {});
  }

  getScheduleHistory(id: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/${id}/history`);
  }
}
