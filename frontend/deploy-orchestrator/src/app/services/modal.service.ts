import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface ModalConfig {
  id: string;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closable?: boolean;
  data?: any;
}

export interface ModalState {
  isOpen: boolean;
  config: ModalConfig | null;
}

@Injectable({
  providedIn: 'root'
})
export class ModalService {
  private modalState$ = new BehaviorSubject<ModalState>({
    isOpen: false,
    config: null
  });

  constructor() {}

  /**
   * Get the current modal state
   */
  getModalState(): Observable<ModalState> {
    return this.modalState$.asObservable();
  }

  /**
   * Open a modal with the given configuration
   */
  open(config: ModalConfig): void {
    this.modalState$.next({
      isOpen: true,
      config: {
        closable: true,
        size: 'md',
        ...config
      }
    });
  }

  /**
   * Close the currently open modal
   */
  close(): void {
    this.modalState$.next({
      isOpen: false,
      config: null
    });
  }

  /**
   * Check if a modal is currently open
   */
  isOpen(): boolean {
    return this.modalState$.value.isOpen;
  }

  /**
   * Get the current modal configuration
   */
  getCurrentConfig(): ModalConfig | null {
    return this.modalState$.value.config;
  }
}
