import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { 
  ProjectIntegration, 
  IntegrationTemplate, 
  IntegrationTestResult, 
  CreateIntegrationRequest, 
  UpdateIntegrationRequest,
  IntegrationType 
} from '../models/project-integration.model';

@Injectable({
  providedIn: 'root'
})
export class ProjectIntegrationService {
  private apiUrl = `${environment.apiUrl}/integration-service/v1`;

  constructor(private http: HttpClient) {}

  // Project Integrations
  getProjectIntegrations(projectId: string): Observable<ProjectIntegration[]> {
    return this.http.get<ProjectIntegration[]>(`${this.apiUrl}/projects/${projectId}/integrations`);
  }

  getIntegration(projectId: string, integrationId: string): Observable<ProjectIntegration> {
    return this.http.get<ProjectIntegration>(`${this.apiUrl}/projects/${projectId}/integrations/${integrationId}`);
  }

  createIntegration(projectId: string, request: CreateIntegrationRequest): Observable<ProjectIntegration> {
    return this.http.post<ProjectIntegration>(`${this.apiUrl}/projects/${projectId}/integrations`, request);
  }

  updateIntegration(projectId: string, integrationId: string, request: UpdateIntegrationRequest): Observable<ProjectIntegration> {
    return this.http.put<ProjectIntegration>(`${this.apiUrl}/projects/${projectId}/integrations/${integrationId}`, request);
  }

  deleteIntegration(projectId: string, integrationId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/projects/${projectId}/integrations/${integrationId}`);
  }

  // Integration Templates
  getIntegrationTemplates(): Observable<IntegrationTemplate[]> {
    return this.http.get<IntegrationTemplate[]>(`${this.apiUrl}/integration-templates`);
  }

  getIntegrationTemplate(type: IntegrationType): Observable<IntegrationTemplate> {
    return this.http.get<IntegrationTemplate>(`${this.apiUrl}/integration-templates/${type}`);
  }

  // Integration Testing
  testIntegration(projectId: string, integrationId: string): Observable<IntegrationTestResult> {
    return this.http.post<IntegrationTestResult>(`${this.apiUrl}/projects/${projectId}/integrations/${integrationId}/test`, {});
  }

  testIntegrationConfig(type: IntegrationType, config: any): Observable<IntegrationTestResult> {
    return this.http.post<IntegrationTestResult>(`${this.apiUrl}/integration-templates/${type}/test`, { config });
  }

  // Integration Actions
  syncIntegration(projectId: string, integrationId: string): Observable<{ success: boolean; message: string }> {
    return this.http.post<{ success: boolean; message: string }>(`${this.apiUrl}/projects/${projectId}/integrations/${integrationId}/sync`, {});
  }

  toggleIntegration(projectId: string, integrationId: string, isActive: boolean): Observable<ProjectIntegration> {
    return this.http.patch<ProjectIntegration>(`${this.apiUrl}/projects/${projectId}/integrations/${integrationId}`, { isActive });
  }

  // Integration Logs and History
  getIntegrationLogs(projectId: string, integrationId: string, limit?: number): Observable<any[]> {
    let params = new HttpParams();
    if (limit) {
      params = params.set('limit', limit.toString());
    }
    return this.http.get<any[]>(`${this.apiUrl}/projects/${projectId}/integrations/${integrationId}/logs`, { params });
  }

  getIntegrationHistory(projectId: string, integrationId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/projects/${projectId}/integrations/${integrationId}/history`);
  }

  // Webhooks
  getWebhookUrl(projectId: string, integrationId: string): Observable<{ url: string; secret: string }> {
    return this.http.get<{ url: string; secret: string }>(`${this.apiUrl}/projects/${projectId}/integrations/${integrationId}/webhook`);
  }

  regenerateWebhookSecret(projectId: string, integrationId: string): Observable<{ secret: string }> {
    return this.http.post<{ secret: string }>(`${this.apiUrl}/projects/${projectId}/integrations/${integrationId}/webhook/regenerate`, {});
  }

  // Integration Statistics
  getIntegrationStats(projectId: string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/projects/${projectId}/integrations/stats`);
  }

  // Bulk Operations
  bulkToggleIntegrations(projectId: string, integrationIds: string[], isActive: boolean): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/projects/${projectId}/integrations/bulk`, { integrationIds, isActive });
  }

  bulkDeleteIntegrations(projectId: string, integrationIds: string[]): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/projects/${projectId}/integrations/bulk`, { body: { integrationIds } });
  }
}
