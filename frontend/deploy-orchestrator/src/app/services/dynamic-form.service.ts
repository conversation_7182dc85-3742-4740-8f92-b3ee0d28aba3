import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, FormControl, Validators, ValidatorFn } from '@angular/forms';
import { Observable, of } from 'rxjs';

export interface DynamicFormField {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'textarea' | 'password' | 'url' | 'email' | 'object' | 'array';
  label: string;
  description?: string;
  required?: boolean;
  sensitive?: boolean;
  default?: any;
  placeholder?: string;
  options?: string[] | { value: string; label: string }[];
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
    enum?: string[];
  };
  properties?: DynamicFormField[]; // For object types
  items?: DynamicFormField; // For array types
  conditional?: {
    field: string;
    value: any;
    operator?: 'equals' | 'not_equals' | 'contains' | 'not_contains';
  };
}

export interface DynamicFormSchema {
  title: string;
  description?: string;
  fields: DynamicFormField[];
}

@Injectable({
  providedIn: 'root'
})
export class DynamicFormService {

  constructor(private fb: FormBuilder) {}

  /**
   * Parse plugin YAML configSchema to dynamic form schema
   */
  parsePluginConfigSchema(pluginConfigSchema: any): DynamicFormSchema {
    const schema: DynamicFormSchema = {
      title: 'Plugin Configuration',
      description: 'Configure the plugin parameters',
      fields: []
    };

    if (pluginConfigSchema?.properties) {
      schema.fields = this.parseSchemaProperties(pluginConfigSchema.properties);
    }

    return schema;
  }

  /**
   * Parse schema properties recursively
   */
  private parseSchemaProperties(properties: any): DynamicFormField[] {
    const fields: DynamicFormField[] = [];

    for (const [key, prop] of Object.entries(properties)) {
      const property = prop as any;
      const field: DynamicFormField = {
        name: key,
        type: this.mapSchemaTypeToFormType(property.type),
        label: property.title || this.formatLabel(key),
        description: property.description,
        required: property.required === true,
        sensitive: property.sensitive === true,
        default: property.default,
        placeholder: property.example || property.placeholder
      };

      // Handle validation
      if (property.pattern || property.minimum || property.maximum || 
          property.minLength || property.maxLength || property.enum) {
        field.validation = {
          pattern: property.pattern,
          min: property.minimum,
          max: property.maximum,
          minLength: property.minLength,
          maxLength: property.maxLength,
          enum: property.enum
        };
      }

      // Handle select options
      if (property.enum) {
        field.type = 'select';
        field.options = property.enum.map((value: any) => ({
          value: value,
          label: this.formatLabel(value)
        }));
      }

      // Handle object properties
      if (property.type === 'object' && property.properties) {
        field.properties = this.parseSchemaProperties(property.properties);
      }

      // Handle array items
      if (property.type === 'array' && property.items) {
        field.items = this.parseSchemaProperties({ item: property.items })[0];
      }

      fields.push(field);
    }

    return fields;
  }

  /**
   * Map JSON Schema types to form field types
   */
  private mapSchemaTypeToFormType(schemaType: string): DynamicFormField['type'] {
    switch (schemaType) {
      case 'string': return 'string';
      case 'number': 
      case 'integer': return 'number';
      case 'boolean': return 'boolean';
      case 'object': return 'object';
      case 'array': return 'array';
      default: return 'string';
    }
  }

  /**
   * Format field names to human-readable labels
   */
  private formatLabel(name: string): string {
    return name
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .replace(/_/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Build reactive form from schema
   */
  buildFormFromSchema(schema: DynamicFormSchema, initialValues: any = {}): FormGroup {
    const formControls: { [key: string]: FormControl } = {};

    schema.fields.forEach(field => {
      const validators = this.buildValidators(field);
      const defaultValue = initialValues[field.name] ?? field.default ?? this.getDefaultValue(field.type);
      
      formControls[field.name] = new FormControl(defaultValue, validators);
    });

    return this.fb.group(formControls);
  }

  /**
   * Build validators for a field
   */
  private buildValidators(field: DynamicFormField): ValidatorFn[] {
    const validators: ValidatorFn[] = [];

    if (field.required) {
      validators.push(Validators.required);
    }

    if (field.validation) {
      const validation = field.validation;

      if (validation.pattern) {
        validators.push(Validators.pattern(validation.pattern));
      }

      if (validation.min !== undefined) {
        validators.push(Validators.min(validation.min));
      }

      if (validation.max !== undefined) {
        validators.push(Validators.max(validation.max));
      }

      if (validation.minLength !== undefined) {
        validators.push(Validators.minLength(validation.minLength));
      }

      if (validation.maxLength !== undefined) {
        validators.push(Validators.maxLength(validation.maxLength));
      }
    }

    // Type-specific validators
    if (field.type === 'email') {
      validators.push(Validators.email);
    }

    if (field.type === 'url') {
      validators.push(this.urlValidator());
    }

    return validators;
  }

  /**
   * Custom URL validator
   */
  private urlValidator(): ValidatorFn {
    return (control) => {
      if (!control.value) return null;
      
      try {
        new URL(control.value);
        return null;
      } catch {
        return { invalidUrl: true };
      }
    };
  }

  /**
   * Get default value for field type
   */
  private getDefaultValue(type: DynamicFormField['type']): any {
    switch (type) {
      case 'boolean': return false;
      case 'number': return 0;
      case 'array': return [];
      case 'object': return {};
      default: return '';
    }
  }

  /**
   * Check if field should be visible based on conditional logic
   */
  isFieldVisible(field: DynamicFormField, formValues: any): boolean {
    if (!field.conditional) return true;

    const { field: conditionField, value: conditionValue, operator = 'equals' } = field.conditional;
    const fieldValue = formValues[conditionField];

    switch (operator) {
      case 'equals':
        return fieldValue === conditionValue;
      case 'not_equals':
        return fieldValue !== conditionValue;
      case 'contains':
        return Array.isArray(fieldValue) ? fieldValue.includes(conditionValue) : 
               String(fieldValue).includes(String(conditionValue));
      case 'not_contains':
        return Array.isArray(fieldValue) ? !fieldValue.includes(conditionValue) : 
               !String(fieldValue).includes(String(conditionValue));
      default:
        return true;
    }
  }

  /**
   * Validate form values against schema
   */
  validateFormValues(schema: DynamicFormSchema, values: any): { [key: string]: string[] } {
    const errors: { [key: string]: string[] } = {};

    schema.fields.forEach(field => {
      const fieldErrors: string[] = [];
      const value = values[field.name];

      if (field.required && (value === null || value === undefined || value === '')) {
        fieldErrors.push(`${field.label} is required`);
      }

      if (value && field.validation) {
        const validation = field.validation;

        if (validation.pattern && !new RegExp(validation.pattern).test(String(value))) {
          fieldErrors.push(`${field.label} format is invalid`);
        }

        if (validation.min !== undefined && Number(value) < validation.min) {
          fieldErrors.push(`${field.label} must be at least ${validation.min}`);
        }

        if (validation.max !== undefined && Number(value) > validation.max) {
          fieldErrors.push(`${field.label} must be at most ${validation.max}`);
        }

        if (validation.minLength !== undefined && String(value).length < validation.minLength) {
          fieldErrors.push(`${field.label} must be at least ${validation.minLength} characters`);
        }

        if (validation.maxLength !== undefined && String(value).length > validation.maxLength) {
          fieldErrors.push(`${field.label} must be at most ${validation.maxLength} characters`);
        }

        if (validation.enum && !validation.enum.includes(value)) {
          fieldErrors.push(`${field.label} must be one of: ${validation.enum.join(', ')}`);
        }
      }

      if (fieldErrors.length > 0) {
        errors[field.name] = fieldErrors;
      }
    });

    return errors;
  }
}
