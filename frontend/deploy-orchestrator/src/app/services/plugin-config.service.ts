import { Injectable } from '@angular/core';
import { Observable, forkJoin, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { PluginConfigSchema, PluginConfigField } from '../components/plugin-config-form/plugin-config-form.component';
import { SecretsService } from './secrets.service';

export interface PluginVariable {
  name: string;
  type: string;
  description: string;
  required: boolean;
  sensitive?: boolean;
  defaultValue?: any;
  validation?: {
    pattern?: string;
    minimum?: number;
    maximum?: number;
    minLength?: number;
    maxLength?: number;
    enum?: string[];
  };
}

export interface PluginMetadata {
  name: string;
  version: string;
  description: string;
  variables?: PluginVariable[];
  configSchema?: any;
}

@Injectable({
  providedIn: 'root'
})
export class PluginConfigService {

  constructor(private secretService: SecretsService) {}

  /**
   * Convert plugin metadata to form schema with secrets support
   */
  convertPluginToFormSchema(plugin: PluginMetadata): Observable<PluginConfigSchema> {
    const schema: PluginConfigSchema = {
      title: `Configure ${plugin.name}`,
      description: plugin.description,
      fields: []
    };

    // Convert from variables if available
    if (plugin.variables && plugin.variables.length > 0) {
      schema.fields = plugin.variables.map(variable => this.convertVariableToField(variable));
    }
    // Convert from configSchema if available
    else if (plugin.configSchema?.properties) {
      schema.fields = this.convertSchemaPropertiesToFields(plugin.configSchema);
    }

    // Enhance with secrets for sensitive fields
    return this.enhanceSchemaWithSecrets(schema);
  }

  /**
   * Convert plugin metadata to form schema (synchronous version)
   */
  convertPluginToFormSchemaSync(plugin: PluginMetadata): PluginConfigSchema {
    const schema: PluginConfigSchema = {
      title: `Configure ${plugin.name}`,
      description: plugin.description,
      fields: []
    };

    // Convert from variables if available
    if (plugin.variables && plugin.variables.length > 0) {
      schema.fields = plugin.variables.map(variable => this.convertVariableToField(variable));
    }
    // Convert from configSchema if available
    else if (plugin.configSchema?.properties) {
      schema.fields = this.convertSchemaPropertiesToFields(plugin.configSchema);
    }

    return schema;
  }

  /**
   * Convert plugin variable to form field
   */
  private convertVariableToField(variable: PluginVariable): PluginConfigField {
    const field: PluginConfigField = {
      name: variable.name,
      type: this.mapVariableTypeToFieldType(variable.type),
      label: this.formatLabel(variable.name),
      description: variable.description,
      required: variable.required,
      sensitive: variable.sensitive,
      defaultValue: variable.defaultValue
    };

    // Add validation rules
    if (variable.validation) {
      field.validation = {
        pattern: variable.validation.pattern,
        min: variable.validation.minimum,
        max: variable.validation.maximum,
        minLength: variable.validation.minLength,
        maxLength: variable.validation.maxLength
      };

      // Convert enum to select options
      if (variable.validation.enum) {
        field.type = 'select';
        field.options = variable.validation.enum.map(value => ({
          value: value,
          label: this.formatLabel(value)
        }));
      }
    }

    // Add specific configurations based on variable name patterns
    this.addFieldSpecificConfig(field);

    return field;
  }

  /**
   * Convert JSON schema properties to form fields
   */
  private convertSchemaPropertiesToFields(schema: any): PluginConfigField[] {
    const fields: PluginConfigField[] = [];
    const properties = schema.properties || {};
    const required = schema.required || [];

    Object.keys(properties).forEach(key => {
      const property = properties[key];
      const field: PluginConfigField = {
        name: key,
        type: this.mapSchemaTypeToFieldType(property.type, property),
        label: property.title || this.formatLabel(key),
        description: property.description,
        required: required.includes(key),
        sensitive: property.sensitive || false,
        defaultValue: property.default
      };

      // Add validation from schema
      if (property.pattern) {
        field.validation = { ...field.validation, pattern: property.pattern };
      }
      if (property.minimum !== undefined) {
        field.validation = { ...field.validation, min: property.minimum };
      }
      if (property.maximum !== undefined) {
        field.validation = { ...field.validation, max: property.maximum };
      }
      if (property.minLength !== undefined) {
        field.validation = { ...field.validation, minLength: property.minLength };
      }
      if (property.maxLength !== undefined) {
        field.validation = { ...field.validation, maxLength: property.maxLength };
      }

      // Handle enum values
      if (property.enum) {
        field.type = 'select';
        field.options = property.enum.map((value: any) => ({
          value: value,
          label: property.enumLabels?.[property.enum.indexOf(value)] || this.formatLabel(value)
        }));
      }

      // Add examples as placeholder
      if (property.examples && property.examples.length > 0) {
        field.placeholder = `e.g., ${property.examples[0]}`;
      }

      this.addFieldSpecificConfig(field);
      fields.push(field);
    });

    return fields;
  }

  /**
   * Map variable type to form field type
   */
  private mapVariableTypeToFieldType(type: string): PluginConfigField['type'] {
    switch (type.toLowerCase()) {
      case 'string':
        return 'string';
      case 'number':
      case 'integer':
        return 'number';
      case 'boolean':
        return 'boolean';
      case 'password':
        return 'password';
      case 'email':
        return 'email';
      case 'url':
        return 'url';
      case 'textarea':
        return 'textarea';
      default:
        return 'string';
    }
  }

  /**
   * Map JSON schema type to form field type
   */
  private mapSchemaTypeToFieldType(type: string, property: any): PluginConfigField['type'] {
    if (property.format) {
      switch (property.format) {
        case 'email':
          return 'email';
        case 'uri':
        case 'url':
          return 'url';
        case 'password':
          return 'password';
        case 'textarea':
          return 'textarea';
      }
    }

    switch (type) {
      case 'string':
        return 'string';
      case 'number':
      case 'integer':
        return 'number';
      case 'boolean':
        return 'boolean';
      default:
        return 'string';
    }
  }

  /**
   * Add field-specific configuration based on field name patterns
   */
  private addFieldSpecificConfig(field: PluginConfigField): void {
    const name = field.name.toLowerCase();

    // Password fields
    if (name.includes('password') || name.includes('secret') || name.includes('token') || name.includes('key')) {
      field.type = 'password';
      field.sensitive = true;
    }

    // Email fields
    if (name.includes('email') || name.includes('mail')) {
      field.type = 'email';
    }

    // URL fields
    if (name.includes('url') || name.includes('endpoint') || name.includes('uri')) {
      field.type = 'url';
      field.placeholder = field.placeholder || 'https://example.com';
    }

    // Textarea fields
    if (name.includes('description') || name.includes('notes') || name.includes('config') || name.includes('script')) {
      field.type = 'textarea';
    }

    // Add help text for common fields
    this.addHelpText(field);
  }

  /**
   * Add helpful text for common field patterns
   */
  private addHelpText(field: PluginConfigField): void {
    const name = field.name.toLowerCase();

    if (name.includes('timeout')) {
      field.helpText = 'Specify timeout duration (e.g., 300s, 5m, 1h)';
    } else if (name.includes('api') && name.includes('url')) {
      field.helpText = 'Full API endpoint URL including protocol (https://)';
    } else if (name.includes('namespace') || name.includes('project')) {
      field.helpText = 'Must be a valid Kubernetes namespace name (lowercase, alphanumeric, hyphens)';
    } else if (name.includes('repo') && name.includes('url')) {
      field.helpText = 'Git repository URL (HTTPS or SSH)';
    } else if (name.includes('path')) {
      field.helpText = 'Relative path from repository root';
    } else if (name.includes('tag') || name.includes('version')) {
      field.helpText = 'Semantic version or tag name (e.g., v1.2.3, latest)';
    }
  }

  /**
   * Format field name to human-readable label
   */
  private formatLabel(name: string): string {
    return name
      .replace(/[_-]/g, ' ')
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      .replace(/\b\w/g, l => l.toUpperCase())
      .trim();
  }

  /**
   * Enhance schema with secrets for sensitive fields
   */
  private enhanceSchemaWithSecrets(schema: PluginConfigSchema): Observable<PluginConfigSchema> {
    const sensitiveFields = schema.fields.filter(field => field.sensitive || field.type === 'password');

    if (sensitiveFields.length === 0) {
      return of(schema);
    }

    // Load available secrets
    return this.secretService.getSecrets().pipe(
      map(secrets => {
        // Add secret options to sensitive fields
        sensitiveFields.forEach(field => {
          // Convert to select field with manual input option
          field.type = 'select';
          field.options = [
            { value: '', label: 'Enter manually' },
            { value: '__secret_divider__', label: '--- Available Secrets ---' },
            ...((secrets as any)?.secrets || []).map((secret: any) => ({
              value: `{{secret:${secret.name}}}`,
              label: `🔐 ${secret.name} (${secret.description || 'Secret'})`
            }))
          ];
          field.helpText = 'Select an existing secret or choose "Enter manually" to input the value directly';
        });

        return schema;
      }),
      catchError(error => {
        console.warn('Failed to load secrets, using basic schema:', error);
        return of(schema);
      })
    );
  }

  /**
   * Create form schema for Helm OpenShift plugin with secrets support
   */
  createHelmOpenShiftSchema(): Observable<PluginConfigSchema> {
    const schema = this.createHelmOpenShiftSchemaSync();
    return this.enhanceSchemaWithSecrets(schema);
  }

  /**
   * Create form schema for Helm OpenShift plugin (synchronous)
   */
  createHelmOpenShiftSchemaSync(): PluginConfigSchema {
    return {
      title: 'Helm OpenShift Deploy Configuration',
      description: 'Configure Helm deployment to OpenShift clusters with Bitbucket integration',
      fields: [
        {
          name: 'openshift_api_url',
          type: 'url',
          label: 'OpenShift API URL',
          description: 'OpenShift cluster API endpoint',
          required: true,
          placeholder: 'https://api.cluster.example.com:6443',
          helpText: 'Full API endpoint URL including protocol and port',
          validation: {
            pattern: '^https?://.*'
          }
        },
        {
          name: 'openshift_project',
          type: 'string',
          label: 'OpenShift Project',
          description: 'Target OpenShift project/namespace',
          required: true,
          placeholder: 'my-app-dev',
          helpText: 'Must be a valid Kubernetes namespace name',
          validation: {
            pattern: '^[a-z0-9]([-a-z0-9]*[a-z0-9])?$'
          }
        },
        {
          name: 'username',
          type: 'string',
          label: 'OpenShift Username',
          description: 'Username for OpenShift authentication',
          required: true,
          placeholder: 'developer'
        },
        {
          name: 'password',
          type: 'password',
          label: 'OpenShift Password',
          description: 'Password for OpenShift authentication',
          required: true,
          sensitive: true
        },
        {
          name: 'bitbucket_repo_url',
          type: 'url',
          label: 'Bitbucket Repository URL',
          description: 'URL of the Bitbucket repository containing Helm charts',
          required: true,
          placeholder: 'https://bitbucket.org/myorg/helm-charts.git',
          helpText: 'Git repository URL ending with .git',
          validation: {
            pattern: '^https://.*\\.git$'
          }
        },
        {
          name: 'chart_path',
          type: 'string',
          label: 'Chart Path',
          description: 'Path to Helm chart from repository root',
          required: true,
          placeholder: 'charts/my-application',
          helpText: 'Relative path from repository root to chart directory'
        },
        {
          name: 'values_path',
          type: 'string',
          label: 'Values File Path',
          description: 'Path to values file for environment-specific configuration',
          required: true,
          placeholder: 'values-dev.yaml',
          helpText: 'Path to environment-specific values file'
        },
        {
          name: 'release_name',
          type: 'string',
          label: 'Helm Release Name',
          description: 'Name for the Helm release (optional, defaults to chart name)',
          required: false,
          placeholder: 'my-application',
          validation: {
            pattern: '^[a-z0-9]([-a-z0-9]*[a-z0-9])?$'
          }
        },
        {
          name: 'helm_timeout',
          type: 'string',
          label: 'Helm Timeout',
          description: 'Timeout for Helm operations',
          required: false,
          defaultValue: '300s',
          placeholder: '300s',
          helpText: 'Duration with unit suffix (s, m, h)',
          validation: {
            pattern: '^[0-9]+(s|m|h)$'
          }
        }
      ]
    };
  }

  /**
   * Validate configuration against schema
   */
  validateConfiguration(config: any, schema: PluginConfigSchema): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    schema.fields.forEach(field => {
      const value = config[field.name];

      // Check required fields
      if (field.required && (value === undefined || value === null || value === '')) {
        errors.push(`${field.label} is required`);
        return;
      }

      // Skip validation for empty optional fields
      if (!field.required && (value === undefined || value === null || value === '')) {
        return;
      }

      // Type validation
      if (field.type === 'number' && isNaN(Number(value))) {
        errors.push(`${field.label} must be a valid number`);
      }

      if (field.type === 'boolean' && typeof value !== 'boolean') {
        errors.push(`${field.label} must be a boolean value`);
      }

      // Pattern validation
      if (field.validation?.pattern && typeof value === 'string') {
        const regex = new RegExp(field.validation.pattern);
        if (!regex.test(value)) {
          errors.push(`${field.label} format is invalid`);
        }
      }

      // Range validation
      if (field.type === 'number' && typeof value === 'number') {
        if (field.validation?.min !== undefined && value < field.validation.min) {
          errors.push(`${field.label} must be at least ${field.validation.min}`);
        }
        if (field.validation?.max !== undefined && value > field.validation.max) {
          errors.push(`${field.label} must be at most ${field.validation.max}`);
        }
      }

      // Length validation
      if (typeof value === 'string') {
        if (field.validation?.minLength !== undefined && value.length < field.validation.minLength) {
          errors.push(`${field.label} must be at least ${field.validation.minLength} characters`);
        }
        if (field.validation?.maxLength !== undefined && value.length > field.validation.maxLength) {
          errors.push(`${field.label} must be at most ${field.validation.maxLength} characters`);
        }
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
