import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { DeploymentManagementService, BulkDeploymentRequest, DeploymentResponse } from './deployment-management.service';
import { environment } from '../../environments/environment';

describe('DeploymentManagementService', () => {
  let service: DeploymentManagementService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [DeploymentManagementService]
    });
    service = TestBed.inject(DeploymentManagementService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('createDeployment', () => {
    it('should create a deployment successfully', () => {
      const mockRequest: BulkDeploymentRequest = {
        applicationIds: ['app1'],
        projectId: 'project1',
        environmentId: 'env1',
        version: 'v1.0.0',
        workflowId: 'workflow1',
        providerType: 'helm'
      };

      const mockResponse: DeploymentResponse = {
        deploymentId: 'deployment1',
        workflowExecution: 'execution1',
        status: 'pending',
        applications: [{
          id: 'app1',
          name: 'Test App',
          version: 'v1.0.0',
          status: 'pending'
        }],
        createdAt: new Date().toISOString()
      };

      service.createDeployment(mockRequest).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/application-service/api/v1/deployments`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(mockRequest);
      req.flush(mockResponse);
    });

    it('should handle deployment creation error', () => {
      const mockRequest: BulkDeploymentRequest = {
        applicationIds: ['app1'],
        projectId: 'project1',
        environmentId: 'env1',
        version: 'v1.0.0',
        workflowId: 'workflow1',
        providerType: 'helm'
      };

      service.createDeployment(mockRequest).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.status).toBe(500);
        }
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/application-service/api/v1/deployments`);
      req.flush('Internal Server Error', { status: 500, statusText: 'Internal Server Error' });
    });
  });

  describe('getEnvironmentVersions', () => {
    it('should get environment versions successfully', () => {
      const projectId = 'project1';
      const environmentIds = ['env1', 'env2'];
      const mockResponse = {
        versions: [
          {
            id: 'version1',
            projectId: 'project1',
            environmentId: 'env1',
            version: 'v1.0.0',
            status: 'active',
            deployedAt: new Date().toISOString(),
            deployedBy: 'user1',
            deploymentId: 'deployment1'
          }
        ],
        total: 1
      };

      service.getEnvironmentVersions(projectId, environmentIds).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(req => 
        req.url.includes('/deployments/environment-versions') &&
        req.params.get('projectId') === projectId &&
        req.params.getAll('environmentId')?.length === 2
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('getVersionMatrix', () => {
    it('should get version matrix successfully', () => {
      const projectId = 'project1';
      const mockResponse = {
        matrix: {
          'app1': {
            'env1': 'v1.0.0',
            'env2': 'v0.9.0'
          },
          'app2': {
            'env1': 'v2.0.0',
            'env2': 'v1.9.0'
          }
        }
      };

      service.getVersionMatrix(projectId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(req => 
        req.url.includes('/deployments/version-matrix') &&
        req.params.get('projectId') === projectId
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('updateDeploymentStatus', () => {
    it('should update deployment status successfully', () => {
      const deploymentId = 'deployment1';
      const status = 'succeeded';
      const mockResponse = {
        message: 'Deployment status updated successfully',
        status: 'succeeded'
      };

      service.updateDeploymentStatus(deploymentId, status).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/application-service/api/v1/deployments/${deploymentId}/status`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual({ status });
      req.flush(mockResponse);
    });
  });

  describe('utility methods', () => {
    it('should format status correctly', () => {
      expect(service.formatStatus('pending')).toBe('Pending');
      expect(service.formatStatus('in_progress')).toBe('In Progress');
      expect(service.formatStatus('succeeded')).toBe('Succeeded');
      expect(service.formatStatus('failed')).toBe('Failed');
      expect(service.formatStatus('rolled_back')).toBe('Rolled Back');
      expect(service.formatStatus('unknown')).toBe('unknown');
    });

    it('should return correct status color classes', () => {
      expect(service.getStatusColorClass('pending')).toBe('text-yellow-600 bg-yellow-100');
      expect(service.getStatusColorClass('in_progress')).toBe('text-blue-600 bg-blue-100');
      expect(service.getStatusColorClass('succeeded')).toBe('text-green-600 bg-green-100');
      expect(service.getStatusColorClass('failed')).toBe('text-red-600 bg-red-100');
      expect(service.getStatusColorClass('rolled_back')).toBe('text-gray-600 bg-gray-100');
      expect(service.getStatusColorClass('unknown')).toBe('text-gray-600 bg-gray-100');
    });

    it('should check deployment status correctly', () => {
      expect(service.isDeploymentInProgress('pending')).toBe(true);
      expect(service.isDeploymentInProgress('in_progress')).toBe(true);
      expect(service.isDeploymentInProgress('succeeded')).toBe(false);

      expect(service.isDeploymentSuccessful('succeeded')).toBe(true);
      expect(service.isDeploymentSuccessful('failed')).toBe(false);

      expect(service.isDeploymentFailed('failed')).toBe(true);
      expect(service.isDeploymentFailed('succeeded')).toBe(false);
    });

    it('should return deployment strategies', () => {
      const strategies = service.getDeploymentStrategies();
      expect(strategies).toContain('rolling_update');
      expect(strategies).toContain('blue_green');
      expect(strategies).toContain('canary');
      expect(strategies).toContain('recreate');
    });

    it('should return deployment statuses', () => {
      const statuses = service.getDeploymentStatuses();
      expect(statuses).toContain('pending');
      expect(statuses).toContain('in_progress');
      expect(statuses).toContain('succeeded');
      expect(statuses).toContain('failed');
      expect(statuses).toContain('rolled_back');
    });
  });
});
