import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { 
  ProjectAuditLog, 
  ProjectAuditFilter, 
  ProjectAuditResponse, 
  ProjectAuditSummary 
} from '../models/project-audit.model';

@Injectable({
  providedIn: 'root'
})
export class ProjectAuditService {
  private apiUrl = `${environment.apiUrl}/audit-service/v1`;

  constructor(private http: HttpClient) {}

  // Project Audit Logs
  getProjectAuditLogs(projectId: string, filter?: ProjectAuditFilter): Observable<ProjectAuditResponse> {
    let params = new HttpParams();
    
    if (filter) {
      if (filter.startDate) params = params.set('startDate', filter.startDate);
      if (filter.endDate) params = params.set('endDate', filter.endDate);
      if (filter.userId) params = params.set('userId', filter.userId);
      if (filter.action) params = params.set('action', filter.action);
      if (filter.resource) params = params.set('resource', filter.resource);
      if (filter.success !== undefined) params = params.set('success', filter.success.toString());
      if (filter.searchTerm) params = params.set('searchTerm', filter.searchTerm);
      if (filter.page) params = params.set('page', filter.page.toString());
      if (filter.limit) params = params.set('limit', filter.limit.toString());
    }

    return this.http.get<ProjectAuditResponse>(`${this.apiUrl}/projects/${projectId}/audit-logs`, { params });
  }

  getAuditLog(projectId: string, logId: string): Observable<ProjectAuditLog> {
    return this.http.get<ProjectAuditLog>(`${this.apiUrl}/projects/${projectId}/audit-logs/${logId}`);
  }

  // Project Audit Summary
  getProjectAuditSummary(projectId: string, startDate?: string, endDate?: string): Observable<ProjectAuditSummary> {
    let params = new HttpParams();
    if (startDate) params = params.set('startDate', startDate);
    if (endDate) params = params.set('endDate', endDate);

    return this.http.get<ProjectAuditSummary>(`${this.apiUrl}/projects/${projectId}/audit-summary`, { params });
  }

  // Export Audit Logs
  exportProjectAuditLogs(projectId: string, filter?: ProjectAuditFilter): Observable<Blob> {
    let params = new HttpParams();
    
    if (filter) {
      if (filter.startDate) params = params.set('startDate', filter.startDate);
      if (filter.endDate) params = params.set('endDate', filter.endDate);
      if (filter.userId) params = params.set('userId', filter.userId);
      if (filter.action) params = params.set('action', filter.action);
      if (filter.resource) params = params.set('resource', filter.resource);
      if (filter.success !== undefined) params = params.set('success', filter.success.toString());
      if (filter.searchTerm) params = params.set('searchTerm', filter.searchTerm);
    }

    return this.http.get(`${this.apiUrl}/projects/${projectId}/audit-logs/export`, { 
      params, 
      responseType: 'blob' 
    });
  }

  // Audit Log Search
  searchAuditLogs(projectId: string, query: string, limit?: number): Observable<ProjectAuditLog[]> {
    let params = new HttpParams().set('q', query);
    if (limit) params = params.set('limit', limit.toString());

    return this.http.get<ProjectAuditLog[]>(`${this.apiUrl}/projects/${projectId}/audit-logs/search`, { params });
  }

  // User Activity
  getUserActivity(projectId: string, userId: string, limit?: number): Observable<ProjectAuditLog[]> {
    let params = new HttpParams();
    if (limit) params = params.set('limit', limit.toString());

    return this.http.get<ProjectAuditLog[]>(`${this.apiUrl}/projects/${projectId}/users/${userId}/activity`, { params });
  }

  // Resource Activity
  getResourceActivity(projectId: string, resourceType: string, resourceId: string): Observable<ProjectAuditLog[]> {
    return this.http.get<ProjectAuditLog[]>(`${this.apiUrl}/projects/${projectId}/resources/${resourceType}/${resourceId}/activity`);
  }

  // Audit Retention
  getAuditRetentionPolicy(projectId: string): Observable<{ retentionDays: number; autoCleanup: boolean }> {
    return this.http.get<{ retentionDays: number; autoCleanup: boolean }>(`${this.apiUrl}/projects/${projectId}/audit-retention`);
  }

  updateAuditRetentionPolicy(projectId: string, retentionDays: number, autoCleanup: boolean): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/projects/${projectId}/audit-retention`, { retentionDays, autoCleanup });
  }

  // Manual audit log creation (for custom events)
  createAuditLog(projectId: string, log: Partial<ProjectAuditLog>): Observable<ProjectAuditLog> {
    return this.http.post<ProjectAuditLog>(`${this.apiUrl}/projects/${projectId}/audit-logs`, log);
  }
}
