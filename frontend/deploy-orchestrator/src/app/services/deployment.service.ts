import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { environment } from '../../environments/environment';
import { Application, DeploymentStrategy, DeploymentStatus } from '../models/application.model';
import { EnvironmentConfig } from './environment.service';

// Re-export for convenience
export { DeploymentStrategy, DeploymentStatus } from '../models/application.model';

// Core Deployment Interfaces
export interface Deployment {
  id: string;
  applicationId: string;
  environmentId: string;
  version: string;
  status: DeploymentStatus;
  strategy: DeploymentStrategy;
  description?: string;
  rollbackOnFailure: boolean;
  runHealthChecks: boolean;
  createdAt: string;
  updatedAt: string;
  deployedAt?: string;
  startedAt?: string;
  completedAt?: string;
  application?: Application;
  environment?: EnvironmentConfig;
  logs?: DeploymentLog[];
  parameters?: { [key: string]: any };
  name?: string;
  applicationName?: string;
  environmentName?: string;
  workflowId?: string;
  configuration?: { [key: string]: any };
  metrics?: {
    healthScore?: number;
    [key: string]: any;
  };
  metadata?: { [key: string]: any };
}

export interface BatchDeployment {
  id: string;
  name: string;
  description?: string;
  environmentId: string;
  applicationIds: string[];
  strategy: BatchDeploymentStrategy;
  status: BatchDeploymentStatus;
  rollbackOnFailure: boolean;
  createdAt: string;
  updatedAt: string;
  deployments: Deployment[];
}

export interface MultiAppDeployment {
  id: string;
  name: string;
  description?: string;
  environmentId: string;
  config: MultiAppDeploymentConfig;
  status: string;
  createdAt: string;
  updatedAt: string;
  deployments: Deployment[];
}

export interface DeploymentStatistics {
  total: number;
  successful: number;
  failed: number;
  pending: number;
  running: number;
  successRate: number;
  averageDeploymentTime: number;
  deploymentsToday: number;
  deploymentsThisWeek: number;
  deploymentsThisMonth: number;
  successfulDeployments: number;
  failedDeployments: number;
}

export interface DeploymentMatrix {
  applications: Application[];
  environments: EnvironmentConfig[];
  matrix: { [appId: string]: { [envId: string]: Deployment | null } };
}

export interface PromotionHistory {
  id: string;
  sourceDeploymentId: string;
  targetEnvironmentId: string;
  status: PromotionStatus;
  description?: string;
  runTests: boolean;
  createdAt: string;
  updatedAt: string;
  sourceDeployment?: Deployment;
  targetEnvironment?: EnvironmentConfig;
}

export interface DeploymentAuditLog {
  id: string;
  deploymentId: string;
  action: string;
  user: string;
  timestamp: string;
  details?: { [key: string]: any };
}

export interface DeploymentTemplate {
  id: string;
  name: string;
  description?: string;
  applicationId: string;
  environmentId: string;
  strategy: DeploymentStrategy;
  parameters: { [key: string]: any };
  createdAt: string;
  updatedAt: string;
}

export interface DeploymentLog {
  id: string;
  deploymentId: string;
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug' | 'INFO' | 'WARN' | 'ERROR' | 'DEBUG';
  message: string;
  source?: string;
  metadata?: { [key: string]: any };
}

export interface DeploymentEvent {
  id: string;
  deploymentId: string;
  type: string;
  timestamp: string;
  data: { [key: string]: any };
}

// Request Interfaces
export interface CreateDeploymentRequest {
  applicationId: string;
  environmentId: string;
  version?: string;
  strategy: DeploymentStrategy;
  description?: string;
  rollbackOnFailure?: boolean;
  runHealthChecks?: boolean;
  parameters?: { [key: string]: any };
  name?: string;
  workflowId?: string;
  configuration?: { [key: string]: any };
}

export interface CreateBatchDeploymentRequest {
  name: string;
  description?: string;
  environmentId: string;
  applicationIds: string[];
  strategy: BatchDeploymentStrategy;
  rollbackOnFailure?: boolean;
  projectId?: string;
  deployments?: {
    applicationId: string;
    version?: string;
    strategy?: DeploymentStrategy;
  }[];
}

export interface CreateMultiAppDeploymentRequest {
  name: string;
  description?: string;
  environmentId: string;
  config: MultiAppDeploymentConfig;
  projectId?: string;
  applications?: {
    applicationId: string;
    version?: string;
    strategy?: DeploymentStrategy;
  }[];
}

export interface PromotionRequest {
  sourceDeploymentId: string;
  targetEnvironmentId: string;
  description?: string;
  runTests?: boolean;
}

// Filter and Configuration Interfaces
export interface DeploymentFilter {
  applicationId?: string;
  environmentId?: string;
  status?: string[];
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  projectId?: string;
  limit?: number;
}

export interface MultiAppDeploymentConfig {
  applications: {
    applicationId: string;
    version?: string;
    strategy: DeploymentStrategy;
  }[];
  coordination: {
    mode: 'sequential' | 'parallel';
    rollbackOnFailure: boolean;
  };
}

// Enums
export enum BatchDeploymentStrategy {
  SEQUENTIAL = 'sequential',
  PARALLEL = 'parallel',
  ROLLING = 'rolling',
  ROLLING_UPDATE = 'rolling_update',
  BLUE_GREEN = 'blue_green',
  CANARY = 'canary',
  RECREATE = 'recreate'
}

export enum BatchDeploymentStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum PromotionStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

@Injectable({
  providedIn: 'root'
})
export class DeploymentService {
  private apiUrl = `${environment.apiUrl}`;

  constructor(private http: HttpClient) {}

  // Deployment CRUD operations
  getDeployments(filter?: DeploymentFilter): Observable<Deployment[]> {
    let params = new HttpParams();
    if (filter) {
      if (filter.applicationId) params = params.set('applicationId', filter.applicationId);
      if (filter.environmentId) params = params.set('environmentId', filter.environmentId);
      if (filter.status && filter.status.length > 0) params = params.set('status', filter.status.join(','));
      if (filter.dateFrom) params = params.set('dateFrom', filter.dateFrom);
      if (filter.dateTo) params = params.set('dateTo', filter.dateTo);
      if (filter.search) params = params.set('search', filter.search);
    }
    return this.http.get<Deployment[]>(`${this.apiUrl}/deployments`, { params });
  }

  getDeployment(id: string): Observable<Deployment> {
    return this.http.get<Deployment>(`${this.apiUrl}/deployments/${id}`);
  }

  createDeployment(request: CreateDeploymentRequest): Observable<Deployment> {
    return this.http.post<Deployment>(`${this.apiUrl}/deployments`, request);
  }

  updateDeployment(id: string, updates: Partial<Deployment>): Observable<Deployment> {
    return this.http.put<Deployment>(`${this.apiUrl}/deployments/${id}`, updates);
  }

  deleteDeployment(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/deployments/${id}`);
  }

  // Batch Deployment operations
  getBatchDeployments(): Observable<BatchDeployment[]> {
    return this.http.get<BatchDeployment[]>(`${this.apiUrl}/batch-deployments`);
  }

  createBatchDeployment(request: CreateBatchDeploymentRequest): Observable<BatchDeployment> {
    return this.http.post<BatchDeployment>(`${this.apiUrl}/batch-deployments`, request);
  }

  // Multi-App Deployment operations
  getMultiAppDeployments(): Observable<MultiAppDeployment[]> {
    return this.http.get<MultiAppDeployment[]>(`${this.apiUrl}/multi-app-deployments`);
  }

  createMultiAppDeployment(request: CreateMultiAppDeploymentRequest): Observable<MultiAppDeployment> {
    return this.http.post<MultiAppDeployment>(`${this.apiUrl}/multi-app-deployments`, request);
  }

  // Promotion operations
  promoteDeployment(request: PromotionRequest): Observable<PromotionHistory> {
    return this.http.post<PromotionHistory>(`${this.apiUrl}/promotions`, request);
  }

  getPromotionHistory(deploymentId?: string): Observable<PromotionHistory[]> {
    let params = new HttpParams();
    if (deploymentId) params = params.set('deploymentId', deploymentId);
    return this.http.get<PromotionHistory[]>(`${this.apiUrl}/promotions`, { params });
  }

  // Statistics and Analytics
  getDeploymentStatistics(): Observable<DeploymentStatistics> {
    return this.http.get<DeploymentStatistics>(`${this.apiUrl}/deployments/statistics`);
  }

  getDeploymentMatrix(): Observable<DeploymentMatrix> {
    return this.http.get<DeploymentMatrix>(`${this.apiUrl}/deployments/matrix`);
  }

  // Logs and Events
  getDeploymentLogs(deploymentId: string): Observable<DeploymentLog[]> {
    return this.http.get<DeploymentLog[]>(`${this.apiUrl}/deployments/${deploymentId}/logs`);
  }

  getDeploymentEvents(deploymentId: string): Observable<DeploymentEvent[]> {
    return this.http.get<DeploymentEvent[]>(`${this.apiUrl}/deployments/${deploymentId}/events`);
  }

  // Audit operations
  getAuditLogs(deploymentId?: string): Observable<DeploymentAuditLog[]> {
    let params = new HttpParams();
    if (deploymentId) params = params.set('deploymentId', deploymentId);
    return this.http.get<DeploymentAuditLog[]>(`${this.apiUrl}/deployments/audit`, { params });
  }

  // Control operations
  startDeployment(id: string): Observable<Deployment> {
    return this.http.post<Deployment>(`${this.apiUrl}/deployments/${id}/start`, {});
  }

  stopDeployment(id: string): Observable<Deployment> {
    return this.http.post<Deployment>(`${this.apiUrl}/deployments/${id}/stop`, {});
  }

  rollbackDeployment(id: string): Observable<Deployment> {
    return this.http.post<Deployment>(`${this.apiUrl}/deployments/${id}/rollback`, {});
  }

  retryDeployment(id: string): Observable<Deployment> {
    return this.http.post<Deployment>(`${this.apiUrl}/deployments/${id}/retry`, {});
  }

  cancelDeployment(id: string): Observable<Deployment> {
    return this.http.post<Deployment>(`${this.apiUrl}/deployments/${id}/cancel`, {});
  }

  // Template operations
  getDeploymentTemplates(): Observable<DeploymentTemplate[]> {
    return this.http.get<DeploymentTemplate[]>(`${this.apiUrl}/deployment-templates`);
  }

  createDeploymentTemplate(template: Omit<DeploymentTemplate, 'id' | 'createdAt' | 'updatedAt'>): Observable<DeploymentTemplate> {
    return this.http.post<DeploymentTemplate>(`${this.apiUrl}/deployment-templates`, template);
  }
}