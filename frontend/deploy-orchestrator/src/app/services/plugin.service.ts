import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, interval } from 'rxjs';
import { map, switchMap, catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import {
  Plugin,
  PluginInstallRequest,
  PluginConfigUpdateRequest,
  PluginStatus,
  PluginLog,
  PluginTemplate,
  ProviderInfo,
  PluginMarketplace,
  MarketplacePlugin,
  PluginMetrics,
  HotReloadEvent,
  PluginValidationResult,
  PluginDeploymentRequest,
  PluginDeploymentResult,
  PluginHealth,
  PluginCapability,
  PluginDependency,
  PluginUpdate,
  PluginBackup,
  PluginRestoreRequest
} from '../models/plugin.model';

@Injectable({
  providedIn: 'root'
})
export class PluginService {
  private baseUrl = `${environment.apiUrl}`;
  private pluginsSubject = new BehaviorSubject<Plugin[]>([]);
  private hotReloadEventsSubject = new BehaviorSubject<HotReloadEvent[]>([]);

  public plugins$ = this.pluginsSubject.asObservable();
  public hotReloadEvents$ = this.hotReloadEventsSubject.asObservable();

  constructor(private http: HttpClient) {
    // Auto-refresh plugins every 30 seconds
    interval(30000).pipe(
      switchMap(() => this.getPlugins()),
      catchError(error => {
        console.error('Failed to auto-refresh plugins:', error);
        return [];
      })
    ).subscribe(plugins => {
      this.pluginsSubject.next(plugins);
    });
  }

  // Plugin Management
  getPlugins(enabled?: boolean, type?: string): Observable<Plugin[]> {
    let params = new HttpParams();
    if (enabled !== undefined) {
      params = params.set('enabled', enabled.toString());
    }
    if (type) {
      params = params.set('type', type);
    }

    return this.http.get<{ plugins: Plugin[], total: number }>(`${this.baseUrl}/plugins`, { params })
      .pipe(map(response => response.plugins));
  }

  getPlugin(name: string): Observable<Plugin> {
    return this.http.get<Plugin>(`${this.baseUrl}/plugins/${name}`);
  }

  installPlugin(request: PluginInstallRequest): Observable<Plugin> {
    return this.http.post<Plugin>(`${this.baseUrl}/plugins`, request);
  }

  uninstallPlugin(name: string): Observable<{ message: string, plugin: string }> {
    return this.http.delete<{ message: string, plugin: string }>(`${this.baseUrl}/plugins/${name}`);
  }

  updatePluginConfig(name: string, request: PluginConfigUpdateRequest): Observable<Plugin> {
    return this.http.put<Plugin>(`${this.baseUrl}/plugins/${name}/config`, request);
  }

  enablePlugin(name: string): Observable<Plugin> {
    return this.http.post<Plugin>(`${this.baseUrl}/plugins/${name}/enable`, {});
  }

  disablePlugin(name: string): Observable<Plugin> {
    return this.http.post<Plugin>(`${this.baseUrl}/plugins/${name}/disable`, {});
  }

  reloadPlugin(name: string): Observable<Plugin> {
    return this.http.post<Plugin>(`${this.baseUrl}/plugins/${name}/reload`, {});
  }

  // Plugin Status and Health
  getPluginStatus(name: string): Observable<PluginStatus> {
    return this.http.get<PluginStatus>(`${this.baseUrl}/plugins/${name}/status`);
  }

  getPluginHealth(name: string): Observable<PluginHealth> {
    return this.http.get<PluginHealth>(`${this.baseUrl}/plugins/${name}/health`);
  }

  getPluginMetrics(name: string): Observable<PluginMetrics> {
    return this.http.get<PluginMetrics>(`${this.baseUrl}/plugins/${name}/metrics`);
  }

  // Plugin Logs
  getPluginLogs(name: string, lines: number = 100, follow: boolean = false): Observable<{ plugin: string, lines: number, follow: boolean, logs: PluginLog[] }> {
    let params = new HttpParams()
      .set('lines', lines.toString())
      .set('follow', follow.toString());

    return this.http.get<{ plugin: string, lines: number, follow: boolean, logs: PluginLog[] }>(`${this.baseUrl}/plugins/${name}/logs`, { params });
  }

  // Plugin Templates
  getPluginTemplates(pluginName?: string): Observable<PluginTemplate[]> {
    let params = new HttpParams();
    if (pluginName) {
      params = params.set('plugin', pluginName);
    }

    return this.http.get<{ templates: PluginTemplate[] }>(`${this.baseUrl}/plugins/templates`, { params })
      .pipe(map(response => response.templates));
  }

  getPluginTemplate(templateId: string): Observable<PluginTemplate> {
    return this.http.get<PluginTemplate>(`${this.baseUrl}/plugins/templates/${templateId}`);
  }

  // Provider Information
  getProviders(): Observable<ProviderInfo[]> {
    return this.http.get<{ providers: ProviderInfo[] }>(`${this.baseUrl}/providers`)
      .pipe(map(response => response.providers));
  }

  getProvider(type: string): Observable<ProviderInfo> {
    return this.http.get<ProviderInfo>(`${this.baseUrl}/providers/${type}`);
  }

  getProviderCapabilities(type: string): Observable<PluginCapability[]> {
    return this.http.get<{ capabilities: PluginCapability[] }>(`${this.baseUrl}/providers/${type}/capabilities`)
      .pipe(map(response => response.capabilities));
  }

  // Plugin Marketplace
  getMarketplace(category?: string, tag?: string, search?: string): Observable<PluginMarketplace> {
    let params = new HttpParams();
    if (category) {
      params = params.set('category', category);
    }
    if (tag) {
      params = params.set('tag', tag);
    }
    if (search) {
      params = params.set('search', search);
    }

    return this.http.get<PluginMarketplace>(`${this.baseUrl}/marketplace`, { params });
  }

  getMarketplacePlugin(name: string): Observable<MarketplacePlugin> {
    return this.http.get<MarketplacePlugin>(`${this.baseUrl}/marketplace/${name}`);
  }

  installFromMarketplace(name: string, config?: any): Observable<Plugin> {
    return this.http.post<Plugin>(`${this.baseUrl}/marketplace/${name}/install`, { config });
  }

  // Plugin Validation
  validatePlugin(source: string): Observable<PluginValidationResult> {
    return this.http.post<PluginValidationResult>(`${this.baseUrl}/plugins/validate`, { source });
  }

  // Plugin Deployment
  deployWithPlugin(request: PluginDeploymentRequest): Observable<PluginDeploymentResult> {
    return this.http.post<PluginDeploymentResult>(`${this.baseUrl}/plugins/deploy`, request);
  }

  // Hot Reload Events
  getHotReloadEvents(pluginName?: string): Observable<HotReloadEvent[]> {
    let params = new HttpParams();
    if (pluginName) {
      params = params.set('plugin', pluginName);
    }

    return this.http.get<{ events: HotReloadEvent[] }>(`${this.baseUrl}/plugins/hot-reload/events`, { params })
      .pipe(map(response => response.events));
  }

  subscribeToHotReloadEvents(): void {
    // In a real implementation, this would use WebSocket or SSE
    interval(5000).pipe(
      switchMap(() => this.getHotReloadEvents()),
      catchError(error => {
        console.error('Failed to get hot reload events:', error);
        return [];
      })
    ).subscribe(events => {
      this.hotReloadEventsSubject.next(events);
    });
  }

  // Plugin Dependencies
  getPluginDependencies(name: string): Observable<PluginDependency[]> {
    return this.http.get<{ dependencies: PluginDependency[] }>(`${this.baseUrl}/plugins/${name}/dependencies`)
      .pipe(map(response => response.dependencies));
  }

  installDependency(pluginName: string, dependencyName: string): Observable<{ success: boolean, message: string }> {
    return this.http.post<{ success: boolean, message: string }>(`${this.baseUrl}/plugins/${pluginName}/dependencies/${dependencyName}/install`, {});
  }

  // Plugin Updates
  checkForUpdates(name?: string): Observable<PluginUpdate[]> {
    let params = new HttpParams();
    if (name) {
      params = params.set('plugin', name);
    }

    return this.http.get<{ updates: PluginUpdate[] }>(`${this.baseUrl}/plugins/updates`, { params })
      .pipe(map(response => response.updates));
  }

  updatePlugin(name: string, version?: string): Observable<Plugin> {
    const body = version ? { version } : {};
    return this.http.post<Plugin>(`${this.baseUrl}/plugins/${name}/update`, body);
  }

  // Plugin Backup and Restore
  getPluginBackups(pluginName?: string): Observable<PluginBackup[]> {
    let params = new HttpParams();
    if (pluginName) {
      params = params.set('plugin', pluginName);
    }

    return this.http.get<{ backups: PluginBackup[] }>(`${this.baseUrl}/plugins/backups`, { params })
      .pipe(map(response => response.backups));
  }

  createPluginBackup(pluginName: string, description?: string): Observable<PluginBackup> {
    return this.http.post<PluginBackup>(`${this.baseUrl}/plugins/${pluginName}/backup`, { description });
  }

  restorePlugin(request: PluginRestoreRequest): Observable<Plugin> {
    return this.http.post<Plugin>(`${this.baseUrl}/plugins/restore`, request);
  }

  deletePluginBackup(backupId: string): Observable<{ success: boolean, message: string }> {
    return this.http.delete<{ success: boolean, message: string }>(`${this.baseUrl}/plugins/backups/${backupId}`);
  }

  // Utility Methods
  refreshPlugins(): void {
    this.getPlugins().subscribe(plugins => {
      this.pluginsSubject.next(plugins);
    });
  }

  getPluginsByType(type: string): Observable<Plugin[]> {
    return this.plugins$.pipe(
      map(plugins => plugins.filter(plugin => plugin.type === type))
    );
  }

  getEnabledPlugins(): Observable<Plugin[]> {
    return this.plugins$.pipe(
      map(plugins => plugins.filter(plugin => plugin.enabled))
    );
  }

  getPluginsByStatus(status: string): Observable<Plugin[]> {
    return this.plugins$.pipe(
      map(plugins => plugins.filter(plugin => plugin.status === status))
    );
  }

  searchPlugins(query: string): Observable<Plugin[]> {
    return this.plugins$.pipe(
      map(plugins => plugins.filter(plugin =>
        plugin.name.toLowerCase().includes(query.toLowerCase()) ||
        plugin.description.toLowerCase().includes(query.toLowerCase()) ||
        plugin.type.toLowerCase().includes(query.toLowerCase())
      ))
    );
  }
}
