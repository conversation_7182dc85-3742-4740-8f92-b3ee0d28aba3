import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { User, Role, Permission, Group } from '../models/user.model';
import { Project } from '../models/project.model';

@Injectable({
  providedIn: 'root'
})
export class AdminService {
  private apiUrl = `${environment.apiV1Url}/admin-service`;
  private apiV1Url = `${environment.apiV1Url}`;

  constructor(private http: HttpClient) { }

  // User Management
  getUsers(): Observable<User[]> {
    return this.http.get<User[]>(`${this.apiUrl}/users`);
  }

  getUser(id: string): Observable<User> {
    return this.http.get<User>(`${this.apiUrl}/users/${id}`);
  }



  // Role Management
  getRoles(): Observable<Role[]> {
    return this.http.get<Role[]>(`${this.apiUrl}/roles`);
  }

  getRole(id: string): Observable<Role> {
    return this.http.get<Role>(`${this.apiUrl}/roles/${id}`);
  }

  createRole(role: any): Observable<Role> {
    return this.http.post<Role>(`${this.apiUrl}/roles`, role);
  }

  updateRole(role: any): Observable<Role> {
    return this.http.put<Role>(`${this.apiUrl}/roles/${role.id}`, role);
  }

  deleteRole(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/roles/${id}`);
  }

  // Group Management
  getGroups(): Observable<Group[]> {
    return this.http.get<Group[]>(`${this.apiUrl}/groups`);
  }

  getGroup(id: string): Observable<Group> {
    return this.http.get<Group>(`${this.apiUrl}/groups/${id}`);
  }



  assignRoleToGroup(groupId: string, roleId: string, projectId?: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/groups/${groupId}/roles`, { roleId, projectId });
  }

  removeRoleFromGroup(groupId: string, roleId: string, projectId?: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/groups/${groupId}/roles/${roleId}${projectId ? `?projectId=${projectId}` : ''}`);
  }

  // Group-Role Mapping for Identity Providers
  getGroupRoleMappings(groupId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/groups/${groupId}/role-mappings`);
  }

  createGroupRoleMapping(groupId: string, roleId: string, projectId?: string): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/groups/${groupId}/role-mappings`, { roleId, projectId });
  }

  deleteGroupRoleMapping(mappingId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/role-mappings/${mappingId}`);
  }

  // Effective Permissions
  getUserEffectivePermissions(userId: string, projectId?: string): Observable<string[]> {
    return this.http.get<string[]>(`${this.apiUrl}/users/${userId}/effective-permissions${projectId ? `?projectId=${projectId}` : ''}`);
  }

  // Group-Project Assignment (NEW MODEL)
  assignGroupToProject(groupId: string, projectId: string): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/group-projects`, { groupId, projectId });
  }

  removeGroupFromProject(groupId: string, projectId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/group-projects/${groupId}/${projectId}`);
  }

  getGroupProjects(groupId: string): Observable<Project[]> {
    return this.http.get<Project[]>(`${this.apiUrl}/groups/${groupId}/projects`);
  }

  getProjectGroups(projectId: string): Observable<Group[]> {
    return this.http.get<Group[]>(`${this.apiUrl}/projects/${projectId}/groups`);
  }

  getUserProjects(userId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/users/${userId}/projects`);
  }

  checkUserProjectAccess(userId: string, projectId: string): Observable<{ hasAccess: boolean }> {
    return this.http.get<{ hasAccess: boolean }>(`${this.apiUrl}/users/${userId}/project-access/${projectId}`);
  }

  getGroupProjectAssignments(params?: any): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/group-projects`, { params });
  }

  bulkAssignGroupsToProject(groupIds: string[], projectId: string): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/group-projects/bulk-groups`, { groupIds, projectId });
  }

  bulkAssignProjectsToGroup(projectIds: string[], groupId: string): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/group-projects/bulk-projects`, { projectIds, groupId });
  }

  getUserProjectMatrix(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/group-projects/matrix`);
  }

  // Permission Management Methods
  getPermissions(): Observable<Permission[]> {
    return this.http.get<Permission[]>(`${this.apiUrl}/permissions`);
  }

  createPermission(permission: Partial<Permission>): Observable<Permission> {
    return this.http.post<Permission>(`${this.apiUrl}/permissions`, permission);
  }

  updatePermission(permission: Permission): Observable<Permission> {
    return this.http.put<Permission>(`${this.apiUrl}/permissions/${permission.id}`, permission);
  }

  deletePermission(permissionId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/permissions/${permissionId}`);
  }

  getPermissionsByCategory(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/permissions/by-category`);
  }

  // Get Group Roles
  getGroupRoles(groupId: string): Observable<Role[]> {
    return this.http.get<Role[]>(`${this.apiUrl}/groups/${groupId}/roles`);
  }

  // User Management
  createUser(userData: any): Observable<User> {
    return this.http.post<User>(`${this.apiUrl}/users`, userData);
  }

  updateUser(userId: string, userData: Partial<User>): Observable<User> {
    return this.http.put<User>(`${this.apiUrl}/users/${userId}`, userData);
  }

  deleteUser(userId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/users/${userId}`);
  }

  getUserRoles(userId: string): Observable<Role[]> {
    return this.http.get<Role[]>(`${this.apiUrl}/users/${userId}/roles`);
  }

  assignRoleToUser(userId: string, roleId: string, projectId?: string): Observable<void> {
    const body = projectId ? { roleId, projectId } : { roleId };
    return this.http.post<void>(`${this.apiUrl}/users/${userId}/roles`, body);
  }

  removeRoleFromUser(userId: string, roleId: string, projectId?: string): Observable<void> {
    const params = projectId ? `?projectId=${projectId}` : '';
    return this.http.delete<void>(`${this.apiUrl}/users/${userId}/roles/${roleId}${params}`);
  }

  // Group-Project Management
  getProjectsForGroup(groupId: string): Observable<Project[]> {
    return this.http.get<Project[]>(`${this.apiUrl}/groups/${groupId}/projects`);
  }

  getGroupEffectivePermissions(groupId: string, projectId?: string): Observable<string[]> {
    return this.http.get<string[]>(`${this.apiUrl}/groups/${groupId}/effective-permissions${projectId ? `?projectId=${projectId}` : ''}`);
  }
}
