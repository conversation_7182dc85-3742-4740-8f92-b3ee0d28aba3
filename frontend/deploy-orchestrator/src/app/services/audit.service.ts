import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

interface AuditLog {
  id: string;
  timestamp: Date;
  userId: string;
  username: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: string;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
}

interface AuditLogResponse {
  logs: AuditLog[];
  total: number;
  page: number;
  limit: number;
}

interface AuditLogFilters {
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
  userId?: string;
  action?: string;
  resource?: string;
  success?: boolean;
  searchTerm?: string;
  export?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class AuditService {
  private apiUrl = `${environment.apiV1Url}/audit`;

  constructor(private http: HttpClient) { }

  getAuditLogs(filters: AuditLogFilters = {}): Observable<AuditLogResponse> {
    let params = new HttpParams();

    // Add pagination parameters
    if (filters.page) {
      params = params.set('page', filters.page.toString());
    }
    if (filters.limit) {
      params = params.set('limit', filters.limit.toString());
    }

    // Add filter parameters
    if (filters.startDate) {
      params = params.set('startDate', filters.startDate);
    }
    if (filters.endDate) {
      params = params.set('endDate', filters.endDate);
    }
    if (filters.userId) {
      params = params.set('userId', filters.userId);
    }
    if (filters.action) {
      params = params.set('action', filters.action);
    }
    if (filters.resource) {
      params = params.set('resource', filters.resource);
    }
    if (filters.success !== undefined) {
      params = params.set('success', filters.success.toString());
    }
    if (filters.searchTerm) {
      params = params.set('search', filters.searchTerm);
    }

    return this.http.get<AuditLogResponse>(this.apiUrl, { params });
  }

  getAuditLog(id: string): Observable<AuditLog> {
    return this.http.get<AuditLog>(`${this.apiUrl}/${id}`);
  }

  exportAuditLogs(filters: AuditLogFilters = {}): Observable<Blob> {
    let params = new HttpParams();

    // Add filter parameters for export
    if (filters.startDate) {
      params = params.set('startDate', filters.startDate);
    }
    if (filters.endDate) {
      params = params.set('endDate', filters.endDate);
    }
    if (filters.userId) {
      params = params.set('userId', filters.userId);
    }
    if (filters.action) {
      params = params.set('action', filters.action);
    }
    if (filters.resource) {
      params = params.set('resource', filters.resource);
    }
    if (filters.success !== undefined) {
      params = params.set('success', filters.success.toString());
    }
    if (filters.searchTerm) {
      params = params.set('search', filters.searchTerm);
    }

    return this.http.get(`${this.apiUrl}/export`, {
      params,
      responseType: 'blob'
    });
  }

  createAuditLog(auditLog: Partial<AuditLog>): Observable<AuditLog> {
    return this.http.post<AuditLog>(this.apiUrl, auditLog);
  }

  getAuditStats(): Observable<any> {
    return this.http.get(`${this.apiUrl}/stats`);
  }

  getRecentActivity(limit: number = 10): Observable<AuditLog[]> {
    const params = new HttpParams().set('limit', limit.toString()).set('recent', 'true');
    return this.http.get<AuditLog[]>(`${this.apiUrl}/recent`, { params });
  }

  getUserActivity(userId: string, limit: number = 50): Observable<AuditLog[]> {
    const params = new HttpParams()
      .set('userId', userId)
      .set('limit', limit.toString());
    return this.http.get<AuditLog[]>(this.apiUrl, { params });
  }

  getResourceActivity(resource: string, resourceId?: string, limit: number = 50): Observable<AuditLog[]> {
    let params = new HttpParams()
      .set('resource', resource)
      .set('limit', limit.toString());
    
    if (resourceId) {
      params = params.set('resourceId', resourceId);
    }

    return this.http.get<AuditLog[]>(this.apiUrl, { params });
  }
}
