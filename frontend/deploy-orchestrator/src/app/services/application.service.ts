import {Injectable} from '@angular/core';
import {HttpClient, HttpParams} from '@angular/common/http';
import {Observable, BehaviorSubject, firstValueFrom} from 'rxjs';
import {map, tap} from 'rxjs/operators';
import {environment} from '../../environments/environment';

import {
    ApplicationGroup,
    Application,
    Component,
    ApplicationDeployment,
    DeploymentPipeline,
    ApplicationEnvironment,
    ComponentDeployment,
    DeploymentStatus,
    HealthStatus,
    ApplicationMetrics,
    CreateApplicationGroupRequest,
    CreateApplicationRequest,
    CreateComponentRequest,
    UpdateComponentRequest
} from '../models/application.model';
import {EnvironmentConfig} from "./environment.service";

export interface UpdateApplicationGroupRequest {
    name?: string;
    description?: string;
    tags?: string[];
}

export interface UpdateApplicationRequest {
    name?: string;
    description?: string;
    repository?: {
        url: string;
        branch: string;
        provider: string;
    };
    tags?: string[];
}

export interface DeployApplicationRequest {
    applicationId: string;
    environment: string;
    version?: string;
    components?: string[];
    strategy?: string;
    rollbackOnFailure?: boolean;
}

export interface PromoteApplicationRequest {
    applicationId: string;
    fromEnvironment: string;
    toEnvironment: string;
    version?: string;
    requireApproval?: boolean;
    approvers?: string[];
}

export interface ApplicationFilter {
    projectId?: string;
    groupId?: string;
    type?: string;
    status?: HealthStatus;
    environment?: string;
    search?: string;
    tags?: string[];
}

export interface DeploymentFilter {
    applicationId?: string;
    environment?: string;
    status?: DeploymentStatus;
    deployedBy?: string;
    dateFrom?: string;
    dateTo?: string;
}

export interface MetricSeries {
    timestamps: string[];
    values: number[];
    unit: string;
}

export interface NetworkMetricSeries {
    bytesIn: MetricSeries;
    bytesOut: MetricSeries;
    connections: MetricSeries;
}

@Injectable({
    providedIn: 'root'
})
export class ApplicationService {
    private readonly baseUrl = `${environment.apiUrl}`;

    // Real-time updates
    private applicationGroupsSubject = new BehaviorSubject<ApplicationGroup[]>([]);
    public applicationGroups$ = this.applicationGroupsSubject.asObservable();

    private deploymentsSubject = new BehaviorSubject<ApplicationDeployment[]>([]);
    public deployments$ = this.deploymentsSubject.asObservable();

    constructor(private http: HttpClient) {
    }

    // Application Groups
    getApplicationGroups(projectId?: string): Promise<ApplicationGroup[]> {
        let params = new HttpParams();
        if (projectId) {
            params = params.set('projectId', projectId);
        }
        
        return firstValueFrom(this.http.get<ApplicationGroup[]>(`${this.baseUrl}/application-groups`, { params })
            .pipe(
                tap(groups => this.applicationGroupsSubject.next(groups))
            ))
            .then(groups => groups || []);
    }

    getApplicationGroup(groupId: string): Observable<ApplicationGroup> {
        return this.http.get<ApplicationGroup>(`${this.baseUrl}/application-groups/${groupId}`);
    }

    createApplicationGroup(request: CreateApplicationGroupRequest): Promise<ApplicationGroup> {
        return this.http.post<ApplicationGroup>(`${this.baseUrl}/application-groups`, request)
            .pipe(
                tap(group => {
                    const currentGroups = this.applicationGroupsSubject.value;
                    this.applicationGroupsSubject.next([...currentGroups, group]);
                })
            )
            .toPromise()
            .then(group => group!);
    }

    updateApplicationGroup(groupId: string, request: UpdateApplicationGroupRequest): Observable<ApplicationGroup> {
        return this.http.put<ApplicationGroup>(`${this.baseUrl}/application-groups/${groupId}`, request)
            .pipe(
                tap(updatedGroup => {
                    const currentGroups = this.applicationGroupsSubject.value;
                    const index = currentGroups.findIndex(g => g.id === groupId);
                    if (index !== -1) {
                        currentGroups[index] = updatedGroup;
                        this.applicationGroupsSubject.next([...currentGroups]);
                    }
                })
            );
    }

    deleteApplicationGroup(groupId: string): Observable<void> {
        return this.http.delete<void>(`${this.baseUrl}/application-groups/${groupId}`)
            .pipe(
                tap(() => {
                    const currentGroups = this.applicationGroupsSubject.value;
                    const filteredGroups = currentGroups.filter(g => g.id !== groupId);
                    this.applicationGroupsSubject.next(filteredGroups);
                })
            );
    }

    // Applications
    getApplications(filter?: ApplicationFilter): Observable<{ deployables: Application[], total: number }> {
        let params = new HttpParams();
        if (filter) {
            Object.keys(filter).forEach(key => {
                const value = (filter as any)[key];
                if (value !== undefined && value !== null) {
                    if (Array.isArray(value)) {
                        value.forEach(v => params = params.append(key, v));
                    } else {
                        params = params.set(key, value.toString());
                    }
                }
            });
        }

        return this.http.get<any>(`${this.baseUrl}/applications`, {params});
    }

    getApplication(applicationId: string): Observable<Application> {
        return this.http.get<Application>(`${this.baseUrl}/applications/${applicationId}`);
    }

    createApplication(request: CreateApplicationRequest): Observable<Application> {
        return this.http.post<Application>(`${this.baseUrl}/applications`, request);
    }

    updateApplication(applicationId: string, request: UpdateApplicationRequest): Observable<Application> {
        return this.http.put<Application>(`${this.baseUrl}/applications/${applicationId}`, request);
    }

    deleteApplication(applicationId: string): Observable<void> {
        return this.http.delete<void>(`${this.baseUrl}/applications/${applicationId}`);
    }

    // Application Environments
    getApplicationEnvironments(applicationId: string): Observable<ApplicationEnvironment[]> {
        return this.http.get<{
            environments: ApplicationEnvironment[]
        }>(`${this.baseUrl}/applications/${applicationId}/environments`)
            .pipe(map(response => response.environments));
    }

    getApplicationEnvironment(applicationId: string, environment: string): Observable<ApplicationEnvironment> {
        return this.http.get<ApplicationEnvironment>(`${this.baseUrl}/applications/${applicationId}/environments/${environment}`);
    }

    // Components
    getApplicationComponents(applicationId: string): Observable<Component[]> {
        return this.http.get<Component[]>(`${this.baseUrl}/applications/${applicationId}/components`);
    }

    getComponent(componentId: string): Observable<Component> {
        return this.http.get<Component>(`${this.baseUrl}/components/${componentId}`);
    }

    createComponent(applicationId: string, component: Partial<Component>): Observable<Component> {
        return this.http.post<Component>(`${this.baseUrl}/applications/${applicationId}/components`, component);
    }

    updateComponent(applicationId: string, componentId: string, component: Partial<Component>): Observable<Component> {
        return this.http.put<Component>(`${this.baseUrl}/applications/${applicationId}/components/${componentId}`, component);
    }

    deleteComponent(applicationId: string, componentId: string): Observable<void> {
        return this.http.delete<void>(`${this.baseUrl}/applications/${applicationId}/components/${componentId}`);
    }

    // Deployments
    deployApplication(applicationId: string, environment: string, request?: DeployApplicationRequest): Promise<ApplicationDeployment> {
        const deployRequest = request || {applicationId, environment};

        return this.http.post<ApplicationDeployment>(`${this.baseUrl}/applications/${applicationId}/deploy`, deployRequest)
            .pipe(
                tap(deployment => {
                    const currentDeployments = this.deploymentsSubject.value;
                    this.deploymentsSubject.next([deployment, ...currentDeployments]);
                })
            )
            .toPromise()
            .then(deployment => deployment!);
    }

    promoteApplication(applicationId: string, fromEnvironment: string, toEnvironment: string, request?: PromoteApplicationRequest): Promise<ApplicationDeployment> {
        const promoteRequest = request || {applicationId, fromEnvironment, toEnvironment};

        return this.http.post<ApplicationDeployment>(`${this.baseUrl}/applications/${applicationId}/promote`, promoteRequest)
            .pipe(
                tap(deployment => {
                    const currentDeployments = this.deploymentsSubject.value;
                    this.deploymentsSubject.next([deployment, ...currentDeployments]);
                })
            )
            .toPromise()
            .then(deployment => deployment!);
    }

    getApplicationDeployments(applicationId: string, filter?: DeploymentFilter): Observable<ApplicationDeployment[]> {
        let params = new HttpParams();
        if (filter) {
            Object.keys(filter).forEach(key => {
                const value = (filter as any)[key];
                if (value !== undefined && value !== null) {
                    params = params.set(key, value.toString());
                }
            });
        }

        return this.http.get<ApplicationDeployment[]>(`${this.baseUrl}/applications/${applicationId}/deployments`, {params});
    }

    getDeployment(deploymentId: string): Observable<ApplicationDeployment> {
        return this.http.get<ApplicationDeployment>(`${this.baseUrl}/deployments/${deploymentId}`);
    }

    cancelDeployment(deploymentId: string): Observable<ApplicationDeployment> {
        return this.http.post<ApplicationDeployment>(`${this.baseUrl}/deployments/${deploymentId}/cancel`, {});
    }

    rollbackDeployment(deploymentId: string): Observable<ApplicationDeployment> {
        return this.http.post<ApplicationDeployment>(`${this.baseUrl}/deployments/${deploymentId}/rollback`, {});
    }

    // Deployment Pipelines
    getApplicationPipelines(applicationId: string): Observable<DeploymentPipeline[]> {
        return this.http.get<DeploymentPipeline[]>(`${this.baseUrl}/applications/${applicationId}/pipelines`);
    }

    createDeploymentPipeline(applicationId: string, pipeline: Partial<DeploymentPipeline>): Observable<DeploymentPipeline> {
        return this.http.post<DeploymentPipeline>(`${this.baseUrl}/applications/${applicationId}/pipelines`, pipeline);
    }

    updateDeploymentPipeline(pipelineId: string, pipeline: Partial<DeploymentPipeline>): Observable<DeploymentPipeline> {
        return this.http.put<DeploymentPipeline>(`${this.baseUrl}/pipelines/${pipelineId}`, pipeline);
    }

    deleteDeploymentPipeline(pipelineId: string): Observable<void> {
        return this.http.delete<void>(`${this.baseUrl}/pipelines/${pipelineId}`);
    }

    triggerPipeline(pipelineId: string, version?: string): Observable<ApplicationDeployment> {
        return this.http.post<ApplicationDeployment>(`${this.baseUrl}/pipelines/${pipelineId}/trigger`, {version});
    }

    // Metrics and Monitoring
    getApplicationMetrics(applicationId: string, environment: string, timeRange: string = '1h'): Observable<ApplicationMetrics> {
        const params = new HttpParams()
            .set('environment', environment)
            .set('timeRange', timeRange);

        return this.http.get<ApplicationMetrics>(`${this.baseUrl}/applications/${applicationId}/metrics`, {params});
    }

    getComponentMetrics(componentId: string, environment: string, timeRange: string = '1h'): Observable<any> {
        const params = new HttpParams()
            .set('environment', environment)
            .set('timeRange', timeRange);

        return this.http.get(`${this.baseUrl}/components/${componentId}/metrics`, {params});
    }

    // Health and Status
    getApplicationHealth(applicationId: string, environment: string): Observable<any> {
        return this.http.get(`${this.baseUrl}/applications/${applicationId}/health/${environment}`);
    }

    getComponentHealth(componentId: string, environment: string): Observable<any> {
        return this.http.get(`${this.baseUrl}/components/${componentId}/health/${environment}`);
    }

    // Logs
    getApplicationLogs(applicationId: string, environment: string, options?: any): Observable<any> {
        let params = new HttpParams().set('environment', environment);
        if (options) {
            Object.keys(options).forEach(key => {
                if (options[key] !== undefined) {
                    params = params.set(key, options[key].toString());
                }
            });
        }

        return this.http.get(`${this.baseUrl}/applications/${applicationId}/logs`, {params});
    }

    getComponentLogs(componentId: string, environment: string, options?: any): Observable<any> {
        let params = new HttpParams().set('environment', environment);
        if (options) {
            Object.keys(options).forEach(key => {
                if (options[key] !== undefined) {
                    params = params.set(key, options[key].toString());
                }
            });
        }

        return this.http.get(`${this.baseUrl}/components/${componentId}/logs`, {params});
    }

    // Configuration Management
    getApplicationConfiguration(applicationId: string, environment: string): Observable<any> {
        return this.http.get(`${this.baseUrl}/applications/${applicationId}/configuration/${environment}`);
    }

    updateApplicationConfiguration(applicationId: string, environment: string, configuration: any): Observable<any> {
        return this.http.put(`${this.baseUrl}/applications/${applicationId}/configuration/${environment}`, configuration);
    }

    // Additional methods needed by components
    getDeploymentHistory(applicationId: string): Observable<any[]> {
        return this.http.get<any[]>(`${this.baseUrl}/applications/${applicationId}/deployment-history`);
    }

    getEnvironments(): Observable<any[]> {
        return this.http.get<any[]>(`${this.baseUrl}/environments`);
    }

    rollbackApplication(applicationId: string, environment: string): Observable<any> {
        return this.http.post(`${this.baseUrl}/applications/${applicationId}/rollback`, {
            environment
        });
    }

    deployComponent(componentId: string, environment: string, version?: string): Observable<any> {
        return this.http.post(`${this.baseUrl}/components/${componentId}/deploy`, {
            environment,
            version
        });
    }

    rollbackComponent(componentId: string, environment: string, version: string): Observable<any> {
        return this.http.post(`${this.baseUrl}/components/${componentId}/rollback`, {
            environment,
            version
        });
    }

    // Utility Methods
    refreshData(): void {
        this.getApplicationGroups();
    }

    clearCache(): void {
        this.applicationGroupsSubject.next([]);
        this.deploymentsSubject.next([]);
    }

    // Mock Data for Development
    private getMockApplicationGroups(): ApplicationGroup[] {
        return [
            {
                id: '1',
                name: 'Frontend Applications',
                description: 'All frontend applications and static assets',
                projectId: 'project-1',
                applications: [
                    {
                        id: 'app-1',
                        name: 'React Dashboard',
                        description: 'Main user dashboard built with React',
                        type: 'WEB_APPLICATION' as any,
                        groupId: '1',
                        projectId: 'project-1',
                        components: [],
                        environments: [
                            {
                                name: 'development',
                                status: HealthStatus.HEALTHY,
                                currentVersion: 'v1.2.3',
                                deploymentStatus: DeploymentStatus.DEPLOYED,
                                lastDeployedAt: '2024-01-15T10:30:00Z',
                                components: [],
                                healthScore: 95,
                                url: 'https://dev-dashboard.example.com'
                            },
                            {
                                name: 'staging',
                                status: HealthStatus.HEALTHY,
                                currentVersion: 'v1.2.2',
                                deploymentStatus: DeploymentStatus.DEPLOYED,
                                lastDeployedAt: '2024-01-14T15:20:00Z',
                                components: [],
                                healthScore: 98,
                                url: 'https://staging-dashboard.example.com'
                            },
                            {
                                name: 'production',
                                status: HealthStatus.HEALTHY,
                                currentVersion: 'v1.2.1',
                                deploymentStatus: DeploymentStatus.DEPLOYED,
                                lastDeployedAt: '2024-01-13T09:15:00Z',
                                components: [],
                                healthScore: 99,
                                url: 'https://dashboard.example.com'
                            }
                        ],
                        overallStatus: HealthStatus.HEALTHY,
                        configuration: {} as any,
                        createdAt: '2024-01-01T00:00:00Z',
                        updatedAt: '2024-01-15T10:30:00Z'
                    }
                ],
                createdAt: '2024-01-01T00:00:00Z',
                updatedAt: '2024-01-15T10:30:00Z'
            },
            {
                id: '2',
                name: 'Backend Services',
                description: 'Microservices and APIs',
                projectId: 'project-1',
                applications: [
                    {
                        id: 'app-2',
                        name: 'User API',
                        description: 'User management microservice',
                        type: 'MICROSERVICE' as any,
                        groupId: '2',
                        projectId: 'project-1',
                        components: [],
                        environments: [
                            {
                                name: 'development',
                                status: HealthStatus.WARNING,
                                currentVersion: 'v2.1.0',
                                deploymentStatus: DeploymentStatus.DEPLOYED,
                                lastDeployedAt: '2024-01-15T11:00:00Z',
                                components: [],
                                healthScore: 85,
                                url: 'https://api-dev.example.com/users'
                            },
                            {
                                name: 'staging',
                                status: HealthStatus.HEALTHY,
                                currentVersion: 'v2.0.5',
                                deploymentStatus: DeploymentStatus.DEPLOYED,
                                lastDeployedAt: '2024-01-14T16:30:00Z',
                                components: [],
                                healthScore: 92,
                                url: 'https://api-staging.example.com/users'
                            },
                            {
                                name: 'production',
                                status: HealthStatus.HEALTHY,
                                currentVersion: 'v2.0.4',
                                deploymentStatus: DeploymentStatus.DEPLOYED,
                                lastDeployedAt: '2024-01-12T14:20:00Z',
                                components: [],
                                healthScore: 97,
                                url: 'https://api.example.com/users'
                            }
                        ],
                        overallStatus: HealthStatus.WARNING,
                        configuration: {} as any,
                        createdAt: '2024-01-01T00:00:00Z',
                        updatedAt: '2024-01-15T11:00:00Z'
                    }
                ],
                createdAt: '2024-01-01T00:00:00Z',
                updatedAt: '2024-01-15T11:00:00Z'
            }
        ];
    }
}
