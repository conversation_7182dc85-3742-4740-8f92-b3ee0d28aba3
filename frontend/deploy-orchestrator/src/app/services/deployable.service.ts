import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { 
  Deployable, 
  DeployableCreateRequest, 
  DeployableUpdateRequest,
  DeployableListResponse,
  DeployableType,
  DeploymentStatus,
  WorkflowReference
} from '../models/deployable.model';

@Injectable({
  providedIn: 'root'
})
export class DeployableService {
  private baseUrl = '/api/v1/deployables';
  private deploymentsSubject = new BehaviorSubject<Deployable[]>([]);
  public deployments$ = this.deploymentsSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Get all deployables with optional filtering
   */
  getDeployables(filters?: {
    type?: DeployableType;
    status?: DeploymentStatus;
    environment?: string;
    parentId?: string;
    search?: string;
    projectId?: string;
  }): Observable<DeployableListResponse> {
    let params = new HttpParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<DeployableListResponse>(this.baseUrl, { params })
      .pipe(
        tap(response => this.deploymentsSubject.next(response.deployables))
      );
  }

  /**
   * Get a specific deployable by ID
   */
  getDeployable(id: string, projectId?: string): Observable<Deployable> {
    let params = new HttpParams();
    if (projectId) {
      params = params.set('projectId', projectId);
    }
    return this.http.get<Deployable>(`${this.baseUrl}/${id}`, { params });
  }

  /**
   * Create a new deployable
   */
  createDeployable(request: DeployableCreateRequest): Observable<Deployable> {
    return this.http.post<Deployable>(this.baseUrl, request)
      .pipe(
        tap(() => this.refreshDeployables())
      );
  }

  /**
   * Update an existing deployable
   */
  updateDeployable(id: string, request: DeployableUpdateRequest): Observable<Deployable> {
    return this.http.put<Deployable>(`${this.baseUrl}/${id}`, request)
      .pipe(
        tap(() => this.refreshDeployables())
      );
  }

  /**
   * Delete a deployable
   */
  deleteDeployable(id: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/${id}`)
      .pipe(
        tap(() => this.refreshDeployables())
      );
  }

  /**
   * Get deployable hierarchy (parent-child relationships)
   */
  getDeployableHierarchy(rootId?: string): Observable<Deployable[]> {
    const url = rootId ? `${this.baseUrl}/${rootId}/hierarchy` : `${this.baseUrl}/hierarchy`;
    return this.http.get<Deployable[]>(url);
  }

  /**
   * Deploy a deployable to specific environment
   */
  deployToEnvironment(deployableId: string, environmentId: string, projectId?: string, options?: {
    workflowId?: string;
    configOverrides?: Record<string, any>;
    dryRun?: boolean;
  }): Observable<{ executionId: string; workflowReference: WorkflowReference }> {
    const body = {
      environmentId,
      projectId,
      ...options
    };
    
    return this.http.post<{ executionId: string; workflowReference: WorkflowReference }>(
      `${this.baseUrl}/${deployableId}/deploy`, 
      body
    );
  }

  /**
   * Get deployment history for a deployable
   */
  getDeploymentHistory(deployableId: string, environmentId?: string, projectId?: string): Observable<any[]> {
    let params = new HttpParams();
    if (environmentId) {
      params = params.set('environmentId', environmentId);
    }
    if (projectId) {
      params = params.set('projectId', projectId);
    }

    return this.http.get<any[]>(`${this.baseUrl}/${deployableId}/deployments`, { params });
  }

  /**
   * Get environment status for a deployable
   */
  getEnvironmentStatus(deployableId: string, projectId?: string): Observable<Record<string, DeploymentStatus>> {
    let params = new HttpParams();
    if (projectId) {
      params = params.set('projectId', projectId);
    }
    return this.http.get<Record<string, DeploymentStatus>>(`${this.baseUrl}/${deployableId}/status`, { params });
  }

  /**
   * Promote deployable between environments
   */
  promoteToEnvironment(
    deployableId: string, 
    sourceEnvironmentId: string, 
    targetEnvironmentId: string,
    options?: {
      workflowId?: string;
      approvalRequired?: boolean;
    }
  ): Observable<{ executionId: string; workflowReference: WorkflowReference }> {
    const body = {
      sourceEnvironmentId,
      targetEnvironmentId,
      ...options
    };

    return this.http.post<{ executionId: string; workflowReference: WorkflowReference }>(
      `${this.baseUrl}/${deployableId}/promote`, 
      body
    );
  }

  /**
   * Get available workflows for a deployable type
   */
  getAvailableWorkflows(type: DeployableType, operation: 'deploy' | 'promote' | 'rollback'): Observable<WorkflowReference[]> {
    const params = new HttpParams()
      .set('type', type)
      .set('operation', operation);

    return this.http.get<WorkflowReference[]>(`${this.baseUrl}/workflows`, { params });
  }

  /**
   * Validate deployable configuration
   */
  validateConfiguration(config: DeployableCreateRequest | DeployableUpdateRequest): Observable<{
    valid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    return this.http.post<{
      valid: boolean;
      errors: string[];
      warnings: string[];
    }>(`${this.baseUrl}/validate`, config);
  }

  /**
   * Get deployable metrics and health status
   */
  getDeployableMetrics(deployableId: string, environmentId: string, projectId?: string): Observable<{
    health: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
    metrics: Record<string, any>;
    lastUpdated: string;
  }> {
    let params = new HttpParams();
    if (projectId) {
      params = params.set('projectId', projectId);
    }
    return this.http.get<{
      health: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
      metrics: Record<string, any>;
      lastUpdated: string;
    }>(`${this.baseUrl}/${deployableId}/metrics/${environmentId}`, { params });
  }

  /**
   * Rollback deployable to previous version
   */
  rollbackDeployable(
    deployableId: string, 
    environmentId: string, 
    projectId?: string,
    targetVersion?: string
  ): Observable<{ executionId: string; workflowReference: WorkflowReference }> {
    const body = {
      environmentId,
      projectId,
      targetVersion
    };

    return this.http.post<{ executionId: string; workflowReference: WorkflowReference }>(
      `${this.baseUrl}/${deployableId}/rollback`, 
      body
    );
  }

  /**
   * Scale deployable in specific environment
   */
  scaleDeployable(
    deployableId: string, 
    environmentId: string, 
    replicas: number
  ): Observable<{ executionId: string; workflowReference: WorkflowReference }> {
    const body = {
      environmentId,
      replicas
    };

    return this.http.post<{ executionId: string; workflowReference: WorkflowReference }>(
      `${this.baseUrl}/${deployableId}/scale`, 
      body
    );
  }

  private refreshDeployables(projectId?: string): void {
    this.getDeployables({ projectId }).subscribe();
  }
}
