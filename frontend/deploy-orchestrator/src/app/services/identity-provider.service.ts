import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import {
  IdentityProvider,
  IdentityProviderType,
  OIDCProvider,
  SAMLProvider,
  LDAPProvider
} from '../models/identity-provider.model';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class IdentityProviderService {
  private apiUrl = `${environment.apiUrl}`;
  private adminApiUrl = `${environment.apiUrl}/admin-service`
  constructor(private http: HttpClient) { }

  // Get all identity providers
  getAllProviders(): Observable<IdentityProvider[]> {
    return this.http.get<IdentityProvider[]>(`${this.adminApiUrl}/identity-providers`);
  }

  // Get only enabled identity providers (for login)
  getEnabledProviders(): Observable<IdentityProvider[]> {
    return this.http.get<IdentityProvider[]>(`${this.apiUrl}/identity-providers/enabled`);
  }

  // OIDC provider methods
  getOIDCProviders(): Observable<OIDCProvider[]> {
    return this.http.get<OIDCProvider[]>(`${this.adminApiUrl}/identity-providers/oidc`);
  }

  getOIDCProvider(id: string): Observable<OIDCProvider> {
    return this.http.get<OIDCProvider>(`${this.adminApiUrl}/identity-providers/oidc/${id}`);
  }

  createOIDCProvider(provider: OIDCProvider): Observable<OIDCProvider> {
    return this.http.post<OIDCProvider>(`${this.adminApiUrl}/identity-providers/oidc`, provider);
  }

  updateOIDCProvider(provider: OIDCProvider): Observable<OIDCProvider> {
    return this.http.put<OIDCProvider>(`${this.adminApiUrl}/identity-providers/oidc/${provider.id}`, provider);
  }

  deleteOIDCProvider(id: string): Observable<void> {
    return this.http.delete<void>(`${this.adminApiUrl}/identity-providers/oidc/${id}`);
  }

  // SAML provider methods
  getSAMLProviders(): Observable<SAMLProvider[]> {
    return this.http.get<SAMLProvider[]>(`${this.adminApiUrl}/identity-providers/saml`);
  }

  getSAMLProvider(id: string): Observable<SAMLProvider> {
    return this.http.get<SAMLProvider>(`${this.adminApiUrl}/identity-providers/saml/${id}`);
  }

  createSAMLProvider(provider: SAMLProvider): Observable<SAMLProvider> {
    return this.http.post<SAMLProvider>(`${this.adminApiUrl}/identity-providers/saml`, provider);
  }

  updateSAMLProvider(provider: SAMLProvider): Observable<SAMLProvider> {
    return this.http.put<SAMLProvider>(`${this.adminApiUrl}/identity-providers/saml/${provider.id}`, provider);
  }

  deleteSAMLProvider(id: string): Observable<void> {
    return this.http.delete<void>(`${this.adminApiUrl}/identity-providers/saml/${id}`);
  }

  // LDAP provider methods
  getLDAPProviders(): Observable<LDAPProvider[]> {
    return this.http.get<LDAPProvider[]>(`${this.adminApiUrl}/identity-providers/ldap`);
  }

  getLDAPProvider(id: string): Observable<LDAPProvider> {
    return this.http.get<LDAPProvider>(`${this.adminApiUrl}/identity-providers/ldap/${id}`);
  }

  createLDAPProvider(provider: LDAPProvider): Observable<LDAPProvider> {
    return this.http.post<LDAPProvider>(`${this.adminApiUrl}/identity-providers/ldap`, provider);
  }

  updateLDAPProvider(provider: LDAPProvider): Observable<LDAPProvider> {
    return this.http.put<LDAPProvider>(`${this.adminApiUrl}/identity-providers/ldap/${provider.id}`, provider);
  }

  deleteLDAPProvider(id: string): Observable<void> {
    return this.http.delete<void>(`${this.adminApiUrl}/identity-providers/ldap/${id}`);
  }

  // Common provider methods
  setDefaultProvider(id: string, type: IdentityProviderType): Observable<void> {
    return this.http.post<void>(`${this.adminApiUrl}/identity-providers/${type}/${id}/default`, {});
  }

  enableProvider(id: string, type: IdentityProviderType): Observable<void> {
    return this.http.post<void>(`${this.adminApiUrl}/identity-providers/${type}/${id}/enable`, {});
  }

  disableProvider(id: string, type: IdentityProviderType): Observable<void> {
    return this.http.post<void>(`${this.adminApiUrl}/identity-providers/${type}/${id}/disable`, {});
  }

  testConnection(id: string, type: IdentityProviderType): Observable<{ success: boolean, message: string }> {
    return this.http.post<{ success: boolean, message: string }>(`${this.adminApiUrl}/identity-providers/${type}/${id}/test`, {});
  }

  // Role mapping methods
  getRoleMapping(providerType: IdentityProviderType, id: string): Observable<Record<string, string>> {
    return this.http.get<Record<string, string>>(`${this.adminApiUrl}/identity-providers/${providerType}/${id}/role-mapping`);
  }

  updateRoleMapping(providerType: IdentityProviderType, id: string, mapping: Record<string, string>): Observable<void> {
    return this.http.put<void>(`${this.adminApiUrl}/identity-providers/${providerType}/${id}/role-mapping`, mapping);
  }

  // Group discovery for LDAP and OIDC
  discoverGroups(providerType: IdentityProviderType, id: string, credentials?: {
    username: string,
    password: string
  }): Observable<string[]> {
    return this.http.post<string[]>(`${this.adminApiUrl}/identity-providers/${providerType}/${id}/discover-groups`, credentials || {});
  }
}
