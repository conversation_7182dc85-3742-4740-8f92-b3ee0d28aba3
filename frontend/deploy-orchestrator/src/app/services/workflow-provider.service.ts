import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface WorkflowProvider {
  type: string;
  name: string;
  description: string;
  configFields: WorkflowProviderConfigField[];
}

export interface WorkflowProviderConfigField {
  name: string;
  key?: string; // Alias for name to be compatible with plugin schema interface
  label?: string; // Display label
  type: string;
  required: boolean;
  description: string;
  default?: any;
  placeholder?: string;
  options?: { label: string; value: any }[] | string[];
}

export interface WorkflowProviderListResponse {
  providers: WorkflowProvider[];
}

@Injectable({
  providedIn: 'root'
})
export class WorkflowProviderService {
  private readonly baseUrl = `${environment.apiUrl}`;

  constructor(private http: HttpClient) {}

  /**
   * Get all available workflow providers
   */
  getProviders(): Observable<WorkflowProviderListResponse> {
    return this.http.get<WorkflowProviderListResponse>(`${this.baseUrl}/providers`);
  }

  /**
   * Get details about a specific workflow provider
   */
  getProvider(type: string): Observable<WorkflowProvider> {
    return this.http.get<WorkflowProvider>(`${this.baseUrl}/providers/${type}`);
  }
}
