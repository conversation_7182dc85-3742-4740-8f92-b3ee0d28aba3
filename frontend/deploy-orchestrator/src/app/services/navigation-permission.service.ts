import { Injectable } from '@angular/core';
import { Observable, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';
import { AuthService } from './auth.service';
import { PluginPermissionService } from './plugin-permission.service';
import { PLUGIN_PERMISSIONS } from '../models/plugin-permission.model';

export interface NavigationItem {
  id: string;
  label: string;
  icon: string;
  route: string;
  visible: boolean;
  children?: NavigationItem[];
  requiredPermissions?: string[];
  requiredRoles?: string[];
  adminOnly?: boolean;
  badge?: {
    text: string;
    color: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class NavigationPermissionService {

  constructor(
    private authService: AuthService,
    private pluginPermissionService: PluginPermissionService
  ) {}

  getNavigationItems(): Observable<NavigationItem[]> {
    return combineLatest([
      this.authService.currentUser$,
      this.pluginPermissionService.permissionMatrix$,
      this.pluginPermissionService.isPluginAdmin()
    ]).pipe(
      map(([user, permissionMatrix, isPluginAdmin]) => {
        const items: NavigationItem[] = [
          {
            id: 'dashboard',
            label: 'Dashboard',
            icon: 'fas fa-tachometer-alt',
            route: '/dashboard',
            visible: true
          },
          {
            id: 'projects',
            label: 'Projects',
            icon: 'fas fa-folder',
            route: '/projects',
            visible: true
          },
          {
            id: 'workflows',
            label: 'Workflows',
            icon: 'fas fa-sitemap',
            route: '/workflows',
            visible: this.hasWorkflowAccess(user),
            children: [
              {
                id: 'workflow-list',
                label: 'All Workflows',
                icon: 'fas fa-list',
                route: '/workflows',
                visible: this.hasWorkflowAccess(user)
              },
              {
                id: 'workflow-create',
                label: 'Create Workflow',
                icon: 'fas fa-plus',
                route: '/workflows/create',
                visible: this.hasWorkflowCreateAccess(user)
              },
              {
                id: 'workflow-templates',
                label: 'Templates',
                icon: 'fas fa-template',
                route: '/workflows/templates',
                visible: this.hasTemplateAccess(permissionMatrix)
              }
            ]
          },
          {
            id: 'deployments',
            label: 'Deployments',
            icon: 'fas fa-rocket',
            route: '/deployments',
            visible: this.hasDeploymentAccess(user, permissionMatrix)
          },
          {
            id: 'environments',
            label: 'Environments',
            icon: 'fas fa-server',
            route: '/environments',
            visible: this.hasEnvironmentAccess(user)
          },
          {
            id: 'plugins',
            label: 'Plugins',
            icon: 'fas fa-puzzle-piece',
            route: '/plugins',
            visible: this.hasPluginAccess(permissionMatrix, isPluginAdmin),
            badge: this.getPluginBadge(permissionMatrix),
            children: [
              {
                id: 'plugin-management',
                label: 'Plugin Management',
                icon: 'fas fa-cogs',
                route: '/plugins',
                visible: this.hasPluginManagementAccess(permissionMatrix, isPluginAdmin)
              },
              {
                id: 'provider-config',
                label: 'Providers',
                icon: 'fas fa-cloud',
                route: '/providers',
                visible: this.hasProviderAccess(permissionMatrix, isPluginAdmin)
              },
              {
                id: 'plugin-marketplace',
                label: 'Marketplace',
                icon: 'fas fa-store',
                route: '/plugins?tab=marketplace',
                visible: this.hasMarketplaceAccess(isPluginAdmin)
              }
            ]
          },
          {
            id: 'secrets',
            label: 'Secrets',
            icon: 'fas fa-key',
            route: '/secrets',
            visible: this.hasSecretsAccess(user)
          },
          {
            id: 'monitoring',
            label: 'Monitoring',
            icon: 'fas fa-chart-line',
            route: '/monitoring',
            visible: this.hasMonitoringAccess(user),
            children: [
              {
                id: 'execution-monitoring',
                label: 'Executions',
                icon: 'fas fa-play-circle',
                route: '/execution-monitoring',
                visible: this.hasExecutionMonitoringAccess(user)
              },
              {
                id: 'plugin-metrics',
                label: 'Plugin Metrics',
                icon: 'fas fa-chart-bar',
                route: '/monitoring/plugins',
                visible: this.hasPluginMetricsAccess(permissionMatrix, isPluginAdmin)
              },
              {
                id: 'system-health',
                label: 'System Health',
                icon: 'fas fa-heartbeat',
                route: '/monitoring/health',
                visible: this.hasSystemHealthAccess(user)
              }
            ]
          },
          {
            id: 'admin',
            label: 'Administration',
            icon: 'fas fa-shield-alt',
            route: '/admin',
            visible: this.isAdmin(user),
            adminOnly: true,
            children: [
              {
                id: 'user-management',
                label: 'Users',
                icon: 'fas fa-users',
                route: '/admin/users',
                visible: this.isAdmin(user),
                adminOnly: true
              },
              {
                id: 'group-management',
                label: 'Groups',
                icon: 'fas fa-users-cog',
                route: '/admin/groups',
                visible: this.isAdmin(user),
                adminOnly: true
              },
              {
                id: 'permission-management',
                label: 'Permissions',
                icon: 'fas fa-lock',
                route: '/admin/permissions',
                visible: this.isAdmin(user),
                adminOnly: true
              },
              {
                id: 'plugin-permissions',
                label: 'Plugin Permissions',
                icon: 'fas fa-puzzle-piece',
                route: '/admin/plugin-permissions',
                visible: this.isAdmin(user),
                adminOnly: true
              },
              {
                id: 'audit-logs',
                label: 'Audit Logs',
                icon: 'fas fa-clipboard-list',
                route: '/admin/audit-logs',
                visible: this.isAdmin(user),
                adminOnly: true
              }
            ]
          }
        ];

        return this.filterVisibleItems(items);
      })
    );
  }

  private filterVisibleItems(items: NavigationItem[]): NavigationItem[] {
    return items.filter(item => {
      if (!item.visible) return false;

      if (item.children) {
        item.children = this.filterVisibleItems(item.children);
        // Hide parent if no children are visible
        if (item.children.length === 0 && item.route.includes('children-required')) {
          return false;
        }
      }

      return true;
    });
  }

  // Permission checking methods
  private hasWorkflowAccess(user: any): boolean {
    return user && (user.permissions?.includes('workflow:view') || this.isAdmin(user));
  }

  private hasWorkflowCreateAccess(user: any): boolean {
    return user && (user.permissions?.includes('workflow:create') || this.isAdmin(user));
  }

  private hasDeploymentAccess(user: any, permissionMatrix: any): boolean {
    if (this.isAdmin(user)) return true;
    if (user?.permissions?.includes('deployment:view')) return true;

    // Check if user has any plugin deployment permissions
    if (permissionMatrix) {
      return Object.values(permissionMatrix.plugins || {}).some((plugin: any) =>
        plugin.canDeploy || plugin.canView
      );
    }

    return false;
  }

  private hasEnvironmentAccess(user: any): boolean {
    return user && (user.permissions?.includes('environment:view') || this.isAdmin(user));
  }

  private hasPluginAccess(permissionMatrix: any, isPluginAdmin: boolean): boolean {
    if (isPluginAdmin) return true;

    if (permissionMatrix) {
      return Object.keys(permissionMatrix.plugins || {}).length > 0;
    }

    return false;
  }

  private hasPluginManagementAccess(permissionMatrix: any, isPluginAdmin: boolean): boolean {
    if (isPluginAdmin) return true;

    if (permissionMatrix) {
      return Object.values(permissionMatrix.plugins || {}).some((plugin: any) =>
        plugin.canView || plugin.canManage || plugin.canConfigure
      );
    }

    return false;
  }

  private hasProviderAccess(permissionMatrix: any, isPluginAdmin: boolean): boolean {
    if (isPluginAdmin) return true;

    if (permissionMatrix) {
      return Object.values(permissionMatrix.plugins || {}).some((plugin: any) =>
        Object.keys(plugin.providers || {}).length > 0
      );
    }

    return false;
  }

  private hasMarketplaceAccess(isPluginAdmin: boolean): boolean {
    return isPluginAdmin; // Only admins can access marketplace for now
  }

  private hasTemplateAccess(permissionMatrix: any): boolean {
    if (permissionMatrix) {
      return Object.values(permissionMatrix.plugins || {}).some((plugin: any) =>
        Object.keys(plugin.templates || {}).length > 0
      );
    }

    return false;
  }

  private hasSecretsAccess(user: any): boolean {
    return user && (user.permissions?.includes('secret:view') || this.isAdmin(user));
  }

  private hasMonitoringAccess(user: any): boolean {
    return user && (
      user.permissions?.includes('monitoring:view') ||
      user.permissions?.includes('workflow:monitor') ||
      this.isAdmin(user)
    );
  }

  private hasExecutionMonitoringAccess(user: any): boolean {
    return user && (user.permissions?.includes('workflow:monitor') || this.isAdmin(user));
  }

  private hasPluginMetricsAccess(permissionMatrix: any, isPluginAdmin: boolean): boolean {
    if (isPluginAdmin) return true;

    if (permissionMatrix) {
      return Object.values(permissionMatrix.plugins || {}).some((plugin: any) =>
        plugin.features?.metrics?.accessible
      );
    }

    return false;
  }

  private hasSystemHealthAccess(user: any): boolean {
    return user && (user.permissions?.includes('system:health') || this.isAdmin(user));
  }

  private isAdmin(user: any): boolean {
    return user && (user.roles?.includes('admin') || user.isAdmin);
  }

  private getPluginBadge(permissionMatrix: any): { text: string; color: string } | undefined {
    if (!permissionMatrix) return undefined;

    const pluginCount = Object.keys(permissionMatrix.plugins || {}).length;
    if (pluginCount === 0) return undefined;

    return {
      text: pluginCount.toString(),
      color: 'blue'
    };
  }

  // Helper method to check specific navigation permissions
  canAccessRoute(route: string): Observable<boolean> {
    return combineLatest([
      this.authService.currentUser$,
      this.pluginPermissionService.permissionMatrix$,
      this.pluginPermissionService.isPluginAdmin()
    ]).pipe(
      map(([user, permissionMatrix, isPluginAdmin]) => {
        switch (route) {
          case '/plugins':
            return this.hasPluginAccess(permissionMatrix, isPluginAdmin);
          case '/providers':
            return this.hasProviderAccess(permissionMatrix, isPluginAdmin);
          case '/workflows/templates':
            return this.hasTemplateAccess(permissionMatrix);
          case '/monitoring/plugins':
            return this.hasPluginMetricsAccess(permissionMatrix, isPluginAdmin);
          default:
            return true; // Default to allowing access for unknown routes
        }
      })
    );
  }

  // Get navigation items for a specific section
  getPluginNavigationItems(): Observable<NavigationItem[]> {
    return this.getNavigationItems().pipe(
      map(items => {
        const pluginItem = items.find(item => item.id === 'plugins');
        return pluginItem?.children || [];
      })
    );
  }

  getAdminNavigationItems(): Observable<NavigationItem[]> {
    return this.getNavigationItems().pipe(
      map(items => {
        const adminItem = items.find(item => item.id === 'admin');
        return adminItem?.children || [];
      })
    );
  }

  // Individual permission methods for sidebar
  canViewEnvironments(): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => this.hasEnvironmentAccess(user))
    );
  }

  canManageEnvironments(): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => !!(user && (user.isAdmin || this.isAdmin(user))))
    );
  }

  canDeployToEnvironments(): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => !!(user && (user.isAdmin || this.isAdmin(user) || this.hasDeploymentRole(user))))
    );
  }

  private hasDeploymentRole(user: any): boolean {
    if (!user?.roles) return false;
    return user.roles.some((role: any) =>
      role.permissions?.some((permission: any) =>
        permission.name === 'environment:deploy' || permission.name === 'deployment:create'
      )
    );
  }

  canViewWorkflows(): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => this.hasWorkflowAccess(user))
    );
  }

  canViewTemplates(): Observable<boolean> {
    return combineLatest([
      this.authService.currentUser$,
      this.pluginPermissionService.permissionMatrix$
    ]).pipe(
      map(([user, permissionMatrix]) => this.hasTemplateAccess(permissionMatrix) || this.isAdmin(user))
    );
  }

  canViewTemplateAnalytics(): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => !!(user && (user.isAdmin || this.isAdmin(user))))
    );
  }

  canViewExecutionMonitoring(): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => this.hasExecutionMonitoringAccess(user))
    );
  }
}
