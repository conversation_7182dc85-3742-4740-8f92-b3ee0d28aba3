import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import {
  ProjectPermission,
  ProjectRole,
  ProjectGroup,
  PermissionAssignmentRequest,
  PermissionUpdateRequest
} from '../models/project-permission.model';

@Injectable({
  providedIn: 'root'
})
export class ProjectPermissionService {
  private apiUrl = `${environment.apiUrl}/admin-service/v1`;

  constructor(private http: HttpClient) {}

  // Project Permissions
  getProjectPermissions(projectId: string): Observable<ProjectPermission[]> {
    return this.http.get<ProjectPermission[]>(`${this.apiUrl}/projects/${projectId}/permissions`);
  }

  assignPermission(projectId: string, request: PermissionAssignmentRequest): Observable<ProjectPermission> {
    return this.http.post<ProjectPermission>(`${this.apiUrl}/projects/${projectId}/permissions`, request);
  }

  updatePermission(projectId: string, permissionId: string, request: PermissionUpdateRequest): Observable<ProjectPermission> {
    return this.http.put<ProjectPermission>(`${this.apiUrl}/projects/${projectId}/permissions/${permissionId}`, request);
  }

  revokePermission(projectId: string, permissionId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/projects/${projectId}/permissions/${permissionId}`);
  }

  // Project Roles
  getProjectRoles(projectId: string): Observable<ProjectRole[]> {
    return this.http.get<ProjectRole[]>(`${this.apiUrl}/projects/${projectId}/roles`);
  }

  getGlobalRoles(): Observable<ProjectRole[]> {
    return this.http.get<ProjectRole[]>(`${this.apiUrl}/roles`);
  }

  createProjectRole(projectId: string, role: Partial<ProjectRole>): Observable<ProjectRole> {
    return this.http.post<ProjectRole>(`${this.apiUrl}/projects/${projectId}/roles`, role);
  }

  updateProjectRole(projectId: string, roleId: string, role: Partial<ProjectRole>): Observable<ProjectRole> {
    return this.http.put<ProjectRole>(`${this.apiUrl}/projects/${projectId}/roles/${roleId}`, role);
  }

  deleteProjectRole(projectId: string, roleId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/projects/${projectId}/roles/${roleId}`);
  }

  // Project Groups
  getProjectGroups(projectId: string): Observable<ProjectGroup[]> {
    return this.http.get<ProjectGroup[]>(`${this.apiUrl}/projects/${projectId}/groups`);
  }

  assignGroupToProject(projectId: string, groupId: string, roleIds: string[]): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/projects/${projectId}/groups/${groupId}`, { roleIds });
  }

  removeGroupFromProject(projectId: string, groupId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/projects/${projectId}/groups/${groupId}`);
  }

  updateGroupRoles(projectId: string, groupId: string, roleIds: string[]): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/projects/${projectId}/groups/${groupId}/roles`, { roleIds });
  }

  // Available Users and Groups for assignment
  getAvailableUsers(projectId: string, search?: string): Observable<any[]> {
    let params = new HttpParams();
    if (search) {
      params = params.set('search', search);
    }
    return this.http.get<any[]>(`${this.apiUrl}/projects/${projectId}/available-users`, { params });
  }

  getAvailableGroups(projectId: string, search?: string): Observable<ProjectGroup[]> {
    let params = new HttpParams();
    if (search) {
      params = params.set('search', search);
    }
    return this.http.get<ProjectGroup[]>(`${this.apiUrl}/projects/${projectId}/available-groups`, { params });
  }

  // Permission validation
  validatePermissions(projectId: string, permissions: string[]): Observable<{ valid: boolean; errors: string[] }> {
    return this.http.post<{ valid: boolean; errors: string[] }>(`${this.apiUrl}/projects/${projectId}/permissions/validate`, { permissions });
  }

  // Bulk operations
  bulkAssignPermissions(projectId: string, assignments: PermissionAssignmentRequest[]): Observable<ProjectPermission[]> {
    return this.http.post<ProjectPermission[]>(`${this.apiUrl}/projects/${projectId}/permissions/bulk`, { assignments });
  }

  bulkRevokePermissions(projectId: string, permissionIds: string[]): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/projects/${projectId}/permissions/bulk`, { body: { permissionIds } });
  }

  // Permission inheritance and effective permissions
  getEffectivePermissions(projectId: string, userId: string): Observable<string[]> {
    return this.http.get<string[]>(`${this.apiUrl}/projects/${projectId}/users/${userId}/effective-permissions`);
  }

  getPermissionInheritance(projectId: string, userId: string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/projects/${projectId}/users/${userId}/permission-inheritance`);
  }
}
