import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Observable, Subject, BehaviorSubject } from 'rxjs';
import { io, Socket } from 'socket.io-client';
import { LogEntry } from './workflow-execution.service';

export interface LogSubscription {
  executionId: string;
  socket: Socket;
  logs$: Subject<LogEntry>;
  connected: boolean;
}

export interface LogViewerConfig {
  autoScroll: boolean;
  maxLogs: number;
  filterLevel?: string;
  filterStep?: string;
  showTimestamps: boolean;
  showProgress: boolean;
  highlightDuration: number; // ms
}

@Injectable({
  providedIn: 'root'
})
export class RealtimeLoggingService implements OnDestroy {
  private subscriptions = new Map<string, LogSubscription>();
  private defaultConfig: LogViewerConfig = {
    autoScroll: true,
    maxLogs: 1000,
    showTimestamps: true,
    showProgress: true,
    highlightDuration: 2000
  };

  constructor() {}

  ngOnDestroy(): void {
    // Clean up all subscriptions
    this.subscriptions.forEach(sub => {
      this.unsubscribeFromLogs(sub.executionId);
    });
  }

  // Subscribe to real-time logs for an execution
  subscribeToLogs(executionId: string): Observable<LogEntry> {
    // Check if already subscribed
    if (this.subscriptions.has(executionId)) {
      const existing = this.subscriptions.get(executionId)!;
      return existing.logs$.asObservable();
    }

    // Create new subscription
    const socket = io('/workflow-logs', {
      transports: ['websocket', 'polling'],
      timeout: 5000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000
    });

    const logs$ = new Subject<LogEntry>();
    
    const subscription: LogSubscription = {
      executionId,
      socket,
      logs$,
      connected: false
    };

    // Set up socket event handlers
    socket.on('connect', () => {
      console.log(`Connected to log stream for execution ${executionId}`);
      subscription.connected = true;
      
      // Subscribe to logs for this execution
      socket.emit('subscribe-logs', { executionId });
    });

    socket.on('disconnect', () => {
      console.log(`Disconnected from log stream for execution ${executionId}`);
      subscription.connected = false;
    });

    socket.on('log-entry', (logEntry: LogEntry) => {
      // Mark as new for highlighting
      logEntry.isNew = true;
      
      // Emit to subscribers
      logs$.next(logEntry);
      
      // Remove new flag after highlight duration
      setTimeout(() => {
        logEntry.isNew = false;
      }, this.defaultConfig.highlightDuration);
    });

    socket.on('connect_error', (error: any) => {
      console.error(`Connection error for execution ${executionId}:`, error);
    });

    socket.on('error', (error: any) => {
      console.error(`Socket error for execution ${executionId}:`, error);
      logs$.error(error);
    });

    // Store subscription
    this.subscriptions.set(executionId, subscription);

    return logs$.asObservable();
  }

  // Unsubscribe from logs
  unsubscribeFromLogs(executionId: string): void {
    const subscription = this.subscriptions.get(executionId);
    if (subscription) {
      subscription.socket.disconnect();
      subscription.logs$.complete();
      this.subscriptions.delete(executionId);
      console.log(`Unsubscribed from logs for execution ${executionId}`);
    }
  }

  // Check if subscribed to an execution
  isSubscribed(executionId: string): boolean {
    return this.subscriptions.has(executionId);
  }

  // Check if connection is active
  isConnected(executionId: string): boolean {
    const subscription = this.subscriptions.get(executionId);
    return subscription ? subscription.connected : false;
  }

  // Get all active subscriptions
  getActiveSubscriptions(): string[] {
    return Array.from(this.subscriptions.keys());
  }

  // Reconnect to a specific execution
  reconnect(executionId: string): void {
    const subscription = this.subscriptions.get(executionId);
    if (subscription) {
      subscription.socket.connect();
    }
  }

  // Reconnect all subscriptions
  reconnectAll(): void {
    this.subscriptions.forEach(subscription => {
      subscription.socket.connect();
    });
  }

  // Get connection status for all subscriptions
  getConnectionStatus(): { [executionId: string]: boolean } {
    const status: { [executionId: string]: boolean } = {};
    this.subscriptions.forEach((subscription, executionId) => {
      status[executionId] = subscription.connected;
    });
    return status;
  }

  // Utility methods for log processing
  filterLogs(logs: LogEntry[], config: Partial<LogViewerConfig>): LogEntry[] {
    let filtered = [...logs];

    // Filter by level
    if (config.filterLevel) {
      filtered = filtered.filter(log => log.level === config.filterLevel);
    }

    // Filter by step
    if (config.filterStep) {
      filtered = filtered.filter(log => log.stepName === config.filterStep);
    }

    // Limit number of logs
    const maxLogs = config.maxLogs || this.defaultConfig.maxLogs;
    if (filtered.length > maxLogs) {
      filtered = filtered.slice(-maxLogs);
    }

    return filtered;
  }

  // Format log entry for display
  formatLogEntry(log: LogEntry, config: Partial<LogViewerConfig> = {}): string {
    const showTimestamps = config.showTimestamps !== false;
    const parts: string[] = [];

    if (showTimestamps) {
      const timestamp = new Date(log.timestamp).toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        fractionalSecondDigits: 3
      });
      parts.push(`[${timestamp}]`);
    }

    parts.push(`[${log.level.toUpperCase()}]`);
    parts.push(`[${log.stepName}]`);
    parts.push(log.message);

    if (log.progress !== undefined && config.showProgress !== false) {
      parts.push(`(${log.progress}%)`);
    }

    return parts.join(' ');
  }

  // Get log level color class
  getLogLevelClass(level: string): string {
    switch (level.toLowerCase()) {
      case 'debug': return 'text-gray-500';
      case 'info': return 'text-blue-600';
      case 'warn': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-700';
    }
  }

  // Get step status color class
  getStepStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'pending': return 'text-gray-500';
      case 'running': return 'text-blue-600';
      case 'success': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'skipped': return 'text-yellow-600';
      default: return 'text-gray-700';
    }
  }

  // Export logs to file
  exportLogs(logs: LogEntry[], filename?: string): void {
    const logText = logs.map(log => this.formatLogEntry(log, {
      showTimestamps: true,
      showProgress: true
    })).join('\n');

    const blob = new Blob([logText], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || `execution-logs-${new Date().toISOString().slice(0, 19)}.txt`;
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    window.URL.revokeObjectURL(url);
  }

  // Search logs
  searchLogs(logs: LogEntry[], query: string): LogEntry[] {
    if (!query.trim()) return logs;
    
    const searchTerm = query.toLowerCase();
    return logs.filter(log => 
      log.message.toLowerCase().includes(searchTerm) ||
      log.stepName.toLowerCase().includes(searchTerm) ||
      log.level.toLowerCase().includes(searchTerm)
    );
  }

  // Get log statistics
  getLogStatistics(logs: LogEntry[]): {
    total: number;
    byLevel: { [level: string]: number };
    byStep: { [step: string]: number };
    timeRange: { start?: string; end?: string };
  } {
    const stats = {
      total: logs.length,
      byLevel: {} as { [level: string]: number },
      byStep: {} as { [step: string]: number },
      timeRange: {} as { start?: string; end?: string }
    };

    if (logs.length === 0) return stats;

    // Count by level and step
    logs.forEach(log => {
      stats.byLevel[log.level] = (stats.byLevel[log.level] || 0) + 1;
      stats.byStep[log.stepName] = (stats.byStep[log.stepName] || 0) + 1;
    });

    // Time range
    const timestamps = logs.map(log => new Date(log.timestamp).getTime()).sort();
    stats.timeRange.start = new Date(timestamps[0]).toISOString();
    stats.timeRange.end = new Date(timestamps[timestamps.length - 1]).toISOString();

    return stats;
  }

  // Default configuration
  getDefaultConfig(): LogViewerConfig {
    return { ...this.defaultConfig };
  }

  // Update default configuration
  updateDefaultConfig(config: Partial<LogViewerConfig>): void {
    this.defaultConfig = { ...this.defaultConfig, ...config };
  }
}
