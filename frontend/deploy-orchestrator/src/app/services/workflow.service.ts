import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '../../environments/environment';
import {
  WorkflowDefinition,
  WorkflowTemplate,
  WorkflowExecution,
  WorkflowStepExecution
} from '../models/workflow.model';

@Injectable({
  providedIn: 'root'
})
export class WorkflowService {
  private baseUrl = '/api/v1';
  private currentWorkflowSubject = new BehaviorSubject<WorkflowDefinition | null>(null);
  public currentWorkflow$ = this.currentWorkflowSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Workflow Definitions
  getWorkflows(projectId?: string, limit: number = 50, offset: number = 0): Observable<WorkflowDefinition[]> {
    let params = new HttpParams()
      .set('limit', limit.toString())
      .set('offset', offset.toString());

    if (projectId) {
      params = params.set('projectId', projectId);
    }

    return this.http.get<WorkflowDefinition[]>(`${this.baseUrl}/workflows`, { params });
  }

  getWorkflow(id: string): Observable<WorkflowDefinition> {
    return this.http.get<WorkflowDefinition>(`${this.baseUrl}/workflows/${id}`);
  }

  createWorkflow(workflow: Partial<WorkflowDefinition>): Observable<WorkflowDefinition> {
    return this.http.post<WorkflowDefinition>(`${this.baseUrl}/workflows`, workflow);
  }

  updateWorkflow(id: string, workflow: Partial<WorkflowDefinition>): Observable<WorkflowDefinition> {
    return this.http.put<WorkflowDefinition>(`${this.baseUrl}/workflows/${id}`, workflow);
  }

  deleteWorkflow(id: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/workflows/${id}`);
  }

  validateWorkflow(workflow: WorkflowDefinition): Observable<{ valid: boolean; errors: string[] }> {
    return this.http.post<{ valid: boolean; errors: string[] }>(`${this.baseUrl}/workflows/validate`, workflow);
  }

  // Workflow Templates
  getTemplates(filters?: {
    category?: string;
    isFeatured?: boolean;
    search?: string;
    tags?: string[];
    sortBy?: string;
    sortOrder?: string;
    limit?: number;
    offset?: number;
  }): Observable<WorkflowTemplate[]> {
    let params = new HttpParams()
      .set('limit', (filters?.limit || 50).toString())
      .set('offset', (filters?.offset || 0).toString());

    if (filters?.category) {
      params = params.set('category', filters.category);
    }

    if (filters?.isFeatured !== undefined) {
      params = params.set('isFeatured', filters.isFeatured.toString());
    }

    if (filters?.search) {
      params = params.set('search', filters.search);
    }

    if (filters?.tags && filters.tags.length > 0) {
      filters.tags.forEach(tag => {
        params = params.append('tags', tag);
      });
    }

    if (filters?.sortBy) {
      params = params.set('sortBy', filters.sortBy);
    }

    if (filters?.sortOrder) {
      params = params.set('sortOrder', filters.sortOrder);
    }

    return this.http.get<WorkflowTemplate[]>(`${this.baseUrl}/templates`, { params });
  }

  getFeaturedTemplates(limit: number = 10): Observable<WorkflowTemplate[]> {
    const params = new HttpParams().set('limit', limit.toString());
    return this.http.get<WorkflowTemplate[]>(`${this.baseUrl}/templates/featured`, { params });
  }

  getTemplateCategories(): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/templates/categories`);
  }

  getTemplate(id: string): Observable<WorkflowTemplate> {
    return this.http.get<WorkflowTemplate>(`${this.baseUrl}/templates/${id}`);
  }

  downloadTemplate(id: string): Observable<WorkflowTemplate> {
    return this.http.get<WorkflowTemplate>(`${this.baseUrl}/templates/${id}/download`);
  }

  createTemplate(template: Partial<WorkflowTemplate>): Observable<WorkflowTemplate> {
    return this.http.post<WorkflowTemplate>(`${this.baseUrl}/templates`, template);
  }

  updateTemplate(id: string, template: Partial<WorkflowTemplate>): Observable<WorkflowTemplate> {
    return this.http.put<WorkflowTemplate>(`${this.baseUrl}/templates/${id}`, template);
  }

  deleteTemplate(id: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/templates/${id}`);
  }

  createWorkflowFromTemplate(templateId: string, request: {
    name: string;
    description?: string;
    projectId: string;
    parameters?: { [key: string]: any };
  }): Observable<WorkflowDefinition> {
    return this.http.post<WorkflowDefinition>(`${this.baseUrl}/templates/${templateId}/create-workflow`, request);
  }

  // ===== RATINGS & REVIEWS =====

  getTemplateReviews(templateId: string, limit: number = 20, offset: number = 0): Observable<any[]> {
    const params = new HttpParams()
      .set('limit', limit.toString())
      .set('offset', offset.toString());
    return this.http.get<any[]>(`${this.baseUrl}/templates/${templateId}/reviews`, { params });
  }

  rateTemplate(templateId: string, ratingData: { rating: number; review?: string }): Observable<any> {
    return this.http.post(`${this.baseUrl}/templates/${templateId}/rate`, ratingData);
  }

  createTemplateReview(templateId: string, reviewData: any): Observable<any> {
    return this.http.post(`${this.baseUrl}/templates/${templateId}/review`, reviewData);
  }

  markReviewHelpful(reviewId: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/templates/reviews/${reviewId}/helpful`, {});
  }

  // ===== COMMUNITY FEATURES =====

  addToFavorites(templateId: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/templates/${templateId}/favorite`, {});
  }

  removeFromFavorites(templateId: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/templates/${templateId}/favorite`);
  }

  getUserFavorites(): Observable<WorkflowTemplate[]> {
    return this.http.get<WorkflowTemplate[]>(`${this.baseUrl}/templates/favorites`);
  }

  // ===== TEMPLATE VERSIONING =====

  getTemplateVersions(templateId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/templates/${templateId}/versions`);
  }

  createTemplateVersion(templateId: string, versionData: any): Observable<any> {
    return this.http.post(`${this.baseUrl}/templates/${templateId}/versions`, versionData);
  }

  // ===== IMPORT/EXPORT =====

  exportTemplate(templateId: string): Observable<any> {
    return this.http.get(`${this.baseUrl}/templates/${templateId}/export`);
  }

  importTemplate(templateData: any): Observable<WorkflowTemplate> {
    return this.http.post<WorkflowTemplate>(`${this.baseUrl}/templates/import`, templateData);
  }

  publishTemplate(templateId: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/templates/${templateId}/publish`, {});
  }

  createTemplateFromWorkflow(templateRequest: any): Observable<WorkflowTemplate> {
    return this.http.post<WorkflowTemplate>(`${this.baseUrl}/templates/from-workflow`, templateRequest);
  }

  // Workflow Executions
  executeWorkflow(
    workflowId: string,
    parameters: { [key: string]: any } = {},
    triggerType: string = 'manual',
    triggerData: { [key: string]: any } = {}
  ): Observable<WorkflowExecution> {
    return this.http.post<WorkflowExecution>(`${this.baseUrl}/workflows/${workflowId}/execute`, {
      parameters,
      triggerType,
      triggerData
    });
  }

  getExecutions(
    workflowId?: string,
    projectId?: string,
    status?: string,
    limit: number = 50,
    offset: number = 0
  ): Observable<WorkflowExecution[]> {
    let params = new HttpParams()
      .set('limit', limit.toString())
      .set('offset', offset.toString());

    if (workflowId) params = params.set('workflowId', workflowId);
    if (projectId) params = params.set('projectId', projectId);
    if (status) params = params.set('status', status);

    return this.http.get<WorkflowExecution[]>(`${this.baseUrl}/executions`, { params });
  }

  getExecution(id: string): Observable<WorkflowExecution> {
    return this.http.get<WorkflowExecution>(`${this.baseUrl}/executions/${id}`);
  }

  cancelExecution(id: string): Observable<void> {
    return this.http.post<void>(`${this.baseUrl}/executions/${id}/cancel`, {});
  }

  getExecutionSteps(executionId: string): Observable<WorkflowStepExecution[]> {
    return this.http.get<WorkflowStepExecution[]>(`${this.baseUrl}/executions/${executionId}/steps`);
  }

  getExecutionStep(executionId: string, stepId: string): Observable<WorkflowStepExecution> {
    return this.http.get<WorkflowStepExecution>(`${this.baseUrl}/executions/${executionId}/steps/${stepId}`);
  }

  // State management
  setCurrentWorkflow(workflow: WorkflowDefinition | null): void {
    this.currentWorkflowSubject.next(workflow);
  }

  getCurrentWorkflow(): WorkflowDefinition | null {
    return this.currentWorkflowSubject.value;
  }

  // Utility methods
  generateStepId(): string {
    return 'step_' + Math.random().toString(36).substr(2, 9);
  }

  generateConnectionId(): string {
    return 'conn_' + Math.random().toString(36).substr(2, 9);
  }

  exportWorkflow(workflow: WorkflowDefinition): string {
    return JSON.stringify(workflow, null, 2);
  }

  importWorkflow(jsonData: string): WorkflowDefinition {
    try {
      const workflow = JSON.parse(jsonData) as WorkflowDefinition;
      // Generate new IDs to avoid conflicts
      workflow.id = '';
      workflow.steps.forEach(step => {
        step.id = this.generateStepId();
      });
      return workflow;
    } catch (error) {
      throw new Error('Invalid workflow JSON format');
    }
  }

  cloneWorkflow(workflow: WorkflowDefinition): WorkflowDefinition {
    const cloned = JSON.parse(JSON.stringify(workflow)) as WorkflowDefinition;
    cloned.id = '';
    cloned.name = `${workflow.name} (Copy)`;
    cloned.steps.forEach(step => {
      step.id = this.generateStepId();
    });
    return cloned;
  }

  // Monitoring endpoints
  getExecutionMetrics(executionId: string): Observable<any> {
    return this.http.get(`${this.baseUrl}/monitoring/executions/${executionId}/metrics`);
  }

  getAllExecutionMetrics(): Observable<any> {
    return this.http.get(`${this.baseUrl}/monitoring/executions/metrics`);
  }

  getExecutionEvents(executionId: string, limit: number = 50, offset: number = 0, type?: string): Observable<any> {
    let params = new HttpParams()
      .set('limit', limit.toString())
      .set('offset', offset.toString());

    if (type) {
      params = params.set('type', type);
    }

    return this.http.get(`${this.baseUrl}/monitoring/executions/${executionId}/events`, { params });
  }

  getExecutionDashboard(projectId?: string): Observable<any> {
    let params = new HttpParams();

    if (projectId) {
      params = params.set('projectId', projectId);
    }

    return this.http.get(`${this.baseUrl}/monitoring/dashboard`, { params });
  }

  getExecutionPerformance(projectId?: string, workflowId?: string, limit: number = 100): Observable<any> {
    let params = new HttpParams().set('limit', limit.toString());

    if (projectId) {
      params = params.set('projectId', projectId);
    }

    if (workflowId) {
      params = params.set('workflowId', workflowId);
    }

    return this.http.get(`${this.baseUrl}/monitoring/performance`, { params });
  }

  getSystemHealth(): Observable<any> {
    return this.http.get(`${this.baseUrl}/monitoring/health`);
  }

  // Workflow instance management methods
  getWorkflowInstances(): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/instances`);
  }

  getInstanceLabels(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/instances/labels`);
  }

  getInstancesByLabels(labels: { [key: string]: string }): Observable<any> {
    const params = new URLSearchParams();
    Object.entries(labels).forEach(([key, value]) => {
      params.append(key, value);
    });
    return this.http.get<any>(`${this.baseUrl}/instances/by-labels?${params.toString()}`);
  }

  updateInstanceLabels(instanceId: string, labels: { [key: string]: string }): Observable<any> {
    return this.http.put<any>(`${this.baseUrl}/instances/${instanceId}/labels`, { labels });
  }

  removeInstanceLabel(instanceId: string, labelKey: string): Observable<any> {
    return this.http.delete<any>(`${this.baseUrl}/instances/${instanceId}/labels/${labelKey}`);
  }

  bulkUpdateInstanceLabels(instanceIds: string[], labels: { [key: string]: string }, operation: 'add' | 'remove' | 'replace'): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/instances/bulk/labels`, {
      instanceIds,
      labels,
      operation
    });
  }

  // Audit log methods
  getAuditLogs(filters?: any, limit?: number, offset?: number): Observable<any[]> {
    let params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          params.append(key, value as string);
        }
      });
    }
    if (limit) params.append('limit', limit.toString());
    if (offset) params.append('offset', offset.toString());

    return this.http.get<any[]>(`${this.baseUrl}/audit/logs?${params.toString()}`);
  }

  // Notification rule methods
  getNotificationRules(projectId?: string): Observable<any[]> {
    const params = projectId ? `?projectId=${projectId}` : '';
    return this.http.get<any[]>(`${this.baseUrl}/notifications/rules${params}`);
  }

  createNotificationRule(rule: any): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/notifications/rules`, rule);
  }

  updateNotificationRule(ruleId: string, rule: any): Observable<any> {
    return this.http.put<any>(`${this.baseUrl}/notifications/rules/${ruleId}`, rule);
  }

  deleteNotificationRule(ruleId: string): Observable<any> {
    return this.http.delete<any>(`${this.baseUrl}/notifications/rules/${ruleId}`);
  }
}
