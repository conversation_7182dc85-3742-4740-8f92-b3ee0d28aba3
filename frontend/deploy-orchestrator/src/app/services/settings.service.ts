import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

interface SystemSetting {
  key: string;
  value: string;
  description: string;
  type: 'string' | 'number' | 'boolean' | 'json';
  category: string;
  isSecret?: boolean;
}

interface CreateSettingRequest {
  key: string;
  value: string;
  description?: string;
  type: 'string' | 'number' | 'boolean' | 'json';
  category: string;
  isSecret?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class SettingsService {
  private apiUrl = `${environment.apiV1Url}/settings`;

  constructor(private http: HttpClient) { }

  getSettings(): Observable<SystemSetting[]> {
    return this.http.get<SystemSetting[]>(this.apiUrl);
  }

  getSetting(key: string): Observable<SystemSetting> {
    return this.http.get<SystemSetting>(`${this.apiUrl}/${key}`);
  }

  getSettingsByCategory(category: string): Observable<SystemSetting[]> {
    return this.http.get<SystemSetting[]>(`${this.apiUrl}/category/${category}`);
  }

  createSetting(setting: CreateSettingRequest): Observable<SystemSetting> {
    return this.http.post<SystemSetting>(this.apiUrl, setting);
  }

  updateSetting(key: string, value: string): Observable<SystemSetting> {
    return this.http.put<SystemSetting>(`${this.apiUrl}/${key}`, { value });
  }

  deleteSetting(key: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${key}`);
  }

  resetToDefaults(): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/reset`, {});
  }

  exportSettings(): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/export`, {
      responseType: 'blob'
    });
  }

  importSettings(formData: FormData): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/import`, formData);
  }

  validateSetting(key: string, value: string, type: string): Observable<{ valid: boolean; message?: string }> {
    return this.http.post<{ valid: boolean; message?: string }>(`${this.apiUrl}/validate`, {
      key,
      value,
      type
    });
  }

  getSettingHistory(key: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/${key}/history`);
  }

  // Convenience methods for common settings
  getAppName(): Observable<string> {
    return this.http.get<string>(`${this.apiUrl}/app.name/value`);
  }

  getAppVersion(): Observable<string> {
    return this.http.get<string>(`${this.apiUrl}/app.version/value`);
  }

  getMaintenanceMode(): Observable<boolean> {
    return this.http.get<boolean>(`${this.apiUrl}/app.maintenance_mode/value`);
  }

  setMaintenanceMode(enabled: boolean): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/app.maintenance_mode`, { value: enabled.toString() });
  }

  getMaxFileUploadSize(): Observable<number> {
    return this.http.get<number>(`${this.apiUrl}/app.max_file_upload_size/value`);
  }

  getSessionTimeout(): Observable<number> {
    return this.http.get<number>(`${this.apiUrl}/security.session_timeout/value`);
  }

  getPasswordPolicy(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/security.password_policy/value`);
  }

  getNotificationSettings(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/notifications.settings/value`);
  }

  getDeploymentDefaults(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/deployment.defaults/value`);
  }

  getAuditSettings(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/audit.settings/value`);
  }

  // Bulk operations
  bulkUpdateSettings(settings: { [key: string]: string }): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/bulk`, { settings });
  }

  bulkDeleteSettings(keys: string[]): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/bulk`, { body: { keys } });
  }
}
