import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map } from 'rxjs/operators';
import { EnvironmentConfig } from './environment.service';

// Execution Types
export type ExecutionStatus = 'pending' | 'running' | 'success' | 'failed' | 'cancelled' | 'paused';
export type StepStatus = 'pending' | 'running' | 'success' | 'failed' | 'skipped';

export interface VersionInfo {
  number: string;
  gitCommit?: string;
  gitBranch?: string;
  gitTag?: string;
  buildId?: string;
  artifacts?: Artifact[];
}

export interface Artifact {
  id: string;
  name: string;
  type: string;
  location: string;
  size: number;
  checksum: string;
  metadata?: { [key: string]: string };
}

export interface WorkflowStepExecution {
  id: string;
  stepName: string;
  stepType: string;
  status: StepStatus;
  startedAt?: string;
  completedAt?: string;
  duration?: number;
  config?: { [key: string]: any };
  output?: { [key: string]: any };
  logs?: LogEntry[];
  metrics?: { [key: string]: number };
  artifacts?: Artifact[];
  errorMessage?: string;
  errorDetails?: string;
  retryCount: number;
  maxRetries: number;
  retryOnFailure: boolean;
}

export interface DeployedService {
  name: string;
  type: string;
  version: string;
  status: string;
  endpoints?: ServiceEndpoint[];
  resources: {
    cpu?: string;
    memory?: string;
    replicas?: number;
  };
  healthCheck: {
    status: string;
    lastCheck: string;
    endpoint?: string;
  };
  labels?: { [key: string]: string };
  annotations?: { [key: string]: string };
}

export interface ServiceEndpoint {
  name: string;
  url: string;
  type: string;
  port: number;
  protocol: string;
  public: boolean;
}

export interface LogEntry {
  id: string;
  timestamp: string;
  level: string;
  stepName: string;
  message: string;
  source?: string;
  context?: { [key: string]: any };
  progress?: number;
  isNew?: boolean;
  isImportant?: boolean;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  projectId: string;
  environmentId: string;
  version: VersionInfo;
  status: ExecutionStatus;
  startedAt: string;
  completedAt?: string;
  duration?: number;
  triggerType: string;
  triggerBy: string;
  parameters: { [key: string]: any };
  variables: { [key: string]: string };
  steps: WorkflowStepExecution[];
  deployedServices: DeployedService[];
  logStreamId: string;
  metricsEnabled: boolean;
  errorMessage?: string;
  errorDetails?: string;
  createdAt: string;
  updatedAt: string;
  workflow?: any;
  project?: any;
  environment?: any;
}

export interface ExecutionSummary {
  id: string;
  workflowName: string;
  projectName: string;
  environment: string;
  version: string;
  status: ExecutionStatus;
  startedAt: string;
  duration?: number;
  triggerBy: string;
  totalSteps: number;
  completedSteps: number;
  failedSteps: number;
  servicesDeployed: number;
  healthStatus?: string;
}

export interface StartExecutionRequest {
  workflowId: string;
  projectId: string;
  environmentId: string;
  version: VersionInfo;
  triggerType: string;
  triggerBy: string;
  parameters?: { [key: string]: any };
  variables?: { [key: string]: string };
}

export interface ExecutionFilter {
  projectId?: string;
  workflowId?: string;
  environmentId?: string;
  status?: string;
  triggerType?: string;
  limit?: number;
  offset?: number;
}

export interface LogFilter {
  level?: string;
  stepName?: string;
  since?: string;
  until?: string;
  limit?: number;
  offset?: number;
  search?: string;
}

export interface LogStats {
  total: number;
  byLevel: { [level: string]: number };
}

@Injectable({
  providedIn: 'root'
})
export class WorkflowExecutionService {
  private baseUrl = '/api/v1/workflow-service';
  private executionsSubject = new BehaviorSubject<WorkflowExecution[]>([]);
  public executions$ = this.executionsSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Execution Management
  startExecution(request: StartExecutionRequest): Observable<WorkflowExecution> {
    return this.http.post<WorkflowExecution>(`${this.baseUrl}/executions`, request);
  }

  getExecution(id: string): Observable<WorkflowExecution> {
    return this.http.get<WorkflowExecution>(`${this.baseUrl}/executions/${id}`);
  }

  getExecutions(filter?: ExecutionFilter): Observable<{ executions: WorkflowExecution[], total: number }> {
    let params = new HttpParams();
    if (filter) {
      Object.keys(filter).forEach(key => {
        const value = (filter as any)[key];
        if (value !== null && value !== undefined && value !== '') {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<{ executions: WorkflowExecution[], total: number }>(`${this.baseUrl}/executions`, {
      params
    }).pipe(
      map(response => {
        this.executionsSubject.next(response.executions);
        return response;
      })
    );
  }

  cancelExecution(id: string, reason: string): Observable<void> {
    return this.http.post<void>(`${this.baseUrl}/executions/${id}/cancel`, { reason });
  }

  retryExecution(id: string): Observable<WorkflowExecution> {
    return this.http.post<WorkflowExecution>(`${this.baseUrl}/executions/${id}/retry`, {});
  }

  // Execution Summaries
  getExecutionSummaries(filter?: ExecutionFilter): Observable<{ summaries: ExecutionSummary[], total: number }> {
    let params = new HttpParams();
    if (filter) {
      Object.keys(filter).forEach(key => {
        const value = (filter as any)[key];
        if (value !== null && value !== undefined && value !== '') {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<{ summaries: ExecutionSummary[], total: number }>(`${this.baseUrl}/executions/summaries`, {
      params
    });
  }

  // Project Executions
  getProjectExecutions(projectId: string, filter?: ExecutionFilter): Observable<{ executions: WorkflowExecution[] }> {
    let params = new HttpParams();
    if (filter) {
      Object.keys(filter).forEach(key => {
        const value = (filter as any)[key];
        if (value !== null && value !== undefined && value !== '') {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<{ executions: WorkflowExecution[] }>(`${this.baseUrl}/projects/${projectId}/executions`, {
      params
    });
  }

  // Environment Executions
  getEnvironmentExecutions(environmentId: string, filter?: ExecutionFilter): Observable<{ executions: WorkflowExecution[] }> {
    let params = new HttpParams();
    if (filter) {
      Object.keys(filter).forEach(key => {
        const value = (filter as any)[key];
        if (value !== null && value !== undefined && value !== '') {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<{ executions: WorkflowExecution[] }>(`${this.baseUrl}/environments/${environmentId}/executions`, {
      params
    });
  }

  // Logging
  getExecutionLogs(id: string, filter?: LogFilter): Observable<{ logs: LogEntry[] }> {
    let params = new HttpParams();
    if (filter) {
      Object.keys(filter).forEach(key => {
        const value = (filter as any)[key];
        if (value !== null && value !== undefined && value !== '') {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<{ logs: LogEntry[] }>(`${this.baseUrl}/executions/${id}/logs`, {
      params
    });
  }

  getLogStats(executionId: string): Observable<LogStats> {
    return this.http.get<LogStats>(`${this.baseUrl}/executions/${executionId}/logs/stats`);
  }

  // Step Management
  getStepLogs(executionId: string, stepId: string, filter?: LogFilter): Observable<{ logs: LogEntry[] }> {
    let params = new HttpParams();
    if (filter) {
      Object.keys(filter).forEach(key => {
        const value = (filter as any)[key];
        if (value !== null && value !== undefined && value !== '') {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<{ logs: LogEntry[] }>(`${this.baseUrl}/executions/${executionId}/steps/${stepId}/logs`, {
      params
    });
  }

  retryStep(executionId: string, stepId: string): Observable<WorkflowStepExecution> {
    return this.http.post<WorkflowStepExecution>(`${this.baseUrl}/executions/${executionId}/steps/${stepId}/retry`, {});
  }

  skipStep(executionId: string, stepId: string, reason: string): Observable<WorkflowStepExecution> {
    return this.http.post<WorkflowStepExecution>(`${this.baseUrl}/executions/${executionId}/steps/${stepId}/skip`, { reason });
  }

  // Metrics and Monitoring
  getExecutionMetrics(id: string): Observable<{ metrics: any }> {
    return this.http.get<{ metrics: any }>(`${this.baseUrl}/executions/${id}/metrics`);
  }

  getStepMetrics(executionId: string, stepId: string): Observable<{ metrics: any }> {
    return this.http.get<{ metrics: any }>(`${this.baseUrl}/executions/${executionId}/steps/${stepId}/metrics`);
  }

  // Artifacts
  getExecutionArtifacts(id: string): Observable<{ artifacts: Artifact[] }> {
    return this.http.get<{ artifacts: Artifact[] }>(`${this.baseUrl}/executions/${id}/artifacts`);
  }

  getStepArtifacts(executionId: string, stepId: string): Observable<{ artifacts: Artifact[] }> {
    return this.http.get<{ artifacts: Artifact[] }>(`${this.baseUrl}/executions/${executionId}/steps/${stepId}/artifacts`);
  }

  downloadArtifact(artifactId: string): Observable<Blob> {
    return this.http.get(`${this.baseUrl}/artifacts/${artifactId}/download`, {
      responseType: 'blob'
    });
  }

  // Promotion Methods
  startPromotion(request: any): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/promotions`, request);
  }

  getPromotionStatus(promotionId: string): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/promotions/${promotionId}`);
  }

  getPromotionHistory(projectId: string): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/promotions/history/${projectId}`);
  }

  // Utility Methods
  refreshExecutions(): void {
    this.getExecutions().subscribe();
  }

  getCurrentExecutions(): WorkflowExecution[] {
    return this.executionsSubject.value;
  }

  getExecutionsByProject(projectId: string): WorkflowExecution[] {
    return this.executionsSubject.value.filter(exec => exec.projectId === projectId);
  }

  getExecutionsByEnvironment(environmentId: string): WorkflowExecution[] {
    return this.executionsSubject.value.filter(exec => exec.environmentId === environmentId);
  }

  getRunningExecutions(): WorkflowExecution[] {
    return this.executionsSubject.value.filter(exec => exec.status === 'running' || exec.status === 'pending');
  }

  // Status helpers
  isExecutionRunning(execution: WorkflowExecution): boolean {
    return execution.status === 'running' || execution.status === 'pending';
  }

  isExecutionComplete(execution: WorkflowExecution): boolean {
    return execution.status === 'success' || execution.status === 'failed' || execution.status === 'cancelled';
  }

  getExecutionProgress(execution: WorkflowExecution): number {
    if (execution.steps.length === 0) return 0;

    const completedSteps = execution.steps.filter(step =>
      step.status === 'success' || step.status === 'failed' || step.status === 'skipped'
    ).length;

    return Math.round((completedSteps / execution.steps.length) * 100);
  }

  getStepProgress(step: WorkflowStepExecution): number {
    switch (step.status) {
      case 'pending': return 0;
      case 'running': return 50;
      case 'success': return 100;
      case 'failed': return 100;
      case 'skipped': return 100;
      default: return 0;
    }
  }
}
