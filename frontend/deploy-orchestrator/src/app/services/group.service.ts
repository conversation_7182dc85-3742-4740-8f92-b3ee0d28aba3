import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Group, Role } from '../models/user.model';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class GroupService {
  private apiUrl = `${environment.apiUrl}`;

  constructor(private http: HttpClient) { }

  getGroups(): Observable<Group[]> {
    return this.http.get<Group[]>(`${this.apiUrl}/groups`);
  }

  getGroup(id: string): Observable<Group> {
    return this.http.get<Group>(`${this.apiUrl}/groups/${id}`);
  }

  createGroup(group: Group): Observable<Group> {
    return this.http.post<Group>(`${this.apiUrl}/groups`, group);
  }

  updateGroup(group: Group): Observable<Group> {
    return this.http.put<Group>(`${this.apiUrl}/groups/${group.id}`, group);
  }

  deleteGroup(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/groups/${id}`);
  }

  // Group synchronization methods
  syncAllUsers(providerId: string, providerType: string, dryRun: boolean): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/sync-all-users`, {
      providerId,
      providerType,
      dryRun
    });
  }

  syncUserByUsername(username: string, providerId: string, providerType: string, dryRun: boolean): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/sync-user-by-username`, {
      username,
      providerId,
      providerType,
      dryRun
    });
  }

  // Group role management
  getRolesForGroup(groupId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/groups/${groupId}/roles`);
  }

  assignRoleToGroup(groupId: string, roleId: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/groups/${groupId}/roles/${roleId}`, {});
  }

  removeRoleFromGroup(groupId: string, roleId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/groups/${groupId}/roles/${roleId}`);
  }

  // Group-Project Assignment Methods (NEW MODEL)
  assignGroupToProject(groupId: string, projectId: string): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/group-projects`, { groupId, projectId });
  }

  removeGroupFromProject(groupId: string, projectId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/group-projects/${groupId}/${projectId}`);
  }

  getGroupProjects(groupId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/groups/${groupId}/projects`);
  }

  getProjectGroups(projectId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/projects/${projectId}/groups`);
  }

  getUserProjects(userId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/users/${userId}/projects`);
  }

  checkUserProjectAccess(userId: string, projectId: string): Observable<{ hasAccess: boolean }> {
    return this.http.get<{ hasAccess: boolean }>(`${this.apiUrl}/users/${userId}/project-access/${projectId}`);
  }

  getGroupProjectAssignments(params?: any): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/group-projects`, { params });
  }

  bulkAssignGroupsToProject(groupIds: string[], projectId: string): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/group-projects/bulk-groups`, { groupIds, projectId });
  }

  bulkAssignProjectsToGroup(projectIds: string[], groupId: string): Observable<any> {
    return this.http.post<any>(`${this.apiUrl}/group-projects/bulk-projects`, { projectIds, groupId });
  }

  getUserProjectMatrix(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/group-projects/matrix`);
  }
}
