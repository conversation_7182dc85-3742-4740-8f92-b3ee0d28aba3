import { Injectable } from '@angular/core';
import { AuthService } from './auth.service';

/**
 * Centralized service for refreshing all application data after authentication
 * This service manages data refresh for all entity services in a centralized way
 */
@Injectable({
  providedIn: 'root'
})
export class DataRefreshService {
  
  // Registry of refresh functions from various services
  private refreshFunctions: (() => void)[] = [];

  constructor(private authService: AuthService) {}

  /**
   * Register a refresh function from a service
   * Services should call this in their constructor to register their refresh method
   */
  registerRefreshFunction(refreshFn: () => void): void {
    this.refreshFunctions.push(refreshFn);
  }

  /**
   * Refresh all registered data sources
   * This should be called after successful authentication
   */
  refreshAllData(): void {
    if (!this.authService.isAuthenticated()) {
      console.warn('DataRefreshService: Cannot refresh data - user not authenticated');
      return;
    }

    console.log('DataRefreshService: Refreshing all application data');
    
    // Call all registered refresh functions
    this.refreshFunctions.forEach((refreshFn, index) => {
      try {
        refreshFn();
        console.log(`DataRefreshService: Successfully refreshed data source ${index + 1}`);
      } catch (error) {
        console.error(`DataRefreshService: Error refreshing data source ${index + 1}:`, error);
      }
    });
  }

  /**
   * Clear all registered refresh functions
   * Useful for testing or when services are destroyed
   */
  clearRefreshFunctions(): void {
    this.refreshFunctions = [];
  }

  /**
   * Get the number of registered refresh functions
   * Useful for debugging
   */
  getRegisteredCount(): number {
    return this.refreshFunctions.length;
  }
}
