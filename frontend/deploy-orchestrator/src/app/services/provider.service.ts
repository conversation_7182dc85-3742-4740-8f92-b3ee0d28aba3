import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map } from 'rxjs/operators';

// Provider Types (matching backend)
export type ProviderType =
  // Kubernetes providers
  | 'gke' | 'aks' | 'eks' | 'openshift' | 'k3s' | 'microk8s' | 'rancher'
  // Cloud VM providers
  | 'gce' | 'ec2' | 'azure-vm' | 'digitalocean' | 'linode' | 'vultr'
  // Container platforms
  | 'docker-swarm' | 'nomad' | 'mesos'
  // Serverless platforms
  | 'lambda' | 'cloud-functions' | 'azure-functions' | 'cloudflare-workers' | 'vercel' | 'netlify'
  // Edge computing
  | 'cloudflare-edge' | 'aws-wavelength' | 'azure-edge'
  // On-premise/Hybrid
  | 'bare-metal' | 'vmware' | 'hyper-v' | 'proxmox' | 'openstack'
  // CI/CD platforms
  | 'github-actions' | 'gitlab-ci' | 'jenkins' | 'circleci' | 'travis-ci';

export type AuthMethod = 'service-account' | 'oauth' | 'api-key' | 'token' | 'certificate' | 'basic' | 'ssh' | 'iam' | 'mtls';

export type DeploymentStrategy = 'rolling' | 'blue-green' | 'canary' | 'recreate' | 'immediate';

export type ProviderCapability =
  | 'containers' | 'load-balancing' | 'auto-scaling' | 'persistent-storage'
  | 'networking' | 'monitoring' | 'logging' | 'secrets' | 'ingress'
  | 'service-mesh' | 'gpu' | 'spot-instances' | 'multi-region'
  | 'backup' | 'disaster-recovery';

export interface ConfigField {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'multiselect' | 'password' | 'file';
  label: string;
  description: string;
  required: boolean;
  default?: any;
  options?: Option[];
  validation?: Validation;
  sensitive?: boolean;
  group?: string;
  dependsOn?: string;
  placeholder?: string;
}

export interface Option {
  value: string;
  label: string;
}

export interface Validation {
  pattern?: string;
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
}

export interface Region {
  id: string;
  name: string;
  location: string;
  available: boolean;
}

export interface InstanceType {
  id: string;
  name: string;
  cpu: string;
  memory: string;
  storage?: string;
  network?: string;
  gpu?: string;
  pricePerHour?: number;
}

export interface Version {
  id: string;
  name: string;
  supported: boolean;
  default?: boolean;
  deprecated?: boolean;
}

export interface ProviderInfo {
  type: ProviderType;
  name: string;
  description: string;
  category: string;
  icon?: string;
  documentation?: string;
  capabilities: ProviderCapability[];
  authMethods: AuthMethod[];
  strategies: DeploymentStrategy[];
  configFields: ConfigField[];
  regions?: Region[];
  instanceTypes?: InstanceType[];
  versions?: Version[];
}

export interface ProviderMetadata {
  version: string;
  author: string;
  license: string;
  homepage?: string;
  repository?: string;
  tags?: string[];
  experimental?: boolean;
  deprecated?: boolean;
  replacement?: string;
  minVersion?: string;
  maxVersion?: string;
  dependencies?: string[];
  conflicts?: string[];
  extra?: { [key: string]: any };
}

export interface ExtendedProviderInfo extends ProviderInfo {
  metadata?: ProviderMetadata;
}

export interface ProviderValidationResult {
  valid: boolean;
  errors?: string[];
}

export interface ProviderConfigForm {
  [fieldName: string]: any;
}

@Injectable({
  providedIn: 'root'
})
export class ProviderService {
  private baseUrl = '/api/v1/environment-service';
  private providersSubject = new BehaviorSubject<ProviderInfo[]>([]);
  public providers$ = this.providersSubject.asObservable();

  // Provider categories
  readonly categories = [
    { id: 'kubernetes', name: 'Kubernetes', icon: 'kubernetes' },
    { id: 'vm', name: 'Virtual Machines', icon: 'server' },
    { id: 'serverless', name: 'Serverless', icon: 'cloud' },
    { id: 'container', name: 'Container Platforms', icon: 'container' },
    { id: 'edge', name: 'Edge Computing', icon: 'edge' },
    { id: 'hybrid', name: 'Hybrid/On-Premise', icon: 'hybrid' },
    { id: 'cicd', name: 'CI/CD Platforms', icon: 'pipeline' }
  ];

  constructor(private http: HttpClient) {
    this.loadProviders();
  }

  // Provider Management
  getProviders(): Observable<{ providers: ProviderInfo[] }> {
    return this.http.get<{ providers: ProviderInfo[] }>(`${this.baseUrl}/providers`).pipe(
      map(response => {
        this.providersSubject.next(response.providers);
        return response;
      })
    );
  }

  getProvider(type: ProviderType): Observable<ProviderInfo> {
    return this.http.get<ProviderInfo>(`${this.baseUrl}/providers/${type}`);
  }

  getProviderCapabilities(type: ProviderType): Observable<ProviderInfo> {
    return this.http.get<ProviderInfo>(`${this.baseUrl}/providers/${type}/capabilities`);
  }

  validateProviderConfig(type: ProviderType, config: ProviderConfigForm): Observable<ProviderValidationResult> {
    return this.http.post<ProviderValidationResult>(`${this.baseUrl}/providers/${type}/validate-config`, config);
  }

  testProviderConnection(type: ProviderType, config: ProviderConfigForm): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/providers/${type}/test-connection`, config);
  }

  // Provider Discovery
  getProvidersByCategory(category: string): ProviderInfo[] {
    return this.providersSubject.value.filter(provider => provider.category === category);
  }

  getProvidersByCapability(capability: ProviderCapability): ProviderInfo[] {
    return this.providersSubject.value.filter(provider =>
      provider.capabilities.includes(capability)
    );
  }

  searchProviders(query: string): ProviderInfo[] {
    const searchTerm = query.toLowerCase();
    return this.providersSubject.value.filter(provider =>
      provider.name.toLowerCase().includes(searchTerm) ||
      provider.description.toLowerCase().includes(searchTerm) ||
      provider.type.toLowerCase().includes(searchTerm) ||
      provider.category.toLowerCase().includes(searchTerm)
    );
  }

  // Configuration Helpers
  getConfigFieldsByGroup(provider: ProviderInfo): { [group: string]: ConfigField[] } {
    const grouped: { [group: string]: ConfigField[] } = {};

    provider.configFields.forEach(field => {
      const group = field.group || 'general';
      if (!grouped[group]) {
        grouped[group] = [];
      }
      grouped[group].push(field);
    });

    return grouped;
  }

  getRequiredFields(provider: ProviderInfo): ConfigField[] {
    return provider.configFields.filter(field => field.required);
  }

  getSensitiveFields(provider: ProviderInfo): ConfigField[] {
    return provider.configFields.filter(field => field.sensitive);
  }

  getConditionalFields(provider: ProviderInfo): ConfigField[] {
    return provider.configFields.filter(field => field.dependsOn);
  }

  // Validation Helpers
  validateField(field: ConfigField, value: any): string[] {
    const errors: string[] = [];

    // Required validation
    if (field.required && (value === null || value === undefined || value === '')) {
      errors.push(`${field.label} is required`);
      return errors;
    }

    // Skip other validations if field is empty and not required
    if (!value && !field.required) {
      return errors;
    }

    // Type validation
    switch (field.type) {
      case 'number':
        if (isNaN(Number(value))) {
          errors.push(`${field.label} must be a number`);
        }
        break;
      case 'boolean':
        if (typeof value !== 'boolean') {
          errors.push(`${field.label} must be true or false`);
        }
        break;
    }

    // Validation rules
    if (field.validation) {
      const validation = field.validation;

      if (validation.pattern) {
        const regex = new RegExp(validation.pattern);
        if (!regex.test(String(value))) {
          errors.push(`${field.label} format is invalid`);
        }
      }

      if (field.type === 'number') {
        const numValue = Number(value);
        if (validation.min !== undefined && numValue < validation.min) {
          errors.push(`${field.label} must be at least ${validation.min}`);
        }
        if (validation.max !== undefined && numValue > validation.max) {
          errors.push(`${field.label} must be at most ${validation.max}`);
        }
      }

      if (field.type === 'string') {
        const strValue = String(value);
        if (validation.minLength !== undefined && strValue.length < validation.minLength) {
          errors.push(`${field.label} must be at least ${validation.minLength} characters`);
        }
        if (validation.maxLength !== undefined && strValue.length > validation.maxLength) {
          errors.push(`${field.label} must be at most ${validation.maxLength} characters`);
        }
      }
    }

    return errors;
  }

  validateConfig(provider: ProviderInfo, config: ProviderConfigForm): { [fieldName: string]: string[] } {
    const errors: { [fieldName: string]: string[] } = {};

    provider.configFields.forEach(field => {
      const fieldErrors = this.validateField(field, config[field.name]);
      if (fieldErrors.length > 0) {
        errors[field.name] = fieldErrors;
      }
    });

    return errors;
  }

  // Form Helpers
  createDefaultConfig(provider: ProviderInfo): ProviderConfigForm {
    const config: ProviderConfigForm = {};

    provider.configFields.forEach(field => {
      if (field.default !== undefined) {
        config[field.name] = field.default;
      }
    });

    return config;
  }

  shouldShowField(field: ConfigField, config: ProviderConfigForm): boolean {
    if (!field.dependsOn) {
      return true;
    }

    const dependentValue = config[field.dependsOn];
    return !!dependentValue;
  }

  // UI Helpers
  getProviderIcon(type: ProviderType): string {
    const iconMap: { [key in ProviderType]?: string } = {
      'gke': 'gcp',
      'aks': 'azure',
      'eks': 'aws',
      'openshift': 'redhat',
      'k3s': 'kubernetes',
      'microk8s': 'kubernetes',
      'rancher': 'rancher',
      'gce': 'gcp',
      'ec2': 'aws',
      'azure-vm': 'azure',
      'digitalocean': 'digitalocean',
      'linode': 'linode',
      'vultr': 'vultr',
      'docker-swarm': 'docker',
      'nomad': 'hashicorp',
      'mesos': 'apache',
      'lambda': 'aws',
      'cloud-functions': 'gcp',
      'azure-functions': 'azure',
      'cloudflare-workers': 'cloudflare',
      'vercel': 'vercel',
      'netlify': 'netlify',
      'bare-metal': 'server',
      'vmware': 'vmware',
      'github-actions': 'github',
      'gitlab-ci': 'gitlab',
      'jenkins': 'jenkins',
      'circleci': 'circleci'
    };

    return iconMap[type] || 'cloud';
  }

  getCategoryIcon(category: string): string {
    const categoryInfo = this.categories.find(c => c.id === category);
    return categoryInfo?.icon || 'cloud';
  }

  // Utility Methods
  private loadProviders(): void {
    this.getProviders().subscribe();
  }

  refreshProviders(): void {
    this.loadProviders();
  }

  getCurrentProviders(): ProviderInfo[] {
    return this.providersSubject.value;
  }

  getProviderByType(type: ProviderType): ProviderInfo | undefined {
    return this.providersSubject.value.find(provider => provider.type === type);
  }

  isProviderSupported(type: ProviderType): boolean {
    return this.providersSubject.value.some(provider => provider.type === type);
  }

  getProviderCount(): number {
    return this.providersSubject.value.length;
  }

  getProviderCountByCategory(): { [category: string]: number } {
    const counts: { [category: string]: number } = {};

    this.providersSubject.value.forEach(provider => {
      counts[provider.category] = (counts[provider.category] || 0) + 1;
    });

    return counts;
  }
}
