import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, combineLatest } from 'rxjs';
import { map, switchMap, catchError, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { AuthService } from './auth.service';
import {
  PluginPermission,
  UserPluginPermissions,
  PluginAccessControl,
  PluginPermissionCheck,
  PluginFeatureAccess,
  PluginPermissionMatrix,
  PluginPermissionRequest,
  PluginPermissionResponse,
  PluginSecurityContext,
  PluginAuditLog,
  PLUGIN_PERMISSIONS,
  PLUGIN_FEATURES,
  PluginPermissionType,
  PluginFeature
} from '../models/plugin-permission.model';

@Injectable({
  providedIn: 'root'
})
export class PluginPermissionService {
  private baseUrl = `${environment.apiUrl}/admin-service/`;
  private permissionMatrixSubject = new BehaviorSubject<PluginPermissionMatrix | null>(null);
  private securityContextSubject = new BehaviorSubject<PluginSecurityContext | null>(null);

  public permissionMatrix$ = this.permissionMatrixSubject.asObservable();
  public securityContext$ = this.securityContextSubject.asObservable();

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {
    // Load permissions when user authentication changes
    this.authService.currentUser$.subscribe(user => {
      if (user) {
        this.loadUserPermissions();
        this.loadSecurityContext();
      } else {
        this.permissionMatrixSubject.next(null);
        this.securityContextSubject.next(null);
      }
    });
  }

  // Permission Loading
  loadUserPermissions(): void {
    this.getUserPermissionMatrix().subscribe({
      next: (matrix) => {
        this.permissionMatrixSubject.next(matrix);
      },
      error: (error) => {
        console.error('Failed to load user plugin permissions:', error);
      }
    });
  }

  loadSecurityContext(): void {
    this.getSecurityContext().subscribe({
      next: (context) => {
        this.securityContextSubject.next(context);
      },
      error: (error) => {
        console.error('Failed to load security context:', error);
      }
    });
  }

  // API Calls
  getUserPermissionMatrix(): Observable<PluginPermissionMatrix> {
    return this.http.get<PluginPermissionMatrix>(`${this.baseUrl}plugin-permissions/matrix`);
  }

  getSecurityContext(): Observable<PluginSecurityContext> {
    return this.http.get<PluginSecurityContext>(`${this.baseUrl}plugin-permissions/context`);
  }

  checkPermission(request: PluginPermissionRequest): Observable<PluginPermissionResponse> {
    return this.http.post<PluginPermissionResponse>(`${this.baseUrl}plugin-permissions/check`, request);
  }

  getPluginAccessControl(pluginName: string): Observable<PluginAccessControl> {
    return this.http.get<PluginAccessControl>(`${this.baseUrl}plugin-permissions/access-control/${pluginName}`);
  }

  getUserPluginPermissions(userId?: string): Observable<UserPluginPermissions> {
    const params = userId ? new HttpParams().set('userId', userId) : new HttpParams();
    return this.http.get<UserPluginPermissions>(`${this.baseUrl}plugin-permissions/user`, { params });
  }

  getPluginAuditLogs(pluginName?: string, userId?: string, limit: number = 100): Observable<PluginAuditLog[]> {
    let params = new HttpParams().set('limit', limit.toString());
    if (pluginName) params = params.set('pluginName', pluginName);
    if (userId) params = params.set('userId', userId);

    return this.http.get<{ logs: PluginAuditLog[] }>(`${this.baseUrl}plugin-permissions/audit-logs`, { params })
      .pipe(map(response => response.logs));
  }

  // Permission Checking Methods
  hasPluginPermission(pluginName: string, permission: PluginPermissionType): Observable<boolean> {
    return this.permissionMatrix$.pipe(
      map(matrix => {
        if (!matrix || !matrix.plugins[pluginName]) return false;
        return this.checkPluginPermissionInMatrix(matrix, pluginName, permission);
      })
    );
  }

  hasPluginFeatureAccess(pluginName: string, feature: PluginFeature): Observable<PluginFeatureAccess> {
    return this.permissionMatrix$.pipe(
      map(matrix => {
        if (!matrix || !matrix.plugins[pluginName]) {
          return {
            feature,
            accessible: false,
            reason: 'Plugin not accessible or not found'
          };
        }

        const pluginAccess = matrix.plugins[pluginName];
        const featureAccess = pluginAccess.features[feature];

        return featureAccess || {
          feature,
          accessible: false,
          reason: 'Feature not configured'
        };
      })
    );
  }

  canDeployTemplate(pluginName: string, templateId: string, projectId?: string, environmentId?: string): Observable<boolean> {
    return this.permissionMatrix$.pipe(
      map(matrix => {
        if (!matrix || !matrix.plugins[pluginName]) return false;

        const pluginAccess = matrix.plugins[pluginName];
        const templateAccess = pluginAccess.templates[templateId];

        if (!templateAccess || !templateAccess.canDeploy) return false;

        // Check project scope
        if (projectId && templateAccess.allowedProjects.length > 0) {
          if (!templateAccess.allowedProjects.includes(projectId)) return false;
        }

        // Check environment scope
        if (environmentId && templateAccess.allowedEnvironments.length > 0) {
          if (!templateAccess.allowedEnvironments.includes(environmentId)) return false;
        }

        return true;
      })
    );
  }

  canAccessProvider(providerType: string, action: 'view' | 'configure' | 'deploy', projectId?: string): Observable<boolean> {
    return this.permissionMatrix$.pipe(
      map(matrix => {
        if (!matrix) return false;

        // Check across all plugins that support this provider
        for (const pluginName in matrix.plugins) {
          const pluginAccess = matrix.plugins[pluginName];
          const providerAccess = pluginAccess.providers[providerType];

          if (!providerAccess) continue;

          let hasAccess = false;
          switch (action) {
            case 'view':
              hasAccess = providerAccess.canView;
              break;
            case 'configure':
              hasAccess = providerAccess.canConfigure;
              break;
            case 'deploy':
              hasAccess = providerAccess.canDeploy;
              break;
          }

          if (hasAccess) {
            // Check project scope if specified
            if (projectId && providerAccess.allowedProjects.length > 0) {
              return providerAccess.allowedProjects.includes(projectId);
            }
            return true;
          }
        }

        return false;
      })
    );
  }

  // Synchronous permission checks (using cached matrix)
  hasPluginPermissionSync(pluginName: string, permission: PluginPermissionType): boolean {
    const matrix = this.permissionMatrixSubject.value;
    if (!matrix || !matrix.plugins[pluginName]) return false;
    return this.checkPluginPermissionInMatrix(matrix, pluginName, permission);
  }

  hasPluginFeatureAccessSync(pluginName: string, feature: PluginFeature): PluginFeatureAccess {
    const matrix = this.permissionMatrixSubject.value;
    if (!matrix || !matrix.plugins[pluginName]) {
      return {
        feature,
        accessible: false,
        reason: 'Plugin not accessible or not found'
      };
    }

    const pluginAccess = matrix.plugins[pluginName];
    const featureAccess = pluginAccess.features[feature];

    return featureAccess || {
      feature,
      accessible: false,
      reason: 'Feature not configured'
    };
  }

  canDeployTemplateSync(pluginName: string, templateId: string, projectId?: string, environmentId?: string): boolean {
    const matrix = this.permissionMatrixSubject.value;
    if (!matrix || !matrix.plugins[pluginName]) return false;

    const pluginAccess = matrix.plugins[pluginName];
    const templateAccess = pluginAccess.templates[templateId];

    if (!templateAccess || !templateAccess.canDeploy) return false;

    // Check project scope
    if (projectId && templateAccess.allowedProjects.length > 0) {
      if (!templateAccess.allowedProjects.includes(projectId)) return false;
    }

    // Check environment scope
    if (environmentId && templateAccess.allowedEnvironments.length > 0) {
      if (!templateAccess.allowedEnvironments.includes(environmentId)) return false;
    }

    return true;
  }

  // Utility Methods
  private checkPluginPermissionInMatrix(matrix: PluginPermissionMatrix, pluginName: string, permission: PluginPermissionType): boolean {
    const pluginAccess = matrix.plugins[pluginName];
    if (!pluginAccess) return false;

    switch (permission) {
      case PLUGIN_PERMISSIONS.PLUGIN_VIEW:
        return pluginAccess.canView;
      case PLUGIN_PERMISSIONS.PLUGIN_INSTALL:
        return pluginAccess.canInstall;
      case PLUGIN_PERMISSIONS.PLUGIN_CONFIGURE:
        return pluginAccess.canConfigure;
      case PLUGIN_PERMISSIONS.PLUGIN_MANAGE:
        return pluginAccess.canManage;
      case PLUGIN_PERMISSIONS.PROVIDER_DEPLOY:
      case PLUGIN_PERMISSIONS.DEPLOYMENT_CREATE:
      case PLUGIN_PERMISSIONS.TEMPLATE_DEPLOY:
        return pluginAccess.canDeploy;
      default:
        return false;
    }
  }

  // Filter methods for UI components
  getAccessiblePlugins(): Observable<string[]> {
    return this.permissionMatrix$.pipe(
      map(matrix => {
        if (!matrix) return [];
        return Object.keys(matrix.plugins).filter(pluginName =>
          matrix.plugins[pluginName].canView
        );
      })
    );
  }

  getAccessibleTemplates(pluginName?: string): Observable<string[]> {
    return this.permissionMatrix$.pipe(
      map(matrix => {
        if (!matrix) return [];

        const accessibleTemplates: string[] = [];

        if (pluginName) {
          const pluginAccess = matrix.plugins[pluginName];
          if (pluginAccess) {
            Object.keys(pluginAccess.templates).forEach(templateId => {
              if (pluginAccess.templates[templateId].canView) {
                accessibleTemplates.push(templateId);
              }
            });
          }
        } else {
          // Get templates from all accessible plugins
          Object.keys(matrix.plugins).forEach(pName => {
            const pluginAccess = matrix.plugins[pName];
            Object.keys(pluginAccess.templates).forEach(templateId => {
              if (pluginAccess.templates[templateId].canView && !accessibleTemplates.includes(templateId)) {
                accessibleTemplates.push(templateId);
              }
            });
          });
        }

        return accessibleTemplates;
      })
    );
  }

  getAccessibleProviders(): Observable<string[]> {
    return this.permissionMatrix$.pipe(
      map(matrix => {
        if (!matrix) return [];

        const accessibleProviders: string[] = [];

        Object.keys(matrix.plugins).forEach(pluginName => {
          const pluginAccess = matrix.plugins[pluginName];
          Object.keys(pluginAccess.providers).forEach(providerType => {
            if (pluginAccess.providers[providerType].canView && !accessibleProviders.includes(providerType)) {
              accessibleProviders.push(providerType);
            }
          });
        });

        return accessibleProviders;
      })
    );
  }

  // Project and Environment filtering
  getAccessibleProjectsForPlugin(pluginName: string): Observable<string[]> {
    return combineLatest([
      this.permissionMatrix$,
      this.securityContext$
    ]).pipe(
      map(([matrix, context]) => {
        if (!matrix || !context || !matrix.plugins[pluginName]) return [];

        const pluginAccess = matrix.plugins[pluginName];
        const userAllowedProjects = context.restrictions.allowedProjects;

        // If user has global access, return all projects they can access
        if (userAllowedProjects.includes('*')) {
          return userAllowedProjects.filter(p => p !== '*');
        }

        // Return intersection of plugin-allowed and user-allowed projects
        const pluginProjects: string[] = [];
        Object.values(pluginAccess.templates).forEach(template => {
          template.allowedProjects.forEach(projectId => {
            if (!pluginProjects.includes(projectId)) {
              pluginProjects.push(projectId);
            }
          });
        });

        return pluginProjects.filter(projectId =>
          userAllowedProjects.includes(projectId)
        );
      })
    );
  }

  getAccessibleEnvironmentsForPlugin(pluginName: string, projectId?: string): Observable<string[]> {
    return combineLatest([
      this.permissionMatrix$,
      this.securityContext$
    ]).pipe(
      map(([matrix, context]) => {
        if (!matrix || !context || !matrix.plugins[pluginName]) return [];

        const pluginAccess = matrix.plugins[pluginName];
        const userAllowedEnvironments = context.restrictions.allowedEnvironments;

        const pluginEnvironments: string[] = [];

        // Collect environments from templates
        Object.values(pluginAccess.templates).forEach(template => {
          template.allowedEnvironments.forEach(envId => {
            if (!pluginEnvironments.includes(envId)) {
              pluginEnvironments.push(envId);
            }
          });
        });

        // Collect environments from providers
        Object.values(pluginAccess.providers).forEach(provider => {
          provider.allowedEnvironments.forEach(envId => {
            if (!pluginEnvironments.includes(envId)) {
              pluginEnvironments.push(envId);
            }
          });
        });

        return pluginEnvironments.filter(envId =>
          userAllowedEnvironments.includes(envId) || userAllowedEnvironments.includes('*')
        );
      })
    );
  }

  // Audit logging
  logPluginAction(action: string, resource: string, details: any): Observable<void> {
    const auditLog = {
      action,
      resource,
      resourceType: 'plugin',
      details,
      timestamp: new Date().toISOString()
    };

    return this.http.post<void>(`${this.baseUrl}plugin-permissions/audit-logs`, auditLog);
  }

  // Permission refresh
  refreshPermissions(): void {
    this.loadUserPermissions();
    this.loadSecurityContext();
  }

  // Check if user is plugin admin
  isPluginAdmin(): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => {
        if (!user) return false;
        return user.roles?.some(role => role.name === 'admin') ||
               user.isAdmin ||
               false;
      })
    );
  }

  // Navigation permission methods
  canViewPlugins(): Observable<boolean> {
    return combineLatest([
      this.permissionMatrix$,
      this.isPluginAdmin()
    ]).pipe(
      map(([matrix, isAdmin]) => {
        if (isAdmin) return true;
        if (!matrix) return false;

        // Check if user has access to any plugins
        return Object.keys(matrix.plugins).some(pluginName =>
          matrix.plugins[pluginName].canView
        );
      })
    );
  }

  canManagePlugins(): Observable<boolean> {
    return combineLatest([
      this.permissionMatrix$,
      this.isPluginAdmin()
    ]).pipe(
      map(([matrix, isAdmin]) => {
        if (isAdmin) return true;
        if (!matrix) return false;

        // Check if user can manage any plugins
        return Object.keys(matrix.plugins).some(pluginName =>
          matrix.plugins[pluginName].canManage || matrix.plugins[pluginName].canInstall
        );
      })
    );
  }

  canAccessProviders(): Observable<boolean> {
    return combineLatest([
      this.permissionMatrix$,
      this.isPluginAdmin()
    ]).pipe(
      map(([matrix, isAdmin]) => {
        if (isAdmin) return true;
        if (!matrix) return false;

        // Check if user has access to any providers
        return Object.keys(matrix.plugins).some(pluginName => {
          const pluginAccess = matrix.plugins[pluginName];
          return Object.keys(pluginAccess.providers).some(providerType =>
            pluginAccess.providers[providerType].canView
          );
        });
      })
    );
  }

  // Get current security context
  getCurrentSecurityContext(): PluginSecurityContext | null {
    return this.securityContextSubject.value;
  }

  // Get current permission matrix
  getCurrentPermissionMatrix(): PluginPermissionMatrix | null {
    return this.permissionMatrixSubject.value;
  }
}
