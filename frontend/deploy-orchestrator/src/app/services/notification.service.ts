import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number; // in milliseconds, 0 means persistent
  timestamp: Date;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private notifications$ = new BehaviorSubject<Notification[]>([]);
  private idCounter = 0;

  constructor() {}

  // Get notifications observable
  getNotifications(): Observable<Notification[]> {
    return this.notifications$.asObservable();
  }

  // Show success notification
  success(title: string, message?: string, duration: number = 5000): string {
    return this.addNotification('success', title, message, duration);
  }

  // Show error notification
  error(title: string, message?: string, duration: number = 8000): string {
    return this.addNotification('error', title, message, duration);
  }

  // Show warning notification
  warning(title: string, message?: string, duration: number = 6000): string {
    return this.addNotification('warning', title, message, duration);
  }

  // Show info notification
  info(title: string, message?: string, duration: number = 5000): string {
    return this.addNotification('info', title, message, duration);
  }

  // Add notification
  private addNotification(type: Notification['type'], title: string, message?: string, duration: number = 5000): string {
    const id = `notification-${++this.idCounter}`;
    const notification: Notification = {
      id,
      type,
      title,
      message,
      duration,
      timestamp: new Date()
    };

    const currentNotifications = this.notifications$.value;
    this.notifications$.next([...currentNotifications, notification]);

    // Auto-remove notification after duration (if not persistent)
    if (duration > 0) {
      setTimeout(() => {
        this.remove(id);
      }, duration);
    }

    return id;
  }

  // Remove notification by ID
  remove(id: string): void {
    const currentNotifications = this.notifications$.value;
    const filteredNotifications = currentNotifications.filter(n => n.id !== id);
    this.notifications$.next(filteredNotifications);
  }

  // Clear all notifications
  clear(): void {
    this.notifications$.next([]);
  }

  // Remove all notifications of a specific type
  clearByType(type: Notification['type']): void {
    const currentNotifications = this.notifications$.value;
    const filteredNotifications = currentNotifications.filter(n => n.type !== type);
    this.notifications$.next(filteredNotifications);
  }
}
