import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, tap, map } from 'rxjs';
import { Project } from '../models/project.model';
import { Group } from '../models/user.model';
import { environment } from '../../environments/environment';
import { AuthService } from './auth.service';
import { DataRefreshService } from './data-refresh.service';

@Injectable({
  providedIn: 'root'
})
export class ProjectService {
  private apiUrl = `${environment.apiUrl}`;
  private apiV1Url = `${environment.apiV1Url}`;

  // BehaviorSubject to store and share the projects list
  private projectsSubject = new BehaviorSubject<Project[]>([]);

  // BehaviorSubject to store and share the selected project
  private selectedProjectSubject = new BehaviorSubject<Project | null>(null);

  // Observable that components can subscribe to
  public projects$ = this.projectsSubject.asObservable();

  // Observable for the selected project
  public selectedProject$ = this.selectedProjectSubject.asObservable();

  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private dataRefreshService: DataRefreshService
  ) {
    // Register this service's refresh function with the centralized data refresh service
    this.dataRefreshService.registerRefreshFunction(() => {
      if (this.authService.isAuthenticated()) {
        this.refreshProjects();
      }
    });

    // Only load projects if user is authenticated
    if (this.authService.isAuthenticated()) {
      this.refreshProjects();
    }
  }

  // Check if the current user is an admin
  isAdmin(): boolean {
    return this.authService.isAdmin();
  }

  // Method to refresh the projects list
  refreshProjects(): void {
    // Only make the request if user is authenticated
    if (!this.authService.isAuthenticated()) {
      console.warn('Cannot load projects: User not authenticated');
      return;
    }

    this.http.get<Project[]>(`${this.apiUrl}/projects`)
      .subscribe({
        next: (projects) => {
          this.projectsSubject.next(projects);
          // Auto-select first project if none is selected and projects are available
          if (projects.length > 0 && !this.selectedProjectSubject.value) {
            this.setSelectedProject(projects[0]);
          }
        },
        error: (error) => {
          console.error('Error loading projects', error);
          // Error handling (including 401 redirects) is now handled by HTTP interceptors
          if (error.status === 401) {
            console.warn('Authentication failed while loading projects. Interceptor will handle redirect.');
          }
        }
      });
  }

  // Method to set the selected project
  setSelectedProject(project: Project | null): void {
    this.selectedProjectSubject.next(project);
  }

  // Method to get the current selected project
  getSelectedProject(): Project | null {
    return this.selectedProjectSubject.value;
  }

  // Method to clear the selected project
  clearSelectedProject(): void {
    this.selectedProjectSubject.next(null);
  }

  getProjects(): Observable<Project[]> {
    // Return the cached projects and refresh in the background only if authenticated
    if (this.authService.isAuthenticated()) {
      this.refreshProjects();
    }
    return this.projects$;
  }

  getProject(id: string): Observable<Project> {
    return this.http.get<Project>(`${this.apiUrl}/projects/${id}`);
  }

  createProject(project: Project): Observable<Project> {
    // Only admins can create projects
    if (!this.isAdmin()) {
      throw new Error('Permission denied: Only administrators can create projects');
    }

    return this.http.post<Project>(`${this.apiUrl}/projects`, project)
      .pipe(
        tap(() => {
          // Refresh the projects list after creating a new project
          this.refreshProjects();
        })
      );
  }

  updateProject(project: Project): Observable<Project> {
    // Only admins can update projects
    if (!this.isAdmin()) {
      throw new Error('Permission denied: Only administrators can update projects');
    }

    return this.http.put<Project>(`${this.apiUrl}/projects/${project.id}`, project)
      .pipe(
        tap(() => {
          // Refresh the projects list after updating a project
          this.refreshProjects();
        })
      );
  }

  deleteProject(id: string): Observable<void> {
    // Only admins can delete projects
    if (!this.isAdmin()) {
      throw new Error('Permission denied: Only administrators can delete projects');
    }

    return this.http.delete<void>(`${this.apiUrl}/projects/${id}`)
      .pipe(
        tap(() => {
          // Refresh the projects list after deleting a project
          this.refreshProjects();
        })
      );
  }

  getProjectsForUser(userId: string): Observable<Project[]> {
    return this.http.get<Project[]>(`${this.apiUrl}/users/${userId}/projects`);
  }

  getGroupsForProject(projectId: string): Observable<Group[]> {
    return this.http.get<Group[]>(`${this.apiUrl}/projects/${projectId}/groups`);
  }

  assignGroupToProject(projectId: string, groupId: string): Observable<void> {
    // Only admins can assign groups to projects
    if (!this.isAdmin()) {
      throw new Error('Permission denied: Only administrators can assign groups to projects');
    }

    return this.http.post<void>(`${this.apiUrl}/projects/${projectId}/groups/${groupId}`, {});
  }

  removeGroupFromProject(projectId: string, groupId: string): Observable<void> {
    // Only admins can remove groups from projects
    if (!this.isAdmin()) {
      throw new Error('Permission denied: Only administrators can remove groups from projects');
    }

    return this.http.delete<void>(`${this.apiUrl}/projects/${projectId}/groups/${groupId}`);
  }

  // Check if user has access to a specific project
  checkProjectAccess(projectId: string): Observable<boolean> {
    const userId = this.authService.currentUserValue?.id;
    if (!userId) {
      return new Observable<boolean>(observer => {
        observer.next(false);
        observer.complete();
      });
    }

    return this.http.get<{access: boolean}>(`${this.apiUrl}/users/${userId}/projects/${projectId}/access`).pipe(
      map(response => response.access)
    );
  }
}
