import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { environment } from '../../environments/environment';
import { 
  DeploymentPlugin, 
  EnvironmentPluginMapping, 
  DeploymentRequest,
  DeployableType 
} from '../models/deployable.model';

@Injectable({
  providedIn: 'root'
})
export class DeploymentPluginService {
  private baseUrl = `${environment.apiUrl}/deployment-plugins`;

  constructor(private http: HttpClient) {}

  /**
   * Get all available deployment plugins
   */
  getAvailablePlugins(): Observable<DeploymentPlugin[]> {
    return this.http.get<DeploymentPlugin[]>(`${this.baseUrl}`)
      .pipe(
        catchError(error => {
          console.error('Error fetching available plugins:', error);
          return throwError(error);
        })
      );
  }

  /**
   * Get plugins compatible with specific artifact types
   */
  getCompatiblePlugins(artifactTypes: DeployableType[]): Observable<DeploymentPlugin[]> {
    let params = new HttpParams();
    if (artifactTypes && artifactTypes.length > 0) {
      // Convert DeployableType enum values to strings for the API
      const artifactTypeStrings = artifactTypes.map(type => type.toString());
      params = params.set('artifactTypes', artifactTypeStrings.join(','));
    }
    
    return this.http.get<DeploymentPlugin[]>(`${this.baseUrl}/compatible`, { params })
      .pipe(
        catchError(error => {
          console.error('Error fetching compatible plugins:', error);
          return throwError(error);
        })
      );
  }

  /**
   * Get environment-plugin mappings
   */
  getEnvironmentPluginMappings(): Observable<EnvironmentPluginMapping[]> {
    return this.http.get<EnvironmentPluginMapping[]>(`${this.baseUrl}/environment-mappings`)
      .pipe(
        catchError(error => {
          console.error('Error fetching environment plugin mappings:', error);
          return throwError(error);
        })
      );
  }

  /**
   * Create or update environment-plugin mapping
   */
  setEnvironmentPluginMapping(mapping: EnvironmentPluginMapping): Observable<EnvironmentPluginMapping> {
    return this.http.post<EnvironmentPluginMapping>(`${this.baseUrl}/environment-mappings`, mapping)
      .pipe(
        catchError(error => {
          console.error('Error setting environment plugin mapping:', error);
          return throwError(error);
        })
      );
  }

  /**
   * Get default plugin for an environment
   */
  getDefaultPluginForEnvironment(environmentId: string): Observable<DeploymentPlugin | null> {
    return this.http.get<DeploymentPlugin>(`${this.baseUrl}/environment/${environmentId}/default`)
      .pipe(
        catchError(error => {
          if (error.status === 404) {
            // No default plugin found for this environment
            return throwError({ 
              ...error, 
              isNotFound: true, 
              message: 'No default plugin configured for this environment' 
            });
          }
          console.error('Error fetching default plugin for environment:', error);
          return throwError(error);
        })
      );
  }

  /**
   * Execute deployment
   */
  executeDeployment(request: DeploymentRequest): Observable<any> {
    return this.http.post(`${this.baseUrl}/deploy`, request)
      .pipe(
        catchError(error => {
          console.error('Error executing deployment:', error);
          return throwError(error);
        })
      );
  }

  /**
   * Validate plugin configuration
   */
  validateConfiguration(pluginId: string, configuration: { [key: string]: any }): Observable<{ valid: boolean; errors: string[] }> {
    return this.http.post<{ valid: boolean; errors: string[] }>(`${this.baseUrl}/${pluginId}/validate`, configuration)
      .pipe(
        catchError(error => {
          console.error('Error validating plugin configuration:', error);
          return throwError(error);
        })
      );
  }
}
