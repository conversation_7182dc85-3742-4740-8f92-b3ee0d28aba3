import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { ProviderInfo } from './provider.service';

export type ProviderType = 'gke' | 'aks' | 'eks' | 'openshift' | 'bare-metal';
import { AuthService } from './auth.service';

// Environment Types
export type EnvironmentType = 'kubernetes' | 'vm' | 'serverless' | 'container';
export type AuthMethod = 'service-account' | 'oauth' | 'certificate' | 'token' | 'basic';
export type EnvironmentStatus = 'active' | 'inactive' | 'maintenance' | 'error';

export interface ProviderConfig {
  cluster?: string;
  zone?: string;
  region?: string;
  project?: string;
  resourceGroup?: string;
  subscriptionId?: string;
  endpoint?: string;
  namespace?: string;
  authMethod: AuthMethod;
  credentials?: { [key: string]: any };
  extra?: { [key: string]: any };
}

export interface ResourceConfig {
  cpu: string;
  memory: string;
  storage: string;
  replicas?: number;
  minReplicas?: number;
  maxReplicas?: number;
  instanceType?: string;
  diskSize?: string;
}

export interface NetworkingConfig {
  ingress?: string[];
  loadBalancer?: boolean;
  ssl?: boolean;
  allowedPorts?: number[];
  allowedSources?: string[];
  customDomains?: string[];
  serviceMesh?: boolean;
}

export interface SecretMapping {
  secretId: string;
  variableName: string;
  mountPath?: string;
  format?: string;
}

export interface HealthCheck {
  enabled: boolean;
  endpoint?: string;
  interval?: number;
  timeout?: number;
}

export interface EnvironmentConfig {
  id: string;
  projectId: string;
  projectName?: string; // Added for display purposes
  name: string;
  type: EnvironmentType;
  provider: {
    type: ProviderType;
    config: ProviderConfig;
  };
  resources: ResourceConfig;
  networking: NetworkingConfig;
  variables: { [key: string]: string };
  secretMappings: SecretMapping[];
  healthCheck: HealthCheck;
  deploymentStrategy: string;
  status: EnvironmentStatus;
  description: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  project?: any;
  // Health-related properties
  health?: string;
  lastHealthCheck?: string;
  // Active status
  isActive?: boolean;
}

export interface CreateEnvironmentRequest {
  projectId: string;
  name: string;
  type: EnvironmentType;
  provider: {
    type: ProviderType;
    config: ProviderConfig;
  };
  resources: ResourceConfig;
  networking: NetworkingConfig;
  variables?: { [key: string]: string };
  secretMappings?: SecretMapping[];
  healthCheck: HealthCheck;
  deploymentStrategy: string;
  description?: string;
  tags?: string[];
}

export interface UpdateEnvironmentRequest {
  name?: string;
  provider?: {
    type: ProviderType;
    config: ProviderConfig;
  };
  resources?: ResourceConfig;
  networking?: NetworkingConfig;
  variables?: { [key: string]: string };
  secretMappings?: SecretMapping[];
  healthCheck?: HealthCheck;
  deploymentStrategy?: string;
  status?: EnvironmentStatus;
  description?: string;
  tags?: string[];
}

export interface EnvironmentFilter {
  projectId?: string;
  type?: string;
  status?: string;
  provider?: string;
  limit?: number;
  offset?: number;
}

export interface ConnectionTestResult {
  environmentId: string;
  success: boolean;
  message: string;
  details?: string;
  testedAt: string;
  latency?: number;
}

export interface EnvironmentHealth {
  environmentId: string;
  status: string;
  lastCheck: string;
  metrics: {
    cpu: number;
    memory: number;
    disk: number;
    responseTime: number;
    errorRate: number;
    throughput: number;
  };
  issues?: HealthIssue[];
}

export interface HealthIssue {
  type: string;
  severity: string;
  message: string;
  detectedAt: string;
  resolvedAt?: string;
}

export interface VersionMatrix {
  projectId: string;
  environments: { [envName: string]: EnvironmentVersion };
  generatedAt: string;
}

export interface EnvironmentVersion {
  currentVersion: string;
  deployedAt: string;
  health: string;
  metrics: { [key: string]: number };
  services: DeployedServiceInfo[];
}

export interface DeployedServiceInfo {
  name: string;
  version: string;
  status: string;
  health: string;
  updatedAt: string;
}

export interface ProviderCapabilities {
  type: ProviderType;
  supportedFeatures: string[];
  authMethods: AuthMethod[];
  regions?: string[];
  instanceTypes?: string[];
  kubernetesVersions?: string[];
}

@Injectable({
  providedIn: 'root'
})
export class EnvironmentService {
  private baseUrl = '/api/v1/environment-service';
  private environmentsSubject = new BehaviorSubject<EnvironmentConfig[]>([]);
  public environments$ = this.environmentsSubject.asObservable();

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  // Environment Management
  getEnvironments(filter?: EnvironmentFilter): Observable<{ environments: EnvironmentConfig[], total: number }> {
    return this.authService.currentUser$.pipe(
      switchMap(user => {
        if (!user) {
          return new Observable(observer => observer.next({ environments: [], total: 0 }));
        }

        let params = new HttpParams();

        // If user is not admin, filter by their accessible projects
        if (!this.authService.isAdmin()) {
          return this.authService.getUserProjects().pipe(
            switchMap(userProjects => {
              if (!userProjects || userProjects.length === 0) {
                return new Observable(observer => observer.next({ environments: [], total: 0 }));
              }

              // Add project filter for non-admin users
              if (filter?.projectId) {
                // Check if requested project is in user's accessible projects
                if (!userProjects.some(p => (p.id || p.projectId) === filter.projectId)) {
                  return new Observable(observer => observer.next({ environments: [], total: 0 }));
                }
                params = params.set('projectId', filter.projectId);
              } else {
                // Filter by all user's accessible projects
                userProjects.forEach(project => {
                  params = params.append('projectIds', project.id || project.projectId);
                });
              }

              // Add other filters
              if (filter) {
                Object.keys(filter).forEach(key => {
                  if (key !== 'projectId') { // Already handled above
                    const value = (filter as any)[key];
                    if (value !== null && value !== undefined && value !== '') {
                      params = params.set(key, value.toString());
                    }
                  }
                });
              }

              return this.http.get<{ environments: EnvironmentConfig[], total: number }>(`${this.baseUrl}/environments`, {
                params
              });
            })
          );
        } else {
          // Admin can see all environments
          if (filter?.projectId) {
            params = params.set('projectId', filter.projectId);
          }

          // Add other filters
          if (filter) {
            Object.keys(filter).forEach(key => {
              const value = (filter as any)[key];
              if (value !== null && value !== undefined && value !== '') {
                params = params.set(key, value.toString());
              }
            });
          }

          return this.http.get<{ environments: EnvironmentConfig[], total: number }>(`${this.baseUrl}/environments`, {
            params
          });
        }
      }),
      map((response: any) => {
        this.environmentsSubject.next(response.environments || []);
        return response;
      })
    );
  }

  getEnvironment(id: string): Observable<EnvironmentConfig> {
    return this.http.get<EnvironmentConfig>(`${this.baseUrl}/environments/${id}`);
  }

  createEnvironment(request: CreateEnvironmentRequest): Observable<EnvironmentConfig> {
    return this.http.post<EnvironmentConfig>(`${this.baseUrl}/environments`, request);
  }

  updateEnvironment(id: string, request: UpdateEnvironmentRequest): Observable<EnvironmentConfig> {
    return this.http.put<EnvironmentConfig>(`${this.baseUrl}/environments/${id}`, request);
  }

  deleteEnvironment(id: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/environments/${id}`);
  }

  // Project Environments
  getProjectEnvironments(projectId: string): Observable<{ environments: EnvironmentConfig[] }> {
    return this.http.get<{ environments: EnvironmentConfig[] }>(`${this.baseUrl}/projects/${projectId}/environments`);
  }

  createProjectEnvironment(projectId: string, request: CreateEnvironmentRequest): Observable<EnvironmentConfig> {
    return this.http.post<EnvironmentConfig>(`${this.baseUrl}/projects/${projectId}/environments`, request);
  }

  // Environment Operations
  testConnection(id: string): Observable<ConnectionTestResult> {
    return this.http.post<ConnectionTestResult>(`${this.baseUrl}/environments/${id}/test-connection`, {});
  }

  deployToEnvironment(id: string, deploymentRequest: any): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/environments/${id}/deploy`, deploymentRequest);
  }

  getEnvironmentStatus(id: string): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/environments/${id}/status`);
  }

  getEnvironmentLogs(id: string, filter?: any): Observable<any> {
    let params = new HttpParams();
    if (filter) {
      Object.keys(filter).forEach(key => {
        const value = filter[key];
        if (value !== null && value !== undefined && value !== '') {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<any>(`${this.baseUrl}/environments/${id}/logs`, { params });
  }

  // Health Monitoring
  getAllEnvironmentHealth(): Observable<{ health: EnvironmentHealth[] }> {
    return this.http.get<{ health: EnvironmentHealth[] }>(`${this.baseUrl}/health/environments`);
  }

  getEnvironmentHealth(id: string): Observable<EnvironmentHealth> {
    return this.http.get<EnvironmentHealth>(`${this.baseUrl}/health/environments/${id}`);
  }

  triggerHealthCheck(id: string): Observable<EnvironmentHealth> {
    return this.http.post<EnvironmentHealth>(`${this.baseUrl}/health/environments/${id}/check`, {});
  }

  // Provider Capabilities
  getProviders(): Observable<{ providers: ProviderCapabilities[] }> {
    return this.http.get<{ providers: ProviderCapabilities[] }>(`${this.baseUrl}/providers`);
  }

  getProviderTypes(): Observable<{ types: any[], total: number }> {
    return this.http.get<{ types: any[], total: number }>(`${this.baseUrl}/providers/types`);
  }

  getProviderCapabilities(type: ProviderType): Observable<ProviderCapabilities> {
    return this.http.get<ProviderCapabilities>(`${this.baseUrl}/providers/${type}/capabilities`);
  }

  getProviderSchema(type: string): Observable<{ type: string, schema: any }> {
    return this.http.get<{ type: string, schema: any }>(`${this.baseUrl}/providers/${type}/schema`);
  }

  validateProviderConfig(type: ProviderType, config: ProviderConfig): Observable<{ valid: boolean, errors?: string[] }> {
    return this.http.post<{ valid: boolean, errors?: string[] }>(`${this.baseUrl}/providers/${type}/validate-config`, config);
  }

  // Version Matrix
  getVersionMatrix(projectId: string): Observable<VersionMatrix> {
    return this.http.get<VersionMatrix>(`${this.baseUrl}/versions/matrix/${projectId}`);
  }

  getDeploymentHistory(environmentId: string): Observable<{ history: any[] }> {
    return this.http.get<{ history: any[] }>(`${this.baseUrl}/versions/history/${environmentId}`);
  }

  // Utility Methods
  refreshEnvironments(): void {
    this.getEnvironments().subscribe();
  }

  getCurrentEnvironments(): EnvironmentConfig[] {
    return this.environmentsSubject.value;
  }

  getEnvironmentsByProject(projectId: string): EnvironmentConfig[] {
    return this.environmentsSubject.value.filter(env => env.projectId === projectId);
  }

  // Permission Check Methods
  canViewEnvironments(): boolean {
    return this.authService.isAdmin();
  }

  canCreateEnvironments(): boolean {
    return this.authService.isAdmin();
  }

  canUpdateEnvironments(): boolean {
    return this.authService.isAdmin();
  }

  canDeleteEnvironments(): boolean {
    return this.authService.isAdmin();
  }

  canDeployToEnvironments(): boolean {
    return this.authService.isAdmin();
  }

  hasProjectAccess(projectId: string): boolean {
    // For now, just check if user is admin
    // TODO: Implement proper project access checking
    return this.authService.isAdmin();
  }

  canManageEnvironments(): boolean {
    return this.canCreateEnvironments() || this.canUpdateEnvironments() || this.canDeleteEnvironments();
  }



  getUserAccessibleProjects(): Observable<any[]> {
    return this.authService.getUserProjects();
  }

  // Sync version for immediate checks (less reliable)
  hasProjectAccessSync(projectId: string): boolean {
    if (this.authService.isAdmin()) {
      return true;
    }

    // This is a fallback - prefer using the Observable version
    const userProjects = this.authService.getUserProjectsSync();
    return userProjects ? userProjects.some(p => p.id === projectId || p.projectId === projectId) : false;
  }
}
