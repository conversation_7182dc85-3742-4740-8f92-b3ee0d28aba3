import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {BehaviorSubject, Observable, of, throwError} from 'rxjs';
import {catchError, map, tap, switchMap} from 'rxjs/operators';
import {Router} from '@angular/router';
import {LoginRequest, LoginResponse, RefreshTokenRequest, RefreshTokenResponse, ExternalLoginResponse} from '../models/auth.model';
import {User} from '../models/user.model';
import {environment} from '../../environments/environment';
import {IdentityProviderType} from '../models/identity-provider.model';

@Injectable({
    providedIn: 'root'
})
export class AuthService {
    private currentUserSubject: BehaviorSubject<User | null>;
    public currentUser: Observable<User | null>;
    private refreshTokenTimeout: any;
    private apiUrl = `${environment.apiUrl}`;
    private apiV1Url = `${environment.apiV1Url}`;

    constructor(private http: HttpClient, private router: Router) {
        const storedUser = localStorage.getItem('currentUser');
        this.currentUserSubject = new BehaviorSubject<User | null>(storedUser ? JSON.parse(storedUser) : null);
        this.currentUser = this.currentUserSubject.asObservable();
    }

    public get currentUserValue(): User | null {
        return this.currentUserSubject.value;
    }

    login(username: string, password: string, authProvider?: string, providerId?: string): Observable<User> {
        const payload = {
            username,
            password,
            ...(authProvider && { authProvider }),
            ...(providerId && { providerId })
        };

        return this.http.post<any>(`${this.apiUrl}/auth/login`, payload)
            .pipe(
                map(response => {
                    // Store user details and tokens in local storage
                    const user: User = {
                        id: response.userId,
                        username: response.username,
                        email: response.email,
                        firstName: response.firstName || '',
                        lastName: response.lastName || '',
                        isAdmin: response.isAdmin,
                        isActive: true,
                        // Convert string[] groups to Group[] if needed
                        groups: response.groups ? response.groups.map((name: string) => ({
                            id: name, // Use name as ID for simplicity
                            name: name,
                            source: 'ldap'
                        })) : []
                    };
                    console.log('AuthService: Storing login response');
                    console.log('AuthService: User:', user);
                    console.log('AuthService: Token:', response.token ? `${response.token.substring(0, 20)}...` : 'null');
                    console.log('AuthService: RefreshToken:', response.refreshToken ? `${response.refreshToken.substring(0, 20)}...` : 'null');

                    localStorage.setItem('currentUser', JSON.stringify(user));
                    localStorage.setItem('accessToken', response.token);
                    localStorage.setItem('refreshToken', response.refreshToken);

                    console.log('AuthService: Tokens stored in localStorage');
                    console.log('AuthService: Verification - accessToken:', localStorage.getItem('accessToken') ? 'stored' : 'NOT STORED');

                    this.currentUserSubject.next(user);
                    this.startRefreshTokenTimer();
                    return user;
                })
            );
    }

    logout(): void {
        // Remove user from local storage and set current user to null
        localStorage.removeItem('currentUser');
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('returnUrl');

        // Clear any other auth-related storage
        sessionStorage.clear();

        // Reset authentication state
        this.currentUserSubject.next(null);
        this.stopRefreshTokenTimer();

        // Clear any cached data to prevent stale state
        this.clearApplicationState();

        // Navigate to login with a clean state
        this.router.navigate(['/login'], { replaceUrl: true });
    }

    private clearApplicationState(): void {
        // Clear any application-specific cached data
        // This helps prevent infinite loops when returning to login
        try {
            // Clear any cached service data that might cause loops
            const keysToRemove = Object.keys(localStorage).filter(key =>
                key.startsWith('cached_') ||
                key.startsWith('app_') ||
                key.includes('_cache')
            );
            keysToRemove.forEach(key => localStorage.removeItem(key));
        } catch (error) {
            console.warn('Error clearing application state:', error);
        }
    }

    refreshToken(): Observable<RefreshTokenResponse> {
        const refreshToken = localStorage.getItem('refreshToken');
        if (!refreshToken) {
            return throwError(() => new Error('No refresh token available'));
        }

        return this.http.post<RefreshTokenResponse>(`${this.apiUrl}/auth/refresh`, {refreshToken})
            .pipe(
                tap(response => {
                    localStorage.setItem('accessToken', response.token);
                    localStorage.setItem('refreshToken', response.refreshToken);

                    // Update user groups if provided
                    if (response.groups) {
                        const currentUser = this.currentUserValue;
                        if (currentUser) {
                            currentUser.groups = response.groups.map((name: string) => ({
                                id: name,
                                name: name,
                                source: 'refresh'
                            }));
                            localStorage.setItem('currentUser', JSON.stringify(currentUser));
                            this.currentUserSubject.next(currentUser);
                        }
                    }

                    this.startRefreshTokenTimer();
                }),
                catchError(error => {
                    this.logout();
                    return throwError(() => error);
                })
            );
    }

    private startRefreshTokenTimer(): void {
        // Parse the JWT token to get the expiration time
        const accessToken = localStorage.getItem('accessToken');
        if (!accessToken) {
            console.warn('AuthService: No access token found for refresh timer');
            return;
        }

        try {
            // Validate JWT format before parsing
            const tokenParts = accessToken.split('.');
            if (tokenParts.length !== 3) {
                console.error('AuthService: Invalid JWT format - expected 3 parts, got', tokenParts.length);
                console.error('AuthService: Token value:', accessToken.substring(0, 50) + '...');
                this.logout(); // Clear invalid token
                return;
            }

            const jwtToken = JSON.parse(atob(tokenParts[1]));
            if (!jwtToken.exp) {
                console.warn('AuthService: JWT token has no expiration time');
                return;
            }

            const expires = new Date(jwtToken.exp * 1000);
            const timeout = expires.getTime() - Date.now() - (60 * 1000); // Refresh 1 minute before expiry

            console.log('AuthService: Setting refresh timer for', expires);
            this.refreshTokenTimeout = setTimeout(() => this.refreshToken().subscribe(), timeout);
        } catch (error) {
            console.error('AuthService: Error parsing JWT token for refresh timer:', error);
            console.error('AuthService: Token value:', accessToken.substring(0, 50) + '...');
            this.logout(); // Clear invalid token
        }
    }

    private stopRefreshTokenTimer(): void {
        clearTimeout(this.refreshTokenTimeout);
    }

    getAccessToken(): string | null {
        const token = localStorage.getItem('accessToken');

        console.log('AuthService.getAccessToken() called');
        console.log('Token from localStorage:', token ? `${token.substring(0, 20)}...` : 'null');

        if (!token) {
            console.warn('AuthService: No accessToken found in localStorage');
            return null;
        }

        // Validate JWT format
        const tokenParts = token.split('.');
        if (tokenParts.length !== 3) {
            console.error('AuthService: Invalid JWT format in getAccessToken - expected 3 parts, got', tokenParts.length);
            console.error('AuthService: Token value:', token);
            console.error('AuthService: Clearing invalid token');
            localStorage.removeItem('accessToken');
            return null;
        }

        console.log('AuthService: Returning valid token');
        return token;
    }

    isAuthenticated(): boolean {
        const token = this.getAccessToken();
        if (!token) {
            return false;
        }

        // Check if token is expired
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const expirationTime = payload.exp * 1000; // Convert to milliseconds
            const currentTime = Date.now();

            if (currentTime >= expirationTime) {
                console.log('AuthService: Token is expired, user not authenticated');
                // Don't automatically logout here, let the interceptor handle it
                return false;
            }

            return true;
        } catch (error) {
            console.warn('AuthService: Error parsing token:', error);
            return false;
        }
    }

    isTokenExpiringSoon(): boolean {
        const token = this.getAccessToken();
        if (!token) {
            return false;
        }

        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const expirationTime = payload.exp * 1000; // Convert to milliseconds
            const currentTime = Date.now();
            const timeUntilExpiration = expirationTime - currentTime;

            // Return true if token expires in less than 5 minutes
            return timeUntilExpiration < 5 * 60 * 1000;
        } catch (error) {
            console.warn('AuthService: Error parsing token for expiration check:', error);
            return true; // Assume it's expiring if we can't parse it
        }
    }

    isAdmin(): boolean {
        const user = this.currentUserValue;
        return user ? user.isAdmin : false;
    }

    hasPermission(permission: string, projectId: string): Observable<boolean> {
        // If user is admin, they have all permissions
        if (this.isAdmin()) {
            return of(true);
        }

        // For now, return a simple check based on authentication
        // This should be replaced with actual permission checking logic
        // that calls the backend to verify user permissions
        if (!this.isAuthenticated()) {
            return of(false);
        }

        // Make API call to check permission
        const params: any = { permission };
        if (projectId) {
            params.projectId = projectId;
        }

        return this.http.get<{ hasPermission: boolean }>(`${this.apiUrl}/auth/check-permission`, { params })
            .pipe(
                map(response => response.hasPermission),
                catchError(() => of(false)) // Default to false if permission check fails
            );
    }

    // External login methods
    initiateExternalLogin(providerId: string, providerType: IdentityProviderType): Observable<ExternalLoginResponse> {
        return this.http.post<ExternalLoginResponse>(`${this.apiUrl}/auth/external/initiate`, {
            providerId,
            providerType
        });
    }

    handleExternalLoginCallback(code: string, state: string): Observable<User> {
        return this.http.post<any>(`${this.apiUrl}/auth/external/callback`, { code, state })
            .pipe(
                map(response => {
                    // Store user details and tokens in local storage
                    const user: User = {
                        id: response.userId,
                        username: response.username,
                        email: response.email,
                        firstName: response.firstName || '',
                        lastName: response.lastName || '',
                        isAdmin: response.isAdmin,
                        isActive: true,
                        // Convert string[] groups to Group[] if needed
                        groups: response.groups ? response.groups.map((name: string) => ({
                            id: name, // Use name as ID for simplicity
                            name: name,
                            source: 'external'
                        })) : []
                    };
                    localStorage.setItem('currentUser', JSON.stringify(user));
                    localStorage.setItem('accessToken', response.token);
                    localStorage.setItem('refreshToken', response.refreshToken);
                    this.currentUserSubject.next(user);
                    this.startRefreshTokenTimer();
                    return user;
                })
            );
    }

    // Group-Project Access Methods

    /**
     * Get all projects accessible to the current user through their group memberships
     */
    getUserProjects(): Observable<any[]> {
        if (this.isAdmin()) {
            // Admin users can access all projects - return empty array to indicate "all projects"
            return of([]);
        }

        return this.http.get<{ projects: any[] }>(`${this.apiV1Url}/admin-service/auth/user/projects`)
            .pipe(
                map(response => response.projects || []),
                catchError(error => {
                    console.error('Failed to get user projects:', error);
                    return of([]);
                })
            );
    }

    /**
     * Check if the current user has access to a specific project
     */
    hasProjectAccess(projectId: string): Observable<boolean> {
        if (this.isAdmin()) {
            return of(true);
        }

        if (!projectId) {
            return of(false);
        }

        return this.http.get<{ hasAccess: boolean }>(`${this.apiUrl}/auth/user/project-access/${projectId}`)
            .pipe(
                map(response => response.hasAccess),
                catchError(error => {
                    console.error('Failed to check project access:', error);
                    return of(false);
                })
            );
    }

    /**
     * Get project IDs accessible to the current user (for filtering)
     */
    getUserProjectIds(): Observable<string[]> {
        return this.getUserProjects().pipe(
            map(projects => projects.map(p => p.projectId || p.id))
        );
    }

    /**
     * Validate that the current user has access to a project, throw error if not
     */
    validateProjectAccess(projectId: string): Observable<void> {
        return this.hasProjectAccess(projectId).pipe(
            map(hasAccess => {
                if (!hasAccess) {
                    throw new Error(`Access denied: No permission for project ${projectId}`);
                }
            })
        );
    }

    /**
     * Filter a list of items by user's project access
     */
    filterByProjectAccess<T extends { projectId: string }>(items: T[]): Observable<T[]> {
        if (this.isAdmin()) {
            return of(items);
        }

        return this.getUserProjectIds().pipe(
            map(accessibleProjectIds => {
                return items.filter(item => accessibleProjectIds.includes(item.projectId));
            })
        );
    }

    /**
     * Enhanced permission check that includes project access validation
     */
    hasPermissionForProject(permission: string, projectId: string): Observable<boolean> {
        // First check if user has the permission
        return this.hasPermission(permission, projectId).pipe(
            switchMap(hasPermission => {
                if (!hasPermission) {
                    return of(false);
                }

                // If user has permission, check project access
                return this.hasProjectAccess(projectId);
            })
        );
    }

    /**
     * Get current user's accessible projects (cached)
     */
    get currentUser$(): Observable<User | null> {
        return this.currentUser;
    }

    /**
     * Sync method to get user projects from cache (returns empty array if not loaded)
     */
    getUserProjectsSync(): any[] {
        // This would need to be implemented with caching
        // For now, return empty array and rely on async methods
        return [];
    }
}
