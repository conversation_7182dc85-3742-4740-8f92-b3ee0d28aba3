import { Injectable } from '@angular/core';
import { Observable, Subject, BehaviorSubject } from 'rxjs';
import { environment } from '../../environments/environment';

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
  id?: string;
}

@Injectable({
  providedIn: 'root'
})
export class WebSocketService {
  private socket: WebSocket | null = null;
  private messageSubject = new Subject<any>();
  private connectionStatusSubject = new BehaviorSubject<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;
  private reconnectTimer: any;

  public messages$ = this.messageSubject.asObservable();
  public connectionStatus$ = this.connectionStatusSubject.asObservable();

  constructor() {}

  connect(endpoint: string, params?: { [key: string]: any }): Observable<any> {
    this.disconnect(); // Close any existing connection

    const wsUrl = this.buildWebSocketUrl(endpoint, params);
    this.connectionStatusSubject.next('connecting');

    try {
      this.socket = new WebSocket(wsUrl);
      this.setupEventHandlers();
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.connectionStatusSubject.next('error');
    }

    return this.messages$;
  }

  disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    this.connectionStatusSubject.next('disconnected');
    this.reconnectAttempts = 0;
  }

  send(message: any): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      try {
        const messageToSend = typeof message === 'string' ? message : JSON.stringify(message);
        this.socket.send(messageToSend);
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
      }
    } else {
      console.warn('WebSocket is not connected. Cannot send message:', message);
    }
  }

  private buildWebSocketUrl(endpoint: string, params?: { [key: string]: any }): string {
    const baseUrl = environment.wsUrl || environment.apiUrl.replace('http', 'ws');
    let url = `${baseUrl}${endpoint}`;

    if (params) {
      const queryParams = new URLSearchParams();
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null) {
          queryParams.append(key, params[key].toString());
        }
      });
      
      if (queryParams.toString()) {
        url += `?${queryParams.toString()}`;
      }
    }

    return url;
  }

  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.onopen = (event) => {
      console.log('WebSocket connected:', event);
      this.connectionStatusSubject.next('connected');
      this.reconnectAttempts = 0;
    };

    this.socket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.messageSubject.next(message);
      } catch (error) {
        // If it's not JSON, treat it as a plain text message
        this.messageSubject.next({
          type: 'text',
          data: event.data,
          timestamp: new Date().toISOString()
        });
      }
    };

    this.socket.onclose = (event) => {
      console.log('WebSocket closed:', event);
      this.connectionStatusSubject.next('disconnected');
      
      // Attempt to reconnect if it wasn't a clean close
      if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
        this.scheduleReconnect();
      }
    };

    this.socket.onerror = (event) => {
      console.error('WebSocket error:', event);
      this.connectionStatusSubject.next('error');
    };
  }

  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    console.log(`Scheduling WebSocket reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
    
    this.reconnectTimer = setTimeout(() => {
      if (this.socket && this.socket.readyState === WebSocket.CLOSED) {
        this.connectionStatusSubject.next('connecting');
        // Note: This is a simplified reconnect. In a real implementation,
        // you'd need to store the original endpoint and params to reconnect properly.
        console.log('Attempting to reconnect WebSocket...');
      }
    }, this.reconnectInterval * this.reconnectAttempts);
  }

  // Utility methods for specific message types
  subscribeToLogs(workflowId?: string, pluginName?: string): Observable<any> {
    const params: any = {};
    if (workflowId) params.workflowId = workflowId;
    if (pluginName) params.pluginName = pluginName;
    
    return this.connect('/ws/logs', params);
  }

  subscribeToWorkflowEvents(workflowId: string): Observable<any> {
    return this.connect('/ws/workflows', { workflowId });
  }

  subscribeToPluginEvents(pluginName?: string): Observable<any> {
    const params = pluginName ? { pluginName } : {};
    return this.connect('/ws/plugins', params);
  }

  subscribeToDeploymentEvents(deploymentId?: string): Observable<any> {
    const params = deploymentId ? { deploymentId } : {};
    return this.connect('/ws/deployments', params);
  }

  subscribeToSystemEvents(): Observable<any> {
    return this.connect('/ws/system');
  }

  // Health check for WebSocket connection
  isConnected(): boolean {
    return this.socket !== null && this.socket.readyState === WebSocket.OPEN;
  }

  getConnectionState(): string {
    if (!this.socket) return 'disconnected';
    
    switch (this.socket.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'disconnecting';
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }

  // Send specific message types
  sendLogFilter(filter: any): void {
    this.send({
      type: 'log_filter',
      data: filter,
      timestamp: new Date().toISOString()
    });
  }

  sendWorkflowCommand(workflowId: string, command: string, params?: any): void {
    this.send({
      type: 'workflow_command',
      data: {
        workflowId,
        command,
        params
      },
      timestamp: new Date().toISOString()
    });
  }

  sendPluginCommand(pluginName: string, command: string, params?: any): void {
    this.send({
      type: 'plugin_command',
      data: {
        pluginName,
        command,
        params
      },
      timestamp: new Date().toISOString()
    });
  }

  // Ping/Pong for connection health
  ping(): void {
    this.send({
      type: 'ping',
      timestamp: new Date().toISOString()
    });
  }

  // Configuration
  setReconnectConfig(maxAttempts: number, interval: number): void {
    this.maxReconnectAttempts = maxAttempts;
    this.reconnectInterval = interval;
  }

  // Event handlers for specific message types
  onLogMessage(callback: (log: any) => void): void {
    this.messages$.subscribe(message => {
      if (message.type === 'log' || message.type === 'log_entry') {
        callback(message.data || message);
      }
    });
  }

  onWorkflowEvent(callback: (event: any) => void): void {
    this.messages$.subscribe(message => {
      if (message.type === 'workflow_event' || message.type === 'workflow_status') {
        callback(message.data || message);
      }
    });
  }

  onPluginEvent(callback: (event: any) => void): void {
    this.messages$.subscribe(message => {
      if (message.type === 'plugin_event' || message.type === 'plugin_status') {
        callback(message.data || message);
      }
    });
  }

  onDeploymentEvent(callback: (event: any) => void): void {
    this.messages$.subscribe(message => {
      if (message.type === 'deployment_event' || message.type === 'deployment_status') {
        callback(message.data || message);
      }
    });
  }

  onSystemEvent(callback: (event: any) => void): void {
    this.messages$.subscribe(message => {
      if (message.type === 'system_event' || message.type === 'system_status') {
        callback(message.data || message);
      }
    });
  }

  // Error handling
  onError(callback: (error: any) => void): void {
    this.connectionStatus$.subscribe(status => {
      if (status === 'error') {
        callback({ type: 'connection_error', message: 'WebSocket connection error' });
      }
    });
  }

  // Cleanup
  destroy(): void {
    this.disconnect();
    this.messageSubject.complete();
    this.connectionStatusSubject.complete();
  }
}
