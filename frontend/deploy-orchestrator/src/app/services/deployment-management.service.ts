import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface BulkDeploymentRequest {
  applicationIds?: string[];
  componentIds?: string[];
  projectId: string;
  environmentId: string;
  version: string;
  workflowId: string;
  providerType: string;
  parameters?: { [key: string]: any };
  secretMappings?: { [key: string]: string };
  strategy?: string;
  configuration?: { [key: string]: any };
}

export interface DeploymentResponse {
  deploymentId: string;
  workflowExecution: string;
  status: string;
  applications?: ApplicationInfo[];
  components?: ComponentInfo[];
  createdAt: string;
}

export interface ApplicationInfo {
  id: string;
  name: string;
  version: string;
  status: string;
}

export interface ComponentInfo {
  id: string;
  name: string;
  applicationId: string;
  version: string;
  status: string;
}

export interface EnvironmentVersion {
  id: string;
  applicationId?: string;
  componentId?: string;
  projectId: string;
  environmentId: string;
  version: string;
  deploymentId: string;
  status: string;
  deployedAt: string;
  deployedBy: string;
  promotedFrom?: string;
  application?: any;
  component?: any;
}

export interface VersionMatrix {
  [entityName: string]: {
    [environmentId: string]: string;
  };
}

export interface PromotionRequest {
  applicationId?: string;
  componentId?: string;
  projectId: string;
  sourceEnvironmentId: string;
  targetEnvironmentId: string;
  version: string;
  workflowId: string;
  providerType: string;
  parameters?: { [key: string]: any };
  secretMappings?: { [key: string]: string };
  strategy?: string;
  configuration?: { [key: string]: any };
}

@Injectable({
  providedIn: 'root'
})
export class DeploymentManagementService {
  private apiUrl = `${environment.apiUrl}/application-service/api/v1/deployments`;

  constructor(private http: HttpClient) {}

  /**
   * Create a bulk deployment
   */
  createDeployment(request: BulkDeploymentRequest): Observable<DeploymentResponse> {
    return this.http.post<DeploymentResponse>(this.apiUrl, request);
  }

  /**
   * Get environment versions for a project
   */
  getEnvironmentVersions(projectId: string, environmentIds?: string[]): Observable<{ versions: EnvironmentVersion[], total: number }> {
    let params = new HttpParams().set('projectId', projectId);
    
    if (environmentIds && environmentIds.length > 0) {
      environmentIds.forEach(envId => {
        params = params.append('environmentId', envId);
      });
    }

    return this.http.get<{ versions: EnvironmentVersion[], total: number }>(`${this.apiUrl}/environment-versions`, { params });
  }

  /**
   * Get version matrix showing which versions are deployed to which environments
   */
  getVersionMatrix(projectId: string): Observable<{ matrix: VersionMatrix }> {
    const params = new HttpParams().set('projectId', projectId);
    return this.http.get<{ matrix: VersionMatrix }>(`${this.apiUrl}/version-matrix`, { params });
  }

  /**
   * Update deployment status
   */
  updateDeploymentStatus(deploymentId: string, status: string): Observable<{ message: string, status: string }> {
    return this.http.put<{ message: string, status: string }>(`${this.apiUrl}/${deploymentId}/status`, { status });
  }

  /**
   * Promote a version from one environment to another
   */
  promoteVersion(request: PromotionRequest): Observable<{ message: string, deployment: DeploymentResponse }> {
    return this.http.post<{ message: string, deployment: DeploymentResponse }>(`${this.apiUrl}/promote`, request);
  }

  /**
   * Get deployment status options
   */
  getDeploymentStatuses(): string[] {
    return ['pending', 'in_progress', 'succeeded', 'failed', 'rolled_back'];
  }

  /**
   * Get deployment strategies
   */
  getDeploymentStrategies(): string[] {
    return ['rolling_update', 'blue_green', 'canary', 'recreate'];
  }

  /**
   * Format deployment status for display
   */
  formatStatus(status: string): string {
    switch (status) {
      case 'pending': return 'Pending';
      case 'in_progress': return 'In Progress';
      case 'succeeded': return 'Succeeded';
      case 'failed': return 'Failed';
      case 'rolled_back': return 'Rolled Back';
      default: return status;
    }
  }

  /**
   * Get status color class for UI
   */
  getStatusColorClass(status: string): string {
    switch (status) {
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'in_progress': return 'text-blue-600 bg-blue-100';
      case 'succeeded': return 'text-green-600 bg-green-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'rolled_back': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  }

  /**
   * Check if status indicates deployment is in progress
   */
  isDeploymentInProgress(status: string): boolean {
    return status === 'pending' || status === 'in_progress';
  }

  /**
   * Check if status indicates deployment completed successfully
   */
  isDeploymentSuccessful(status: string): boolean {
    return status === 'succeeded';
  }

  /**
   * Check if status indicates deployment failed
   */
  isDeploymentFailed(status: string): boolean {
    return status === 'failed';
  }
}
