import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface BulkDeploymentRequest {
  projectId: string;
  environmentId: string;
  applications: {
    applicationId: string;
    version?: string;
    configuration?: { [key: string]: any };
  }[];
  strategy?: string;
  workflowId?: string;
  parameters?: { [key: string]: any };
  secretMappings?: { [key: string]: string };
  provider?: string;
  configuration?: { [key: string]: any };
}

export interface DeploymentResponse {
  deploymentId: string;
  workflowExecution: string;
  status: string;
  applications?: ApplicationInfo[];
  components?: ComponentInfo[];
  createdAt: string;
}

export interface ApplicationInfo {
  id: string;
  name: string;
  version: string;
  status: string;
}

export interface ComponentInfo {
  id: string;
  name: string;
  applicationId: string;
  version: string;
  status: string;
}

export interface EnvironmentVersion {
  id: string;
  applicationId?: string;
  componentId?: string;
  projectId: string;
  environmentId: string;
  version: string;
  deploymentId: string;
  status: string;
  deployedAt: string;
  deployedBy: string;
  promotedFrom?: string;
  application?: any;
  component?: any;
}

export interface VersionMatrix {
  [entityName: string]: {
    [environmentId: string]: string;
  };
}

export interface PromotionRequest {
  applicationId?: string;
  componentId?: string;
  projectId: string;
  sourceEnvironmentId: string;
  targetEnvironmentId: string;
  version: string;
  workflowId: string;
  provider?: string;           // Changed from providerType to provider
  parameters?: { [key: string]: any };
  secretMappings?: { [key: string]: string };
  strategy?: string;
  configuration?: { [key: string]: any };
}

@Injectable({
  providedIn: 'root'
})
export class DeploymentManagementService {
  private deploymentApiUrl = `${environment.apiUrl}/workflow-service/deployments`; // Use workflow service for deployment creation
  private versionApiUrl = `${environment.apiUrl}/deployments`; // Use deployment service for version/history data

  constructor(private http: HttpClient) {}

  /**
   * Create a bulk deployment
   */
  createDeployment(request: BulkDeploymentRequest): Observable<DeploymentResponse> {
    // Use workflow service multi-app deployment endpoint
    return this.http.post<DeploymentResponse>(`${this.deploymentApiUrl}/multi-app`, request);
  }

  /**
   * Get environment versions for a project
   */
  getEnvironmentVersions(projectId: string, environmentIds?: string[]): Observable<{ versions: EnvironmentVersion[], total: number }> {
    // Use deployment service endpoint for project deployments
    return this.http.get<{ versions: EnvironmentVersion[], total: number }>(`${this.versionApiUrl}/projects/${projectId}/deployments`);
  }

  /**
   * Get version matrix showing which versions are deployed to which environments
   */
  getVersionMatrix(projectId: string): Observable<{ matrix: VersionMatrix }> {
    // Use deployment service endpoint for version matrix
    return this.http.get<{ matrix: VersionMatrix }>(`${this.versionApiUrl}/versions/matrix/${projectId}`);
  }

  /**
   * Update deployment status
   */
  updateDeploymentStatus(deploymentId: string, status: string): Observable<{ message: string, status: string }> {
    return this.http.put<{ message: string, status: string }>(`${this.deploymentApiUrl}/${deploymentId}/status`, { status });
  }

  /**
   * Promote a version from one environment to another
   */
  promoteVersion(request: PromotionRequest): Observable<{ message: string, deployment: DeploymentResponse }> {
    return this.http.post<{ message: string, deployment: DeploymentResponse }>(`${this.deploymentApiUrl}/promote`, request);
  }

  /**
   * Get deployment logs
   */
  getDeploymentLogs(deploymentId: string): Observable<{ logs: any[] }> {
    // Use workflow service for deployment logs since it handles workflow execution logs
    return this.http.get<{ logs: any[] }>(`${this.deploymentApiUrl}/${deploymentId}/logs`);
  }

  /**
   * Get deployment status options
   */
  getDeploymentStatuses(): string[] {
    return ['pending', 'in_progress', 'succeeded', 'failed', 'rolled_back'];
  }

  /**
   * Get deployment strategies
   */
  getDeploymentStrategies(): string[] {
    return ['rolling_update', 'blue_green', 'canary', 'recreate'];
  }

  /**
   * Format deployment status for display
   */
  formatStatus(status: string): string {
    switch (status) {
      case 'pending': return 'Pending';
      case 'in_progress': return 'In Progress';
      case 'succeeded': return 'Succeeded';
      case 'failed': return 'Failed';
      case 'rolled_back': return 'Rolled Back';
      default: return status;
    }
  }

  /**
   * Get status color class for UI
   */
  getStatusColorClass(status: string): string {
    switch (status) {
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'in_progress': return 'text-blue-600 bg-blue-100';
      case 'succeeded': return 'text-green-600 bg-green-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'rolled_back': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  }

  /**
   * Check if status indicates deployment is in progress
   */
  isDeploymentInProgress(status: string): boolean {
    return status === 'pending' || status === 'in_progress';
  }

  /**
   * Check if status indicates deployment completed successfully
   */
  isDeploymentSuccessful(status: string): boolean {
    return status === 'succeeded';
  }

  /**
   * Check if status indicates deployment failed
   */
  isDeploymentFailed(status: string): boolean {
    return status === 'failed';
  }
}
