import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map } from 'rxjs/operators';

export interface Secret {
  id: string;
  name: string;
  description: string;
  scopeId: string;
  scope?: {
    id: string;
    name: string;
    type: string;
  };
  type: string;
  provider: string;
  tags: string[];
  metadata: any;
  rotationPolicy?: {
    id: string;
    name: string;
    interval: string;
  };
  lastRotated?: string;
  nextRotation?: string;
  requiresApproval: boolean;
  status: string;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ProjectSecretBinding {
  id: string;
  projectId: string;
  secretId: string;
  variableName: string;
  variableType: string;
  accessLevel: string;
  environments: string[];
  services: string[];
  description: string;
  secret?: Secret;
  createdAt: string;
  updatedAt: string;
}

export interface SecretVariable {
  id: string;
  secretId: string;
  projectId: string;
  name: string;
  type: string;
  path?: string;
  format?: string;
  environment?: string;
  service?: string;
  namespace?: string;
  transform?: any;
  createdAt: string;
  updatedAt: string;
}

export interface SecretScope {
  id: string;
  name: string;
  type: string;
  description: string;
  isActive: boolean;
}

export interface Provider {
  id: string;
  name: string;
  type: string;
  description: string;
  isActive: boolean;
  testStatus: string;
}

export interface CreateSecretRequest {
  name: string;
  description: string;
  scopeId: string;
  type: string;
  provider: string;
  value: string;
  tags: string[];
  metadata: any;
  rotationPolicyId?: string;
  requiresApproval: boolean;
  expiresAt?: string;
}

export interface BindSecretToProjectRequest {
  secretId: string;
  variableName: string;
  variableType: string;
  accessLevel: string;
  environments: string[];
  services: string[];
  description: string;
}

export interface CreateSecretVariableRequest {
  secretId: string;
  name: string;
  type: string;
  path?: string;
  format?: string;
  environment?: string;
  service?: string;
  namespace?: string;
  transform?: any;
}

export interface DeploymentSecretsRequest {
  deploymentId: string;
  projectId: string;
  environment: string;
  service?: string;
  namespace?: string;
}

export interface WorkflowSecretsRequest {
  workflowId: string;
  executionId: string;
  projectId: string;
  environment?: string;
  service?: string;
  stepName?: string;
  secretMapping?: { [templateVariable: string]: string };
}

export interface SecretVariableResponse {
  name: string;
  type: string;
  value?: string;
  path?: string;
  format?: string;
  environment?: string;
  service?: string;
  namespace?: string;
  transform?: any;
  secretId: string;
  variableId: string;
}

@Injectable({
  providedIn: 'root'
})
export class SecretsService {
  private baseUrl = '/api/v1/secrets-service';
  private secretsSubject = new BehaviorSubject<Secret[]>([]);
  public secrets$ = this.secretsSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Secret Management
  getSecrets(params?: any): Observable<{ secrets: Secret[], total: number }> {
    let httpParams = new HttpParams();
    if (params) {
      Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
          httpParams = httpParams.set(key, params[key]);
        }
      });
    }

    return this.http.get<{ secrets: Secret[], total: number }>(`${this.baseUrl}/secrets`, {
      params: httpParams
    }).pipe(
      map(response => {
        this.secretsSubject.next(response.secrets);
        return response;
      })
    );
  }

  getSecret(id: string): Observable<Secret> {
    return this.http.get<Secret>(`${this.baseUrl}/secrets/${id}`);
  }

  createSecret(request: CreateSecretRequest): Observable<Secret> {
    return this.http.post<Secret>(`${this.baseUrl}/secrets`, request);
  }

  updateSecret(id: string, updates: Partial<Secret>): Observable<Secret> {
    return this.http.put<Secret>(`${this.baseUrl}/secrets/${id}`, updates);
  }

  deleteSecret(id: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/secrets/${id}`);
  }

  rotateSecret(id: string): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/secrets/${id}/rotate`, {});
  }

  // Project Secret Management
  getProjectSecrets(projectId: string, params?: any): Observable<{ secrets: ProjectSecretBinding[] }> {
    let httpParams = new HttpParams();
    if (params) {
      Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
          httpParams = httpParams.set(key, params[key]);
        }
      });
    }

    return this.http.get<{ secrets: ProjectSecretBinding[] }>(`${this.baseUrl}/projects/${projectId}/secrets`, {
      params: httpParams
    });
  }

  bindSecretToProject(projectId: string, request: BindSecretToProjectRequest): Observable<ProjectSecretBinding> {
    return this.http.post<ProjectSecretBinding>(`${this.baseUrl}/projects/${projectId}/secrets/bind`, request);
  }

  unbindSecretFromProject(projectId: string, bindingId: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/projects/${projectId}/secrets/${bindingId}`);
  }

  // Secret Variables
  getProjectSecretVariables(projectId: string, params?: any): Observable<{ variables: SecretVariable[] }> {
    console.log('SecretsService: Making request to', `${this.baseUrl}/projects/${projectId}/variables`);
    console.log('SecretsService: Expecting AuthInterceptor to add token automatically');

    let httpParams = new HttpParams();
    if (params) {
      Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
          httpParams = httpParams.set(key, params[key]);
        }
      });
    }

    return this.http.get<{ variables: SecretVariable[] }>(`${this.baseUrl}/projects/${projectId}/variables`, {
      params: httpParams
    });
  }

  createSecretVariable(projectId: string, request: CreateSecretVariableRequest): Observable<SecretVariable> {
    return this.http.post<SecretVariable>(`${this.baseUrl}/projects/${projectId}/variables`, request);
  }

  updateSecretVariable(projectId: string, variableId: string, updates: Partial<SecretVariable>): Observable<SecretVariable> {
    return this.http.put<SecretVariable>(`${this.baseUrl}/projects/${projectId}/variables/${variableId}`, updates);
  }

  deleteSecretVariable(projectId: string, variableId: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/projects/${projectId}/variables/${variableId}`);
  }

  // Integration APIs
  getDeploymentSecrets(request: DeploymentSecretsRequest): Observable<{
    variables: SecretVariableResponse[],
    errors: string[],
    metadata: any
  }> {
    return this.http.post<{
      variables: SecretVariableResponse[],
      errors: string[],
      metadata: any
    }>(`${this.baseUrl}/integration/deployment/secrets`, request);
  }

  getWorkflowSecrets(request: WorkflowSecretsRequest): Observable<{
    variables: SecretVariableResponse[],
    errors: string[],
    metadata: any
  }> {
    return this.http.post<{
      variables: SecretVariableResponse[],
      errors: string[],
      metadata: any
    }>(`${this.baseUrl}/integration/workflow/secrets`, request);
  }

  // Scopes
  getScopes(): Observable<{ scopes: SecretScope[] }> {
    return this.http.get<{ scopes: SecretScope[] }>(`${this.baseUrl}/scopes`);
  }

  createScope(scope: any): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/scopes`, scope);
  }

  // Providers
  getProviders(): Observable<{ providers: Provider[] }> {
    return this.http.get<{ providers: Provider[] }>(`${this.baseUrl}/providers`);
  }

  getProviderTypes(): Observable<{ types: any[], total: number }> {
    return this.http.get<{ types: any[], total: number }>(`${this.baseUrl}/providers/types`);
  }

  createProvider(provider: any): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/providers`, provider);
  }

  updateProvider(providerId: string, provider: any): Observable<any> {
    return this.http.put<any>(`${this.baseUrl}/providers/${providerId}`, provider);
  }

  deleteProvider(providerId: string): Observable<any> {
    return this.http.delete<any>(`${this.baseUrl}/providers/${providerId}`);
  }

  testProvider(providerId: string): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/providers/${providerId}/test`, {});
  }

  // Rotation Policies
  getRotationPolicies(): Observable<{ policies: any[] }> {
    return this.http.get<{ policies: any[] }>(`${this.baseUrl}/rotation/policies`);
  }

  // Audit and Monitoring
  getAuditLogs(params?: any): Observable<any> {
    let httpParams = new HttpParams();
    if (params) {
      Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
          httpParams = httpParams.set(key, params[key]);
        }
      });
    }

    return this.http.get<any>(`${this.baseUrl}/audit/logs`, {
      params: httpParams
    });
  }

  getMetrics(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/audit/metrics`);
  }

  // Secret Mapping for Workflows
  validateSecretMapping(templateId: string, projectId: string): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/validate-mapping`, {
      templateId,
      projectId
    });
  }

  getSecretRecommendations(templateId: string, projectId: string): Observable<{ recommendations: any[] }> {
    return this.http.get<{ recommendations: any[] }>(`${this.baseUrl}/recommendations`, {
      params: new HttpParams().set('templateId', templateId).set('projectId', projectId)
    });
  }

  // Secret compatibility checking
  isSecretCompatibleWithTemplate(secret: any, templateVariable: string): boolean {
    const secretName = secret.name.toLowerCase();
    const templateVar = templateVariable.toLowerCase();

    // Direct name match
    if (secretName === templateVar) return true;

    // Pattern matching for common secret types
    const patterns = [
      { template: ['username', 'user'], secret: ['user', 'username'] },
      { template: ['password', 'pass'], secret: ['pass', 'password'] },
      { template: ['key', 'private'], secret: ['key', 'private'] },
      { template: ['token', 'api'], secret: ['token', 'api'] },
      { template: ['cert', 'certificate'], secret: ['cert', 'certificate'] },
      { template: ['url', 'endpoint'], secret: ['url', 'endpoint'] },
      { template: ['host', 'hostname'], secret: ['host', 'hostname'] },
      { template: ['port'], secret: ['port'] },
      { template: ['database', 'db'], secret: ['database', 'db'] }
    ];

    for (const pattern of patterns) {
      const templateMatches = pattern.template.some(p => templateVar.includes(p));
      const secretMatches = pattern.secret.some(p => secretName.includes(p));
      if (templateMatches && secretMatches) {
        return true;
      }
    }

    return false;
  }

  // Secret mapping utilities
  generateSecretMappingRecommendations(
    templateVariables: string[],
    projectSecrets: any[]
  ): { [templateVar: string]: { secret: string; confidence: number; reason: string } } {
    const recommendations: { [templateVar: string]: { secret: string; confidence: number; reason: string } } = {};

    templateVariables.forEach(templateVar => {
      let bestMatch: { secret: any; confidence: number; reason: string } | any = null;

      projectSecrets.forEach(secret => {
        const confidence = this.calculateSecretMatchConfidence(secret, templateVar);
        if (confidence > 0.5 && (!bestMatch || confidence > bestMatch.confidence)) {
          bestMatch = {
            secret,
            confidence,
            reason: this.getMatchReason(secret, templateVar)
          };
        }
      });

      if (bestMatch) {
        recommendations[templateVar] = {
          secret: bestMatch.secret.name,
          confidence: bestMatch.confidence,
          reason: bestMatch.reason
        };
      }
    });

    return recommendations;
  }

  private calculateSecretMatchConfidence(secret: any, templateVariable: string): number {
    const secretName = secret.name.toLowerCase();
    const templateVar = templateVariable.toLowerCase();

    // Exact match
    if (secretName === templateVar) return 1.0;

    // High confidence patterns
    const highConfidencePatterns = [
      { template: 'username', secret: ['username', 'user'], confidence: 0.9 },
      { template: 'password', secret: ['password', 'pass'], confidence: 0.9 },
      { template: 'private_key', secret: ['private_key', 'key'], confidence: 0.9 },
      { template: 'api_token', secret: ['api_token', 'token'], confidence: 0.9 }
    ];

    for (const pattern of highConfidencePatterns) {
      if (templateVar.includes(pattern.template)) {
        for (const secretPattern of pattern.secret) {
          if (secretName.includes(secretPattern)) {
            return pattern.confidence;
          }
        }
      }
    }

    // Medium confidence patterns
    const mediumConfidencePatterns = [
      { template: ['user'], secret: ['user'], confidence: 0.7 },
      { template: ['pass'], secret: ['pass'], confidence: 0.7 },
      { template: ['key'], secret: ['key'], confidence: 0.7 },
      { template: ['token'], secret: ['token'], confidence: 0.7 }
    ];

    for (const pattern of mediumConfidencePatterns) {
      const templateMatches = pattern.template.some(p => templateVar.includes(p));
      const secretMatches = pattern.secret.some(p => secretName.includes(p));
      if (templateMatches && secretMatches) {
        return pattern.confidence;
      }
    }

    // Low confidence - any partial match
    if (secretName.includes(templateVar) || templateVar.includes(secretName)) {
      return 0.5;
    }

    return 0;
  }

  private getMatchReason(secret: any, templateVariable: string): string {
    const secretName = secret.name.toLowerCase();
    const templateVar = templateVariable.toLowerCase();

    if (secretName === templateVar) {
      return 'Exact name match';
    }

    if (templateVar.includes('username') && secretName.includes('user')) {
      return 'Username pattern match';
    }

    if (templateVar.includes('password') && secretName.includes('pass')) {
      return 'Password pattern match';
    }

    if (templateVar.includes('key') && secretName.includes('key')) {
      return 'Key pattern match';
    }

    if (templateVar.includes('token') && secretName.includes('token')) {
      return 'Token pattern match';
    }

    return 'Partial name match';
  }

  // Utility methods
  refreshSecrets(): void {
    this.getSecrets().subscribe();
  }

  getCurrentSecrets(): Secret[] {
    return this.secretsSubject.value;
  }
}
