export interface DeployableGroup {
  id: string;
  name: string;
  description?: string;
  projectId: string;
  deployables: Deployable[];
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface Deployable {
  id: string;
  name: string;
  description?: string;
  type: DeployableType;
  parentId?: string; // For hierarchical relationships
  projectId: string;
  groupId?: string;
  
  // Status
  status: DeploymentStatus;
  
  // Repository & Build
  repository?: RepositoryInfo;
  buildConfig?: BuildConfiguration;
  
  // Deployment
  deploymentConfig: DeploymentConfiguration;
  deploymentStrategy: DeploymentStrategy;
  
  // Health & Monitoring
  healthChecks: HealthCheck[];
  healthStatus: HealthStatus;
  
  // Relationships
  children: Deployable[]; // Sub-components
  dependencies: DeployableDependency[];
  
  // Environment Status
  environments: DeployableEnvironment[];
  
  // Workflow Integration
  deploymentWorkflow?: WorkflowReference;
  rollbackWorkflow?: WorkflowReference;
  healthCheckWorkflow?: WorkflowReference;
  
  // Metadata
  tags?: string[];
  version: string;
  owner?: string;
  createdAt: string;
  updatedAt: string;
}

export enum DeployableType {
  // Application Artifacts
  JAR = 'jar',
  WAR = 'war',
  EAR = 'ear',
  ZIP = 'zip',
  HELM_CHART = 'helm_chart',
  
  // Container & Images
  DOCKER_IMAGE = 'docker_image',
  CONTAINER = 'container',
  
  // Applications
  APPLICATION = 'application',
  WEB_APPLICATION = 'web_application',
  MICROSERVICE = 'microservice',
  MOBILE_APP = 'mobile_app',
  DESKTOP_APP = 'desktop_app',
  
  // Infrastructure components
  INFRASTRUCTURE = 'infrastructure',
  DATABASE = 'database',
  MESSAGE_QUEUE = 'message_queue',
  CACHE = 'cache',
  STORAGE = 'storage',
  
  // Supporting services
  SERVICE = 'service',
  API_GATEWAY = 'api_gateway',
  LOAD_BALANCER = 'load_balancer',
  MONITORING = 'monitoring',
  LOGGING = 'logging',
  SECURITY = 'security',
  
  // Sub-components
  COMPONENT = 'component',
  FRONTEND = 'frontend',
  BACKEND = 'backend',
  WORKER = 'worker',
  SCHEDULED_JOB = 'scheduled_job',
  API_ENDPOINT = 'api_endpoint',
  
  // Infrastructure as Code
  TERRAFORM_MODULE = 'terraform_module',
  DOCKER_COMPOSE = 'docker_compose',
  KUBERNETES_MANIFEST = 'kubernetes_manifest',
  
  // Generic Types
  GENERIC = 'generic',
  OTHER = 'other'
}

export interface RepositoryInfo {
  url: string;
  branch: string;
  provider: 'github' | 'gitlab' | 'bitbucket' | 'azure_devops';
  accessToken?: string;
  deployKey?: string;
  buildPath?: string;
  dockerfilePath?: string;
}

export interface BuildConfiguration {
  buildCommand?: string;
  testCommand?: string;
  buildArgs?: { [key: string]: string };
  dockerfile?: string;
  buildContext?: string;
  targetPlatform?: string[];
  cachingStrategy?: 'layer' | 'registry' | 'local';
}

export interface DeploymentConfiguration {
  strategy: DeploymentStrategy;
  resources: ResourceRequirements;
  networking: NetworkingConfig;
  environment: EnvironmentVariables;
  volumes?: VolumeMount[];
  secrets?: SecretReference[];
  configMaps?: ConfigMapReference[];
  
  // Workflow Integration
  preDeploymentSteps?: WorkflowStep[];
  postDeploymentSteps?: WorkflowStep[];
  rollbackSteps?: WorkflowStep[];
}

export interface DeploymentStrategy {
  type: 'rolling' | 'blue_green' | 'canary' | 'recreate' | 'custom';
  maxUnavailable?: number | string;
  maxSurge?: number | string;
  canaryPercentage?: number;
  blueGreenConfig?: BlueGreenConfig;
  customWorkflow?: string; // Reference to custom deployment workflow
}

export interface BlueGreenConfig {
  autoPromoteAfter?: number; // seconds
  scaleDownDelay?: number; // seconds
  previewService?: boolean;
}

export interface ResourceRequirements {
  cpu: string;
  memory: string;
  storage?: string;
  replicas: number;
  minReplicas?: number;
  maxReplicas?: number;
  autoscaling?: AutoscalingConfig;
}

export interface AutoscalingConfig {
  enabled: boolean;
  targetCPU?: number;
  targetMemory?: number;
  customMetrics?: CustomMetric[];
}

export interface CustomMetric {
  name: string;
  targetValue: number;
  type: 'Resource' | 'Pods' | 'Object' | 'External';
}

export interface NetworkingConfig {
  ports: Port[];
  ingress?: IngressConfig;
  service?: ServiceConfig;
}

export interface Port {
  name: string;
  port: number;
  targetPort: number;
  protocol: 'TCP' | 'UDP';
}

export interface IngressConfig {
  enabled: boolean;
  hostname?: string;
  path: string;
  pathType: 'Exact' | 'Prefix' | 'ImplementationSpecific';
  tls: boolean;
  annotations?: { [key: string]: string };
}

export interface ServiceConfig {
  type: 'ClusterIP' | 'NodePort' | 'LoadBalancer' | 'ExternalName';
  sessionAffinity?: 'None' | 'ClientIP';
  annotations?: { [key: string]: string };
}

export interface EnvironmentVariables {
  [key: string]: string | SecretReference | ConfigMapReference;
}

export interface VolumeMount {
  name: string;
  mountPath: string;
  subPath?: string;
  readOnly?: boolean;
  source: VolumeSource;
}

export interface VolumeSource {
  type: 'configMap' | 'secret' | 'persistentVolumeClaim' | 'emptyDir' | 'hostPath';
  name?: string;
  path?: string;
}

export interface SecretReference {
  name: string;
  key?: string;
  optional?: boolean;
}

export interface ConfigMapReference {
  name: string;
  key?: string;
  optional?: boolean;
}

export interface HealthCheck {
  type: 'http' | 'tcp' | 'exec' | 'grpc';
  path?: string;
  port?: number;
  command?: string[];
  initialDelaySeconds: number;
  periodSeconds: number;
  timeoutSeconds: number;
  failureThreshold: number;
  successThreshold: number;
}

export enum HealthStatus {
  HEALTHY = 'healthy',
  WARNING = 'warning',
  ERROR = 'error',
  UNKNOWN = 'unknown',
  DEPLOYING = 'deploying',
  MAINTENANCE = 'maintenance'
}

export interface DeployableDependency {
  id: string;
  name: string;
  type: 'hard' | 'soft';
  deployableId: string;
  version?: string;
  healthStatus?: HealthStatus;
}

export interface DeployableEnvironment {
  name: string;
  status: HealthStatus;
  currentVersion: string;
  deploymentStatus: DeploymentStatus;
  lastDeployedAt?: string;
  instances: DeployableInstance[];
  url?: string;
  healthScore: number;
  metrics?: DeployableMetrics;
  
  // Environment-specific configuration overrides
  configOverrides?: Partial<DeploymentConfiguration>;
  environmentVariables?: EnvironmentVariables;
}

export interface DeployableInstance {
  id: string;
  name: string;
  status: HealthStatus;
  version: string;
  startedAt: string;
  restartCount: number;
  resources: {
    cpu: number;
    memory: number;
  };
  node?: string;
  logs?: LogEntry[];
}

export interface DeployableMetrics {
  cpu: MetricData[];
  memory: MetricData[];
  network: NetworkMetrics;
  storage?: StorageMetrics;
  custom?: { [key: string]: MetricData[] };
}

export interface MetricData {
  timestamp: string;
  value: number;
}

export interface NetworkMetrics {
  bytesIn: MetricData[];
  bytesOut: MetricData[];
  requestsPerSecond?: MetricData[];
  errorRate?: MetricData[];
  responseTime?: MetricData[];
}

export interface StorageMetrics {
  used: MetricData[];
  available: MetricData[];
  iops?: MetricData[];
}

export interface LogEntry {
  timestamp: string;
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
  message: string;
  source?: string;
}

export enum DeploymentStatus {
  PENDING = 'pending',
  DEPLOYING = 'deploying',
  DEPLOYED = 'deployed',
  IN_PROGRESS = 'in_progress',
  SUCCEEDED = 'succeeded',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  ROLLING_BACK = 'rolling_back'
}

// Workflow Integration Models
export interface WorkflowReference {
  workflowId: string;
  workflowName: string;
  version?: string;
  parameters?: { [key: string]: any };
}

export interface WorkflowStep {
  id: string;
  name: string;
  type: string;
  parameters?: { [key: string]: any };
  dependsOn?: string[];
  condition?: string;
  timeout?: number;
  retryPolicy?: RetryPolicy;
}

export interface RetryPolicy {
  maxAttempts: number;
  backoffStrategy: 'fixed' | 'exponential' | 'linear';
  backoffDelay: number;
  maxBackoffDelay?: number;
}

// Request/Response interfaces for API
export interface CreateDeployableRequest {
  name: string;
  description?: string;
  type: DeployableType;
  parentId?: string;
  groupId?: string;
  repository?: RepositoryInfo;
  buildConfig?: BuildConfiguration;
  deploymentConfig: DeploymentConfiguration;
  tags?: string[];
}

export interface UpdateDeployableRequest {
  name?: string;
  description?: string;
  repository?: RepositoryInfo;
  buildConfig?: BuildConfiguration;
  deploymentConfig?: Partial<DeploymentConfiguration>;
  tags?: string[];
}

export interface DeployRequest {
  deployableId: string;
  environment: string;
  version?: string;
  configOverrides?: Partial<DeploymentConfiguration>;
  workflowParameters?: { [key: string]: any };
  dryRun?: boolean;
}

export interface RollbackRequest {
  deployableId: string;
  environment: string;
  targetVersion: string;
  workflowParameters?: { [key: string]: any };
}

export interface ScaleRequest {
  deployableId: string;
  environment: string;
  replicas: number;
}

export interface DeploymentPromotion {
  id: string;
  sourceDeploymentId: string;
  targetEnvironmentId: string;
  status: DeploymentStatus;
  description?: string;
  runTests: boolean;
  createdAt: string;
  completedAt?: string;
  error?: string;
}

// Type Guards
export function isTopLevelDeployable(type: DeployableType): boolean {
  return [
    DeployableType.WEB_APPLICATION,
    DeployableType.MICROSERVICE,
    DeployableType.MOBILE_APP,
    DeployableType.DESKTOP_APP
  ].includes(type);
}

export function isInfrastructureDeployable(type: DeployableType): boolean {
  return [
    DeployableType.DATABASE,
    DeployableType.MESSAGE_QUEUE,
    DeployableType.CACHE,
    DeployableType.STORAGE,
    DeployableType.API_GATEWAY,
    DeployableType.LOAD_BALANCER,
    DeployableType.MONITORING,
    DeployableType.LOGGING,
    DeployableType.SECURITY
  ].includes(type);
}

export function isComponentDeployable(type: DeployableType): boolean {
  return [
    DeployableType.FRONTEND,
    DeployableType.BACKEND,
    DeployableType.WORKER,
    DeployableType.SCHEDULED_JOB,
    DeployableType.API_ENDPOINT
  ].includes(type);
}

export function isInfrastructureAsCodeDeployable(type: DeployableType): boolean {
  return [
    DeployableType.TERRAFORM_MODULE,
    DeployableType.HELM_CHART,
    DeployableType.DOCKER_COMPOSE,
    DeployableType.KUBERNETES_MANIFEST
  ].includes(type);
}

// Create Request Interface
export interface DeployableCreateRequest {
  name: string;
  description?: string;
  type: DeployableType;
  parentId?: string;
  projectId: string;
  groupId?: string;
  repository?: RepositoryInfo;
  buildConfig?: BuildConfiguration;
  deploymentConfig: Partial<DeploymentConfiguration>;
  deploymentStrategy?: DeploymentStrategy;
  healthChecks?: HealthCheck[];
  dependencies?: DeployableDependencyRequest[];
  tags?: string[];
  version?: string;
  owner?: string;
}

export interface DeployableDependencyRequest {
  dependsOnId: string;
  type: 'hard' | 'soft';
  required: boolean;
}

// Deployment Wizard Interfaces
export interface DeploymentWizardState {
  step: 1 | 2 | 3;
  selectedDeployables: Deployable[];
  selectedGroups: DeployableGroup[];
  selectedEnvironment: any | null;
  selectedPlugin: DeploymentPlugin | null;
  pluginConfiguration: { [key: string]: any };
  isDeploying: boolean;
}

export interface DeploymentPlugin {
  id: string;
  name: string;
  version: string;
  description: string;
  type: 'helm' | 'kubernetes' | 'openshift' | 'ansible' | 'docker' | 'terraform' | 'generic';
  supportedArtifacts: DeployableType[];
  // Updated to support actual plugin manifest structure
  configuration?: {
    schema: {
      type: string;
      required?: string[];
      properties: { [key: string]: PluginPropertySchema };
    };
  };
  // Deprecated - keeping for backward compatibility
  configurationSchema?: PluginConfigSchema[];
  isEnabled: boolean;
  isDefault?: boolean;
}

export interface PluginPropertySchema {
  type: string;
  title?: string;
  description?: string;
  pattern?: string;
  examples?: string[];
  default?: any;
  sensitive?: boolean;
  enum?: string[];
  minimum?: number;
  maximum?: number;
}

export interface PluginConfigSchema {
  key: string;
  label: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'textarea' | 'file' | 'password';
  required: boolean;
  defaultValue?: any;
  options?: { label: string; value: any }[];
  description?: string;
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
  };
}

export interface EnvironmentPluginMapping {
  environmentId: string;
  defaultPluginId: string;
  allowUserOverride: boolean;
  adminConfiguration: { [key: string]: any };
}

export interface DeploymentRequest {
  deployables: string[];
  groups?: string[];
  environmentId: string;
  pluginId: string;
  configuration: { [key: string]: any };
  runHealthChecks?: boolean;
  notifyOnComplete?: boolean;
}

// Helper functions for artifact types
export function getArtifactIcon(type: DeployableType): string {
  switch (type) {
    case DeployableType.JAR:
      return 'fas fa-file-archive';
    case DeployableType.WAR:
      return 'fas fa-globe';
    case DeployableType.EAR:
      return 'fas fa-server';
    case DeployableType.ZIP:
      return 'fas fa-file-zipper';
    case DeployableType.HELM_CHART:
      return 'fas fa-ship';
    case DeployableType.DOCKER_IMAGE:
    case DeployableType.CONTAINER:
      return 'fab fa-docker';
    case DeployableType.APPLICATION:
    case DeployableType.WEB_APPLICATION:
      return 'fas fa-desktop';
    case DeployableType.MICROSERVICE:
    case DeployableType.SERVICE:
      return 'fas fa-cogs';
    case DeployableType.DATABASE:
      return 'fas fa-database';
    case DeployableType.INFRASTRUCTURE:
      return 'fas fa-cloud';
    case DeployableType.GENERIC:
      return 'fas fa-cube';
    default:
      return 'fas fa-file';
  }
}

export function getArtifactColor(type: DeployableType): string {
  switch (type) {
    case DeployableType.JAR:
    case DeployableType.WAR:
    case DeployableType.EAR:
      return 'from-orange-500 to-red-500';
    case DeployableType.ZIP:
      return 'from-gray-500 to-gray-600';
    case DeployableType.HELM_CHART:
      return 'from-blue-500 to-indigo-500';
    case DeployableType.DOCKER_IMAGE:
    case DeployableType.CONTAINER:
      return 'from-cyan-500 to-blue-500';
    case DeployableType.APPLICATION:
    case DeployableType.WEB_APPLICATION:
      return 'from-green-500 to-emerald-500';
    case DeployableType.MICROSERVICE:
    case DeployableType.SERVICE:
      return 'from-purple-500 to-pink-500';
    case DeployableType.DATABASE:
      return 'from-yellow-500 to-orange-500';
    case DeployableType.INFRASTRUCTURE:
      return 'from-indigo-500 to-purple-500';
    case DeployableType.GENERIC:
      return 'from-slate-500 to-gray-500';
    default:
      return 'from-gray-400 to-gray-500';
  }
}

// Helper Functions for Legacy Compatibility
export function isApplication(type: DeployableType): boolean {
  return type === DeployableType.APPLICATION || isTopLevelDeployable(type);
}

export function isComponent(type: DeployableType): boolean {
  return type === DeployableType.COMPONENT || isComponentDeployable(type);
}

export function isService(type: DeployableType): boolean {
  return type === DeployableType.SERVICE || type === DeployableType.MICROSERVICE;
}

export function isInfrastructure(type: DeployableType): boolean {
  return type === DeployableType.INFRASTRUCTURE || isInfrastructureDeployable(type);
}

// Additional Response interfaces for API
export interface DeployableUpdateRequest {
  name?: string;
  description?: string;
  repository?: RepositoryInfo;
  buildConfig?: BuildConfiguration;
  deploymentConfig?: Partial<DeploymentConfiguration>;
  tags?: string[];
}

export interface DeployableListResponse {
  deployables: Deployable[];
  total: number;
  page: number;
  pageSize: number;
}
