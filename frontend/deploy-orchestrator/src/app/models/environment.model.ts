export interface Environment {
  id?: string;
  name: string;
  description: string;
  projectId: string;
  provider: ProviderConfig;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  status?: 'active' | 'inactive' | 'error';
  lastDeployment?: string;
  deploymentCount?: number;
  tags?: string[];
  metadata?: { [key: string]: any };
}

export interface ProviderConfig {
  type: string;
  config: {
    cluster: string;
    region: string;
    zone?: string;
    namespace: string;
    endpoint?: string;
    project?: string;
    extra?: { [key: string]: any };
  };
}

export interface EnvironmentVariable {
  name: string;
  value: string;
  sensitive: boolean;
  description?: string;
}

export interface EnvironmentSecret {
  name: string;
  key: string;
  description?: string;
}

export interface EnvironmentHealth {
  status: 'healthy' | 'unhealthy' | 'unknown';
  message: string;
  lastCheck: string;
  checks: HealthCheck[];
}

export interface HealthCheck {
  name: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  message: string;
  timestamp: string;
}

export interface EnvironmentMetrics {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkIn: number;
  networkOut: number;
  activeDeployments: number;
  lastUpdated: string;
}

export interface EnvironmentDeployment {
  id: string;
  name: string;
  version: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  replicas: number;
  endpoints: string[];
}

export interface EnvironmentLog {
  timestamp: string;
  level: string;
  message: string;
  source: string;
  deployment?: string;
}

export interface EnvironmentEvent {
  id: string;
  type: string;
  message: string;
  timestamp: string;
  source: string;
  severity: 'info' | 'warning' | 'error';
  metadata?: { [key: string]: any };
}

export interface EnvironmentBackup {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  size: number;
  status: 'creating' | 'completed' | 'failed';
  type: 'full' | 'incremental';
}

export interface EnvironmentRestore {
  backupId: string;
  targetEnvironmentId: string;
  options: {
    includeSecrets: boolean;
    includeConfigs: boolean;
    includeDeployments: boolean;
  };
}

export interface EnvironmentTemplate {
  id: string;
  name: string;
  description: string;
  provider: string;
  version: string;
  parameters: TemplateParameter[];
  defaultConfig: ProviderConfig;
  tags: string[];
  category: string;
}

export interface TemplateParameter {
  name: string;
  type: string;
  description: string;
  required: boolean;
  default?: any;
  options?: string[];
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
  };
}

export interface EnvironmentAccess {
  userId: string;
  groupId?: string;
  roleId: string;
  permissions: string[];
  grantedAt: string;
  grantedBy: string;
  expiresAt?: string;
}

export interface EnvironmentQuota {
  cpu: {
    limit: number;
    used: number;
  };
  memory: {
    limit: number;
    used: number;
  };
  storage: {
    limit: number;
    used: number;
  };
  deployments: {
    limit: number;
    used: number;
  };
}

export interface EnvironmentPolicy {
  id: string;
  name: string;
  description: string;
  rules: PolicyRule[];
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PolicyRule {
  type: string;
  condition: string;
  action: string;
  parameters: { [key: string]: any };
}

export interface EnvironmentMonitoring {
  enabled: boolean;
  alerting: {
    enabled: boolean;
    channels: string[];
    rules: AlertRule[];
  };
  logging: {
    enabled: boolean;
    level: string;
    retention: string;
  };
  metrics: {
    enabled: boolean;
    interval: string;
    retention: string;
  };
}

export interface AlertRule {
  name: string;
  condition: string;
  threshold: number;
  duration: string;
  severity: 'info' | 'warning' | 'critical';
  enabled: boolean;
}

export interface EnvironmentNetwork {
  vpc?: string;
  subnet?: string;
  securityGroups?: string[];
  loadBalancer?: {
    type: string;
    scheme: string;
    listeners: LoadBalancerListener[];
  };
  ingress?: {
    enabled: boolean;
    className?: string;
    annotations?: { [key: string]: string };
    rules: IngressRule[];
  };
}

export interface LoadBalancerListener {
  port: number;
  protocol: string;
  targetPort: number;
  healthCheck?: {
    path: string;
    interval: number;
    timeout: number;
    healthyThreshold: number;
    unhealthyThreshold: number;
  };
}

export interface IngressRule {
  host: string;
  paths: IngressPath[];
  tls?: {
    enabled: boolean;
    secretName?: string;
  };
}

export interface IngressPath {
  path: string;
  pathType: string;
  serviceName: string;
  servicePort: number;
}

export interface EnvironmentStorage {
  persistent: {
    enabled: boolean;
    storageClass?: string;
    size: string;
    accessModes: string[];
  };
  backup: {
    enabled: boolean;
    schedule: string;
    retention: string;
    destination: string;
  };
}

export interface EnvironmentSecurity {
  rbac: {
    enabled: boolean;
    serviceAccount?: string;
    roles: string[];
  };
  networkPolicies: {
    enabled: boolean;
    policies: NetworkPolicy[];
  };
  podSecurityPolicy: {
    enabled: boolean;
    policy: string;
  };
  secrets: {
    encryption: boolean;
    provider: string;
    keyId?: string;
  };
}

export interface NetworkPolicy {
  name: string;
  podSelector: { [key: string]: string };
  ingress?: NetworkPolicyRule[];
  egress?: NetworkPolicyRule[];
}

export interface NetworkPolicyRule {
  from?: NetworkPolicyPeer[];
  to?: NetworkPolicyPeer[];
  ports?: NetworkPolicyPort[];
}

export interface NetworkPolicyPeer {
  podSelector?: { [key: string]: string };
  namespaceSelector?: { [key: string]: string };
  ipBlock?: {
    cidr: string;
    except?: string[];
  };
}

export interface NetworkPolicyPort {
  protocol: string;
  port: number | string;
}

export interface EnvironmentScaling {
  horizontal: {
    enabled: boolean;
    minReplicas: number;
    maxReplicas: number;
    targetCPUUtilization: number;
    targetMemoryUtilization?: number;
    customMetrics?: ScalingMetric[];
  };
  vertical: {
    enabled: boolean;
    updateMode: string;
    resourcePolicy?: {
      containerPolicies: ContainerResourcePolicy[];
    };
  };
}

export interface ScalingMetric {
  name: string;
  type: string;
  target: {
    type: string;
    value: number;
  };
}

export interface ContainerResourcePolicy {
  containerName: string;
  minAllowed?: ResourceRequirements;
  maxAllowed?: ResourceRequirements;
  controlledResources?: string[];
}

export interface ResourceRequirements {
  cpu?: string;
  memory?: string;
}

export interface EnvironmentStatus {
  phase: 'pending' | 'active' | 'terminating' | 'failed';
  conditions: EnvironmentCondition[];
  message?: string;
  reason?: string;
  lastTransitionTime: string;
}

export interface EnvironmentCondition {
  type: string;
  status: 'True' | 'False' | 'Unknown';
  lastTransitionTime: string;
  reason?: string;
  message?: string;
}
