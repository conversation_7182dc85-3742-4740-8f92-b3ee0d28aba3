export interface PluginPermission {
  id: string;
  name: string;
  description: string;
  category: 'plugin' | 'provider' | 'template' | 'deployment';
  resource: string; // e.g., 'openshift-plugin', 'gke-provider', 'template:basic-deploy'
  action: string;  // e.g., 'view', 'install', 'configure', 'deploy', 'manage'
  scope: 'global' | 'project' | 'environment';
  createdAt: string;
  updatedAt: string;
}

export interface UserPluginPermissions {
  userId: string;
  permissions: PluginPermissionGrant[];
  effectivePermissions: string[]; // Computed from groups and roles
  lastUpdated: string;
}

export interface PluginPermissionGrant {
  permissionId: string;
  permission: PluginPermission;
  grantedBy: 'role' | 'group' | 'direct';
  grantedVia: string; // role ID, group ID, or 'direct'
  projectId?: string; // For project-scoped permissions
  environmentId?: string; // For environment-scoped permissions
  grantedAt: string;
  expiresAt?: string;
}

export interface PluginAccessControl {
  pluginName: string;
  requiredPermissions: PluginPermissionRequirement[];
  publicFeatures: string[]; // Features available to all authenticated users
  adminOnlyFeatures: string[]; // Features requiring admin role
  projectScopedFeatures: PluginProjectFeature[];
  environmentScopedFeatures: PluginEnvironmentFeature[];
}

export interface PluginPermissionRequirement {
  action: string;
  permission: string;
  scope: 'global' | 'project' | 'environment';
  description: string;
  required: boolean;
  alternatives?: string[]; // Alternative permissions that can satisfy this requirement
}

export interface PluginProjectFeature {
  feature: string;
  requiredPermission: string;
  description: string;
  appliesToProjects: string[] | 'all';
}

export interface PluginEnvironmentFeature {
  feature: string;
  requiredPermission: string;
  description: string;
  appliesToEnvironments: string[] | 'all';
  appliesToProviders: string[] | 'all';
}

export interface PluginRolePermissions {
  roleId: string;
  roleName: string;
  permissions: string[];
  pluginAccess: {
    [pluginName: string]: {
      canView: boolean;
      canInstall: boolean;
      canConfigure: boolean;
      canManage: boolean;
      canDeploy: boolean;
      allowedTemplates: string[];
      allowedProviders: string[];
      projectRestrictions: string[] | 'all';
      environmentRestrictions: string[] | 'all';
    };
  };
}

export interface PluginGroupPermissions {
  groupId: string;
  groupName: string;
  permissions: string[];
  projects: string[]; // Projects this group has access to
  pluginAccess: {
    [pluginName: string]: {
      canView: boolean;
      canDeploy: boolean;
      allowedTemplates: string[];
      allowedEnvironments: string[];
      projectScope: string[]; // Projects where this plugin can be used
    };
  };
}

export interface PluginPermissionCheck {
  hasPermission: boolean;
  reason?: string;
  requiredPermissions: string[];
  missingPermissions: string[];
  grantedVia?: string; // How the permission was granted
  scope?: {
    projectId?: string;
    environmentId?: string;
  };
}

export interface PluginFeatureAccess {
  feature: string;
  accessible: boolean;
  reason?: string;
  requiredPermission?: string;
  scope?: 'global' | 'project' | 'environment';
  restrictions?: {
    projects?: string[];
    environments?: string[];
    providers?: string[];
  };
}

export interface PluginPermissionMatrix {
  userId: string;
  plugins: {
    [pluginName: string]: {
      canView: boolean;
      canInstall: boolean;
      canConfigure: boolean;
      canManage: boolean;
      canDeploy: boolean;
      features: {
        [featureName: string]: PluginFeatureAccess;
      };
      templates: {
        [templateId: string]: {
          canView: boolean;
          canDeploy: boolean;
          allowedProjects: string[];
          allowedEnvironments: string[];
        };
      };
      providers: {
        [providerType: string]: {
          canView: boolean;
          canConfigure: boolean;
          canDeploy: boolean;
          allowedProjects: string[];
          allowedEnvironments: string[];
        };
      };
    };
  };
}

export interface PluginAuditLog {
  id: string;
  userId: string;
  userName: string;
  action: string;
  resource: string; // plugin name, template ID, etc.
  resourceType: 'plugin' | 'template' | 'provider' | 'deployment';
  details: {
    pluginName?: string;
    templateId?: string;
    providerType?: string;
    projectId?: string;
    environmentId?: string;
    parameters?: any;
    result?: 'success' | 'failure' | 'partial';
    error?: string;
  };
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  permissionUsed: string;
  scope?: {
    projectId?: string;
    environmentId?: string;
  };
}

export interface PluginPermissionPolicy {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  rules: PluginPermissionRule[];
  appliesTo: {
    users?: string[];
    groups?: string[];
    roles?: string[];
    projects?: string[];
    environments?: string[];
  };
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface PluginPermissionRule {
  condition: string; // e.g., "user.groups contains 'developers'"
  effect: 'allow' | 'deny';
  permissions: string[];
  resources: string[];
  scope?: {
    projects?: string[];
    environments?: string[];
  };
  timeRestrictions?: {
    startTime?: string;
    endTime?: string;
    daysOfWeek?: number[];
    timezone?: string;
  };
}

export interface PluginSecurityContext {
  userId: string;
  userGroups: string[];
  userRoles: string[];
  currentProject?: string;
  currentEnvironment?: string;
  sessionId: string;
  permissions: string[];
  restrictions: {
    allowedProjects: string[];
    allowedEnvironments: string[];
    allowedProviders: string[];
    timeRestrictions?: {
      startTime: string;
      endTime: string;
    };
  };
}

export interface PluginPermissionRequest {
  action: string;
  resource: string;
  resourceType: 'plugin' | 'template' | 'provider' | 'deployment';
  scope?: {
    projectId?: string;
    environmentId?: string;
  };
  context?: {
    templateId?: string;
    providerType?: string;
    parameters?: any;
  };
}

export interface PluginPermissionResponse {
  allowed: boolean;
  reason?: string;
  requiredPermissions: string[];
  grantedPermissions: string[];
  restrictions?: {
    projects?: string[];
    environments?: string[];
    providers?: string[];
    templates?: string[];
  };
  auditLogId?: string;
}

// Predefined plugin permissions
export const PLUGIN_PERMISSIONS = {
  // Plugin Management
  PLUGIN_VIEW: 'plugin:view',
  PLUGIN_INSTALL: 'plugin:install',
  PLUGIN_UNINSTALL: 'plugin:uninstall',
  PLUGIN_CONFIGURE: 'plugin:configure',
  PLUGIN_MANAGE: 'plugin:manage',
  PLUGIN_RELOAD: 'plugin:reload',
  PLUGIN_LOGS: 'plugin:logs',
  PLUGIN_METRICS: 'plugin:metrics',
  PLUGIN_HEALTH: 'plugin:health',

  // Provider Management
  PROVIDER_VIEW: 'provider:view',
  PROVIDER_CONFIGURE: 'provider:configure',
  PROVIDER_DEPLOY: 'provider:deploy',
  PROVIDER_MANAGE: 'provider:manage',

  // Template Management
  TEMPLATE_VIEW: 'template:view',
  TEMPLATE_DEPLOY: 'template:deploy',
  TEMPLATE_CREATE: 'template:create',
  TEMPLATE_EDIT: 'template:edit',
  TEMPLATE_DELETE: 'template:delete',
  TEMPLATE_PUBLISH: 'template:publish',

  // Deployment Operations
  DEPLOYMENT_CREATE: 'deployment:create',
  DEPLOYMENT_VIEW: 'deployment:view',
  DEPLOYMENT_MANAGE: 'deployment:manage',
  DEPLOYMENT_ROLLBACK: 'deployment:rollback',
  DEPLOYMENT_SCALE: 'deployment:scale',
  DEPLOYMENT_DELETE: 'deployment:delete',

  // Environment Operations
  ENVIRONMENT_DEPLOY: 'environment:deploy',
  ENVIRONMENT_VIEW: 'environment:view',
  ENVIRONMENT_CONFIGURE: 'environment:configure',

  // Project Operations
  PROJECT_PLUGIN_USE: 'project:plugin:use',
  PROJECT_TEMPLATE_DEPLOY: 'project:template:deploy',
  PROJECT_PROVIDER_ACCESS: 'project:provider:access',

  // Admin Operations
  PLUGIN_ADMIN: 'plugin:admin',
  PROVIDER_ADMIN: 'provider:admin',
  TEMPLATE_ADMIN: 'template:admin',
  PERMISSION_ADMIN: 'permission:admin'
} as const;

export type PluginPermissionType = typeof PLUGIN_PERMISSIONS[keyof typeof PLUGIN_PERMISSIONS];

// Permission scopes
export const PERMISSION_SCOPES = {
  GLOBAL: 'global',
  PROJECT: 'project',
  ENVIRONMENT: 'environment'
} as const;

export type PermissionScope = typeof PERMISSION_SCOPES[keyof typeof PERMISSION_SCOPES];

// Plugin features that can be permission-controlled
export const PLUGIN_FEATURES = {
  INSTALL: 'install',
  CONFIGURE: 'configure',
  DEPLOY: 'deploy',
  MONITOR: 'monitor',
  LOGS: 'logs',
  METRICS: 'metrics',
  TEMPLATES: 'templates',
  MARKETPLACE: 'marketplace',
  HOT_RELOAD: 'hot_reload',
  BACKUP: 'backup',
  RESTORE: 'restore'
} as const;

export type PluginFeature = typeof PLUGIN_FEATURES[keyof typeof PLUGIN_FEATURES];
