export interface LoginRequest {
    username: string;
    password: string;
}

export interface LoginResponse {
    token: string;
    refreshToken: string;
    expiresAt: string;
    userId: string;
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    isAdmin: boolean;
    groups: string[];
}

export interface RefreshTokenRequest {
    refreshToken: string;
}

export interface RefreshTokenResponse {
    token: string;
    refreshToken: string;
    expiresAt?: string;
    groups?: string[];
}

export interface ExternalLoginResponse {
    redirectUrl: string;
    state: string;
}

export interface ExternalLoginCallbackRequest {
    code: string;
    state: string;
}
