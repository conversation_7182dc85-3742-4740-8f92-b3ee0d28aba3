export interface Plugin {
  name: string;
  version: string;
  type: string;
  description: string;
  path: string;
  config: { [key: string]: any };
  enabled: boolean;
  lastReload: string;
  reloadCount: number;
  status: 'loading' | 'running' | 'error' | 'disabled';
  error?: string;
  hotReload: boolean;
  health?: string;
}

export interface PluginManifest {
  apiVersion: string;
  kind: string;
  metadata: {
    name: string;
    version: string;
    description: string;
    author: string;
    license: string;
  };
  spec: {
    type: string;
    runtime: string;
    entrypoint: string;
    provider?: {
      type: string;
      capabilities: string[];
    };
    configSchema: {
      type: string;
      properties: { [key: string]: any };
    };
    hotReload: {
      enabled: boolean;
      watchPaths: string[];
      excludePaths: string[];
      debounceInterval: string;
    };
    dependencies: Array<{
      name: string;
      version: string;
    }>;
    resources: {
      memory: string;
      cpu: string;
    };
    tags?: string[];
    categories?: string[];
  };
}

export interface PluginInstallRequest {
  name: string;
  source: string;
  config?: { [key: string]: any };
  enabled: boolean;
}

export interface PluginConfigUpdateRequest {
  config: { [key: string]: any };
}

export interface PluginStatus {
  name: string;
  version: string;
  status: string;
  lastReload: string;
  reloadCount: number;
  hotReload: boolean;
  error?: string;
  health: string;
  capabilities?: string[];
  metrics?: {
    executionCount: number;
    successCount: number;
    errorCount: number;
    averageExecutionTime: number;
  };
}

export interface PluginLog {
  timestamp: string;
  level: string;
  message: string;
  source?: string;
}

export interface PluginTemplate {
  id: string;
  name: string;
  description: string;
  provider: string;
  category: string;
  tags: string[];
  parameters: TemplateParameter[];
  steps: WorkflowStep[];
  manifests?: { [key: string]: string };
}

export interface TemplateParameter {
  name: string;
  type: string;
  description: string;
  required: boolean;
  default?: any;
  options?: string[];
}

export interface WorkflowStep {
  name: string;
  type: string;
  config: { [key: string]: any };
  dependsOn?: string[];
  condition?: string;
}

export interface ProviderInfo {
  type: string;
  capabilities: string[];
  templates: PluginTemplate[];
  status: 'available' | 'installed' | 'error';
  plugin?: Plugin;
}

export interface PluginMarketplace {
  plugins: MarketplacePlugin[];
  categories: string[];
  tags: string[];
  total: number;
}

export interface MarketplacePlugin {
  name: string;
  version: string;
  description: string;
  author: string;
  license: string;
  type: string;
  provider?: string;
  capabilities: string[];
  tags: string[];
  categories: string[];
  downloadUrl: string;
  documentationUrl?: string;
  sourceUrl?: string;
  rating: number;
  downloads: number;
  lastUpdated: string;
  screenshots?: string[];
  installed: boolean;
}

export interface PluginMetrics {
  name: string;
  executionCount: number;
  successCount: number;
  errorCount: number;
  averageExecutionTime: number;
  lastExecution: string;
  resourceUsage: {
    cpu: number;
    memory: number;
  };
}

export interface HotReloadEvent {
  pluginName: string;
  event: 'file-changed' | 'reload-started' | 'reload-completed' | 'reload-failed';
  timestamp: string;
  details?: string;
  error?: string;
}

export interface PluginValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  manifest?: PluginManifest;
}

export interface PluginDeploymentRequest {
  pluginName: string;
  templateId: string;
  parameters: { [key: string]: any };
  environmentId: string;
  projectId: string;
}

export interface PluginDeploymentResult {
  success: boolean;
  deploymentId: string;
  message: string;
  services?: DeployedService[];
  metrics?: { [key: string]: number };
}

export interface DeployedService {
  name: string;
  type: string;
  version: string;
  status: string;
  endpoints: ServiceEndpoint[];
}

export interface ServiceEndpoint {
  name: string;
  url: string;
  type: string;
  port: number;
  protocol: string;
  public: boolean;
}

export interface PluginConfiguration {
  [key: string]: any;
}

export interface PluginConfigField {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description: string;
  required: boolean;
  sensitive?: boolean;
  default?: any;
  options?: string[];
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
  };
}

export interface PluginHealth {
  status: 'healthy' | 'unhealthy' | 'unknown';
  message: string;
  lastCheck: string;
  checks: HealthCheck[];
}

export interface HealthCheck {
  name: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  message: string;
  timestamp: string;
}

export interface PluginCapability {
  name: string;
  description: string;
  required: boolean;
  supported: boolean;
}

export interface PluginDependency {
  name: string;
  version: string;
  type: 'required' | 'optional';
  installed: boolean;
  installedVersion?: string;
}

export interface PluginUpdate {
  currentVersion: string;
  latestVersion: string;
  updateAvailable: boolean;
  changelog?: string;
  breaking: boolean;
}

export interface PluginBackup {
  id: string;
  pluginName: string;
  version: string;
  timestamp: string;
  size: number;
  description?: string;
}

export interface PluginRestoreRequest {
  backupId: string;
  pluginName: string;
  overwrite: boolean;
}
