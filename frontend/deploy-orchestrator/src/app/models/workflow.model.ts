export interface Position {
  x: number;
  y: number;
}

export interface StepCondition {
  variable: string;
  operator: string; // eq, ne, gt, lt, gte, lte, contains, exists
  value: any;
  logic: string; // and, or
}

export interface RetryPolicy {
  maxAttempts: number;
  delaySeconds: number;
  backoffMultiplier: number;
}

export interface WorkflowStep {
  id: string;
  name: string;
  type: string; // script, http, deployment, condition, parallel, sequential
  description: string;
  config: { [key: string]: any };
  dependencies: string[]; // IDs of steps that must complete first
  conditions: StepCondition[]; // Conditions for step execution
  onSuccess: string[]; // Next steps if successful
  onFailure: string[]; // Next steps if failed
  timeout: number; // Timeout in minutes
  retryPolicy: RetryPolicy;
  position: Position; // For visual designer
}

export interface WorkflowParameter {
  name: string;
  type: string;
  description: string;
  required: boolean;
  defaultValue?: any;
  options?: string[]; // For enum/select types
}

export interface WorkflowTrigger {
  type: string; // manual, schedule, webhook, event
  config: { [key: string]: any };
  enabled: boolean;
}

export interface WorkflowDefinition {
  id: string;
  name: string;
  description: string;
  projectId: string;
  version: string;
  isActive: boolean;
  steps: WorkflowStep[];
  parameters: WorkflowParameter[];
  triggers: WorkflowTrigger[];
  variables: { [key: string]: any };
  tags: string[];
  createdAt?: Date;
  updatedAt?: Date;
  createdBy?: string;
  updatedBy?: string;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  isPublic: boolean;
  isFeatured: boolean;
  createdBy: string;
  authorName: string;
  authorEmail: string;
  version: string;
  steps: WorkflowStep[];
  parameters: WorkflowParameter[];
  variables: { [key: string]: any };
  tags: string[];
  usageCount: number;
  downloadCount: number;
  rating: number;
  ratingCount: number;
  documentation: string;
  requirements: string[];
  screenshots: string[];
  license: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface TemplateCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  sortOrder?: number;
  isActive?: boolean;
}

export interface TemplateRating {
  id: string;
  templateId: string;
  userId: string;
  rating: number;
  review: string;
  isHelpful: boolean;
  createdAt?: Date;
}

export interface TemplateReview {
  id: string;
  templateId: string;
  userId: string;
  userName: string;
  title: string;
  content: string;
  rating: number;
  pros: string[];
  cons: string[];
  helpfulCount: number;
  reportCount: number;
  isVerified: boolean;
  isModerated: boolean;
  createdAt?: Date;
}

export interface StepLog {
  timestamp: Date;
  level: string;
  message: string;
  data?: { [key: string]: any };
}

export interface WorkflowStepExecution {
  id: string;
  executionId: string;
  stepId: string;
  stepName: string;
  status: string; // pending, running, completed, failed, skipped
  startedAt?: Date;
  completedAt?: Date;
  duration: number; // Duration in seconds
  attemptCount: number;
  input: { [key: string]: any };
  output: { [key: string]: any };
  errorMessage: string;
  logs: StepLog[];
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  workflowName: string;
  projectId: string;
  status: string; // pending, running, completed, failed, cancelled
  startedAt?: Date;
  completedAt?: Date;
  startedBy: string;
  duration: number; // Duration in seconds
  parameters: { [key: string]: any };
  variables: { [key: string]: any };
  currentStep: string;
  errorMessage: string;
  triggerType: string;
  triggerData: { [key: string]: any };
  steps?: WorkflowStepExecution[];
}

// UI-specific interfaces for the designer
export interface WorkflowConnection {
  id: string;
  sourceStepId: string;
  targetStepId: string;
  type: 'success' | 'failure' | 'dependency';
  label?: string;
}

export interface WorkflowDesignerState {
  workflow: WorkflowDefinition;
  selectedStep?: WorkflowStep;
  selectedConnection?: WorkflowConnection;
  connections: WorkflowConnection[];
  canvasPosition: Position;
  canvasZoom: number;
  isDirty: boolean;
}

export interface StepType {
  type: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  configSchema: { [key: string]: any };
  defaultConfig: { [key: string]: any };
}

// Predefined step types for the palette
export const STEP_TYPES: StepType[] = [
  {
    type: 'script',
    name: 'Script',
    description: 'Execute a custom script',
    icon: 'code',
    category: 'Execution',
    configSchema: {
      language: { type: 'select', options: ['bash', 'python', 'javascript', 'powershell'] },
      script: { type: 'textarea', required: true },
      workingDirectory: { type: 'text' },
      environment: { type: 'object' }
    },
    defaultConfig: {
      language: 'bash',
      script: '#!/bin/bash\necho "Hello World"',
      workingDirectory: '',
      environment: {}
    }
  },
  {
    type: 'http',
    name: 'HTTP Request',
    description: 'Make an HTTP request',
    icon: 'globe',
    category: 'Integration',
    configSchema: {
      method: { type: 'select', options: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'] },
      url: { type: 'text', required: true },
      headers: { type: 'object' },
      body: { type: 'textarea' },
      timeout: { type: 'number' }
    },
    defaultConfig: {
      method: 'GET',
      url: '',
      headers: {},
      body: '',
      timeout: 30
    }
  },
  {
    type: 'deployment',
    name: 'Deployment',
    description: 'Deploy an application',
    icon: 'rocket',
    category: 'Deployment',
    configSchema: {
      deploymentId: { type: 'text', required: true },
      environment: { type: 'text', required: true },
      parameters: { type: 'object' }
    },
    defaultConfig: {
      deploymentId: '',
      environment: 'staging',
      parameters: {}
    }
  },
  {
    type: 'condition',
    name: 'Condition',
    description: 'Conditional branching',
    icon: 'branch',
    category: 'Control Flow',
    configSchema: {
      expression: { type: 'text', required: true },
      trueSteps: { type: 'array' },
      falseSteps: { type: 'array' }
    },
    defaultConfig: {
      expression: '',
      trueSteps: [],
      falseSteps: []
    }
  },
  {
    type: 'parallel',
    name: 'Parallel',
    description: 'Execute steps in parallel',
    icon: 'parallel',
    category: 'Control Flow',
    configSchema: {
      steps: { type: 'array', required: true },
      waitForAll: { type: 'boolean' }
    },
    defaultConfig: {
      steps: [],
      waitForAll: true
    }
  },
  {
    type: 'sequential',
    name: 'Sequential',
    description: 'Execute steps sequentially',
    icon: 'sequential',
    category: 'Control Flow',
    configSchema: {
      steps: { type: 'array', required: true }
    },
    defaultConfig: {
      steps: []
    }
  },
  {
    type: 'provider',
    name: 'Provider',
    description: 'Configure and use a deployment provider',
    icon: 'cloud',
    category: 'Infrastructure',
    configSchema: {
      providerType: { type: 'select', required: true, options: [] }, // Will be populated dynamically
      providerConfig: { type: 'object', required: true } // Dynamic form based on selected provider
    },
    defaultConfig: {
      providerType: '',
      providerConfig: {}
    }
  },
  {
    type: 'plugin',
    name: 'Plugin',
    description: 'Configure and use a deployment plugin',
    icon: 'puzzle-piece',
    category: 'Deployment',
    configSchema: {
      pluginId: { type: 'select', required: true, options: [] }, // Will be populated dynamically
      pluginConfig: { type: 'object', required: true } // Dynamic form based on selected plugin
    },
    defaultConfig: {
      pluginId: '',
      pluginConfig: {}
    }
  }
];
