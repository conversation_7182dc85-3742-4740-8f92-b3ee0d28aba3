export interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  isAdmin: boolean;
  isActive: boolean;
  lastLogin?: Date;
  groups?: Group[];
  roles?: Role[];
}

export interface Group {
  id: string;
  name: string;
  source: string;
  externalId?: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  projectId?: string | null;
  permissions?: Permission[];
}

export interface Permission {
  id: string;
  name: string;
  description: string;
}
