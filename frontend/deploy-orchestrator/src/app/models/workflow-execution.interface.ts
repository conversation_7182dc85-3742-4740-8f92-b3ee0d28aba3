export interface WorkflowExecutionRequest {
  projectId: string;
  parameters?: { [key: string]: any };
  secretMapping?: { [templateVariable: string]: string }; // templateVar -> userSecretName
  startedBy: string;
  triggerType?: string;
  triggerData?: { [key: string]: any };
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  projectId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startedBy: string;
  startedAt?: Date;
  completedAt?: Date;
  duration?: number; // Duration in seconds
  parameters?: { [key: string]: any };
  variables?: { [key: string]: any };
  secretMapping?: { [templateVariable: string]: string };
  currentStep?: string;
  errorMessage?: string;
  triggerType?: string;
  triggerData?: { [key: string]: any };
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  isPublic: boolean;
  isFeatured: boolean;
  createdBy: string;
  authorName: string;
  authorEmail: string;
  version: string;
  steps: WorkflowStep[];
  parameters: WorkflowParameter[];
  variables: { [key: string]: any };
  tags: string[];
  usageCount: number;
  downloadCount: number;
  rating: number;
  ratingCount: number;
  documentation: string;
  requirements: string[];
  screenshots: string[];
  license: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkflowParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'secret';
  description: string;
  required: boolean;
  defaultValue?: any;
  options?: string[]; // For enum-like parameters
  secretHint?: string; // Indicates this parameter expects a secret
}

export interface WorkflowStep {
  id: string;
  name: string;
  type: string; // script, http, deployment, condition, parallel, sequential
  description: string;
  config: { [key: string]: any };
  dependencies: string[]; // IDs of steps that must complete first
  conditions: StepCondition[]; // Basic conditions for step execution
  advancedConditions?: AdvancedCondition[]; // Enhanced conditional logic
  onSuccess: string[]; // Next steps if successful
  onFailure: string[]; // Next steps if failed
  timeout: number; // Timeout in minutes
  retryPolicy: RetryPolicy;
  position: Position; // For visual designer
}

export interface StepCondition {
  variable: string;
  operator: 'eq' | 'ne' | 'gt' | 'lt' | 'gte' | 'lte' | 'contains' | 'exists';
  value: any;
  logic: 'and' | 'or'; // for combining multiple conditions
}

export interface AdvancedCondition {
  id: string;
  type: 'expression' | 'script' | 'comparison' | 'logical';
  expression?: string; // For expression-based conditions
  script?: string; // For script-based conditions
  language?: string; // For script conditions
  left?: any; // Left operand for comparisons
  operator?: string; // ==, !=, >, <, >=, <=, contains, etc.
  right?: any; // Right operand for comparisons
  conditions?: AdvancedCondition[]; // For logical operators (AND, OR)
  logicalOp?: 'AND' | 'OR' | 'NOT';
  variables?: { [key: string]: any }; // Context variables
}

export interface RetryPolicy {
  maxAttempts: number;
  delaySeconds: number;
  backoffMultiplier: number;
}

export interface Position {
  x: number;
  y: number;
}

export interface SecretMappingConfig {
  templateVariable: string;
  description?: string;
  required: boolean;
  userSecrets: UserSecret[];
  selectedSecret?: string;
}

export interface UserSecret {
  id: string;
  name: string;
  description: string;
  type: string;
  environment?: string;
  service?: string;
  createdAt: Date;
}

export interface TemplateExecutionForm {
  templateId: string;
  projectId: string;
  parameters: { [key: string]: any };
  secretMappings: { [templateVariable: string]: string };
  environment?: string;
  service?: string;
}

export interface SecretMappingValidation {
  isValid: boolean;
  missingMappings: string[];
  availableSecrets: UserSecret[];
  recommendations: SecretMappingRecommendation[];
}

export interface SecretMappingRecommendation {
  templateVariable: string;
  recommendedSecret: string;
  confidence: number; // 0-1 confidence score
  reason: string;
}

// Service interfaces for API calls
export interface WorkflowExecutionService {
  executeTemplate(request: WorkflowExecutionRequest): Promise<WorkflowExecution>;
  getExecution(executionId: string): Promise<WorkflowExecution>;
  stopExecution(executionId: string): Promise<void>;
  getExecutionLogs(executionId: string): Promise<ExecutionLog[]>;
  validateSecretMapping(templateId: string, projectId: string): Promise<SecretMappingValidation>;
}

export interface ExecutionLog {
  id: string;
  executionId: string;
  stepId?: string;
  stepName?: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  timestamp: Date;
  source?: string;
  data?: { [key: string]: any };
}

export interface StepExecution {
  id: string;
  executionId: string;
  stepId: string;
  stepName: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  startedAt?: Date;
  completedAt?: Date;
  duration?: number; // Duration in seconds
  attemptCount: number;
  input?: { [key: string]: any };
  output?: { [key: string]: any };
  errorMessage?: string;
  logs: ExecutionLog[];
}

export interface ExecutionMetrics {
  id: string;
  executionId: string;
  totalSteps: number;
  completedSteps: number;
  failedSteps: number;
  skippedSteps: number;
  averageStepTime: number;
  totalExecutionTime: number;
  resourceUsage?: { [key: string]: any };
  performanceData?: { [key: string]: any };
}

export interface ExecutionEvent {
  id: string;
  executionId: string;
  stepId?: string;
  eventType: string; // step_started, step_completed, step_failed, execution_paused, etc.
  eventData?: { [key: string]: any };
  timestamp: Date;
  severity: 'info' | 'warning' | 'error' | 'critical';
  source: string; // workflow-engine, step-executor, instance-manager
  userId: string;
  projectId: string;
}
