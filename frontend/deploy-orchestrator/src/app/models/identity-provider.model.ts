export interface IdentityProvider {
  id: string;
  name: string;
  description: string;
  type: IdentityProviderType;
  enabled: boolean;
  isDefault: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export enum IdentityProviderType {
  OIDC = 'oidc',
  SAML = 'saml',
  LDAP = 'ldap'
}

export interface OIDCProvider extends IdentityProvider {
  issuerURL: string;
  clientID: string;
  clientSecret: string;
  redirectURL: string;
  scopes: string;
  groupsClaim: string;
  usernameClaim: string;
  emailClaim: string;
}

export interface SAMLProvider extends IdentityProvider {
  entityID: string;
  metadataURL: string;
  acsURL: string;
  spCertificate: string;
  spPrivateKey: string;
  groupsAttribute: string;
}

export interface LDAPProvider extends IdentityProvider {
  url: string;
  bindDN: string;
  bindPassword: string;
  baseDN: string;
  userFilter: string;
  groupFilter: string;
  groupsAttr: string;
  useSSL: boolean;
  startTLS: boolean;
  insecureSkip: boolean;
}
