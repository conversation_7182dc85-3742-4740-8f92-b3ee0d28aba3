export interface ProjectIntegration {
  id: string;
  projectId: string;
  type: IntegrationType;
  name: string;
  description: string;
  config: IntegrationConfig;
  status: IntegrationStatus;
  isActive: boolean;
  lastSync?: string;
  lastError?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;

  // UI state
  showConfig?: boolean;

  // Metadata
  metadata?: {
    version?: string;
    tags?: string[];
    [key: string]: any;
  };
}

export type IntegrationType =
  | 'git'
  | 'ci_cd'
  | 'monitoring'
  | 'notification'
  | 'secret_manager'
  | 'container_registry'
  | 'artifact_repository'
  | 'issue_tracker'
  | 'webhook';

export type IntegrationStatus =
  | 'active'
  | 'inactive'
  | 'error'
  | 'syncing'
  | 'pending'
  | 'testing';

export interface IntegrationConfig {
  // Git integrations
  repositoryUrl?: string;
  branch?: string;
  accessToken?: string;
  sshKey?: string;

  // CI/CD integrations
  pipelineId?: string;
  buildTrigger?: string;

  // Monitoring integrations
  endpoint?: string;
  apiKey?: string;
  dashboardUrl?: string;

  // Notification integrations
  webhookUrl?: string;
  channels?: string[];

  // Secret manager integrations
  vaultUrl?: string;
  namespace?: string;

  // Container registry integrations
  registryUrl?: string;
  username?: string;
  password?: string;

  // Generic config
  [key: string]: any;
}

export interface IntegrationTemplate {
  type: IntegrationType;
  name: string;
  description: string;
  icon: string;
  configSchema: IntegrationConfigField[];
  isPopular: boolean;
  documentation?: string;
}

export interface IntegrationConfigField {
  name: string;
  label: string;
  type: 'text' | 'password' | 'url' | 'select' | 'textarea' | 'boolean';
  required: boolean;
  description?: string;
  placeholder?: string;
  options?: Array<{ value: string; label: string }>;
  validation?: {
    pattern?: string;
    minLength?: number;
    maxLength?: number;
  };
}

export interface IntegrationTestResult {
  success: boolean;
  message: string;
  details?: any;
  testedAt: string;
}

export interface CreateIntegrationRequest {
  type: IntegrationType;
  name: string;
  description: string;
  config: IntegrationConfig;
  isActive?: boolean;
}

export interface UpdateIntegrationRequest {
  name?: string;
  description?: string;
  config?: Partial<IntegrationConfig>;
  isActive?: boolean;
}
