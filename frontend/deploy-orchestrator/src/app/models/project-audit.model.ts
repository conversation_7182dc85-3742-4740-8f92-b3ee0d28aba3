export interface ProjectAuditLog {
  id: string;
  projectId: string;
  userId: string;
  action: ProjectAuditAction;
  resource: ProjectAuditResource;
  resourceId?: string;
  details: ProjectAuditDetails;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  errorMessage?: string;
  timestamp: string;
  
  // Populated fields
  user?: {
    id: string;
    username: string;
    email: string;
    fullName: string;
  };
}

export type ProjectAuditAction = 
  | 'CREATE'
  | 'UPDATE'
  | 'DELETE'
  | 'VIEW'
  | 'DEPLOY'
  | 'ROLLBACK'
  | 'SCALE'
  | 'RESTART'
  | 'PERMISSION_GRANT'
  | 'PERMISSION_REVOKE'
  | 'INTEGRATION_ADD'
  | 'INTEGRATION_REMOVE'
  | 'INTEGRATION_SYNC'
  | 'SECRET_CREATE'
  | 'SECRET_UPDATE'
  | 'SECRET_DELETE'
  | 'SECRET_ACCESS'
  | 'ENVIRONMENT_CREATE'
  | 'ENVIRONMENT_UPDATE'
  | 'ENVIRONMENT_DELETE'
  | 'WORKFLOW_EXECUTE'
  | 'WORKFLOW_CANCEL'
  | 'SETTINGS_UPDATE';

export type ProjectAuditResource = 
  | 'PROJECT'
  | 'ENVIRONMENT'
  | 'DEPLOYMENT'
  | 'WORKFLOW'
  | 'SECRET'
  | 'INTEGRATION'
  | 'PERMISSION'
  | 'ROLE'
  | 'GROUP'
  | 'USER'
  | 'SETTINGS';

export interface ProjectAuditDetails {
  // Resource-specific details
  resourceName?: string;
  previousValues?: Record<string, any>;
  newValues?: Record<string, any>;
  
  // Action-specific details
  deploymentId?: string;
  workflowId?: string;
  environmentId?: string;
  integrationId?: string;
  secretId?: string;
  
  // Permission-specific details
  grantedPermissions?: string[];
  revokedPermissions?: string[];
  targetUserId?: string;
  targetGroupId?: string;
  
  // Additional context
  reason?: string;
  metadata?: Record<string, any>;
}

export interface ProjectAuditFilter {
  startDate?: string;
  endDate?: string;
  userId?: string;
  action?: ProjectAuditAction;
  resource?: ProjectAuditResource;
  success?: boolean;
  searchTerm?: string;
  page?: number;
  limit?: number;
}

export interface ProjectAuditResponse {
  logs: ProjectAuditLog[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ProjectAuditSummary {
  totalActions: number;
  successfulActions: number;
  failedActions: number;
  uniqueUsers: number;
  mostActiveUser: {
    userId: string;
    username: string;
    actionCount: number;
  };
  actionBreakdown: Array<{
    action: ProjectAuditAction;
    count: number;
  }>;
  resourceBreakdown: Array<{
    resource: ProjectAuditResource;
    count: number;
  }>;
  timeRange: {
    start: string;
    end: string;
  };
}
