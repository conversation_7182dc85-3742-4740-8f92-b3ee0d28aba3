export interface ProjectPermission {
  id: string;
  projectId: string;
  userId?: string;
  groupId?: string;
  roleId: string;
  permissions: string[];
  grantedBy: string;
  grantedAt: string;
  expiresAt?: string;
  isActive: boolean;
  
  // Populated fields
  user?: {
    id: string;
    username: string;
    email: string;
    fullName: string;
  };
  group?: {
    id: string;
    name: string;
    description: string;
    memberCount: number;
  };
  role?: {
    id: string;
    name: string;
    description: string;
    permissions: string[];
  };
}

export interface ProjectRole {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  isBuiltIn: boolean;
  projectId?: string; // null for global roles
  createdAt: string;
  updatedAt: string;
}

export interface ProjectGroup {
  id: string;
  name: string;
  description: string;
  projectIds: string[];
  userIds: string[];
  roleIds: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  
  // Populated fields
  users?: Array<{
    id: string;
    username: string;
    email: string;
    fullName: string;
  }>;
  projects?: Array<{
    id: string;
    name: string;
  }>;
  roles?: ProjectRole[];
}

export interface PermissionAssignmentRequest {
  userId?: string;
  groupId?: string;
  roleId: string;
  expiresAt?: string;
}

export interface PermissionUpdateRequest {
  roleId?: string;
  permissions?: string[];
  expiresAt?: string;
  isActive?: boolean;
}
