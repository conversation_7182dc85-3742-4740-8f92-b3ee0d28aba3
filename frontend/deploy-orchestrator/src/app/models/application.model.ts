export interface ApplicationGroup {
  id: string;
  name: string;
  description?: string;
  projectId: string;
  applications: Application[];
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface Application {
  id: string;
  name: string;
  description?: string;
  type: ApplicationType;
  groupId: string;
  projectId: string;
  components: Component[];
  environments: ApplicationEnvironment[];
  overallStatus: HealthStatus;
  repository?: RepositoryInfo;
  tags?: string[];
  configuration: ApplicationConfiguration;
  createdAt: string;
  updatedAt: string;
  version?: string;
  lastDeployment?: DeploymentHistory;
  owner?: string;
  group?: ApplicationGroup;
}

export interface Component {
  id: string;
  name: string;
  description?: string;
  type: ComponentType;
  applicationId: string;
  projectId: string;
  version: string;
  repository?: RepositoryInfo;
  dependencies: ComponentDependency[];
  configuration: ComponentConfiguration;
  deploymentConfig: DeploymentConfiguration;
  deploymentStrategy: DeploymentStrategy;
  healthChecks: HealthCheck[];
  status: ComponentStatus;
  healthStatus: HealthStatus;
  resources: ResourceRequirements;
  environments?: ComponentEnvironmentInfo[];
  deploymentHistory?: DeploymentHistory[];
}

export interface ApplicationEnvironment {
  name: string;
  status: HealthStatus;
  currentVersion: string;
  deploymentStatus: DeploymentStatus;
  lastDeployedAt?: string;
  components: ComponentEnvironmentStatus[];
  url?: string;
  healthScore: number;
}

export interface ComponentEnvironmentInfo {
  name: string;
  deploymentStatus: DeploymentStatus;
  version: string;
  instances: number;
  healthyInstances: number;
  url?: string;
  lastDeployedAt?: string;
  healthStatus?: HealthStatus;
  metrics?: ComponentMetrics;
  lastDeployment?: DeploymentHistory;
}

export interface ComponentEnvironmentStatus {
  componentId: string;
  componentName: string;
  status: HealthStatus;
  version: string;
  instances: number;
  healthyInstances: number;
  url?: string;
  metrics: ComponentMetrics;
}

export interface DeploymentPipeline {
  id: string;
  name: string;
  applicationId: string;
  stages: PipelineStage[];
  triggers: PipelineTrigger[];
  isActive: boolean;
}

export interface PipelineStage {
  id: string;
  name: string;
  type: StageType;
  environment: string;
  approvers?: string[];
  automatedTests?: TestConfiguration[];
  deploymentStrategy: DeploymentStrategy;
  rollbackStrategy: RollbackStrategy;
  order: number;
}

export interface ApplicationDeployment {
  id: string;
  applicationId: string;
  environment: string;
  version: string;
  status: DeploymentStatus;
  startedAt: string;
  completedAt?: string;
  deployedBy: string;
  componentDeployments: ComponentDeployment[];
  metrics: DeploymentMetrics;
  logs: DeploymentLog[];
  rollbackInfo?: RollbackInfo;
}

export interface ComponentDeployment {
  componentId: string;
  componentName: string;
  status: DeploymentStatus;
  version: string;
  startedAt: string;
  completedAt?: string;
  instances: DeploymentInstance[];
  configChanges: ConfigurationChange[];
}

export interface DeploymentInstance {
  id: string;
  status: InstanceStatus;
  health: HealthStatus;
  startedAt: string;
  resources: ResourceUsage;
  logs: string[];
}

// Enums
export enum ApplicationType {
  WEB_APPLICATION = 'web_application',
  MICROSERVICE = 'microservice',
  DATABASE = 'database',
  MESSAGE_QUEUE = 'message_queue',
  CACHE = 'cache',
  API_GATEWAY = 'api_gateway',
  LOAD_BALANCER = 'load_balancer',
  MONITORING = 'monitoring',
  LOGGING = 'logging',
  OTHER = 'other'
}

export enum ComponentType {
  FRONTEND = 'frontend',
  BACKEND = 'backend',
  DATABASE = 'database',
  CACHE = 'cache',
  MESSAGE_QUEUE = 'message_queue',
  API = 'api',
  WORKER = 'worker',
  SCHEDULER = 'scheduler',
  LOAD_BALANCER = 'load_balancer',
  REVERSE_PROXY = 'reverse_proxy',
  OTHER = 'other'
}

export enum HealthStatus {
  HEALTHY = 'healthy',
  WARNING = 'warning',
  ERROR = 'error',
  UNKNOWN = 'unknown'
}

export enum DeploymentStatus {
  PENDING = 'pending',
  DEPLOYING = 'deploying',
  DEPLOYED = 'deployed',
  FAILED = 'failed',
  ROLLING_BACK = 'rolling_back',
  ROLLED_BACK = 'rolled_back',
  CANCELLED = 'cancelled'
}

export enum InstanceStatus {
  STARTING = 'starting',
  RUNNING = 'running',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
  FAILED = 'failed',
  RESTARTING = 'restarting'
}

export enum StageType {
  DEPLOY = 'deploy',
  TEST = 'test',
  APPROVAL = 'approval',
  NOTIFICATION = 'notification',
  CUSTOM = 'custom'
}

export enum DeploymentStrategy {
  ROLLING = 'rolling_update',
  ROLLING_UPDATE = 'rolling_update',
  BLUE_GREEN = 'blue_green',
  CANARY = 'canary',
  RECREATE = 'recreate'
}

// Supporting interfaces
export interface ComponentDependency {
  componentId: string;
  type: 'runtime' | 'build' | 'data';
  required: boolean;
  name?: string;
  healthStatus?: HealthStatus;
}

export interface ComponentConfiguration {
  image?: string;
  ports: Port[];
  environmentVariables: EnvironmentVariable[];
  volumes: Volume[];
  secrets: SecretReference[];
  configMaps: ConfigMapReference[];
  resources: ResourceRequirements & {
    replicas: number;
  };
  networking: NetworkingConfiguration & {
    ports: string[];
  };
  healthChecks: {
    enabled: boolean;
    readinessProbe: HealthProbe;
    livenessProbe: HealthProbe;
  };
}

export interface DeploymentConfiguration {
  replicas: number;
  strategy: DeploymentStrategy;
  resources: ResourceRequirements;
  autoscaling?: AutoscalingConfiguration;
  networking?: NetworkingConfiguration;
}

export interface HealthCheck {
  type: 'http' | 'tcp' | 'exec';
  path?: string;
  port?: number;
  command?: string[];
  initialDelaySeconds: number;
  periodSeconds: number;
  timeoutSeconds: number;
  failureThreshold: number;
  enabled?: boolean;
  readinessProbe?: HealthProbe;
  livenessProbe?: HealthProbe;
}

export interface HealthProbe {
  path?: string;
  port?: number;
  initialDelaySeconds?: number;
  periodSeconds?: number;
  timeoutSeconds?: number;
  failureThreshold?: number;
}

export interface ComponentStatus {
  health: HealthStatus;
  instances: number;
  healthyInstances: number;
  lastChecked: string;
  issues: HealthIssue[];
}

export interface ResourceRequirements {
  cpu: ResourceSpec;
  memory: ResourceSpec;
  storage?: ResourceSpec;
}

export interface ResourceSpec {
  requests: string;
  limits: string;
}

export interface ApplicationConfiguration {
  defaultReplicas: number;
  defaultResources: ResourceRequirements;
  globalEnvironmentVariables: EnvironmentVariable[];
  globalSecrets: SecretReference[];
  networkPolicies: NetworkPolicy[];
}

export interface RepositoryInfo {
  url: string;
  branch: string;
  lastCommit?: string;
  provider: 'github' | 'gitlab' | 'bitbucket' | 'azure_devops';
  buildPath?: string;
}

export interface ComponentMetrics {
  cpu: MetricValue;
  memory: MetricValue;
  network: NetworkMetrics;
  responseTime?: MetricValue;
  errorRate?: MetricValue;
  requestRate?: MetricValue;
}

export interface MetricValue {
  current: number;
  average: number;
  max: number;
  unit: string;
  timestamp: string;
}

export interface NetworkMetrics {
  bytesIn: MetricValue;
  bytesOut: MetricValue;
  connectionsActive: MetricValue;
}

export interface PipelineTrigger {
  type: 'manual' | 'git_push' | 'schedule' | 'webhook';
  configuration: Record<string, any>;
}

export interface TestConfiguration {
  type: 'unit' | 'integration' | 'e2e' | 'performance' | 'security';
  command: string;
  timeout: number;
  retries: number;
}

export interface RollbackStrategy {
  enabled: boolean;
  autoRollback: boolean;
  conditions: RollbackCondition[];
}

export interface RollbackCondition {
  metric: string;
  threshold: number;
  duration: number;
}

export interface DeploymentMetrics {
  duration: number;
  successRate: number;
  resourceUsage: ResourceUsage;
  healthScore: number;
  errors: DeploymentError[];
}

export interface ResourceUsage {
  cpu: number;
  memory: number;
  storage: number;
  network: number;
}

export interface DeploymentLog {
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  component: string;
  message: string;
  metadata?: Record<string, any>;
}

export interface RollbackInfo {
  previousVersion: string;
  reason: string;
  rolledBackAt: string;
  rolledBackBy: string;
}

export interface ConfigurationChange {
  key: string;
  oldValue: any;
  newValue: any;
  type: 'added' | 'modified' | 'removed';
}

export interface HealthIssue {
  type: 'warning' | 'error';
  message: string;
  component?: string;
  timestamp: string;
  resolved: boolean;
}

export interface Port {
  name: string;
  port: number;
  protocol: 'TCP' | 'UDP';
  expose?: boolean;
}

export interface EnvironmentVariable {
  name: string;
  value?: string;
  valueFrom?: VariableSource;
}

export interface VariableSource {
  secretKeyRef?: SecretKeySelector;
  configMapKeyRef?: ConfigMapKeySelector;
}

export interface SecretKeySelector {
  name: string;
  key: string;
}

export interface ConfigMapKeySelector {
  name: string;
  key: string;
}

export interface Volume {
  name: string;
  type: 'emptyDir' | 'persistentVolume' | 'configMap' | 'secret';
  mountPath: string;
  source?: VolumeSource;
}

export interface VolumeSource {
  persistentVolumeClaim?: string;
  configMap?: string;
  secret?: string;
}

export interface SecretReference {
  name: string;
  keys?: string[];
}

export interface ConfigMapReference {
  name: string;
  keys?: string[];
}

export interface AutoscalingConfiguration {
  enabled: boolean;
  minReplicas: number;
  maxReplicas: number;
  targetCPUUtilization?: number;
  targetMemoryUtilization?: number;
  customMetrics?: CustomMetric[];
}

export interface CustomMetric {
  name: string;
  target: number;
  type: 'value' | 'utilization';
}

export interface NetworkingConfiguration {
  ingress?: IngressConfiguration;
  service?: ServiceConfiguration;
  networkPolicies?: NetworkPolicy[];
}

export interface IngressConfiguration {
  enabled: boolean;
  hosts: string[];
  paths: IngressPath[];
  tls?: TLSConfiguration;
  annotations?: Record<string, string>;
  hostname?: string;
}

export interface IngressPath {
  path: string;
  pathType: 'Exact' | 'Prefix';
  backend: IngressBackend;
}

export interface IngressBackend {
  serviceName: string;
  servicePort: number;
}

export interface TLSConfiguration {
  enabled: boolean;
  secretName?: string;
  hosts: string[];
}

export interface ServiceConfiguration {
  type: 'ClusterIP' | 'NodePort' | 'LoadBalancer';
  ports: ServicePort[];
  annotations?: Record<string, string>;
}

export interface ServicePort {
  name: string;
  port: number;
  targetPort: number;
  protocol: 'TCP' | 'UDP';
}

export interface NetworkPolicy {
  name: string;
  podSelector: LabelSelector;
  ingress?: NetworkPolicyIngress[];
  egress?: NetworkPolicyEgress[];
}

export interface LabelSelector {
  matchLabels?: Record<string, string>;
  matchExpressions?: LabelSelectorRequirement[];
}

export interface LabelSelectorRequirement {
  key: string;
  operator: 'In' | 'NotIn' | 'Exists' | 'DoesNotExist';
  values?: string[];
}

export interface NetworkPolicyIngress {
  from?: NetworkPolicyPeer[];
  ports?: NetworkPolicyPort[];
}

export interface NetworkPolicyEgress {
  to?: NetworkPolicyPeer[];
  ports?: NetworkPolicyPort[];
}

export interface NetworkPolicyPeer {
  podSelector?: LabelSelector;
  namespaceSelector?: LabelSelector;
}

export interface NetworkPolicyPort {
  protocol?: 'TCP' | 'UDP';
  port?: number;
}

export interface DeploymentError {
  component: string;
  error: string;
  timestamp: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

// Additional interfaces needed by components
export interface DeploymentHistory {
  id: string;
  applicationId: string;
  environment: string;
  version: string;
  status: DeploymentStatus;
  deployedAt: string;
  timestamp: string;
  deployedBy: string;
  duration: number;
  rollbackInfo?: RollbackInfo;
  componentName?: string;
}

export interface MetricData {
  timestamp: string;
  value: number;
  metric: string;
  component?: string;
  environment?: string;
}

export interface Environment {
  id: string;
  name: string;
  type: 'development' | 'staging' | 'production' | 'testing';
  description?: string;
  namespace?: string;
  cluster: string;
  configuration: EnvironmentConfiguration;
  status: HealthStatus;
  applications: ApplicationEnvironmentStatus[];
}

export interface EnvironmentConfiguration {
  defaultResources: ResourceRequirements;
  networkPolicies: NetworkPolicy[];
  secrets: SecretReference[];
  configMaps: ConfigMapReference[];
}

export interface ApplicationEnvironmentStatus {
  applicationId: string;
  applicationName: string;
  version: string;
  status: HealthStatus;
  deploymentStatus: DeploymentStatus;
  components: ComponentEnvironmentStatus[];
}

export interface ApplicationMetrics {
  applicationId: string;
  environment: string;
  timeSeriesData: TimeSeriesMetric[];
  aggregateMetrics: AggregateMetrics;
  healthScore: number;
  lastUpdated: string;
  totalRequests: number;
  averageResponseTime: number;
  errorRate: number;
}

export interface TimeSeriesMetric {
  timestamp: string;
  responseTime: number;
  cpuUsage: number;
  memoryUsage: number;
  requestRate: number;
  errorRate: number;
  activeUsers?: number;
  throughput?: number;
}

export interface AggregateMetrics {
  averageResponseTime: number;
  totalRequests: number;
  errorCount: number;
  uptime: number;
  availability: number;
  errorRate?: number;
}

// Request/Response types for API calls
export interface CreateApplicationGroupRequest {
  name: string;
  description?: string;
  projectId: string;
  tags?: string[];
}

export interface CreateApplicationRequest {
  name: string;
  description?: string;
  type: ApplicationType;
  projectId: string;
  groupId: string;
  repository?: RepositoryInfo;
  tags?: string[];
  configuration: ApplicationConfiguration;
}

export interface CreateComponentRequest {
  name: string;
  description?: string;
  type: ComponentType;
  version: string;
  repository?: RepositoryInfo;
  configuration: ComponentConfiguration;
  deploymentConfig: DeploymentConfiguration;
  healthChecks: HealthCheck[];
}

export interface UpdateComponentRequest extends Partial<CreateComponentRequest> {
  id: string;
}
