import { Injectable } from '@angular/core';
import {
  <PERSON>ttp<PERSON>equest,
  <PERSON>ttpHandler,
  HttpEvent,
  HttpInterceptor
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { AuthService } from '../services/auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(private authService: AuthService) {}

  intercept(request: HttpRequest<unknown>, next: <PERSON>ttpHandler): Observable<HttpEvent<unknown>> {
    // Add auth header with jwt if user is logged in and request is to any API endpoint
    const accessToken = this.authService.getAccessToken();
    const isLoggedIn = this.authService.isAuthenticated();

    // Check if this is an API request (including secrets-service)
    const isApiUrl = request.url.startsWith('/api/') ||
                     request.url.startsWith(environment.apiUrl) ||
                     request.url.includes('/api/');

    // Skip auth for login/refresh endpoints
    const isAuthEndpoint = request.url.includes('/auth/login') ||
                          request.url.includes('/auth/refresh');

    console.log('AuthInterceptor: Processing request', {
      url: request.url,
      isLoggedIn,
      isApiUrl,
      isAuthEndpoint,
      hasToken: !!accessToken
    });

    if (isLoggedIn && isApiUrl && !isAuthEndpoint && accessToken) {
      console.log('AuthInterceptor: Adding token to request for', request.url);
      request = request.clone({
        setHeaders: {
          Authorization: `Bearer ${accessToken}`
        }
      });
    } else if (isApiUrl && !isAuthEndpoint) {
      console.warn('AuthInterceptor: API request without token', {
        url: request.url,
        isLoggedIn,
        hasToken: !!accessToken
      });
    }

    return next.handle(request);
  }
}

import { environment } from '../../environments/environment';
