import { Injectable } from '@angular/core';
import {
  <PERSON>ttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  intercept(request: HttpRequest<unknown>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<unknown>> {
    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        // Enhanced logging for debugging
        console.log('ErrorInterceptor: HTTP Error detected:', {
          status: error.status,
          statusText: error.statusText,
          url: request.url,
          method: request.method,
          errorBody: error.error,
          headers: error.headers?.keys()
        });

        if (error.status === 401 && !request.url.includes('/auth/login') && !request.url.includes('/auth/refresh')) {
          // Check if this is a token expiration or other auth error
          const errorBody = error.error;
          const errorCode = errorBody?.code;
          const errorMessage = errorBody?.error || errorBody?.message || error.message;

          console.log('ErrorInterceptor: 401 Authentication error detected:', {
            status: error.status,
            code: errorCode,
            message: errorMessage,
            url: request.url,
            hasToken: !!this.authService.getAccessToken(),
            isAuthenticated: this.authService.isAuthenticated()
          });

          // Check if user is still considered authenticated
          if (!this.authService.isAuthenticated()) {
            console.log('ErrorInterceptor: User not authenticated, redirecting to login');
            this.authService.logout();
            this.router.navigate(['/login']);
            return throwError(() => error);
          }

          // If token is expired, invalid, or auth required, try to refresh
          const shouldTryRefresh = errorCode === 'TOKEN_EXPIRED' ||
                                 errorCode === 'TOKEN_INVALID' ||
                                 errorCode === 'AUTH_REQUIRED' ||
                                 errorMessage?.toLowerCase().includes('expired') ||
                                 errorMessage?.toLowerCase().includes('invalid token') ||
                                 !errorCode; // If no specific error code, try refresh

          if (shouldTryRefresh) {
            console.log('ErrorInterceptor: Attempting token refresh for error:', errorCode || 'no-code');
            return this.authService.refreshToken().pipe(
              switchMap(() => {
                // Update the request with the new token
                const accessToken = this.authService.getAccessToken();
                if (accessToken) {
                  console.log('ErrorInterceptor: Token refreshed, retrying request');
                  request = request.clone({
                    setHeaders: {
                      Authorization: `Bearer ${accessToken}`
                    }
                  });
                  return next.handle(request);
                } else {
                  console.warn('ErrorInterceptor: No access token after refresh, logging out');
                  this.authService.logout();
                  this.router.navigate(['/login']);
                  return throwError(() => new Error('No access token after refresh'));
                }
              }),
              catchError(refreshError => {
                // If refresh fails, logout and redirect to login
                console.warn('ErrorInterceptor: Token refresh failed, redirecting to login:', refreshError);
                this.authService.logout();
                this.router.navigate(['/login']);
                return throwError(() => refreshError);
              })
            );
          } else {
            // For other auth errors (malformed token, invalid claims, etc.), logout immediately
            console.warn('ErrorInterceptor: Authentication error, logging out immediately:', errorCode);
            this.authService.logout();
            this.router.navigate(['/login']);
            return throwError(() => error);
          }
        }

        // Handle 403 errors that might indicate token expiration
        if (error.status === 403 && !request.url.includes('/auth/login')) {
          const errorBody = error.error;
          const errorMessage = errorBody?.error || errorBody?.message || error.message;

          console.log('ErrorInterceptor: 403 Forbidden error detected:', {
            status: error.status,
            message: errorMessage,
            url: request.url,
            hasToken: !!this.authService.getAccessToken(),
            isAuthenticated: this.authService.isAuthenticated()
          });

          // Check if this might be a token expiration issue
          if (errorMessage?.toLowerCase().includes('expired') ||
              errorMessage?.toLowerCase().includes('invalid token') ||
              errorMessage?.toLowerCase().includes('token') ||
              !this.authService.isAuthenticated()) {
            console.log('ErrorInterceptor: 403 error appears to be token-related, attempting refresh');
            return this.authService.refreshToken().pipe(
              switchMap(() => {
                const accessToken = this.authService.getAccessToken();
                if (accessToken) {
                  console.log('ErrorInterceptor: Token refreshed for 403 error, retrying request');
                  request = request.clone({
                    setHeaders: {
                      Authorization: `Bearer ${accessToken}`
                    }
                  });
                  return next.handle(request);
                } else {
                  console.warn('ErrorInterceptor: No access token after refresh for 403 error');
                  this.authService.logout();
                  this.router.navigate(['/login']);
                  return throwError(() => new Error('No access token after refresh'));
                }
              }),
              catchError(refreshError => {
                console.warn('ErrorInterceptor: Token refresh failed for 403 error, redirecting to login');
                this.authService.logout();
                this.router.navigate(['/login']);
                return throwError(() => refreshError);
              })
            );
          }
        }

        // For authentication errors, sanitize the error message
        if (error.status === 401 && request.url.includes('/auth/login')) {
          // Replace detailed error with generic message
          const sanitizedError = new HttpErrorResponse({
            error: { error: 'Invalid username or password' },
            status: 401,
            statusText: 'Unauthorized',
            url: error.url || undefined
          });
          return throwError(() => sanitizedError);
        }

        // For other errors, just pass through
        return throwError(() => error);
      })
    );
  }
}
