import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LoginComponent } from './components/login/login.component';
import { AuthCallbackComponent } from './components/auth-callback/auth-callback.component';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { IdpConfigComponent } from './components/idp-config/idp-config.component';
import { GroupMappingComponent } from './components/group-mapping/group-mapping.component';
import { ProjectsComponent } from './components/projects/projects.component';
import { ProjectFormComponent } from './components/projects/project-form/project-form.component';
import { ProjectSettingsComponent } from './components/projects/project-settings/project-settings.component';
import { RolesComponent } from './components/roles/roles.component';

import { SchedulesComponent } from './components/schedules/schedules.component';
import { UsersComponent } from './components/users/users.component';
import { AuditLogsComponent } from './components/audit-logs/audit-logs.component';
import { SettingsComponent } from './components/settings/settings.component';
import { WorkflowsComponent } from './components/workflows/workflows.component';
import { WorkflowDesignerComponent } from './components/workflow-designer/workflow-designer.component';
import { TemplateMarketplaceComponent } from './components/template-marketplace/template-marketplace.component';
import { TemplateDetailComponent } from './components/template-detail/template-detail.component';
import { TemplateAnalyticsComponent } from './components/template-analytics/template-analytics.component';
import { WorkflowInstancesComponent } from './components/workflow-instances/workflow-instances.component';
import { ExecutionMonitoringComponent } from './components/execution-monitoring/execution-monitoring.component';
import { EnvironmentProviderAdminComponent } from './components/environment-provider-config/environment-provider-config.component';
import { SecretProviderAdminComponent } from './components/secret-provider-config/secret-provider-config.component';
import { EnvironmentsComponent } from './components/environments/environments.component';
import { EnvironmentConfigComponent } from './components/environment-config/environment-config.component';

import { AuthGuard } from './guards/auth.guard';
import { AdminGuard } from './guards/admin.guard';
import { WorkflowPermissionGuard } from './guards/workflow-permission.guard';
import { EnvironmentPermissionGuard } from './guards/environment-permission.guard';
import { GroupProjectAssignmentsComponent } from './components/group-project-assignments/group-project-assignments.component';
import { PermissionManagementComponent } from './components/permission-management/permission-management.component';
import { MainLayoutComponent } from './components/layout/main-layout/main-layout.component';
import { PluginManagementComponent } from './components/plugin-management/plugin-management.component';
import { EnvironmentProviderConfigComponent } from './components/provider-config/provider-config.component';
import { PluginManagementGuard, EnvironmentProviderAccessGuard } from './guards/plugin-permission.guard';
import { DeployableManagementComponent } from './components/deployable-management/deployable-management.component';
import { DeploymentPageComponent } from './components/deployment-page/deployment-page.component';
import { DeploymentsComponent } from './components/deployments/deployments.component';
import { RealTimeLogsComponent } from './components/real-time-logs/real-time-logs.component';

const routes: Routes = [
  { path: 'login', component: LoginComponent },
  { path: 'auth/callback', component: AuthCallbackComponent },
  {
    path: '',
    component: MainLayoutComponent,
    canActivate: [AuthGuard],
    children: [
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      { path: 'dashboard', component: DashboardComponent },
      { path: 'projects', component: ProjectsComponent },
      { path: 'projects/new', component: ProjectFormComponent, canActivate: [AdminGuard] },
      { path: 'projects/:id/edit', component: ProjectFormComponent, canActivate: [AdminGuard] },
      { path: 'projects/:id/settings', component: ProjectSettingsComponent },
      // Unified Deployable Management
      {
        path: 'deployables',
        component: DeployableManagementComponent,
        canActivate: [EnvironmentPermissionGuard],
        data: { permission: 'deployable:view' }
      },
      {
        path: 'deployables/:deployableId',
        component: DeployableManagementComponent,
        canActivate: [EnvironmentPermissionGuard],
        data: { permission: 'deployable:view' }
      },
      // Unified Deployment Management
      {
        path: 'deployments',
        component: DeploymentsComponent,
        canActivate: [EnvironmentPermissionGuard],
        data: { permission: 'deployment:view' }
      },
      // Application Management
      {
        path: 'applications',
        loadComponent: () => import('./components/application-management/application-management.component').then(m => m.ApplicationManagementComponent),
        canActivate: [EnvironmentPermissionGuard],
        data: { permission: 'deployment:view' }
      },
      {
        path: 'applications/:applicationId',
        redirectTo: 'deployables/:applicationId',
        pathMatch: 'full'
      },
      {
        path: 'component-management',
        redirectTo: 'deployables',
        pathMatch: 'full'
      },
      {
        path: 'component-management/:applicationId',
        redirectTo: 'deployables',
        pathMatch: 'full'
      },
      {
        path: 'environments',
        component: EnvironmentsComponent,
        canActivate: [EnvironmentPermissionGuard],
        data: { permission: 'environment:view' }
      },
      {
        path: 'environment-config',
        component: EnvironmentConfigComponent,
        canActivate: [EnvironmentPermissionGuard],
        data: { permission: 'environment:create' }
      },

      { path: 'schedules', component: SchedulesComponent },
      {
        path: 'workflows',
        component: WorkflowsComponent,
        canActivate: [WorkflowPermissionGuard],
        data: { permission: 'workflow:view' }
      },
      {
        path: 'workflows/designer',
        component: WorkflowDesignerComponent,
        canActivate: [WorkflowPermissionGuard],
        data: { permission: 'workflow:create' }
      },
      {
        path: 'workflows/designer/:id',
        component: WorkflowDesignerComponent,
        canActivate: [WorkflowPermissionGuard],
        data: { permission: 'workflow:update' }
      },
      {
        path: 'templates',
        component: TemplateMarketplaceComponent,
        canActivate: [WorkflowPermissionGuard],
        data: { permission: 'workflow:view' }
      },
      {
        path: 'templates/:id',
        component: TemplateDetailComponent,
        canActivate: [WorkflowPermissionGuard],
        data: { permission: 'workflow:view' }
      },
      {
        path: 'analytics/templates',
        component: TemplateAnalyticsComponent,
        canActivate: [WorkflowPermissionGuard],
        data: { permission: 'template:analytics' }
      },
      {
        path: 'execution-monitoring',
        component: ExecutionMonitoringComponent,
        canActivate: [WorkflowPermissionGuard],
        data: { permission: 'workflow:monitor' }
      },
      {
        path: 'execution-monitoring/:executionId',
        component: ExecutionMonitoringComponent, // RealTimeLogsComponent
        canActivate: [WorkflowPermissionGuard],
        data: { permission: 'workflow:monitor' }
      },
      {
        path: 'real-time-logs',
        component: RealTimeLogsComponent,
        canActivate: [WorkflowPermissionGuard],
        data: { permission: 'workflow:monitor' }
      },
      {
        path: 'plugins',
        component: PluginManagementComponent,
        canActivate: [PluginManagementGuard],
        data: {
          title: 'Plugin Management',
          breadcrumb: 'Plugins'
        }
      },
      {
        path: 'environment-providers',
        component: EnvironmentProviderConfigComponent,
        canActivate: [EnvironmentProviderAccessGuard],
        data: {
          title: 'Environment Provider Configuration',
          breadcrumb: 'Environment Providers'
        }
      },
      {
        path: 'admin',
        canActivate: [AdminGuard],
        children: [
          { path: '', redirectTo: 'idp-config', pathMatch: 'full' },
          { path: 'idp-config', component: IdpConfigComponent },
          { path: 'environment-provider-config', component: EnvironmentProviderAdminComponent },
          { path: 'provider-config', component: SecretProviderAdminComponent }, // Secret providers
          { path: 'group-mapping', component: GroupMappingComponent },
          { path: 'roles', component: RolesComponent },
          { path: 'group-project-assignments', component: GroupProjectAssignmentsComponent },
          { path: 'role-project-assignments', component: GroupProjectAssignmentsComponent }, // Alias for backward compatibility
          { path: 'permission-management', component: PermissionManagementComponent },
          { path: 'users', component: UsersComponent },
          { path: 'audit-logs', component: AuditLogsComponent },
          { path: 'workflow-instances', component: WorkflowInstancesComponent },
          { path: 'settings', component: SettingsComponent }
        ]
      }
    ]
  },
  { path: '**', redirectTo: '' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
