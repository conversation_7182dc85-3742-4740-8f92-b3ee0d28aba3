# Template Variable System in Deploy Orchestrator

This document explains the corrected template variable system in the Deploy Orchestrator UI for creating flexible, reusable workflow templates.

## Overview

The template variable system allows you to:
- Define template variables when creating workflows
- Use variables in step configurations with template syntax
- Override variable values when creating workflows from templates
- Provide runtime parameters when executing workflows

## Correct Workflow: Template Variables

### 1. Template Creation (Workflow Designer)

When designing a workflow that will become a template:

1. Open the Workflow Designer
2. Click the **"Configure Workflow"** button to open the properties panel
3. Navigate to the **"Template Variables"** tab
4. Click **"Add Variable"** to create template variables

### Template Variable Configuration

Each template variable has:

- **Variable Name**: The identifier used in step configurations (e.g., `imageName`, `environment`)
- **Default Value**: The fallback value that works out-of-the-box
- **Usage**: Referenced in step configurations using `{{.variableName}}` syntax

### Variable Usage in Steps

Variables are used in step configurations with template syntax:
```yaml
# Docker Build Step
config:
  image: "{{.imageName}}:{{.version}}"
  context: "{{.buildContext}}"

# Kubernetes Deploy Step
config:
  namespace: "{{.environment}}"
  replicas: "{{.replicaCount}}"
```

### Variable Usage

Variables can be referenced in step configurations using template syntax:
- `{{.variableName}}` - Go template syntax
- `${variableName}` - Shell variable syntax (for advanced conditions)

## Using Templates with Parameters

### 1. Template Marketplace

When browsing templates in the marketplace:
- Templates with parameters show a parameter count badge
- Click on a template to view its parameter details

### 2. Template Detail Page

The template detail page shows:
- **Overview Tab**: Lists all available parameters with their types and descriptions
- **Parameters Section**: Shows required/optional status and default values
- **Sidebar**: Displays parameter count

### 3. Creating Workflows from Templates

When creating a workflow from a template:

1. Click "Use Template" on any template
2. Fill in basic information (name, project)
3. Configure parameters in the "Configure Template Parameters" section
4. The system validates required parameters before allowing workflow creation

### Parameter Configuration Interface

The parameter configuration interface provides:
- **Type-specific inputs**: Text, number, checkbox, dropdown, array inputs
- **Validation**: Real-time validation with error messages
- **Default values**: Pre-populated with template defaults
- **Help text**: Parameter descriptions and usage hints

## Template Syntax Examples

### Basic Variable Usage

```yaml
# In step configuration
config:
  image: "{{.imageName}}:{{.version}}"
  environment: "{{.environment}}"
  replicas: "{{.replicaCount}}"
```

### Advanced Conditions

```yaml
# Using variables in conditions
advancedConditions:
  - type: "expression"
    expression: "${environment} == 'production'"
```

### Environment Variables

Variables are automatically injected as environment variables in script steps:
```bash
# In script step
echo "Deploying ${IMAGE_NAME} to ${ENVIRONMENT}"
```

## Best Practices

### Parameter Design

1. **Use descriptive names**: `imageName` instead of `img`
2. **Provide clear descriptions**: Explain what each parameter controls
3. **Set sensible defaults**: Reduce configuration burden
4. **Mark required parameters**: Ensure critical values are provided
5. **Use appropriate types**: Number for counts, enum for limited choices

### Variable Organization

1. **Group related variables**: Keep similar configurations together
2. **Use consistent naming**: Follow a naming convention
3. **Document complex variables**: Add comments in descriptions
4. **Avoid hardcoding**: Use variables for environment-specific values

### Template Creation

1. **Test with different parameter values**: Ensure template works with various inputs
2. **Validate parameter combinations**: Check for conflicting settings
3. **Provide examples**: Show sample parameter values in descriptions
4. **Version your templates**: Update versions when changing parameters

## Troubleshooting

### Common Issues

1. **Parameter validation errors**: Check required fields and data types
2. **Template syntax errors**: Verify variable names match parameter names
3. **Missing variables**: Ensure all referenced variables are defined
4. **Type mismatches**: Confirm parameter types match expected values

### Debugging Tips

1. **Check parameter names**: Ensure consistency between definition and usage
2. **Verify default values**: Test with and without custom parameter values
3. **Review step configurations**: Look for template syntax errors
4. **Test incrementally**: Add parameters one at a time

## API Integration

The parameter system integrates with the backend API:

- **Template Creation**: `POST /templates/from-workflow` includes parameters
- **Workflow Creation**: `POST /templates/{id}/create-workflow` accepts parameter values
- **Parameter Validation**: Client-side validation with server-side enforcement

## Future Enhancements

Planned improvements include:
- Parameter validation rules (min/max values, regex patterns)
- Conditional parameters (show/hide based on other parameter values)
- Parameter groups and sections
- Import/export parameter configurations
- Parameter templates and presets
