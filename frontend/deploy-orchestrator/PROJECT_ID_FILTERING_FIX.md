# Project ID Filtering Implementation Fix

This fix addresses the issue where API calls in the deployable-management component were not filtering resources by projectId, which should be taken from the project selector in the header.

## Changes Made

### 1. DeployableManagementComponent Updates
- Added `ProjectService` import and injected it in the component constructor
- Added a `currentProjectId` property to store the selected project ID
- Added a `setupProjectSubscription` method to subscribe to project selection changes from ProjectService
- Modified `loadInitialData` to pass projectId to all service calls for:
  - DeployableService.getDeployables()
  - EnvironmentService.getEnvironments()
  - WorkflowService.getWorkflows()
- Updated `refreshDeploymentStatus` to include projectId when refreshing data
- Updated `loadParentDeployable` and `loadDeployableDetails` to pass projectId when fetching data
- Updated `deployToEnvironment` and `rollbackDeployable` to include projectId parameter
- Added a helper method `getCurrentProjectInfo` for debugging purposes

### 2. DeployableService Updates
- Modified `getDeployable` method to accept and use projectId parameter
- Updated `deployToEnvironment` to include projectId in the request body
- Updated `rollbackDeployable` to include projectId in the request body
- Modified `getDeploymentHistory` to accept and use projectId parameter
- Modified `getEnvironmentStatus` to accept and use projectId parameter
- Modified `getDeployableMetrics` to accept and use projectId parameter
- The `refreshDeployables` method was already set up to accept a projectId parameter

## Result
- All resources (deployables, environments, workflows) are now properly filtered based on the currently selected project
- The component subscribes to project selection changes and updates the displayed data accordingly
- All API calls include the projectId parameter to ensure consistent filtering

## Next Steps
1. Test the implementation by switching between different projects in the UI
2. Verify all resources are correctly filtered based on the selected project
3. Consider adding similar project-based filtering to other components if needed
