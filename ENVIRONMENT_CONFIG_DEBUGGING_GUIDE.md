# Environment Configuration Debugging Guide

## Overview

This guide helps debug and fix issues with the environment configuration page, specifically:
1. Conditional fields not showing correctly (e.g., username field not appearing when selecting "username_password" auth method)
2. Empty secret list when trying to select secrets from the secret provider

## Issues Identified

### Issue 1: Conditional Fields Not Showing

**Problem**: When selecting "username_password" auth method in OpenShift provider, the username field doesn't appear.

**Root Cause Analysis**:
- The OpenShift provider schema correctly defines conditional fields with `dependsOn` and `showWhen` properties
- The frontend `shouldShowField` method should handle these conditions
- Possible issues:
  1. Form control not initialized with default values
  2. Change detection not triggered when dependent field changes
  3. Type mismatch in conditional logic

### Issue 2: Empty Secret List

**Problem**: Secret dropdown shows "No secrets available" even when secrets are added to the project.

**Root Cause Analysis**:
- Project ID might not be passed correctly to the component
- Secret loading API call might be failing
- Response format might not match expected structure

## Debugging Steps

### Step 1: Enable Debug Logging

The component now includes comprehensive debug logging. Open browser developer tools and:

1. Navigate to environment configuration page
2. Select "Red Hat OpenShift Container Platform Provider"
3. Check console for debug messages:

```
Converting JSON Schema to config fields: {...}
Processing field auth_method: {...}
Processing field username: {...}
Field username: dependsOn=auth_method, showWhen=username_password, currentValue=undefined
```

### Step 2: Verify Schema Structure

Check that the OpenShift provider schema includes correct conditional logic:

```json
{
  "username": {
    "type": "string",
    "title": "Username",
    "description": "OpenShift username",
    "dependsOn": "auth_method",
    "showWhen": "username_password"
  }
}
```

### Step 3: Test Form Control Initialization

1. Select OpenShift provider
2. Check that auth_method field has default value:
   ```javascript
   // In browser console
   angular.element(document.querySelector('app-environment-config')).componentInstance.environmentForm.get('config').get('auth_method').value
   ```

### Step 4: Test Secret Loading

1. Check project ID is set:
   ```javascript
   // In browser console
   angular.element(document.querySelector('app-environment-config')).componentInstance.projectId
   ```

2. Check secret loading API call:
   ```javascript
   // Check Network tab for calls to:
   // /api/v1/secrets-service/projects/{projectId}/secrets
   ```

## Fixes Implemented

### Fix 1: Enhanced Conditional Field Logic

**File**: `frontend/deploy-orchestrator/src/app/components/environment-config/environment-config.component.ts`

**Changes**:
1. Added comprehensive debug logging to `shouldShowField` method
2. Enhanced conditional logic to handle different data types
3. Added form change detection to trigger field visibility updates
4. Improved schema conversion with detailed logging

### Fix 2: Improved Secret Loading

**Changes**:
1. Added detailed logging to `loadAvailableSecrets` method
2. Enhanced error handling with specific error details
3. Added `OnChanges` lifecycle hook to reload secrets when project changes
4. Improved response validation and transformation

### Fix 3: Form Control Enhancement

**Changes**:
1. Added validation rules based on field schema
2. Enhanced form control initialization with proper defaults
3. Added change detection subscription for conditional fields

## Testing Instructions

### Test 1: Conditional Fields

1. **Navigate** to environment configuration page
2. **Select** "Red Hat OpenShift Container Platform Provider"
3. **Open** browser developer tools (F12)
4. **Check** Console tab for debug messages
5. **Select** "username_password" from Auth Method dropdown
6. **Verify** username and password fields appear
7. **Select** "token" from Auth Method dropdown
8. **Verify** token field appears and username/password fields hide

**Expected Console Output**:
```
Field username: dependsOn=auth_method, showWhen=username_password, currentValue=username_password
Field username: String comparison - username_password === username_password = true
```

### Test 2: Secret Integration

1. **Ensure** you have secrets added to the current project
2. **Navigate** to environment configuration page
3. **Select** any provider with password/secret fields
4. **Click** the key icon next to a password field
5. **Verify** secret dropdown shows available secrets

**Expected Console Output**:
```
Loading available secrets for project: project-123
Raw project secrets response: {secrets: [...]}
Transformed available secrets: [{id: "...", name: "...", ...}]
```

### Test 3: Error Scenarios

1. **Test with no project selected**:
   - Should show warning: "No project ID available for loading secrets"

2. **Test with invalid project**:
   - Should show error details in console

3. **Test with malformed schema**:
   - Should show warning: "Invalid JSON Schema - missing properties"

## Common Issues and Solutions

### Issue: Username field still not showing

**Solution**:
1. Check that auth_method field has correct default value
2. Verify form control is properly initialized
3. Check for JavaScript errors in console

**Debug Commands**:
```javascript
// Check form structure
const form = angular.element(document.querySelector('app-environment-config')).componentInstance.environmentForm;
console.log('Form value:', form.value);
console.log('Config controls:', Object.keys(form.get('config').controls));

// Check field configuration
const component = angular.element(document.querySelector('app-environment-config')).componentInstance;
console.log('Selected provider schema:', component.selectedProvider?.configSchema);
```

### Issue: Secrets still not loading

**Solution**:
1. Verify project ID is correctly set
2. Check network requests in browser dev tools
3. Verify secret service API is working

**Debug Commands**:
```javascript
// Check project ID
const component = angular.element(document.querySelector('app-environment-config')).componentInstance;
console.log('Project ID:', component.projectId);

// Manually trigger secret loading
component.loadAvailableSecrets();

// Check available secrets
console.log('Available secrets:', component.availableSecrets);
```

### Issue: Form validation errors

**Solution**:
1. Check that required fields are properly marked
2. Verify validation rules are correctly applied
3. Check for circular dependencies in conditional fields

## Production Deployment

### Before Deploying

1. **Remove debug logging** from production build
2. **Test all provider types** with conditional fields
3. **Verify secret integration** works across different projects
4. **Test error scenarios** and ensure graceful handling

### Monitoring

1. **Monitor browser console** for JavaScript errors
2. **Check API logs** for secret loading failures
3. **Monitor user feedback** for configuration issues

## Additional Resources

- [Angular Reactive Forms Documentation](https://angular.io/guide/reactive-forms)
- [JSON Schema Specification](https://json-schema.org/)
- [Environment Service API Documentation](./docs/api/environment-service.md)
- [Secrets Service API Documentation](./docs/api/secrets-service.md)

## Support

If issues persist after following this guide:

1. **Collect debug logs** from browser console
2. **Export network requests** from browser dev tools
3. **Document steps to reproduce** the issue
4. **Contact development team** with collected information
