#!/bin/bash

# Initialize and start the deployment orchestration application
echo "Initializing Deployment Orchestration Application"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Build and start containers
echo "Building and starting containers..."
docker-compose up --build -d

# Wait for services to be ready
echo "Waiting for services to start up..."
sleep 5

# Check if services are running
echo "Checking services:"

# Frontend
curl -s http://localhost:4200 > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ Frontend is running"
else 
    echo "❌ Frontend is not accessible"
fi

# Backend services
services=(
    "deployment-service:8081"
    "scheduling-service:8082"
    "notification-service:8083"
    "integration-service:8084"
    "audit-service:8085"
    "admin-service:8086"
)

for service in "${services[@]}"; do
    IFS=':' read -r name port <<< "$service"
    curl -s http://localhost:$port/health > /dev/null
    if [ $? -eq 0 ]; then
        echo "✅ $name is running"
    else 
        echo "❌ $name is not accessible"
    fi
done

echo ""
echo "Deployment Orchestrator is now running!"
echo "- Frontend: http://localhost:4200"
echo "- API Documentation: http://localhost:8081/api/docs"
echo ""
echo "To stop the application, run: docker-compose down"
