version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: deploy_orchestrator
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  kafka:
    image: confluentinc/cp-kafka:latest
    environment:
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    ports:
      - "9092:9092"
    depends_on:
      - zookeeper

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"

  # Workflow Service Instance 1 - Linux/AMD64
  workflow-service-1:
    build:
      context: ./backend
      dockerfile: workflow-service/Dockerfile
    ports:
      - "8085:8085"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - POSTGRES_URL=******************************************/deploy_orchestrator?sslmode=disable
      - INSTANCE_NAME=workflow-linux-1
      - INSTANCE_LABELS=environment=production,region=us-east-1,zone=a,os=linux,arch=amd64
      - INSTANCE_CAPABILITIES=workflow-execution,script-execution,docker,kubernetes
      - ENVIRONMENT=production
      - REGION=us-east-1
      - ZONE=a
      - WORKFLOW_MAX_CONCURRENT_EXECUTIONS=5
      - SCRIPT_EXECUTOR_ENABLED=true
      - SCRIPT_EXECUTOR_SANDBOX=true
    volumes:
      - ./backend/workflow-service/config:/app/config
    depends_on:
      postgres:
        condition: service_healthy
      kafka:
        condition: service_started
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Workflow Service Instance 2 - Linux/AMD64 (Different Zone)
  workflow-service-2:
    build:
      context: ./backend
      dockerfile: workflow-service/Dockerfile
    ports:
      - "8086:8085"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - POSTGRES_URL=******************************************/deploy_orchestrator?sslmode=disable
      - PORT=8085
      - INSTANCE_NAME=workflow-linux-2
      - INSTANCE_LABELS=environment=production,region=us-east-1,zone=b,os=linux,arch=amd64
      - INSTANCE_CAPABILITIES=workflow-execution,script-execution,docker,kubernetes
      - ENVIRONMENT=production
      - REGION=us-east-1
      - ZONE=b
      - WORKFLOW_MAX_CONCURRENT_EXECUTIONS=5
      - SCRIPT_EXECUTOR_ENABLED=true
      - SCRIPT_EXECUTOR_SANDBOX=true
    volumes:
      - ./backend/workflow-service/config:/app/config
    depends_on:
      postgres:
        condition: service_healthy
      kafka:
        condition: service_started
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Workflow Service Instance 3 - Development Environment
  workflow-service-dev:
    build:
      context: ./backend
      dockerfile: workflow-service/Dockerfile
    ports:
      - "8087:8085"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - POSTGRES_URL=******************************************/deploy_orchestrator?sslmode=disable
      - PORT=8085
      - INSTANCE_NAME=workflow-dev-1
      - INSTANCE_LABELS=environment=development,region=local,zone=default,os=linux,arch=amd64
      - INSTANCE_CAPABILITIES=workflow-execution,script-execution,docker
      - ENVIRONMENT=development
      - REGION=local
      - ZONE=default
      - WORKFLOW_MAX_CONCURRENT_EXECUTIONS=3
      - SCRIPT_EXECUTOR_ENABLED=true
      - SCRIPT_EXECUTOR_SANDBOX=false
    volumes:
      - ./backend/workflow-service/config:/app/config
    depends_on:
      postgres:
        condition: service_healthy
      kafka:
        condition: service_started
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Workflow Service Instance 4 - High Memory Instance
  workflow-service-highmem:
    build:
      context: ./backend
      dockerfile: workflow-service/Dockerfile
    ports:
      - "8088:8085"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - POSTGRES_URL=******************************************/deploy_orchestrator?sslmode=disable
      - PORT=8085
      - INSTANCE_NAME=workflow-highmem-1
      - INSTANCE_LABELS=environment=production,region=us-east-1,zone=c,os=linux,arch=amd64,type=highmem
      - INSTANCE_CAPABILITIES=workflow-execution,script-execution,docker,kubernetes,high-memory
      - ENVIRONMENT=production
      - REGION=us-east-1
      - ZONE=c
      - WORKFLOW_MAX_CONCURRENT_EXECUTIONS=10
      - SCRIPT_EXECUTOR_ENABLED=true
      - SCRIPT_EXECUTOR_SANDBOX=true
      - SCRIPT_EXECUTOR_MAX_MEMORY_MB=2048
    volumes:
      - ./backend/workflow-service/config:/app/config
    depends_on:
      postgres:
        condition: service_healthy
      kafka:
        condition: service_started
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Gateway Service (handles load balancing and routing)
  gateway-service:
    build:
      context: ./backend
      dockerfile: gateway-service/Dockerfile
    ports:
      - "8080:8080"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - POSTGRES_URL=******************************************/deploy_orchestrator?sslmode=disable
      - PORT=8080
    volumes:
      - ./backend/gateway-service/config:/app/config
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped

  # Admin Service
  admin-service:
    build:
      context: ./backend
      dockerfile: admin-service/Dockerfile
    ports:
      - "8089:8086"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - POSTGRES_URL=******************************************/deploy_orchestrator?sslmode=disable
      - PORT=8086
    volumes:
      - ./backend/admin-service/config:/app/config
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped

  # Deployment Service
  deployment-service:
    build:
      context: ./backend
      dockerfile: deployment-service/Dockerfile
    ports:
      - "8082:8082"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - POSTGRES_URL=******************************************/deploy_orchestrator?sslmode=disable
    volumes:
      - ./backend/deployment-service/config:/app/config
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  default:
    driver: bridge
