package tests

import (
	"fmt"
	"net"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// PortAllocation represents the expected port allocation strategy
type PortAllocation struct {
	Service     string
	Port        int
	Category    string
	Description string
}

// ExpectedPortAllocations defines the port management strategy
var ExpectedPortAllocations = []PortAllocation{
	// Core Services (8000-8099)
	{Service: "gateway-service", Port: 8000, Category: "core", Description: "API Gateway"},
	{Service: "admin-service", Port: 8080, Category: "core", Description: "Admin Service (Internal)"},
	{Service: "environment-service", Port: 8088, Category: "core", Description: "Environment Service"},
	{Service: "secrets-service", Port: 8082, Category: "core", Description: "Secrets Service"},
	{Service: "notification-service", Port: 8083, Category: "core", Description: "Notification Service"},
	{Service: "scheduling-service", Port: 8084, Category: "core", Description: "Scheduling Service"},
	{Service: "integration-service", Port: 8085, Category: "core", Description: "Integration Service"},
	{Service: "workflow-service", Port: 8088, Category: "core", Description: "Workflow Service (Fixed from 8085)"},
	{Service: "audit-service", Port: 8087, Category: "core", Description: "Audit Service"},

	// External Environment Providers (8100-8199)
	{Service: "gke-provider", Port: 8100, Category: "provider", Description: "GKE Environment Provider"},
	{Service: "aks-provider", Port: 8101, Category: "provider", Description: "AKS Environment Provider"},
	{Service: "eks-provider", Port: 8102, Category: "provider", Description: "EKS Environment Provider"},
	{Service: "openshift-provider", Port: 8103, Category: "provider", Description: "OpenShift Environment Provider"},

	// Plugins (8200-8299)
	{Service: "default-plugin", Port: 8200, Category: "plugin", Description: "Default Plugin Port"},
	{Service: "helm-plugin", Port: 8201, Category: "plugin", Description: "Helm OpenShift Plugin"},
	{Service: "bitbucket-plugin", Port: 8202, Category: "plugin", Description: "Bitbucket Plugin"},
}

func TestPortAllocationStrategy(t *testing.T) {
	t.Run("No port conflicts in allocation", func(t *testing.T) {
		usedPorts := make(map[int]string)

		for _, allocation := range ExpectedPortAllocations {
			if existingService, exists := usedPorts[allocation.Port]; exists {
				t.Errorf("Port conflict detected: %s and %s both use port %d",
					allocation.Service, existingService, allocation.Port)
			}
			usedPorts[allocation.Port] = allocation.Service
		}
	})

	t.Run("Port ranges are correctly allocated", func(t *testing.T) {
		coreServices := []PortAllocation{}
		providers := []PortAllocation{}
		plugins := []PortAllocation{}

		for _, allocation := range ExpectedPortAllocations {
			switch allocation.Category {
			case "core":
				coreServices = append(coreServices, allocation)
			case "provider":
				providers = append(providers, allocation)
			case "plugin":
				plugins = append(plugins, allocation)
			}
		}

		// Verify core services are in 8000-8099 range
		for _, service := range coreServices {
			assert.GreaterOrEqual(t, service.Port, 8000,
				"Core service %s port %d should be >= 8000", service.Service, service.Port)
			assert.LessOrEqual(t, service.Port, 8099,
				"Core service %s port %d should be <= 8099", service.Service, service.Port)
		}

		// Verify providers are in 8100-8199 range
		for _, provider := range providers {
			assert.GreaterOrEqual(t, provider.Port, 8100,
				"Provider %s port %d should be >= 8100", provider.Service, provider.Port)
			assert.LessOrEqual(t, provider.Port, 8199,
				"Provider %s port %d should be <= 8199", provider.Service, provider.Port)
		}

		// Verify plugins are in 8200-8299 range
		for _, plugin := range plugins {
			assert.GreaterOrEqual(t, plugin.Port, 8200,
				"Plugin %s port %d should be >= 8200", plugin.Service, plugin.Port)
			assert.LessOrEqual(t, plugin.Port, 8299,
				"Plugin %s port %d should be <= 8299", plugin.Service, plugin.Port)
		}
	})
}

func TestPortAvailability(t *testing.T) {
	t.Run("Check port availability", func(t *testing.T) {
		for _, allocation := range ExpectedPortAllocations {
			t.Run(fmt.Sprintf("Port %d for %s", allocation.Port, allocation.Service), func(t *testing.T) {
				// Try to bind to the port to check if it's available
				listener, err := net.Listen("tcp", fmt.Sprintf(":%d", allocation.Port))
				if err != nil {
					t.Logf("Port %d is already in use (expected if service is running): %v", allocation.Port, err)
					return
				}
				defer listener.Close()

				// Port is available
				t.Logf("Port %d is available for %s", allocation.Port, allocation.Service)
			})
		}
	})
}

func TestEnvironmentVariablePortOverrides(t *testing.T) {
	tests := []struct {
		name        string
		envVar      string
		defaultPort int
		testPort    int
	}{
		{
			name:        "Plugin port override",
			envVar:      "PLUGIN_PORT",
			defaultPort: 8200,
			testPort:    8250,
		},
		{
			name:        "GKE provider port override",
			envVar:      "PORT",
			defaultPort: 8100,
			testPort:    8150,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variable
			originalValue := os.Getenv(tt.envVar)
			defer func() {
				if originalValue != "" {
					os.Setenv(tt.envVar, originalValue)
				} else {
					os.Unsetenv(tt.envVar)
				}
			}()

			os.Setenv(tt.envVar, fmt.Sprintf("%d", tt.testPort))

			// Verify environment variable is set
			assert.Equal(t, fmt.Sprintf("%d", tt.testPort), os.Getenv(tt.envVar))
		})
	}
}

func TestServiceStartupPortValidation(t *testing.T) {
	t.Run("Validate service can start on assigned ports", func(t *testing.T) {
		// Test that we can create listeners on the assigned ports
		// This simulates service startup validation

		testPorts := []int{8088, 8100, 8200} // Sample ports from different categories

		for _, port := range testPorts {
			t.Run(fmt.Sprintf("Port %d", port), func(t *testing.T) {
				// Try to create a listener
				listener, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
				if err != nil {
					// Port might be in use, which is okay if service is running
					t.Logf("Port %d is in use (service might be running): %v", port, err)
					return
				}
				defer listener.Close()

				// Verify we can accept connections
				go func() {
					conn, err := listener.Accept()
					if err == nil {
						conn.Close()
					}
				}()

				// Try to connect to verify the listener is working
				conn, err := net.DialTimeout("tcp", fmt.Sprintf("localhost:%d", port), time.Second)
				if err == nil {
					conn.Close()
					t.Logf("Successfully validated port %d", port)
				}
			})
		}
	})
}

func TestPortConflictDetection(t *testing.T) {
	t.Run("Detect potential port conflicts", func(t *testing.T) {
		// This test checks for common port conflicts with system services
		systemPorts := map[int]string{
			22:   "SSH",
			80:   "HTTP",
			443:  "HTTPS",
			3306: "MySQL",
			5432: "PostgreSQL",
			6379: "Redis",
			9092: "Kafka",
		}

		for _, allocation := range ExpectedPortAllocations {
			if service, exists := systemPorts[allocation.Port]; exists {
				t.Errorf("Port conflict: %s (port %d) conflicts with system service %s",
					allocation.Service, allocation.Port, service)
			}
		}
	})
}

func TestDockerComposePortMapping(t *testing.T) {
	t.Run("Verify Docker Compose port mappings", func(t *testing.T) {
		// Expected Docker Compose port mappings
		expectedMappings := map[string]string{
			"gateway-service":      "8000:8000",
			"admin-service":        "8086:8080", // External:Internal
			"environment-service":  "8088:8088",
			"workflow-service":     "8088:8088", // Fixed from 8085:8085
			"secrets-service":      "8082:8082",
			"notification-service": "8083:8083",
			"scheduling-service":   "8084:8084",
			"integration-service":  "8085:8085",
			"audit-service":        "8087:8087",
		}

		// This would typically read from docker-compose.yml and validate
		// For now, we just verify the expected mappings are documented
		for service, mapping := range expectedMappings {
			t.Logf("Expected Docker mapping for %s: %s", service, mapping)
		}
	})
}

func BenchmarkPortBinding(b *testing.B) {
	// Benchmark port binding performance
	port := 9999 // Use a test port

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		listener, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
		if err != nil {
			b.Fatalf("Failed to bind to port %d: %v", port, err)
		}
		listener.Close()
	}
}
