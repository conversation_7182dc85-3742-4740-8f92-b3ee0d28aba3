# Deploy Orchestrator - Monitoring & Observability

This document describes the comprehensive monitoring and observability infrastructure implemented for the Deploy Orchestrator platform.

## 🎯 Overview

The monitoring system provides:
- **Health Checks**: Comprehensive service health monitoring
- **Metrics Collection**: Prometheus-compatible metrics
- **Structured Logging**: JSON-formatted logs with correlation IDs
- **Distributed Tracing**: Request tracing across services
- **Monitoring Dashboard**: Real-time service status visualization

## 🏗️ Architecture

### Components

1. **Shared Monitoring Package** (`backend/shared/monitoring/`)
   - Health check system
   - Metrics collection (Prometheus)
   - Structured logging (Logrus)
   - Distributed tracing
   - Monitoring manager

2. **Monitoring Dashboard** (`backend/monitoring-dashboard/`)
   - Web-based dashboard
   - Service status aggregation
   - Real-time health monitoring
   - REST API for status data

3. **Service Integration** ✅
   - **All microservices integrated**: Admin, Deployment, Scheduling, Integration, Notification, Audit
   - **Automatic middleware setup**: HTTP request tracking, error handling, performance metrics
   - **Database monitoring**: Connection pooling, query performance, health checks
   - **Business operation tracking**: Custom metrics for service-specific operations
   - **Graceful shutdown**: Proper cleanup of monitoring resources

## 📊 Features

### Health Checks

Each service provides multiple health check endpoints:

- `GET /health` - Comprehensive health check with detailed status
- `GET /health/ready` - Readiness probe for Kubernetes
- `GET /health/live` - Liveness probe for Kubernetes

**Health Check Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "service": {
    "name": "admin-service",
    "version": "1.0.0",
    "environment": "development",
    "startTime": "2024-01-15T10:00:00Z",
    "uptime": "30m0s"
  },
  "checks": {
    "database": {
      "name": "database",
      "status": "healthy",
      "message": "Database connection is healthy",
      "duration": "5ms",
      "details": {
        "open_connections": 5,
        "in_use": 2,
        "idle": 3
      }
    },
    "memory": {
      "name": "memory",
      "status": "healthy",
      "message": "Memory usage is normal (128 MB)",
      "duration": "1ms",
      "details": {
        "alloc_mb": 128,
        "sys_mb": 256,
        "goroutines": 45
      }
    }
  },
  "summary": {
    "total": 2,
    "healthy": 2,
    "unhealthy": 0,
    "degraded": 0
  }
}
```

### Metrics Collection

Prometheus-compatible metrics are exposed at `GET /metrics`:

**HTTP Metrics:**
- `http_requests_total` - Total HTTP requests
- `http_request_duration_seconds` - Request duration histogram
- `http_requests_in_flight` - Current requests being processed

**Database Metrics:**
- `db_connections_open` - Open database connections
- `db_connections_in_use` - Database connections in use
- `db_query_duration_seconds` - Database query duration
- `db_queries_total` - Total database queries

**Application Metrics:**
- `app_info` - Application information
- `app_uptime_seconds` - Application uptime
- `app_memory_usage_bytes` - Memory usage
- `app_goroutines` - Number of goroutines

**Business Metrics:**
- `business_operations_total` - Total business operations
- `business_operation_duration_seconds` - Business operation duration

### Structured Logging

All services use structured JSON logging with:

- **Correlation IDs**: Request tracking across services
- **Context Fields**: User ID, session ID, operation context
- **Log Levels**: Debug, Info, Warn, Error, Fatal
- **Automatic Fields**: Service name, version, environment

**Log Entry Example:**
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "info",
  "message": "Request completed",
  "service": "admin-service",
  "version": "1.0.0",
  "environment": "development",
  "request_id": "req-123456",
  "user_id": "user-789",
  "method": "POST",
  "path": "/api/v1/projects",
  "status_code": 201,
  "duration_ms": 45
}
```

### Distributed Tracing

Request tracing across services with:

- **Trace IDs**: Unique identifier for request flows
- **Span IDs**: Individual operation tracking
- **Parent-Child Relationships**: Service call hierarchy
- **Tags and Logs**: Contextual information

## 🚀 Usage

### Service Integration

```go
// Initialize monitoring
monitoringManager := monitoring.NewMonitoringManager("service-name", "1.0.0", "production")
monitoringManager.AddDatabase(db, "postgres")

// Setup routes and middleware
router := gin.New()
monitoringManager.SetupRoutes(router)
monitoringManager.SetupMiddleware(router)

// Business operation logging
finishOp := monitoringManager.LogBusinessOperation(ctx, "create_project")
defer finishOp("success", nil, map[string]interface{}{
    "project_id": projectID,
})
```

### Custom Health Checks

```go
// Add custom health checker
type CustomHealthChecker struct{}

func (c *CustomHealthChecker) Check(ctx context.Context) monitoring.HealthCheck {
    // Custom health check logic
    return monitoring.HealthCheck{
        Name:     "custom_service",
        Status:   monitoring.HealthStatusHealthy,
        Message:  "Custom service is operational",
        Duration: time.Since(start),
    }
}

monitoringManager.Health.AddChecker("custom", &CustomHealthChecker{})
```

## 📈 Monitoring Dashboard

Access the monitoring dashboard at: `http://localhost:9090`

**Features:**
- Real-time service status
- Overall system health
- Service response times
- Auto-refresh every 30 seconds
- Mobile-responsive design

**API Endpoint:**
- `GET /api/status` - JSON status data for all services

## 🔧 Configuration

### Environment Variables

- `LOG_LEVEL` - Logging level (debug, info, warn, error, fatal)
- `METRICS_ENABLED` - Enable/disable metrics collection
- `TRACING_ENABLED` - Enable/disable distributed tracing

### Service Configuration

Each service can be configured with:
- Memory limits for health checks
- Database connection monitoring
- Custom business metrics
- Log correlation settings

## 🐳 Deployment

### Docker Compose

```yaml
version: '3.8'
services:
  monitoring-dashboard:
    build: ./backend/monitoring-dashboard
    ports:
      - "9090:9090"
    depends_on:
      - admin-service
      - deployment-service

  admin-service:
    build: ./backend/admin-service
    ports:
      - "8080:8080"
    environment:
      - LOG_LEVEL=info
```

### Kubernetes

```yaml
apiVersion: v1
kind: Service
metadata:
  name: monitoring-dashboard
spec:
  selector:
    app: monitoring-dashboard
  ports:
    - port: 9090
      targetPort: 9090
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: monitoring-dashboard
spec:
  replicas: 1
  selector:
    matchLabels:
      app: monitoring-dashboard
  template:
    metadata:
      labels:
        app: monitoring-dashboard
    spec:
      containers:
      - name: monitoring-dashboard
        image: deploy-orchestrator/monitoring-dashboard:latest
        ports:
        - containerPort: 9090
        livenessProbe:
          httpGet:
            path: /health
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 10
```

## 📊 Metrics Integration

### Prometheus Configuration

```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'deploy-orchestrator'
    static_configs:
      - targets:
        - 'admin-service:8080'
        - 'deployment-service:8081'
        - 'scheduling-service:8082'
        - 'integration-service:8083'
        - 'notification-service:8084'
        - 'audit-service:8085'
    metrics_path: '/metrics'
    scrape_interval: 30s
```

### Grafana Dashboard

Import the provided Grafana dashboard configuration for:
- Service health overview
- Request rate and latency
- Database performance
- Memory and CPU usage
- Business metrics

## 🧪 Testing Monitoring Integration

### Automated Testing

Use the provided test script to verify monitoring integration:

```bash
# Test all monitoring endpoints
./scripts/test-monitoring-integration.sh
```

This script tests:
- Health check endpoints (`/health`, `/health/ready`, `/health/live`)
- Metrics endpoints (`/metrics`)
- Service availability and response codes

### Manual Testing

Test individual service endpoints:

```bash
# Health check
curl http://localhost:8086/health

# Readiness probe
curl http://localhost:8086/health/ready

# Metrics
curl http://localhost:8086/metrics

# Monitoring dashboard
curl http://localhost:9090/api/status
```

## 🔍 Troubleshooting

### Common Issues

1. **Health Check Failures**
   - Check database connectivity
   - Verify memory limits
   - Review service logs

2. **Missing Metrics**
   - Ensure `/metrics` endpoint is accessible
   - Check Prometheus configuration
   - Verify service registration

3. **Service Integration Issues**
   - Verify monitoring dependencies are installed
   - Check go.mod includes required packages
   - Ensure monitoring manager is properly initialized

4. **Concurrency Issues (RESOLVED)**
   - ✅ Fixed "concurrent map writes" error in tracing system
   - ✅ Added thread-safe synchronization with RWMutex
   - ✅ Comprehensive concurrency tests implemented
   - See `backend/MONITORING_CONCURRENCY_FIX.md` for details

3. **Log Correlation Issues**
   - Verify request ID headers
   - Check middleware order
   - Review context propagation

### Debug Commands

```bash
# Check service health
curl http://localhost:8080/health

# Get metrics
curl http://localhost:8080/metrics

# View dashboard status
curl http://localhost:9090/api/status
```

## 🎯 Next Steps

1. **Alerting**: Integrate with AlertManager for notifications
2. **Log Aggregation**: Set up ELK stack or similar
3. **APM Integration**: Add application performance monitoring
4. **Custom Dashboards**: Create service-specific dashboards
5. **SLA Monitoring**: Implement SLA tracking and reporting
