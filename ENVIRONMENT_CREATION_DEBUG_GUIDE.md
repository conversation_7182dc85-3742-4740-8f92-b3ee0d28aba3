# Environment Creation Debug Guide

## Issue Description

When creating a new environment using the OpenShift provider, the API call to `/api/v1/environment-service/environments` fails with:

```json
{
    "error": "invalid provider configuration: required field 'api_url' is missing"
}
```

This suggests a field mapping issue between the frontend form and backend API.

## Debugging Steps

### 1. **Check Form Data Being Sent**

I've added comprehensive debugging to the environment creation process. When you submit the form, check the browser console for these debug messages:

```javascript
// Expected console output:
Form values before submission: {name: "test-env", config: {api_url: "...", project: "...", ...}}
Config values: {api_url: "https://api.cluster.example.com:6443", project: "test-project", ...}
Selected provider: Red Hat OpenShift Container Platform Provider
Final create request: {
  "projectId": "project-123",
  "name": "test-env",
  "type": "kubernetes",
  "provider": {
    "type": "Red Hat OpenShift Container Platform Provider",
    "config": {
      "api_url": "https://api.cluster.example.com:6443",
      "project": "test-project",
      "auth_method": "username_password",
      "username": "admin",
      "password": "{{secret:secret-123:value}}"
    }
  },
  ...
}
```

### 2. **Verify Field Names Match Backend Schema**

The backend OpenShift provider expects these exact field names:

**Required Fields:**
- `api_url` (string) - OpenShift API URL
- `project` (string) - OpenShift project name

**Optional Fields:**
- `auth_method` (enum) - Authentication method
- `username` (string) - Username (when auth_method = "username_password")
- `password` (string) - Password (when auth_method = "username_password")
- `token` (string) - Token (when auth_method = "token")
- `kubeconfig` (string) - Kubeconfig content (when auth_method = "kubeconfig")
- `oauth_client_id` (string) - OAuth client ID (when auth_method = "oauth")
- `oauth_client_secret` (string) - OAuth client secret (when auth_method = "oauth")
- `insecure_skip_tls_verify` (boolean) - Skip TLS verification
- `timeout` (integer) - Connection timeout

### 3. **Test Form Field Generation**

Run this in browser console to check field generation:

```javascript
// Check if component is loaded
const component = document.querySelector('app-environment-config');
if (!component) {
    console.error('Environment config component not found');
} else {
    console.log('✅ Component found');
}

// Get component instance
const comp = angular.element(component).componentInstance;

// Check selected provider
console.log('Selected provider:', comp.selectedProvider?.metadata.name);
console.log('Provider schema:', comp.selectedProvider?.configSchema);

// Check form structure
console.log('Form value:', comp.environmentForm.value);
console.log('Config form controls:', Object.keys(comp.environmentForm.get('config').controls));

// Check specific field values
const configForm = comp.environmentForm.get('config');
console.log('api_url value:', configForm.get('api_url')?.value);
console.log('project value:', configForm.get('project')?.value);
console.log('auth_method value:', configForm.get('auth_method')?.value);
```

### 4. **Check Provider Schema Loading**

Verify the schema is loaded correctly:

```javascript
// Check if schema was loaded from backend
const comp = angular.element(document.querySelector('app-environment-config')).componentInstance;

// Manually trigger schema loading
comp.environmentService.getProviderSchema('Red Hat OpenShift Container Platform Provider').subscribe({
    next: (response) => {
        console.log('Provider schema response:', response);
        console.log('Schema properties:', Object.keys(response.schema.properties));
        console.log('Required fields:', response.schema.required);
    },
    error: (error) => {
        console.error('Failed to load schema:', error);
    }
});
```

### 5. **Test API Call Manually**

Test the API endpoint directly:

```javascript
// Get current form data
const comp = angular.element(document.querySelector('app-environment-config')).componentInstance;
const formData = comp.environmentForm.value;

// Create test request
const testRequest = {
    projectId: "test-project-id",
    name: "test-environment",
    type: "kubernetes",
    provider: {
        type: "Red Hat OpenShift Container Platform Provider",
        config: {
            api_url: "https://api.cluster.example.com:6443",
            project: "test-project",
            auth_method: "username_password",
            username: "admin",
            password: "test-password"
        }
    },
    resources: {
        cpu: "1000m",
        memory: "1Gi",
        storage: "10Gi",
        replicas: 1
    },
    networking: {
        loadBalancer: false,
        ssl: false
    },
    variables: {},
    secretMappings: [],
    healthCheck: {
        enabled: true,
        endpoint: "/health",
        interval: 30,
        timeout: 10
    },
    deploymentStrategy: "rolling",
    description: "",
    tags: []
};

// Test the API call
fetch('/api/v1/environment-service/environments', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + localStorage.getItem('token') // Adjust based on your auth
    },
    body: JSON.stringify(testRequest)
})
.then(response => response.json())
.then(data => console.log('API Response:', data))
.catch(error => console.error('API Error:', error));
```

## Common Issues and Solutions

### Issue 1: Field Names Don't Match

**Symptoms:**
- Error: "required field 'api_url' is missing"
- Form shows field but API doesn't receive it

**Solution:**
Check that the form field names exactly match the backend schema:
- Frontend: `api_url` ✅
- Backend expects: `api_url` ✅

### Issue 2: Provider Type Mismatch

**Symptoms:**
- Provider not found errors
- Schema loading failures

**Solution:**
Ensure provider type name is correctly URL-encoded:
```javascript
// Check provider type being sent
console.log('Provider type:', comp.selectedProvider?.metadata.name);
// Should be: "Red Hat OpenShift Container Platform Provider"
```

### Issue 3: Form Validation Issues

**Symptoms:**
- Required fields not marked as required
- Form submits with empty values

**Solution:**
Check form validation:
```javascript
const configForm = comp.environmentForm.get('config');
console.log('Form valid:', configForm.valid);
console.log('Form errors:', configForm.errors);

// Check individual field validation
Object.keys(configForm.controls).forEach(fieldName => {
    const control = configForm.get(fieldName);
    if (control.errors) {
        console.log(`Field ${fieldName} errors:`, control.errors);
    }
});
```

### Issue 4: Secret References Not Resolved

**Symptoms:**
- Secret placeholders like `{{secret:id:key}}` sent to API
- Backend doesn't understand secret references

**Solution:**
Check secret mapping:
```javascript
console.log('Secret references:', comp.secretReferences);
console.log('Secret mappings in request:', comp.secretMappings);
```

## Testing Instructions

### 1. **Basic Form Test**
1. Navigate to environment configuration
2. Select "Red Hat OpenShift Container Platform Provider"
3. Fill in required fields:
   - **OpenShift API URL**: `https://api.cluster.example.com:6443`
   - **Project Name**: `test-project`
   - **Authentication Method**: `username_password`
   - **Username**: `admin`
   - **Password**: `test-password`
4. Open browser console and check debug output
5. Submit form and check API request

### 2. **Secret Integration Test**
1. Add a secret to your project first
2. Fill in the form as above
3. For password field, click the key icon and select a secret
4. Check that secret reference is properly mapped
5. Submit and verify secret mapping in request

### 3. **Provider Schema Test**
1. Open browser console
2. Run the provider schema loading test above
3. Verify all expected fields are present
4. Check field types and validation rules

## Expected Behavior

When working correctly, you should see:

1. **Console Output:**
```
Converting JSON Schema to config fields: {type: "object", required: ["api_url", "project"], ...}
Processing field api_url: {type: "string", title: "OpenShift API URL", ...}
Created config field for api_url: {name: "api_url", type: "text", label: "OpenShift API URL", ...}
Form values before submission: {name: "test-env", config: {api_url: "...", project: "...", ...}}
Final create request: {...}
```

2. **Successful API Response:**
```json
{
  "id": "env-123",
  "name": "test-env",
  "status": "created",
  "message": "Environment created successfully"
}
```

## Next Steps

If the issue persists after checking the above:

1. **Check Backend Logs** - Look for detailed error messages in environment-service logs
2. **Verify Provider Registration** - Ensure OpenShift provider is properly registered
3. **Test Provider Validation** - Use the provider validation endpoint to test config
4. **Check Authentication** - Verify user has permissions to create environments

The debugging output will help identify exactly where the field mapping issue occurs.
