# Microservice Database Separation Fix

## Issue Description

Environment creation was failing with:
```
ERROR: insert or update on table "environment_configs" violates foreign key constraint "fk_environment_configs_project" (SQLSTATE 23503)
```

## Root Cause

The issue was caused by **microservice database separation**:

- **admin-service** manages projects in its own database (`admin_service`)
- **environment-service** manages environments in its own database (`environment_service`)
- The `EnvironmentConfig` model had a foreign key constraint referencing a `projects` table that doesn't exist in the environment-service database

## Solution Implemented

### 1. **Removed Foreign Key Constraint**

**File**: `backend/shared/models/environment.go`

```go
// Before (PROBLEMATIC):
Project Project `json:"project,omitempty" gorm:"foreignKey:ProjectID"`

// After (FIXED):
// Relations - Note: No foreign key constraint as projects are managed by admin-service
// Project validation is done via API calls to admin-service
Project Project `json:"project,omitempty" gorm:"-"`
```

### 2. **Created Project Validator Service**

**File**: `backend/environment-service/internal/services/project_validator.go`

- **Validates project existence** via API calls to admin-service
- **Checks user access** to projects
- **Provides project details** for display purposes
- **Health checks** admin-service connectivity

#### Key Methods:
- `ValidateProject(ctx, projectID, authToken)` - Validates project exists and is active
- `ValidateProjectAccess(ctx, projectID, userID, authToken)` - Checks user access
- `GetProjectName(ctx, projectID, authToken)` - Gets project name for display
- `HealthCheck(ctx)` - Checks admin-service connectivity

### 3. **Enhanced Environment Service**

**File**: `backend/environment-service/internal/services/environment_service.go`

#### Added Project Validation:
```go
// CreateEnvironment now validates projects before creation
func (s *EnvironmentService) CreateEnvironment(ctx context.Context, req *CreateEnvironmentRequest) (*models.EnvironmentConfig, error) {
    // Validate project exists and user has access
    if s.projectValidator != nil {
        authToken := s.extractAuthTokenFromContext(ctx)
        
        if err := s.projectValidator.ValidateProject(ctx, req.ProjectID, authToken); err != nil {
            return nil, fmt.Errorf("project validation failed: %w", err)
        }
    }
    
    // Continue with environment creation...
}
```

#### Added Auth Token Extraction:
```go
func (s *EnvironmentService) extractAuthTokenFromContext(ctx context.Context) string {
    // Extracts JWT token from request context for API calls
}
```

### 4. **Updated Service Initialization**

**File**: `backend/environment-service/main.go`

```go
// Initialize project validator
projectValidator := services.NewProjectValidator(cfg.Auth.AdminServiceURL, logger)

// Initialize services with project validator
environmentService := services.NewEnvironmentService(db, projectValidator, logger)
```

### 5. **Database Migration Script**

**File**: `backend/environment-service/migrations/remove_project_fk.sql`

- **Removes existing foreign key constraints** to projects table
- **Adds index** on project_id for performance
- **Documents the change** with column comments
- **Safe to run** on existing databases

## Architecture Benefits

### ✅ **Proper Microservice Separation**
- Each service manages its own database
- No cross-database foreign key constraints
- Services communicate via APIs

### ✅ **Robust Validation**
- Project validation via admin-service API
- User access verification
- Graceful handling of admin-service unavailability

### ✅ **Better Error Messages**
- Clear project validation errors
- Specific error messages for different scenarios
- Comprehensive logging for debugging

### ✅ **Scalability**
- Services can be deployed independently
- Databases can be scaled separately
- No tight coupling between services

## API Validation Flow

### Before (BROKEN):
```
Frontend → Environment Service → Database (FK constraint fails)
```

### After (WORKING):
```
Frontend → Environment Service → Admin Service API (validate project) → Database (success)
```

## Error Handling

### Project Not Found:
```json
{
  "error": "project validation failed: project f0768aa2-aec0-4864-87bc-62e8f39921de not found"
}
```

### Access Denied:
```json
{
  "error": "project validation failed: user does not have access to project f0768aa2-aec0-4864-87bc-62e8f39921de"
}
```

### Admin Service Unavailable:
- **Graceful degradation**: Environment creation continues with warning
- **Logging**: Detailed error logs for monitoring
- **Retry logic**: Built into HTTP client

## Deployment Instructions

### 1. **Update Environment Service**
```bash
cd backend/environment-service
go mod tidy
go build -o environment-service main.go
```

### 2. **Run Database Migration**
```bash
# Connect to environment-service database
psql -h localhost -U deploy -d environment_service

# Run migration script
\i migrations/remove_project_fk.sql
```

### 3. **Update Configuration**
Ensure `admin_service_url` is correctly configured in `config.yaml`:
```yaml
auth:
  admin_service_url: "http://localhost:8086"
```

### 4. **Restart Service**
```bash
./environment-service
```

## Testing

### 1. **Test Environment Creation**
```bash
curl -X POST "http://localhost:8088/api/v1/environment-service/environments" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "projectId": "valid-project-id",
    "name": "test-environment",
    "type": "kubernetes",
    "provider": {
      "type": "Red Hat OpenShift Container Platform Provider",
      "config": {
        "authMethod": "username_password",
        "project": "test-project",
        "credentials": {
          "username": "admin",
          "password": "password"
        },
        "extra": {
          "api_url": "https://api.cluster.com:6443"
        }
      }
    },
    "resources": {
      "cpu": "1000m",
      "memory": "1Gi",
      "storage": "10Gi",
      "replicas": 1
    },
    "networking": {
      "loadBalancer": false,
      "ssl": false
    },
    "variables": {},
    "secretMappings": [],
    "healthCheck": {
      "enabled": true,
      "endpoint": "/health",
      "interval": 30,
      "timeout": 10
    },
    "deploymentStrategy": "rolling",
    "description": "Test environment",
    "tags": []
  }'
```

### 2. **Expected Success Response**
```json
{
  "id": "env-123",
  "projectId": "valid-project-id",
  "name": "test-environment",
  "status": "inactive",
  "createdAt": "2024-01-01T00:00:00Z"
}
```

## Files Modified

1. **`backend/shared/models/environment.go`** - Removed foreign key constraint
2. **`backend/environment-service/internal/services/project_validator.go`** - New project validator service
3. **`backend/environment-service/internal/services/environment_service.go`** - Added project validation
4. **`backend/environment-service/main.go`** - Updated service initialization
5. **`backend/environment-service/migrations/remove_project_fk.sql`** - Database migration script

## Monitoring

### Logs to Monitor:
- **Project validation success**: `Project validation successful`
- **Project validation failure**: `Project validation failed`
- **Admin service connectivity**: `admin-service health check failed`

### Metrics to Track:
- Project validation success rate
- Admin service API response times
- Environment creation success rate

The foreign key constraint issue has been completely resolved by implementing proper microservice separation with API-based project validation.
