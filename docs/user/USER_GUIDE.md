# Deploy Orchestrator User Guide

This guide explains how to use the Deploy Orchestrator to manage environments, secrets, and deployments across multiple cloud platforms.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Managing Projects](#managing-projects)
3. [Secret Management](#secret-management)
4. [Environment Configuration](#environment-configuration)
5. [Environment Promotion](#environment-promotion)
6. [Monitoring and Troubleshooting](#monitoring-and-troubleshooting)

## Getting Started

### Prerequisites

- Access to the Deploy Orchestrator web interface
- Appropriate permissions for your project(s)
- Cloud provider credentials (AWS, Azure, Google Cloud)

### First Login

1. Navigate to the Deploy Orchestrator URL
2. Log in with your credentials
3. You'll see the dashboard with your accessible projects

### Dashboard Overview

The dashboard provides:
- **Project Overview**: List of projects you have access to
- **Recent Environments**: Recently created or modified environments
- **System Status**: Overall system health
- **Quick Actions**: Common tasks like creating environments

## Managing Projects

### Project Structure

Projects are the top-level organizational unit in Deploy Orchestrator:
- **Environments**: Different deployment targets (dev, staging, prod)
- **Secrets**: Sensitive configuration data
- **Workflows**: Deployment pipelines
- **Users**: Team members with access

### Creating a Project

1. Click **"New Project"** on the dashboard
2. Fill in project details:
   - **Name**: Unique project identifier
   - **Description**: Project purpose and details
   - **Tags**: Organizational labels
3. Click **"Create Project"**

### Project Settings

Access project settings via the gear icon:
- **General**: Basic project information
- **Members**: User access and permissions
- **Secrets**: Project-specific secrets
- **Environments**: Environment configurations
- **Audit Log**: Activity history

## Secret Management

### Overview

Secrets store sensitive information like:
- Database credentials
- API keys
- SSL certificates
- SSH keys

### Secret Providers

Deploy Orchestrator supports multiple secret providers:

| Provider | Description | Use Case |
|----------|-------------|----------|
| **Internal** | Built-in encrypted storage | Development, testing |
| **HashiCorp Vault** | Enterprise secret management | Production environments |
| **AWS Secrets Manager** | AWS-native secret storage | AWS-based deployments |
| **Azure Key Vault** | Azure-native secret storage | Azure-based deployments |
| **Google Secret Manager** | GCP-native secret storage | GCP-based deployments |

### Creating Secrets

#### Method 1: Create New Secret

1. Navigate to **Project Settings** → **Secrets**
2. Click **"Create Secret"** (blue button)
3. Fill in secret details:
   - **Name**: Descriptive name for the secret
   - **Type**: Secret category (database, api, certificate, ssh)
   - **Provider**: Where to store the secret
   - **Description**: Purpose and usage notes
4. Configure provider-specific settings
5. Enter the secret value
6. Click **"Create Secret"**

The secret will be automatically bound to your project.

#### Method 2: Bind Existing Secret

1. Navigate to **Project Settings** → **Secrets**
2. Click **"Bind Existing Secret"** (purple button)
3. Select an existing secret from the dropdown
4. Configure binding settings:
   - **Variable Name**: How to reference in environments
   - **Description**: Binding purpose
5. Click **"Bind Secret"**

### Using Secrets in Environments

When configuring environments, sensitive fields will show a dropdown to select from available project secrets:

1. In environment configuration, look for fields marked with a lock icon
2. Click the dropdown next to the field
3. Select the appropriate secret
4. The field will be populated with a reference like `${secret:db-password}`

### Secret Best Practices

- **Use descriptive names**: `prod-db-password` instead of `secret1`
- **Rotate regularly**: Update secrets on a schedule
- **Limit access**: Only bind secrets to projects that need them
- **Use appropriate providers**: Vault for production, internal for development
- **Document usage**: Add clear descriptions

## Environment Configuration

### Overview

Environments represent deployment targets where your applications run. Each environment is backed by a specific provider (AWS EKS, Azure AKS, Google Cloud Run, etc.).

### Supported Providers

#### Container Orchestration
- **Kubernetes**: Generic Kubernetes clusters
- **AWS EKS**: Amazon Elastic Kubernetes Service
- **Azure AKS**: Azure Kubernetes Service
- **OpenShift**: Red Hat OpenShift
- **Docker Swarm**: Docker Swarm clusters

#### Serverless Platforms
- **Google Cloud Run**: Serverless containers
- **Azure Container Instances**: Fast container deployment
- **AWS Fargate**: Serverless container compute

#### API Gateways
- **Google Apigee**: API management platform

### Creating an Environment

1. Navigate to **Environment Configuration**
2. **Step 1: Select Provider**
   - Browse available providers
   - Click on your desired provider
   - Review provider capabilities and documentation

3. **Step 2: Configure Provider**
   - Fill in required configuration fields
   - Use secrets for sensitive data (credentials, keys)
   - Configure optional settings as needed

4. **Step 3: Review and Create**
   - Review your configuration
   - Click **"Create Environment"**
   - Monitor creation progress

### Provider-Specific Configuration

#### AWS EKS Configuration

Required fields:
- **Region**: AWS region (e.g., us-west-2)
- **Cluster Name**: EKS cluster identifier
- **Access Key ID**: AWS access credentials
- **Secret Access Key**: AWS secret credentials

Optional fields:
- **Namespace**: Kubernetes namespace (default: default)
- **Node Group**: Specific node group for workloads
- **Instance Types**: EC2 instance types for workers
- **Enable Spot Instances**: Use spot instances for cost savings
- **Enable Cluster Autoscaler**: Automatic scaling
- **VPC Configuration**: Network settings

#### Azure AKS Configuration

Required fields:
- **Subscription ID**: Azure subscription
- **Resource Group**: Azure resource group
- **Cluster Name**: AKS cluster name
- **Location**: Azure region
- **Client ID**: Service principal client ID
- **Client Secret**: Service principal secret
- **Tenant ID**: Azure tenant ID

Optional fields:
- **VM Size**: Virtual machine size for nodes
- **Network Plugin**: Kubernetes networking (kubenet/azure)
- **Enable Azure Monitor**: Container monitoring
- **Enable Azure Policy**: Policy enforcement

#### Google Cloud Run Configuration

Required fields:
- **Project ID**: Google Cloud project
- **Region**: GCP region
- **Service Account Key**: JSON service account key
- **Service Name**: Cloud Run service name
- **Container Image**: Container image URL

Optional fields:
- **CPU Limit**: CPU allocation per instance
- **Memory Limit**: Memory allocation per instance
- **Min/Max Instances**: Scaling configuration
- **Concurrency**: Requests per instance
- **VPC Connector**: Private network access

### Environment Validation

The system automatically validates your configuration:

- **Required Fields**: Ensures all mandatory fields are provided
- **Format Validation**: Checks field formats (emails, URLs, etc.)
- **Cloud Provider Validation**: Validates region names, resource formats
- **Credential Validation**: Verifies authentication credentials
- **Resource Limits**: Ensures values are within acceptable ranges

Common validation errors:
- **Invalid Region**: Check provider documentation for valid regions
- **Invalid Credentials**: Verify access keys and permissions
- **Resource Name Conflicts**: Use unique names for resources
- **Insufficient Permissions**: Ensure credentials have required permissions

### Environment Status

After creation, monitor environment status:

- **Creating**: Environment is being provisioned
- **Running**: Environment is active and healthy
- **Error**: Issue with environment (check logs)
- **Updating**: Configuration changes being applied
- **Deleting**: Environment is being removed

## Environment Promotion

### Overview

Environment promotion allows you to deploy applications between environments (e.g., dev → staging → production) with version tracking and rollback capabilities.

### Promotion Workflow

1. **Navigate to Environment Promotion**
2. **Select Source Environment**: Where your application is currently deployed
3. **Select Target Environment**: Where you want to promote to
4. **Choose Version**: Specific version to promote
5. **Review Changes**: Compare configurations
6. **Execute Promotion**: Deploy to target environment
7. **Monitor Progress**: Track deployment status

### Promotion Features

- **Version Tracking**: Track all deployments and versions
- **Rollback Support**: Quickly revert to previous versions
- **Configuration Diff**: See differences between environments
- **Approval Workflows**: Require approvals for production deployments
- **Automated Testing**: Run tests before promotion
- **Notification Integration**: Slack, email, webhook notifications

### Best Practices

- **Use Consistent Naming**: dev-app, staging-app, prod-app
- **Test Thoroughly**: Always test in staging before production
- **Monitor Deployments**: Watch metrics during and after promotion
- **Plan Rollbacks**: Have rollback procedures ready
- **Document Changes**: Include release notes and change descriptions

## Monitoring and Troubleshooting

### Environment Health

Monitor environment health through:

- **Status Dashboard**: Real-time environment status
- **Health Checks**: Automated health monitoring
- **Metrics**: CPU, memory, request rates
- **Logs**: Application and infrastructure logs
- **Alerts**: Automated notifications for issues

### Common Issues

#### Environment Creation Fails

**Symptoms**: Environment stuck in "Creating" status
**Causes**:
- Invalid credentials
- Insufficient permissions
- Resource quota limits
- Network connectivity issues

**Solutions**:
1. Verify credentials are correct and have required permissions
2. Check cloud provider quotas and limits
3. Review network configuration and security groups
4. Check environment service logs for detailed errors

#### Secret Access Issues

**Symptoms**: Applications can't access secrets
**Causes**:
- Secret not bound to project
- Incorrect variable name
- Provider connectivity issues
- Permission problems

**Solutions**:
1. Verify secret is bound to the correct project
2. Check variable name matches environment configuration
3. Test secret provider connectivity
4. Review access permissions

#### Performance Issues

**Symptoms**: Slow response times, high resource usage
**Causes**:
- Insufficient resources
- Inefficient application code
- Database bottlenecks
- Network latency

**Solutions**:
1. Scale environment resources
2. Review application performance
3. Optimize database queries
4. Check network configuration

### Getting Help

#### Documentation
- **Provider Guides**: Detailed provider-specific documentation
- **API Reference**: Complete API documentation
- **Troubleshooting**: Common issues and solutions

#### Support Channels
- **Help Desk**: Submit support tickets
- **Community Forum**: Ask questions and share knowledge
- **Slack Channel**: Real-time chat support
- **Email Support**: Direct email assistance

#### Self-Service Tools
- **Health Checks**: Built-in diagnostic tools
- **Log Viewer**: Search and analyze logs
- **Metrics Dashboard**: Performance monitoring
- **Audit Trail**: Track all system changes

### Advanced Features

#### Custom Providers
- Extend the system with custom environment providers
- Support for specialized platforms and tools
- Plugin architecture for third-party integrations

#### Workflow Automation
- Automated deployment pipelines
- Integration with CI/CD systems
- Custom workflow templates

#### Multi-Cloud Management
- Deploy across multiple cloud providers
- Unified management interface
- Cross-cloud networking and security

#### Enterprise Features
- Single sign-on (SSO) integration
- Advanced RBAC and permissions
- Compliance and audit reporting
- High availability and disaster recovery

## Quick Reference

### Common Tasks

| Task | Navigation | Action |
|------|------------|--------|
| Create Project | Dashboard | Click "New Project" |
| Add Secret | Project Settings → Secrets | Click "Create Secret" |
| Create Environment | Environment Configuration | Select provider, configure, create |
| Promote Application | Environment Promotion | Select environments, choose version |
| View Logs | Environment Details | Click "View Logs" |
| Monitor Health | Dashboard | Check status indicators |

### Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+N` | New project |
| `Ctrl+E` | New environment |
| `Ctrl+S` | Save configuration |
| `Ctrl+/` | Search |
| `Esc` | Close modal |

### API Endpoints

| Endpoint | Purpose |
|----------|---------|
| `/api/v1/projects` | Project management |
| `/api/v1/environments` | Environment operations |
| `/api/v1/secrets` | Secret management |
| `/api/v1/promotions` | Environment promotion |

For detailed API documentation, see the [API Reference](../api/API_REFERENCE.md).

## Next Steps

1. **Create Your First Project**: Start with a simple development project
2. **Add Secrets**: Configure necessary credentials and keys
3. **Create Development Environment**: Set up your first environment
4. **Deploy Application**: Use environment promotion to deploy
5. **Monitor and Scale**: Use monitoring tools to optimize performance

For more advanced topics, see:
- [Provider Extension Guide](../providers/PROVIDER_EXTENSION_GUIDE.md)
- [Validation Guide](../validation/VALIDATION_GUIDE.md)
- [Testing Guide](../testing/TESTING_GUIDE.md)
- [API Documentation](../api/API_REFERENCE.md)
