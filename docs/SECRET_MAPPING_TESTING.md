# Secret Mapping Testing Guide

This document provides comprehensive testing coverage for the secret mapping functionality in the deploy-orchestrator system.

## 🧪 Test Coverage Overview

### **1. Unit Tests**
- **Location**: `backend/workflow-service/tests/unit/secret_mapping_test.go`
- **Coverage**: Core secret mapping logic, validation, and transformation
- **Test Cases**: 15+ test scenarios covering edge cases and error conditions

### **2. Integration Tests**
- **Location**: `backend/workflow-service/tests/integration/secret_mapping_integration_test.go`
- **Coverage**: End-to-end workflow execution with secret mapping
- **Test Cases**: Complete flow from template execution to secret injection

### **3. Security Tests**
- **Location**: `backend/secrets-service/tests/security/secret_isolation_test.go`
- **Coverage**: Project isolation, access control, and audit logging
- **Test Cases**: Cross-project access prevention and security validation

### **4. Frontend Unit Tests**
- **Location**: `frontend/deploy-orchestrator/src/app/components/secret-mapping/secret-mapping.component.spec.ts`
- **Coverage**: UI component behavior, form validation, and user interactions
- **Test Cases**: Component lifecycle, validation, and error handling

## 🚀 Running Tests

### **Quick Start**
```bash
# Run all secret mapping tests
./scripts/test-secret-mapping.sh

# Run specific test suites
./scripts/test-secret-mapping.sh --unit-only
./scripts/test-secret-mapping.sh --integration-only
./scripts/test-secret-mapping.sh --security-only
./scripts/test-secret-mapping.sh --frontend-only

# Run comprehensive test suite
./scripts/test-secret-mapping.sh --all
```

### **Individual Test Commands**

**Backend Unit Tests:**
```bash
cd backend/workflow-service
go test -v ./tests/unit/secret_mapping_test.go
```

**Backend Integration Tests:**
```bash
cd backend/workflow-service
go test -v ./tests/integration/secret_mapping_integration_test.go
```

**Security Tests:**
```bash
cd backend/secrets-service
go test -v ./tests/security/secret_isolation_test.go
```

**Frontend Tests:**
```bash
cd frontend/deploy-orchestrator
npm test -- --include="**/secret-mapping.component.spec.ts"
```

## 📋 Test Scenarios

### **Unit Test Scenarios**

#### **1. Basic Secret Mapping**
- ✅ Direct secret mapping (SSH_USERNAME → DEV_USERNAME)
- ✅ Partial secret mapping (only some variables mapped)
- ✅ Empty secret mapping (no mapping provided)
- ✅ Multiple mappings to same secret

#### **2. Edge Cases**
- ✅ Mapping to non-existent secrets
- ✅ Duplicate template variable mapping
- ✅ Case sensitivity in mapping
- ✅ Special characters in secret names

#### **3. Validation Logic**
- ✅ Required secret validation
- ✅ Optional secret validation
- ✅ Missing mapping detection
- ✅ Invalid mapping detection

### **Integration Test Scenarios**

#### **1. Complete Workflow Execution**
- ✅ Template creation with secret parameters
- ✅ Execution request with secret mapping
- ✅ Secret retrieval and injection
- ✅ Database persistence of mapping

#### **2. Environment-Specific Mapping**
- ✅ Development environment secrets
- ✅ Staging environment secrets
- ✅ Production environment secrets
- ✅ Cross-environment isolation

#### **3. Concurrent Executions**
- ✅ Multiple users executing same template
- ✅ Different secret mappings per execution
- ✅ Isolation between concurrent executions

### **Security Test Scenarios**

#### **1. Project Isolation**
- ✅ Users can only access their project secrets
- ✅ Cross-project access prevention
- ✅ Role-based access control
- ✅ Permission validation

#### **2. Secret Mapping Security**
- ✅ Mapping only within project boundaries
- ✅ Prevention of unauthorized secret access
- ✅ Audit logging of all access attempts
- ✅ Failed access attempt logging

#### **3. Data Protection**
- ✅ Expired secret handling
- ✅ Revoked secret handling
- ✅ Secret value encryption
- ✅ Secure transmission

### **Frontend Test Scenarios**

#### **1. Component Behavior**
- ✅ Component initialization
- ✅ Form creation and validation
- ✅ Secret loading and filtering
- ✅ Recommendation system

#### **2. User Interactions**
- ✅ Secret selection from dropdowns
- ✅ Form submission and validation
- ✅ Error message display
- ✅ Recommendation acceptance

#### **3. State Management**
- ✅ Form state persistence
- ✅ Validation state updates
- ✅ Loading state handling
- ✅ Error state recovery

## 🔍 Test Data

### **Sample Template**
```json
{
  "id": "ssh-deployment-template",
  "name": "SSH Deployment Template",
  "parameters": [
    {
      "name": "SSH_USERNAME",
      "type": "secret",
      "required": true,
      "secretHint": "SSH_USERNAME"
    },
    {
      "name": "SSH_PRIVATE_KEY", 
      "type": "secret",
      "required": true,
      "secretHint": "SSH_PRIVATE_KEY"
    }
  ]
}
```

### **Sample Project Secrets**
```json
[
  {
    "name": "DEV_USERNAME",
    "type": "username",
    "environment": "dev"
  },
  {
    "name": "DEV_KEY",
    "type": "private_key", 
    "environment": "dev"
  },
  {
    "name": "QA_USERNAME",
    "type": "username",
    "environment": "qa"
  }
]
```

### **Sample Secret Mapping**
```json
{
  "SSH_USERNAME": "DEV_USERNAME",
  "SSH_PRIVATE_KEY": "DEV_KEY"
}
```

## 📊 Coverage Metrics

### **Target Coverage**
- **Unit Tests**: 95%+ line coverage
- **Integration Tests**: 90%+ feature coverage
- **Security Tests**: 100% security scenario coverage
- **Frontend Tests**: 90%+ component coverage

### **Generate Coverage Reports**
```bash
# Backend coverage
cd backend/workflow-service
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html

# Frontend coverage
cd frontend/deploy-orchestrator
npm run test -- --code-coverage
```

## 🐛 Common Test Issues

### **Backend Issues**
1. **Database Connection**: Ensure test database is available
2. **Mock Services**: Verify mock services are properly configured
3. **Environment Variables**: Set required test environment variables

### **Frontend Issues**
1. **Dependencies**: Run `npm install` before testing
2. **Browser**: Ensure Chrome/Chromium is available for headless testing
3. **Async Operations**: Handle async operations properly in tests

### **Integration Issues**
1. **Service Startup**: Allow sufficient time for services to start
2. **Port Conflicts**: Ensure test ports are available
3. **Cleanup**: Properly cleanup test data and processes

## 🔧 Test Configuration

### **Backend Test Configuration**
```go
// Test database configuration
db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})

// Mock services configuration
mockSecretsClient := &MockSecretsClient{}
mockSecretsClient.On("GetWorkflowSecrets", mock.Anything, mock.Anything, mock.Anything)
```

### **Frontend Test Configuration**
```typescript
// TestBed configuration
TestBed.configureTestingModule({
  imports: [SecretMappingComponent, ReactiveFormsModule],
  providers: [
    { provide: SecretsService, useValue: mockSecretsService },
    { provide: NotificationService, useValue: mockNotificationService }
  ]
});
```

## 🎯 Test Automation

### **CI/CD Integration**
```yaml
# GitHub Actions example
- name: Run Secret Mapping Tests
  run: |
    ./scripts/test-secret-mapping.sh --all
    
- name: Upload Coverage Reports
  uses: codecov/codecov-action@v1
  with:
    files: ./coverage.xml
```

### **Pre-commit Hooks**
```bash
# Run tests before commit
#!/bin/sh
./scripts/test-secret-mapping.sh --unit-only
if [ $? -ne 0 ]; then
  echo "Tests failed. Commit aborted."
  exit 1
fi
```

## 📈 Performance Testing

### **Load Testing**
- Test secret mapping with 100+ concurrent executions
- Measure response times for secret retrieval
- Validate memory usage during peak load

### **Stress Testing**
- Test with large numbers of secrets (1000+)
- Test with complex secret mappings (50+ variables)
- Test database performance under load

## 🔒 Security Testing Checklist

- [ ] Project isolation verified
- [ ] Cross-project access prevented
- [ ] Audit logging functional
- [ ] Secret encryption verified
- [ ] Access control working
- [ ] Permission validation active
- [ ] Failed access attempts logged
- [ ] Expired secrets handled
- [ ] Revoked secrets blocked
- [ ] Secure transmission verified

## 🎉 Success Criteria

### **All Tests Must Pass**
- ✅ Unit tests: 100% pass rate
- ✅ Integration tests: 100% pass rate  
- ✅ Security tests: 100% pass rate
- ✅ Frontend tests: 100% pass rate

### **Performance Requirements**
- ✅ Secret retrieval: < 100ms
- ✅ Mapping validation: < 50ms
- ✅ Form rendering: < 200ms
- ✅ Concurrent executions: No degradation

### **Security Requirements**
- ✅ Zero cross-project access
- ✅ 100% audit coverage
- ✅ Encrypted secret transmission
- ✅ Proper access control

The secret mapping implementation is **production-ready** when all tests pass and meet the success criteria! 🚀
