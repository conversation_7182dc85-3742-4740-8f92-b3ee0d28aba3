# 🚀 Production Deployment Guide

## 📋 Overview

The Deploy Orchestrator is now **production-ready** with the complete **Environment + Workflow architecture**. This guide covers deployment, configuration, and operational procedures.

## ✅ **Production-Ready Features**

### **🏗️ Core Architecture**
- ✅ **Environment Service** - Multi-cloud deployment management
- ✅ **Workflow Service** - Real-time execution with logging
- ✅ **Secrets Service** - Encrypted credential management
- ✅ **Admin Service** - Authentication and authorization
- ✅ **Gateway Service** - API routing and load balancing
- ✅ **Frontend** - Modern Angular UI with real-time updates

### **🔐 Security & Compliance**
- ✅ **JWT Authentication** - Secure token-based auth
- ✅ **Permission-based Access** - Fine-grained authorization
- ✅ **Secret Filtering** - Automatic sensitive data masking
- ✅ **Audit Logging** - Complete operation tracking
- ✅ **Encrypted Storage** - Secure credential storage

### **📊 Monitoring & Observability**
- ✅ **Health Checks** - Service and environment monitoring
- ✅ **Metrics Collection** - Prometheus integration
- ✅ **Structured Logging** - Centralized log management
- ✅ **Real-time Updates** - WebSocket-based streaming

## 🚀 **Quick Start**

### **1. Clone and Setup**
```bash
git clone <repository-url>
cd deploy-orchestrator

# Copy environment configuration
cp .env.example .env
# Edit .env with your configuration
```

### **2. Start Services**
```bash
# Start all services
docker-compose up -d

# Check service health
docker-compose ps
```

### **3. Access Application**
- **Frontend**: http://localhost:4200
- **API Gateway**: http://localhost:8000
- **Admin Panel**: http://localhost:4200/admin

### **4. Initial Setup**
```bash
# Create admin user (first time only)
curl -X POST http://localhost:8000/api/v1/admin-service/users \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "email": "<EMAIL>",
    "password": "secure-password",
    "role": "admin"
  }'
```

## 🔧 **Configuration**

### **Environment Variables**

#### **Core Configuration**
```bash
# Database
DATABASE_URL=**********************************/deploy_orchestrator?sslmode=require

# Authentication
JWT_SECRET_KEY=your-super-secure-jwt-secret-key-256-bits
ACCESS_TOKEN_EXPIRY=3600
REFRESH_TOKEN_EXPIRY=86400

# Services
GATEWAY_URL=https://api.yourdomain.com
ADMIN_SERVICE_URL=https://api.yourdomain.com/admin
ENVIRONMENT=production
```

#### **Service-Specific Configuration**
```bash
# Environment Service
ENVIRONMENT_SERVICE_PORT=8083
PROVIDER_TIMEOUT=30
HEALTH_CHECK_INTERVAL=60

# Workflow Service  
WORKFLOW_SERVICE_PORT=8084
MAX_CONCURRENT_EXECUTIONS=100
LOG_RETENTION_DAYS=30

# Secrets Service
SECRETS_SERVICE_PORT=8087
ENCRYPTION_KEY=your-32-byte-encryption-key
```

### **Provider Configuration**

#### **Google Cloud (GKE)**
```yaml
providers:
  gke:
    enabled: true
    timeout: 30
    default_config:
      project: "your-gcp-project"
      zone: "us-central1-a"
```

#### **Azure (AKS)**
```yaml
providers:
  aks:
    enabled: true
    timeout: 30
    default_config:
      subscription_id: "your-subscription-id"
      resource_group: "your-resource-group"
```

#### **AWS (EKS)**
```yaml
providers:
  eks:
    enabled: true
    timeout: 30
    default_config:
      region: "us-west-2"
```

## 🏭 **Production Deployment**

### **1. Infrastructure Requirements**

#### **Minimum Requirements**
- **CPU**: 4 cores
- **Memory**: 8GB RAM
- **Storage**: 50GB SSD
- **Network**: 1Gbps

#### **Recommended Production**
- **CPU**: 8+ cores
- **Memory**: 16GB+ RAM
- **Storage**: 100GB+ SSD
- **Network**: 10Gbps
- **Load Balancer**: Yes
- **Database**: Managed PostgreSQL

### **2. Database Setup**

#### **PostgreSQL Configuration**
```sql
-- Create database
CREATE DATABASE deploy_orchestrator;

-- Create user
CREATE USER deploy_user WITH PASSWORD 'secure-password';
GRANT ALL PRIVILEGES ON DATABASE deploy_orchestrator TO deploy_user;

-- Performance tuning
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
```

#### **Connection Pooling**
```yaml
database:
  url: "*********************************************/deploy_orchestrator"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300
```

### **3. Load Balancer Configuration**

#### **Nginx Configuration**
```nginx
upstream deploy_orchestrator {
    server app1:8000;
    server app2:8000;
    server app3:8000;
}

server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://deploy_orchestrator;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://deploy_orchestrator/health;
        access_log off;
    }
}
```

### **4. SSL/TLS Configuration**

#### **Certificate Setup**
```bash
# Using Let's Encrypt
certbot --nginx -d api.yourdomain.com

# Or use your own certificates
cp your-cert.pem /etc/ssl/certs/
cp your-key.pem /etc/ssl/private/
```

## 📊 **Monitoring & Alerting**

### **1. Health Checks**

#### **Service Health Endpoints**
```bash
# Gateway Service
curl https://api.yourdomain.com/health

# Environment Service
curl https://api.yourdomain.com/api/v1/environment-service/health

# Workflow Service
curl https://api.yourdomain.com/api/v1/workflow-service/health
```

#### **Database Health**
```bash
# Check database connectivity
curl https://api.yourdomain.com/api/v1/admin-service/health/database
```

### **2. Metrics Collection**

#### **Prometheus Configuration**
```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'deploy-orchestrator'
    static_configs:
      - targets: ['api.yourdomain.com:8000']
    metrics_path: '/metrics'
```

#### **Key Metrics to Monitor**
- **Request Rate**: HTTP requests per second
- **Response Time**: API response latency
- **Error Rate**: HTTP 4xx/5xx responses
- **Database Connections**: Active/idle connections
- **Workflow Executions**: Success/failure rates
- **Environment Health**: Provider connectivity

### **3. Alerting Rules**

#### **Critical Alerts**
```yaml
groups:
  - name: deploy-orchestrator
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"
          
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
```

## 🔐 **Security Hardening**

### **1. Network Security**

#### **Firewall Rules**
```bash
# Allow only necessary ports
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw deny 5432/tcp   # PostgreSQL (internal only)
ufw enable
```

#### **Service Isolation**
```yaml
# Docker network isolation
networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true
```

### **2. Authentication Security**

#### **JWT Configuration**
```yaml
auth:
  jwt_secret: "your-256-bit-secret-key"
  access_token_expiry: 900    # 15 minutes
  refresh_token_expiry: 86400 # 24 hours
  issuer: "deploy-orchestrator"
  audience: "api.yourdomain.com"
```

#### **Password Policy**
```yaml
password_policy:
  min_length: 12
  require_uppercase: true
  require_lowercase: true
  require_numbers: true
  require_symbols: true
  max_age_days: 90
```

### **3. Data Encryption**

#### **Secrets Encryption**
```yaml
encryption:
  algorithm: "AES-256-GCM"
  key_rotation_days: 30
  backup_keys: 3
```

## 🔄 **Backup & Recovery**

### **1. Database Backup**

#### **Automated Backup Script**
```bash
#!/bin/bash
# backup-db.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/postgres"
DB_NAME="deploy_orchestrator"

# Create backup
pg_dump -h postgres -U deploy_user -d $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/backup_$DATE.sql

# Keep only last 30 days
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete
```

#### **Backup Schedule**
```bash
# Add to crontab
0 2 * * * /scripts/backup-db.sh
```

### **2. Configuration Backup**
```bash
# Backup configuration files
tar -czf config-backup-$(date +%Y%m%d).tar.gz \
  backend/*/config/ \
  frontend/src/environments/ \
  docker-compose.yml \
  .env
```

### **3. Disaster Recovery**

#### **Recovery Procedure**
1. **Restore Database**
   ```bash
   gunzip backup_20241201_020000.sql.gz
   psql -h postgres -U deploy_user -d deploy_orchestrator < backup_20241201_020000.sql
   ```

2. **Restore Configuration**
   ```bash
   tar -xzf config-backup-20241201.tar.gz
   ```

3. **Restart Services**
   ```bash
   docker-compose down
   docker-compose up -d
   ```

## 📈 **Performance Optimization**

### **1. Database Optimization**

#### **Indexing Strategy**
```sql
-- Environment queries
CREATE INDEX idx_environments_project_id ON environments(project_id);
CREATE INDEX idx_environments_status ON environments(status);
CREATE INDEX idx_environments_provider_type ON environments((provider->>'type'));

-- Workflow executions
CREATE INDEX idx_executions_environment_id ON workflow_executions(environment_id);
CREATE INDEX idx_executions_status ON workflow_executions(status);
CREATE INDEX idx_executions_created_at ON workflow_executions(created_at);
```

### **2. Caching Strategy**

#### **Redis Configuration**
```yaml
redis:
  host: "redis"
  port: 6379
  db: 0
  ttl: 300  # 5 minutes
  
cache_keys:
  providers: "providers:list"
  environments: "env:{project_id}"
  health: "health:{env_id}"
```

### **3. Connection Pooling**
```yaml
database:
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300
  
http_client:
  timeout: 30
  max_idle_conns: 100
  max_conns_per_host: 10
```

## 🎯 **Production Checklist**

### **Pre-Deployment**
- [ ] Environment variables configured
- [ ] SSL certificates installed
- [ ] Database migrations applied
- [ ] Load balancer configured
- [ ] Monitoring setup complete
- [ ] Backup procedures tested

### **Post-Deployment**
- [ ] Health checks passing
- [ ] Metrics collection working
- [ ] Alerts configured
- [ ] User access tested
- [ ] Provider connections verified
- [ ] Workflow execution tested

### **Ongoing Operations**
- [ ] Monitor service health
- [ ] Review error logs daily
- [ ] Check backup integrity weekly
- [ ] Update security patches monthly
- [ ] Review access permissions quarterly

## 🆘 **Troubleshooting**

### **Common Issues**

#### **Service Won't Start**
```bash
# Check logs
docker-compose logs environment-service

# Check configuration
docker-compose config

# Restart service
docker-compose restart environment-service
```

#### **Database Connection Issues**
```bash
# Test database connectivity
docker-compose exec environment-service \
  psql $DATABASE_URL -c "SELECT 1"

# Check connection pool
curl http://localhost:8083/health
```

#### **Provider Connection Failures**
```bash
# Test provider connectivity
curl -X POST http://localhost:8083/api/v1/environment-service/providers/gke/validate-config \
  -H "Content-Type: application/json" \
  -d '{"project": "your-project", "zone": "us-central1-a"}'
```

### **Performance Issues**

#### **High Memory Usage**
```bash
# Check memory usage
docker stats

# Adjust memory limits
docker-compose up -d --scale environment-service=2
```

#### **Slow Database Queries**
```sql
-- Enable query logging
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 1000;

-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
```

## 📞 **Support**

### **Documentation**
- **[API Documentation](./API_REFERENCE.md)**
- **[Architecture Guide](./ENVIRONMENT_WORKFLOW_ARCHITECTURE.md)**
- **[Security Guide](./SECURITY.md)**

### **Monitoring Dashboards**
- **Grafana**: http://monitoring.yourdomain.com:3000
- **Prometheus**: http://monitoring.yourdomain.com:9090
- **Logs**: http://logs.yourdomain.com

### **Emergency Contacts**
- **Operations Team**: <EMAIL>
- **Development Team**: <EMAIL>
- **Security Team**: <EMAIL>

---

**Deploy Orchestrator is now production-ready!** 🚀

For additional support, please refer to the documentation or contact the development team.
