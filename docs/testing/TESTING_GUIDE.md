# Testing Guide

This guide covers comprehensive testing strategies for the Deploy Orchestrator environment service and provider system.

## Testing Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Unit Tests    │    │ Integration     │    │   E2E Tests     │
│                 │    │ Tests           │    │                 │
│ • Providers     │    │ • API Endpoints │    │ • Full Workflow │
│ • Validation    │    │ • Database      │    │ • UI Testing    │
│ • Services      │    │ • External APIs │    │ • Performance   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Test Categories

### 1. Unit Tests

#### Provider Tests
Test individual provider implementations:

```go
// aws_eks_provider_test.go
func TestAWSEKSProvider_GetMetadata(t *testing.T) {
    provider := NewAWSEKSProvider()
    metadata := provider.GetMetadata()
    
    assert.Equal(t, "aws-eks", metadata.Name)
    assert.Equal(t, "container-orchestration", metadata.Type)
    assert.Contains(t, metadata.Capabilities, "deploy")
}

func TestAWSEKSProvider_Validate(t *testing.T) {
    provider := NewAWSEKSProvider()
    
    tests := []struct {
        name        string
        config      map[string]interface{}
        expectError bool
    }{
        {
            name: "Valid configuration",
            config: map[string]interface{}{
                "region":            "us-west-2",
                "cluster_name":      "test-cluster",
                "access_key_id":     "AKIAIOSFODNN7EXAMPLE",
                "secret_access_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
            },
            expectError: false,
        },
        {
            name: "Invalid region",
            config: map[string]interface{}{
                "region": "invalid-region",
            },
            expectError: true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := provider.Validate(tt.config)
            if tt.expectError {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
            }
        })
    }
}
```

#### Validation Tests
Test validation rules and custom validators:

```go
// provider_validator_test.go
func TestProviderValidator_ValidateCustom(t *testing.T) {
    tests := []struct {
        name     string
        rule     ValidationRule
        config   map[string]interface{}
        expected bool
    }{
        {
            name: "Valid AWS region",
            rule: ValidationRule{
                Field:  "region",
                Type:   "string",
                Custom: "aws_region",
            },
            config:   map[string]interface{}{"region": "us-east-1"},
            expected: true,
        },
        {
            name: "Invalid AWS region",
            rule: ValidationRule{
                Field:  "region",
                Type:   "string",
                Custom: "aws_region",
            },
            config:   map[string]interface{}{"region": "invalid-region"},
            expected: false,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            validator := NewProviderValidator()
            validator.AddRule("test-provider", tt.rule)
            result := validator.Validate("test-provider", tt.config)
            assert.Equal(t, tt.expected, result.Valid)
        })
    }
}
```

#### Service Tests
Test business logic and service methods:

```go
// environment_service_test.go
func TestEnvironmentService_CreateEnvironment(t *testing.T) {
    // Setup test database
    db := setupTestDB(t)
    defer db.Close()
    
    service := NewEnvironmentService(db)
    
    request := CreateEnvironmentRequest{
        ProjectID: "test-project",
        Name:      "test-env",
        Type:      "kubernetes",
        Provider: ProviderConfig{
            Type: "aws-eks",
            Config: map[string]interface{}{
                "region":       "us-west-2",
                "cluster_name": "test-cluster",
            },
        },
    }
    
    env, err := service.CreateEnvironment(context.Background(), request)
    assert.NoError(t, err)
    assert.Equal(t, "test-env", env.Name)
    assert.Equal(t, "test-project", env.ProjectID)
}
```

### 2. Integration Tests

#### API Endpoint Tests
Test HTTP endpoints with real database:

```go
// api_test.go
func TestCreateEnvironmentAPI(t *testing.T) {
    // Setup test server
    server := setupTestServer(t)
    defer server.Close()
    
    payload := map[string]interface{}{
        "projectId": "test-project",
        "name":      "test-env",
        "type":      "kubernetes",
        "provider": map[string]interface{}{
            "type": "aws-eks",
            "config": map[string]interface{}{
                "region":       "us-west-2",
                "cluster_name": "test-cluster",
            },
        },
    }
    
    resp, err := http.Post(
        server.URL+"/api/v1/environments",
        "application/json",
        jsonBody(payload),
    )
    
    assert.NoError(t, err)
    assert.Equal(t, http.StatusCreated, resp.StatusCode)
    
    var env Environment
    json.NewDecoder(resp.Body).Decode(&env)
    assert.Equal(t, "test-env", env.Name)
}
```

#### Database Tests
Test database operations and migrations:

```go
// database_test.go
func TestEnvironmentRepository(t *testing.T) {
    db := setupTestDB(t)
    defer db.Close()
    
    repo := NewEnvironmentRepository(db)
    
    env := &Environment{
        ID:        "test-id",
        ProjectID: "test-project",
        Name:      "test-env",
        Type:      "kubernetes",
    }
    
    // Test Create
    err := repo.Create(env)
    assert.NoError(t, err)
    
    // Test Get
    retrieved, err := repo.GetByID("test-id")
    assert.NoError(t, err)
    assert.Equal(t, env.Name, retrieved.Name)
    
    // Test Update
    retrieved.Status = "running"
    err = repo.Update(retrieved)
    assert.NoError(t, err)
    
    // Test Delete
    err = repo.Delete("test-id")
    assert.NoError(t, err)
}
```

#### External API Tests
Test integration with external services (with mocking):

```go
// external_api_test.go
func TestAWSEKSIntegration(t *testing.T) {
    // Mock AWS EKS API
    server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        switch r.URL.Path {
        case "/clusters":
            w.WriteHeader(http.StatusOK)
            json.NewEncoder(w).Encode(map[string]interface{}{
                "clusters": []string{"test-cluster"},
            })
        default:
            w.WriteHeader(http.StatusNotFound)
        }
    }))
    defer server.Close()
    
    // Configure provider to use mock server
    provider := NewAWSEKSProvider()
    provider.SetEndpoint(server.URL)
    
    // Test provider operations
    status, err := provider.GetEnvironmentStatus(context.Background(), "test-env")
    assert.NoError(t, err)
    assert.Equal(t, "running", status.Status)
}
```

### 3. End-to-End Tests

#### Full Workflow Tests
Test complete user workflows:

```go
// e2e_test.go
func TestCompleteEnvironmentWorkflow(t *testing.T) {
    // Start test server
    server := startTestServer(t)
    defer server.Stop()
    
    client := NewTestClient(server.URL)
    
    // 1. Create project
    project, err := client.CreateProject("test-project")
    assert.NoError(t, err)
    
    // 2. Add secrets to project
    secret, err := client.CreateSecret(project.ID, "db-credentials", "vault")
    assert.NoError(t, err)
    
    // 3. Create environment
    env, err := client.CreateEnvironment(CreateEnvironmentRequest{
        ProjectID: project.ID,
        Name:      "test-env",
        Type:      "kubernetes",
        Provider: ProviderConfig{
            Type: "aws-eks",
            Config: map[string]interface{}{
                "region":       "us-west-2",
                "cluster_name": "test-cluster",
            },
        },
        SecretMappings: []SecretMapping{
            {
                SecretID:     secret.ID,
                VariableName: "DB_PASSWORD",
                Type:         "env",
            },
        },
    })
    assert.NoError(t, err)
    
    // 4. Verify environment status
    status, err := client.GetEnvironmentStatus(env.ID)
    assert.NoError(t, err)
    assert.Equal(t, "running", status.Status)
    
    // 5. Scale environment
    err = client.ScaleEnvironment(env.ID, ScalingConfig{
        Replicas: 3,
    })
    assert.NoError(t, err)
    
    // 6. Delete environment
    err = client.DeleteEnvironment(env.ID)
    assert.NoError(t, err)
}
```

#### Frontend E2E Tests
Test UI workflows with Cypress or Playwright:

```typescript
// cypress/integration/environment-config.spec.ts
describe('Environment Configuration', () => {
  beforeEach(() => {
    cy.login('admin', 'password');
    cy.visit('/environment-config');
  });

  it('should create AWS EKS environment', () => {
    // Select provider
    cy.get('[data-cy=provider-aws-eks]').click();
    
    // Fill configuration form
    cy.get('[data-cy=region]').select('us-west-2');
    cy.get('[data-cy=cluster-name]').type('test-cluster');
    cy.get('[data-cy=access-key-id]').type('AKIAIOSFODNN7EXAMPLE');
    cy.get('[data-cy=secret-access-key]').type('wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY');
    
    // Submit form
    cy.get('[data-cy=create-environment]').click();
    
    // Verify success
    cy.get('[data-cy=success-message]').should('contain', 'Environment created successfully');
  });

  it('should validate required fields', () => {
    cy.get('[data-cy=provider-aws-eks]').click();
    cy.get('[data-cy=create-environment]').click();
    
    cy.get('[data-cy=region-error]').should('contain', 'Region is required');
    cy.get('[data-cy=cluster-name-error]').should('contain', 'Cluster name is required');
  });
});
```

## Test Data Management

### Test Fixtures
Create reusable test data:

```go
// fixtures.go
func CreateTestEnvironment() *Environment {
    return &Environment{
        ID:        "test-env-" + uuid.New().String(),
        ProjectID: "test-project",
        Name:      "test-environment",
        Type:      "kubernetes",
        Provider: ProviderConfig{
            Type: "aws-eks",
            Config: map[string]interface{}{
                "region":       "us-west-2",
                "cluster_name": "test-cluster",
            },
        },
        Status:    "created",
        CreatedAt: time.Now(),
        UpdatedAt: time.Now(),
    }
}

func CreateTestProject() *Project {
    return &Project{
        ID:          "test-project-" + uuid.New().String(),
        Name:        "Test Project",
        Description: "Test project for unit tests",
        CreatedAt:   time.Now(),
        UpdatedAt:   time.Now(),
    }
}
```

### Database Seeding
Seed test database with consistent data:

```go
// test_helpers.go
func setupTestDB(t *testing.T) *gorm.DB {
    db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
    require.NoError(t, err)
    
    // Run migrations
    err = db.AutoMigrate(&Environment{}, &Project{}, &Secret{})
    require.NoError(t, err)
    
    // Seed test data
    seedTestData(db)
    
    return db
}

func seedTestData(db *gorm.DB) {
    projects := []*Project{
        {ID: "project-1", Name: "Test Project 1"},
        {ID: "project-2", Name: "Test Project 2"},
    }
    
    for _, project := range projects {
        db.Create(project)
    }
}
```

## Mocking and Stubbing

### Provider Mocking
Mock external provider APIs:

```go
// mock_provider.go
type MockAWSEKSProvider struct {
    environments map[string]*EnvironmentResult
    shouldFail   bool
}

func NewMockAWSEKSProvider() *MockAWSEKSProvider {
    return &MockAWSEKSProvider{
        environments: make(map[string]*EnvironmentResult),
    }
}

func (m *MockAWSEKSProvider) CreateEnvironment(ctx context.Context, config EnvironmentConfig) (*EnvironmentResult, error) {
    if m.shouldFail {
        return nil, errors.New("mock error")
    }
    
    result := &EnvironmentResult{
        ID:     config.Name + "-mock",
        Name:   config.Name,
        Status: "created",
    }
    
    m.environments[result.ID] = result
    return result, nil
}

func (m *MockAWSEKSProvider) SetShouldFail(fail bool) {
    m.shouldFail = fail
}
```

### HTTP Mocking
Mock external HTTP services:

```go
// http_mock.go
func setupMockServer() *httptest.Server {
    return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        switch {
        case strings.Contains(r.URL.Path, "/clusters"):
            handleClustersRequest(w, r)
        case strings.Contains(r.URL.Path, "/nodes"):
            handleNodesRequest(w, r)
        default:
            w.WriteHeader(http.StatusNotFound)
        }
    }))
}

func handleClustersRequest(w http.ResponseWriter, r *http.Request) {
    response := map[string]interface{}{
        "clusters": []map[string]interface{}{
            {
                "name":   "test-cluster",
                "status": "ACTIVE",
                "version": "1.28",
            },
        },
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(response)
}
```

## Performance Testing

### Load Testing
Test system performance under load:

```go
// load_test.go
func TestEnvironmentCreationLoad(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping load test in short mode")
    }
    
    server := setupTestServer(t)
    defer server.Close()
    
    concurrency := 10
    requests := 100
    
    var wg sync.WaitGroup
    errors := make(chan error, requests)
    
    for i := 0; i < concurrency; i++ {
        wg.Add(1)
        go func(workerID int) {
            defer wg.Done()
            
            for j := 0; j < requests/concurrency; j++ {
                err := createTestEnvironment(server.URL, fmt.Sprintf("env-%d-%d", workerID, j))
                if err != nil {
                    errors <- err
                }
            }
        }(i)
    }
    
    wg.Wait()
    close(errors)
    
    errorCount := 0
    for err := range errors {
        t.Logf("Error: %v", err)
        errorCount++
    }
    
    if errorCount > requests*0.05 { // Allow 5% error rate
        t.Errorf("Too many errors: %d/%d", errorCount, requests)
    }
}
```

### Benchmark Testing
Benchmark critical operations:

```go
// benchmark_test.go
func BenchmarkProviderValidation(b *testing.B) {
    validator := NewProviderValidator()
    rules := GetDefaultRules()
    validator.AddRules("aws-eks", rules["aws-eks"])
    
    config := map[string]interface{}{
        "region":            "us-west-2",
        "cluster_name":      "test-cluster",
        "access_key_id":     "AKIAIOSFODNN7EXAMPLE",
        "secret_access_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
    }
    
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        validator.Validate("aws-eks", config)
    }
}

func BenchmarkEnvironmentCreation(b *testing.B) {
    db := setupTestDB(b)
    service := NewEnvironmentService(db)
    
    request := CreateEnvironmentRequest{
        ProjectID: "test-project",
        Name:      "test-env",
        Type:      "kubernetes",
        Provider: ProviderConfig{
            Type: "mock",
            Config: map[string]interface{}{
                "endpoint": "http://mock.example.com",
            },
        },
    }
    
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        request.Name = fmt.Sprintf("test-env-%d", i)
        service.CreateEnvironment(context.Background(), request)
    }
}
```

## Test Execution

### Running Tests

```bash
# Run all tests
go test ./... -v

# Run unit tests only
go test ./internal/... -v

# Run integration tests
go test ./tests/integration/... -v

# Run with coverage
go test ./... -coverprofile=coverage.out
go tool cover -html=coverage.out

# Run specific test
go test ./internal/providers -run TestAWSEKSProvider_Validate -v

# Run benchmarks
go test ./... -bench=. -benchmem

# Run load tests
go test ./tests/load/... -v -timeout=30m
```

### Test Configuration

```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: '1.21'
      - run: go test ./internal/... -v -coverprofile=coverage.out
      - uses: codecov/codecov-action@v3

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: '1.21'
      - run: go test ./tests/integration/... -v

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run e2e
```

## Test Best Practices

### 1. Test Naming
```go
// Good: Descriptive test names
func TestAWSEKSProvider_CreateEnvironment_WithValidConfig_ReturnsSuccess(t *testing.T)
func TestProviderValidator_ValidateRegion_WithInvalidRegion_ReturnsError(t *testing.T)

// Bad: Vague test names
func TestCreate(t *testing.T)
func TestValidate(t *testing.T)
```

### 2. Test Structure
Follow the Arrange-Act-Assert pattern:

```go
func TestEnvironmentService_CreateEnvironment(t *testing.T) {
    // Arrange
    db := setupTestDB(t)
    service := NewEnvironmentService(db)
    request := CreateEnvironmentRequest{...}
    
    // Act
    result, err := service.CreateEnvironment(context.Background(), request)
    
    // Assert
    assert.NoError(t, err)
    assert.Equal(t, "test-env", result.Name)
}
```

### 3. Test Independence
Each test should be independent:

```go
func TestEnvironmentOperations(t *testing.T) {
    tests := []struct {
        name string
        test func(t *testing.T)
    }{
        {"Create", testCreateEnvironment},
        {"Update", testUpdateEnvironment},
        {"Delete", testDeleteEnvironment},
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Fresh setup for each test
            db := setupTestDB(t)
            defer db.Close()
            
            tt.test(t)
        })
    }
}
```

### 4. Error Testing
Test both success and failure cases:

```go
func TestProviderValidation(t *testing.T) {
    tests := []struct {
        name        string
        config      map[string]interface{}
        expectError bool
        errorCode   string
    }{
        {
            name:        "Valid config",
            config:      validConfig(),
            expectError: false,
        },
        {
            name:        "Missing required field",
            config:      configWithoutRegion(),
            expectError: true,
            errorCode:   "REQUIRED",
        },
        {
            name:        "Invalid region",
            config:      configWithInvalidRegion(),
            expectError: true,
            errorCode:   "INVALID_AWS_REGION",
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result := validator.Validate("aws-eks", tt.config)
            
            if tt.expectError {
                assert.False(t, result.Valid)
                assert.Contains(t, result.Errors[0].Code, tt.errorCode)
            } else {
                assert.True(t, result.Valid)
            }
        })
    }
}
```

### 5. Test Data Management
Use builders for complex test data:

```go
type EnvironmentBuilder struct {
    env *Environment
}

func NewEnvironmentBuilder() *EnvironmentBuilder {
    return &EnvironmentBuilder{
        env: &Environment{
            ID:        uuid.New().String(),
            Name:      "test-env",
            Type:      "kubernetes",
            Status:    "created",
            CreatedAt: time.Now(),
            UpdatedAt: time.Now(),
        },
    }
}

func (b *EnvironmentBuilder) WithName(name string) *EnvironmentBuilder {
    b.env.Name = name
    return b
}

func (b *EnvironmentBuilder) WithProvider(providerType string) *EnvironmentBuilder {
    b.env.Provider.Type = providerType
    return b
}

func (b *EnvironmentBuilder) Build() *Environment {
    return b.env
}

// Usage
env := NewEnvironmentBuilder().
    WithName("production-env").
    WithProvider("aws-eks").
    Build()
```

## Continuous Integration

### Test Pipeline
```yaml
# test-pipeline.yml
stages:
  - name: unit-tests
    commands:
      - go test ./internal/... -v -coverprofile=coverage.out
      - go tool cover -func=coverage.out
    
  - name: integration-tests
    depends_on: [unit-tests]
    commands:
      - docker-compose up -d postgres
      - go test ./tests/integration/... -v
    
  - name: e2e-tests
    depends_on: [integration-tests]
    commands:
      - docker-compose up -d
      - npm run e2e
    
  - name: performance-tests
    depends_on: [e2e-tests]
    commands:
      - go test ./tests/load/... -v -timeout=30m
```

### Quality Gates
- Minimum 80% code coverage
- All tests must pass
- No critical security vulnerabilities
- Performance benchmarks within acceptable range

For more information, see the [Provider Extension Guide](../providers/PROVIDER_EXTENSION_GUIDE.md) and [Validation Guide](../validation/VALIDATION_GUIDE.md).
