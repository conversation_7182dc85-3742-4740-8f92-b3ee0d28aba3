# Port Management Strategy

## Overview
This document outlines the comprehensive port management strategy to prevent collisions between core services, external environment providers, and plugins.

## Current Port Allocation

### Core Services (8000-8099)
- **8000**: Gateway Service (API Gateway)
- **8080**: Admin Service (Internal: 8080, External: 8086)
- **8081**: Environment Service
- **8082**: Secrets Service
- **8083**: Notification Service
- **8084**: Scheduling Service
- **8085**: ⚠️ **CONFLICT** - Both Workflow Service & Integration Service
- **8087**: Audit Service

### External Environment Providers (8100-8199)
- **8100**: GKE Environment Provider (currently 8090 - needs migration)
- **8101**: AKS Environment Provider (currently 8091 - needs migration)
- **8102**: EKS Environment Provider
- **8103**: OpenShift Environment Provider
- **8104**: Azure Container Instances Provider
- **8105**: Google Cloud Run Provider
- **8106**: Docker Swarm Provider
- **8107-8199**: Reserved for future environment providers

### Plugins (8200-8299)
- **8200**: Default plugin port (currently 8090 - needs migration)
- **8201**: Helm OpenShift Plugin
- **8202**: Bitbucket Plugin
- **8203**: Git Plugin
- **8204**: Jenkins Plugin
- **8205-8299**: Reserved for future plugins

### Infrastructure Services (5000-5999)
- **5432**: PostgreSQL
- **9092**: Kafka
- **2181**: Zookeeper
- **4200**: Frontend (development)

## Port Collision Prevention

### 1. Immediate Fixes Required

#### Fix Workflow Service Port Conflict
```yaml
# backend/workflow-service/config/config.yaml
server:
  port: 8088  # Change from 8085 to 8088
```

#### Migrate External Providers
```yaml
# backend/environment-service/config/config.yaml
external_providers:
  - name: "Google Kubernetes Engine"
    url: "http://localhost:8100"  # Change from 8090
    port: "8100"                  # Change from 8090
  - name: "Azure Kubernetes Service External"
    url: "http://localhost:8101"  # Change from 8091
    port: "8101"                  # Change from 8091
```

#### Update Plugin Default Port
```go
// backend/shared/plugin/server.go
func NewSimplePluginServer(plugin Plugin) *SimplePluginServer {
    port := os.Getenv("PLUGIN_PORT")
    if port == "" {
        port = "8200"  // Change from 8090
    }
    // ...
}
```

### 2. Dynamic Port Management

#### Environment Variable Override
All services support port override via environment variables:
```bash
# Core services
SERVER_PORT=8081
PLUGIN_PORT=8200

# External providers
GKE_PROVIDER_PORT=8100
AKS_PROVIDER_PORT=8101
```

#### Configuration-Based Port Assignment
```yaml
# config/port-registry.yaml
port_registry:
  core_services:
    range: "8000-8099"
    assignments:
      gateway: 8000
      admin: 8080
      environment: 8081
      secrets: 8082
      notification: 8083
      scheduling: 8084
      integration: 8085
      audit: 8087
      workflow: 8088

  external_providers:
    range: "8100-8199"
    auto_assign: true
    start_port: 8100

  plugins:
    range: "8200-8299"
    auto_assign: true
    start_port: 8200
```

### 3. Port Registry Service

#### Centralized Port Management
```go
type PortRegistry struct {
    allocatedPorts map[int]string
    ranges         map[string]PortRange
}

type PortRange struct {
    Start int
    End   int
    Type  string // "core", "provider", "plugin"
}

func (pr *PortRegistry) AllocatePort(serviceType, serviceName string) (int, error) {
    // Auto-allocate next available port in range
}

func (pr *PortRegistry) ReservePort(port int, serviceName string) error {
    // Reserve specific port
}
```

### 4. Docker Compose Port Mapping

#### Updated Port Mappings
```yaml
# docker-compose.yml
services:
  workflow-service:
    ports:
      - "8088:8088"  # Fix conflict

  # External providers (when containerized)
  gke-provider:
    ports:
      - "8100:8100"

  aks-provider:
    ports:
      - "8101:8101"
```

### 5. Health Check Integration

#### Port Availability Checks
```go
func CheckPortAvailability(port int) error {
    conn, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
    if err != nil {
        return fmt.Errorf("port %d is already in use", port)
    }
    conn.Close()
    return nil
}
```

#### Startup Validation
```go
func ValidatePortConfiguration(config *Config) error {
    // Check all configured ports are available
    // Validate no conflicts in port assignments
    // Ensure ports are within valid ranges
}
```

## Implementation Status

### ✅ Phase 1: Immediate Fixes (COMPLETED)
1. ✅ **Fixed workflow service port conflict** (8085 → 8088)
   - Updated `backend/workflow-service/config/config.go`
   - Updated `docker-compose.yml` port mapping
2. ✅ **Migrated external providers to 8100+ range**
   - GKE provider: 8090 → 8100
   - AKS provider: 8091 → 8101
   - Updated `backend/environment-service/config/config.yaml`
3. ✅ **Updated plugin default port to 8200+**
   - Updated `backend/shared/plugin/server.go` (8090 → 8200)
   - Updated GKE provider default port (8090 → 8100)
4. ✅ **Updated docker-compose.yml mappings**
   - Workflow service: 8085:8085 → 8088:8088

### 🔧 Phase 2: Enhanced Management (IN PROGRESS)
1. ✅ Port registry service design completed
2. ⏳ Configuration-based port assignment
3. ✅ Port availability validation tests created
4. ⏳ Health checks for port conflicts

### 📋 Phase 3: Advanced Features (PLANNED)
1. Dynamic port allocation
2. Port usage monitoring
3. Automatic conflict resolution
4. Port usage analytics

## Monitoring and Alerting

### Port Usage Metrics
- Track allocated vs available ports
- Monitor port conflicts
- Alert on port exhaustion in ranges

### Health Checks
- Validate all services are running on expected ports
- Check for port conflicts during startup
- Monitor external provider connectivity

## Best Practices

1. **Always use environment variables** for port configuration
2. **Reserve port ranges** for different service types
3. **Validate port availability** before service startup
4. **Document port assignments** in service documentation
5. **Use consistent port naming** conventions
6. **Implement graceful fallback** for port conflicts
7. **Monitor port usage** in production environments

## Conclusion

This strategy ensures:
- ✅ No port conflicts between services
- ✅ Scalable port management
- ✅ Easy service discovery
- ✅ Flexible deployment options
- ✅ Production-ready port allocation
