# Deploy Orchestrator Plugin System

## 🎯 **Overview**

The Deploy Orchestrator features a comprehensive plugin system that allows you to extend functionality without modifying the core application. The system supports **hot reload**, meaning plugins can be updated, reloaded, or replaced without restarting the service.

## 🔥 **Hot Reload Support - YES!**

**✅ The Deploy Orchestrator DOES support hot reload for plugins.**

### **Hot Reload Features:**

- **File Watching**: Automatically detects changes to plugin files
- **Debounced Reloading**: Prevents excessive reloads during rapid file changes
- **Zero Downtime**: Plugins reload without service interruption
- **Graceful Fallback**: If reload fails, the old version continues running
- **Real-time Monitoring**: Track reload events and status through API/UI

### **Supported File Types for Hot Reload:**
- `.go` files (Go source code)
- `.yaml`/`.yml` files (Configuration and templates)
- `.json` files (Configuration files)
- Plugin manifest (`plugin.yaml`)

## 📚 **Documentation Structure**

| Document | Purpose | Audience |
|----------|---------|----------|
| [Quick Start Guide](./QUICK_START.md) | 5-minute plugin creation | Beginners |
| [Plugin Development Guide](./PLUGIN_DEVELOPMENT_GUIDE.md) | Comprehensive development guide | Developers |
| [API Reference](./API_REFERENCE.md) | Complete API documentation | Advanced users |
| [Examples](../examples/) | Real-world plugin examples | All users |

## 🚀 **Quick Start**

### **1. Create Your First Plugin (5 minutes)**

```bash
# Create plugin directory
mkdir -p plugins/my-plugin
cd plugins/my-plugin

# Create plugin manifest
cat > plugin.yaml << EOF
apiVersion: v1
kind: Plugin
metadata:
  name: my-plugin
  version: 1.0.0
  description: "My first plugin"
spec:
  type: provider
  runtime: go
  entrypoint: main.go
  hotReload:
    enabled: true
    watchPaths: ["*.go", "*.yaml"]
EOF

# Create basic plugin implementation
cat > main.go << 'EOF'
package main

import (
    "context"
    "fmt"
    "github.com/claudio/deploy-orchestrator/workflow-service/internal/providers"
    "go.uber.org/zap"
)

type MyPlugin struct {
    logger *zap.Logger
}

func NewPlugin(config map[string]interface{}, logger *zap.Logger) (*MyPlugin, error) {
    return &MyPlugin{logger: logger}, nil
}

func (p *MyPlugin) GetExecutor() providers.ProviderExecutor {
    return &MyExecutor{logger: p.logger}
}

func (p *MyPlugin) Initialize(ctx context.Context) error {
    p.logger.Info("My plugin initialized!")
    return nil
}

// ... implement other required methods
EOF
```

### **2. Install Plugin**

```bash
# Install via API
curl -X POST http://localhost:8080/api/v1/plugins \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-plugin",
    "source": "./plugins/my-plugin",
    "enabled": true,
    "config": {}
  }'

# Or install via CLI
deploy-orchestrator plugin install my-plugin --source ./plugins/my-plugin
```

### **3. Test Hot Reload**

```bash
# Edit your plugin file
echo '// Updated plugin code' >> plugins/my-plugin/main.go

# Plugin automatically reloads within 2 seconds!
# Check reload status
curl http://localhost:8080/api/v1/plugins/my-plugin/status
```

## 🏗️ **Plugin Types**

### **1. Provider Plugins**
Extend cloud provider support (AWS Lambda, DigitalOcean, Heroku, etc.)

```yaml
spec:
  type: provider
  provider:
    type: my-cloud
    capabilities:
      - deploy
      - rollback
      - scaling
```

### **2. Step Executor Plugins**
Add custom workflow steps (notifications, integrations, etc.)

```yaml
spec:
  type: step-executor
  stepExecutor:
    supportedSteps:
      - notification
      - slack
      - email
```

### **3. Workflow Template Plugins**
Provide pre-built deployment patterns

```yaml
spec:
  type: template
  templates:
    - name: microservice-deploy
      category: deployment
```

## 🔧 **Installation Methods**

### **1. Local Development**
```bash
# Copy plugin to plugins directory
cp -r my-plugin/ /opt/deploy-orchestrator/plugins/

# Register via API
curl -X POST http://localhost:8080/api/v1/plugins -d '{...}'
```

### **2. CLI Installation**
```bash
# Install from local path
deploy-orchestrator plugin install --source ./my-plugin

# Install from Git repository
deploy-orchestrator plugin install git://github.com/user/my-plugin.git

# Install from registry
deploy-orchestrator plugin install registry://my-plugin:1.0.0
```

### **3. Package Manager**
```bash
# Install from archive
deploy-orchestrator plugin install file://./my-plugin.tar.gz

# Install with configuration
deploy-orchestrator plugin install my-plugin --config config.yaml
```

## 🔄 **Hot Reload Configuration**

### **Enable Hot Reload in Plugin Manifest**

```yaml
# plugin.yaml
spec:
  hotReload:
    enabled: true                    # Enable hot reload
    watchPaths:                      # Files to watch
      - "*.go"
      - "templates/*.yaml"
      - "config/*.json"
    excludePaths:                    # Files to ignore
      - "tests/*"
      - "*.test"
      - ".git/*"
    debounceInterval: "2s"           # Debounce rapid changes
    maxReloadAttempts: 3             # Max reload retries
```

### **Hot Reload Process**

1. **File Change Detection**: File system watcher detects changes
2. **Debouncing**: Wait for 2 seconds to batch rapid changes
3. **Validation**: Validate plugin manifest and configuration
4. **Graceful Shutdown**: Stop old plugin instance gracefully
5. **Reload**: Load new plugin version
6. **Registration**: Register with provider registry
7. **Health Check**: Verify plugin is working correctly

### **Hot Reload API**

```bash
# Manual reload
POST /api/v1/plugins/{name}/reload

# Check reload status
GET /api/v1/plugins/{name}/status

# Get reload history
GET /api/v1/plugins/{name}/logs
```

## 📊 **Plugin Management API**

### **Core Operations**

```bash
# List all plugins
GET /api/v1/plugins

# Install plugin
POST /api/v1/plugins

# Get plugin details
GET /api/v1/plugins/{name}

# Update configuration
PUT /api/v1/plugins/{name}/config

# Enable/disable
POST /api/v1/plugins/{name}/enable
POST /api/v1/plugins/{name}/disable

# Hot reload
POST /api/v1/plugins/{name}/reload

# Uninstall
DELETE /api/v1/plugins/{name}
```

### **Monitoring & Debugging**

```bash
# Plugin status
GET /api/v1/plugins/{name}/status

# Plugin logs
GET /api/v1/plugins/{name}/logs?lines=100&follow=true

# Health check
GET /api/v1/plugins/{name}/health
```

## 🛡️ **Security & Best Practices**

### **Security Features**
- **Sandboxed Execution**: Plugins run in isolated contexts
- **Permission System**: Fine-grained capability control
- **Credential Management**: Secure handling of sensitive data
- **Input Validation**: Comprehensive validation of plugin inputs
- **Audit Logging**: Complete audit trail of plugin operations

### **Best Practices**
- **Stateless Design**: Keep plugins stateless and idempotent
- **Error Handling**: Implement comprehensive error handling
- **Resource Management**: Properly manage resources and connections
- **Testing**: Write comprehensive unit and integration tests
- **Documentation**: Document plugin capabilities and configuration

## 🎯 **Examples**

### **Real-World Plugin Examples**

1. **[AWS Lambda Plugin](../examples/aws-lambda-plugin/)**
   - Lambda function deployment
   - Environment variable management
   - VPC configuration
   - Hot reload support

2. **[Notification Plugin](../examples/notification-plugin/)**
   - Slack notifications
   - Email notifications
   - Webhook notifications
   - Custom templates

3. **[Database Migration Plugin](../examples/db-migration-plugin/)**
   - Schema migrations
   - Data migrations
   - Rollback capabilities
   - Multi-database support

## 🚀 **Getting Started**

1. **Read the [Quick Start Guide](./QUICK_START.md)** - Create your first plugin in 5 minutes
2. **Follow the [Plugin Development Guide](./PLUGIN_DEVELOPMENT_GUIDE.md)** - Comprehensive development guide
3. **Explore [Examples](../examples/)** - Real-world plugin implementations
4. **Join the Community** - Get help and share your plugins

## 📈 **Plugin Ecosystem**

The Deploy Orchestrator plugin system is designed to foster a rich ecosystem of community-contributed plugins. Share your plugins, contribute to existing ones, and help build the future of deployment automation!

---

## 🎉 **Summary**

**YES, the Deploy Orchestrator supports hot reload!** 

✅ **File watching and automatic reload**  
✅ **Zero-downtime plugin updates**  
✅ **Comprehensive plugin management API**  
✅ **Multiple installation methods**  
✅ **Production-ready security features**  
✅ **Rich development tools and examples**  

Start building your plugins today and extend the Deploy Orchestrator to meet your specific deployment needs!
