# Deploy Orchestrator Plugin Development Guide

## 📋 **Table of Contents**

1. [Overview](#overview)
2. [Plugin Architecture](#plugin-architecture)
3. [Writing Provider Plugins](#writing-provider-plugins)
4. [Writing Step Executor Plugins](#writing-step-executor-plugins)
5. [Plugin Installation](#plugin-installation)
6. [Hot Reload Support](#hot-reload-support)
7. [Testing Plugins](#testing-plugins)
8. [Best Practices](#best-practices)
9. [Examples](#examples)

## 🎯 **Overview**

The Deploy Orchestrator supports a plugin-based architecture that allows you to extend functionality by adding:

- **Provider Executors**: Support for new cloud providers (AWS Lambda, DigitalOcean, etc.)
- **Step Executors**: Custom workflow step types (notifications, integrations, etc.)
- **Workflow Templates**: Pre-built deployment patterns
- **Custom Validators**: Configuration and deployment validation

### **Plugin Types**

| Plugin Type | Purpose | Hot Reload | Examples |
|-------------|---------|------------|----------|
| Provider Executor | Cloud provider integration | ✅ Yes | AWS Lambda, DigitalOcean, Heroku |
| Step Executor | Custom workflow steps | ✅ Yes | Slack notifications, Jira integration |
| Workflow Template | Pre-built workflows | ✅ Yes | Microservice deployment, Database migration |
| Validator | Custom validation logic | ✅ Yes | Security scanning, Compliance checks |

## 🏗️ **Plugin Architecture**

### **Plugin Structure**

```
plugins/
├── my-provider-plugin/
│   ├── plugin.yaml              # Plugin manifest
│   ├── main.go                  # Plugin entry point
│   ├── executor.go              # Provider implementation
│   ├── templates/               # Workflow templates
│   │   ├── basic-deploy.yaml
│   │   └── blue-green.yaml
│   ├── tests/                   # Plugin tests
│   │   └── executor_test.go
│   └── README.md               # Plugin documentation
```

### **Plugin Manifest (plugin.yaml)**

```yaml
apiVersion: v1
kind: Plugin
metadata:
  name: my-provider-plugin
  version: 1.0.0
  description: "Custom provider for XYZ cloud platform"
  author: "Your Name <<EMAIL>>"
  license: "MIT"

spec:
  type: provider                    # provider, step-executor, template, validator
  runtime: go                       # go, javascript, python, binary
  entrypoint: main.go              # Entry point file

  # Provider-specific configuration
  provider:
    type: xyz-cloud                 # Provider type identifier
    capabilities:
      - deploy
      - rollback
      - scaling
      - health-checks
      - logs

  # Dependencies
  dependencies:
    - name: kubernetes
      version: ">=1.20.0"
    - name: helm
      version: ">=3.0.0"

  # Configuration schema
  configSchema:
    type: object
    properties:
      apiKey:
        type: string
        description: "API key for XYZ cloud"
        required: true
        sensitive: true
      region:
        type: string
        description: "Deployment region"
        default: "us-east-1"

  # Resource requirements
  resources:
    memory: "128Mi"
    cpu: "100m"

  # Hot reload configuration
  hotReload:
    enabled: true
    watchPaths:
      - "*.go"
      - "templates/*.yaml"
    excludePaths:
      - "tests/*"
      - "*.test"
```

## 🔌 **Writing Provider Plugins**

### **Step 1: Create Plugin Structure**

```bash
mkdir -p plugins/xyz-cloud-plugin/{templates,tests}
cd plugins/xyz-cloud-plugin
```

### **Step 2: Implement Provider Executor**

```go
// executor.go
package main

import (
    "context"
    "fmt"
    "time"

    "github.com/claudio/deploy-orchestrator/shared/models"
    "github.com/claudio/deploy-orchestrator/workflow-service/internal/providers"
    "go.uber.org/zap"
)

// XYZCloudExecutor implements the ProviderExecutor interface
type XYZCloudExecutor struct {
    providers.BaseExecutor
    logger    *zap.Logger
    apiClient *XYZCloudClient
}

// NewXYZCloudExecutor creates a new XYZ Cloud executor
func NewXYZCloudExecutor(logger *zap.Logger, config map[string]interface{}) (*XYZCloudExecutor, error) {
    apiKey, ok := config["apiKey"].(string)
    if !ok {
        return nil, fmt.Errorf("apiKey is required")
    }

    region, ok := config["region"].(string)
    if !ok {
        region = "us-east-1"
    }

    client, err := NewXYZCloudClient(apiKey, region)
    if err != nil {
        return nil, fmt.Errorf("failed to create XYZ Cloud client: %w", err)
    }

    return &XYZCloudExecutor{
        BaseExecutor: providers.BaseExecutor{
            ProviderType: "xyz-cloud",
        },
        logger:    logger,
        apiClient: client,
    }, nil
}

// GetCapabilities returns supported capabilities
func (e *XYZCloudExecutor) GetCapabilities() []string {
    return []string{
        providers.CapabilityDeploy,
        providers.CapabilityRollback,
        providers.CapabilityScaling,
        providers.CapabilityHealthChecks,
        providers.CapabilityLogs,
    }
}

// ValidateConfig validates the provider configuration
func (e *XYZCloudExecutor) ValidateConfig(config *models.ProviderConfig) error {
    if config.Endpoint == "" {
        return fmt.Errorf("endpoint is required for XYZ Cloud")
    }

    // Validate API credentials
    if err := e.apiClient.ValidateCredentials(); err != nil {
        return fmt.Errorf("invalid credentials: %w", err)
    }

    return nil
}

// GenerateSteps generates workflow steps for deployment
func (e *XYZCloudExecutor) GenerateSteps(deployment *providers.DeploymentRequest) ([]*models.WorkflowStep, error) {
    steps := []*models.WorkflowStep{
        {
            Name: "validate-app",
            Type: "xyz_validate",
            Config: map[string]interface{}{
                "appName": deployment.Configuration["appName"],
                "region":  deployment.Environment.Provider.Config.Region,
            },
        },
        {
            Name: "deploy-app",
            Type: "xyz_deploy",
            Config: map[string]interface{}{
                "appName":     deployment.Configuration["appName"],
                "version":     deployment.Version.Tag,
                "environment": deployment.Environment.Name,
                "variables":   deployment.Variables,
            },
            DependsOn: []string{"validate-app"},
        },
        {
            Name: "health-check",
            Type: "xyz_health_check",
            Config: map[string]interface{}{
                "appName": deployment.Configuration["appName"],
                "timeout": "300s",
            },
            DependsOn: []string{"deploy-app"},
        },
    }

    return steps, nil
}

// Execute performs the deployment step
func (e *XYZCloudExecutor) Execute(ctx context.Context, request *providers.ExecutionRequest) (*providers.ExecutionResult, error) {
    e.LogInfo(request, fmt.Sprintf("Starting XYZ Cloud execution: %s", request.StepType))

    switch request.StepType {
    case "xyz_validate":
        return e.executeValidate(ctx, request)
    case "xyz_deploy":
        return e.executeDeploy(ctx, request)
    case "xyz_health_check":
        return e.executeHealthCheck(ctx, request)
    default:
        return nil, fmt.Errorf("unsupported step type: %s", request.StepType)
    }
}

// executeValidate validates the application
func (e *XYZCloudExecutor) executeValidate(ctx context.Context, request *providers.ExecutionRequest) (*providers.ExecutionResult, error) {
    appName := request.Config["appName"].(string)

    e.LogInfo(request, fmt.Sprintf("Validating application: %s", appName))

    // Perform validation using XYZ Cloud API
    if err := e.apiClient.ValidateApp(ctx, appName); err != nil {
        return &providers.ExecutionResult{
            Success:      false,
            ErrorMessage: fmt.Sprintf("Validation failed: %s", err.Error()),
        }, nil
    }

    e.LogInfo(request, "Application validation successful")

    return &providers.ExecutionResult{
        Success: true,
        Output: map[string]interface{}{
            "validated": true,
            "appName":   appName,
        },
    }, nil
}

// executeDeploy performs the deployment
func (e *XYZCloudExecutor) executeDeploy(ctx context.Context, request *providers.ExecutionRequest) (*providers.ExecutionResult, error) {
    appName := request.Config["appName"].(string)
    version := request.Config["version"].(string)

    e.LogInfo(request, fmt.Sprintf("Deploying %s version %s", appName, version))

    // Perform deployment
    deploymentID, err := e.apiClient.Deploy(ctx, &DeploymentRequest{
        AppName:     appName,
        Version:     version,
        Environment: request.Config["environment"].(string),
        Variables:   request.Variables,
    })
    if err != nil {
        return &providers.ExecutionResult{
            Success:      false,
            ErrorMessage: fmt.Sprintf("Deployment failed: %s", err.Error()),
        }, nil
    }

    e.LogInfo(request, fmt.Sprintf("Deployment started with ID: %s", deploymentID))

    return &providers.ExecutionResult{
        Success: true,
        Output: map[string]interface{}{
            "deploymentId": deploymentID,
            "appName":      appName,
            "version":      version,
        },
        Metrics: map[string]float64{
            "deploymentTime": 45.2,
        },
    }, nil
}

// executeHealthCheck performs health checks
func (e *XYZCloudExecutor) executeHealthCheck(ctx context.Context, request *providers.ExecutionRequest) (*providers.ExecutionResult, error) {
    appName := request.Config["appName"].(string)

    e.LogInfo(request, fmt.Sprintf("Performing health check for: %s", appName))

    // Check application health
    health, err := e.apiClient.GetAppHealth(ctx, appName)
    if err != nil {
        return &providers.ExecutionResult{
            Success:      false,
            ErrorMessage: fmt.Sprintf("Health check failed: %s", err.Error()),
        }, nil
    }

    if health.Status != "healthy" {
        return &providers.ExecutionResult{
            Success:      false,
            ErrorMessage: fmt.Sprintf("Application is not healthy: %s", health.Message),
        }, nil
    }

    e.LogInfo(request, "Health check passed")

    return &providers.ExecutionResult{
        Success: true,
        Output: map[string]interface{}{
            "health": health,
        },
    }, nil
}

// GetStatus checks deployment status
func (e *XYZCloudExecutor) GetStatus(ctx context.Context, deploymentID string) (*providers.DeploymentStatus, error) {
    status, err := e.apiClient.GetDeploymentStatus(ctx, deploymentID)
    if err != nil {
        return nil, err
    }

    return &providers.DeploymentStatus{
        ID:          deploymentID,
        Status:      status.Status,
        Phase:       status.Phase,
        Progress:    status.Progress,
        Message:     status.Message,
        LastUpdated: time.Now().Format(time.RFC3339),
    }, nil
}

// Rollback performs rollback
func (e *XYZCloudExecutor) Rollback(ctx context.Context, deploymentID string, targetVersion string) error {
    return e.apiClient.Rollback(ctx, deploymentID, targetVersion)
}

// GetLogs retrieves logs
func (e *XYZCloudExecutor) GetLogs(ctx context.Context, deploymentID string, options *providers.LogOptions) ([]*models.LogEntry, error) {
    logs, err := e.apiClient.GetLogs(ctx, deploymentID, options)
    if err != nil {
        return nil, err
    }

    // Convert to LogEntry format
    entries := make([]*models.LogEntry, len(logs))
    for i, log := range logs {
        entries[i] = &models.LogEntry{
            ID:        log.ID,
            Timestamp: log.Timestamp,
            Level:     log.Level,
            Message:   log.Message,
            Source:    "xyz-cloud",
        }
    }

    return entries, nil
}

// Cleanup performs cleanup
func (e *XYZCloudExecutor) Cleanup(ctx context.Context, deploymentID string) error {
    return e.apiClient.Cleanup(ctx, deploymentID)
}
```

### **Step 3: Plugin Entry Point**

```go
// main.go
package main

import (
    "context"
    "encoding/json"
    "fmt"
    "os"

    "github.com/claudio/deploy-orchestrator/workflow-service/internal/providers"
    "go.uber.org/zap"
)

// PluginInfo contains plugin metadata
type PluginInfo struct {
    Name        string `json:"name"`
    Version     string `json:"version"`
    Type        string `json:"type"`
    Description string `json:"description"`
}

// Plugin represents the plugin instance
type Plugin struct {
    info     PluginInfo
    executor *XYZCloudExecutor
    logger   *zap.Logger
}

// NewPlugin creates a new plugin instance
func NewPlugin(config map[string]interface{}, logger *zap.Logger) (*Plugin, error) {
    executor, err := NewXYZCloudExecutor(logger, config)
    if err != nil {
        return nil, err
    }

    return &Plugin{
        info: PluginInfo{
            Name:        "xyz-cloud-plugin",
            Version:     "1.0.0",
            Type:        "provider",
            Description: "XYZ Cloud provider plugin",
        },
        executor: executor,
        logger:   logger,
    }, nil
}

// GetInfo returns plugin information
func (p *Plugin) GetInfo() PluginInfo {
    return p.info
}

// GetExecutor returns the provider executor
func (p *Plugin) GetExecutor() providers.ProviderExecutor {
    return p.executor
}

// Initialize initializes the plugin
func (p *Plugin) Initialize(ctx context.Context) error {
    p.logger.Info("Initializing XYZ Cloud plugin")

    // Perform any initialization logic
    if err := p.executor.apiClient.Connect(); err != nil {
        return fmt.Errorf("failed to connect to XYZ Cloud: %w", err)
    }

    p.logger.Info("XYZ Cloud plugin initialized successfully")
    return nil
}

// Shutdown gracefully shuts down the plugin
func (p *Plugin) Shutdown(ctx context.Context) error {
    p.logger.Info("Shutting down XYZ Cloud plugin")

    if err := p.executor.apiClient.Disconnect(); err != nil {
        p.logger.Error("Error disconnecting from XYZ Cloud", zap.Error(err))
    }

    return nil
}

// Health checks plugin health
func (p *Plugin) Health(ctx context.Context) error {
    return p.executor.apiClient.Ping()
}

// main is the plugin entry point (for standalone execution)
func main() {
    if len(os.Args) < 2 {
        fmt.Fprintf(os.Stderr, "Usage: %s <command>\n", os.Args[0])
        os.Exit(1)
    }

    command := os.Args[1]

    switch command {
    case "info":
        info := PluginInfo{
            Name:        "xyz-cloud-plugin",
            Version:     "1.0.0",
            Type:        "provider",
            Description: "XYZ Cloud provider plugin",
        }

        data, _ := json.MarshalIndent(info, "", "  ")
        fmt.Println(string(data))

    case "validate":
        // Validate plugin configuration
        fmt.Println("Plugin validation successful")

    default:
        fmt.Fprintf(os.Stderr, "Unknown command: %s\n", command)
        os.Exit(1)
    }
}
```

### **Step 4: Create Workflow Templates**

```yaml
# templates/basic-deploy.yaml
apiVersion: v1
kind: WorkflowTemplate
metadata:
  name: xyz-cloud-basic-deploy
  description: "Basic deployment to XYZ Cloud"
  provider: xyz-cloud
  category: deployment

spec:
  parameters:
    - name: appName
      type: string
      description: "Application name"
      required: true
    - name: version
      type: string
      description: "Version to deploy"
      required: true
    - name: environment
      type: string
      description: "Target environment"
      default: "staging"

  steps:
    - name: validate-app
      type: xyz_validate
      config:
        appName: "{{.appName}}"
        region: "{{.environment.region}}"

    - name: deploy-app
      type: xyz_deploy
      dependsOn: ["validate-app"]
      config:
        appName: "{{.appName}}"
        version: "{{.version}}"
        environment: "{{.environment}}"

    - name: health-check
      type: xyz_health_check
      dependsOn: ["deploy-app"]
      config:
        appName: "{{.appName}}"
        timeout: "300s"

    - name: notify-success
      type: notification
      dependsOn: ["health-check"]
      config:
        type: slack
        message: "✅ Successfully deployed {{.appName}} v{{.version}} to {{.environment}}"
```

## 🔧 **Writing Step Executor Plugins**

### **Step Executor Example**

```go
// notification_executor.go
package main

import (
    "context"
    "fmt"

    "github.com/claudio/deploy-orchestrator/workflow-service/internal/providers"
    "go.uber.org/zap"
)

// NotificationExecutor implements custom notification steps
type NotificationExecutor struct {
    logger *zap.Logger
}

// NewNotificationExecutor creates a new notification executor
func NewNotificationExecutor(logger *zap.Logger) *NotificationExecutor {
    return &NotificationExecutor{
        logger: logger,
    }
}

// Execute executes a notification step
func (e *NotificationExecutor) Execute(ctx context.Context, request *providers.ExecutionRequest) (*providers.ExecutionResult, error) {
    notificationType := request.Config["type"].(string)
    message := request.Config["message"].(string)

    switch notificationType {
    case "slack":
        return e.sendSlackNotification(ctx, message, request.Config)
    case "email":
        return e.sendEmailNotification(ctx, message, request.Config)
    case "webhook":
        return e.sendWebhookNotification(ctx, message, request.Config)
    default:
        return nil, fmt.Errorf("unsupported notification type: %s", notificationType)
    }
}

// GetSupportedStepTypes returns supported step types
func (e *NotificationExecutor) GetSupportedStepTypes() []string {
    return []string{"notification", "slack", "email", "webhook"}
}

// ValidateStep validates step configuration
func (e *NotificationExecutor) ValidateStep(step *models.WorkflowStep) error {
    if step.Config["type"] == nil {
        return fmt.Errorf("notification type is required")
    }

    if step.Config["message"] == nil {
        return fmt.Errorf("message is required")
    }

    return nil
}

// sendSlackNotification sends a Slack notification
func (e *NotificationExecutor) sendSlackNotification(ctx context.Context, message string, config map[string]interface{}) (*providers.ExecutionResult, error) {
    webhookURL := config["webhookUrl"].(string)
    channel := config["channel"].(string)

    // Send Slack notification
    if err := sendSlackMessage(webhookURL, channel, message); err != nil {
        return &providers.ExecutionResult{
            Success:      false,
            ErrorMessage: fmt.Sprintf("Failed to send Slack notification: %s", err.Error()),
        }, nil
    }

    return &providers.ExecutionResult{
        Success: true,
        Output: map[string]interface{}{
            "notificationType": "slack",
            "channel":          channel,
            "message":          message,
        },
    }, nil
}
```

## 📦 **Plugin Installation**

### **Manual Installation**

1. **Copy Plugin to Plugins Directory**
```bash
# Copy plugin to the plugins directory
cp -r my-plugin/ /opt/deploy-orchestrator/plugins/

# Set proper permissions
chmod +x /opt/deploy-orchestrator/plugins/my-plugin/main.go
```

2. **Register Plugin**
```bash
# Register the plugin with the orchestrator
curl -X POST http://localhost:8080/api/v1/plugins \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-plugin",
    "path": "/opt/deploy-orchestrator/plugins/my-plugin",
    "enabled": true,
    "config": {
      "apiKey": "your-api-key",
      "region": "us-east-1"
    }
  }'
```

### **Automated Installation via CLI**

```bash
# Install plugin using the CLI
deploy-orchestrator plugin install \
  --name my-plugin \
  --source ./my-plugin \
  --config config.yaml \
  --enable

# List installed plugins
deploy-orchestrator plugin list

# Enable/disable plugin
deploy-orchestrator plugin enable my-plugin
deploy-orchestrator plugin disable my-plugin

# Update plugin
deploy-orchestrator plugin update my-plugin --source ./my-plugin-v2

# Remove plugin
deploy-orchestrator plugin remove my-plugin
```

### **Installation via Package Manager**

```bash
# Install from registry
deploy-orchestrator plugin install registry://xyz-cloud-plugin:1.0.0

# Install from Git repository
deploy-orchestrator plugin install git://github.com/user/xyz-cloud-plugin.git

# Install from local archive
deploy-orchestrator plugin install file://./xyz-cloud-plugin.tar.gz
```

## 🔄 **Hot Reload Support**

The Deploy Orchestrator supports hot reloading of plugins without service restart.

### **Enabling Hot Reload**

```yaml
# plugin.yaml
spec:
  hotReload:
    enabled: true
    watchPaths:
      - "*.go"
      - "templates/*.yaml"
      - "config/*.json"
    excludePaths:
      - "tests/*"
      - "*.test"
      - ".git/*"
    debounceInterval: "2s"
    maxReloadAttempts: 3
```

### **Hot Reload Implementation**

```go
// hot_reload.go
package main

import (
    "context"
    "path/filepath"
    "time"

    "github.com/fsnotify/fsnotify"
    "go.uber.org/zap"
)

// HotReloadManager manages plugin hot reloading
type HotReloadManager struct {
    pluginPath string
    watcher    *fsnotify.Watcher
    logger     *zap.Logger
    reloadChan chan struct{}
}

// NewHotReloadManager creates a new hot reload manager
func NewHotReloadManager(pluginPath string, logger *zap.Logger) (*HotReloadManager, error) {
    watcher, err := fsnotify.NewWatcher()
    if err != nil {
        return nil, err
    }

    return &HotReloadManager{
        pluginPath: pluginPath,
        watcher:    watcher,
        logger:     logger,
        reloadChan: make(chan struct{}, 1),
    }, nil
}

// Start starts watching for file changes
func (h *HotReloadManager) Start(ctx context.Context) error {
    // Add plugin directory to watcher
    if err := h.watcher.Add(h.pluginPath); err != nil {
        return err
    }

    // Watch for changes
    go h.watchLoop(ctx)

    return nil
}

// watchLoop watches for file system events
func (h *HotReloadManager) watchLoop(ctx context.Context) {
    debounceTimer := time.NewTimer(0)
    debounceTimer.Stop()

    for {
        select {
        case <-ctx.Done():
            return

        case event, ok := <-h.watcher.Events:
            if !ok {
                return
            }

            // Check if file should trigger reload
            if h.shouldReload(event.Name) {
                h.logger.Info("File changed, scheduling reload",
                    zap.String("file", event.Name))

                // Debounce rapid file changes
                debounceTimer.Reset(2 * time.Second)
            }

        case err, ok := <-h.watcher.Errors:
            if !ok {
                return
            }
            h.logger.Error("Watcher error", zap.Error(err))

        case <-debounceTimer.C:
            // Trigger reload
            select {
            case h.reloadChan <- struct{}{}:
            default:
                // Channel full, reload already pending
            }
        }
    }
}

// shouldReload checks if file change should trigger reload
func (h *HotReloadManager) shouldReload(filename string) bool {
    ext := filepath.Ext(filename)

    // Watch Go files, YAML templates, JSON configs
    watchExtensions := []string{".go", ".yaml", ".yml", ".json"}

    for _, watchExt := range watchExtensions {
        if ext == watchExt {
            return true
        }
    }

    return false
}

// ReloadChan returns the reload notification channel
func (h *HotReloadManager) ReloadChan() <-chan struct{} {
    return h.reloadChan
}

// Stop stops the hot reload manager
func (h *HotReloadManager) Stop() error {
    return h.watcher.Close()
}
```

### **Plugin Manager with Hot Reload**

```go
// plugin_manager.go
package main

import (
    "context"
    "fmt"
    "sync"
    "time"

    "go.uber.org/zap"
)

// PluginManager manages plugin lifecycle with hot reload
type PluginManager struct {
    plugins     map[string]*PluginInstance
    registry    *providers.ProviderRegistry
    logger      *zap.Logger
    mutex       sync.RWMutex
    hotReload   bool
}

// PluginInstance represents a loaded plugin instance
type PluginInstance struct {
    Plugin      *Plugin
    Config      map[string]interface{}
    Path        string
    HotReload   *HotReloadManager
    LastReload  time.Time
    ReloadCount int
}

// NewPluginManager creates a new plugin manager
func NewPluginManager(registry *providers.ProviderRegistry, logger *zap.Logger) *PluginManager {
    return &PluginManager{
        plugins:   make(map[string]*PluginInstance),
        registry:  registry,
        logger:    logger,
        hotReload: true,
    }
}

// LoadPlugin loads a plugin with hot reload support
func (pm *PluginManager) LoadPlugin(ctx context.Context, name, path string, config map[string]interface{}) error {
    pm.mutex.Lock()
    defer pm.mutex.Unlock()

    // Create plugin instance
    plugin, err := NewPlugin(config, pm.logger)
    if err != nil {
        return fmt.Errorf("failed to create plugin: %w", err)
    }

    // Initialize plugin
    if err := plugin.Initialize(ctx); err != nil {
        return fmt.Errorf("failed to initialize plugin: %w", err)
    }

    // Register with provider registry
    if err := pm.registry.RegisterProvider(plugin.GetExecutor()); err != nil {
        return fmt.Errorf("failed to register provider: %w", err)
    }

    // Set up hot reload if enabled
    var hotReloadManager *HotReloadManager
    if pm.hotReload {
        hotReloadManager, err = NewHotReloadManager(path, pm.logger)
        if err != nil {
            pm.logger.Warn("Failed to set up hot reload", zap.Error(err))
        } else {
            if err := hotReloadManager.Start(ctx); err != nil {
                pm.logger.Warn("Failed to start hot reload", zap.Error(err))
            } else {
                // Start reload handler
                go pm.handleReload(ctx, name, hotReloadManager)
            }
        }
    }

    // Store plugin instance
    pm.plugins[name] = &PluginInstance{
        Plugin:      plugin,
        Config:      config,
        Path:        path,
        HotReload:   hotReloadManager,
        LastReload:  time.Now(),
        ReloadCount: 0,
    }

    pm.logger.Info("Plugin loaded successfully",
        zap.String("name", name),
        zap.Bool("hotReload", hotReloadManager != nil))

    return nil
}

// handleReload handles plugin hot reload
func (pm *PluginManager) handleReload(ctx context.Context, name string, hotReload *HotReloadManager) {
    for {
        select {
        case <-ctx.Done():
            return

        case <-hotReload.ReloadChan():
            pm.logger.Info("Hot reloading plugin", zap.String("name", name))

            if err := pm.reloadPlugin(ctx, name); err != nil {
                pm.logger.Error("Failed to hot reload plugin",
                    zap.String("name", name),
                    zap.Error(err))
            } else {
                pm.logger.Info("Plugin hot reloaded successfully",
                    zap.String("name", name))
            }
        }
    }
}

// reloadPlugin reloads a plugin
func (pm *PluginManager) reloadPlugin(ctx context.Context, name string) error {
    pm.mutex.Lock()
    defer pm.mutex.Unlock()

    instance, exists := pm.plugins[name]
    if !exists {
        return fmt.Errorf("plugin not found: %s", name)
    }

    // Shutdown old plugin
    if err := instance.Plugin.Shutdown(ctx); err != nil {
        pm.logger.Warn("Error shutting down plugin", zap.Error(err))
    }

    // Create new plugin instance
    newPlugin, err := NewPlugin(instance.Config, pm.logger)
    if err != nil {
        return fmt.Errorf("failed to create new plugin instance: %w", err)
    }

    // Initialize new plugin
    if err := newPlugin.Initialize(ctx); err != nil {
        return fmt.Errorf("failed to initialize new plugin: %w", err)
    }

    // Update registry (this would need to support replacement)
    // pm.registry.ReplaceProvider(name, newPlugin.GetExecutor())

    // Update instance
    instance.Plugin = newPlugin
    instance.LastReload = time.Now()
    instance.ReloadCount++

    return nil
}

// UnloadPlugin unloads a plugin
func (pm *PluginManager) UnloadPlugin(ctx context.Context, name string) error {
    pm.mutex.Lock()
    defer pm.mutex.Unlock()

    instance, exists := pm.plugins[name]
    if !exists {
        return fmt.Errorf("plugin not found: %s", name)
    }

    // Stop hot reload
    if instance.HotReload != nil {
        instance.HotReload.Stop()
    }

    // Shutdown plugin
    if err := instance.Plugin.Shutdown(ctx); err != nil {
        pm.logger.Warn("Error shutting down plugin", zap.Error(err))
    }

    // Remove from registry
    // pm.registry.UnregisterProvider(name)

    // Remove from plugins map
    delete(pm.plugins, name)

    pm.logger.Info("Plugin unloaded", zap.String("name", name))

    return nil
}

// GetPluginStatus returns plugin status information
func (pm *PluginManager) GetPluginStatus(name string) (*PluginStatus, error) {
    pm.mutex.RLock()
    defer pm.mutex.RUnlock()

    instance, exists := pm.plugins[name]
    if !exists {
        return nil, fmt.Errorf("plugin not found: %s", name)
    }

    status := &PluginStatus{
        Name:        name,
        Version:     instance.Plugin.GetInfo().Version,
        Status:      "running",
        LastReload:  instance.LastReload,
        ReloadCount: instance.ReloadCount,
        HotReload:   instance.HotReload != nil,
    }

    // Check plugin health
    if err := instance.Plugin.Health(context.Background()); err != nil {
        status.Status = "unhealthy"
        status.Error = err.Error()
    }

    return status, nil
}

// PluginStatus represents plugin status
type PluginStatus struct {
    Name        string    `json:"name"`
    Version     string    `json:"version"`
    Status      string    `json:"status"`
    LastReload  time.Time `json:"lastReload"`
    ReloadCount int       `json:"reloadCount"`
    HotReload   bool      `json:"hotReload"`
    Error       string    `json:"error,omitempty"`
}
```

## 🧪 **Testing Plugins**

### **Unit Testing**

```go
// executor_test.go
package main

import (
    "context"
    "testing"

    "github.com/claudio/deploy-orchestrator/workflow-service/internal/providers"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"
    "go.uber.org/zap/zaptest"
)

func TestXYZCloudExecutor_ValidateConfig(t *testing.T) {
    logger := zaptest.NewLogger(t)
    executor := NewXYZCloudExecutor(logger, map[string]interface{}{
        "apiKey": "test-key",
        "region": "us-east-1",
    })

    tests := []struct {
        name    string
        config  *models.ProviderConfig
        wantErr bool
    }{
        {
            name: "valid config",
            config: &models.ProviderConfig{
                Cluster:  "test-cluster",
                Region:   "us-east-1",
                Endpoint: "https://api.xyz-cloud.com",
            },
            wantErr: false,
        },
        {
            name: "missing endpoint",
            config: &models.ProviderConfig{
                Cluster: "test-cluster",
                Region:  "us-east-1",
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := executor.ValidateConfig(tt.config)
            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
            }
        })
    }
}

func TestXYZCloudExecutor_Execute(t *testing.T) {
    logger := zaptest.NewLogger(t)
    executor := NewXYZCloudExecutor(logger, map[string]interface{}{
        "apiKey": "test-key",
        "region": "us-east-1",
    })

    ctx := context.Background()
    request := &providers.ExecutionRequest{
        StepType: "xyz_validate",
        Config: map[string]interface{}{
            "appName": "test-app",
        },
    }

    result, err := executor.Execute(ctx, request)
    require.NoError(t, err)
    assert.True(t, result.Success)
    assert.Equal(t, "test-app", result.Output["appName"])
}
```

### **Integration Testing**

```go
// integration_test.go
package main

import (
    "context"
    "testing"
    "time"

    "github.com/claudio/deploy-orchestrator/workflow-service/internal/providers"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"
    "go.uber.org/zap/zaptest"
)

func TestPluginIntegration(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping integration test")
    }

    logger := zaptest.NewLogger(t)
    registry := providers.NewProviderRegistry(logger)

    // Load plugin
    plugin, err := NewPlugin(map[string]interface{}{
        "apiKey": "test-key",
        "region": "us-east-1",
    }, logger)
    require.NoError(t, err)

    // Initialize plugin
    ctx := context.Background()
    err = plugin.Initialize(ctx)
    require.NoError(t, err)

    // Register with registry
    err = registry.RegisterProvider(plugin.GetExecutor())
    require.NoError(t, err)

    // Test provider capabilities
    capabilities := plugin.GetExecutor().GetCapabilities()
    assert.Contains(t, capabilities, providers.CapabilityDeploy)

    // Test step generation
    deployment := &providers.DeploymentRequest{
        ID:         "test-deployment",
        WorkflowID: "test-workflow",
        Environment: &models.EnvironmentConfig{
            Provider: struct {
                Type   models.ProviderType   `json:"type"`
                Config models.ProviderConfig `json:"config" gorm:"type:jsonb"`
            }{
                Type: "xyz-cloud",
                Config: models.ProviderConfig{
                    Region: "us-east-1",
                },
            },
        },
        Configuration: map[string]interface{}{
            "appName": "test-app",
        },
    }

    steps, err := plugin.GetExecutor().GenerateSteps(deployment)
    require.NoError(t, err)
    assert.Len(t, steps, 3) // validate, deploy, health-check

    // Cleanup
    err = plugin.Shutdown(ctx)
    assert.NoError(t, err)
}
```

### **Plugin Testing Framework**

```go
// test_framework.go
package main

import (
    "context"
    "testing"

    "github.com/claudio/deploy-orchestrator/workflow-service/internal/providers"
    "go.uber.org/zap/zaptest"
)

// PluginTestSuite provides a test suite for plugins
type PluginTestSuite struct {
    Plugin   *Plugin
    Registry *providers.ProviderRegistry
    Logger   *zap.Logger
    Config   map[string]interface{}
}

// NewPluginTestSuite creates a new plugin test suite
func NewPluginTestSuite(t *testing.T, config map[string]interface{}) *PluginTestSuite {
    logger := zaptest.NewLogger(t)
    registry := providers.NewProviderRegistry(logger)

    return &PluginTestSuite{
        Registry: registry,
        Logger:   logger,
        Config:   config,
    }
}

// SetupPlugin sets up the plugin for testing
func (pts *PluginTestSuite) SetupPlugin(t *testing.T) {
    plugin, err := NewPlugin(pts.Config, pts.Logger)
    require.NoError(t, err)

    err = plugin.Initialize(context.Background())
    require.NoError(t, err)

    err = pts.Registry.RegisterProvider(plugin.GetExecutor())
    require.NoError(t, err)

    pts.Plugin = plugin
}

// TeardownPlugin cleans up the plugin after testing
func (pts *PluginTestSuite) TeardownPlugin(t *testing.T) {
    if pts.Plugin != nil {
        err := pts.Plugin.Shutdown(context.Background())
        assert.NoError(t, err)
    }
}

// TestBasicFunctionality tests basic plugin functionality
func (pts *PluginTestSuite) TestBasicFunctionality(t *testing.T) {
    // Test plugin info
    info := pts.Plugin.GetInfo()
    assert.NotEmpty(t, info.Name)
    assert.NotEmpty(t, info.Version)

    // Test capabilities
    capabilities := pts.Plugin.GetExecutor().GetCapabilities()
    assert.NotEmpty(t, capabilities)

    // Test health check
    err := pts.Plugin.Health(context.Background())
    assert.NoError(t, err)
}
```

## 📚 **Best Practices**

### **1. Plugin Design Principles**

- **Single Responsibility**: Each plugin should have a clear, focused purpose
- **Stateless Design**: Plugins should be stateless and idempotent
- **Error Handling**: Implement comprehensive error handling and recovery
- **Logging**: Use structured logging for debugging and monitoring
- **Configuration**: Support flexible configuration with validation

### **2. Security Considerations**

```go
// Secure credential handling
func (e *XYZCloudExecutor) loadCredentials(config map[string]interface{}) error {
    // Use secure credential storage
    apiKey, err := e.secretManager.GetSecret("xyz-cloud-api-key")
    if err != nil {
        return fmt.Errorf("failed to load API key: %w", err)
    }

    // Validate credentials
    if err := e.validateCredentials(apiKey); err != nil {
        return fmt.Errorf("invalid credentials: %w", err)
    }

    e.apiKey = apiKey
    return nil
}

// Sanitize logs to prevent credential leakage
func (e *XYZCloudExecutor) sanitizeLog(message string) string {
    // Remove sensitive information from logs
    sanitized := strings.ReplaceAll(message, e.apiKey, "***")
    return sanitized
}
```

### **3. Performance Optimization**

```go
// Connection pooling
type XYZCloudClient struct {
    httpClient *http.Client
    pool       *connectionPool
}

// Caching
type CachedExecutor struct {
    executor providers.ProviderExecutor
    cache    *cache.Cache
}

func (c *CachedExecutor) Execute(ctx context.Context, request *providers.ExecutionRequest) (*providers.ExecutionResult, error) {
    // Check cache first
    cacheKey := generateCacheKey(request)
    if result, found := c.cache.Get(cacheKey); found {
        return result.(*providers.ExecutionResult), nil
    }

    // Execute and cache result
    result, err := c.executor.Execute(ctx, request)
    if err == nil && result.Success {
        c.cache.Set(cacheKey, result, 5*time.Minute)
    }

    return result, err
}
```

### **4. Monitoring and Observability**

```go
// Metrics collection
func (e *XYZCloudExecutor) Execute(ctx context.Context, request *providers.ExecutionRequest) (*providers.ExecutionResult, error) {
    start := time.Now()
    defer func() {
        duration := time.Since(start)
        e.metrics.RecordExecutionTime(request.StepType, duration)
    }()

    // Increment execution counter
    e.metrics.IncrementExecutionCount(request.StepType)

    result, err := e.executeInternal(ctx, request)

    // Record success/failure metrics
    if err != nil {
        e.metrics.IncrementErrorCount(request.StepType)
    } else if result.Success {
        e.metrics.IncrementSuccessCount(request.StepType)
    }

    return result, err
}
```

### **5. Documentation Standards**

```go
// XYZCloudExecutor implements deployment to XYZ Cloud platform.
//
// This executor supports the following capabilities:
// - Application deployment and rollback
// - Health checks and monitoring
// - Log retrieval and analysis
//
// Configuration:
//   apiKey: XYZ Cloud API key (required)
//   region: Deployment region (default: us-east-1)
//   endpoint: API endpoint URL (required)
//
// Example usage:
//   executor := NewXYZCloudExecutor(logger, config)
//   result, err := executor.Execute(ctx, request)
type XYZCloudExecutor struct {
    // ... implementation
}
```

## 🔧 **Plugin Management API**

### **REST API Endpoints**

```bash
# List all plugins
GET /api/v1/plugins

# Get plugin details
GET /api/v1/plugins/{name}

# Install plugin
POST /api/v1/plugins
{
  "name": "xyz-cloud-plugin",
  "source": "registry://xyz-cloud-plugin:1.0.0",
  "config": {
    "apiKey": "your-api-key",
    "region": "us-east-1"
  },
  "enabled": true
}

# Update plugin configuration
PUT /api/v1/plugins/{name}/config
{
  "apiKey": "new-api-key",
  "region": "us-west-2"
}

# Enable/disable plugin
POST /api/v1/plugins/{name}/enable
POST /api/v1/plugins/{name}/disable

# Reload plugin (hot reload)
POST /api/v1/plugins/{name}/reload

# Get plugin status
GET /api/v1/plugins/{name}/status

# Get plugin logs
GET /api/v1/plugins/{name}/logs

# Uninstall plugin
DELETE /api/v1/plugins/{name}
```

### **CLI Commands**

```bash
# Plugin management commands
deploy-orchestrator plugin --help

# List plugins
deploy-orchestrator plugin list
deploy-orchestrator plugin list --enabled
deploy-orchestrator plugin list --type provider

# Install plugin
deploy-orchestrator plugin install xyz-cloud-plugin
deploy-orchestrator plugin install --source ./my-plugin
deploy-orchestrator plugin install --source registry://xyz-cloud:1.0.0

# Configure plugin
deploy-orchestrator plugin config xyz-cloud-plugin --set apiKey=new-key
deploy-orchestrator plugin config xyz-cloud-plugin --file config.yaml

# Plugin status and logs
deploy-orchestrator plugin status xyz-cloud-plugin
deploy-orchestrator plugin logs xyz-cloud-plugin
deploy-orchestrator plugin logs xyz-cloud-plugin --follow

# Hot reload
deploy-orchestrator plugin reload xyz-cloud-plugin

# Enable/disable
deploy-orchestrator plugin enable xyz-cloud-plugin
deploy-orchestrator plugin disable xyz-cloud-plugin

# Remove plugin
deploy-orchestrator plugin remove xyz-cloud-plugin
```

## 🎯 **Examples**

### **Complete AWS Lambda Plugin**

See `examples/aws-lambda-plugin/` for a complete implementation of an AWS Lambda provider plugin with:

- Lambda function deployment
- Environment variable management
- VPC configuration
- IAM role management
- CloudWatch logs integration
- Hot reload support

### **Notification Plugin**

See `examples/notification-plugin/` for a step executor plugin that supports:

- Slack notifications
- Email notifications
- Webhook notifications
- Microsoft Teams integration
- Custom notification templates

### **Database Migration Plugin**

See `examples/db-migration-plugin/` for a specialized plugin that handles:

- Database schema migrations
- Data migrations
- Rollback capabilities
- Multiple database support (PostgreSQL, MySQL, MongoDB)

## 📖 **Additional Resources**

- [Plugin API Reference](./API_REFERENCE.md)
- [Plugin Examples](../examples/)
- [Troubleshooting Guide](./TROUBLESHOOTING.md)
- [Security Guidelines](./SECURITY.md)
- [Performance Tuning](./PERFORMANCE.md)

---

## 🎉 **Summary**

The Deploy Orchestrator plugin system provides:

✅ **Hot Reload Support**: Plugins can be reloaded without service restart
✅ **Flexible Architecture**: Support for multiple plugin types
✅ **Easy Development**: Clear interfaces and comprehensive examples
✅ **Production Ready**: Security, monitoring, and error handling
✅ **Extensible**: Easy to add new cloud providers and integrations

Start building your custom plugins today and extend the Deploy Orchestrator to support your specific deployment needs!
