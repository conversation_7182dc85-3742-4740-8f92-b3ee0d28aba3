# Plugin Development Quick Start Guide

## 🚀 **5-Minute Plugin Setup**

This guide will help you create your first Deploy Orchestrator plugin in just 5 minutes.

### **Prerequisites**

- Go 1.19+ installed
- Deploy Orchestrator development environment
- Basic understanding of Go interfaces

### **Step 1: Create Plugin Structure**

```bash
# Create plugin directory
mkdir -p plugins/my-first-plugin/{templates,tests}
cd plugins/my-first-plugin

# Initialize Go module
go mod init my-first-plugin
```

### **Step 2: Create Plugin Manifest**

```yaml
# plugin.yaml
apiVersion: v1
kind: Plugin
metadata:
  name: my-first-plugin
  version: 1.0.0
  description: "My first Deploy Orchestrator plugin"
  author: "Your Name <<EMAIL>>"
  
spec:
  type: provider
  runtime: go
  entrypoint: main.go
  
  provider:
    type: my-cloud
    capabilities:
      - deploy
      - health-checks
      
  configSchema:
    type: object
    properties:
      apiKey:
        type: string
        required: true
        sensitive: true
      region:
        type: string
        default: "us-east-1"
        
  hotReload:
    enabled: true
    watchPaths:
      - "*.go"
```

### **Step 3: Implement Basic Provider**

```go
// main.go
package main

import (
    "context"
    "fmt"
    "time"

    "github.com/claudio/deploy-orchestrator/shared/models"
    "github.com/claudio/deploy-orchestrator/workflow-service/internal/providers"
    "go.uber.org/zap"
)

// MyCloudExecutor implements a simple cloud provider
type MyCloudExecutor struct {
    providers.BaseExecutor
    logger *zap.Logger
    apiKey string
    region string
}

// NewMyCloudExecutor creates a new executor
func NewMyCloudExecutor(logger *zap.Logger, config map[string]interface{}) (*MyCloudExecutor, error) {
    apiKey, ok := config["apiKey"].(string)
    if !ok {
        return nil, fmt.Errorf("apiKey is required")
    }
    
    region, ok := config["region"].(string)
    if !ok {
        region = "us-east-1"
    }
    
    return &MyCloudExecutor{
        BaseExecutor: providers.BaseExecutor{
            ProviderType: "my-cloud",
        },
        logger: logger,
        apiKey: apiKey,
        region: region,
    }, nil
}

// GetCapabilities returns supported capabilities
func (e *MyCloudExecutor) GetCapabilities() []string {
    return []string{
        providers.CapabilityDeploy,
        providers.CapabilityHealthChecks,
    }
}

// ValidateConfig validates provider configuration
func (e *MyCloudExecutor) ValidateConfig(config *models.ProviderConfig) error {
    if config.Endpoint == "" {
        return fmt.Errorf("endpoint is required")
    }
    return nil
}

// GenerateSteps generates workflow steps
func (e *MyCloudExecutor) GenerateSteps(deployment *providers.DeploymentRequest) ([]*models.WorkflowStep, error) {
    return []*models.WorkflowStep{
        {
            Name: "deploy-app",
            Type: "my_cloud_deploy",
            Config: map[string]interface{}{
                "appName": deployment.Configuration["appName"],
                "region":  e.region,
            },
        },
        {
            Name: "health-check",
            Type: "my_cloud_health",
            Config: map[string]interface{}{
                "appName": deployment.Configuration["appName"],
                "timeout": "300s",
            },
            DependsOn: []string{"deploy-app"},
        },
    }, nil
}

// Execute performs deployment steps
func (e *MyCloudExecutor) Execute(ctx context.Context, request *providers.ExecutionRequest) (*providers.ExecutionResult, error) {
    e.LogInfo(request, fmt.Sprintf("Executing step: %s", request.StepType))
    
    switch request.StepType {
    case "my_cloud_deploy":
        return e.executeDeploy(ctx, request)
    case "my_cloud_health":
        return e.executeHealthCheck(ctx, request)
    default:
        return nil, fmt.Errorf("unsupported step type: %s", request.StepType)
    }
}

// executeDeploy simulates deployment
func (e *MyCloudExecutor) executeDeploy(ctx context.Context, request *providers.ExecutionRequest) (*providers.ExecutionResult, error) {
    appName := request.Config["appName"].(string)
    
    e.LogInfo(request, fmt.Sprintf("Deploying application: %s", appName))
    
    // Simulate deployment time
    time.Sleep(2 * time.Second)
    
    e.LogInfo(request, "Deployment completed successfully")
    
    return &providers.ExecutionResult{
        Success: true,
        Output: map[string]interface{}{
            "deploymentId": "deploy-123",
            "appName":      appName,
            "status":       "deployed",
        },
        Metrics: map[string]float64{
            "deploymentTime": 2.1,
        },
    }, nil
}

// executeHealthCheck simulates health check
func (e *MyCloudExecutor) executeHealthCheck(ctx context.Context, request *providers.ExecutionRequest) (*providers.ExecutionResult, error) {
    appName := request.Config["appName"].(string)
    
    e.LogInfo(request, fmt.Sprintf("Checking health for: %s", appName))
    
    // Simulate health check
    time.Sleep(1 * time.Second)
    
    e.LogInfo(request, "Health check passed")
    
    return &providers.ExecutionResult{
        Success: true,
        Output: map[string]interface{}{
            "health": "healthy",
            "appName": appName,
        },
    }, nil
}

// Implement remaining interface methods with simple implementations
func (e *MyCloudExecutor) GetStatus(ctx context.Context, deploymentID string) (*providers.DeploymentStatus, error) {
    return &providers.DeploymentStatus{
        ID:       deploymentID,
        Status:   "success",
        Progress: 100,
        Message:  "Deployment completed",
    }, nil
}

func (e *MyCloudExecutor) Rollback(ctx context.Context, deploymentID string, targetVersion string) error {
    e.logger.Info("Rollback requested", zap.String("deploymentId", deploymentID))
    return nil
}

func (e *MyCloudExecutor) GetLogs(ctx context.Context, deploymentID string, options *providers.LogOptions) ([]*models.LogEntry, error) {
    return []*models.LogEntry{
        {
            ID:        "log-1",
            Timestamp: time.Now(),
            Level:     "info",
            Message:   "Sample log entry",
            Source:    "my-cloud",
        },
    }, nil
}

func (e *MyCloudExecutor) Cleanup(ctx context.Context, deploymentID string) error {
    e.logger.Info("Cleanup requested", zap.String("deploymentId", deploymentID))
    return nil
}

// Plugin wrapper
type Plugin struct {
    executor *MyCloudExecutor
    logger   *zap.Logger
}

func NewPlugin(config map[string]interface{}, logger *zap.Logger) (*Plugin, error) {
    executor, err := NewMyCloudExecutor(logger, config)
    if err != nil {
        return nil, err
    }
    
    return &Plugin{
        executor: executor,
        logger:   logger,
    }, nil
}

func (p *Plugin) GetExecutor() providers.ProviderExecutor {
    return p.executor
}

func (p *Plugin) Initialize(ctx context.Context) error {
    p.logger.Info("Initializing My Cloud plugin")
    return nil
}

func (p *Plugin) Shutdown(ctx context.Context) error {
    p.logger.Info("Shutting down My Cloud plugin")
    return nil
}

func (p *Plugin) Health(ctx context.Context) error {
    return nil
}

func (p *Plugin) GetInfo() PluginInfo {
    return PluginInfo{
        Name:        "my-first-plugin",
        Version:     "1.0.0",
        Type:        "provider",
        Description: "My first Deploy Orchestrator plugin",
    }
}

type PluginInfo struct {
    Name        string `json:"name"`
    Version     string `json:"version"`
    Type        string `json:"type"`
    Description string `json:"description"`
}

// Entry point for standalone execution
func main() {
    fmt.Println("My First Plugin - Ready for deployment!")
}
```

### **Step 4: Create Basic Test**

```go
// tests/plugin_test.go
package main

import (
    "context"
    "testing"

    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"
    "go.uber.org/zap/zaptest"
)

func TestPlugin(t *testing.T) {
    logger := zaptest.NewLogger(t)
    
    config := map[string]interface{}{
        "apiKey": "test-key",
        "region": "us-east-1",
    }
    
    plugin, err := NewPlugin(config, logger)
    require.NoError(t, err)
    
    // Test initialization
    err = plugin.Initialize(context.Background())
    assert.NoError(t, err)
    
    // Test capabilities
    capabilities := plugin.GetExecutor().GetCapabilities()
    assert.Contains(t, capabilities, "deploy")
    
    // Test plugin info
    info := plugin.GetInfo()
    assert.Equal(t, "my-first-plugin", info.Name)
    
    // Cleanup
    err = plugin.Shutdown(context.Background())
    assert.NoError(t, err)
}
```

### **Step 5: Install and Test**

```bash
# Build the plugin
go build -o my-first-plugin .

# Test the plugin
go test ./tests/

# Install the plugin
deploy-orchestrator plugin install \
  --name my-first-plugin \
  --source . \
  --config config.yaml \
  --enable

# Check plugin status
deploy-orchestrator plugin status my-first-plugin
```

### **Step 6: Create Configuration File**

```yaml
# config.yaml
apiKey: "your-api-key-here"
region: "us-west-2"
```

## 🎯 **Next Steps**

### **Enhance Your Plugin**

1. **Add Real API Integration**
   - Replace mock implementations with actual API calls
   - Add proper error handling and retries
   - Implement authentication

2. **Add More Capabilities**
   - Scaling operations
   - Log retrieval
   - Metrics collection
   - Rollback functionality

3. **Create Workflow Templates**
   - Add pre-built deployment workflows
   - Support different deployment strategies
   - Add validation steps

4. **Add Comprehensive Testing**
   - Unit tests for all methods
   - Integration tests with real APIs
   - Performance tests

### **Advanced Features**

1. **Hot Reload**
   - Enable file watching
   - Test hot reload functionality
   - Handle reload errors gracefully

2. **Monitoring**
   - Add metrics collection
   - Implement health checks
   - Add logging and observability

3. **Security**
   - Secure credential handling
   - Input validation
   - Log sanitization

## 📚 **Resources**

- [Complete Plugin Development Guide](./PLUGIN_DEVELOPMENT_GUIDE.md)
- [Plugin Examples](../examples/)
- [API Reference](./API_REFERENCE.md)
- [Best Practices](./BEST_PRACTICES.md)

## 🎉 **Congratulations!**

You've successfully created your first Deploy Orchestrator plugin! 

Your plugin now supports:
- ✅ Basic deployment operations
- ✅ Health checks
- ✅ Hot reload capability
- ✅ Proper logging
- ✅ Configuration validation

Continue with the [Complete Plugin Development Guide](./PLUGIN_DEVELOPMENT_GUIDE.md) to add more advanced features and capabilities.
