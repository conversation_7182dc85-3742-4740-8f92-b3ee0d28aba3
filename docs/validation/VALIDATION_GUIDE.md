# Validation System Guide

This guide explains the comprehensive validation system for environment provider configurations in the Deploy Orchestrator.

## Overview

The validation system provides:
- **Type validation**: Ensures correct data types
- **Format validation**: Validates patterns, lengths, and ranges
- **Custom validation**: Specialized validators for common formats
- **Conditional validation**: Rules that depend on other field values
- **Extensible rules**: Easy to add new validation types

## Validation Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend        │    │   Validation    │
│   Form          │───▶│   API            │───▶│   Engine        │
│   Validation    │    │   Endpoint       │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │   Provider      │
                                               │   Specific      │
                                               │   Rules         │
                                               └─────────────────┘
```

## Validation Rules

### Basic Types

#### String Validation
```json
{
  "field": "cluster_name",
  "type": "string",
  "required": true,
  "minLength": 1,
  "maxLength": 63,
  "pattern": "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$",
  "message": "Must be a valid Kubernetes name"
}
```

#### Integer Validation
```json
{
  "field": "port",
  "type": "integer",
  "required": false,
  "minimum": 1,
  "maximum": 65535,
  "default": 8080
}
```

#### Number Validation
```json
{
  "field": "cpu_cores",
  "type": "number",
  "required": false,
  "minimum": 0.1,
  "maximum": 64.0,
  "enum": [0.25, 0.5, 1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 64.0]
}
```

#### Boolean Validation
```json
{
  "field": "enable_autoscaling",
  "type": "boolean",
  "required": false,
  "default": true
}
```

#### Array Validation
```json
{
  "field": "instance_types",
  "type": "array",
  "required": false,
  "minLength": 1,
  "maxLength": 5,
  "items": {
    "type": "string",
    "enum": ["t3.micro", "t3.small", "t3.medium", "t3.large"]
  }
}
```

#### Object Validation
```json
{
  "field": "networking",
  "type": "object",
  "required": false,
  "properties": {
    "vpc_id": {"type": "string", "pattern": "^vpc-[a-f0-9]{8,17}$"},
    "subnet_ids": {"type": "array", "items": {"type": "string"}}
  }
}
```

### Custom Validation Types

#### Email Validation
```json
{
  "field": "admin_email",
  "type": "string",
  "custom": "email",
  "message": "Must be a valid email address"
}
```

#### URL Validation
```json
{
  "field": "webhook_url",
  "type": "string",
  "custom": "url",
  "message": "Must be a valid URL"
}
```

#### IP Address Validation
```json
{
  "field": "load_balancer_ip",
  "type": "string",
  "custom": "ip",
  "message": "Must be a valid IP address"
}
```

#### CIDR Validation
```json
{
  "field": "pod_cidr",
  "type": "string",
  "custom": "cidr",
  "message": "Must be a valid CIDR notation"
}
```

#### Port Validation
```json
{
  "field": "service_port",
  "type": "integer",
  "custom": "port",
  "message": "Must be a valid port number (1-65535)"
}
```

#### Cloud Provider Regions
```json
{
  "field": "aws_region",
  "type": "string",
  "custom": "aws_region",
  "message": "Must be a valid AWS region"
}
```

```json
{
  "field": "azure_location",
  "type": "string",
  "custom": "azure_location",
  "message": "Must be a valid Azure location"
}
```

```json
{
  "field": "gcp_region",
  "type": "string",
  "custom": "gcp_region",
  "message": "Must be a valid Google Cloud region"
}
```

#### Kubernetes Names
```json
{
  "field": "namespace",
  "type": "string",
  "custom": "kubernetes_name",
  "message": "Must be a valid Kubernetes resource name"
}
```

#### Docker Images
```json
{
  "field": "container_image",
  "type": "string",
  "custom": "docker_image",
  "message": "Must be a valid Docker image name"
}
```

### Conditional Validation

Conditional validation allows rules to depend on other field values:

```json
{
  "field": "ssl_certificate",
  "type": "string",
  "required": false,
  "conditional": {
    "if": {"enable_ssl": true},
    "then": [
      {
        "field": "ssl_certificate",
        "type": "string",
        "required": true,
        "message": "SSL certificate is required when SSL is enabled"
      }
    ]
  }
}
```

#### Complex Conditional Example
```json
{
  "field": "node_pool_config",
  "type": "object",
  "conditional": {
    "if": {"provider": "azure-aks"},
    "then": [
      {
        "field": "vm_size",
        "type": "string",
        "required": true,
        "enum": ["Standard_B2s", "Standard_D2s_v3", "Standard_D4s_v3"]
      }
    ],
    "else": [
      {
        "field": "instance_type",
        "type": "string",
        "required": true,
        "enum": ["t3.medium", "m5.large", "c5.xlarge"]
      }
    ]
  }
}
```

## Provider-Specific Validation Rules

### AWS EKS
```json
{
  "aws-eks": [
    {
      "field": "region",
      "type": "string",
      "required": true,
      "custom": "aws_region"
    },
    {
      "field": "cluster_name",
      "type": "string",
      "required": true,
      "custom": "kubernetes_name"
    },
    {
      "field": "access_key_id",
      "type": "string",
      "required": true,
      "minLength": 16,
      "maxLength": 32,
      "pattern": "^AKIA[0-9A-Z]{16}$"
    },
    {
      "field": "secret_access_key",
      "type": "string",
      "required": true,
      "minLength": 40,
      "sensitive": true
    }
  ]
}
```

### Azure AKS
```json
{
  "azure-aks": [
    {
      "field": "subscription_id",
      "type": "string",
      "required": true,
      "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
    },
    {
      "field": "location",
      "type": "string",
      "required": true,
      "custom": "azure_location"
    },
    {
      "field": "cluster_name",
      "type": "string",
      "required": true,
      "custom": "kubernetes_name"
    }
  ]
}
```

### Google Cloud Run
```json
{
  "cloud-run": [
    {
      "field": "project_id",
      "type": "string",
      "required": true,
      "pattern": "^[a-z][a-z0-9-]{4,28}[a-z0-9]$"
    },
    {
      "field": "region",
      "type": "string",
      "required": true,
      "custom": "gcp_region"
    },
    {
      "field": "service_name",
      "type": "string",
      "required": true,
      "custom": "kubernetes_name"
    },
    {
      "field": "container_image",
      "type": "string",
      "required": true,
      "custom": "docker_image"
    }
  ]
}
```

## Using the Validation System

### Backend Implementation

```go
package main

import (
    "github.com/claudio/deploy-orchestrator/environment-service/internal/validation"
)

func validateProviderConfig(providerType string, config map[string]interface{}) error {
    validator := validation.NewProviderValidator()
    
    // Load default rules
    defaultRules := validation.GetDefaultRules()
    for provider, rules := range defaultRules {
        validator.AddRules(provider, rules)
    }
    
    // Add custom rules
    validator.AddRule(providerType, validation.ValidationRule{
        Field:    "custom_field",
        Type:     "string",
        Required: true,
        Custom:   "email",
    })
    
    // Validate configuration
    result := validator.Validate(providerType, config)
    if !result.Valid {
        for _, err := range result.Errors {
            log.Printf("Validation error: %s - %s", err.Field, err.Message)
        }
        return fmt.Errorf("validation failed")
    }
    
    return nil
}
```

### Frontend Integration

The frontend automatically generates forms based on provider schemas and applies validation rules:

```typescript
// Environment service automatically handles validation
this.environmentService.validateProviderConfig(providerType, config).subscribe({
  next: (result) => {
    if (result.valid) {
      // Proceed with environment creation
    } else {
      // Display validation errors
      this.displayValidationErrors(result.errors);
    }
  }
});
```

## Error Handling

### Validation Error Structure
```json
{
  "valid": false,
  "errors": [
    {
      "field": "cluster_name",
      "message": "Must be a valid Kubernetes name",
      "code": "INVALID_K8S_NAME",
      "value": "Invalid-Name"
    },
    {
      "field": "port",
      "message": "Port must be between 1 and 65535",
      "code": "INVALID_PORT_RANGE",
      "value": 70000
    }
  ]
}
```

### Error Codes

| Code | Description |
|------|-------------|
| `REQUIRED` | Required field is missing |
| `TYPE_MISMATCH` | Value type doesn't match expected type |
| `PATTERN_MISMATCH` | Value doesn't match required pattern |
| `MIN_LENGTH` | String/array is too short |
| `MAX_LENGTH` | String/array is too long |
| `MIN_VALUE` | Number is below minimum |
| `MAX_VALUE` | Number is above maximum |
| `INVALID_ENUM` | Value is not in allowed enum list |
| `INVALID_EMAIL` | Invalid email format |
| `INVALID_URL` | Invalid URL format |
| `INVALID_IP` | Invalid IP address |
| `INVALID_CIDR` | Invalid CIDR notation |
| `INVALID_PORT` | Invalid port number |
| `INVALID_AWS_REGION` | Invalid AWS region |
| `INVALID_AZURE_LOCATION` | Invalid Azure location |
| `INVALID_GCP_REGION` | Invalid Google Cloud region |
| `INVALID_K8S_NAME` | Invalid Kubernetes name |
| `INVALID_DOCKER_IMAGE` | Invalid Docker image name |

## Adding Custom Validators

### Step 1: Define the Validator Function

```go
func (v *ProviderValidator) validateMyCustomType(rule ValidationRule, value interface{}) *ValidationError {
    str, ok := value.(string)
    if !ok {
        return nil
    }
    
    // Custom validation logic
    if !isValidMyCustomType(str) {
        return &ValidationError{
            Field:   rule.Field,
            Message: v.getErrorMessage(rule, "Must be a valid custom type"),
            Code:    "INVALID_CUSTOM_TYPE",
            Value:   value,
        }
    }
    
    return nil
}
```

### Step 2: Register the Validator

```go
func (v *ProviderValidator) validateCustom(rule ValidationRule, value interface{}) *ValidationError {
    switch rule.Custom {
    case "my_custom_type":
        return v.validateMyCustomType(rule, value)
    // ... other cases
    }
}
```

### Step 3: Use in Schema

```json
{
  "field": "my_field",
  "type": "string",
  "custom": "my_custom_type",
  "message": "Must be a valid custom type"
}
```

## Testing Validation Rules

### Unit Tests

```go
func TestCustomValidation(t *testing.T) {
    validator := NewProviderValidator()
    
    validator.AddRule("test-provider", ValidationRule{
        Field:  "test_field",
        Type:   "string",
        Custom: "my_custom_type",
    })
    
    tests := []struct {
        name     string
        config   map[string]interface{}
        expected bool
    }{
        {
            name:     "Valid custom type",
            config:   map[string]interface{}{"test_field": "valid-value"},
            expected: true,
        },
        {
            name:     "Invalid custom type",
            config:   map[string]interface{}{"test_field": "invalid-value"},
            expected: false,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result := validator.Validate("test-provider", tt.config)
            if result.Valid != tt.expected {
                t.Errorf("Expected valid=%v, got valid=%v", tt.expected, result.Valid)
            }
        })
    }
}
```

### Integration Tests

```bash
# Test validation endpoint
curl -X POST http://localhost:8080/api/v1/providers/aws-eks/validate-config \
  -H "Content-Type: application/json" \
  -d '{
    "region": "us-west-2",
    "cluster_name": "test-cluster",
    "access_key_id": "AKIAIOSFODNN7EXAMPLE",
    "secret_access_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
  }'
```

## Best Practices

### 1. Provide Clear Error Messages
```json
{
  "message": "Cluster name must be 1-63 characters, start and end with alphanumeric characters, and contain only lowercase letters, numbers, and hyphens"
}
```

### 2. Use Appropriate Validation Types
- Use `custom: "kubernetes_name"` for Kubernetes resources
- Use `custom: "docker_image"` for container images
- Use `custom: "url"` for endpoints

### 3. Implement Progressive Validation
- Basic type checking first
- Format validation second
- Business logic validation last

### 4. Handle Sensitive Data
```json
{
  "field": "api_key",
  "type": "string",
  "sensitive": true,
  "format": "password"
}
```

### 5. Provide Helpful Defaults
```json
{
  "field": "timeout",
  "type": "integer",
  "default": 300,
  "minimum": 30,
  "maximum": 3600
}
```

## Troubleshooting

### Common Issues

1. **Validation not triggering**
   - Check rule registration
   - Verify field names match exactly
   - Ensure provider type is correct

2. **Custom validation not working**
   - Verify custom validator is implemented
   - Check switch statement in `validateCustom`
   - Test validator function independently

3. **Frontend form not validating**
   - Check schema format
   - Verify API response structure
   - Test validation endpoint directly

### Debug Mode

Enable validation debugging:

```go
validator.SetDebugMode(true)
```

This will log all validation steps and rule evaluations.

## Performance Considerations

- Validation rules are cached after first load
- Complex regex patterns may impact performance
- Consider async validation for external API calls
- Use conditional validation to reduce unnecessary checks

For more information, see the [Provider Extension Guide](../providers/PROVIDER_EXTENSION_GUIDE.md) and [API Documentation](../api/ENVIRONMENT_SERVICE_API.md).
