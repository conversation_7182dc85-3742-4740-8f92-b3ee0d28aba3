# 🔌 Provider Development Guide

## 📋 Overview

This guide explains how to create new deployment providers for the Deploy Orchestrator. The provider system is designed to be **extremely extensible** - adding support for a new platform requires minimal code.

## 🏗️ Provider Architecture

### **Core Concepts**

1. **Provider Interface** - Standard contract all providers implement
2. **Provider Registry** - Auto-discovery and management system
3. **Configuration Fields** - Dynamic form generation
4. **Capabilities** - Feature discovery system

### **Provider Lifecycle**

```mermaid
graph LR
    A[Register Factory] --> B[Import Provider]
    B --> C[Auto-Discovery]
    C --> D[Create Instance]
    D --> E[Configure]
    E --> F[Validate]
    F --> G[Test]
    G --> H[Deploy]
```

### **Factory Pattern Architecture**

The provider system uses a **factory pattern** for better performance and flexibility:

```mermaid
graph TB
    A[Provider Package] --> B[init() Function]
    B --> C[RegisterFactory()]
    C --> D[Global Factory Registry]

    E[Environment Service] --> F[ListProviders()]
    F --> G[CreateProvider() for each type]
    G --> D
    D --> H[Factory Function]
    H --> I[New Provider Instance]
    I --> J[GetInfo()]
    J --> K[Return Provider Metadata]
```

**Benefits**:
- ✅ **On-demand creation** - Providers created only when needed
- ✅ **Memory efficient** - No persistent instances
- ✅ **Thread-safe** - Each request gets fresh instance
- ✅ **Auto-discovery** - Automatic provider detection
- ✅ **Extensible** - Easy to add new providers

## 🚀 Creating a New Provider

### **Step 1: Define Provider Structure**

Create a new directory: `backend/shared/src/providers/mycloud/`

```go
package mycloud

import (
    "context"
    "fmt"
    "time"
    "github.com/claudio/deploy-orchestrator/shared/providers"
)

type MyCloudProvider struct {
    // Add any provider-specific fields
    client *MyCloudClient
}

func NewMyCloudProvider() providers.Provider {
    return &MyCloudProvider{}
}
```

### **Step 2: Implement Provider Interface**

```go
func (p *MyCloudProvider) GetInfo() providers.ProviderInfo {
    return providers.ProviderInfo{
        Type:        "mycloud",
        Name:        "My Cloud Platform",
        Description: "Deploy applications to My Cloud Platform",
        Category:    providers.CategoryVM,
        Icon:        "mycloud-icon",
        Documentation: "https://docs.mycloud.com/deploy-orchestrator",

        // Define capabilities
        Capabilities: []providers.ProviderCapability{
            providers.CapabilityContainers,
            providers.CapabilityLoadBalancing,
            providers.CapabilityAutoScaling,
            providers.CapabilityPersistentStorage,
            providers.CapabilityMonitoring,
        },

        // Supported auth methods
        AuthMethods: []providers.AuthMethod{
            providers.AuthAPIKey,
            providers.AuthOAuth,
        },

        // Deployment strategies
        Strategies: []providers.DeploymentStrategy{
            providers.StrategyRolling,
            providers.StrategyBlueGreen,
        },

        // Configuration fields (auto-generates UI form)
        ConfigFields: []providers.ConfigField{
            {
                Name:        "apiKey",
                Type:        "password",
                Label:       "API Key",
                Description: "Your My Cloud API key",
                Required:    true,
                Sensitive:   true,
                Group:       "authentication",
                Placeholder: "Enter your API key",
            },
            {
                Name:        "region",
                Type:        "select",
                Label:       "Region",
                Description: "Deployment region",
                Required:    true,
                Group:       "location",
                Options: []providers.Option{
                    {Value: "us-east-1", Label: "US East 1 (Virginia)"},
                    {Value: "us-west-2", Label: "US West 2 (Oregon)"},
                    {Value: "eu-west-1", Label: "EU West 1 (Ireland)"},
                },
            },
            {
                Name:        "instanceType",
                Type:        "select",
                Label:       "Instance Type",
                Description: "VM instance type",
                Required:    false,
                Default:     "standard-2",
                Group:       "resources",
                Options: []providers.Option{
                    {Value: "micro-1", Label: "Micro (1 vCPU, 1GB RAM)"},
                    {Value: "standard-2", Label: "Standard (2 vCPU, 4GB RAM)"},
                    {Value: "performance-4", Label: "Performance (4 vCPU, 8GB RAM)"},
                },
            },
            {
                Name:        "enableMonitoring",
                Type:        "boolean",
                Label:       "Enable Monitoring",
                Description: "Enable built-in monitoring and alerting",
                Required:    false,
                Default:     true,
                Group:       "monitoring",
            },
            {
                Name:        "customDomain",
                Type:        "string",
                Label:       "Custom Domain",
                Description: "Custom domain for your application",
                Required:    false,
                Group:       "networking",
                Validation: providers.Validation{
                    Pattern: `^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$`,
                },
                Placeholder: "app.example.com",
            },
        },

        // Available regions
        Regions: []providers.Region{
            {ID: "us-east-1", Name: "US East 1", Location: "Virginia, USA", Available: true},
            {ID: "us-west-2", Name: "US West 2", Location: "Oregon, USA", Available: true},
            {ID: "eu-west-1", Name: "EU West 1", Location: "Ireland", Available: true},
        },

        // Instance types
        InstanceTypes: []providers.InstanceType{
            {ID: "micro-1", Name: "Micro", CPU: "1 vCPU", Memory: "1 GB", PricePerHour: &[]float64{0.01}[0]},
            {ID: "standard-2", Name: "Standard", CPU: "2 vCPU", Memory: "4 GB", PricePerHour: &[]float64{0.05}[0]},
            {ID: "performance-4", Name: "Performance", CPU: "4 vCPU", Memory: "8 GB", PricePerHour: &[]float64{0.10}[0]},
        },
    }
}
```

### **Step 3: Implement Required Methods**

```go
// ValidateConfig validates the provider configuration
func (p *MyCloudProvider) ValidateConfig(config map[string]interface{}) error {
    // Check required fields
    if apiKey, ok := config["apiKey"].(string); !ok || apiKey == "" {
        return fmt.Errorf("API key is required")
    }

    if region, ok := config["region"].(string); !ok || region == "" {
        return fmt.Errorf("region is required")
    }

    // Validate region
    validRegions := []string{"us-east-1", "us-west-2", "eu-west-1"}
    regionValid := false
    for _, validRegion := range validRegions {
        if region == validRegion {
            regionValid = true
            break
        }
    }
    if !regionValid {
        return fmt.Errorf("invalid region: %s", region)
    }

    return nil
}

// TestConnection tests connectivity to the provider
func (p *MyCloudProvider) TestConnection(ctx context.Context, config map[string]interface{}) (*providers.ConnectionTestResult, error) {
    start := time.Now()

    // Extract config
    apiKey := config["apiKey"].(string)
    region := config["region"].(string)

    // Initialize client
    client := NewMyCloudClient(apiKey, region)

    // Test connection
    err := client.Ping(ctx)
    if err != nil {
        return &providers.ConnectionTestResult{
            Success:  false,
            Message:  fmt.Sprintf("Connection failed: %s", err.Error()),
            TestedAt: time.Now(),
            Latency:  time.Since(start),
        }, nil
    }

    // Get account info for additional validation
    account, err := client.GetAccount(ctx)
    if err != nil {
        return &providers.ConnectionTestResult{
            Success:  false,
            Message:  "Authentication failed",
            TestedAt: time.Now(),
            Latency:  time.Since(start),
        }, nil
    }

    return &providers.ConnectionTestResult{
        Success: true,
        Message: fmt.Sprintf("Connected successfully to %s", account.Name),
        Details: fmt.Sprintf("Account ID: %s, Region: %s", account.ID, region),
        TestedAt: time.Now(),
        Latency:  time.Since(start),
        Metadata: map[string]string{
            "accountId":   account.ID,
            "accountName": account.Name,
            "region":      region,
        },
    }, nil
}

// Deploy deploys services to the provider
func (p *MyCloudProvider) Deploy(ctx context.Context, config map[string]interface{}, deployment providers.DeploymentRequest) (*providers.DeploymentResult, error) {
    // Initialize client
    client := NewMyCloudClient(config["apiKey"].(string), config["region"].(string))

    var deployedServices []providers.DeployedService
    var endpoints []providers.ServiceEndpoint

    // Deploy each service
    for _, service := range deployment.Services {
        // Create deployment spec
        spec := &MyCloudDeploymentSpec{
            Name:      service.Name,
            Image:     fmt.Sprintf("%s:%s", service.Image, service.Tag),
            Replicas:  1, // Default
            Resources: convertResources(service.Resources),
            Ports:     convertPorts(service.Ports),
            Env:       service.Env,
        }

        // Deploy to My Cloud
        result, err := client.Deploy(ctx, spec)
        if err != nil {
            return nil, fmt.Errorf("failed to deploy service %s: %w", service.Name, err)
        }

        // Track deployed service
        deployedServices = append(deployedServices, providers.DeployedService{
            Name:     service.Name,
            Type:     "container",
            Version:  service.Tag,
            Status:   "running",
            Replicas: result.Replicas,
            Resources: map[string]string{
                "cpu":    service.Resources.CPU,
                "memory": service.Resources.Memory,
            },
        })

        // Track endpoints
        for _, endpoint := range result.Endpoints {
            endpoints = append(endpoints, providers.ServiceEndpoint{
                Name:     endpoint.Name,
                URL:      endpoint.URL,
                Type:     "http",
                Port:     endpoint.Port,
                Protocol: "HTTP",
                Public:   endpoint.Public,
            })
        }
    }

    return &providers.DeploymentResult{
        Success:    true,
        Message:    "Deployment completed successfully",
        Services:   deployedServices,
        Endpoints:  endpoints,
        DeployedAt: time.Now(),
        Metadata: map[string]interface{}{
            "deploymentId": deployment.ID,
            "region":       config["region"],
        },
    }, nil
}

// Implement other required methods...
func (p *MyCloudProvider) GetStatus(ctx context.Context, config map[string]interface{}, deploymentID string) (*providers.DeploymentStatus, error) {
    // Implementation here
}

func (p *MyCloudProvider) GetLogs(ctx context.Context, config map[string]interface{}, deploymentID string, options providers.LogOptions) ([]providers.LogEntry, error) {
    // Implementation here
}

func (p *MyCloudProvider) Scale(ctx context.Context, config map[string]interface{}, deploymentID string, replicas int) error {
    // Implementation here
}

func (p *MyCloudProvider) Delete(ctx context.Context, config map[string]interface{}, deploymentID string) error {
    // Implementation here
}

func (p *MyCloudProvider) GetMetrics(ctx context.Context, config map[string]interface{}, deploymentID string) (map[string]interface{}, error) {
    // Implementation here
}
```

### **Step 4: Register Provider Factory**

**Important**: Providers use a **factory pattern** for registration. This allows the system to create provider instances on-demand.

```go
func init() {
    // Register the provider factory with the correct provider type constant
    providers.RegisterFactory(providers.ProviderMyCloud, func() providers.Provider {
        return NewMyCloudProvider()
    })
}
```

**Note**: Make sure to:
1. Use the correct `ProviderType` constant (e.g., `providers.ProviderMyCloud`)
2. Add the constant to `backend/shared/providers/provider_interface.go` if it doesn't exist
3. The factory function should return a new instance each time

### **Step 5: Add Provider Type Constant**

Add your provider type to `backend/shared/providers/provider_interface.go`:

```go
const (
    // Existing providers...
    ProviderGKE        ProviderType = "gke"
    ProviderAKS        ProviderType = "aks"

    // Add your new provider
    ProviderMyCloud    ProviderType = "mycloud"
)
```

### **Step 6: Import Provider in Environment Service**

Add to environment service `main.go`:

```go
import (
    // Import providers for registration
    _ "github.com/claudio/deploy-orchestrator/shared/providers/gke"
    _ "github.com/claudio/deploy-orchestrator/shared/providers/mycloud"  // Add this line
)
```

### **Step 7: Update Provider Service (if needed)**

The environment service automatically discovers registered providers. If you're adding a new provider type, ensure it's included in the provider types list in `backend/environment-service/internal/services/provider_service.go`:

```go
// Get all possible provider types - only try those that might have implementations
providerTypes := []providers.ProviderType{
    providers.ProviderGKE,
    providers.ProviderAKS,
    providers.ProviderEKS,
    providers.ProviderMyCloud,  // Add your provider here
    // ... other providers
}
```

## 🎨 Configuration Field Types

### **Supported Field Types**

| Type | Description | Example |
|------|-------------|---------|
| `string` | Text input | API endpoints, names |
| `password` | Masked input | API keys, passwords |
| `number` | Numeric input | Port numbers, timeouts |
| `boolean` | Checkbox | Enable/disable features |
| `select` | Dropdown | Regions, instance types |
| `multiselect` | Multiple selection | Tags, capabilities |
| `file` | File upload | Certificates, keys |

### **Field Properties**

```go
providers.ConfigField{
    Name:        "fieldName",        // Internal field name
    Type:        "string",           // Field type
    Label:       "Display Name",     // UI label
    Description: "Help text",        // Tooltip/help
    Required:    true,               // Validation
    Default:     "default-value",    // Default value
    Sensitive:   true,               // Hide in logs
    Group:       "authentication",   // UI grouping
    DependsOn:   "otherField",      // Conditional display
    Placeholder: "Enter value...",   // Input placeholder

    // Validation rules
    Validation: providers.Validation{
        Pattern:   "^[a-z]+$",       // Regex pattern
        Min:       &[]int{1}[0],     // Minimum value
        Max:       &[]int{100}[0],   // Maximum value
        MinLength: &[]int{5}[0],     // Min string length
        MaxLength: &[]int{50}[0],    // Max string length
    },

    // Options for select/multiselect
    Options: []providers.Option{
        {Value: "option1", Label: "Option 1"},
        {Value: "option2", Label: "Option 2"},
    },
}
```

## 🧪 Testing Your Provider

### **Unit Tests**

```go
func TestMyCloudProvider_ValidateConfig(t *testing.T) {
    provider := NewMyCloudProvider()

    tests := []struct {
        name    string
        config  map[string]interface{}
        wantErr bool
    }{
        {
            name: "valid config",
            config: map[string]interface{}{
                "apiKey": "test-key",
                "region": "us-east-1",
            },
            wantErr: false,
        },
        {
            name: "missing api key",
            config: map[string]interface{}{
                "region": "us-east-1",
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := provider.ValidateConfig(tt.config)
            if (err != nil) != tt.wantErr {
                t.Errorf("ValidateConfig() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}
```

### **Integration Tests**

```go
func TestMyCloudProvider_Integration(t *testing.T) {
    if testing.Short() {
        t.Skip("skipping integration test")
    }

    provider := NewMyCloudProvider()
    config := map[string]interface{}{
        "apiKey": os.Getenv("MYCLOUD_API_KEY"),
        "region": "us-east-1",
    }

    // Test connection
    result, err := provider.TestConnection(context.Background(), config)
    assert.NoError(t, err)
    assert.True(t, result.Success)
}
```

## 📚 Best Practices

### **1. Error Handling**
- Return descriptive error messages
- Use context for cancellation
- Handle timeouts gracefully

### **2. Configuration**
- Validate all required fields
- Provide sensible defaults
- Use appropriate field types

### **3. Security**
- Mark sensitive fields as `Sensitive: true`
- Never log sensitive data
- Use secure credential storage

### **4. Performance**
- Implement connection pooling
- Cache client instances
- Use async operations where possible

### **5. Monitoring**
- Implement health checks
- Provide meaningful metrics
- Log important events

## 🔍 Debugging & Troubleshooting

### **Common Issues**

#### **1. Provider Not Appearing in List**

**Problem**: Your provider doesn't show up when calling `/api/v1/environment-service/providers`

**Solutions**:
```bash
# Check if factory is registered
curl http://localhost:8083/api/v1/environment-service/providers
```

**Possible causes**:
- ❌ Provider not imported in `environment-service/main.go`
- ❌ `init()` function not called (missing import)
- ❌ Provider type not in the `providerTypes` list in `provider_service.go`
- ❌ Factory registration failed

**Fix**:
```go
// 1. Add import to main.go
import (
    _ "github.com/claudio/deploy-orchestrator/shared/providers/mycloud"
)

// 2. Ensure provider type is in the list
providerTypes := []providers.ProviderType{
    providers.ProviderMyCloud,  // Add this
    // ... other providers
}

// 3. Check factory registration
func init() {
    providers.RegisterFactory(providers.ProviderMyCloud, func() providers.Provider {
        return NewMyCloudProvider()
    })
}
```

#### **2. Provider Returns Null/Empty**

**Problem**: API returns `{"providers": null, "total": 0}`

**Root Cause**: This was a common issue where the system was calling `providers.List()` instead of using the factory pattern.

**Solution**: The environment service now uses `providers.CreateProvider()` which properly creates instances from registered factories.

#### **3. Factory Registration Errors**

**Problem**: Provider factory fails to register

**Debug**:
```go
func init() {
    err := providers.RegisterFactory(providers.ProviderMyCloud, func() providers.Provider {
        return NewMyCloudProvider()
    })
    if err != nil {
        log.Printf("Failed to register MyCloud provider: %v", err)
    }
}
```

### **Enable Debug Logging**
```bash
export LOG_LEVEL=debug
export PROVIDER_DEBUG=true
```

### **Test Provider Registration**
```bash
# Test if provider is available
curl http://localhost:8083/api/v1/environment-service/providers

# Expected response:
{
  "providers": [
    {
      "type": "mycloud",
      "name": "My Cloud Platform",
      "description": "Deploy applications to My Cloud Platform",
      "category": "vm",
      "capabilities": ["containers", "load-balancing"],
      "authMethods": ["api-key", "oauth"],
      "configFields": [...]
    }
  ],
  "total": 1
}
```

### **Validate Configuration**
```bash
curl -X POST http://localhost:8083/api/v1/environment-service/providers/mycloud/validate-config \
  -H "Content-Type: application/json" \
  -d '{"apiKey": "test", "region": "us-east-1"}'
```

### **Test Connection**
```bash
curl -X POST http://localhost:8083/api/v1/environment-service/providers/mycloud/test-connection \
  -H "Content-Type: application/json" \
  -d '{"apiKey": "real-key", "region": "us-east-1"}'
```

### **Debug Provider Discovery**

Add debug logging to your provider:

```go
func init() {
    log.Printf("Registering MyCloud provider...")
    err := providers.RegisterFactory(providers.ProviderMyCloud, func() providers.Provider {
        log.Printf("Creating new MyCloud provider instance")
        return NewMyCloudProvider()
    })
    if err != nil {
        log.Printf("Failed to register MyCloud provider: %v", err)
    } else {
        log.Printf("MyCloud provider registered successfully")
    }
}
```

## 🚀 Publishing Your Provider

1. **Create Documentation** - Add provider-specific docs
2. **Add Examples** - Provide configuration examples
3. **Submit PR** - Contribute back to the project
4. **Update Registry** - Add to supported providers list

## 📖 Examples

See the `examples/providers/` directory for complete provider implementations:
- **GKE Provider** - Kubernetes deployment
- **AWS Lambda Provider** - Serverless functions
- **DigitalOcean Provider** - VM deployment

---

**Ready to build your provider? Start with the template and customize for your platform!** 🔌
