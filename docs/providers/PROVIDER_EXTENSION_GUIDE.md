# Provider Extension Guide

This guide explains how to extend the Deploy Orchestrator with new environment provider types.

## Overview

The Deploy Orchestrator supports multiple environment providers through a pluggable architecture. You can add new providers to support additional cloud platforms, container orchestrators, or deployment targets.

## Current Providers

### Built-in Providers

| Provider | Type | Description | Status |
|----------|------|-------------|--------|
| `kubernetes` | Container Orchestration | Generic Kubernetes clusters | ✅ Active |
| `openshift` | Container Orchestration | Red Hat OpenShift | ✅ Active |
| `docker-swarm` | Container Orchestration | Docker Swarm clusters | ✅ Active |
| `aws-eks` | Container Orchestration | Amazon Elastic Kubernetes Service | ✅ Active |
| `aws-ecs` | Container Orchestration | Amazon Elastic Container Service | ✅ Active |
| `azure-aks` | Container Orchestration | Azure Kubernetes Service | ✅ Active |
| `azure-aci` | Serverless | Azure Container Instances | ✅ Active |
| `cloud-run` | Serverless | Google Cloud Run | ✅ Active |
| `apigee` | API Gateway | Google Apigee API Management | ✅ Active |

### External Providers

| Provider | Type | Description | Status |
|----------|------|-------------|--------|
| `gke` | Container Orchestration | Google Kubernetes Engine | 🔌 Plugin |

## Adding a New Provider

### Step 1: Implement the Provider Interface

Create a new file in `backend/environment-service/internal/providers/`:

```go
package providers

import (
    "context"
    "fmt"
    "time"
)

// MyCustomProvider implements the EnvironmentProvider interface
type MyCustomProvider struct {
    config map[string]interface{}
}

// NewMyCustomProvider creates a new provider instance
func NewMyCustomProvider() EnvironmentProvider {
    return &MyCustomProvider{}
}

// GetMetadata returns provider metadata
func (p *MyCustomProvider) GetMetadata() ProviderMetadata {
    return ProviderMetadata{
        Name:        "my-custom-provider",
        Version:     "1.0.0",
        Description: "My custom environment provider",
        Author:      "Your Name",
        Type:        "container-orchestration", // or "serverless", "api-gateway", etc.
        Category:    "custom",
        Capabilities: []string{
            "deploy", "scale", "monitor", "logs", "health-check",
        },
        Tags: []string{"custom", "example"},
        Icon: "my-provider-icon",
        Documentation: "https://docs.example.com/my-provider",
    }
}

// GetConfigSchema returns the configuration schema
func (p *MyCustomProvider) GetConfigSchema() map[string]interface{} {
    return map[string]interface{}{
        "type": "object",
        "properties": map[string]interface{}{
            "endpoint": map[string]interface{}{
                "type":        "string",
                "title":       "API Endpoint",
                "description": "Provider API endpoint URL",
                "required":    true,
            },
            "api_key": map[string]interface{}{
                "type":        "string",
                "title":       "API Key",
                "description": "Authentication API key",
                "required":    true,
                "sensitive":   true,
            },
            // Add more configuration fields as needed
        },
        "required": []string{"endpoint", "api_key"},
    }
}

// Initialize initializes the provider with configuration
func (p *MyCustomProvider) Initialize(config map[string]interface{}) error {
    p.config = config
    
    // Validate required fields
    required := []string{"endpoint", "api_key"}
    for _, field := range required {
        if _, exists := config[field]; !exists {
            return fmt.Errorf("required field '%s' is missing", field)
        }
    }
    
    return nil
}

// Validate validates the provider configuration
func (p *MyCustomProvider) Validate(config map[string]interface{}) error {
    // Add custom validation logic here
    return nil
}

// CreateEnvironment creates a new environment
func (p *MyCustomProvider) CreateEnvironment(ctx context.Context, config EnvironmentConfig) (*EnvironmentResult, error) {
    // Implement environment creation logic
    return &EnvironmentResult{
        ID:      config.Name + "-" + fmt.Sprintf("%d", time.Now().Unix()),
        Name:    config.Name,
        Status:  "created",
        Message: "Environment created successfully",
        // Add resources, endpoints, etc.
        CreatedAt: time.Now().Format(time.RFC3339),
        UpdatedAt: time.Now().Format(time.RFC3339),
    }, nil
}

// UpdateEnvironment updates an existing environment
func (p *MyCustomProvider) UpdateEnvironment(ctx context.Context, id string, config EnvironmentConfig) (*EnvironmentResult, error) {
    // Implement environment update logic
    return &EnvironmentResult{
        ID:        id,
        Name:      config.Name,
        Status:    "updated",
        Message:   "Environment updated successfully",
        UpdatedAt: time.Now().Format(time.RFC3339),
    }, nil
}

// DeleteEnvironment deletes an environment
func (p *MyCustomProvider) DeleteEnvironment(ctx context.Context, id string) error {
    // Implement environment deletion logic
    return nil
}

// GetEnvironmentStatus returns environment status
func (p *MyCustomProvider) GetEnvironmentStatus(ctx context.Context, id string) (*EnvironmentStatus, error) {
    // Implement status retrieval logic
    return &EnvironmentStatus{
        Status:    "running",
        Health:    "healthy",
        Message:   "Environment is running normally",
        LastCheck: time.Now().Format(time.RFC3339),
    }, nil
}

// GetResources returns environment resources
func (p *MyCustomProvider) GetResources(ctx context.Context, environmentID string) ([]Resource, error) {
    // Implement resource listing logic
    return []Resource{}, nil
}

// ScaleResources scales environment resources
func (p *MyCustomProvider) ScaleResources(ctx context.Context, environmentID string, scaling ScalingConfig) error {
    // Implement scaling logic
    return nil
}

// HealthCheck performs health check on the environment
func (p *MyCustomProvider) HealthCheck(ctx context.Context, environmentID string) (*HealthStatus, error) {
    // Implement health check logic
    return &HealthStatus{
        Status:    "healthy",
        Message:   "Environment is healthy",
        Timestamp: time.Now().Format(time.RFC3339),
    }, nil
}

// GetMetrics returns environment metrics
func (p *MyCustomProvider) GetMetrics(ctx context.Context, environmentID string) (*MetricsData, error) {
    // Implement metrics collection logic
    return &MetricsData{
        Timestamp: time.Now().Format(time.RFC3339),
        Metrics:   map[string]interface{}{},
    }, nil
}

// Cleanup cleans up provider resources
func (p *MyCustomProvider) Cleanup() error {
    // Implement cleanup logic
    return nil
}
```

### Step 2: Register the Provider

Add your provider to the registry in `provider_registry.go`:

```go
// Register My Custom provider
if err := registry.RegisterProvider("my-custom-provider", NewMyCustomProvider()); err != nil {
    return fmt.Errorf("failed to register my-custom-provider: %w", err)
}
```

### Step 3: Add Validation Rules

Add custom validation rules in `internal/validation/provider_validator.go`:

```go
"my-custom-provider": {
    {Field: "endpoint", Type: "string", Required: true, Custom: "url"},
    {Field: "api_key", Type: "string", Required: true, MinLength: 32},
},
```

### Step 4: Write Tests

Create comprehensive tests in `my_custom_provider_test.go`:

```go
package providers

import (
    "context"
    "testing"
)

func TestMyCustomProvider_GetMetadata(t *testing.T) {
    provider := NewMyCustomProvider()
    metadata := provider.GetMetadata()
    
    if metadata.Name != "my-custom-provider" {
        t.Errorf("Expected name 'my-custom-provider', got '%s'", metadata.Name)
    }
}

// Add more tests for all methods
```

### Step 5: Update Frontend

Add the new provider to the frontend enum in `frontend/deploy-orchestrator/src/app/services/environment.service.ts`:

```typescript
export type ProviderType = 
  | 'kubernetes'
  | 'openshift'
  | 'docker-swarm'
  | 'aws-eks'
  | 'aws-ecs'
  | 'azure-aks'
  | 'azure-aci'
  | 'cloud-run'
  | 'apigee'
  | 'my-custom-provider'; // Add your provider here
```

## Configuration Schema Guidelines

### Field Types

- `string`: Text input
- `integer`: Numeric input
- `number`: Numeric input (allows decimals)
- `boolean`: Checkbox
- `array`: List of items
- `object`: Nested configuration

### Field Properties

- `title`: Display name for the field
- `description`: Help text for the field
- `required`: Whether the field is mandatory
- `sensitive`: Whether the field contains sensitive data (passwords, keys)
- `pattern`: Regular expression for validation
- `minLength`/`maxLength`: String length constraints
- `minimum`/`maximum`: Numeric range constraints
- `enum`: List of allowed values
- `default`: Default value

### Custom Validation

Use the `custom` property for specialized validation:

- `email`: Email address format
- `url`: URL format
- `ip`: IP address format
- `cidr`: CIDR notation format
- `port`: Port number (1-65535)
- `aws_region`: Valid AWS region
- `azure_location`: Valid Azure location
- `gcp_region`: Valid Google Cloud region
- `kubernetes_name`: Valid Kubernetes resource name
- `docker_image`: Valid Docker image name

## Best Practices

### 1. Error Handling

Always provide meaningful error messages:

```go
if err != nil {
    return fmt.Errorf("failed to connect to %s: %w", endpoint, err)
}
```

### 2. Configuration Validation

Validate configuration early and thoroughly:

```go
func (p *MyProvider) Validate(config map[string]interface{}) error {
    if endpoint, ok := config["endpoint"].(string); ok {
        if !strings.HasPrefix(endpoint, "https://") {
            return fmt.Errorf("endpoint must use HTTPS")
        }
    }
    return nil
}
```

### 3. Resource Management

Always clean up resources:

```go
func (p *MyProvider) Cleanup() error {
    // Close connections, release resources, etc.
    return nil
}
```

### 4. Logging

Use structured logging:

```go
import "github.com/claudio/deploy-orchestrator/shared/logging"

logger := logging.GetLogger("my-provider")
logger.Info("Creating environment", "name", config.Name)
```

### 5. Metrics

Provide meaningful metrics:

```go
func (p *MyProvider) GetMetrics(ctx context.Context, environmentID string) (*MetricsData, error) {
    return &MetricsData{
        Timestamp: time.Now().Format(time.RFC3339),
        Metrics: map[string]interface{}{
            "cpu_usage":     45.5,
            "memory_usage":  60.2,
            "request_count": 1250,
            "error_rate":    0.1,
        },
    }, nil
}
```

## Testing Your Provider

### Unit Tests

Run unit tests for your provider:

```bash
go test ./internal/providers/my_custom_provider_test.go -v
```

### Integration Tests

Test with the full system:

```bash
# Start the environment service
go run main.go

# Test provider registration
curl http://localhost:8080/api/v1/providers

# Test provider schema
curl http://localhost:8080/api/v1/providers/my-custom-provider/schema
```

### Frontend Testing

Test the provider in the frontend:

1. Navigate to Environment Configuration
2. Select your custom provider
3. Verify the configuration form is generated correctly
4. Test environment creation

## Troubleshooting

### Common Issues

1. **Provider not appearing in list**
   - Check provider registration in `InitializeProviders()`
   - Verify provider implements all interface methods

2. **Configuration form not rendering**
   - Check schema format in `GetConfigSchema()`
   - Verify frontend provider type enum

3. **Validation errors**
   - Check validation rules in provider validator
   - Verify required fields are properly defined

4. **Environment creation fails**
   - Check provider initialization
   - Verify API credentials and connectivity
   - Review error logs

### Debug Mode

Enable debug logging:

```bash
export LOG_LEVEL=debug
go run main.go
```

## External Providers

For complex providers or third-party integrations, consider creating external providers as separate services. See the GKE provider example in `plugins/gke-environment-provider/`.

External providers:
- Run as independent services
- Communicate via REST APIs
- Can be developed in any language
- Support hot-reload and independent deployment

## Contributing

When contributing a new provider:

1. Follow the coding standards
2. Include comprehensive tests
3. Add documentation
4. Update the provider list in this guide
5. Submit a pull request with examples

For questions or support, please refer to the main documentation or create an issue in the repository.
