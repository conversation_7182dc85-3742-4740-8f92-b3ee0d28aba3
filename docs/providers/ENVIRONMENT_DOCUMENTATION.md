# Environment Provider Development Guide

This guide explains how to create new environment providers for the Deploy Orchestrator platform. Environment providers enable the platform to manage different types of deployment environments (Kubernetes, serverless platforms, API gateways, etc.).

## Overview

Environment providers are plugins that implement a standardized interface to manage deployment environments. They can be built-in (compiled into the environment service) or external (standalone services).

## Provider Types

### 1. **Built-in Providers** (Recommended for Core Platforms)
- Compiled into the environment service
- Faster startup and communication
- Shared dependencies and configuration
- Examples: Kubernetes, OpenShift, Azure ACI

### 2. **External Providers** (Recommended for Extensions)
- Standalone services with REST APIs
- Independent deployment and scaling
- Language agnostic (any language)
- Examples: GKE provider, custom cloud platforms

## Quick Start

### Option A: Built-in Provider

1. **Create provider file:**
   ```bash
   touch backend/environment-service/internal/providers/my_provider.go
   ```

2. **Implement the interface:**
   ```go
   type MyProvider struct {
       config map[string]interface{}
   }

   func (p *MyProvider) GetMetadata() ProviderMetadata { /* ... */ }
   func (p *MyProvider) GetConfigSchema() map[string]interface{} { /* ... */ }
   // ... implement all interface methods
   ```

3. **Register the provider:**
   ```go
   // In provider_registry.go InitializeProviders()
   if err := registry.RegisterProvider("my-provider", NewMyProvider()); err != nil {
       return fmt.Errorf("failed to register my-provider: %w", err)
   }
   ```

### Option B: External Provider

1. **Create plugin directory:**
   ```bash
   mkdir -p plugins/my-environment-provider
   cd plugins/my-environment-provider
   ```

2. **Use the GKE provider as template:**
   ```bash
   cp -r ../gke-environment-provider/* .
   # Modify main.go, plugin.yaml, README.md
   ```

3. **Build and run:**
   ```bash
   make build-local
   make run
   ```

## Provider Interface

All providers must implement the `EnvironmentProvider` interface:

```go
type EnvironmentProvider interface {
    // Metadata
    GetMetadata() ProviderMetadata
    GetConfigSchema() map[string]interface{}

    // Lifecycle
    Initialize(config map[string]interface{}) error
    Validate(config map[string]interface{}) error

    // Environment Operations
    CreateEnvironment(ctx context.Context, config EnvironmentConfig) (*EnvironmentResult, error)
    UpdateEnvironment(ctx context.Context, id string, config EnvironmentConfig) (*EnvironmentResult, error)
    DeleteEnvironment(ctx context.Context, id string) error
    GetEnvironmentStatus(ctx context.Context, id string) (*EnvironmentStatus, error)

    // Resource Operations
    GetResources(ctx context.Context, environmentID string) ([]Resource, error)
    ScaleResources(ctx context.Context, environmentID string, scaling ScalingConfig) error

    // Health and Monitoring
    HealthCheck(ctx context.Context, environmentID string) (*HealthStatus, error)
    GetMetrics(ctx context.Context, environmentID string) (*MetricsData, error)

    // Cleanup
    Cleanup() error
}
```

## Implementation Guide

### 1. Provider Metadata

Define your provider's capabilities and information:

```go
func (p *MyProvider) GetMetadata() ProviderMetadata {
    return ProviderMetadata{
        Name:        "my-provider",
        Version:     "1.0.0",
        Description: "My custom environment provider",
        Author:      "Your Name",
        Type:        "container-orchestration", // or "serverless", "api-management", etc.
        Category:    "custom",
        Capabilities: []string{
            "deploy", "scale", "monitor", "logs", "health-check",
        },
        Tags: []string{"custom", "containers"},
        Icon: "custom-icon",
        Documentation: "https://docs.example.com",
        Metadata: map[string]interface{}{
            "supported_versions": []string{"1.0", "2.0"},
            "regions": []string{"us-east-1", "eu-west-1"},
        },
    }
}
```

### 2. Configuration Schema

Define the configuration schema for dynamic form generation:

```go
func (p *MyProvider) GetConfigSchema() map[string]interface{} {
    return map[string]interface{}{
        "type": "object",
        "required": []string{"endpoint", "credentials"},
        "properties": map[string]interface{}{
            "endpoint": map[string]interface{}{
                "type":        "string",
                "title":       "API Endpoint",
                "description": "The API endpoint URL",
            },
            "credentials": map[string]interface{}{
                "type":        "string",
                "title":       "API Credentials",
                "description": "Authentication credentials",
                "sensitive":   true, // Marks field as sensitive
            },
            "region": map[string]interface{}{
                "type":        "string",
                "title":       "Region",
                "description": "Deployment region",
                "enum":        []string{"us-east-1", "eu-west-1"},
                "default":     "us-east-1",
            },
            "enable_monitoring": map[string]interface{}{
                "type":        "boolean",
                "title":       "Enable Monitoring",
                "description": "Enable monitoring and metrics collection",
                "default":     true,
            },
        },
    }
}
```

### 3. Configuration Validation

Implement robust configuration validation:

```go
func (p *MyProvider) Validate(config map[string]interface{}) error {
    // Check required fields
    requiredFields := []string{"endpoint", "credentials"}
    for _, field := range requiredFields {
        if val, ok := config[field]; !ok || val == "" {
            return fmt.Errorf("required field '%s' is missing or empty", field)
        }
    }

    // Validate endpoint format
    endpoint, ok := config["endpoint"].(string)
    if !ok {
        return fmt.Errorf("endpoint must be a string")
    }

    if !strings.HasPrefix(endpoint, "https://") {
        return fmt.Errorf("endpoint must use HTTPS")
    }

    // Validate enum values
    if region, ok := config["region"].(string); ok {
        validRegions := []string{"us-east-1", "eu-west-1"}
        if !contains(validRegions, region) {
            return fmt.Errorf("invalid region: %s", region)
        }
    }

    return nil
}
```

### 4. Environment Operations

Implement the core environment management operations:

```go
func (p *MyProvider) CreateEnvironment(ctx context.Context, config EnvironmentConfig) (*EnvironmentResult, error) {
    // 1. Validate configuration
    if err := p.Validate(config.Config); err != nil {
        return nil, fmt.Errorf("invalid configuration: %w", err)
    }

    // 2. Create environment in target platform
    environmentID, err := p.createEnvironmentInPlatform(ctx, config)
    if err != nil {
        return nil, fmt.Errorf("failed to create environment: %w", err)
    }

    // 3. Return result
    return &EnvironmentResult{
        ID:      environmentID,
        Name:    config.Name,
        Status:  "created",
        Message: "Environment created successfully",
        Resources: []Resource{
            {
                ID:     "resource-1",
                Name:   config.Name,
                Type:   "Environment",
                Status: "Active",
            },
        },
        Endpoints: []Endpoint{
            {
                Name:     "api",
                URL:      p.config["endpoint"].(string),
                Type:     "api",
                Protocol: "https",
                Port:     443,
                Health:   "healthy",
            },
        },
        CreatedAt: time.Now().Format(time.RFC3339),
        UpdatedAt: time.Now().Format(time.RFC3339),
    }, nil
}
```

### 5. Health Checks and Monitoring

Implement comprehensive health checks:

```go
func (p *MyProvider) HealthCheck(ctx context.Context, environmentID string) (*HealthStatus, error) {
    checks := []HealthCheck{}

    // Check API connectivity
    start := time.Now()
    if err := p.checkAPIConnectivity(ctx); err != nil {
        checks = append(checks, HealthCheck{
            Name:      "api-connectivity",
            Status:    "unhealthy",
            Message:   err.Error(),
            Duration:  int(time.Since(start).Milliseconds()),
            Timestamp: time.Now().Format(time.RFC3339),
        })
    } else {
        checks = append(checks, HealthCheck{
            Name:      "api-connectivity",
            Status:    "healthy",
            Message:   "API is responding",
            Duration:  int(time.Since(start).Milliseconds()),
            Timestamp: time.Now().Format(time.RFC3339),
        })
    }

    // Check environment status
    // ... additional checks

    // Determine overall status
    overallStatus := "healthy"
    for _, check := range checks {
        if check.Status != "healthy" {
            overallStatus = "unhealthy"
            break
        }
    }

    return &HealthStatus{
        Status:    overallStatus,
        Message:   "Health check completed",
        Checks:    checks,
        Timestamp: time.Now().Format(time.RFC3339),
    }, nil
}
```

## External Provider Development

### 1. Project Structure

```
plugins/my-environment-provider/
├── main.go              # Main application
├── go.mod              # Go module
├── Makefile            # Build automation
├── plugin.yaml         # Plugin metadata
├── README.md           # Documentation
├── build/              # Build artifacts
└── dist/               # Distribution packages
```

### 2. HTTP API Implementation

External providers expose REST APIs:

```go
func setupRoutes() *gin.Engine {
    router := gin.New()
    provider := NewMyProvider()

    // Plugin metadata
    router.GET("/metadata", func(c *gin.Context) {
        c.JSON(http.StatusOK, provider.GetMetadata())
    })

    // Configuration schema
    router.GET("/schema", func(c *gin.Context) {
        c.JSON(http.StatusOK, provider.GetConfigSchema())
    })

    // Environment operations
    router.POST("/environments", func(c *gin.Context) {
        var config EnvironmentConfig
        if err := c.ShouldBindJSON(&config); err != nil {
            c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
            return
        }

        result, err := provider.CreateEnvironment(c.Request.Context(), config)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
            return
        }

        c.JSON(http.StatusCreated, result)
    })

    // ... other endpoints

    return router
}
```

### 3. Plugin Metadata (plugin.yaml)

```yaml
apiVersion: v1
kind: EnvironmentProvider
metadata:
  name: my-provider
  version: 1.0.0
  description: My custom environment provider

spec:
  type: custom
  category: custom
  capabilities:
    - deploy
    - scale
    - monitor

  runtime:
    type: standalone
    port: 8090
    healthEndpoint: /health

  configSchema:
    type: object
    required:
      - endpoint
      - credentials
    properties:
      endpoint:
        type: string
        title: API Endpoint
      credentials:
        type: string
        title: Credentials
        sensitive: true
```

## Best Practices

### 1. Error Handling

```go
// Use structured errors
type ProviderError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

func (e *ProviderError) Error() string {
    return e.Message
}

// Return meaningful errors
func (p *MyProvider) CreateEnvironment(ctx context.Context, config EnvironmentConfig) (*EnvironmentResult, error) {
    if err := p.validateConfig(config); err != nil {
        return nil, &ProviderError{
            Code:    "INVALID_CONFIG",
            Message: "Configuration validation failed",
            Details: err.Error(),
        }
    }

    // ... implementation
}
```

### 2. Logging

```go
import "log/slog"

func (p *MyProvider) CreateEnvironment(ctx context.Context, config EnvironmentConfig) (*EnvironmentResult, error) {
    slog.Info("Creating environment",
        "name", config.Name,
        "provider", "my-provider",
        "type", config.Type,
    )

    // ... implementation

    slog.Info("Environment created successfully",
        "name", config.Name,
        "id", result.ID,
    )

    return result, nil
}
```

### 3. Context Handling

```go
func (p *MyProvider) CreateEnvironment(ctx context.Context, config EnvironmentConfig) (*EnvironmentResult, error) {
    // Check context cancellation
    select {
    case <-ctx.Done():
        return nil, ctx.Err()
    default:
    }

    // Use context in API calls
    req, err := http.NewRequestWithContext(ctx, "POST", endpoint, body)
    if err != nil {
        return nil, err
    }

    // ... implementation
}
```

### 4. Configuration Security

```go
// Mark sensitive fields in schema
"password": map[string]interface{}{
    "type":      "string",
    "title":     "Password",
    "sensitive": true, // This field will be encrypted
}

// Validate sensitive data
func (p *MyProvider) Validate(config map[string]interface{}) error {
    if password, ok := config["password"].(string); ok {
        if len(password) < 8 {
            return fmt.Errorf("password must be at least 8 characters")
        }
    }
    return nil
}
```

## Testing

### 1. Unit Tests

```go
func TestMyProvider_Validate(t *testing.T) {
    provider := NewMyProvider()

    tests := []struct {
        name    string
        config  map[string]interface{}
        wantErr bool
    }{
        {
            name: "valid config",
            config: map[string]interface{}{
                "endpoint":    "https://api.example.com",
                "credentials": "valid-token",
            },
            wantErr: false,
        },
        {
            name: "missing endpoint",
            config: map[string]interface{}{
                "credentials": "valid-token",
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := provider.Validate(tt.config)
            if (err != nil) != tt.wantErr {
                t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}
```

### 2. Integration Tests

```go
func TestMyProvider_Integration(t *testing.T) {
    if testing.Short() {
        t.Skip("skipping integration test")
    }

    provider := NewMyProvider()
    config := map[string]interface{}{
        "endpoint":    os.Getenv("TEST_ENDPOINT"),
        "credentials": os.Getenv("TEST_CREDENTIALS"),
    }

    // Test initialization
    err := provider.Initialize(config)
    require.NoError(t, err)

    // Test environment creation
    envConfig := EnvironmentConfig{
        Name: "test-env",
        Type: "test",
        Config: config,
    }

    result, err := provider.CreateEnvironment(context.Background(), envConfig)
    require.NoError(t, err)
    require.NotEmpty(t, result.ID)

    // Cleanup
    defer provider.DeleteEnvironment(context.Background(), result.ID)
}
```

## Deployment

### Built-in Provider
1. Add to `provider_registry.go`
2. Rebuild environment service
3. Deploy updated service

### External Provider
1. Build provider binary
2. Deploy as separate service
3. Register with environment service
4. Configure service discovery

## Examples

See the following examples:
- **Built-in**: `backend/environment-service/internal/providers/kubernetes_provider.go`
- **External**: `plugins/gke-environment-provider/`

## Provider Categories

### Container Orchestration
- **Kubernetes** - Standard Kubernetes clusters
- **OpenShift** - Red Hat OpenShift platform
- **Docker Swarm** - Docker Swarm clusters

### Managed Kubernetes
- **GKE** - Google Kubernetes Engine (external plugin)
- **AKS** - Azure Kubernetes Service
- **EKS** - Amazon Elastic Kubernetes Service

### Serverless Containers
- **Azure Container Instances** - Pay-per-use containers
- **Google Cloud Run** - Knative-based serverless
- **AWS Fargate** - Serverless containers on AWS

### API Management
- **Apigee** - Google Cloud API management
- **Azure API Management** - Microsoft API gateway
- **AWS API Gateway** - Amazon API management

### Custom Platforms
- **On-premises** - Custom datacenter platforms
- **Edge computing** - Edge deployment platforms
- **IoT platforms** - Internet of Things environments

## Migration Guide

### Moving Built-in Provider to External

1. **Create external plugin structure:**
   ```bash
   mkdir -p plugins/my-provider
   cp backend/environment-service/internal/providers/my_provider.go plugins/my-provider/main.go
   ```

2. **Add HTTP API layer:**
   ```go
   // Add REST API endpoints
   func setupRoutes() *gin.Engine {
       // ... implement HTTP handlers
   }
   ```

3. **Create plugin metadata:**
   ```yaml
   # plugin.yaml
   apiVersion: v1
   kind: EnvironmentProvider
   metadata:
     name: my-provider
   ```

4. **Remove from built-in registry:**
   ```go
   // Remove from InitializeProviders()
   // registry.RegisterProvider("my-provider", NewMyProvider())
   ```

5. **Deploy as external service:**
   ```bash
   make build
   make install
   ```

## Troubleshooting

### Common Issues

1. **Provider not appearing in UI**
   - Check provider registration in `InitializeProviders()`
   - Verify provider metadata is correct
   - Check environment service logs

2. **Configuration validation errors**
   - Verify schema format (JSON Schema)
   - Check required fields are marked correctly
   - Test validation logic with sample data

3. **External provider connection issues**
   - Check provider service is running
   - Verify port and endpoint configuration
   - Test health endpoint manually

4. **Authentication failures**
   - Verify credentials format
   - Check sensitive field handling
   - Test authentication with provider API

### Debug Commands

```bash
# Check provider registration
curl http://localhost:8080/api/v1/environment-service/providers

# Test provider schema
curl http://localhost:8080/api/v1/environment-service/providers/my-provider/schema

# Validate configuration
curl -X POST http://localhost:8080/api/v1/environment-service/providers/my-provider/validate-config \
  -H "Content-Type: application/json" \
  -d '{"endpoint": "https://api.example.com"}'

# Test external provider directly
curl http://localhost:8090/metadata
curl http://localhost:8090/health
```

## Support

For questions and support:
- Check existing providers for examples
- Review the interface documentation
- Create issues for bugs or feature requests
- Contribute improvements via pull requests
