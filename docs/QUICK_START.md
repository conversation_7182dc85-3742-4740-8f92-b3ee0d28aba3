# 🚀 Deploy Orchestrator - Quick Start Guide

## 📋 Overview

This guide will get you up and running with the Deploy Orchestrator's new **Environment + Workflow architecture** in 15 minutes.

## 🏗️ What's New

We've replaced the old deployment object system with:

- **🌍 Environment Configurations** - Define where to deploy (GKE, AKS, VMs, etc.)
- **⚡ Workflow Executions** - Track deployments with version control
- **📡 Real-time Logging** - Live monitoring with secret filtering
- **🔌 Extensible Providers** - Easy to add new platforms

## 🚀 Quick Setup

### **1. Start the Services**

```bash
# Backend services
cd backend/environment-service && go run main.go &
cd backend/workflow-service && go run main.go &
cd backend/secrets-service && go run main.go &

# Frontend
cd frontend/deploy-orchestrator && npm start
```

### **2. Create Your First Environment**

Navigate to **Environments** → **Create Environment**

**Example GKE Environment:**
```json
{
  "name": "production",
  "type": "kubernetes",
  "provider": {
    "type": "gke",
    "config": {
      "project": "my-gcp-project",
      "cluster": "prod-cluster",
      "zone": "us-central1-a",
      "serviceAccountKey": "{ ... }"
    }
  },
  "resources": {
    "cpu": "2000m",
    "memory": "4Gi",
    "replicas": 3
  }
}
```

### **3. Test Connection**

Click **Test Connection** to verify your environment is accessible.

### **4. Run a Workflow**

Navigate to **Workflows** → **Execute**

Select your environment and watch real-time logs as your deployment progresses!

## 🔌 Adding New Providers

### **Super Easy - Just 3 Steps!**

**1. Create Provider File:**
```go
// backend/shared/src/providers/mycloud/provider.go
package mycloud

func (p *MyCloudProvider) GetInfo() providers.ProviderInfo {
    return providers.ProviderInfo{
        Type: "mycloud",
        Name: "My Cloud Platform",
        ConfigFields: []providers.ConfigField{
            {
                Name: "apiKey",
                Type: "password",
                Label: "API Key",
                Required: true,
                Sensitive: true,
            },
        },
    }
}
```

**2. Register Provider:**
```go
func init() {
    providers.RegisterFactory("mycloud", func() providers.Provider {
        return &MyCloudProvider{}
    })
}
```

**3. Import in Environment Service:**
```go
import _ "github.com/claudio/deploy-orchestrator/shared/providers/mycloud"
```

**That's it!** Your provider will automatically appear in the UI with a generated form.

## 📡 Real-time Features

### **Live Logs**
- ✅ WebSocket-based streaming
- ✅ Automatic secret masking
- ✅ Step-by-step progress
- ✅ Export functionality

### **Secret Filtering**
Automatically filters:
- `password=secret123` → `password=***REDACTED***`
- `Bearer abc123` → `Bearer ***REDACTED***`
- `api_key=xyz789` → `api_key=***REDACTED***`

### **Version Tracking**
- ✅ Git commit tracking
- ✅ Build artifact linking
- ✅ Environment version matrix
- ✅ Deployment history

## 🌍 Supported Providers

| Category | Providers | Status |
|----------|-----------|--------|
| **Kubernetes** | GKE, AKS, EKS, OpenShift | ✅ Ready |
| **Cloud VMs** | GCE, EC2, Azure VM, DigitalOcean | 🔄 Interface Ready |
| **Serverless** | Lambda, Cloud Functions, Vercel | 🔄 Interface Ready |
| **Container** | Docker Swarm, Nomad | 🔄 Interface Ready |

## 🔧 Key Components

### **Backend Services**
- **Environment Service** (`:8083`) - Manages environments
- **Workflow Service** (`:8084`) - Executes workflows
- **Secrets Service** (`:8085`) - Handles secrets

### **Frontend Components**
- **Environment Manager** - Create/edit environments
- **Workflow Monitor** - Real-time execution tracking
- **Log Viewer** - Live log streaming

### **Database Tables**
- `environment_configs` - Environment definitions
- `workflow_executions` - Execution tracking
- `log_entries` - Persistent logs

## 🌐 API Examples

### **Create Environment**
```bash
curl -X POST /api/v1/environment-service/environments \
  -H "Content-Type: application/json" \
  -d '{
    "projectId": "proj-123",
    "name": "staging",
    "type": "kubernetes",
    "provider": {
      "type": "gke",
      "config": {
        "project": "my-project",
        "cluster": "staging-cluster"
      }
    }
  }'
```

### **Start Workflow Execution**
```bash
curl -X POST /api/v1/workflow-service/executions \
  -H "Content-Type: application/json" \
  -d '{
    "workflowId": "workflow-123",
    "environmentId": "env-456",
    "version": {
      "number": "1.2.3",
      "gitCommit": "abc123"
    }
  }'
```

### **Subscribe to Real-time Logs**
```javascript
const socket = io('/workflow-logs');
socket.emit('subscribe-logs', { executionId: 'exec-789' });
socket.on('log-entry', (log) => console.log(log));
```

## 🔍 Debugging

### **Check Service Health**
```bash
curl http://localhost:8083/health  # Environment Service
curl http://localhost:8084/health  # Workflow Service
curl http://localhost:8085/health  # Secrets Service
```

### **View Logs**
```bash
# Enable debug mode
export LOG_LEVEL=debug

# Check database connection
export DATABASE_URL="postgres://user:pass@localhost/deploy_orchestrator"
```

### **Common Issues**

**Provider Not Found:**
- Ensure provider is registered in `init()`
- Check import statement

**WebSocket Connection Failed:**
- Verify CORS settings
- Check WebSocket endpoint

**Secrets Visible in Logs:**
- Check secret filtering patterns
- Ensure logging service is processing

## 📚 Next Steps

1. **Read Full Documentation**: [ARCHITECTURE.md](ARCHITECTURE.md)
2. **Add Your Provider**: Follow the provider guide
3. **Set Up CI/CD**: Configure automated deployments
4. **Monitor Performance**: Set up metrics and alerts

## 🎯 Key Benefits

- **🚀 Faster Deployments** - Parallel execution across environments
- **🔒 Better Security** - Automatic secret masking and encryption
- **📊 Full Visibility** - Real-time logs and version tracking
- **🔧 Easy Extension** - Plugin-based provider system
- **🌍 Multi-cloud** - Deploy anywhere with consistent interface

## 💡 Pro Tips

1. **Use Environment Templates** - Create reusable environment configs
2. **Set Up Health Checks** - Monitor environment status automatically
3. **Version Everything** - Track all deployments with Git integration
4. **Monitor Logs** - Use real-time logging for debugging
5. **Test Connections** - Always verify environment connectivity

---

**Ready to deploy? Start with creating your first environment!** 🚀
