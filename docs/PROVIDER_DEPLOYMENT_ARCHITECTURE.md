# Provider-Specific Deployment Logic Architecture

## Overview

This document outlines the recommended architecture for implementing provider-specific deployment logic in the Deploy Orchestrator platform.

## Architecture Decision

We recommend a **Hybrid Plugin Architecture** that combines:
1. **Provider Executors** - Specialized step executors for each provider
2. **Workflow Templates** - Pre-built deployment workflows per provider
3. **Dynamic Step Generation** - Runtime step creation based on provider capabilities

## Implementation Strategy

### 1. Provider Executor Pattern

Each provider gets its own specialized executor that implements the `StepExecutor` interface:

```go
// Provider-specific executors
type GKEDeploymentExecutor struct {
    logger *zap.Logger
    k8sClient kubernetes.Interface
    gkeClient *gke.Client
}

type AKSDeploymentExecutor struct {
    logger *zap.Logger
    k8sClient kubernetes.Interface
    azureClient *azure.Client
}

type EKSDeploymentExecutor struct {
    logger *zap.Logger
    k8sClient kubernetes.Interface
    awsClient *aws.Client
}
```

### 2. Provider Registry

A centralized registry manages all provider executors:

```go
type ProviderRegistry struct {
    executors map[string]ProviderExecutor
    templates map[string]*WorkflowTemplate
}

type ProviderExecutor interface {
    StepExecutor
    GetProviderType() string
    GetCapabilities() []string
    ValidateConfig(config *ProviderConfig) error
    GenerateSteps(deployment *DeploymentRequest) ([]*WorkflowStep, error)
}
```

### 3. Workflow Integration

Provider executors integrate seamlessly with the existing workflow engine:

```go
// Register provider executors
engine.RegisterExecutor(&GKEDeploymentExecutor{...})
engine.RegisterExecutor(&AKSDeploymentExecutor{...})
engine.RegisterExecutor(&EKSDeploymentExecutor{...})

// Workflow steps use provider-specific types
{
    "id": "deploy-to-gke",
    "name": "Deploy to GKE",
    "type": "gke_deploy",  // Maps to GKEDeploymentExecutor
    "config": {
        "cluster": "{{.environment.cluster}}",
        "manifests": ["deployment.yaml", "service.yaml"],
        "namespace": "{{.environment.namespace}}"
    }
}
```

## Benefits of This Approach

### ✅ **Extensibility**
- Easy to add new providers by implementing `ProviderExecutor`
- No changes to core workflow engine
- Plugin-based architecture

### ✅ **Reusability**
- Provider executors can be used in any workflow
- Common deployment patterns become reusable templates
- Step composition for complex deployments

### ✅ **Flexibility**
- Mix provider-specific steps with generic steps
- Custom workflows per project/environment
- Runtime step generation based on provider capabilities

### ✅ **Maintainability**
- Provider logic isolated in dedicated executors
- Clear separation of concerns
- Easy testing and debugging

### ✅ **Performance**
- Native provider APIs (no shell scripting overhead)
- Parallel execution support
- Efficient resource utilization

## Implementation Phases

### Phase 1: Provider Executor Framework
1. Create `ProviderExecutor` interface
2. Implement `ProviderRegistry`
3. Create base provider executor with common functionality
4. Integrate with existing workflow engine

### Phase 2: Core Provider Implementations
1. Implement `GKEDeploymentExecutor`
2. Implement `AKSDeploymentExecutor`
3. Implement `EKSDeploymentExecutor`
4. Implement `OpenShiftDeploymentExecutor`

### Phase 3: Advanced Features
1. Dynamic step generation
2. Provider capability detection
3. Template marketplace integration
4. Cross-provider deployment workflows

### Phase 4: Enhanced Providers
1. VM deployment executors
2. Serverless platform executors
3. Custom provider plugin system
4. Third-party provider integrations

## Example Implementation

### GKE Deployment Executor

```go
func (e *GKEDeploymentExecutor) Execute(ctx context.Context, step *WorkflowStep, execCtx *ExecutionContext) (*StepResult, error) {
    // 1. Authenticate with GCP
    if err := e.authenticateGCP(step.Config); err != nil {
        return nil, err
    }

    // 2. Connect to GKE cluster
    cluster, err := e.getGKECluster(step.Config["cluster"].(string))
    if err != nil {
        return nil, err
    }

    // 3. Apply Kubernetes manifests
    manifests := step.Config["manifests"].([]string)
    for _, manifest := range manifests {
        if err := e.applyManifest(ctx, manifest, execCtx.Variables); err != nil {
            return nil, err
        }
    }

    // 4. Wait for deployment readiness
    if err := e.waitForDeployment(ctx, step.Config); err != nil {
        return nil, err
    }

    return &StepResult{Success: true}, nil
}
```

### Workflow Template Example

```yaml
name: "Deploy to Kubernetes"
description: "Standard Kubernetes deployment workflow"
providers: ["gke", "aks", "eks", "openshift"]
steps:
  - id: "validate-manifests"
    name: "Validate Kubernetes Manifests"
    type: "k8s_validate"
    config:
      manifests: "{{.manifests}}"

  - id: "deploy-app"
    name: "Deploy Application"
    type: "{{.provider.type}}_deploy"  # Dynamic provider type
    dependencies: ["validate-manifests"]
    config:
      cluster: "{{.environment.cluster}}"
      namespace: "{{.environment.namespace}}"
      manifests: "{{.manifests}}"

  - id: "verify-deployment"
    name: "Verify Deployment Health"
    type: "k8s_health_check"
    dependencies: ["deploy-app"]
    config:
      namespace: "{{.environment.namespace}}"
      timeout: 300
```

## Migration Strategy

### From Current Implementation

1. **Keep existing methods** as fallback during transition
2. **Gradually migrate** provider logic to new executors
3. **Maintain backward compatibility** with existing workflows
4. **Add new provider types** alongside existing ones

### Timeline

- **Week 1-2**: Framework and interfaces
- **Week 3-4**: Core provider implementations
- **Week 5-6**: Integration and testing
- **Week 7-8**: Advanced features and optimization

## Conclusion

This hybrid approach provides the best balance of:
- **Flexibility** through workflow composition
- **Performance** through native provider APIs
- **Extensibility** through plugin architecture
- **Maintainability** through clear separation of concerns

The architecture supports both simple single-provider deployments and complex multi-provider orchestration workflows.

---

# Implementation Plan

## Phase 1: WebSocket Gateway for Real-time Logs ⚡

### 1.1 Gateway WebSocket Support
- Add WebSocket upgrade capability to gateway service
- Implement WebSocket proxy for workflow logs
- Route WebSocket connections to appropriate workflow instances

### 1.2 Real-time Log Streaming
- Enhance existing logging service with WebSocket broadcasting
- Implement log filtering and secret masking
- Add connection management and cleanup

### 1.3 Frontend Integration
- Enhance existing RealtimeLoggingService
- Add WebSocket reconnection logic
- Implement log buffering and replay

## Phase 2: Provider-Specific Deployment Logic 🔧

### 2.1 Provider Executor Framework
- Create ProviderExecutor interface
- Implement ProviderRegistry
- Refactor existing deployment methods

### 2.2 Core Provider Implementations
- GKEDeploymentExecutor with real GCP integration
- AKSDeploymentExecutor with Azure integration
- EKSDeploymentExecutor with AWS integration
- OpenShiftDeploymentExecutor

### 2.3 Workflow Integration
- Register provider executors with workflow engine
- Update step type definitions
- Create provider-specific workflow templates

## Phase 3: Promotion Workflows Between Environments 🚀

### 3.1 Promotion Service
- Create promotion workflow service
- Implement environment dependency tracking
- Add approval mechanisms

### 3.2 Promotion Workflows
- Dev → Staging → Production pipelines
- Cross-environment validation
- Rollback capabilities

### 3.3 UI Integration
- Promotion workflow designer
- Environment promotion dashboard
- Approval workflow interface

## Implementation Order

1. **WebSocket Gateway** (Foundation for real-time features)
2. **Provider Executors** (Core deployment functionality)
3. **Promotion Workflows** (Advanced orchestration)
