# 🚀 Deploy Orchestrator: Environment + Workflow Architecture Documentation

## 📋 Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Backend Implementation](#backend-implementation)
3. [Frontend Implementation](#frontend-implementation)
4. [Provider System](#provider-system)
5. [Real-time Logging](#real-time-logging)
6. [Database Schema](#database-schema)
7. [API Endpoints](#api-endpoints)
8. [Adding New Providers](#adding-new-providers)
9. [Deployment Guide](#deployment-guide)
10. [Troubleshooting](#troubleshooting)

---

## 🏗️ Architecture Overview

### **Core Concepts**

The Deploy Orchestrator implements a modern **Environment + Workflow-based deployment architecture** that replaces traditional deployment objects with:

1. **Environment Configurations** - Define deployment targets (GKE, AKS, VMs, etc.)
2. **Workflow Executions** - Track deployments with version control
3. **Real-time Logging** - Live monitoring with secret filtering
4. **Extensible Providers** - Plugin-based system for new platforms

### **Key Benefits**

- ✅ **Multi-cloud Support** - Deploy to any platform
- ✅ **Version Tracking** - Complete deployment lineage
- ✅ **Real-time Monitoring** - Live logs and progress tracking
- ✅ **Extensible** - Easy to add new providers
- ✅ **Secure** - Automatic secret masking and encryption

### **System Architecture**

```mermaid
graph TB
    UI[Angular Frontend] --> Gateway[API Gateway]
    Gateway --> EnvSvc[Environment Service]
    Gateway --> WorkflowSvc[Workflow Service]
    Gateway --> SecretsSvc[Secrets Service]

    EnvSvc --> ProviderRegistry[Provider Registry]
    ProviderRegistry --> GKE[GKE Provider]
    ProviderRegistry --> AKS[AKS Provider]
    ProviderRegistry --> EKS[EKS Provider]
    ProviderRegistry --> Custom[Custom Providers...]

    WorkflowSvc --> LoggingSvc[Logging Service]
    LoggingSvc --> WebSocket[WebSocket Gateway]
    WebSocket --> UI

    EnvSvc --> DB[(PostgreSQL)]
    WorkflowSvc --> DB
    SecretsSvc --> DB
```

---

## 🔧 Backend Implementation

### **1. Environment Service**

**Location**: `backend/environment-service/`

**Purpose**: Manages deployment environment configurations

**Key Features**:
- Environment CRUD operations
- Provider validation and testing
- Health monitoring
- Connection testing

**Main Files**:
```
backend/environment-service/
├── main.go                           # Service entry point
├── internal/
│   ├── handlers/
│   │   └── environment_handler.go    # HTTP handlers
│   └── services/
│       └── environment_service.go    # Business logic
└── go.mod
```

**Example Usage**:
```go
// Create environment
env := &CreateEnvironmentRequest{
    ProjectID: "project-123",
    Name: "production",
    Type: "kubernetes",
    Provider: ProviderConfig{
        Type: "gke",
        Config: map[string]interface{}{
            "project": "my-gcp-project",
            "cluster": "prod-cluster",
            "zone": "us-central1-a",
        },
    },
}

result, err := environmentService.CreateEnvironment(ctx, env)
```

### **2. Workflow Execution Service**

**Location**: `backend/workflow-service/internal/services/execution_service.go`

**Purpose**: Manages workflow execution with version tracking

**Key Features**:
- Workflow execution lifecycle
- Step-by-step progress tracking
- Retry and skip functionality
- Artifact management

**Data Models**:
```go
type WorkflowExecution struct {
    ID             string
    WorkflowID     string
    ProjectID      string
    EnvironmentID  string
    Version        VersionInfo
    Status         ExecutionStatus
    Steps          []WorkflowStepExecution
    DeployedServices []DeployedService
    LogStreamID    string
}
```

### **3. Real-time Logging Service**

**Location**: `backend/workflow-service/internal/services/logging_service.go`

**Purpose**: Provides real-time log streaming with secret filtering

**Key Features**:
- WebSocket-based real-time streaming
- Automatic secret masking
- Log persistence and retrieval
- Filtering and search

**Secret Filtering Patterns**:
```go
var secretPatterns = []*regexp.Regexp{
    regexp.MustCompile(`(?i)password[=:\s]+[^\s\n]+`),
    regexp.MustCompile(`(?i)token[=:\s]+[^\s\n]+`),
    regexp.MustCompile(`(?i)Bearer\s+[^\s\n]+`),
    regexp.MustCompile(`(?i)api[_-]?key[=:\s]+[^\s\n]+`),
    // ... more patterns
}
```

---

## 🎨 Frontend Implementation

### **1. Environment Service**

**Location**: `frontend/deploy-orchestrator/src/app/services/environment.service.ts`

**Purpose**: Angular service for environment management

**Key Methods**:
```typescript
// Get environments with filtering
getEnvironments(filter?: EnvironmentFilter): Observable<{environments: EnvironmentConfig[], total: number}>

// Test connection to environment
testConnection(id: string): Observable<ConnectionTestResult>

// Get version matrix across environments
getVersionMatrix(projectId: string): Observable<VersionMatrix>
```

### **2. Workflow Execution Service**

**Location**: `frontend/deploy-orchestrator/src/app/services/workflow-execution.service.ts`

**Purpose**: Manages workflow executions and monitoring

**Key Methods**:
```typescript
// Start new execution
startExecution(request: StartExecutionRequest): Observable<WorkflowExecution>

// Get execution with real-time updates
getExecution(id: string): Observable<WorkflowExecution>

// Cancel running execution
cancelExecution(id: string, reason: string): Observable<void>
```

### **3. Real-time Logging Service**

**Location**: `frontend/deploy-orchestrator/src/app/services/realtime-logging.service.ts`

**Purpose**: WebSocket-based real-time log streaming

**Key Features**:
```typescript
// Subscribe to real-time logs
subscribeToLogs(executionId: string): Observable<LogEntry>

// Filter and search logs
filterLogs(logs: LogEntry[], config: LogViewerConfig): LogEntry[]

// Export logs to file
exportLogs(logs: LogEntry[], filename?: string): void
```

### **4. Environment Management Component**

**Location**: `frontend/deploy-orchestrator/src/app/components/environments/environments.component.ts`

**Purpose**: UI for managing environments

**Features**:
- Environment listing and filtering
- Create/edit/delete environments
- Connection testing
- Health monitoring
- Provider selection

### **5. Workflow Execution Component**

**Location**: `frontend/deploy-orchestrator/src/app/components/workflow-execution/workflow-execution.component.ts`

**Purpose**: Real-time workflow execution monitoring

**Features**:
- Live execution progress
- Step-by-step monitoring
- Real-time log streaming
- Retry/skip operations
- Export functionality

---

## 🔌 Provider System

### **Architecture**

The provider system uses a **plugin-based architecture** that allows easy addition of new deployment targets without modifying core code.

### **Provider Interface**

**Location**: `backend/shared/src/providers/provider_interface.go`

```go
type Provider interface {
    GetInfo() ProviderInfo
    ValidateConfig(config map[string]interface{}) error
    TestConnection(ctx context.Context, config map[string]interface{}) (*ConnectionTestResult, error)
    Deploy(ctx context.Context, config map[string]interface{}, deployment DeploymentRequest) (*DeploymentResult, error)
    GetStatus(ctx context.Context, config map[string]interface{}, deploymentID string) (*DeploymentStatus, error)
    GetLogs(ctx context.Context, config map[string]interface{}, deploymentID string, options LogOptions) ([]LogEntry, error)
    Scale(ctx context.Context, config map[string]interface{}, deploymentID string, replicas int) error
    Delete(ctx context.Context, config map[string]interface{}, deploymentID string) error
    GetMetrics(ctx context.Context, config map[string]interface{}, deploymentID string) (map[string]interface{}, error)
}
```

### **Provider Registry**

**Location**: `backend/shared/src/providers/registry.go`

**Purpose**: Manages provider discovery and instantiation

```go
// Register a provider
providers.Register(NewGKEProvider())

// Get provider by type
provider, err := providers.Get(providers.ProviderGKE)

// List all providers
allProviders := providers.List()

// Get providers by capability
k8sProviders := providers.GetProvidersByCapability(providers.CapabilityContainers)
```

### **Supported Providers**

| Category | Providers | Status |
|----------|-----------|--------|
| **Kubernetes** | GKE, AKS, EKS, OpenShift, K3s, MicroK8s, Rancher | ✅ Interface Defined |
| **Cloud VMs** | GCE, EC2, Azure VM, DigitalOcean, Linode, Vultr | ✅ Interface Defined |
| **Serverless** | Lambda, Cloud Functions, Azure Functions, Vercel, Netlify | ✅ Interface Defined |
| **Container** | Docker Swarm, Nomad, Mesos | ✅ Interface Defined |
| **Edge** | Cloudflare Workers, AWS Wavelength, Azure Edge | ✅ Interface Defined |
| **Hybrid** | Bare Metal, VMware, Hyper-V, Proxmox, OpenStack | ✅ Interface Defined |
| **CI/CD** | GitHub Actions, GitLab CI, Jenkins, CircleCI | ✅ Interface Defined |

### **Provider Configuration**

Each provider defines its configuration fields dynamically:

```go
ConfigFields: []providers.ConfigField{
    {
        Name:        "project",
        Type:        "string",
        Label:       "GCP Project ID",
        Description: "Google Cloud Platform project identifier",
        Required:    true,
        Group:       "authentication",
        Validation: providers.Validation{
            Pattern: "^[a-z][a-z0-9-]{4,28}[a-z0-9]$",
        },
    },
    {
        Name:        "cluster",
        Type:        "string",
        Label:       "Cluster Name",
        Required:    true,
        Group:       "cluster",
    },
    {
        Name:        "zone",
        Type:        "select",
        Label:       "Zone",
        Options: []providers.Option{
            {Value: "us-central1-a", Label: "us-central1-a"},
            {Value: "us-east1-a", Label: "us-east1-a"},
        },
    },
}
```

---

## 📡 Real-time Logging

### **WebSocket Architecture**

```mermaid
sequenceDiagram
    participant UI as Frontend
    participant WS as WebSocket Gateway
    participant LS as Logging Service
    participant WE as Workflow Engine

    UI->>WS: Connect to /workflow-logs
    UI->>WS: Subscribe to execution logs
    WE->>LS: Emit log entry
    LS->>LS: Filter secrets
    LS->>WS: Send filtered log
    WS->>UI: Real-time log entry
```

### **Secret Filtering**

The logging service automatically filters sensitive information:

**Filtered Patterns**:
- Passwords: `password=secret123` → `password=***REDACTED***`
- Tokens: `Bearer abc123` → `Bearer ***REDACTED***`
- API Keys: `api_key=xyz789` → `api_key=***REDACTED***`
- JWT Tokens: `eyJ...` → `***REDACTED_JWT***`
- Base64 Secrets: Long base64 strings → `***REDACTED_BASE64***`

### **Log Persistence**

Logs are stored in PostgreSQL with indexing for efficient retrieval:

```sql
CREATE TABLE log_entries (
    id VARCHAR PRIMARY KEY,
    stream_id VARCHAR NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    level VARCHAR NOT NULL,
    step_name VARCHAR NOT NULL,
    message TEXT NOT NULL,
    source VARCHAR,
    context JSONB,
    progress INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_log_entries_stream_id ON log_entries(stream_id);
CREATE INDEX idx_log_entries_timestamp ON log_entries(timestamp);
CREATE INDEX idx_log_entries_level ON log_entries(level);
```

---

## 🗄️ Database Schema

### **Environment Configuration**

```sql
CREATE TABLE environment_configs (
    id VARCHAR PRIMARY KEY,
    project_id VARCHAR NOT NULL,
    name VARCHAR NOT NULL,
    type VARCHAR NOT NULL,
    provider JSONB NOT NULL,
    resources JSONB NOT NULL,
    networking JSONB NOT NULL,
    variables JSONB,
    secret_mappings JSONB,
    health_check JSONB,
    deployment_strategy VARCHAR,
    status VARCHAR DEFAULT 'inactive',
    description TEXT,
    tags JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    FOREIGN KEY (project_id) REFERENCES projects(id)
);
```

### **Workflow Execution**

```sql
CREATE TABLE workflow_executions (
    id VARCHAR PRIMARY KEY,
    workflow_id VARCHAR NOT NULL,
    project_id VARCHAR NOT NULL,
    environment_id VARCHAR NOT NULL,
    version JSONB NOT NULL,
    status VARCHAR DEFAULT 'pending',
    started_at TIMESTAMP NOT NULL,
    completed_at TIMESTAMP,
    duration BIGINT,
    trigger_type VARCHAR NOT NULL,
    trigger_by VARCHAR NOT NULL,
    parameters JSONB,
    variables JSONB,
    steps JSONB NOT NULL,
    deployed_services JSONB,
    log_stream_id VARCHAR NOT NULL,
    metrics_enabled BOOLEAN DEFAULT true,
    error_message TEXT,
    error_details TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    FOREIGN KEY (workflow_id) REFERENCES workflows(id),
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (environment_id) REFERENCES environment_configs(id)
);
```

### **Environment Health**

```sql
CREATE TABLE environment_health (
    environment_id VARCHAR PRIMARY KEY,
    status VARCHAR NOT NULL,
    last_check TIMESTAMP NOT NULL,
    metrics JSONB,
    issues JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    FOREIGN KEY (environment_id) REFERENCES environment_configs(id)
);
```

---

## 🌐 API Endpoints

### **Environment Service**

**Base URL**: `/api/v1/environment-service`

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/environments` | List environments with filtering |
| `POST` | `/environments` | Create new environment |
| `GET` | `/environments/{id}` | Get environment details |
| `PUT` | `/environments/{id}` | Update environment |
| `DELETE` | `/environments/{id}` | Delete environment |
| `POST` | `/environments/{id}/test-connection` | Test environment connection |
| `GET` | `/environments/{id}/status` | Get environment status |
| `GET` | `/projects/{projectId}/environments` | List project environments |
| `GET` | `/health/environments` | Get all environment health |
| `GET` | `/health/environments/{id}` | Get specific environment health |
| `POST` | `/health/environments/{id}/check` | Trigger health check |
| `GET` | `/providers` | List available providers |
| `GET` | `/providers/{type}/capabilities` | Get provider capabilities |
| `POST` | `/providers/{type}/validate-config` | Validate provider config |
| `GET` | `/versions/matrix/{projectId}` | Get version matrix |
| `GET` | `/versions/history/{environmentId}` | Get deployment history |

### **Workflow Execution Service**

**Base URL**: `/api/v1/workflow-service`

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/executions` | Start workflow execution |
| `GET` | `/executions` | List executions with filtering |
| `GET` | `/executions/{id}` | Get execution details |
| `POST` | `/executions/{id}/cancel` | Cancel execution |
| `POST` | `/executions/{id}/retry` | Retry execution |
| `GET` | `/executions/{id}/logs` | Get execution logs |
| `GET` | `/executions/{id}/metrics` | Get execution metrics |
| `POST` | `/executions/{id}/steps/{stepId}/retry` | Retry specific step |
| `POST` | `/executions/{id}/steps/{stepId}/skip` | Skip specific step |
| `GET` | `/projects/{projectId}/executions` | List project executions |
| `GET` | `/environments/{environmentId}/executions` | List environment executions |

### **WebSocket Endpoints**

| Endpoint | Purpose |
|----------|---------|
| `/workflow-logs` | Real-time log streaming |

**WebSocket Events**:
```typescript
// Subscribe to logs
socket.emit('subscribe-logs', { executionId: 'exec-123' });

// Receive log entries
socket.on('log-entry', (logEntry: LogEntry) => {
    console.log(logEntry);
});
```

---

## ➕ Adding New Providers

### **Step 1: Create Provider Implementation**

Create a new file: `backend/shared/src/providers/mycloud/mycloud_provider.go`

```go
package mycloud

import (
    "context"
    "github.com/claudio/deploy-orchestrator/shared/providers"
)

type MyCloudProvider struct{}

func NewMyCloudProvider() providers.Provider {
    return &MyCloudProvider{}
}

func (p *MyCloudProvider) GetInfo() providers.ProviderInfo {
    return providers.ProviderInfo{
        Type:        "mycloud",
        Name:        "My Cloud Platform",
        Description: "Deploy to My Cloud Platform",
        Category:    providers.CategoryVM,
        Capabilities: []providers.ProviderCapability{
            providers.CapabilityContainers,
            providers.CapabilityLoadBalancing,
        },
        AuthMethods: []providers.AuthMethod{
            providers.AuthAPIKey,
        },
        ConfigFields: []providers.ConfigField{
            {
                Name:        "apiKey",
                Type:        "password",
                Label:       "API Key",
                Description: "Your My Cloud API key",
                Required:    true,
                Sensitive:   true,
                Group:       "authentication",
            },
            {
                Name:        "region",
                Type:        "select",
                Label:       "Region",
                Required:    true,
                Options: []providers.Option{
                    {Value: "us-east-1", Label: "US East 1"},
                    {Value: "eu-west-1", Label: "EU West 1"},
                },
            },
        },
    }
}

func (p *MyCloudProvider) ValidateConfig(config map[string]interface{}) error {
    if apiKey, ok := config["apiKey"].(string); !ok || apiKey == "" {
        return fmt.Errorf("API key is required")
    }
    return nil
}

func (p *MyCloudProvider) TestConnection(ctx context.Context, config map[string]interface{}) (*providers.ConnectionTestResult, error) {
    // Implement connection test
    return &providers.ConnectionTestResult{
        Success: true,
        Message: "Connection successful",
        TestedAt: time.Now(),
    }, nil
}

// Implement other required methods...
```

### **Step 2: Register Provider**

Add to the provider's `init()` function:

```go
func init() {
    providers.RegisterFactory("mycloud", func() providers.Provider {
        return NewMyCloudProvider()
    })
}
```

### **Step 3: Import Provider**

Add import to environment service:

```go
import (
    _ "github.com/claudio/deploy-orchestrator/shared/providers/mycloud"
)
```

### **Step 4: Frontend Auto-Discovery**

The frontend will automatically discover the new provider through the API. No frontend changes needed!

**Result**: The new provider will appear in the UI with:
- ✅ Auto-generated configuration form
- ✅ Validation rules
- ✅ Connection testing
- ✅ Proper categorization

---

## 🚀 Deployment Guide

### **Prerequisites**

- **Go 1.21+** for backend services
- **Node.js 18+** for frontend
- **PostgreSQL 14+** for database
- **Docker** (optional, for containerized deployment)

### **Backend Deployment**

1. **Build Services**:
```bash
# Environment Service
cd backend/environment-service
go build -o environment-service main.go

# Workflow Service
cd backend/workflow-service
go build -o workflow-service main.go

# Secrets Service
cd backend/secrets-service
go build -o secrets-service main.go
```

2. **Database Setup**:
```sql
-- Create database
CREATE DATABASE deploy_orchestrator;

-- Run migrations
psql -d deploy_orchestrator -f migrations/001_initial.sql
psql -d deploy_orchestrator -f migrations/002_environments.sql
psql -d deploy_orchestrator -f migrations/003_executions.sql
```

3. **Environment Variables**:
```bash
export DATABASE_URL="postgres://user:pass@localhost/deploy_orchestrator"
export JWT_SECRET="your-jwt-secret"
export PORT=8080
```

4. **Start Services**:
```bash
# Start each service
./environment-service &
./workflow-service &
./secrets-service &
```

### **Frontend Deployment**

1. **Build Frontend**:
```bash
cd frontend/deploy-orchestrator
npm install
npm run build --prod
```

2. **Serve Static Files**:
```bash
# Using nginx
cp -r dist/* /var/www/html/

# Or using Node.js
npm install -g serve
serve -s dist -l 4200
```

### **Docker Deployment**

1. **Build Images**:
```bash
# Backend services
docker build -t deploy-orchestrator/environment-service backend/environment-service
docker build -t deploy-orchestrator/workflow-service backend/workflow-service
docker build -t deploy-orchestrator/secrets-service backend/secrets-service

# Frontend
docker build -t deploy-orchestrator/frontend frontend/deploy-orchestrator
```

2. **Docker Compose**:
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: deploy_orchestrator
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  environment-service:
    image: deploy-orchestrator/environment-service
    environment:
      DATABASE_URL: *********************************************************
      JWT_SECRET: your-jwt-secret
    depends_on:
      - postgres

  workflow-service:
    image: deploy-orchestrator/workflow-service
    environment:
      DATABASE_URL: *********************************************************
      JWT_SECRET: your-jwt-secret
    depends_on:
      - postgres

  secrets-service:
    image: deploy-orchestrator/secrets-service
    environment:
      DATABASE_URL: *********************************************************
      JWT_SECRET: your-jwt-secret
    depends_on:
      - postgres

  frontend:
    image: deploy-orchestrator/frontend
    ports:
      - "4200:80"
    depends_on:
      - environment-service
      - workflow-service
      - secrets-service

volumes:
  postgres_data:
```

---

## 🔧 Troubleshooting

### **Common Issues**

**1. Provider Not Found**
```
Error: provider mycloud not found
```
**Solution**: Ensure provider is registered in `init()` function and imported.

**2. WebSocket Connection Failed**
```
Error: WebSocket connection failed
```
**Solution**: Check CORS settings and WebSocket endpoint configuration.

**3. Database Connection Error**
```
Error: failed to connect to database
```
**Solution**: Verify DATABASE_URL and ensure PostgreSQL is running.

**4. Secret Filtering Not Working**
```
Secrets visible in logs
```
**Solution**: Check secret filtering patterns and ensure logging service is processing logs.

### **Debug Mode**

Enable debug logging:

```bash
export LOG_LEVEL=debug
export DEBUG=true
```

### **Health Checks**

Check service health:

```bash
# Environment Service
curl http://localhost:8083/health

# Workflow Service
curl http://localhost:8084/health

# Secrets Service
curl http://localhost:8085/health
```

### **Performance Tuning**

**Database Optimization**:
```sql
-- Add indexes for better performance
CREATE INDEX idx_environments_project_id ON environment_configs(project_id);
CREATE INDEX idx_executions_status ON workflow_executions(status);
CREATE INDEX idx_executions_environment_id ON workflow_executions(environment_id);
```

**WebSocket Optimization**:
- Limit concurrent connections
- Implement connection pooling
- Add rate limiting

---

## 📚 Additional Resources

- **API Documentation**: `/docs/api/`
- **Provider Development Guide**: `/docs/providers/`
- **Deployment Examples**: `/examples/`
- **Contributing Guide**: `/CONTRIBUTING.md`
- **Security Guide**: `/docs/SECURITY.md`

---

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests
5. Update documentation
6. Submit a pull request

For provider contributions, see the [Provider Development Guide](docs/providers/DEVELOPMENT.md).

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
