# Production Monitoring & Alerting Guide

This guide provides comprehensive instructions for setting up and managing monitoring and alerting for the Deploy Orchestrator secret mapping functionality in production.

## 🚀 Quick Setup

### **Prerequisites**
- Docker and Docker Compose installed
- Minimum 4GB RAM and 20GB disk space
- Network access to SMTP server and Slack (for notifications)

### **One-Command Setup**
```bash
./scripts/setup-monitoring.sh
```

This script will:
- ✅ Set up Prometheus, Grafana, Alertmanager
- ✅ Configure log aggregation with Loki
- ✅ Set up distributed tracing with Jaeger
- ✅ Create monitoring dashboards
- ✅ Configure alerting rules
- ✅ Set up audit logging

## 📊 Monitoring Stack Components

### **Core Monitoring**
- **Prometheus**: Metrics collection and storage
- **Grafana**: Visualization and dashboards
- **Alertmanager**: Alert routing and notifications
- **Node Exporter**: System metrics
- **Blackbox Exporter**: Endpoint monitoring

### **Logging & Tracing**
- **Loki**: Log aggregation
- **Promtail**: Log collection
- **Jaeger**: Distributed tracing

### **Database Monitoring**
- **PostgreSQL Exporter**: Database metrics
- **Redis Exporter**: Cache metrics

## 🔍 Key Metrics Monitored

### **Secret Mapping Metrics**
```prometheus
# Secret retrieval performance
secret_retrieval_duration_seconds
secret_retrieval_total{status="success|error"}

# Secret mapping validation
secret_mapping_validation_total{template_id, project_id, status}
secret_mapping_errors_total{error_type, project_id}

# Workflow secret injection
workflow_secret_injections_total{workflow_id, project_id, status}
```

### **Security Metrics**
```prometheus
# Security violations
unauthorized_secret_access_attempts_total{user_id, project_id, source_ip}
cross_project_access_attempts_total{user_id, source_project, target_project}
expired_secret_access_attempts_total{user_id, project_id, secret_id}
```

### **Performance Metrics**
```prometheus
# HTTP performance
http_request_duration_seconds{service, endpoint}
http_requests_total{service, status_code}

# Database performance
db_query_duration_seconds{operation, database}
db_connections_open{service}
```

## 🚨 Critical Alerts

### **Security Alerts (Immediate Response)**
- **UnauthorizedSecretAccess**: Any unauthorized access attempt
- **CrossProjectAccessAttempt**: Cross-project boundary violations
- **HighSecretMappingErrorRate**: Unusual error patterns

### **Performance Alerts**
- **SlowSecretRetrieval**: P95 > 1 second
- **SecretRetrievalFailureRate**: > 5% failure rate
- **WorkflowSecretInjectionFailures**: Multiple injection failures

### **Service Health Alerts**
- **ServiceDown**: Any service unavailable > 1 minute
- **HighErrorRate**: > 5% HTTP 5xx errors
- **DatabaseConnectionPoolExhausted**: > 90% pool usage

## 📈 Dashboards

### **Secret Mapping Dashboard**
- **URL**: `http://localhost:3000/d/secret-mapping`
- **Panels**:
  - Secret retrieval rate and success rate
  - Unauthorized access attempts
  - Secret mapping validation metrics
  - Performance percentiles
  - Security events timeline

### **Service Overview Dashboard**
- **URL**: `http://localhost:3000/d/service-overview`
- **Panels**:
  - Service health status
  - Request rates and error rates
  - Response time percentiles
  - Resource utilization

### **Security Dashboard**
- **URL**: `http://localhost:3000/d/security`
- **Panels**:
  - Security events by type
  - Failed authentication attempts
  - Suspicious activity patterns
  - Audit log analysis

## 🔔 Alert Routing

### **Notification Channels**

**Critical Security Alerts**:
- 📧 Email: `<EMAIL>`
- 💬 Slack: `#security-alerts`
- 📱 PagerDuty: Immediate escalation

**Performance Alerts**:
- 📧 Email: `<EMAIL>`
- 💬 Slack: `#performance-alerts`

**Business Metrics**:
- 📧 Email: `<EMAIL>`
- 💬 Slack: `#business-metrics`

### **Escalation Policy**
1. **Immediate** (0-5 min): Slack notification
2. **Short-term** (5-15 min): Email notification
3. **Critical** (15+ min): PagerDuty escalation

## 📝 Audit Logging

### **Audit Events Tracked**
- Secret retrieval operations
- Secret mapping validations
- Unauthorized access attempts
- Cross-project access attempts
- Workflow secret injections
- Secret rotation events
- Secret CRUD operations

### **Log Format**
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "event_type": "secret_retrieval",
  "user_id": "user123",
  "username": "john.doe",
  "project_id": "proj456",
  "secret_name": "DEV_USERNAME",
  "source_ip": "*************",
  "success": true,
  "workflow_id": "wf789",
  "secret_mapping": {
    "SSH_USERNAME": "DEV_USERNAME"
  }
}
```

### **Log Retention**
- **Audit logs**: 7 years (compliance requirement)
- **Application logs**: 90 days
- **Metrics**: 30 days (Prometheus), 1 year (remote storage)

## 🔧 Configuration Management

### **Environment Variables**
```bash
# Monitoring configuration
PROMETHEUS_RETENTION_TIME=30d
PROMETHEUS_RETENTION_SIZE=50GB
GRAFANA_ADMIN_PASSWORD=secure_password
ALERTMANAGER_SMTP_PASSWORD=smtp_password

# Application monitoring
METRICS_ENABLED=true
AUDIT_LOGGING_ENABLED=true
LOG_LEVEL=info
```

### **Service Configuration**
Each service should include monitoring configuration:
```yaml
monitoring:
  enabled: true
  metrics_port: 8080
  metrics_path: /metrics
  audit_logging: true
  log_level: info
```

## 🚀 Deployment Steps

### **1. Initial Setup**
```bash
# Clone monitoring configuration
git clone <monitoring-config-repo>

# Set environment variables
export ENVIRONMENT=production
export DOMAIN=deploy-orchestrator.example.com

# Run setup script
./scripts/setup-monitoring.sh
```

### **2. Configure Secrets**
```bash
# SMTP credentials
echo "smtp_password" > monitoring/secrets/smtp_password

# Slack webhook
echo "https://hooks.slack.com/..." > monitoring/secrets/slack_webhook_url

# PagerDuty service key
echo "pagerduty_key" > monitoring/secrets/pagerduty_service_key
```

### **3. Start Services**
```bash
cd monitoring
docker-compose up -d
```

### **4. Verify Setup**
```bash
# Check service health
curl http://localhost:9090/-/healthy  # Prometheus
curl http://localhost:3000/api/health # Grafana
curl http://localhost:9093/-/healthy  # Alertmanager

# Test alerts
curl -X POST http://localhost:9093/api/v1/alerts
```

## 📊 Performance Tuning

### **Prometheus Optimization**
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

storage:
  tsdb:
    retention.time: 30d
    retention.size: 50GB
    wal-compression: true
```

### **Grafana Optimization**
```yaml
# grafana.ini
[database]
type = postgres
host = postgres:5432

[caching]
enabled = true

[alerting]
enabled = true
execute_alerts = true
```

### **Resource Requirements**

**Minimum Production Setup**:
- **CPU**: 4 cores
- **RAM**: 8GB
- **Disk**: 100GB SSD
- **Network**: 1Gbps

**Recommended Production Setup**:
- **CPU**: 8 cores
- **RAM**: 16GB
- **Disk**: 500GB SSD
- **Network**: 10Gbps

## 🔍 Troubleshooting

### **Common Issues**

**High Memory Usage**:
```bash
# Check Prometheus memory usage
docker stats prometheus

# Reduce retention if needed
--storage.tsdb.retention.time=15d
```

**Missing Metrics**:
```bash
# Check service discovery
curl http://localhost:9090/api/v1/targets

# Verify service metrics endpoint
curl http://service:8080/metrics
```

**Alert Not Firing**:
```bash
# Check alert rules
curl http://localhost:9090/api/v1/rules

# Verify Alertmanager config
curl http://localhost:9093/api/v1/status
```

### **Log Analysis**
```bash
# View audit logs
tail -f logs/deploy-orchestrator/secret-audit.log

# Search for security events
grep "unauthorized_access" logs/deploy-orchestrator/secret-audit.log

# Analyze error patterns
grep "ERROR" logs/deploy-orchestrator/*.log | sort | uniq -c
```

## 🔒 Security Considerations

### **Access Control**
- Grafana admin access restricted to ops team
- Prometheus query access via authentication
- Alertmanager webhook endpoints secured
- Log files with appropriate permissions (600)

### **Data Protection**
- Secret values never logged in plain text
- Audit logs encrypted at rest
- Network traffic encrypted (TLS)
- Regular security scans of monitoring infrastructure

### **Compliance**
- SOC 2 Type II compliance for audit logging
- GDPR compliance for user data in logs
- Regular audit log reviews
- Incident response procedures documented

## 📚 Runbooks

### **Security Incident Response**
1. **Immediate**: Isolate affected systems
2. **Investigation**: Analyze audit logs and metrics
3. **Containment**: Block malicious IPs/users
4. **Recovery**: Restore normal operations
5. **Post-incident**: Update security measures

### **Performance Degradation**
1. **Detection**: Monitor alerts and dashboards
2. **Diagnosis**: Check resource utilization
3. **Mitigation**: Scale resources or optimize queries
4. **Resolution**: Apply permanent fixes
5. **Prevention**: Update capacity planning

The monitoring and alerting system is now **production-ready** and provides comprehensive visibility into the secret mapping functionality! 🎉
