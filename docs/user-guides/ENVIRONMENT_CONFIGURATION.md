# Environment Configuration User Guide

## Overview

The Environment Configuration system allows you to create and manage deployment environments for your projects. This guide covers how to configure environments using various providers like Kubernetes, Azure AKS, Google GKE, and more.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Provider Types](#provider-types)
3. [Configuration Wizard](#configuration-wizard)
4. [Secret Integration](#secret-integration)
5. [Environment Management](#environment-management)
6. [Troubleshooting](#troubleshooting)

## Getting Started

### Prerequisites

- Project access with environment management permissions
- Configured secrets for provider credentials (if required)
- Understanding of your target deployment platform

### Accessing Environment Configuration

1. Navigate to your project dashboard
2. Click on **"Environments"** in the sidebar
3. Click **"Add Environment"** to start the configuration wizard

## Provider Types

### Available Providers

#### Container Orchestration
- **Kubernetes Cluster**: Deploy to any Kubernetes cluster
- **OpenShift**: Deploy to Red Hat OpenShift clusters

#### Cloud Managed Services
- **Azure Kubernetes Service (AKS)**: Deploy to Azure AKS clusters
- **Google Kubernetes Engine (GKE)**: Deploy to Google Cloud GKE clusters
- **Amazon Elastic Kubernetes Service (EKS)**: Deploy to AWS EKS clusters

#### Serverless Platforms
- **Azure Container Instances**: Deploy containers to Azure ACI
- **Google Cloud Run**: Deploy to Google Cloud Run
- **AWS Fargate**: Deploy to AWS Fargate

#### Traditional Infrastructure
- **Linux VM**: Deploy to Linux virtual machines
- **Docker Swarm**: Deploy to Docker Swarm clusters

### Provider Capabilities

Each provider supports different capabilities:

| Provider | Auto-Scaling | Load Balancing | Service Discovery | Monitoring |
|----------|--------------|----------------|-------------------|------------|
| Kubernetes | ✅ | ✅ | ✅ | ✅ |
| Azure AKS | ✅ | ✅ | ✅ | ✅ |
| Google GKE | ✅ | ✅ | ✅ | ✅ |
| AWS EKS | ✅ | ✅ | ✅ | ✅ |
| OpenShift | ✅ | ✅ | ✅ | ✅ |
| Azure ACI | ✅ | ❌ | ❌ | ✅ |
| Cloud Run | ✅ | ✅ | ❌ | ✅ |
| Linux VM | ❌ | ❌ | ❌ | ✅ |

## Configuration Wizard

### Step 1: Provider Selection

1. **Browse Available Providers**: View all available environment providers
2. **Filter by Category**: Use category filters to find specific provider types
3. **Review Capabilities**: Check provider capabilities and requirements
4. **Select Provider**: Click on your desired provider to proceed

### Step 2: Basic Configuration

Configure basic environment settings:

#### Required Fields
- **Environment Name**: Unique name for your environment
- **Description**: Brief description of the environment purpose
- **Project**: Target project (auto-selected)

#### Optional Fields
- **Tags**: Labels for organization and filtering
- **Priority**: Environment priority level (Low, Medium, High, Critical)

### Step 3: Provider Configuration

Configure provider-specific settings based on your selected provider:

#### Kubernetes Cluster Example
```yaml
Cluster Configuration:
  - Cluster Name: production-cluster
  - Namespace: my-app-prod
  - Kubeconfig: [Select from secrets or upload]

Resource Limits:
  - CPU Limit: 2000m
  - Memory Limit: 4Gi
  - Storage: 10Gi

Networking:
  - Service Type: LoadBalancer
  - Ingress Class: nginx
  - TLS Enabled: true
```

#### Azure AKS Example
```yaml
AKS Configuration:
  - Subscription ID: [Select from secrets]
  - Resource Group: my-rg-prod
  - Cluster Name: aks-prod-cluster
  - Node Pool: default

Authentication:
  - Service Principal: [Select from secrets]
  - Client ID: [From secret]
  - Client Secret: [From secret]
```

### Step 4: Secret Integration

Configure secrets and credentials:

1. **Select Required Secrets**: Choose secrets needed for provider authentication
2. **Map Secret Fields**: Map secret keys to configuration fields
3. **Validate Access**: Test secret access and permissions
4. **Review Security**: Ensure proper secret scoping and access controls

### Step 5: Review and Create

1. **Review Configuration**: Verify all settings are correct
2. **Validate Configuration**: Run provider-specific validation
3. **Test Connection**: Test connectivity to target environment
4. **Create Environment**: Finalize environment creation

## Secret Integration

### Supported Secret Types

- **Kubernetes**: Kubeconfig files, service account tokens
- **Azure**: Service principal credentials, subscription keys
- **Google Cloud**: Service account JSON keys, API keys
- **AWS**: Access keys, IAM roles, session tokens
- **Generic**: API keys, passwords, certificates

### Secret Configuration

#### Adding Secrets to Project

1. Navigate to **Project Settings** → **Secrets**
2. Click **"Add Secret"**
3. Configure secret provider and values
4. Set appropriate access scopes

#### Using Secrets in Environment Configuration

1. **Field Detection**: System automatically detects fields that can use secrets
2. **Secret Selection**: Choose from available project secrets
3. **Key Mapping**: Map specific secret keys to configuration fields
4. **Validation**: Verify secret access and format

#### Example Secret Usage

```yaml
# Kubernetes Configuration with Secrets
apiVersion: v1
kind: Environment
metadata:
  name: production-k8s
spec:
  provider: kubernetes
  configuration:
    clusterName: prod-cluster
    namespace: my-app
    credentials:
      kubeconfig: 
        secretRef:
          name: k8s-prod-credentials
          key: kubeconfig
    registry:
      credentials:
        secretRef:
          name: docker-registry-creds
          key: dockerconfig
```

## Environment Management

### Environment Lifecycle

1. **Creation**: Initial environment setup and configuration
2. **Validation**: Provider connectivity and configuration testing
3. **Active**: Environment ready for deployments
4. **Maintenance**: Updates, scaling, and configuration changes
5. **Decommission**: Environment cleanup and removal

### Environment Operations

#### Updating Configuration
1. Navigate to environment details
2. Click **"Edit Configuration"**
3. Modify settings using the configuration wizard
4. Validate and apply changes

#### Environment Health Monitoring
- **Connection Status**: Provider connectivity status
- **Resource Usage**: CPU, memory, storage utilization
- **Deployment Status**: Active deployments and their health
- **Alerts**: Configuration and operational alerts

#### Environment Cloning
1. Select source environment
2. Click **"Clone Environment"**
3. Modify configuration as needed
4. Create new environment with copied settings

## Troubleshooting

### Common Issues

#### Provider Connection Failures

**Symptoms**: Cannot connect to provider, authentication errors

**Solutions**:
1. Verify secret credentials are correct and not expired
2. Check network connectivity to provider endpoints
3. Validate provider-specific permissions and access controls
4. Review provider service status and availability

#### Configuration Validation Errors

**Symptoms**: Configuration validation fails, invalid parameters

**Solutions**:
1. Review provider documentation for required parameters
2. Check parameter formats and value constraints
3. Validate secret references and key mappings
4. Ensure all required fields are populated

#### Secret Access Issues

**Symptoms**: Cannot access secrets, permission denied errors

**Solutions**:
1. Verify user has access to project secrets
2. Check secret provider connectivity and authentication
3. Validate secret key names and formats
4. Review project-level secret permissions

### Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| ENV001 | Provider not found | Check provider name and availability |
| ENV002 | Invalid configuration | Review configuration parameters |
| ENV003 | Secret access denied | Verify secret permissions |
| ENV004 | Connection timeout | Check network connectivity |
| ENV005 | Authentication failed | Verify credentials and secrets |

### Getting Help

1. **Documentation**: Check provider-specific documentation
2. **Support**: Contact system administrators
3. **Community**: Check community forums and knowledge base
4. **Logs**: Review environment and deployment logs for details

## Best Practices

### Security
- Use secrets for all sensitive configuration data
- Implement least-privilege access controls
- Regularly rotate credentials and certificates
- Monitor secret access and usage

### Configuration Management
- Use descriptive names and documentation
- Implement configuration versioning
- Test configurations in non-production environments
- Maintain configuration backups

### Monitoring and Maintenance
- Set up health checks and monitoring
- Implement automated alerting
- Plan for capacity scaling
- Regular security and compliance reviews

### Performance Optimization
- Right-size resource allocations
- Implement auto-scaling where appropriate
- Monitor and optimize resource usage
- Use appropriate storage and networking configurations
