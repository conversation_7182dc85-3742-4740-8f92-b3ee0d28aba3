# Environment variables for Deploy Orchestrator

# Service Authentication Token for Gateway Registration
# This token is used by microservices to authenticate with the API Gateway
SERVICE_AUTH_TOKEN=your-service-auth-token-change-in-production

# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=deploy_orchestrator

# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key-change-in-production

# Admin Service Configuration
ADMIN_SERVICE_URL=http://admin-service:8080

# Gateway Service Configuration
GATEWAY_URL=http://gateway-service:8000

# Service Versions
SERVICE_VERSION=1.0.0
ENVIRONMENT=development
REGION=local

# Optional: Override individual service tokens
# ADMIN_SERVICE_TOKEN=admin-service-specific-token
# DEPLOYMENT_SERVICE_TOKEN=deployment-service-specific-token
# SCHEDULING_SERVICE_TOKEN=scheduling-service-specific-token
# NOTIFICATION_SERVICE_TOKEN=notification-service-specific-token
# INTEGRATION_SERVICE_TOKEN=integration-service-specific-token
# AUDIT_SERVICE_TOKEN=audit-service-specific-token
