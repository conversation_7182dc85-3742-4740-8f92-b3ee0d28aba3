// Foreign Key Constraint Debug Script
// Run this in browser console to debug the project ID issue

console.log('🔍 Foreign Key Constraint Debug Script');
console.log('=====================================');

// Test 1: Check current project selection
function testCurrentProjectSelection() {
    console.log('\n1. Testing Current Project Selection...');
    
    const comp = angular.element(document.querySelector('app-environment-config'))?.componentInstance;
    if (!comp) {
        console.log('❌ Environment config component not found');
        return null;
    }
    
    console.log('Component project IDs:');
    console.log('  currentProjectId:', comp.currentProjectId);
    console.log('  input projectId:', comp.projectId);
    
    const selectedProject = comp.projectService.getSelectedProject();
    console.log('Selected project from service:', selectedProject);
    
    if (selectedProject) {
        console.log('✅ Project selected:', {
            id: selectedProject.id,
            name: selectedProject.name,
            description: selectedProject.description,
            isActive: selectedProject.isActive
        });
        return selectedProject;
    } else {
        console.log('❌ No project selected in shared service');
        return null;
    }
}

// Test 2: Validate project exists via API
async function testProjectExistsAPI(projectId) {
    console.log('\n2. Testing Project Exists via API...');
    console.log('Project ID to test:', projectId);
    
    if (!projectId) {
        console.log('❌ No project ID provided');
        return false;
    }
    
    try {
        // Test project access
        console.log('🔍 Checking project access...');
        const response = await fetch(`/api/v1/admin-service/users/${getCurrentUserId()}/projects/${projectId}/access`, {
            headers: {
                'Authorization': `Bearer ${getAuthToken()}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const accessData = await response.json();
            console.log('✅ Project access check result:', accessData);
        } else {
            console.log('❌ Project access check failed:', response.status, response.statusText);
        }
        
        // Test get project details
        console.log('🔍 Getting project details...');
        const projectResponse = await fetch(`/api/v1/admin-service/projects/${projectId}`, {
            headers: {
                'Authorization': `Bearer ${getAuthToken()}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (projectResponse.ok) {
            const projectData = await projectResponse.json();
            console.log('✅ Project details:', projectData);
            return true;
        } else {
            console.log('❌ Get project failed:', projectResponse.status, projectResponse.statusText);
            if (projectResponse.status === 404) {
                console.log('🚨 PROJECT NOT FOUND - This is likely the cause of the foreign key constraint error');
            }
            return false;
        }
        
    } catch (error) {
        console.error('❌ Error testing project API:', error);
        return false;
    }
}

// Test 3: List all available projects
async function testListAllProjects() {
    console.log('\n3. Testing List All Projects...');
    
    try {
        const response = await fetch('/api/v1/admin-service/projects', {
            headers: {
                'Authorization': `Bearer ${getAuthToken()}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const projects = await response.json();
            console.log('✅ Available projects:', projects.length);
            
            projects.forEach((project, index) => {
                console.log(`  ${index + 1}. ${project.name} (ID: ${project.id})`);
            });
            
            return projects;
        } else {
            console.log('❌ List projects failed:', response.status, response.statusText);
            return [];
        }
    } catch (error) {
        console.error('❌ Error listing projects:', error);
        return [];
    }
}

// Test 4: Check database schema (if possible)
async function testDatabaseSchema() {
    console.log('\n4. Testing Database Schema...');
    
    // This would require a backend endpoint to check the schema
    // For now, we'll just log what we expect
    console.log('Expected database schema:');
    console.log('  projects table: id (UUID), name, description, is_active, created_at, updated_at');
    console.log('  environment_configs table: id, project_id (FK to projects.id), ...');
    console.log('  Foreign key constraint: fk_environment_configs_project');
    
    console.log('\n💡 Possible issues:');
    console.log('  1. Project ID does not exist in projects table');
    console.log('  2. Project ID format mismatch (UUID vs string)');
    console.log('  3. Project is soft-deleted (deleted_at is not null)');
    console.log('  4. Foreign key constraint is incorrectly configured');
}

// Test 5: Test environment service health
async function testEnvironmentServiceHealth() {
    console.log('\n5. Testing Environment Service Health...');
    
    try {
        const response = await fetch('/api/v1/environment-service/health', {
            headers: {
                'Authorization': `Bearer ${getAuthToken()}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const health = await response.json();
            console.log('✅ Environment service health:', health);
        } else {
            console.log('❌ Environment service health check failed:', response.status);
        }
    } catch (error) {
        console.error('❌ Error checking environment service health:', error);
    }
}

// Helper functions
function getCurrentUserId() {
    // Try to get user ID from auth service or local storage
    const token = getAuthToken();
    if (token) {
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            return payload.sub || payload.user_id || payload.id;
        } catch (e) {
            console.warn('Could not parse JWT token');
        }
    }
    return 'unknown';
}

function getAuthToken() {
    // Try different possible token storage locations
    return localStorage.getItem('token') || 
           localStorage.getItem('authToken') || 
           localStorage.getItem('jwt') ||
           sessionStorage.getItem('token') ||
           sessionStorage.getItem('authToken');
}

// Main test runner
async function runForeignKeyDebugTests() {
    console.log('Starting foreign key constraint debug tests...\n');
    
    // Test 1: Current project selection
    const selectedProject = testCurrentProjectSelection();
    const projectIdToTest = selectedProject?.id || 'f0768aa2-aec0-4864-87bc-62e8f39921de';
    
    // Test 2: Project exists via API
    const projectExists = await testProjectExistsAPI(projectIdToTest);
    
    // Test 3: List all projects
    const allProjects = await testListAllProjects();
    
    // Test 4: Database schema info
    testDatabaseSchema();
    
    // Test 5: Environment service health
    await testEnvironmentServiceHealth();
    
    // Summary
    console.log('\n📊 Debug Summary:');
    console.log('================');
    console.log('Project ID being used:', projectIdToTest);
    console.log('Project exists via API:', projectExists ? '✅' : '❌');
    console.log('Total available projects:', allProjects.length);
    console.log('Selected project in UI:', selectedProject ? '✅' : '❌');
    
    if (!projectExists) {
        console.log('\n🚨 ISSUE IDENTIFIED:');
        console.log('The project ID being used does not exist in the database.');
        console.log('This is causing the foreign key constraint violation.');
        
        if (allProjects.length > 0) {
            console.log('\n💡 SOLUTION:');
            console.log('Select one of the available projects:');
            allProjects.slice(0, 3).forEach((project, index) => {
                console.log(`  ${index + 1}. ${project.name} (ID: ${project.id})`);
            });
        } else {
            console.log('\n💡 SOLUTION:');
            console.log('No projects available. You may need to:');
            console.log('  1. Create a project first');
            console.log('  2. Check your permissions');
            console.log('  3. Contact an administrator');
        }
    } else {
        console.log('\n🤔 UNEXPECTED:');
        console.log('Project exists via API but foreign key constraint is failing.');
        console.log('This might indicate:');
        console.log('  1. Database inconsistency');
        console.log('  2. Different database connections');
        console.log('  3. Transaction isolation issues');
        console.log('  4. Soft-deleted project');
    }
}

// Auto-run tests
if (typeof window !== 'undefined') {
    setTimeout(runForeignKeyDebugTests, 1000);
}

// Export for manual testing
window.foreignKeyDebug = {
    runAll: runForeignKeyDebugTests,
    testCurrentProjectSelection,
    testProjectExistsAPI,
    testListAllProjects,
    testDatabaseSchema,
    testEnvironmentServiceHealth
};

console.log('\n💡 You can also run individual tests:');
console.log('window.foreignKeyDebug.testProjectExistsAPI("f0768aa2-aec0-4864-87bc-62e8f39921de")');
console.log('window.foreignKeyDebug.testListAllProjects()');
