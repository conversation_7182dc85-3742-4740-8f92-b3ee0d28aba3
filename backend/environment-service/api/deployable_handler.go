package api

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/claudio/deploy-orchestrator/environment-service/internal/services"
	"github.com/gin-gonic/gin"
)

// DeployableHandler is an API handler for the /api/v1/deployables endpoint
// that maps deployable requests to environments
type DeployableHandler struct {
	environmentService *services.EnvironmentService
}

// NewDeployableHandler creates a new deployable handler
func NewDeployableHandler(environmentService *services.EnvironmentService) *DeployableHandler {
	return &DeployableHandler{
		environmentService: environmentService,
	}
}

// RegisterRoutes registers the deployable routes with authentication
func (h *DeployableHandler) RegisterRoutes(router *gin.Engine, authMiddleware gin.HandlerFunc) {
	api := router.Group("/api/v1/deployables")
	api.Use(authMiddleware) // Apply authentication to all deployable routes
	{
		api.GET("", h.ListDeployables)
		api.GET("/:id", h.GetDeployable)
		api.POST("", h.CreateDeployable)
		api.PUT("/:id", h.UpdateDeployable)
		api.DELETE("/:id", h.DeleteDeployable)
	}
}

// ListDeployables lists all deployables (environments)
func (h *DeployableHandler) ListDeployables(c *gin.Context) {
	var filter services.EnvironmentFilter

	// Log all incoming query parameters
	fmt.Printf("Deployable API - Received request: %s %s\n", c.Request.Method, c.Request.URL.Path)
	fmt.Printf("Deployable API - Query params: %v\n", c.Request.URL.Query())

	// Bind query parameters
	if err := c.ShouldBindQuery(&filter); err != nil {
		fmt.Printf("Deployable API - Error binding query: %v\n", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Ensure ProjectID is set from query parameter (frontend may send it as projectId)
	if filter.ProjectID == "" {
		filter.ProjectID = c.Query("projectId")
		fmt.Printf("Deployable API - Using projectId from query: %s\n", filter.ProjectID)
	}

	// Set default pagination values if not provided
	if filter.Limit <= 0 {
		filter.Limit = 10 // Default page size
	}
	if filter.Offset < 0 {
		filter.Offset = 0
	}

	// Get environments with filter
	environments, total, err := h.environmentService.ListEnvironments(c.Request.Context(), &filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Log successful response
	fmt.Printf("ListDeployables: Found %d environments for projectId: %s\n", total, filter.ProjectID)

	// Calculate current page number safely
	currentPage := 0
	if filter.Limit > 0 {
		currentPage = filter.Offset / filter.Limit
	}

	// Format response to match DeployableListResponse format expected by frontend
	c.JSON(http.StatusOK, gin.H{
		"deployables": environments,
		"total":       total,
		"page":        currentPage,
		"pageSize":    filter.Limit,
	})
}

// GetDeployable gets a deployable by ID
func (h *DeployableHandler) GetDeployable(c *gin.Context) {
	id := c.Param("id")
	projectId := c.Query("projectId") // Get projectId from query params if present

	// Create context with auth token for potential project validation
	ctx := h.addAuthTokenToContext(c)

	// Get the environment with optional project ID filtering
	environment, err := h.environmentService.GetEnvironment(ctx, id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// If projectId is specified, verify it matches the environment's project
	if projectId != "" && environment.ProjectID != projectId {
		c.JSON(http.StatusNotFound, gin.H{"error": "Deployable not found for specified project"})
		return
	}

	c.JSON(http.StatusOK, environment)
}

// CreateDeployable creates a new deployable (environment)
func (h *DeployableHandler) CreateDeployable(c *gin.Context) {
	var req services.CreateEnvironmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create context with auth token for project validation
	ctx := h.addAuthTokenToContext(c)

	environment, err := h.environmentService.CreateEnvironment(ctx, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, environment)
}

// UpdateDeployable updates a deployable (environment)
func (h *DeployableHandler) UpdateDeployable(c *gin.Context) {
	id := c.Param("id")
	var req services.UpdateEnvironmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create context with auth token for project validation
	ctx := h.addAuthTokenToContext(c)

	environment, err := h.environmentService.UpdateEnvironment(ctx, id, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, environment)
}

// DeleteDeployable deletes a deployable (environment)
func (h *DeployableHandler) DeleteDeployable(c *gin.Context) {
	id := c.Param("id")

	// Create context with auth token for project validation
	ctx := h.addAuthTokenToContext(c)

	if err := h.environmentService.DeleteEnvironment(ctx, id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Deployable deleted successfully"})
}

// addAuthTokenToContext extracts the authorization token from Gin context and adds it to Go context
func (h *DeployableHandler) addAuthTokenToContext(c *gin.Context) context.Context {
	// Get the authorization header from the request
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		// Return the original context if no auth header
		return c.Request.Context()
	}

	// Extract the token (remove "Bearer " prefix if present)
	token := authHeader
	if len(authHeader) > 7 && strings.ToLower(authHeader[:7]) == "bearer " {
		token = authHeader[7:]
	}

	// Add the token to the context
	ctx := context.WithValue(c.Request.Context(), "auth_token", token)
	ctx = context.WithValue(ctx, "raw_auth_header", authHeader)

	return ctx
}
