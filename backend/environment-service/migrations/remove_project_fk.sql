-- Migration to remove foreign key constraint on project_id
-- This is needed because projects are managed by admin-service in a different database

-- Drop the foreign key constraint if it exists
DO $$
BEGIN
    -- Check if the constraint exists and drop it
    IF EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_environment_configs_project' 
        AND table_name = 'environment_configs'
    ) THEN
        ALTER TABLE environment_configs DROP CONSTRAINT fk_environment_configs_project;
        RAISE NOTICE 'Dropped foreign key constraint fk_environment_configs_project';
    ELSE
        RAISE NOTICE 'Foreign key constraint fk_environment_configs_project does not exist';
    END IF;
END $$;

-- Also check for any other project-related foreign key constraints
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    -- Find all foreign key constraints that reference a projects table
    FOR constraint_record IN
        SELECT 
            tc.constraint_name,
            tc.table_name,
            ccu.table_name AS foreign_table_name
        FROM 
            information_schema.table_constraints AS tc 
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY' 
          AND ccu.table_name = 'projects'
          AND tc.table_schema = current_schema()
    LOOP
        EXECUTE format('ALTER TABLE %I DROP CONSTRAINT %I', 
                      constraint_record.table_name, 
                      constraint_record.constraint_name);
        RAISE NOTICE 'Dropped foreign key constraint % from table %', 
                     constraint_record.constraint_name, 
                     constraint_record.table_name;
    END LOOP;
END $$;

-- Add a comment to the project_id column to document the change
COMMENT ON COLUMN environment_configs.project_id IS 'Project ID reference - validated via API calls to admin-service (no FK constraint due to microservice separation)';

-- Create an index on project_id for performance (if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_indexes 
        WHERE tablename = 'environment_configs' 
        AND indexname = 'idx_environment_configs_project_id'
    ) THEN
        CREATE INDEX idx_environment_configs_project_id ON environment_configs(project_id);
        RAISE NOTICE 'Created index idx_environment_configs_project_id';
    ELSE
        RAISE NOTICE 'Index idx_environment_configs_project_id already exists';
    END IF;
END $$;
