package tests

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/claudio/deploy-orchestrator/environment-service/internal/handlers"
	"github.com/claudio/deploy-orchestrator/environment-service/internal/providers"
	"github.com/claudio/deploy-orchestrator/environment-service/internal/services"
)

func TestProviderSchemaEndpoint(t *testing.T) {
	// Initialize provider registry with test providers
	registry := providers.NewProviderRegistry()

	// Register test providers
	err := registry.RegisterProvider("azure-aks", providers.NewAzureAKSProvider())
	require.NoError(t, err)

	err = registry.RegisterProvider("kubernetes", providers.NewKubernetesProvider())
	require.NoError(t, err)

	// Create mock services
	providerService := services.NewProviderService()
	environmentService := services.NewEnvironmentService(nil)

	// Create handler
	handler := handlers.NewEnvironmentHandler(environmentService, nil, providerService, nil)

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/providers/:type/schema", handler.GetProviderSchema)

	tests := []struct {
		name           string
		providerType   string
		expectedStatus int
		expectSchema   bool
	}{
		{
			name:           "Valid provider - azure-aks",
			providerType:   "azure-aks",
			expectedStatus: http.StatusOK,
			expectSchema:   true,
		},
		{
			name:           "Valid provider - kubernetes",
			providerType:   "kubernetes",
			expectedStatus: http.StatusOK,
			expectSchema:   true,
		},
		{
			name:           "URL encoded provider name",
			providerType:   url.QueryEscape("Azure AKS"),
			expectedStatus: http.StatusOK,
			expectSchema:   true,
		},
		{
			name:           "URL encoded complex name",
			providerType:   url.QueryEscape("Azure Kubernetes Service (AKS) environment provider"),
			expectedStatus: http.StatusOK,
			expectSchema:   true,
		},
		{
			name:           "Display name - Azure AKS",
			providerType:   "Azure AKS",
			expectedStatus: http.StatusOK,
			expectSchema:   true,
		},
		{
			name:           "Fuzzy match - azureaks",
			providerType:   "azureaks",
			expectedStatus: http.StatusOK,
			expectSchema:   true,
		},
		{
			name:           "Invalid provider",
			providerType:   "nonexistent-provider",
			expectedStatus: http.StatusNotFound,
			expectSchema:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create request
			req, err := http.NewRequest("GET", "/providers/"+tt.providerType+"/schema", nil)
			require.NoError(t, err)

			// Create response recorder
			w := httptest.NewRecorder()

			// Perform request
			router.ServeHTTP(w, req)

			// Check status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			// Parse response
			var response map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			if tt.expectSchema {
				// Should have schema
				assert.Contains(t, response, "schema")
				assert.Contains(t, response, "type")

				// Schema should be a valid object
				schema, ok := response["schema"].(map[string]interface{})
				assert.True(t, ok, "Schema should be an object")
				assert.NotEmpty(t, schema)
			} else {
				// Should have error
				assert.Contains(t, response, "error")
				assert.Equal(t, "Provider not found", response["error"])
			}
		})
	}
}

func TestProviderCapabilitiesEndpoint(t *testing.T) {
	// Initialize provider registry with test providers
	registry := providers.NewProviderRegistry()

	// Register test providers
	err := registry.RegisterProvider("azure-aks", providers.NewAzureAKSProvider())
	require.NoError(t, err)

	// Create mock services
	providerService := services.NewProviderService()
	environmentService := services.NewEnvironmentService(nil)

	// Create handler
	handler := handlers.NewEnvironmentHandler(environmentService, nil, providerService, nil)

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/providers/:type/capabilities", handler.GetProviderCapabilities)

	tests := []struct {
		name           string
		providerType   string
		expectedStatus int
	}{
		{
			name:           "Valid provider capabilities",
			providerType:   "azure-aks",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "URL encoded provider name",
			providerType:   url.QueryEscape("Azure AKS"),
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Invalid provider capabilities",
			providerType:   "nonexistent-provider",
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create request
			req, err := http.NewRequest("GET", "/providers/"+tt.providerType+"/capabilities", nil)
			require.NoError(t, err)

			// Create response recorder
			w := httptest.NewRecorder()

			// Perform request
			router.ServeHTTP(w, req)

			// Check status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			// Parse response
			var response map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			if tt.expectedStatus == http.StatusOK {
				// Should have capabilities data
				assert.NotEmpty(t, response)
			} else {
				// Should have error
				assert.Contains(t, response, "error")
			}
		})
	}
}

func TestProviderRegistryFuzzyMatching(t *testing.T) {
	registry := providers.NewProviderRegistry()

	// Register test provider
	err := registry.RegisterProvider("azure-aks", providers.NewAzureAKSProvider())
	require.NoError(t, err)

	tests := []struct {
		name         string
		searchName   string
		shouldFind   bool
		expectedType string
	}{
		{
			name:         "Exact technical name",
			searchName:   "azure-aks",
			shouldFind:   true,
			expectedType: "azure-aks",
		},
		{
			name:         "Display name",
			searchName:   "Azure AKS",
			shouldFind:   true,
			expectedType: "azure-aks",
		},
		{
			name:         "Normalized fuzzy match",
			searchName:   "azureaks",
			shouldFind:   true,
			expectedType: "azure-aks",
		},
		{
			name:         "With parentheses",
			searchName:   "Azure Kubernetes Service (AKS)",
			shouldFind:   true,
			expectedType: "azure-aks",
		},
		{
			name:         "URL encoded",
			searchName:   url.QueryEscape("Azure AKS"),
			shouldFind:   true,
			expectedType: "azure-aks",
		},
		{
			name:         "Non-existent provider",
			searchName:   "nonexistent",
			shouldFind:   false,
			expectedType: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			provider, err := registry.GetProvider(tt.searchName)

			if tt.shouldFind {
				assert.NoError(t, err)
				assert.NotNil(t, provider)

				// Verify it's the correct provider by checking metadata
				metadata := provider.GetMetadata()
				assert.Contains(t, []string{"Azure AKS", "azure-aks"}, metadata.Name)
			} else {
				assert.Error(t, err)
				assert.Nil(t, provider)
			}
		})
	}
}
