package handlers

import (
	"context"
	"errors"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/claudio/deploy-orchestrator/environment-service/config"
	"github.com/claudio/deploy-orchestrator/environment-service/internal/services"
	"github.com/gin-gonic/gin"
)

type EnvironmentHandler struct {
	environmentService *services.EnvironmentService
	healthService      *services.HealthService
	providerService    *services.ProviderService
	config             *config.Config
}

func NewEnvironmentHandler(envService *services.EnvironmentService, healthService *services.HealthService, providerService *services.ProviderService, cfg *config.Config) *EnvironmentHandler {
	return &EnvironmentHandler{
		environmentService: envService,
		healthService:      healthService,
		providerService:    providerService,
		config:             cfg,
	}
}

// ListEnvironments handles GET /environments
func (h *EnvironmentHandler) ListEnvironments(c *gin.Context) {
	// Parse query parameters
	filter := &services.EnvironmentFilter{
		ProjectID: c.Query("projectId"),
		Type:      c.Query("type"),
		Status:    c.Query("status"),
		Provider:  c.Query("provider"),
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			filter.Limit = l
		}
	}

	if offset := c.Query("offset"); offset != "" {
		if o, err := strconv.Atoi(offset); err == nil {
			filter.Offset = o
		}
	}

	environments, total, err := h.environmentService.ListEnvironments(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"environments": environments,
		"total":        total,
		"limit":        filter.Limit,
		"offset":       filter.Offset,
	})
}

// CreateEnvironment handles POST /environments
func (h *EnvironmentHandler) CreateEnvironment(c *gin.Context) {
	var req services.CreateEnvironmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create context with auth token for project validation
	ctx := h.addAuthTokenToContext(c)

	environment, err := h.environmentService.CreateEnvironment(ctx, &req)
	if err != nil {
		h.handleServiceError(c, err)
		return
	}

	c.JSON(http.StatusCreated, environment)
}

// GetEnvironment handles GET /environments/:id
func (h *EnvironmentHandler) GetEnvironment(c *gin.Context) {
	id := c.Param("id")

	environment, err := h.environmentService.GetEnvironment(c.Request.Context(), id)
	if err != nil {
		if err.Error() == "environment not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, environment)
}

// UpdateEnvironment handles PUT /environments/:id
func (h *EnvironmentHandler) UpdateEnvironment(c *gin.Context) {
	id := c.Param("id")

	var req services.UpdateEnvironmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	environment, err := h.environmentService.UpdateEnvironment(c.Request.Context(), id, &req)
	if err != nil {
		if err.Error() == "environment not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, environment)
}

// DeleteEnvironment handles DELETE /environments/:id
func (h *EnvironmentHandler) DeleteEnvironment(c *gin.Context) {
	id := c.Param("id")

	err := h.environmentService.DeleteEnvironment(c.Request.Context(), id)
	if err != nil {
		if err.Error() == "environment not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// TestConnection handles POST /environments/:id/test-connection
func (h *EnvironmentHandler) TestConnection(c *gin.Context) {
	id := c.Param("id")

	result, err := h.environmentService.TestConnection(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetEnvironmentStatus handles GET /environments/:id/status
func (h *EnvironmentHandler) GetEnvironmentStatus(c *gin.Context) {
	id := c.Param("id")

	// This would integrate with the health service
	health, err := h.healthService.GetEnvironmentHealth(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, health)
}

// ListProjectEnvironments handles GET /projects/:projectId/environments
func (h *EnvironmentHandler) ListProjectEnvironments(c *gin.Context) {
	projectID := c.Param("projectId")

	filter := &services.EnvironmentFilter{
		ProjectID: projectID,
		Type:      c.Query("type"),
		Status:    c.Query("status"),
		Provider:  c.Query("provider"),
	}

	environments, total, err := h.environmentService.ListEnvironments(c.Request.Context(), filter)
	if err != nil {
		h.handleServiceError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"environments": environments,
		"total":        total,
		"projectId":    projectID,
	})
}

// CreateProjectEnvironment handles POST /projects/:projectId/environments
func (h *EnvironmentHandler) CreateProjectEnvironment(c *gin.Context) {
	projectID := c.Param("projectId")

	var req services.CreateEnvironmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set the project ID from the URL
	req.ProjectID = projectID

	// Create context with auth token for project validation
	ctx := h.addAuthTokenToContext(c)

	environment, err := h.environmentService.CreateEnvironment(ctx, &req)
	if err != nil {
		h.handleServiceError(c, err)
		return
	}

	c.JSON(http.StatusCreated, environment)
}

// GetProviderTypes returns available provider types
func (h *EnvironmentHandler) GetProviderTypes(c *gin.Context) {
	// Get providers from the provider service which uses the registry
	providers := h.providerService.ListProviders()

	// Convert to the expected format
	types := make([]map[string]interface{}, len(providers))
	for i, provider := range providers {
		types[i] = map[string]interface{}{
			"type":         provider.Type,
			"name":         provider.Name,
			"description":  provider.Description,
			"category":     provider.Category,
			"capabilities": provider.Capabilities,
			"enabled":      true,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"types": types,
		"total": len(types),
	})
}

// ListProviders handles GET /providers
func (h *EnvironmentHandler) ListProviders(c *gin.Context) {
	providers := h.providerService.ListProviders()

	c.JSON(http.StatusOK, gin.H{
		"providers": providers,
		"total":     len(providers),
	})
}

// GetProviderCapabilities handles GET /providers/:type/capabilities
func (h *EnvironmentHandler) GetProviderCapabilities(c *gin.Context) {
	providerType := c.Param("type")

	// URL decode the provider type to handle special characters
	decodedType, err := url.QueryUnescape(providerType)
	if err != nil {
		decodedType = providerType // fallback to original
	}

	capabilities, err := h.environmentService.GetProviderCapabilities(c.Request.Context(), decodedType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":     "Provider not found",
			"type":      decodedType,
			"requested": providerType,
		})
		return
	}

	c.JSON(http.StatusOK, capabilities)
}

// GetProviderSchema handles GET /providers/:type/schema
func (h *EnvironmentHandler) GetProviderSchema(c *gin.Context) {
	providerType := c.Param("type")

	// URL decode the provider type to handle special characters
	decodedType, err := url.QueryUnescape(providerType)
	if err != nil {
		decodedType = providerType // fallback to original
	}

	schema, err := h.environmentService.GetProviderSchema(c.Request.Context(), decodedType)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":     "Provider not found",
			"type":      decodedType,
			"requested": providerType,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"type":   decodedType,
		"schema": schema,
	})
}

// ValidateProviderConfig handles POST /providers/:type/validate-config
func (h *EnvironmentHandler) ValidateProviderConfig(c *gin.Context) {
	providerType := c.Param("type")

	// URL decode the provider type to handle special characters
	decodedType, err := url.QueryUnescape(providerType)
	if err != nil {
		decodedType = providerType // fallback to original
	}

	var config map[string]interface{}
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	result, err := h.environmentService.ValidateProviderConfig(c.Request.Context(), decodedType, config)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetVersionMatrix handles GET /versions/matrix/:projectId
func (h *EnvironmentHandler) GetVersionMatrix(c *gin.Context) {
	projectID := c.Param("projectId")

	// This would be implemented to show version matrix across environments
	// For now, return a placeholder
	c.JSON(http.StatusOK, gin.H{
		"projectId":    projectID,
		"environments": map[string]interface{}{},
		"generatedAt":  "2024-01-01T00:00:00Z",
	})
}

// GetDeploymentHistory handles GET /versions/history/:environmentId
func (h *EnvironmentHandler) GetDeploymentHistory(c *gin.Context) {
	environmentID := c.Param("environmentId")

	// This would be implemented to show deployment history
	// For now, return a placeholder
	c.JSON(http.StatusOK, gin.H{
		"environmentId": environmentID,
		"deployments":   []interface{}{},
		"total":         0,
	})
}

// DeployToEnvironment handles POST /environments/:id/deploy
func (h *EnvironmentHandler) DeployToEnvironment(c *gin.Context) {
	id := c.Param("id")

	var req struct {
		WorkflowID string                 `json:"workflowId" binding:"required"`
		Version    map[string]interface{} `json:"version"`
		Parameters map[string]interface{} `json:"parameters"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// This would integrate with the workflow service to start a deployment
	// For now, return a placeholder
	c.JSON(http.StatusAccepted, gin.H{
		"message":       "Deployment started",
		"environmentId": id,
		"workflowId":    req.WorkflowID,
		"executionId":   "exec-" + id + "-" + req.WorkflowID,
	})
}

// GetEnvironmentLogs handles GET /environments/:id/logs
func (h *EnvironmentHandler) GetEnvironmentLogs(c *gin.Context) {
	id := c.Param("id")

	// This would integrate with the logging service
	// For now, return a placeholder
	c.JSON(http.StatusOK, gin.H{
		"environmentId": id,
		"logs":          []interface{}{},
		"total":         0,
	})
}

// CloneEnvironment handles POST /environments/:id/clone
func (h *EnvironmentHandler) CloneEnvironment(c *gin.Context) {
	id := c.Param("id")

	var req services.CloneEnvironmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.SourceEnvironmentID = id

	environment, err := h.environmentService.CloneEnvironment(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, environment)
}

// MigrateEnvironment handles POST /environments/:id/migrate
func (h *EnvironmentHandler) MigrateEnvironment(c *gin.Context) {
	id := c.Param("id")

	var req services.MigrateEnvironmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.EnvironmentID = id

	result, err := h.environmentService.MigrateEnvironment(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusAccepted, result)
}

// ReloadProviders reloads external providers from configuration
func (h *EnvironmentHandler) ReloadProviders(c *gin.Context) {
	// Reload configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to reload configuration",
		})
		return
	}

	// Reload external providers
	if err := h.environmentService.ReloadExternalProviders(cfg.ExternalProviders); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to reload external providers",
		})
		return
	}

	// Get updated provider list
	providers := h.providerService.ListProviders()

	c.JSON(http.StatusOK, gin.H{
		"message":            "External providers reloaded successfully",
		"total_providers":    len(providers),
		"external_providers": len(cfg.ExternalProviders),
	})
}

// addAuthTokenToContext extracts the authorization token from Gin context and adds it to Go context
func (h *EnvironmentHandler) addAuthTokenToContext(c *gin.Context) context.Context {
	// Get the authorization header from the request
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		// Return the original context if no auth header
		return c.Request.Context()
	}

	// Extract the token (remove "Bearer " prefix if present)
	token := authHeader
	if len(authHeader) > 7 && strings.ToLower(authHeader[:7]) == "bearer " {
		token = authHeader[7:]
	}

	// Add the token to the context
	ctx := context.WithValue(c.Request.Context(), "auth_token", token)
	ctx = context.WithValue(ctx, "raw_auth_header", authHeader)

	return ctx
}

// handleServiceError checks if an error is a project validation error and returns appropriate HTTP status
func (h *EnvironmentHandler) handleServiceError(c *gin.Context, err error) {
	// Check if it's a project validation error
	var projValidationErr *services.ProjectValidationError
	if errors.As(err, &projValidationErr) {
		c.JSON(projValidationErr.StatusCode, gin.H{
			"error":   projValidationErr.Message,
			"details": projValidationErr.Error(),
		})
		return
	}

	// Handle other known error types
	if err.Error() == "environment not found" {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// Default to internal server error
	c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
}
