package handlers

import (
	"net/http"
	"strconv"

	"github.com/claudio/deploy-orchestrator/environment-service/internal/services"
	"github.com/gin-gonic/gin"
)

type TemplateHandler struct {
	templateService    *services.TemplateService
	environmentService *services.EnvironmentService
}

func NewTemplateHandler(templateService *services.TemplateService, environmentService *services.EnvironmentService) *TemplateHandler {
	return &TemplateHandler{
		templateService:    templateService,
		environmentService: environmentService,
	}
}

// ListTemplates handles GET /templates
func (h *TemplateHandler) ListTemplates(c *gin.Context) {
	req := &services.ListTemplatesRequest{
		Category:  c.Query("category"),
		Provider:  c.<PERSON>("provider"),
		CreatedBy: c.Query("created_by"),
		Search:    c.Query("search"),
		SortBy:    c.Query("sort_by"),
	}

	// Parse is_public parameter
	if isPublic := c.Query("is_public"); isPublic != "" {
		if val, err := strconv.ParseBool(isPublic); err == nil {
			req.IsPublic = &val
		}
	}

	// Parse pagination parameters
	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			req.Limit = l
		}
	}

	if offset := c.Query("offset"); offset != "" {
		if o, err := strconv.Atoi(offset); err == nil {
			req.Offset = o
		}
	}

	templates, err := h.templateService.ListTemplates(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"templates": templates,
		"total":     len(templates),
		"limit":     req.Limit,
		"offset":    req.Offset,
	})
}

// CreateTemplate handles POST /templates
func (h *TemplateHandler) CreateTemplate(c *gin.Context) {
	var req services.CreateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	template, err := h.templateService.CreateTemplate(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, template)
}

// GetTemplate handles GET /templates/:id
func (h *TemplateHandler) GetTemplate(c *gin.Context) {
	id := c.Param("id")

	template, err := h.templateService.GetTemplate(c.Request.Context(), id)
	if err != nil {
		if err.Error() == "template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, template)
}

// UpdateTemplate handles PUT /templates/:id
func (h *TemplateHandler) UpdateTemplate(c *gin.Context) {
	id := c.Param("id")

	var req services.UpdateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	template, err := h.templateService.UpdateTemplate(c.Request.Context(), id, &req)
	if err != nil {
		if err.Error() == "template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, template)
}

// DeleteTemplate handles DELETE /templates/:id
func (h *TemplateHandler) DeleteTemplate(c *gin.Context) {
	id := c.Param("id")

	err := h.templateService.DeleteTemplate(c.Request.Context(), id)
	if err != nil {
		if err.Error() == "template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// CreateEnvironmentFromTemplate handles POST /templates/:id/create-environment
func (h *TemplateHandler) CreateEnvironmentFromTemplate(c *gin.Context) {
	templateID := c.Param("id")

	var req services.CreateFromTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.TemplateID = templateID

	// Create environment request from template
	envReq, err := h.templateService.CreateEnvironmentFromTemplate(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Create the actual environment
	environment, err := h.environmentService.CreateEnvironment(c.Request.Context(), envReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"environment": environment,
		"template_id": templateID,
		"message":     "Environment created successfully from template",
	})
}

// GetTemplatePresets handles GET /templates/:id/presets
func (h *TemplateHandler) GetTemplatePresets(c *gin.Context) {
	id := c.Param("id")

	template, err := h.templateService.GetTemplate(c.Request.Context(), id)
	if err != nil {
		if err.Error() == "template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"template_id": id,
		"presets":     template.Presets,
		"total":       len(template.Presets),
	})
}

// GetTemplateVariables handles GET /templates/:id/variables
func (h *TemplateHandler) GetTemplateVariables(c *gin.Context) {
	id := c.Param("id")

	template, err := h.templateService.GetTemplate(c.Request.Context(), id)
	if err != nil {
		if err.Error() == "template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"template_id": id,
		"variables":   template.Variables,
		"total":       len(template.Variables),
	})
}

// ValidateTemplateConfig handles POST /templates/:id/validate
func (h *TemplateHandler) ValidateTemplateConfig(c *gin.Context) {
	id := c.Param("id")

	var req struct {
		PresetName string                 `json:"preset_name"`
		Variables  map[string]interface{} `json:"variables"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	template, err := h.templateService.GetTemplate(c.Request.Context(), id)
	if err != nil {
		if err.Error() == "template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Validate required variables
	var missingVariables []string
	var invalidVariables []string

	for _, variable := range template.Variables {
		if variable.Required {
			if _, exists := req.Variables[variable.Name]; !exists {
				missingVariables = append(missingVariables, variable.Name)
			}
		}

		// Additional validation logic can be added here
		// For example, type checking, pattern matching, etc.
	}

	isValid := len(missingVariables) == 0 && len(invalidVariables) == 0

	c.JSON(http.StatusOK, gin.H{
		"valid":             isValid,
		"missing_variables": missingVariables,
		"invalid_variables": invalidVariables,
		"template_id":       id,
	})
}

// GetTemplateUsageStats handles GET /templates/:id/stats
func (h *TemplateHandler) GetTemplateUsageStats(c *gin.Context) {
	id := c.Param("id")

	template, err := h.templateService.GetTemplate(c.Request.Context(), id)
	if err != nil {
		if err.Error() == "template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"template_id": id,
		"usage_count": template.UsageCount,
		"rating":      template.Rating,
		"created_at":  template.CreatedAt,
		"updated_at":  template.UpdatedAt,
	})
}
