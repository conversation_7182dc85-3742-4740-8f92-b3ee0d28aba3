package handlers

import (
	"net/http"

	"github.com/claudio/deploy-orchestrator/environment-service/internal/services"
	"github.com/gin-gonic/gin"
)

type HealthHandler struct {
	healthService *services.HealthService
}

func NewHealthHandler(healthService *services.HealthService) *HealthHandler {
	return &HealthHandler{
		healthService: healthService,
	}
}

// GetAllEnvironmentHealth handles GET /health/environments
func (h *HealthHandler) GetAllEnvironmentHealth(c *gin.Context) {
	// Parse query parameters for filtering
	projectID := c.Query("projectId")
	status := c.Query("status")

	healthData, err := h.healthService.GetAllEnvironmentHealth(c.Request.Context(), projectID, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"environments": healthData,
		"total":        len(healthData),
		"timestamp":    "2024-01-01T00:00:00Z", // This would be current time
	})
}

// GetEnvironmentHealth handles GET /health/environments/:id
func (h *HealthHandler) GetEnvironmentHealth(c *gin.Context) {
	id := c.Param("id")

	health, err := h.healthService.GetEnvironmentHealth(c.Request.Context(), id)
	if err != nil {
		if err.Error() == "environment not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, health)
}

// TriggerHealthCheck handles POST /health/environments/:id/check
func (h *HealthHandler) TriggerHealthCheck(c *gin.Context) {
	id := c.Param("id")

	result, err := h.healthService.TriggerHealthCheck(c.Request.Context(), id)
	if err != nil {
		if err.Error() == "environment not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}
