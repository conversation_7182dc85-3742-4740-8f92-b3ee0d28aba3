package services

import (
	"context"
	"fmt"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// EnvironmentTemplate represents a reusable environment configuration template
type EnvironmentTemplate struct {
	ID          string   `json:"id" gorm:"primaryKey"`
	Name        string   `json:"name" gorm:"not null"`
	Description string   `json:"description"`
	Category    string   `json:"category"`
	Provider    string   `json:"provider"`
	Version     string   `json:"version"`
	Tags        []string `json:"tags" gorm:"type:text[]"`
	IsPublic    bool     `json:"is_public" gorm:"default:false"`
	CreatedBy   string   `json:"created_by"`

	// Template configuration
	Config    map[string]interface{} `json:"config" gorm:"type:jsonb"`
	Variables []TemplateVariable     `json:"variables" gorm:"type:jsonb"`
	Presets   []EnvironmentPreset    `json:"presets" gorm:"type:jsonb"`

	// Metadata
	UsageCount int       `json:"usage_count" gorm:"default:0"`
	Rating     float64   `json:"rating" gorm:"default:0"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// TemplateVariable represents a configurable variable in a template
type TemplateVariable struct {
	Name         string      `json:"name"`
	Type         string      `json:"type"` // string, number, boolean, select
	Description  string      `json:"description"`
	DefaultValue interface{} `json:"default_value,omitempty"`
	Required     bool        `json:"required"`
	Options      []string    `json:"options,omitempty"` // For select type
	Validation   *Validation `json:"validation,omitempty"`
}

// Validation represents validation rules for template variables
type Validation struct {
	Pattern string   `json:"pattern,omitempty"`
	Min     *float64 `json:"min,omitempty"`
	Max     *float64 `json:"max,omitempty"`
	Options []string `json:"options,omitempty"`
}

// EnvironmentPreset represents a preset configuration for a template
type EnvironmentPreset struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Values      map[string]interface{} `json:"values"`
	Tags        []string               `json:"tags"`
}

// TemplateService handles environment template operations
type TemplateService struct {
	db *gorm.DB
}

// NewTemplateService creates a new template service
func NewTemplateService(db *gorm.DB) *TemplateService {
	return &TemplateService{db: db}
}

// CreateTemplate creates a new environment template
func (s *TemplateService) CreateTemplate(ctx context.Context, req *CreateTemplateRequest) (*EnvironmentTemplate, error) {
	template := &EnvironmentTemplate{
		ID:          uuid.New().String(),
		Name:        req.Name,
		Description: req.Description,
		Category:    req.Category,
		Provider:    req.Provider,
		Version:     req.Version,
		Tags:        req.Tags,
		IsPublic:    req.IsPublic,
		CreatedBy:   req.CreatedBy,
		Config:      req.Config,
		Variables:   req.Variables,
		Presets:     req.Presets,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.db.Create(template).Error; err != nil {
		return nil, fmt.Errorf("failed to create template: %w", err)
	}

	return template, nil
}

// GetTemplate retrieves a template by ID
func (s *TemplateService) GetTemplate(ctx context.Context, id string) (*EnvironmentTemplate, error) {
	var template EnvironmentTemplate
	if err := s.db.First(&template, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("template not found")
		}
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	return &template, nil
}

// ListTemplates lists templates with optional filtering
func (s *TemplateService) ListTemplates(ctx context.Context, req *ListTemplatesRequest) ([]*EnvironmentTemplate, error) {
	query := s.db.Model(&EnvironmentTemplate{})

	// Apply filters
	if req.Category != "" {
		query = query.Where("category = ?", req.Category)
	}
	if req.Provider != "" {
		query = query.Where("provider = ?", req.Provider)
	}
	if req.CreatedBy != "" {
		query = query.Where("created_by = ?", req.CreatedBy)
	}
	if req.IsPublic != nil {
		query = query.Where("is_public = ?", *req.IsPublic)
	}

	// Apply search
	if req.Search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+req.Search+"%", "%"+req.Search+"%")
	}

	// Apply sorting
	switch req.SortBy {
	case "name":
		query = query.Order("name")
	case "created_at":
		query = query.Order("created_at DESC")
	case "usage_count":
		query = query.Order("usage_count DESC")
	case "rating":
		query = query.Order("rating DESC")
	default:
		query = query.Order("created_at DESC")
	}

	// Apply pagination
	if req.Limit > 0 {
		query = query.Limit(req.Limit)
	}
	if req.Offset > 0 {
		query = query.Offset(req.Offset)
	}

	var templates []*EnvironmentTemplate
	if err := query.Find(&templates).Error; err != nil {
		return nil, fmt.Errorf("failed to list templates: %w", err)
	}

	return templates, nil
}

// UpdateTemplate updates an existing template
func (s *TemplateService) UpdateTemplate(ctx context.Context, id string, req *UpdateTemplateRequest) (*EnvironmentTemplate, error) {
	var template EnvironmentTemplate
	if err := s.db.First(&template, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("template not found")
		}
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	// Update fields
	if req.Name != "" {
		template.Name = req.Name
	}
	if req.Description != "" {
		template.Description = req.Description
	}
	if req.Category != "" {
		template.Category = req.Category
	}
	if req.Version != "" {
		template.Version = req.Version
	}
	if req.Tags != nil {
		template.Tags = req.Tags
	}
	if req.IsPublic != nil {
		template.IsPublic = *req.IsPublic
	}
	if req.Config != nil {
		template.Config = req.Config
	}
	if req.Variables != nil {
		template.Variables = req.Variables
	}
	if req.Presets != nil {
		template.Presets = req.Presets
	}

	template.UpdatedAt = time.Now()

	if err := s.db.Save(&template).Error; err != nil {
		return nil, fmt.Errorf("failed to update template: %w", err)
	}

	return &template, nil
}

// DeleteTemplate deletes a template
func (s *TemplateService) DeleteTemplate(ctx context.Context, id string) error {
	result := s.db.Delete(&EnvironmentTemplate{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete template: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("template not found")
	}

	return nil
}

// CreateEnvironmentFromTemplate creates an environment from a template
func (s *TemplateService) CreateEnvironmentFromTemplate(ctx context.Context, req *CreateFromTemplateRequest) (*CreateEnvironmentRequest, error) {
	// Get the template
	template, err := s.GetTemplate(ctx, req.TemplateID)
	if err != nil {
		return nil, err
	}

	// Start with template config
	config := make(map[string]interface{})
	for k, v := range template.Config {
		config[k] = v
	}

	// Apply preset values if specified
	if req.PresetName != "" {
		for _, preset := range template.Presets {
			if preset.Name == req.PresetName {
				for k, v := range preset.Values {
					config[k] = v
				}
				break
			}
		}
	}

	// Apply user-provided variable values
	for k, v := range req.Variables {
		config[k] = v
	}

	// Validate required variables
	for _, variable := range template.Variables {
		if variable.Required {
			if _, exists := config[variable.Name]; !exists {
				return nil, fmt.Errorf("required variable %s not provided", variable.Name)
			}
		}
	}

	// Convert config map to ProviderConfig struct
	providerConfig := models.ProviderConfig{
		Extra: config,
	}

	// Create environment request
	envReq := &CreateEnvironmentRequest{
		ProjectID:   req.ProjectID,
		Name:        req.Name,
		Description: req.Description,
		Provider: struct {
			Type   models.ProviderType   `json:"type"`
			Config models.ProviderConfig `json:"config"`
		}{
			Type:   models.ProviderType(template.Provider),
			Config: providerConfig,
		},
		Tags: append(template.Tags, req.Tags...),
	}

	// Increment usage count
	s.db.Model(&template).Update("usage_count", gorm.Expr("usage_count + 1"))

	return envReq, nil
}

// Request/Response types
type CreateTemplateRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Description string                 `json:"description"`
	Category    string                 `json:"category" binding:"required"`
	Provider    string                 `json:"provider" binding:"required"`
	Version     string                 `json:"version"`
	Tags        []string               `json:"tags"`
	IsPublic    bool                   `json:"is_public"`
	CreatedBy   string                 `json:"created_by" binding:"required"`
	Config      map[string]interface{} `json:"config" binding:"required"`
	Variables   []TemplateVariable     `json:"variables"`
	Presets     []EnvironmentPreset    `json:"presets"`
}

type UpdateTemplateRequest struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Category    string                 `json:"category"`
	Version     string                 `json:"version"`
	Tags        []string               `json:"tags"`
	IsPublic    *bool                  `json:"is_public"`
	Config      map[string]interface{} `json:"config"`
	Variables   []TemplateVariable     `json:"variables"`
	Presets     []EnvironmentPreset    `json:"presets"`
}

type ListTemplatesRequest struct {
	Category  string `json:"category"`
	Provider  string `json:"provider"`
	CreatedBy string `json:"created_by"`
	IsPublic  *bool  `json:"is_public"`
	Search    string `json:"search"`
	SortBy    string `json:"sort_by"`
	Limit     int    `json:"limit"`
	Offset    int    `json:"offset"`
}

type CreateFromTemplateRequest struct {
	TemplateID  string                 `json:"template_id" binding:"required"`
	ProjectID   string                 `json:"project_id" binding:"required"`
	Name        string                 `json:"name" binding:"required"`
	Description string                 `json:"description"`
	PresetName  string                 `json:"preset_name"`
	Variables   map[string]interface{} `json:"variables"`
	Tags        []string               `json:"tags"`
}
