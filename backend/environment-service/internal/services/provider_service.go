package services

import (
	"github.com/claudio/deploy-orchestrator/environment-service/internal/providers"
	sharedProviders "github.com/claudio/deploy-orchestrator/shared/providers"
)

type ProviderService struct {
	registry *providers.ProviderRegistry
}

func NewProviderService() *ProviderService {
	return &ProviderService{
		registry: providers.GetGlobalRegistry(),
	}
}

// ListProviders returns all available providers from the environment service registry
func (s *ProviderService) ListProviders() []sharedProviders.ProviderInfo {
	// Get providers from the environment service registry
	providerMetadata := s.registry.ListProviders()

	// Convert to shared ProviderInfo format for backward compatibility
	providerInfos := make([]sharedProviders.ProviderInfo, len(providerMetadata))
	for i, meta := range providerMetadata {
		providerInfos[i] = sharedProviders.ProviderInfo{
			Type:         sharedProviders.ProviderType(meta.Name),
			Name:         meta.Description,
			Description:  meta.Description,
			Category:     meta.Category,
			Capabilities: convertCapabilities(meta.Capabilities),
			// Add other fields as needed
		}
	}

	return providerInfos
}

// convertCapabilities converts string capabilities to ProviderCapability enum
func convertCapabilities(capabilities []string) []sharedProviders.ProviderCapability {
	var result []sharedProviders.ProviderCapability
	for _, cap := range capabilities {
		switch cap {
		case "containers":
			result = append(result, sharedProviders.CapabilityContainers)
		case "load-balancing":
			result = append(result, sharedProviders.CapabilityLoadBalancing)
		case "auto-scaling":
			result = append(result, sharedProviders.CapabilityAutoScaling)
		case "persistent-storage":
			result = append(result, sharedProviders.CapabilityPersistentStorage)
		case "monitoring":
			result = append(result, sharedProviders.CapabilityMonitoring)
		case "logging":
			result = append(result, sharedProviders.CapabilityLogging)
		case "secrets":
			result = append(result, sharedProviders.CapabilitySecrets)
		case "ingress":
			result = append(result, sharedProviders.CapabilityIngress)
		case "networking":
			result = append(result, sharedProviders.CapabilityNetworking)
			// Add more mappings as needed
		}
	}
	return result
}

// GetProvider returns a specific provider by type from the environment service registry
func (s *ProviderService) GetProvider(providerType string) (providers.EnvironmentProvider, error) {
	return s.registry.GetProvider(providerType)
}

// GetProvidersByCategory returns providers filtered by category
func (s *ProviderService) GetProvidersByCategory(category string) []sharedProviders.ProviderInfo {
	var filtered []sharedProviders.ProviderInfo

	allProviders := s.ListProviders()
	for _, provider := range allProviders {
		if provider.Category == category {
			filtered = append(filtered, provider)
		}
	}

	return filtered
}

// GetProvidersByCapability returns providers that have a specific capability
func (s *ProviderService) GetProvidersByCapability(capability sharedProviders.ProviderCapability) []sharedProviders.ProviderInfo {
	var filtered []sharedProviders.ProviderInfo

	allProviders := s.ListProviders()
	for _, provider := range allProviders {
		for _, cap := range provider.Capabilities {
			if cap == capability {
				filtered = append(filtered, provider)
				break
			}
		}
	}

	return filtered
}

// ValidateProviderConfig validates a provider configuration
func (s *ProviderService) ValidateProviderConfig(providerType string, config map[string]interface{}) error {
	provider, err := s.GetProvider(providerType)
	if err != nil {
		return err
	}

	return provider.Validate(config)
}

// GetAvailableCategories returns all available provider categories
func (s *ProviderService) GetAvailableCategories() []string {
	categoryMap := make(map[string]bool)

	allProviders := s.ListProviders()
	for _, provider := range allProviders {
		categoryMap[provider.Category] = true
	}

	var categories []string
	for category := range categoryMap {
		categories = append(categories, category)
	}

	return categories
}

// GetAvailableCapabilities returns all available provider capabilities
func (s *ProviderService) GetAvailableCapabilities() []sharedProviders.ProviderCapability {
	capabilityMap := make(map[sharedProviders.ProviderCapability]bool)

	allProviders := s.ListProviders()
	for _, provider := range allProviders {
		for _, capability := range provider.Capabilities {
			capabilityMap[capability] = true
		}
	}

	var capabilities []sharedProviders.ProviderCapability
	for capability := range capabilityMap {
		capabilities = append(capabilities, capability)
	}

	return capabilities
}
