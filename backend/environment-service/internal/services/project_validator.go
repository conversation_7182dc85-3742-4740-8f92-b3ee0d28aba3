package services

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/logging"
)

// ProjectValidationError represents an error with HTTP status code
type ProjectValidationError struct {
	StatusCode int
	Message    string
	Cause      error
}

func (e *ProjectValidationError) Error() string {
	return e.Message
}

func (e *ProjectValidationError) Unwrap() error {
	return e.Cause
}

// NewProjectValidationError creates a new project validation error with status code
func NewProjectValidationError(statusCode int, message string, cause error) *ProjectValidationError {
	return &ProjectValidationError{
		StatusCode: statusCode,
		Message:    message,
		Cause:      cause,
	}
}

// ProjectValidator handles project validation via admin-service API
type ProjectValidator struct {
	adminServiceURL string
	httpClient      *http.Client
	logger          logging.Logger
}

// Project represents a project from admin-service
type Project struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	IsActive    bool   `json:"isActive"`
	CreatedAt   string `json:"createdAt"`
	UpdatedAt   string `json:"updatedAt"`
}

// ProjectAccessResponse represents the response from project access check
type ProjectAccessResponse struct {
	Access bool `json:"access"`
}

// NewProjectValidator creates a new project validator
func NewProjectValidator(adminServiceURL string, logger logging.Logger) *ProjectValidator {
	return &ProjectValidator{
		adminServiceURL: adminServiceURL,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
		logger: logger,
	}
}

// ValidateProject checks if a project exists and is accessible
func (pv *ProjectValidator) ValidateProject(ctx context.Context, projectID string, authToken string) error {
	if projectID == "" {
		return fmt.Errorf("project ID cannot be empty")
	}

	pv.logger.Debug("Starting project validation",
		logging.String("projectId", projectID),
		logging.String("tokenPrefix", func() string {
			if authToken == "" {
				return "EMPTY"
			}
			if len(authToken) > 20 {
				return authToken[:20] + "..."
			}
			return authToken
		}()))

	// Check if project exists
	project, err := pv.getProject(ctx, projectID, authToken)
	if err != nil {
		pv.logger.Error("Project validation failed",
			logging.String("projectId", projectID),
			logging.Error(err))
		return fmt.Errorf("failed to validate project: %w", err)
	}

	// Check if project is active
	if !project.IsActive {
		return fmt.Errorf("project %s is not active (isActive: %t)", projectID, project.IsActive)
	}

	pv.logger.Info("Project validation successful",
		logging.String("projectId", projectID),
		logging.String("projectName", project.Name),
		logging.Bool("isActive", project.IsActive),
	)

	return nil
}

// ValidateProjectAccess checks if a user has access to a project
func (pv *ProjectValidator) ValidateProjectAccess(ctx context.Context, projectID, userID, authToken string) error {
	if projectID == "" {
		return fmt.Errorf("project ID cannot be empty")
	}
	if userID == "" {
		return fmt.Errorf("user ID cannot be empty")
	}

	// Check project access
	hasAccess, err := pv.checkProjectAccess(ctx, projectID, userID, authToken)
	if err != nil {
		return fmt.Errorf("failed to check project access: %w", err)
	}

	if !hasAccess {
		return fmt.Errorf("user %s does not have access to project %s", userID, projectID)
	}

	pv.logger.Info("Project access validation successful",
		logging.String("projectId", projectID),
		logging.String("userId", userID),
	)

	return nil
}

// getProject retrieves project details from admin-service
func (pv *ProjectValidator) getProject(ctx context.Context, projectID, authToken string) (*Project, error) {
	url := fmt.Sprintf("%s/api/v1/admin-service/projects/%s", pv.adminServiceURL, projectID)

	pv.logger.Debug("Making request to admin service",
		logging.String("url", url),
		logging.String("authTokenPresent", func() string {
			if authToken == "" {
				return "NO"
			}
			return "YES"
		}()))

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add authorization header
	if authToken != "" {
		req.Header.Set("Authorization", "Bearer "+authToken)
		pv.logger.Debug("Added Authorization header to request")
	} else {
		pv.logger.Warn("No auth token provided - request will be unauthenticated")
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := pv.httpClient.Do(req)
	if err != nil {
		pv.logger.Error("Failed to make HTTP request", logging.Error(err))
		return nil, fmt.Errorf("failed to make request to admin-service: %w", err)
	}
	defer resp.Body.Close()

	pv.logger.Debug("Received response from admin service",
		logging.Int("statusCode", resp.StatusCode))

	if resp.StatusCode == http.StatusUnauthorized {
		pv.logger.Warn("Admin service returned 401 Unauthorized")
		return nil, NewProjectValidationError(http.StatusUnauthorized, "Authentication required", fmt.Errorf("admin-service returned 401"))
	}

	if resp.StatusCode == http.StatusForbidden {
		pv.logger.Warn("Admin service returned 403 Forbidden")
		return nil, NewProjectValidationError(http.StatusForbidden, "Access denied to project "+projectID, fmt.Errorf("admin-service returned 403"))
	}

	if resp.StatusCode == http.StatusNotFound {
		pv.logger.Warn("Admin service returned 404 Not Found")
		return nil, NewProjectValidationError(http.StatusNotFound, "Project "+projectID+" not found", fmt.Errorf("admin-service returned 404"))
	}

	if resp.StatusCode != http.StatusOK {
		pv.logger.Error("Admin service returned unexpected status",
			logging.Int("statusCode", resp.StatusCode))
		return nil, NewProjectValidationError(http.StatusInternalServerError, fmt.Sprintf("Admin service error (status %d)", resp.StatusCode), fmt.Errorf("admin-service returned status %d", resp.StatusCode))
	}

	var project Project
	if err := json.NewDecoder(resp.Body).Decode(&project); err != nil {
		pv.logger.Error("Failed to decode project response", logging.Error(err))
		return nil, fmt.Errorf("failed to decode project response: %w", err)
	}

	pv.logger.Debug("Successfully retrieved project",
		logging.String("projectName", project.Name),
		logging.Bool("isActive", project.IsActive))

	return &project, nil
}

// checkProjectAccess checks if a user has access to a project
func (pv *ProjectValidator) checkProjectAccess(ctx context.Context, projectID, userID, authToken string) (bool, error) {
	url := fmt.Sprintf("%s/api/v1/admin-service/users/%s/projects/%s/access", pv.adminServiceURL, userID, projectID)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return false, fmt.Errorf("failed to create request: %w", err)
	}

	// Add authorization header
	if authToken != "" {
		req.Header.Set("Authorization", "Bearer "+authToken)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := pv.httpClient.Do(req)
	if err != nil {
		return false, fmt.Errorf("failed to make request to admin-service: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusUnauthorized {
		return false, NewProjectValidationError(http.StatusUnauthorized, "Authentication required", fmt.Errorf("admin-service returned 401"))
	}

	if resp.StatusCode == http.StatusForbidden {
		return false, NewProjectValidationError(http.StatusForbidden, "Access denied", fmt.Errorf("admin-service returned 403"))
	}

	if resp.StatusCode == http.StatusNotFound {
		return false, nil // No access - this is expected for users without access
	}

	if resp.StatusCode != http.StatusOK {
		return false, NewProjectValidationError(http.StatusInternalServerError, fmt.Sprintf("Admin service error (status %d)", resp.StatusCode), fmt.Errorf("admin-service returned status %d", resp.StatusCode))
	}

	var accessResp ProjectAccessResponse
	if err := json.NewDecoder(resp.Body).Decode(&accessResp); err != nil {
		return false, fmt.Errorf("failed to decode access response: %w", err)
	}

	return accessResp.Access, nil
}

// GetProjectName retrieves the project name for display purposes
func (pv *ProjectValidator) GetProjectName(ctx context.Context, projectID, authToken string) (string, error) {
	project, err := pv.getProject(ctx, projectID, authToken)
	if err != nil {
		return "", err
	}
	return project.Name, nil
}

// HealthCheck checks if the admin-service is reachable
func (pv *ProjectValidator) HealthCheck(ctx context.Context) error {
	url := fmt.Sprintf("%s/health", pv.adminServiceURL)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create health check request: %w", err)
	}

	resp, err := pv.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to reach admin-service: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("admin-service health check failed with status %d", resp.StatusCode)
	}

	return nil
}
