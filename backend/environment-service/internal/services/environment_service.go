package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/claudio/deploy-orchestrator/environment-service/config"
	"github.com/claudio/deploy-orchestrator/environment-service/internal/providers"
	"github.com/claudio/deploy-orchestrator/shared/logging"
	"github.com/claudio/deploy-orchestrator/shared/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Request/Response types for cloning and migration
type CloneEnvironmentRequest struct {
	SourceEnvironmentID     string                 `json:"source_environment_id" binding:"required"`
	ProjectID               string                 `json:"project_id" binding:"required"`
	Name                    string                 `json:"name" binding:"required"`
	Description             string                 `json:"description"`
	VariableOverrides       map[string]string      `json:"variable_overrides"`
	ProviderConfigOverrides map[string]interface{} `json:"provider_config_overrides"`
	AdditionalTags          []string               `json:"additional_tags"`
}

type MigrateEnvironmentRequest struct {
	EnvironmentID  string                 `json:"environment_id" binding:"required"`
	TargetProvider string                 `json:"target_provider" binding:"required"`
	TargetConfig   map[string]interface{} `json:"target_config" binding:"required"`
	CreatedBy      string                 `json:"created_by" binding:"required"`
}

type MigrationResult struct {
	MigrationID string          `json:"migration_id"`
	Status      string          `json:"status"`
	Steps       []MigrationStep `json:"steps"`
	StartedAt   time.Time       `json:"started_at"`
	CompletedAt time.Time       `json:"completed_at,omitempty"`
}

type MigrationStep struct {
	Name        string    `json:"name"`
	Status      string    `json:"status"`
	Error       string    `json:"error,omitempty"`
	StartedAt   time.Time `json:"started_at"`
	CompletedAt time.Time `json:"completed_at,omitempty"`
}

type EnvironmentMigration struct {
	ID                  string                 `json:"id" gorm:"primaryKey"`
	SourceEnvironmentID string                 `json:"source_environment_id"`
	TargetProvider      string                 `json:"target_provider"`
	TargetConfig        map[string]interface{} `json:"target_config" gorm:"type:jsonb"`
	Status              string                 `json:"status"`
	BackupData          string                 `json:"backup_data" gorm:"type:text"`
	StartedAt           time.Time              `json:"started_at"`
	CompletedAt         time.Time              `json:"completed_at,omitempty"`
	CreatedBy           string                 `json:"created_by"`
}

// Request/Response types
type CreateEnvironmentRequest struct {
	ProjectID string                 `json:"projectId" binding:"required"`
	Name      string                 `json:"name" binding:"required"`
	Type      models.EnvironmentType `json:"type" binding:"required"`
	Provider  struct {
		Type   models.ProviderType   `json:"type"`
		Config models.ProviderConfig `json:"config"`
	} `json:"provider" binding:"required"`
	Resources      models.ResourceConfig   `json:"resources"`
	Networking     models.NetworkingConfig `json:"networking"`
	Variables      map[string]string       `json:"variables"`
	SecretMappings []models.SecretMapping  `json:"secretMappings"`
	HealthCheck    struct {
		Enabled  bool   `json:"enabled"`
		Endpoint string `json:"endpoint,omitempty"`
		Interval int    `json:"interval,omitempty"`
		Timeout  int    `json:"timeout,omitempty"`
	} `json:"healthCheck"`
	DeploymentStrategy string   `json:"deploymentStrategy"`
	Description        string   `json:"description"`
	Tags               []string `json:"tags"`
}

type UpdateEnvironmentRequest struct {
	Name     string `json:"name,omitempty"`
	Provider *struct {
		Type   models.ProviderType   `json:"type"`
		Config models.ProviderConfig `json:"config"`
	} `json:"provider,omitempty"`
	Resources      *models.ResourceConfig   `json:"resources,omitempty"`
	Networking     *models.NetworkingConfig `json:"networking,omitempty"`
	Variables      map[string]string        `json:"variables,omitempty"`
	SecretMappings []models.SecretMapping   `json:"secretMappings,omitempty"`
	HealthCheck    *struct {
		Enabled  bool   `json:"enabled"`
		Endpoint string `json:"endpoint,omitempty"`
		Interval int    `json:"interval,omitempty"`
		Timeout  int    `json:"timeout,omitempty"`
	} `json:"healthCheck,omitempty"`
	DeploymentStrategy string                   `json:"deploymentStrategy,omitempty"`
	Status             models.EnvironmentStatus `json:"status,omitempty"`
	Description        string                   `json:"description,omitempty"`
	Tags               []string                 `json:"tags,omitempty"`
}

type EnvironmentFilter struct {
	ProjectID string `form:"projectId"`
	Type      string `form:"type"`
	Status    string `form:"status"`
	Provider  string `form:"provider"`
	Limit     int    `form:"limit"`
	Offset    int    `form:"offset"`
}

type ConnectionTestResult struct {
	EnvironmentID string    `json:"environmentId"`
	Success       bool      `json:"success"`
	Message       string    `json:"message"`
	Details       string    `json:"details,omitempty"`
	TestedAt      time.Time `json:"testedAt"`
	Latency       int64     `json:"latency,omitempty"` // milliseconds
}

type VersionMatrix struct {
	ProjectID    string                        `json:"projectId"`
	Environments map[string]EnvironmentVersion `json:"environments"`
	GeneratedAt  time.Time                     `json:"generatedAt"`
}

type EnvironmentVersion struct {
	CurrentVersion string                `json:"currentVersion"`
	DeployedAt     time.Time             `json:"deployedAt"`
	Health         string                `json:"health"`
	Metrics        map[string]float64    `json:"metrics"`
	Services       []DeployedServiceInfo `json:"services"`
}

type DeployedServiceInfo struct {
	Name      string    `json:"name"`
	Version   string    `json:"version"`
	Status    string    `json:"status"`
	Health    string    `json:"health"`
	UpdatedAt time.Time `json:"updatedAt"`
}

// Additional types for handlers
type DeploymentRequest struct {
	WorkflowID string                 `json:"workflowId" binding:"required"`
	Version    models.VersionInfo     `json:"version" binding:"required"`
	Parameters map[string]interface{} `json:"parameters,omitempty"`
	Variables  map[string]interface{} `json:"variables,omitempty"`
}

type DeploymentResult struct {
	ID            string    `json:"id"`
	EnvironmentID string    `json:"environmentId"`
	WorkflowID    string    `json:"workflowId"`
	Status        string    `json:"status"`
	StartedAt     time.Time `json:"startedAt"`
}

type EnvironmentStatus struct {
	EnvironmentID string                   `json:"environmentId"`
	Status        models.EnvironmentStatus `json:"status"`
	Health        string                   `json:"health"`
	LastCheck     time.Time                `json:"lastCheck"`
	Metrics       map[string]interface{}   `json:"metrics,omitempty"`
	Issues        []string                 `json:"issues,omitempty"`
}

type LogFilter struct {
	Level string `json:"level,omitempty"`
	Since string `json:"since,omitempty"`
	Until string `json:"until,omitempty"`
	Limit int    `json:"limit,omitempty"`
}

type LogEntry struct {
	ID        string    `json:"id"`
	Level     string    `json:"level"`
	Message   string    `json:"message"`
	Timestamp time.Time `json:"timestamp"`
	Source    string    `json:"source,omitempty"`
	Tags      []string  `json:"tags,omitempty"`
}

type ProviderInfo struct {
	Type         models.ProviderType   `json:"type"`
	Name         string                `json:"name"`
	Description  string                `json:"description"`
	Capabilities []string              `json:"capabilities"`
	ConfigFields []ProviderConfigField `json:"configFields"`
}

type ProviderConfigField struct {
	Name        string      `json:"name"`
	Type        string      `json:"type"`
	Required    bool        `json:"required"`
	Description string      `json:"description"`
	Default     interface{} `json:"default,omitempty"`
	Options     []string    `json:"options,omitempty"`
	Sensitive   bool        `json:"sensitive,omitempty"`
}

type ProviderCapabilities struct {
	Type                models.ProviderType   `json:"type"`
	SupportedOperations []string              `json:"supportedOperations"`
	ConfigFields        []ProviderConfigField `json:"configFields"`
	HealthCheckSupport  bool                  `json:"healthCheckSupport"`
	LoggingSupport      bool                  `json:"loggingSupport"`
	MetricsSupport      bool                  `json:"metricsSupport"`
}

type ValidationResult struct {
	Valid    bool     `json:"valid"`
	Errors   []string `json:"errors,omitempty"`
	Warnings []string `json:"warnings,omitempty"`
}

type DeploymentHistory struct {
	EnvironmentID string                   `json:"environmentId"`
	Deployments   []DeploymentHistoryEntry `json:"deployments"`
	Total         int                      `json:"total"`
}

type DeploymentHistoryEntry struct {
	ID          string             `json:"id"`
	WorkflowID  string             `json:"workflowId"`
	Version     models.VersionInfo `json:"version"`
	Status      string             `json:"status"`
	StartedAt   time.Time          `json:"startedAt"`
	FinishedAt  *time.Time         `json:"finishedAt,omitempty"`
	Duration    int64              `json:"duration,omitempty"` // milliseconds
	TriggeredBy string             `json:"triggeredBy"`
}

type EnvironmentService struct {
	db               *gorm.DB
	providerRegistry *providers.ProviderRegistry
	projectValidator *ProjectValidator
	logger           logging.Logger
}

func NewEnvironmentService(db *gorm.DB, projectValidator *ProjectValidator, logger logging.Logger) *EnvironmentService {
	return &EnvironmentService{
		db:               db,
		providerRegistry: providers.GetGlobalRegistry(),
		projectValidator: projectValidator,
		logger:           logger,
	}
}

// CreateEnvironment creates a new environment configuration
func (s *EnvironmentService) CreateEnvironment(ctx context.Context, req *CreateEnvironmentRequest) (*models.EnvironmentConfig, error) {
	// Validate project exists and user has access (if project validator is available)
	if s.projectValidator != nil {
		// Extract auth token from context (you may need to adjust this based on your auth implementation)
		authToken := s.extractAuthTokenFromContext(ctx)

		if err := s.projectValidator.ValidateProject(ctx, req.ProjectID, authToken); err != nil {
			s.logger.Error("Project validation failed",
				logging.String("projectId", req.ProjectID),
				logging.Error(err),
			)
			return nil, fmt.Errorf("project validation failed: %w", err)
		}

		s.logger.Info("Project validation successful",
			logging.String("projectId", req.ProjectID),
		)
	}

	env := &models.EnvironmentConfig{
		ID:                 uuid.New().String(),
		ProjectID:          req.ProjectID,
		Name:               req.Name,
		Type:               req.Type,
		Resources:          req.Resources,
		Networking:         req.Networking,
		Variables:          req.Variables,
		SecretMappings:     req.SecretMappings,
		DeploymentStrategy: req.DeploymentStrategy,
		Status:             models.EnvironmentStatusInactive,
		Description:        req.Description,
		Tags:               req.Tags,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}

	// Set provider configuration
	env.Provider.Type = req.Provider.Type
	env.Provider.Config = req.Provider.Config

	// Set health check configuration
	env.HealthCheck.Enabled = req.HealthCheck.Enabled
	env.HealthCheck.Endpoint = req.HealthCheck.Endpoint
	env.HealthCheck.Interval = req.HealthCheck.Interval
	env.HealthCheck.Timeout = req.HealthCheck.Timeout

	// Validate provider configuration
	if err := s.validateProviderConfig(env); err != nil {
		return nil, fmt.Errorf("invalid provider configuration: %w", err)
	}

	// Encrypt sensitive data in provider config
	if err := s.encryptProviderCredentials(env); err != nil {
		return nil, fmt.Errorf("failed to encrypt credentials: %w", err)
	}

	if err := s.db.Create(env).Error; err != nil {
		return nil, fmt.Errorf("failed to create environment: %w", err)
	}

	return env, nil
}

// GetEnvironment retrieves an environment by ID
func (s *EnvironmentService) GetEnvironment(ctx context.Context, id string) (*models.EnvironmentConfig, error) {
	var env models.EnvironmentConfig
	if err := s.db.First(&env, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("environment not found")
		}
		return nil, fmt.Errorf("failed to get environment: %w", err)
	}

	// Decrypt credentials for response (mask sensitive data)
	s.maskSensitiveData(&env)

	return &env, nil
}

// ListEnvironments retrieves environments with filtering
func (s *EnvironmentService) ListEnvironments(ctx context.Context, filter *EnvironmentFilter) ([]*models.EnvironmentConfig, int64, error) {
	query := s.db.Model(&models.EnvironmentConfig{})

	// Apply filters
	if filter.ProjectID != "" {
		query = query.Where("project_id = ?", filter.ProjectID)
	}
	if filter.Type != "" {
		query = query.Where("type = ?", filter.Type)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.Provider != "" {
		query = query.Where("provider->>'type' = ?", filter.Provider)
	}

	// Count total
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count environments: %w", err)
	}

	// Apply pagination
	if filter.Limit > 0 {
		query = query.Limit(filter.Limit)
	}
	if filter.Offset > 0 {
		query = query.Offset(filter.Offset)
	}

	var environments []*models.EnvironmentConfig
	if err := query.Find(&environments).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list environments: %w", err)
	}

	// Mask sensitive data
	for _, env := range environments {
		s.maskSensitiveData(env)
	}

	return environments, total, nil
}

// UpdateEnvironment updates an existing environment
func (s *EnvironmentService) UpdateEnvironment(ctx context.Context, id string, req *UpdateEnvironmentRequest) (*models.EnvironmentConfig, error) {
	var env models.EnvironmentConfig
	if err := s.db.First(&env, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("environment not found")
		}
		return nil, fmt.Errorf("failed to get environment: %w", err)
	}

	// Update fields
	if req.Name != "" {
		env.Name = req.Name
	}
	if req.Description != "" {
		env.Description = req.Description
	}
	if req.Resources != nil {
		env.Resources = *req.Resources
	}
	if req.Networking != nil {
		env.Networking = *req.Networking
	}
	if req.Variables != nil {
		env.Variables = req.Variables
	}
	if req.SecretMappings != nil {
		env.SecretMappings = req.SecretMappings
	}
	if req.HealthCheck != nil {
		env.HealthCheck.Enabled = req.HealthCheck.Enabled
		env.HealthCheck.Endpoint = req.HealthCheck.Endpoint
		env.HealthCheck.Interval = req.HealthCheck.Interval
		env.HealthCheck.Timeout = req.HealthCheck.Timeout
	}
	if req.DeploymentStrategy != "" {
		env.DeploymentStrategy = req.DeploymentStrategy
	}
	if req.Status != "" {
		env.Status = req.Status
	}
	if req.Tags != nil {
		env.Tags = req.Tags
	}

	env.UpdatedAt = time.Now()

	// Validate and encrypt if provider config changed
	if req.Provider != nil {
		env.Provider.Type = req.Provider.Type
		env.Provider.Config = req.Provider.Config
		if err := s.validateProviderConfig(&env); err != nil {
			return nil, fmt.Errorf("invalid provider configuration: %w", err)
		}
		if err := s.encryptProviderCredentials(&env); err != nil {
			return nil, fmt.Errorf("failed to encrypt credentials: %w", err)
		}
	}

	if err := s.db.Save(&env).Error; err != nil {
		return nil, fmt.Errorf("failed to update environment: %w", err)
	}

	s.maskSensitiveData(&env)
	return &env, nil
}

// DeleteEnvironment deletes an environment
func (s *EnvironmentService) DeleteEnvironment(ctx context.Context, id string) error {
	// Check if environment has active deployments
	var count int64
	if err := s.db.Model(&models.WorkflowExecution{}).
		Where("environment_id = ? AND status IN (?)", id, []string{"running", "pending"}).
		Count(&count).Error; err != nil {
		return fmt.Errorf("failed to check active deployments: %w", err)
	}

	if count > 0 {
		return fmt.Errorf("cannot delete environment with active deployments")
	}

	if err := s.db.Delete(&models.EnvironmentConfig{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete environment: %w", err)
	}

	return nil
}

// TestConnection tests connectivity to the environment
func (s *EnvironmentService) TestConnection(ctx context.Context, id string) (*ConnectionTestResult, error) {
	env, err := s.GetEnvironment(ctx, id)
	if err != nil {
		return nil, err
	}

	// Decrypt credentials for testing
	if err := s.decryptProviderCredentials(env); err != nil {
		return nil, fmt.Errorf("failed to decrypt credentials: %w", err)
	}

	// Get provider from registry
	provider, err := s.providerRegistry.GetProvider(string(env.Provider.Type))
	if err != nil {
		return &ConnectionTestResult{
			EnvironmentID: id,
			Success:       false,
			Message:       fmt.Sprintf("Unsupported provider type: %s", env.Provider.Type),
			TestedAt:      time.Now(),
		}, nil
	}

	// Test connection using provider health check
	healthStatus, err := provider.HealthCheck(ctx, env.ID)
	if err != nil {
		return &ConnectionTestResult{
			EnvironmentID: id,
			Success:       false,
			Message:       fmt.Sprintf("Connection test failed: %s", err.Error()),
			TestedAt:      time.Now(),
		}, nil
	}

	// Convert provider result to our result format
	result := &ConnectionTestResult{
		EnvironmentID: id,
		Success:       healthStatus.Status == "healthy",
		Message:       healthStatus.Message,
		Details:       fmt.Sprintf("Health checks: %d", len(healthStatus.Checks)),
		TestedAt:      time.Now(),
	}

	return result, nil
}

// ReloadExternalProviders reloads external providers from configuration
func (s *EnvironmentService) ReloadExternalProviders(externalProviders []config.ExternalProviderConfig) error {
	return providers.ReloadExternalProviders(externalProviders)
}

// Helper methods

func (s *EnvironmentService) validateProviderConfig(env *models.EnvironmentConfig) error {
	// Get provider from registry
	provider, err := s.providerRegistry.GetProvider(string(env.Provider.Type))
	if err != nil {
		return fmt.Errorf("unsupported provider type: %s", env.Provider.Type)
	}

	// Convert provider config to map for validation
	configMap := s.buildConfigMap(env)

	// Validate using provider
	return provider.Validate(configMap)
}

func (s *EnvironmentService) buildConfigMap(env *models.EnvironmentConfig) map[string]interface{} {
	configMap := make(map[string]interface{})

	// Debug logging
	fmt.Printf("DEBUG: Building config map for provider type: %s\n", env.Provider.Type)
	fmt.Printf("DEBUG: Provider config endpoint: %s\n", env.Provider.Config.Endpoint)
	fmt.Printf("DEBUG: Provider config cluster_endpoint: %s\n", env.Provider.Config.ClusterEndpoint)
	fmt.Printf("DEBUG: Provider config extra: %+v\n", env.Provider.Config.Extra)

	// Add credentials
	if env.Provider.Config.Credentials != nil {
		for k, v := range env.Provider.Config.Credentials {
			configMap[k] = v
		}
	}

	// Add other config fields
	if env.Provider.Config.Cluster != "" {
		configMap["cluster"] = env.Provider.Config.Cluster
	}
	if env.Provider.Config.Namespace != "" {
		configMap["namespace"] = env.Provider.Config.Namespace
		fmt.Printf("DEBUG: Using Namespace field: %s\n", env.Provider.Config.Namespace)
	}
	if env.Provider.Config.Zone != "" {
		configMap["zone"] = env.Provider.Config.Zone
	}
	if env.Provider.Config.Region != "" {
		configMap["region"] = env.Provider.Config.Region
	}
	if env.Provider.Config.Project != "" {
		configMap["project"] = env.Provider.Config.Project
	}
	if env.Provider.Config.ResourceGroup != "" {
		configMap["resourceGroup"] = env.Provider.Config.ResourceGroup
	}
	if env.Provider.Config.SubscriptionID != "" {
		configMap["subscriptionId"] = env.Provider.Config.SubscriptionID
	}
	if env.Provider.Config.Endpoint != "" {
		configMap["endpoint"] = env.Provider.Config.Endpoint
	}

	// Handle ClusterEndpoint field for Kubernetes providers
	if env.Provider.Config.ClusterEndpoint != "" {
		configMap["cluster_endpoint"] = env.Provider.Config.ClusterEndpoint
		fmt.Printf("DEBUG: Using ClusterEndpoint field: %s\n", env.Provider.Config.ClusterEndpoint)
	} else if env.Provider.Type == "kubernetes" && env.Provider.Config.Endpoint != "" {
		// Fallback to endpoint for backward compatibility
		configMap["cluster_endpoint"] = env.Provider.Config.Endpoint
		fmt.Printf("DEBUG: Using Endpoint as cluster_endpoint for backward compatibility: %s\n", env.Provider.Config.Endpoint)
	}

	// Add auth method with proper field mapping
	if env.Provider.Config.AuthMethod != "" {
		configMap["auth_method"] = string(env.Provider.Config.AuthMethod)
		configMap["authMethod"] = string(env.Provider.Config.AuthMethod) // Also add original field name for compatibility
	}

	// Add extra fields
	if env.Provider.Config.Extra != nil {
		for k, v := range env.Provider.Config.Extra {
			configMap[k] = v
		}
	}

	// Final check for Kubernetes provider - ensure cluster_endpoint is available
	if env.Provider.Type == "kubernetes" {
		if _, exists := configMap["cluster_endpoint"]; !exists {
			fmt.Printf("DEBUG: WARNING - No cluster_endpoint found for Kubernetes provider!\n")
		}
	}

	fmt.Printf("DEBUG: Final config map: %+v\n", configMap)
	return configMap
}

func (s *EnvironmentService) encryptProviderCredentials(env *models.EnvironmentConfig) error {
	// TODO: Implement encryption of sensitive credentials
	// This should encrypt fields like service account keys, tokens, etc.
	return nil
}

func (s *EnvironmentService) decryptProviderCredentials(env *models.EnvironmentConfig) error {
	// TODO: Implement decryption of sensitive credentials
	return nil
}

func (s *EnvironmentService) maskSensitiveData(env *models.EnvironmentConfig) {
	// Mask sensitive data in credentials
	if env.Provider.Config.Credentials != nil {
		masked := make(map[string]interface{})
		for key := range env.Provider.Config.Credentials {
			masked[key] = "***MASKED***"
		}
		env.Provider.Config.Credentials = masked
	}
}

// extractAuthTokenFromContext extracts the auth token from the request context
func (s *EnvironmentService) extractAuthTokenFromContext(ctx context.Context) string {
	s.logger.Debug("Extracting auth token from context...")

	// Try to get the token from context (stored by middleware)
	if token := ctx.Value("auth_token"); token != nil {
		if tokenStr, ok := token.(string); ok {
			s.logger.Debug("Found auth token in context",
				logging.String("tokenPrefix", tokenStr[:min(20, len(tokenStr))]+"..."))
			return tokenStr
		}
	}

	// Try to get from Authorization header in context
	if authHeader := ctx.Value("Authorization"); authHeader != nil {
		if authStr, ok := authHeader.(string); ok {
			s.logger.Debug("Found Authorization header in context",
				logging.String("header", authStr[:min(30, len(authStr))]+"..."))
			// Remove "Bearer " prefix if present
			if len(authStr) > 7 && authStr[:7] == "Bearer " {
				return authStr[7:]
			}
			return authStr
		}
	}

	// Try to get the raw authorization header
	if rawAuth := ctx.Value("raw_auth_header"); rawAuth != nil {
		if authStr, ok := rawAuth.(string); ok {
			s.logger.Debug("Found raw auth header in context",
				logging.String("rawHeader", authStr[:min(30, len(authStr))]+"..."))
			// Remove "Bearer " prefix if present
			if len(authStr) > 7 && authStr[:7] == "Bearer " {
				return authStr[7:]
			}
			return authStr
		}
	}

	// Return empty string if no token found
	s.logger.Warn("No auth token found in context - available keys:",
		logging.Any("contextKeys", getContextKeys(ctx)))
	return ""
}

// Helper function to debug context keys
func getContextKeys(ctx context.Context) []string {
	keys := []string{}
	// This is a debugging helper - in production you might want to be more careful
	// about what context values you log
	if ctx.Value("auth_token") != nil {
		keys = append(keys, "auth_token")
	}
	if ctx.Value("Authorization") != nil {
		keys = append(keys, "Authorization")
	}
	if ctx.Value("raw_auth_header") != nil {
		keys = append(keys, "raw_auth_header")
	}
	return keys
}

// Helper function for safe string truncation
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// Provider-specific validation methods
func (s *EnvironmentService) validateGKEConfig(config models.ProviderConfig) error {
	if config.Project == "" {
		return fmt.Errorf("GKE project is required")
	}
	if config.Cluster == "" {
		return fmt.Errorf("GKE cluster name is required")
	}
	if config.Zone == "" && config.Region == "" {
		return fmt.Errorf("GKE zone or region is required")
	}
	return nil
}

func (s *EnvironmentService) validateAKSConfig(config models.ProviderConfig) error {
	if config.ResourceGroup == "" {
		return fmt.Errorf("AKS resource group is required")
	}
	if config.SubscriptionID == "" {
		return fmt.Errorf("AKS subscription ID is required")
	}
	if config.Cluster == "" {
		return fmt.Errorf("AKS cluster name is required")
	}
	return nil
}

func (s *EnvironmentService) validateEKSConfig(config models.ProviderConfig) error {
	if config.Cluster == "" {
		return fmt.Errorf("EKS cluster name is required")
	}
	if config.Region == "" {
		return fmt.Errorf("EKS region is required")
	}
	return nil
}

func (s *EnvironmentService) validateOpenShiftConfig(config models.ProviderConfig) error {
	if config.Endpoint == "" {
		return fmt.Errorf("OpenShift endpoint is required")
	}
	return nil
}

// Provider-specific connection test methods
func (s *EnvironmentService) testGKEConnection(env *models.EnvironmentConfig) *ConnectionTestResult {
	result := &ConnectionTestResult{
		EnvironmentID: env.ID,
		TestedAt:      time.Now(),
	}

	// TODO: Implement actual GKE connection test
	// This would use the Google Cloud SDK to test cluster connectivity
	result.Success = true
	result.Message = "GKE connection test successful"
	result.Latency = 150 // mock latency

	return result
}

func (s *EnvironmentService) testAKSConnection(env *models.EnvironmentConfig) *ConnectionTestResult {
	result := &ConnectionTestResult{
		EnvironmentID: env.ID,
		TestedAt:      time.Now(),
	}

	// TODO: Implement actual AKS connection test
	// This would use the Azure SDK to test cluster connectivity
	result.Success = true
	result.Message = "AKS connection test successful"
	result.Latency = 200 // mock latency

	return result
}

func (s *EnvironmentService) testEKSConnection(env *models.EnvironmentConfig) *ConnectionTestResult {
	result := &ConnectionTestResult{
		EnvironmentID: env.ID,
		TestedAt:      time.Now(),
	}

	// TODO: Implement actual EKS connection test
	// This would use the AWS SDK to test cluster connectivity
	result.Success = true
	result.Message = "EKS connection test successful"
	result.Latency = 180 // mock latency

	return result
}

func (s *EnvironmentService) testOpenShiftConnection(env *models.EnvironmentConfig) *ConnectionTestResult {
	result := &ConnectionTestResult{
		EnvironmentID: env.ID,
		TestedAt:      time.Now(),
	}

	// TODO: Implement actual OpenShift connection test
	// This would use the OpenShift client to test cluster connectivity
	result.Success = true
	result.Message = "OpenShift connection test successful"
	result.Latency = 120 // mock latency

	return result
}

// Additional methods for handlers

// DeployToEnvironment starts a deployment to the environment
func (s *EnvironmentService) DeployToEnvironment(ctx context.Context, id string, req *DeploymentRequest) (*DeploymentResult, error) {
	// Get environment to validate it exists
	_, err := s.GetEnvironment(ctx, id)
	if err != nil {
		return nil, err
	}

	// TODO: Integrate with workflow service to start deployment
	// For now, return a mock result
	result := &DeploymentResult{
		ID:            "exec-" + uuid.New().String(),
		EnvironmentID: id,
		WorkflowID:    req.WorkflowID,
		Status:        "pending",
		StartedAt:     time.Now(),
	}

	return result, nil
}

// GetEnvironmentStatus returns the current status of an environment
func (s *EnvironmentService) GetEnvironmentStatus(ctx context.Context, id string) (*EnvironmentStatus, error) {
	env, err := s.GetEnvironment(ctx, id)
	if err != nil {
		return nil, err
	}

	// TODO: Implement actual status checking
	status := &EnvironmentStatus{
		EnvironmentID: id,
		Status:        env.Status,
		Health:        "healthy",
		LastCheck:     time.Now(),
		Metrics:       map[string]interface{}{},
		Issues:        []string{},
	}

	return status, nil
}

// GetEnvironmentLogs returns logs for an environment
func (s *EnvironmentService) GetEnvironmentLogs(ctx context.Context, id string, filter *LogFilter) ([]*LogEntry, error) {
	// TODO: Implement actual log retrieval
	// For now, return empty logs
	return []*LogEntry{}, nil
}

// ListProjectEnvironments returns environments for a specific project
func (s *EnvironmentService) ListProjectEnvironments(ctx context.Context, projectID string) ([]*models.EnvironmentConfig, error) {
	filter := &EnvironmentFilter{
		ProjectID: projectID,
	}

	environments, _, err := s.ListEnvironments(ctx, filter)
	return environments, err
}

// ListProviders returns available providers
func (s *EnvironmentService) ListProviders(ctx context.Context) ([]*ProviderInfo, error) {
	// Get providers from registry
	providerMetadata := s.providerRegistry.ListProviders()

	// Convert to ProviderInfo format for backward compatibility
	providers := make([]*ProviderInfo, len(providerMetadata))
	for i, meta := range providerMetadata {
		providers[i] = &ProviderInfo{
			Type:         models.ProviderType(meta.Name),
			Name:         meta.Description,
			Description:  meta.Description,
			Capabilities: meta.Capabilities,
		}
	}

	return providers, nil
}

// GetProviderCapabilities returns capabilities for a specific provider
func (s *EnvironmentService) GetProviderCapabilities(ctx context.Context, providerType string) (*ProviderCapabilities, error) {
	// TODO: Get from provider registry
	capabilities := &ProviderCapabilities{
		Type:                models.ProviderType(providerType),
		SupportedOperations: []string{"deploy", "scale", "monitor"},
		HealthCheckSupport:  true,
		LoggingSupport:      true,
		MetricsSupport:      true,
	}

	return capabilities, nil
}

// ValidateProviderConfig validates provider configuration
func (s *EnvironmentService) ValidateProviderConfig(ctx context.Context, providerType string, config map[string]interface{}) (*ValidationResult, error) {
	// Get provider from registry
	provider, err := s.providerRegistry.GetProvider(providerType)
	if err != nil {
		return &ValidationResult{
			Valid:  false,
			Errors: []string{fmt.Sprintf("Unsupported provider type: %s", providerType)},
		}, nil
	}

	// Validate using provider
	if err := provider.Validate(config); err != nil {
		return &ValidationResult{
			Valid:  false,
			Errors: []string{err.Error()},
		}, nil
	}

	return &ValidationResult{
		Valid: true,
	}, nil
}

// GetProviderSchema returns the configuration schema for a specific provider
func (s *EnvironmentService) GetProviderSchema(ctx context.Context, providerType string) (map[string]interface{}, error) {
	provider, err := s.providerRegistry.GetProvider(providerType)
	if err != nil {
		return nil, fmt.Errorf("provider not found: %s", providerType)
	}

	return provider.GetConfigSchema(), nil
}

// GetVersionMatrix returns version information across environments
func (s *EnvironmentService) GetVersionMatrix(ctx context.Context, projectID string) (*VersionMatrix, error) {
	// TODO: Implement actual version matrix
	matrix := &VersionMatrix{
		ProjectID:    projectID,
		Environments: map[string]EnvironmentVersion{},
		GeneratedAt:  time.Now(),
	}

	return matrix, nil
}

// CloneEnvironment creates a copy of an existing environment
func (s *EnvironmentService) CloneEnvironment(ctx context.Context, req *CloneEnvironmentRequest) (*models.EnvironmentConfig, error) {
	// Get the source environment
	sourceEnv, err := s.GetEnvironment(ctx, req.SourceEnvironmentID)
	if err != nil {
		return nil, fmt.Errorf("failed to get source environment: %w", err)
	}

	// Create new environment based on source
	newEnv := &models.EnvironmentConfig{
		ID:                 uuid.New().String(),
		ProjectID:          req.ProjectID,
		Name:               req.Name,
		Type:               sourceEnv.Type,
		Provider:           sourceEnv.Provider,
		Resources:          sourceEnv.Resources,
		Networking:         sourceEnv.Networking,
		Variables:          make(map[string]string),
		SecretMappings:     sourceEnv.SecretMappings,
		DeploymentStrategy: sourceEnv.DeploymentStrategy,
		HealthCheck:        sourceEnv.HealthCheck,
		Status:             models.EnvironmentStatusInactive,
		Description:        req.Description,
		Tags:               append(sourceEnv.Tags, req.AdditionalTags...),
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}

	// Copy variables, applying overrides
	for k, v := range sourceEnv.Variables {
		newEnv.Variables[k] = v
	}
	for k, v := range req.VariableOverrides {
		newEnv.Variables[k] = v
	}

	// Apply provider config overrides
	if req.ProviderConfigOverrides != nil {
		// Deep copy provider config
		configBytes, _ := json.Marshal(sourceEnv.Provider.Config)
		json.Unmarshal(configBytes, &newEnv.Provider.Config)

		// Apply overrides to the Extra field which is a map
		if newEnv.Provider.Config.Extra == nil {
			newEnv.Provider.Config.Extra = make(map[string]interface{})
		}
		for k, v := range req.ProviderConfigOverrides {
			newEnv.Provider.Config.Extra[k] = v
		}
	}

	// Validate the new configuration
	if err := s.validateProviderConfig(newEnv); err != nil {
		return nil, fmt.Errorf("invalid cloned configuration: %w", err)
	}

	// Encrypt sensitive data
	if err := s.encryptProviderCredentials(newEnv); err != nil {
		return nil, fmt.Errorf("failed to encrypt credentials: %w", err)
	}

	// Save the cloned environment
	if err := s.db.Create(newEnv).Error; err != nil {
		return nil, fmt.Errorf("failed to create cloned environment: %w", err)
	}

	return newEnv, nil
}

// MigrateEnvironment migrates an environment to a different provider or configuration
func (s *EnvironmentService) MigrateEnvironment(ctx context.Context, req *MigrateEnvironmentRequest) (*MigrationResult, error) {
	// Get the source environment
	sourceEnv, err := s.GetEnvironment(ctx, req.EnvironmentID)
	if err != nil {
		return nil, fmt.Errorf("failed to get source environment: %w", err)
	}

	migrationID := uuid.New().String()

	// Create migration record
	migration := &EnvironmentMigration{
		ID:                  migrationID,
		SourceEnvironmentID: req.EnvironmentID,
		TargetProvider:      req.TargetProvider,
		TargetConfig:        req.TargetConfig,
		Status:              "in_progress",
		StartedAt:           time.Now(),
		CreatedBy:           req.CreatedBy,
	}

	// Save migration record
	if err := s.db.Create(migration).Error; err != nil {
		return nil, fmt.Errorf("failed to create migration record: %w", err)
	}

	// Perform migration steps
	result := &MigrationResult{
		MigrationID: migrationID,
		Status:      "in_progress",
		Steps:       []MigrationStep{},
		StartedAt:   time.Now(),
	}

	// Step 1: Validate target configuration
	step1 := MigrationStep{
		Name:      "validate_target_config",
		Status:    "running",
		StartedAt: time.Now(),
	}

	targetProvider, err := s.providerRegistry.GetProvider(req.TargetProvider)
	if err != nil {
		step1.Status = "failed"
		step1.Error = err.Error()
		step1.CompletedAt = time.Now()
		result.Steps = append(result.Steps, step1)
		result.Status = "failed"
		return result, fmt.Errorf("target provider not found: %w", err)
	}

	if err := targetProvider.Validate(req.TargetConfig); err != nil {
		step1.Status = "failed"
		step1.Error = err.Error()
		step1.CompletedAt = time.Now()
		result.Steps = append(result.Steps, step1)
		result.Status = "failed"
		return result, fmt.Errorf("target configuration invalid: %w", err)
	}

	step1.Status = "completed"
	step1.CompletedAt = time.Now()
	result.Steps = append(result.Steps, step1)

	// Step 2: Create backup of current environment
	step2 := MigrationStep{
		Name:      "create_backup",
		Status:    "running",
		StartedAt: time.Now(),
	}

	backupData, err := json.Marshal(sourceEnv)
	if err != nil {
		step2.Status = "failed"
		step2.Error = err.Error()
		step2.CompletedAt = time.Now()
		result.Steps = append(result.Steps, step2)
		result.Status = "failed"
		return result, fmt.Errorf("failed to create backup: %w", err)
	}

	migration.BackupData = string(backupData)
	s.db.Save(migration)

	step2.Status = "completed"
	step2.CompletedAt = time.Now()
	result.Steps = append(result.Steps, step2)

	// Step 3: Update environment configuration
	step3 := MigrationStep{
		Name:      "update_configuration",
		Status:    "running",
		StartedAt: time.Now(),
	}

	// Update the environment with new provider configuration
	sourceEnv.Provider.Type = models.ProviderType(req.TargetProvider)

	// Convert map to ProviderConfig struct
	configBytes, _ := json.Marshal(req.TargetConfig)
	var newConfig models.ProviderConfig
	json.Unmarshal(configBytes, &newConfig)
	sourceEnv.Provider.Config = newConfig
	sourceEnv.UpdatedAt = time.Now()

	if err := s.encryptProviderCredentials(sourceEnv); err != nil {
		step3.Status = "failed"
		step3.Error = err.Error()
		step3.CompletedAt = time.Now()
		result.Steps = append(result.Steps, step3)
		result.Status = "failed"
		return result, fmt.Errorf("failed to encrypt new credentials: %w", err)
	}

	if err := s.db.Save(sourceEnv).Error; err != nil {
		step3.Status = "failed"
		step3.Error = err.Error()
		step3.CompletedAt = time.Now()
		result.Steps = append(result.Steps, step3)
		result.Status = "failed"
		return result, fmt.Errorf("failed to update environment: %w", err)
	}

	step3.Status = "completed"
	step3.CompletedAt = time.Now()
	result.Steps = append(result.Steps, step3)

	// Update migration record
	migration.Status = "completed"
	migration.CompletedAt = time.Now()
	s.db.Save(migration)

	result.Status = "completed"
	result.CompletedAt = time.Now()

	return result, nil
}

// GetDeploymentHistory returns deployment history for an environment
func (s *EnvironmentService) GetDeploymentHistory(ctx context.Context, environmentID string) (*DeploymentHistory, error) {
	// TODO: Implement actual deployment history
	history := &DeploymentHistory{
		EnvironmentID: environmentID,
		Deployments:   []DeploymentHistoryEntry{},
		Total:         0,
	}

	return history, nil
}
