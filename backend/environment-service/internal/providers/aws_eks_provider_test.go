package providers

import (
	"context"
	"testing"
)

func TestAWSEKSProvider_GetMetadata(t *testing.T) {
	provider := NewAWSEKSProvider()
	metadata := provider.GetMetadata()

	if metadata.Name != "aws-eks" {
		t.<PERSON>("Expected name 'aws-eks', got '%s'", metadata.Name)
	}

	if metadata.Type != "container-orchestration" {
		t.<PERSON>("Expected type 'container-orchestration', got '%s'", metadata.Type)
	}

	if metadata.Category != "kubernetes" {
		t.<PERSON>("Expected category 'kubernetes', got '%s'", metadata.Category)
	}

	expectedCapabilities := []string{
		"deploy", "scale", "monitor", "logs", "health-check",
		"auto-scaling", "load-balancing", "persistent-storage",
		"networking", "secrets", "ingress", "spot-instances",
	}

	if len(metadata.Capabilities) != len(expectedCapabilities) {
		t.<PERSON>("Expected %d capabilities, got %d", len(expectedCapabilities), len(metadata.Capabilities))
	}
}

func TestAWSEKSProvider_GetConfigSchema(t *testing.T) {
	provider := NewAWSEKSProvider()
	schema := provider.GetConfigSchema()

	// Check that schema is an object
	if schema["type"] != "object" {
		t.Errorf("Expected schema type 'object', got '%v'", schema["type"])
	}

	// Check required properties exist
	properties, ok := schema["properties"].(map[string]interface{})
	if !ok {
		t.Fatal("Schema properties should be a map")
	}

	requiredFields := []string{"region", "cluster_name", "access_key_id", "secret_access_key"}
	for _, field := range requiredFields {
		if _, exists := properties[field]; !exists {
			t.Errorf("Required field '%s' missing from schema", field)
		}
	}

	// Check required array
	required, ok := schema["required"].([]string)
	if !ok {
		t.Fatal("Schema required should be a string array")
	}

	if len(required) != len(requiredFields) {
		t.Errorf("Expected %d required fields, got %d", len(requiredFields), len(required))
	}
}

func TestAWSEKSProvider_Initialize(t *testing.T) {
	provider := NewAWSEKSProvider()

	tests := []struct {
		name        string
		config      map[string]interface{}
		expectError bool
	}{
		{
			name: "Valid configuration",
			config: map[string]interface{}{
				"region":            "us-west-2",
				"cluster_name":      "test-cluster",
				"access_key_id":     "AKIAIOSFODNN7EXAMPLE",
				"secret_access_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
			},
			expectError: false,
		},
		{
			name: "Missing required field",
			config: map[string]interface{}{
				"region":       "us-west-2",
				"cluster_name": "test-cluster",
				// Missing access_key_id and secret_access_key
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := provider.Initialize(tt.config)
			if tt.expectError && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
		})
	}
}

func TestAWSEKSProvider_Validate(t *testing.T) {
	provider := NewAWSEKSProvider()

	tests := []struct {
		name        string
		config      map[string]interface{}
		expectError bool
	}{
		{
			name: "Valid configuration",
			config: map[string]interface{}{
				"region":            "us-west-2",
				"cluster_name":      "test-cluster",
				"access_key_id":     "AKIAIOSFODNN7EXAMPLE",
				"secret_access_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
			},
			expectError: false,
		},
		{
			name: "Invalid AWS region",
			config: map[string]interface{}{
				"region":            "invalid-region",
				"cluster_name":      "test-cluster",
				"access_key_id":     "AKIAIOSFODNN7EXAMPLE",
				"secret_access_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
			},
			expectError: true,
		},
		{
			name: "Invalid access key format",
			config: map[string]interface{}{
				"region":            "us-west-2",
				"cluster_name":      "test-cluster",
				"access_key_id":     "short",
				"secret_access_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
			},
			expectError: true,
		},
		{
			name: "Invalid secret key format",
			config: map[string]interface{}{
				"region":            "us-west-2",
				"cluster_name":      "test-cluster",
				"access_key_id":     "AKIAIOSFODNN7EXAMPLE",
				"secret_access_key": "short",
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := provider.Validate(tt.config)
			if tt.expectError && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
		})
	}
}

func TestAWSEKSProvider_CreateEnvironment(t *testing.T) {
	provider := NewAWSEKSProvider()

	config := map[string]interface{}{
		"region":            "us-west-2",
		"cluster_name":      "test-cluster",
		"access_key_id":     "AKIAIOSFODNN7EXAMPLE",
		"secret_access_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
		"namespace":         "test-namespace",
	}

	err := provider.Initialize(config)
	if err != nil {
		t.Fatalf("Failed to initialize provider: %v", err)
	}

	envConfig := EnvironmentConfig{
		Name:     "test-environment",
		Type:     "kubernetes",
		Provider: "aws-eks",
		Config:   config,
	}

	ctx := context.Background()
	result, err := provider.CreateEnvironment(ctx, envConfig)
	if err != nil {
		t.Fatalf("Failed to create environment: %v", err)
	}

	if result.Name != "test-environment" {
		t.Errorf("Expected environment name 'test-environment', got '%s'", result.Name)
	}

	if result.Status != "created" {
		t.Errorf("Expected status 'created', got '%s'", result.Status)
	}

	if len(result.Resources) == 0 {
		t.Error("Expected resources to be created")
	}

	if len(result.Endpoints) == 0 {
		t.Error("Expected endpoints to be created")
	}
}

func TestAWSEKSProvider_GetEnvironmentStatus(t *testing.T) {
	provider := NewAWSEKSProvider()

	config := map[string]interface{}{
		"region":            "us-west-2",
		"cluster_name":      "test-cluster",
		"access_key_id":     "AKIAIOSFODNN7EXAMPLE",
		"secret_access_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
	}

	err := provider.Initialize(config)
	if err != nil {
		t.Fatalf("Failed to initialize provider: %v", err)
	}

	ctx := context.Background()
	status, err := provider.GetEnvironmentStatus(ctx, "test-environment")
	if err != nil {
		t.Fatalf("Failed to get environment status: %v", err)
	}

	if status.Status != "running" {
		t.Errorf("Expected status 'running', got '%s'", status.Status)
	}

	if status.Health != "healthy" {
		t.Errorf("Expected health 'healthy', got '%s'", status.Health)
	}

	if len(status.Resources) == 0 {
		t.Error("Expected resources in status")
	}

	if len(status.Endpoints) == 0 {
		t.Error("Expected endpoints in status")
	}
}

func TestAWSEKSProvider_HealthCheck(t *testing.T) {
	provider := NewAWSEKSProvider()

	config := map[string]interface{}{
		"region":            "us-west-2",
		"cluster_name":      "test-cluster",
		"access_key_id":     "AKIAIOSFODNN7EXAMPLE",
		"secret_access_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
	}

	err := provider.Initialize(config)
	if err != nil {
		t.Fatalf("Failed to initialize provider: %v", err)
	}

	ctx := context.Background()
	health, err := provider.HealthCheck(ctx, "test-environment")
	if err != nil {
		t.Fatalf("Failed to perform health check: %v", err)
	}

	if health.Status != "healthy" {
		t.Errorf("Expected health status 'healthy', got '%s'", health.Status)
	}

	if len(health.Checks) == 0 {
		t.Error("Expected health checks to be performed")
	}
}

func TestAWSEKSProvider_GetMetrics(t *testing.T) {
	provider := NewAWSEKSProvider()

	config := map[string]interface{}{
		"region":            "us-west-2",
		"cluster_name":      "test-cluster",
		"access_key_id":     "AKIAIOSFODNN7EXAMPLE",
		"secret_access_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
	}

	err := provider.Initialize(config)
	if err != nil {
		t.Fatalf("Failed to initialize provider: %v", err)
	}

	ctx := context.Background()
	metrics, err := provider.GetMetrics(ctx, "test-environment")
	if err != nil {
		t.Fatalf("Failed to get metrics: %v", err)
	}

	if len(metrics.Metrics) == 0 {
		t.Error("Expected metrics to be returned")
	}

	if len(metrics.Resources) == 0 {
		t.Error("Expected resource metrics to be returned")
	}
}
