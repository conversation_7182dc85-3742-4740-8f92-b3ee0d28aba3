package providers

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"sync"

	"github.com/claudio/deploy-orchestrator/environment-service/config"
)

// EnvironmentProvider defines the interface for environment providers
type EnvironmentProvider interface {
	// Metadata
	GetMetadata() ProviderMetadata
	GetConfigSchema() map[string]interface{}

	// Lifecycle
	Initialize(config map[string]interface{}) error
	Validate(config map[string]interface{}) error

	// Environment Operations
	CreateEnvironment(ctx context.Context, config EnvironmentConfig) (*EnvironmentResult, error)
	UpdateEnvironment(ctx context.Context, id string, config EnvironmentConfig) (*EnvironmentResult, error)
	DeleteEnvironment(ctx context.Context, id string) error
	GetEnvironmentStatus(ctx context.Context, id string) (*EnvironmentStatus, error)

	// Resource Operations
	GetResources(ctx context.Context, environmentID string) ([]Resource, error)
	ScaleResources(ctx context.Context, environmentID string, scaling ScalingConfig) error

	// Health and Monitoring
	HealthCheck(ctx context.Context, environmentID string) (*HealthStatus, error)
	GetMetrics(ctx context.Context, environmentID string) (*MetricsData, error)

	// Cleanup
	Cleanup() error
}

// ProviderMetadata contains provider information
type ProviderMetadata struct {
	Name          string                 `json:"name"`
	Version       string                 `json:"version"`
	Description   string                 `json:"description"`
	Author        string                 `json:"author"`
	Type          string                 `json:"type"`
	Category      string                 `json:"category"`
	Capabilities  []string               `json:"capabilities"`
	Tags          []string               `json:"tags"`
	Icon          string                 `json:"icon,omitempty"`
	Documentation string                 `json:"documentation,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// EnvironmentConfig represents environment configuration
type EnvironmentConfig struct {
	Name       string                 `json:"name"`
	Type       string                 `json:"type"`
	Provider   string                 `json:"provider"`
	Config     map[string]interface{} `json:"config"`
	Resources  ResourceConfig         `json:"resources"`
	Networking NetworkingConfig       `json:"networking"`
	Variables  map[string]string      `json:"variables"`
	Secrets    map[string]string      `json:"secrets"`
	Tags       []string               `json:"tags"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// ResourceConfig defines resource requirements
type ResourceConfig struct {
	CPU          string            `json:"cpu"`
	Memory       string            `json:"memory"`
	Storage      string            `json:"storage"`
	Replicas     int               `json:"replicas"`
	MinReplicas  int               `json:"minReplicas"`
	MaxReplicas  int               `json:"maxReplicas"`
	InstanceType string            `json:"instanceType,omitempty"`
	DiskSize     string            `json:"diskSize,omitempty"`
	GPUCount     int               `json:"gpuCount,omitempty"`
	GPUType      string            `json:"gpuType,omitempty"`
	NodeSelector map[string]string `json:"nodeSelector,omitempty"`
	Tolerations  []Toleration      `json:"tolerations,omitempty"`
	Affinity     *Affinity         `json:"affinity,omitempty"`
}

// NetworkingConfig defines networking configuration
type NetworkingConfig struct {
	Ingress         []IngressRule   `json:"ingress,omitempty"`
	LoadBalancer    bool            `json:"loadBalancer"`
	SSL             bool            `json:"ssl"`
	AllowedPorts    []int           `json:"allowedPorts,omitempty"`
	AllowedSources  []string        `json:"allowedSources,omitempty"`
	CustomDomains   []string        `json:"customDomains,omitempty"`
	ServiceMesh     bool            `json:"serviceMesh"`
	NetworkPolicies []NetworkPolicy `json:"networkPolicies,omitempty"`
	DNSConfig       *DNSConfig      `json:"dnsConfig,omitempty"`
}

// Supporting types
type Toleration struct {
	Key      string `json:"key"`
	Operator string `json:"operator"`
	Value    string `json:"value"`
	Effect   string `json:"effect"`
}

type Affinity struct {
	NodeAffinity    *NodeAffinity    `json:"nodeAffinity,omitempty"`
	PodAffinity     *PodAffinity     `json:"podAffinity,omitempty"`
	PodAntiAffinity *PodAntiAffinity `json:"podAntiAffinity,omitempty"`
}

type NodeAffinity struct {
	RequiredDuringSchedulingIgnoredDuringExecution  *NodeSelector   `json:"requiredDuringSchedulingIgnoredDuringExecution,omitempty"`
	PreferredDuringSchedulingIgnoredDuringExecution []PreferredNode `json:"preferredDuringSchedulingIgnoredDuringExecution,omitempty"`
}

type PodAffinity struct {
	RequiredDuringSchedulingIgnoredDuringExecution  []PodAffinityTerm         `json:"requiredDuringSchedulingIgnoredDuringExecution,omitempty"`
	PreferredDuringSchedulingIgnoredDuringExecution []WeightedPodAffinityTerm `json:"preferredDuringSchedulingIgnoredDuringExecution,omitempty"`
}

type PodAntiAffinity struct {
	RequiredDuringSchedulingIgnoredDuringExecution  []PodAffinityTerm         `json:"requiredDuringSchedulingIgnoredDuringExecution,omitempty"`
	PreferredDuringSchedulingIgnoredDuringExecution []WeightedPodAffinityTerm `json:"preferredDuringSchedulingIgnoredDuringExecution,omitempty"`
}

type NodeSelector struct {
	NodeSelectorTerms []NodeSelectorTerm `json:"nodeSelectorTerms"`
}

type NodeSelectorTerm struct {
	MatchExpressions []NodeSelectorRequirement `json:"matchExpressions,omitempty"`
	MatchFields      []NodeSelectorRequirement `json:"matchFields,omitempty"`
}

type NodeSelectorRequirement struct {
	Key      string   `json:"key"`
	Operator string   `json:"operator"`
	Values   []string `json:"values,omitempty"`
}

type PreferredNode struct {
	Weight     int32        `json:"weight"`
	Preference NodeSelector `json:"preference"`
}

type PodAffinityTerm struct {
	LabelSelector *LabelSelector `json:"labelSelector,omitempty"`
	Namespaces    []string       `json:"namespaces,omitempty"`
	TopologyKey   string         `json:"topologyKey"`
}

type WeightedPodAffinityTerm struct {
	Weight          int32           `json:"weight"`
	PodAffinityTerm PodAffinityTerm `json:"podAffinityTerm"`
}

type LabelSelector struct {
	MatchLabels      map[string]string          `json:"matchLabels,omitempty"`
	MatchExpressions []LabelSelectorRequirement `json:"matchExpressions,omitempty"`
}

type LabelSelectorRequirement struct {
	Key      string   `json:"key"`
	Operator string   `json:"operator"`
	Values   []string `json:"values,omitempty"`
}

type IngressRule struct {
	Host     string            `json:"host"`
	Path     string            `json:"path"`
	Port     int               `json:"port"`
	Protocol string            `json:"protocol"`
	TLS      bool              `json:"tls"`
	Headers  map[string]string `json:"headers,omitempty"`
}

type NetworkPolicy struct {
	Name     string                 `json:"name"`
	Selector map[string]string      `json:"selector"`
	Ingress  []NetworkPolicyIngress `json:"ingress,omitempty"`
	Egress   []NetworkPolicyEgress  `json:"egress,omitempty"`
}

type NetworkPolicyIngress struct {
	From  []NetworkPolicyPeer `json:"from,omitempty"`
	Ports []NetworkPolicyPort `json:"ports,omitempty"`
}

type NetworkPolicyEgress struct {
	To    []NetworkPolicyPeer `json:"to,omitempty"`
	Ports []NetworkPolicyPort `json:"ports,omitempty"`
}

type NetworkPolicyPeer struct {
	PodSelector       *LabelSelector `json:"podSelector,omitempty"`
	NamespaceSelector *LabelSelector `json:"namespaceSelector,omitempty"`
	IPBlock           *IPBlock       `json:"ipBlock,omitempty"`
}

type NetworkPolicyPort struct {
	Protocol string `json:"protocol,omitempty"`
	Port     string `json:"port,omitempty"`
}

type IPBlock struct {
	CIDR   string   `json:"cidr"`
	Except []string `json:"except,omitempty"`
}

type DNSConfig struct {
	Nameservers []string          `json:"nameservers,omitempty"`
	Searches    []string          `json:"searches,omitempty"`
	Options     []DNSConfigOption `json:"options,omitempty"`
}

type DNSConfigOption struct {
	Name  string `json:"name"`
	Value string `json:"value,omitempty"`
}

// EnvironmentResult represents the result of environment operations
type EnvironmentResult struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Status    string                 `json:"status"`
	Message   string                 `json:"message"`
	Resources []Resource             `json:"resources,omitempty"`
	Endpoints []Endpoint             `json:"endpoints,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt string                 `json:"createdAt"`
	UpdatedAt string                 `json:"updatedAt"`
}

// Resource represents a deployed resource
type Resource struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Status      string                 `json:"status"`
	Namespace   string                 `json:"namespace,omitempty"`
	Labels      map[string]string      `json:"labels,omitempty"`
	Annotations map[string]string      `json:"annotations,omitempty"`
	Spec        map[string]interface{} `json:"spec,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// Endpoint represents a service endpoint
type Endpoint struct {
	Name     string            `json:"name"`
	URL      string            `json:"url"`
	Type     string            `json:"type"`
	Protocol string            `json:"protocol"`
	Port     int               `json:"port"`
	Health   string            `json:"health"`
	Headers  map[string]string `json:"headers,omitempty"`
}

// EnvironmentStatus represents environment status
type EnvironmentStatus struct {
	Status    string                 `json:"status"`
	Health    string                 `json:"health"`
	Message   string                 `json:"message"`
	LastCheck string                 `json:"lastCheck"`
	Resources []ResourceStatus       `json:"resources,omitempty"`
	Endpoints []EndpointStatus       `json:"endpoints,omitempty"`
	Metrics   map[string]interface{} `json:"metrics,omitempty"`
	Issues    []Issue                `json:"issues,omitempty"`
}

// ResourceStatus represents resource status
type ResourceStatus struct {
	Name      string                 `json:"name"`
	Type      string                 `json:"type"`
	Status    string                 `json:"status"`
	Ready     bool                   `json:"ready"`
	Replicas  int                    `json:"replicas,omitempty"`
	Available int                    `json:"available,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// EndpointStatus represents endpoint status
type EndpointStatus struct {
	Name         string `json:"name"`
	URL          string `json:"url"`
	Status       string `json:"status"`
	ResponseTime int    `json:"responseTime,omitempty"`
	StatusCode   int    `json:"statusCode,omitempty"`
	LastCheck    string `json:"lastCheck"`
}

// Issue represents an environment issue
type Issue struct {
	Type      string `json:"type"`
	Severity  string `json:"severity"`
	Message   string `json:"message"`
	Resource  string `json:"resource,omitempty"`
	Timestamp string `json:"timestamp"`
	Resolved  bool   `json:"resolved"`
}

// ScalingConfig represents scaling configuration
type ScalingConfig struct {
	Replicas    int              `json:"replicas,omitempty"`
	MinReplicas int              `json:"minReplicas,omitempty"`
	MaxReplicas int              `json:"maxReplicas,omitempty"`
	CPU         string           `json:"cpu,omitempty"`
	Memory      string           `json:"memory,omitempty"`
	Metrics     []ScalingMetric  `json:"metrics,omitempty"`
	Behavior    *ScalingBehavior `json:"behavior,omitempty"`
}

// ScalingMetric represents a scaling metric
type ScalingMetric struct {
	Type     string                 `json:"type"`
	Resource string                 `json:"resource,omitempty"`
	Target   map[string]interface{} `json:"target"`
}

// ScalingBehavior represents scaling behavior
type ScalingBehavior struct {
	ScaleUp   *ScalingRules `json:"scaleUp,omitempty"`
	ScaleDown *ScalingRules `json:"scaleDown,omitempty"`
}

// ScalingRules represents scaling rules
type ScalingRules struct {
	StabilizationWindowSeconds int             `json:"stabilizationWindowSeconds,omitempty"`
	SelectPolicy               string          `json:"selectPolicy,omitempty"`
	Policies                   []ScalingPolicy `json:"policies,omitempty"`
}

// ScalingPolicy represents a scaling policy
type ScalingPolicy struct {
	Type          string `json:"type"`
	Value         int    `json:"value"`
	PeriodSeconds int    `json:"periodSeconds"`
}

// HealthStatus represents health status
type HealthStatus struct {
	Status    string                 `json:"status"`
	Message   string                 `json:"message"`
	Checks    []HealthCheck          `json:"checks,omitempty"`
	Timestamp string                 `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// HealthCheck represents a health check
type HealthCheck struct {
	Name      string `json:"name"`
	Status    string `json:"status"`
	Message   string `json:"message,omitempty"`
	Duration  int    `json:"duration,omitempty"`
	Timestamp string `json:"timestamp"`
}

// MetricsData represents metrics data
type MetricsData struct {
	Timestamp string                 `json:"timestamp"`
	Metrics   map[string]interface{} `json:"metrics"`
	Resources []ResourceMetrics      `json:"resources,omitempty"`
}

// ResourceMetrics represents resource metrics
type ResourceMetrics struct {
	Name    string                 `json:"name"`
	Type    string                 `json:"type"`
	Metrics map[string]interface{} `json:"metrics"`
}

// ProviderRegistry manages environment providers
type ProviderRegistry struct {
	providers   map[string]EnvironmentProvider
	nameMapping map[string]string // Maps display names to technical names
	mutex       sync.RWMutex
}

// NewProviderRegistry creates a new provider registry
func NewProviderRegistry() *ProviderRegistry {
	return &ProviderRegistry{
		providers:   make(map[string]EnvironmentProvider),
		nameMapping: make(map[string]string),
	}
}

// RegisterProvider registers a new environment provider
func (r *ProviderRegistry) RegisterProvider(name string, provider EnvironmentProvider) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if _, exists := r.providers[name]; exists {
		return fmt.Errorf("provider %s already registered", name)
	}

	r.providers[name] = provider

	// Also register display name mapping
	metadata := provider.GetMetadata()
	if metadata.Name != "" && metadata.Name != name {
		r.nameMapping[metadata.Name] = name
	}

	return nil
}

// GetProvider retrieves a provider by name (supports both technical and display names)
func (r *ProviderRegistry) GetProvider(name string) (EnvironmentProvider, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	// First try direct lookup
	provider, exists := r.providers[name]
	if exists {
		return provider, nil
	}

	// Try display name mapping
	if technicalName, exists := r.nameMapping[name]; exists {
		provider, exists := r.providers[technicalName]
		if exists {
			return provider, nil
		}
	}

	// Try URL-decoded name
	decodedName, err := url.QueryUnescape(name)
	if err == nil && decodedName != name {
		return r.GetProvider(decodedName)
	}

	// Try fuzzy matching for common patterns
	normalizedName := r.normalizeProviderName(name)
	for techName, provider := range r.providers {
		metadata := provider.GetMetadata()

		// Check if normalized names match
		if r.normalizeProviderName(techName) == normalizedName ||
			r.normalizeProviderName(metadata.Name) == normalizedName ||
			r.normalizeProviderName(metadata.Description) == normalizedName {
			return provider, nil
		}
	}

	return nil, fmt.Errorf("provider %s not found", name)
}

// normalizeProviderName normalizes provider names for fuzzy matching
func (r *ProviderRegistry) normalizeProviderName(name string) string {
	// Convert to lowercase and remove special characters
	normalized := strings.ToLower(name)
	normalized = strings.ReplaceAll(normalized, " ", "")
	normalized = strings.ReplaceAll(normalized, "(", "")
	normalized = strings.ReplaceAll(normalized, ")", "")
	normalized = strings.ReplaceAll(normalized, "-", "")
	normalized = strings.ReplaceAll(normalized, "_", "")
	return normalized
}

// ListProviders returns all registered providers
func (r *ProviderRegistry) ListProviders() []ProviderMetadata {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var providers []ProviderMetadata
	for _, provider := range r.providers {
		providers = append(providers, provider.GetMetadata())
	}

	return providers
}

// UnregisterProvider removes a provider from the registry
func (r *ProviderRegistry) UnregisterProvider(name string) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	provider, exists := r.providers[name]
	if !exists {
		return fmt.Errorf("provider %s not found", name)
	}

	// Cleanup provider
	if err := provider.Cleanup(); err != nil {
		return fmt.Errorf("failed to cleanup provider %s: %w", name, err)
	}

	delete(r.providers, name)
	return nil
}

// Global registry instance
var globalRegistry = NewProviderRegistry()

// GetGlobalRegistry returns the global provider registry
func GetGlobalRegistry() *ProviderRegistry {
	return globalRegistry
}

// InitializeProviders registers all built-in providers
func InitializeProviders(externalProviders []config.ExternalProviderConfig) error {
	registry := GetGlobalRegistry()

	fmt.Printf("Initializing environment providers...\n")

	// Register Kubernetes provider
	if err := registry.RegisterProvider("kubernetes", NewKubernetesProvider()); err != nil {
		return fmt.Errorf("failed to register kubernetes provider: %w", err)
	}
	fmt.Printf("Registered kubernetes provider\n")

	// Register OpenShift provider
	if err := registry.RegisterProvider("openshift", NewOpenShiftProvider()); err != nil {
		return fmt.Errorf("failed to register openshift provider: %w", err)
	}
	fmt.Printf("Registered openshift provider\n")

	// Register Docker Swarm provider
	if err := registry.RegisterProvider("docker-swarm", NewDockerSwarmProvider()); err != nil {
		return fmt.Errorf("failed to register docker-swarm provider: %w", err)
	}
	fmt.Printf("Registered docker-swarm provider\n")

	// Register AWS ECS provider
	if err := registry.RegisterProvider("aws-ecs", NewAWSECSProvider()); err != nil {
		return fmt.Errorf("failed to register aws-ecs provider: %w", err)
	}
	fmt.Printf("Registered aws-ecs provider\n")

	// Note: GKE provider is now available as an external plugin
	// See plugins/gke-environment-provider/ for the standalone implementation

	// Register Azure Container Instances provider
	if err := registry.RegisterProvider("azure-aci", NewAzureACIProvider()); err != nil {
		return fmt.Errorf("failed to register azure-aci provider: %w", err)
	}
	fmt.Printf("Registered azure-aci provider\n")

	// Register Google Cloud Run provider
	if err := registry.RegisterProvider("cloud-run", NewCloudRunProvider()); err != nil {
		return fmt.Errorf("failed to register cloud-run provider: %w", err)
	}
	fmt.Printf("Registered cloud-run provider\n")

	// Register Apigee provider
	if err := registry.RegisterProvider("apigee", NewApigeeProvider()); err != nil {
		return fmt.Errorf("failed to register apigee provider: %w", err)
	}
	fmt.Printf("Registered apigee provider\n")

	// Register AWS EKS provider
	if err := registry.RegisterProvider("aws-eks", NewAWSEKSProvider()); err != nil {
		return fmt.Errorf("failed to register aws-eks provider: %w", err)
	}
	fmt.Printf("Registered aws-eks provider\n")

	// Register Azure AKS provider
	if err := registry.RegisterProvider("azure-aks", NewAzureAKSProvider()); err != nil {
		return fmt.Errorf("failed to register azure-aks provider: %w", err)
	}
	fmt.Printf("Registered azure-aks provider\n")

	// Discover and register external providers
	if err := DiscoverExternalProviders(externalProviders); err != nil {
		// Log warning but don't fail initialization
		fmt.Printf("Warning: Failed to discover external providers: %v\n", err)
	}

	// Log final count
	providers := registry.ListProviders()
	fmt.Printf("Provider initialization complete. Total providers registered: %d\n", len(providers))
	for _, provider := range providers {
		fmt.Printf("  - %s (%s): %s\n", provider.Name, provider.Type, provider.Description)
	}

	return nil
}
