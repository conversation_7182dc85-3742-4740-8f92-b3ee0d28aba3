package providers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"time"

	"github.com/claudio/deploy-orchestrator/environment-service/config"
)

// ExternalProvider implements the EnvironmentProvider interface for external plugins
type ExternalProvider struct {
	name     string
	baseURL  string
	client   *http.Client
	metadata ProviderMetadata
	schema   map[string]interface{}
}

// NewExternalProvider creates a new external provider instance
func NewExternalProvider(name, baseURL string) *ExternalProvider {
	return &ExternalProvider{
		name:    name,
		baseURL: baseURL,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// Initialize initializes the external provider
func (p *ExternalProvider) Initialize(config map[string]interface{}) error {
	// Load metadata and schema from external provider
	if err := p.loadMetadata(); err != nil {
		return fmt.Errorf("failed to load metadata: %w", err)
	}

	if err := p.loadSchema(); err != nil {
		return fmt.Errorf("failed to load schema: %w", err)
	}

	// Initialize the external provider
	return p.callExternalAPI("POST", "/initialize", config, nil)
}

// GetMetadata returns provider metadata
func (p *ExternalProvider) GetMetadata() ProviderMetadata {
	if p.metadata.Name == "" {
		// Try to load metadata if not already loaded
		p.loadMetadata()
	}
	return p.metadata
}

// GetConfigSchema returns the configuration schema
func (p *ExternalProvider) GetConfigSchema() map[string]interface{} {
	if p.schema == nil {
		// Try to load schema if not already loaded
		p.loadSchema()
	}
	return p.schema
}

// Validate validates the provider configuration
func (p *ExternalProvider) Validate(config map[string]interface{}) error {
	var result map[string]interface{}
	err := p.callExternalAPI("POST", "/validate", config, &result)
	if err != nil {
		return err
	}

	if valid, ok := result["valid"].(bool); ok && !valid {
		if errors, ok := result["errors"].([]interface{}); ok {
			return fmt.Errorf("validation failed: %v", errors)
		}
		return fmt.Errorf("validation failed")
	}

	return nil
}

// CreateEnvironment creates a new environment
func (p *ExternalProvider) CreateEnvironment(ctx context.Context, config EnvironmentConfig) (*EnvironmentResult, error) {
	var result EnvironmentResult
	err := p.callExternalAPI("POST", "/environments", config, &result)
	return &result, err
}

// UpdateEnvironment updates an existing environment
func (p *ExternalProvider) UpdateEnvironment(ctx context.Context, id string, config EnvironmentConfig) (*EnvironmentResult, error) {
	var result EnvironmentResult
	err := p.callExternalAPI("PUT", fmt.Sprintf("/environments/%s", id), config, &result)
	return &result, err
}

// DeleteEnvironment deletes an environment
func (p *ExternalProvider) DeleteEnvironment(ctx context.Context, id string) error {
	return p.callExternalAPI("DELETE", fmt.Sprintf("/environments/%s", id), nil, nil)
}

// GetEnvironmentStatus returns environment status
func (p *ExternalProvider) GetEnvironmentStatus(ctx context.Context, id string) (*EnvironmentStatus, error) {
	var status EnvironmentStatus
	err := p.callExternalAPI("GET", fmt.Sprintf("/environments/%s/status", id), nil, &status)
	return &status, err
}

// GetResources returns environment resources
func (p *ExternalProvider) GetResources(ctx context.Context, environmentID string) ([]Resource, error) {
	var resources []Resource
	err := p.callExternalAPI("GET", fmt.Sprintf("/environments/%s/resources", environmentID), nil, &resources)
	return resources, err
}

// ScaleResources scales environment resources
func (p *ExternalProvider) ScaleResources(ctx context.Context, environmentID string, scaling ScalingConfig) error {
	return p.callExternalAPI("POST", fmt.Sprintf("/environments/%s/scale", environmentID), scaling, nil)
}

// HealthCheck performs health check on the environment
func (p *ExternalProvider) HealthCheck(ctx context.Context, environmentID string) (*HealthStatus, error) {
	var health HealthStatus
	err := p.callExternalAPI("GET", fmt.Sprintf("/environments/%s/health", environmentID), nil, &health)
	return &health, err
}

// GetMetrics returns environment metrics
func (p *ExternalProvider) GetMetrics(ctx context.Context, environmentID string) (*MetricsData, error) {
	var metrics MetricsData
	err := p.callExternalAPI("GET", fmt.Sprintf("/environments/%s/metrics", environmentID), nil, &metrics)
	return &metrics, err
}

// Cleanup cleans up provider resources
func (p *ExternalProvider) Cleanup() error {
	return p.callExternalAPI("POST", "/cleanup", nil, nil)
}

// loadMetadata loads provider metadata from external provider
func (p *ExternalProvider) loadMetadata() error {
	return p.callExternalAPI("GET", "/metadata", nil, &p.metadata)
}

// loadSchema loads provider schema from external provider
func (p *ExternalProvider) loadSchema() error {
	return p.callExternalAPI("GET", "/schema", nil, &p.schema)
}

// callExternalAPI makes HTTP calls to the external provider
func (p *ExternalProvider) callExternalAPI(method, endpoint string, payload interface{}, result interface{}) error {
	url := p.baseURL + endpoint

	var body io.Reader
	if payload != nil {
		jsonData, err := json.Marshal(payload)
		if err != nil {
			return fmt.Errorf("failed to marshal payload: %w", err)
		}
		body = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	if payload != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	resp, err := p.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to make request to %s: %w", url, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("external provider returned error %d: %s", resp.StatusCode, string(bodyBytes))
	}

	if result != nil && resp.StatusCode != http.StatusNoContent {
		if err := json.NewDecoder(resp.Body).Decode(result); err != nil {
			return fmt.Errorf("failed to decode response: %w", err)
		}
	}

	return nil
}

// StartExternalProvider attempts to start an external provider
func StartExternalProvider(name string, cfg config.ExternalProviderConfig) error {
	// Check if binary exists
	if _, err := os.Stat(cfg.BinaryPath); os.IsNotExist(err) {
		return fmt.Errorf("binary not found at %s", cfg.BinaryPath)
	}

	// Get absolute path
	absPath, err := filepath.Abs(cfg.BinaryPath)
	if err != nil {
		return fmt.Errorf("failed to get absolute path: %w", err)
	}

	// Start the external provider as a background process
	cmd := exec.Command(absPath)
	cmd.Dir = filepath.Dir(absPath)

	// Set environment variables if needed
	cmd.Env = append(os.Environ(),
		fmt.Sprintf("PORT=%s", cfg.Port),
		"LOG_LEVEL=info",
	)

	// Start the process
	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start external provider: %w", err)
	}

	fmt.Printf("Started external provider %s (PID: %d)\n", name, cmd.Process.Pid)
	return nil
}

// RegisterExternalProvider registers an external provider with the registry
func RegisterExternalProvider(name, baseURL string) error {
	provider := NewExternalProvider(name, baseURL)

	// Test connectivity and load metadata
	if err := provider.loadMetadata(); err != nil {
		return fmt.Errorf("failed to connect to external provider %s at %s: %w", name, baseURL, err)
	}

	// Register with global registry
	registry := GetGlobalRegistry()
	return registry.RegisterProvider(name, provider)
}

// DiscoverExternalProviders discovers and registers external providers from configuration
func DiscoverExternalProviders(externalProviders []config.ExternalProviderConfig) error {
	for _, config := range externalProviders {
		if !config.Enabled {
			fmt.Printf("Skipping disabled external provider: %s\n", config.Name)
			continue
		}
		// Check if the external provider is available
		if err := HealthCheckExternalProvider(config.URL); err != nil {
			fmt.Printf("Warning: External provider %s at %s is not available: %v\n", config.Name, config.URL, err)

			// Try to auto-start the external provider if enabled
			if config.AutoStart {
				if err := StartExternalProvider(config.Name, config); err != nil {
					fmt.Printf("Warning: Failed to auto-start external provider %s: %v\n", config.Name, err)
					// Create a mock provider for development/testing
					if err := RegisterMockExternalProvider(config.Name); err != nil {
						fmt.Printf("Warning: Failed to register mock provider %s: %v\n", config.Name, err)
					} else {
						fmt.Printf("Registered mock provider: %s\n", config.Name)
					}
					continue
				}

				// Wait a moment for the provider to start
				startupTimeout := time.Duration(config.StartupTimeout) * time.Second
				if startupTimeout == 0 {
					startupTimeout = 10 * time.Second // default timeout
				}
				time.Sleep(startupTimeout)

				// Check again if it's now available
				if err := HealthCheckExternalProvider(config.URL); err != nil {
					fmt.Printf("Warning: External provider %s failed to start properly: %v\n", config.Name, err)
					// Create a mock provider for development/testing
					if err := RegisterMockExternalProvider(config.Name); err != nil {
						fmt.Printf("Warning: Failed to register mock provider %s: %v\n", config.Name, err)
					} else {
						fmt.Printf("Registered mock provider: %s\n", config.Name)
					}
					continue
				}

				fmt.Printf("Successfully auto-started external provider: %s\n", config.Name)
			} else {
				// Auto-start is disabled, create mock provider
				if err := RegisterMockExternalProvider(config.Name); err != nil {
					fmt.Printf("Warning: Failed to register mock provider %s: %v\n", config.Name, err)
				} else {
					fmt.Printf("Registered mock provider: %s\n", config.Name)
				}
				continue
			}
		}

		// Try to register the external provider
		if err := RegisterExternalProvider(config.Name, config.URL); err != nil {
			// Log the error but don't fail the entire initialization
			fmt.Printf("Warning: Failed to register external provider %s: %v\n", config.Name, err)
			continue
		}
		fmt.Printf("Successfully registered external provider: %s\n", config.Name)
	}

	return nil
}

// ReloadExternalProviders reloads external providers from configuration without restart
func ReloadExternalProviders(externalProviders []config.ExternalProviderConfig) error {
	registry := GetGlobalRegistry()

	// Get current external providers (those with category "external" or specific patterns)
	currentProviders := registry.ListProviders()
	var externalProviderNames []string

	for _, provider := range currentProviders {
		// Identify external providers by category or naming pattern
		if provider.Category == "external" || provider.Category == "managed-kubernetes" {
			externalProviderNames = append(externalProviderNames, provider.Name)
		}
	}

	// Unregister existing external providers
	for _, name := range externalProviderNames {
		if err := registry.UnregisterProvider(name); err != nil {
			fmt.Printf("Warning: Failed to unregister external provider %s: %v\n", name, err)
		} else {
			fmt.Printf("Unregistered external provider: %s\n", name)
		}
	}

	// Re-discover and register external providers
	return DiscoverExternalProviders(externalProviders)
}

// HealthCheckExternalProvider checks if an external provider is healthy
func HealthCheckExternalProvider(baseURL string) error {
	client := &http.Client{Timeout: 5 * time.Second}

	resp, err := client.Get(baseURL + "/health")
	if err != nil {
		return fmt.Errorf("health check failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("health check returned status %d", resp.StatusCode)
	}

	return nil
}

// RegisterMockExternalProvider registers a mock external provider for development/testing
func RegisterMockExternalProvider(name string) error {
	// Create a mock provider that implements the interface
	mockProvider := &MockExternalProvider{name: name}

	// Register with global registry
	registry := GetGlobalRegistry()
	return registry.RegisterProvider(name, mockProvider)
}

// MockExternalProvider is a mock implementation for testing
type MockExternalProvider struct {
	name string
}

func (m *MockExternalProvider) GetMetadata() ProviderMetadata {
	return ProviderMetadata{
		Name:        m.name,
		Version:     "1.0.0-mock",
		Description: "Mock external provider for development/testing",
		Author:      "Deploy Orchestrator Team",
		Type:        "container-orchestration",
		Category:    "mock",
		Capabilities: []string{
			"deploy", "scale", "monitor", "logs", "health-check",
		},
		Tags:          []string{"mock", "development", "testing"},
		Icon:          "kubernetes",
		Documentation: "https://docs.example.com/mock-provider",
	}
}

func (m *MockExternalProvider) GetConfigSchema() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"project_id": map[string]interface{}{
				"type":        "string",
				"title":       "Project ID",
				"description": "Google Cloud project ID",
				"required":    true,
			},
			"region": map[string]interface{}{
				"type":        "string",
				"title":       "Region",
				"description": "Google Cloud region",
				"required":    true,
				"enum":        []string{"us-central1", "us-east1", "us-west1", "europe-west1"},
				"default":     "us-central1",
			},
			"cluster_name": map[string]interface{}{
				"type":        "string",
				"title":       "Cluster Name",
				"description": "GKE cluster name",
				"required":    true,
			},
			"service_account_key": map[string]interface{}{
				"type":        "string",
				"title":       "Service Account Key",
				"description": "Base64 encoded service account key JSON",
				"required":    true,
				"sensitive":   true,
			},
		},
		"required": []string{"project_id", "region", "cluster_name", "service_account_key"},
	}
}

func (m *MockExternalProvider) Initialize(config map[string]interface{}) error {
	return nil
}

func (m *MockExternalProvider) Validate(config map[string]interface{}) error {
	required := []string{"project_id", "region", "cluster_name", "service_account_key"}
	for _, field := range required {
		if _, ok := config[field]; !ok {
			return fmt.Errorf("required field '%s' is missing", field)
		}
	}
	return nil
}

func (m *MockExternalProvider) CreateEnvironment(ctx context.Context, config EnvironmentConfig) (*EnvironmentResult, error) {
	return &EnvironmentResult{
		ID:      config.Name + "-mock",
		Name:    config.Name,
		Status:  "created",
		Message: "Mock environment created successfully",
		Resources: []Resource{
			{
				ID:        "cluster-" + config.Name,
				Name:      config.Name,
				Type:      "GKECluster",
				Status:    "running",
				Namespace: "default",
			},
		},
		Endpoints: []Endpoint{
			{
				Name:     "kubernetes-api",
				URL:      "https://mock-cluster.googleapis.com",
				Type:     "api",
				Protocol: "https",
				Port:     443,
				Health:   "healthy",
			},
		},
		CreatedAt: time.Now().Format(time.RFC3339),
		UpdatedAt: time.Now().Format(time.RFC3339),
	}, nil
}

func (m *MockExternalProvider) UpdateEnvironment(ctx context.Context, id string, config EnvironmentConfig) (*EnvironmentResult, error) {
	return &EnvironmentResult{
		ID:        id,
		Name:      config.Name,
		Status:    "updated",
		Message:   "Mock environment updated successfully",
		UpdatedAt: time.Now().Format(time.RFC3339),
	}, nil
}

func (m *MockExternalProvider) DeleteEnvironment(ctx context.Context, id string) error {
	return nil
}

func (m *MockExternalProvider) GetEnvironmentStatus(ctx context.Context, id string) (*EnvironmentStatus, error) {
	return &EnvironmentStatus{
		Status:    "running",
		Health:    "healthy",
		Message:   "Mock environment is running normally",
		LastCheck: time.Now().Format(time.RFC3339),
	}, nil
}

func (m *MockExternalProvider) GetResources(ctx context.Context, environmentID string) ([]Resource, error) {
	return []Resource{}, nil
}

func (m *MockExternalProvider) ScaleResources(ctx context.Context, environmentID string, scaling ScalingConfig) error {
	return nil
}

func (m *MockExternalProvider) HealthCheck(ctx context.Context, environmentID string) (*HealthStatus, error) {
	return &HealthStatus{
		Status:    "healthy",
		Message:   "Mock environment is healthy",
		Timestamp: time.Now().Format(time.RFC3339),
	}, nil
}

func (m *MockExternalProvider) GetMetrics(ctx context.Context, environmentID string) (*MetricsData, error) {
	return &MetricsData{
		Timestamp: time.Now().Format(time.RFC3339),
		Metrics:   map[string]interface{}{},
	}, nil
}

func (m *MockExternalProvider) Cleanup() error {
	return nil
}
