package validation

import (
	"fmt"
	"net"
	"net/url"
	"regexp"
	"strconv"
)

// ValidationRule represents a custom validation rule
type ValidationRule struct {
	Field       string                 `json:"field"`
	Type        string                 `json:"type"`
	Required    bool                   `json:"required"`
	Pattern     string                 `json:"pattern,omitempty"`
	MinLength   int                    `json:"minLength,omitempty"`
	MaxLength   int                    `json:"maxLength,omitempty"`
	Minimum     float64                `json:"minimum,omitempty"`
	Maximum     float64                `json:"maximum,omitempty"`
	Enum        []interface{}          `json:"enum,omitempty"`
	Custom      string                 `json:"custom,omitempty"`
	Message     string                 `json:"message,omitempty"`
	Conditional *ConditionalValidation `json:"conditional,omitempty"`
}

// ConditionalValidation represents conditional validation rules
type ConditionalValidation struct {
	If   map[string]interface{} `json:"if"`
	Then []ValidationRule       `json:"then"`
	Else []ValidationRule       `json:"else,omitempty"`
}

// ValidationResult represents the result of validation
type ValidationResult struct {
	Valid  bool              `json:"valid"`
	Errors []ValidationError `json:"errors,omitempty"`
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string      `json:"field"`
	Message string      `json:"message"`
	Code    string      `json:"code"`
	Value   interface{} `json:"value,omitempty"`
}

// ProviderValidator validates provider configurations
type ProviderValidator struct {
	rules map[string][]ValidationRule
}

// NewProviderValidator creates a new provider validator
func NewProviderValidator() *ProviderValidator {
	return &ProviderValidator{
		rules: make(map[string][]ValidationRule),
	}
}

// AddRule adds a validation rule for a provider
func (v *ProviderValidator) AddRule(provider string, rule ValidationRule) {
	if v.rules[provider] == nil {
		v.rules[provider] = make([]ValidationRule, 0)
	}
	v.rules[provider] = append(v.rules[provider], rule)
}

// AddRules adds multiple validation rules for a provider
func (v *ProviderValidator) AddRules(provider string, rules []ValidationRule) {
	for _, rule := range rules {
		v.AddRule(provider, rule)
	}
}

// Validate validates a provider configuration
func (v *ProviderValidator) Validate(provider string, config map[string]interface{}) *ValidationResult {
	result := &ValidationResult{Valid: true, Errors: make([]ValidationError, 0)}

	rules, exists := v.rules[provider]
	if !exists {
		return result // No rules defined, consider valid
	}

	for _, rule := range rules {
		if err := v.validateRule(rule, config); err != nil {
			result.Valid = false
			result.Errors = append(result.Errors, *err)
		}
	}

	return result
}

// validateRule validates a single rule
func (v *ProviderValidator) validateRule(rule ValidationRule, config map[string]interface{}) *ValidationError {
	value, exists := config[rule.Field]

	// Check required fields
	if rule.Required && !exists {
		return &ValidationError{
			Field:   rule.Field,
			Message: v.getErrorMessage(rule, "Field is required"),
			Code:    "REQUIRED",
		}
	}

	if !exists {
		return nil // Field is optional and not provided
	}

	// Check conditional validation
	if rule.Conditional != nil {
		if v.evaluateCondition(rule.Conditional.If, config) {
			for _, condRule := range rule.Conditional.Then {
				if err := v.validateRule(condRule, config); err != nil {
					return err
				}
			}
		} else if rule.Conditional.Else != nil {
			for _, condRule := range rule.Conditional.Else {
				if err := v.validateRule(condRule, config); err != nil {
					return err
				}
			}
		}
	}

	// Type validation
	if err := v.validateType(rule, value); err != nil {
		return err
	}

	// Pattern validation
	if rule.Pattern != "" {
		if err := v.validatePattern(rule, value); err != nil {
			return err
		}
	}

	// Length validation
	if err := v.validateLength(rule, value); err != nil {
		return err
	}

	// Range validation
	if err := v.validateRange(rule, value); err != nil {
		return err
	}

	// Enum validation
	if len(rule.Enum) > 0 {
		if err := v.validateEnum(rule, value); err != nil {
			return err
		}
	}

	// Custom validation
	if rule.Custom != "" {
		if err := v.validateCustom(rule, value); err != nil {
			return err
		}
	}

	return nil
}

// validateType validates the type of a value
func (v *ProviderValidator) validateType(rule ValidationRule, value interface{}) *ValidationError {
	switch rule.Type {
	case "string":
		if _, ok := value.(string); !ok {
			return &ValidationError{
				Field:   rule.Field,
				Message: v.getErrorMessage(rule, "Must be a string"),
				Code:    "TYPE_MISMATCH",
				Value:   value,
			}
		}
	case "integer":
		switch value.(type) {
		case int, int32, int64, float64:
			// Valid integer types
		default:
			return &ValidationError{
				Field:   rule.Field,
				Message: v.getErrorMessage(rule, "Must be an integer"),
				Code:    "TYPE_MISMATCH",
				Value:   value,
			}
		}
	case "number":
		switch value.(type) {
		case int, int32, int64, float32, float64:
			// Valid number types
		default:
			return &ValidationError{
				Field:   rule.Field,
				Message: v.getErrorMessage(rule, "Must be a number"),
				Code:    "TYPE_MISMATCH",
				Value:   value,
			}
		}
	case "boolean":
		if _, ok := value.(bool); !ok {
			return &ValidationError{
				Field:   rule.Field,
				Message: v.getErrorMessage(rule, "Must be a boolean"),
				Code:    "TYPE_MISMATCH",
				Value:   value,
			}
		}
	case "array":
		if _, ok := value.([]interface{}); !ok {
			return &ValidationError{
				Field:   rule.Field,
				Message: v.getErrorMessage(rule, "Must be an array"),
				Code:    "TYPE_MISMATCH",
				Value:   value,
			}
		}
	case "object":
		if _, ok := value.(map[string]interface{}); !ok {
			return &ValidationError{
				Field:   rule.Field,
				Message: v.getErrorMessage(rule, "Must be an object"),
				Code:    "TYPE_MISMATCH",
				Value:   value,
			}
		}
	}
	return nil
}

// validatePattern validates a pattern
func (v *ProviderValidator) validatePattern(rule ValidationRule, value interface{}) *ValidationError {
	str, ok := value.(string)
	if !ok {
		return nil // Pattern validation only applies to strings
	}

	matched, err := regexp.MatchString(rule.Pattern, str)
	if err != nil || !matched {
		return &ValidationError{
			Field:   rule.Field,
			Message: v.getErrorMessage(rule, fmt.Sprintf("Must match pattern: %s", rule.Pattern)),
			Code:    "PATTERN_MISMATCH",
			Value:   value,
		}
	}
	return nil
}

// validateLength validates string length or array length
func (v *ProviderValidator) validateLength(rule ValidationRule, value interface{}) *ValidationError {
	var length int

	switch v := value.(type) {
	case string:
		length = len(v)
	case []interface{}:
		length = len(v)
	default:
		return nil // Length validation only applies to strings and arrays
	}

	if rule.MinLength > 0 && length < rule.MinLength {
		return &ValidationError{
			Field:   rule.Field,
			Message: v.getErrorMessage(rule, fmt.Sprintf("Must be at least %d characters/items", rule.MinLength)),
			Code:    "MIN_LENGTH",
			Value:   value,
		}
	}

	if rule.MaxLength > 0 && length > rule.MaxLength {
		return &ValidationError{
			Field:   rule.Field,
			Message: v.getErrorMessage(rule, fmt.Sprintf("Must be at most %d characters/items", rule.MaxLength)),
			Code:    "MAX_LENGTH",
			Value:   value,
		}
	}

	return nil
}

// validateRange validates numeric ranges
func (v *ProviderValidator) validateRange(rule ValidationRule, value interface{}) *ValidationError {
	var num float64
	var ok bool

	switch v := value.(type) {
	case int:
		num = float64(v)
		ok = true
	case int32:
		num = float64(v)
		ok = true
	case int64:
		num = float64(v)
		ok = true
	case float32:
		num = float64(v)
		ok = true
	case float64:
		num = v
		ok = true
	}

	if !ok {
		return nil // Range validation only applies to numbers
	}

	if rule.Minimum != 0 && num < rule.Minimum {
		return &ValidationError{
			Field:   rule.Field,
			Message: v.getErrorMessage(rule, fmt.Sprintf("Must be at least %g", rule.Minimum)),
			Code:    "MIN_VALUE",
			Value:   value,
		}
	}

	if rule.Maximum != 0 && num > rule.Maximum {
		return &ValidationError{
			Field:   rule.Field,
			Message: v.getErrorMessage(rule, fmt.Sprintf("Must be at most %g", rule.Maximum)),
			Code:    "MAX_VALUE",
			Value:   value,
		}
	}

	return nil
}

// validateEnum validates enum values
func (v *ProviderValidator) validateEnum(rule ValidationRule, value interface{}) *ValidationError {
	for _, enumValue := range rule.Enum {
		if value == enumValue {
			return nil
		}
	}

	return &ValidationError{
		Field:   rule.Field,
		Message: v.getErrorMessage(rule, "Must be one of the allowed values"),
		Code:    "INVALID_ENUM",
		Value:   value,
	}
}

// validateCustom validates custom rules
func (v *ProviderValidator) validateCustom(rule ValidationRule, value interface{}) *ValidationError {
	switch rule.Custom {
	case "email":
		return v.validateEmail(rule, value)
	case "url":
		return v.validateURL(rule, value)
	case "ip":
		return v.validateIP(rule, value)
	case "cidr":
		return v.validateCIDR(rule, value)
	case "port":
		return v.validatePort(rule, value)
	case "aws_region":
		return v.validateAWSRegion(rule, value)
	case "azure_location":
		return v.validateAzureLocation(rule, value)
	case "gcp_region":
		return v.validateGCPRegion(rule, value)
	case "kubernetes_name":
		return v.validateKubernetesName(rule, value)
	case "docker_image":
		return v.validateDockerImage(rule, value)
	default:
		return &ValidationError{
			Field:   rule.Field,
			Message: v.getErrorMessage(rule, fmt.Sprintf("Unknown custom validation: %s", rule.Custom)),
			Code:    "UNKNOWN_CUSTOM",
			Value:   value,
		}
	}
}

// Custom validation methods

// validateEmail validates email format
func (v *ProviderValidator) validateEmail(rule ValidationRule, value interface{}) *ValidationError {
	str, ok := value.(string)
	if !ok {
		return nil
	}

	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(str) {
		return &ValidationError{
			Field:   rule.Field,
			Message: v.getErrorMessage(rule, "Must be a valid email address"),
			Code:    "INVALID_EMAIL",
			Value:   value,
		}
	}
	return nil
}

// validateURL validates URL format
func (v *ProviderValidator) validateURL(rule ValidationRule, value interface{}) *ValidationError {
	str, ok := value.(string)
	if !ok {
		return nil
	}

	_, err := url.Parse(str)
	if err != nil {
		return &ValidationError{
			Field:   rule.Field,
			Message: v.getErrorMessage(rule, "Must be a valid URL"),
			Code:    "INVALID_URL",
			Value:   value,
		}
	}
	return nil
}

// validateIP validates IP address format
func (v *ProviderValidator) validateIP(rule ValidationRule, value interface{}) *ValidationError {
	str, ok := value.(string)
	if !ok {
		return nil
	}

	if net.ParseIP(str) == nil {
		return &ValidationError{
			Field:   rule.Field,
			Message: v.getErrorMessage(rule, "Must be a valid IP address"),
			Code:    "INVALID_IP",
			Value:   value,
		}
	}
	return nil
}

// validateCIDR validates CIDR notation
func (v *ProviderValidator) validateCIDR(rule ValidationRule, value interface{}) *ValidationError {
	str, ok := value.(string)
	if !ok {
		return nil
	}

	_, _, err := net.ParseCIDR(str)
	if err != nil {
		return &ValidationError{
			Field:   rule.Field,
			Message: v.getErrorMessage(rule, "Must be a valid CIDR notation"),
			Code:    "INVALID_CIDR",
			Value:   value,
		}
	}
	return nil
}

// validatePort validates port number
func (v *ProviderValidator) validatePort(rule ValidationRule, value interface{}) *ValidationError {
	var port int

	switch val := value.(type) {
	case int:
		port = val
	case float64:
		port = int(val)
	case string:
		var err error
		port, err = strconv.Atoi(val)
		if err != nil {
			return &ValidationError{
				Field:   rule.Field,
				Message: v.getErrorMessage(rule, "Port must be a number"),
				Code:    "INVALID_PORT",
				Value:   value,
			}
		}
	default:
		return nil
	}

	if port < 1 || port > 65535 {
		return &ValidationError{
			Field:   rule.Field,
			Message: v.getErrorMessage(rule, "Port must be between 1 and 65535"),
			Code:    "INVALID_PORT_RANGE",
			Value:   value,
		}
	}
	return nil
}

// validateAWSRegion validates AWS region
func (v *ProviderValidator) validateAWSRegion(rule ValidationRule, value interface{}) *ValidationError {
	str, ok := value.(string)
	if !ok {
		return nil
	}

	validRegions := []string{
		"us-east-1", "us-east-2", "us-west-1", "us-west-2",
		"eu-west-1", "eu-west-2", "eu-west-3", "eu-central-1",
		"ap-southeast-1", "ap-southeast-2", "ap-northeast-1",
		"ap-northeast-2", "ap-south-1", "ca-central-1",
		"sa-east-1", "af-south-1", "me-south-1",
	}

	for _, region := range validRegions {
		if str == region {
			return nil
		}
	}

	return &ValidationError{
		Field:   rule.Field,
		Message: v.getErrorMessage(rule, "Must be a valid AWS region"),
		Code:    "INVALID_AWS_REGION",
		Value:   value,
	}
}

// validateAzureLocation validates Azure location
func (v *ProviderValidator) validateAzureLocation(rule ValidationRule, value interface{}) *ValidationError {
	str, ok := value.(string)
	if !ok {
		return nil
	}

	validLocations := []string{
		"eastus", "eastus2", "westus", "westus2", "westus3",
		"centralus", "northcentralus", "southcentralus",
		"westeurope", "northeurope", "uksouth", "ukwest",
		"francecentral", "germanywestcentral", "norwayeast",
		"switzerlandnorth", "swedencentral",
		"eastasia", "southeastasia", "japaneast", "japanwest",
		"koreacentral", "australiaeast", "australiasoutheast",
		"brazilsouth", "canadacentral", "canadaeast",
		"southafricanorth", "uaenorth",
	}

	for _, location := range validLocations {
		if str == location {
			return nil
		}
	}

	return &ValidationError{
		Field:   rule.Field,
		Message: v.getErrorMessage(rule, "Must be a valid Azure location"),
		Code:    "INVALID_AZURE_LOCATION",
		Value:   value,
	}
}

// validateGCPRegion validates Google Cloud region
func (v *ProviderValidator) validateGCPRegion(rule ValidationRule, value interface{}) *ValidationError {
	str, ok := value.(string)
	if !ok {
		return nil
	}

	validRegions := []string{
		"us-central1", "us-east1", "us-east4", "us-west1", "us-west2", "us-west3", "us-west4",
		"europe-north1", "europe-west1", "europe-west2", "europe-west3", "europe-west4", "europe-west6",
		"asia-east1", "asia-east2", "asia-northeast1", "asia-northeast2", "asia-northeast3",
		"asia-south1", "asia-southeast1", "asia-southeast2", "australia-southeast1",
	}

	for _, region := range validRegions {
		if str == region {
			return nil
		}
	}

	return &ValidationError{
		Field:   rule.Field,
		Message: v.getErrorMessage(rule, "Must be a valid Google Cloud region"),
		Code:    "INVALID_GCP_REGION",
		Value:   value,
	}
}

// validateKubernetesName validates Kubernetes resource name
func (v *ProviderValidator) validateKubernetesName(rule ValidationRule, value interface{}) *ValidationError {
	str, ok := value.(string)
	if !ok {
		return nil
	}

	// Kubernetes names must be lowercase alphanumeric with hyphens
	k8sNameRegex := regexp.MustCompile(`^[a-z0-9]([-a-z0-9]*[a-z0-9])?$`)
	if !k8sNameRegex.MatchString(str) {
		return &ValidationError{
			Field:   rule.Field,
			Message: v.getErrorMessage(rule, "Must be a valid Kubernetes name (lowercase alphanumeric with hyphens)"),
			Code:    "INVALID_K8S_NAME",
			Value:   value,
		}
	}

	if len(str) > 63 {
		return &ValidationError{
			Field:   rule.Field,
			Message: v.getErrorMessage(rule, "Kubernetes names must be 63 characters or less"),
			Code:    "K8S_NAME_TOO_LONG",
			Value:   value,
		}
	}

	return nil
}

// validateDockerImage validates Docker image format
func (v *ProviderValidator) validateDockerImage(rule ValidationRule, value interface{}) *ValidationError {
	str, ok := value.(string)
	if !ok {
		return nil
	}

	// Basic Docker image format validation
	dockerImageRegex := regexp.MustCompile(`^([a-zA-Z0-9._-]+/)?[a-zA-Z0-9._-]+(/[a-zA-Z0-9._-]+)*(:[\w.-]+)?$`)
	if !dockerImageRegex.MatchString(str) {
		return &ValidationError{
			Field:   rule.Field,
			Message: v.getErrorMessage(rule, "Must be a valid Docker image name"),
			Code:    "INVALID_DOCKER_IMAGE",
			Value:   value,
		}
	}

	return nil
}

// Helper methods

// evaluateCondition evaluates a conditional expression
func (v *ProviderValidator) evaluateCondition(condition map[string]interface{}, config map[string]interface{}) bool {
	for field, expectedValue := range condition {
		actualValue, exists := config[field]
		if !exists || actualValue != expectedValue {
			return false
		}
	}
	return true
}

// getErrorMessage returns the error message, using custom message if provided
func (v *ProviderValidator) getErrorMessage(rule ValidationRule, defaultMessage string) string {
	if rule.Message != "" {
		return rule.Message
	}
	return defaultMessage
}

// GetDefaultRules returns default validation rules for common provider types
func GetDefaultRules() map[string][]ValidationRule {
	return map[string][]ValidationRule{
		"aws-eks": {
			{Field: "region", Type: "string", Required: true, Custom: "aws_region"},
			{Field: "cluster_name", Type: "string", Required: true, Custom: "kubernetes_name"},
			{Field: "access_key_id", Type: "string", Required: true, MinLength: 16, MaxLength: 32},
			{Field: "secret_access_key", Type: "string", Required: true, MinLength: 40},
			{Field: "namespace", Type: "string", Required: false, Custom: "kubernetes_name"},
		},
		"azure-aks": {
			{Field: "location", Type: "string", Required: true, Custom: "azure_location"},
			{Field: "cluster_name", Type: "string", Required: true, Custom: "kubernetes_name"},
			{Field: "subscription_id", Type: "string", Required: true, Pattern: "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"},
			{Field: "client_id", Type: "string", Required: true, Pattern: "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"},
			{Field: "tenant_id", Type: "string", Required: true, Pattern: "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"},
		},
		"cloud-run": {
			{Field: "region", Type: "string", Required: true, Custom: "gcp_region"},
			{Field: "project_id", Type: "string", Required: true, Pattern: "^[a-z][a-z0-9-]{4,28}[a-z0-9]$"},
			{Field: "service_name", Type: "string", Required: true, Custom: "kubernetes_name"},
			{Field: "container_image", Type: "string", Required: true, Custom: "docker_image"},
			{Field: "container_port", Type: "integer", Required: false, Custom: "port"},
		},
		"kubernetes": {
			{Field: "cluster_endpoint", Type: "string", Required: true, Custom: "url"},
			{Field: "namespace", Type: "string", Required: false, Custom: "kubernetes_name"},
		},
	}
}
