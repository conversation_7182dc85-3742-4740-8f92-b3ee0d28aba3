package validation

import (
	"testing"
)

func TestProviderValidator_ValidateRequired(t *testing.T) {
	validator := NewProviderValidator()

	// Add a required field rule
	validator.AddRule("test-provider", ValidationRule{
		Field:    "required_field",
		Type:     "string",
		Required: true,
	})

	tests := []struct {
		name     string
		config   map[string]interface{}
		expected bool
	}{
		{
			name:     "Required field present",
			config:   map[string]interface{}{"required_field": "value"},
			expected: true,
		},
		{
			name:     "Required field missing",
			config:   map[string]interface{}{},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.Validate("test-provider", tt.config)
			if result.Valid != tt.expected {
				t.<PERSON>rf("Expected valid=%v, got valid=%v", tt.expected, result.Valid)
			}
		})
	}
}

func TestProviderValidator_ValidateType(t *testing.T) {
	tests := []struct {
		name     string
		rule     ValidationRule
		config   map[string]interface{}
		expected bool
	}{
		{
			name:     "Valid string",
			rule:     ValidationRule{Field: "field", Type: "string"},
			config:   map[string]interface{}{"field": "value"},
			expected: true,
		},
		{
			name:     "Invalid string type",
			rule:     ValidationRule{Field: "field", Type: "string"},
			config:   map[string]interface{}{"field": 123},
			expected: false,
		},
		{
			name:     "Valid integer",
			rule:     ValidationRule{Field: "field", Type: "integer"},
			config:   map[string]interface{}{"field": 123},
			expected: true,
		},
		{
			name:     "Invalid integer type",
			rule:     ValidationRule{Field: "field", Type: "integer"},
			config:   map[string]interface{}{"field": "not-a-number"},
			expected: false,
		},
		{
			name:     "Valid boolean",
			rule:     ValidationRule{Field: "field", Type: "boolean"},
			config:   map[string]interface{}{"field": true},
			expected: true,
		},
		{
			name:     "Invalid boolean type",
			rule:     ValidationRule{Field: "field", Type: "boolean"},
			config:   map[string]interface{}{"field": "not-a-boolean"},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			validator := NewProviderValidator()
			validator.AddRule("test-provider", tt.rule)
			result := validator.Validate("test-provider", tt.config)
			if result.Valid != tt.expected {
				t.Errorf("Expected valid=%v, got valid=%v. Errors: %v", tt.expected, result.Valid, result.Errors)
			}
		})
	}
}

func TestProviderValidator_ValidatePattern(t *testing.T) {
	validator := NewProviderValidator()

	validator.AddRule("test-provider", ValidationRule{
		Field:   "email",
		Type:    "string",
		Pattern: `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`,
	})

	tests := []struct {
		name     string
		config   map[string]interface{}
		expected bool
	}{
		{
			name:     "Valid email",
			config:   map[string]interface{}{"email": "<EMAIL>"},
			expected: true,
		},
		{
			name:     "Invalid email",
			config:   map[string]interface{}{"email": "invalid-email"},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.Validate("test-provider", tt.config)
			if result.Valid != tt.expected {
				t.Errorf("Expected valid=%v, got valid=%v. Errors: %v", tt.expected, result.Valid, result.Errors)
			}
		})
	}
}

func TestProviderValidator_ValidateLength(t *testing.T) {
	validator := NewProviderValidator()

	validator.AddRule("test-provider", ValidationRule{
		Field:     "name",
		Type:      "string",
		MinLength: 3,
		MaxLength: 10,
	})

	tests := []struct {
		name     string
		config   map[string]interface{}
		expected bool
	}{
		{
			name:     "Valid length",
			config:   map[string]interface{}{"name": "valid"},
			expected: true,
		},
		{
			name:     "Too short",
			config:   map[string]interface{}{"name": "ab"},
			expected: false,
		},
		{
			name:     "Too long",
			config:   map[string]interface{}{"name": "this-is-too-long"},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.Validate("test-provider", tt.config)
			if result.Valid != tt.expected {
				t.Errorf("Expected valid=%v, got valid=%v. Errors: %v", tt.expected, result.Valid, result.Errors)
			}
		})
	}
}

func TestProviderValidator_ValidateRange(t *testing.T) {
	validator := NewProviderValidator()

	validator.AddRule("test-provider", ValidationRule{
		Field:   "port",
		Type:    "integer",
		Minimum: 1,
		Maximum: 65535,
	})

	tests := []struct {
		name     string
		config   map[string]interface{}
		expected bool
	}{
		{
			name:     "Valid port",
			config:   map[string]interface{}{"port": 8080},
			expected: true,
		},
		{
			name:     "Port too low",
			config:   map[string]interface{}{"port": 0},
			expected: false,
		},
		{
			name:     "Port too high",
			config:   map[string]interface{}{"port": 70000},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.Validate("test-provider", tt.config)
			if result.Valid != tt.expected {
				t.Errorf("Expected valid=%v, got valid=%v. Errors: %v", tt.expected, result.Valid, result.Errors)
			}
		})
	}
}

func TestProviderValidator_ValidateEnum(t *testing.T) {
	validator := NewProviderValidator()

	validator.AddRule("test-provider", ValidationRule{
		Field: "region",
		Type:  "string",
		Enum:  []interface{}{"us-east-1", "us-west-2", "eu-west-1"},
	})

	tests := []struct {
		name     string
		config   map[string]interface{}
		expected bool
	}{
		{
			name:     "Valid enum value",
			config:   map[string]interface{}{"region": "us-east-1"},
			expected: true,
		},
		{
			name:     "Invalid enum value",
			config:   map[string]interface{}{"region": "invalid-region"},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.Validate("test-provider", tt.config)
			if result.Valid != tt.expected {
				t.Errorf("Expected valid=%v, got valid=%v. Errors: %v", tt.expected, result.Valid, result.Errors)
			}
		})
	}
}

func TestProviderValidator_ValidateCustom(t *testing.T) {
	tests := []struct {
		name     string
		rule     ValidationRule
		config   map[string]interface{}
		expected bool
	}{
		{
			name:     "Valid email",
			rule:     ValidationRule{Field: "email", Type: "string", Custom: "email"},
			config:   map[string]interface{}{"email": "<EMAIL>"},
			expected: true,
		},
		{
			name:     "Invalid email",
			rule:     ValidationRule{Field: "email", Type: "string", Custom: "email"},
			config:   map[string]interface{}{"email": "invalid-email"},
			expected: false,
		},
		{
			name:     "Valid URL",
			rule:     ValidationRule{Field: "url", Type: "string", Custom: "url"},
			config:   map[string]interface{}{"url": "https://example.com"},
			expected: true,
		},
		{
			name:     "Valid IP",
			rule:     ValidationRule{Field: "ip", Type: "string", Custom: "ip"},
			config:   map[string]interface{}{"ip": "***********"},
			expected: true,
		},
		{
			name:     "Invalid IP",
			rule:     ValidationRule{Field: "ip", Type: "string", Custom: "ip"},
			config:   map[string]interface{}{"ip": "invalid-ip"},
			expected: false,
		},
		{
			name:     "Valid CIDR",
			rule:     ValidationRule{Field: "cidr", Type: "string", Custom: "cidr"},
			config:   map[string]interface{}{"cidr": "***********/24"},
			expected: true,
		},
		{
			name:     "Invalid CIDR",
			rule:     ValidationRule{Field: "cidr", Type: "string", Custom: "cidr"},
			config:   map[string]interface{}{"cidr": "invalid-cidr"},
			expected: false,
		},
		{
			name:     "Valid port",
			rule:     ValidationRule{Field: "port", Type: "integer", Custom: "port"},
			config:   map[string]interface{}{"port": 8080},
			expected: true,
		},
		{
			name:     "Invalid port",
			rule:     ValidationRule{Field: "port", Type: "integer", Custom: "port"},
			config:   map[string]interface{}{"port": 70000},
			expected: false,
		},
		{
			name:     "Valid AWS region",
			rule:     ValidationRule{Field: "region", Type: "string", Custom: "aws_region"},
			config:   map[string]interface{}{"region": "us-east-1"},
			expected: true,
		},
		{
			name:     "Invalid AWS region",
			rule:     ValidationRule{Field: "region", Type: "string", Custom: "aws_region"},
			config:   map[string]interface{}{"region": "invalid-region"},
			expected: false,
		},
		{
			name:     "Valid Kubernetes name",
			rule:     ValidationRule{Field: "name", Type: "string", Custom: "kubernetes_name"},
			config:   map[string]interface{}{"name": "my-app"},
			expected: true,
		},
		{
			name:     "Invalid Kubernetes name",
			rule:     ValidationRule{Field: "name", Type: "string", Custom: "kubernetes_name"},
			config:   map[string]interface{}{"name": "My-App"},
			expected: false,
		},
		{
			name:     "Valid Docker image",
			rule:     ValidationRule{Field: "image", Type: "string", Custom: "docker_image"},
			config:   map[string]interface{}{"image": "nginx:latest"},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			validator := NewProviderValidator()
			validator.AddRule("test-provider", tt.rule)
			result := validator.Validate("test-provider", tt.config)
			if result.Valid != tt.expected {
				t.Errorf("Expected valid=%v, got valid=%v. Errors: %v", tt.expected, result.Valid, result.Errors)
			}
		})
	}
}
