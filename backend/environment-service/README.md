# Environment Service

The Environment Service is a core microservice in the Deploy Orchestrator platform that manages deployment environment configurations and provider integrations using the **shared module architecture**.

## Overview

This service replaces traditional deployment objects with a modern **environment-based approach** that provides:

- **Multi-cloud Environment Management** - Configure deployment targets across different cloud providers
- **Provider Registry Integration** - Extensible plugin system for deployment platforms
- **Real-time Health Monitoring** - Track environment status and performance
- **Configuration Validation** - Validate provider configurations before deployment
- **Version Tracking** - Monitor deployments across environments

## Shared Module Integration

The Environment Service uses the shared module for:

- ✅ **Authentication** - JWT-based auth with admin service integration
- ✅ **Authorization** - Permission-based access control
- ✅ **Configuration** - Unified config management with environment variables
- ✅ **Database** - GORM-based database operations with connection pooling
- ✅ **Gateway** - Service registration and discovery
- ✅ **Logging** - Structured logging with zap
- ✅ **Monitoring** - Metrics and health checks

## Architecture

The Environment Service integrates with the existing microservice ecosystem:

```
Environment Service (Port 8083)
├── Environment Management
├── Provider Registry
├── Health Monitoring
├── Configuration Validation
└── Version Tracking

Uses Shared Modules:
├── Auth (JWT + Permissions)
├── Config (YAML + Env Vars)
├── Database (GORM + Postgres)
├── Gateway (Registration)
├── Logging (Structured)
└── Monitoring (Metrics)

Integrates with:
├── Workflow Service (for deployments)
├── Secrets Service (for credentials)
├── Admin Service (for authentication)
└── Gateway Service (for routing)
```

## Key Features

### 🌍 **Environment Management**
- Create, update, delete environment configurations
- Support for multiple environment types (dev, staging, production)
- Project-scoped environment organization
- Environment status tracking

### 🔌 **Provider System**
- Extensible provider registry
- Support for 30+ deployment targets
- Dynamic configuration forms
- Provider capability discovery

### 📊 **Health Monitoring**
- Real-time environment health checks
- Performance metrics collection
- Issue detection and reporting
- Scheduled health monitoring

### ✅ **Configuration Validation**
- Provider-specific validation rules
- Real-time configuration testing
- Connection verification
- Error reporting and suggestions

## API Endpoints

### Environment Management
- `GET /api/v1/environment-service/environments` - List environments
- `POST /api/v1/environment-service/environments` - Create environment
- `GET /api/v1/environment-service/environments/:id` - Get environment
- `PUT /api/v1/environment-service/environments/:id` - Update environment
- `DELETE /api/v1/environment-service/environments/:id` - Delete environment

### Environment Operations
- `POST /api/v1/environment-service/environments/:id/test-connection` - Test connectivity
- `GET /api/v1/environment-service/environments/:id/status` - Get status
- `POST /api/v1/environment-service/environments/:id/deploy` - Deploy to environment

### Provider Management
- `GET /api/v1/environment-service/providers` - List available providers
- `GET /api/v1/environment-service/providers/:type/capabilities` - Get provider info
- `POST /api/v1/environment-service/providers/:type/validate-config` - Validate config

### Health Monitoring
- `GET /api/v1/environment-service/health/environments` - Get all health status
- `GET /api/v1/environment-service/health/environments/:id` - Get specific health
- `POST /api/v1/environment-service/health/environments/:id/check` - Trigger health check

## Supported Providers

| Category | Providers | Status |
|----------|-----------|--------|
| **Kubernetes** | GKE, AKS, EKS, OpenShift, K3s | ✅ Ready |
| **Cloud VMs** | GCE, EC2, Azure VM, DigitalOcean | 🔄 Interface Ready |
| **Serverless** | Lambda, Cloud Functions, Vercel | 🔄 Interface Ready |
| **Container** | Docker Swarm, Nomad, Mesos | 🔄 Interface Ready |

## Configuration

The service uses a YAML configuration file (`config/config.yaml`):

```yaml
server:
  port: "8083"

database:
  host: "localhost"
  port: 5432
  dbname: "deploy_orchestrator"

jwt:
  secret: "your-jwt-secret"

providers:
  gke:
    enabled: true
  aks:
    enabled: true
```

## Environment Variables

- `PORT` - Server port (default: 8083)
- `DATABASE_URL` - PostgreSQL connection string
- `JWT_SECRET` - JWT signing secret
- `LOG_LEVEL` - Logging level (debug, info, warn, error)

## Running the Service

### Development
```bash
cd backend/environment-service
go mod tidy
go run main.go
```

### Production
```bash
go build -o environment-service main.go
./environment-service
```

### Docker
```bash
docker build -t environment-service .
docker run -p 8083:8083 environment-service
```

## Database Schema

The service uses the following main tables:

- `environment_configs` - Environment definitions
- `environment_health` - Health monitoring data
- `provider_configs` - Provider-specific configurations

## Integration with Other Services

### Workflow Service
- Environments are referenced in workflow executions
- Provides deployment targets for workflows
- Receives deployment status updates

### Secrets Service
- Stores provider credentials securely
- References secrets in environment configurations
- Handles credential encryption/decryption

### Admin Service
- Uses JWT authentication
- Enforces project-based permissions
- Integrates with user management

### Gateway Service
- Routes requests to environment service
- Handles load balancing and service discovery
- Provides centralized API access

## Adding New Providers

Adding support for a new deployment platform is straightforward:

1. **Create Provider Implementation**:
```go
type MyCloudProvider struct{}

func (p *MyCloudProvider) GetInfo() providers.ProviderInfo {
    return providers.ProviderInfo{
        Type: "mycloud",
        Name: "My Cloud Platform",
        ConfigFields: []providers.ConfigField{
            {
                Name: "apiKey",
                Type: "password",
                Required: true,
            },
        },
    }
}
```

2. **Register Provider**:
```go
func init() {
    providers.RegisterFactory("mycloud", func() providers.Provider {
        return &MyCloudProvider{}
    })
}
```

3. **Import in main.go**:
```go
import _ "path/to/mycloud"
```

The provider will automatically appear in the UI with a generated configuration form!

## Testing

```bash
# Run unit tests
go test ./...

# Run integration tests
go test -tags=integration ./...

# Test API endpoints
curl http://localhost:8083/health
curl http://localhost:8083/api/v1/environment-service/providers
```

## Monitoring

The service exposes metrics on port 9083:
- Environment count by status
- Provider usage statistics
- Health check success rates
- API response times

## Security

- JWT-based authentication
- Provider credential encryption
- Project-scoped access control
- Audit logging for all operations

## Future Enhancements

- **Environment Promotion** - Automated promotion workflows
- **Cost Tracking** - Resource usage monitoring
- **Compliance Checks** - Policy enforcement
- **Advanced Analytics** - Deployment insights

## Contributing

1. Follow the existing code structure
2. Add tests for new functionality
3. Update documentation
4. Ensure provider compatibility

For detailed provider development, see [Provider Development Guide](../../docs/providers/DEVELOPMENT.md).
