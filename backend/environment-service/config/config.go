package config

import (
	"fmt"
	"os"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/config"
)

// Config holds the complete configuration for the environment service
type Config struct {
	Service           config.ServiceConfig     `yaml:"service"`
	Server            config.ServerConfig      `yaml:"server"`
	Database          config.DBConfig          `yaml:"db"`
	Logging           config.LoggingConfig     `yaml:"logging"`
	Monitoring        config.MonitoringConfig  `yaml:"monitoring"`
	Auth              config.AuthConfig        `yaml:"auth"`
	Gateway           config.GatewayConfig     `yaml:"gateway"`
	Encryption        config.EncryptionConfig  `yaml:"encryption"`
	Providers         ProvidersConfig          `yaml:"providers"`
	ExternalProviders []ExternalProviderConfig `mapstructure:"external_providers" yaml:"external_providers"`
	HealthCheck       HealthCheckConfig        `yaml:"health_checks"`
	Security          SecurityConfig           `yaml:"security"`
}

// ProvidersConfig holds configuration for deployment providers
type ProvidersConfig struct {
	GKE   GKEConfig            `yaml:"gke"`
	AKS   AKSConfig            `yaml:"aks"`
	EKS   EKSConfig            `yaml:"eks"`
	Types []ProviderTypeConfig `mapstructure:"types" yaml:"types"`
}

// ProviderTypeConfig defines an environment provider type
type ProviderTypeConfig struct {
	Type         string                 `yaml:"type" json:"type"`
	Name         string                 `yaml:"name" json:"name"`
	Description  string                 `yaml:"description" json:"description"`
	Category     string                 `yaml:"category" json:"category"`
	ConfigFields []ProviderConfigField  `yaml:"configFields" json:"configFields"`
	Capabilities []string               `yaml:"capabilities" json:"capabilities"`
	Metadata     map[string]interface{} `yaml:"metadata" json:"metadata"`
}

// ProviderConfigField defines a configuration field for a provider type
type ProviderConfigField struct {
	Name        string                 `yaml:"name" json:"name"`
	Type        string                 `yaml:"type" json:"type"`
	Label       string                 `yaml:"label" json:"label"`
	Description string                 `yaml:"description" json:"description"`
	Required    bool                   `yaml:"required" json:"required"`
	Sensitive   bool                   `yaml:"sensitive" json:"sensitive"`
	Default     interface{}            `yaml:"default" json:"default"`
	Validation  map[string]interface{} `yaml:"validation" json:"validation"`
	Options     []string               `yaml:"options" json:"options"`
}

// GKEConfig holds GKE provider configuration
type GKEConfig struct {
	Enabled bool `yaml:"enabled" env:"GKE_ENABLED" default:"true"`
	Timeout int  `yaml:"timeout" env:"GKE_TIMEOUT" default:"30"`
}

// AKSConfig holds AKS provider configuration
type AKSConfig struct {
	Enabled bool `yaml:"enabled" env:"AKS_ENABLED" default:"true"`
	Timeout int  `yaml:"timeout" env:"AKS_TIMEOUT" default:"30"`
}

// EKSConfig holds EKS provider configuration
type EKSConfig struct {
	Enabled bool `yaml:"enabled" env:"EKS_ENABLED" default:"true"`
	Timeout int  `yaml:"timeout" env:"EKS_TIMEOUT" default:"30"`
}

// ExternalProviderConfig holds configuration for external providers
type ExternalProviderConfig struct {
	Name               string `mapstructure:"name" yaml:"name" json:"name"`
	URL                string `mapstructure:"url" yaml:"url" json:"url"`
	BinaryPath         string `mapstructure:"binary_path" yaml:"binary_path" json:"binary_path"`
	Port               string `mapstructure:"port" yaml:"port" json:"port"`
	Enabled            bool   `mapstructure:"enabled" yaml:"enabled" json:"enabled"`
	AutoStart          bool   `mapstructure:"auto_start" yaml:"auto_start" json:"auto_start"`
	HealthCheckTimeout int    `mapstructure:"health_check_timeout" yaml:"health_check_timeout" json:"health_check_timeout"`
	StartupTimeout     int    `mapstructure:"startup_timeout" yaml:"startup_timeout" json:"startup_timeout"`
}

// HealthCheckConfig holds health check configuration
type HealthCheckConfig struct {
	Enabled       bool   `yaml:"enabled" env:"HEALTH_CHECK_ENABLED" default:"true"`
	Interval      string `yaml:"interval" env:"HEALTH_CHECK_INTERVAL" default:"5m"`
	Timeout       string `yaml:"timeout" env:"HEALTH_CHECK_TIMEOUT" default:"30s"`
	RetryAttempts int    `yaml:"retry_attempts" env:"HEALTH_CHECK_RETRY_ATTEMPTS" default:"3"`
}

// SecurityConfig holds security-related configuration
type SecurityConfig struct {
	AuditEnabled     bool `yaml:"audit_enabled" env:"SECURITY_AUDIT_ENABLED" default:"true"`
	AccessLogEnabled bool `yaml:"access_log_enabled" env:"SECURITY_ACCESS_LOG_ENABLED" default:"true"`
	RateLimitEnabled bool `yaml:"rate_limit_enabled" env:"SECURITY_RATE_LIMIT_ENABLED" default:"true"`
	RateLimitRPS     int  `yaml:"rate_limit_rps" env:"SECURITY_RATE_LIMIT_RPS" default:"100"`
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.Server.Port <= 0 || c.Server.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", c.Server.Port)
	}

	if c.Database.URL == "" {
		return fmt.Errorf("database URL is required")
	}

	if c.Encryption.MasterKey == "" {
		return fmt.Errorf("encryption master key is required")
	}

	// Validate health check intervals
	if c.HealthCheck.Enabled {
		if _, err := time.ParseDuration(c.HealthCheck.Interval); err != nil {
			return fmt.Errorf("invalid health check interval: %v", err)
		}
		if _, err := time.ParseDuration(c.HealthCheck.Timeout); err != nil {
			return fmt.Errorf("invalid health check timeout: %v", err)
		}
	}

	return nil
}

// initializeDefaultProviderTypes sets up default environment provider types
func (c *Config) initializeDefaultProviderTypes() {
	c.Providers.Types = []ProviderTypeConfig{
		{
			Type:        "gke",
			Name:        "Google Kubernetes Engine",
			Description: "Deploy to Google Kubernetes Engine clusters",
			Category:    "kubernetes",
			ConfigFields: []ProviderConfigField{
				{Name: "projectId", Type: "string", Label: "Project ID", Required: true, Description: "Google Cloud Project ID"},
				{Name: "region", Type: "string", Label: "Region", Required: true, Description: "GKE cluster region"},
				{Name: "clusterName", Type: "string", Label: "Cluster Name", Required: true, Description: "GKE cluster name"},
				{Name: "credentialsJson", Type: "text", Label: "Service Account JSON", Required: true, Sensitive: true, Description: "Google Cloud service account credentials"},
				{Name: "namespace", Type: "string", Label: "Namespace", Required: false, Default: "default", Description: "Kubernetes namespace"},
			},
			Capabilities: []string{"containers", "load-balancing", "auto-scaling", "persistent-storage", "monitoring"},
		},
		{
			Type:        "aks",
			Name:        "Azure Kubernetes Service",
			Description: "Deploy to Azure Kubernetes Service clusters",
			Category:    "kubernetes",
			ConfigFields: []ProviderConfigField{
				{Name: "subscriptionId", Type: "string", Label: "Subscription ID", Required: true, Description: "Azure subscription ID"},
				{Name: "resourceGroup", Type: "string", Label: "Resource Group", Required: true, Description: "Azure resource group name"},
				{Name: "clusterName", Type: "string", Label: "Cluster Name", Required: true, Description: "AKS cluster name"},
				{Name: "clientId", Type: "string", Label: "Client ID", Required: true, Description: "Azure AD application client ID"},
				{Name: "clientSecret", Type: "string", Label: "Client Secret", Required: true, Sensitive: true, Description: "Azure AD application client secret"},
				{Name: "tenantId", Type: "string", Label: "Tenant ID", Required: true, Description: "Azure AD tenant ID"},
				{Name: "namespace", Type: "string", Label: "Namespace", Required: false, Default: "default", Description: "Kubernetes namespace"},
			},
			Capabilities: []string{"containers", "load-balancing", "auto-scaling", "persistent-storage", "monitoring"},
		},
		{
			Type:        "eks",
			Name:        "Amazon Elastic Kubernetes Service",
			Description: "Deploy to Amazon EKS clusters",
			Category:    "kubernetes",
			ConfigFields: []ProviderConfigField{
				{Name: "region", Type: "string", Label: "AWS Region", Required: true, Default: "us-east-1", Description: "AWS region"},
				{Name: "clusterName", Type: "string", Label: "Cluster Name", Required: true, Description: "EKS cluster name"},
				{Name: "accessKeyId", Type: "string", Label: "Access Key ID", Required: false, Description: "AWS access key ID (optional if using IAM roles)"},
				{Name: "secretAccessKey", Type: "string", Label: "Secret Access Key", Required: false, Sensitive: true, Description: "AWS secret access key"},
				{Name: "sessionToken", Type: "string", Label: "Session Token", Required: false, Sensitive: true, Description: "AWS session token (for temporary credentials)"},
				{Name: "namespace", Type: "string", Label: "Namespace", Required: false, Default: "default", Description: "Kubernetes namespace"},
			},
			Capabilities: []string{"containers", "load-balancing", "auto-scaling", "persistent-storage", "monitoring"},
		},
		{
			Type:        "openshift",
			Name:        "Red Hat OpenShift",
			Description: "Deploy to Red Hat OpenShift clusters",
			Category:    "kubernetes",
			ConfigFields: []ProviderConfigField{
				{Name: "apiUrl", Type: "string", Label: "API URL", Required: true, Description: "OpenShift cluster API URL"},
				{Name: "token", Type: "string", Label: "Access Token", Required: true, Sensitive: true, Description: "OpenShift access token"},
				{Name: "namespace", Type: "string", Label: "Project/Namespace", Required: false, Default: "default", Description: "OpenShift project/namespace"},
				{Name: "insecureSkipTLSVerify", Type: "boolean", Label: "Skip TLS Verification", Required: false, Default: false, Description: "Skip TLS certificate verification"},
			},
			Capabilities: []string{"containers", "load-balancing", "auto-scaling", "persistent-storage", "monitoring", "security"},
		},
		{
			Type:        "vm-linux",
			Name:        "Linux Virtual Machine",
			Description: "Deploy to Linux virtual machines",
			Category:    "vm",
			ConfigFields: []ProviderConfigField{
				{Name: "host", Type: "string", Label: "Host", Required: true, Description: "VM hostname or IP address"},
				{Name: "port", Type: "number", Label: "SSH Port", Required: false, Default: 22, Description: "SSH port"},
				{Name: "username", Type: "string", Label: "Username", Required: true, Description: "SSH username"},
				{Name: "privateKey", Type: "text", Label: "Private Key", Required: false, Sensitive: true, Description: "SSH private key"},
				{Name: "password", Type: "string", Label: "Password", Required: false, Sensitive: true, Description: "SSH password (if not using key)"},
				{Name: "workingDir", Type: "string", Label: "Working Directory", Required: false, Default: "/opt/deploy", Description: "Deployment working directory"},
			},
			Capabilities: []string{"containers", "systemd-services", "docker", "monitoring"},
		},
		{
			Type:        "docker-swarm",
			Name:        "Docker Swarm",
			Description: "Deploy to Docker Swarm clusters",
			Category:    "container",
			ConfigFields: []ProviderConfigField{
				{Name: "managerHost", Type: "string", Label: "Manager Host", Required: true, Description: "Docker Swarm manager hostname or IP"},
				{Name: "port", Type: "number", Label: "Docker Port", Required: false, Default: 2376, Description: "Docker daemon port"},
				{Name: "tlsCert", Type: "text", Label: "TLS Certificate", Required: false, Sensitive: true, Description: "Docker TLS certificate"},
				{Name: "tlsKey", Type: "text", Label: "TLS Key", Required: false, Sensitive: true, Description: "Docker TLS private key"},
				{Name: "tlsCa", Type: "text", Label: "TLS CA", Required: false, Sensitive: true, Description: "Docker TLS CA certificate"},
			},
			Capabilities: []string{"containers", "load-balancing", "auto-scaling", "overlay-networking"},
		},
	}
}

// LoadConfig loads configuration from file and environment variables
func LoadConfig() (*Config, error) {
	cfg := &Config{
		Service: config.ServiceConfig{
			Name:    "environment-service",
			Version: "1.0.0",
		},
		Server: config.ServerConfig{
			Host:         "0.0.0.0",
			Port:         8083,
			ReadTimeout:  30,
			WriteTimeout: 30,
		},
		Database: config.DBConfig{
			URL:           "postgres://deploy:deploy@localhost:5432/environment_service?sslmode=disable",
			MaxRetries:    5,
			RetryInterval: 3,
			MaxOpenConns:  25,
			MaxIdleConns:  10,
			QueryTimeout:  10,
			LogLevel:      "info",
		},
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "json",
		},
		Monitoring: config.MonitoringConfig{
			Enabled: true,
			Port:    9083,
			Path:    "/metrics",
		},
		Auth: config.AuthConfig{
			JWTSecret:            os.Getenv("JWT_SECRET"),
			JWTExpirationMinutes: 1440,
			AdminServiceURL:      "http://localhost:8086",
		},
		Gateway: config.GatewayConfig{
			URL:           "http://localhost:8080",
			Enabled:       true,
			Timeout:       30,
			RetryAttempts: 3,
		},
		Encryption: config.EncryptionConfig{
			MasterKey:        os.Getenv("ENCRYPTION_MASTER_KEY"),
			Algorithm:        "AES-256-GCM",
			KeySize:          32,
			SaltSize:         16,
			RotationInterval: "30d",
			MaxKeyAge:        "90d",
			TLSEnabled:       true,
			TLSMinVersion:    "1.2",
			HSMEnabled:       false,
		},
		Providers: ProvidersConfig{
			GKE: GKEConfig{Enabled: true, Timeout: 30},
			AKS: AKSConfig{Enabled: true, Timeout: 30},
			EKS: EKSConfig{Enabled: true, Timeout: 30},
		},
		HealthCheck: HealthCheckConfig{
			Enabled:       true,
			Interval:      "5m",
			Timeout:       "30s",
			RetryAttempts: 3,
		},
		Security: SecurityConfig{
			AuditEnabled:     true,
			AccessLogEnabled: true,
			RateLimitEnabled: true,
			RateLimitRPS:     100,
		},
	}

	// Override with environment variables
	if dbURL := os.Getenv("DATABASE_URL"); dbURL != "" {
		cfg.Database.URL = dbURL
	}
	if jwtSecret := os.Getenv("JWT_SECRET"); jwtSecret != "" {
		cfg.Auth.JWTSecret = jwtSecret
	}
	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		cfg.Logging.Level = logLevel
	}
	if gatewayURL := os.Getenv("GATEWAY_URL"); gatewayURL != "" {
		cfg.Gateway.URL = gatewayURL
	}
	if adminServiceURL := os.Getenv("ADMIN_SERVICE_URL"); adminServiceURL != "" {
		cfg.Auth.AdminServiceURL = adminServiceURL
	}
	if encryptionKey := os.Getenv("ENCRYPTION_MASTER_KEY"); encryptionKey == "" {
		cfg.Encryption.MasterKey = "default-encryption-key-for-dev-32"
	}

	// Use shared config loader
	if err := config.LoadConfig(cfg, "environment-service"); err != nil {
		return nil, fmt.Errorf("failed to load config: %v", err)
	}

	// Initialize default provider types if none configured
	if len(cfg.Providers.Types) == 0 {
		cfg.initializeDefaultProviderTypes()
	}

	return cfg, nil
}

// SaveDefaultConfig saves a default configuration file
func SaveDefaultConfig() error {
	cfg, err := LoadConfig()
	if err != nil {
		return err
	}

	return config.SaveDefaultConfig(cfg, "environment-service")
}
