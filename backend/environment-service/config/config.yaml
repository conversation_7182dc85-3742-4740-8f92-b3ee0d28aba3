service:
  name: "environment-service"
  version: "1.0.0"

server:
  host: "0.0.0.0"
  port: 8088
  read_timeout: 30
  write_timeout: 30

db:
  url: "postgres://deploy:deploy@localhost:5432/environment_service?sslmode=disable"
  max_retries: 5
  retry_interval: 3
  max_open_conns: 25
  max_idle_conns: 10
  query_timeout: 10
  log_level: "info"

logging:
  level: "info"
  format: "json"

monitoring:
  enabled: true
  port: 9083
  path: "/metrics"

auth:
  jwt_secret: "default-secret-key-in-production"
  jwt_expiration_minutes: 60
  access_token_expiry: "1h"
  refresh_token_expiry: "168h"
  admin_service_url: "http://localhost:8086"
  api_key_header: "X-API-Key"
  disable: false

gateway:
  url: "http://localhost:8000"
  token: ""
  enabled: true
  service_version: "1.0.0"
  environment: "development"
  region: "local"
  retry_attempts: 3
  health_check_path: "/health"
  tags: ["api", "environments", "microservice"]

encryption:
  master_key: "your-encryption-key-32-characters"
  algorithm: "AES-256-GCM"
  key_size: 32
  salt_size: 16
  rotation_interval: "30d"
  max_key_age: "90d"
  tls_enabled: true
  tls_min_version: "1.2"
  hsm_enabled: false

providers:
  gke:
    enabled: true
    timeout: 30
  aks:
    enabled: true
    timeout: 30
  eks:
    enabled: true
    timeout: 30

external_providers:
  - name: "Google Kubernetes Engine"
    url: "http://localhost:8100"
    binary_path: "../../plugins/gke-environment-provider/build/gke-environment-provider"
    port: "8100"
    enabled: true
    auto_start: true
    health_check_timeout: 5
    startup_timeout: 10
  - name: "Azure Kubernetes Service External"
    url: "http://localhost:8101"
    binary_path: "../../plugins/aks-environment-provider/build/aks-environment-provider"
    port: "8101"
    enabled: false
    auto_start: false
    health_check_timeout: 5
    startup_timeout: 10

health_checks:
  enabled: true
  interval: "5m"
  timeout: "30s"
  retry_attempts: 3

security:
  audit_enabled: true
  access_log_enabled: true
  rate_limit_enabled: true
  rate_limit_rps: 100
