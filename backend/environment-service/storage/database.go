package storage

import (
	"fmt"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"github.com/claudio/deploy-orchestrator/environment-service/config"
	"github.com/claudio/deploy-orchestrator/environment-service/internal/services"
	"github.com/claudio/deploy-orchestrator/shared/models"
)

// InitDatabase initializes the database connection
func InitDatabase(cfg *config.Config) (*gorm.DB, error) {
	var gormConfig *gorm.Config

	// Configure GORM logger based on config
	switch cfg.Database.LogLevel {
	case "debug":
		gormConfig = &gorm.Config{
			Logger: logger.Default.LogMode(logger.Info),
		}
	case "info":
		gormConfig = &gorm.Config{
			Logger: logger.Default.LogMode(logger.Warn),
		}
	default:
		gormConfig = &gorm.Config{
			Logger: logger.Default.LogMode(logger.Silent),
		}
	}

	// Connect to database with retries
	var db *gorm.DB
	var err error

	for i := 0; i < cfg.Database.MaxRetries; i++ {
		db, err = gorm.Open(postgres.Open(cfg.Database.URL), gormConfig)
		if err == nil {
			break
		}

		if i < cfg.Database.MaxRetries-1 {
			time.Sleep(time.Duration(cfg.Database.RetryInterval) * time.Second)
		}
	}

	if err != nil {
		return nil, fmt.Errorf("failed to connect to database after %d retries: %v", cfg.Database.MaxRetries, err)
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %v", err)
	}

	sqlDB.SetMaxOpenConns(cfg.Database.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.Database.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(time.Duration(cfg.Database.QueryTimeout) * time.Minute)

	return db, nil
}

// MigrateEnvironmentModels runs database migrations for environment models
func MigrateEnvironmentModels(db *gorm.DB) error {
	return db.AutoMigrate(
		&models.EnvironmentConfig{},
		&models.EnvironmentHealth{},
		&services.EnvironmentTemplate{},
		&services.EnvironmentMigration{},
	)
}

// Repository provides database operations for environment service
type Repository struct {
	db *gorm.DB
}

// NewRepository creates a new repository instance
func NewRepository(db *gorm.DB) *Repository {
	return &Repository{db: db}
}

// GetDB returns the database instance
func (r *Repository) GetDB() *gorm.DB {
	return r.db
}

// Health check for database
func (r *Repository) HealthCheck() error {
	sqlDB, err := r.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}
