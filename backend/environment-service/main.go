package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/claudio/deploy-orchestrator/environment-service/api"
	"github.com/claudio/deploy-orchestrator/environment-service/config"
	"github.com/claudio/deploy-orchestrator/environment-service/internal/handlers"
	"github.com/claudio/deploy-orchestrator/environment-service/internal/providers"
	"github.com/claudio/deploy-orchestrator/environment-service/internal/services"
	"github.com/claudio/deploy-orchestrator/environment-service/storage"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	// Import shared modules
	"github.com/claudio/deploy-orchestrator/shared/auth"
	"github.com/claudio/deploy-orchestrator/shared/gateway"
	"github.com/claudio/deploy-orchestrator/shared/logging"
	"github.com/claudio/deploy-orchestrator/shared/monitoring"
)

func main() {
	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		fmt.Printf("Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logger with configuration
	logging.InitLogger(cfg.Logging)
	logger := logging.Default().Named("environment-service")

	// Log startup information
	logger.Info("Starting environment service",
		logging.String("version", cfg.Service.Version),
		logging.String("environment", "development"),
	)

	// Create default config file if it doesn't exist
	if err := config.SaveDefaultConfig(); err != nil {
		logger.Warn("Failed to save default config", logging.Error(err))
	}

	// Initialize database
	var db *gorm.DB
	var dbErr error
	if cfg.Database.URL != "" {
		logger.Info("Connecting to database",
			logging.String("url", cfg.Database.URL),
			logging.Int("maxRetries", cfg.Database.MaxRetries),
			logging.Int("retryInterval", cfg.Database.RetryInterval),
		)
		db, dbErr = storage.InitDatabase(cfg)
		if dbErr != nil {
			logger.Error("Failed to initialize database", logging.Error(dbErr))
			os.Exit(1)
		}
		logger.Info("Database connection established successfully")

		// Run database migrations
		logger.Info("Running database migrations...")
		if err := storage.MigrateEnvironmentModels(db); err != nil {
			logger.Error("Failed to run database migrations", logging.Error(err))
			os.Exit(1)
		}
		logger.Info("Database migrations completed successfully")
	} else {
		logger.Warn("Database URL not configured, running without database")
		db = nil
	}

	// Initialize environment providers
	logger.Info("Initializing environment providers...")
	if err := providers.InitializeProviders(cfg.ExternalProviders); err != nil {
		logger.Error("Failed to initialize providers", logging.Error(err))
		os.Exit(1)
	}
	logger.Info("Environment providers initialized successfully")

	// Initialize monitoring system
	monitoringManager := monitoring.NewMonitoringManager("environment-service", cfg.Service.Version, "development")
	monitoringManager.AddDatabase(db, "postgres")
	logger.Info("Monitoring system initialized")

	// Initialize gateway registration
	zapLogger, _ := zap.NewProduction()
	gatewayClient := gateway.NewClientFromSharedConfig("environment-service", cfg.Server.Port, &cfg.Gateway, zapLogger)
	gatewayClient.SafeRegister()
	logger.Info("Gateway registration attempted")

	// Initialize shared authentication manager
	authConfig := &auth.Config{
		JWTSecretKey:       cfg.Auth.JWTSecret,
		AccessTokenExpiry:  time.Duration(cfg.Auth.JWTExpirationMinutes) * time.Minute,
		RefreshTokenExpiry: time.Hour * 24 * 7, // 7 days
		AdminServiceURL:    cfg.Auth.AdminServiceURL,
	}
	authManager, err := auth.NewAuthManager(authConfig)
	if err != nil {
		logger.Error("Failed to create auth manager", logging.Error(err))
		os.Exit(1)
	}

	// Use shared authentication middleware
	authMiddleware := authManager.AuthMiddleware()

	// Initialize permission service and middleware
	permissionService := auth.NewHTTPPermissionService(cfg.Auth.AdminServiceURL)
	permissionMiddleware := auth.NewPermissionMiddleware(permissionService)

	// Initialize project validator
	projectValidator := services.NewProjectValidator(cfg.Auth.AdminServiceURL, logger)

	// Initialize services
	environmentService := services.NewEnvironmentService(db, projectValidator, logger)
	healthService := services.NewHealthService(db)
	providerService := services.NewProviderService()
	templateService := services.NewTemplateService(db)

	// Initialize handlers
	environmentHandler := handlers.NewEnvironmentHandler(environmentService, healthService, providerService, cfg)
	healthHandler := handlers.NewHealthHandler(healthService)
	templateHandler := handlers.NewTemplateHandler(templateService, environmentService)

	// Initialize deployable handler (maps to environments for frontend compatibility)
	deployableHandler := api.NewDeployableHandler(environmentService)

	// Set up the Gin router
	if cfg.Logging.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Setup monitoring routes and middleware
	monitoringManager.SetupRoutes(router)
	monitoringManager.SetupMiddleware(router)

	// Apply global middleware
	router.Use(func(c *gin.Context) {
		start := time.Now()
		c.Next()
		duration := time.Since(start)
		logger.Info("Request",
			logging.String("method", c.Request.Method),
			logging.String("path", c.Request.URL.Path),
			logging.Int("status", c.Writer.Status()),
			logging.Duration("duration", duration),
		)
	})

	router.Use(func(c *gin.Context) {
		c.Next()
		if len(c.Errors) > 0 {
			logger.Error("Request error", logging.String("error", c.Errors.Last().Error()))
		}
	})

	// Apply CORS middleware
	router.Use(authManager.CORSMiddleware())

	// Setup routes
	setupRoutes(router, environmentHandler, healthHandler, templateHandler, authMiddleware, permissionMiddleware)

	// Register deployable handler routes (maps environments to deployables for frontend)
	deployableHandler.RegisterRoutes(router, authMiddleware)

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
	}

	// Start server in a goroutine
	go func() {
		logger.Info("Starting environment service",
			logging.String("host", cfg.Server.Host),
			logging.Int("port", cfg.Server.Port),
		)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", logging.Error(err))
		}
	}()

	// Set up graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info("Shutting down server...")

	// Deregister from gateway
	gatewayClient.SafeDeregister()
	logger.Info("Deregistered from gateway")

	// Create a deadline to wait for
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer shutdownCancel()

	// Shutdown monitoring system
	if err := monitoringManager.Shutdown(shutdownCtx); err != nil {
		logger.Error("Failed to shutdown monitoring system", logging.Error(err))
	}

	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Fatal("Server forced to shutdown", logging.Error(err))
	}

	logger.Info("Server exited properly")
}

func setupRoutes(router *gin.Engine, environmentHandler *handlers.EnvironmentHandler, healthHandler *handlers.HealthHandler, templateHandler *handlers.TemplateHandler, authMiddleware gin.HandlerFunc, permissionMiddleware *auth.PermissionMiddleware) {
	// API routes with authentication - use v1 prefix with service-specific path
	v1 := router.Group("/api/v1/environment-service")
	v1.Use(authMiddleware)

	// Also add a direct /api/v1/environments endpoint for cleaner API paths
	directEnvAPI := router.Group("/api/v1/environments")
	directEnvAPI.Use(authMiddleware)
	{
		directEnvAPI.POST("",
			permissionMiddleware.RequirePermission("environments:create", nil),
			environmentHandler.CreateEnvironment)
		directEnvAPI.GET("", environmentHandler.ListEnvironments)
		directEnvAPI.GET("/:id", environmentHandler.GetEnvironment)
		directEnvAPI.PUT("/:id",
			permissionMiddleware.RequirePermission("environments:write", nil),
			environmentHandler.UpdateEnvironment)
		directEnvAPI.DELETE("/:id",
			permissionMiddleware.RequirePermission("environments:delete", nil),
			environmentHandler.DeleteEnvironment)
	}
	{
		// Environment management (Permission-based access)
		environments := v1.Group("/environments")
		{
			environments.POST("",
				permissionMiddleware.RequirePermission("environments:create", nil),
				environmentHandler.CreateEnvironment)
			environments.GET("", environmentHandler.ListEnvironments)   // List shows only accessible environments
			environments.GET("/:id", environmentHandler.GetEnvironment) // Handler checks environment access
			environments.PUT("/:id",
				permissionMiddleware.RequirePermission("environments:write", nil),
				environmentHandler.UpdateEnvironment)
			environments.DELETE("/:id",
				permissionMiddleware.RequirePermission("environments:delete", nil),
				environmentHandler.DeleteEnvironment)
			environments.POST("/:id/test-connection",
				permissionMiddleware.RequirePermission("environments:test", nil),
				environmentHandler.TestConnection)
			environments.POST("/:id/deploy",
				permissionMiddleware.RequirePermission("environments:deploy", nil),
				environmentHandler.DeployToEnvironment)
			environments.GET("/:id/status", environmentHandler.GetEnvironmentStatus)
			environments.GET("/:id/logs", environmentHandler.GetEnvironmentLogs)

			// Environment cloning and migration
			environments.POST("/:id/clone",
				permissionMiddleware.RequirePermission("environments:create", nil),
				environmentHandler.CloneEnvironment)
			environments.POST("/:id/migrate",
				permissionMiddleware.RequirePermission("environments:write", nil),
				environmentHandler.MigrateEnvironment)
		}

		// Project-specific environments (Project-scoped access)
		projects := v1.Group("/projects")
		projects.Use(permissionMiddleware.ProjectAccessMiddleware())
		{
			projects.GET("/:projectId/environments", environmentHandler.ListProjectEnvironments)
			projects.POST("/:projectId/environments",
				permissionMiddleware.RequirePermission("environments:create", auth.ProjectIDFromParam),
				environmentHandler.CreateProjectEnvironment)
		}

		// Health monitoring
		health := v1.Group("/health")
		{
			health.GET("/environments", healthHandler.GetAllEnvironmentHealth)
			health.GET("/environments/:id", healthHandler.GetEnvironmentHealth)
			health.POST("/environments/:id/check",
				permissionMiddleware.RequirePermission("environments:test", nil),
				healthHandler.TriggerHealthCheck)
		}

		// Provider management (Admin only for some operations)
		providers := v1.Group("/providers")
		{
			providers.GET("/types", environmentHandler.GetProviderTypes)                        // Get available provider types
			providers.GET("", environmentHandler.ListProviders)                                 // Public endpoint
			providers.GET("/:type/capabilities", environmentHandler.GetProviderCapabilities)    // Public endpoint
			providers.GET("/:type/schema", environmentHandler.GetProviderSchema)                // Get provider configuration schema
			providers.POST("/:type/validate-config", environmentHandler.ValidateProviderConfig) // Public endpoint
			providers.POST("/reload",
				permissionMiddleware.RequirePermission("providers:admin", nil),
				environmentHandler.ReloadProviders) // Admin-only endpoint to reload external providers
		}

		// Version tracking
		versions := v1.Group("/versions")
		{
			versions.GET("/matrix/:projectId", environmentHandler.GetVersionMatrix)
			versions.GET("/history/:environmentId", environmentHandler.GetDeploymentHistory)
		}

		// Environment templates
		templates := v1.Group("/templates")
		{
			templates.GET("", templateHandler.ListTemplates)
			templates.POST("",
				permissionMiddleware.RequirePermission("templates:create", nil),
				templateHandler.CreateTemplate)
			templates.GET("/:id", templateHandler.GetTemplate)
			templates.PUT("/:id",
				permissionMiddleware.RequirePermission("templates:write", nil),
				templateHandler.UpdateTemplate)
			templates.DELETE("/:id",
				permissionMiddleware.RequirePermission("templates:delete", nil),
				templateHandler.DeleteTemplate)
			templates.POST("/:id/create-environment",
				permissionMiddleware.RequirePermission("environments:create", nil),
				templateHandler.CreateEnvironmentFromTemplate)
			templates.GET("/:id/presets", templateHandler.GetTemplatePresets)
			templates.GET("/:id/variables", templateHandler.GetTemplateVariables)
			templates.POST("/:id/validate", templateHandler.ValidateTemplateConfig)
			templates.GET("/:id/stats", templateHandler.GetTemplateUsageStats)
		}
	}
}
