package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"

	"deployment-service/api"
	"deployment-service/config"
	"deployment-service/internal/deployment"
	"deployment-service/internal/promotion"
	"deployment-service/internal/rollback"
	"deployment-service/internal/version"
	"deployment-service/storage"
	"shared/auth"
	"shared/db"
	"shared/logging"
	"shared/monitoring"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found: %v", err)
	}

	// Initialize configuration
	cfg := config.Load()

	// Initialize logger
	logger := logging.NewLogger(cfg.LogLevel, cfg.ServiceName)

	// Initialize database
	database, err := db.NewConnection(cfg.Database)
	if err != nil {
		logger.Fatal("Failed to connect to database", "error", err)
	}
	defer database.Close()

	// Run migrations
	if err := db.RunMigrations(database, "migrations"); err != nil {
		logger.Fatal("Failed to run migrations", "error", err)
	}

	// Initialize storage
	store := storage.NewPostgresStorage(database)

	// Initialize auth client
	authClient := auth.NewClient(cfg.Auth.ServiceURL, cfg.Auth.SharedSecret)

	// Initialize services
	deploymentService := deployment.NewService(store, logger)
	promotionService := promotion.NewService(store, logger)
	rollbackService := rollback.NewService(store, logger)
	versionService := version.NewService(store, logger)

	// Initialize monitoring
	monitor := monitoring.NewMonitor(cfg.ServiceName, cfg.Monitoring)

	// Initialize Gin router
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}
	router := gin.New()

	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(monitor.Middleware())

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   cfg.ServiceName,
			"version":   cfg.Version,
			"timestamp": time.Now().UTC(),
		})
	})

	// Initialize API routes
	apiHandler := api.NewHandler(
		deploymentService,
		promotionService,
		rollbackService,
		versionService,
		authClient,
		logger,
	)

	// Register API routes
	v1 := router.Group("/api/v1")
	{
		// Deployment management
		deployments := v1.Group("/deployments")
		{
			deployments.POST("", apiHandler.CreateDeployment)
			deployments.GET("", apiHandler.ListDeployments)
			deployments.GET("/:id", apiHandler.GetDeployment)
			deployments.PUT("/:id", apiHandler.UpdateDeployment)
			deployments.DELETE("/:id", apiHandler.DeleteDeployment)
			deployments.POST("/:id/cancel", apiHandler.CancelDeployment)
			deployments.GET("/:id/status", apiHandler.GetDeploymentStatus)
			deployments.GET("/:id/logs", apiHandler.GetDeploymentLogs)
		}

		// Bulk deployment operations
		bulk := v1.Group("/bulk")
		{
			bulk.POST("/deploy", apiHandler.BulkDeploy)
			bulk.POST("/cancel", apiHandler.BulkCancel)
			bulk.GET("/status", apiHandler.BulkStatus)
		}

		// Version management
		versions := v1.Group("/versions")
		{
			versions.GET("/matrix/:projectId", apiHandler.GetVersionMatrix)
			versions.GET("/history/:environmentId", apiHandler.GetVersionHistory)
			versions.POST("/compare", apiHandler.CompareVersions)
		}

		// Environment promotions
		promotions := v1.Group("/promotions")
		{
			promotions.POST("", apiHandler.CreatePromotion)
			promotions.GET("", apiHandler.ListPromotions)
			promotions.GET("/:id", apiHandler.GetPromotion)
			promotions.POST("/:id/approve", apiHandler.ApprovePromotion)
			promotions.POST("/:id/reject", apiHandler.RejectPromotion)
		}

		// Rollback operations
		rollbacks := v1.Group("/rollbacks")
		{
			rollbacks.POST("", apiHandler.CreateRollback)
			rollbacks.GET("", apiHandler.ListRollbacks)
			rollbacks.GET("/:id", apiHandler.GetRollback)
			rollbacks.POST("/:id/execute", apiHandler.ExecuteRollback)
		}

		// Project-specific endpoints
		projects := v1.Group("/projects/:projectId")
		{
			projects.GET("/deployments", apiHandler.GetProjectDeployments)
			projects.GET("/environments/:environmentId/versions", apiHandler.GetEnvironmentVersions)
			projects.POST("/environments/:environmentId/deploy", apiHandler.DeployToEnvironment)
			projects.POST("/promote/:fromEnv/:toEnv", apiHandler.PromoteEnvironment)
		}
	}

	// Start HTTP server
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Port),
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		logger.Info("Starting deployment service", "port", cfg.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", "error", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down deployment service...")

	// Give outstanding requests 30 seconds to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.Fatal("Server forced to shutdown", "error", err)
	}

	logger.Info("Deployment service stopped")
}
