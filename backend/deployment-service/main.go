package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/claudio/deploy-orchestrator/deployment-service/api"
	"github.com/claudio/deploy-orchestrator/deployment-service/config"
	"github.com/claudio/deploy-orchestrator/deployment-service/internal/deployment"
	"github.com/claudio/deploy-orchestrator/deployment-service/internal/promotion"
	"github.com/claudio/deploy-orchestrator/deployment-service/internal/rollback"
	"github.com/claudio/deploy-orchestrator/deployment-service/internal/version"
	"github.com/claudio/deploy-orchestrator/deployment-service/storage"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"gorm.io/gorm"

	"github.com/claudio/deploy-orchestrator/shared/db"
	"github.com/claudio/deploy-orchestrator/shared/logging"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found: %v", err)
	}

	// Initialize configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize logger
	logger := logging.NewLogger(cfg.Logging)

	// Initialize database
	dbConfig := db.Config{
		URL:           cfg.Database.URL,
		MaxRetries:    cfg.Database.MaxRetries,
		RetryInterval: time.Duration(cfg.Database.RetryInterval) * time.Second,
		LogLevel:      cfg.Database.LogLevel,
	}

	database, err := db.Connect(dbConfig)
	if err != nil {
		logger.Fatal("Failed to connect to database", logging.Error(err))
	}

	// Get underlying SQL DB for storage layer
	sqlDB, err := database.DB()
	if err != nil {
		logger.Fatal("Failed to get SQL database", logging.Error(err))
	}
	defer sqlDB.Close()

	// Run database migrations
	if err := runMigrations(database); err != nil {
		logger.Fatal("Failed to run migrations", logging.Error(err))
	}

	// Initialize storage
	store := storage.NewPostgresStorage(sqlDB)

	// TODO: Initialize auth client when needed

	// Initialize services
	deploymentService := deployment.NewService(store, logger)
	promotionService := promotion.NewService(store, logger)
	rollbackService := rollback.NewService(store, logger)
	versionService := version.NewService(store, logger)

	// TODO: Initialize monitoring when needed

	// Initialize Gin router
	gin.SetMode(gin.ReleaseMode) // Default to release mode
	router := gin.New()

	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   cfg.Service.Name,
			"version":   cfg.Service.Version,
			"timestamp": time.Now().UTC(),
		})
	})

	// Initialize API routes
	apiHandler := api.NewHandler(
		deploymentService,
		promotionService,
		rollbackService,
		versionService,
		logger,
	)

	// Register API routes
	v1 := router.Group("/api/v1")
	{
		// Deployment management
		deployments := v1.Group("/deployments")
		{
			deployments.POST("", apiHandler.CreateDeployment)
			deployments.GET("", apiHandler.ListDeployments)
			deployments.GET("/:id", apiHandler.GetDeployment)
			deployments.PUT("/:id", apiHandler.UpdateDeployment)
			deployments.DELETE("/:id", apiHandler.DeleteDeployment)
			deployments.POST("/:id/cancel", apiHandler.CancelDeployment)
			deployments.GET("/:id/status", apiHandler.GetDeploymentStatus)
			deployments.GET("/:id/logs", apiHandler.GetDeploymentLogs)
		}

		// Bulk deployment operations
		bulk := v1.Group("/bulk")
		{
			bulk.POST("/deploy", apiHandler.BulkDeploy)
			bulk.POST("/cancel", apiHandler.BulkCancel)
			bulk.GET("/status", apiHandler.BulkStatus)
		}

		// Version management
		versions := v1.Group("/versions")
		{
			versions.GET("/matrix/:projectId", apiHandler.GetVersionMatrix)
			versions.GET("/history/:environmentId", apiHandler.GetVersionHistory)
			versions.POST("/compare", apiHandler.CompareVersions)
		}

		// Environment promotions
		promotions := v1.Group("/promotions")
		{
			promotions.POST("", apiHandler.CreatePromotion)
			promotions.GET("", apiHandler.ListPromotions)
			promotions.GET("/:id", apiHandler.GetPromotion)
			promotions.POST("/:id/approve", apiHandler.ApprovePromotion)
			promotions.POST("/:id/reject", apiHandler.RejectPromotion)
		}

		// Rollback operations
		rollbacks := v1.Group("/rollbacks")
		{
			rollbacks.POST("", apiHandler.CreateRollback)
			rollbacks.GET("", apiHandler.ListRollbacks)
			rollbacks.GET("/:id", apiHandler.GetRollback)
			rollbacks.POST("/:id/execute", apiHandler.ExecuteRollback)
		}

		// Project-specific endpoints
		projects := v1.Group("/projects/:projectId")
		{
			projects.GET("/deployments", apiHandler.GetProjectDeployments)
			projects.GET("/environments/:environmentId/versions", apiHandler.GetEnvironmentVersions)
			projects.POST("/environments/:environmentId/deploy", apiHandler.DeployToEnvironment)
			projects.POST("/promote/:fromEnv/:toEnv", apiHandler.PromoteEnvironment)
		}
	}

	// Start HTTP server
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		logger.Info("Starting deployment service", logging.Int("port", cfg.Server.Port))
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", logging.Error(err))
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down deployment service...")

	// Give outstanding requests 30 seconds to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.Fatal("Server forced to shutdown", logging.Error(err))
	}

	logger.Info("Deployment service stopped")
}

// runMigrations runs database migrations for the deployment service
func runMigrations(db *gorm.DB) error {
	log.Println("Running deployment-service database migrations...")

	// For now, we'll use GORM AutoMigrate to create the tables
	// This will create the deployments table that the SQL storage layer expects
	return db.Exec(`
		CREATE TABLE IF NOT EXISTS deployments (
			id VARCHAR(255) PRIMARY KEY,
			project_id VARCHAR(255) NOT NULL,
			environment_id VARCHAR(255) NOT NULL,
			application_id VARCHAR(255),
			workflow_id VARCHAR(255) NOT NULL,
			version VARCHAR(255) NOT NULL,
			status VARCHAR(50) NOT NULL DEFAULT 'pending',
			strategy VARCHAR(50) NOT NULL DEFAULT 'rolling_update',
			provider VARCHAR(50) NOT NULL DEFAULT 'helm',
			configuration JSONB DEFAULT '{}',
			parameters JSONB DEFAULT '{}',
			secret_mappings JSONB DEFAULT '{}',
			created_by VARCHAR(255) NOT NULL,
			created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
			updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
			started_at TIMESTAMP WITH TIME ZONE,
			completed_at TIMESTAMP WITH TIME ZONE,
			error TEXT
		);
	`).Error
}
