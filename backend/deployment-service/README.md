# Deployment Service

The Deployment Service is a unified microservice that handles all deployment operations in the deploy-orchestrator system. It consolidates deployment management, version tracking, promotions, and rollbacks into a single, cohesive service.

## Features

- **Unified Deployment Management**: Single API for all deployment operations
- **Multi-Provider Support**: Helm, Kubernetes, OpenShift, and Docker deployments
- **Bulk Operations**: Deploy multiple applications simultaneously
- **Version Management**: Track and compare versions across environments
- **Environment Promotions**: Promote deployments between environments with approval workflows
- **Rollback Support**: Automated and manual rollback capabilities
- **Real-time Monitoring**: Track deployment progress and status
- **Comprehensive Logging**: Detailed deployment logs and audit trails

## API Endpoints

### Deployment Management
- `POST /api/v1/deployments` - Create a new deployment
- `GET /api/v1/deployments` - List deployments with filtering
- `GET /api/v1/deployments/:id` - Get specific deployment
- `PUT /api/v1/deployments/:id` - Update deployment
- `DELETE /api/v1/deployments/:id` - Delete deployment
- `POST /api/v1/deployments/:id/cancel` - Cancel deployment
- `GET /api/v1/deployments/:id/status` - Get deployment status
- `GET /api/v1/deployments/:id/logs` - Get deployment logs

### Bulk Operations
- `POST /api/v1/bulk/deploy` - Bulk deployment
- `POST /api/v1/bulk/cancel` - Bulk cancellation
- `GET /api/v1/bulk/status` - Bulk status check

### Version Management
- `GET /api/v1/versions/matrix/:projectId` - Get version matrix
- `GET /api/v1/versions/history/:environmentId` - Get version history
- `POST /api/v1/versions/compare` - Compare versions

### Promotions
- `POST /api/v1/promotions` - Create promotion
- `GET /api/v1/promotions` - List promotions
- `GET /api/v1/promotions/:id` - Get promotion
- `POST /api/v1/promotions/:id/approve` - Approve promotion
- `POST /api/v1/promotions/:id/reject` - Reject promotion

### Rollbacks
- `POST /api/v1/rollbacks` - Create rollback
- `GET /api/v1/rollbacks` - List rollbacks
- `GET /api/v1/rollbacks/:id` - Get rollback
- `POST /api/v1/rollbacks/:id/execute` - Execute rollback

### Project-Specific Endpoints
- `GET /api/v1/projects/:projectId/deployments` - Get project deployments
- `GET /api/v1/projects/:projectId/environments/:environmentId/versions` - Get environment versions
- `POST /api/v1/projects/:projectId/environments/:environmentId/deploy` - Deploy to environment
- `POST /api/v1/projects/:projectId/promote/:fromEnv/:toEnv` - Promote between environments

## Configuration

The service can be configured using environment variables or the `config.yaml` file:

### Environment Variables
- `SERVICE_NAME` - Service name (default: deployment-service)
- `PORT` - Service port (default: 8080)
- `DB_HOST` - Database host
- `DB_PORT` - Database port
- `DB_NAME` - Database name
- `DB_USER` - Database user
- `DB_PASSWORD` - Database password
- `AUTH_SERVICE_URL` - Authentication service URL
- `WORKFLOW_SERVICE_URL` - Workflow service URL
- `APPLICATION_SERVICE_URL` - Application service URL
- `ENVIRONMENT_SERVICE_URL` - Environment service URL
- `SECRETS_SERVICE_URL` - Secrets service URL

### Provider Configuration
- `HELM_ENABLED` - Enable Helm provider
- `KUBERNETES_ENABLED` - Enable Kubernetes provider
- `OPENSHIFT_ENABLED` - Enable OpenShift provider
- `DOCKER_ENABLED` - Enable Docker provider

## Development

### Prerequisites
- Go 1.21+
- PostgreSQL 12+
- Docker (optional)

### Setup
1. Clone the repository
2. Copy `.env.example` to `.env` and configure
3. Install dependencies: `make deps`
4. Run database migrations: `make migrate-up`
5. Start the service: `make run`

### Development Commands
```bash
# Build the service
make build

# Run tests
make test

# Run with hot reload
make dev

# Format code
make fmt

# Run linter
make lint

# Build Docker image
make docker-build

# Run Docker container
make docker-run
```

### Database Migrations
```bash
# Create new migration
make migrate-create name=create_deployments_table

# Run migrations up
make migrate-up

# Run migrations down
make migrate-down
```

## Deployment

### Docker
```bash
# Build image
docker build -t deployment-service .

# Run container
docker run -p 8080:8080 --env-file .env deployment-service
```

### Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: deployment-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: deployment-service
  template:
    metadata:
      labels:
        app: deployment-service
    spec:
      containers:
      - name: deployment-service
        image: deployment-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: DB_HOST
          value: "postgres-service"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
```

## Architecture

The Deployment Service follows a clean architecture pattern:

- **API Layer**: HTTP handlers and routing
- **Service Layer**: Business logic and orchestration
- **Storage Layer**: Data persistence and retrieval
- **Provider Layer**: Deployment provider implementations

### Key Components

1. **Deployment Manager**: Orchestrates deployment operations
2. **Version Manager**: Tracks and compares versions
3. **Promotion Manager**: Handles environment promotions
4. **Rollback Manager**: Manages rollback operations
5. **Provider Manager**: Manages deployment providers

## Monitoring

The service exposes metrics at `/metrics` for Prometheus scraping:

- Deployment counters by status
- Deployment duration histograms
- Provider-specific metrics
- Error rates and latencies

Health check is available at `/health`.

## Security

- JWT-based authentication
- Role-based access control
- Environment-specific permissions
- Audit logging for all operations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run `make test` and `make lint`
6. Submit a pull request

## License

This project is licensed under the MIT License.
