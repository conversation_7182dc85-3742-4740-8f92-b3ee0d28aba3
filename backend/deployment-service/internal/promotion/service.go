package promotion

import (
	"context"
	"fmt"
	"time"

	"github.com/claudio/deploy-orchestrator/deployment-service/models"
	"github.com/claudio/deploy-orchestrator/deployment-service/storage"
	"github.com/claudio/deploy-orchestrator/shared/logging"

	"github.com/google/uuid"
)

// Service handles promotion operations
type Service struct {
	storage storage.Storage
	logger  logging.Logger
}

// NewService creates a new promotion service
func NewService(storage storage.Storage, logger logging.Logger) *Service {
	return &Service{
		storage: storage,
		logger:  logger,
	}
}

// CreatePromotion creates a new promotion
func (s *Service) CreatePromotion(ctx context.Context, req *models.CreatePromotionRequest) (*models.Promotion, error) {
	promotion := &models.Promotion{
		ID:                uuid.New().String(),
		ProjectID:         req.ProjectID,
		SourceEnvironment: req.SourceEnvironment,
		TargetEnvironment: req.TargetEnvironment,
		ApplicationIDs:    req.ApplicationIDs,
		Version:           req.Version,
		Type:              req.Type,
		Status:            models.PromotionStatusPending,
		Strategy:          req.Strategy,
		Configuration:     req.Configuration,
		ApprovalRequired:  req.ApprovalRequired,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	err := s.storage.CreatePromotion(ctx, promotion)
	if err != nil {
		s.logger.Error("Failed to create promotion", logging.Error(err))
		return nil, fmt.Errorf("failed to create promotion: %w", err)
	}

	s.logger.Info("Promotion created", logging.String("promotionId", promotion.ID))
	return promotion, nil
}

// ListPromotions lists promotions with filtering
func (s *Service) ListPromotions(ctx context.Context, filter *models.PromotionFilter) ([]*models.Promotion, int, error) {
	promotions, total, err := s.storage.ListPromotions(ctx, filter)
	if err != nil {
		s.logger.Error("Failed to list promotions", logging.Error(err))
		return nil, 0, fmt.Errorf("failed to list promotions: %w", err)
	}

	return promotions, total, nil
}

// GetPromotion gets a specific promotion
func (s *Service) GetPromotion(ctx context.Context, id string) (*models.Promotion, error) {
	promotion, err := s.storage.GetPromotion(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get promotion", logging.Error(err), logging.String("promotionId", id))
		return nil, fmt.Errorf("failed to get promotion: %w", err)
	}

	return promotion, nil
}

// ApprovePromotion approves a promotion
func (s *Service) ApprovePromotion(ctx context.Context, id, userID string) error {
	promotion, err := s.storage.GetPromotion(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get promotion: %w", err)
	}

	if promotion.Status != models.PromotionStatusPending {
		return fmt.Errorf("cannot approve promotion in status: %s", promotion.Status)
	}

	promotion.Status = models.PromotionStatusApproved
	promotion.ApprovedBy = &userID
	now := time.Now()
	promotion.ApprovedAt = &now
	promotion.UpdatedAt = now

	err = s.storage.UpdatePromotion(ctx, promotion)
	if err != nil {
		return fmt.Errorf("failed to update promotion: %w", err)
	}

	s.logger.Info("Promotion approved", logging.String("promotionId", id), logging.String("approvedBy", userID))
	return nil
}

// RejectPromotion rejects a promotion
func (s *Service) RejectPromotion(ctx context.Context, id, userID, reason string) error {
	promotion, err := s.storage.GetPromotion(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get promotion: %w", err)
	}

	if promotion.Status != models.PromotionStatusPending {
		return fmt.Errorf("cannot reject promotion in status: %s", promotion.Status)
	}

	promotion.Status = models.PromotionStatusRejected
	promotion.RejectedBy = &userID
	promotion.RejectionReason = &reason
	now := time.Now()
	promotion.RejectedAt = &now
	promotion.UpdatedAt = now

	err = s.storage.UpdatePromotion(ctx, promotion)
	if err != nil {
		return fmt.Errorf("failed to update promotion: %w", err)
	}

	s.logger.Info("Promotion rejected", logging.String("promotionId", id), logging.String("rejectedBy", userID), logging.String("reason", reason))
	return nil
}

// PromoteEnvironment promotes between environments
func (s *Service) PromoteEnvironment(ctx context.Context, req *models.EnvironmentPromotionRequest) (*models.Promotion, error) {
	createReq := &models.CreatePromotionRequest{
		ProjectID:         req.ProjectID,
		SourceEnvironment: req.SourceEnvironment,
		TargetEnvironment: req.TargetEnvironment,
		ApplicationIDs:    req.ApplicationIDs,
		Version:           "", // Will be determined from source environment
		Type:              models.PromotionTypeManual,
		Strategy:          req.Strategy,
		Configuration:     req.Configuration,
		ApprovalRequired:  req.ApprovalRequired,
	}

	return s.CreatePromotion(ctx, createReq)
}
