package rollback

import (
	"context"
	"fmt"
	"time"

	"deployment-service/models"
	"deployment-service/storage"
	"shared/logging"

	"github.com/google/uuid"
)

// Service handles rollback operations
type Service struct {
	storage storage.Storage
	logger  *logging.Logger
}

// NewService creates a new rollback service
func NewService(storage storage.Storage, logger *logging.Logger) *Service {
	return &Service{
		storage: storage,
		logger:  logger,
	}
}

// CreateRollback creates a new rollback
func (s *Service) CreateRollback(ctx context.Context, req *models.CreateRollbackRequest) (*models.Rollback, error) {
	rollback := &models.Rollback{
		ID:            uuid.New().String(),
		ProjectID:     req.ProjectID,
		EnvironmentID: req.EnvironmentID,
		ApplicationID: req.ApplicationID,
		DeploymentID:  req.DeploymentID,
		ToVersion:     req.ToVersion,
		Type:          req.Type,
		Status:        models.RollbackStatusPending,
		Trigger:       models.TriggerManual,
		Reason:        req.Reason,
		Strategy:      req.Strategy,
		Configuration: req.Configuration,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	err := s.storage.CreateRollback(ctx, rollback)
	if err != nil {
		s.logger.Error("Failed to create rollback", "error", err)
		return nil, fmt.Errorf("failed to create rollback: %w", err)
	}

	s.logger.Info("Rollback created", "rollbackId", rollback.ID)
	return rollback, nil
}

// ListRollbacks lists rollbacks with filtering
func (s *Service) ListRollbacks(ctx context.Context, filter *models.RollbackFilter) ([]*models.Rollback, int, error) {
	rollbacks, total, err := s.storage.ListRollbacks(ctx, filter)
	if err != nil {
		s.logger.Error("Failed to list rollbacks", "error", err)
		return nil, 0, fmt.Errorf("failed to list rollbacks: %w", err)
	}

	return rollbacks, total, nil
}

// GetRollback gets a specific rollback
func (s *Service) GetRollback(ctx context.Context, id string) (*models.Rollback, error) {
	rollback, err := s.storage.GetRollback(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get rollback", "error", err, "rollbackId", id)
		return nil, fmt.Errorf("failed to get rollback: %w", err)
	}

	return rollback, nil
}

// ExecuteRollback executes a rollback
func (s *Service) ExecuteRollback(ctx context.Context, id, userID string) error {
	rollback, err := s.storage.GetRollback(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get rollback: %w", err)
	}

	if rollback.Status != models.RollbackStatusPending {
		return fmt.Errorf("cannot execute rollback in status: %s", rollback.Status)
	}

	rollback.Status = models.RollbackStatusExecuting
	now := time.Now()
	rollback.ExecutedAt = &now
	rollback.UpdatedAt = now

	err = s.storage.UpdateRollback(ctx, rollback)
	if err != nil {
		return fmt.Errorf("failed to update rollback: %w", err)
	}

	s.logger.Info("Rollback execution started", "rollbackId", id, "executedBy", userID)

	// TODO: Implement actual rollback execution logic
	// This would involve:
	// 1. Creating a new deployment with the target version
	// 2. Monitoring the deployment progress
	// 3. Updating rollback status based on deployment result

	return nil
}
