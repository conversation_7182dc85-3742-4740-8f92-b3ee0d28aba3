package version

import (
	"context"
	"fmt"

	"deployment-service/models"
	"deployment-service/storage"
	"shared/logging"
)

// Service handles version operations
type Service struct {
	storage storage.Storage
	logger  *logging.Logger
}

// NewService creates a new version service
func NewService(storage storage.Storage, logger *logging.Logger) *Service {
	return &Service{
		storage: storage,
		logger:  logger,
	}
}

// GetVersionMatrix gets version matrix for a project
func (s *Service) GetVersionMatrix(ctx context.Context, projectID string) (*models.VersionMatrix, error) {
	matrix, err := s.storage.GetVersionMatrix(ctx, projectID)
	if err != nil {
		s.logger.Error("Failed to get version matrix", "error", err, "projectId", projectID)
		return nil, fmt.Errorf("failed to get version matrix: %w", err)
	}

	// Convert storage format to response format
	versionMatrix := &models.VersionMatrix{
		ProjectID:    projectID,
		Applications: make(map[string]models.ApplicationVersions),
		Environments: []string{},
	}

	// Extract environments
	envSet := make(map[string]bool)
	for _, appVersions := range matrix {
		for env := range appVersions {
			if !envSet[env] {
				envSet[env] = true
				versionMatrix.Environments = append(versionMatrix.Environments, env)
			}
		}
	}

	// Convert to ApplicationVersions format
	for appID, envVersions := range matrix {
		appVersions := models.ApplicationVersions{
			ApplicationID:   appID,
			ApplicationName: appID, // TODO: Get actual app name
			Environments:    make(map[string]models.VersionInfo),
		}

		for env, versionInfo := range envVersions {
			appVersions.Environments[env] = *versionInfo
		}

		versionMatrix.Applications[appID] = appVersions
	}

	return versionMatrix, nil
}

// GetVersionHistory gets version history for an environment
func (s *Service) GetVersionHistory(ctx context.Context, environmentID string) (*models.VersionHistory, error) {
	entries, err := s.storage.GetVersionHistory(ctx, environmentID)
	if err != nil {
		s.logger.Error("Failed to get version history", "error", err, "environmentId", environmentID)
		return nil, fmt.Errorf("failed to get version history: %w", err)
	}

	history := &models.VersionHistory{
		EnvironmentID: environmentID,
		Versions:      []*models.VersionEntry{},
		Total:         len(entries),
	}

	for _, entry := range entries {
		history.Versions = append(history.Versions, entry)
	}

	return history, nil
}

// CompareVersions compares versions between environments
func (s *Service) CompareVersions(ctx context.Context, req *models.CompareVersionsRequest) (*models.VersionComparison, error) {
	// Get versions for both environments
	sourceVersions, err := s.storage.GetEnvironmentVersions(ctx, req.ProjectID, req.SourceEnvironment)
	if err != nil {
		return nil, fmt.Errorf("failed to get source environment versions: %w", err)
	}

	targetVersions, err := s.storage.GetEnvironmentVersions(ctx, req.ProjectID, req.TargetEnvironment)
	if err != nil {
		return nil, fmt.Errorf("failed to get target environment versions: %w", err)
	}

	comparison := &models.VersionComparison{
		ProjectID:         req.ProjectID,
		SourceEnvironment: req.SourceEnvironment,
		TargetEnvironment: req.TargetEnvironment,
		Applications:      []models.ApplicationComparison{},
		Summary: models.ComparisonSummary{
			TotalApplications: 0,
			Same:              0,
			Newer:             0,
			Older:             0,
			Missing:           0,
			Conflicts:         0,
			CanPromoteAll:     true,
		},
	}

	// Compare versions for each application
	allApps := make(map[string]bool)
	for appID := range sourceVersions {
		allApps[appID] = true
	}
	for appID := range targetVersions {
		allApps[appID] = true
	}

	for appID := range allApps {
		sourceVersion, hasSource := sourceVersions[appID]
		targetVersion, hasTarget := targetVersions[appID]

		appComparison := models.ApplicationComparison{
			ApplicationID:   appID,
			ApplicationName: appID, // TODO: Get actual app name
			Status:          models.ComparisonStatusMissing,
			CanPromote:      false,
			Differences:     []string{},
		}

		if hasSource {
			appComparison.SourceVersion = *sourceVersion
		}
		if hasTarget {
			appComparison.TargetVersion = *targetVersion
		}

		// Determine comparison status
		if hasSource && hasTarget {
			if sourceVersion.Version == targetVersion.Version {
				appComparison.Status = models.ComparisonStatusSame
				comparison.Summary.Same++
			} else {
				// Simple version comparison (could be enhanced)
				appComparison.Status = models.ComparisonStatusNewer
				appComparison.CanPromote = true
				comparison.Summary.Newer++
			}
		} else if hasSource && !hasTarget {
			appComparison.Status = models.ComparisonStatusMissing
			appComparison.CanPromote = true
			comparison.Summary.Missing++
		} else if !hasSource && hasTarget {
			appComparison.Status = models.ComparisonStatusMissing
			comparison.Summary.Missing++
		}

		comparison.Applications = append(comparison.Applications, appComparison)
		comparison.Summary.TotalApplications++

		if !appComparison.CanPromote {
			comparison.Summary.CanPromoteAll = false
		}
	}

	return comparison, nil
}

// GetEnvironmentVersions gets versions for a specific environment
func (s *Service) GetEnvironmentVersions(ctx context.Context, projectID, environmentID string) (*models.EnvironmentVersions, error) {
	versions, err := s.storage.GetEnvironmentVersions(ctx, projectID, environmentID)
	if err != nil {
		s.logger.Error("Failed to get environment versions", "error", err, "projectId", projectID, "environmentId", environmentID)
		return nil, fmt.Errorf("failed to get environment versions: %w", err)
	}

	envVersions := &models.EnvironmentVersions{
		ProjectID:     projectID,
		EnvironmentID: environmentID,
		Applications:  make(map[string]models.VersionInfo),
	}

	for appID, versionInfo := range versions {
		envVersions.Applications[appID] = *versionInfo
	}

	return envVersions, nil
}
