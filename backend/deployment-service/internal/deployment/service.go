package deployment

import (
	"context"
	"fmt"
	"time"

	"deployment-service/models"
	"deployment-service/storage"
	"shared/logging"

	"github.com/google/uuid"
)

// Service handles deployment operations
type Service struct {
	storage storage.Storage
	logger  *logging.Logger
}

// NewService creates a new deployment service
func NewService(storage storage.Storage, logger *logging.Logger) *Service {
	return &Service{
		storage: storage,
		logger:  logger,
	}
}

// CreateDeployment creates a new deployment
func (s *Service) CreateDeployment(ctx context.Context, req *models.CreateDeploymentRequest) (*models.Deployment, error) {
	deployment := &models.Deployment{
		ID:            uuid.New().String(),
		ProjectID:     req.ProjectID,
		EnvironmentID: req.EnvironmentID,
		WorkflowID:    req.WorkflowID,
		Version:       req.Version,
		Status:        models.DeploymentStatusPending,
		Strategy:      req.Strategy,
		Provider:      req.Provider,
		Configuration: req.Configuration,
		Parameters:    req.Parameters,
		SecretMappings: req.SecretMappings,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// For bulk deployment, create multiple deployments
	if len(req.ApplicationIDs) > 1 {
		return s.createBulkDeployment(ctx, req)
	}

	// Single application deployment
	if len(req.ApplicationIDs) > 0 {
		deployment.ApplicationID = req.ApplicationIDs[0]
	}

	err := s.storage.CreateDeployment(ctx, deployment)
	if err != nil {
		s.logger.Error("Failed to create deployment", "error", err)
		return nil, fmt.Errorf("failed to create deployment: %w", err)
	}

	s.logger.Info("Deployment created", "deploymentId", deployment.ID)
	return deployment, nil
}

// createBulkDeployment handles bulk deployment creation
func (s *Service) createBulkDeployment(ctx context.Context, req *models.CreateDeploymentRequest) (*models.Deployment, error) {
	// Create a parent deployment for tracking
	parentDeployment := &models.Deployment{
		ID:            uuid.New().String(),
		ProjectID:     req.ProjectID,
		EnvironmentID: req.EnvironmentID,
		WorkflowID:    req.WorkflowID,
		Version:       req.Version,
		Status:        models.DeploymentStatusPending,
		Strategy:      req.Strategy,
		Provider:      req.Provider,
		Configuration: req.Configuration,
		Parameters:    req.Parameters,
		SecretMappings: req.SecretMappings,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	err := s.storage.CreateDeployment(ctx, parentDeployment)
	if err != nil {
		return nil, fmt.Errorf("failed to create parent deployment: %w", err)
	}

	// Create individual deployments for each application
	for _, appID := range req.ApplicationIDs {
		childDeployment := &models.Deployment{
			ID:            uuid.New().String(),
			ProjectID:     req.ProjectID,
			EnvironmentID: req.EnvironmentID,
			ApplicationID: appID,
			WorkflowID:    req.WorkflowID,
			Version:       req.Version,
			Status:        models.DeploymentStatusPending,
			Strategy:      req.Strategy,
			Provider:      req.Provider,
			Configuration: req.Configuration,
			Parameters:    req.Parameters,
			SecretMappings: req.SecretMappings,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}

		err := s.storage.CreateDeployment(ctx, childDeployment)
		if err != nil {
			s.logger.Error("Failed to create child deployment", "error", err, "appId", appID)
			// Continue with other deployments even if one fails
		}
	}

	return parentDeployment, nil
}

// GetDeployment retrieves a deployment by ID
func (s *Service) GetDeployment(ctx context.Context, id string) (*models.Deployment, error) {
	deployment, err := s.storage.GetDeployment(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get deployment", "error", err, "deploymentId", id)
		return nil, fmt.Errorf("failed to get deployment: %w", err)
	}

	return deployment, nil
}

// UpdateDeployment updates a deployment
func (s *Service) UpdateDeployment(ctx context.Context, id string, req *models.UpdateDeploymentRequest) (*models.Deployment, error) {
	deployment, err := s.storage.GetDeployment(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get deployment: %w", err)
	}

	// Update fields if provided
	if req.Status != nil {
		deployment.Status = *req.Status
	}
	if req.Configuration != nil {
		deployment.Configuration = req.Configuration
	}
	if req.Parameters != nil {
		deployment.Parameters = req.Parameters
	}
	if req.Error != nil {
		deployment.Error = req.Error
	}

	deployment.UpdatedAt = time.Now()

	err = s.storage.UpdateDeployment(ctx, deployment)
	if err != nil {
		s.logger.Error("Failed to update deployment", "error", err, "deploymentId", id)
		return nil, fmt.Errorf("failed to update deployment: %w", err)
	}

	s.logger.Info("Deployment updated", "deploymentId", id)
	return deployment, nil
}

// DeleteDeployment deletes a deployment
func (s *Service) DeleteDeployment(ctx context.Context, id string) error {
	err := s.storage.DeleteDeployment(ctx, id)
	if err != nil {
		s.logger.Error("Failed to delete deployment", "error", err, "deploymentId", id)
		return fmt.Errorf("failed to delete deployment: %w", err)
	}

	s.logger.Info("Deployment deleted", "deploymentId", id)
	return nil
}

// ListDeployments lists deployments with filtering
func (s *Service) ListDeployments(ctx context.Context, filter *models.DeploymentFilter) ([]*models.Deployment, int, error) {
	deployments, total, err := s.storage.ListDeployments(ctx, filter)
	if err != nil {
		s.logger.Error("Failed to list deployments", "error", err)
		return nil, 0, fmt.Errorf("failed to list deployments: %w", err)
	}

	return deployments, total, nil
}

// CancelDeployment cancels a deployment
func (s *Service) CancelDeployment(ctx context.Context, id string) error {
	deployment, err := s.storage.GetDeployment(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get deployment: %w", err)
	}

	if deployment.Status != models.DeploymentStatusPending && deployment.Status != models.DeploymentStatusRunning {
		return fmt.Errorf("cannot cancel deployment in status: %s", deployment.Status)
	}

	deployment.Status = models.DeploymentStatusCancelled
	deployment.UpdatedAt = time.Now()

	err = s.storage.UpdateDeployment(ctx, deployment)
	if err != nil {
		return fmt.Errorf("failed to update deployment status: %w", err)
	}

	s.logger.Info("Deployment cancelled", "deploymentId", id)
	return nil
}

// GetDeploymentStatus gets deployment status
func (s *Service) GetDeploymentStatus(ctx context.Context, id string) (*models.DeploymentStatusInfo, error) {
	deployment, err := s.storage.GetDeployment(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get deployment: %w", err)
	}

	status := &models.DeploymentStatusInfo{
		ID:          deployment.ID,
		Status:      deployment.Status,
		Progress:    s.calculateProgress(deployment),
		Message:     s.getStatusMessage(deployment),
		StartedAt:   deployment.StartedAt,
		CompletedAt: deployment.CompletedAt,
		Error:       deployment.Error,
		LastUpdated: deployment.UpdatedAt,
	}

	return status, nil
}

// GetDeploymentLogs gets deployment logs
func (s *Service) GetDeploymentLogs(ctx context.Context, id string) ([]string, error) {
	logs, err := s.storage.GetDeploymentLogs(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get deployment logs: %w", err)
	}

	var logMessages []string
	for _, log := range logs {
		logMessages = append(logMessages, log.Message)
	}

	return logMessages, nil
}

// BulkDeploy performs bulk deployment
func (s *Service) BulkDeploy(ctx context.Context, req *models.BulkDeploymentRequest) (*models.BulkDeploymentResult, error) {
	// Convert to CreateDeploymentRequest
	createReq := &models.CreateDeploymentRequest{
		ProjectID:      req.ProjectID,
		ApplicationIDs: req.ApplicationIDs,
		EnvironmentID:  req.EnvironmentID,
		WorkflowID:     req.WorkflowID,
		Version:        req.Version,
		Strategy:       req.Strategy,
		Provider:       req.Provider,
		Configuration:  req.Configuration,
		Parameters:     req.Parameters,
		SecretMappings: req.SecretMappings,
	}

	deployment, err := s.CreateDeployment(ctx, createReq)
	if err != nil {
		return nil, err
	}

	result := &models.BulkDeploymentResult{
		DeploymentID: deployment.ID,
		Deployments:  []*models.Deployment{deployment},
		Succeeded:    []string{deployment.ID},
		Failed:       []string{},
		Errors:       make(map[string]string),
	}

	return result, nil
}

// BulkCancel cancels multiple deployments
func (s *Service) BulkCancel(ctx context.Context, req *models.BulkCancelRequest) (*models.BulkCancelResult, error) {
	result := &models.BulkCancelResult{
		Succeeded: []string{},
		Failed:    []string{},
		Errors:    make(map[string]string),
	}

	for _, deploymentID := range req.DeploymentIDs {
		err := s.CancelDeployment(ctx, deploymentID)
		if err != nil {
			result.Failed = append(result.Failed, deploymentID)
			result.Errors[deploymentID] = err.Error()
		} else {
			result.Succeeded = append(result.Succeeded, deploymentID)
		}
	}

	return result, nil
}

// BulkStatus gets status of multiple deployments
func (s *Service) BulkStatus(ctx context.Context, deploymentIDs []string) (map[string]*models.DeploymentStatusInfo, error) {
	statuses := make(map[string]*models.DeploymentStatusInfo)

	for _, deploymentID := range deploymentIDs {
		status, err := s.GetDeploymentStatus(ctx, deploymentID)
		if err != nil {
			s.logger.Error("Failed to get deployment status", "error", err, "deploymentId", deploymentID)
			continue
		}
		statuses[deploymentID] = status
	}

	return statuses, nil
}

// Helper methods
func (s *Service) calculateProgress(deployment *models.Deployment) int {
	switch deployment.Status {
	case models.DeploymentStatusPending:
		return 0
	case models.DeploymentStatusRunning:
		return 50
	case models.DeploymentStatusSucceeded:
		return 100
	case models.DeploymentStatusFailed, models.DeploymentStatusCancelled:
		return 0
	default:
		return 0
	}
}

func (s *Service) getStatusMessage(deployment *models.Deployment) string {
	switch deployment.Status {
	case models.DeploymentStatusPending:
		return "Deployment is pending"
	case models.DeploymentStatusRunning:
		return "Deployment is in progress"
	case models.DeploymentStatusSucceeded:
		return "Deployment completed successfully"
	case models.DeploymentStatusFailed:
		if deployment.Error != nil {
			return fmt.Sprintf("Deployment failed: %s", *deployment.Error)
		}
		return "Deployment failed"
	case models.DeploymentStatusCancelled:
		return "Deployment was cancelled"
	default:
		return "Unknown status"
	}
}

// DeployToEnvironment deploys to a specific environment
func (s *Service) DeployToEnvironment(ctx context.Context, req *models.EnvironmentDeploymentRequest) (*models.Deployment, error) {
	createReq := &models.CreateDeploymentRequest{
		ProjectID:      req.ProjectID,
		ApplicationIDs: req.ApplicationIDs,
		EnvironmentID:  req.EnvironmentID,
		WorkflowID:     req.WorkflowID,
		Version:        req.Version,
		Strategy:       req.Strategy,
		Provider:       req.Provider,
		Configuration:  req.Configuration,
		Parameters:     req.Parameters,
		SecretMappings: req.SecretMappings,
	}

	return s.CreateDeployment(ctx, createReq)
}
