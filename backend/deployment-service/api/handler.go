package api

import (
	"net/http"
	"strconv"

	"github.com/claudio/deploy-orchestrator/deployment-service/internal/deployment"
	"github.com/claudio/deploy-orchestrator/deployment-service/internal/promotion"
	"github.com/claudio/deploy-orchestrator/deployment-service/internal/rollback"
	"github.com/claudio/deploy-orchestrator/deployment-service/internal/version"
	"github.com/claudio/deploy-orchestrator/deployment-service/models"
	"github.com/claudio/deploy-orchestrator/shared/logging"
	"github.com/gin-gonic/gin"
)

// Hand<PERSON> handles HTTP requests for the deployment service
type Handler struct {
	deploymentService *deployment.Service
	promotionService  *promotion.Service
	rollbackService   *rollback.Service
	versionService    *version.Service
	logger            logging.Logger
}

// NewHandler creates a new API handler
func NewHandler(
	deploymentService *deployment.Service,
	promotionService *promotion.Service,
	rollbackService *rollback.Service,
	versionService *version.Service,
	logger logging.Logger,
) *Handler {
	return &Handler{
		deploymentService: deploymentService,
		promotionService:  promotionService,
		rollbackService:   rollbackService,
		versionService:    versionService,
		logger:            logger,
	}
}

// CreateDeployment creates a new deployment
func (h *Handler) CreateDeployment(c *gin.Context) {
	var req models.CreateDeploymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// TODO: Add auth check
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	deployment, err := h.deploymentService.CreateDeployment(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to create deployment", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create deployment"})
		return
	}

	c.JSON(http.StatusCreated, deployment)
}

// ListDeployments lists deployments with optional filtering
func (h *Handler) ListDeployments(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	// Parse query parameters
	filter := models.DeploymentFilter{
		ProjectID:     c.Query("projectId"),
		EnvironmentID: c.Query("environmentId"),
		ApplicationID: c.Query("applicationId"),
		Status:        c.Query("status"),
		UserID:        c.Query("userId"),
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			filter.Limit = l
		}
	}

	if offset := c.Query("offset"); offset != "" {
		if o, err := strconv.Atoi(offset); err == nil {
			filter.Offset = o
		}
	}

	deployments, total, err := h.deploymentService.ListDeployments(c.Request.Context(), &filter)
	if err != nil {
		h.logger.Error("Failed to list deployments", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list deployments"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"deployments": deployments,
		"total":       total,
	})
}

// GetDeployment gets a specific deployment
func (h *Handler) GetDeployment(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	deploymentID := c.Param("id")
	deployment, err := h.deploymentService.GetDeployment(c.Request.Context(), deploymentID)
	if err != nil {
		h.logger.Error("Failed to get deployment", logging.Error(err), logging.String("deploymentId", deploymentID))
		c.JSON(http.StatusNotFound, gin.H{"error": "Deployment not found"})
		return
	}

	c.JSON(http.StatusOK, deployment)
}

// UpdateDeployment updates a deployment
func (h *Handler) UpdateDeployment(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	deploymentID := c.Param("id")
	var req models.UpdateDeploymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	deployment, err := h.deploymentService.UpdateDeployment(c.Request.Context(), deploymentID, &req)
	if err != nil {
		h.logger.Error("Failed to update deployment", logging.Error(err), logging.String("deploymentId", deploymentID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update deployment"})
		return
	}

	c.JSON(http.StatusOK, deployment)
}

// DeleteDeployment deletes a deployment
func (h *Handler) DeleteDeployment(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	deploymentID := c.Param("id")
	err := h.deploymentService.DeleteDeployment(c.Request.Context(), deploymentID)
	if err != nil {
		h.logger.Error("Failed to delete deployment", logging.Error(err), logging.String("deploymentId", deploymentID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete deployment"})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// CancelDeployment cancels a deployment
func (h *Handler) CancelDeployment(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	deploymentID := c.Param("id")
	err := h.deploymentService.CancelDeployment(c.Request.Context(), deploymentID)
	if err != nil {
		h.logger.Error("Failed to cancel deployment", logging.Error(err), logging.String("deploymentId", deploymentID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel deployment"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Deployment cancelled successfully"})
}

// GetDeploymentStatus gets deployment status
func (h *Handler) GetDeploymentStatus(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	deploymentID := c.Param("id")
	status, err := h.deploymentService.GetDeploymentStatus(c.Request.Context(), deploymentID)
	if err != nil {
		h.logger.Error("Failed to get deployment status", logging.Error(err), logging.String("deploymentId", deploymentID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get deployment status"})
		return
	}

	c.JSON(http.StatusOK, status)
}

// GetDeploymentLogs gets deployment logs
func (h *Handler) GetDeploymentLogs(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	deploymentID := c.Param("id")
	logs, err := h.deploymentService.GetDeploymentLogs(c.Request.Context(), deploymentID)
	if err != nil {
		h.logger.Error("Failed to get deployment logs", logging.Error(err), logging.String("deploymentId", deploymentID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get deployment logs"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"logs": logs})
}

// BulkDeploy performs bulk deployment
func (h *Handler) BulkDeploy(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	var req models.BulkDeploymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	result, err := h.deploymentService.BulkDeploy(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to perform bulk deployment", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to perform bulk deployment"})
		return
	}

	c.JSON(http.StatusCreated, result)
}

// BulkCancel cancels multiple deployments
func (h *Handler) BulkCancel(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	var req models.BulkCancelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	result, err := h.deploymentService.BulkCancel(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to perform bulk cancel", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to perform bulk cancel"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// BulkStatus gets status of multiple deployments
func (h *Handler) BulkStatus(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	deploymentIDs := c.QueryArray("ids")
	if len(deploymentIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No deployment IDs provided"})
		return
	}

	statuses, err := h.deploymentService.BulkStatus(c.Request.Context(), deploymentIDs)
	if err != nil {
		h.logger.Error("Failed to get bulk status", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get bulk status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"statuses": statuses})
}

// GetVersionMatrix gets version matrix for a project
func (h *Handler) GetVersionMatrix(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	projectID := c.Param("projectId")
	matrix, err := h.versionService.GetVersionMatrix(c.Request.Context(), projectID)
	if err != nil {
		h.logger.Error("Failed to get version matrix", logging.Error(err), logging.String("projectId", projectID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get version matrix"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"matrix": matrix})
}

// GetVersionHistory gets version history for an environment
func (h *Handler) GetVersionHistory(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	environmentID := c.Param("environmentId")
	history, err := h.versionService.GetVersionHistory(c.Request.Context(), environmentID)
	if err != nil {
		h.logger.Error("Failed to get version history", logging.Error(err), logging.String("environmentId", environmentID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get version history"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"history": history})
}

// CompareVersions compares versions between environments
func (h *Handler) CompareVersions(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	var req models.CompareVersionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	comparison, err := h.versionService.CompareVersions(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to compare versions", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to compare versions"})
		return
	}

	c.JSON(http.StatusOK, comparison)
}

// CreatePromotion creates a new promotion
func (h *Handler) CreatePromotion(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	var req models.CreatePromotionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	promotion, err := h.promotionService.CreatePromotion(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to create promotion", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create promotion"})
		return
	}

	c.JSON(http.StatusCreated, promotion)
}

// ListPromotions lists promotions
func (h *Handler) ListPromotions(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	filter := models.PromotionFilter{
		ProjectID:         c.Query("projectId"),
		SourceEnvironment: c.Query("sourceEnvironment"),
		TargetEnvironment: c.Query("targetEnvironment"),
		Status:            c.Query("status"),
	}

	promotions, total, err := h.promotionService.ListPromotions(c.Request.Context(), &filter)
	if err != nil {
		h.logger.Error("Failed to list promotions", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list promotions"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"promotions": promotions,
		"total":      total,
	})
}

// GetPromotion gets a specific promotion
func (h *Handler) GetPromotion(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	promotionID := c.Param("id")
	promotion, err := h.promotionService.GetPromotion(c.Request.Context(), promotionID)
	if err != nil {
		h.logger.Error("Failed to get promotion", logging.Error(err), logging.String("promotionId", promotionID))
		c.JSON(http.StatusNotFound, gin.H{"error": "Promotion not found"})
		return
	}

	c.JSON(http.StatusOK, promotion)
}

// ApprovePromotion approves a promotion
func (h *Handler) ApprovePromotion(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	promotionID := c.Param("id")
	err := h.promotionService.ApprovePromotion(c.Request.Context(), promotionID, userID)
	if err != nil {
		h.logger.Error("Failed to approve promotion", logging.Error(err), logging.String("promotionId", promotionID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to approve promotion"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Promotion approved successfully"})
}

// RejectPromotion rejects a promotion
func (h *Handler) RejectPromotion(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	promotionID := c.Param("id")
	var req models.RejectPromotionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := h.promotionService.RejectPromotion(c.Request.Context(), promotionID, userID, req.Reason)
	if err != nil {
		h.logger.Error("Failed to reject promotion", logging.Error(err), logging.String("promotionId", promotionID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to reject promotion"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Promotion rejected successfully"})
}

// CreateRollback creates a new rollback
func (h *Handler) CreateRollback(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	var req models.CreateRollbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	rollback, err := h.rollbackService.CreateRollback(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to create rollback", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create rollback"})
		return
	}

	c.JSON(http.StatusCreated, rollback)
}

// ListRollbacks lists rollbacks
func (h *Handler) ListRollbacks(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	filter := models.RollbackFilter{
		ProjectID:     c.Query("projectId"),
		EnvironmentID: c.Query("environmentId"),
		ApplicationID: c.Query("applicationId"),
		Status:        c.Query("status"),
	}

	rollbacks, total, err := h.rollbackService.ListRollbacks(c.Request.Context(), &filter)
	if err != nil {
		h.logger.Error("Failed to list rollbacks", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list rollbacks"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"rollbacks": rollbacks,
		"total":     total,
	})
}

// GetRollback gets a specific rollback
func (h *Handler) GetRollback(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	rollbackID := c.Param("id")
	rollback, err := h.rollbackService.GetRollback(c.Request.Context(), rollbackID)
	if err != nil {
		h.logger.Error("Failed to get rollback", logging.Error(err), logging.String("rollbackId", rollbackID))
		c.JSON(http.StatusNotFound, gin.H{"error": "Rollback not found"})
		return
	}

	c.JSON(http.StatusOK, rollback)
}

// ExecuteRollback executes a rollback
func (h *Handler) ExecuteRollback(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	rollbackID := c.Param("id")
	err := h.rollbackService.ExecuteRollback(c.Request.Context(), rollbackID, userID)
	if err != nil {
		h.logger.Error("Failed to execute rollback", logging.Error(err), logging.String("rollbackId", rollbackID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to execute rollback"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Rollback executed successfully"})
}

// GetProjectDeployments gets deployments for a specific project
func (h *Handler) GetProjectDeployments(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	projectID := c.Param("projectId")
	filter := models.DeploymentFilter{
		ProjectID: projectID,
	}

	deployments, total, err := h.deploymentService.ListDeployments(c.Request.Context(), &filter)
	if err != nil {
		h.logger.Error("Failed to get project deployments", logging.Error(err), logging.String("projectId", projectID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get project deployments"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"deployments": deployments,
		"total":       total,
	})
}

// GetEnvironmentVersions gets versions for a specific environment
func (h *Handler) GetEnvironmentVersions(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	projectID := c.Param("projectId")
	environmentID := c.Param("environmentId")

	versions, err := h.versionService.GetEnvironmentVersions(c.Request.Context(), projectID, environmentID)
	if err != nil {
		h.logger.Error("Failed to get environment versions", logging.Error(err), logging.String("projectId", projectID), logging.String("environmentId", environmentID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get environment versions"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"versions": versions})
}

// DeployToEnvironment deploys to a specific environment
func (h *Handler) DeployToEnvironment(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	projectID := c.Param("projectId")
	environmentID := c.Param("environmentId")

	var req models.EnvironmentDeploymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.ProjectID = projectID
	req.EnvironmentID = environmentID

	deployment, err := h.deploymentService.DeployToEnvironment(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to deploy to environment", logging.Error(err), logging.String("projectId", projectID), logging.String("environmentId", environmentID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to deploy to environment"})
		return
	}

	c.JSON(http.StatusCreated, deployment)
}

// PromoteEnvironment promotes between environments
func (h *Handler) PromoteEnvironment(c *gin.Context) {
	userID := c.GetString("user_id")
	_ = userID // TODO: Add auth check

	projectID := c.Param("projectId")
	fromEnv := c.Param("fromEnv")
	toEnv := c.Param("toEnv")

	var req models.EnvironmentPromotionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.ProjectID = projectID
	req.SourceEnvironment = fromEnv
	req.TargetEnvironment = toEnv

	promotion, err := h.promotionService.PromoteEnvironment(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to promote environment", logging.Error(err), logging.String("projectId", projectID), logging.String("fromEnv", fromEnv), logging.String("toEnv", toEnv))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to promote environment"})
		return
	}

	c.JSON(http.StatusCreated, promotion)
}
