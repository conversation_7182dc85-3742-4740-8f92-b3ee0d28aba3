# Variables
BINARY_NAME=deployment-service
DOCKER_IMAGE=deployment-service
DOCKER_TAG=latest
GO_VERSION=1.21

# Build commands
.PHONY: build
build:
	go build -o bin/$(BINARY_NAME) .

.PHONY: build-linux
build-linux:
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/$(BINARY_NAME) .

.PHONY: run
run:
	go run .

.PHONY: clean
clean:
	go clean
	rm -f bin/$(BINARY_NAME)

# Testing
.PHONY: test
test:
	go test -v ./...

.PHONY: test-coverage
test-coverage:
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

.PHONY: test-race
test-race:
	go test -race -v ./...

# Linting and formatting
.PHONY: fmt
fmt:
	go fmt ./...

.PHONY: vet
vet:
	go vet ./...

.PHONY: lint
lint:
	golangci-lint run

# Dependencies
.PHONY: deps
deps:
	go mod download
	go mod tidy

.PHONY: deps-update
deps-update:
	go get -u ./...
	go mod tidy

# Docker commands
.PHONY: docker-build
docker-build:
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .

.PHONY: docker-run
docker-run:
	docker run -p 8080:8080 --env-file .env $(DOCKER_IMAGE):$(DOCKER_TAG)

.PHONY: docker-push
docker-push:
	docker push $(DOCKER_IMAGE):$(DOCKER_TAG)

.PHONY: docker-clean
docker-clean:
	docker rmi $(DOCKER_IMAGE):$(DOCKER_TAG)

# Database migrations
.PHONY: migrate-up
migrate-up:
	migrate -path migrations -database "postgres://postgres:postgres@localhost:5432/deployment_service?sslmode=disable" up

.PHONY: migrate-down
migrate-down:
	migrate -path migrations -database "postgres://postgres:postgres@localhost:5432/deployment_service?sslmode=disable" down

.PHONY: migrate-create
migrate-create:
	migrate create -ext sql -dir migrations -seq $(name)

# Development
.PHONY: dev
dev:
	air

.PHONY: install-tools
install-tools:
	go install github.com/cosmtrek/air@latest
	go install github.com/golang-migrate/migrate/v4/cmd/migrate@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# Help
.PHONY: help
help:
	@echo "Available commands:"
	@echo "  build          - Build the binary"
	@echo "  build-linux    - Build the binary for Linux"
	@echo "  run            - Run the application"
	@echo "  clean          - Clean build artifacts"
	@echo "  test           - Run tests"
	@echo "  test-coverage  - Run tests with coverage"
	@echo "  test-race      - Run tests with race detection"
	@echo "  fmt            - Format code"
	@echo "  vet            - Run go vet"
	@echo "  lint           - Run linter"
	@echo "  deps           - Download dependencies"
	@echo "  deps-update    - Update dependencies"
	@echo "  docker-build   - Build Docker image"
	@echo "  docker-run     - Run Docker container"
	@echo "  docker-push    - Push Docker image"
	@echo "  docker-clean   - Remove Docker image"
	@echo "  migrate-up     - Run database migrations up"
	@echo "  migrate-down   - Run database migrations down"
	@echo "  migrate-create - Create new migration"
	@echo "  dev            - Run in development mode with air"
	@echo "  install-tools  - Install development tools"
	@echo "  help           - Show this help message"
