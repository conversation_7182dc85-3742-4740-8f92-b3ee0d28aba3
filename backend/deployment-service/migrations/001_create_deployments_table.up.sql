-- Create deployments table
CREATE TABLE IF NOT EXISTS deployments (
    id VARCHAR(255) PRIMARY KEY,
    project_id VARCHAR(255) NOT NULL,
    environment_id VARCHAR(255) NOT NULL,
    application_id VARCHAR(255),
    workflow_id VARCHAR(255) NOT NULL,
    version VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    strategy VARCHAR(50) NOT NULL DEFAULT 'rolling_update',
    provider VARCHAR(50) NOT NULL DEFAULT 'helm',
    configuration JSONB DEFAULT '{}',
    parameters JSONB DEFAULT '{}',
    secret_mappings JSONB DEFAULT '{}',
    created_by VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error TEXT,
    logs TEXT[]
);

-- <PERSON><PERSON> indexes
CREATE INDEX idx_deployments_project_id ON deployments(project_id);
CREATE INDEX idx_deployments_environment_id ON deployments(environment_id);
CREATE INDEX idx_deployments_application_id ON deployments(application_id);
CREATE INDEX idx_deployments_status ON deployments(status);
CREATE INDEX idx_deployments_created_at ON deployments(created_at);
CREATE INDEX idx_deployments_created_by ON deployments(created_by);

-- Create version_history table
CREATE TABLE IF NOT EXISTS version_history (
    id VARCHAR(255) PRIMARY KEY,
    project_id VARCHAR(255) NOT NULL,
    environment_id VARCHAR(255) NOT NULL,
    application_id VARCHAR(255) NOT NULL,
    application_name VARCHAR(255) NOT NULL,
    version VARCHAR(255) NOT NULL,
    previous_version VARCHAR(255),
    deployment_id VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL,
    deployed_at TIMESTAMP WITH TIME ZONE NOT NULL,
    deployed_by VARCHAR(255) NOT NULL,
    duration INTEGER DEFAULT 0,
    rollback_info JSONB
);

-- Create indexes for version_history
CREATE INDEX idx_version_history_project_id ON version_history(project_id);
CREATE INDEX idx_version_history_environment_id ON version_history(environment_id);
CREATE INDEX idx_version_history_application_id ON version_history(application_id);
CREATE INDEX idx_version_history_deployed_at ON version_history(deployed_at);

-- Create promotions table
CREATE TABLE IF NOT EXISTS promotions (
    id VARCHAR(255) PRIMARY KEY,
    project_id VARCHAR(255) NOT NULL,
    source_environment VARCHAR(255) NOT NULL,
    target_environment VARCHAR(255) NOT NULL,
    application_ids TEXT[] NOT NULL,
    version VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL DEFAULT 'manual',
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    strategy VARCHAR(50) NOT NULL DEFAULT 'rolling_update',
    configuration JSONB DEFAULT '{}',
    approval_required BOOLEAN DEFAULT false,
    approved_by VARCHAR(255),
    approved_at TIMESTAMP WITH TIME ZONE,
    rejected_by VARCHAR(255),
    rejected_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    created_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    executed_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error TEXT,
    deployment_id VARCHAR(255)
);

-- Create indexes for promotions
CREATE INDEX idx_promotions_project_id ON promotions(project_id);
CREATE INDEX idx_promotions_source_environment ON promotions(source_environment);
CREATE INDEX idx_promotions_target_environment ON promotions(target_environment);
CREATE INDEX idx_promotions_status ON promotions(status);
CREATE INDEX idx_promotions_created_at ON promotions(created_at);

-- Create rollbacks table
CREATE TABLE IF NOT EXISTS rollbacks (
    id VARCHAR(255) PRIMARY KEY,
    project_id VARCHAR(255) NOT NULL,
    environment_id VARCHAR(255) NOT NULL,
    application_id VARCHAR(255) NOT NULL,
    deployment_id VARCHAR(255) NOT NULL,
    from_version VARCHAR(255) NOT NULL,
    to_version VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL DEFAULT 'manual',
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    trigger VARCHAR(50) NOT NULL DEFAULT 'manual',
    reason TEXT NOT NULL,
    strategy VARCHAR(50) NOT NULL DEFAULT 'rolling_update',
    configuration JSONB DEFAULT '{}',
    created_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    executed_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error TEXT,
    rollback_deployment_id VARCHAR(255),
    logs TEXT[]
);

-- Create indexes for rollbacks
CREATE INDEX idx_rollbacks_project_id ON rollbacks(project_id);
CREATE INDEX idx_rollbacks_environment_id ON rollbacks(environment_id);
CREATE INDEX idx_rollbacks_application_id ON rollbacks(application_id);
CREATE INDEX idx_rollbacks_deployment_id ON rollbacks(deployment_id);
CREATE INDEX idx_rollbacks_status ON rollbacks(status);
CREATE INDEX idx_rollbacks_created_at ON rollbacks(created_at);

-- Create deployment_logs table
CREATE TABLE IF NOT EXISTS deployment_logs (
    id VARCHAR(255) PRIMARY KEY,
    deployment_id VARCHAR(255) NOT NULL,
    level VARCHAR(20) NOT NULL DEFAULT 'info',
    message TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    source VARCHAR(100) NOT NULL DEFAULT 'deployment-service',
    metadata JSONB DEFAULT '{}'
);

-- Create indexes for deployment_logs
CREATE INDEX idx_deployment_logs_deployment_id ON deployment_logs(deployment_id);
CREATE INDEX idx_deployment_logs_timestamp ON deployment_logs(timestamp);
CREATE INDEX idx_deployment_logs_level ON deployment_logs(level);
