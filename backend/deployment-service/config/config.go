package config

import (
	"os"
	"strconv"

	"shared/config"
)

// Config holds the configuration for the deployment service
type Config struct {
	ServiceName string
	Version     string
	Environment string
	Port        int
	LogLevel    string

	Database   config.DatabaseConfig
	Auth       config.AuthConfig
	Monitoring config.MonitoringConfig

	// Deployment-specific configuration
	Deployment DeploymentConfig
}

// DeploymentConfig holds deployment-specific configuration
type DeploymentConfig struct {
	MaxConcurrentDeployments int
	DefaultTimeout           int // in seconds
	RetryAttempts           int
	RetryDelay              int // in seconds
	
	// Provider configurations
	Providers ProvidersConfig
	
	// Workflow integration
	WorkflowServiceURL string
	
	// Application service integration
	ApplicationServiceURL string
	
	// Environment service integration
	EnvironmentServiceURL string
	
	// Secrets service integration
	SecretsServiceURL string
}

// ProvidersConfig holds configuration for deployment providers
type ProvidersConfig struct {
	Helm       HelmConfig
	Kubernetes KubernetesConfig
	OpenShift  OpenShiftConfig
	Docker     DockerConfig
}

// HelmConfig holds Helm-specific configuration
type HelmConfig struct {
	Enabled        bool
	DefaultTimeout int
	TillerTimeout  int
	MaxHistory     int
}

// KubernetesConfig holds Kubernetes-specific configuration
type KubernetesConfig struct {
	Enabled        bool
	DefaultTimeout int
	MaxRetries     int
}

// OpenShiftConfig holds OpenShift-specific configuration
type OpenShiftConfig struct {
	Enabled        bool
	DefaultTimeout int
	MaxRetries     int
}

// DockerConfig holds Docker-specific configuration
type DockerConfig struct {
	Enabled        bool
	DefaultTimeout int
	MaxRetries     int
	RegistryURL    string
}

// Load loads the configuration from environment variables
func Load() *Config {
	return &Config{
		ServiceName: getEnv("SERVICE_NAME", "deployment-service"),
		Version:     getEnv("SERVICE_VERSION", "1.0.0"),
		Environment: getEnv("ENVIRONMENT", "development"),
		Port:        getEnvAsInt("PORT", 8080),
		LogLevel:    getEnv("LOG_LEVEL", "info"),

		Database: config.DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnvAsInt("DB_PORT", 5432),
			Name:     getEnv("DB_NAME", "deployment_service"),
			User:     getEnv("DB_USER", "postgres"),
			Password: getEnv("DB_PASSWORD", "postgres"),
			SSLMode:  getEnv("DB_SSL_MODE", "disable"),
		},

		Auth: config.AuthConfig{
			ServiceURL:   getEnv("AUTH_SERVICE_URL", "http://localhost:8081"),
			SharedSecret: getEnv("AUTH_SHARED_SECRET", "your-shared-secret"),
		},

		Monitoring: config.MonitoringConfig{
			Enabled:    getEnvAsBool("MONITORING_ENABLED", true),
			MetricsURL: getEnv("METRICS_URL", "/metrics"),
		},

		Deployment: DeploymentConfig{
			MaxConcurrentDeployments: getEnvAsInt("MAX_CONCURRENT_DEPLOYMENTS", 10),
			DefaultTimeout:           getEnvAsInt("DEFAULT_TIMEOUT", 1800), // 30 minutes
			RetryAttempts:           getEnvAsInt("RETRY_ATTEMPTS", 3),
			RetryDelay:              getEnvAsInt("RETRY_DELAY", 30),

			WorkflowServiceURL:    getEnv("WORKFLOW_SERVICE_URL", "http://localhost:8082"),
			ApplicationServiceURL: getEnv("APPLICATION_SERVICE_URL", "http://localhost:8083"),
			EnvironmentServiceURL: getEnv("ENVIRONMENT_SERVICE_URL", "http://localhost:8084"),
			SecretsServiceURL:     getEnv("SECRETS_SERVICE_URL", "http://localhost:8085"),

			Providers: ProvidersConfig{
				Helm: HelmConfig{
					Enabled:        getEnvAsBool("HELM_ENABLED", true),
					DefaultTimeout: getEnvAsInt("HELM_DEFAULT_TIMEOUT", 600),
					TillerTimeout:  getEnvAsInt("HELM_TILLER_TIMEOUT", 300),
					MaxHistory:     getEnvAsInt("HELM_MAX_HISTORY", 10),
				},
				Kubernetes: KubernetesConfig{
					Enabled:        getEnvAsBool("KUBERNETES_ENABLED", true),
					DefaultTimeout: getEnvAsInt("KUBERNETES_DEFAULT_TIMEOUT", 600),
					MaxRetries:     getEnvAsInt("KUBERNETES_MAX_RETRIES", 3),
				},
				OpenShift: OpenShiftConfig{
					Enabled:        getEnvAsBool("OPENSHIFT_ENABLED", false),
					DefaultTimeout: getEnvAsInt("OPENSHIFT_DEFAULT_TIMEOUT", 600),
					MaxRetries:     getEnvAsInt("OPENSHIFT_MAX_RETRIES", 3),
				},
				Docker: DockerConfig{
					Enabled:        getEnvAsBool("DOCKER_ENABLED", true),
					DefaultTimeout: getEnvAsInt("DOCKER_DEFAULT_TIMEOUT", 300),
					MaxRetries:     getEnvAsInt("DOCKER_MAX_RETRIES", 3),
					RegistryURL:    getEnv("DOCKER_REGISTRY_URL", ""),
				},
			},
		},
	}
}

// Helper functions
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}
