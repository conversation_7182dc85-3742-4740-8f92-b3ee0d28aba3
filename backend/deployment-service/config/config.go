package config

import (
	"fmt"

	sharedConfig "github.com/claudio/deploy-orchestrator/shared/config"
)

// Config holds the configuration for the deployment service
type Config struct {
	Service    sharedConfig.ServiceConfig    `mapstructure:"service" yaml:"service"`
	Server     sharedConfig.ServerConfig     `mapstructure:"server" yaml:"server"`
	Database   sharedConfig.DBConfig         `mapstructure:"db" yaml:"db"`
	Logging    sharedConfig.LoggingConfig    `mapstructure:"logging" yaml:"logging"`
	Auth       sharedConfig.AuthConfig       `mapstructure:"auth" yaml:"auth"`
	Gateway    sharedConfig.GatewayConfig    `mapstructure:"gateway" yaml:"gateway"`
	Monitoring sharedConfig.MonitoringConfig `mapstructure:"monitoring" yaml:"monitoring"`

	// Deployment-specific configuration
	Deployment DeploymentConfig `mapstructure:"deployment" yaml:"deployment"`
}

// DeploymentConfig holds deployment-specific configuration
type DeploymentConfig struct {
	MaxConcurrentDeployments int `mapstructure:"max_concurrent_deployments" yaml:"max_concurrent_deployments" env:"MAX_CONCURRENT_DEPLOYMENTS" default:"10"`
	DefaultTimeout           int `mapstructure:"default_timeout" yaml:"default_timeout" env:"DEFAULT_TIMEOUT" default:"1800"`
	RetryAttempts            int `mapstructure:"retry_attempts" yaml:"retry_attempts" env:"RETRY_ATTEMPTS" default:"3"`
	RetryDelay               int `mapstructure:"retry_delay" yaml:"retry_delay" env:"RETRY_DELAY" default:"30"`

	// Provider configurations
	Providers ProvidersConfig `mapstructure:"providers" yaml:"providers"`

	// Workflow integration
	WorkflowServiceURL string `mapstructure:"workflow_service_url" yaml:"workflow_service_url" env:"WORKFLOW_SERVICE_URL" default:"http://localhost:8082"`

	// Application service integration
	ApplicationServiceURL string `mapstructure:"application_service_url" yaml:"application_service_url" env:"APPLICATION_SERVICE_URL" default:"http://localhost:8083"`

	// Environment service integration
	EnvironmentServiceURL string `mapstructure:"environment_service_url" yaml:"environment_service_url" env:"ENVIRONMENT_SERVICE_URL" default:"http://localhost:8084"`

	// Secrets service integration
	SecretsServiceURL string `mapstructure:"secrets_service_url" yaml:"secrets_service_url" env:"SECRETS_SERVICE_URL" default:"http://localhost:8085"`
}

// ProvidersConfig holds configuration for deployment providers
type ProvidersConfig struct {
	Helm       HelmConfig       `mapstructure:"helm" yaml:"helm"`
	Kubernetes KubernetesConfig `mapstructure:"kubernetes" yaml:"kubernetes"`
	OpenShift  OpenShiftConfig  `mapstructure:"openshift" yaml:"openshift"`
	Docker     DockerConfig     `mapstructure:"docker" yaml:"docker"`
}

// HelmConfig holds Helm-specific configuration
type HelmConfig struct {
	Enabled        bool `mapstructure:"enabled" yaml:"enabled" env:"HELM_ENABLED" default:"true"`
	DefaultTimeout int  `mapstructure:"default_timeout" yaml:"default_timeout" env:"HELM_DEFAULT_TIMEOUT" default:"600"`
	TillerTimeout  int  `mapstructure:"tiller_timeout" yaml:"tiller_timeout" env:"HELM_TILLER_TIMEOUT" default:"300"`
	MaxHistory     int  `mapstructure:"max_history" yaml:"max_history" env:"HELM_MAX_HISTORY" default:"10"`
}

// KubernetesConfig holds Kubernetes-specific configuration
type KubernetesConfig struct {
	Enabled        bool `mapstructure:"enabled" yaml:"enabled" env:"KUBERNETES_ENABLED" default:"true"`
	DefaultTimeout int  `mapstructure:"default_timeout" yaml:"default_timeout" env:"KUBERNETES_DEFAULT_TIMEOUT" default:"600"`
	MaxRetries     int  `mapstructure:"max_retries" yaml:"max_retries" env:"KUBERNETES_MAX_RETRIES" default:"3"`
}

// OpenShiftConfig holds OpenShift-specific configuration
type OpenShiftConfig struct {
	Enabled        bool `mapstructure:"enabled" yaml:"enabled" env:"OPENSHIFT_ENABLED" default:"false"`
	DefaultTimeout int  `mapstructure:"default_timeout" yaml:"default_timeout" env:"OPENSHIFT_DEFAULT_TIMEOUT" default:"600"`
	MaxRetries     int  `mapstructure:"max_retries" yaml:"max_retries" env:"OPENSHIFT_MAX_RETRIES" default:"3"`
}

// DockerConfig holds Docker-specific configuration
type DockerConfig struct {
	Enabled        bool   `mapstructure:"enabled" yaml:"enabled" env:"DOCKER_ENABLED" default:"true"`
	DefaultTimeout int    `mapstructure:"default_timeout" yaml:"default_timeout" env:"DOCKER_DEFAULT_TIMEOUT" default:"300"`
	MaxRetries     int    `mapstructure:"max_retries" yaml:"max_retries" env:"DOCKER_MAX_RETRIES" default:"3"`
	RegistryURL    string `mapstructure:"registry_url" yaml:"registry_url" env:"DOCKER_REGISTRY_URL" default:""`
}

// LoadConfig loads configuration from environment variables and config file
func LoadConfig() (*Config, error) {
	cfg := &Config{}

	// Use the shared config loader
	if err := sharedConfig.LoadConfig(cfg, "deployment-service"); err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	return cfg, nil
}
