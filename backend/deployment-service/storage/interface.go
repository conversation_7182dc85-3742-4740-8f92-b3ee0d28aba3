package storage

import (
	"context"

	"deployment-service/models"
)

// Storage defines the interface for data persistence
type Storage interface {
	// Deployment operations
	CreateDeployment(ctx context.Context, deployment *models.Deployment) error
	GetDeployment(ctx context.Context, id string) (*models.Deployment, error)
	UpdateDeployment(ctx context.Context, deployment *models.Deployment) error
	DeleteDeployment(ctx context.Context, id string) error
	ListDeployments(ctx context.Context, filter *models.DeploymentFilter) ([]*models.Deployment, int, error)

	// Version operations
	CreateVersionEntry(ctx context.Context, entry *models.VersionEntry) error
	GetVersionHistory(ctx context.Context, environmentID string) ([]*models.VersionEntry, error)
	GetVersionMatrix(ctx context.Context, projectID string) (map[string]map[string]*models.VersionInfo, error)
	GetEnvironmentVersions(ctx context.Context, projectID, environmentID string) (map[string]*models.VersionInfo, error)

	// Promotion operations
	CreatePromotion(ctx context.Context, promotion *models.Promotion) error
	GetPromotion(ctx context.Context, id string) (*models.Promotion, error)
	UpdatePromotion(ctx context.Context, promotion *models.Promotion) error
	ListPromotions(ctx context.Context, filter *models.PromotionFilter) ([]*models.Promotion, int, error)

	// Rollback operations
	CreateRollback(ctx context.Context, rollback *models.Rollback) error
	GetRollback(ctx context.Context, id string) (*models.Rollback, error)
	UpdateRollback(ctx context.Context, rollback *models.Rollback) error
	ListRollbacks(ctx context.Context, filter *models.RollbackFilter) ([]*models.Rollback, int, error)

	// Deployment logs
	CreateDeploymentLog(ctx context.Context, log *models.DeploymentLog) error
	GetDeploymentLogs(ctx context.Context, deploymentID string) ([]*models.DeploymentLog, error)

	// Metrics and analytics
	GetDeploymentMetrics(ctx context.Context, projectID string, timeRange string) (*models.DeploymentMetrics, error)
	GetVersionMetrics(ctx context.Context, projectID string) (*models.VersionMetrics, error)
	GetPromotionMetrics(ctx context.Context, projectID string) (*models.PromotionMetrics, error)
	GetRollbackMetrics(ctx context.Context, projectID string) (*models.RollbackMetrics, error)
}
