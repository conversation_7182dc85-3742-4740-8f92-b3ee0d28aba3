package storage

import (
	"context"
	"database/sql"
	"encoding/json"

	"github.com/claudio/deploy-orchestrator/deployment-service/models"
	"github.com/claudio/deploy-orchestrator/shared/logging"
)

// PostgresStorage implements the Storage interface using PostgreSQL
type PostgresStorage struct {
	db     *sql.DB
	logger logging.Logger
}

// NewPostgresStorage creates a new PostgreSQL storage instance
func NewPostgresStorage(db *sql.DB) *PostgresStorage {
	return &PostgresStorage{
		db:     db,
		logger: logging.Default().Named("postgres-storage"),
	}
}

// Deployment operations
func (s *PostgresStorage) CreateDeployment(ctx context.Context, deployment *models.Deployment) error {
	query := `
		INSERT INTO deployments (
			id, project_id, environment_id, application_id, workflow_id,
			version, status, strategy, provider, configuration, parameters,
			secret_mappings, created_by, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)`

	configJSON, _ := json.Marshal(deployment.Configuration)
	paramsJSON, _ := json.Marshal(deployment.Parameters)
	secretsJSON, _ := json.Marshal(deployment.SecretMappings)

	_, err := s.db.ExecContext(ctx, query,
		deployment.ID, deployment.ProjectID, deployment.EnvironmentID,
		deployment.ApplicationID, deployment.WorkflowID, deployment.Version,
		deployment.Status, deployment.Strategy, deployment.Provider,
		configJSON, paramsJSON, secretsJSON, deployment.CreatedBy,
		deployment.CreatedAt, deployment.UpdatedAt,
	)

	return err
}

func (s *PostgresStorage) GetDeployment(ctx context.Context, id string) (*models.Deployment, error) {
	query := `
		SELECT id, project_id, environment_id, application_id, workflow_id,
			   version, status, strategy, provider, configuration, parameters,
			   secret_mappings, created_by, created_at, updated_at,
			   started_at, completed_at, error
		FROM deployments WHERE id = $1`

	var deployment models.Deployment
	var configJSON, paramsJSON, secretsJSON []byte
	var startedAt, completedAt sql.NullTime
	var errorMsg sql.NullString

	err := s.db.QueryRowContext(ctx, query, id).Scan(
		&deployment.ID, &deployment.ProjectID, &deployment.EnvironmentID,
		&deployment.ApplicationID, &deployment.WorkflowID, &deployment.Version,
		&deployment.Status, &deployment.Strategy, &deployment.Provider,
		&configJSON, &paramsJSON, &secretsJSON, &deployment.CreatedBy,
		&deployment.CreatedAt, &deployment.UpdatedAt, &startedAt, &completedAt, &errorMsg,
	)

	if err != nil {
		return nil, err
	}

	// Unmarshal JSON fields
	json.Unmarshal(configJSON, &deployment.Configuration)
	json.Unmarshal(paramsJSON, &deployment.Parameters)
	json.Unmarshal(secretsJSON, &deployment.SecretMappings)

	if startedAt.Valid {
		deployment.StartedAt = &startedAt.Time
	}
	if completedAt.Valid {
		deployment.CompletedAt = &completedAt.Time
	}
	if errorMsg.Valid {
		deployment.Error = &errorMsg.String
	}

	return &deployment, nil
}

func (s *PostgresStorage) UpdateDeployment(ctx context.Context, deployment *models.Deployment) error {
	query := `
		UPDATE deployments SET
			status = $2, configuration = $3, parameters = $4,
			updated_at = $5, started_at = $6, completed_at = $7, error = $8
		WHERE id = $1`

	configJSON, _ := json.Marshal(deployment.Configuration)
	paramsJSON, _ := json.Marshal(deployment.Parameters)

	_, err := s.db.ExecContext(ctx, query,
		deployment.ID, deployment.Status, configJSON, paramsJSON,
		deployment.UpdatedAt, deployment.StartedAt, deployment.CompletedAt, deployment.Error,
	)

	return err
}

func (s *PostgresStorage) DeleteDeployment(ctx context.Context, id string) error {
	query := `DELETE FROM deployments WHERE id = $1`
	_, err := s.db.ExecContext(ctx, query, id)
	return err
}

func (s *PostgresStorage) ListDeployments(ctx context.Context, filter *models.DeploymentFilter) ([]*models.Deployment, int, error) {
	// Build WHERE clause based on filter
	whereClause := "WHERE 1=1"
	args := []interface{}{}
	argIndex := 1

	if filter.ProjectID != "" {
		whereClause += " AND project_id = $" + string(rune(argIndex))
		args = append(args, filter.ProjectID)
		argIndex++
	}

	if filter.EnvironmentID != "" {
		whereClause += " AND environment_id = $" + string(rune(argIndex))
		args = append(args, filter.EnvironmentID)
		argIndex++
	}

	if filter.ApplicationID != "" {
		whereClause += " AND application_id = $" + string(rune(argIndex))
		args = append(args, filter.ApplicationID)
		argIndex++
	}

	if filter.Status != "" {
		whereClause += " AND status = $" + string(rune(argIndex))
		args = append(args, filter.Status)
		argIndex++
	}

	if filter.UserID != "" {
		whereClause += " AND created_by = $" + string(rune(argIndex))
		args = append(args, filter.UserID)
		argIndex++
	}

	// Count total records
	countQuery := "SELECT COUNT(*) FROM deployments " + whereClause
	var total int
	err := s.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	// Get paginated results
	query := `
		SELECT id, project_id, environment_id, application_id, workflow_id,
			   version, status, strategy, provider, configuration, parameters,
			   secret_mappings, created_by, created_at, updated_at,
			   started_at, completed_at, error
		FROM deployments ` + whereClause + `
		ORDER BY created_at DESC
		LIMIT $` + string(rune(argIndex)) + ` OFFSET $` + string(rune(argIndex+1))

	limit := filter.Limit
	if limit <= 0 {
		limit = 50
	}
	offset := filter.Offset
	if offset < 0 {
		offset = 0
	}

	args = append(args, limit, offset)

	rows, err := s.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var deployments []*models.Deployment
	for rows.Next() {
		var deployment models.Deployment
		var configJSON, paramsJSON, secretsJSON []byte
		var startedAt, completedAt sql.NullTime
		var errorMsg sql.NullString

		err := rows.Scan(
			&deployment.ID, &deployment.ProjectID, &deployment.EnvironmentID,
			&deployment.ApplicationID, &deployment.WorkflowID, &deployment.Version,
			&deployment.Status, &deployment.Strategy, &deployment.Provider,
			&configJSON, &paramsJSON, &secretsJSON, &deployment.CreatedBy,
			&deployment.CreatedAt, &deployment.UpdatedAt, &startedAt, &completedAt, &errorMsg,
		)
		if err != nil {
			return nil, 0, err
		}

		// Unmarshal JSON fields
		json.Unmarshal(configJSON, &deployment.Configuration)
		json.Unmarshal(paramsJSON, &deployment.Parameters)
		json.Unmarshal(secretsJSON, &deployment.SecretMappings)

		if startedAt.Valid {
			deployment.StartedAt = &startedAt.Time
		}
		if completedAt.Valid {
			deployment.CompletedAt = &completedAt.Time
		}
		if errorMsg.Valid {
			deployment.Error = &errorMsg.String
		}

		deployments = append(deployments, &deployment)
	}

	return deployments, total, nil
}

// Version operations
func (s *PostgresStorage) CreateVersionEntry(ctx context.Context, entry *models.VersionEntry) error {
	query := `
		INSERT INTO version_history (
			id, project_id, environment_id, application_id, application_name,
			version, previous_version, deployment_id, status, deployed_at,
			deployed_by, duration
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)`

	_, err := s.db.ExecContext(ctx, query,
		entry.ID, entry.ProjectID, entry.EnvironmentID, entry.ApplicationID,
		entry.ApplicationName, entry.Version, entry.PreviousVersion,
		entry.DeploymentID, entry.Status, entry.DeployedAt, entry.DeployedBy,
		entry.Duration,
	)

	return err
}

func (s *PostgresStorage) GetVersionHistory(ctx context.Context, environmentID string) ([]*models.VersionEntry, error) {
	query := `
		SELECT id, project_id, environment_id, application_id, application_name,
			   version, previous_version, deployment_id, status, deployed_at,
			   deployed_by, duration
		FROM version_history 
		WHERE environment_id = $1
		ORDER BY deployed_at DESC`

	rows, err := s.db.QueryContext(ctx, query, environmentID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var entries []*models.VersionEntry
	for rows.Next() {
		var entry models.VersionEntry
		err := rows.Scan(
			&entry.ID, &entry.ProjectID, &entry.EnvironmentID,
			&entry.ApplicationID, &entry.ApplicationName, &entry.Version,
			&entry.PreviousVersion, &entry.DeploymentID, &entry.Status,
			&entry.DeployedAt, &entry.DeployedBy, &entry.Duration,
		)
		if err != nil {
			return nil, err
		}
		entries = append(entries, &entry)
	}

	return entries, nil
}

// GetVersionMatrix gets version matrix for a project
func (s *PostgresStorage) GetVersionMatrix(ctx context.Context, projectID string) (map[string]map[string]*models.VersionInfo, error) {
	// TODO: Implement version matrix retrieval
	return make(map[string]map[string]*models.VersionInfo), nil
}

// GetEnvironmentVersions gets versions for a specific environment
func (s *PostgresStorage) GetEnvironmentVersions(ctx context.Context, projectID, environmentID string) (map[string]*models.VersionInfo, error) {
	// TODO: Implement environment versions retrieval
	return make(map[string]*models.VersionInfo), nil
}

// CreatePromotion creates a new promotion
func (s *PostgresStorage) CreatePromotion(ctx context.Context, promotion *models.Promotion) error {
	// TODO: Implement promotion creation
	return nil
}

// GetPromotion gets a promotion by ID
func (s *PostgresStorage) GetPromotion(ctx context.Context, id string) (*models.Promotion, error) {
	// TODO: Implement promotion retrieval
	return nil, nil
}

// UpdatePromotion updates a promotion
func (s *PostgresStorage) UpdatePromotion(ctx context.Context, promotion *models.Promotion) error {
	// TODO: Implement promotion update
	return nil
}

// ListPromotions lists promotions with filtering
func (s *PostgresStorage) ListPromotions(ctx context.Context, filter *models.PromotionFilter) ([]*models.Promotion, int, error) {
	// TODO: Implement promotion listing
	return []*models.Promotion{}, 0, nil
}

// CreateRollback creates a new rollback
func (s *PostgresStorage) CreateRollback(ctx context.Context, rollback *models.Rollback) error {
	// TODO: Implement rollback creation
	return nil
}

// GetRollback gets a rollback by ID
func (s *PostgresStorage) GetRollback(ctx context.Context, id string) (*models.Rollback, error) {
	// TODO: Implement rollback retrieval
	return nil, nil
}

// UpdateRollback updates a rollback
func (s *PostgresStorage) UpdateRollback(ctx context.Context, rollback *models.Rollback) error {
	// TODO: Implement rollback update
	return nil
}

// ListRollbacks lists rollbacks with filtering
func (s *PostgresStorage) ListRollbacks(ctx context.Context, filter *models.RollbackFilter) ([]*models.Rollback, int, error) {
	// TODO: Implement rollback listing
	return []*models.Rollback{}, 0, nil
}

// CreateDeploymentLog creates a deployment log entry
func (s *PostgresStorage) CreateDeploymentLog(ctx context.Context, log *models.DeploymentLog) error {
	// TODO: Implement deployment log creation
	return nil
}

// GetDeploymentLogs gets deployment logs
func (s *PostgresStorage) GetDeploymentLogs(ctx context.Context, deploymentID string) ([]*models.DeploymentLog, error) {
	// TODO: Implement deployment logs retrieval
	return []*models.DeploymentLog{}, nil
}

// GetDeploymentMetrics gets deployment metrics
func (s *PostgresStorage) GetDeploymentMetrics(ctx context.Context, projectID string, timeRange string) (*models.DeploymentMetrics, error) {
	// TODO: Implement deployment metrics retrieval
	return nil, nil
}

// GetVersionMetrics gets version metrics
func (s *PostgresStorage) GetVersionMetrics(ctx context.Context, projectID string) (*models.VersionMetrics, error) {
	// TODO: Implement version metrics retrieval
	return nil, nil
}

// GetPromotionMetrics gets promotion metrics
func (s *PostgresStorage) GetPromotionMetrics(ctx context.Context, projectID string) (*models.PromotionMetrics, error) {
	// TODO: Implement promotion metrics retrieval
	return nil, nil
}

// GetRollbackMetrics gets rollback metrics
func (s *PostgresStorage) GetRollbackMetrics(ctx context.Context, projectID string) (*models.RollbackMetrics, error) {
	// TODO: Implement rollback metrics retrieval
	return nil, nil
}
