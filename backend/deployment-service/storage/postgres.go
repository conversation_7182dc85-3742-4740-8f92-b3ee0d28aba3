package storage

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

	"deployment-service/models"
	"shared/logging"

	"github.com/lib/pq"
)

// PostgresStorage implements the Storage interface using PostgreSQL
type PostgresStorage struct {
	db     *sql.DB
	logger *logging.Logger
}

// NewPostgresStorage creates a new PostgreSQL storage instance
func NewPostgresStorage(db *sql.DB) *PostgresStorage {
	return &PostgresStorage{
		db:     db,
		logger: logging.NewLogger("info", "postgres-storage"),
	}
}

// Deployment operations
func (s *PostgresStorage) CreateDeployment(ctx context.Context, deployment *models.Deployment) error {
	query := `
		INSERT INTO deployments (
			id, project_id, environment_id, application_id, workflow_id,
			version, status, strategy, provider, configuration, parameters,
			secret_mappings, created_by, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)`

	configJ<PERSON><PERSON>, _ := json.Marshal(deployment.Configuration)
	paramsJSON, _ := json.Marshal(deployment.Parameters)
	secretsJSON, _ := json.Marshal(deployment.SecretMappings)

	_, err := s.db.ExecContext(ctx, query,
		deployment.ID, deployment.ProjectID, deployment.EnvironmentID,
		deployment.ApplicationID, deployment.WorkflowID, deployment.Version,
		deployment.Status, deployment.Strategy, deployment.Provider,
		configJSON, paramsJSON, secretsJSON, deployment.CreatedBy,
		deployment.CreatedAt, deployment.UpdatedAt,
	)

	return err
}

func (s *PostgresStorage) GetDeployment(ctx context.Context, id string) (*models.Deployment, error) {
	query := `
		SELECT id, project_id, environment_id, application_id, workflow_id,
			   version, status, strategy, provider, configuration, parameters,
			   secret_mappings, created_by, created_at, updated_at,
			   started_at, completed_at, error
		FROM deployments WHERE id = $1`

	var deployment models.Deployment
	var configJSON, paramsJSON, secretsJSON []byte
	var startedAt, completedAt sql.NullTime
	var errorMsg sql.NullString

	err := s.db.QueryRowContext(ctx, query, id).Scan(
		&deployment.ID, &deployment.ProjectID, &deployment.EnvironmentID,
		&deployment.ApplicationID, &deployment.WorkflowID, &deployment.Version,
		&deployment.Status, &deployment.Strategy, &deployment.Provider,
		&configJSON, &paramsJSON, &secretsJSON, &deployment.CreatedBy,
		&deployment.CreatedAt, &deployment.UpdatedAt, &startedAt, &completedAt, &errorMsg,
	)

	if err != nil {
		return nil, err
	}

	// Unmarshal JSON fields
	json.Unmarshal(configJSON, &deployment.Configuration)
	json.Unmarshal(paramsJSON, &deployment.Parameters)
	json.Unmarshal(secretsJSON, &deployment.SecretMappings)

	if startedAt.Valid {
		deployment.StartedAt = &startedAt.Time
	}
	if completedAt.Valid {
		deployment.CompletedAt = &completedAt.Time
	}
	if errorMsg.Valid {
		deployment.Error = &errorMsg.String
	}

	return &deployment, nil
}

func (s *PostgresStorage) UpdateDeployment(ctx context.Context, deployment *models.Deployment) error {
	query := `
		UPDATE deployments SET
			status = $2, configuration = $3, parameters = $4,
			updated_at = $5, started_at = $6, completed_at = $7, error = $8
		WHERE id = $1`

	configJSON, _ := json.Marshal(deployment.Configuration)
	paramsJSON, _ := json.Marshal(deployment.Parameters)

	_, err := s.db.ExecContext(ctx, query,
		deployment.ID, deployment.Status, configJSON, paramsJSON,
		deployment.UpdatedAt, deployment.StartedAt, deployment.CompletedAt, deployment.Error,
	)

	return err
}

func (s *PostgresStorage) DeleteDeployment(ctx context.Context, id string) error {
	query := `DELETE FROM deployments WHERE id = $1`
	_, err := s.db.ExecContext(ctx, query, id)
	return err
}

func (s *PostgresStorage) ListDeployments(ctx context.Context, filter *models.DeploymentFilter) ([]*models.Deployment, int, error) {
	// Build WHERE clause based on filter
	whereClause := "WHERE 1=1"
	args := []interface{}{}
	argIndex := 1

	if filter.ProjectID != "" {
		whereClause += " AND project_id = $" + string(rune(argIndex))
		args = append(args, filter.ProjectID)
		argIndex++
	}

	if filter.EnvironmentID != "" {
		whereClause += " AND environment_id = $" + string(rune(argIndex))
		args = append(args, filter.EnvironmentID)
		argIndex++
	}

	if filter.ApplicationID != "" {
		whereClause += " AND application_id = $" + string(rune(argIndex))
		args = append(args, filter.ApplicationID)
		argIndex++
	}

	if filter.Status != "" {
		whereClause += " AND status = $" + string(rune(argIndex))
		args = append(args, filter.Status)
		argIndex++
	}

	if filter.UserID != "" {
		whereClause += " AND created_by = $" + string(rune(argIndex))
		args = append(args, filter.UserID)
		argIndex++
	}

	// Count total records
	countQuery := "SELECT COUNT(*) FROM deployments " + whereClause
	var total int
	err := s.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	// Get paginated results
	query := `
		SELECT id, project_id, environment_id, application_id, workflow_id,
			   version, status, strategy, provider, configuration, parameters,
			   secret_mappings, created_by, created_at, updated_at,
			   started_at, completed_at, error
		FROM deployments ` + whereClause + `
		ORDER BY created_at DESC
		LIMIT $` + string(rune(argIndex)) + ` OFFSET $` + string(rune(argIndex+1))

	limit := filter.Limit
	if limit <= 0 {
		limit = 50
	}
	offset := filter.Offset
	if offset < 0 {
		offset = 0
	}

	args = append(args, limit, offset)

	rows, err := s.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var deployments []*models.Deployment
	for rows.Next() {
		var deployment models.Deployment
		var configJSON, paramsJSON, secretsJSON []byte
		var startedAt, completedAt sql.NullTime
		var errorMsg sql.NullString

		err := rows.Scan(
			&deployment.ID, &deployment.ProjectID, &deployment.EnvironmentID,
			&deployment.ApplicationID, &deployment.WorkflowID, &deployment.Version,
			&deployment.Status, &deployment.Strategy, &deployment.Provider,
			&configJSON, &paramsJSON, &secretsJSON, &deployment.CreatedBy,
			&deployment.CreatedAt, &deployment.UpdatedAt, &startedAt, &completedAt, &errorMsg,
		)
		if err != nil {
			return nil, 0, err
		}

		// Unmarshal JSON fields
		json.Unmarshal(configJSON, &deployment.Configuration)
		json.Unmarshal(paramsJSON, &deployment.Parameters)
		json.Unmarshal(secretsJSON, &deployment.SecretMappings)

		if startedAt.Valid {
			deployment.StartedAt = &startedAt.Time
		}
		if completedAt.Valid {
			deployment.CompletedAt = &completedAt.Time
		}
		if errorMsg.Valid {
			deployment.Error = &errorMsg.String
		}

		deployments = append(deployments, &deployment)
	}

	return deployments, total, nil
}

// Version operations
func (s *PostgresStorage) CreateVersionEntry(ctx context.Context, entry *models.VersionEntry) error {
	query := `
		INSERT INTO version_history (
			id, project_id, environment_id, application_id, application_name,
			version, previous_version, deployment_id, status, deployed_at,
			deployed_by, duration
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)`

	_, err := s.db.ExecContext(ctx, query,
		entry.ID, entry.ProjectID, entry.EnvironmentID, entry.ApplicationID,
		entry.ApplicationName, entry.Version, entry.PreviousVersion,
		entry.DeploymentID, entry.Status, entry.DeployedAt, entry.DeployedBy,
		entry.Duration,
	)

	return err
}

func (s *PostgresStorage) GetVersionHistory(ctx context.Context, environmentID string) ([]*models.VersionEntry, error) {
	query := `
		SELECT id, project_id, environment_id, application_id, application_name,
			   version, previous_version, deployment_id, status, deployed_at,
			   deployed_by, duration
		FROM version_history 
		WHERE environment_id = $1
		ORDER BY deployed_at DESC`

	rows, err := s.db.QueryContext(ctx, query, environmentID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var entries []*models.VersionEntry
	for rows.Next() {
		var entry models.VersionEntry
		err := rows.Scan(
			&entry.ID, &entry.ProjectID, &entry.EnvironmentID,
			&entry.ApplicationID, &entry.ApplicationName, &entry.Version,
			&entry.PreviousVersion, &entry.DeploymentID, &entry.Status,
			&entry.DeployedAt, &entry.DeployedBy, &entry.Duration,
		)
		if err != nil {
			return nil, err
		}
		entries = append(entries, &entry)
	}

	return entries, nil
}
