service:
  name: "deployment-service"
  version: "1.0.0"

server:
  port: 8090
  host: "0.0.0.0"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 60

db:
  url: "postgres://postgres:postgres@localhost:5432/deployment_service?sslmode=disable"
  max_retries: 5
  retry_interval: 3
  max_open_conns: 25
  max_idle_conns: 10
  query_timeout: 10
  log_level: "info"

auth:
  admin_service_url: "http://localhost:8081"
  jwt_secret: "default-secret-key-in-production"
  jwt_expiration_minutes: 60
  disable: false

logging:
  level: "info"
  format: "json"
  output: "stdout"

monitoring:
  enabled: true
  port: 9090
  path: "/metrics"

gateway:
  enabled: true
  url: "http://localhost:8080"
  environment: "development"

deployment:
  max_concurrent_deployments: 10
  default_timeout: 1800  # 30 minutes in seconds
  retry_attempts: 3
  retry_delay: 30  # seconds
  
  # Service URLs for integration
  workflow_service_url: "http://localhost:8082"
  application_service_url: "http://localhost:8083"
  environment_service_url: "http://localhost:8084"
  secrets_service_url: "http://localhost:8085"
  
  # Provider configurations
  providers:
    helm:
      enabled: true
      default_timeout: 600  # 10 minutes
      tiller_timeout: 300   # 5 minutes
      max_history: 10
      
    kubernetes:
      enabled: true
      default_timeout: 600  # 10 minutes
      max_retries: 3
      
    openshift:
      enabled: false
      default_timeout: 600  # 10 minutes
      max_retries: 3
      
    docker:
      enabled: true
      default_timeout: 300  # 5 minutes
      max_retries: 3
      registry_url: ""

# Admin permissions configuration
admin:
  permissions:
    - "deployment:create"
    - "deployment:read"
    - "deployment:update"
    - "deployment:delete"
    - "deployment:cancel"
    - "deployment:rollback"
    - "promotion:create"
    - "promotion:approve"
    - "promotion:reject"
    - "version:view"
    - "version:compare"
    - "rollback:create"
    - "rollback:execute"

# Environment-specific permissions
environment_permissions:
  development:
    - "deployment:create"
    - "deployment:read"
    - "deployment:cancel"
    - "version:view"
    
  staging:
    - "deployment:create"
    - "deployment:read"
    - "deployment:cancel"
    - "promotion:create"
    - "version:view"
    - "version:compare"
    
  production:
    - "deployment:read"
    - "promotion:approve"
    - "rollback:create"
    - "rollback:execute"
    - "version:view"

# Rate limiting
rate_limiting:
  enabled: true
  requests_per_minute: 100
  burst_size: 20

# CORS settings
cors:
  enabled: true
  allowed_origins:
    - "http://localhost:4200"
    - "http://localhost:3000"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"

# Security settings
security:
  enable_https: false
  cert_file: ""
  key_file: ""
  trusted_proxies: []
