package models

import (
	"time"
)

// DeploymentStatus represents the status of a deployment
type DeploymentStatus string

const (
	DeploymentStatusPending    DeploymentStatus = "pending"
	DeploymentStatusRunning    DeploymentStatus = "running"
	DeploymentStatusSucceeded  DeploymentStatus = "succeeded"
	DeploymentStatusFailed     DeploymentStatus = "failed"
	DeploymentStatusCancelled  DeploymentStatus = "cancelled"
	DeploymentStatusRolledBack DeploymentStatus = "rolled_back"
)

// DeploymentStrategy represents the deployment strategy
type DeploymentStrategy string

const (
	StrategyRollingUpdate DeploymentStrategy = "rolling_update"
	StrategyBlueGreen     DeploymentStrategy = "blue_green"
	StrategyCanary        DeploymentStrategy = "canary"
	StrategyRecreate      DeploymentStrategy = "recreate"
)

// ProviderType represents the deployment provider type
type ProviderType string

const (
	ProviderHelm       ProviderType = "helm"
	ProviderKubernetes ProviderType = "kubernetes"
	ProviderOpenShift  ProviderType = "openshift"
	ProviderDocker     ProviderType = "docker"
)

// Deployment represents a deployment
type Deployment struct {
	ID             string                 `json:"id" db:"id"`
	ProjectID      string                 `json:"projectId" db:"project_id"`
	EnvironmentID  string                 `json:"environmentId" db:"environment_id"`
	ApplicationID  string                 `json:"applicationId" db:"application_id"`
	WorkflowID     string                 `json:"workflowId" db:"workflow_id"`
	Version        string                 `json:"version" db:"version"`
	Status         DeploymentStatus       `json:"status" db:"status"`
	Strategy       DeploymentStrategy     `json:"strategy" db:"strategy"`
	Provider       ProviderType           `json:"provider" db:"provider"`
	Configuration  map[string]interface{} `json:"configuration" db:"configuration"`
	Parameters     map[string]interface{} `json:"parameters" db:"parameters"`
	SecretMappings map[string]string      `json:"secretMappings" db:"secret_mappings"`
	CreatedBy      string                 `json:"createdBy" db:"created_by"`
	CreatedAt      time.Time              `json:"createdAt" db:"created_at"`
	UpdatedAt      time.Time              `json:"updatedAt" db:"updated_at"`
	StartedAt      *time.Time             `json:"startedAt" db:"started_at"`
	CompletedAt    *time.Time             `json:"completedAt" db:"completed_at"`
	Error          *string                `json:"error" db:"error"`
	Logs           []string               `json:"logs" db:"logs"`
}

// CreateDeploymentRequest represents a request to create a deployment
type CreateDeploymentRequest struct {
	ProjectID      string                 `json:"projectId" binding:"required"`
	ApplicationIDs []string               `json:"applicationIds" binding:"required"`
	EnvironmentID  string                 `json:"environmentId" binding:"required"`
	WorkflowID     string                 `json:"workflowId" binding:"required"`
	Version        string                 `json:"version" binding:"required"`
	Strategy       DeploymentStrategy     `json:"strategy"`
	Provider       ProviderType           `json:"provider"`
	Configuration  map[string]interface{} `json:"configuration"`
	Parameters     map[string]interface{} `json:"parameters"`
	SecretMappings map[string]string      `json:"secretMappings"`
}

// UpdateDeploymentRequest represents a request to update a deployment
type UpdateDeploymentRequest struct {
	Status        *DeploymentStatus      `json:"status"`
	Configuration map[string]interface{} `json:"configuration"`
	Parameters    map[string]interface{} `json:"parameters"`
	Error         *string                `json:"error"`
}

// DeploymentFilter represents filters for listing deployments
type DeploymentFilter struct {
	ProjectID     string `json:"projectId"`
	EnvironmentID string `json:"environmentId"`
	ApplicationID string `json:"applicationId"`
	Status        string `json:"status"`
	UserID        string `json:"userId"`
	Limit         int    `json:"limit"`
	Offset        int    `json:"offset"`
}

// BulkDeploymentRequest represents a request for bulk deployment
type BulkDeploymentRequest struct {
	ProjectID      string                 `json:"projectId" binding:"required"`
	ApplicationIDs []string               `json:"applicationIds" binding:"required"`
	EnvironmentID  string                 `json:"environmentId" binding:"required"`
	WorkflowID     string                 `json:"workflowId" binding:"required"`
	Version        string                 `json:"version" binding:"required"`
	Strategy       DeploymentStrategy     `json:"strategy"`
	Provider       ProviderType           `json:"provider"`
	Configuration  map[string]interface{} `json:"configuration"`
	Parameters     map[string]interface{} `json:"parameters"`
	SecretMappings map[string]string      `json:"secretMappings"`
}

// BulkDeploymentResult represents the result of a bulk deployment
type BulkDeploymentResult struct {
	DeploymentID string            `json:"deploymentId"`
	Deployments  []Deployment      `json:"deployments"`
	Succeeded    []string          `json:"succeeded"`
	Failed       []string          `json:"failed"`
	Errors       map[string]string `json:"errors"`
}

// BulkCancelRequest represents a request to cancel multiple deployments
type BulkCancelRequest struct {
	DeploymentIDs []string `json:"deploymentIds" binding:"required"`
}

// BulkCancelResult represents the result of bulk cancellation
type BulkCancelResult struct {
	Succeeded []string          `json:"succeeded"`
	Failed    []string          `json:"failed"`
	Errors    map[string]string `json:"errors"`
}

// DeploymentStatus represents the status of a deployment
type DeploymentStatusInfo struct {
	ID          string           `json:"id"`
	Status      DeploymentStatus `json:"status"`
	Progress    int              `json:"progress"`
	Message     string           `json:"message"`
	StartedAt   *time.Time       `json:"startedAt"`
	CompletedAt *time.Time       `json:"completedAt"`
	Error       *string          `json:"error"`
	LastUpdated time.Time        `json:"lastUpdated"`
}

// EnvironmentDeploymentRequest represents a request to deploy to an environment
type EnvironmentDeploymentRequest struct {
	ProjectID      string                 `json:"projectId"`
	EnvironmentID  string                 `json:"environmentId"`
	ApplicationIDs []string               `json:"applicationIds" binding:"required"`
	Version        string                 `json:"version" binding:"required"`
	WorkflowID     string                 `json:"workflowId" binding:"required"`
	Strategy       DeploymentStrategy     `json:"strategy"`
	Provider       ProviderType           `json:"provider"`
	Configuration  map[string]interface{} `json:"configuration"`
	Parameters     map[string]interface{} `json:"parameters"`
	SecretMappings map[string]string      `json:"secretMappings"`
}

// DeploymentLog represents a deployment log entry
type DeploymentLog struct {
	ID           string                 `json:"id" db:"id"`
	DeploymentID string                 `json:"deploymentId" db:"deployment_id"`
	Level        string                 `json:"level" db:"level"`
	Message      string                 `json:"message" db:"message"`
	Timestamp    time.Time              `json:"timestamp" db:"timestamp"`
	Source       string                 `json:"source" db:"source"`
	Metadata     map[string]interface{} `json:"metadata" db:"metadata"`
}

// DeploymentMetrics represents deployment metrics
type DeploymentMetrics struct {
	DeploymentID     string        `json:"deploymentId"`
	Duration         time.Duration `json:"duration"`
	ResourceUsage    ResourceUsage `json:"resourceUsage"`
	SuccessRate      float64       `json:"successRate"`
	ErrorRate        float64       `json:"errorRate"`
	ThroughputPerMin int           `json:"throughputPerMin"`
}

// ResourceUsage represents resource usage metrics
type ResourceUsage struct {
	CPU     float64 `json:"cpu"`
	Memory  float64 `json:"memory"`
	Disk    float64 `json:"disk"`
	Network float64 `json:"network"`
}
