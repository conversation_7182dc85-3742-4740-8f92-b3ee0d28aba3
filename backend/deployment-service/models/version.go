package models

import (
	"time"
)

// VersionMatrix represents the version matrix for a project
type VersionMatrix struct {
	ProjectID    string                         `json:"projectId"`
	Applications map[string]ApplicationVersions `json:"applications"`
	Environments []string                       `json:"environments"`
	GeneratedAt  time.Time                      `json:"generatedAt"`
}

// ApplicationVersions represents versions of an application across environments
type ApplicationVersions struct {
	ApplicationID   string                 `json:"applicationId"`
	ApplicationName string                 `json:"applicationName"`
	Environments    map[string]VersionInfo `json:"environments"`
}

// VersionInfo represents version information for an application in an environment
type VersionInfo struct {
	Version     string    `json:"version"`
	DeployedAt  time.Time `json:"deployedAt"`
	DeployedBy  string    `json:"deployedBy"`
	Status      string    `json:"status"`
	Health      string    `json:"health"`
	Instances   int       `json:"instances"`
	LastChecked time.Time `json:"lastChecked"`
}

// VersionHistory represents the version history for an environment
type VersionHistory struct {
	EnvironmentID string         `json:"environmentId"`
	ProjectID     string         `json:"projectId"`
	Versions      []VersionEntry `json:"versions"`
	Total         int            `json:"total"`
}

// VersionEntry represents a single version entry in history
type VersionEntry struct {
	ID              string        `json:"id" db:"id"`
	ProjectID       string        `json:"projectId" db:"project_id"`
	EnvironmentID   string        `json:"environmentId" db:"environment_id"`
	ApplicationID   string        `json:"applicationId" db:"application_id"`
	ApplicationName string        `json:"applicationName" db:"application_name"`
	Version         string        `json:"version" db:"version"`
	PreviousVersion string        `json:"previousVersion" db:"previous_version"`
	DeploymentID    string        `json:"deploymentId" db:"deployment_id"`
	Status          string        `json:"status" db:"status"`
	DeployedAt      time.Time     `json:"deployedAt" db:"deployed_at"`
	DeployedBy      string        `json:"deployedBy" db:"deployed_by"`
	Duration        int           `json:"duration" db:"duration"` // in seconds
	RollbackInfo    *RollbackInfo `json:"rollbackInfo" db:"rollback_info"`
}

// RollbackInfo represents rollback information
type RollbackInfo struct {
	RolledBackAt time.Time `json:"rolledBackAt"`
	RolledBackBy string    `json:"rolledBackBy"`
	Reason       string    `json:"reason"`
	ToVersion    string    `json:"toVersion"`
}

// CompareVersionsRequest represents a request to compare versions
type CompareVersionsRequest struct {
	ProjectID         string   `json:"projectId" binding:"required"`
	SourceEnvironment string   `json:"sourceEnvironment" binding:"required"`
	TargetEnvironment string   `json:"targetEnvironment" binding:"required"`
	ApplicationIDs    []string `json:"applicationIds"`
}

// VersionComparison represents the result of version comparison
type VersionComparison struct {
	ProjectID         string                  `json:"projectId"`
	SourceEnvironment string                  `json:"sourceEnvironment"`
	TargetEnvironment string                  `json:"targetEnvironment"`
	Applications      []ApplicationComparison `json:"applications"`
	Summary           ComparisonSummary       `json:"summary"`
	GeneratedAt       time.Time               `json:"generatedAt"`
}

// ApplicationComparison represents version comparison for a single application
type ApplicationComparison struct {
	ApplicationID   string           `json:"applicationId"`
	ApplicationName string           `json:"applicationName"`
	SourceVersion   VersionInfo      `json:"sourceVersion"`
	TargetVersion   VersionInfo      `json:"targetVersion"`
	Status          ComparisonStatus `json:"status"`
	CanPromote      bool             `json:"canPromote"`
	Differences     []string         `json:"differences"`
}

// ComparisonStatus represents the status of version comparison
type ComparisonStatus string

const (
	ComparisonStatusSame     ComparisonStatus = "same"
	ComparisonStatusNewer    ComparisonStatus = "newer"
	ComparisonStatusOlder    ComparisonStatus = "older"
	ComparisonStatusMissing  ComparisonStatus = "missing"
	ComparisonStatusConflict ComparisonStatus = "conflict"
)

// ComparisonSummary represents a summary of version comparison
type ComparisonSummary struct {
	TotalApplications int  `json:"totalApplications"`
	Same              int  `json:"same"`
	Newer             int  `json:"newer"`
	Older             int  `json:"older"`
	Missing           int  `json:"missing"`
	Conflicts         int  `json:"conflicts"`
	CanPromoteAll     bool `json:"canPromoteAll"`
}

// EnvironmentVersions represents versions for a specific environment
type EnvironmentVersions struct {
	ProjectID     string                 `json:"projectId"`
	EnvironmentID string                 `json:"environmentId"`
	Applications  map[string]VersionInfo `json:"applications"`
	LastUpdated   time.Time              `json:"lastUpdated"`
}

// VersionDrift represents version drift between environments
type VersionDrift struct {
	ProjectID           string               `json:"projectId"`
	SourceEnvironment   string               `json:"sourceEnvironment"`
	TargetEnvironment   string               `json:"targetEnvironment"`
	DriftedApplications []DriftedApplication `json:"driftedApplications"`
	DriftScore          float64              `json:"driftScore"` // 0-100, higher means more drift
	LastCalculated      time.Time            `json:"lastCalculated"`
}

// DriftedApplication represents an application with version drift
type DriftedApplication struct {
	ApplicationID   string `json:"applicationId"`
	ApplicationName string `json:"applicationName"`
	SourceVersion   string `json:"sourceVersion"`
	TargetVersion   string `json:"targetVersion"`
	DriftDays       int    `json:"driftDays"`
	Severity        string `json:"severity"` // low, medium, high, critical
}

// VersionTag represents a version tag
type VersionTag struct {
	ID          string    `json:"id" db:"id"`
	ProjectID   string    `json:"projectId" db:"project_id"`
	Version     string    `json:"version" db:"version"`
	Tag         string    `json:"tag" db:"tag"`
	Description string    `json:"description" db:"description"`
	CreatedBy   string    `json:"createdBy" db:"created_by"`
	CreatedAt   time.Time `json:"createdAt" db:"created_at"`
}

// CreateVersionTagRequest represents a request to create a version tag
type CreateVersionTagRequest struct {
	ProjectID   string `json:"projectId" binding:"required"`
	Version     string `json:"version" binding:"required"`
	Tag         string `json:"tag" binding:"required"`
	Description string `json:"description"`
}

// VersionMetrics represents version-related metrics
type VersionMetrics struct {
	ProjectID              string    `json:"projectId"`
	TotalVersions          int       `json:"totalVersions"`
	ActiveVersions         int       `json:"activeVersions"`
	AverageDeploymentTime  int       `json:"averageDeploymentTime"` // in seconds
	SuccessfulDeployments  int       `json:"successfulDeployments"`
	FailedDeployments      int       `json:"failedDeployments"`
	RollbackRate           float64   `json:"rollbackRate"`
	LastDeployment         time.Time `json:"lastDeployment"`
	MostActiveEnvironment  string    `json:"mostActiveEnvironment"`
	LeastActiveEnvironment string    `json:"leastActiveEnvironment"`
}
