package models

import (
	"time"
)

// RollbackStatus represents the status of a rollback
type RollbackStatus string

const (
	RollbackStatusPending   RollbackStatus = "pending"
	RollbackStatusExecuting RollbackStatus = "executing"
	RollbackStatusCompleted RollbackStatus = "completed"
	RollbackStatusFailed    RollbackStatus = "failed"
	RollbackStatusCancelled RollbackStatus = "cancelled"
)

// RollbackType represents the type of rollback
type RollbackType string

const (
	RollbackTypeManual    RollbackType = "manual"
	RollbackTypeAutomatic RollbackType = "automatic"
	RollbackTypeEmergency RollbackType = "emergency"
)

// RollbackTrigger represents what triggered the rollback
type RollbackTrigger string

const (
	TriggerManual      RollbackTrigger = "manual"
	TriggerHealthCheck RollbackTrigger = "health_check"
	TriggerMetrics     RollbackTrigger = "metrics"
	TriggerErrorRate   RollbackTrigger = "error_rate"
	TriggerPerformance RollbackTrigger = "performance"
)

// Rollback represents a rollback operation
type Rollback struct {
	ID                   string                 `json:"id" db:"id"`
	ProjectID            string                 `json:"projectId" db:"project_id"`
	EnvironmentID        string                 `json:"environmentId" db:"environment_id"`
	ApplicationID        string                 `json:"applicationId" db:"application_id"`
	DeploymentID         string                 `json:"deploymentId" db:"deployment_id"`
	FromVersion          string                 `json:"fromVersion" db:"from_version"`
	ToVersion            string                 `json:"toVersion" db:"to_version"`
	Type                 RollbackType           `json:"type" db:"type"`
	Status               RollbackStatus         `json:"status" db:"status"`
	Trigger              RollbackTrigger        `json:"trigger" db:"trigger"`
	Reason               string                 `json:"reason" db:"reason"`
	Strategy             DeploymentStrategy     `json:"strategy" db:"strategy"`
	Configuration        map[string]interface{} `json:"configuration" db:"configuration"`
	CreatedBy            string                 `json:"createdBy" db:"created_by"`
	CreatedAt            time.Time              `json:"createdAt" db:"created_at"`
	UpdatedAt            time.Time              `json:"updatedAt" db:"updated_at"`
	ExecutedAt           *time.Time             `json:"executedAt" db:"executed_at"`
	CompletedAt          *time.Time             `json:"completedAt" db:"completed_at"`
	Error                *string                `json:"error" db:"error"`
	RollbackDeploymentID *string                `json:"rollbackDeploymentId" db:"rollback_deployment_id"`
	Logs                 []string               `json:"logs" db:"logs"`
}

// CreateRollbackRequest represents a request to create a rollback
type CreateRollbackRequest struct {
	ProjectID     string                 `json:"projectId" binding:"required"`
	EnvironmentID string                 `json:"environmentId" binding:"required"`
	ApplicationID string                 `json:"applicationId" binding:"required"`
	DeploymentID  string                 `json:"deploymentId" binding:"required"`
	ToVersion     string                 `json:"toVersion" binding:"required"`
	Type          RollbackType           `json:"type"`
	Reason        string                 `json:"reason" binding:"required"`
	Strategy      DeploymentStrategy     `json:"strategy"`
	Configuration map[string]interface{} `json:"configuration"`
}

// RollbackFilter represents filters for listing rollbacks
type RollbackFilter struct {
	ProjectID     string `json:"projectId"`
	EnvironmentID string `json:"environmentId"`
	ApplicationID string `json:"applicationId"`
	Status        string `json:"status"`
	Type          string `json:"type"`
	Trigger       string `json:"trigger"`
	Limit         int    `json:"limit"`
	Offset        int    `json:"offset"`
}

// RollbackPlan represents a rollback plan
type RollbackPlan struct {
	ID                string             `json:"id"`
	ProjectID         string             `json:"projectId"`
	EnvironmentID     string             `json:"environmentId"`
	ApplicationID     string             `json:"applicationId"`
	CurrentVersion    string             `json:"currentVersion"`
	TargetVersion     string             `json:"targetVersion"`
	AvailableVersions []AvailableVersion `json:"availableVersions"`
	EstimatedDuration time.Duration      `json:"estimatedDuration"`
	Risks             []RollbackRisk     `json:"risks"`
	Prerequisites     []string           `json:"prerequisites"`
	Steps             []RollbackStep     `json:"steps"`
	CreatedAt         time.Time          `json:"createdAt"`
}

// AvailableVersion represents a version available for rollback
type AvailableVersion struct {
	Version     string    `json:"version"`
	DeployedAt  time.Time `json:"deployedAt"`
	DeployedBy  string    `json:"deployedBy"`
	Status      string    `json:"status"`
	Stability   string    `json:"stability"` // stable, unstable, unknown
	CanRollback bool      `json:"canRollback"`
}

// RollbackRisk represents a risk in rollback
type RollbackRisk struct {
	Type        string `json:"type"`
	Severity    string `json:"severity"` // low, medium, high, critical
	Description string `json:"description"`
	Mitigation  string `json:"mitigation"`
}

// RollbackStep represents a step in rollback process
type RollbackStep struct {
	ID          string        `json:"id"`
	Name        string        `json:"name"`
	Description string        `json:"description"`
	Order       int           `json:"order"`
	Duration    time.Duration `json:"duration"`
	Required    bool          `json:"required"`
	Automated   bool          `json:"automated"`
}

// RollbackValidation represents rollback validation
type RollbackValidation struct {
	CanRollback   bool          `json:"canRollback"`
	Reasons       []string      `json:"reasons"`
	Warnings      []string      `json:"warnings"`
	Prerequisites []string      `json:"prerequisites"`
	EstimatedTime time.Duration `json:"estimatedTime"`
	DataLossRisk  bool          `json:"dataLossRisk"`
	DowntimeRisk  bool          `json:"downtimeRisk"`
}

// AutoRollbackConfig represents automatic rollback configuration
type AutoRollbackConfig struct {
	ID             string                  `json:"id" db:"id"`
	ProjectID      string                  `json:"projectId" db:"project_id"`
	EnvironmentID  string                  `json:"environmentId" db:"environment_id"`
	ApplicationID  string                  `json:"applicationId" db:"application_id"`
	Enabled        bool                    `json:"enabled" db:"enabled"`
	Triggers       []AutoRollbackTrigger   `json:"triggers" db:"triggers"`
	Conditions     []AutoRollbackCondition `json:"conditions" db:"conditions"`
	MaxAttempts    int                     `json:"maxAttempts" db:"max_attempts"`
	CooldownPeriod time.Duration           `json:"cooldownPeriod" db:"cooldown_period"`
	CreatedBy      string                  `json:"createdBy" db:"created_by"`
	CreatedAt      time.Time               `json:"createdAt" db:"created_at"`
	UpdatedAt      time.Time               `json:"updatedAt" db:"updated_at"`
}

// AutoRollbackTrigger represents an automatic rollback trigger
type AutoRollbackTrigger struct {
	Type      RollbackTrigger `json:"type"`
	Enabled   bool            `json:"enabled"`
	Threshold float64         `json:"threshold"`
	Duration  time.Duration   `json:"duration"`
}

// AutoRollbackCondition represents a condition for automatic rollback
type AutoRollbackCondition struct {
	Metric   string        `json:"metric"`
	Operator string        `json:"operator"` // gt, lt, eq, gte, lte
	Value    float64       `json:"value"`
	Duration time.Duration `json:"duration"`
}

// RollbackMetrics represents rollback metrics
type RollbackMetrics struct {
	ProjectID           string         `json:"projectId"`
	TotalRollbacks      int            `json:"totalRollbacks"`
	SuccessfulRollbacks int            `json:"successfulRollbacks"`
	FailedRollbacks     int            `json:"failedRollbacks"`
	AutomaticRollbacks  int            `json:"automaticRollbacks"`
	ManualRollbacks     int            `json:"manualRollbacks"`
	AverageRollbackTime int            `json:"averageRollbackTime"` // in seconds
	SuccessRate         float64        `json:"successRate"`
	LastRollback        time.Time      `json:"lastRollback"`
	MostRolledBackApp   string         `json:"mostRolledBackApp"`
	CommonTriggers      map[string]int `json:"commonTriggers"`
}
