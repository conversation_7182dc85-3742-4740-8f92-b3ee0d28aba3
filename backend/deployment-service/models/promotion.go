package models

import (
	"time"
)

// PromotionStatus represents the status of a promotion
type PromotionStatus string

const (
	PromotionStatusPending   PromotionStatus = "pending"
	PromotionStatusApproved  PromotionStatus = "approved"
	PromotionStatusRejected  PromotionStatus = "rejected"
	PromotionStatusExecuting PromotionStatus = "executing"
	PromotionStatusCompleted PromotionStatus = "completed"
	PromotionStatusFailed    PromotionStatus = "failed"
	PromotionStatusCancelled PromotionStatus = "cancelled"
)

// PromotionType represents the type of promotion
type PromotionType string

const (
	PromotionTypeManual    PromotionType = "manual"
	PromotionTypeAutomatic PromotionType = "automatic"
	PromotionTypeScheduled PromotionType = "scheduled"
)

// Promotion represents a promotion between environments
type Promotion struct {
	ID                string            `json:"id" db:"id"`
	ProjectID         string            `json:"projectId" db:"project_id"`
	SourceEnvironment string            `json:"sourceEnvironment" db:"source_environment"`
	TargetEnvironment string            `json:"targetEnvironment" db:"target_environment"`
	ApplicationIDs    []string          `json:"applicationIds" db:"application_ids"`
	Version           string            `json:"version" db:"version"`
	Type              PromotionType     `json:"type" db:"type"`
	Status            PromotionStatus   `json:"status" db:"status"`
	Strategy          DeploymentStrategy `json:"strategy" db:"strategy"`
	Configuration     map[string]interface{} `json:"configuration" db:"configuration"`
	ApprovalRequired  bool              `json:"approvalRequired" db:"approval_required"`
	ApprovedBy        *string           `json:"approvedBy" db:"approved_by"`
	ApprovedAt        *time.Time        `json:"approvedAt" db:"approved_at"`
	RejectedBy        *string           `json:"rejectedBy" db:"rejected_by"`
	RejectedAt        *time.Time        `json:"rejectedAt" db:"rejected_at"`
	RejectionReason   *string           `json:"rejectionReason" db:"rejection_reason"`
	CreatedBy         string            `json:"createdBy" db:"created_by"`
	CreatedAt         time.Time         `json:"createdAt" db:"created_at"`
	UpdatedAt         time.Time         `json:"updatedAt" db:"updated_at"`
	ExecutedAt        *time.Time        `json:"executedAt" db:"executed_at"`
	CompletedAt       *time.Time        `json:"completedAt" db:"completed_at"`
	Error             *string           `json:"error" db:"error"`
	DeploymentID      *string           `json:"deploymentId" db:"deployment_id"`
}

// CreatePromotionRequest represents a request to create a promotion
type CreatePromotionRequest struct {
	ProjectID         string                 `json:"projectId" binding:"required"`
	SourceEnvironment string                 `json:"sourceEnvironment" binding:"required"`
	TargetEnvironment string                 `json:"targetEnvironment" binding:"required"`
	ApplicationIDs    []string               `json:"applicationIds" binding:"required"`
	Version           string                 `json:"version" binding:"required"`
	Type              PromotionType          `json:"type"`
	Strategy          DeploymentStrategy     `json:"strategy"`
	Configuration     map[string]interface{} `json:"configuration"`
	ApprovalRequired  bool                   `json:"approvalRequired"`
	ScheduledAt       *time.Time             `json:"scheduledAt"`
}

// PromotionFilter represents filters for listing promotions
type PromotionFilter struct {
	ProjectID         string `json:"projectId"`
	SourceEnvironment string `json:"sourceEnvironment"`
	TargetEnvironment string `json:"targetEnvironment"`
	Status            string `json:"status"`
	Type              string `json:"type"`
	Limit             int    `json:"limit"`
	Offset            int    `json:"offset"`
}

// RejectPromotionRequest represents a request to reject a promotion
type RejectPromotionRequest struct {
	Reason string `json:"reason" binding:"required"`
}

// EnvironmentPromotionRequest represents a request to promote between environments
type EnvironmentPromotionRequest struct {
	ProjectID         string                 `json:"projectId"`
	SourceEnvironment string                 `json:"sourceEnvironment"`
	TargetEnvironment string                 `json:"targetEnvironment"`
	ApplicationIDs    []string               `json:"applicationIds" binding:"required"`
	Strategy          DeploymentStrategy     `json:"strategy"`
	Configuration     map[string]interface{} `json:"configuration"`
	ApprovalRequired  bool                   `json:"approvalRequired"`
}

// PromotionPlan represents a promotion plan
type PromotionPlan struct {
	ID                string                    `json:"id"`
	ProjectID         string                    `json:"projectId"`
	SourceEnvironment string                    `json:"sourceEnvironment"`
	TargetEnvironment string                    `json:"targetEnvironment"`
	Applications      []PromotionPlanApplication `json:"applications"`
	EstimatedDuration time.Duration             `json:"estimatedDuration"`
	Risks             []PromotionRisk           `json:"risks"`
	Prerequisites     []string                  `json:"prerequisites"`
	CreatedAt         time.Time                 `json:"createdAt"`
}

// PromotionPlanApplication represents an application in a promotion plan
type PromotionPlanApplication struct {
	ApplicationID   string    `json:"applicationId"`
	ApplicationName string    `json:"applicationName"`
	CurrentVersion  string    `json:"currentVersion"`
	TargetVersion   string    `json:"targetVersion"`
	Changes         []string  `json:"changes"`
	Dependencies    []string  `json:"dependencies"`
	EstimatedTime   time.Duration `json:"estimatedTime"`
}

// PromotionRisk represents a risk in promotion
type PromotionRisk struct {
	Type        string `json:"type"`
	Severity    string `json:"severity"` // low, medium, high, critical
	Description string `json:"description"`
	Mitigation  string `json:"mitigation"`
}

// PromotionApproval represents a promotion approval
type PromotionApproval struct {
	ID           string    `json:"id" db:"id"`
	PromotionID  string    `json:"promotionId" db:"promotion_id"`
	ApproverID   string    `json:"approverId" db:"approver_id"`
	Status       string    `json:"status" db:"status"` // approved, rejected, pending
	Comments     string    `json:"comments" db:"comments"`
	ApprovedAt   time.Time `json:"approvedAt" db:"approved_at"`
}

// PromotionMetrics represents promotion metrics
type PromotionMetrics struct {
	ProjectID              string    `json:"projectId"`
	TotalPromotions        int       `json:"totalPromotions"`
	SuccessfulPromotions   int       `json:"successfulPromotions"`
	FailedPromotions       int       `json:"failedPromotions"`
	PendingPromotions      int       `json:"pendingPromotions"`
	AveragePromotionTime   int       `json:"averagePromotionTime"` // in seconds
	SuccessRate            float64   `json:"successRate"`
	LastPromotion          time.Time `json:"lastPromotion"`
	MostPromotedApp        string    `json:"mostPromotedApp"`
	MostActiveEnvironment  string    `json:"mostActiveEnvironment"`
}
