#!/bin/bash

# Fix logging calls with error and one string parameter
find . -name "*.go" -exec sed -i '' 's/h\.logger\.Error("\([^"]*\)", logging\.Error(err), "\([^"]*\)", \([^)]*\))/h.logger.Error("\1", logging.Error(err), logging.String("\2", \3))/g' {} \;

# Fix logging calls with error and two string parameters  
find . -name "*.go" -exec sed -i '' 's/h\.logger\.Error("\([^"]*\)", logging\.Error(err), logging\.String("\([^"]*\)", \([^)]*\)), "\([^"]*\)", \([^)]*\))/h.logger.Error("\1", logging.Error(err), logging.String("\2", \3), logging.String("\4", \5))/g' {} \;

# Fix logging calls with error and three string parameters
find . -name "*.go" -exec sed -i '' 's/h\.logger\.Error("\([^"]*\)", logging\.Error(err), logging\.String("\([^"]*\)", \([^)]*\)), logging\.String("\([^"]*\)", \([^)]*\)), "\([^"]*\)", \([^)]*\))/h.logger.Error("\1", logging.Error(err), logging.String("\2", \3), logging.String("\4", \5), logging.String("\6", \7))/g' {} \;

echo "Fixed logging calls"
