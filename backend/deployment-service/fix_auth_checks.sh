#!/bin/bash

# Fix all broken auth check patterns
sed -i '' '/^[[:space:]]*userID := c\.GetString("user_id")$/,/^[[:space:]]*}$/{
    /^[[:space:]]*userID := c\.GetString("user_id")$/!{
        /^[[:space:]]*}$/!d
    }
}' api/handler.go

# Add proper auth check pattern after userID lines
sed -i '' 's/userID := c\.GetString("user_id")/userID := c.GetString("user_id")\
_ = userID \/\/ TODO: Add auth check/g' api/handler.go

echo "Fixed auth checks"
