package main

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/claudio/deploy-orchestrator/gateway-service/config"
	"github.com/claudio/deploy-orchestrator/gateway-service/discovery"
	"github.com/claudio/deploy-orchestrator/gateway-service/middleware"
	"github.com/claudio/deploy-orchestrator/gateway-service/proxy"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
)

func TestGatewayServiceIntegration(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create test logger
	logger, err := zap.NewDevelopment()
	require.NoError(t, err)

	// Create test configuration
	cfg := &config.GatewayServiceConfig{
		Gateway: config.GatewayConfig{
			Discovery: config.DiscoveryConfig{
				Method:              "memory",
				HealthCheckInterval: 30,
				HealthCheckTimeout:  5,
				ServiceTTL:          60,
			},
			LoadBalancer: config.LoadBalancerConfig{
				Algorithm:          "round_robin",
				HealthCheckEnabled: true,
			},
			RateLimit: config.RateLimitConfig{
				Enabled:           true,
				RequestsPerMinute: 100,
				BurstSize:         10,
			},
			CircuitBreaker: config.CircuitBreakerConfig{
				Enabled:          true,
				FailureThreshold: 5,
				RecoveryTimeout:  60,
				SuccessThreshold: 3,
			},
			Timeouts: config.TimeoutConfig{
				RequestTimeout:    30,
				ConnectionTimeout: 10,
				IdleTimeout:       60,
			},
		},
	}

	// Initialize components
	registry := discovery.NewMemoryRegistry(
		logger,
		time.Duration(cfg.Gateway.Discovery.HealthCheckInterval)*time.Second,
		time.Duration(cfg.Gateway.Discovery.HealthCheckTimeout)*time.Second,
	)

	loadBalancer := proxy.NewLoadBalancer(
		proxy.LoadBalancerType(cfg.Gateway.LoadBalancer.Algorithm),
		logger,
	)

	rateLimiter := middleware.NewRateLimiter(
		cfg.Gateway.RateLimit.RequestsPerMinute,
		cfg.Gateway.RateLimit.BurstSize,
		logger,
	)
	defer rateLimiter.Stop()

	circuitBreaker := middleware.NewCircuitBreaker(
		cfg.Gateway.CircuitBreaker.FailureThreshold,
		time.Duration(cfg.Gateway.CircuitBreaker.RecoveryTimeout)*time.Second,
		cfg.Gateway.CircuitBreaker.SuccessThreshold,
		logger,
	)

	// Start registry
	err = registry.Start()
	require.NoError(t, err)
	defer registry.Stop()

	// Register test service
	testInstance := &discovery.ServiceInstance{
		ID:      "test-service-1",
		Name:    "test-service",
		URL:     "http://localhost:9999",
		Version: "1.0.0",
		Tags:    []string{"test"},
		Healthy: true,
		Weight:  1,
	}

	err = registry.Register(testInstance)
	require.NoError(t, err)

	// Create test router
	router := gin.New()

	// Add basic middleware
	router.Use(gin.Recovery())

	// Add gateway health endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   "gateway-service",
			"timestamp": time.Now().UTC(),
		})
	})

	// Test service discovery endpoints
	router.GET("/gateway/services", func(c *gin.Context) {
		services, err := registry.GetServices()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"services": services})
	})

	router.GET("/gateway/services/:name", func(c *gin.Context) {
		serviceName := c.Param("name")
		service, err := registry.GetService(serviceName)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Service not found"})
			return
		}
		c.JSON(http.StatusOK, gin.H{"service": service})
	})

	// Test load balancer endpoints
	router.GET("/gateway/load-balancer/metrics", func(c *gin.Context) {
		metrics := loadBalancer.GetMetrics()
		connections := loadBalancer.GetConnectionCounts()
		c.JSON(http.StatusOK, gin.H{
			"metrics":     metrics,
			"connections": connections,
		})
	})

	// Test rate limiter endpoints
	router.GET("/gateway/rate-limiter/stats", func(c *gin.Context) {
		stats := rateLimiter.GetStats()
		c.JSON(http.StatusOK, stats)
	})

	// Test circuit breaker endpoints
	router.GET("/gateway/circuit-breaker/status", func(c *gin.Context) {
		status := circuitBreaker.GetCircuitStatus()
		c.JSON(http.StatusOK, gin.H{"circuits": status})
	})

	// Run tests
	t.Run("Health Check", func(t *testing.T) {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/health", nil)
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "healthy", response["status"])
		assert.Equal(t, "gateway-service", response["service"])
	})

	t.Run("Service Discovery - List Services", func(t *testing.T) {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/gateway/services", nil)
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		services, ok := response["services"].(map[string]interface{})
		assert.True(t, ok)
		assert.Contains(t, services, "test-service")
	})

	t.Run("Service Discovery - Get Specific Service", func(t *testing.T) {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/gateway/services/test-service", nil)
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		service, ok := response["service"].(map[string]interface{})
		assert.True(t, ok)
		assert.Equal(t, "test-service", service["name"])
	})

	t.Run("Service Discovery - Service Not Found", func(t *testing.T) {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/gateway/services/nonexistent-service", nil)
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Load Balancer Metrics", func(t *testing.T) {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/gateway/load-balancer/metrics", nil)
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "metrics")
		assert.Contains(t, response, "connections")
	})

	t.Run("Rate Limiter Stats", func(t *testing.T) {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/gateway/rate-limiter/stats", nil)
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "total_clients")
		assert.Contains(t, response, "requests_per_minute")
	})

	t.Run("Circuit Breaker Status", func(t *testing.T) {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/gateway/circuit-breaker/status", nil)
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "circuits")
	})
}

func TestLoadBalancerSelection(t *testing.T) {
	logger, err := zap.NewDevelopment()
	require.NoError(t, err)

	loadBalancer := proxy.NewLoadBalancer(proxy.RoundRobin, logger)

	// Create test instances
	instances := []*discovery.ServiceInstance{
		{
			ID:      "instance-1",
			Name:    "test-service",
			URL:     "http://localhost:8001",
			Healthy: true,
			Weight:  1,
		},
		{
			ID:      "instance-2",
			Name:    "test-service",
			URL:     "http://localhost:8002",
			Healthy: true,
			Weight:  1,
		},
		{
			ID:      "instance-3",
			Name:    "test-service",
			URL:     "http://localhost:8003",
			Healthy: false, // Unhealthy instance
			Weight:  1,
		},
	}

	// Test round-robin selection
	selected1, err := loadBalancer.SelectInstance("test-service", instances)
	assert.NoError(t, err)
	assert.NotNil(t, selected1)
	assert.True(t, selected1.Healthy)

	selected2, err := loadBalancer.SelectInstance("test-service", instances)
	assert.NoError(t, err)
	assert.NotNil(t, selected2)
	assert.True(t, selected2.Healthy)

	// Should not select the same instance twice in a row (round-robin)
	assert.NotEqual(t, selected1.ID, selected2.ID)

	// Test with no healthy instances
	unhealthyInstances := []*discovery.ServiceInstance{
		{
			ID:      "instance-1",
			Name:    "test-service",
			URL:     "http://localhost:8001",
			Healthy: false,
		},
	}

	_, err = loadBalancer.SelectInstance("test-service", unhealthyInstances)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "no healthy instances available")
}

func TestRateLimiterTokenBucket(t *testing.T) {
	logger, err := zap.NewDevelopment()
	require.NoError(t, err)

	// Create rate limiter with very low limits for testing
	rateLimiter := middleware.NewRateLimiter(2, 2, logger) // 2 requests per minute, burst of 2
	defer rateLimiter.Stop()

	router := gin.New()
	router.Use(rateLimiter.RateLimitMiddleware())
	router.GET("/test", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// First request should succeed
	w1 := httptest.NewRecorder()
	req1, _ := http.NewRequest("GET", "/test", nil)
	req1.RemoteAddr = "127.0.0.1:12345"
	router.ServeHTTP(w1, req1)
	assert.Equal(t, http.StatusOK, w1.Code)

	// Second request should succeed (within burst)
	w2 := httptest.NewRecorder()
	req2, _ := http.NewRequest("GET", "/test", nil)
	req2.RemoteAddr = "127.0.0.1:12345"
	router.ServeHTTP(w2, req2)
	assert.Equal(t, http.StatusOK, w2.Code)

	// Third request should be rate limited
	w3 := httptest.NewRecorder()
	req3, _ := http.NewRequest("GET", "/test", nil)
	req3.RemoteAddr = "127.0.0.1:12345"
	router.ServeHTTP(w3, req3)
	assert.Equal(t, http.StatusTooManyRequests, w3.Code)
}
