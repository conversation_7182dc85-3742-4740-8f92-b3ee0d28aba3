package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/claudio/deploy-orchestrator/gateway-service/api"
	"github.com/claudio/deploy-orchestrator/gateway-service/config"
	"github.com/claudio/deploy-orchestrator/gateway-service/discovery"
	"github.com/claudio/deploy-orchestrator/gateway-service/middleware"
	"github.com/claudio/deploy-orchestrator/gateway-service/proxy"
	wsHandler "github.com/claudio/deploy-orchestrator/gateway-service/websocket"
	"github.com/claudio/deploy-orchestrator/shared/auth"
	"github.com/claudio/deploy-orchestrator/shared/monitoring"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

const (
	serviceName = "gateway-service"
	version     = "1.0.0"
)

func main() {
	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	// Initialize logger
	logger, err := zap.NewProduction()
	if err != nil {
		fmt.Printf("Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer logger.Sync()

	logger.Info("Starting gateway service",
		zap.String("service", serviceName),
		zap.String("version", version),
		zap.Int("port", cfg.Server.Port))

	// Initialize monitoring system
	monitoringManager := monitoring.NewMonitoringManager(serviceName, version, "production")
	logger.Info("Monitoring system initialized")

	// Initialize authentication manager
	authManager, err := auth.NewAuthManagerFromSharedConfig(&cfg.Auth)
	if err != nil {
		logger.Fatal("Failed to initialize auth manager", zap.Error(err))
	}
	logger.Info("Authentication manager initialized")

	// Initialize service registry
	var registry discovery.ServiceRegistry
	switch cfg.Gateway.Discovery.Method {
	case "memory":
		registry = discovery.NewMemoryRegistry(
			logger,
			time.Duration(cfg.Gateway.Discovery.HealthCheckInterval)*time.Second,
			time.Duration(cfg.Gateway.Discovery.HealthCheckTimeout)*time.Second,
		)
	default:
		logger.Fatal("Unsupported discovery method", zap.String("method", cfg.Gateway.Discovery.Method))
	}

	// Register static services
	if err := registerStaticServices(registry, cfg, logger); err != nil {
		logger.Fatal("Failed to register static services", zap.Error(err))
	}

	// Start service registry
	if err := registry.Start(); err != nil {
		logger.Fatal("Failed to start service registry", zap.Error(err))
	}
	defer registry.Stop()

	// Initialize load balancer
	loadBalancer := proxy.NewLoadBalancer(
		proxy.LoadBalancerType(cfg.Gateway.LoadBalancer.Algorithm),
		logger,
	)

	// Initialize rate limiter
	rateLimiter := middleware.NewRateLimiter(
		cfg.Gateway.RateLimit.RequestsPerMinute,
		cfg.Gateway.RateLimit.BurstSize,
		logger,
	)
	defer rateLimiter.Stop()

	// Initialize circuit breaker
	circuitBreaker := middleware.NewCircuitBreaker(
		cfg.Gateway.CircuitBreaker.FailureThreshold,
		time.Duration(cfg.Gateway.CircuitBreaker.RecoveryTimeout)*time.Second,
		cfg.Gateway.CircuitBreaker.SuccessThreshold,
		logger,
	)

	// Initialize proxy handler
	proxyHandler := proxy.NewProxyHandler(
		registry,
		loadBalancer,
		logger,
		time.Duration(cfg.Gateway.Timeouts.RequestTimeout)*time.Second,
		time.Duration(cfg.Gateway.Timeouts.ConnectionTimeout)*time.Second,
	)

	// Initialize WebSocket handler
	wsHandler := wsHandler.NewWebSocketHandler(registry, logger)

	// Initialize middleware
	authMiddleware := middleware.NewAuthMiddleware(authManager, logger)

	// Setup router
	router := gin.New()

	// Setup monitoring routes and middleware
	monitoringManager.SetupRoutes(router)
	monitoringManager.SetupMiddleware(router)

	// Use middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(authMiddleware.CORSMiddleware())

	// Gateway management API - requires authentication
	gatewayHandler := api.NewGatewayHandler(registry, loadBalancer, rateLimiter, circuitBreaker, logger)
	gatewayAPI := router.Group("/gateway")

	// Most gateway routes require user authentication
	gatewayAPI.Use(authMiddleware.RequireAuth())
	gatewayAPI.GET("/status", gatewayHandler.GetGatewayStatus)
	gatewayAPI.GET("/health", gatewayHandler.GetGatewayHealth)
	gatewayAPI.GET("/services", gatewayHandler.GetServices)
	gatewayAPI.GET("/services/:name", gatewayHandler.GetService)
	gatewayAPI.DELETE("/services/:name/instances/:id", gatewayHandler.DeregisterServiceInstance)
	gatewayAPI.GET("/load-balancer/metrics", gatewayHandler.GetLoadBalancerMetrics)
	gatewayAPI.POST("/load-balancer/reset", gatewayHandler.ResetLoadBalancer)
	gatewayAPI.GET("/rate-limiter/stats", gatewayHandler.GetRateLimiterStats)
	gatewayAPI.GET("/circuit-breaker/status", gatewayHandler.GetCircuitBreakerStatus)
	gatewayAPI.POST("/circuit-breaker/reset", gatewayHandler.ResetCircuitBreakers)
	gatewayAPI.POST("/circuit-breaker/reset/:service", gatewayHandler.ResetCircuitBreaker)
	gatewayAPI.GET("/config", gatewayHandler.GetGatewayConfig)
	gatewayAPI.GET("/websocket/stats", func(c *gin.Context) {
		stats := wsHandler.GetConnectionStats()
		c.JSON(http.StatusOK, stats)
	})

	// Service registration endpoint - accepts both user and service tokens
	serviceRegistrationAPI := router.Group("/gateway")
	serviceRegistrationAPI.Use(authMiddleware.ServiceRegistrationAuth())
	serviceRegistrationAPI.POST("/services/:name/instances", gatewayHandler.RegisterServiceInstance)

	// WebSocket routes for real-time logs
	wsAPI := router.Group("/ws")
	wsAPI.Use(authMiddleware.RequireAuth()) // Require authentication for WebSocket connections
	wsAPI.GET("/:service/*path", wsHandler.HandleWebSocket)

	// Main proxy routes - apply all middleware
	// Use specific service routes instead of catch-all to avoid conflicts
	serviceRoutes := []string{
		"admin-service",
		"application-service",
		"deployment-service",
		"scheduling-service",
		"notification-service",
		"integration-service",
		"audit-service",
		"workflow-service",
		"secrets-service",
	}

	for _, serviceName := range serviceRoutes {
		serviceGroup := router.Group("/" + serviceName)
		{
			// Apply middleware in order
			if cfg.Gateway.RateLimit.Enabled {
				serviceGroup.Use(rateLimiter.RateLimitMiddleware())
			}

			if cfg.Gateway.CircuitBreaker.Enabled {
				serviceGroup.Use(circuitBreaker.CircuitBreakerMiddleware())
			}

			// Apply service-specific authentication
			serviceGroup.Use(authMiddleware.ServiceSpecificAuthMiddleware())

			// Log authentication events
			serviceGroup.Use(authMiddleware.LogAuthEvents())

			// Handle all requests for this service
			serviceGroup.Any("/*path", proxyHandler.ProxyRequest)
		}
	}

	// API route mappings from frontend paths to backend services
	for frontendPath, backendServicePath := range config.ServiceRouteMapping {
		if frontendPath == backendServicePath || frontendPath[:strings.Index(frontendPath, "/")] == backendServicePath {
			// Skip direct mappings that are already handled above
			continue
		}

		apiGroup := router.Group("/" + frontendPath)
		{
			// Apply middleware in order
			if cfg.Gateway.RateLimit.Enabled {
				apiGroup.Use(rateLimiter.RateLimitMiddleware())
			}

			if cfg.Gateway.CircuitBreaker.Enabled {
				apiGroup.Use(circuitBreaker.CircuitBreakerMiddleware())
			}

			// Apply service-specific authentication
			apiGroup.Use(authMiddleware.ServiceSpecificAuthMiddleware())

			// Log authentication events
			apiGroup.Use(authMiddleware.LogAuthEvents())

			// Handle all requests with custom path mapping
			apiGroup.Any("/*path", func(c *gin.Context) {
				// Store the original path
				originalPath := c.Request.URL.Path

				// Get query string for logging
				queryString := c.Request.URL.RawQuery

				// Modify the path to point to the target service
				targetPath := strings.Replace(originalPath, "/"+frontendPath, "/"+backendServicePath, 1)
				c.Request.URL.Path = targetPath

				// Log the path translation with info level for easier debugging
				logger.Info("API path translation",
					zap.String("frontendPath", frontendPath),
					zap.String("backendServicePath", backendServicePath),
					zap.String("originalPath", originalPath),
					zap.String("targetPath", targetPath),
					zap.String("queryParams", queryString))

				// Forward to the proxy handler
				proxyHandler.ProxyRequest(c)
			})
		}
	}

	// Create HTTP server
	serverAddr := fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port)
	srv := &http.Server{
		Addr:         serverAddr,
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Gateway.Timeouts.IdleTimeout) * time.Second,
	}

	// Start server in a goroutine
	go func() {
		logger.Info("Starting gateway server", zap.String("address", serverAddr))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", zap.Error(err))
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down gateway service...")

	// Give outstanding requests a deadline for completion
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown server
	if err := srv.Shutdown(ctx); err != nil {
		logger.Error("Server forced to shutdown", zap.Error(err))
	}

	logger.Info("Gateway service stopped")
}

// registerStaticServices registers static services from configuration
func registerStaticServices(registry discovery.ServiceRegistry, cfg *config.GatewayServiceConfig, logger *zap.Logger) error {
	for _, staticService := range cfg.Gateway.Discovery.StaticServices {
		for i, instanceURL := range staticService.Instances {
			instance := &discovery.ServiceInstance{
				ID:       fmt.Sprintf("%s-%d", staticService.Name, i),
				Name:     staticService.Name,
				URL:      instanceURL,
				Version:  staticService.Version,
				Tags:     staticService.Tags,
				Healthy:  true,
				Weight:   1,
				Metadata: make(map[string]string),
			}

			if err := registry.Register(instance); err != nil {
				return fmt.Errorf("failed to register static service %s: %w", staticService.Name, err)
			}

			logger.Info("Registered static service instance",
				zap.String("service", staticService.Name),
				zap.String("instance_id", instance.ID),
				zap.String("url", instanceURL))
		}
	}

	return nil
}
