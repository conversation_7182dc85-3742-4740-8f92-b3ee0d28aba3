package websocket

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/claudio/deploy-orchestrator/gateway-service/discovery"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

// WebSocketHandler manages WebSocket connections and proxying
type WebSocketHandler struct {
	registry    discovery.ServiceRegistry
	logger      *zap.Logger
	upgrader    websocket.Upgrader
	connections map[string]*Connection
	connMutex   sync.RWMutex
}

// Connection represents a WebSocket connection
type Connection struct {
	ID           string
	ClientConn   *websocket.Conn
	BackendConn  *websocket.Conn
	ServiceName  string
	Path         string
	LastActivity time.Time
	Context      context.Context
	Cancel       context.CancelFunc
}

// LogMessage represents a log message structure
type LogMessage struct {
	ID          string                 `json:"id"`
	Timestamp   time.Time              `json:"timestamp"`
	Level       string                 `json:"level"`
	Message     string                 `json:"message"`
	StepName    string                 `json:"stepName,omitempty"`
	ExecutionID string                 `json:"executionId,omitempty"`
	Source      string                 `json:"source,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	IsNew       bool                   `json:"isNew,omitempty"`
}

// SubscriptionMessage represents a subscription request
type SubscriptionMessage struct {
	Type        string `json:"type"`
	ExecutionID string `json:"executionId,omitempty"`
	StreamID    string `json:"streamId,omitempty"`
}

// NewWebSocketHandler creates a new WebSocket handler
func NewWebSocketHandler(registry discovery.ServiceRegistry, logger *zap.Logger) *WebSocketHandler {
	upgrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			// Allow all origins for now - in production, implement proper CORS
			return true
		},
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
	}

	handler := &WebSocketHandler{
		registry:    registry,
		logger:      logger,
		upgrader:    upgrader,
		connections: make(map[string]*Connection),
	}

	// Start cleanup routine
	go handler.cleanupRoutine()

	return handler
}

// HandleWebSocket handles WebSocket upgrade and proxying
func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	// Extract service name from path
	serviceName := h.extractServiceName(c.Request.URL.Path)
	if serviceName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid WebSocket path"})
		return
	}

	// Upgrade connection
	clientConn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		h.logger.Error("Failed to upgrade WebSocket connection", zap.Error(err))
		return
	}

	// Create connection context
	ctx, cancel := context.WithCancel(context.Background())

	// Create connection object
	conn := &Connection{
		ID:           fmt.Sprintf("%s-%d", serviceName, time.Now().UnixNano()),
		ClientConn:   clientConn,
		ServiceName:  serviceName,
		Path:         c.Request.URL.Path,
		LastActivity: time.Now(),
		Context:      ctx,
		Cancel:       cancel,
	}

	// Store connection
	h.connMutex.Lock()
	h.connections[conn.ID] = conn
	h.connMutex.Unlock()

	h.logger.Info("WebSocket connection established",
		zap.String("connectionId", conn.ID),
		zap.String("service", serviceName),
		zap.String("path", conn.Path))

	// Handle the connection
	h.handleConnection(conn)
}

// handleConnection manages the WebSocket connection lifecycle
func (h *WebSocketHandler) handleConnection(conn *Connection) {
	defer h.cleanup(conn)

	// Set up ping/pong handlers
	conn.ClientConn.SetPongHandler(func(string) error {
		conn.LastActivity = time.Now()
		return nil
	})

	// Start ping routine
	go h.pingRoutine(conn)

	// Handle messages
	for {
		select {
		case <-conn.Context.Done():
			return
		default:
			// Read message from client
			messageType, data, err := conn.ClientConn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					h.logger.Error("WebSocket read error", zap.Error(err))
				}
				return
			}

			conn.LastActivity = time.Now()

			// Handle different message types
			switch messageType {
			case websocket.TextMessage:
				h.handleTextMessage(conn, data)
			case websocket.BinaryMessage:
				h.handleBinaryMessage(conn, data)
			}
		}
	}
}

// handleTextMessage processes text messages
func (h *WebSocketHandler) handleTextMessage(conn *Connection, data []byte) {
	var msg SubscriptionMessage
	if err := json.Unmarshal(data, &msg); err != nil {
		h.logger.Error("Failed to parse WebSocket message", zap.Error(err))
		return
	}

	switch msg.Type {
	case "subscribe-logs":
		h.handleLogSubscription(conn, msg)
	case "unsubscribe-logs":
		h.handleLogUnsubscription(conn, msg)
	default:
		// Forward to backend service
		h.forwardToBackend(conn, data)
	}
}

// handleBinaryMessage processes binary messages
func (h *WebSocketHandler) handleBinaryMessage(conn *Connection, data []byte) {
	// Forward binary messages to backend
	h.forwardToBackend(conn, data)
}

// handleLogSubscription handles log subscription requests
func (h *WebSocketHandler) handleLogSubscription(conn *Connection, msg SubscriptionMessage) {
	h.logger.Info("Log subscription request",
		zap.String("connectionId", conn.ID),
		zap.String("executionId", msg.ExecutionID))

	// Connect to backend service for log streaming
	if err := h.connectToBackend(conn, msg); err != nil {
		h.logger.Error("Failed to connect to backend for logs",
			zap.String("connectionId", conn.ID),
			zap.Error(err))

		// Send error message to client
		errorMsg := map[string]interface{}{
			"type":  "error",
			"error": "Failed to connect to log stream",
		}
		h.sendToClient(conn, errorMsg)
	}
}

// handleLogUnsubscription handles log unsubscription requests
func (h *WebSocketHandler) handleLogUnsubscription(conn *Connection, msg SubscriptionMessage) {
	h.logger.Info("Log unsubscription request",
		zap.String("connectionId", conn.ID),
		zap.String("executionId", msg.ExecutionID))

	// Close backend connection if exists
	if conn.BackendConn != nil {
		conn.BackendConn.Close()
		conn.BackendConn = nil
	}
}

// connectToBackend establishes connection to backend service
func (h *WebSocketHandler) connectToBackend(conn *Connection, msg SubscriptionMessage) error {
	// Discover backend service instances
	instances, err := h.registry.Discover(conn.ServiceName)
	if err != nil {
		return fmt.Errorf("service discovery failed: %w", err)
	}

	if len(instances) == 0 {
		return fmt.Errorf("no instances available for service: %s", conn.ServiceName)
	}

	// Use first available instance (could implement load balancing here)
	instance := instances[0]

	// Build WebSocket URL for backend
	wsURL := url.URL{
		Scheme: "ws",
		Host:   fmt.Sprintf("%s:%d", instance.Address, instance.Port),
		Path:   conn.Path,
	}

	// Connect to backend WebSocket
	backendConn, _, err := websocket.DefaultDialer.Dial(wsURL.String(), nil)
	if err != nil {
		return fmt.Errorf("failed to connect to backend WebSocket: %w", err)
	}

	conn.BackendConn = backendConn

	// Send subscription message to backend
	if err := backendConn.WriteJSON(msg); err != nil {
		backendConn.Close()
		return fmt.Errorf("failed to send subscription to backend: %w", err)
	}

	// Start forwarding messages from backend to client
	go h.forwardFromBackend(conn)

	return nil
}

// forwardFromBackend forwards messages from backend to client
func (h *WebSocketHandler) forwardFromBackend(conn *Connection) {
	defer func() {
		if conn.BackendConn != nil {
			conn.BackendConn.Close()
			conn.BackendConn = nil
		}
	}()

	for {
		select {
		case <-conn.Context.Done():
			return
		default:
			messageType, data, err := conn.BackendConn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					h.logger.Error("Backend WebSocket read error", zap.Error(err))
				}
				return
			}

			// Forward message to client
			if err := conn.ClientConn.WriteMessage(messageType, data); err != nil {
				h.logger.Error("Failed to forward message to client", zap.Error(err))
				return
			}
		}
	}
}

// forwardToBackend forwards messages from client to backend
func (h *WebSocketHandler) forwardToBackend(conn *Connection, data []byte) {
	if conn.BackendConn == nil {
		h.logger.Warn("No backend connection available for forwarding")
		return
	}

	if err := conn.BackendConn.WriteMessage(websocket.TextMessage, data); err != nil {
		h.logger.Error("Failed to forward message to backend", zap.Error(err))
	}
}

// sendToClient sends a message to the client
func (h *WebSocketHandler) sendToClient(conn *Connection, message interface{}) {
	if err := conn.ClientConn.WriteJSON(message); err != nil {
		h.logger.Error("Failed to send message to client", zap.Error(err))
	}
}

// extractServiceName extracts service name from WebSocket path
func (h *WebSocketHandler) extractServiceName(path string) string {
	// Expected format: /ws/{service-name}/...
	parts := strings.Split(strings.Trim(path, "/"), "/")
	if len(parts) >= 2 && parts[0] == "ws" {
		return parts[1]
	}
	return ""
}

// pingRoutine sends periodic ping messages
func (h *WebSocketHandler) pingRoutine(conn *Connection) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-conn.Context.Done():
			return
		case <-ticker.C:
			if err := conn.ClientConn.WriteMessage(websocket.PingMessage, nil); err != nil {
				h.logger.Error("Failed to send ping", zap.Error(err))
				conn.Cancel()
				return
			}
		}
	}
}

// cleanup cleans up connection resources
func (h *WebSocketHandler) cleanup(conn *Connection) {
	h.logger.Info("Cleaning up WebSocket connection", zap.String("connectionId", conn.ID))

	// Cancel context
	conn.Cancel()

	// Close connections
	if conn.ClientConn != nil {
		conn.ClientConn.Close()
	}
	if conn.BackendConn != nil {
		conn.BackendConn.Close()
	}

	// Remove from connections map
	h.connMutex.Lock()
	delete(h.connections, conn.ID)
	h.connMutex.Unlock()
}

// cleanupRoutine periodically cleans up stale connections
func (h *WebSocketHandler) cleanupRoutine() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		h.connMutex.RLock()
		var staleConnections []*Connection
		for _, conn := range h.connections {
			if time.Since(conn.LastActivity) > 10*time.Minute {
				staleConnections = append(staleConnections, conn)
			}
		}
		h.connMutex.RUnlock()

		// Clean up stale connections
		for _, conn := range staleConnections {
			h.logger.Info("Cleaning up stale connection", zap.String("connectionId", conn.ID))
			h.cleanup(conn)
		}
	}
}

// GetConnectionStats returns connection statistics
func (h *WebSocketHandler) GetConnectionStats() map[string]interface{} {
	h.connMutex.RLock()
	defer h.connMutex.RUnlock()

	stats := map[string]interface{}{
		"totalConnections":     len(h.connections),
		"connectionsByService": make(map[string]int),
	}

	serviceCount := make(map[string]int)
	for _, conn := range h.connections {
		serviceCount[conn.ServiceName]++
	}
	stats["connectionsByService"] = serviceCount

	return stats
}
