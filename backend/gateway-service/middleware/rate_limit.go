package middleware

import (
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// RateLimiter implements token bucket rate limiting
type RateLimiter struct {
	requestsPerMinute int
	burstSize         int
	logger            *zap.Logger

	// Client buckets
	buckets map[string]*TokenBucket
	mutex   sync.RWMutex

	// Cleanup ticker
	cleanupTicker *time.Ticker
	stopChan      chan struct{}
}

// TokenBucket represents a token bucket for rate limiting
type TokenBucket struct {
	tokens     int
	maxTokens  int
	refillRate int // tokens per minute
	lastRefill time.Time
	mutex      sync.Mutex
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(requestsPerMinute, burstSize int, logger *zap.Logger) *RateLimiter {
	rl := &RateLimiter{
		requestsPerMinute: requestsPerMinute,
		burstSize:         burstSize,
		logger:            logger,
		buckets:           make(map[string]*TokenBucket),
		stopChan:          make(chan struct{}),
	}

	// Start cleanup routine
	rl.cleanupTicker = time.NewTicker(5 * time.Minute)
	go rl.cleanupRoutine()

	return rl
}

// RateLimitMiddleware creates a rate limiting middleware
func (rl *RateLimiter) RateLimitMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		clientIP := c.ClientIP()

		// Get or create bucket for client
		bucket := rl.getBucket(clientIP)

		// Check if request is allowed
		if !bucket.allowRequest() {
			rl.logger.Warn("Rate limit exceeded",
				zap.String("client_ip", clientIP),
				zap.String("path", c.Request.URL.Path),
				zap.String("method", c.Request.Method))

			c.Header("X-RateLimit-Limit", string(rune(rl.requestsPerMinute)))
			c.Header("X-RateLimit-Remaining", "0")
			c.Header("X-RateLimit-Reset", string(rune(time.Now().Add(time.Minute).Unix())))

			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":   "Rate limit exceeded",
				"message": "Too many requests, please try again later",
			})
			c.Abort()
			return
		}

		// Add rate limit headers
		remaining := bucket.getRemainingTokens()
		c.Header("X-RateLimit-Limit", string(rune(rl.requestsPerMinute)))
		c.Header("X-RateLimit-Remaining", string(rune(remaining)))
		c.Header("X-RateLimit-Reset", string(rune(time.Now().Add(time.Minute).Unix())))

		c.Next()
	}
}

// getBucket gets or creates a token bucket for a client
func (rl *RateLimiter) getBucket(clientIP string) *TokenBucket {
	rl.mutex.RLock()
	bucket, exists := rl.buckets[clientIP]
	rl.mutex.RUnlock()

	if exists {
		return bucket
	}

	// Create new bucket
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	// Double-check after acquiring write lock
	if bucket, exists := rl.buckets[clientIP]; exists {
		return bucket
	}

	bucket = &TokenBucket{
		tokens:     rl.burstSize,
		maxTokens:  rl.burstSize,
		refillRate: rl.requestsPerMinute,
		lastRefill: time.Now(),
	}

	rl.buckets[clientIP] = bucket
	return bucket
}

// allowRequest checks if a request is allowed and consumes a token
func (tb *TokenBucket) allowRequest() bool {
	tb.mutex.Lock()
	defer tb.mutex.Unlock()

	// Refill tokens based on time elapsed
	tb.refill()

	if tb.tokens > 0 {
		tb.tokens--
		return true
	}

	return false
}

// refill adds tokens to the bucket based on elapsed time
func (tb *TokenBucket) refill() {
	now := time.Now()
	elapsed := now.Sub(tb.lastRefill)

	// Calculate tokens to add (refillRate per minute)
	tokensToAdd := int(elapsed.Minutes() * float64(tb.refillRate))

	if tokensToAdd > 0 {
		tb.tokens += tokensToAdd
		if tb.tokens > tb.maxTokens {
			tb.tokens = tb.maxTokens
		}
		tb.lastRefill = now
	}
}

// getRemainingTokens returns the number of remaining tokens
func (tb *TokenBucket) getRemainingTokens() int {
	tb.mutex.Lock()
	defer tb.mutex.Unlock()

	tb.refill()
	return tb.tokens
}

// cleanupRoutine removes old buckets to prevent memory leaks
func (rl *RateLimiter) cleanupRoutine() {
	for {
		select {
		case <-rl.cleanupTicker.C:
			rl.cleanup()
		case <-rl.stopChan:
			return
		}
	}
}

// cleanup removes buckets that haven't been used recently
func (rl *RateLimiter) cleanup() {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	cutoff := time.Now().Add(-10 * time.Minute)

	for clientIP, bucket := range rl.buckets {
		bucket.mutex.Lock()
		if bucket.lastRefill.Before(cutoff) {
			delete(rl.buckets, clientIP)
		}
		bucket.mutex.Unlock()
	}

	rl.logger.Debug("Rate limiter cleanup completed",
		zap.Int("remaining_buckets", len(rl.buckets)))
}

// Stop stops the rate limiter cleanup routine
func (rl *RateLimiter) Stop() {
	if rl.cleanupTicker != nil {
		rl.cleanupTicker.Stop()
	}
	close(rl.stopChan)
}

// GetStats returns rate limiter statistics
func (rl *RateLimiter) GetStats() map[string]interface{} {
	rl.mutex.RLock()
	defer rl.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_clients":       len(rl.buckets),
		"requests_per_minute": rl.requestsPerMinute,
		"burst_size":          rl.burstSize,
	}

	// Add per-client stats
	clientStats := make(map[string]interface{})
	for clientIP, bucket := range rl.buckets {
		bucket.mutex.Lock()
		clientStats[clientIP] = map[string]interface{}{
			"remaining_tokens": bucket.tokens,
			"last_refill":      bucket.lastRefill,
		}
		bucket.mutex.Unlock()
	}
	stats["clients"] = clientStats

	return stats
}

// ServiceSpecificRateLimitMiddleware creates rate limiting middleware with service-specific limits
func (rl *RateLimiter) ServiceSpecificRateLimitMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract service name from path
		serviceName := extractServiceNameFromPath(c.Request.URL.Path)

		// Apply service-specific rate limits
		limits := rl.getServiceLimits(serviceName)

		// Create temporary rate limiter with service-specific limits
		serviceRL := &RateLimiter{
			requestsPerMinute: limits.requestsPerMinute,
			burstSize:         limits.burstSize,
			logger:            rl.logger,
			buckets:           rl.buckets, // Share buckets but with different limits
		}

		// Apply rate limiting
		serviceRL.RateLimitMiddleware()(c)
	}
}

// ServiceLimits defines rate limits for a service
type ServiceLimits struct {
	requestsPerMinute int
	burstSize         int
}

// getServiceLimits returns rate limits for a specific service
func (rl *RateLimiter) getServiceLimits(serviceName string) ServiceLimits {
	// Define service-specific limits
	serviceLimits := map[string]ServiceLimits{
		"admin-service":        {requestsPerMinute: 500, burstSize: 50},
		"audit-service":        {requestsPerMinute: 200, burstSize: 20},
		"deployment-service":   {requestsPerMinute: 1000, burstSize: 100},
		"scheduling-service":   {requestsPerMinute: 300, burstSize: 30},
		"integration-service":  {requestsPerMinute: 800, burstSize: 80},
		"notification-service": {requestsPerMinute: 600, burstSize: 60},
	}

	if limits, exists := serviceLimits[serviceName]; exists {
		return limits
	}

	// Default limits
	return ServiceLimits{
		requestsPerMinute: rl.requestsPerMinute,
		burstSize:         rl.burstSize,
	}
}

// extractServiceNameFromPath extracts service name from request path
func extractServiceNameFromPath(path string) string {
	// This is a simplified version - should match the logic in proxy handler
	parts := strings.Split(strings.Trim(path, "/"), "/")
	if len(parts) >= 3 && parts[0] == "api" && parts[1] == "v1" {
		return parts[2]
	}
	if len(parts) >= 1 {
		return parts[0]
	}
	return ""
}
