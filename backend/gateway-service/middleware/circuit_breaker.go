package middleware

import (
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/claudio/deploy-orchestrator/gateway-service/config"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CircuitState represents the state of a circuit breaker
type CircuitState int

const (
	CircuitClosed CircuitState = iota
	CircuitOpen
	CircuitHalfOpen
)

// CircuitBreaker implements the circuit breaker pattern
type CircuitBreaker struct {
	failureThreshold int
	recoveryTimeout  time.Duration
	successThreshold int
	logger           *zap.Logger

	// Per-service circuit state
	circuits map[string]*ServiceCircuit
	mutex    sync.RWMutex
}

// ServiceCircuit represents the circuit state for a specific service
type ServiceCircuit struct {
	state           CircuitState
	failureCount    int
	successCount    int
	lastFailureTime time.Time
	lastSuccessTime time.Time
	mutex           sync.RWMutex
}

// NewCircuitBreaker creates a new circuit breaker
func NewCircuitBreaker(failureThreshold int, recoveryTimeout time.Duration, successThreshold int, logger *zap.Logger) *CircuitBreaker {
	return &CircuitBreaker{
		failureThreshold: failureThreshold,
		recoveryTimeout:  recoveryTimeout,
		successThreshold: successThreshold,
		logger:           logger,
		circuits:         make(map[string]*ServiceCircuit),
	}
}

// CircuitBreakerMiddleware creates a circuit breaker middleware
func (cb *CircuitBreaker) CircuitBreakerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		serviceName := cb.extractServiceName(c.Request.URL.Path)
		if serviceName == "" {
			c.Next()
			return
		}

		circuit := cb.getCircuit(serviceName)

		// Check if circuit is open
		if circuit.isOpen() {
			cb.logger.Warn("Circuit breaker is open",
				zap.String("service", serviceName),
				zap.String("path", c.Request.URL.Path))

			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error":   "Service temporarily unavailable",
				"message": "Circuit breaker is open for this service",
				"service": serviceName,
			})
			c.Abort()
			return
		}

		// Process request
		c.Next()

		// Record result
		statusCode := c.Writer.Status()
		if statusCode >= 500 {
			circuit.recordFailure()
			cb.logger.Debug("Circuit breaker recorded failure",
				zap.String("service", serviceName),
				zap.Int("status_code", statusCode))
		} else {
			circuit.recordSuccess()
			cb.logger.Debug("Circuit breaker recorded success",
				zap.String("service", serviceName),
				zap.Int("status_code", statusCode))
		}
	}
}

// getCircuit gets or creates a circuit for a service
func (cb *CircuitBreaker) getCircuit(serviceName string) *ServiceCircuit {
	cb.mutex.RLock()
	circuit, exists := cb.circuits[serviceName]
	cb.mutex.RUnlock()

	if exists {
		return circuit
	}

	// Create new circuit
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	// Double-check after acquiring write lock
	if circuit, exists := cb.circuits[serviceName]; exists {
		return circuit
	}

	circuit = &ServiceCircuit{
		state: CircuitClosed,
	}

	cb.circuits[serviceName] = circuit
	return circuit
}

// isOpen checks if the circuit is open
func (sc *ServiceCircuit) isOpen() bool {
	sc.mutex.RLock()
	defer sc.mutex.RUnlock()

	return sc.state == CircuitOpen
}

// recordFailure records a failure and potentially opens the circuit
func (sc *ServiceCircuit) recordFailure() {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()

	sc.lastFailureTime = time.Now()

	switch sc.state {
	case CircuitClosed:
		sc.failureCount++
		if sc.failureCount >= 5 { // Use the circuit breaker's failure threshold
			sc.state = CircuitOpen
			sc.successCount = 0
		}
	case CircuitHalfOpen:
		sc.state = CircuitOpen
		sc.failureCount++
		sc.successCount = 0
	}
}

// recordSuccess records a success and potentially closes the circuit
func (sc *ServiceCircuit) recordSuccess() {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()

	sc.lastSuccessTime = time.Now()

	switch sc.state {
	case CircuitClosed:
		sc.failureCount = 0
	case CircuitHalfOpen:
		sc.successCount++
		if sc.successCount >= 3 { // Use the circuit breaker's success threshold
			sc.state = CircuitClosed
			sc.failureCount = 0
		}
	case CircuitOpen:
		// Check if recovery timeout has passed
		if time.Since(sc.lastFailureTime) >= time.Minute { // Use the circuit breaker's recovery timeout
			sc.state = CircuitHalfOpen
			sc.successCount = 1
			sc.failureCount = 0
		}
	}
}

// extractServiceName extracts service name from request path
func (cb *CircuitBreaker) extractServiceName(path string) string {
	// First check if the full path is mapped to a service (like deployables -> environment-service)
	fullPath := strings.TrimPrefix(path, "/")

	// Check if api/v1/deployables pattern
	if strings.HasPrefix(fullPath, "api/v1/") {
		apiPath := strings.SplitN(fullPath, "/", 3)
		if len(apiPath) >= 3 {
			apiEndpoint := "api/v1/" + apiPath[2]
			// Check if this API endpoint is mapped to a service
			if mappedService, exists := config.ServiceRouteMapping[apiEndpoint]; exists {
				// Extract the service name from the mapping value (e.g., "environment-service/api/v1/environments" -> "environment-service")
				parts := strings.Split(mappedService, "/")
				if len(parts) > 0 {
					return parts[0]
				}
			}
		}
	}

	// Default behavior: extract from path
	parts := strings.Split(strings.Trim(path, "/"), "/")
	if len(parts) == 0 {
		return ""
	}

	// Handle /api/v1/{service-name} format
	if len(parts) >= 3 && parts[0] == "api" && parts[1] == "v1" {
		return parts[2]
	}

	// Handle /{service-name} format
	if len(parts) >= 1 {
		return parts[0]
	}

	return ""
}

// GetCircuitStatus returns the status of all circuits
func (cb *CircuitBreaker) GetCircuitStatus() map[string]interface{} {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()

	status := make(map[string]interface{})

	for serviceName, circuit := range cb.circuits {
		circuit.mutex.RLock()

		var stateStr string
		switch circuit.state {
		case CircuitClosed:
			stateStr = "closed"
		case CircuitOpen:
			stateStr = "open"
		case CircuitHalfOpen:
			stateStr = "half-open"
		}

		status[serviceName] = map[string]interface{}{
			"state":             stateStr,
			"failure_count":     circuit.failureCount,
			"success_count":     circuit.successCount,
			"last_failure_time": circuit.lastFailureTime,
			"last_success_time": circuit.lastSuccessTime,
		}

		circuit.mutex.RUnlock()
	}

	return status
}

// ResetCircuit resets a specific circuit
func (cb *CircuitBreaker) ResetCircuit(serviceName string) {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	if circuit, exists := cb.circuits[serviceName]; exists {
		circuit.mutex.Lock()
		circuit.state = CircuitClosed
		circuit.failureCount = 0
		circuit.successCount = 0
		circuit.mutex.Unlock()

		cb.logger.Info("Circuit breaker reset",
			zap.String("service", serviceName))
	}
}

// ResetAllCircuits resets all circuits
func (cb *CircuitBreaker) ResetAllCircuits() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	for _, circuit := range cb.circuits {
		circuit.mutex.Lock()
		circuit.state = CircuitClosed
		circuit.failureCount = 0
		circuit.successCount = 0
		circuit.mutex.Unlock()
	}

	cb.logger.Info("All circuit breakers reset")
}

// HealthCheckCircuits performs health checks and updates circuit states
func (cb *CircuitBreaker) HealthCheckCircuits() {
	cb.mutex.RLock()
	circuits := make(map[string]*ServiceCircuit)
	for name, circuit := range cb.circuits {
		circuits[name] = circuit
	}
	cb.mutex.RUnlock()

	for serviceName, circuit := range circuits {
		circuit.mutex.Lock()

		// If circuit is open and recovery timeout has passed, move to half-open
		if circuit.state == CircuitOpen &&
			time.Since(circuit.lastFailureTime) >= cb.recoveryTimeout {
			circuit.state = CircuitHalfOpen
			circuit.successCount = 0

			cb.logger.Info("Circuit breaker moved to half-open",
				zap.String("service", serviceName))
		}

		circuit.mutex.Unlock()
	}
}
