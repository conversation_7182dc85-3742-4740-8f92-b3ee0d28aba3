package middleware

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/claudio/deploy-orchestrator/shared/auth"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// AuthMiddleware creates authentication middleware for the gateway
type AuthMiddleware struct {
	authManager *auth.AuthManager
	logger      *zap.Logger
	jwtSecret   string
}

// NewAuthMiddleware creates a new authentication middleware
func NewAuthMiddleware(authManager *auth.AuthManager, logger *zap.Logger) *AuthMiddleware {
	return &AuthMiddleware{
		authManager: authManager,
		logger:      logger,
		jwtSecret:   string(authManager.JWTManager.GetSecretKey()),
	}
}

// RequireAuth creates a middleware that requires authentication
func (am *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return am.authManager.AuthMiddleware()
}

// OptionalAuth creates a middleware that optionally authenticates requests
func (am *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return am.authManager.OptionalAuthMiddleware()
}

// RequireAdmin creates a middleware that requires admin privileges
func (am *AuthMiddleware) RequireAdmin() gin.HandlerFunc {
	return am.authManager.AdminMiddleware()
}

// RequireRole creates a middleware that requires specific roles
func (am *AuthMiddleware) RequireRole(roles ...string) gin.HandlerFunc {
	return am.authManager.RequireRoleMiddleware(roles...)
}

// ServiceAuthMiddleware creates a middleware that handles authentication based on service requirements
func (am *AuthMiddleware) ServiceAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract service name from path
		serviceName := am.extractServiceName(c.Request.URL.Path)

		// Define authentication requirements per service
		authRequired := am.isAuthRequired(serviceName, c.Request.URL.Path)

		if authRequired {
			// Use standard auth middleware
			am.authManager.AuthMiddleware()(c)
			if c.IsAborted() {
				return
			}
		} else {
			// Use optional auth middleware to set user context if token is present
			am.authManager.OptionalAuthMiddleware()(c)
		}

		c.Next()
	}
}

// extractServiceName extracts the service name from the request path
func (am *AuthMiddleware) extractServiceName(path string) string {
	parts := strings.Split(strings.Trim(path, "/"), "/")
	if len(parts) == 0 {
		return ""
	}

	// Handle /api/v1/{service-name} format
	if len(parts) >= 3 && parts[0] == "api" && parts[1] == "v1" {
		return parts[2]
	}

	// Handle /{service-name} format
	if len(parts) >= 1 {
		return parts[0]
	}

	return ""
}

// isAuthRequired determines if authentication is required for a given service and path
func (am *AuthMiddleware) isAuthRequired(serviceName, path string) bool {
	// Health check endpoints don't require auth
	if strings.HasSuffix(path, "/health") ||
		strings.HasSuffix(path, "/health/ready") ||
		strings.HasSuffix(path, "/health/live") {
		return false
	}

	// Metrics endpoints don't require auth (could be changed based on security requirements)
	if strings.HasSuffix(path, "/metrics") {
		return false
	}

	// Gateway management endpoints don't require auth for basic info
	if serviceName == "gateway" {
		if strings.HasSuffix(path, "/status") ||
			strings.HasSuffix(path, "/services") {
			return false
		}
	}

	// All other API endpoints require authentication
	if strings.Contains(path, "/api/") {
		return true
	}

	// Default to requiring auth for unknown patterns
	return true
}

// AdminOnlyMiddleware creates a middleware that restricts access to admin-only endpoints
func (am *AuthMiddleware) AdminOnlyMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// First ensure user is authenticated
		am.authManager.AuthMiddleware()(c)
		if c.IsAborted() {
			return
		}

		// Then check admin privileges
		am.authManager.AdminMiddleware()(c)
		if c.IsAborted() {
			return
		}

		c.Next()
	}
}

// ServiceSpecificAuthMiddleware creates middleware for service-specific authentication rules
func (am *AuthMiddleware) ServiceSpecificAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		serviceName := am.extractServiceName(c.Request.URL.Path)
		path := c.Request.URL.Path

		// Apply service-specific authentication rules
		switch serviceName {
		case "admin-service":
			// Admin service requires authentication for all endpoints except health
			if !am.isPublicEndpoint(path) {
				am.authManager.AuthMiddleware()(c)
				if c.IsAborted() {
					return
				}
			}

		case "audit-service":
			// Audit service requires authentication
			if !am.isPublicEndpoint(path) {
				am.authManager.AuthMiddleware()(c)
				if c.IsAborted() {
					return
				}
			}

		case "deployment-service", "scheduling-service", "integration-service", "notification-service":
			// Core services require authentication
			if !am.isPublicEndpoint(path) {
				am.authManager.AuthMiddleware()(c)
				if c.IsAborted() {
					return
				}
			}

		default:
			// Unknown services default to requiring authentication
			if !am.isPublicEndpoint(path) {
				am.authManager.AuthMiddleware()(c)
				if c.IsAborted() {
					return
				}
			}
		}

		c.Next()
	}
}

// isPublicEndpoint checks if an endpoint is public (doesn't require authentication)
func (am *AuthMiddleware) isPublicEndpoint(path string) bool {
	publicEndpoints := []string{
		"/health",
		"/health/ready",
		"/health/live",
		"/metrics",
	}

	for _, endpoint := range publicEndpoints {
		if strings.HasSuffix(path, endpoint) {
			return true
		}
	}

	return false
}

// ServiceRegistrationAuth creates middleware for service registration endpoints
// This allows both user tokens and service tokens for registration
func (am *AuthMiddleware) ServiceRegistrationAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
			c.Abort()
			return
		}

		tokenString := parts[1]

		// Try to verify as a regular user token first
		if claims, err := am.authManager.JWTManager.VerifyToken(tokenString); err == nil {
			// Valid user token - set user context
			c.Set("userID", claims.UserID)
			c.Set("username", claims.Username)
			c.Set("email", claims.Email)
			c.Set("roles", claims.Roles)
			c.Set("tokenType", "user")
			am.logger.Debug("User token authenticated for service registration",
				zap.String("userID", claims.UserID),
				zap.String("username", claims.Username))
			c.Next()
			return
		}

		// Try to verify as a service token
		if err := am.verifyServiceToken(tokenString, c); err == nil {
			c.Next()
			return
		}

		// Both verification attempts failed
		am.logger.Warn("Authentication failed for service registration",
			zap.String("path", c.Request.URL.Path),
			zap.String("method", c.Request.Method))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
		c.Abort()
	}
}

// verifyServiceToken verifies a service token and sets service context
func (am *AuthMiddleware) verifyServiceToken(tokenString string, c *gin.Context) error {
	// Use the service auth utility to verify the token
	// For now, we'll implement a simple verification that matches our token generation

	// Try to parse as service JWT using the same secret
	if claims, err := am.parseServiceJWT(tokenString); err == nil {
		// Valid service token - set service context
		c.Set("userID", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("email", claims.Email)
		c.Set("roles", claims.Roles)
		c.Set("tokenType", "service")
		c.Set("serviceName", am.extractServiceNameFromClaims(claims))

		am.logger.Info("Service token authenticated for registration",
			zap.String("serviceName", am.extractServiceNameFromClaims(claims)),
			zap.String("userID", claims.UserID))
		return nil
	}

	return fmt.Errorf("invalid service token")
}

// parseServiceJWT parses a service JWT token
func (am *AuthMiddleware) parseServiceJWT(tokenString string) (*auth.Claims, error) {
	// Use the same JWT manager to parse the token
	return am.authManager.JWTManager.VerifyToken(tokenString)
}

// extractServiceNameFromClaims extracts service name from JWT claims
func (am *AuthMiddleware) extractServiceNameFromClaims(claims *auth.Claims) string {
	// Service tokens have userID in format "service:service-name"
	if len(claims.UserID) > 8 && claims.UserID[:8] == "service:" {
		return claims.UserID[8:]
	}
	return claims.Username // Fallback to username
}

// CORSMiddleware creates CORS middleware for the gateway
func (am *AuthMiddleware) CORSMiddleware() gin.HandlerFunc {
	return am.authManager.CORSMiddleware()
}

// LogAuthEvents logs authentication events for monitoring
func (am *AuthMiddleware) LogAuthEvents() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Process request
		c.Next()

		// Log authentication events
		userID, exists := c.Get("userID")
		if exists {
			am.logger.Info("Authenticated request",
				zap.String("user_id", userID.(string)),
				zap.String("method", c.Request.Method),
				zap.String("path", c.Request.URL.Path),
				zap.String("remote_addr", c.ClientIP()),
				zap.Int("status", c.Writer.Status()))
		} else if c.Writer.Status() == http.StatusUnauthorized {
			am.logger.Warn("Unauthorized request",
				zap.String("method", c.Request.Method),
				zap.String("path", c.Request.URL.Path),
				zap.String("remote_addr", c.ClientIP()),
				zap.String("user_agent", c.Request.UserAgent()))
		}
	}
}
