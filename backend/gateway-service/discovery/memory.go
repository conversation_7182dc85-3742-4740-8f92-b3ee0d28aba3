package discovery

import (
	"fmt"
	"net/http"
	"sync"
	"time"

	"go.uber.org/zap"
)

// MemoryRegistry implements ServiceRegistry using in-memory storage
type MemoryRegistry struct {
	services map[string]*Service
	mutex    sync.RWMutex
	logger   *zap.Logger

	// Configuration
	healthCheckInterval time.Duration
	healthCheckTimeout  time.Duration

	// Background processes
	stopChan chan struct{}
	started  bool
}

// NewMemoryRegistry creates a new in-memory service registry
func NewMemoryRegistry(logger *zap.Logger, healthCheckInterval, healthCheckTimeout time.Duration) *MemoryRegistry {
	return &MemoryRegistry{
		services:            make(map[string]*Service),
		logger:              logger,
		healthCheckInterval: healthCheckInterval,
		healthCheckTimeout:  healthCheckTimeout,
		stopChan:            make(chan struct{}),
	}
}

// Register registers a service instance
func (mr *MemoryRegistry) Register(instance *ServiceInstance) error {
	mr.mutex.Lock()
	defer mr.mutex.Unlock()

	service, exists := mr.services[instance.Name]
	if !exists {
		service = &Service{
			Name:      instance.Name,
			Instances: make([]*ServiceInstance, 0),
			Version:   instance.Version,
			Tags:      instance.Tags,
		}
		mr.services[instance.Name] = service
	}

	// Check if instance already exists (update if so)
	for i, existing := range service.Instances {
		if existing.ID == instance.ID {
			service.Instances[i] = instance
			mr.logger.Info("Updated service instance",
				zap.String("service", instance.Name),
				zap.String("instance_id", instance.ID),
				zap.String("url", instance.URL))
			return nil
		}
	}

	// Add new instance
	service.Instances = append(service.Instances, instance)
	mr.logger.Info("Registered service instance",
		zap.String("service", instance.Name),
		zap.String("instance_id", instance.ID),
		zap.String("url", instance.URL))

	return nil
}

// Deregister removes a service instance
func (mr *MemoryRegistry) Deregister(serviceID string) error {
	mr.mutex.Lock()
	defer mr.mutex.Unlock()

	for serviceName, service := range mr.services {
		for i, instance := range service.Instances {
			if instance.ID == serviceID {
				// Remove instance from slice
				service.Instances = append(service.Instances[:i], service.Instances[i+1:]...)
				mr.logger.Info("Deregistered service instance",
					zap.String("service", serviceName),
					zap.String("instance_id", serviceID))

				// Remove service if no instances left
				if len(service.Instances) == 0 {
					delete(mr.services, serviceName)
					mr.logger.Info("Removed empty service", zap.String("service", serviceName))
				}
				return nil
			}
		}
	}

	return fmt.Errorf("service instance not found: %s", serviceID)
}

// Discover returns all healthy instances of a service
func (mr *MemoryRegistry) Discover(serviceName string) ([]*ServiceInstance, error) {
	mr.mutex.RLock()
	defer mr.mutex.RUnlock()

	service, exists := mr.services[serviceName]
	if !exists {
		return nil, fmt.Errorf("service not found: %s", serviceName)
	}

	// Return only healthy instances
	var healthyInstances []*ServiceInstance
	for _, instance := range service.Instances {
		if instance.Healthy {
			healthyInstances = append(healthyInstances, instance)
		}
	}

	return healthyInstances, nil
}

// GetServices returns all services
func (mr *MemoryRegistry) GetServices() (map[string]*Service, error) {
	mr.mutex.RLock()
	defer mr.mutex.RUnlock()

	// Return a copy to prevent external modification
	services := make(map[string]*Service)
	for name, service := range mr.services {
		services[name] = service
	}

	return services, nil
}

// GetService returns a specific service
func (mr *MemoryRegistry) GetService(serviceName string) (*Service, error) {
	mr.mutex.RLock()
	defer mr.mutex.RUnlock()

	service, exists := mr.services[serviceName]
	if !exists {
		return nil, fmt.Errorf("service not found: %s", serviceName)
	}

	return service, nil
}

// HealthCheck performs health checks on all service instances
func (mr *MemoryRegistry) HealthCheck() error {
	mr.mutex.Lock()
	defer mr.mutex.Unlock()

	client := &http.Client{
		Timeout: mr.healthCheckTimeout,
	}

	for _, service := range mr.services {
		for _, instance := range service.Instances {
			healthy := mr.checkInstanceHealth(client, instance, service.HealthURL)
			instance.Healthy = healthy
			instance.LastChecked = time.Now()

			if healthy {
				mr.logger.Debug("Health check passed",
					zap.String("service", instance.Name),
					zap.String("instance_id", instance.ID))
			} else {
				mr.logger.Warn("Health check failed",
					zap.String("service", instance.Name),
					zap.String("instance_id", instance.ID),
					zap.String("url", instance.URL))
			}
		}
	}

	return nil
}

// checkInstanceHealth performs a health check on a single instance
func (mr *MemoryRegistry) checkInstanceHealth(client *http.Client, instance *ServiceInstance, healthPath string) bool {
	if healthPath == "" {
		healthPath = "/health"
	}

	healthURL := instance.URL + healthPath
	resp, err := client.Get(healthURL)
	if err != nil {
		mr.logger.Debug("Health check request failed",
			zap.String("url", healthURL),
			zap.Error(err))
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode >= 200 && resp.StatusCode < 300
}

// Start begins background health checking
func (mr *MemoryRegistry) Start() error {
	if mr.started {
		return fmt.Errorf("registry already started")
	}

	mr.started = true

	// Start health check ticker
	ticker := time.NewTicker(mr.healthCheckInterval)

	go func() {
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				if err := mr.HealthCheck(); err != nil {
					mr.logger.Error("Health check failed", zap.Error(err))
				}
			case <-mr.stopChan:
				mr.logger.Info("Stopping memory registry health checker")
				return
			}
		}
	}()

	mr.logger.Info("Started memory registry",
		zap.Duration("health_check_interval", mr.healthCheckInterval))

	return nil
}

// Stop stops the registry
func (mr *MemoryRegistry) Stop() error {
	if !mr.started {
		return nil
	}

	close(mr.stopChan)
	mr.started = false
	mr.logger.Info("Stopped memory registry")

	return nil
}

// GetMetrics returns discovery metrics
func (mr *MemoryRegistry) GetMetrics() DiscoveryMetrics {
	mr.mutex.RLock()
	defer mr.mutex.RUnlock()

	metrics := DiscoveryMetrics{
		ServiceInstanceCounts: make(map[string]int),
		ServiceHealthStatus:   make(map[string]bool),
	}

	for serviceName, service := range mr.services {
		metrics.TotalServices++
		metrics.ServiceInstanceCounts[serviceName] = len(service.Instances)

		healthyCount := 0
		for _, instance := range service.Instances {
			metrics.TotalInstances++
			if instance.Healthy {
				metrics.HealthyInstances++
				healthyCount++
			} else {
				metrics.UnhealthyInstances++
			}
		}

		// Service is healthy if at least one instance is healthy
		metrics.ServiceHealthStatus[serviceName] = healthyCount > 0
	}

	return metrics
}
