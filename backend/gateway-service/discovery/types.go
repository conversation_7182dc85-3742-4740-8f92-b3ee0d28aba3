package discovery

import (
	"time"
)

// ServiceInstance represents a single instance of a service
type ServiceInstance struct {
	ID       string            `json:"id"`
	Name     string            `json:"name"`
	Address  string            `json:"address"`
	Port     int               `json:"port"`
	URL      string            `json:"url"`
	Version  string            `json:"version"`
	Tags     []string          `json:"tags"`
	Metadata map[string]string `json:"metadata"`

	// Health status
	Healthy     bool      `json:"healthy"`
	LastChecked time.Time `json:"last_checked"`

	// Load balancing metrics
	ActiveConnections int `json:"active_connections"`
	Weight            int `json:"weight"`
}

// Service represents a logical service with multiple instances
type Service struct {
	Name      string             `json:"name"`
	Instances []*ServiceInstance `json:"instances"`
	Version   string             `json:"version"`
	Tags      []string           `json:"tags"`

	// Service-level configuration
	HealthURL string `json:"health_url"`

	// Load balancing state
	RoundRobinIndex int `json:"-"`
}

// ServiceRegistry defines the interface for service discovery implementations
type ServiceRegistry interface {
	// Register a service instance
	Register(instance *ServiceInstance) error

	// Deregister a service instance
	Deregister(serviceID string) error

	// Discover all instances of a service
	Discover(serviceName string) ([]*ServiceInstance, error)

	// Get all services
	GetServices() (map[string]*Service, error)

	// Get a specific service
	GetService(serviceName string) (*Service, error)

	// Health check all services
	HealthCheck() error

	// Start the registry (for background processes)
	Start() error

	// Stop the registry
	Stop() error
}

// HealthChecker defines the interface for health checking
type HealthChecker interface {
	// Check health of a service instance
	CheckHealth(instance *ServiceInstance) bool

	// Check health of all instances
	CheckAllHealth() map[string]bool
}

// LoadBalancer defines the interface for load balancing
type LoadBalancer interface {
	// Select an instance from available healthy instances
	SelectInstance(instances []*ServiceInstance) *ServiceInstance

	// Update instance metrics (for weighted load balancing)
	UpdateMetrics(instanceID string, connections int, responseTime time.Duration)
}

// DiscoveryEvent represents events in the service registry
type DiscoveryEvent struct {
	Type      string           `json:"type"` // "register", "deregister", "health_change"
	Service   string           `json:"service"`
	Instance  *ServiceInstance `json:"instance"`
	Timestamp time.Time        `json:"timestamp"`
}

// DiscoveryEventHandler handles discovery events
type DiscoveryEventHandler func(event DiscoveryEvent)

// ServiceFilter defines criteria for filtering services
type ServiceFilter struct {
	Name    string   `json:"name"`
	Tags    []string `json:"tags"`
	Version string   `json:"version"`
	Healthy *bool    `json:"healthy"`
}

// DiscoveryMetrics holds metrics for the discovery system
type DiscoveryMetrics struct {
	TotalServices         int             `json:"total_services"`
	TotalInstances        int             `json:"total_instances"`
	HealthyInstances      int             `json:"healthy_instances"`
	UnhealthyInstances    int             `json:"unhealthy_instances"`
	LastHealthCheck       time.Time       `json:"last_health_check"`
	HealthCheckDuration   time.Duration   `json:"health_check_duration"`
	ServiceInstanceCounts map[string]int  `json:"service_instance_counts"`
	ServiceHealthStatus   map[string]bool `json:"service_health_status"`
}

// DiscoveryConfig holds configuration for discovery implementations
type DiscoveryConfig struct {
	Method              string        `json:"method"`
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	HealthCheckTimeout  time.Duration `json:"health_check_timeout"`
	ServiceTTL          time.Duration `json:"service_ttl"`
}
