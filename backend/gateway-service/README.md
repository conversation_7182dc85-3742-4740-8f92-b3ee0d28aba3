# API Gateway Service

The API Gateway Service provides a unified entry point for all microservices in the deployment orchestration platform. It implements service discovery, load balancing, rate limiting, circuit breaking, and authentication.

## Features

### Core Features
- **Service Discovery**: Automatic discovery and registration of backend services
- **Load Balancing**: Multiple algorithms (round-robin, least connections, weighted)
- **Rate Limiting**: Token bucket algorithm with per-client limits
- **Circuit Breaker**: Fault tolerance with automatic recovery
- **Authentication**: Integration with platform authentication system
- **Monitoring**: Comprehensive metrics and health checks

### Service Discovery
- **Memory-based**: In-memory registry with health checking (default)
- **Future**: Support for Consul, etcd, and Kubernetes service discovery
- **Health Checks**: Automatic health monitoring of backend services
- **Dynamic Registration**: Runtime service registration and deregistration

### Load Balancing
- **Round Robin**: Equal distribution across healthy instances
- **Least Connections**: Route to instance with fewest active connections
- **Weighted Round Robin**: Distribution based on instance weights
- **Health-aware**: Only routes to healthy instances

### Rate Limiting
- **Token Bucket**: Configurable requests per minute and burst size
- **Per-client**: Individual rate limits per client IP
- **Service-specific**: Different limits for different services
- **Automatic Cleanup**: Removes old client buckets to prevent memory leaks

### Circuit Breaker
- **Per-service**: Individual circuit breakers for each backend service
- **Configurable Thresholds**: Failure count and recovery timeouts
- **Automatic Recovery**: Half-open state for testing service recovery
- **Manual Reset**: API endpoints for manual circuit reset

## Configuration

### Environment Variables
```bash
# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8000

# Authentication
JWT_SECRET_KEY=your-secret-key
ADMIN_SERVICE_URL=http://localhost:8080

# Discovery
DISCOVERY_METHOD=memory
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=5

# Load Balancing
LB_ALGORITHM=round_robin

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_RPM=1000
RATE_LIMIT_BURST=100

# Circuit Breaker
CIRCUIT_BREAKER_ENABLED=true
CB_FAILURE_THRESHOLD=5
CB_RECOVERY_TIMEOUT=60
CB_SUCCESS_THRESHOLD=3
```

### Configuration File (config.yaml)
See `config/config.yaml` for complete configuration options.

## API Endpoints

### Health and Status
- `GET /health` - Gateway health check
- `GET /gateway/status` - Comprehensive gateway status
- `GET /gateway/health` - Detailed health information

### Service Discovery
- `GET /gateway/services` - List all registered services
- `GET /gateway/services/{name}` - Get specific service details
- `POST /gateway/services/{name}/instances` - Register service instance
- `DELETE /gateway/services/{name}/instances/{id}` - Deregister service instance

### Load Balancer Management
- `GET /gateway/load-balancer/metrics` - Load balancer metrics
- `POST /gateway/load-balancer/reset` - Reset load balancer state

### Rate Limiter Management
- `GET /gateway/rate-limiter/stats` - Rate limiter statistics

### Circuit Breaker Management
- `GET /gateway/circuit-breaker/status` - Circuit breaker status
- `POST /gateway/circuit-breaker/reset` - Reset all circuit breakers
- `POST /gateway/circuit-breaker/reset/{service}` - Reset specific circuit breaker

### Gateway Configuration
- `GET /gateway/config` - Gateway configuration and capabilities

## Request Routing

The gateway routes requests based on URL patterns:

### API v1 Format
```
/api/v1/{service-name}/{endpoint}
```
Routes to: `{service-url}/api/v1/{service-name}/{endpoint}`

### Direct Service Format
```
/{service-name}/{endpoint}
```
Routes to: `{service-url}/{service-name}/{endpoint}`

### Examples
- `/api/v1/deployment-service/deployments` → `http://localhost:8082/api/v1/deployment-service/deployments`
- `/admin-service/users` → `http://localhost:8080/admin-service/users`

## Authentication

The gateway integrates with the platform's authentication system:

### Public Endpoints (No Authentication)
- `/health`, `/health/ready`, `/health/live`
- `/metrics`
- Basic gateway status endpoints

### Protected Endpoints (Authentication Required)
- All `/api/v1/*` endpoints
- Service-specific endpoints based on configuration
- Gateway management endpoints

### Service-Specific Rules
- **Admin Service**: Authentication required for all non-health endpoints
- **Core Services**: Authentication required for API endpoints
- **Unknown Services**: Default to requiring authentication

## Monitoring and Observability

### Metrics
- Request count and response times per service
- Load balancer connection counts
- Rate limiter statistics
- Circuit breaker state changes
- Authentication events

### Health Checks
- Gateway health endpoint
- Backend service health monitoring
- Component health status (registry, load balancer, etc.)

### Logging
- Structured JSON logging
- Request/response logging with correlation IDs
- Authentication event logging
- Error and warning logs for troubleshooting

## Development

### Running Locally
```bash
# Install dependencies
go mod download

# Run the service
go run main.go

# Or build and run
go build -o gateway-service
./gateway-service
```

### Testing
```bash
# Run tests
go test ./...

# Run with coverage
go test -cover ./...
```

### Docker
```bash
# Build image
docker build -t gateway-service .

# Run container
docker run -p 8000:8000 gateway-service
```

## Production Deployment

### Docker Compose
Add to your `docker-compose.yml`:

```yaml
gateway-service:
  build:
    context: ./backend/gateway-service
    dockerfile: Dockerfile
  ports:
    - "8000:8000"
  environment:
    - CONFIG_PATH=/app/config/config.yaml
  volumes:
    - ./backend/gateway-service/config:/app/config
  depends_on:
    - admin-service
    - deployment-service
    - scheduling-service
    - notification-service
    - integration-service
    - audit-service
  restart: unless-stopped
```

### Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gateway-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: gateway-service
  template:
    metadata:
      labels:
        app: gateway-service
    spec:
      containers:
      - name: gateway-service
        image: gateway-service:latest
        ports:
        - containerPort: 8000
        env:
        - name: SERVER_PORT
          value: "8000"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: gateway-service
spec:
  selector:
    app: gateway-service
  ports:
  - port: 8000
    targetPort: 8000
  type: LoadBalancer
```

## Security Considerations

1. **Authentication**: All management endpoints require authentication
2. **Rate Limiting**: Prevents abuse and DoS attacks
3. **Circuit Breaking**: Protects backend services from cascading failures
4. **CORS**: Configurable CORS policies
5. **Headers**: Adds security headers and removes sensitive information
6. **Logging**: Comprehensive audit logging for security monitoring

## Future Enhancements

1. **Advanced Service Discovery**: Consul, etcd, Kubernetes integration
2. **SSL/TLS Termination**: HTTPS support with certificate management
3. **Request Transformation**: Request/response modification capabilities
4. **Caching**: Response caching for improved performance
5. **WebSocket Support**: Proxy WebSocket connections
6. **Metrics Export**: Prometheus metrics export
7. **Distributed Tracing**: OpenTelemetry integration
