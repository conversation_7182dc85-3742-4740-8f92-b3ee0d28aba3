#!/bin/bash

# Example script showing how to register/deregister services with the API Gateway
# This demonstrates the REST API calls that microservices would make

set -e

GATEWAY_URL="${GATEWAY_URL:-http://localhost:8000}"
AUTH_TOKEN="${AUTH_TOKEN:-your-jwt-token-here}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Service Registration Demo${NC}"
echo "=================================="

# Function to make authenticated requests
make_auth_request() {
    local method=$1
    local url=$2
    local data=$3
    
    if [ -n "$data" ]; then
        curl -s -X "$method" "$url" \
             -H "Content-Type: application/json" \
             -H "Authorization: Bearer $AUTH_TOKEN" \
             -d "$data"
    else
        curl -s -X "$method" "$url" \
             -H "Authorization: Bearer $AUTH_TOKEN"
    fi
}

# Function to register a service
register_service() {
    local service_name=$1
    local instance_id=$2
    local service_url=$3
    local version=$4
    local tags=$5
    
    echo -e "\n${YELLOW}📝 Registering service: $service_name${NC}"
    echo "   Instance ID: $instance_id"
    echo "   URL: $service_url"
    echo "   Version: $version"
    
    local registration_data=$(cat <<EOF
{
    "id": "$instance_id",
    "url": "$service_url",
    "version": "$version",
    "tags": [$tags],
    "metadata": {
        "environment": "development",
        "region": "local",
        "started_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "hostname": "$(hostname)"
    }
}
EOF
)
    
    local response=$(make_auth_request "POST" "$GATEWAY_URL/gateway/services/$service_name/instances" "$registration_data")
    
    if echo "$response" | grep -q "successfully"; then
        echo -e "   ${GREEN}✅ Registration successful${NC}"
    else
        echo -e "   ${RED}❌ Registration failed: $response${NC}"
    fi
}

# Function to deregister a service
deregister_service() {
    local service_name=$1
    local instance_id=$2
    
    echo -e "\n${YELLOW}🗑️  Deregistering service: $service_name${NC}"
    echo "   Instance ID: $instance_id"
    
    local response=$(make_auth_request "DELETE" "$GATEWAY_URL/gateway/services/$service_name/instances/$instance_id")
    
    if echo "$response" | grep -q "successfully"; then
        echo -e "   ${GREEN}✅ Deregistration successful${NC}"
    else
        echo -e "   ${RED}❌ Deregistration failed: $response${NC}"
    fi
}

# Function to list services
list_services() {
    echo -e "\n${BLUE}📋 Current registered services:${NC}"
    local response=$(make_auth_request "GET" "$GATEWAY_URL/gateway/services")
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
}

# Function to get specific service details
get_service() {
    local service_name=$1
    echo -e "\n${BLUE}🔍 Service details for: $service_name${NC}"
    local response=$(make_auth_request "GET" "$GATEWAY_URL/gateway/services/$service_name")
    echo "$response" | jq '.' 2>/dev/null || echo "$response"
}

# Check if gateway is running
echo -e "\n${BLUE}🔍 Checking gateway status...${NC}"
if curl -s "$GATEWAY_URL/health" > /dev/null; then
    echo -e "${GREEN}✅ Gateway is running${NC}"
else
    echo -e "${RED}❌ Gateway is not accessible at $GATEWAY_URL${NC}"
    echo "Please make sure the gateway service is running"
    exit 1
fi

# Demo: Register multiple service instances
echo -e "\n${BLUE}🎯 Demo: Registering multiple services${NC}"

# Register user-service instances
register_service "user-service" "user-service-1" "http://localhost:8081" "1.0.0" '"user", "api"'
register_service "user-service" "user-service-2" "http://localhost:8082" "1.0.0" '"user", "api"'

# Register order-service instance
register_service "order-service" "order-service-1" "http://localhost:8083" "1.2.0" '"order", "api", "critical"'

# Register notification-service instance
register_service "notification-service" "notification-service-1" "http://localhost:8084" "1.1.0" '"notification", "messaging"'

# List all services
list_services

# Get specific service details
get_service "user-service"

# Demo: Service lifecycle
echo -e "\n${BLUE}🔄 Demo: Service lifecycle (register -> check -> deregister)${NC}"

# Register a temporary service
register_service "temp-service" "temp-service-1" "http://localhost:9999" "0.1.0" '"temp", "demo"'

# Check it's registered
get_service "temp-service"

# Wait a moment
echo -e "\n${YELLOW}⏳ Waiting 3 seconds...${NC}"
sleep 3

# Deregister it
deregister_service "temp-service" "temp-service-1"

# Verify it's gone
echo -e "\n${BLUE}🔍 Verifying temp-service is deregistered:${NC}"
get_service "temp-service"

echo -e "\n${BLUE}🧹 Cleanup: Deregistering demo services${NC}"

# Cleanup: Deregister all demo services
deregister_service "user-service" "user-service-1"
deregister_service "user-service" "user-service-2"
deregister_service "order-service" "order-service-1"
deregister_service "notification-service" "notification-service-1"

# Final service list
list_services

echo -e "\n${GREEN}✅ Service registration demo completed!${NC}"
echo ""
echo -e "${BLUE}📚 Key Points:${NC}"
echo "   • Services register with POST /gateway/services/{name}/instances"
echo "   • Services deregister with DELETE /gateway/services/{name}/instances/{id}"
echo "   • Registration requires authentication (JWT token)"
echo "   • Services should register on startup and deregister on shutdown"
echo "   • Gateway performs health checks on registered services"
echo "   • Multiple instances of the same service can be registered"
echo ""
echo -e "${BLUE}🔗 Useful Commands:${NC}"
echo "   • List services: curl -H \"Authorization: Bearer \$TOKEN\" $GATEWAY_URL/gateway/services"
echo "   • Gateway status: curl $GATEWAY_URL/gateway/status"
echo "   • Gateway health: curl $GATEWAY_URL/health"
