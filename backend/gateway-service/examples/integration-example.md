# Easy Integration Guide

This guide shows how to easily integrate gateway registration into existing microservices.

## Quick Integration (3 lines of code)

### Step 1: Add to your main.go

```go
import (
    "github.com/claudio/deploy-orchestrator/shared/gateway"
    // ... your other imports
)

func main() {
    // ... your existing initialization code ...

    logger, _ := zap.NewProduction()
    defer logger.Sync()

    // 🚀 Add these 3 lines for gateway integration:
    gatewayClient := gateway.IntegrateWithService("your-service-name", 8080, logger)
    defer gatewayClient.SafeDeregister()
    
    // ... rest of your service code ...
}
```

### Step 2: Set environment variables

```bash
# Required for gateway registration
export GATEWAY_URL="http://localhost:8000"
export GATEWAY_AUTH_TOKEN="your-jwt-token"

# Optional configuration
export SERVICE_VERSION="1.0.0"
export ENVIRONMENT="production"
export REGION="us-west-2"
```

### Step 3: Update Docker Compose

```yaml
services:
  your-service:
    build: ./backend/your-service
    environment:
      - GATEWAY_URL=http://gateway-service:8000
      - GATEWAY_AUTH_TOKEN=${SERVICE_AUTH_TOKEN}
      - SERVICE_VERSION=1.0.0
      - ENVIRONMENT=production
    depends_on:
      - gateway-service
```

That's it! Your service will now:
- ✅ Register with the gateway on startup
- ✅ Deregister on shutdown
- ✅ Continue working even if gateway is unavailable
- ✅ Include metadata (hostname, PID, start time, etc.)

## Advanced Integration

For more control, use the gateway client directly:

```go
func main() {
    logger, _ := zap.NewProduction()
    
    // Create gateway client with custom configuration
    config := gateway.Config{
        GatewayURL:  "http://localhost:8000",
        ServiceName: "my-service",
        ServicePort: 8080,
        AuthToken:   "jwt-token",
        Version:     "2.1.0",
        Tags:        []string{"api", "critical", "user-facing"},
        Environment: "production",
        Region:      "us-west-2",
    }
    
    gatewayClient := gateway.NewClient(config, logger)
    
    // Register with retry logic
    if err := gatewayClient.RegisterWithRetry(5); err != nil {
        logger.Error("Failed to register with gateway", zap.Error(err))
        // Decide whether to continue or exit
    }
    
    // Setup graceful shutdown
    c := make(chan os.Signal, 1)
    signal.Notify(c, os.Interrupt, syscall.SIGTERM)
    
    // Start your service...
    go startServer()
    
    // Wait for shutdown
    <-c
    logger.Info("Shutting down...")
    
    // Deregister from gateway
    gatewayClient.SafeDeregister()
    
    // ... rest of shutdown logic ...
}
```

## Real Example: Admin Service Integration

Here's how to integrate into the existing admin-service:

```go
// In backend/admin-service/main.go

func main() {
    // ... existing code ...
    
    logger, err := zap.NewProduction()
    if err != nil {
        fmt.Printf("Failed to initialize logger: %v\n", err)
        os.Exit(1)
    }
    defer logger.Sync()

    // Load configuration
    cfg, err := config.LoadConfig()
    if err != nil {
        logger.Fatal("Failed to load config", zap.Error(err))
    }

    logger.Info("Starting admin service",
        zap.String("service", "admin-service"),
        zap.Int("port", cfg.Server.Port))

    // 🚀 Add gateway registration
    gatewayClient := gateway.IntegrateWithService("admin-service", cfg.Server.Port, logger)

    // ... existing database, auth setup ...

    // Setup router
    router := setupRouter(cfg, logger)

    // Create HTTP server
    srv := &http.Server{
        Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
        Handler: router,
    }

    // Start server
    go func() {
        logger.Info("Starting HTTP server", zap.Int("port", cfg.Server.Port))
        if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
            logger.Fatal("Failed to start server", zap.Error(err))
        }
    }()

    // Wait for interrupt signal
    quit := make(chan os.Signal, 1)
    signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
    <-quit

    logger.Info("Shutting down admin service...")

    // 🚀 Deregister from gateway
    gatewayClient.SafeDeregister()

    // Shutdown server
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()
    
    if err := srv.Shutdown(ctx); err != nil {
        logger.Error("Server forced to shutdown", zap.Error(err))
    }

    logger.Info("Admin service stopped")
}
```

## Environment Variables Reference

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `GATEWAY_URL` | Yes | - | URL of the API Gateway (e.g., `http://localhost:8000`) |
| `GATEWAY_AUTH_TOKEN` | Yes* | - | JWT token for authentication |
| `SERVICE_VERSION` | No | `1.0.0` | Version of your service |
| `ENVIRONMENT` | No | `development` | Environment name (dev, staging, prod) |
| `REGION` | No | `local` | Region or datacenter |

*Required if gateway has authentication enabled

## Docker Compose Example

```yaml
version: '3.8'

services:
  gateway-service:
    build: ./backend/gateway-service
    ports:
      - "8000:8000"
    environment:
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}

  admin-service:
    build: ./backend/admin-service
    ports:
      - "8080:8080"
    environment:
      - GATEWAY_URL=http://gateway-service:8000
      - GATEWAY_AUTH_TOKEN=${SERVICE_AUTH_TOKEN}
      - SERVICE_VERSION=1.0.0
      - ENVIRONMENT=production
    depends_on:
      - gateway-service
      - postgres

  deployment-service:
    build: ./backend/deployment-service
    ports:
      - "8082:8080"
    environment:
      - GATEWAY_URL=http://gateway-service:8000
      - GATEWAY_AUTH_TOKEN=${SERVICE_AUTH_TOKEN}
      - SERVICE_VERSION=1.0.0
    depends_on:
      - gateway-service
      - postgres

  # ... other services
```

## Kubernetes Example

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-service
spec:
  replicas: 2
  template:
    spec:
      containers:
      - name: admin-service
        image: admin-service:latest
        env:
        - name: GATEWAY_URL
          value: "http://gateway-service:8000"
        - name: GATEWAY_AUTH_TOKEN
          valueFrom:
            secretKeyRef:
              name: service-auth
              key: token
        - name: SERVICE_VERSION
          value: "1.0.0"
        - name: ENVIRONMENT
          value: "production"
```

## Testing Registration

Use the provided test script to verify registration:

```bash
# Make the script executable
chmod +x backend/gateway-service/examples/register-service.sh

# Set your auth token
export AUTH_TOKEN="your-jwt-token"

# Run the demo
./backend/gateway-service/examples/register-service.sh
```

## Troubleshooting

### Service not registering
1. Check `GATEWAY_URL` is correct and gateway is running
2. Verify `GATEWAY_AUTH_TOKEN` is valid
3. Check service logs for registration errors
4. Test gateway health: `curl http://localhost:8000/health`

### Registration fails with 401
- Check JWT token is valid and not expired
- Verify token has correct permissions
- Check gateway authentication configuration

### Service registers but not receiving traffic
- Verify service health endpoint is working
- Check gateway health checks are passing
- Confirm service URL is accessible from gateway

### Gateway unavailable
- Services will continue to work without registration
- Check gateway logs for errors
- Verify gateway dependencies (database, etc.)

## Benefits

✅ **Zero downtime**: Services work with or without gateway
✅ **Automatic discovery**: No manual service configuration
✅ **Health monitoring**: Gateway monitors service health
✅ **Load balancing**: Traffic distributed across instances
✅ **Fault tolerance**: Circuit breakers protect services
✅ **Observability**: Comprehensive metrics and logging
