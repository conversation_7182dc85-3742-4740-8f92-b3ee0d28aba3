package main

// Example of how to integrate gateway registration into the existing admin-service

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"go.uber.org/zap"
)

// This is an example of how to modify the existing admin-service main.go
// to include gateway registration

func main() {
	// ... existing admin-service initialization code ...

	// Initialize logger (assuming you have this)
	logger, err := zap.NewProduction()
	if err != nil {
		fmt.Printf("Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer logger.Sync()

	// Load configuration (assuming you have this)
	cfg := loadConfig() // Your existing config loading

	logger.Info("Starting admin service",
		zap.String("service", "admin-service"),
		zap.Int("port", cfg.Server.Port))

	// ... existing service setup (database, auth, etc.) ...

	// Gateway registration integration
	var gatewayClient *GatewayClient
	if gatewayURL := os.Getenv("GATEWAY_URL"); gatewayURL != "" {
		gatewayClient = NewGatewayClient(
			gatewayURL,
			"admin-service",
			generateInstanceID(),
			fmt.Sprintf("http://localhost:%d", cfg.Server.Port),
			os.Getenv("GATEWAY_AUTH_TOKEN"),
			logger,
		)

		// Register with gateway (safe - won't fail service startup)
		gatewayClient.SafeRegister()
		logger.Info("Gateway registration attempted")
	} else {
		logger.Info("Gateway URL not configured, skipping registration")
	}

	// ... existing router setup ...
	router := setupRouter() // Your existing router setup

	// Create HTTP server
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		logger.Info("Starting HTTP server", zap.Int("port", cfg.Server.Port))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", zap.Error(err))
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down admin service...")

	// Deregister from gateway first
	if gatewayClient != nil {
		gatewayClient.SafeDeregister()
		logger.Info("Deregistered from gateway")
	}

	// Give outstanding requests a deadline for completion
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown server
	if err := srv.Shutdown(ctx); err != nil {
		logger.Error("Server forced to shutdown", zap.Error(err))
	}

	logger.Info("Admin service stopped")
}

// Placeholder functions - replace with your actual implementations
func loadConfig() struct {
	Server struct {
		Port int
	}
} {
	return struct {
		Server struct {
			Port int
		}
	}{
		Server: struct {
			Port int
		}{
			Port: 8080,
		},
	}
}

func setupRouter() http.Handler {
	// Return your existing router
	return http.NewServeMux()
}
