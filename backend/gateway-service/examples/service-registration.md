# Dynamic Service Registration with API Gateway

This document explains how microservices can dynamically register and deregister themselves with the API Gateway.

## Registration API

### Register Service Instance

**Endpoint**: `POST /gateway/services/{service-name}/instances`
**Authentication**: Required (JWT token)
**Content-Type**: `application/json`

**Request Body**:
```json
{
  "id": "unique-instance-id",
  "url": "http://service-host:port",
  "version": "1.0.0",
  "tags": ["tag1", "tag2"],
  "metadata": {
    "environment": "production",
    "region": "us-west-2",
    "custom_key": "custom_value"
  }
}
```

**Response** (201 Created):
```json
{
  "message": "Service instance registered successfully"
}
```

### Deregister Service Instance

**Endpoint**: `DELETE /gateway/services/{service-name}/instances/{instance-id}`
**Authentication**: Required (JWT token)

**Response** (200 OK):
```json
{
  "message": "Service instance deregistered successfully"
}
```

## Implementation Examples

### 1. Go Service Registration

```go
package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "net/http"
    "os"
    "os/signal"
    "syscall"
    "time"
)

type ServiceInstance struct {
    ID       string            `json:"id"`
    URL      string            `json:"url"`
    Version  string            `json:"version"`
    Tags     []string          `json:"tags"`
    Metadata map[string]string `json:"metadata"`
}

type ServiceRegistrar struct {
    gatewayURL   string
    serviceName  string
    instanceID   string
    serviceURL   string
    authToken    string
    client       *http.Client
}

func NewServiceRegistrar(gatewayURL, serviceName, instanceID, serviceURL, authToken string) *ServiceRegistrar {
    return &ServiceRegistrar{
        gatewayURL:  gatewayURL,
        serviceName: serviceName,
        instanceID:  instanceID,
        serviceURL:  serviceURL,
        authToken:   authToken,
        client: &http.Client{
            Timeout: 10 * time.Second,
        },
    }
}

func (sr *ServiceRegistrar) Register() error {
    instance := ServiceInstance{
        ID:      sr.instanceID,
        URL:     sr.serviceURL,
        Version: "1.0.0",
        Tags:    []string{"api", "microservice"},
        Metadata: map[string]string{
            "environment": os.Getenv("ENVIRONMENT"),
            "region":      os.Getenv("REGION"),
            "started_at":  time.Now().UTC().Format(time.RFC3339),
        },
    }

    jsonData, err := json.Marshal(instance)
    if err != nil {
        return fmt.Errorf("failed to marshal instance data: %w", err)
    }

    url := fmt.Sprintf("%s/gateway/services/%s/instances", sr.gatewayURL, sr.serviceName)
    req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
    if err != nil {
        return fmt.Errorf("failed to create request: %w", err)
    }

    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer "+sr.authToken)

    resp, err := sr.client.Do(req)
    if err != nil {
        return fmt.Errorf("failed to register service: %w", err)
    }
    defer resp.Body.Close()

    if resp.StatusCode != http.StatusCreated {
        return fmt.Errorf("registration failed with status: %d", resp.StatusCode)
    }

    fmt.Printf("Service %s instance %s registered successfully\n", sr.serviceName, sr.instanceID)
    return nil
}

func (sr *ServiceRegistrar) Deregister() error {
    url := fmt.Sprintf("%s/gateway/services/%s/instances/%s", 
        sr.gatewayURL, sr.serviceName, sr.instanceID)
    
    req, err := http.NewRequest("DELETE", url, nil)
    if err != nil {
        return fmt.Errorf("failed to create deregister request: %w", err)
    }

    req.Header.Set("Authorization", "Bearer "+sr.authToken)

    resp, err := sr.client.Do(req)
    if err != nil {
        return fmt.Errorf("failed to deregister service: %w", err)
    }
    defer resp.Body.Close()

    if resp.StatusCode != http.StatusOK {
        return fmt.Errorf("deregistration failed with status: %d", resp.StatusCode)
    }

    fmt.Printf("Service %s instance %s deregistered successfully\n", sr.serviceName, sr.instanceID)
    return nil
}

// Example usage in a microservice
func main() {
    // Service configuration
    gatewayURL := os.Getenv("GATEWAY_URL")     // http://localhost:8000
    serviceName := os.Getenv("SERVICE_NAME")   // e.g., "user-service"
    instanceID := os.Getenv("INSTANCE_ID")     // e.g., "user-service-1"
    serviceURL := os.Getenv("SERVICE_URL")     // e.g., "http://localhost:8080"
    authToken := os.Getenv("AUTH_TOKEN")       // JWT token for authentication

    if gatewayURL == "" || serviceName == "" || instanceID == "" || serviceURL == "" || authToken == "" {
        fmt.Println("Missing required environment variables")
        os.Exit(1)
    }

    registrar := NewServiceRegistrar(gatewayURL, serviceName, instanceID, serviceURL, authToken)

    // Register on startup
    if err := registrar.Register(); err != nil {
        fmt.Printf("Failed to register service: %v\n", err)
        os.Exit(1)
    }

    // Setup graceful shutdown
    c := make(chan os.Signal, 1)
    signal.Notify(c, os.Interrupt, syscall.SIGTERM)

    // Wait for shutdown signal
    <-c
    fmt.Println("Shutting down...")

    // Deregister on shutdown
    if err := registrar.Deregister(); err != nil {
        fmt.Printf("Failed to deregister service: %v\n", err)
    }

    fmt.Println("Service stopped")
}
```

### 2. Integration with Existing Services

For existing services, add registration to the startup process:

```go
// In your existing service main.go
func main() {
    // ... existing initialization code ...

    // Service registration
    if gatewayURL := os.Getenv("GATEWAY_URL"); gatewayURL != "" {
        registrar := NewServiceRegistrar(
            gatewayURL,
            "deployment-service", // service name
            generateInstanceID(), // unique instance ID
            fmt.Sprintf("http://localhost:%d", cfg.Server.Port),
            getAuthToken(), // get JWT token
        )

        // Register with gateway
        if err := registrar.Register(); err != nil {
            logger.Error("Failed to register with gateway", zap.Error(err))
            // Don't exit - service can still work without gateway
        }

        // Setup deregistration on shutdown
        defer func() {
            if err := registrar.Deregister(); err != nil {
                logger.Error("Failed to deregister from gateway", zap.Error(err))
            }
        }()
    }

    // ... rest of your service code ...
}

func generateInstanceID() string {
    hostname, _ := os.Hostname()
    return fmt.Sprintf("%s-%d", hostname, os.Getpid())
}

func getAuthToken() string {
    // Option 1: Service-to-service token
    return os.Getenv("SERVICE_AUTH_TOKEN")
    
    // Option 2: Get token from auth service
    // return requestServiceToken()
}
```

### 3. Docker Compose Integration

```yaml
services:
  user-service:
    build: ./backend/user-service
    environment:
      - GATEWAY_URL=http://gateway-service:8000
      - SERVICE_NAME=user-service
      - INSTANCE_ID=user-service-${HOSTNAME:-1}
      - SERVICE_URL=http://user-service:8080
      - AUTH_TOKEN=${SERVICE_AUTH_TOKEN}
    depends_on:
      - gateway-service
```

### 4. Kubernetes Integration

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
spec:
  replicas: 3
  template:
    spec:
      containers:
      - name: user-service
        image: user-service:latest
        env:
        - name: GATEWAY_URL
          value: "http://gateway-service:8000"
        - name: SERVICE_NAME
          value: "user-service"
        - name: INSTANCE_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: SERVICE_URL
          value: "http://$(POD_IP):8080"
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: AUTH_TOKEN
          valueFrom:
            secretKeyRef:
              name: service-auth
              key: token
```

## Registration Strategies

### 1. Startup Registration
- Register immediately after service starts
- Include health check endpoint in registration
- Set appropriate metadata (version, environment, etc.)

### 2. Health-Based Registration
- Only register after service passes health checks
- Implement retry logic for failed registrations
- Monitor service health and re-register if needed

### 3. Graceful Deregistration
- Always deregister on shutdown
- Use signal handlers for graceful shutdown
- Implement timeout for deregistration

## Best Practices

### 1. Instance ID Generation
```go
func generateInstanceID() string {
    hostname, _ := os.Hostname()
    pid := os.Getpid()
    timestamp := time.Now().Unix()
    return fmt.Sprintf("%s-%d-%d", hostname, pid, timestamp)
}
```

### 2. Retry Logic
```go
func (sr *ServiceRegistrar) RegisterWithRetry(maxRetries int) error {
    for i := 0; i < maxRetries; i++ {
        if err := sr.Register(); err == nil {
            return nil
        }
        time.Sleep(time.Duration(i+1) * time.Second)
    }
    return fmt.Errorf("failed to register after %d retries", maxRetries)
}
```

### 3. Health Check Integration
```go
func (sr *ServiceRegistrar) RegisterWithHealthCheck() error {
    // Wait for service to be healthy before registering
    if err := sr.waitForHealthy(); err != nil {
        return err
    }
    return sr.Register()
}

func (sr *ServiceRegistrar) waitForHealthy() error {
    healthURL := sr.serviceURL + "/health"
    for i := 0; i < 30; i++ { // 30 second timeout
        resp, err := http.Get(healthURL)
        if err == nil && resp.StatusCode == 200 {
            resp.Body.Close()
            return nil
        }
        if resp != nil {
            resp.Body.Close()
        }
        time.Sleep(1 * time.Second)
    }
    return fmt.Errorf("service not healthy after 30 seconds")
}
```

## Error Handling

### Common Error Scenarios
1. **Gateway Unavailable**: Service should continue without registration
2. **Authentication Failed**: Check token validity and refresh if needed
3. **Network Issues**: Implement retry with exponential backoff
4. **Duplicate Registration**: Handle gracefully (gateway will update existing)

### Example Error Handling
```go
func (sr *ServiceRegistrar) SafeRegister() {
    if err := sr.RegisterWithRetry(3); err != nil {
        // Log error but don't fail service startup
        fmt.Printf("Warning: Failed to register with gateway: %v\n", err)
        fmt.Println("Service will continue without gateway registration")
    }
}
```

This approach allows services to be resilient and continue operating even if the gateway is temporarily unavailable.
