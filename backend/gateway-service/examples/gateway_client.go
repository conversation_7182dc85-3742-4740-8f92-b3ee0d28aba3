package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"go.uber.org/zap"
)

// GatewayClient handles service registration with the API Gateway
type GatewayClient struct {
	gatewayURL  string
	serviceName string
	instanceID  string
	serviceURL  string
	authToken   string
	client      *http.Client
	logger      *zap.Logger
	registered  bool
}

// ServiceInstance represents a service instance for registration
type ServiceInstance struct {
	ID       string            `json:"id"`
	URL      string            `json:"url"`
	Version  string            `json:"version"`
	Tags     []string          `json:"tags"`
	Metadata map[string]string `json:"metadata"`
}

// NewGatewayClient creates a new gateway client
func NewGatewayClient(gatewayURL, serviceName, instanceID, serviceURL, authToken string, logger *zap.Logger) *GatewayClient {
	return &GatewayClient{
		gatewayURL:  gatewayURL,
		serviceName: serviceName,
		instanceID:  instanceID,
		serviceURL:  serviceURL,
		authToken:   authToken,
		logger:      logger,
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// Register registers the service instance with the gateway
func (gc *GatewayClient) Register() error {
	if gc.gatewayURL == "" {
		gc.logger.Info("Gateway URL not configured, skipping registration")
		return nil
	}

	instance := ServiceInstance{
		ID:      gc.instanceID,
		URL:     gc.serviceURL,
		Version: getServiceVersion(),
		Tags:    getServiceTags(),
		Metadata: map[string]string{
			"environment": getEnv("ENVIRONMENT", "development"),
			"region":      getEnv("REGION", "local"),
			"started_at":  time.Now().UTC().Format(time.RFC3339),
			"hostname":    getHostname(),
		},
	}

	jsonData, err := json.Marshal(instance)
	if err != nil {
		return fmt.Errorf("failed to marshal instance data: %w", err)
	}

	url := fmt.Sprintf("%s/gateway/services/%s/instances", gc.gatewayURL, gc.serviceName)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if gc.authToken != "" {
		req.Header.Set("Authorization", "Bearer "+gc.authToken)
	}

	resp, err := gc.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to register service: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusCreated {
		gc.registered = true
		gc.logger.Info("Service registered with gateway",
			zap.String("service", gc.serviceName),
			zap.String("instance_id", gc.instanceID),
			zap.String("url", gc.serviceURL))
		return nil
	}

	return fmt.Errorf("registration failed with status: %d", resp.StatusCode)
}

// Deregister removes the service instance from the gateway
func (gc *GatewayClient) Deregister() error {
	if !gc.registered || gc.gatewayURL == "" {
		return nil
	}

	url := fmt.Sprintf("%s/gateway/services/%s/instances/%s",
		gc.gatewayURL, gc.serviceName, gc.instanceID)

	req, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create deregister request: %w", err)
	}

	if gc.authToken != "" {
		req.Header.Set("Authorization", "Bearer "+gc.authToken)
	}

	resp, err := gc.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to deregister service: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		gc.registered = false
		gc.logger.Info("Service deregistered from gateway",
			zap.String("service", gc.serviceName),
			zap.String("instance_id", gc.instanceID))
		return nil
	}

	return fmt.Errorf("deregistration failed with status: %d", resp.StatusCode)
}

// RegisterWithRetry attempts to register with retry logic
func (gc *GatewayClient) RegisterWithRetry(maxRetries int) error {
	var lastErr error
	for i := 0; i < maxRetries; i++ {
		if err := gc.Register(); err == nil {
			return nil
		} else {
			lastErr = err
			gc.logger.Warn("Registration attempt failed, retrying",
				zap.Int("attempt", i+1),
				zap.Int("max_retries", maxRetries),
				zap.Error(err))
			time.Sleep(time.Duration(i+1) * time.Second)
		}
	}
	return fmt.Errorf("failed to register after %d retries: %w", maxRetries, lastErr)
}

// SafeRegister registers with the gateway but doesn't fail if registration fails
func (gc *GatewayClient) SafeRegister() {
	if err := gc.RegisterWithRetry(3); err != nil {
		gc.logger.Warn("Failed to register with gateway, service will continue without registration",
			zap.Error(err))
	}
}

// SafeDeregister deregisters from the gateway but doesn't fail if deregistration fails
func (gc *GatewayClient) SafeDeregister() {
	if err := gc.Deregister(); err != nil {
		gc.logger.Warn("Failed to deregister from gateway",
			zap.Error(err))
	}
}

// IsRegistered returns whether the service is currently registered
func (gc *GatewayClient) IsRegistered() bool {
	return gc.registered
}

// Helper functions

func getServiceVersion() string {
	if version := os.Getenv("SERVICE_VERSION"); version != "" {
		return version
	}
	return "1.0.0"
}

func getServiceTags() []string {
	if tags := os.Getenv("SERVICE_TAGS"); tags != "" {
		// Simple comma-separated tags
		return []string{tags}
	}
	return []string{"api", "microservice"}
}

func getHostname() string {
	if hostname, err := os.Hostname(); err == nil {
		return hostname
	}
	return "unknown"
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// Example integration function for existing services
func IntegrateGatewayRegistration(serviceName string, servicePort int, logger *zap.Logger) *GatewayClient {
	// Configuration from environment variables
	gatewayURL := os.Getenv("GATEWAY_URL")
	instanceID := generateInstanceID()
	serviceURL := fmt.Sprintf("http://localhost:%d", servicePort)
	authToken := os.Getenv("GATEWAY_AUTH_TOKEN")

	// Create gateway client
	client := NewGatewayClient(gatewayURL, serviceName, instanceID, serviceURL, authToken, logger)

	// Register with gateway (safe - won't fail service startup)
	client.SafeRegister()

	return client
}

func generateInstanceID() string {
	hostname := getHostname()
	pid := os.Getpid()
	timestamp := time.Now().Unix()
	return fmt.Sprintf("%s-%d-%d", hostname, pid, timestamp)
}

// Example usage in a service main function:
/*
func main() {
    // ... existing service initialization ...

    logger, _ := zap.NewProduction()
    defer logger.Sync()

    // Integrate gateway registration
    gatewayClient := IntegrateGatewayRegistration("my-service", 8080, logger)

    // Setup graceful shutdown
    c := make(chan os.Signal, 1)
    signal.Notify(c, os.Interrupt, syscall.SIGTERM)

    // Start your service...
    go startServer()

    // Wait for shutdown signal
    <-c
    logger.Info("Shutting down...")

    // Deregister from gateway
    gatewayClient.SafeDeregister()

    // ... rest of shutdown logic ...
}
*/
