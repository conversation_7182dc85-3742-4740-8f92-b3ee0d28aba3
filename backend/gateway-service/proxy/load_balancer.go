package proxy

import (
	"fmt"
	"sync"
	"time"

	"github.com/claudio/deploy-orchestrator/gateway-service/discovery"
	"go.uber.org/zap"
)

// LoadBalancerType defines the type of load balancing algorithm
type LoadBalancerType string

const (
	RoundRobin         LoadBalancerType = "round_robin"
	LeastConnections   LoadBalancerType = "least_connections"
	WeightedRoundRobin LoadBalancerType = "weighted_round_robin"
)

// LoadBalancer implements load balancing algorithms
type LoadBalancer struct {
	algorithm LoadBalancerType
	logger    *zap.Logger

	// Round robin state
	roundRobinCounters map[string]int
	mutex              sync.RWMutex

	// Connection tracking for least connections
	connectionCounts map[string]int

	// Metrics tracking
	metrics map[string]*InstanceMetrics
}

// InstanceMetrics tracks metrics for load balancing decisions
type InstanceMetrics struct {
	ActiveConnections int           `json:"active_connections"`
	TotalRequests     int64         `json:"total_requests"`
	FailedRequests    int64         `json:"failed_requests"`
	AverageResponse   time.Duration `json:"average_response"`
	LastUsed          time.Time     `json:"last_used"`
	Weight            int           `json:"weight"`
}

// NewLoadBalancer creates a new load balancer
func NewLoadBalancer(algorithm LoadBalancerType, logger *zap.Logger) *LoadBalancer {
	return &LoadBalancer{
		algorithm:          algorithm,
		logger:             logger,
		roundRobinCounters: make(map[string]int),
		connectionCounts:   make(map[string]int),
		metrics:            make(map[string]*InstanceMetrics),
	}
}

// SelectInstance selects an instance based on the configured algorithm
func (lb *LoadBalancer) SelectInstance(serviceName string, instances []*discovery.ServiceInstance) (*discovery.ServiceInstance, error) {
	if len(instances) == 0 {
		return nil, fmt.Errorf("no healthy instances available for service: %s", serviceName)
	}

	// Filter only healthy instances
	healthyInstances := make([]*discovery.ServiceInstance, 0)
	for _, instance := range instances {
		if instance.Healthy {
			healthyInstances = append(healthyInstances, instance)
		}
	}

	if len(healthyInstances) == 0 {
		return nil, fmt.Errorf("no healthy instances available for service: %s", serviceName)
	}

	switch lb.algorithm {
	case RoundRobin:
		return lb.roundRobinSelect(serviceName, healthyInstances), nil
	case LeastConnections:
		return lb.leastConnectionsSelect(healthyInstances), nil
	case WeightedRoundRobin:
		return lb.weightedRoundRobinSelect(serviceName, healthyInstances), nil
	default:
		return lb.roundRobinSelect(serviceName, healthyInstances), nil
	}
}

// roundRobinSelect implements round-robin load balancing
func (lb *LoadBalancer) roundRobinSelect(serviceName string, instances []*discovery.ServiceInstance) *discovery.ServiceInstance {
	lb.mutex.Lock()
	defer lb.mutex.Unlock()

	counter := lb.roundRobinCounters[serviceName]
	selected := instances[counter%len(instances)]
	lb.roundRobinCounters[serviceName] = (counter + 1) % len(instances)

	lb.logger.Debug("Round robin selection",
		zap.String("service", serviceName),
		zap.String("selected_instance", selected.ID),
		zap.Int("counter", counter))

	return selected
}

// leastConnectionsSelect implements least connections load balancing
func (lb *LoadBalancer) leastConnectionsSelect(instances []*discovery.ServiceInstance) *discovery.ServiceInstance {
	lb.mutex.RLock()
	defer lb.mutex.RUnlock()

	var selected *discovery.ServiceInstance
	minConnections := int(^uint(0) >> 1) // Max int

	for _, instance := range instances {
		connections := lb.connectionCounts[instance.ID]
		if connections < minConnections {
			minConnections = connections
			selected = instance
		}
	}

	if selected == nil {
		selected = instances[0] // Fallback to first instance
	}

	lb.logger.Debug("Least connections selection",
		zap.String("selected_instance", selected.ID),
		zap.Int("connections", minConnections))

	return selected
}

// weightedRoundRobinSelect implements weighted round-robin load balancing
func (lb *LoadBalancer) weightedRoundRobinSelect(serviceName string, instances []*discovery.ServiceInstance) *discovery.ServiceInstance {
	lb.mutex.Lock()
	defer lb.mutex.Unlock()

	// For simplicity, use equal weights if not specified
	totalWeight := 0
	for _, instance := range instances {
		weight := instance.Weight
		if weight <= 0 {
			weight = 1 // Default weight
		}
		totalWeight += weight
	}

	counter := lb.roundRobinCounters[serviceName]
	targetWeight := counter % totalWeight

	currentWeight := 0
	for _, instance := range instances {
		weight := instance.Weight
		if weight <= 0 {
			weight = 1
		}
		currentWeight += weight
		if targetWeight < currentWeight {
			lb.roundRobinCounters[serviceName] = (counter + 1) % totalWeight

			lb.logger.Debug("Weighted round robin selection",
				zap.String("service", serviceName),
				zap.String("selected_instance", instance.ID),
				zap.Int("weight", weight),
				zap.Int("counter", counter))

			return instance
		}
	}

	// Fallback to first instance
	lb.roundRobinCounters[serviceName] = (counter + 1) % totalWeight
	return instances[0]
}

// IncrementConnections increments the connection count for an instance
func (lb *LoadBalancer) IncrementConnections(instanceID string) {
	lb.mutex.Lock()
	defer lb.mutex.Unlock()

	lb.connectionCounts[instanceID]++

	if metrics, exists := lb.metrics[instanceID]; exists {
		metrics.ActiveConnections++
		metrics.TotalRequests++
		metrics.LastUsed = time.Now()
	} else {
		lb.metrics[instanceID] = &InstanceMetrics{
			ActiveConnections: 1,
			TotalRequests:     1,
			LastUsed:          time.Now(),
			Weight:            1,
		}
	}
}

// DecrementConnections decrements the connection count for an instance
func (lb *LoadBalancer) DecrementConnections(instanceID string) {
	lb.mutex.Lock()
	defer lb.mutex.Unlock()

	if lb.connectionCounts[instanceID] > 0 {
		lb.connectionCounts[instanceID]--
	}

	if metrics, exists := lb.metrics[instanceID]; exists && metrics.ActiveConnections > 0 {
		metrics.ActiveConnections--
	}
}

// RecordFailure records a failed request for an instance
func (lb *LoadBalancer) RecordFailure(instanceID string) {
	lb.mutex.Lock()
	defer lb.mutex.Unlock()

	if metrics, exists := lb.metrics[instanceID]; exists {
		metrics.FailedRequests++
	} else {
		lb.metrics[instanceID] = &InstanceMetrics{
			FailedRequests: 1,
			Weight:         1,
		}
	}
}

// UpdateResponseTime updates the average response time for an instance
func (lb *LoadBalancer) UpdateResponseTime(instanceID string, responseTime time.Duration) {
	lb.mutex.Lock()
	defer lb.mutex.Unlock()

	if metrics, exists := lb.metrics[instanceID]; exists {
		// Simple moving average (could be improved with exponential moving average)
		if metrics.AverageResponse == 0 {
			metrics.AverageResponse = responseTime
		} else {
			metrics.AverageResponse = (metrics.AverageResponse + responseTime) / 2
		}
	}
}

// GetMetrics returns load balancing metrics for all instances
func (lb *LoadBalancer) GetMetrics() map[string]*InstanceMetrics {
	lb.mutex.RLock()
	defer lb.mutex.RUnlock()

	// Return a copy to prevent external modification
	metrics := make(map[string]*InstanceMetrics)
	for instanceID, metric := range lb.metrics {
		metricCopy := *metric
		metrics[instanceID] = &metricCopy
	}

	return metrics
}

// GetConnectionCounts returns current connection counts
func (lb *LoadBalancer) GetConnectionCounts() map[string]int {
	lb.mutex.RLock()
	defer lb.mutex.RUnlock()

	counts := make(map[string]int)
	for instanceID, count := range lb.connectionCounts {
		counts[instanceID] = count
	}

	return counts
}

// Reset resets all load balancer state
func (lb *LoadBalancer) Reset() {
	lb.mutex.Lock()
	defer lb.mutex.Unlock()

	lb.roundRobinCounters = make(map[string]int)
	lb.connectionCounts = make(map[string]int)
	lb.metrics = make(map[string]*InstanceMetrics)

	lb.logger.Info("Load balancer state reset")
}
