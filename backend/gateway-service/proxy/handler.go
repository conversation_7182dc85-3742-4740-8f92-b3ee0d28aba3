package proxy

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/claudio/deploy-orchestrator/gateway-service/config"
	"github.com/claudio/deploy-orchestrator/gateway-service/discovery"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ProxyHandler handles HTTP request proxying to backend services
type ProxyHandler struct {
	registry     discovery.ServiceRegistry
	loadBalancer *LoadBalancer
	logger       *zap.Logger

	// Configuration
	requestTimeout    time.Duration
	connectionTimeout time.Duration

	// HTTP client for proxying requests
	client *http.Client
}

// NewProxyHandler creates a new proxy handler
func NewProxyHandler(registry discovery.ServiceRegistry, loadBalancer *LoadBalancer, logger *zap.Logger, requestTimeout, connectionTimeout time.Duration) *ProxyHandler {
	client := &http.Client{
		Timeout: requestTimeout,
		Transport: &http.Transport{
			ResponseHeaderTimeout: connectionTimeout,
			DisableKeepAlives:     false,
			MaxIdleConns:          100,
			MaxIdleConnsPerHost:   10,
		},
	}

	return &ProxyHandler{
		registry:          registry,
		loadBalancer:      loadBalancer,
		logger:            logger,
		requestTimeout:    requestTimeout,
		connectionTimeout: connectionTimeout,
		client:            client,
	}
}

// ProxyRequest handles the main proxy logic
func (ph *ProxyHandler) ProxyRequest(c *gin.Context) {
	startTime := time.Now()

	// Extract service name from the path
	serviceName := ph.extractServiceName(c.Request.URL.Path)
	if serviceName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid service path"})
		return
	}

	// Discover service instances
	instances, err := ph.registry.Discover(serviceName)
	if err != nil {
		ph.logger.Error("Service discovery failed",
			zap.String("service", serviceName),
			zap.Error(err))
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Service not available"})
		return
	}

	// Select an instance using load balancer
	instance, err := ph.loadBalancer.SelectInstance(serviceName, instances)
	if err != nil {
		ph.logger.Error("Load balancer selection failed",
			zap.String("service", serviceName),
			zap.Error(err))
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "No healthy instances available"})
		return
	}

	// Track connection
	ph.loadBalancer.IncrementConnections(instance.ID)
	defer ph.loadBalancer.DecrementConnections(instance.ID)

	// Build target URL
	targetURL := ph.buildTargetURL(instance, c.Request.URL.Path, c.Request.URL.RawQuery)

	// Create proxy request
	proxyReq, err := ph.createProxyRequest(c.Request, targetURL)
	if err != nil {
		ph.logger.Error("Failed to create proxy request",
			zap.String("target_url", targetURL),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create proxy request"})
		return
	}

	// Copy headers from original request
	ph.copyHeaders(c.Request.Header, proxyReq.Header)

	// Add gateway headers
	ph.addGatewayHeaders(proxyReq, instance, serviceName)

	// Execute proxy request
	resp, err := ph.client.Do(proxyReq)
	if err != nil {
		ph.logger.Error("Proxy request failed",
			zap.String("service", serviceName),
			zap.String("instance", instance.ID),
			zap.String("target_url", targetURL),
			zap.Error(err))

		ph.loadBalancer.RecordFailure(instance.ID)
		c.JSON(http.StatusBadGateway, gin.H{"error": "Backend service error"})
		return
	}
	defer resp.Body.Close()

	// Record response time
	responseTime := time.Since(startTime)
	ph.loadBalancer.UpdateResponseTime(instance.ID, responseTime)

	// Copy response headers
	ph.copyHeaders(resp.Header, c.Writer.Header())

	// Add gateway response headers
	ph.addGatewayResponseHeaders(c, instance, serviceName, responseTime)

	// Set status code
	c.Status(resp.StatusCode)

	// Copy response body
	if _, err := io.Copy(c.Writer, resp.Body); err != nil {
		ph.logger.Error("Failed to copy response body",
			zap.String("service", serviceName),
			zap.String("instance", instance.ID),
			zap.Error(err))
		return
	}

	ph.logger.Debug("Proxy request completed",
		zap.String("service", serviceName),
		zap.String("instance", instance.ID),
		zap.String("method", c.Request.Method),
		zap.String("path", c.Request.URL.Path),
		zap.Int("status", resp.StatusCode),
		zap.Duration("response_time", responseTime))
}

// extractServiceName extracts the service name from the request path
func (ph *ProxyHandler) extractServiceName(path string) string {
	// Expected format: /api/v1/{service-name}/...
	// or: /{service-name}/...

	// First check if the full path is mapped to a service
	fullPath := strings.TrimPrefix(path, "/")
	pathParts := strings.SplitN(fullPath, "/", 2)
	if len(pathParts) > 0 {
		// Check if api/v1/deployables pattern
		if strings.HasPrefix(fullPath, "api/v1/") {
			apiPath := strings.SplitN(fullPath, "/", 3)
			if len(apiPath) >= 3 {
				apiEndpoint := "api/v1/" + apiPath[2]
				// Check if this API endpoint is mapped to a service
				if mappedService, exists := ph.getMappedService(apiEndpoint); exists {
					return mappedService
				}
			}
		}
	}

	parts := strings.Split(strings.Trim(path, "/"), "/")
	if len(parts) == 0 {
		return ""
	}

	// Handle /api/v1/{service-name} format
	if len(parts) >= 3 && parts[0] == "api" && parts[1] == "v1" {
		return parts[2]
	}

	// Handle /{service-name} format
	if len(parts) >= 1 {
		return parts[0]
	}

	return ""
}

// getMappedService checks if a path is mapped to a specific service
func (ph *ProxyHandler) getMappedService(path string) (string, bool) {
	// Use the routes defined in config/routes.go
	service, exists := config.ServiceRouteMapping[path]
	if exists {
		// Extract the service name from the mapping value (e.g., "environment-service/api/v1/environments" -> "environment-service")
		parts := strings.Split(service, "/")
		if len(parts) > 0 {
			return parts[0], true
		}
	}

	return "", false
}

// buildTargetURL builds the target URL for the backend service
func (ph *ProxyHandler) buildTargetURL(instance *discovery.ServiceInstance, path, query string) string {
	// Special handling for /api/v1/deployables endpoint
	if strings.HasPrefix(path, "/api/v1/deployables") {
		// Check if we have an ID in the path (e.g., /api/v1/deployables/{id})
		pathParts := strings.Split(strings.TrimPrefix(path, "/api/v1/deployables"), "/")

		// Base path for environment service
		targetPath := "/api/v1/environments"

		// If we have an ID, append it
		if len(pathParts) > 0 && pathParts[0] != "" {
			targetPath += "/" + pathParts[0]
		}

		// Build the target URL
		targetURL := instance.URL + targetPath
		if query != "" {
			targetURL += "?" + query
		}

		ph.logger.Info("Special handling for deployables path",
			zap.String("originalPath", path),
			zap.String("targetURL", targetURL),
			zap.String("query", query))

		return targetURL
	}

	// Check if this path should be remapped based on our route mappings
	origPath := strings.TrimPrefix(path, "/")

	// Check for API endpoint mappings like api/v1/deployables
	if strings.HasPrefix(origPath, "api/v1/") {
		pathParts := strings.SplitN(origPath, "/", 3)
		if len(pathParts) >= 3 {
			apiPath := "api/v1/" + pathParts[2]

			// Check if we have a mapping for this API pattern
			if targetPath, exists := config.ServiceRouteMapping[apiPath]; exists {
				// Extract any remainder of the path
				remainderParts := strings.SplitN(origPath, "/", 4)
				remainder := ""
				if len(remainderParts) >= 4 {
					remainder = remainderParts[3]
				}

				// Build the target URL with remapped path
				targetURL := instance.URL + "/" + targetPath
				if remainder != "" {
					targetURL += "/" + remainder
				}
				if query != "" {
					targetURL += "?" + query
				}

				ph.logger.Info("Remapped request path",
					zap.String("originalPath", path),
					zap.String("targetURL", targetURL),
					zap.String("query", query))

				return targetURL
			}
		}
	}

	// Default behavior if no remapping
	targetURL := instance.URL + path
	if query != "" {
		targetURL += "?" + query
	}
	return targetURL
}

// createProxyRequest creates a new HTTP request for proxying
func (ph *ProxyHandler) createProxyRequest(originalReq *http.Request, targetURL string) (*http.Request, error) {
	// Read body if present
	var body io.Reader
	if originalReq.Body != nil {
		bodyBytes, err := io.ReadAll(originalReq.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to read request body: %w", err)
		}
		body = bytes.NewReader(bodyBytes)

		// Reset original request body for potential retries
		originalReq.Body = io.NopCloser(bytes.NewReader(bodyBytes))
	}

	// Create new request
	proxyReq, err := http.NewRequest(originalReq.Method, targetURL, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Copy context
	proxyReq = proxyReq.WithContext(originalReq.Context())

	return proxyReq, nil
}

// copyHeaders copies headers from source to destination
func (ph *ProxyHandler) copyHeaders(src, dst http.Header) {
	for key, values := range src {
		// Skip hop-by-hop headers
		if ph.isHopByHopHeader(key) {
			continue
		}

		for _, value := range values {
			dst.Add(key, value)
		}
	}
}

// isHopByHopHeader checks if a header is hop-by-hop
func (ph *ProxyHandler) isHopByHopHeader(header string) bool {
	hopByHopHeaders := []string{
		"Connection",
		"Keep-Alive",
		"Proxy-Authenticate",
		"Proxy-Authorization",
		"Te",
		"Trailers",
		"Transfer-Encoding",
		"Upgrade",
	}

	header = strings.ToLower(header)
	for _, hopHeader := range hopByHopHeaders {
		if strings.ToLower(hopHeader) == header {
			return true
		}
	}
	return false
}

// addGatewayHeaders adds gateway-specific headers to the request
func (ph *ProxyHandler) addGatewayHeaders(req *http.Request, instance *discovery.ServiceInstance, serviceName string) {
	req.Header.Set("X-Gateway-Service", serviceName)
	req.Header.Set("X-Gateway-Instance", instance.ID)
	req.Header.Set("X-Gateway-Version", instance.Version)
	req.Header.Set("X-Forwarded-By", "deploy-orchestrator-gateway")

	// Preserve original host
	if req.Header.Get("X-Forwarded-Host") == "" {
		req.Header.Set("X-Forwarded-Host", req.Host)
	}

	// Set forwarded proto
	if req.Header.Get("X-Forwarded-Proto") == "" {
		if req.TLS != nil {
			req.Header.Set("X-Forwarded-Proto", "https")
		} else {
			req.Header.Set("X-Forwarded-Proto", "http")
		}
	}
}

// addGatewayResponseHeaders adds gateway-specific headers to the response
func (ph *ProxyHandler) addGatewayResponseHeaders(c *gin.Context, instance *discovery.ServiceInstance, serviceName string, responseTime time.Duration) {
	c.Header("X-Gateway-Service", serviceName)
	c.Header("X-Gateway-Instance", instance.ID)
	c.Header("X-Gateway-Response-Time", responseTime.String())
	c.Header("X-Gateway-Timestamp", time.Now().UTC().Format(time.RFC3339))
}
