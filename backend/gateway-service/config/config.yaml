# Gateway Service Configuration
server:
  host: "0.0.0.0"
  port: 8000
  read_timeout: 30
  write_timeout: 30

service:
  name: "gateway-service"
  version: "1.0.0"

logging:
  level: "info"
  format: "json"
  output: "stdout"

tracing:
  service_name: "gateway-service"
  enabled: true

# Auth Configuration
auth:
  jwt_secret: "default-secret-key-in-production"
  jwt_expiration_minutes: 60
  access_token_expiry: "1h"
  refresh_token_expiry: "168h"
  admin_service_url: "http://localhost:8086"
  api_key_header: "X-API-Key"
  disable: false

gateway:
  discovery:
    method: "memory"
    health_check_interval: 30
    health_check_timeout: 5
    service_ttl: 60
    static_services:
      - name: "admin-service"
        instances:
          - "http://localhost:8086"
        health_url: "/health"
        version: "1.0.0"
        tags: ["admin", "auth"]

      - name: "audit-service"
        instances:
          - "http://localhost:8081"
        health_url: "/health"
        version: "1.0.0"
        tags: ["audit", "logging"]

      - name: "deployment-service"
        instances:
          - "http://localhost:8082"
        health_url: "/health"
        version: "1.0.0"
        tags: ["deployment", "core"]

      - name: "notification-service"
        instances:
          - "http://localhost:8083"
        health_url: "/health"
        version: "1.0.0"
        tags: ["notification", "messaging"]

      - name: "scheduling-service"
        instances:
          - "http://localhost:8084"
        health_url: "/health"
        version: "1.0.0"
        tags: ["scheduling", "cron"]

      - name: "integration-service"
        instances:
          - "http://localhost:8083"
        health_url: "/health"
        version: "1.0.0"
        tags: ["integration", "external"]

      - name: "workflow-service"
        instances:
          - "http://localhost:8085"
        health_url: "/health"
        version: "1.0.0"
        tags: ["workflow", "orchestration"]

  load_balancer:
    algorithm: "round_robin"
    health_check_enabled: true

  rate_limit:
    enabled: true
    requests_per_minute: 1000
    burst_size: 100

  circuit_breaker:
    enabled: true
    failure_threshold: 5
    recovery_timeout: 60
    success_threshold: 3

  timeouts:
    request_timeout: 30
    connection_timeout: 10
    idle_timeout: 60
