package config

import (
	"fmt"

	sharedConfig "github.com/claudio/deploy-orchestrator/shared/config"
)

// GatewayServiceConfig holds the configuration for the API gateway service
type GatewayServiceConfig struct {
	// Common configuration sections from shared module
	Database sharedConfig.DBConfig      `mapstructure:"database" yaml:"database"`
	Server   sharedConfig.ServerConfig  `mapstructure:"server" yaml:"server"`
	Logging  sharedConfig.LoggingConfig `mapstructure:"logging" yaml:"logging"`
	Tracing  sharedConfig.TracingConfig `mapstructure:"tracing" yaml:"tracing"`
	Auth     sharedConfig.AuthConfig    `mapstructure:"auth" yaml:"auth"`
	Service  sharedConfig.ServiceConfig `mapstructure:"service" yaml:"service"`

	// Gateway-specific configuration
	Gateway GatewayConfig `mapstructure:"gateway" yaml:"gateway"`
}

// GatewayConfig holds gateway-specific configuration
type GatewayConfig struct {
	// Service discovery configuration
	Discovery DiscoveryConfig `mapstructure:"discovery" yaml:"discovery"`

	// Load balancing configuration
	LoadBalancer LoadBalancerConfig `mapstructure:"load_balancer" yaml:"load_balancer"`

	// Rate limiting configuration
	RateLimit RateLimitConfig `mapstructure:"rate_limit" yaml:"rate_limit"`

	// Circuit breaker configuration
	CircuitBreaker CircuitBreakerConfig `mapstructure:"circuit_breaker" yaml:"circuit_breaker"`

	// Timeout configuration
	Timeouts TimeoutConfig `mapstructure:"timeouts" yaml:"timeouts"`
}

// DiscoveryConfig holds service discovery configuration
type DiscoveryConfig struct {
	// Discovery method: "memory", "consul", "etcd", "kubernetes"
	Method string `mapstructure:"method" yaml:"method" env:"DISCOVERY_METHOD" default:"memory"`

	// Health check interval in seconds
	HealthCheckInterval int `mapstructure:"health_check_interval" yaml:"health_check_interval" env:"HEALTH_CHECK_INTERVAL" default:"30"`

	// Health check timeout in seconds
	HealthCheckTimeout int `mapstructure:"health_check_timeout" yaml:"health_check_timeout" env:"HEALTH_CHECK_TIMEOUT" default:"5"`

	// Service registration TTL in seconds
	ServiceTTL int `mapstructure:"service_ttl" yaml:"service_ttl" env:"SERVICE_TTL" default:"60"`

	// Static service definitions (for memory-based discovery)
	StaticServices []StaticServiceConfig `mapstructure:"static_services" yaml:"static_services"`
}

// StaticServiceConfig defines a static service for memory-based discovery
type StaticServiceConfig struct {
	Name      string   `mapstructure:"name" yaml:"name"`
	Instances []string `mapstructure:"instances" yaml:"instances"`
	HealthURL string   `mapstructure:"health_url" yaml:"health_url"`
	Version   string   `mapstructure:"version" yaml:"version"`
	Tags      []string `mapstructure:"tags" yaml:"tags"`
}

// LoadBalancerConfig holds load balancing configuration
type LoadBalancerConfig struct {
	// Load balancing algorithm: "round_robin", "least_connections", "weighted_round_robin"
	Algorithm string `mapstructure:"algorithm" yaml:"algorithm" env:"LB_ALGORITHM" default:"round_robin"`

	// Health check enabled
	HealthCheckEnabled bool `mapstructure:"health_check_enabled" yaml:"health_check_enabled" env:"LB_HEALTH_CHECK_ENABLED" default:"true"`
}

// RateLimitConfig holds rate limiting configuration
type RateLimitConfig struct {
	// Rate limiting enabled
	Enabled bool `mapstructure:"enabled" yaml:"enabled" env:"RATE_LIMIT_ENABLED" default:"true"`

	// Requests per minute per IP
	RequestsPerMinute int `mapstructure:"requests_per_minute" yaml:"requests_per_minute" env:"RATE_LIMIT_RPM" default:"1000"`

	// Burst size
	BurstSize int `mapstructure:"burst_size" yaml:"burst_size" env:"RATE_LIMIT_BURST" default:"100"`
}

// CircuitBreakerConfig holds circuit breaker configuration
type CircuitBreakerConfig struct {
	// Circuit breaker enabled
	Enabled bool `mapstructure:"enabled" yaml:"enabled" env:"CIRCUIT_BREAKER_ENABLED" default:"true"`

	// Failure threshold (number of failures before opening circuit)
	FailureThreshold int `mapstructure:"failure_threshold" yaml:"failure_threshold" env:"CB_FAILURE_THRESHOLD" default:"5"`

	// Recovery timeout in seconds
	RecoveryTimeout int `mapstructure:"recovery_timeout" yaml:"recovery_timeout" env:"CB_RECOVERY_TIMEOUT" default:"60"`

	// Success threshold (number of successes before closing circuit)
	SuccessThreshold int `mapstructure:"success_threshold" yaml:"success_threshold" env:"CB_SUCCESS_THRESHOLD" default:"3"`
}

// TimeoutConfig holds timeout configuration
type TimeoutConfig struct {
	// Request timeout in seconds
	RequestTimeout int `mapstructure:"request_timeout" yaml:"request_timeout" env:"REQUEST_TIMEOUT" default:"30"`

	// Connection timeout in seconds
	ConnectionTimeout int `mapstructure:"connection_timeout" yaml:"connection_timeout" env:"CONNECTION_TIMEOUT" default:"10"`

	// Idle timeout in seconds
	IdleTimeout int `mapstructure:"idle_timeout" yaml:"idle_timeout" env:"IDLE_TIMEOUT" default:"60"`
}

// LoadConfig loads the gateway service configuration from file and environment variables
func LoadConfig() (*GatewayServiceConfig, error) {
	config := &GatewayServiceConfig{
		Server: sharedConfig.ServerConfig{
			Port: 8000, // Default port for API gateway
		},
		Tracing: sharedConfig.TracingConfig{
			ServiceName: "gateway-service",
		},
		Service: sharedConfig.ServiceConfig{
			Name:    "gateway-service",
			Version: "1.0.0",
		},
		Gateway: GatewayConfig{
			Discovery: DiscoveryConfig{
				Method:              "memory",
				HealthCheckInterval: 30,
				HealthCheckTimeout:  5,
				ServiceTTL:          60,
				StaticServices: []StaticServiceConfig{
					{
						Name:      "admin-service",
						Instances: []string{"http://localhost:8080"},
						HealthURL: "/health",
						Version:   "1.0.0",
						Tags:      []string{"admin", "auth"},
					},
					{
						Name:      "audit-service",
						Instances: []string{"http://localhost:8081"},
						HealthURL: "/health",
						Version:   "1.0.0",
						Tags:      []string{"audit", "logging"},
					},
					{
						Name:      "deployment-service",
						Instances: []string{"http://localhost:8082"},
						HealthURL: "/health",
						Version:   "1.0.0",
						Tags:      []string{"deployment", "core"},
					},
					{
						Name:      "notification-service",
						Instances: []string{"http://localhost:8083"},
						HealthURL: "/health",
						Version:   "1.0.0",
						Tags:      []string{"notification", "messaging"},
					},
					{
						Name:      "scheduling-service",
						Instances: []string{"http://localhost:8084"},
						HealthURL: "/health",
						Version:   "1.0.0",
						Tags:      []string{"scheduling", "cron"},
					},
					{
						Name:      "integration-service",
						Instances: []string{"http://localhost:8085"},
						HealthURL: "/health",
						Version:   "1.0.0",
						Tags:      []string{"integration", "external"},
					},
				},
			},
			LoadBalancer: LoadBalancerConfig{
				Algorithm:          "round_robin",
				HealthCheckEnabled: true,
			},
			RateLimit: RateLimitConfig{
				Enabled:           true,
				RequestsPerMinute: 1000,
				BurstSize:         100,
			},
			CircuitBreaker: CircuitBreakerConfig{
				Enabled:          true,
				FailureThreshold: 5,
				RecoveryTimeout:  60,
				SuccessThreshold: 3,
			},
			Timeouts: TimeoutConfig{
				RequestTimeout:    30,
				ConnectionTimeout: 10,
				IdleTimeout:       60,
			},
		},
	}

	if err := sharedConfig.LoadConfig(config, "gateway-service"); err != nil {
		return nil, fmt.Errorf("failed to load gateway service config: %w", err)
	}

	return config, nil
}

// SaveDefaultConfig saves a default configuration file if one doesn't exist
func SaveDefaultConfig() error {
	config, err := LoadConfig()
	if err != nil {
		return err
	}

	return sharedConfig.SaveDefaultConfig(config, "gateway-service")
}
