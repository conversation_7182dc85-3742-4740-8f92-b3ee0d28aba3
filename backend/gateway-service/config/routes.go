package config

// ServiceRouteMapping defines how API routes should be mapped to backend services
var ServiceRouteMapping = map[string]string{
	// Core services
	"admin-service":        "admin-service",
	"application-service":  "application-service",
	"environment-service":  "environment-service",
	"workflow-service":     "workflow-service",
	"audit-service":        "audit-service",
	"scheduling-service":   "scheduling-service",
	"integration-service":  "integration-service",
	"notification-service": "notification-service",

	// API aliases - map frontend expected paths to actual service paths
	"api/v1/deployables":        "environment-service/api/v1/environments",
	"api/v1/deployment-plugins": "workflow-service/api/v1/plugins",
}
