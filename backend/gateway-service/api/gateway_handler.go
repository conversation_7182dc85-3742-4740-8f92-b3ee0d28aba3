package api

import (
	"net/http"
	"time"

	"github.com/claudio/deploy-orchestrator/gateway-service/discovery"
	"github.com/claudio/deploy-orchestrator/gateway-service/middleware"
	"github.com/claudio/deploy-orchestrator/gateway-service/proxy"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// GatewayHandler handles gateway management API endpoints
type GatewayHandler struct {
	registry       discovery.ServiceRegistry
	loadBalancer   *proxy.LoadBalancer
	rateLimiter    *middleware.RateLimiter
	circuitBreaker *middleware.CircuitBreaker
	logger         *zap.Logger
}

// NewGatewayHandler creates a new gateway handler
func NewGatewayHandler(
	registry discovery.ServiceRegistry,
	loadBalancer *proxy.LoadBalancer,
	rateLimiter *middleware.RateLimiter,
	circuitBreaker *middleware.CircuitBreaker,
	logger *zap.Logger,
) *GatewayHandler {
	return &GatewayHandler{
		registry:       registry,
		loadBalancer:   loadBalancer,
		rateLimiter:    rateLimiter,
		circuitBreaker: circuitBreaker,
		logger:         logger,
	}
}

// RegisterRoutes registers gateway management routes
func (gh *GatewayHandler) RegisterRoutes(router *gin.Engine) {
	api := router.Group("/gateway")
	{
		// Gateway status and health
		api.GET("/status", gh.GetGatewayStatus)
		api.GET("/health", gh.GetGatewayHealth)

		// Service discovery
		api.GET("/services", gh.GetServices)
		api.GET("/services/:name", gh.GetService)
		api.POST("/services/:name/instances", gh.RegisterServiceInstance)
		api.DELETE("/services/:name/instances/:id", gh.DeregisterServiceInstance)

		// Load balancer management
		api.GET("/load-balancer/metrics", gh.GetLoadBalancerMetrics)
		api.POST("/load-balancer/reset", gh.ResetLoadBalancer)

		// Rate limiter management
		api.GET("/rate-limiter/stats", gh.GetRateLimiterStats)

		// Circuit breaker management
		api.GET("/circuit-breaker/status", gh.GetCircuitBreakerStatus)
		api.POST("/circuit-breaker/reset", gh.ResetCircuitBreakers)
		api.POST("/circuit-breaker/reset/:service", gh.ResetCircuitBreaker)

		// Gateway configuration
		api.GET("/config", gh.GetGatewayConfig)
	}
}

// GetGatewayStatus returns the overall gateway status
func (gh *GatewayHandler) GetGatewayStatus(c *gin.Context) {
	services, err := gh.registry.GetServices()
	if err != nil {
		gh.logger.Error("Failed to get services", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get services"})
		return
	}

	totalServices := len(services)
	healthyServices := 0
	totalInstances := 0
	healthyInstances := 0

	for _, service := range services {
		hasHealthyInstance := false
		for _, instance := range service.Instances {
			totalInstances++
			if instance.Healthy {
				healthyInstances++
				hasHealthyInstance = true
			}
		}
		if hasHealthyInstance {
			healthyServices++
		}
	}

	status := map[string]interface{}{
		"gateway": map[string]interface{}{
			"status":    "running",
			"timestamp": time.Now().UTC(),
			"uptime":    time.Since(time.Now().Add(-time.Hour)), // Placeholder
		},
		"services": map[string]interface{}{
			"total":             totalServices,
			"healthy":           healthyServices,
			"total_instances":   totalInstances,
			"healthy_instances": healthyInstances,
		},
		"load_balancer": map[string]interface{}{
			"active_connections": gh.loadBalancer.GetConnectionCounts(),
		},
		"circuit_breaker": gh.circuitBreaker.GetCircuitStatus(),
		"rate_limiter":    gh.rateLimiter.GetStats(),
	}

	c.JSON(http.StatusOK, status)
}

// GetGatewayHealth returns gateway health status
func (gh *GatewayHandler) GetGatewayHealth(c *gin.Context) {
	health := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now().UTC(),
		"checks": map[string]interface{}{
			"service_registry": "healthy",
			"load_balancer":    "healthy",
			"rate_limiter":     "healthy",
			"circuit_breaker":  "healthy",
		},
	}

	c.JSON(http.StatusOK, health)
}

// GetServices returns all registered services
func (gh *GatewayHandler) GetServices(c *gin.Context) {
	services, err := gh.registry.GetServices()
	if err != nil {
		gh.logger.Error("Failed to get services", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get services"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"services": services})
}

// GetService returns a specific service
func (gh *GatewayHandler) GetService(c *gin.Context) {
	serviceName := c.Param("name")

	service, err := gh.registry.GetService(serviceName)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Service not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"service": service})
}

// RegisterServiceInstance registers a new service instance
func (gh *GatewayHandler) RegisterServiceInstance(c *gin.Context) {
	serviceName := c.Param("name")

	var instance discovery.ServiceInstance
	if err := c.ShouldBindJSON(&instance); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	instance.Name = serviceName
	instance.Healthy = true

	if err := gh.registry.Register(&instance); err != nil {
		gh.logger.Error("Failed to register service instance",
			zap.String("service", serviceName),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to register service instance"})
		return
	}

	gh.logger.Info("Service instance registered",
		zap.String("service", serviceName),
		zap.String("instance_id", instance.ID))

	c.JSON(http.StatusCreated, gin.H{"message": "Service instance registered successfully"})
}

// DeregisterServiceInstance removes a service instance
func (gh *GatewayHandler) DeregisterServiceInstance(c *gin.Context) {
	serviceName := c.Param("name")
	instanceID := c.Param("id")

	if err := gh.registry.Deregister(instanceID); err != nil {
		gh.logger.Error("Failed to deregister service instance",
			zap.String("service", serviceName),
			zap.String("instance_id", instanceID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to deregister service instance"})
		return
	}

	gh.logger.Info("Service instance deregistered",
		zap.String("service", serviceName),
		zap.String("instance_id", instanceID))

	c.JSON(http.StatusOK, gin.H{"message": "Service instance deregistered successfully"})
}

// GetLoadBalancerMetrics returns load balancer metrics
func (gh *GatewayHandler) GetLoadBalancerMetrics(c *gin.Context) {
	metrics := gh.loadBalancer.GetMetrics()
	connections := gh.loadBalancer.GetConnectionCounts()

	response := map[string]interface{}{
		"metrics":     metrics,
		"connections": connections,
	}

	c.JSON(http.StatusOK, response)
}

// ResetLoadBalancer resets load balancer state
func (gh *GatewayHandler) ResetLoadBalancer(c *gin.Context) {
	gh.loadBalancer.Reset()
	gh.logger.Info("Load balancer reset via API")
	c.JSON(http.StatusOK, gin.H{"message": "Load balancer reset successfully"})
}

// GetRateLimiterStats returns rate limiter statistics
func (gh *GatewayHandler) GetRateLimiterStats(c *gin.Context) {
	stats := gh.rateLimiter.GetStats()
	c.JSON(http.StatusOK, stats)
}

// GetCircuitBreakerStatus returns circuit breaker status
func (gh *GatewayHandler) GetCircuitBreakerStatus(c *gin.Context) {
	status := gh.circuitBreaker.GetCircuitStatus()
	c.JSON(http.StatusOK, gin.H{"circuits": status})
}

// ResetCircuitBreakers resets all circuit breakers
func (gh *GatewayHandler) ResetCircuitBreakers(c *gin.Context) {
	gh.circuitBreaker.ResetAllCircuits()
	gh.logger.Info("All circuit breakers reset via API")
	c.JSON(http.StatusOK, gin.H{"message": "All circuit breakers reset successfully"})
}

// ResetCircuitBreaker resets a specific circuit breaker
func (gh *GatewayHandler) ResetCircuitBreaker(c *gin.Context) {
	serviceName := c.Param("service")

	gh.circuitBreaker.ResetCircuit(serviceName)
	gh.logger.Info("Circuit breaker reset via API",
		zap.String("service", serviceName))

	c.JSON(http.StatusOK, gin.H{
		"message": "Circuit breaker reset successfully",
		"service": serviceName,
	})
}

// GetGatewayConfig returns gateway configuration
func (gh *GatewayHandler) GetGatewayConfig(c *gin.Context) {
	config := map[string]interface{}{
		"version": "1.0.0",
		"features": map[string]bool{
			"service_discovery": true,
			"load_balancing":    true,
			"rate_limiting":     true,
			"circuit_breaker":   true,
			"authentication":    true,
			"monitoring":        true,
		},
		"algorithms": map[string]interface{}{
			"load_balancing": []string{"round_robin", "least_connections", "weighted_round_robin"},
			"rate_limiting":  "token_bucket",
		},
	}

	c.JSON(http.StatusOK, config)
}
