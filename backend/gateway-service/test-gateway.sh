#!/bin/bash

# Test script for API Gateway Service
# This script tests various gateway endpoints and functionality

set -e

GATEWAY_URL="http://localhost:8000"
ADMIN_SERVICE_URL="http://localhost:8080"

echo "🚀 Testing API Gateway Service"
echo "================================"

# Function to make HTTP requests and show results
make_request() {
    local method=$1
    local url=$2
    local description=$3
    local data=$4
    
    echo ""
    echo "📡 $description"
    echo "   $method $url"
    
    if [ -n "$data" ]; then
        curl -s -X "$method" "$url" \
             -H "Content-Type: application/json" \
             -d "$data" | jq '.' || echo "Response not JSON"
    else
        curl -s -X "$method" "$url" | jq '.' || echo "Response not JSON"
    fi
}

# Test 1: Gateway Health Check
make_request "GET" "$GATEWAY_URL/health" "Gateway Health Check"

# Test 2: Gateway Status
make_request "GET" "$GATEWAY_URL/gateway/status" "Gateway Status (requires auth)"

# Test 3: Service Discovery - List Services
make_request "GET" "$GATEWAY_URL/gateway/services" "List Registered Services (requires auth)"

# Test 4: Load Balancer Metrics
make_request "GET" "$GATEWAY_URL/gateway/load-balancer/metrics" "Load Balancer Metrics (requires auth)"

# Test 5: Rate Limiter Stats
make_request "GET" "$GATEWAY_URL/gateway/rate-limiter/stats" "Rate Limiter Statistics (requires auth)"

# Test 6: Circuit Breaker Status
make_request "GET" "$GATEWAY_URL/gateway/circuit-breaker/status" "Circuit Breaker Status (requires auth)"

# Test 7: Gateway Configuration
make_request "GET" "$GATEWAY_URL/gateway/config" "Gateway Configuration (requires auth)"

echo ""
echo "🔄 Testing Proxy Functionality"
echo "==============================="

# Test 8: Proxy to Admin Service Health (should work without auth)
make_request "GET" "$GATEWAY_URL/admin-service/health" "Proxy to Admin Service Health"

# Test 9: Proxy to Admin Service API (requires auth)
make_request "GET" "$GATEWAY_URL/api/v1/admin-service/users" "Proxy to Admin Service Users API (requires auth)"

echo ""
echo "⚡ Testing Rate Limiting"
echo "======================="

# Test 10: Rate Limiting Test
echo "📡 Testing Rate Limiting (making multiple requests quickly)"
for i in {1..5}; do
    echo "Request $i:"
    curl -s -w "Status: %{http_code}\n" "$GATEWAY_URL/health" > /dev/null
    sleep 0.1
done

echo ""
echo "🔧 Testing Service Registration"
echo "==============================="

# Test 11: Register a new service instance (requires auth)
SERVICE_INSTANCE='{
    "id": "test-service-manual",
    "url": "http://localhost:9999",
    "version": "1.0.0",
    "tags": ["test", "manual"],
    "metadata": {
        "environment": "test"
    }
}'

make_request "POST" "$GATEWAY_URL/gateway/services/test-service/instances" "Register Test Service Instance (requires auth)" "$SERVICE_INSTANCE"

# Test 12: List services again to see the new instance
make_request "GET" "$GATEWAY_URL/gateway/services/test-service" "Get Test Service Details (requires auth)"

# Test 13: Deregister the test service instance
make_request "DELETE" "$GATEWAY_URL/gateway/services/test-service/instances/test-service-manual" "Deregister Test Service Instance (requires auth)"

echo ""
echo "📊 Performance Test"
echo "==================="

# Test 14: Simple performance test
echo "📡 Running simple performance test (10 concurrent requests)"
echo "curl -s -w 'Time: %{time_total}s, Status: %{http_code}\n' '$GATEWAY_URL/health'"

for i in {1..10}; do
    curl -s -w "Request $i - Time: %{time_total}s, Status: %{http_code}\n" "$GATEWAY_URL/health" > /dev/null &
done
wait

echo ""
echo "✅ Gateway Testing Complete!"
echo ""
echo "📝 Notes:"
echo "   - Most gateway management endpoints require authentication"
echo "   - Health endpoints are public and don't require authentication"
echo "   - The gateway proxies requests to backend services based on URL patterns"
echo "   - Rate limiting is active and will throttle excessive requests"
echo "   - Circuit breakers protect backend services from failures"
echo ""
echo "🔗 Useful URLs:"
echo "   - Gateway Health: $GATEWAY_URL/health"
echo "   - Gateway Status: $GATEWAY_URL/gateway/status"
echo "   - Admin Service via Gateway: $GATEWAY_URL/admin-service/health"
echo "   - Direct Admin Service: $ADMIN_SERVICE_URL/health"
