package rotation

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/claudio/deploy-orchestrator/secrets-service/encryption"
	"github.com/claudio/deploy-orchestrator/secrets-service/models"
	"github.com/claudio/deploy-orchestrator/secrets-service/providers"
)

// Manager handles secret rotation operations
type Manager struct {
	db                *gorm.DB
	providerManager   *providers.Manager
	encryptionManager *encryption.Manager
	logger            *zap.Logger
	scheduler         *Scheduler
}

// NewManager creates a new rotation manager
func NewManager(db *gorm.DB, providerManager *providers.Manager, encryptionManager *encryption.Manager, logger *zap.Logger) *Manager {
	manager := &Manager{
		db:                db,
		providerManager:   providerManager,
		encryptionManager: encryptionManager,
		logger:            logger,
	}

	manager.scheduler = NewScheduler(manager, logger)

	return manager
}

// StartScheduler starts the rotation scheduler
func (m *Manager) StartScheduler(ctx context.Context) {
	m.scheduler.Start(ctx)
}

// RotateSecret rotates a specific secret
func (m *Manager) RotateSecret(ctx context.Context, secretID string, triggeredBy string, reason string) (*models.RotationHistory, error) {
	// Get the secret
	var secret models.Secret
	if err := m.db.WithContext(ctx).Preload("RotationPolicy").First(&secret, "id = ?", secretID).Error; err != nil {
		return nil, fmt.Errorf("failed to find secret: %w", err)
	}

	// Start rotation history record
	history := &models.RotationHistory{
		ID:               generateID(),
		SecretID:         secret.ID,
		RotationPolicyID: *secret.RotationPolicyID,
		Status:           models.RotationStatusPending,
		StartedAt:        time.Now(),
		TriggeredBy:      triggeredBy,
		TriggerReason:    reason,
		OldVersion:       0, // Will be set based on current version
		NewVersion:       0, // Will be set after rotation
	}

	// Save initial history record
	if err := m.db.WithContext(ctx).Create(history).Error; err != nil {
		return nil, fmt.Errorf("failed to create rotation history: %w", err)
	}

	// Update status to in progress
	history.Status = models.RotationStatusInProgress
	if err := m.db.WithContext(ctx).Save(history).Error; err != nil {
		m.logger.Error("Failed to update rotation status", zap.Error(err))
	}

	// Perform the actual rotation
	err := m.performRotation(ctx, &secret, history)

	// Update completion status
	now := time.Now()
	history.CompletedAt = &now
	history.Duration = int(now.Sub(history.StartedAt).Seconds())

	if err != nil {
		history.Status = models.RotationStatusFailed
		history.ErrorMessage = err.Error()
		m.logger.Error("Secret rotation failed",
			zap.String("secretID", secretID),
			zap.Error(err))
	} else {
		history.Status = models.RotationStatusCompleted
		m.logger.Info("Secret rotation completed successfully",
			zap.String("secretID", secretID),
			zap.Int("newVersion", history.NewVersion))
	}

	// Save final history record
	if saveErr := m.db.WithContext(ctx).Save(history).Error; saveErr != nil {
		m.logger.Error("Failed to save rotation history", zap.Error(saveErr))
	}

	return history, err
}

// performRotation performs the actual secret rotation
func (m *Manager) performRotation(ctx context.Context, secret *models.Secret, history *models.RotationHistory) error {
	// Get the current secret version
	var currentVersion models.SecretVersion
	if err := m.db.WithContext(ctx).Where("secret_id = ? AND is_active = ?", secret.ID, true).First(&currentVersion).Error; err != nil {
		return fmt.Errorf("failed to find current secret version: %w", err)
	}

	history.OldVersion = currentVersion.Version

	// Get provider
	provider, err := m.providerManager.GetProvider(secret.Provider)
	if err != nil {
		return fmt.Errorf("failed to get provider: %w", err)
	}

	// Generate new secret value using provider's rotation capability
	var newValue string
	if secret.Provider == "internal" {
		// For internal provider, generate a new random value
		newValue, err = generateRandomSecret(32)
		if err != nil {
			return fmt.Errorf("failed to generate new secret value: %w", err)
		}
	} else {
		// Use provider's rotation capability
		newValue, err = provider.RotateSecret(ctx, secret.Name)
		if err != nil {
			return fmt.Errorf("failed to rotate secret in provider: %w", err)
		}
	}

	// Encrypt the new value
	encryptedValue, err := m.encryptionManager.EncryptSecret(newValue, secret.KeyID)
	if err != nil {
		return fmt.Errorf("failed to encrypt new secret value: %w", err)
	}

	// Create new secret version
	newVersion := &models.SecretVersion{
		ID:             generateID(),
		SecretID:       secret.ID,
		Version:        currentVersion.Version + 1,
		EncryptedValue: encryptedValue,
		KeyID:          secret.KeyID,
		CreatedBy:      history.TriggeredBy,
		Reason:         fmt.Sprintf("Automatic rotation: %s", history.TriggerReason),
		IsActive:       false, // Will be activated after successful rotation
	}

	// Save new version
	if err := m.db.WithContext(ctx).Create(newVersion).Error; err != nil {
		return fmt.Errorf("failed to create new secret version: %w", err)
	}

	history.NewVersion = newVersion.Version

	// Apply rotation strategy
	if err := m.applyRotationStrategy(ctx, secret, &currentVersion, newVersion); err != nil {
		return fmt.Errorf("failed to apply rotation strategy: %w", err)
	}

	// Update secret's last rotation time and next rotation time
	now := time.Now()
	secret.LastRotated = &now

	if secret.RotationPolicy != nil {
		interval, parseErr := time.ParseDuration(secret.RotationPolicy.Interval)
		if parseErr == nil {
			nextRotation := now.Add(interval)
			secret.NextRotation = &nextRotation
		}
	}

	// Update secret in database
	if err := m.db.WithContext(ctx).Save(secret).Error; err != nil {
		return fmt.Errorf("failed to update secret rotation times: %w", err)
	}

	return nil
}

// applyRotationStrategy applies the rotation strategy (replace, versioned, blue-green)
func (m *Manager) applyRotationStrategy(ctx context.Context, secret *models.Secret, oldVersion, newVersion *models.SecretVersion) error {
	strategy := models.RotationStrategyReplace
	if secret.RotationPolicy != nil {
		strategy = secret.RotationPolicy.Strategy
	}

	switch strategy {
	case models.RotationStrategyReplace:
		// Immediately replace the old version with the new one
		oldVersion.IsActive = false
		newVersion.IsActive = true

		if err := m.db.WithContext(ctx).Save(oldVersion).Error; err != nil {
			return fmt.Errorf("failed to deactivate old version: %w", err)
		}

		if err := m.db.WithContext(ctx).Save(newVersion).Error; err != nil {
			return fmt.Errorf("failed to activate new version: %w", err)
		}

	case models.RotationStrategyVersioned:
		// Keep both versions active for a grace period
		newVersion.IsActive = true
		if err := m.db.WithContext(ctx).Save(newVersion).Error; err != nil {
			return fmt.Errorf("failed to activate new version: %w", err)
		}

		// Schedule deactivation of old version after grace period
		if secret.RotationPolicy != nil && secret.RotationPolicy.GracePeriod != "" {
			gracePeriod, err := time.ParseDuration(secret.RotationPolicy.GracePeriod)
			if err == nil {
				// In a real implementation, you would schedule this for later
				m.logger.Info("Old version will be deactivated after grace period",
					zap.String("secretID", secret.ID),
					zap.Duration("gracePeriod", gracePeriod))
			}
		}

	case models.RotationStrategyBlueGreen:
		// Blue-green deployment style rotation
		// Activate new version first, then deactivate old after validation
		newVersion.IsActive = true
		if err := m.db.WithContext(ctx).Save(newVersion).Error; err != nil {
			return fmt.Errorf("failed to activate new version: %w", err)
		}

		// In a real implementation, you would validate the new version here
		// For now, we'll immediately deactivate the old version
		oldVersion.IsActive = false
		if err := m.db.WithContext(ctx).Save(oldVersion).Error; err != nil {
			return fmt.Errorf("failed to deactivate old version: %w", err)
		}

	default:
		return fmt.Errorf("unknown rotation strategy: %s", strategy)
	}

	return nil
}

// CheckRotationNeeded checks if any secrets need rotation
func (m *Manager) CheckRotationNeeded(ctx context.Context) ([]models.Secret, error) {
	var secrets []models.Secret
	now := time.Now()

	// Find secrets that need rotation based on next_rotation time
	if err := m.db.WithContext(ctx).
		Preload("RotationPolicy").
		Where("next_rotation IS NOT NULL AND next_rotation <= ?", now).
		Where("status = ?", models.SecretStatusActive).
		Find(&secrets).Error; err != nil {
		return nil, fmt.Errorf("failed to find secrets needing rotation: %w", err)
	}

	return secrets, nil
}

// RotateSecretsIfNeeded checks and rotates secrets that need rotation
func (m *Manager) RotateSecretsIfNeeded(ctx context.Context) error {
	secrets, err := m.CheckRotationNeeded(ctx)
	if err != nil {
		return err
	}

	if len(secrets) == 0 {
		m.logger.Debug("No secrets need rotation")
		return nil
	}

	m.logger.Info("Found secrets needing rotation", zap.Int("count", len(secrets)))

	for _, secret := range secrets {
		_, err := m.RotateSecret(ctx, secret.ID, "system", "scheduled rotation")
		if err != nil {
			m.logger.Error("Failed to rotate secret",
				zap.String("secretID", secret.ID),
				zap.String("secretName", secret.Name),
				zap.Error(err))
			// Continue with other secrets even if one fails
		}
	}

	return nil
}

// GetRotationHistory returns rotation history for a secret
func (m *Manager) GetRotationHistory(ctx context.Context, secretID string, limit int) ([]models.RotationHistory, error) {
	var history []models.RotationHistory

	query := m.db.WithContext(ctx).Where("secret_id = ?", secretID).Order("started_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&history).Error; err != nil {
		return nil, fmt.Errorf("failed to get rotation history: %w", err)
	}

	return history, nil
}

// GetRotationStats returns rotation statistics
func (m *Manager) GetRotationStats(ctx context.Context) (*RotationStats, error) {
	stats := &RotationStats{}

	// Count total rotations
	if err := m.db.WithContext(ctx).Model(&models.RotationHistory{}).Count(&stats.TotalRotations).Error; err != nil {
		return nil, fmt.Errorf("failed to count total rotations: %w", err)
	}

	// Count successful rotations
	if err := m.db.WithContext(ctx).Model(&models.RotationHistory{}).
		Where("status = ?", models.RotationStatusCompleted).
		Count(&stats.SuccessfulRotations).Error; err != nil {
		return nil, fmt.Errorf("failed to count successful rotations: %w", err)
	}

	// Count failed rotations
	if err := m.db.WithContext(ctx).Model(&models.RotationHistory{}).
		Where("status = ?", models.RotationStatusFailed).
		Count(&stats.FailedRotations).Error; err != nil {
		return nil, fmt.Errorf("failed to count failed rotations: %w", err)
	}

	// Count secrets needing rotation
	now := time.Now()
	if err := m.db.WithContext(ctx).Model(&models.Secret{}).
		Where("next_rotation IS NOT NULL AND next_rotation <= ?", now).
		Where("status = ?", models.SecretStatusActive).
		Count(&stats.SecretsNeedingRotation).Error; err != nil {
		return nil, fmt.Errorf("failed to count secrets needing rotation: %w", err)
	}

	// Calculate success rate
	if stats.TotalRotations > 0 {
		stats.SuccessRate = float64(stats.SuccessfulRotations) / float64(stats.TotalRotations) * 100
	}

	return stats, nil
}

// RotationStats represents rotation statistics
type RotationStats struct {
	TotalRotations         int64   `json:"totalRotations"`
	SuccessfulRotations    int64   `json:"successfulRotations"`
	FailedRotations        int64   `json:"failedRotations"`
	SecretsNeedingRotation int64   `json:"secretsNeedingRotation"`
	SuccessRate            float64 `json:"successRate"`
}

// generateRandomSecret generates a random secret value
func generateRandomSecret(length int) (string, error) {
	// This is a simplified implementation
	// In production, you might want more sophisticated password generation
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"

	bytes := make([]byte, length)
	for i := range bytes {
		bytes[i] = charset[i%len(charset)]
	}

	return string(bytes), nil
}

// generateID generates a unique ID
func generateID() string {
	return fmt.Sprintf("rot_%d", time.Now().UnixNano())
}
