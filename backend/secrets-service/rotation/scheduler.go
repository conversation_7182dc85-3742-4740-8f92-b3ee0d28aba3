package rotation

import (
	"context"
	"time"

	"go.uber.org/zap"
)

// Scheduler handles scheduled secret rotations
type Scheduler struct {
	manager *Manager
	logger  *zap.Logger
	ticker  *time.Ticker
	done    chan bool
}

// NewScheduler creates a new rotation scheduler
func NewScheduler(manager *Manager, logger *zap.Logger) *Scheduler {
	return &Scheduler{
		manager: manager,
		logger:  logger,
		done:    make(chan bool),
	}
}

// Start starts the rotation scheduler
func (s *Scheduler) Start(ctx context.Context) {
	// Default check interval is 1 hour
	interval := 1 * time.Hour

	// TODO: Get interval from configuration
	// if s.manager.config.Rotation.CheckInterval != "" {
	//     if parsedInterval, err := time.ParseDuration(s.manager.config.Rotation.CheckInterval); err == nil {
	//         interval = parsedInterval
	//     }
	// }

	s.ticker = time.NewTicker(interval)

	s.logger.Info("Starting rotation scheduler", zap.Duration("interval", interval))

	go func() {
		for {
			select {
			case <-s.ticker.C:
				s.checkAndRotate(ctx)
			case <-s.done:
				s.logger.Info("Rotation scheduler stopped")
				return
			case <-ctx.Done():
				s.logger.Info("Rotation scheduler stopped due to context cancellation")
				return
			}
		}
	}()
}

// Stop stops the rotation scheduler
func (s *Scheduler) Stop() {
	if s.ticker != nil {
		s.ticker.Stop()
	}
	s.done <- true
}

// checkAndRotate checks for secrets that need rotation and rotates them
func (s *Scheduler) checkAndRotate(ctx context.Context) {
	s.logger.Debug("Checking for secrets needing rotation")

	start := time.Now()
	err := s.manager.RotateSecretsIfNeeded(ctx)
	duration := time.Since(start)

	if err != nil {
		s.logger.Error("Error during scheduled rotation check",
			zap.Error(err),
			zap.Duration("duration", duration))
	} else {
		s.logger.Debug("Completed rotation check",
			zap.Duration("duration", duration))
	}
}

// ForceCheck forces an immediate rotation check
func (s *Scheduler) ForceCheck(ctx context.Context) error {
	s.logger.Info("Forcing immediate rotation check")
	return s.manager.RotateSecretsIfNeeded(ctx)
}
