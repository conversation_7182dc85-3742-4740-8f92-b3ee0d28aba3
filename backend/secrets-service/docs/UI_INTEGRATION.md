# Secrets Management UI Integration Guide

## Admin UI - Provider Configuration

### CyberArk Provider Setup
```typescript
// Admin can configure CyberArk providers
interface CyberArkConfig {
  name: string;
  type: 'cyberark';
  description: string;
  config: {
    baseURL: string;
    appID: string;
    safe: string;
    username: string;
    password: string;
    certificatePath?: string;
    timeout: number;
    retryAttempts: number;
  };
  priority: number;
}

// API Endpoints for Admin
POST /api/v1/providers              // Create provider
GET  /api/v1/providers              // List all providers
GET  /api/v1/providers/:id          // Get provider details
PUT  /api/v1/providers/:id          // Update provider
DELETE /api/v1/providers/:id        // Delete provider
POST /api/v1/providers/:id/test     // Test connection
```

### Admin Provider Management UI
```typescript
// Provider Configuration Form
const ProviderConfigForm = () => {
  const [config, setConfig] = useState<CyberArkConfig>({
    name: '',
    type: 'cyberark',
    description: '',
    config: {
      baseURL: '',
      appID: '',
      safe: '',
      username: '',
      password: '',
      timeout: 30,
      retryAttempts: 3
    },
    priority: 1
  });

  const handleSubmit = async () => {
    const response = await fetch('/api/v1/providers', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(config)
    });
    
    if (response.ok) {
      // Provider created successfully
      showNotification('CyberArk provider configured successfully');
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input 
        placeholder="Provider Name"
        value={config.name}
        onChange={(e) => setConfig({...config, name: e.target.value})}
      />
      <input 
        placeholder="CyberArk Base URL"
        value={config.config.baseURL}
        onChange={(e) => setConfig({
          ...config, 
          config: {...config.config, baseURL: e.target.value}
        })}
      />
      {/* More form fields... */}
      <button type="submit">Create Provider</button>
    </form>
  );
};
```

## User UI - Secret Management

### Project-Scoped Secret Access
```typescript
// Users can only access secrets for their projects
interface ProjectSecret {
  id: string;
  name: string;
  description: string;
  scope: string;
  provider: string;
  variableName: string;
  variableType: 'env' | 'file' | 'config' | 'mount';
  accessLevel: 'read' | 'write' | 'admin';
  environments: string[];
  services: string[];
}

// API Endpoints for Users
GET  /api/v1/projects/:projectId/secrets           // Get project secrets
POST /api/v1/projects/:projectId/secrets/bind     // Bind secret to project
DELETE /api/v1/projects/:projectId/secrets/:bindingId // Unbind secret
GET  /api/v1/secrets                               // List accessible secrets
POST /api/v1/secrets                               // Create new secret
```

### Secret Creation UI
```typescript
const CreateSecretForm = ({ projectId }: { projectId: string }) => {
  const [secret, setSecret] = useState({
    name: '',
    description: '',
    scopeId: '',
    providerType: 'cyberark',
    providerConfig: {
      objectName: '',
      safe: '',
      folder: ''
    },
    tags: []
  });

  const handleCreateSecret = async () => {
    // Step 1: Create the secret
    const secretResponse = await fetch('/api/v1/secrets', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(secret)
    });

    if (secretResponse.ok) {
      const newSecret = await secretResponse.json();
      
      // Step 2: Bind secret to project
      const bindResponse = await fetch(`/api/v1/projects/${projectId}/secrets/bind`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          secretId: newSecret.id,
          variableName: 'MY_SECRET',
          variableType: 'env',
          accessLevel: 'read',
          environments: ['production', 'staging'],
          services: ['api', 'worker']
        })
      });

      if (bindResponse.ok) {
        showNotification('Secret created and bound to project successfully');
      }
    }
  };

  return (
    <form onSubmit={handleCreateSecret}>
      <input 
        placeholder="Secret Name"
        value={secret.name}
        onChange={(e) => setSecret({...secret, name: e.target.value})}
      />
      <textarea 
        placeholder="Description"
        value={secret.description}
        onChange={(e) => setSecret({...secret, description: e.target.value})}
      />
      <select 
        value={secret.providerType}
        onChange={(e) => setSecret({...secret, providerType: e.target.value})}
      >
        <option value="cyberark">CyberArk</option>
        <option value="vault">HashiCorp Vault</option>
        <option value="aws">AWS Secrets Manager</option>
      </select>
      
      {secret.providerType === 'cyberark' && (
        <div>
          <input 
            placeholder="CyberArk Object Name"
            value={secret.providerConfig.objectName}
            onChange={(e) => setSecret({
              ...secret, 
              providerConfig: {...secret.providerConfig, objectName: e.target.value}
            })}
          />
          <input 
            placeholder="Safe Name"
            value={secret.providerConfig.safe}
            onChange={(e) => setSecret({
              ...secret, 
              providerConfig: {...secret.providerConfig, safe: e.target.value}
            })}
          />
        </div>
      )}
      
      <button type="submit">Create Secret</button>
    </form>
  );
};
```

### Project Secrets List UI
```typescript
const ProjectSecretsList = ({ projectId }: { projectId: string }) => {
  const [secrets, setSecrets] = useState<ProjectSecret[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProjectSecrets();
  }, [projectId]);

  const fetchProjectSecrets = async () => {
    try {
      const response = await fetch(`/api/v1/projects/${projectId}/secrets`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setSecrets(data.secrets);
      }
    } catch (error) {
      console.error('Failed to fetch secrets:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUnbindSecret = async (bindingId: string) => {
    const response = await fetch(`/api/v1/projects/${projectId}/secrets/${bindingId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.ok) {
      setSecrets(secrets.filter(s => s.id !== bindingId));
      showNotification('Secret unbound from project');
    }
  };

  if (loading) return <div>Loading secrets...</div>;

  return (
    <div>
      <h3>Project Secrets</h3>
      <button onClick={() => setShowCreateForm(true)}>Add Secret</button>
      
      <table>
        <thead>
          <tr>
            <th>Name</th>
            <th>Variable Name</th>
            <th>Type</th>
            <th>Provider</th>
            <th>Environments</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {secrets.map(secret => (
            <tr key={secret.id}>
              <td>{secret.name}</td>
              <td>{secret.variableName}</td>
              <td>{secret.variableType}</td>
              <td>{secret.provider}</td>
              <td>{secret.environments.join(', ')}</td>
              <td>
                <button onClick={() => handleUnbindSecret(secret.id)}>
                  Unbind
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
```

## Permission-Based UI Controls

### Conditional Rendering Based on Permissions
```typescript
const SecretManagementUI = ({ projectId, userPermissions }: { 
  projectId: string; 
  userPermissions: string[] 
}) => {
  const canCreateSecrets = userPermissions.includes('secrets:create');
  const canWriteSecrets = userPermissions.includes('secrets:write');
  const canDeleteSecrets = userPermissions.includes('secrets:delete');

  return (
    <div>
      <ProjectSecretsList projectId={projectId} />
      
      {canCreateSecrets && (
        <CreateSecretForm projectId={projectId} />
      )}
      
      {!canWriteSecrets && (
        <div className="read-only-notice">
          You have read-only access to secrets in this project
        </div>
      )}
    </div>
  );
};
```
