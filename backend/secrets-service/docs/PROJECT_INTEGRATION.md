# Project-Scoped Secrets and Integration Guide

This document explains how secrets are scoped to projects and integrated with deployments and workflows as variables.

## Overview

The secrets service supports project-scoped visibility and seamless integration with deployments and workflows through a sophisticated variable system. Secrets can be bound to projects and exposed as environment variables, configuration files, or mounted volumes.

## Project-Scoped Secrets

### Architecture

```
Project
├── Secret Bindings (ProjectSecretBinding)
│   ├── Secret Reference
│   ├── Variable Name (how it appears in deployments/workflows)
│   ├── Variable Type (env, file, config, mount)
│   ├── Access Level (read, write, admin)
│   ├── Environment Restrictions
│   └── Service Restrictions
└── Secret Variables (SecretVariable)
    ├── Variable Configuration
    ├── Transformation Rules
    ├── Context Restrictions
    └── Format Specifications
```

### Key Concepts

1. **Project Secret Binding**: Links a secret to a project with specific access rules
2. **Secret Variable**: Defines how a secret appears as a variable in deployments/workflows
3. **Context Restrictions**: Environment, service, and namespace-specific access
4. **Transformations**: Rules for modifying secret values before injection

## Secret Variable Types

### 1. Environment Variables (`env`)
Secrets injected as environment variables into containers/processes.

```json
{
  "name": "DATABASE_PASSWORD",
  "type": "env",
  "secretId": "sec_123",
  "projectId": "proj_456"
}
```

### 2. Configuration Files (`file`)
Secrets written to files in the container/process filesystem.

```json
{
  "name": "database_config",
  "type": "file",
  "path": "/etc/app/database.conf",
  "format": "json",
  "secretId": "sec_123",
  "projectId": "proj_456"
}
```

### 3. Configuration Objects (`config`)
Secrets injected as structured configuration (JSON/YAML).

```json
{
  "name": "app_config",
  "type": "config",
  "format": "yaml",
  "transform": {
    "template": "database:\n  password: {{.value}}\n  host: {{.host}}"
  },
  "secretId": "sec_123",
  "projectId": "proj_456"
}
```

### 4. Volume Mounts (`mount`)
Secrets mounted as volumes in Kubernetes or Docker containers.

```json
{
  "name": "ssl_certificates",
  "type": "mount",
  "path": "/etc/ssl/certs",
  "secretId": "sec_123",
  "projectId": "proj_456"
}
```

## API Endpoints

### Project Secret Management

#### Bind Secret to Project
```http
POST /api/v1/projects/{projectId}/secrets/bind
Content-Type: application/json

{
  "secretId": "sec_123456789",
  "variableName": "DATABASE_PASSWORD",
  "variableType": "env",
  "accessLevel": "read",
  "environments": ["production", "staging"],
  "services": ["web-service", "api-service"],
  "description": "Database password for web services"
}
```

#### Get Project Secrets
```http
GET /api/v1/projects/{projectId}/secrets?environment=production&service=web-service
```

#### Create Secret Variable
```http
POST /api/v1/projects/{projectId}/variables
Content-Type: application/json

{
  "secretId": "sec_123456789",
  "name": "DATABASE_PASSWORD",
  "type": "env",
  "environment": "production",
  "service": "web-service",
  "transform": {
    "prefix": "mysql://user:",
    "suffix": "@localhost:3306/mydb"
  }
}
```

### Deployment Integration

#### Get Deployment Secrets
```http
POST /api/v1/integration/deployment/secrets
Content-Type: application/json

{
  "deploymentId": "deploy_123456789",
  "projectId": "proj_123456789",
  "environment": "production",
  "service": "web-service",
  "namespace": "default"
}
```

**Response:**
```json
{
  "variables": [
    {
      "name": "DATABASE_PASSWORD",
      "type": "env",
      "value": "encrypted_secret_value",
      "secretId": "sec_123456789",
      "variableId": "var_123456789"
    },
    {
      "name": "api_config",
      "type": "file",
      "path": "/etc/app/api.json",
      "format": "json",
      "secretId": "sec_987654321",
      "variableId": "var_987654321"
    }
  ],
  "errors": [],
  "metadata": {
    "deploymentId": "deploy_123456789",
    "projectId": "proj_123456789",
    "environment": "production",
    "retrievedAt": "2024-01-15T10:30:00Z"
  }
}
```

### Workflow Integration

#### Get Workflow Secrets
```http
POST /api/v1/integration/workflow/secrets
Content-Type: application/json

{
  "workflowId": "workflow_123456789",
  "executionId": "exec_123456789",
  "projectId": "proj_123456789",
  "environment": "production",
  "service": "deployment-service",
  "stepName": "deploy-step"
}
```

## Integration Examples

### 1. Deployment Service Integration

```go
// In deployment service
func (d *DeploymentService) getSecretsForDeployment(deploymentID, projectID, environment, service string) ([]SecretVariable, error) {
    req := DeploymentSecretsRequest{
        DeploymentID: deploymentID,
        ProjectID:    projectID,
        Environment:  environment,
        Service:      service,
    }
    
    resp, err := d.secretsClient.GetDeploymentSecrets(req)
    if err != nil {
        return nil, err
    }
    
    return resp.Variables, nil
}

func (d *DeploymentService) injectSecrets(deployment *Deployment, secrets []SecretVariable) error {
    for _, secret := range secrets {
        switch secret.Type {
        case "env":
            deployment.Spec.Template.Spec.Containers[0].Env = append(
                deployment.Spec.Template.Spec.Containers[0].Env,
                corev1.EnvVar{
                    Name:  secret.Name,
                    Value: secret.Value,
                },
            )
        case "file":
            // Create config map and mount as volume
            d.createSecretFile(deployment, secret)
        case "mount":
            // Create secret volume mount
            d.createSecretMount(deployment, secret)
        }
    }
    return nil
}
```

### 2. Workflow Service Integration

```go
// In workflow service
func (w *WorkflowService) executeStep(execution *WorkflowExecution, step *WorkflowStep) error {
    // Get secrets for this step
    secrets, err := w.getSecretsForStep(execution, step)
    if err != nil {
        return err
    }
    
    // Inject secrets into step environment
    stepEnv := make(map[string]string)
    for _, secret := range secrets {
        if secret.Type == "env" {
            stepEnv[secret.Name] = secret.Value
        }
    }
    
    // Execute step with secrets
    return w.executeStepWithSecrets(step, stepEnv)
}
```

### 3. Frontend Integration

```typescript
// In Angular deployment component
async deployApplication(deployment: Deployment) {
  // Get available secrets for this project/environment
  const secrets = await this.secretsService.getProjectSecrets(
    deployment.projectId,
    deployment.environment,
    deployment.service
  );
  
  // Show secret selection UI
  const selectedSecrets = await this.showSecretSelectionDialog(secrets);
  
  // Deploy with selected secrets
  deployment.secrets = selectedSecrets;
  return this.deploymentService.deploy(deployment);
}
```

## Security Considerations

### 1. Access Control
- Secrets are scoped to projects with role-based access
- Environment and service restrictions limit exposure
- Audit logging tracks all secret access

### 2. Encryption
- Secrets are encrypted at rest using AES-256-GCM
- Values are decrypted only when needed for injection
- Network transmission uses TLS encryption

### 3. Audit Trail
- All secret access is logged with context
- Deployment and workflow usage is tracked
- Failed access attempts are recorded

### 4. Least Privilege
- Secrets are only accessible to authorized services
- Context restrictions limit scope of access
- Time-based access controls can be implemented

## Best Practices

### 1. Secret Organization
- Use descriptive variable names (e.g., `DATABASE_PASSWORD` not `DB_PASS`)
- Group related secrets by service or component
- Use consistent naming conventions across projects

### 2. Environment Management
- Separate secrets for different environments
- Use environment-specific variable configurations
- Implement promotion workflows for secret updates

### 3. Service Isolation
- Restrict secrets to specific services when possible
- Use service-specific transformations
- Implement network-level isolation

### 4. Monitoring
- Monitor secret access patterns
- Set up alerts for unusual access
- Regular audit of secret usage

## Troubleshooting

### Common Issues

1. **Secret Not Found in Deployment**
   - Check project binding exists
   - Verify environment/service restrictions
   - Confirm secret is active and not expired

2. **Permission Denied**
   - Verify user has project access
   - Check role permissions
   - Confirm service authentication

3. **Transformation Errors**
   - Validate transformation syntax
   - Check for circular references
   - Verify format compatibility

### Debug Commands

```bash
# Check project secret bindings
curl -H "Authorization: Bearer $TOKEN" \
  "https://secrets-service/api/v1/projects/proj_123/secrets"

# Test secret access for deployment
curl -X POST -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"deploymentId":"deploy_123","projectId":"proj_123","environment":"prod"}' \
  "https://secrets-service/api/v1/integration/deployment/secrets"

# Check audit logs
curl -H "Authorization: Bearer $TOKEN" \
  "https://secrets-service/api/v1/audit/logs?resource=secret&action=access"
```

This integration system provides a secure, flexible, and auditable way to manage secrets across the deploy-orchestrator platform while maintaining proper isolation and access controls.
