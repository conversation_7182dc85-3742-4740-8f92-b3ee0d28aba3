# Secrets Service API Reference

## Authentication & Authorization

All endpoints require authentication via <PERSON><PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

### Permission Levels
- **Admin**: Full access to all secrets and provider configuration
- **User**: Project-scoped access based on role assignments

## Provider Management (Admin Only)

### Create Provider
```http
POST /api/v1/providers
Content-Type: application/json
Authorization: Bearer <admin_token>

{
  "name": "CyberArk Production",
  "type": "cyberark",
  "description": "Production CyberArk instance",
  "config": {
    "baseURL": "https://cyberark.company.com",
    "appID": "MyApp",
    "safe": "MyAppSecrets",
    "username": "service_account",
    "password": "secure_password",
    "certificatePath": "/path/to/cert.pem",
    "timeout": 30,
    "retryAttempts": 3
  },
  "priority": 1
}
```

**Response (201 Created):**
```json
{
  "id": "prov_1234567890",
  "name": "CyberArk Production",
  "type": "cyberark",
  "description": "Production CyberArk instance",
  "isActive": true,
  "priority": 1,
  "testStatus": "unknown",
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

### List Providers
```http
GET /api/v1/providers?type=cyberark&limit=50&offset=0
Authorization: Bearer <admin_token>
```

**Response (200 OK):**
```json
{
  "providers": [
    {
      "id": "prov_1234567890",
      "name": "CyberArk Production",
      "type": "cyberark",
      "description": "Production CyberArk instance",
      "isActive": true,
      "priority": 1,
      "testStatus": "passed",
      "lastTested": "2024-01-15T11:00:00Z"
    }
  ],
  "total": 1,
  "limit": 50,
  "offset": 0
}
```

### Test Provider Connection
```http
POST /api/v1/providers/prov_1234567890/test
Authorization: Bearer <admin_token>
```

**Response (200 OK):**
```json
{
  "providerID": "prov_1234567890",
  "testStatus": "passed",
  "testMessage": "Connection successful",
  "lastTested": "2024-01-15T11:00:00Z",
  "duration": "1.2s"
}
```

## Secret Management

### Create Secret
```http
POST /api/v1/secrets
Content-Type: application/json
Authorization: Bearer <user_token>

{
  "name": "Database Password",
  "description": "Production database password",
  "scopeId": "scope_prod",
  "providerType": "cyberark",
  "providerConfig": {
    "objectName": "DB_PROD_PASSWORD",
    "safe": "DatabaseSecrets",
    "folder": "Production"
  },
  "tags": ["database", "production"]
}
```

**Response (201 Created):**
```json
{
  "id": "sec_1234567890",
  "name": "Database Password",
  "description": "Production database password",
  "scope": {
    "id": "scope_prod",
    "name": "Production"
  },
  "provider": "cyberark",
  "status": "active",
  "version": 1,
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

### List User's Accessible Secrets
```http
GET /api/v1/secrets?scope=production&provider=cyberark&limit=50&offset=0
Authorization: Bearer <user_token>
```

**Response (200 OK):**
```json
{
  "secrets": [
    {
      "id": "sec_1234567890",
      "name": "Database Password",
      "description": "Production database password",
      "scope": "Production",
      "provider": "cyberark",
      "status": "active",
      "version": 1,
      "lastRotated": "2024-01-10T08:00:00Z"
    }
  ],
  "total": 1,
  "limit": 50,
  "offset": 0
}
```

## Project Secret Management

### Get Project Secrets
```http
GET /api/v1/projects/proj_123/secrets?environment=production&service=api
Authorization: Bearer <user_token>
```

**Response (200 OK):**
```json
{
  "secrets": [
    {
      "id": "bind_1234567890",
      "secretId": "sec_1234567890",
      "secret": {
        "id": "sec_1234567890",
        "name": "Database Password",
        "description": "Production database password",
        "provider": "cyberark",
        "status": "active"
      },
      "variableName": "DB_PASSWORD",
      "variableType": "env",
      "accessLevel": "read",
      "environments": ["production"],
      "services": ["api", "worker"],
      "description": "Database connection password",
      "createdAt": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 1,
  "limit": 50,
  "offset": 0
}
```

### Bind Secret to Project
```http
POST /api/v1/projects/proj_123/secrets/bind
Content-Type: application/json
Authorization: Bearer <user_token>

{
  "secretId": "sec_1234567890",
  "variableName": "DB_PASSWORD",
  "variableType": "env",
  "accessLevel": "read",
  "environments": ["production", "staging"],
  "services": ["api", "worker"],
  "description": "Database connection password"
}
```

**Response (201 Created):**
```json
{
  "id": "bind_1234567890",
  "projectId": "proj_123",
  "secretId": "sec_1234567890",
  "variableName": "DB_PASSWORD",
  "variableType": "env",
  "accessLevel": "read",
  "environments": ["production", "staging"],
  "services": ["api", "worker"],
  "description": "Database connection password",
  "createdAt": "2024-01-15T10:30:00Z"
}
```

### Unbind Secret from Project
```http
DELETE /api/v1/projects/proj_123/secrets/bind_1234567890
Authorization: Bearer <user_token>
```

**Response (204 No Content)**

## Secret Variables

### Create Secret Variable Configuration
```http
POST /api/v1/projects/proj_123/variables
Content-Type: application/json
Authorization: Bearer <user_token>

{
  "secretId": "sec_1234567890",
  "name": "DATABASE_URL",
  "type": "env",
  "path": "/app/config/database.env",
  "format": "plain",
  "environment": "production",
  "service": "api",
  "namespace": "default",
  "transform": {
    "prefix": "DB_",
    "suffix": "_PROD",
    "case": "upper"
  }
}
```

**Response (201 Created):**
```json
{
  "id": "var_1234567890",
  "secretId": "sec_1234567890",
  "projectId": "proj_123",
  "name": "DATABASE_URL",
  "type": "env",
  "path": "/app/config/database.env",
  "format": "plain",
  "environment": "production",
  "service": "api",
  "namespace": "default",
  "transform": {
    "prefix": "DB_",
    "suffix": "_PROD",
    "case": "upper"
  },
  "createdAt": "2024-01-15T10:30:00Z"
}
```

## Integration Endpoints

### Retrieve Secrets for Deployment
```http
POST /api/v1/integration/deployment/secrets
Content-Type: application/json
Authorization: Bearer <service_token>

{
  "projectId": "proj_123",
  "environment": "production",
  "service": "api",
  "namespace": "default"
}
```

**Response (200 OK):**
```json
{
  "secrets": [
    {
      "name": "DB_PASSWORD",
      "value": "encrypted_secret_value",
      "type": "env",
      "source": "cyberark"
    }
  ],
  "metadata": {
    "projectId": "proj_123",
    "environment": "production",
    "retrievedAt": "2024-01-15T12:00:00Z",
    "expiresAt": "2024-01-15T13:00:00Z"
  }
}
```

## Error Responses

### 401 Unauthorized
```json
{
  "error": "Authorization header required"
}
```

### 403 Forbidden
```json
{
  "error": "Permission denied",
  "permission": "secrets:write",
  "projectId": "proj_123"
}
```

### 404 Not Found
```json
{
  "error": "Secret not found"
}
```

### 409 Conflict
```json
{
  "error": "Secret is already bound to this project"
}
```

### 500 Internal Server Error
```json
{
  "error": "Failed to encrypt provider config",
  "details": "Encryption key not available"
}
```
