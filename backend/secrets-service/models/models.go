package models

import (
	"encoding/json"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/models"
	"gorm.io/gorm"
)

// Secret represents a secret stored in the system
type Secret struct {
	ID          string       `json:"id" gorm:"primaryKey;type:text"`
	Name        string       `json:"name" gorm:"uniqueIndex:idx_secret_scope;not null"`
	Description string       `json:"description" gorm:"type:text"`
	ScopeID     string       `json:"scopeId" gorm:"uniqueIndex:idx_secret_scope;not null;index"`
	Scope       *SecretScope `json:"scope,omitempty" gorm:"foreignKey:ScopeID"`
	Type        SecretType   `json:"type" gorm:"not null"`
	Provider    string       `json:"provider" gorm:"not null;default:'internal'"`
	ProviderID  string       `json:"providerId" gorm:"index"`

	// Encrypted secret data
	EncryptedValue string `json:"-" gorm:"type:text;not null"`
	KeyID          string `json:"keyId" gorm:"not null;index"`

	// Metadata
	Tags         []string               `json:"tags" gorm:"-"`
	TagsJSON     json.RawMessage        `json:"-" gorm:"column:tags;type:jsonb"`
	Metadata     map[string]interface{} `json:"metadata" gorm:"-"`
	MetadataJSON json.RawMessage        `json:"-" gorm:"column:metadata;type:jsonb"`

	// Rotation settings
	RotationPolicy   *RotationPolicy `json:"rotationPolicy,omitempty" gorm:"foreignKey:RotationPolicyID"`
	RotationPolicyID *string         `json:"rotationPolicyId" gorm:"index"`
	LastRotated      *time.Time      `json:"lastRotated"`
	NextRotation     *time.Time      `json:"nextRotation" gorm:"index"`

	// Access control
	RequiresApproval bool            `json:"requiresApproval" gorm:"default:false"`
	ApprovedBy       []string        `json:"approvedBy" gorm:"-"`
	ApprovedByJSON   json.RawMessage `json:"-" gorm:"column:approved_by;type:jsonb"`

	// Status
	Status    SecretStatus `json:"status" gorm:"default:'active'"`
	ExpiresAt *time.Time   `json:"expiresAt" gorm:"index"`

	models.Base
}

// SecretVersion represents a version of a secret
type SecretVersion struct {
	ID             string                 `json:"id" gorm:"primaryKey;type:text"`
	SecretID       string                 `json:"secretId" gorm:"not null;index"`
	Secret         *Secret                `json:"secret,omitempty" gorm:"foreignKey:SecretID"`
	Version        int                    `json:"version" gorm:"not null"`
	EncryptedValue string                 `json:"-" gorm:"type:text;not null"`
	KeyID          string                 `json:"keyId" gorm:"not null;index"`
	CreatedBy      string                 `json:"createdBy" gorm:"not null"`
	Reason         string                 `json:"reason" gorm:"type:text"`
	Metadata       map[string]interface{} `json:"metadata" gorm:"-"`
	MetadataJSON   json.RawMessage        `json:"-" gorm:"column:metadata;type:jsonb"`
	IsActive       bool                   `json:"isActive" gorm:"default:false;index"`

	models.Base
}

// SecretScope represents a scope for organizing secrets (environment, application, etc.)
type SecretScope struct {
	ID          string         `json:"id" gorm:"primaryKey;type:text"`
	Name        string         `json:"name" gorm:"uniqueIndex;not null"`
	Type        ScopeType      `json:"type" gorm:"not null"`
	Description string         `json:"description" gorm:"type:text"`
	ParentID    *string        `json:"parentId" gorm:"index"`
	Parent      *SecretScope   `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Children    []*SecretScope `json:"children,omitempty" gorm:"foreignKey:ParentID"`

	// Configuration
	Config     map[string]interface{} `json:"config" gorm:"-"`
	ConfigJSON json.RawMessage        `json:"-" gorm:"column:config;type:jsonb"`

	// Access control
	Permissions     []ScopePermission `json:"permissions" gorm:"-"`
	PermissionsJSON json.RawMessage   `json:"-" gorm:"column:permissions;type:jsonb"`

	// Status
	IsActive bool `json:"isActive" gorm:"default:true;index"`

	models.Base
}

// Provider represents an external secret provider configuration
type Provider struct {
	ID          string       `json:"id" gorm:"primaryKey;type:text"`
	Name        string       `json:"name" gorm:"uniqueIndex;not null"`
	Type        ProviderType `json:"type" gorm:"not null"`
	Description string       `json:"description" gorm:"type:text"`

	// Configuration (encrypted)
	Config          map[string]interface{} `json:"config" gorm:"-"`
	EncryptedConfig string                 `json:"-" gorm:"type:text;not null"`
	KeyID           string                 `json:"keyId" gorm:"not null;index"`

	// Status
	IsActive    bool       `json:"isActive" gorm:"default:true;index"`
	LastTested  *time.Time `json:"lastTested"`
	TestStatus  string     `json:"testStatus" gorm:"default:'unknown'"`
	TestMessage string     `json:"testMessage" gorm:"type:text"`

	// Priority for provider selection
	Priority int `json:"priority" gorm:"default:0"`

	models.Base
}

// RotationPolicy represents a secret rotation policy
type RotationPolicy struct {
	ID          string `json:"id" gorm:"primaryKey;type:text"`
	Name        string `json:"name" gorm:"uniqueIndex;not null"`
	Description string `json:"description" gorm:"type:text"`

	// Rotation settings
	Interval    string `json:"interval" gorm:"not null"` // Duration string like "30d", "7d"
	MaxAge      string `json:"maxAge"`                   // Maximum age before forced rotation
	GracePeriod string `json:"gracePeriod"`              // Grace period for old secrets

	// Rotation strategy
	Strategy   RotationStrategy       `json:"strategy" gorm:"not null;default:'replace'"`
	Config     map[string]interface{} `json:"config" gorm:"-"`
	ConfigJSON json.RawMessage        `json:"-" gorm:"column:config;type:jsonb"`

	// Notification settings
	NotifyOnSuccess bool            `json:"notifyOnSuccess" gorm:"default:false"`
	NotifyOnFailure bool            `json:"notifyOnFailure" gorm:"default:true"`
	NotifyUsers     []string        `json:"notifyUsers" gorm:"-"`
	NotifyUsersJSON json.RawMessage `json:"-" gorm:"column:notify_users;type:jsonb"`

	// Status
	IsActive bool `json:"isActive" gorm:"default:true;index"`

	models.Base
}

// RotationHistory represents the history of secret rotations
type RotationHistory struct {
	ID               string          `json:"id" gorm:"primaryKey;type:text"`
	SecretID         string          `json:"secretId" gorm:"not null;index"`
	Secret           *Secret         `json:"secret,omitempty" gorm:"foreignKey:SecretID"`
	RotationPolicyID string          `json:"rotationPolicyId" gorm:"not null;index"`
	RotationPolicy   *RotationPolicy `json:"rotationPolicy,omitempty" gorm:"foreignKey:RotationPolicyID"`

	// Rotation details
	Status      RotationStatus `json:"status" gorm:"not null"`
	StartedAt   time.Time      `json:"startedAt" gorm:"not null"`
	CompletedAt *time.Time     `json:"completedAt"`
	Duration    int            `json:"duration"` // Duration in seconds

	// Version information
	OldVersion int `json:"oldVersion"`
	NewVersion int `json:"newVersion"`

	// Error information
	ErrorMessage string                 `json:"errorMessage" gorm:"type:text"`
	Metadata     map[string]interface{} `json:"metadata" gorm:"-"`
	MetadataJSON json.RawMessage        `json:"-" gorm:"column:metadata;type:jsonb"`

	// Triggered by
	TriggeredBy   string `json:"triggeredBy" gorm:"not null"`
	TriggerReason string `json:"triggerReason" gorm:"type:text"`

	models.Base
}

// EncryptionKey represents an encryption key used for secret encryption
type EncryptionKey struct {
	ID        string `json:"id" gorm:"primaryKey;type:text"`
	Name      string `json:"name" gorm:"uniqueIndex;not null"`
	Algorithm string `json:"algorithm" gorm:"not null"`
	KeySize   int    `json:"keySize" gorm:"not null"`
	Purpose   string `json:"purpose" gorm:"not null"` // "secret-encryption", "config-encryption", etc.

	// Key material (encrypted with master key)
	EncryptedKey string `json:"-" gorm:"type:text;not null"`
	Salt         string `json:"-" gorm:"type:text;not null"`

	// Status
	IsActive  bool       `json:"isActive" gorm:"default:true;index"`
	IsPrimary bool       `json:"isPrimary" gorm:"default:false;index"`
	ExpiresAt *time.Time `json:"expiresAt" gorm:"index"`
	RotatedAt *time.Time `json:"rotatedAt"`

	// Usage tracking
	UsageCount int64      `json:"usageCount" gorm:"default:0"`
	LastUsed   *time.Time `json:"lastUsed"`

	models.Base
}

// AuditLog represents an audit log entry for secret operations
type AuditLog struct {
	ID         string `json:"id" gorm:"primaryKey;type:text"`
	Action     string `json:"action" gorm:"not null;index"`
	Resource   string `json:"resource" gorm:"not null;index"`
	ResourceID string `json:"resourceId" gorm:"index"`
	UserID     string `json:"userId" gorm:"not null;index"`
	Username   string `json:"username" gorm:"not null"`

	// Request details
	IPAddress string `json:"ipAddress" gorm:"index"`
	UserAgent string `json:"userAgent" gorm:"type:text"`
	Method    string `json:"method" gorm:"not null"`
	Path      string `json:"path" gorm:"not null"`

	// Result
	Status  int    `json:"status" gorm:"not null;index"`
	Success bool   `json:"success" gorm:"not null;index"`
	Error   string `json:"error" gorm:"type:text"`

	// Additional data
	Metadata     map[string]interface{} `json:"metadata" gorm:"-"`
	MetadataJSON json.RawMessage        `json:"-" gorm:"column:metadata;type:jsonb"`

	// Timing
	Duration int64 `json:"duration"` // Duration in microseconds

	models.Base
}

// AccessLog represents an access log entry for secret retrieval
type AccessLog struct {
	ID       string  `json:"id" gorm:"primaryKey;type:text"`
	SecretID string  `json:"secretId" gorm:"not null;index"`
	Secret   *Secret `json:"secret,omitempty" gorm:"foreignKey:SecretID"`

	// Access details
	UserID    string `json:"userId" gorm:"not null;index"`
	Username  string `json:"username" gorm:"not null"`
	ServiceID string `json:"serviceId" gorm:"index"`

	// Request details
	IPAddress string `json:"ipAddress" gorm:"index"`
	UserAgent string `json:"userAgent" gorm:"type:text"`
	Method    string `json:"method" gorm:"not null"`

	// Result
	Success bool   `json:"success" gorm:"not null;index"`
	Error   string `json:"error" gorm:"type:text"`
	Version int    `json:"version"`

	// Additional data
	Metadata     map[string]interface{} `json:"metadata" gorm:"-"`
	MetadataJSON json.RawMessage        `json:"-" gorm:"column:metadata;type:jsonb"`

	models.Base
}

// Enums

type SecretType string

const (
	SecretTypeGeneric     SecretType = "generic"
	SecretTypePassword    SecretType = "password"
	SecretTypeAPIKey      SecretType = "api_key"
	SecretTypeCertificate SecretType = "certificate"
	SecretTypeSSHKey      SecretType = "ssh_key"
	SecretTypeDatabase    SecretType = "database"
	SecretTypeOAuth       SecretType = "oauth"
	SecretTypeJWT         SecretType = "jwt"
)

type SecretStatus string

const (
	SecretStatusActive   SecretStatus = "active"
	SecretStatusInactive SecretStatus = "inactive"
	SecretStatusExpired  SecretStatus = "expired"
	SecretStatusRotating SecretStatus = "rotating"
	SecretStatusPending  SecretStatus = "pending"
)

type ScopeType string

const (
	ScopeTypeEnvironment ScopeType = "environment"
	ScopeTypeApplication ScopeType = "application"
	ScopeTypeProject     ScopeType = "project"
	ScopeTypeTeam        ScopeType = "team"
	ScopeTypeService     ScopeType = "service"
	ScopeTypeNamespace   ScopeType = "namespace"
	ScopeTypeGlobal      ScopeType = "global"
)

type ProviderType string

const (
	ProviderTypeInternal ProviderType = "internal"
	ProviderTypeConjur   ProviderType = "conjur"
	ProviderTypeVault    ProviderType = "vault"
	ProviderTypeAWS      ProviderType = "aws"
	ProviderTypeAzure    ProviderType = "azure"
	ProviderTypeGCP      ProviderType = "gcp"
)

type RotationStrategy string

const (
	RotationStrategyReplace   RotationStrategy = "replace"
	RotationStrategyVersioned RotationStrategy = "versioned"
	RotationStrategyBlueGreen RotationStrategy = "blue_green"
)

type RotationStatus string

const (
	RotationStatusPending    RotationStatus = "pending"
	RotationStatusInProgress RotationStatus = "in_progress"
	RotationStatusCompleted  RotationStatus = "completed"
	RotationStatusFailed     RotationStatus = "failed"
	RotationStatusCancelled  RotationStatus = "cancelled"
)

// ScopePermission represents permissions for a scope
type ScopePermission struct {
	UserID      string   `json:"userId"`
	Username    string   `json:"username"`
	Permissions []string `json:"permissions"` // read, write, delete, rotate, approve
}

// ProjectSecretBinding represents the binding between secrets and projects
type ProjectSecretBinding struct {
	ID        string `json:"id" gorm:"primaryKey;type:varchar(255)"`
	ProjectID string `json:"projectId" gorm:"not null;index"`
	SecretID  string `json:"secretId" gorm:"not null;index"`

	// Variable configuration for deployment/workflow usage
	VariableName string `json:"variableName" gorm:"not null"` // How this secret appears as a variable
	VariableType string `json:"variableType" gorm:"not null"` // env, file, config, etc.

	// Access control
	AccessLevel string `json:"accessLevel" gorm:"not null"` // read, write, admin

	// Usage context
	Environments     []string        `json:"environments" gorm:"-"` // Which environments can use this
	EnvironmentsJSON json.RawMessage `json:"-" gorm:"column:environments;type:jsonb"`

	Services     []string        `json:"services" gorm:"-"` // Which services can use this
	ServicesJSON json.RawMessage `json:"-" gorm:"column:services;type:jsonb"`

	// Metadata
	Description  string                 `json:"description"`
	Metadata     map[string]interface{} `json:"metadata" gorm:"-"`
	MetadataJSON json.RawMessage        `json:"-" gorm:"column:metadata;type:jsonb"`

	// Relationships
	Project *Project `json:"project,omitempty" gorm:"foreignKey:ProjectID"`
	Secret  *Secret  `json:"secret,omitempty" gorm:"foreignKey:SecretID"`

	models.Base
}

// Project represents a project (simplified reference)
type Project struct {
	ID          string `json:"id" gorm:"primaryKey;type:varchar(255)"`
	Name        string `json:"name" gorm:"not null"`
	Description string `json:"description"`

	models.Base
}

// SecretVariable represents how a secret is exposed as a variable
type SecretVariable struct {
	ID        string `json:"id" gorm:"primaryKey;type:varchar(255)"`
	SecretID  string `json:"secretId" gorm:"not null;index"`
	ProjectID string `json:"projectId" gorm:"not null;index"`

	// Variable definition
	Name   string `json:"name" gorm:"not null"` // Variable name (e.g., DATABASE_PASSWORD)
	Type   string `json:"type" gorm:"not null"` // env, file, config, mount
	Path   string `json:"path"`                 // For file/mount types
	Format string `json:"format"`               // json, yaml, plain, base64

	// Context restrictions
	Environment string `json:"environment"` // Specific environment
	Service     string `json:"service"`     // Specific service
	Namespace   string `json:"namespace"`   // Kubernetes namespace

	// Transformation rules
	Transform     map[string]interface{} `json:"transform" gorm:"-"`
	TransformJSON json.RawMessage        `json:"-" gorm:"column:transform;type:jsonb"`

	// Relationships
	Secret  *Secret  `json:"secret,omitempty" gorm:"foreignKey:SecretID"`
	Project *Project `json:"project,omitempty" gorm:"foreignKey:ProjectID"`

	models.Base
}

// DeploymentSecretUsage tracks secret usage in deployments
type DeploymentSecretUsage struct {
	ID           string `json:"id" gorm:"primaryKey;type:varchar(255)"`
	DeploymentID string `json:"deploymentId" gorm:"not null;index"`
	SecretID     string `json:"secretId" gorm:"not null;index"`
	VariableID   string `json:"variableId" gorm:"not null;index"`

	// Usage details
	UsedAt      time.Time `json:"usedAt" gorm:"not null"`
	UsedBy      string    `json:"usedBy" gorm:"not null"` // User or service
	Environment string    `json:"environment"`
	Service     string    `json:"service"`

	// Status
	Status       string `json:"status" gorm:"not null"` // success, failed, pending
	ErrorMessage string `json:"errorMessage"`

	// Relationships
	Secret   *Secret         `json:"secret,omitempty" gorm:"foreignKey:SecretID"`
	Variable *SecretVariable `json:"variable,omitempty" gorm:"foreignKey:VariableID"`

	models.Base
}

// WorkflowSecretUsage tracks secret usage in workflows
type WorkflowSecretUsage struct {
	ID          string `json:"id" gorm:"primaryKey;type:varchar(255)"`
	WorkflowID  string `json:"workflowId" gorm:"not null;index"`
	ExecutionID string `json:"executionId" gorm:"not null;index"`
	SecretID    string `json:"secretId" gorm:"not null;index"`
	VariableID  string `json:"variableId" gorm:"not null;index"`

	// Usage details
	StepName string    `json:"stepName"`
	UsedAt   time.Time `json:"usedAt" gorm:"not null"`
	UsedBy   string    `json:"usedBy" gorm:"not null"`

	// Status
	Status       string `json:"status" gorm:"not null"`
	ErrorMessage string `json:"errorMessage"`

	// Relationships
	Secret   *Secret         `json:"secret,omitempty" gorm:"foreignKey:SecretID"`
	Variable *SecretVariable `json:"variable,omitempty" gorm:"foreignKey:VariableID"`

	models.Base
}

// GORM hooks for JSON marshaling/unmarshaling

func (s *Secret) BeforeSave() error {
	if s.Tags != nil {
		if data, err := json.Marshal(s.Tags); err == nil {
			s.TagsJSON = data
		}
	}
	if s.Metadata != nil {
		if data, err := json.Marshal(s.Metadata); err == nil {
			s.MetadataJSON = data
		}
	}
	if s.ApprovedBy != nil {
		if data, err := json.Marshal(s.ApprovedBy); err == nil {
			s.ApprovedByJSON = data
		}
	}
	return nil
}

func (s *Secret) AfterFind() error {
	if len(s.TagsJSON) > 0 {
		json.Unmarshal(s.TagsJSON, &s.Tags)
	}
	if len(s.MetadataJSON) > 0 {
		json.Unmarshal(s.MetadataJSON, &s.Metadata)
	}
	if len(s.ApprovedByJSON) > 0 {
		json.Unmarshal(s.ApprovedByJSON, &s.ApprovedBy)
	}
	return nil
}

// Additional GORM hooks for other models

func (sv *SecretVersion) BeforeSave() error {
	if sv.Metadata != nil {
		if data, err := json.Marshal(sv.Metadata); err == nil {
			sv.MetadataJSON = data
		}
	}
	return nil
}

func (sv *SecretVersion) AfterFind() error {
	if len(sv.MetadataJSON) > 0 {
		json.Unmarshal(sv.MetadataJSON, &sv.Metadata)
	}
	return nil
}

func (ss *SecretScope) BeforeSave() error {
	if ss.Config != nil {
		if data, err := json.Marshal(ss.Config); err == nil {
			ss.ConfigJSON = data
		}
	}
	if ss.Permissions != nil {
		if data, err := json.Marshal(ss.Permissions); err == nil {
			ss.PermissionsJSON = data
		}
	}
	return nil
}

func (ss *SecretScope) AfterFind() error {
	if len(ss.ConfigJSON) > 0 {
		json.Unmarshal(ss.ConfigJSON, &ss.Config)
	}
	if len(ss.PermissionsJSON) > 0 {
		json.Unmarshal(ss.PermissionsJSON, &ss.Permissions)
	}
	return nil
}

func (rp *RotationPolicy) BeforeSave() error {
	if rp.Config != nil {
		if data, err := json.Marshal(rp.Config); err == nil {
			rp.ConfigJSON = data
		}
	}
	if rp.NotifyUsers != nil {
		if data, err := json.Marshal(rp.NotifyUsers); err == nil {
			rp.NotifyUsersJSON = data
		}
	}
	return nil
}

func (rp *RotationPolicy) AfterFind() error {
	if len(rp.ConfigJSON) > 0 {
		json.Unmarshal(rp.ConfigJSON, &rp.Config)
	}
	if len(rp.NotifyUsersJSON) > 0 {
		json.Unmarshal(rp.NotifyUsersJSON, &rp.NotifyUsers)
	}
	return nil
}

func (rh *RotationHistory) BeforeSave() error {
	if rh.Metadata != nil {
		if data, err := json.Marshal(rh.Metadata); err == nil {
			rh.MetadataJSON = data
		}
	}
	return nil
}

func (rh *RotationHistory) AfterFind() error {
	if len(rh.MetadataJSON) > 0 {
		json.Unmarshal(rh.MetadataJSON, &rh.Metadata)
	}
	return nil
}

func (al *AuditLog) BeforeSave() error {
	if al.Metadata != nil {
		if data, err := json.Marshal(al.Metadata); err == nil {
			al.MetadataJSON = data
		}
	}
	return nil
}

func (al *AuditLog) AfterFind() error {
	if len(al.MetadataJSON) > 0 {
		json.Unmarshal(al.MetadataJSON, &al.Metadata)
	}
	return nil
}

func (acl *AccessLog) BeforeSave() error {
	if acl.Metadata != nil {
		if data, err := json.Marshal(acl.Metadata); err == nil {
			acl.MetadataJSON = data
		}
	}
	return nil
}

func (acl *AccessLog) AfterFind() error {
	if len(acl.MetadataJSON) > 0 {
		json.Unmarshal(acl.MetadataJSON, &acl.Metadata)
	}
	return nil
}

// GORM hooks for new models

func (psb *ProjectSecretBinding) BeforeSave() error {
	if psb.Environments != nil {
		if data, err := json.Marshal(psb.Environments); err == nil {
			psb.EnvironmentsJSON = data
		}
	}
	if psb.Services != nil {
		if data, err := json.Marshal(psb.Services); err == nil {
			psb.ServicesJSON = data
		}
	}
	if psb.Metadata != nil {
		if data, err := json.Marshal(psb.Metadata); err == nil {
			psb.MetadataJSON = data
		}
	}
	return nil
}

func (psb *ProjectSecretBinding) AfterFind() error {
	if len(psb.EnvironmentsJSON) > 0 {
		json.Unmarshal(psb.EnvironmentsJSON, &psb.Environments)
	}
	if len(psb.ServicesJSON) > 0 {
		json.Unmarshal(psb.ServicesJSON, &psb.Services)
	}
	if len(psb.MetadataJSON) > 0 {
		json.Unmarshal(psb.MetadataJSON, &psb.Metadata)
	}
	return nil
}

func (sv *SecretVariable) BeforeSave() error {
	if sv.Transform != nil {
		if data, err := json.Marshal(sv.Transform); err == nil {
			sv.TransformJSON = data
		}
	}
	return nil
}

func (sv *SecretVariable) AfterFind() error {
	if len(sv.TransformJSON) > 0 {
		json.Unmarshal(sv.TransformJSON, &sv.Transform)
	}
	return nil
}

// MigrateAll runs all database migrations
func MigrateAll(db *gorm.DB) error {
	// Define the order of migrations to handle foreign key dependencies
	modelsToMigrate := []interface{}{
		&SecretScope{},
		&RotationPolicy{},
		&Provider{},
		&EncryptionKey{},
		&Project{},
		&Secret{},
		&SecretVersion{},
		&ProjectSecretBinding{},
		&SecretVariable{},
		&DeploymentSecretUsage{},
		&WorkflowSecretUsage{},
		&RotationHistory{},
		&AuditLog{},
		&AccessLog{},
	}

	for _, model := range modelsToMigrate {
		if err := db.AutoMigrate(model); err != nil {
			return err
		}
	}

	return nil
}
