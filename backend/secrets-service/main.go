package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/claudio/deploy-orchestrator/secrets-service/api"
	"github.com/claudio/deploy-orchestrator/secrets-service/config"
	"github.com/claudio/deploy-orchestrator/secrets-service/encryption"
	"github.com/claudio/deploy-orchestrator/secrets-service/models"
	"github.com/claudio/deploy-orchestrator/secrets-service/providers"
	"github.com/claudio/deploy-orchestrator/secrets-service/rotation"
	"github.com/claudio/deploy-orchestrator/secrets-service/storage"
	"github.com/claudio/deploy-orchestrator/shared/auth"
	"github.com/claudio/deploy-orchestrator/shared/gateway"
	"github.com/claudio/deploy-orchestrator/shared/logging"
	"github.com/claudio/deploy-orchestrator/shared/monitoring"
)

func main() {
	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		fmt.Printf("Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logger with configuration
	logging.InitLogger(cfg.Logging)
	logger := logging.Default().Named("secrets-service")

	// Log startup information
	logger.Info("Starting secrets service",
		logging.String("version", cfg.Service.Version),
		logging.String("environment", "development"),
	)

	// Create default config file if it doesn't exist
	if err := config.SaveDefaultConfig(); err != nil {
		logger.Warn("Failed to save default config", logging.Error(err))
	}

	// Initialize database
	var db *gorm.DB
	var dbErr error
	if cfg.Database.URL != "" {
		logger.Info("Connecting to database",
			logging.String("url", cfg.Database.URL),
			logging.Int("maxRetries", cfg.Database.MaxRetries),
			logging.Int("retryInterval", cfg.Database.RetryInterval),
		)
		db, dbErr = storage.InitDatabase(cfg)
	} else {
		logger.Info("Database URL not configured, using in-memory database")
		db = &gorm.DB{}
	}

	if dbErr != nil {
		logger.Error("Failed to initialize database", logging.Error(dbErr))
		os.Exit(1)
	}

	logger.Info("Database connection established successfully")

	// Run database migrations
	logger.Info("Running database migrations...")
	if err := models.MigrateAll(db); err != nil {
		logger.Error("Failed to run database migrations", logging.Error(err))
		os.Exit(1)
	}
	logger.Info("Database migrations completed successfully")

	// Initialize monitoring system
	monitoringManager := monitoring.NewMonitoringManager("secrets-service", cfg.Service.Version, "development")
	monitoringManager.AddDatabase(db, "postgres")
	logger.Info("Monitoring system initialized")

	// Initialize gateway registration
	zapLogger, _ := zap.NewProduction()
	gatewayClient := gateway.NewClientFromSharedConfig("secrets-service", cfg.Server.Port, &cfg.Gateway, zapLogger)
	gatewayClient.SafeRegister()
	logger.Info("Gateway registration attempted")

	// Initialize encryption manager
	encryptionManager, err := encryption.NewManager(cfg.Encryption)
	if err != nil {
		logger.Error("Failed to initialize encryption manager", logging.Error(err))
		os.Exit(1)
	}

	// Initialize secret providers
	providerManager, err := providers.NewManager(cfg.Providers, zapLogger)
	if err != nil {
		logger.Error("Failed to initialize provider manager", logging.Error(err))
		os.Exit(1)
	}

	// Initialize rotation manager
	rotationManager := rotation.NewManager(db, providerManager, encryptionManager, zapLogger)

	// Start rotation scheduler
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go rotationManager.StartScheduler(ctx)
	// Initialize shared authentication manager
	authConfig := &auth.Config{
		JWTSecretKey:       cfg.Auth.JWTSecret,
		AccessTokenExpiry:  time.Duration(cfg.Auth.JWTExpirationMinutes) * time.Minute,
		RefreshTokenExpiry: time.Hour * 24 * 7, // 7 days
		AdminServiceURL:    cfg.Auth.AdminServiceURL,
	}
	authManager, err := auth.NewAuthManager(authConfig)
	if err != nil {
		logger.Error("Failed to create auth manager", logging.Error(err))
		os.Exit(1)
	}

	// Use shared authentication middleware
	authMiddleware := authManager.AuthMiddleware()

	// Initialize permission service and middleware
	permissionService := auth.NewHTTPPermissionService(cfg.Auth.AdminServiceURL)
	permissionMiddleware := auth.NewPermissionMiddleware(permissionService)

	// Set up the Gin router
	if cfg.Logging.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Setup monitoring routes and middleware
	monitoringManager.SetupRoutes(router)
	monitoringManager.SetupMiddleware(router)

	// Apply global middleware
	router.Use(func(c *gin.Context) {
		start := time.Now()
		c.Next()
		duration := time.Since(start)
		logger.Info("Request",
			logging.String("method", c.Request.Method),
			logging.String("path", c.Request.URL.Path),
			logging.Int("status", c.Writer.Status()),
			logging.Duration("duration", duration),
		)
	})

	router.Use(func(c *gin.Context) {
		c.Next()
		if len(c.Errors) > 0 {
			logger.Error("Request error", logging.String("error", c.Errors.Last().Error()))
		}
	})

	// Apply CORS middleware
	router.Use(authManager.CORSMiddleware())

	// Initialize repository
	repository := storage.NewRepository(db)

	// Initialize API handlers
	apiHandler := api.NewHandler(db, repository, providerManager, encryptionManager, rotationManager, zapLogger, cfg)

	// Setup routes
	setupRoutes(router, apiHandler, authMiddleware, permissionMiddleware)

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
	}

	// Start server in a goroutine
	go func() {
		logger.Info("Starting secrets service",
			logging.String("host", cfg.Server.Host),
			logging.Int("port", cfg.Server.Port),
		)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", logging.Error(err))
		}
	}()

	// Set up graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info("Shutting down server...")

	// Deregister from gateway
	gatewayClient.SafeDeregister()
	logger.Info("Deregistered from gateway")

	// Cancel rotation scheduler
	cancel()

	// Create a deadline to wait for
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer shutdownCancel()

	// Shutdown monitoring system
	if err := monitoringManager.Shutdown(shutdownCtx); err != nil {
		logger.Error("Failed to shutdown monitoring system", logging.Error(err))
	}

	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Fatal("Server forced to shutdown", logging.Error(err))
	}

	logger.Info("Server exited properly")
}

func setupRoutes(router *gin.Engine, handler *api.Handler, authMiddleware gin.HandlerFunc, permissionMiddleware *auth.PermissionMiddleware) {
	// API routes with authentication - use v1 prefix with service-specific path
	v1 := router.Group("/api/v1/secrets-service")
	v1.Use(authMiddleware)
	{
		// Secret management (Permission-based access)
		secrets := v1.Group("/secrets")
		{
			secrets.POST("",
				permissionMiddleware.RequirePermission("secrets:create", nil),
				handler.CreateSecret)
			secrets.GET("", handler.ListSecrets)   // List shows only accessible secrets
			secrets.GET("/:id", handler.GetSecret) // Handler checks secret access
			secrets.PUT("/:id",
				permissionMiddleware.RequirePermission("secrets:write", nil),
				handler.UpdateSecret)
			secrets.DELETE("/:id",
				permissionMiddleware.RequirePermission("secrets:delete", nil),
				handler.DeleteSecret)
			secrets.POST("/:id/rotate",
				permissionMiddleware.RequirePermission("secrets:write", nil),
				handler.RotateSecret)
			secrets.GET("/:id/versions", handler.GetSecretVersions)
			secrets.GET("/:id/versions/:version", handler.GetSecretVersion)
		}

		// Secret scopes (environments, applications)
		scopes := v1.Group("/scopes")
		{
			scopes.POST("", handler.CreateScope)
			scopes.GET("", handler.ListScopes)
			scopes.GET("/:id", handler.GetScope)
			scopes.PUT("/:id", handler.UpdateScope)
			scopes.DELETE("/:id", handler.DeleteScope)
		}

		// Provider management (Admin only)
		providers := v1.Group("/providers")
		providers.Use(permissionMiddleware.RequireAdmin())
		{
			providers.GET("/types", handler.GetProviderTypes) // Get available provider types
			providers.POST("", handler.CreateProvider)
			providers.GET("", handler.ListProviders)
			providers.GET("/:id", handler.GetProvider)
			providers.PUT("/:id", handler.UpdateProvider)
			providers.DELETE("/:id", handler.DeleteProvider)
			providers.POST("/:id/test", handler.TestProvider)
		}

		// Rotation policies
		rotation := v1.Group("/rotation")
		{
			rotation.POST("/policies", handler.CreateRotationPolicy)
			rotation.GET("/policies", handler.ListRotationPolicies)
			rotation.GET("/policies/:id", handler.GetRotationPolicy)
			rotation.PUT("/policies/:id", handler.UpdateRotationPolicy)
			rotation.DELETE("/policies/:id", handler.DeleteRotationPolicy)
			rotation.GET("/history", handler.GetRotationHistory)
		}

		// Encryption key management
		encryption := v1.Group("/encryption")
		{
			encryption.POST("/keys", handler.CreateEncryptionKey)
			encryption.GET("/keys", handler.ListEncryptionKeys)
			encryption.POST("/keys/:id/rotate", handler.RotateEncryptionKey)
			encryption.GET("/keys/:id/status", handler.GetKeyStatus)
		}

		// Project secret management (Project-scoped access)
		projects := v1.Group("/projects")
		projects.Use(permissionMiddleware.ProjectAccessMiddleware())
		{
			projects.GET("/:projectId/secrets", handler.GetProjectSecrets)
			projects.POST("/:projectId/secrets/bind",
				permissionMiddleware.RequirePermission("secrets:write", auth.ProjectIDFromParam),
				handler.BindSecretToProject)
			projects.DELETE("/:projectId/secrets/:bindingId",
				permissionMiddleware.RequirePermission("secrets:write", auth.ProjectIDFromParam),
				handler.UnbindSecretFromProject)
			projects.GET("/:projectId/variables", handler.GetProjectSecretVariables)
			projects.POST("/:projectId/variables",
				permissionMiddleware.RequirePermission("secrets:write", auth.ProjectIDFromParam),
				handler.CreateSecretVariable)
		}

		// Audit and monitoring
		audit := v1.Group("/audit")
		{
			audit.GET("/logs", handler.GetAuditLogs)
			audit.GET("/access", handler.GetAccessLogs)
			audit.GET("/metrics", handler.GetSecretMetrics)
		}

		// Integration endpoints for other services
		integration := v1.Group("/integration")
		{
			integration.POST("/retrieve", handler.RetrieveSecrets)
			integration.POST("/bulk-retrieve", handler.BulkRetrieveSecrets)
			integration.GET("/service/:service/secrets", handler.GetServiceSecrets)
			integration.POST("/deployment/secrets", handler.GetDeploymentSecrets)
			integration.POST("/workflow/secrets", handler.GetWorkflowSecrets)
		}
	}
}
