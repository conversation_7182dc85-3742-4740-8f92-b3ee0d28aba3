package encryption

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"time"

	"golang.org/x/crypto/pbkdf2"

	"github.com/claudio/deploy-orchestrator/secrets-service/models"
	"github.com/claudio/deploy-orchestrator/shared/config"
)

// Manager handles encryption and decryption operations
type Manager struct {
	config    config.EncryptionConfig
	masterKey []byte
	gcm       cipher.AEAD
}

// NewManager creates a new encryption manager
func NewManager(cfg config.EncryptionConfig) (*Manager, error) {
	if cfg.MasterKey == "" {
		return nil, fmt.Errorf("master key is required")
	}

	// Derive master key from configuration
	masterKey := deriveKey(cfg.MasterKey, "master-key-salt", cfg.KeySize)

	// Create AES cipher
	block, err := aes.NewCipher(masterKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %w", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	return &Manager{
		config:    cfg,
		masterKey: masterKey,
		gcm:       gcm,
	}, nil
}

// EncryptSecret encrypts a secret value
func (m *Manager) EncryptSecret(plaintext string, keyID string) (string, error) {
	if plaintext == "" {
		return "", fmt.Errorf("plaintext cannot be empty")
	}

	// Generate a random nonce
	nonce := make([]byte, m.gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}

	// Encrypt the plaintext
	ciphertext := m.gcm.Seal(nonce, nonce, []byte(plaintext), []byte(keyID))

	// Encode to base64
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// DecryptSecret decrypts a secret value
func (m *Manager) DecryptSecret(ciphertext string, keyID string) (string, error) {
	if ciphertext == "" {
		return "", fmt.Errorf("ciphertext cannot be empty")
	}

	// Decode from base64
	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %w", err)
	}

	// Extract nonce and ciphertext
	nonceSize := m.gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	nonce, cipherData := data[:nonceSize], data[nonceSize:]

	// Decrypt
	plaintext, err := m.gcm.Open(nil, nonce, cipherData, []byte(keyID))
	if err != nil {
		return "", fmt.Errorf("failed to decrypt: %w", err)
	}

	return string(plaintext), nil
}

// EncryptConfig encrypts configuration data
func (m *Manager) EncryptConfig(config map[string]interface{}, keyID string) (string, error) {
	// Convert config to JSON
	data, err := json.Marshal(config)
	if err != nil {
		return "", fmt.Errorf("failed to marshal config: %w", err)
	}

	return m.EncryptSecret(string(data), keyID)
}

// DecryptConfig decrypts configuration data
func (m *Manager) DecryptConfig(encryptedConfig string, keyID string) (map[string]interface{}, error) {
	// Decrypt the data
	plaintext, err := m.DecryptSecret(encryptedConfig, keyID)
	if err != nil {
		return nil, err
	}

	// Parse JSON
	var config map[string]interface{}
	if err := json.Unmarshal([]byte(plaintext), &config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return config, nil
}

// GenerateKey generates a new encryption key
func (m *Manager) GenerateKey(name, purpose string) (*models.EncryptionKey, error) {
	// Generate random key material
	keyMaterial := make([]byte, m.config.KeySize)
	if _, err := io.ReadFull(rand.Reader, keyMaterial); err != nil {
		return nil, fmt.Errorf("failed to generate key material: %w", err)
	}

	// Generate salt
	salt := make([]byte, m.config.SaltSize)
	if _, err := io.ReadFull(rand.Reader, salt); err != nil {
		return nil, fmt.Errorf("failed to generate salt: %w", err)
	}

	// Encrypt key material with master key
	encryptedKey, err := m.encryptKeyMaterial(keyMaterial)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt key material: %w", err)
	}

	// Create encryption key model
	key := &models.EncryptionKey{
		ID:           generateID(),
		Name:         name,
		Algorithm:    m.config.Algorithm,
		KeySize:      m.config.KeySize,
		Purpose:      purpose,
		EncryptedKey: encryptedKey,
		Salt:         base64.StdEncoding.EncodeToString(salt),
		IsActive:     true,
		IsPrimary:    false,
		UsageCount:   0,
	}

	return key, nil
}

// RotateKey creates a new version of an encryption key
func (m *Manager) RotateKey(oldKey *models.EncryptionKey) (*models.EncryptionKey, error) {
	// Generate new key with same properties
	newKey, err := m.GenerateKey(oldKey.Name, oldKey.Purpose)
	if err != nil {
		return nil, err
	}

	// Mark old key as inactive
	oldKey.IsActive = false
	oldKey.IsPrimary = false
	now := time.Now()
	oldKey.RotatedAt = &now

	return newKey, nil
}

// GetKeyMaterial retrieves and decrypts key material
func (m *Manager) GetKeyMaterial(key *models.EncryptionKey) ([]byte, error) {
	return m.decryptKeyMaterial(key.EncryptedKey)
}

// ValidateKeyAge checks if a key needs rotation based on age
func (m *Manager) ValidateKeyAge(key *models.EncryptionKey) (bool, error) {
	if m.config.MaxKeyAge == "" {
		return false, nil // No age limit configured
	}

	maxAge, err := time.ParseDuration(m.config.MaxKeyAge)
	if err != nil {
		return false, fmt.Errorf("invalid max key age: %w", err)
	}

	age := time.Since(key.CreatedAt)
	return age > maxAge, nil
}

// encryptKeyMaterial encrypts key material with the master key
func (m *Manager) encryptKeyMaterial(keyMaterial []byte) (string, error) {
	// Generate nonce
	nonce := make([]byte, m.gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}

	// Encrypt
	ciphertext := m.gcm.Seal(nonce, nonce, keyMaterial, nil)

	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decryptKeyMaterial decrypts key material with the master key
func (m *Manager) decryptKeyMaterial(encryptedKey string) ([]byte, error) {
	// Decode from base64
	data, err := base64.StdEncoding.DecodeString(encryptedKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64: %w", err)
	}

	// Extract nonce and ciphertext
	nonceSize := m.gcm.NonceSize()
	if len(data) < nonceSize {
		return nil, fmt.Errorf("encrypted key too short")
	}

	nonce, cipherData := data[:nonceSize], data[nonceSize:]

	// Decrypt
	keyMaterial, err := m.gcm.Open(nil, nonce, cipherData, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt key material: %w", err)
	}

	return keyMaterial, nil
}

// deriveKey derives a key from a password using PBKDF2
func deriveKey(password, salt string, keyLen int) []byte {
	return pbkdf2.Key([]byte(password), []byte(salt), 100000, keyLen, sha256.New)
}

// generateID generates a unique ID
func generateID() string {
	// Generate random bytes
	bytes := make([]byte, 16)
	if _, err := io.ReadFull(rand.Reader, bytes); err != nil {
		// Fallback to timestamp-based ID
		return fmt.Sprintf("key_%d", time.Now().UnixNano())
	}

	return fmt.Sprintf("key_%x", bytes)
}

// SecretEncryptor provides a simplified interface for secret encryption
type SecretEncryptor struct {
	manager *Manager
	keyID   string
}

// NewSecretEncryptor creates a new secret encryptor with a specific key
func (m *Manager) NewSecretEncryptor(keyID string) *SecretEncryptor {
	return &SecretEncryptor{
		manager: m,
		keyID:   keyID,
	}
}

// Encrypt encrypts a value using the configured key
func (se *SecretEncryptor) Encrypt(plaintext string) (string, error) {
	return se.manager.EncryptSecret(plaintext, se.keyID)
}

// Decrypt decrypts a value using the configured key
func (se *SecretEncryptor) Decrypt(ciphertext string) (string, error) {
	return se.manager.DecryptSecret(ciphertext, se.keyID)
}

// KeyRotationInfo provides information about key rotation requirements
type KeyRotationInfo struct {
	KeyID           string     `json:"keyId"`
	Name            string     `json:"name"`
	Age             string     `json:"age"`
	NeedsRotation   bool       `json:"needsRotation"`
	RotationReason  string     `json:"rotationReason"`
	LastRotated     *time.Time `json:"lastRotated"`
	UsageCount      int64      `json:"usageCount"`
	RecommendedDate *time.Time `json:"recommendedDate"`
}

// GetKeyRotationInfo analyzes a key and provides rotation recommendations
func (m *Manager) GetKeyRotationInfo(key *models.EncryptionKey) (*KeyRotationInfo, error) {
	info := &KeyRotationInfo{
		KeyID:       key.ID,
		Name:        key.Name,
		Age:         time.Since(key.CreatedAt).String(),
		LastRotated: key.RotatedAt,
		UsageCount:  key.UsageCount,
	}

	// Check age-based rotation
	needsRotation, err := m.ValidateKeyAge(key)
	if err != nil {
		return nil, err
	}

	if needsRotation {
		info.NeedsRotation = true
		info.RotationReason = "Key has exceeded maximum age"
	}

	// Check rotation interval
	if m.config.RotationInterval != "" {
		interval, err := time.ParseDuration(m.config.RotationInterval)
		if err == nil {
			var lastRotation time.Time
			if key.RotatedAt != nil {
				lastRotation = *key.RotatedAt
			} else {
				lastRotation = key.CreatedAt
			}

			if time.Since(lastRotation) > interval {
				info.NeedsRotation = true
				if info.RotationReason == "" {
					info.RotationReason = "Key rotation interval exceeded"
				}
			}

			// Calculate recommended rotation date
			recommendedDate := lastRotation.Add(interval)
			info.RecommendedDate = &recommendedDate
		}
	}

	return info, nil
}
