package config

import (
	"fmt"
	"os"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/config"
)

// Config holds the complete configuration for the secrets service
type Config struct {
	Service    config.ServiceConfig    `yaml:"service"`
	Server     config.ServerConfig     `yaml:"server"`
	Database   config.DBConfig         `yaml:"db"`
	Logging    config.LoggingConfig    `yaml:"logging"`
	Monitoring config.MonitoringConfig `yaml:"monitoring"`
	Auth       config.AuthConfig       `yaml:"auth"`
	Gateway    config.GatewayConfig    `yaml:"gateway"`
	Encryption config.EncryptionConfig `yaml:"encryption"`
	Providers  ProvidersConfig         `yaml:"providers"`
	Rotation   RotationConfig          `yaml:"rotation"`
	Security   SecurityConfig          `yaml:"security"`
}

// ProvidersConfig holds configuration for external secret providers
type ProvidersConfig struct {
	// CyberArk Conjur
	Conjur ConjurConfig `mapstructure:"conjur" yaml:"conjur"`

	// HashiCorp Vault
	Vault VaultConfig `mapstructure:"vault" yaml:"vault"`

	// AWS Secrets Manager
	AWS AWSConfig `mapstructure:"aws" yaml:"aws"`

	// Azure Key Vault
	Azure AzureConfig `mapstructure:"azure" yaml:"azure"`

	// Google Secret Manager
	GCP GCPConfig `mapstructure:"gcp" yaml:"gcp"`

	// Default provider
	DefaultProvider string `mapstructure:"default_provider" yaml:"default_provider" env:"SECRETS_DEFAULT_PROVIDER" default:"internal"`

	// Provider types configuration
	Types []ProviderTypeConfig `mapstructure:"types" yaml:"types"`
}

// ProviderTypeConfig defines a secret provider type
type ProviderTypeConfig struct {
	Type         string                 `yaml:"type" json:"type"`
	Name         string                 `yaml:"name" json:"name"`
	Description  string                 `yaml:"description" json:"description"`
	Category     string                 `yaml:"category" json:"category"`
	ConfigFields []ProviderConfigField  `yaml:"configFields" json:"configFields"`
	Capabilities []string               `yaml:"capabilities" json:"capabilities"`
	Metadata     map[string]interface{} `yaml:"metadata" json:"metadata"`
}

// ProviderConfigField defines a configuration field for a provider type
type ProviderConfigField struct {
	Name        string                 `yaml:"name" json:"name"`
	Type        string                 `yaml:"type" json:"type"`
	Label       string                 `yaml:"label" json:"label"`
	Description string                 `yaml:"description" json:"description"`
	Required    bool                   `yaml:"required" json:"required"`
	Sensitive   bool                   `yaml:"sensitive" json:"sensitive"`
	Default     interface{}            `yaml:"default" json:"default"`
	Validation  map[string]interface{} `yaml:"validation" json:"validation"`
	Options     []string               `yaml:"options" json:"options"`
}

// ConjurConfig holds CyberArk Conjur configuration
type ConjurConfig struct {
	Enabled   bool   `mapstructure:"enabled" yaml:"enabled" env:"CONJUR_ENABLED" default:"false"`
	URL       string `mapstructure:"url" yaml:"url" env:"CONJUR_URL"`
	Account   string `mapstructure:"account" yaml:"account" env:"CONJUR_ACCOUNT"`
	Username  string `mapstructure:"username" yaml:"username" env:"CONJUR_USERNAME"`
	APIKey    string `mapstructure:"api_key" yaml:"api_key" env:"CONJUR_API_KEY"`
	CertPath  string `mapstructure:"cert_path" yaml:"cert_path" env:"CONJUR_CERT_PATH"`
	VerifySSL bool   `mapstructure:"verify_ssl" yaml:"verify_ssl" env:"CONJUR_VERIFY_SSL" default:"true"`
	Timeout   string `mapstructure:"timeout" yaml:"timeout" env:"CONJUR_TIMEOUT" default:"30s"`
}

// VaultConfig holds HashiCorp Vault configuration
type VaultConfig struct {
	Enabled   bool      `mapstructure:"enabled" yaml:"enabled" env:"VAULT_ENABLED" default:"false"`
	Address   string    `mapstructure:"address" yaml:"address" env:"VAULT_ADDR"`
	Token     string    `mapstructure:"token" yaml:"token" env:"VAULT_TOKEN"`
	Namespace string    `mapstructure:"namespace" yaml:"namespace" env:"VAULT_NAMESPACE"`
	Mount     string    `mapstructure:"mount" yaml:"mount" env:"VAULT_MOUNT" default:"secret"`
	Version   string    `mapstructure:"version" yaml:"version" env:"VAULT_VERSION" default:"v2"`
	Timeout   string    `mapstructure:"timeout" yaml:"timeout" env:"VAULT_TIMEOUT" default:"30s"`
	TLSConfig TLSConfig `mapstructure:"tls" yaml:"tls"`
}

// AWSConfig holds AWS Secrets Manager configuration
type AWSConfig struct {
	Enabled   bool   `mapstructure:"enabled" yaml:"enabled" env:"AWS_SECRETS_ENABLED" default:"false"`
	Region    string `mapstructure:"region" yaml:"region" env:"AWS_REGION"`
	AccessKey string `mapstructure:"access_key" yaml:"access_key" env:"AWS_ACCESS_KEY_ID"`
	SecretKey string `mapstructure:"secret_key" yaml:"secret_key" env:"AWS_SECRET_ACCESS_KEY"`
	Profile   string `mapstructure:"profile" yaml:"profile" env:"AWS_PROFILE"`
	RoleARN   string `mapstructure:"role_arn" yaml:"role_arn" env:"AWS_ROLE_ARN"`
	Timeout   string `mapstructure:"timeout" yaml:"timeout" env:"AWS_TIMEOUT" default:"30s"`
}

// AzureConfig holds Azure Key Vault configuration
type AzureConfig struct {
	Enabled      bool   `mapstructure:"enabled" yaml:"enabled" env:"AZURE_KEYVAULT_ENABLED" default:"false"`
	VaultURL     string `mapstructure:"vault_url" yaml:"vault_url" env:"AZURE_KEYVAULT_URL"`
	TenantID     string `mapstructure:"tenant_id" yaml:"tenant_id" env:"AZURE_TENANT_ID"`
	ClientID     string `mapstructure:"client_id" yaml:"client_id" env:"AZURE_CLIENT_ID"`
	ClientSecret string `mapstructure:"client_secret" yaml:"client_secret" env:"AZURE_CLIENT_SECRET"`
	Timeout      string `mapstructure:"timeout" yaml:"timeout" env:"AZURE_TIMEOUT" default:"30s"`
}

// GCPConfig holds Google Secret Manager configuration
type GCPConfig struct {
	Enabled         bool   `mapstructure:"enabled" yaml:"enabled" env:"GCP_SECRETS_ENABLED" default:"false"`
	ProjectID       string `mapstructure:"project_id" yaml:"project_id" env:"GCP_PROJECT_ID"`
	CredentialsPath string `mapstructure:"credentials_path" yaml:"credentials_path" env:"GOOGLE_APPLICATION_CREDENTIALS"`
	CredentialsJSON string `mapstructure:"credentials_json" yaml:"credentials_json" env:"GCP_CREDENTIALS_JSON"`
	Timeout         string `mapstructure:"timeout" yaml:"timeout" env:"GCP_TIMEOUT" default:"30s"`
}

// TLSConfig holds TLS configuration for providers
type TLSConfig struct {
	CACert     string `mapstructure:"ca_cert" yaml:"ca_cert" env:"TLS_CA_CERT"`
	ClientCert string `mapstructure:"client_cert" yaml:"client_cert" env:"TLS_CLIENT_CERT"`
	ClientKey  string `mapstructure:"client_key" yaml:"client_key" env:"TLS_CLIENT_KEY"`
	SkipVerify bool   `mapstructure:"skip_verify" yaml:"skip_verify" env:"TLS_SKIP_VERIFY" default:"false"`
}

// RotationConfig holds secret rotation configuration
type RotationConfig struct {
	Enabled         bool   `mapstructure:"enabled" yaml:"enabled" env:"ROTATION_ENABLED" default:"true"`
	CheckInterval   string `mapstructure:"check_interval" yaml:"check_interval" env:"ROTATION_CHECK_INTERVAL" default:"1h"`
	DefaultInterval string `mapstructure:"default_interval" yaml:"default_interval" env:"ROTATION_DEFAULT_INTERVAL" default:"30d"`
	MaxRetries      int    `mapstructure:"max_retries" yaml:"max_retries" env:"ROTATION_MAX_RETRIES" default:"3"`
	RetryDelay      string `mapstructure:"retry_delay" yaml:"retry_delay" env:"ROTATION_RETRY_DELAY" default:"5m"`
	NotifyOnFailure bool   `mapstructure:"notify_on_failure" yaml:"notify_on_failure" env:"ROTATION_NOTIFY_ON_FAILURE" default:"true"`
	NotifyOnSuccess bool   `mapstructure:"notify_on_success" yaml:"notify_on_success" env:"ROTATION_NOTIFY_ON_SUCCESS" default:"false"`
	GracePeriod     string `mapstructure:"grace_period" yaml:"grace_period" env:"ROTATION_GRACE_PERIOD" default:"24h"`
}

// SecurityConfig holds security-related configuration
type SecurityConfig struct {
	AuditEnabled      bool   `mapstructure:"audit_enabled" yaml:"audit_enabled" env:"SECURITY_AUDIT_ENABLED" default:"true"`
	AccessLogEnabled  bool   `mapstructure:"access_log_enabled" yaml:"access_log_enabled" env:"SECURITY_ACCESS_LOG_ENABLED" default:"true"`
	RateLimitEnabled  bool   `mapstructure:"rate_limit_enabled" yaml:"rate_limit_enabled" env:"SECURITY_RATE_LIMIT_ENABLED" default:"true"`
	RateLimitRPS      int    `mapstructure:"rate_limit_rps" yaml:"rate_limit_rps" env:"SECURITY_RATE_LIMIT_RPS" default:"100"`
	MaxSecretSize     int    `mapstructure:"max_secret_size" yaml:"max_secret_size" env:"SECURITY_MAX_SECRET_SIZE" default:"65536"`
	SecretTTL         string `mapstructure:"secret_ttl" yaml:"secret_ttl" env:"SECURITY_SECRET_TTL" default:"0"`
	RequireApproval   bool   `mapstructure:"require_approval" yaml:"require_approval" env:"SECURITY_REQUIRE_APPROVAL" default:"false"`
	ApprovalThreshold int    `mapstructure:"approval_threshold" yaml:"approval_threshold" env:"SECURITY_APPROVAL_THRESHOLD" default:"1"`
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.Server.Port <= 0 || c.Server.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", c.Server.Port)
	}

	if c.Database.URL == "" {
		return fmt.Errorf("database URL is required")
	}

	if c.Encryption.MasterKey == "" {
		return fmt.Errorf("encryption master key is required")
	}

	// Validate rotation intervals
	if c.Rotation.Enabled {
		if _, err := time.ParseDuration(c.Rotation.CheckInterval); err != nil {
			return fmt.Errorf("invalid rotation check interval: %v", err)
		}
		if _, err := time.ParseDuration(c.Rotation.DefaultInterval); err != nil {
			return fmt.Errorf("invalid rotation default interval: %v", err)
		}
	}

	// Validate provider configurations
	if err := c.validateProviders(); err != nil {
		return fmt.Errorf("provider validation failed: %v", err)
	}

	return nil
}

// validateProviders validates provider configurations
func (c *Config) validateProviders() error {
	enabledProviders := 0

	if c.Providers.Conjur.Enabled {
		enabledProviders++
		if c.Providers.Conjur.URL == "" || c.Providers.Conjur.Account == "" {
			return fmt.Errorf("conjur URL and account are required when enabled")
		}
	}

	if c.Providers.Vault.Enabled {
		enabledProviders++
		if c.Providers.Vault.Address == "" {
			return fmt.Errorf("vault address is required when enabled")
		}
	}

	if c.Providers.AWS.Enabled {
		enabledProviders++
		if c.Providers.AWS.Region == "" {
			return fmt.Errorf("AWS region is required when enabled")
		}
	}

	if c.Providers.Azure.Enabled {
		enabledProviders++
		if c.Providers.Azure.VaultURL == "" || c.Providers.Azure.TenantID == "" {
			return fmt.Errorf("azure vault URL and tenant ID are required when enabled")
		}
	}

	if c.Providers.GCP.Enabled {
		enabledProviders++
		if c.Providers.GCP.ProjectID == "" {
			return fmt.Errorf("GCP project ID is required when enabled")
		}
	}

	// At least one provider should be available (internal is always available)
	if enabledProviders == 0 && c.Providers.DefaultProvider != "internal" {
		return fmt.Errorf("at least one external provider must be enabled or use 'internal' as default")
	}

	return nil
}

// initializeDefaultProviderTypes sets up default secret provider types
func (c *Config) initializeDefaultProviderTypes() {
	c.Providers.Types = []ProviderTypeConfig{
		{
			Type:        "vault",
			Name:        "HashiCorp Vault",
			Description: "HashiCorp Vault secret management",
			Category:    "enterprise",
			ConfigFields: []ProviderConfigField{
				{Name: "url", Type: "string", Label: "Vault URL", Required: true, Description: "Vault server URL"},
				{Name: "token", Type: "string", Label: "Vault Token", Required: true, Sensitive: true, Description: "Vault authentication token"},
				{Name: "namespace", Type: "string", Label: "Namespace", Required: false, Description: "Vault namespace (Enterprise)"},
				{Name: "mountPath", Type: "string", Label: "Mount Path", Required: true, Default: "secret", Description: "KV secrets engine mount path"},
			},
			Capabilities: []string{"get", "set", "delete", "list", "rotate"},
		},
		{
			Type:        "cyberark",
			Name:        "CyberArk",
			Description: "CyberArk Privileged Access Security",
			Category:    "enterprise",
			ConfigFields: []ProviderConfigField{
				{Name: "baseUrl", Type: "string", Label: "Base URL", Required: true, Description: "CyberArk PVWA base URL"},
				{Name: "username", Type: "string", Label: "Username", Required: true, Description: "CyberArk username"},
				{Name: "password", Type: "string", Label: "Password", Required: true, Sensitive: true, Description: "CyberArk password"},
				{Name: "appId", Type: "string", Label: "Application ID", Required: true, Description: "CyberArk application ID"},
				{Name: "safe", Type: "string", Label: "Safe Name", Required: true, Description: "CyberArk safe name"},
			},
			Capabilities: []string{"get", "set", "delete", "list"},
		},
		{
			Type:        "aws",
			Name:        "AWS Secrets Manager",
			Description: "Amazon Web Services Secrets Manager",
			Category:    "cloud",
			ConfigFields: []ProviderConfigField{
				{Name: "region", Type: "string", Label: "AWS Region", Required: true, Default: "us-east-1", Description: "AWS region"},
				{Name: "accessKeyId", Type: "string", Label: "Access Key ID", Required: false, Description: "AWS access key ID (optional if using IAM roles)"},
				{Name: "secretAccessKey", Type: "string", Label: "Secret Access Key", Required: false, Sensitive: true, Description: "AWS secret access key"},
				{Name: "sessionToken", Type: "string", Label: "Session Token", Required: false, Sensitive: true, Description: "AWS session token (for temporary credentials)"},
			},
			Capabilities: []string{"get", "set", "delete", "list", "rotate"},
		},
		{
			Type:        "azure",
			Name:        "Azure Key Vault",
			Description: "Microsoft Azure Key Vault",
			Category:    "cloud",
			ConfigFields: []ProviderConfigField{
				{Name: "vaultUrl", Type: "string", Label: "Key Vault URL", Required: true, Description: "Azure Key Vault URL"},
				{Name: "clientId", Type: "string", Label: "Client ID", Required: true, Description: "Azure AD application client ID"},
				{Name: "clientSecret", Type: "string", Label: "Client Secret", Required: true, Sensitive: true, Description: "Azure AD application client secret"},
				{Name: "tenantId", Type: "string", Label: "Tenant ID", Required: true, Description: "Azure AD tenant ID"},
			},
			Capabilities: []string{"get", "set", "delete", "list"},
		},
		{
			Type:        "gcp",
			Name:        "GCP Secret Manager",
			Description: "Google Cloud Platform Secret Manager",
			Category:    "cloud",
			ConfigFields: []ProviderConfigField{
				{Name: "projectId", Type: "string", Label: "Project ID", Required: true, Description: "GCP project ID"},
				{Name: "credentialsJson", Type: "text", Label: "Service Account JSON", Required: true, Sensitive: true, Description: "GCP service account credentials JSON"},
			},
			Capabilities: []string{"get", "set", "delete", "list"},
		},
		{
			Type:        "conjur",
			Name:        "CyberArk Conjur",
			Description: "CyberArk Conjur Secrets Manager",
			Category:    "enterprise",
			ConfigFields: []ProviderConfigField{
				{Name: "appliance_url", Type: "string", Label: "Appliance URL", Required: true, Description: "Conjur appliance URL"},
				{Name: "account", Type: "string", Label: "Account", Required: true, Description: "Conjur account"},
				{Name: "username", Type: "string", Label: "Username", Required: true, Description: "Conjur username"},
				{Name: "api_key", Type: "string", Label: "API Key", Required: true, Sensitive: true, Description: "Conjur API key"},
			},
			Capabilities: []string{"get", "set", "delete", "list"},
		},
		{
			Type:         "internal",
			Name:         "Internal",
			Description:  "Built-in encrypted storage",
			Category:     "internal",
			ConfigFields: []ProviderConfigField{},
			Capabilities: []string{"get", "set", "delete", "list"},
		},
	}
}

// LoadConfig loads configuration from file and environment variables
func LoadConfig() (*Config, error) {
	cfg := &Config{
		Service: config.ServiceConfig{
			Name:    "secrets-service",
			Version: "1.0.0",
		},
		Server: config.ServerConfig{
			Host:         "0.0.0.0",
			Port:         8087,
			ReadTimeout:  30,
			WriteTimeout: 30,
		},
		Database: config.DBConfig{
			URL:           "postgres://deploy:deploy@localhost:5432/secrets_service?sslmode=disable",
			MaxRetries:    5,
			RetryInterval: 3,
			MaxOpenConns:  25,
			MaxIdleConns:  10,
			QueryTimeout:  10,
			LogLevel:      "info",
		},
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "json",
		},
		Monitoring: config.MonitoringConfig{
			Enabled: true,
			Port:    9090,
			Path:    "/metrics",
		},
		Encryption: config.EncryptionConfig{
			MasterKey:        os.Getenv("ENCRYPTION_MASTER_KEY"),
			Algorithm:        "AES-256-GCM",
			KeySize:          32,
			SaltSize:         16,
			RotationInterval: "30d",
			MaxKeyAge:        "90d",
			TLSEnabled:       true,
			TLSMinVersion:    "1.2",
			HSMEnabled:       false,
		},
		Providers: ProvidersConfig{
			DefaultProvider: "internal",
		},
		Rotation: RotationConfig{
			Enabled:         true,
			CheckInterval:   "1h",
			DefaultInterval: "30d",
			MaxRetries:      3,
			RetryDelay:      "5m",
			NotifyOnFailure: true,
			NotifyOnSuccess: false,
			GracePeriod:     "24h",
		},
		Security: SecurityConfig{
			AuditEnabled:      true,
			AccessLogEnabled:  true,
			RateLimitEnabled:  true,
			RateLimitRPS:      100,
			MaxSecretSize:     65536,
			SecretTTL:         "0",
			RequireApproval:   false,
			ApprovalThreshold: 1,
		},
	}

	// Override with environment variables
	if dbURL := os.Getenv("DATABASE_URL"); dbURL != "" {
		cfg.Database.URL = dbURL
	}
	if jwtSecret := os.Getenv("JWT_SECRET"); jwtSecret != "" {
		cfg.Auth.JWTSecret = jwtSecret
	}
	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		cfg.Logging.Level = logLevel
	}
	if gatewayURL := os.Getenv("GATEWAY_URL"); gatewayURL != "" {
		cfg.Gateway.URL = gatewayURL
	}
	if adminServiceURL := os.Getenv("ADMIN_SERVICE_URL"); adminServiceURL != "" {
		cfg.Auth.AdminServiceURL = adminServiceURL
	}

	// Use shared config loader
	if err := config.LoadConfig(cfg, "secrets-service"); err != nil {
		return nil, fmt.Errorf("failed to load config: %v", err)
	}

	// Initialize default provider types if none configured
	if len(cfg.Providers.Types) == 0 {
		cfg.initializeDefaultProviderTypes()
	}

	return cfg, nil
}

// SaveDefaultConfig saves a default configuration file
func SaveDefaultConfig() error {
	cfg, err := LoadConfig()
	if err != nil {
		return err
	}

	return config.SaveDefaultConfig(cfg, "secrets-service")
}
