# Secrets Management Service Configuration

# Service information
service:
  name: "secrets-service"
  version: "1.0.0"

# Server settings
server:
  port: 8087
  host: "0.0.0.0"
  read_timeout: 30
  write_timeout: 30
  shutdown_timeout: 10
  trusted_proxies: ""
  tls_cert_file: ""
  tls_key_file: ""

# Database Configuration
database:
  url: "postgres://deploy:deploy@localhost:5432/secrets_service?sslmode=disable"
  max_retries: 5
  retry_interval: 3
  max_open_conns: 25
  max_idle_conns: 10
  query_timeout: 10
  log_level: "info"

# Auth Configuration
auth:
  jwt_secret: "default-secret-key-in-production"
  jwt_expiration_minutes: 60
  access_token_expiry: "1h"
  refresh_token_expiry: "168h"
  admin_service_url: "http://localhost:8086"
  api_key_header: "X-API-Key"
  disable: false

# Logging configuration
logging:
  level: "info"
  format: "json"
  output: "stdout"
  time_format: "2006-01-02T15:04:05Z07:00"

monitoring:
  enabled: true
  port: 9087
  path: "/metrics"

# Tracing Configuration
tracing_enabled: false
tracing_service_name: "secrets-service"
tracing_endpoint: "localhost:4317"

# Encryption Configuration
encryption:
  master_key: "change-me-in-production-use-external-kms"
  algorithm: "AES-256-GCM"
  key_size: 32
  salt_size: 16
  rotation_interval: "30d"
  max_key_age: "90d"
  tls_enabled: true
  tls_min_version: "1.2"
  hsm_enabled: false
  hsm_config: ""

# External Secret Providers Configuration
providers:
  default_provider: "internal"
  
  # CyberArk Conjur
  conjur:
    enabled: false
    url: ""
    account: ""
    username: ""
    api_key: ""
    cert_path: ""
    verify_ssl: true
    timeout: "30s"
  
  # HashiCorp Vault
  vault:
    enabled: false
    address: ""
    token: ""
    namespace: ""
    mount: "secret"
    version: "v2"
    timeout: "30s"
    tls:
      ca_cert: ""
      client_cert: ""
      client_key: ""
      skip_verify: false
  
  # AWS Secrets Manager
  aws:
    enabled: false
    region: ""
    access_key: ""
    secret_key: ""
    profile: ""
    role_arn: ""
    timeout: "30s"
  
  # Azure Key Vault
  azure:
    enabled: false
    vault_url: ""
    tenant_id: ""
    client_id: ""
    client_secret: ""
    timeout: "30s"
  
  # Google Secret Manager
  gcp:
    enabled: false
    project_id: ""
    credentials_path: ""
    credentials_json: ""
    timeout: "30s"

# Secret Rotation Configuration
rotation:
  enabled: true
  check_interval: "1h"
  default_interval: "30d"
  max_retries: 3
  retry_delay: "5m"
  notify_on_failure: true
  notify_on_success: false
  grace_period: "24h"

# Security Configuration
security:
  audit_enabled: true
  access_log_enabled: true
  rate_limit_enabled: true
  rate_limit_rps: 100
  max_secret_size: 65536  # 64KB
  secret_ttl: "0"  # No TTL by default
  require_approval: false
  approval_threshold: 1

# Example Environment-Specific Overrides (use environment variables in production)
# Environment variables take precedence over these values:
#
# export ENCRYPTION_MASTER_KEY="your-production-master-key"
# export CONJUR_ENABLED="true"
# export CONJUR_URL="https://conjur.company.com"
# export CONJUR_ACCOUNT="mycompany"
# export CONJUR_USERNAME="secrets-service"
# export CONJUR_API_KEY="your-conjur-api-key"
#
# export VAULT_ENABLED="true"
# export VAULT_ADDR="https://vault.company.com:8200"
# export VAULT_TOKEN="your-vault-token"
#
# export AWS_SECRETS_ENABLED="true"
# export AWS_REGION="us-west-2"
# export AWS_ACCESS_KEY_ID="your-access-key"
# export AWS_SECRET_ACCESS_KEY="your-secret-key"
#
# export AZURE_KEYVAULT_ENABLED="true"
# export AZURE_KEYVAULT_URL="https://your-vault.vault.azure.net/"
# export AZURE_TENANT_ID="your-tenant-id"
# export AZURE_CLIENT_ID="your-client-id"
# export AZURE_CLIENT_SECRET="your-client-secret"
#
# export GCP_SECRETS_ENABLED="true"
# export GCP_PROJECT_ID="your-project-id"
# export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"

gateway:
  url: "http://localhost:8000"
  token: ""
  enabled: true
  service_version: "1.0.0"
  environment: "development"
  region: "local"
  retry_attempts: 3
  health_check_path: "/health"
  tags: ["api", "secrets", "microservice"]