package providers

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/claudio/deploy-orchestrator/secrets-service/config"
	"github.com/claudio/deploy-orchestrator/secrets-service/models"
)

// Provider interface defines the contract for secret providers
type Provider interface {
	// GetSecret retrieves a secret by name
	GetSecret(ctx context.Context, name string) (string, error)

	// SetSecret stores a secret
	SetSecret(ctx context.Context, name, value string) error

	// DeleteSecret removes a secret
	DeleteSecret(ctx context.Context, name string) error

	// ListSecrets returns a list of secret names
	ListSecrets(ctx context.Context) ([]string, error)

	// TestConnection tests the provider connection
	TestConnection(ctx context.Context) error

	// GetProviderInfo returns provider information
	GetProviderInfo() ProviderInfo

	// RotateSecret rotates a secret (if supported)
	RotateSecret(ctx context.Context, name string) (string, error)
}

// ProviderInfo contains information about a provider
type ProviderInfo struct {
	Type         models.ProviderType `json:"type"`
	Name         string              `json:"name"`
	Version      string              `json:"version"`
	Capabilities []string            `json:"capabilities"`
	Status       string              `json:"status"`
	LastTested   *time.Time          `json:"lastTested"`
}

// Manager manages multiple secret providers
type Manager struct {
	providers       map[string]Provider
	config          config.ProvidersConfig
	logger          *zap.Logger
	defaultProvider Provider
}

// NewManager creates a new provider manager
func NewManager(cfg config.ProvidersConfig, logger *zap.Logger) (*Manager, error) {
	manager := &Manager{
		providers: make(map[string]Provider),
		config:    cfg,
		logger:    logger,
	}

	// Initialize internal provider (always available)
	internalProvider := NewInternalProvider(logger)
	manager.providers["internal"] = internalProvider

	// Initialize external providers based on configuration
	if cfg.Conjur.Enabled {
		conjurProvider, err := NewConjurProvider(cfg.Conjur, logger)
		if err != nil {
			logger.Error("Failed to initialize Conjur provider", zap.Error(err))
		} else {
			manager.providers["conjur"] = conjurProvider
			logger.Info("Conjur provider initialized")
		}
	}

	if cfg.Vault.Enabled {
		vaultProvider, err := NewVaultProvider(cfg.Vault, logger)
		if err != nil {
			logger.Error("Failed to initialize Vault provider", zap.Error(err))
		} else {
			manager.providers["vault"] = vaultProvider
			logger.Info("Vault provider initialized")
		}
	}

	if cfg.AWS.Enabled {
		awsProvider, err := NewAWSProvider(cfg.AWS, logger)
		if err != nil {
			logger.Error("Failed to initialize AWS provider", zap.Error(err))
		} else {
			manager.providers["aws"] = awsProvider
			logger.Info("AWS Secrets Manager provider initialized")
		}
	}

	if cfg.Azure.Enabled {
		azureProvider, err := NewAzureProvider(cfg.Azure, logger)
		if err != nil {
			logger.Error("Failed to initialize Azure provider", zap.Error(err))
		} else {
			manager.providers["azure"] = azureProvider
			logger.Info("Azure Key Vault provider initialized")
		}
	}

	if cfg.GCP.Enabled {
		gcpProvider, err := NewGCPProvider(cfg.GCP, logger)
		if err != nil {
			logger.Error("Failed to initialize GCP provider", zap.Error(err))
		} else {
			manager.providers["gcp"] = gcpProvider
			logger.Info("GCP Secret Manager provider initialized")
		}
	}

	// Set default provider
	if defaultProvider, exists := manager.providers[cfg.DefaultProvider]; exists {
		manager.defaultProvider = defaultProvider
		logger.Info("Default provider set", zap.String("provider", cfg.DefaultProvider))
	} else {
		manager.defaultProvider = internalProvider
		logger.Warn("Default provider not found, using internal",
			zap.String("requested", cfg.DefaultProvider))
	}

	return manager, nil
}

// GetProvider returns a provider by name
func (m *Manager) GetProvider(name string) (Provider, error) {
	provider, exists := m.providers[name]
	if !exists {
		return nil, fmt.Errorf("provider '%s' not found", name)
	}
	return provider, nil
}

// GetDefaultProvider returns the default provider
func (m *Manager) GetDefaultProvider() Provider {
	return m.defaultProvider
}

// ListProviders returns a list of available providers
func (m *Manager) ListProviders() []ProviderInfo {
	var providers []ProviderInfo
	for _, provider := range m.providers {
		providers = append(providers, provider.GetProviderInfo())
	}
	return providers
}

// TestProvider tests a specific provider
func (m *Manager) TestProvider(ctx context.Context, name string) error {
	provider, err := m.GetProvider(name)
	if err != nil {
		return err
	}
	return provider.TestConnection(ctx)
}

// TestAllProviders tests all configured providers
func (m *Manager) TestAllProviders(ctx context.Context) map[string]error {
	results := make(map[string]error)
	for name, provider := range m.providers {
		results[name] = provider.TestConnection(ctx)
	}
	return results
}

// GetSecret retrieves a secret from the specified provider
func (m *Manager) GetSecret(ctx context.Context, providerName, secretName string) (string, error) {
	provider, err := m.GetProvider(providerName)
	if err != nil {
		return "", err
	}
	return provider.GetSecret(ctx, secretName)
}

// SetSecret stores a secret in the specified provider
func (m *Manager) SetSecret(ctx context.Context, providerName, secretName, value string) error {
	provider, err := m.GetProvider(providerName)
	if err != nil {
		return err
	}
	return provider.SetSecret(ctx, secretName, value)
}

// DeleteSecret removes a secret from the specified provider
func (m *Manager) DeleteSecret(ctx context.Context, providerName, secretName string) error {
	provider, err := m.GetProvider(providerName)
	if err != nil {
		return err
	}
	return provider.DeleteSecret(ctx, secretName)
}

// RotateSecret rotates a secret in the specified provider
func (m *Manager) RotateSecret(ctx context.Context, providerName, secretName string) (string, error) {
	provider, err := m.GetProvider(providerName)
	if err != nil {
		return "", err
	}
	return provider.RotateSecret(ctx, secretName)
}

// SyncSecret synchronizes a secret across multiple providers
func (m *Manager) SyncSecret(ctx context.Context, secretName, value string, providers []string) error {
	var errors []error

	for _, providerName := range providers {
		if err := m.SetSecret(ctx, providerName, secretName, value); err != nil {
			errors = append(errors, fmt.Errorf("failed to sync to %s: %w", providerName, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("sync failed for some providers: %v", errors)
	}

	return nil
}

// GetProviderHealth returns health status for all providers
func (m *Manager) GetProviderHealth(ctx context.Context) map[string]ProviderHealth {
	health := make(map[string]ProviderHealth)

	for name, provider := range m.providers {
		start := time.Now()
		err := provider.TestConnection(ctx)
		duration := time.Since(start)

		status := "healthy"
		if err != nil {
			status = "unhealthy"
		}

		health[name] = ProviderHealth{
			Name:         name,
			Status:       status,
			Error:        err,
			ResponseTime: duration,
			LastChecked:  time.Now(),
		}
	}

	return health
}

// ProviderHealth represents the health status of a provider
type ProviderHealth struct {
	Name         string        `json:"name"`
	Status       string        `json:"status"`
	Error        error         `json:"error,omitempty"`
	ResponseTime time.Duration `json:"responseTime"`
	LastChecked  time.Time     `json:"lastChecked"`
}

// SecretRequest represents a request for secret operations
type SecretRequest struct {
	Provider string            `json:"provider"`
	Name     string            `json:"name"`
	Value    string            `json:"value,omitempty"`
	Metadata map[string]string `json:"metadata,omitempty"`
}

// SecretResponse represents a response from secret operations
type SecretResponse struct {
	Name      string            `json:"name"`
	Value     string            `json:"value,omitempty"`
	Provider  string            `json:"provider"`
	Version   string            `json:"version,omitempty"`
	Metadata  map[string]string `json:"metadata,omitempty"`
	CreatedAt time.Time         `json:"createdAt"`
	UpdatedAt time.Time         `json:"updatedAt"`
}

// BulkSecretRequest represents a bulk operation request
type BulkSecretRequest struct {
	Provider string          `json:"provider"`
	Secrets  []SecretRequest `json:"secrets"`
}

// BulkSecretResponse represents a bulk operation response
type BulkSecretResponse struct {
	Successful []SecretResponse `json:"successful"`
	Failed     []SecretError    `json:"failed"`
}

// SecretError represents an error in secret operations
type SecretError struct {
	Name     string `json:"name"`
	Provider string `json:"provider"`
	Error    string `json:"error"`
}

// ProcessBulkRequest processes a bulk secret request
func (m *Manager) ProcessBulkRequest(ctx context.Context, request BulkSecretRequest) BulkSecretResponse {
	response := BulkSecretResponse{
		Successful: make([]SecretResponse, 0),
		Failed:     make([]SecretError, 0),
	}

	provider, err := m.GetProvider(request.Provider)
	if err != nil {
		// All secrets fail if provider is not found
		for _, secret := range request.Secrets {
			response.Failed = append(response.Failed, SecretError{
				Name:     secret.Name,
				Provider: request.Provider,
				Error:    err.Error(),
			})
		}
		return response
	}

	for _, secret := range request.Secrets {
		if secret.Value != "" {
			// Set operation
			if err := provider.SetSecret(ctx, secret.Name, secret.Value); err != nil {
				response.Failed = append(response.Failed, SecretError{
					Name:     secret.Name,
					Provider: request.Provider,
					Error:    err.Error(),
				})
			} else {
				response.Successful = append(response.Successful, SecretResponse{
					Name:      secret.Name,
					Provider:  request.Provider,
					Metadata:  secret.Metadata,
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				})
			}
		} else {
			// Get operation
			value, err := provider.GetSecret(ctx, secret.Name)
			if err != nil {
				response.Failed = append(response.Failed, SecretError{
					Name:     secret.Name,
					Provider: request.Provider,
					Error:    err.Error(),
				})
			} else {
				response.Successful = append(response.Successful, SecretResponse{
					Name:      secret.Name,
					Value:     value,
					Provider:  request.Provider,
					Metadata:  secret.Metadata,
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				})
			}
		}
	}

	return response
}
