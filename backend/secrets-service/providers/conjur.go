package providers

import (
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/claudio/deploy-orchestrator/secrets-service/config"
	"github.com/claudio/deploy-orchestrator/secrets-service/models"
)

// ConjurProvider implements the Provider interface for CyberArk Conjur
type ConjurProvider struct {
	config     config.ConjurConfig
	logger     *zap.Logger
	httpClient *http.Client
	authToken  string
	tokenExp   time.Time
}

// ConjurAuthResponse represents the authentication response from Conjur
type ConjurAuthResponse struct {
	Token string `json:"token"`
}

// ConjurSecretResponse represents a secret response from Conjur
type ConjurSecretResponse struct {
	Value string `json:"value"`
}

// NewConjurProvider creates a new CyberArk Conjur provider
func NewConjurProvider(cfg config.ConjurConfig, logger *zap.Logger) (*ConjurProvider, error) {
	if cfg.URL == "" {
		return nil, fmt.Errorf("conjur URL is required")
	}
	if cfg.Account == "" {
		return nil, fmt.Errorf("conjur account is required")
	}
	if cfg.Username == "" {
		return nil, fmt.Errorf("conjur username is required")
	}
	if cfg.APIKey == "" {
		return nil, fmt.Errorf("conjur API key is required")
	}

	// Parse timeout
	timeout, err := time.ParseDuration(cfg.Timeout)
	if err != nil {
		timeout = 30 * time.Second
	}

	// Create HTTP client with custom TLS configuration
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: !cfg.VerifySSL,
		},
	}

	// Load custom certificate if provided
	if cfg.CertPath != "" {
		// In a real implementation, you would load the certificate from the file
		logger.Info("Custom certificate path provided", zap.String("path", cfg.CertPath))
	}

	httpClient := &http.Client{
		Transport: transport,
		Timeout:   timeout,
	}

	provider := &ConjurProvider{
		config:     cfg,
		logger:     logger,
		httpClient: httpClient,
	}

	return provider, nil
}

// authenticate authenticates with Conjur and obtains an access token
func (p *ConjurProvider) authenticate(ctx context.Context) error {
	// Check if we have a valid token
	if p.authToken != "" && time.Now().Before(p.tokenExp) {
		return nil
	}

	authURL := fmt.Sprintf("%s/authn/%s/%s/authenticate",
		strings.TrimSuffix(p.config.URL, "/"),
		p.config.Account,
		url.QueryEscape(p.config.Username))

	req, err := http.NewRequestWithContext(ctx, "POST", authURL, strings.NewReader(p.config.APIKey))
	if err != nil {
		return fmt.Errorf("failed to create auth request: %w", err)
	}

	req.Header.Set("Content-Type", "text/plain")

	resp, err := p.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("authentication request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("authentication failed with status %d: %s", resp.StatusCode, string(body))
	}

	tokenBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read auth response: %w", err)
	}

	p.authToken = string(tokenBytes)
	// Conjur tokens typically expire after 8 minutes, we'll refresh after 7 minutes
	p.tokenExp = time.Now().Add(7 * time.Minute)

	p.logger.Debug("Successfully authenticated with Conjur")

	return nil
}

// GetSecret retrieves a secret by name
func (p *ConjurProvider) GetSecret(ctx context.Context, name string) (string, error) {
	if err := p.authenticate(ctx); err != nil {
		return "", fmt.Errorf("authentication failed: %w", err)
	}

	secretURL := fmt.Sprintf("%s/secrets/%s/variable/%s",
		strings.TrimSuffix(p.config.URL, "/"),
		p.config.Account,
		url.QueryEscape(name))

	req, err := http.NewRequestWithContext(ctx, "GET", secretURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Token token=\"%s\"", p.authToken))

	resp, err := p.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return "", fmt.Errorf("secret '%s' not found", name)
	}

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	value, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	p.logger.Debug("Retrieved secret from Conjur", zap.String("name", name))

	return string(value), nil
}

// SetSecret stores a secret
func (p *ConjurProvider) SetSecret(ctx context.Context, name, value string) error {
	if err := p.authenticate(ctx); err != nil {
		return fmt.Errorf("authentication failed: %w", err)
	}

	secretURL := fmt.Sprintf("%s/secrets/%s/variable/%s",
		strings.TrimSuffix(p.config.URL, "/"),
		p.config.Account,
		url.QueryEscape(name))

	req, err := http.NewRequestWithContext(ctx, "POST", secretURL, strings.NewReader(value))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Token token=\"%s\"", p.authToken))
	req.Header.Set("Content-Type", "application/octet-stream")

	resp, err := p.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	p.logger.Info("Stored secret in Conjur", zap.String("name", name))

	return nil
}

// DeleteSecret removes a secret (Conjur doesn't support deletion, so we'll set it to empty)
func (p *ConjurProvider) DeleteSecret(ctx context.Context, name string) error {
	// Conjur doesn't support secret deletion, so we'll set it to an empty value
	// and add a metadata indicator that it's been "deleted"
	return p.SetSecret(ctx, name, "")
}

// ListSecrets returns a list of secret names (Conjur doesn't provide a direct list API)
func (p *ConjurProvider) ListSecrets(ctx context.Context) ([]string, error) {
	// Conjur doesn't provide a direct API to list all secrets
	// This would require querying the policy or using the search API
	// For now, we'll return an empty list and log a warning
	p.logger.Warn("ListSecrets is not fully supported by Conjur provider")
	return []string{}, nil
}

// TestConnection tests the provider connection
func (p *ConjurProvider) TestConnection(ctx context.Context) error {
	return p.authenticate(ctx)
}

// GetProviderInfo returns provider information
func (p *ConjurProvider) GetProviderInfo() ProviderInfo {
	status := "unknown"
	var lastTested *time.Time

	// Try a quick connection test
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := p.TestConnection(ctx); err == nil {
		status = "healthy"
	} else {
		status = "unhealthy"
	}

	now := time.Now()
	lastTested = &now

	return ProviderInfo{
		Type:    models.ProviderTypeConjur,
		Name:    "CyberArk Conjur",
		Version: "1.0.0",
		Capabilities: []string{
			"get", "set", "test",
		},
		Status:     status,
		LastTested: lastTested,
	}
}

// RotateSecret rotates a secret (generates a new value and stores it)
func (p *ConjurProvider) RotateSecret(ctx context.Context, name string) (string, error) {
	// Generate a new random value
	newValue, err := generateRandomValue(32)
	if err != nil {
		return "", fmt.Errorf("failed to generate new value: %w", err)
	}

	// Store the new value
	if err := p.SetSecret(ctx, name, newValue); err != nil {
		return "", fmt.Errorf("failed to store rotated secret: %w", err)
	}

	p.logger.Info("Rotated secret in Conjur", zap.String("name", name))

	return newValue, nil
}

// GetSecretVersion retrieves a specific version of a secret
func (p *ConjurProvider) GetSecretVersion(ctx context.Context, name string, version int) (string, error) {
	if err := p.authenticate(ctx); err != nil {
		return "", fmt.Errorf("authentication failed: %w", err)
	}

	secretURL := fmt.Sprintf("%s/secrets/%s/variable/%s?version=%d",
		strings.TrimSuffix(p.config.URL, "/"),
		p.config.Account,
		url.QueryEscape(name),
		version)

	req, err := http.NewRequestWithContext(ctx, "GET", secretURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Token token=\"%s\"", p.authToken))

	resp, err := p.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return "", fmt.Errorf("secret '%s' version %d not found", name, version)
	}

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	value, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	return string(value), nil
}

// GetSecretMetadata retrieves metadata for a secret
func (p *ConjurProvider) GetSecretMetadata(ctx context.Context, name string) (map[string]interface{}, error) {
	// Conjur doesn't provide direct metadata API
	// This would require additional API calls to get policy information
	return map[string]interface{}{
		"provider": "conjur",
		"name":     name,
	}, nil
}

// generateRandomValue generates a random value for secret rotation
func generateRandomValue(length int) (string, error) {
	// This is a simplified implementation
	// In production, you might want more sophisticated password generation
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"

	bytes := make([]byte, length)
	for i := range bytes {
		bytes[i] = charset[i%len(charset)]
	}

	return string(bytes), nil
}
