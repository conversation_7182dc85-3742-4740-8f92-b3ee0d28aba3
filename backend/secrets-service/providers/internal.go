package providers

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/claudio/deploy-orchestrator/secrets-service/models"
)

// InternalProvider implements the Provider interface using internal storage
type InternalProvider struct {
	secrets map[string]InternalSecret
	mutex   sync.RWMutex
	logger  *zap.Logger
}

// InternalSecret represents a secret stored internally
type InternalSecret struct {
	Name      string            `json:"name"`
	Value     string            `json:"value"`
	Metadata  map[string]string `json:"metadata"`
	CreatedAt time.Time         `json:"createdAt"`
	UpdatedAt time.Time         `json:"updatedAt"`
	Version   int               `json:"version"`
}

// NewInternalProvider creates a new internal provider
func NewInternalProvider(logger *zap.Logger) *InternalProvider {
	return &InternalProvider{
		secrets: make(map[string]InternalSecret),
		logger:  logger,
	}
}

// GetSecret retrieves a secret by name
func (p *InternalProvider) GetSecret(ctx context.Context, name string) (string, error) {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	secret, exists := p.secrets[name]
	if !exists {
		return "", fmt.Errorf("secret '%s' not found", name)
	}

	p.logger.Debug("Retrieved secret from internal provider",
		zap.String("name", name),
		zap.Time("lastUpdated", secret.UpdatedAt))

	return secret.Value, nil
}

// SetSecret stores a secret
func (p *InternalProvider) SetSecret(ctx context.Context, name, value string) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	now := time.Now()

	// Check if secret already exists
	existing, exists := p.secrets[name]
	version := 1
	createdAt := now

	if exists {
		version = existing.Version + 1
		createdAt = existing.CreatedAt
	}

	secret := InternalSecret{
		Name:      name,
		Value:     value,
		Metadata:  make(map[string]string),
		CreatedAt: createdAt,
		UpdatedAt: now,
		Version:   version,
	}

	p.secrets[name] = secret

	p.logger.Info("Stored secret in internal provider",
		zap.String("name", name),
		zap.Int("version", version),
		zap.Bool("isUpdate", exists))

	return nil
}

// DeleteSecret removes a secret
func (p *InternalProvider) DeleteSecret(ctx context.Context, name string) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if _, exists := p.secrets[name]; !exists {
		return fmt.Errorf("secret '%s' not found", name)
	}

	delete(p.secrets, name)

	p.logger.Info("Deleted secret from internal provider", zap.String("name", name))

	return nil
}

// ListSecrets returns a list of secret names
func (p *InternalProvider) ListSecrets(ctx context.Context) ([]string, error) {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	names := make([]string, 0, len(p.secrets))
	for name := range p.secrets {
		names = append(names, name)
	}

	return names, nil
}

// TestConnection tests the provider connection
func (p *InternalProvider) TestConnection(ctx context.Context) error {
	// Internal provider is always available
	return nil
}

// GetProviderInfo returns provider information
func (p *InternalProvider) GetProviderInfo() ProviderInfo {
	return ProviderInfo{
		Type:    models.ProviderTypeInternal,
		Name:    "Internal Provider",
		Version: "1.0.0",
		Capabilities: []string{
			"get", "set", "delete", "list", "rotate",
		},
		Status:     "healthy",
		LastTested: timePtr(time.Now()),
	}
}

// RotateSecret rotates a secret (generates a new random value)
func (p *InternalProvider) RotateSecret(ctx context.Context, name string) (string, error) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// Check if secret exists
	existing, exists := p.secrets[name]
	if !exists {
		return "", fmt.Errorf("secret '%s' not found", name)
	}

	// Generate new random value
	newValue, err := p.generateRandomSecret(32) // 32 bytes = 256 bits
	if err != nil {
		return "", fmt.Errorf("failed to generate new secret value: %w", err)
	}

	// Update the secret
	now := time.Now()
	secret := InternalSecret{
		Name:      name,
		Value:     newValue,
		Metadata:  existing.Metadata,
		CreatedAt: existing.CreatedAt,
		UpdatedAt: now,
		Version:   existing.Version + 1,
	}

	p.secrets[name] = secret

	p.logger.Info("Rotated secret in internal provider",
		zap.String("name", name),
		zap.Int("newVersion", secret.Version))

	return newValue, nil
}

// generateRandomSecret generates a random secret value
func (p *InternalProvider) generateRandomSecret(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes), nil
}

// GetSecretWithMetadata retrieves a secret with its metadata
func (p *InternalProvider) GetSecretWithMetadata(ctx context.Context, name string) (*InternalSecret, error) {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	secret, exists := p.secrets[name]
	if !exists {
		return nil, fmt.Errorf("secret '%s' not found", name)
	}

	// Return a copy to prevent external modification
	secretCopy := secret
	return &secretCopy, nil
}

// SetSecretWithMetadata stores a secret with metadata
func (p *InternalProvider) SetSecretWithMetadata(ctx context.Context, name, value string, metadata map[string]string) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	now := time.Now()

	// Check if secret already exists
	existing, exists := p.secrets[name]
	version := 1
	createdAt := now

	if exists {
		version = existing.Version + 1
		createdAt = existing.CreatedAt
	}

	// Copy metadata to prevent external modification
	metadataCopy := make(map[string]string)
	for k, v := range metadata {
		metadataCopy[k] = v
	}

	secret := InternalSecret{
		Name:      name,
		Value:     value,
		Metadata:  metadataCopy,
		CreatedAt: createdAt,
		UpdatedAt: now,
		Version:   version,
	}

	p.secrets[name] = secret

	p.logger.Info("Stored secret with metadata in internal provider",
		zap.String("name", name),
		zap.Int("version", version),
		zap.Int("metadataCount", len(metadata)),
		zap.Bool("isUpdate", exists))

	return nil
}

// GetSecretHistory returns the history of a secret (for internal provider, just current version)
func (p *InternalProvider) GetSecretHistory(ctx context.Context, name string) ([]InternalSecret, error) {
	secret, err := p.GetSecretWithMetadata(ctx, name)
	if err != nil {
		return nil, err
	}

	return []InternalSecret{*secret}, nil
}

// GetStats returns statistics about the internal provider
func (p *InternalProvider) GetStats() InternalProviderStats {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	stats := InternalProviderStats{
		TotalSecrets: len(p.secrets),
		SecretsByAge: make(map[string]int),
	}

	now := time.Now()
	for _, secret := range p.secrets {
		age := now.Sub(secret.CreatedAt)

		switch {
		case age < 24*time.Hour:
			stats.SecretsByAge["< 1 day"]++
		case age < 7*24*time.Hour:
			stats.SecretsByAge["< 1 week"]++
		case age < 30*24*time.Hour:
			stats.SecretsByAge["< 1 month"]++
		case age < 365*24*time.Hour:
			stats.SecretsByAge["< 1 year"]++
		default:
			stats.SecretsByAge[">= 1 year"]++
		}
	}

	return stats
}

// InternalProviderStats represents statistics for the internal provider
type InternalProviderStats struct {
	TotalSecrets int            `json:"totalSecrets"`
	SecretsByAge map[string]int `json:"secretsByAge"`
}

// Clear removes all secrets (useful for testing)
func (p *InternalProvider) Clear() {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	p.secrets = make(map[string]InternalSecret)
	p.logger.Info("Cleared all secrets from internal provider")
}

// Import imports secrets from a map
func (p *InternalProvider) Import(secrets map[string]string) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	now := time.Now()
	imported := 0

	for name, value := range secrets {
		secret := InternalSecret{
			Name:      name,
			Value:     value,
			Metadata:  make(map[string]string),
			CreatedAt: now,
			UpdatedAt: now,
			Version:   1,
		}

		p.secrets[name] = secret
		imported++
	}

	p.logger.Info("Imported secrets to internal provider",
		zap.Int("count", imported))

	return nil
}

// Export exports all secrets to a map (values are not included for security)
func (p *InternalProvider) Export() map[string]InternalSecret {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	exported := make(map[string]InternalSecret)
	for name, secret := range p.secrets {
		// Create a copy without the actual value for security
		exported[name] = InternalSecret{
			Name:      secret.Name,
			Value:     "[REDACTED]",
			Metadata:  secret.Metadata,
			CreatedAt: secret.CreatedAt,
			UpdatedAt: secret.UpdatedAt,
			Version:   secret.Version,
		}
	}

	return exported
}

// timePtr returns a pointer to the given time
func timePtr(t time.Time) *time.Time {
	return &t
}
