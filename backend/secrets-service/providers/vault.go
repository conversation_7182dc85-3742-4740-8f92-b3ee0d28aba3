package providers

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/claudio/deploy-orchestrator/secrets-service/config"
	"github.com/claudio/deploy-orchestrator/secrets-service/models"
)

// VaultProvider implements the Provider interface for HashiCorp Vault
type VaultProvider struct {
	config     config.VaultConfig
	logger     *zap.Logger
	httpClient *http.Client
}

// VaultSecretResponse represents a secret response from Vault
type VaultSecretResponse struct {
	Data struct {
		Data     map[string]interface{} `json:"data"`
		Metadata struct {
			Version     int       `json:"version"`
			CreatedTime time.Time `json:"created_time"`
			Destroyed   bool      `json:"destroyed"`
		} `json:"metadata"`
	} `json:"data"`
}

// VaultListResponse represents a list response from Vault
type VaultListResponse struct {
	Data struct {
		Keys []string `json:"keys"`
	} `json:"data"`
}

// VaultWriteRequest represents a write request to Vault
type VaultWriteRequest struct {
	Data map[string]interface{} `json:"data"`
}

// NewVaultProvider creates a new HashiCorp Vault provider
func NewVaultProvider(cfg config.VaultConfig, logger *zap.Logger) (*VaultProvider, error) {
	if cfg.Address == "" {
		return nil, fmt.Errorf("vault address is required")
	}
	if cfg.Token == "" {
		return nil, fmt.Errorf("vault token is required")
	}

	// Parse timeout
	timeout, err := time.ParseDuration(cfg.Timeout)
	if err != nil {
		timeout = 30 * time.Second
	}

	// Create TLS configuration
	tlsConfig := &tls.Config{
		InsecureSkipVerify: cfg.TLSConfig.SkipVerify,
	}

	// Load custom certificates if provided
	if cfg.TLSConfig.CACert != "" || cfg.TLSConfig.ClientCert != "" {
		// In a real implementation, you would load the certificates from files
		logger.Info("Custom TLS configuration provided")
	}

	// Create HTTP client
	transport := &http.Transport{
		TLSClientConfig: tlsConfig,
	}

	httpClient := &http.Client{
		Transport: transport,
		Timeout:   timeout,
	}

	provider := &VaultProvider{
		config:     cfg,
		logger:     logger,
		httpClient: httpClient,
	}

	return provider, nil
}

// buildSecretPath builds the full path for a secret based on Vault version
func (p *VaultProvider) buildSecretPath(name string) string {
	mount := p.config.Mount
	if mount == "" {
		mount = "secret"
	}

	if p.config.Version == "v2" {
		return fmt.Sprintf("%s/data/%s", mount, name)
	}
	return fmt.Sprintf("%s/%s", mount, name)
}

// buildMetadataPath builds the metadata path for KV v2
func (p *VaultProvider) buildMetadataPath(name string) string {
	mount := p.config.Mount
	if mount == "" {
		mount = "secret"
	}
	return fmt.Sprintf("%s/metadata/%s", mount, name)
}

// makeRequest makes an HTTP request to Vault
func (p *VaultProvider) makeRequest(ctx context.Context, method, path string, body interface{}) (*http.Response, error) {
	var reqBody io.Reader
	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		reqBody = bytes.NewReader(jsonData)
	}

	url := fmt.Sprintf("%s/v1/%s", strings.TrimSuffix(p.config.Address, "/"), path)
	req, err := http.NewRequestWithContext(ctx, method, url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("X-Vault-Token", p.config.Token)
	if p.config.Namespace != "" {
		req.Header.Set("X-Vault-Namespace", p.config.Namespace)
	}
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	resp, err := p.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}

	return resp, nil
}

// GetSecret retrieves a secret by name
func (p *VaultProvider) GetSecret(ctx context.Context, name string) (string, error) {
	path := p.buildSecretPath(name)
	resp, err := p.makeRequest(ctx, "GET", path, nil)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return "", fmt.Errorf("secret '%s' not found", name)
	}

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var secretResp VaultSecretResponse
	if err := json.NewDecoder(resp.Body).Decode(&secretResp); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	// Extract the secret value
	var data map[string]interface{}
	if p.config.Version == "v2" {
		data = secretResp.Data.Data
	} else {
		// For KV v1, the data is directly in the response
		if err := json.NewDecoder(resp.Body).Decode(&data); err != nil {
			return "", fmt.Errorf("failed to decode v1 response: %w", err)
		}
	}

	// Look for common secret keys
	for _, key := range []string{"value", "secret", "password", "data"} {
		if value, exists := data[key]; exists {
			if strValue, ok := value.(string); ok {
				p.logger.Debug("Retrieved secret from Vault",
					zap.String("name", name),
					zap.String("key", key))
				return strValue, nil
			}
		}
	}

	// If no standard key found, return the first string value
	for key, value := range data {
		if strValue, ok := value.(string); ok {
			p.logger.Debug("Retrieved secret from Vault",
				zap.String("name", name),
				zap.String("key", key))
			return strValue, nil
		}
	}

	return "", fmt.Errorf("no string value found in secret '%s'", name)
}

// SetSecret stores a secret
func (p *VaultProvider) SetSecret(ctx context.Context, name, value string) error {
	path := p.buildSecretPath(name)

	var requestBody interface{}
	if p.config.Version == "v2" {
		requestBody = VaultWriteRequest{
			Data: map[string]interface{}{
				"value": value,
			},
		}
	} else {
		requestBody = map[string]interface{}{
			"value": value,
		}
	}

	resp, err := p.makeRequest(ctx, "POST", path, requestBody)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusNoContent {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	p.logger.Info("Stored secret in Vault", zap.String("name", name))

	return nil
}

// DeleteSecret removes a secret
func (p *VaultProvider) DeleteSecret(ctx context.Context, name string) error {
	if p.config.Version == "v2" {
		// For KV v2, we need to delete the metadata
		path := p.buildMetadataPath(name)
		resp, err := p.makeRequest(ctx, "DELETE", path, nil)
		if err != nil {
			return err
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusNoContent && resp.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(resp.Body)
			return fmt.Errorf("delete failed with status %d: %s", resp.StatusCode, string(body))
		}
	} else {
		// For KV v1, delete the secret directly
		path := p.buildSecretPath(name)
		resp, err := p.makeRequest(ctx, "DELETE", path, nil)
		if err != nil {
			return err
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusNoContent && resp.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(resp.Body)
			return fmt.Errorf("delete failed with status %d: %s", resp.StatusCode, string(body))
		}
	}

	p.logger.Info("Deleted secret from Vault", zap.String("name", name))

	return nil
}

// ListSecrets returns a list of secret names
func (p *VaultProvider) ListSecrets(ctx context.Context) ([]string, error) {
	mount := p.config.Mount
	if mount == "" {
		mount = "secret"
	}

	var path string
	if p.config.Version == "v2" {
		path = fmt.Sprintf("%s/metadata", mount)
	} else {
		path = mount
	}

	// Add LIST parameter
	resp, err := p.makeRequest(ctx, "LIST", path, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return []string{}, nil // No secrets found
	}

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("list request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var listResp VaultListResponse
	if err := json.NewDecoder(resp.Body).Decode(&listResp); err != nil {
		return nil, fmt.Errorf("failed to decode list response: %w", err)
	}

	return listResp.Data.Keys, nil
}

// TestConnection tests the provider connection
func (p *VaultProvider) TestConnection(ctx context.Context) error {
	// Test by checking Vault's health endpoint
	resp, err := p.makeRequest(ctx, "GET", "sys/health", nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// Vault health endpoint returns various status codes for different states
	// 200 = initialized, unsealed, and active
	// 429 = unsealed and standby
	// 472 = data recovery mode replication secondary and active
	// 473 = performance standby
	// 501 = not initialized
	// 503 = sealed
	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		return nil
	}

	if resp.StatusCode == 429 || resp.StatusCode == 472 || resp.StatusCode == 473 {
		// These are acceptable states
		return nil
	}

	body, _ := io.ReadAll(resp.Body)
	return fmt.Errorf("vault health check failed with status %d: %s", resp.StatusCode, string(body))
}

// GetProviderInfo returns provider information
func (p *VaultProvider) GetProviderInfo() ProviderInfo {
	status := "unknown"
	var lastTested *time.Time

	// Try a quick connection test
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := p.TestConnection(ctx); err == nil {
		status = "healthy"
	} else {
		status = "unhealthy"
	}

	now := time.Now()
	lastTested = &now

	capabilities := []string{"get", "set", "delete", "list", "test", "rotate"}
	if p.config.Version == "v2" {
		capabilities = append(capabilities, "versioning", "metadata")
	}

	return ProviderInfo{
		Type:         models.ProviderTypeVault,
		Name:         "HashiCorp Vault",
		Version:      p.config.Version,
		Capabilities: capabilities,
		Status:       status,
		LastTested:   lastTested,
	}
}

// RotateSecret rotates a secret (generates a new value and stores it)
func (p *VaultProvider) RotateSecret(ctx context.Context, name string) (string, error) {
	// Generate a new random value
	newValue, err := generateRandomValue(32)
	if err != nil {
		return "", fmt.Errorf("failed to generate new value: %w", err)
	}

	// Store the new value
	if err := p.SetSecret(ctx, name, newValue); err != nil {
		return "", fmt.Errorf("failed to store rotated secret: %w", err)
	}

	p.logger.Info("Rotated secret in Vault", zap.String("name", name))

	return newValue, nil
}

// GetSecretVersion retrieves a specific version of a secret (KV v2 only)
func (p *VaultProvider) GetSecretVersion(ctx context.Context, name string, version int) (string, error) {
	if p.config.Version != "v2" {
		return "", fmt.Errorf("secret versioning is only supported in KV v2")
	}

	path := fmt.Sprintf("%s?version=%d", p.buildSecretPath(name), version)
	resp, err := p.makeRequest(ctx, "GET", path, nil)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return "", fmt.Errorf("secret '%s' version %d not found", name, version)
	}

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var secretResp VaultSecretResponse
	if err := json.NewDecoder(resp.Body).Decode(&secretResp); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	// Extract the secret value
	data := secretResp.Data.Data
	if value, exists := data["value"]; exists {
		if strValue, ok := value.(string); ok {
			return strValue, nil
		}
	}

	return "", fmt.Errorf("no value found in secret '%s' version %d", name, version)
}
