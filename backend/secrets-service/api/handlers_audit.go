package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/claudio/deploy-orchestrator/secrets-service/models"
)

// GetAuditLogs returns audit logs with optional filtering
func (h *Handler) GetAuditLogs(c *gin.Context) {
	userID := c.Query("userId")
	resource := c.Query("resource")
	action := c.Query("action")
	fromStr := c.Query("from")
	toStr := c.Query("to")
	limitStr := c.<PERSON>ult<PERSON>("limit", "100")
	offsetStr := c.<PERSON>("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	query := h.db.Model(&models.AuditLog{})

	// Apply filters
	if userID != "" {
		query = query.Where("user_id = ?", userID)
	}
	if resource != "" {
		query = query.Where("resource = ?", resource)
	}
	if action != "" {
		query = query.Where("action = ?", action)
	}

	// Date range filtering
	if fromStr != "" {
		if from, err := time.Parse(time.RFC3339, fromStr); err == nil {
			query = query.Where("created_at >= ?", from)
		}
	}
	if toStr != "" {
		if to, err := time.Parse(time.RFC3339, toStr); err == nil {
			query = query.Where("created_at <= ?", to)
		}
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	var logs []models.AuditLog
	if err := query.Order("created_at DESC").Find(&logs).Error; err != nil {
		h.logger.Error("Failed to get audit logs", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get audit logs"})
		return
	}

	// Get total count for pagination
	var total int64
	countQuery := h.db.Model(&models.AuditLog{})
	if userID != "" {
		countQuery = countQuery.Where("user_id = ?", userID)
	}
	if resource != "" {
		countQuery = countQuery.Where("resource = ?", resource)
	}
	if action != "" {
		countQuery = countQuery.Where("action = ?", action)
	}
	if fromStr != "" {
		if from, err := time.Parse(time.RFC3339, fromStr); err == nil {
			countQuery = countQuery.Where("created_at >= ?", from)
		}
	}
	if toStr != "" {
		if to, err := time.Parse(time.RFC3339, toStr); err == nil {
			countQuery = countQuery.Where("created_at <= ?", to)
		}
	}
	countQuery.Count(&total)

	c.JSON(http.StatusOK, gin.H{
		"logs":   logs,
		"total":  total,
		"limit":  limit,
		"offset": offset,
	})
}

// GetAccessLogs returns access logs with optional filtering
func (h *Handler) GetAccessLogs(c *gin.Context) {
	secretID := c.Query("secretId")
	userID := c.Query("userId")
	fromStr := c.Query("from")
	toStr := c.Query("to")
	limitStr := c.DefaultQuery("limit", "100")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	query := h.db.Model(&models.AccessLog{}).Preload("Secret")

	// Apply filters
	if secretID != "" {
		query = query.Where("secret_id = ?", secretID)
	}
	if userID != "" {
		query = query.Where("user_id = ?", userID)
	}

	// Date range filtering
	if fromStr != "" {
		if from, err := time.Parse(time.RFC3339, fromStr); err == nil {
			query = query.Where("created_at >= ?", from)
		}
	}
	if toStr != "" {
		if to, err := time.Parse(time.RFC3339, toStr); err == nil {
			query = query.Where("created_at <= ?", to)
		}
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	var logs []models.AccessLog
	if err := query.Order("created_at DESC").Find(&logs).Error; err != nil {
		h.logger.Error("Failed to get access logs", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get access logs"})
		return
	}

	// Get total count for pagination
	var total int64
	countQuery := h.db.Model(&models.AccessLog{})
	if secretID != "" {
		countQuery = countQuery.Where("secret_id = ?", secretID)
	}
	if userID != "" {
		countQuery = countQuery.Where("user_id = ?", userID)
	}
	if fromStr != "" {
		if from, err := time.Parse(time.RFC3339, fromStr); err == nil {
			countQuery = countQuery.Where("created_at >= ?", from)
		}
	}
	if toStr != "" {
		if to, err := time.Parse(time.RFC3339, toStr); err == nil {
			countQuery = countQuery.Where("created_at <= ?", to)
		}
	}
	countQuery.Count(&total)

	c.JSON(http.StatusOK, gin.H{
		"logs":   logs,
		"total":  total,
		"limit":  limit,
		"offset": offset,
	})
}

// GetSecretMetrics returns metrics about secrets
func (h *Handler) GetSecretMetrics(c *gin.Context) {
	metrics := make(map[string]interface{})

	// Total secrets count
	var totalSecrets int64
	if err := h.db.Model(&models.Secret{}).Count(&totalSecrets).Error; err != nil {
		h.logger.Error("Failed to count total secrets", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get metrics"})
		return
	}
	metrics["totalSecrets"] = totalSecrets

	// Active secrets count
	var activeSecrets int64
	if err := h.db.Model(&models.Secret{}).Where("status = ?", models.SecretStatusActive).Count(&activeSecrets).Error; err != nil {
		h.logger.Error("Failed to count active secrets", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get metrics"})
		return
	}
	metrics["activeSecrets"] = activeSecrets

	// Secrets by type
	var secretsByType []struct {
		Type  models.SecretType `json:"type"`
		Count int64             `json:"count"`
	}
	if err := h.db.Model(&models.Secret{}).
		Select("type, count(*) as count").
		Group("type").
		Find(&secretsByType).Error; err != nil {
		h.logger.Error("Failed to get secrets by type", zap.Error(err))
	} else {
		metrics["secretsByType"] = secretsByType
	}

	// Secrets by provider
	var secretsByProvider []struct {
		Provider string `json:"provider"`
		Count    int64  `json:"count"`
	}
	if err := h.db.Model(&models.Secret{}).
		Select("provider, count(*) as count").
		Group("provider").
		Find(&secretsByProvider).Error; err != nil {
		h.logger.Error("Failed to get secrets by provider", zap.Error(err))
	} else {
		metrics["secretsByProvider"] = secretsByProvider
	}

	// Secrets by scope
	var secretsByScope []struct {
		ScopeID   string `json:"scopeId"`
		ScopeName string `json:"scopeName"`
		Count     int64  `json:"count"`
	}
	if err := h.db.Table("secrets").
		Select("secrets.scope_id, secret_scopes.name as scope_name, count(*) as count").
		Joins("LEFT JOIN secret_scopes ON secrets.scope_id = secret_scopes.id").
		Group("secrets.scope_id, secret_scopes.name").
		Find(&secretsByScope).Error; err != nil {
		h.logger.Error("Failed to get secrets by scope", zap.Error(err))
	} else {
		metrics["secretsByScope"] = secretsByScope
	}

	// Secrets needing rotation
	var secretsNeedingRotation int64
	now := time.Now()
	if err := h.db.Model(&models.Secret{}).
		Where("next_rotation IS NOT NULL AND next_rotation <= ?", now).
		Where("status = ?", models.SecretStatusActive).
		Count(&secretsNeedingRotation).Error; err != nil {
		h.logger.Error("Failed to count secrets needing rotation", zap.Error(err))
	} else {
		metrics["secretsNeedingRotation"] = secretsNeedingRotation
	}

	// Recent rotations (last 30 days)
	var recentRotations int64
	thirtyDaysAgo := now.AddDate(0, 0, -30)
	if err := h.db.Model(&models.RotationHistory{}).
		Where("started_at >= ?", thirtyDaysAgo).
		Count(&recentRotations).Error; err != nil {
		h.logger.Error("Failed to count recent rotations", zap.Error(err))
	} else {
		metrics["recentRotations"] = recentRotations
	}

	// Successful rotations (last 30 days)
	var successfulRotations int64
	if err := h.db.Model(&models.RotationHistory{}).
		Where("started_at >= ? AND status = ?", thirtyDaysAgo, models.RotationStatusCompleted).
		Count(&successfulRotations).Error; err != nil {
		h.logger.Error("Failed to count successful rotations", zap.Error(err))
	} else {
		metrics["successfulRotations"] = successfulRotations
	}

	// Failed rotations (last 30 days)
	var failedRotations int64
	if err := h.db.Model(&models.RotationHistory{}).
		Where("started_at >= ? AND status = ?", thirtyDaysAgo, models.RotationStatusFailed).
		Count(&failedRotations).Error; err != nil {
		h.logger.Error("Failed to count failed rotations", zap.Error(err))
	} else {
		metrics["failedRotations"] = failedRotations
	}

	// Calculate rotation success rate
	if recentRotations > 0 {
		successRate := float64(successfulRotations) / float64(recentRotations) * 100
		metrics["rotationSuccessRate"] = successRate
	} else {
		metrics["rotationSuccessRate"] = 0.0
	}

	// Recent access count (last 24 hours)
	var recentAccess int64
	twentyFourHoursAgo := now.Add(-24 * time.Hour)
	if err := h.db.Model(&models.AccessLog{}).
		Where("created_at >= ?", twentyFourHoursAgo).
		Count(&recentAccess).Error; err != nil {
		h.logger.Error("Failed to count recent access", zap.Error(err))
	} else {
		metrics["recentAccess"] = recentAccess
	}

	// Provider health status
	providerHealth := h.providerManager.GetProviderHealth(c.Request.Context())
	metrics["providerHealth"] = providerHealth

	// Get rotation statistics
	if rotationStats, err := h.rotationManager.GetRotationStats(c.Request.Context()); err == nil {
		metrics["rotationStats"] = rotationStats
	}

	c.JSON(http.StatusOK, metrics)
}

// GetSystemHealth returns system health information
func (h *Handler) GetSystemHealth(c *gin.Context) {
	health := make(map[string]interface{})

	// Database connectivity
	sqlDB, err := h.db.DB()
	if err != nil {
		health["database"] = map[string]interface{}{
			"status": "error",
			"error":  err.Error(),
		}
	} else {
		if err := sqlDB.Ping(); err != nil {
			health["database"] = map[string]interface{}{
				"status": "error",
				"error":  err.Error(),
			}
		} else {
			health["database"] = map[string]interface{}{
				"status": "healthy",
			}
		}
	}

	// Provider health
	providerHealth := h.providerManager.GetProviderHealth(c.Request.Context())
	health["providers"] = providerHealth

	// Encryption manager status
	health["encryption"] = map[string]interface{}{
		"status": "healthy", // Simplified check
	}

	// Overall status
	overallStatus := "healthy"
	if dbHealth, ok := health["database"].(map[string]interface{}); ok {
		if status, ok := dbHealth["status"].(string); ok && status != "healthy" {
			overallStatus = "degraded"
		}
	}

	// Check if any providers are unhealthy
	for _, provider := range providerHealth {
		if provider.Status != "healthy" {
			overallStatus = "degraded"
			break
		}
	}

	health["overall"] = overallStatus
	health["timestamp"] = time.Now()

	if overallStatus == "healthy" {
		c.JSON(http.StatusOK, health)
	} else {
		c.JSON(http.StatusServiceUnavailable, health)
	}
}
