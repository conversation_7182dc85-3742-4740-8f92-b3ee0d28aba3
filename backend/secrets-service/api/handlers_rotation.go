package api

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/claudio/deploy-orchestrator/secrets-service/models"
)

// CreateRotationPolicyRequest represents a request to create a rotation policy
type CreateRotationPolicyRequest struct {
	Name            string                  `json:"name" binding:"required"`
	Description     string                  `json:"description"`
	Interval        string                  `json:"interval" binding:"required"`
	MaxAge          string                  `json:"maxAge"`
	GracePeriod     string                  `json:"gracePeriod"`
	Strategy        models.RotationStrategy `json:"strategy"`
	Config          map[string]interface{}  `json:"config"`
	NotifyOnSuccess bool                    `json:"notifyOnSuccess"`
	NotifyOnFailure bool                    `json:"notifyOnFailure"`
	NotifyUsers     []string                `json:"notifyUsers"`
}

// UpdateRotationPolicyRequest represents a request to update a rotation policy
type UpdateRotationPolicyRequest struct {
	Description     string                  `json:"description"`
	Interval        string                  `json:"interval"`
	MaxAge          string                  `json:"maxAge"`
	GracePeriod     string                  `json:"gracePeriod"`
	Strategy        models.RotationStrategy `json:"strategy"`
	Config          map[string]interface{}  `json:"config"`
	NotifyOnSuccess *bool                   `json:"notifyOnSuccess"`
	NotifyOnFailure *bool                   `json:"notifyOnFailure"`
	NotifyUsers     []string                `json:"notifyUsers"`
	IsActive        *bool                   `json:"isActive"`
}

// CreateRotationPolicy creates a new rotation policy
func (h *Handler) CreateRotationPolicy(c *gin.Context) {
	var req CreateRotationPolicyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := c.GetString("user_id")
	username := c.GetString("username")

	// Validate interval format
	if _, err := time.ParseDuration(req.Interval); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid interval format"})
		return
	}

	// Validate max age format if provided
	if req.MaxAge != "" {
		if _, err := time.ParseDuration(req.MaxAge); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid maxAge format"})
			return
		}
	}

	// Validate grace period format if provided
	if req.GracePeriod != "" {
		if _, err := time.ParseDuration(req.GracePeriod); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid gracePeriod format"})
			return
		}
	}

	// Set default strategy if not provided
	if req.Strategy == "" {
		req.Strategy = models.RotationStrategyReplace
	}

	policy := &models.RotationPolicy{
		ID:              generateRotationPolicyID(),
		Name:            req.Name,
		Description:     req.Description,
		Interval:        req.Interval,
		MaxAge:          req.MaxAge,
		GracePeriod:     req.GracePeriod,
		Strategy:        req.Strategy,
		Config:          req.Config,
		NotifyOnSuccess: req.NotifyOnSuccess,
		NotifyOnFailure: req.NotifyOnFailure,
		NotifyUsers:     req.NotifyUsers,
		IsActive:        true,
	}

	if err := h.db.Create(policy).Error; err != nil {
		h.logger.Error("Failed to create rotation policy", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create rotation policy"})
		return
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "create",
		Resource:   "rotation_policy",
		ResourceID: policy.ID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusCreated,
		Success:    true,
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Rotation policy created successfully",
		zap.String("policyID", policy.ID),
		zap.String("name", policy.Name),
		zap.String("userID", userID))

	c.JSON(http.StatusCreated, policy)
}

// GetRotationPolicy retrieves a rotation policy by ID
func (h *Handler) GetRotationPolicy(c *gin.Context) {
	policyID := c.Param("id")

	var policy models.RotationPolicy
	if err := h.db.First(&policy, "id = ?", policyID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Rotation policy not found"})
		} else {
			h.logger.Error("Failed to get rotation policy", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get rotation policy"})
		}
		return
	}

	c.JSON(http.StatusOK, policy)
}

// ListRotationPolicies lists all rotation policies
func (h *Handler) ListRotationPolicies(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	query := h.db.Model(&models.RotationPolicy{})

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	var policies []models.RotationPolicy
	if err := query.Order("name ASC").Find(&policies).Error; err != nil {
		h.logger.Error("Failed to list rotation policies", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list rotation policies"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"policies": policies,
		"total":    len(policies),
		"limit":    limit,
		"offset":   offset,
	})
}

// UpdateRotationPolicy updates an existing rotation policy
func (h *Handler) UpdateRotationPolicy(c *gin.Context) {
	policyID := c.Param("id")
	userID := c.GetString("user_id")
	username := c.GetString("username")

	var req UpdateRotationPolicyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var policy models.RotationPolicy
	if err := h.db.First(&policy, "id = ?", policyID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Rotation policy not found"})
		} else {
			h.logger.Error("Failed to get rotation policy", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get rotation policy"})
		}
		return
	}

	// Update fields
	if req.Description != "" {
		policy.Description = req.Description
	}
	if req.Interval != "" {
		if _, err := time.ParseDuration(req.Interval); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid interval format"})
			return
		}
		policy.Interval = req.Interval
	}
	if req.MaxAge != "" {
		if _, err := time.ParseDuration(req.MaxAge); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid maxAge format"})
			return
		}
		policy.MaxAge = req.MaxAge
	}
	if req.GracePeriod != "" {
		if _, err := time.ParseDuration(req.GracePeriod); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid gracePeriod format"})
			return
		}
		policy.GracePeriod = req.GracePeriod
	}
	if req.Strategy != "" {
		policy.Strategy = req.Strategy
	}
	if req.Config != nil {
		policy.Config = req.Config
	}
	if req.NotifyOnSuccess != nil {
		policy.NotifyOnSuccess = *req.NotifyOnSuccess
	}
	if req.NotifyOnFailure != nil {
		policy.NotifyOnFailure = *req.NotifyOnFailure
	}
	if req.NotifyUsers != nil {
		policy.NotifyUsers = req.NotifyUsers
	}
	if req.IsActive != nil {
		policy.IsActive = *req.IsActive
	}

	if err := h.db.Save(&policy).Error; err != nil {
		h.logger.Error("Failed to update rotation policy", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update rotation policy"})
		return
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "update",
		Resource:   "rotation_policy",
		ResourceID: policy.ID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusOK,
		Success:    true,
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Rotation policy updated successfully",
		zap.String("policyID", policy.ID),
		zap.String("userID", userID))

	c.JSON(http.StatusOK, policy)
}

// DeleteRotationPolicy deletes a rotation policy
func (h *Handler) DeleteRotationPolicy(c *gin.Context) {
	policyID := c.Param("id")
	userID := c.GetString("user_id")
	username := c.GetString("username")

	// Check if policy is being used by any secrets
	var secretCount int64
	if err := h.db.Model(&models.Secret{}).Where("rotation_policy_id = ?", policyID).Count(&secretCount).Error; err != nil {
		h.logger.Error("Failed to count secrets using rotation policy", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check policy usage"})
		return
	}

	if secretCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete rotation policy with existing secrets"})
		return
	}

	if err := h.db.Delete(&models.RotationPolicy{}, "id = ?", policyID).Error; err != nil {
		h.logger.Error("Failed to delete rotation policy", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete rotation policy"})
		return
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "delete",
		Resource:   "rotation_policy",
		ResourceID: policyID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusNoContent,
		Success:    true,
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Rotation policy deleted successfully",
		zap.String("policyID", policyID),
		zap.String("userID", userID))

	c.Status(http.StatusNoContent)
}

// GetRotationHistory returns rotation history
func (h *Handler) GetRotationHistory(c *gin.Context) {
	secretID := c.Query("secretId")
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	query := h.db.Model(&models.RotationHistory{}).Preload("Secret").Preload("RotationPolicy")

	if secretID != "" {
		query = query.Where("secret_id = ?", secretID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	var history []models.RotationHistory
	if err := query.Order("started_at DESC").Find(&history).Error; err != nil {
		h.logger.Error("Failed to get rotation history", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get rotation history"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"history": history,
		"total":   len(history),
		"limit":   limit,
		"offset":  offset,
	})
}

// generateRotationPolicyID generates a unique rotation policy ID
func generateRotationPolicyID() string {
	return fmt.Sprintf("rotpol_%d", time.Now().UnixNano())
}
