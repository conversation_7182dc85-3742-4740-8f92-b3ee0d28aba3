package api

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/claudio/deploy-orchestrator/secrets-service/config"
	"github.com/claudio/deploy-orchestrator/secrets-service/encryption"
	"github.com/claudio/deploy-orchestrator/secrets-service/models"
	"github.com/claudio/deploy-orchestrator/secrets-service/providers"
	"github.com/claudio/deploy-orchestrator/secrets-service/rotation"
	"github.com/claudio/deploy-orchestrator/secrets-service/storage"
	"github.com/claudio/deploy-orchestrator/shared/auth"
)

// Handler handles HTTP requests for the secrets service
type Handler struct {
	db                *gorm.DB
	repository        *storage.Repository
	providerManager   *providers.Manager
	encryptionManager *encryption.Manager
	rotationManager   *rotation.Manager
	logger            *zap.Logger
	authHelper        *auth.AuthHelper
	config            *config.Config
}

// NewHandler creates a new API handler
func NewHandler(db *gorm.DB, repository *storage.Repository, providerManager *providers.Manager, encryptionManager *encryption.Manager, rotationManager *rotation.Manager, logger *zap.Logger, cfg *config.Config) *Handler {
	return &Handler{
		db:                db,
		repository:        repository,
		providerManager:   providerManager,
		encryptionManager: encryptionManager,
		rotationManager:   rotationManager,
		logger:            logger,
		authHelper:        auth.NewAuthHelper(),
		config:            cfg,
	}
}

// CreateSecretRequest represents a request to create a secret
type CreateSecretRequest struct {
	Name             string                 `json:"name" binding:"required"`
	Description      string                 `json:"description"`
	ScopeID          string                 `json:"scopeId" binding:"required"`
	Type             models.SecretType      `json:"type" binding:"required"`
	Provider         string                 `json:"provider"`
	Value            string                 `json:"value" binding:"required"`
	Tags             []string               `json:"tags"`
	Metadata         map[string]interface{} `json:"metadata"`
	RotationPolicyID *string                `json:"rotationPolicyId"`
	RequiresApproval bool                   `json:"requiresApproval"`
	ExpiresAt        *time.Time             `json:"expiresAt"`
}

// UpdateSecretRequest represents a request to update a secret
type UpdateSecretRequest struct {
	Description      string                 `json:"description"`
	Value            *string                `json:"value"`
	Tags             []string               `json:"tags"`
	Metadata         map[string]interface{} `json:"metadata"`
	RotationPolicyID *string                `json:"rotationPolicyId"`
	RequiresApproval *bool                  `json:"requiresApproval"`
	ExpiresAt        *time.Time             `json:"expiresAt"`
}

// CreateSecret creates a new secret
func (h *Handler) CreateSecret(c *gin.Context) {
	var req CreateSecretRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check user authentication using centralized auth helper
	authContext := h.authHelper.RequireAuthentication(c)
	if authContext == nil {
		// RequireAuthentication already sent the 401 response
		return
	}

	// Set default provider if not specified
	if req.Provider == "" {
		req.Provider = "internal"
	}

	// Get default encryption key
	var encryptionKey models.EncryptionKey
	if err := h.db.Where("is_primary = ? AND is_active = ?", true, true).First(&encryptionKey).Error; err != nil {
		h.logger.Error("Failed to get encryption key", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get encryption key"})
		return
	}

	// Encrypt the secret value
	encryptedValue, err := h.encryptionManager.EncryptSecret(req.Value, encryptionKey.ID)
	if err != nil {
		h.logger.Error("Failed to encrypt secret", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to encrypt secret"})
		return
	}

	// Create secret
	secret := &models.Secret{
		ID:               generateID(),
		Name:             req.Name,
		Description:      req.Description,
		ScopeID:          req.ScopeID,
		Type:             req.Type,
		Provider:         req.Provider,
		EncryptedValue:   encryptedValue,
		KeyID:            encryptionKey.ID,
		Tags:             req.Tags,
		Metadata:         req.Metadata,
		RotationPolicyID: req.RotationPolicyID,
		RequiresApproval: req.RequiresApproval,
		Status:           models.SecretStatusActive,
		ExpiresAt:        req.ExpiresAt,
	}

	// Set next rotation time if rotation policy is specified
	if req.RotationPolicyID != nil {
		var policy models.RotationPolicy
		if err := h.db.First(&policy, "id = ?", *req.RotationPolicyID).Error; err == nil {
			if interval, parseErr := time.ParseDuration(policy.Interval); parseErr == nil {
				nextRotation := time.Now().Add(interval)
				secret.NextRotation = &nextRotation
			}
		}
	}

	if err := h.repository.CreateSecret(secret); err != nil {
		h.logger.Error("Failed to create secret", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create secret"})
		return
	}

	// Create initial secret version
	version := &models.SecretVersion{
		ID:             generateID(),
		SecretID:       secret.ID,
		Version:        1,
		EncryptedValue: encryptedValue,
		KeyID:          encryptionKey.ID,
		CreatedBy:      authContext.UserID,
		Reason:         "Initial creation",
		IsActive:       true,
	}

	if err := h.repository.CreateSecretVersion(version); err != nil {
		h.logger.Error("Failed to create secret version", zap.Error(err))
		// Don't return error here, secret is already created
	}

	// Store in external provider if not internal
	if req.Provider != "internal" {
		if err := h.providerManager.SetSecret(c.Request.Context(), req.Provider, req.Name, req.Value); err != nil {
			h.logger.Error("Failed to store secret in external provider",
				zap.String("provider", req.Provider),
				zap.Error(err))
			// Don't fail the request, but log the error
		}
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "create",
		Resource:   "secret",
		ResourceID: secret.ID,
		UserID:     authContext.UserID,
		Username:   authContext.Username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusCreated,
		Success:    true,
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Secret created successfully",
		zap.String("secretID", secret.ID),
		zap.String("name", secret.Name),
		zap.String("userID", authContext.UserID))

	c.JSON(http.StatusCreated, secret)
}

// GetSecret retrieves a secret by ID
func (h *Handler) GetSecret(c *gin.Context) {
	secretID := c.Param("id")
	userID := c.GetString("user_id")
	username := c.GetString("username")

	secret, err := h.repository.GetSecret(secretID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Secret not found"})
		} else {
			h.logger.Error("Failed to get secret", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get secret"})
		}
		return
	}

	// Get active version
	version, err := h.repository.GetActiveSecretVersion(secretID)
	if err != nil {
		h.logger.Error("Failed to get active secret version", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get secret version"})
		return
	}

	// Decrypt the secret value
	decryptedValue, err := h.encryptionManager.DecryptSecret(version.EncryptedValue, version.KeyID)
	if err != nil {
		h.logger.Error("Failed to decrypt secret", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decrypt secret"})
		return
	}

	// Create access log
	accessLog := &models.AccessLog{
		ID:        generateID(),
		SecretID:  secret.ID,
		UserID:    userID,
		Username:  username,
		IPAddress: c.ClientIP(),
		UserAgent: c.GetHeader("User-Agent"),
		Method:    c.Request.Method,
		Success:   true,
		Version:   version.Version,
	}
	h.repository.CreateAccessLog(accessLog)

	// Return secret with decrypted value
	response := map[string]interface{}{
		"id":               secret.ID,
		"name":             secret.Name,
		"description":      secret.Description,
		"scopeId":          secret.ScopeID,
		"scope":            secret.Scope,
		"type":             secret.Type,
		"provider":         secret.Provider,
		"value":            decryptedValue,
		"tags":             secret.Tags,
		"metadata":         secret.Metadata,
		"rotationPolicy":   secret.RotationPolicy,
		"lastRotated":      secret.LastRotated,
		"nextRotation":     secret.NextRotation,
		"requiresApproval": secret.RequiresApproval,
		"status":           secret.Status,
		"expiresAt":        secret.ExpiresAt,
		"createdAt":        secret.CreatedAt,
		"updatedAt":        secret.UpdatedAt,
		"version":          version.Version,
	}

	c.JSON(http.StatusOK, response)
}

// ListSecrets lists secrets with optional filtering
func (h *Handler) ListSecrets(c *gin.Context) {
	scopeID := c.Query("scopeId")
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	secrets, err := h.repository.ListSecrets(scopeID, limit, offset)
	if err != nil {
		h.logger.Error("Failed to list secrets", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list secrets"})
		return
	}

	// Return secrets without values for security
	var response []map[string]interface{}
	for _, secret := range secrets {
		response = append(response, map[string]interface{}{
			"id":               secret.ID,
			"name":             secret.Name,
			"description":      secret.Description,
			"scopeId":          secret.ScopeID,
			"scope":            secret.Scope,
			"type":             secret.Type,
			"provider":         secret.Provider,
			"tags":             secret.Tags,
			"metadata":         secret.Metadata,
			"rotationPolicy":   secret.RotationPolicy,
			"lastRotated":      secret.LastRotated,
			"nextRotation":     secret.NextRotation,
			"requiresApproval": secret.RequiresApproval,
			"status":           secret.Status,
			"expiresAt":        secret.ExpiresAt,
			"createdAt":        secret.CreatedAt,
			"updatedAt":        secret.UpdatedAt,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"secrets": response,
		"total":   len(response),
		"limit":   limit,
		"offset":  offset,
	})
}

// UpdateSecret updates an existing secret
func (h *Handler) UpdateSecret(c *gin.Context) {
	secretID := c.Param("id")
	userID := c.GetString("user_id")
	username := c.GetString("username")

	var req UpdateSecretRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	secret, err := h.repository.GetSecret(secretID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Secret not found"})
		} else {
			h.logger.Error("Failed to get secret", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get secret"})
		}
		return
	}

	// Update fields
	if req.Description != "" {
		secret.Description = req.Description
	}
	if req.Tags != nil {
		secret.Tags = req.Tags
	}
	if req.Metadata != nil {
		secret.Metadata = req.Metadata
	}
	if req.RotationPolicyID != nil {
		secret.RotationPolicyID = req.RotationPolicyID
	}
	if req.RequiresApproval != nil {
		secret.RequiresApproval = *req.RequiresApproval
	}
	if req.ExpiresAt != nil {
		secret.ExpiresAt = req.ExpiresAt
	}

	// If value is being updated, create a new version
	if req.Value != nil {
		// Get encryption key
		var encryptionKey models.EncryptionKey
		if err := h.db.Where("is_primary = ? AND is_active = ?", true, true).First(&encryptionKey).Error; err != nil {
			h.logger.Error("Failed to get encryption key", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get encryption key"})
			return
		}

		// Encrypt new value
		encryptedValue, err := h.encryptionManager.EncryptSecret(*req.Value, encryptionKey.ID)
		if err != nil {
			h.logger.Error("Failed to encrypt secret", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to encrypt secret"})
			return
		}

		// Get current version number
		currentVersion, err := h.repository.GetActiveSecretVersion(secretID)
		if err != nil {
			h.logger.Error("Failed to get current version", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get current version"})
			return
		}

		// Deactivate current version
		currentVersion.IsActive = false
		h.db.Save(currentVersion)

		// Create new version
		newVersion := &models.SecretVersion{
			ID:             generateID(),
			SecretID:       secret.ID,
			Version:        currentVersion.Version + 1,
			EncryptedValue: encryptedValue,
			KeyID:          encryptionKey.ID,
			CreatedBy:      userID,
			Reason:         "Manual update",
			IsActive:       true,
		}

		if err := h.repository.CreateSecretVersion(newVersion); err != nil {
			h.logger.Error("Failed to create new secret version", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create new version"})
			return
		}

		// Update secret's encrypted value
		secret.EncryptedValue = encryptedValue

		// Update in external provider if not internal
		if secret.Provider != "internal" {
			if err := h.providerManager.SetSecret(c.Request.Context(), secret.Provider, secret.Name, *req.Value); err != nil {
				h.logger.Error("Failed to update secret in external provider",
					zap.String("provider", secret.Provider),
					zap.Error(err))
			}
		}
	}

	if err := h.repository.UpdateSecret(secret); err != nil {
		h.logger.Error("Failed to update secret", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update secret"})
		return
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "update",
		Resource:   "secret",
		ResourceID: secret.ID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusOK,
		Success:    true,
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Secret updated successfully",
		zap.String("secretID", secret.ID),
		zap.String("userID", userID))

	c.JSON(http.StatusOK, secret)
}

// DeleteSecret deletes a secret
func (h *Handler) DeleteSecret(c *gin.Context) {
	secretID := c.Param("id")
	userID := c.GetString("user_id")
	username := c.GetString("username")

	secret, err := h.repository.GetSecret(secretID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Secret not found"})
		} else {
			h.logger.Error("Failed to get secret", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get secret"})
		}
		return
	}

	// Delete from external provider if not internal
	if secret.Provider != "internal" {
		if err := h.providerManager.DeleteSecret(c.Request.Context(), secret.Provider, secret.Name); err != nil {
			h.logger.Error("Failed to delete secret from external provider",
				zap.String("provider", secret.Provider),
				zap.Error(err))
			// Don't fail the request, but log the error
		}
	}

	if err := h.repository.DeleteSecret(secretID); err != nil {
		h.logger.Error("Failed to delete secret", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete secret"})
		return
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "delete",
		Resource:   "secret",
		ResourceID: secret.ID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusNoContent,
		Success:    true,
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Secret deleted successfully",
		zap.String("secretID", secret.ID),
		zap.String("userID", userID))

	c.Status(http.StatusNoContent)
}

// RotateSecret rotates a secret
func (h *Handler) RotateSecret(c *gin.Context) {
	secretID := c.Param("id")
	userID := c.GetString("user_id")
	username := c.GetString("username")

	history, err := h.rotationManager.RotateSecret(c.Request.Context(), secretID, userID, "manual rotation")
	if err != nil {
		h.logger.Error("Failed to rotate secret", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to rotate secret"})
		return
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "rotate",
		Resource:   "secret",
		ResourceID: secretID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusOK,
		Success:    true,
	}
	h.repository.CreateAuditLog(auditLog)

	c.JSON(http.StatusOK, history)
}

// GetSecretVersions returns all versions of a secret
func (h *Handler) GetSecretVersions(c *gin.Context) {
	secretID := c.Param("id")

	versions, err := h.repository.GetSecretVersions(secretID)
	if err != nil {
		h.logger.Error("Failed to get secret versions", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get secret versions"})
		return
	}

	// Return versions without encrypted values for security
	var response []map[string]interface{}
	for _, version := range versions {
		response = append(response, map[string]interface{}{
			"id":        version.ID,
			"version":   version.Version,
			"createdBy": version.CreatedBy,
			"reason":    version.Reason,
			"isActive":  version.IsActive,
			"createdAt": version.CreatedAt,
		})
	}

	c.JSON(http.StatusOK, gin.H{"versions": response})
}

// GetSecretVersion returns a specific version of a secret
func (h *Handler) GetSecretVersion(c *gin.Context) {
	secretID := c.Param("id")
	versionStr := c.Param("version")
	userID := c.GetString("user_id")
	username := c.GetString("username")

	version, err := strconv.Atoi(versionStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid version parameter"})
		return
	}

	var secretVersion models.SecretVersion
	if err := h.db.Where("secret_id = ? AND version = ?", secretID, version).First(&secretVersion).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Secret version not found"})
		} else {
			h.logger.Error("Failed to get secret version", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get secret version"})
		}
		return
	}

	// Decrypt the secret value
	decryptedValue, err := h.encryptionManager.DecryptSecret(secretVersion.EncryptedValue, secretVersion.KeyID)
	if err != nil {
		h.logger.Error("Failed to decrypt secret", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decrypt secret"})
		return
	}

	// Create access log
	accessLog := &models.AccessLog{
		ID:        generateID(),
		SecretID:  secretID,
		UserID:    userID,
		Username:  username,
		IPAddress: c.ClientIP(),
		UserAgent: c.GetHeader("User-Agent"),
		Method:    c.Request.Method,
		Success:   true,
		Version:   version,
	}
	h.repository.CreateAccessLog(accessLog)

	response := map[string]interface{}{
		"id":        secretVersion.ID,
		"version":   secretVersion.Version,
		"value":     decryptedValue,
		"createdBy": secretVersion.CreatedBy,
		"reason":    secretVersion.Reason,
		"isActive":  secretVersion.IsActive,
		"createdAt": secretVersion.CreatedAt,
	}

	c.JSON(http.StatusOK, response)
}

// generateID generates a unique ID
func generateID() string {
	return fmt.Sprintf("sec_%d", time.Now().UnixNano())
}
