package api

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/claudio/deploy-orchestrator/secrets-service/models"
)

// RetrieveSecretsRequest represents a request to retrieve secrets
type RetrieveSecretsRequest struct {
	SecretNames []string `json:"secretNames" binding:"required"`
	ScopeID     string   `json:"scopeId" binding:"required"`
	ServiceID   string   `json:"serviceId"`
}

// BulkRetrieveSecretsRequest represents a bulk secret retrieval request
type BulkRetrieveSecretsRequest struct {
	Requests []RetrieveSecretsRequest `json:"requests" binding:"required"`
}

// SecretResponse represents a secret in the response
type SecretResponse struct {
	Name      string                 `json:"name"`
	Value     string                 `json:"value"`
	Type      models.SecretType      `json:"type"`
	Metadata  map[string]interface{} `json:"metadata"`
	Version   int                    `json:"version"`
	ExpiresAt *time.Time             `json:"expiresAt"`
}

// RetrieveSecretsResponse represents the response for secret retrieval
type RetrieveSecretsResponse struct {
	Secrets []SecretResponse `json:"secrets"`
	Errors  []SecretError    `json:"errors"`
}

// SecretError represents an error retrieving a secret
type SecretError struct {
	Name  string `json:"name"`
	Error string `json:"error"`
}

// RetrieveSecrets retrieves multiple secrets for service integration
func (h *Handler) RetrieveSecrets(c *gin.Context) {
	var req RetrieveSecretsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := c.GetString("user_id")
	username := c.GetString("username")

	response := RetrieveSecretsResponse{
		Secrets: make([]SecretResponse, 0),
		Errors:  make([]SecretError, 0),
	}

	for _, secretName := range req.SecretNames {
		secret, err := h.repository.GetSecretByName(secretName, req.ScopeID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				response.Errors = append(response.Errors, SecretError{
					Name:  secretName,
					Error: "Secret not found",
				})
			} else {
				h.logger.Error("Failed to get secret",
					zap.String("name", secretName),
					zap.Error(err))
				response.Errors = append(response.Errors, SecretError{
					Name:  secretName,
					Error: "Failed to retrieve secret",
				})
			}
			continue
		}

		// Check if secret is active
		if secret.Status != models.SecretStatusActive {
			response.Errors = append(response.Errors, SecretError{
				Name:  secretName,
				Error: "Secret is not active",
			})
			continue
		}

		// Check if secret is expired
		if secret.ExpiresAt != nil && time.Now().After(*secret.ExpiresAt) {
			response.Errors = append(response.Errors, SecretError{
				Name:  secretName,
				Error: "Secret has expired",
			})
			continue
		}

		// Get active version
		version, err := h.repository.GetActiveSecretVersion(secret.ID)
		if err != nil {
			h.logger.Error("Failed to get active secret version",
				zap.String("secretID", secret.ID),
				zap.Error(err))
			response.Errors = append(response.Errors, SecretError{
				Name:  secretName,
				Error: "Failed to get secret version",
			})
			continue
		}

		// Decrypt the secret value
		decryptedValue, err := h.encryptionManager.DecryptSecret(version.EncryptedValue, version.KeyID)
		if err != nil {
			h.logger.Error("Failed to decrypt secret",
				zap.String("secretID", secret.ID),
				zap.Error(err))
			response.Errors = append(response.Errors, SecretError{
				Name:  secretName,
				Error: "Failed to decrypt secret",
			})
			continue
		}

		// Create access log
		accessLog := &models.AccessLog{
			ID:        generateID(),
			SecretID:  secret.ID,
			UserID:    userID,
			Username:  username,
			ServiceID: req.ServiceID,
			IPAddress: c.ClientIP(),
			UserAgent: c.GetHeader("User-Agent"),
			Method:    c.Request.Method,
			Success:   true,
			Version:   version.Version,
		}
		h.repository.CreateAccessLog(accessLog)

		// Add to response
		response.Secrets = append(response.Secrets, SecretResponse{
			Name:      secret.Name,
			Value:     decryptedValue,
			Type:      secret.Type,
			Metadata:  secret.Metadata,
			Version:   version.Version,
			ExpiresAt: secret.ExpiresAt,
		})
	}

	h.logger.Info("Secrets retrieved for service integration",
		zap.String("serviceID", req.ServiceID),
		zap.String("scopeID", req.ScopeID),
		zap.Int("successCount", len(response.Secrets)),
		zap.Int("errorCount", len(response.Errors)),
		zap.String("userID", userID))

	c.JSON(http.StatusOK, response)
}

// BulkRetrieveSecrets retrieves secrets in bulk for multiple scopes/services
func (h *Handler) BulkRetrieveSecrets(c *gin.Context) {
	var req BulkRetrieveSecretsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := c.GetString("user_id")
	responses := make(map[string]RetrieveSecretsResponse)

	for i, subReq := range req.Requests {
		key := fmt.Sprintf("request_%d", i)
		if subReq.ServiceID != "" {
			key = subReq.ServiceID
		}

		response := RetrieveSecretsResponse{
			Secrets: make([]SecretResponse, 0),
			Errors:  make([]SecretError, 0),
		}

		for _, secretName := range subReq.SecretNames {
			secret, err := h.repository.GetSecretByName(secretName, subReq.ScopeID)
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					response.Errors = append(response.Errors, SecretError{
						Name:  secretName,
						Error: "Secret not found",
					})
				} else {
					response.Errors = append(response.Errors, SecretError{
						Name:  secretName,
						Error: "Failed to retrieve secret",
					})
				}
				continue
			}

			// Check secret status and expiration (same as above)
			if secret.Status != models.SecretStatusActive {
				response.Errors = append(response.Errors, SecretError{
					Name:  secretName,
					Error: "Secret is not active",
				})
				continue
			}

			if secret.ExpiresAt != nil && time.Now().After(*secret.ExpiresAt) {
				response.Errors = append(response.Errors, SecretError{
					Name:  secretName,
					Error: "Secret has expired",
				})
				continue
			}

			// Get and decrypt secret (same as above)
			version, err := h.repository.GetActiveSecretVersion(secret.ID)
			if err != nil {
				response.Errors = append(response.Errors, SecretError{
					Name:  secretName,
					Error: "Failed to get secret version",
				})
				continue
			}

			decryptedValue, err := h.encryptionManager.DecryptSecret(version.EncryptedValue, version.KeyID)
			if err != nil {
				response.Errors = append(response.Errors, SecretError{
					Name:  secretName,
					Error: "Failed to decrypt secret",
				})
				continue
			}

			// Create access log
			accessLog := &models.AccessLog{
				ID:        generateID(),
				SecretID:  secret.ID,
				UserID:    userID,
				Username:  c.GetString("username"),
				ServiceID: subReq.ServiceID,
				IPAddress: c.ClientIP(),
				UserAgent: c.GetHeader("User-Agent"),
				Method:    c.Request.Method,
				Success:   true,
				Version:   version.Version,
			}
			h.repository.CreateAccessLog(accessLog)

			response.Secrets = append(response.Secrets, SecretResponse{
				Name:      secret.Name,
				Value:     decryptedValue,
				Type:      secret.Type,
				Metadata:  secret.Metadata,
				Version:   version.Version,
				ExpiresAt: secret.ExpiresAt,
			})
		}

		responses[key] = response
	}

	h.logger.Info("Bulk secrets retrieved",
		zap.Int("requestCount", len(req.Requests)),
		zap.String("userID", userID))

	c.JSON(http.StatusOK, responses)
}

// GetServiceSecrets retrieves all secrets for a specific service
func (h *Handler) GetServiceSecrets(c *gin.Context) {
	serviceID := c.Param("service")
	scopeID := c.Query("scopeId")
	userID := c.GetString("user_id")
	username := c.GetString("username")

	if scopeID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "scopeId parameter is required"})
		return
	}

	// Get all secrets in the scope
	secrets, err := h.repository.ListSecrets(scopeID, 0, 0)
	if err != nil {
		h.logger.Error("Failed to list secrets for service", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve secrets"})
		return
	}

	response := RetrieveSecretsResponse{
		Secrets: make([]SecretResponse, 0),
		Errors:  make([]SecretError, 0),
	}

	for _, secret := range secrets {
		// Check secret status and expiration
		if secret.Status != models.SecretStatusActive {
			continue
		}

		if secret.ExpiresAt != nil && time.Now().After(*secret.ExpiresAt) {
			continue
		}

		// Get active version
		version, err := h.repository.GetActiveSecretVersion(secret.ID)
		if err != nil {
			h.logger.Error("Failed to get active secret version",
				zap.String("secretID", secret.ID),
				zap.Error(err))
			response.Errors = append(response.Errors, SecretError{
				Name:  secret.Name,
				Error: "Failed to get secret version",
			})
			continue
		}

		// Decrypt the secret value
		decryptedValue, err := h.encryptionManager.DecryptSecret(version.EncryptedValue, version.KeyID)
		if err != nil {
			h.logger.Error("Failed to decrypt secret",
				zap.String("secretID", secret.ID),
				zap.Error(err))
			response.Errors = append(response.Errors, SecretError{
				Name:  secret.Name,
				Error: "Failed to decrypt secret",
			})
			continue
		}

		// Create access log
		accessLog := &models.AccessLog{
			ID:        generateID(),
			SecretID:  secret.ID,
			UserID:    userID,
			Username:  username,
			ServiceID: serviceID,
			IPAddress: c.ClientIP(),
			UserAgent: c.GetHeader("User-Agent"),
			Method:    c.Request.Method,
			Success:   true,
			Version:   version.Version,
		}
		h.repository.CreateAccessLog(accessLog)

		response.Secrets = append(response.Secrets, SecretResponse{
			Name:      secret.Name,
			Value:     decryptedValue,
			Type:      secret.Type,
			Metadata:  secret.Metadata,
			Version:   version.Version,
			ExpiresAt: secret.ExpiresAt,
		})
	}

	h.logger.Info("Service secrets retrieved",
		zap.String("serviceID", serviceID),
		zap.String("scopeID", scopeID),
		zap.Int("secretCount", len(response.Secrets)),
		zap.String("userID", userID))

	c.JSON(http.StatusOK, response)
}
