package api

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/claudio/deploy-orchestrator/secrets-service/models"
)

// CreateEncryptionKeyRequest represents a request to create an encryption key
type CreateEncryptionKeyRequest struct {
	Name    string `json:"name" binding:"required"`
	Purpose string `json:"purpose" binding:"required"`
}

// RotateEncryptionKeyRequest represents a request to rotate an encryption key
type RotateEncryptionKeyRequest struct {
	Reason string `json:"reason"`
}

// CreateEncryption<PERSON>ey creates a new encryption key
func (h *Handler) CreateEncryptionKey(c *gin.Context) {
	var req CreateEncryptionKeyRequest
	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := c.GetString("user_id")
	username := c.GetString("username")

	// Generate new encryption key
	key, err := h.encryptionManager.GenerateKey(req.Name, req.Purpose)
	if err != nil {
		h.logger.Error("Failed to generate encryption key", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate encryption key"})
		return
	}

	// Save to database
	if err := h.db.Create(key).Error; err != nil {
		h.logger.Error("Failed to save encryption key", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save encryption key"})
		return
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "create",
		Resource:   "encryption_key",
		ResourceID: key.ID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusCreated,
		Success:    true,
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Encryption key created successfully",
		zap.String("keyID", key.ID),
		zap.String("name", key.Name),
		zap.String("purpose", key.Purpose),
		zap.String("userID", userID))

	// Return key without sensitive data
	response := map[string]interface{}{
		"id":        key.ID,
		"name":      key.Name,
		"algorithm": key.Algorithm,
		"keySize":   key.KeySize,
		"purpose":   key.Purpose,
		"isActive":  key.IsActive,
		"isPrimary": key.IsPrimary,
		"createdAt": key.CreatedAt,
		"updatedAt": key.UpdatedAt,
	}

	c.JSON(http.StatusCreated, response)
}

// ListEncryptionKeys lists all encryption keys
func (h *Handler) ListEncryptionKeys(c *gin.Context) {
	purpose := c.Query("purpose")
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	query := h.db.Model(&models.EncryptionKey{})

	if purpose != "" {
		query = query.Where("purpose = ?", purpose)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	var keys []models.EncryptionKey
	if err := query.Order("is_primary DESC, created_at DESC").Find(&keys).Error; err != nil {
		h.logger.Error("Failed to list encryption keys", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list encryption keys"})
		return
	}

	// Return keys without sensitive data
	var response []map[string]interface{}
	for _, key := range keys {
		// Get rotation info
		rotationInfo, err := h.encryptionManager.GetKeyRotationInfo(&key)
		if err != nil {
			h.logger.Error("Failed to get key rotation info",
				zap.String("keyID", key.ID),
				zap.Error(err))
			rotationInfo = nil
		}

		keyData := map[string]interface{}{
			"id":           key.ID,
			"name":         key.Name,
			"algorithm":    key.Algorithm,
			"keySize":      key.KeySize,
			"purpose":      key.Purpose,
			"isActive":     key.IsActive,
			"isPrimary":    key.IsPrimary,
			"expiresAt":    key.ExpiresAt,
			"rotatedAt":    key.RotatedAt,
			"usageCount":   key.UsageCount,
			"lastUsed":     key.LastUsed,
			"createdAt":    key.CreatedAt,
			"updatedAt":    key.UpdatedAt,
			"rotationInfo": rotationInfo,
		}

		response = append(response, keyData)
	}

	c.JSON(http.StatusOK, gin.H{
		"keys":   response,
		"total":  len(response),
		"limit":  limit,
		"offset": offset,
	})
}

// RotateEncryptionKey rotates an encryption key
func (h *Handler) RotateEncryptionKey(c *gin.Context) {
	keyID := c.Param("id")
	userID := c.GetString("user_id")
	username := c.GetString("username")

	var req RotateEncryptionKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get the existing key
	var oldKey models.EncryptionKey
	if err := h.db.First(&oldKey, "id = ?", keyID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Encryption key not found"})
		} else {
			h.logger.Error("Failed to get encryption key", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get encryption key"})
		}
		return
	}

	// Generate new key
	newKey, err := h.encryptionManager.RotateKey(&oldKey)
	if err != nil {
		h.logger.Error("Failed to rotate encryption key", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to rotate encryption key"})
		return
	}

	// Save both keys in a transaction
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update old key
	if err := tx.Save(&oldKey).Error; err != nil {
		tx.Rollback()
		h.logger.Error("Failed to update old encryption key", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to rotate encryption key"})
		return
	}

	// Save new key
	if err := tx.Create(newKey).Error; err != nil {
		tx.Rollback()
		h.logger.Error("Failed to save new encryption key", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to rotate encryption key"})
		return
	}

	// If old key was primary, make new key primary
	if oldKey.IsPrimary {
		newKey.IsPrimary = true
		if err := tx.Save(newKey).Error; err != nil {
			tx.Rollback()
			h.logger.Error("Failed to set new key as primary", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to rotate encryption key"})
			return
		}
	}

	tx.Commit()

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "rotate",
		Resource:   "encryption_key",
		ResourceID: oldKey.ID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusOK,
		Success:    true,
		Metadata: map[string]interface{}{
			"reason":   req.Reason,
			"newKeyID": newKey.ID,
			"oldKeyID": oldKey.ID,
		},
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Encryption key rotated successfully",
		zap.String("oldKeyID", oldKey.ID),
		zap.String("newKeyID", newKey.ID),
		zap.String("reason", req.Reason),
		zap.String("userID", userID))

	// Return new key without sensitive data
	response := map[string]interface{}{
		"oldKey": map[string]interface{}{
			"id":        oldKey.ID,
			"isActive":  oldKey.IsActive,
			"isPrimary": oldKey.IsPrimary,
			"rotatedAt": oldKey.RotatedAt,
		},
		"newKey": map[string]interface{}{
			"id":        newKey.ID,
			"name":      newKey.Name,
			"algorithm": newKey.Algorithm,
			"keySize":   newKey.KeySize,
			"purpose":   newKey.Purpose,
			"isActive":  newKey.IsActive,
			"isPrimary": newKey.IsPrimary,
			"createdAt": newKey.CreatedAt,
		},
		"reason": req.Reason,
	}

	c.JSON(http.StatusOK, response)
}

// GetKeyStatus returns the status of an encryption key
func (h *Handler) GetKeyStatus(c *gin.Context) {
	keyID := c.Param("id")

	var key models.EncryptionKey
	if err := h.db.First(&key, "id = ?", keyID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Encryption key not found"})
		} else {
			h.logger.Error("Failed to get encryption key", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get encryption key"})
		}
		return
	}

	// Get rotation info
	rotationInfo, err := h.encryptionManager.GetKeyRotationInfo(&key)
	if err != nil {
		h.logger.Error("Failed to get key rotation info", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get key rotation info"})
		return
	}

	// Count secrets using this key
	var secretCount int64
	if err := h.db.Model(&models.Secret{}).Where("key_id = ?", keyID).Count(&secretCount).Error; err != nil {
		h.logger.Error("Failed to count secrets using key", zap.Error(err))
		secretCount = -1 // Indicate error
	}

	// Count secret versions using this key
	var versionCount int64
	if err := h.db.Model(&models.SecretVersion{}).Where("key_id = ?", keyID).Count(&versionCount).Error; err != nil {
		h.logger.Error("Failed to count secret versions using key", zap.Error(err))
		versionCount = -1 // Indicate error
	}

	response := map[string]interface{}{
		"id":           key.ID,
		"name":         key.Name,
		"algorithm":    key.Algorithm,
		"keySize":      key.KeySize,
		"purpose":      key.Purpose,
		"isActive":     key.IsActive,
		"isPrimary":    key.IsPrimary,
		"expiresAt":    key.ExpiresAt,
		"rotatedAt":    key.RotatedAt,
		"usageCount":   key.UsageCount,
		"lastUsed":     key.LastUsed,
		"createdAt":    key.CreatedAt,
		"updatedAt":    key.UpdatedAt,
		"rotationInfo": rotationInfo,
		"secretCount":  secretCount,
		"versionCount": versionCount,
	}

	c.JSON(http.StatusOK, response)
}

// generateEncryptionKeyID generates a unique encryption key ID
func generateEncryptionKeyID() string {
	return fmt.Sprintf("enckey_%d", time.Now().UnixNano())
}
