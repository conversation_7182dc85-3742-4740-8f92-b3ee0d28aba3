package api

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/claudio/deploy-orchestrator/secrets-service/models"
)

// CreateScopeRequest represents a request to create a scope
type CreateScopeRequest struct {
	Name        string                   `json:"name" binding:"required"`
	Type        models.ScopeType         `json:"type" binding:"required"`
	Description string                   `json:"description"`
	ParentID    *string                  `json:"parentId"`
	Config      map[string]interface{}   `json:"config"`
	Permissions []models.ScopePermission `json:"permissions"`
}

// UpdateScopeRequest represents a request to update a scope
type UpdateScopeRequest struct {
	Description string                   `json:"description"`
	Config      map[string]interface{}   `json:"config"`
	Permissions []models.ScopePermission `json:"permissions"`
	IsActive    *bool                    `json:"isActive"`
}

// CreateScope creates a new secret scope
func (h *Handler) CreateScope(c *gin.Context) {
	var req CreateScopeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := c.GetString("user_id")
	username := c.GetString("username")

	// Check if parent scope exists if specified
	if req.ParentID != nil {
		var parentScope models.SecretScope
		if err := h.db.First(&parentScope, "id = ?", *req.ParentID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Parent scope not found"})
			} else {
				h.logger.Error("Failed to check parent scope", zap.Error(err))
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check parent scope"})
			}
			return
		}
	}

	scope := &models.SecretScope{
		ID:          generateScopeID(),
		Name:        req.Name,
		Type:        req.Type,
		Description: req.Description,
		ParentID:    req.ParentID,
		Config:      req.Config,
		Permissions: req.Permissions,
		IsActive:    true,
	}

	if err := h.db.Create(scope).Error; err != nil {
		h.logger.Error("Failed to create scope", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create scope"})
		return
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "create",
		Resource:   "scope",
		ResourceID: scope.ID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusCreated,
		Success:    true,
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Scope created successfully",
		zap.String("scopeID", scope.ID),
		zap.String("name", scope.Name),
		zap.String("userID", userID))

	c.JSON(http.StatusCreated, scope)
}

// GetScope retrieves a scope by ID
func (h *Handler) GetScope(c *gin.Context) {
	scopeID := c.Param("id")

	var scope models.SecretScope
	if err := h.db.Preload("Parent").Preload("Children").First(&scope, "id = ?", scopeID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Scope not found"})
		} else {
			h.logger.Error("Failed to get scope", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get scope"})
		}
		return
	}

	c.JSON(http.StatusOK, scope)
}

// ListScopes lists all scopes with optional filtering
func (h *Handler) ListScopes(c *gin.Context) {
	scopeType := c.Query("type")
	parentID := c.Query("parentId")
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	query := h.db.Model(&models.SecretScope{}).Preload("Parent").Preload("Children")

	if scopeType != "" {
		query = query.Where("type = ?", scopeType)
	}

	if parentID != "" {
		if parentID == "null" {
			query = query.Where("parent_id IS NULL")
		} else {
			query = query.Where("parent_id = ?", parentID)
		}
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	var scopes []models.SecretScope
	if err := query.Find(&scopes).Error; err != nil {
		h.logger.Error("Failed to list scopes", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list scopes"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"scopes": scopes,
		"total":  len(scopes),
		"limit":  limit,
		"offset": offset,
	})
}

// UpdateScope updates an existing scope
func (h *Handler) UpdateScope(c *gin.Context) {
	scopeID := c.Param("id")
	userID := c.GetString("user_id")
	username := c.GetString("username")

	var req UpdateScopeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var scope models.SecretScope
	if err := h.db.First(&scope, "id = ?", scopeID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Scope not found"})
		} else {
			h.logger.Error("Failed to get scope", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get scope"})
		}
		return
	}

	// Update fields
	if req.Description != "" {
		scope.Description = req.Description
	}
	if req.Config != nil {
		scope.Config = req.Config
	}
	if req.Permissions != nil {
		scope.Permissions = req.Permissions
	}
	if req.IsActive != nil {
		scope.IsActive = *req.IsActive
	}

	if err := h.db.Save(&scope).Error; err != nil {
		h.logger.Error("Failed to update scope", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update scope"})
		return
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "update",
		Resource:   "scope",
		ResourceID: scope.ID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusOK,
		Success:    true,
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Scope updated successfully",
		zap.String("scopeID", scope.ID),
		zap.String("userID", userID))

	c.JSON(http.StatusOK, scope)
}

// DeleteScope deletes a scope
func (h *Handler) DeleteScope(c *gin.Context) {
	scopeID := c.Param("id")
	userID := c.GetString("user_id")
	username := c.GetString("username")

	// Check if scope has any secrets
	var secretCount int64
	if err := h.db.Model(&models.Secret{}).Where("scope_id = ?", scopeID).Count(&secretCount).Error; err != nil {
		h.logger.Error("Failed to count secrets in scope", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check scope usage"})
		return
	}

	if secretCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete scope with existing secrets"})
		return
	}

	// Check if scope has children
	var childCount int64
	if err := h.db.Model(&models.SecretScope{}).Where("parent_id = ?", scopeID).Count(&childCount).Error; err != nil {
		h.logger.Error("Failed to count child scopes", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check scope children"})
		return
	}

	if childCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete scope with child scopes"})
		return
	}

	if err := h.db.Delete(&models.SecretScope{}, "id = ?", scopeID).Error; err != nil {
		h.logger.Error("Failed to delete scope", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete scope"})
		return
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "delete",
		Resource:   "scope",
		ResourceID: scopeID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusNoContent,
		Success:    true,
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Scope deleted successfully",
		zap.String("scopeID", scopeID),
		zap.String("userID", userID))

	c.Status(http.StatusNoContent)
}

// generateScopeID generates a unique scope ID
func generateScopeID() string {
	return fmt.Sprintf("scope_%d", time.Now().UnixNano())
}
