package api

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/claudio/deploy-orchestrator/secrets-service/models"
)

// BindSecretToProjectRequest represents a request to bind a secret to a project
type BindSecretToProjectRequest struct {
	SecretID     string   `json:"secretId" binding:"required"`
	VariableName string   `json:"variableName" binding:"required"`
	VariableType string   `json:"variableType" binding:"required"` // env, file, config, mount
	AccessLevel  string   `json:"accessLevel" binding:"required"`  // read, write, admin
	Environments []string `json:"environments"`
	Services     []string `json:"services"`
	Description  string   `json:"description"`
}

// CreateSecretVariableRequest represents a request to create a secret variable
type CreateSecretVariableRequest struct {
	SecretID    string                 `json:"secretId" binding:"required"`
	Name        string                 `json:"name" binding:"required"`
	Type        string                 `json:"type" binding:"required"` // env, file, config, mount
	Path        string                 `json:"path"`
	Format      string                 `json:"format"` // json, yaml, plain, base64
	Environment string                 `json:"environment"`
	Service     string                 `json:"service"`
	Namespace   string                 `json:"namespace"`
	Transform   map[string]interface{} `json:"transform"`
}

// GetProjectSecrets returns all secrets bound to a project
func (h *Handler) GetProjectSecrets(c *gin.Context) {
	projectID := c.Param("projectId")
	environment := c.Query("environment")
	service := c.Query("service")
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	query := h.db.Model(&models.ProjectSecretBinding{}).
		Preload("Secret").
		Preload("Secret.Scope").
		Where("project_id = ?", projectID)

	// Filter by environment if specified
	if environment != "" {
		query = query.Where("environments @> ?", fmt.Sprintf(`["%s"]`, environment))
	}

	// Filter by service if specified
	if service != "" {
		query = query.Where("services @> ?", fmt.Sprintf(`["%s"]`, service))
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	var bindings []models.ProjectSecretBinding
	if err := query.Find(&bindings).Error; err != nil {
		h.logger.Error("Failed to get project secrets", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get project secrets"})
		return
	}

	// Transform response to include variable information
	var response []map[string]interface{}
	for _, binding := range bindings {
		secretData := map[string]interface{}{
			"id":           binding.ID,
			"secretId":     binding.SecretID,
			"secret":       binding.Secret,
			"variableName": binding.VariableName,
			"variableType": binding.VariableType,
			"accessLevel":  binding.AccessLevel,
			"environments": binding.Environments,
			"services":     binding.Services,
			"description":  binding.Description,
			"createdAt":    binding.CreatedAt,
			"updatedAt":    binding.UpdatedAt,
		}
		response = append(response, secretData)
	}

	c.JSON(http.StatusOK, gin.H{
		"secrets": response,
		"total":   len(response),
		"limit":   limit,
		"offset":  offset,
	})
}

// BindSecretToProject binds a secret to a project with variable configuration
func (h *Handler) BindSecretToProject(c *gin.Context) {
	projectID := c.Param("projectId")
	userID := c.GetString("user_id")
	username := c.GetString("username")

	var req BindSecretToProjectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if secret exists and user has access
	var secret models.Secret
	if err := h.db.First(&secret, "id = ?", req.SecretID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Secret not found"})
		} else {
			h.logger.Error("Failed to get secret", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get secret"})
		}
		return
	}

	// Ensure project exists in secrets service database
	var project models.Project
	if err := h.db.First(&project, "id = ?", projectID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Auto-create project reference
			project = models.Project{
				ID:          projectID,
				Name:        fmt.Sprintf("Project %s", projectID[:8]), // Use first 8 chars as name
				Description: "Auto-created project reference for secrets binding",
			}
			if err := h.db.Create(&project).Error; err != nil {
				h.logger.Error("Failed to create project reference", zap.Error(err))
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create project reference"})
				return
			}
			h.logger.Info("Auto-created project reference", zap.String("projectID", projectID))
		} else {
			h.logger.Error("Failed to check project existence", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check project existence"})
			return
		}
	}

	// Check if binding already exists
	var existingBinding models.ProjectSecretBinding
	if err := h.db.Where("project_id = ? AND secret_id = ?", projectID, req.SecretID).First(&existingBinding).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Secret is already bound to this project"})
		return
	}

	// Create binding
	binding := &models.ProjectSecretBinding{
		ID:           generateBindingID(),
		ProjectID:    projectID,
		SecretID:     req.SecretID,
		VariableName: req.VariableName,
		VariableType: req.VariableType,
		AccessLevel:  req.AccessLevel,
		Environments: req.Environments,
		Services:     req.Services,
		Description:  req.Description,
	}

	if err := h.db.Create(binding).Error; err != nil {
		h.logger.Error("Failed to bind secret to project", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to bind secret to project"})
		return
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "bind_secret",
		Resource:   "project_secret",
		ResourceID: binding.ID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusCreated,
		Success:    true,
		Metadata: map[string]interface{}{
			"projectId":    projectID,
			"secretId":     req.SecretID,
			"variableName": req.VariableName,
		},
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Secret bound to project successfully",
		zap.String("bindingID", binding.ID),
		zap.String("projectID", projectID),
		zap.String("secretID", req.SecretID),
		zap.String("userID", userID))

	c.JSON(http.StatusCreated, binding)
}

// UnbindSecretFromProject removes a secret binding from a project
func (h *Handler) UnbindSecretFromProject(c *gin.Context) {
	projectID := c.Param("projectId")
	bindingID := c.Param("bindingId")
	userID := c.GetString("user_id")
	username := c.GetString("username")

	// Get the binding
	var binding models.ProjectSecretBinding
	if err := h.db.Where("id = ? AND project_id = ?", bindingID, projectID).First(&binding).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Secret binding not found"})
		} else {
			h.logger.Error("Failed to get secret binding", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get secret binding"})
		}
		return
	}

	// Delete the binding
	if err := h.db.Delete(&binding).Error; err != nil {
		h.logger.Error("Failed to unbind secret from project", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to unbind secret from project"})
		return
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "unbind_secret",
		Resource:   "project_secret",
		ResourceID: binding.ID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusNoContent,
		Success:    true,
		Metadata: map[string]interface{}{
			"projectId": projectID,
			"secretId":  binding.SecretID,
		},
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Secret unbound from project successfully",
		zap.String("bindingID", binding.ID),
		zap.String("projectID", projectID),
		zap.String("userID", userID))

	c.Status(http.StatusNoContent)
}

// GetProjectSecretVariables returns all secret variables for a project
func (h *Handler) GetProjectSecretVariables(c *gin.Context) {
	projectID := c.Param("projectId")
	environment := c.Query("environment")
	service := c.Query("service")
	variableType := c.Query("type")

	query := h.db.Model(&models.SecretVariable{}).
		Preload("Secret").
		Where("project_id = ?", projectID)

	if environment != "" {
		query = query.Where("environment = ? OR environment = ''", environment)
	}

	if service != "" {
		query = query.Where("service = ? OR service = ''", service)
	}

	if variableType != "" {
		query = query.Where("type = ?", variableType)
	}

	var variables []models.SecretVariable
	if err := query.Find(&variables).Error; err != nil {
		h.logger.Error("Failed to get project secret variables", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get secret variables"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"variables": variables})
}

// CreateSecretVariable creates a new secret variable configuration
func (h *Handler) CreateSecretVariable(c *gin.Context) {
	projectID := c.Param("projectId")
	userID := c.GetString("user_id")
	username := c.GetString("username")

	var req CreateSecretVariableRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if secret exists and is bound to project
	var binding models.ProjectSecretBinding
	if err := h.db.Where("project_id = ? AND secret_id = ?", projectID, req.SecretID).First(&binding).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Secret is not bound to this project"})
		} else {
			h.logger.Error("Failed to check secret binding", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check secret binding"})
		}
		return
	}

	// Create variable
	variable := &models.SecretVariable{
		ID:          generateVariableID(),
		SecretID:    req.SecretID,
		ProjectID:   projectID,
		Name:        req.Name,
		Type:        req.Type,
		Path:        req.Path,
		Format:      req.Format,
		Environment: req.Environment,
		Service:     req.Service,
		Namespace:   req.Namespace,
		Transform:   req.Transform,
	}

	if err := h.db.Create(variable).Error; err != nil {
		h.logger.Error("Failed to create secret variable", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create secret variable"})
		return
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "create",
		Resource:   "secret_variable",
		ResourceID: variable.ID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusCreated,
		Success:    true,
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Secret variable created successfully",
		zap.String("variableID", variable.ID),
		zap.String("projectID", projectID),
		zap.String("name", variable.Name),
		zap.String("userID", userID))

	c.JSON(http.StatusCreated, variable)
}

// generateBindingID generates a unique binding ID
func generateBindingID() string {
	return fmt.Sprintf("bind_%d", time.Now().UnixNano())
}

// generateVariableID generates a unique variable ID
func generateVariableID() string {
	return fmt.Sprintf("var_%d", time.Now().UnixNano())
}
