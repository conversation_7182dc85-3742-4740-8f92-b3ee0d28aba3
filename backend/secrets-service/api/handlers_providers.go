package api

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/claudio/deploy-orchestrator/secrets-service/models"
)

// CreateProviderRequest represents a request to create a provider
type CreateProviderRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Type        models.ProviderType    `json:"type" binding:"required"`
	Description string                 `json:"description"`
	Config      map[string]interface{} `json:"config" binding:"required"`
	Priority    int                    `json:"priority"`
}

// UpdateProviderRequest represents a request to update a provider
type UpdateProviderRequest struct {
	Description string                 `json:"description"`
	Config      map[string]interface{} `json:"config"`
	Priority    *int                   `json:"priority"`
	IsActive    *bool                  `json:"isActive"`
}

// CreateProvider creates a new secret provider
func (h *Handler) CreateProvider(c *gin.Context) {
	var req CreateProviderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := c.GetString("user_id")
	username := c.GetString("username")

	// Get default encryption key for encrypting provider config
	var encryptionKey models.EncryptionKey
	if err := h.db.Where("is_primary = ? AND is_active = ?", true, true).First(&encryptionKey).Error; err != nil {
		h.logger.Error("Failed to get encryption key", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get encryption key"})
		return
	}

	// Encrypt provider configuration
	encryptedConfig, err := h.encryptionManager.EncryptConfig(req.Config, encryptionKey.ID)
	if err != nil {
		h.logger.Error("Failed to encrypt provider config", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to encrypt provider config"})
		return
	}

	provider := &models.Provider{
		ID:              generateProviderID(),
		Name:            req.Name,
		Type:            req.Type,
		Description:     req.Description,
		EncryptedConfig: encryptedConfig,
		KeyID:           encryptionKey.ID,
		IsActive:        true,
		Priority:        req.Priority,
		TestStatus:      "unknown",
	}

	if err := h.db.Create(provider).Error; err != nil {
		h.logger.Error("Failed to create provider", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create provider"})
		return
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "create",
		Resource:   "provider",
		ResourceID: provider.ID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusCreated,
		Success:    true,
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Provider created successfully",
		zap.String("providerID", provider.ID),
		zap.String("name", provider.Name),
		zap.String("type", string(provider.Type)),
		zap.String("userID", userID))

	// Return provider without encrypted config
	response := map[string]interface{}{
		"id":          provider.ID,
		"name":        provider.Name,
		"type":        provider.Type,
		"description": provider.Description,
		"isActive":    provider.IsActive,
		"priority":    provider.Priority,
		"testStatus":  provider.TestStatus,
		"createdAt":   provider.CreatedAt,
		"updatedAt":   provider.UpdatedAt,
	}

	c.JSON(http.StatusCreated, response)
}

// GetProvider retrieves a provider by ID
func (h *Handler) GetProvider(c *gin.Context) {
	providerID := c.Param("id")

	var provider models.Provider
	if err := h.db.First(&provider, "id = ?", providerID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		} else {
			h.logger.Error("Failed to get provider", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get provider"})
		}
		return
	}

	// Decrypt configuration for display (mask sensitive values)
	config, err := h.encryptionManager.DecryptConfig(provider.EncryptedConfig, provider.KeyID)
	if err != nil {
		h.logger.Error("Failed to decrypt provider config", zap.Error(err))
		config = map[string]interface{}{"error": "Failed to decrypt configuration"}
	} else {
		// Mask sensitive configuration values
		config = maskSensitiveConfig(config)
	}

	response := map[string]interface{}{
		"id":          provider.ID,
		"name":        provider.Name,
		"type":        provider.Type,
		"description": provider.Description,
		"config":      config,
		"isActive":    provider.IsActive,
		"priority":    provider.Priority,
		"testStatus":  provider.TestStatus,
		"testMessage": provider.TestMessage,
		"lastTested":  provider.LastTested,
		"createdAt":   provider.CreatedAt,
		"updatedAt":   provider.UpdatedAt,
	}

	c.JSON(http.StatusOK, response)
}

// ListProviders lists all providers
func (h *Handler) ListProviders(c *gin.Context) {
	providerType := c.Query("type")
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	query := h.db.Model(&models.Provider{})

	if providerType != "" {
		query = query.Where("type = ?", providerType)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	var providers []models.Provider
	if err := query.Order("priority DESC, name ASC").Find(&providers).Error; err != nil {
		h.logger.Error("Failed to list providers", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list providers"})
		return
	}

	// Return providers without encrypted config
	var response []map[string]interface{}
	for _, provider := range providers {
		response = append(response, map[string]interface{}{
			"id":          provider.ID,
			"name":        provider.Name,
			"type":        provider.Type,
			"description": provider.Description,
			"isActive":    provider.IsActive,
			"priority":    provider.Priority,
			"testStatus":  provider.TestStatus,
			"testMessage": provider.TestMessage,
			"lastTested":  provider.LastTested,
			"createdAt":   provider.CreatedAt,
			"updatedAt":   provider.UpdatedAt,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"providers": response,
		"total":     len(response),
		"limit":     limit,
		"offset":    offset,
	})
}

// UpdateProvider updates an existing provider
func (h *Handler) UpdateProvider(c *gin.Context) {
	providerID := c.Param("id")
	userID := c.GetString("user_id")
	username := c.GetString("username")

	var req UpdateProviderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var provider models.Provider
	if err := h.db.First(&provider, "id = ?", providerID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		} else {
			h.logger.Error("Failed to get provider", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get provider"})
		}
		return
	}

	// Update fields
	if req.Description != "" {
		provider.Description = req.Description
	}
	if req.Priority != nil {
		provider.Priority = *req.Priority
	}
	if req.IsActive != nil {
		provider.IsActive = *req.IsActive
	}

	// Update configuration if provided
	if req.Config != nil {
		encryptedConfig, err := h.encryptionManager.EncryptConfig(req.Config, provider.KeyID)
		if err != nil {
			h.logger.Error("Failed to encrypt provider config", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to encrypt provider config"})
			return
		}
		provider.EncryptedConfig = encryptedConfig
		// Reset test status when config changes
		provider.TestStatus = "unknown"
		provider.TestMessage = ""
		provider.LastTested = nil
	}

	if err := h.db.Save(&provider).Error; err != nil {
		h.logger.Error("Failed to update provider", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update provider"})
		return
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "update",
		Resource:   "provider",
		ResourceID: provider.ID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusOK,
		Success:    true,
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Provider updated successfully",
		zap.String("providerID", provider.ID),
		zap.String("userID", userID))

	// Return provider without encrypted config
	response := map[string]interface{}{
		"id":          provider.ID,
		"name":        provider.Name,
		"type":        provider.Type,
		"description": provider.Description,
		"isActive":    provider.IsActive,
		"priority":    provider.Priority,
		"testStatus":  provider.TestStatus,
		"testMessage": provider.TestMessage,
		"lastTested":  provider.LastTested,
		"createdAt":   provider.CreatedAt,
		"updatedAt":   provider.UpdatedAt,
	}

	c.JSON(http.StatusOK, response)
}

// DeleteProvider deletes a provider
func (h *Handler) DeleteProvider(c *gin.Context) {
	providerID := c.Param("id")
	userID := c.GetString("user_id")
	username := c.GetString("username")

	// Check if provider is being used by any secrets
	var secretCount int64
	if err := h.db.Model(&models.Secret{}).Where("provider_id = ?", providerID).Count(&secretCount).Error; err != nil {
		h.logger.Error("Failed to count secrets using provider", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check provider usage"})
		return
	}

	if secretCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete provider with existing secrets"})
		return
	}

	if err := h.db.Delete(&models.Provider{}, "id = ?", providerID).Error; err != nil {
		h.logger.Error("Failed to delete provider", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete provider"})
		return
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "delete",
		Resource:   "provider",
		ResourceID: providerID,
		UserID:     userID,
		Username:   username,
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusNoContent,
		Success:    true,
	}
	h.repository.CreateAuditLog(auditLog)

	h.logger.Info("Provider deleted successfully",
		zap.String("providerID", providerID),
		zap.String("userID", userID))

	c.Status(http.StatusNoContent)
}

// TestProvider tests a provider connection
func (h *Handler) TestProvider(c *gin.Context) {
	providerID := c.Param("id")
	userID := c.GetString("user_id")

	var provider models.Provider
	if err := h.db.First(&provider, "id = ?", providerID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Provider not found"})
		} else {
			h.logger.Error("Failed to get provider", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get provider"})
		}
		return
	}

	// Test the provider connection
	start := time.Now()
	err := h.providerManager.TestProvider(c.Request.Context(), string(provider.Type))
	duration := time.Since(start)

	// Update provider test status
	now := time.Now()
	provider.LastTested = &now

	if err != nil {
		provider.TestStatus = "failed"
		provider.TestMessage = err.Error()
		h.logger.Error("Provider test failed",
			zap.String("providerID", providerID),
			zap.Error(err))
	} else {
		provider.TestStatus = "passed"
		provider.TestMessage = "Connection successful"
		h.logger.Info("Provider test passed",
			zap.String("providerID", providerID),
			zap.Duration("duration", duration))
	}

	// Save test results
	if saveErr := h.db.Save(&provider).Error; saveErr != nil {
		h.logger.Error("Failed to save provider test results", zap.Error(saveErr))
	}

	// Create audit log
	auditLog := &models.AuditLog{
		ID:         generateID(),
		Action:     "test",
		Resource:   "provider",
		ResourceID: provider.ID,
		UserID:     userID,
		Username:   c.GetString("username"),
		IPAddress:  c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Method:     c.Request.Method,
		Path:       c.Request.URL.Path,
		Status:     http.StatusOK,
		Success:    err == nil,
		Duration:   duration.Microseconds(),
	}
	if err != nil {
		auditLog.Error = err.Error()
	}
	h.repository.CreateAuditLog(auditLog)

	response := map[string]interface{}{
		"providerID":  provider.ID,
		"testStatus":  provider.TestStatus,
		"testMessage": provider.TestMessage,
		"lastTested":  provider.LastTested,
		"duration":    duration.String(),
	}

	if err != nil {
		c.JSON(http.StatusBadRequest, response)
	} else {
		c.JSON(http.StatusOK, response)
	}
}

// maskSensitiveConfig masks sensitive values in provider configuration
func maskSensitiveConfig(config map[string]interface{}) map[string]interface{} {
	sensitiveKeys := []string{
		"password", "secret", "key", "token", "api_key", "apikey",
		"client_secret", "private_key", "credentials", "auth",
	}

	masked := make(map[string]interface{})
	for k, v := range config {
		masked[k] = v
		for _, sensitiveKey := range sensitiveKeys {
			if k == sensitiveKey ||
				(len(k) > len(sensitiveKey) &&
					(k[len(k)-len(sensitiveKey):] == sensitiveKey ||
						k[:len(sensitiveKey)] == sensitiveKey)) {
				if str, ok := v.(string); ok && str != "" {
					masked[k] = "***MASKED***"
				}
				break
			}
		}
	}

	return masked
}

// generateProviderID generates a unique provider ID
func generateProviderID() string {
	return fmt.Sprintf("prov_%d", time.Now().UnixNano())
}

// GetProviderTypes returns available provider types
func (h *Handler) GetProviderTypes(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"types": h.config.Providers.Types,
		"total": len(h.config.Providers.Types),
	})
}
