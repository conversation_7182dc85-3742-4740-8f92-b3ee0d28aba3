package api

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/claudio/deploy-orchestrator/secrets-service/models"
)

// DeploymentSecretsRequest represents a request for deployment secrets
type DeploymentSecretsRequest struct {
	DeploymentID string `json:"deploymentId" binding:"required"`
	ProjectID    string `json:"projectId" binding:"required"`
	Environment  string `json:"environment" binding:"required"`
	Service      string `json:"service"`
	Namespace    string `json:"namespace"`
}

// WorkflowSecretsRequest represents a request for workflow secrets
type WorkflowSecretsRequest struct {
	WorkflowID    string            `json:"workflowId" binding:"required"`
	ExecutionID   string            `json:"executionId" binding:"required"`
	ProjectID     string            `json:"projectId" binding:"required"`
	Environment   string            `json:"environment"`
	Service       string            `json:"service"`
	StepName      string            `json:"stepName"`
	SecretMapping map[string]string `json:"secretMapping"` // templateVar -> userSecretName
}

// SecretVariableResponse represents a secret variable in deployment/workflow context
type SecretVariableResponse struct {
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Value       string                 `json:"value,omitempty"`
	Path        string                 `json:"path,omitempty"`
	Format      string                 `json:"format,omitempty"`
	Environment string                 `json:"environment,omitempty"`
	Service     string                 `json:"service,omitempty"`
	Namespace   string                 `json:"namespace,omitempty"`
	Transform   map[string]interface{} `json:"transform,omitempty"`
	SecretID    string                 `json:"secretId"`
	VariableID  string                 `json:"variableId"`
}

// GetDeploymentSecrets retrieves all secrets for a deployment
func (h *Handler) GetDeploymentSecrets(c *gin.Context) {
	var req DeploymentSecretsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := c.GetString("user_id")
	username := c.GetString("username")

	// Get all secret variables for the project that match the deployment context
	query := h.db.Model(&models.SecretVariable{}).
		Preload("Secret").
		Where("project_id = ?", req.ProjectID)

	// Filter by environment (exact match or empty for global)
	if req.Environment != "" {
		query = query.Where("environment = ? OR environment = ''", req.Environment)
	}

	// Filter by service (exact match or empty for global)
	if req.Service != "" {
		query = query.Where("service = ? OR service = ''", req.Service)
	}

	// Filter by namespace (exact match or empty for global)
	if req.Namespace != "" {
		query = query.Where("namespace = ? OR namespace = ''", req.Namespace)
	}

	var variables []models.SecretVariable
	if err := query.Find(&variables).Error; err != nil {
		h.logger.Error("Failed to get deployment secrets", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get deployment secrets"})
		return
	}

	// Process variables and decrypt secrets
	var response []SecretVariableResponse
	var errors []string

	for _, variable := range variables {
		// Check if secret is active and not expired
		if variable.Secret.Status != models.SecretStatusActive {
			errors = append(errors, fmt.Sprintf("Secret %s is not active", variable.Secret.Name))
			continue
		}

		if variable.Secret.ExpiresAt != nil && time.Now().After(*variable.Secret.ExpiresAt) {
			errors = append(errors, fmt.Sprintf("Secret %s has expired", variable.Secret.Name))
			continue
		}

		// Get active secret version
		version, err := h.repository.GetActiveSecretVersion(variable.SecretID)
		if err != nil {
			h.logger.Error("Failed to get active secret version",
				zap.String("secretID", variable.SecretID),
				zap.Error(err))
			errors = append(errors, fmt.Sprintf("Failed to get version for secret %s", variable.Secret.Name))
			continue
		}

		// Decrypt secret value
		decryptedValue, err := h.encryptionManager.DecryptSecret(version.EncryptedValue, version.KeyID)
		if err != nil {
			h.logger.Error("Failed to decrypt secret",
				zap.String("secretID", variable.SecretID),
				zap.Error(err))
			errors = append(errors, fmt.Sprintf("Failed to decrypt secret %s", variable.Secret.Name))
			continue
		}

		// Apply transformations if specified
		finalValue := decryptedValue
		if variable.Transform != nil {
			finalValue = h.applyTransformations(decryptedValue, variable.Transform)
		}

		// Create response
		varResponse := SecretVariableResponse{
			Name:        variable.Name,
			Type:        variable.Type,
			Value:       finalValue,
			Path:        variable.Path,
			Format:      variable.Format,
			Environment: variable.Environment,
			Service:     variable.Service,
			Namespace:   variable.Namespace,
			Transform:   variable.Transform,
			SecretID:    variable.SecretID,
			VariableID:  variable.ID,
		}

		// For file type, don't include value in response for security
		if variable.Type == "file" || variable.Type == "mount" {
			varResponse.Value = "" // Value will be written to file system
		}

		response = append(response, varResponse)

		// Create usage tracking
		usage := &models.DeploymentSecretUsage{
			ID:           generateUsageID(),
			DeploymentID: req.DeploymentID,
			SecretID:     variable.SecretID,
			VariableID:   variable.ID,
			UsedAt:       time.Now(),
			UsedBy:       userID,
			Environment:  req.Environment,
			Service:      req.Service,
			Status:       "success",
		}
		h.db.Create(usage)

		// Create access log
		accessLog := &models.AccessLog{
			ID:        generateID(),
			SecretID:  variable.SecretID,
			UserID:    userID,
			Username:  username,
			ServiceID: req.Service,
			IPAddress: c.ClientIP(),
			UserAgent: c.GetHeader("User-Agent"),
			Method:    c.Request.Method,
			Success:   true,
			Version:   version.Version,
			Metadata: map[string]interface{}{
				"deploymentId": req.DeploymentID,
				"variableId":   variable.ID,
				"variableName": variable.Name,
			},
		}
		h.repository.CreateAccessLog(accessLog)
	}

	h.logger.Info("Deployment secrets retrieved",
		zap.String("deploymentID", req.DeploymentID),
		zap.String("projectID", req.ProjectID),
		zap.String("environment", req.Environment),
		zap.Int("secretCount", len(response)),
		zap.Int("errorCount", len(errors)),
		zap.String("userID", userID))

	responseData := gin.H{
		"variables": response,
		"errors":    errors,
		"metadata": gin.H{
			"deploymentId": req.DeploymentID,
			"projectId":    req.ProjectID,
			"environment":  req.Environment,
			"service":      req.Service,
			"namespace":    req.Namespace,
			"retrievedAt":  time.Now(),
		},
	}

	c.JSON(http.StatusOK, responseData)
}

// GetWorkflowSecrets retrieves all secrets for a workflow execution
func (h *Handler) GetWorkflowSecrets(c *gin.Context) {
	var req WorkflowSecretsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := c.GetString("user_id")
	username := c.GetString("username")

	// Get all secret variables for the project that match the workflow context
	// NOTE: User access is controlled by PROJECT permissions, not individual secret ownership
	query := h.db.Model(&models.SecretVariable{}).
		Preload("Secret").
		Where("project_id = ?", req.ProjectID)

	// Filter by environment if specified
	if req.Environment != "" {
		query = query.Where("environment = ? OR environment = ''", req.Environment)
	}

	// Filter by service if specified
	if req.Service != "" {
		query = query.Where("service = ? OR service = ''", req.Service)
	}

	// If secret mapping is provided, filter by mapped secret names within the project
	if len(req.SecretMapping) > 0 {
		// Get the project's secret names that are mapped to template variables
		var projectSecretNames []string
		for _, projectSecretName := range req.SecretMapping {
			projectSecretNames = append(projectSecretNames, projectSecretName)
		}

		// Join with secrets table to filter by secret names within this project
		query = query.Joins("JOIN secrets ON secret_variables.secret_id = secrets.id").
			Where("secrets.name IN ?", projectSecretNames)
	}

	var variables []models.SecretVariable
	if err := query.Find(&variables).Error; err != nil {
		h.logger.Error("Failed to get workflow secrets", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get workflow secrets"})
		return
	}

	// Process variables similar to deployment secrets
	var response []SecretVariableResponse
	var errors []string

	for _, variable := range variables {
		// Check secret status and expiration
		if variable.Secret.Status != models.SecretStatusActive {
			errors = append(errors, fmt.Sprintf("Secret %s is not active", variable.Secret.Name))
			continue
		}

		if variable.Secret.ExpiresAt != nil && time.Now().After(*variable.Secret.ExpiresAt) {
			errors = append(errors, fmt.Sprintf("Secret %s has expired", variable.Secret.Name))
			continue
		}

		// Get and decrypt secret
		version, err := h.repository.GetActiveSecretVersion(variable.SecretID)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to get version for secret %s", variable.Secret.Name))
			continue
		}

		decryptedValue, err := h.encryptionManager.DecryptSecret(version.EncryptedValue, version.KeyID)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to decrypt secret %s", variable.Secret.Name))
			continue
		}

		// Apply transformations
		finalValue := decryptedValue
		if variable.Transform != nil {
			finalValue = h.applyTransformations(decryptedValue, variable.Transform)
		}

		// Determine the variable name to use in response
		variableName := variable.Name

		// If secret mapping is provided, map the secret name back to template variable name
		if len(req.SecretMapping) > 0 {
			for templateVar, userSecretName := range req.SecretMapping {
				if variable.Secret.Name == userSecretName {
					variableName = templateVar
					break
				}
			}
		}

		response = append(response, SecretVariableResponse{
			Name:        variableName,
			Type:        variable.Type,
			Value:       finalValue,
			Path:        variable.Path,
			Format:      variable.Format,
			Environment: variable.Environment,
			Service:     variable.Service,
			Namespace:   variable.Namespace,
			Transform:   variable.Transform,
			SecretID:    variable.SecretID,
			VariableID:  variable.ID,
		})

		// Create workflow usage tracking
		usage := &models.WorkflowSecretUsage{
			ID:          generateUsageID(),
			WorkflowID:  req.WorkflowID,
			ExecutionID: req.ExecutionID,
			SecretID:    variable.SecretID,
			VariableID:  variable.ID,
			StepName:    req.StepName,
			UsedAt:      time.Now(),
			UsedBy:      userID,
			Status:      "success",
		}
		h.db.Create(usage)

		// Create access log
		accessLog := &models.AccessLog{
			ID:        generateID(),
			SecretID:  variable.SecretID,
			UserID:    userID,
			Username:  username,
			ServiceID: req.Service,
			IPAddress: c.ClientIP(),
			UserAgent: c.GetHeader("User-Agent"),
			Method:    c.Request.Method,
			Success:   true,
			Version:   version.Version,
			Metadata: map[string]interface{}{
				"workflowId":  req.WorkflowID,
				"executionId": req.ExecutionID,
				"stepName":    req.StepName,
				"variableId":  variable.ID,
			},
		}
		h.repository.CreateAccessLog(accessLog)
	}

	h.logger.Info("Workflow secrets retrieved",
		zap.String("workflowID", req.WorkflowID),
		zap.String("executionID", req.ExecutionID),
		zap.String("projectID", req.ProjectID),
		zap.Int("secretCount", len(response)),
		zap.String("userID", userID))

	responseData := gin.H{
		"variables": response,
		"errors":    errors,
		"metadata": gin.H{
			"workflowId":  req.WorkflowID,
			"executionId": req.ExecutionID,
			"projectId":   req.ProjectID,
			"stepName":    req.StepName,
			"retrievedAt": time.Now(),
		},
	}

	c.JSON(http.StatusOK, responseData)
}

// applyTransformations applies transformation rules to secret values
func (h *Handler) applyTransformations(value string, transforms map[string]interface{}) string {
	// This is a simplified implementation
	// In production, you would implement more sophisticated transformations

	if encoding, ok := transforms["encoding"].(string); ok {
		switch encoding {
		case "base64":
			// Base64 encode the value
			// return base64.StdEncoding.EncodeToString([]byte(value))
		case "url":
			// URL encode the value
			// return url.QueryEscape(value)
		}
	}

	if prefix, ok := transforms["prefix"].(string); ok {
		value = prefix + value
	}

	if suffix, ok := transforms["suffix"].(string); ok {
		value = value + suffix
	}

	return value
}

// generateUsageID generates a unique usage ID
func generateUsageID() string {
	return fmt.Sprintf("usage_%d", time.Now().UnixNano())
}
