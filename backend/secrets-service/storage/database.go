package storage

import (
	"fmt"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"github.com/claudio/deploy-orchestrator/secrets-service/config"
	"github.com/claudio/deploy-orchestrator/secrets-service/models"
	"github.com/claudio/deploy-orchestrator/shared/db"
)

// InitDatabase initializes the database connection using shared utilities
func InitDatabase(cfg *config.Config) (*gorm.DB, error) {
	// Use shared database initialization
	dbConfig := db.Config{
		URL:           cfg.Database.URL,
		MaxRetries:    cfg.Database.MaxRetries,
		RetryInterval: time.Duration(cfg.Database.RetryInterval) * time.Second,
		LogLevel:      cfg.Database.LogLevel,
	}

	database, err := db.Connect(dbConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize database: %v", err)
	}

	// Run migrations
	if err := autoMigrate(database); err != nil {
		return nil, fmt.Errorf("failed to run migrations: %v", err)
	}

	// Create indexes (non-critical, log errors but don't fail)
	if err := createIndexes(database); err != nil {
		// Log the error but don't fail the startup
		fmt.Printf("Warning: failed to create some indexes: %v\n", err)
	}

	return database, nil
}

// NewDatabase creates a new database connection (legacy function)
func NewDatabase(databaseURL string) (*gorm.DB, error) {
	// Configure GORM
	config := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}

	// Connect to database
	db, err := gorm.Open(postgres.Open(databaseURL), config)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Set connection pool settings
	sqlDB.SetMaxOpenConns(25)
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetConnMaxLifetime(5 * time.Minute)

	// Auto-migrate the schema
	if err := autoMigrate(db); err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	return db, nil
}

// autoMigrate runs database migrations
func autoMigrate(db *gorm.DB) error {
	// Define the order of migrations to handle foreign key dependencies
	models := []any{
		&models.SecretScope{},
		&models.RotationPolicy{},
		&models.Provider{},
		&models.EncryptionKey{},
		&models.Project{},
		&models.Secret{},
		&models.SecretVersion{},
		&models.ProjectSecretBinding{},
		&models.SecretVariable{},
		&models.DeploymentSecretUsage{},
		&models.WorkflowSecretUsage{},
		&models.RotationHistory{},
		&models.AuditLog{},
		&models.AccessLog{},
	}

	for _, model := range models {
		if err := db.AutoMigrate(model); err != nil {
			return fmt.Errorf("failed to migrate %T: %w", model, err)
		}
	}

	// Insert default data
	if err := insertDefaultData(db); err != nil {
		return fmt.Errorf("failed to insert default data: %w", err)
	}

	return nil
}

// createIndexes creates additional database indexes for performance
func createIndexes(db *gorm.DB) error {
	// Helper function to check if table exists before creating indexes
	tableExists := func(tableName string) bool {
		var count int64
		db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = ?", tableName).Scan(&count)
		return count > 0
	}

	// Secrets table indexes
	if tableExists("secrets") {
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_secrets_provider ON secrets(provider)").Error; err != nil {
			return err
		}
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_secrets_status ON secrets(status)").Error; err != nil {
			return err
		}
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_secrets_next_rotation ON secrets(next_rotation) WHERE next_rotation IS NOT NULL").Error; err != nil {
			return err
		}
	}

	// Secret versions table indexes
	if tableExists("secret_versions") {
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_secret_versions_secret_version ON secret_versions(secret_id, version)").Error; err != nil {
			return err
		}
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_secret_versions_active ON secret_versions(secret_id, is_active) WHERE is_active = true").Error; err != nil {
			return err
		}
	}

	// Rotation history table indexes
	if tableExists("rotation_histories") {
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_rotation_history_secret_started ON rotation_histories(secret_id, started_at)").Error; err != nil {
			return err
		}
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_rotation_history_status ON rotation_histories(status)").Error; err != nil {
			return err
		}
	}

	// Audit logs table indexes
	if tableExists("audit_logs") {
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_audit_logs_user_action ON audit_logs(user_id, action)").Error; err != nil {
			return err
		}
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON audit_logs(resource, resource_id)").Error; err != nil {
			return err
		}
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at)").Error; err != nil {
			return err
		}
	}

	// Access logs table indexes
	if tableExists("access_logs") {
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_access_logs_secret_user ON access_logs(secret_id, user_id)").Error; err != nil {
			return err
		}
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_access_logs_created_at ON access_logs(created_at)").Error; err != nil {
			return err
		}
	}

	// Project secret bindings indexes
	if tableExists("project_secret_bindings") {
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_project_secret_bindings_project ON project_secret_bindings(project_id)").Error; err != nil {
			return err
		}
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_project_secret_bindings_secret ON project_secret_bindings(secret_id)").Error; err != nil {
			return err
		}
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_project_secret_bindings_variable_name ON project_secret_bindings(variable_name)").Error; err != nil {
			return err
		}
	}

	// Secret variables indexes
	if tableExists("secret_variables") {
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_secret_variables_project_secret ON secret_variables(project_id, secret_id)").Error; err != nil {
			return err
		}
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_secret_variables_name ON secret_variables(name)").Error; err != nil {
			return err
		}
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_secret_variables_environment ON secret_variables(environment) WHERE environment != ''").Error; err != nil {
			return err
		}
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_secret_variables_service ON secret_variables(service) WHERE service != ''").Error; err != nil {
			return err
		}
	}

	// Deployment secret usage indexes
	if tableExists("deployment_secret_usages") {
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_deployment_secret_usage_deployment ON deployment_secret_usages(deployment_id)").Error; err != nil {
			return err
		}
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_deployment_secret_usage_secret ON deployment_secret_usages(secret_id)").Error; err != nil {
			return err
		}
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_deployment_secret_usage_used_at ON deployment_secret_usages(used_at)").Error; err != nil {
			return err
		}
	}

	// Workflow secret usage indexes
	if tableExists("workflow_secret_usages") {
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_workflow_secret_usage_workflow ON workflow_secret_usages(workflow_id)").Error; err != nil {
			return err
		}
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_workflow_secret_usage_execution ON workflow_secret_usages(execution_id)").Error; err != nil {
			return err
		}
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_workflow_secret_usage_secret ON workflow_secret_usages(secret_id)").Error; err != nil {
			return err
		}
	}

	return nil
}

// insertDefaultData inserts default data into the database
func insertDefaultData(db *gorm.DB) error {
	// Create default secret scope
	var defaultScope models.SecretScope
	if err := db.Where("name = ?", "default").First(&defaultScope).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			defaultScope = models.SecretScope{
				ID:          "scope_default",
				Name:        "default",
				Type:        models.ScopeTypeEnvironment,
				Description: "Default scope for secrets",
				IsActive:    true,
			}
			if err := db.Create(&defaultScope).Error; err != nil {
				return fmt.Errorf("failed to create default scope: %w", err)
			}
		} else {
			return fmt.Errorf("failed to check for default scope: %w", err)
		}
	}

	// Create default rotation policy
	var defaultPolicy models.RotationPolicy
	if err := db.Where("name = ?", "default-30d").First(&defaultPolicy).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			defaultPolicy = models.RotationPolicy{
				ID:              "policy_default_30d",
				Name:            "default-30d",
				Description:     "Default 30-day rotation policy",
				Interval:        "720h",  // 30 days
				MaxAge:          "2160h", // 90 days
				GracePeriod:     "24h",   // 1 day
				Strategy:        models.RotationStrategyReplace,
				NotifyOnFailure: true,
				NotifyOnSuccess: false,
				IsActive:        true,
			}
			if err := db.Create(&defaultPolicy).Error; err != nil {
				return fmt.Errorf("failed to create default rotation policy: %w", err)
			}
		} else {
			return fmt.Errorf("failed to check for default rotation policy: %w", err)
		}
	}

	// Create default encryption key
	var defaultKey models.EncryptionKey
	if err := db.Where("name = ?", "default").First(&defaultKey).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// In a real implementation, you would generate a proper encryption key
			defaultKey = models.EncryptionKey{
				ID:           "key_default",
				Name:         "default",
				Algorithm:    "AES-256-GCM",
				KeySize:      32,
				Purpose:      "secret-encryption",
				EncryptedKey: "placeholder-encrypted-key", // This should be properly generated
				Salt:         "placeholder-salt",          // This should be properly generated
				IsActive:     true,
				IsPrimary:    true,
				UsageCount:   0,
			}
			if err := db.Create(&defaultKey).Error; err != nil {
				return fmt.Errorf("failed to create default encryption key: %w", err)
			}
		} else {
			return fmt.Errorf("failed to check for default encryption key: %w", err)
		}
	}

	return nil
}

// Repository provides database operations for secrets management
type Repository struct {
	db *gorm.DB
}

// NewRepository creates a new repository
func NewRepository(db *gorm.DB) *Repository {
	return &Repository{db: db}
}

// GetSecret retrieves a secret by ID
func (r *Repository) GetSecret(id string) (*models.Secret, error) {
	var secret models.Secret
	if err := r.db.Preload("Scope").Preload("RotationPolicy").First(&secret, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &secret, nil
}

// GetSecretByName retrieves a secret by name and scope
func (r *Repository) GetSecretByName(name, scopeID string) (*models.Secret, error) {
	var secret models.Secret
	if err := r.db.Preload("Scope").Preload("RotationPolicy").
		Where("name = ? AND scope_id = ?", name, scopeID).First(&secret).Error; err != nil {
		return nil, err
	}
	return &secret, nil
}

// CreateSecret creates a new secret
func (r *Repository) CreateSecret(secret *models.Secret) error {
	return r.db.Create(secret).Error
}

// UpdateSecret updates an existing secret
func (r *Repository) UpdateSecret(secret *models.Secret) error {
	return r.db.Save(secret).Error
}

// DeleteSecret deletes a secret
func (r *Repository) DeleteSecret(id string) error {
	return r.db.Delete(&models.Secret{}, "id = ?", id).Error
}

// ListSecrets lists secrets with optional filtering
func (r *Repository) ListSecrets(scopeID string, limit, offset int) ([]models.Secret, error) {
	var secrets []models.Secret
	query := r.db.Preload("Scope").Preload("RotationPolicy")

	if scopeID != "" {
		query = query.Where("scope_id = ?", scopeID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&secrets).Error; err != nil {
		return nil, err
	}

	return secrets, nil
}

// GetActiveSecretVersion retrieves the active version of a secret
func (r *Repository) GetActiveSecretVersion(secretID string) (*models.SecretVersion, error) {
	var version models.SecretVersion
	if err := r.db.Where("secret_id = ? AND is_active = ?", secretID, true).First(&version).Error; err != nil {
		return nil, err
	}
	return &version, nil
}

// CreateSecretVersion creates a new secret version
func (r *Repository) CreateSecretVersion(version *models.SecretVersion) error {
	return r.db.Create(version).Error
}

// GetSecretVersions retrieves all versions of a secret
func (r *Repository) GetSecretVersions(secretID string) ([]models.SecretVersion, error) {
	var versions []models.SecretVersion
	if err := r.db.Where("secret_id = ?", secretID).Order("version DESC").Find(&versions).Error; err != nil {
		return nil, err
	}
	return versions, nil
}

// CreateAuditLog creates an audit log entry
func (r *Repository) CreateAuditLog(log *models.AuditLog) error {
	return r.db.Create(log).Error
}

// CreateAccessLog creates an access log entry
func (r *Repository) CreateAccessLog(log *models.AccessLog) error {
	return r.db.Create(log).Error
}

// GetAuditLogs retrieves audit logs with optional filtering
func (r *Repository) GetAuditLogs(userID, resource string, limit, offset int) ([]models.AuditLog, error) {
	var logs []models.AuditLog
	query := r.db.Model(&models.AuditLog{})

	if userID != "" {
		query = query.Where("user_id = ?", userID)
	}

	if resource != "" {
		query = query.Where("resource = ?", resource)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Order("created_at DESC").Find(&logs).Error; err != nil {
		return nil, err
	}

	return logs, nil
}

// GetAccessLogs retrieves access logs with optional filtering
func (r *Repository) GetAccessLogs(secretID, userID string, limit, offset int) ([]models.AccessLog, error) {
	var logs []models.AccessLog
	query := r.db.Model(&models.AccessLog{}).Preload("Secret")

	if secretID != "" {
		query = query.Where("secret_id = ?", secretID)
	}

	if userID != "" {
		query = query.Where("user_id = ?", userID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Order("created_at DESC").Find(&logs).Error; err != nil {
		return nil, err
	}

	return logs, nil
}
