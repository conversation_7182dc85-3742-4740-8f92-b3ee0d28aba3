package security

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/claudio/deploy-orchestrator/secrets-service/api"
	"github.com/claudio/deploy-orchestrator/secrets-service/models"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type SecretIsolationTestSuite struct {
	suite.Suite
	db     *gorm.DB
	router *gin.Engine
}

func (suite *SecretIsolationTestSuite) SetupSuite() {
	// Setup in-memory database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Auto-migrate models
	err = db.AutoMigrate(
		&models.Secret{},
		&models.SecretVariable{},
		&models.SecretVersion{},
		&models.AccessLog{},
		&models.WorkflowSecretUsage{},
	)
	suite.Require().NoError(err)

	suite.db = db

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()
}

func (suite *SecretIsolationTestSuite) SetupTest() {
	// Clean database before each test
	suite.db.Exec("DELETE FROM secrets")
	suite.db.Exec("DELETE FROM secret_variables")
	suite.db.Exec("DELETE FROM secret_versions")
	suite.db.Exec("DELETE FROM access_logs")
	suite.db.Exec("DELETE FROM workflow_secret_usages")

	// Create test data
	suite.createTestData()
}

func (suite *SecretIsolationTestSuite) createTestData() {
	// Create secrets for different projects
	secrets := []models.Secret{
		{
			ID:          "secret_project_a_1",
			Name:        "PROJECT_A_SECRET_1",
			Description: "Secret for Project A",
			Type:        "password",
			Status:      models.SecretStatusActive,
			ScopeID:     "project_a_scope",
		},
		{
			ID:          "secret_project_a_2",
			Name:        "PROJECT_A_SECRET_2",
			Description: "Another secret for Project A",
			Type:        "api_key",
			Status:      models.SecretStatusActive,
			ScopeID:     "project_a_scope",
		},
		{
			ID:          "secret_project_b_1",
			Name:        "PROJECT_B_SECRET_1",
			Description: "Secret for Project B",
			Type:        "password",
			Status:      models.SecretStatusActive,
			ScopeID:     "project_b_scope",
		},
		{
			ID:          "secret_project_b_2",
			Name:        "PROJECT_B_SECRET_2",
			Description: "Another secret for Project B",
			Type:        "private_key",
			Status:      models.SecretStatusActive,
			ScopeID:     "project_b_scope",
		},
	}

	for _, secret := range secrets {
		suite.db.Create(&secret)
	}

	// Create secret variables (project bindings)
	variables := []models.SecretVariable{
		{
			ID:        "var_project_a_1",
			SecretID:  "secret_project_a_1",
			ProjectID: "project_a",
			Name:      "PROJECT_A_SECRET_1",
			Type:      "env",
		},
		{
			ID:        "var_project_a_2",
			SecretID:  "secret_project_a_2",
			ProjectID: "project_a",
			Name:      "PROJECT_A_SECRET_2",
			Type:      "env",
		},
		{
			ID:        "var_project_b_1",
			SecretID:  "secret_project_b_1",
			ProjectID: "project_b",
			Name:      "PROJECT_B_SECRET_1",
			Type:      "env",
		},
		{
			ID:        "var_project_b_2",
			SecretID:  "secret_project_b_2",
			ProjectID: "project_b",
			Name:      "PROJECT_B_SECRET_2",
			Type:      "env",
		},
	}

	for _, variable := range variables {
		suite.db.Create(&variable)
	}
}

func (suite *SecretIsolationTestSuite) TestProjectSecretIsolation() {
	// Test that users can only access secrets from their authorized projects
	tests := []struct {
		name            string
		userProjects    []string
		requestProject  string
		expectedSecrets []string
		shouldSucceed   bool
	}{
		{
			name:            "User with Project A access requests Project A secrets",
			userProjects:    []string{"project_a"},
			requestProject:  "project_a",
			expectedSecrets: []string{"PROJECT_A_SECRET_1", "PROJECT_A_SECRET_2"},
			shouldSucceed:   true,
		},
		{
			name:            "User with Project B access requests Project B secrets",
			userProjects:    []string{"project_b"},
			requestProject:  "project_b",
			expectedSecrets: []string{"PROJECT_B_SECRET_1", "PROJECT_B_SECRET_2"},
			shouldSucceed:   true,
		},
		{
			name:            "User with Project A access tries to access Project B secrets",
			userProjects:    []string{"project_a"},
			requestProject:  "project_b",
			expectedSecrets: []string{},
			shouldSucceed:   false,
		},
		{
			name:            "User with both projects can access both",
			userProjects:    []string{"project_a", "project_b"},
			requestProject:  "project_a",
			expectedSecrets: []string{"PROJECT_A_SECRET_1", "PROJECT_A_SECRET_2"},
			shouldSucceed:   true,
		},
		{
			name:            "User with no projects cannot access any secrets",
			userProjects:    []string{},
			requestProject:  "project_a",
			expectedSecrets: []string{},
			shouldSucceed:   false,
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			// Mock authorization middleware
			suite.router.Use(func(c *gin.Context) {
				c.Set("user_id", "test_user")
				c.Set("user_projects", tt.userProjects)
				c.Next()
			})

			// Create request for workflow secrets
			request := map[string]interface{}{
				"workflowId":  "test_workflow",
				"executionId": "test_execution",
				"projectId":   tt.requestProject,
			}

			jsonData, _ := json.Marshal(request)
			req, _ := http.NewRequest("POST", "/api/v1/integration/workflow/secrets", bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("Authorization", "Bearer test-token")

			w := httptest.NewRecorder()
			suite.router.ServeHTTP(w, req)

			if tt.shouldSucceed {
				assert.Equal(suite.T(), http.StatusOK, w.Code)

				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				suite.Require().NoError(err)

				variables := response["variables"].([]interface{})
				assert.Equal(suite.T(), len(tt.expectedSecrets), len(variables))

				for i, expectedSecret := range tt.expectedSecrets {
					variable := variables[i].(map[string]interface{})
					assert.Equal(suite.T(), expectedSecret, variable["name"])
				}
			} else {
				assert.Equal(suite.T(), http.StatusForbidden, w.Code)
			}
		})
	}
}

func (suite *SecretIsolationTestSuite) TestSecretMappingIsolation() {
	// Test that secret mapping only works within project boundaries
	tests := []struct {
		name           string
		userProject    string
		secretMapping  map[string]string
		expectedResult map[string]string
		shouldSucceed  bool
	}{
		{
			name:        "Valid mapping within same project",
			userProject: "project_a",
			secretMapping: map[string]string{
				"SSH_USERNAME": "PROJECT_A_SECRET_1",
				"SSH_PASSWORD": "PROJECT_A_SECRET_2",
			},
			expectedResult: map[string]string{
				"SSH_USERNAME": "PROJECT_A_SECRET_1",
				"SSH_PASSWORD": "PROJECT_A_SECRET_2",
			},
			shouldSucceed: true,
		},
		{
			name:        "Invalid mapping to different project's secrets",
			userProject: "project_a",
			secretMapping: map[string]string{
				"SSH_USERNAME": "PROJECT_B_SECRET_1", // Trying to access Project B secret
			},
			expectedResult: map[string]string{},
			shouldSucceed:  false,
		},
		{
			name:        "Mixed mapping - some valid, some invalid",
			userProject: "project_a",
			secretMapping: map[string]string{
				"SSH_USERNAME": "PROJECT_A_SECRET_1", // Valid
				"SSH_PASSWORD": "PROJECT_B_SECRET_1", // Invalid
			},
			expectedResult: map[string]string{
				"SSH_USERNAME": "PROJECT_A_SECRET_1", // Only valid mapping should work
			},
			shouldSucceed: true, // Partial success
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			// Mock authorization middleware
			suite.router.Use(func(c *gin.Context) {
				c.Set("user_id", "test_user")
				c.Set("user_projects", []string{tt.userProject})
				c.Next()
			})

			request := map[string]interface{}{
				"workflowId":    "test_workflow",
				"executionId":   "test_execution",
				"projectId":     tt.userProject,
				"secretMapping": tt.secretMapping,
			}

			jsonData, _ := json.Marshal(request)
			req, _ := http.NewRequest("POST", "/api/v1/integration/workflow/secrets", bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("Authorization", "Bearer test-token")

			w := httptest.NewRecorder()
			suite.router.ServeHTTP(w, req)

			if tt.shouldSucceed {
				assert.Equal(suite.T(), http.StatusOK, w.Code)

				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				suite.Require().NoError(err)

				variables := response["variables"].([]interface{})

				// Verify only expected secrets are returned
				returnedSecrets := make(map[string]string)
				for _, v := range variables {
					variable := v.(map[string]interface{})
					returnedSecrets[variable["name"].(string)] = variable["name"].(string)
				}

				for expectedName := range tt.expectedResult {
					assert.Contains(suite.T(), returnedSecrets, expectedName)
				}
			} else {
				// Should either return forbidden or empty results
				assert.True(suite.T(), w.Code == http.StatusForbidden || w.Code == http.StatusOK)

				if w.Code == http.StatusOK {
					var response map[string]interface{}
					err := json.Unmarshal(w.Body.Bytes(), &response)
					suite.Require().NoError(err)

					variables := response["variables"].([]interface{})
					assert.Equal(suite.T(), 0, len(variables))
				}
			}
		})
	}
}

func (suite *SecretIsolationTestSuite) TestCrossProjectSecretAccess() {
	// Test that secrets from one project cannot be accessed by users of another project

	// User A tries to access Project B secrets through secret mapping
	suite.router.Use(func(c *gin.Context) {
		c.Set("user_id", "user_a")
		c.Set("user_projects", []string{"project_a"})
		c.Next()
	})

	// Attempt to map to Project B secrets
	request := map[string]interface{}{
		"workflowId":  "test_workflow",
		"executionId": "test_execution",
		"projectId":   "project_a", // User's valid project
		"secretMapping": map[string]string{
			"MALICIOUS_SECRET": "PROJECT_B_SECRET_1", // Trying to access Project B secret
		},
	}

	jsonData, _ := json.Marshal(request)
	req, _ := http.NewRequest("POST", "/api/v1/integration/workflow/secrets", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer test-token")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Should succeed but return no secrets for the invalid mapping
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.Require().NoError(err)

	variables := response["variables"].([]interface{})

	// Should not return any secrets for the malicious mapping
	for _, v := range variables {
		variable := v.(map[string]interface{})
		assert.NotEqual(suite.T(), "MALICIOUS_SECRET", variable["name"])
		assert.NotEqual(suite.T(), "PROJECT_B_SECRET_1", variable["name"])
	}

	// Verify access is logged for audit
	var accessLogs []models.AccessLog
	suite.db.Find(&accessLogs, "user_id = ?", "user_a")
	assert.Greater(suite.T(), len(accessLogs), 0)
}

func (suite *SecretIsolationTestSuite) TestSecretMappingAuditLogging() {
	// Test that all secret mapping operations are properly audited

	suite.router.Use(func(c *gin.Context) {
		c.Set("user_id", "audit_user")
		c.Set("username", "audit_user")
		c.Set("user_projects", []string{"project_a"})
		c.Next()
	})

	request := map[string]interface{}{
		"workflowId":  "audit_workflow",
		"executionId": "audit_execution",
		"projectId":   "project_a",
		"secretMapping": map[string]string{
			"SSH_USERNAME": "PROJECT_A_SECRET_1",
		},
	}

	jsonData, _ := json.Marshal(request)
	req, _ := http.NewRequest("POST", "/api/v1/integration/workflow/secrets", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer test-token")
	req.Header.Set("X-Forwarded-For", "*************")
	req.Header.Set("User-Agent", "TestAgent/1.0")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// Verify audit logs are created
	var accessLogs []models.AccessLog
	suite.db.Find(&accessLogs, "user_id = ?", "audit_user")
	assert.Greater(suite.T(), len(accessLogs), 0)

	// Verify workflow usage tracking
	var usageLogs []models.WorkflowSecretUsage
	suite.db.Find(&usageLogs, "workflow_id = ?", "audit_workflow")
	assert.Greater(suite.T(), len(usageLogs), 0)

	// Verify audit log contains correct information
	log := accessLogs[0]
	assert.Equal(suite.T(), "audit_user", log.UserID)
	assert.Equal(suite.T(), "audit_user", log.Username)
	assert.Equal(suite.T(), "*************", log.IPAddress)
	assert.Equal(suite.T(), "TestAgent/1.0", log.UserAgent)
	assert.Equal(suite.T(), true, log.Success)

	// Verify usage log contains workflow context
	usage := usageLogs[0]
	assert.Equal(suite.T(), "audit_workflow", usage.WorkflowID)
	assert.Equal(suite.T(), "audit_execution", usage.ExecutionID)
	assert.Equal(suite.T(), "audit_user", usage.UsedBy)
	assert.Equal(suite.T(), "success", usage.Status)
}

func (suite *SecretIsolationTestSuite) TestSecretMappingWithExpiredSecrets() {
	// Test that expired secrets are not accessible through mapping

	// Create an expired secret
	expiredSecret := models.Secret{
		ID:          "expired_secret",
		Name:        "EXPIRED_SECRET",
		Description: "Expired secret",
		Type:        "password",
		Status:      models.SecretStatusActive,
		ScopeID:     "project_a_scope",
		// ExpiresAt:   &time.Time{}, // Set to past time
	}
	suite.db.Create(&expiredSecret)

	expiredVariable := models.SecretVariable{
		ID:        "var_expired",
		SecretID:  "expired_secret",
		ProjectID: "project_a",
		Name:      "EXPIRED_SECRET",
		Type:      "env",
	}
	suite.db.Create(&expiredVariable)

	suite.router.Use(func(c *gin.Context) {
		c.Set("user_id", "test_user")
		c.Set("user_projects", []string{"project_a"})
		c.Next()
	})

	request := map[string]interface{}{
		"workflowId":  "test_workflow",
		"executionId": "test_execution",
		"projectId":   "project_a",
		"secretMapping": map[string]string{
			"MAPPED_SECRET": "EXPIRED_SECRET",
		},
	}

	jsonData, _ := json.Marshal(request)
	req, _ := http.NewRequest("POST", "/api/v1/integration/workflow/secrets", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer test-token")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.Require().NoError(err)

	// Should have errors for expired secret
	errors := response["errors"].([]interface{})
	assert.Greater(suite.T(), len(errors), 0)

	// Should not return the expired secret
	variables := response["variables"].([]interface{})
	for _, v := range variables {
		variable := v.(map[string]interface{})
		assert.NotEqual(suite.T(), "MAPPED_SECRET", variable["name"])
	}
}

func TestSecretIsolationTestSuite(t *testing.T) {
	suite.Run(t, new(SecretIsolationTestSuite))
}
