# Secrets Management Service

A comprehensive secrets management service that provides secure storage, encryption, rotation, and access control for sensitive data across the deploy-orchestrator platform.

## Features

### 🔐 **Multi-Provider Support**
- **Internal Provider**: Built-in secure storage with AES-256-GCM encryption
- **CyberArk Conjur**: Enterprise-grade secrets management integration
- **HashiCorp Vault**: Popular open-source secrets management
- **AWS Secrets Manager**: Cloud-native AWS integration
- **Azure Key Vault**: Microsoft Azure cloud integration
- **Google Secret Manager**: Google Cloud Platform integration

### 🔄 **Automatic Secret Rotation**
- Configurable rotation policies with flexible intervals
- Multiple rotation strategies (replace, versioned, blue-green)
- Automatic scheduling with grace periods
- Rotation history and audit trails
- Failure handling and retry mechanisms

### 🛡️ **Advanced Security**
- AES-256-GCM encryption at rest
- TLS encryption in transit
- Key rotation and management
- Role-based access control
- Comprehensive audit logging
- Secret scoping by environment/application

### 📊 **Monitoring & Observability**
- Real-time metrics and health checks
- Provider health monitoring
- Access logging and analytics
- Rotation success/failure tracking
- Performance monitoring

### 🔧 **Enterprise Features**
- Secret versioning and rollback
- Approval workflows for sensitive operations
- Bulk operations for efficiency
- Integration APIs for services
- Comprehensive REST API

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend UI   │    │   Other Services │    │   External Apps │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │    Secrets Service API    │
                    │   (Port 8087)            │
                    └─────────────┬─────────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
    ┌─────▼─────┐      ┌─────────▼─────────┐      ┌─────▼─────┐
    │ Encryption│      │   Provider        │      │ Rotation  │
    │ Manager   │      │   Manager         │      │ Manager   │
    └───────────┘      └─────────┬─────────┘      └───────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
   ┌────▼────┐  ┌────▼────┐  ┌──▼───┐  ┌────▼────┐  ┌───▼────┐
   │Internal │  │ Conjur  │  │Vault │  │   AWS   │  │  GCP   │
   │Provider │  │Provider │  │      │  │Provider │  │Provider│
   └─────────┘  └─────────┘  └──────┘  └─────────┘  └────────┘
```

## Quick Start

### 1. Configuration

Create a configuration file `config.yaml`:

```yaml
# Basic service configuration
name: "secrets-service"
version: "1.0.0"
server_port: 8087

# Database configuration
db_url: "postgres://deploy:deploy@localhost:5432/secrets_service?sslmode=disable"

# Encryption configuration
encryption:
  master_key: "your-secure-master-key-here"
  algorithm: "AES-256-GCM"
  rotation_interval: "30d"

# Provider configuration
providers:
  default_provider: "internal"
  
  # Enable external providers as needed
  vault:
    enabled: true
    address: "https://vault.company.com:8200"
    token: "your-vault-token"
  
  conjur:
    enabled: true
    url: "https://conjur.company.com"
    account: "mycompany"
    username: "secrets-service"
    api_key: "your-conjur-api-key"

# Rotation configuration
rotation:
  enabled: true
  check_interval: "1h"
  default_interval: "30d"
```

### 2. Environment Variables

Set required environment variables:

```bash
export ENCRYPTION_MASTER_KEY="your-production-master-key"
export DATABASE_URL="postgres://user:pass@localhost:5432/secrets_service"

# For external providers
export VAULT_ADDR="https://vault.company.com:8200"
export VAULT_TOKEN="your-vault-token"
export CONJUR_URL="https://conjur.company.com"
export CONJUR_API_KEY="your-conjur-api-key"
```

### 3. Run the Service

```bash
# Install dependencies
go mod download

# Run database migrations
go run main.go migrate

# Start the service
go run main.go
```

## API Documentation

### Authentication

All API endpoints require authentication via JWT token:

```bash
curl -H "Authorization: Bearer <jwt-token>" \
     https://secrets-service:8087/api/v1/secrets
```

### Core Endpoints

#### Secrets Management

```bash
# Create a secret
POST /api/v1/secrets
{
  "name": "database-password",
  "value": "super-secure-password",
  "scopeId": "scope_production",
  "type": "password",
  "provider": "vault"
}

# Get a secret
GET /api/v1/secrets/{id}

# List secrets
GET /api/v1/secrets?scopeId=scope_production

# Update a secret
PUT /api/v1/secrets/{id}
{
  "value": "new-password",
  "description": "Updated password"
}

# Delete a secret
DELETE /api/v1/secrets/{id}

# Rotate a secret
POST /api/v1/secrets/{id}/rotate
```

#### Secret Scopes

```bash
# Create a scope
POST /api/v1/scopes
{
  "name": "production",
  "type": "environment",
  "description": "Production environment secrets"
}

# List scopes
GET /api/v1/scopes
```

#### Provider Management

```bash
# Create a provider
POST /api/v1/providers
{
  "name": "vault-prod",
  "type": "vault",
  "config": {
    "address": "https://vault.company.com:8200",
    "token": "vault-token"
  }
}

# Test provider connection
POST /api/v1/providers/{id}/test
```

#### Integration Endpoints

```bash
# Retrieve secrets for service integration
POST /api/v1/integration/retrieve
{
  "secretNames": ["db-password", "api-key"],
  "scopeId": "scope_production",
  "serviceId": "web-service"
}

# Bulk retrieve secrets
POST /api/v1/integration/bulk-retrieve
{
  "requests": [
    {
      "secretNames": ["db-password"],
      "scopeId": "scope_production",
      "serviceId": "web-service"
    }
  ]
}
```

### Monitoring Endpoints

```bash
# Health check
GET /health

# Metrics
GET /metrics

# Secret metrics
GET /api/v1/audit/metrics

# Audit logs
GET /api/v1/audit/logs?userId=user123&action=create
```

## Security Best Practices

### 1. **Encryption Keys**
- Use a strong master key (32+ characters)
- Store master key in external key management system
- Rotate encryption keys regularly
- Use hardware security modules (HSM) when available

### 2. **Access Control**
- Implement least-privilege access
- Use service accounts for automated access
- Regularly audit access logs
- Enable approval workflows for sensitive secrets

### 3. **Network Security**
- Use TLS for all communications
- Implement network segmentation
- Use VPN or private networks for provider connections
- Enable certificate validation

### 4. **Operational Security**
- Monitor all secret access
- Set up alerts for failed rotations
- Regularly review audit logs
- Implement backup and disaster recovery

## Deployment

### Docker Deployment

```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o secrets-service main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/secrets-service .
COPY config.yaml .
CMD ["./secrets-service"]
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: secrets-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: secrets-service
  template:
    metadata:
      labels:
        app: secrets-service
    spec:
      containers:
      - name: secrets-service
        image: secrets-service:latest
        ports:
        - containerPort: 8087
        env:
        - name: ENCRYPTION_MASTER_KEY
          valueFrom:
            secretKeyRef:
              name: secrets-service-config
              key: master-key
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: secrets-service-config
              key: database-url
```

## Monitoring

### Metrics

The service exposes Prometheus metrics at `/metrics`:

- `secrets_total`: Total number of secrets
- `secrets_by_provider`: Secrets count by provider
- `rotation_success_rate`: Secret rotation success rate
- `provider_health`: Provider health status
- `api_requests_total`: API request count and latency

### Health Checks

- `/health`: Overall service health
- `/health/ready`: Readiness probe
- `/health/live`: Liveness probe

### Logging

Structured JSON logging with configurable levels:

```json
{
  "level": "info",
  "timestamp": "2024-01-15T10:30:00Z",
  "message": "Secret created successfully",
  "secretID": "sec_123456789",
  "userID": "user_123",
  "provider": "vault"
}
```

## Development

### Running Tests

```bash
# Unit tests
go test ./...

# Integration tests
go test -tags=integration ./...

# Load tests
go test -tags=load ./...
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check database URL and credentials
   - Verify network connectivity
   - Ensure database exists and migrations are applied

2. **Provider Connection Failures**
   - Verify provider configuration
   - Check network connectivity to external providers
   - Validate authentication credentials

3. **Encryption Errors**
   - Ensure master key is properly configured
   - Check encryption key status
   - Verify key rotation hasn't failed

### Debug Mode

Enable debug logging:

```bash
export LOG_LEVEL=debug
go run main.go
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
