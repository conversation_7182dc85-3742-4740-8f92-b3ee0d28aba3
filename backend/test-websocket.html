<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Real-time Logs Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .controls input, .controls button {
            margin: 5px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .controls button {
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        .controls button:hover {
            background: #0056b3;
        }
        .controls button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .logs-container {
            height: 500px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #000;
            color: #fff;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-entry.new {
            background: rgba(0, 255, 0, 0.1);
            animation: highlight 2s ease-out;
        }
        .log-entry.info { color: #17a2b8; }
        .log-entry.warn { color: #ffc107; }
        .log-entry.error { color: #dc3545; }
        .log-entry.debug { color: #6c757d; }
        .timestamp {
            color: #6c757d;
            font-size: 12px;
        }
        .step-name {
            color: #28a745;
            font-weight: bold;
        }
        @keyframes highlight {
            from { background: rgba(0, 255, 0, 0.3); }
            to { background: rgba(0, 255, 0, 0.1); }
        }
        .stats {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            flex: 1;
            padding: 15px;
            background: #e9ecef;
            border-radius: 5px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Deploy Orchestrator - Real-time Logs</h1>
        
        <div class="controls">
            <input type="text" id="gatewayUrl" placeholder="Gateway URL" value="ws://localhost:8080/ws/workflow-service/v1/ws/logs">
            <input type="text" id="executionId" placeholder="Execution ID" value="test-execution-123">
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
            <button onclick="clearLogs()">Clear Logs</button>
            <button onclick="testLog()">Send Test Log</button>
        </div>

        <div id="status" class="status disconnected">Disconnected</div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-value" id="totalLogs">0</div>
                <div class="stat-label">Total Logs</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="errorCount">0</div>
                <div class="stat-label">Errors</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="connectionTime">0s</div>
                <div class="stat-label">Connected</div>
            </div>
        </div>

        <div class="logs-container" id="logsContainer">
            <div class="log-entry info">
                <span class="timestamp">[Ready]</span> 
                <span class="step-name">[System]</span> 
                WebSocket client ready. Click Connect to start receiving real-time logs.
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let logCount = 0;
        let errorCount = 0;
        let connectionStartTime = null;
        let connectionTimer = null;

        function updateStatus(status, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${status}`;
            statusEl.textContent = message;
        }

        function updateStats() {
            document.getElementById('totalLogs').textContent = logCount;
            document.getElementById('errorCount').textContent = errorCount;
            
            if (connectionStartTime) {
                const elapsed = Math.floor((Date.now() - connectionStartTime) / 1000);
                document.getElementById('connectionTime').textContent = `${elapsed}s`;
            }
        }

        function addLogEntry(entry) {
            const container = document.getElementById('logsContainer');
            const logDiv = document.createElement('div');
            logDiv.className = `log-entry ${entry.level || 'info'}${entry.isNew ? ' new' : ''}`;
            
            const timestamp = new Date(entry.timestamp).toLocaleTimeString();
            const stepName = entry.stepName || 'system';
            
            logDiv.innerHTML = `
                <span class="timestamp">[${timestamp}]</span>
                <span class="step-name">[${stepName}]</span>
                ${entry.message}
            `;
            
            container.appendChild(logDiv);
            container.scrollTop = container.scrollHeight;
            
            logCount++;
            if (entry.level === 'error') {
                errorCount++;
            }
            updateStats();
        }

        function connect() {
            const gatewayUrl = document.getElementById('gatewayUrl').value;
            const executionId = document.getElementById('executionId').value;
            
            if (!gatewayUrl || !executionId) {
                alert('Please enter both Gateway URL and Execution ID');
                return;
            }

            updateStatus('connecting', 'Connecting...');
            
            try {
                ws = new WebSocket(gatewayUrl);
                
                ws.onopen = function() {
                    updateStatus('connected', 'Connected to WebSocket Gateway');
                    connectionStartTime = Date.now();
                    connectionTimer = setInterval(updateStats, 1000);
                    
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    
                    // Subscribe to logs for the execution
                    ws.send(JSON.stringify({
                        type: 'subscribe-logs',
                        executionId: executionId
                    }));
                    
                    addLogEntry({
                        timestamp: new Date(),
                        level: 'info',
                        stepName: 'websocket',
                        message: `Connected to gateway and subscribed to execution: ${executionId}`,
                        isNew: true
                    });
                };
                
                ws.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        
                        switch (message.type) {
                            case 'subscription-confirmed':
                                addLogEntry({
                                    timestamp: new Date(),
                                    level: 'info',
                                    stepName: 'websocket',
                                    message: `Subscription confirmed for stream: ${message.data.streamId}`,
                                    isNew: true
                                });
                                break;
                                
                            case 'log-entry':
                                addLogEntry(message.data);
                                break;
                                
                            case 'error':
                                addLogEntry({
                                    timestamp: new Date(),
                                    level: 'error',
                                    stepName: 'websocket',
                                    message: `WebSocket error: ${message.data.error}`,
                                    isNew: true
                                });
                                break;
                                
                            default:
                                console.log('Unknown message type:', message);
                        }
                    } catch (e) {
                        console.error('Failed to parse WebSocket message:', e);
                    }
                };
                
                ws.onclose = function() {
                    updateStatus('disconnected', 'Disconnected from WebSocket Gateway');
                    if (connectionTimer) {
                        clearInterval(connectionTimer);
                        connectionTimer = null;
                    }
                    connectionStartTime = null;
                    
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    
                    addLogEntry({
                        timestamp: new Date(),
                        level: 'warn',
                        stepName: 'websocket',
                        message: 'WebSocket connection closed',
                        isNew: true
                    });
                };
                
                ws.onerror = function(error) {
                    updateStatus('disconnected', 'WebSocket connection error');
                    addLogEntry({
                        timestamp: new Date(),
                        level: 'error',
                        stepName: 'websocket',
                        message: `WebSocket error: ${error.message || 'Unknown error'}`,
                        isNew: true
                    });
                };
                
            } catch (error) {
                updateStatus('disconnected', 'Failed to connect');
                addLogEntry({
                    timestamp: new Date(),
                    level: 'error',
                    stepName: 'websocket',
                    message: `Connection failed: ${error.message}`,
                    isNew: true
                });
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function clearLogs() {
            document.getElementById('logsContainer').innerHTML = '';
            logCount = 0;
            errorCount = 0;
            updateStats();
        }

        function testLog() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                // This would normally come from the backend, but we can simulate it
                addLogEntry({
                    timestamp: new Date(),
                    level: 'info',
                    stepName: 'test-step',
                    message: 'This is a test log entry from the client',
                    isNew: true
                });
            } else {
                alert('WebSocket is not connected');
            }
        }

        // Initialize stats
        updateStats();
    </script>
</body>
</html>
