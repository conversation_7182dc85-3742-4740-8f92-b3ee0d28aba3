package storage

import (
	"log"

	"github.com/claudio/deploy-orchestrator/shared/db"
	sharedModels "github.com/claudio/deploy-orchestrator/shared/models"
	"gorm.io/gorm"
)

// Database represents a connection to the database using GORM
type Database struct {
	DB         *gorm.DB
	InMemoryDB *InMemoryDatabase
}

// NewDatabase creates a new database connection using GORM
func NewDatabase(postgresURL string) (*Database, error) {
	config := db.Config{
		URL:           postgresURL,
		MaxRetries:    5,
		RetryInterval: 2,
	}

	gormDB, err := db.Connect(config)
	if err != nil {
		return nil, err
	}

	// Run migrations immediately
	if err := RunMigrations(gormDB); err != nil {
		return nil, err
	}

	return &Database{DB: gormDB}, nil
}

// Close closes the database connection
func (d *Database) Close() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// RunMigrations performs all necessary database migrations
func RunMigrations(grm *gorm.DB) error {
	log.Println("Running integration-service database migrations...")

	// Use the shared RunMigrations function to run migrations for all models
	return db.RunMigrations(grm,
		&sharedModels.Integration{},
	)
}

// SeedTestData populates the database with initial test data
// Only for testing purposes
func SeedTestData(db *gorm.DB) error {
	log.Println("Seeding integration database with test data...")

	// Create sample integrations
	testIntegrations := []sharedModels.Integration{
		{
			ID:       "integration-test-1",
			Name:     "Test Slack Integration",
			Type:     "slack",
			Endpoint: "https://hooks.slack.com/services/test",
			AuthType: "token",
			Enabled:  true,
		},
		{
			ID:       "integration-test-2",
			Name:     "Test Email Integration",
			Type:     "email",
			Endpoint: "smtp://<EMAIL>",
			AuthType: "password",
			Enabled:  true,
		},
	}

	// Add the test integrations to the database
	for _, integration := range testIntegrations {
		if result := db.Create(&integration); result.Error != nil {
			return result.Error
		}
	}

	return nil
}
