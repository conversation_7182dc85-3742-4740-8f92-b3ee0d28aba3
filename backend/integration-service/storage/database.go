package storage

import (
	"context"
	"errors"
	"log"

	"github.com/google/uuid"
	"gorm.io/gorm"

	sharedModels "github.com/claudio/deploy-orchestrator/shared/models"
)

// GetIntegration retrieves an integration by ID
func (d *Database) GetIntegration(ctx context.Context, id string) (*sharedModels.Integration, error) {
	// Check if we're using in-memory database
	if d.InMemoryDB != nil {
		return d.InMemoryDB.GetIntegration(ctx, id)
	}

	// Use real database
	var integration sharedModels.Integration
	result := d.DB.First(&integration, "id = ?", id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil // Return nil, nil when not found to be consistent with other services
		}
		return nil, result.Error
	}
	return &integration, nil
}

// ListIntegrations retrieves all integrations with optional filtering
func (d *Database) ListIntegrations(ctx context.Context, filter map[string]interface{}) ([]sharedModels.Integration, error) {
	// Check if we're using in-memory database
	if d.InMemoryDB != nil {
		return d.InMemoryDB.ListIntegrations(ctx, filter)
	}

	// Use real database
	var integrations []sharedModels.Integration

	query := d.DB
	if filter != nil {
		query = query.Where(filter)
	}

	if err := query.Find(&integrations).Error; err != nil {
		return nil, err
	}

	return integrations, nil
}

// CreateIntegration creates a new integration
func (d *Database) CreateIntegration(ctx context.Context, integration *sharedModels.Integration) error {
	// Check if we're using in-memory database
	if d.InMemoryDB != nil {
		return d.InMemoryDB.CreateIntegration(ctx, integration)
	}

	// Use real database
	if integration.ID == "" {
		integration.ID = uuid.New().String()
	}
	return d.DB.Create(integration).Error
}

// UpdateIntegration updates an existing integration
func (d *Database) UpdateIntegration(ctx context.Context, id string, integration *sharedModels.Integration) error {
	// Check if we're using in-memory database
	if d.InMemoryDB != nil {
		return d.InMemoryDB.UpdateIntegration(ctx, id, integration)
	}

	// Use real database
	// First check if the integration exists
	var existing sharedModels.Integration
	result := d.DB.First(&existing, "id = ?", id)
	if result.Error != nil {
		return result.Error
	}

	// Make sure the ID is not changed
	integration.ID = id

	// Update the integration
	return d.DB.Save(integration).Error
}

// DeleteIntegration deletes an integration by ID
func (d *Database) DeleteIntegration(ctx context.Context, id string) error {
	// Check if we're using in-memory database
	if d.InMemoryDB != nil {
		return d.InMemoryDB.DeleteIntegration(ctx, id)
	}

	// Use real database
	result := d.DB.Delete(&sharedModels.Integration{}, "id = ?", id)
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return errors.New("integration not found")
	}

	return nil
}

// EnableIntegration enables an integration by ID
func (d *Database) EnableIntegration(ctx context.Context, id string) error {
	// Check if we're using in-memory database
	if d.InMemoryDB != nil {
		return d.InMemoryDB.EnableIntegration(ctx, id)
	}

	// Use real database
	return d.DB.Model(&sharedModels.Integration{}).Where("id = ?", id).Update("enabled", true).Error
}

// DisableIntegration disables an integration by ID
func (d *Database) DisableIntegration(ctx context.Context, id string) error {
	// Check if we're using in-memory database
	if d.InMemoryDB != nil {
		return d.InMemoryDB.DisableIntegration(ctx, id)
	}

	// Use real database
	return d.DB.Model(&sharedModels.Integration{}).Where("id = ?", id).Update("enabled", false).Error
}

// TestConnection tests the database connection
func (d *Database) TestConnection() error {
	// Check if we're using in-memory database
	if d.InMemoryDB != nil {
		return d.InMemoryDB.TestConnection()
	}

	// Use real database
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}

// InMemoryDatabase provides a simple in-memory implementation for testing
type InMemoryDatabase struct {
	integrations map[string]sharedModels.Integration
}

// NewInMemoryDatabase creates a new in-memory database
func NewInMemoryDatabase() *InMemoryDatabase {
	log.Println("Using in-memory database")
	return &InMemoryDatabase{
		integrations: make(map[string]sharedModels.Integration),
	}
}

// GetIntegration retrieves an integration by ID from memory
func (d *InMemoryDatabase) GetIntegration(ctx context.Context, id string) (*sharedModels.Integration, error) {
	integration, exists := d.integrations[id]
	if !exists {
		return nil, nil
	}
	return &integration, nil
}

// ListIntegrations retrieves all integrations from memory with optional filtering
func (d *InMemoryDatabase) ListIntegrations(ctx context.Context, filter map[string]interface{}) ([]sharedModels.Integration, error) {
	var results []sharedModels.Integration

	// Apply filtering if provided
	for _, integration := range d.integrations {
		match := true

		// Apply filters if any
		if filter != nil {
			for key, value := range filter {
				switch key {
				case "type":
					if integration.Type != value.(string) {
						match = false
					}
				case "enabled":
					if integration.Enabled != value.(bool) {
						match = false
					}
				}
			}
		}

		if match {
			results = append(results, integration)
		}
	}

	return results, nil
}

// CreateIntegration creates a new integration in memory
func (d *InMemoryDatabase) CreateIntegration(ctx context.Context, integration *sharedModels.Integration) error {
	if integration.ID == "" {
		integration.ID = uuid.New().String()
	}
	d.integrations[integration.ID] = *integration
	return nil
}

// UpdateIntegration updates an existing integration in memory
func (d *InMemoryDatabase) UpdateIntegration(ctx context.Context, id string, integration *sharedModels.Integration) error {
	_, exists := d.integrations[id]
	if !exists {
		return errors.New("integration not found")
	}

	// Make sure the ID is not changed
	integration.ID = id
	d.integrations[id] = *integration
	return nil
}

// DeleteIntegration deletes an integration from memory
func (d *InMemoryDatabase) DeleteIntegration(ctx context.Context, id string) error {
	_, exists := d.integrations[id]
	if !exists {
		return errors.New("integration not found")
	}

	delete(d.integrations, id)
	return nil
}

// EnableIntegration enables an integration in memory
func (d *InMemoryDatabase) EnableIntegration(ctx context.Context, id string) error {
	integration, exists := d.integrations[id]
	if !exists {
		return errors.New("integration not found")
	}

	integration.Enabled = true
	d.integrations[id] = integration
	return nil
}

// DisableIntegration disables an integration in memory
func (d *InMemoryDatabase) DisableIntegration(ctx context.Context, id string) error {
	integration, exists := d.integrations[id]
	if !exists {
		return errors.New("integration not found")
	}

	integration.Enabled = false
	d.integrations[id] = integration
	return nil
}

// TestConnection for in-memory database always returns nil
func (d *InMemoryDatabase) TestConnection() error {
	return nil
}

// Close does nothing for in-memory database
func (d *InMemoryDatabase) Close() error {
	return nil
}
