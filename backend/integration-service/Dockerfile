FROM golang:1.24.3-alpine AS builder

WORKDIR /app

# Copy go mod and sum files
COPY go.mod ./
# Uncomment if you have a go.sum file
# COPY go.sum ./

# Download dependencies
RUN go mod tidy

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o integration-service .

FROM alpine:3.21.3

WORKDIR /app

# Create config directory
RUN mkdir -p /app/config

# Copy the binary from builder
COPY --from=builder /app/integration-service .

# Copy config files
COPY --from=builder /app/config/config.yaml /app/config/

# Expose the application port
EXPOSE 8080

# Set default config path
ENV CONFIG_PATH=/app/config/config.yaml

# Run the application
CMD ["./integration-service"]
