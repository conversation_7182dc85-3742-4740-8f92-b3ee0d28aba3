package middleware

import (
	"log"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware is a middleware for authenticating API requests
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the Authorization header
		authHeader := c.<PERSON>("Authorization")

		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		// Check if the header starts with "Bearer "
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "Authorization header format must be Bearer {token}"})
			c.Abort()
			return
		}

		token := parts[1]

		// In a real implementation, validate token with your auth service
		// For now, this is a simple placeholder that allows any non-empty token
		if token == "" {
			c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		// Add user info to request context
		// In a real implementation, decode token and set user details
		c.Set("userID", "user-123") // Placeholder user ID
		c.Set("role", "admin")      // Placeholder role

		log.Printf("Request authenticated for user: user-123")
		c.Next()
	}
}
