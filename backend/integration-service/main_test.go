package main

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"github.com/claudio/deploy-orchestrator/shared/handlers"
)

func TestHealthEndpoint(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a new router
	r := gin.New()

	// Register the health endpoint
	serviceInfo := handlers.ServiceInfo{
		Name:    "integration-service",
		Version: "test-version",
	}
	r.GET("/health", handlers.NewHealthHandler(serviceInfo))

	// Create a test request
	req, err := http.NewRequest("GET", "/health", nil)
	if err != nil {
		t.Fatalf("Couldn't create request: %v\n", err)
	}

	// Create a response recorder to record the response
	w := httptest.NewRecorder()

	// Perform the request
	r.ServeHTTP(w, req)

	// Check the status code
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse the response body
	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Couldn't parse response: %v\n", err)
	}

	// Check the response
	assert.Equal(t, "integration-service", response["name"])
	assert.Equal(t, "test-version", response["version"])
	assert.Equal(t, "healthy", response["status"])
}
