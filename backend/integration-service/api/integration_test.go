package api

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"github.com/claudio/deploy-orchestrator/integration-service/storage"
	"github.com/claudio/deploy-orchestrator/shared/handlers"
	"github.com/claudio/deploy-orchestrator/shared/models"
)

func setupTestRouter() *gin.Engine {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a new router
	router := gin.New()

	// Setup in-memory database
	inMemoryDB := storage.NewInMemoryDatabase()
	db := &storage.Database{InMemoryDB: inMemoryDB}

	// Setup handlers
	integrationHandler := NewIntegrationHandler(db)

	// Register health endpoint
	serviceInfo := handlers.ServiceInfo{
		Name:    "integration-service",
		Version: "test",
	}
	router.GET("/health", handlers.NewHealthHandler(serviceInfo))

	// Register API endpoints
	v1 := router.Group("/api/v1")
	integrations := v1.Group("/integrations")
	{
		integrations.GET("", integrationHandler.GetIntegrations)
		integrations.GET("/:id", integrationHandler.GetIntegration)
		integrations.POST("", integrationHandler.CreateIntegration)
		integrations.PUT("/:id", integrationHandler.UpdateIntegration)
		integrations.DELETE("/:id", integrationHandler.DeleteIntegration)
		integrations.POST("/:id/test", integrationHandler.TestIntegration)
	}

	return router
}

func TestHealthEndpoint(t *testing.T) {
	router := setupTestRouter()

	// Create a test request for the health endpoint
	req, _ := http.NewRequest("GET", "/health", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check response
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse the response
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Verify the structure of the health response
	assert.Equal(t, "integration-service", response["name"])
	assert.Equal(t, "test", response["version"])
	assert.Equal(t, "healthy", response["status"])
}

func TestIntegrationCRUD(t *testing.T) {
	router := setupTestRouter()

	// Test data
	testIntegration := models.Integration{
		Name:     "Test Integration",
		Type:     "slack",
		Endpoint: "https://hooks.slack.com/services/test",
		AuthType: "token",
		Enabled:  true,
	}

	// Test CREATE
	t.Run("Create Integration", func(t *testing.T) {
		jsonData, _ := json.Marshal(testIntegration)
		req, _ := http.NewRequest("POST", "/api/v1/integrations", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Check response
		assert.Equal(t, http.StatusCreated, w.Code)

		// Parse the response
		var response models.Integration
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Save the ID for later tests
		testIntegration.ID = response.ID
		assert.NotEmpty(t, response.ID)
		assert.Equal(t, testIntegration.Name, response.Name)
	})

	// Test GET by ID
	t.Run("Get Integration", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/integrations/"+testIntegration.ID, nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Check response
		assert.Equal(t, http.StatusOK, w.Code)

		// Parse the response
		var response models.Integration
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		assert.Equal(t, testIntegration.ID, response.ID)
		assert.Equal(t, testIntegration.Name, response.Name)
	})

	// Test UPDATE
	t.Run("Update Integration", func(t *testing.T) {
		// Update the test data
		testIntegration.Name = "Updated Integration"
		testIntegration.Enabled = false

		jsonData, _ := json.Marshal(testIntegration)
		req, _ := http.NewRequest("PUT", "/api/v1/integrations/"+testIntegration.ID, bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Check response
		assert.Equal(t, http.StatusOK, w.Code)

		// Parse the response
		var response models.Integration
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		assert.Equal(t, "Updated Integration", response.Name)
		assert.Equal(t, false, response.Enabled)
	})

	// Test LIST
	t.Run("List Integrations", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/integrations", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Check response
		assert.Equal(t, http.StatusOK, w.Code)

		// Parse the response
		var response []models.Integration
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Should have at least one integration
		assert.True(t, len(response) > 0)
	})

	// Test DELETE
	t.Run("Delete Integration", func(t *testing.T) {
		req, _ := http.NewRequest("DELETE", "/api/v1/integrations/"+testIntegration.ID, nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Check response
		assert.Equal(t, http.StatusNoContent, w.Code)

		// Verify it's gone
		req, _ = http.NewRequest("GET", "/api/v1/integrations/"+testIntegration.ID, nil)
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should return 404
		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}
