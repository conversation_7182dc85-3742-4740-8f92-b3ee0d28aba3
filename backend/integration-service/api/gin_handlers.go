package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/claudio/deploy-orchestrator/integration-service/storage"
	"github.com/claudio/deploy-orchestrator/shared/auth"
	"github.com/claudio/deploy-orchestrator/shared/models"
)

// IntegrationHandler handles HTTP requests for integrations
type IntegrationHandler struct {
	db         *storage.Database
	authHelper *auth.AuthHelper
}

// NewIntegrationHandler creates a new IntegrationHandler
func NewIntegrationHandler(db *storage.Database) *IntegrationHandler {
	return &IntegrationHandler{
		db:         db,
		authHelper: auth.NewAuthHelper(),
	}
}

// GetIntegrations retrieves all integrations
func (h *IntegrationHandler) GetIntegrations(c *gin.Context) {
	// Check user authentication using centralized auth helper
	authContext := h.authHelper.RequireAuthentication(c)
	if authContext == nil {
		// RequireAuthentication already sent the 401 response
		return
	}

	// Check if filtering by type is requested
	integrationType := c.Query("type")

	var filter map[string]interface{}
	if integrationType != "" {
		filter = map[string]interface{}{"type": integrationType}
	}

	integrations, err := h.db.ListIntegrations(c.Request.Context(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve integrations"})
		return
	}

	c.JSON(http.StatusOK, integrations)
}

// GetIntegration retrieves an integration by ID
func (h *IntegrationHandler) GetIntegration(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Integration ID is required"})
		return
	}

	integration, err := h.db.GetIntegration(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve integration"})
		return
	}

	if integration == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Integration not found"})
		return
	}

	c.JSON(http.StatusOK, integration)
}

// CreateIntegration creates a new integration
func (h *IntegrationHandler) CreateIntegration(c *gin.Context) {
	var integration models.Integration
	if err := c.ShouldBindJSON(&integration); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid integration data"})
		return
	}

	// Generate UUID if not provided
	if integration.ID == "" {
		integration.ID = uuid.New().String()
	}

	if err := h.db.CreateIntegration(c.Request.Context(), &integration); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create integration"})
		return
	}

	c.JSON(http.StatusCreated, integration)
}

// UpdateIntegration updates an existing integration
func (h *IntegrationHandler) UpdateIntegration(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Integration ID is required"})
		return
	}

	var integration models.Integration
	if err := c.ShouldBindJSON(&integration); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid integration data"})
		return
	}

	// Make sure ID in URL matches ID in body
	integration.ID = id

	if err := h.db.UpdateIntegration(c.Request.Context(), id, &integration); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update integration"})
		return
	}

	c.JSON(http.StatusOK, integration)
}

// DeleteIntegration deletes an integration
func (h *IntegrationHandler) DeleteIntegration(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Integration ID is required"})
		return
	}

	if err := h.db.DeleteIntegration(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete integration"})
		return
	}

	c.Status(http.StatusNoContent)
}

// TestIntegration tests an integration connection
func (h *IntegrationHandler) TestIntegration(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Integration ID is required"})
		return
	}

	// In a real implementation, this would test the integration connection
	// For now, just return a success message
	c.JSON(http.StatusOK, gin.H{"message": "Integration test successful", "id": id})
}
