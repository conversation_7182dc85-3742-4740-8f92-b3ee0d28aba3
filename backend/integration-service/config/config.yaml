# Integration Service Configuration

# Database Configuration
database:
  url: "postgres://postgres:postgres@localhost:5432/integration_service?sslmode=disable"
  max_retries: 5
  retry_interval: 3
  max_open_conns: 25
  max_idle_conns: 10
  query_timeout: 10
  log_level: "info"

# Server Configuration
server:
  port: 8083
  host: "0.0.0.0"
  read_timeout: 30
  write_timeout: 30
  shutdown_timeout: 10
  trusted_proxies: ""
  tls_cert_file: ""
  tls_key_file: ""

# Logging Configuration
logging:
  level: "info"
  format: "json"
  output: "stdout"
  time_format: "2006-01-02T15:04:05Z07:00"

# Tracing Configuration
tracing:
  enabled: false
  service_name: "integration-service"
  endpoint: "localhost:4317"

# Auth Configuration
auth:
  jwt_secret: "default-secret-key-in-production"
  jwt_expiration_minutes: 60
  access_token_expiry: "1h"
  refresh_token_expiry: "168h"
  admin_service_url: "http://localhost:8086"
  api_key_header: "X-API-Key"
  disable: false

# Integration Service Specific Configuration
webhook_timeout_sec: 30
webhook_retry_count: 3
webhook_retry_interval_sec: 60
worker_count: 5
notification_service_url: "http://localhost:8083"
webhook_secret_key: "change-me-in-production"
enable_rate_limit: true
rate_limit_per_min: 60
