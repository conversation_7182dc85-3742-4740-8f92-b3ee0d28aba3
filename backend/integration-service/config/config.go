package config

import (
	"fmt"

	sharedConfig "github.com/claudio/deploy-orchestrator/shared/config"
)

// IntegrationServiceConfig holds the configuration for the integration service
type IntegrationServiceConfig struct {
	// Common configuration sections
	Database sharedConfig.DBConfig      `mapstructure:"database" yaml:"database"`
	Server   sharedConfig.ServerConfig  `mapstructure:"server" yaml:"server"`
	Logging  sharedConfig.LoggingConfig `mapstructure:"logging" yaml:"logging"`
	Tracing  sharedConfig.TracingConfig `mapstructure:"tracing" yaml:"tracing"`
	Auth     sharedConfig.AuthConfig    `mapstructure:"auth" yaml:"auth"`
	Service  sharedConfig.ServiceConfig `mapstructure:"service" yaml:"service"`
	Gateway  sharedConfig.GatewayConfig `mapstructure:"gateway" yaml:"gateway"`

	// Integration service specific configuration
	WebhookTimeoutSec       int    `mapstructure:"webhook_timeout_sec" yaml:"webhook_timeout_sec" env:"WEBHOOK_TIMEOUT_SEC" default:"30"`
	WebhookRetryCount       int    `mapstructure:"webhook_retry_count" yaml:"webhook_retry_count" env:"WEBHOOK_RETRY_COUNT" default:"3"`
	WebhookRetryIntervalSec int    `mapstructure:"webhook_retry_interval_sec" yaml:"webhook_retry_interval_sec" env:"WEBHOOK_RETRY_INTERVAL_SEC" default:"60"`
	WorkerCount             int    `mapstructure:"worker_count" yaml:"worker_count" env:"WORKER_COUNT" default:"5"`
	NotificationServiceURL  string `mapstructure:"notification_service_url" yaml:"notification_service_url" env:"NOTIFICATION_SERVICE_URL" default:"http://localhost:8083"`
	WebhookSecretKey        string `mapstructure:"webhook_secret_key" yaml:"webhook_secret_key" env:"WEBHOOK_SECRET_KEY" default:""`
	EnableRateLimit         bool   `mapstructure:"enable_rate_limit" yaml:"enable_rate_limit" env:"ENABLE_RATE_LIMIT" default:"true"`
	RateLimitPerMin         int    `mapstructure:"rate_limit_per_min" yaml:"rate_limit_per_min" env:"RATE_LIMIT_PER_MIN" default:"60"`
}

// LoadConfig loads the integration service configuration from file and environment variables
func LoadConfig() (*IntegrationServiceConfig, error) {
	config := &IntegrationServiceConfig{
		Server: sharedConfig.ServerConfig{
			Port: 8085, // Default port for integration service
		},
		Tracing: sharedConfig.TracingConfig{
			ServiceName: "integration-service",
		},
	}

	if err := sharedConfig.LoadConfig(config, "integration-service"); err != nil {
		return nil, fmt.Errorf("failed to load integration service config: %w", err)
	}

	return config, nil
}

// SaveDefaultConfig saves a default configuration file if one doesn't exist
func SaveDefaultConfig() error {
	config := &IntegrationServiceConfig{
		Server: sharedConfig.ServerConfig{
			Port: 8085,
		},
		Tracing: sharedConfig.TracingConfig{
			ServiceName: "integration-service",
		},
	}

	return sharedConfig.SaveDefaultConfig(config, "integration-service")
}
