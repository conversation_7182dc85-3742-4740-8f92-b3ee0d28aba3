package migrations

import (
	"fmt"
	"log"

	"github.com/claudio/deploy-orchestrator/workflow-service/internal/models"
	"gorm.io/gorm"
)

// MigratePromotionTables creates the promotion-related database tables
func MigratePromotionTables(db *gorm.DB) error {
	log.Println("Starting promotion tables migration...")

	// Auto-migrate promotion tables
	err := db.AutoMigrate(
		&models.EnvironmentDeployment{},
		&models.EnvironmentPromotion{},
	)
	if err != nil {
		return fmt.Errorf("failed to migrate promotion tables: %w", err)
	}

	// Create indexes for better performance
	if err := createPromotionIndexes(db); err != nil {
		return fmt.Errorf("failed to create promotion indexes: %w", err)
	}

	// Create constraints
	if err := createPromotionConstraints(db); err != nil {
		return fmt.Errorf("failed to create promotion constraints: %w", err)
	}

	log.Println("Promotion tables migration completed successfully")
	return nil
}

// createPromotionIndexes creates database indexes for promotion tables
func createPromotionIndexes(db *gorm.DB) error {
	indexes := []string{
		// Environment deployments indexes
		"CREATE INDEX IF NOT EXISTS idx_environment_deployments_environment_id ON environment_deployments(environment_id)",
		"CREATE INDEX IF NOT EXISTS idx_environment_deployments_project_id ON environment_deployments(project_id)",
		"CREATE INDEX IF NOT EXISTS idx_environment_deployments_deployed_at ON environment_deployments(deployed_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_environment_deployments_status ON environment_deployments(status)",
		"CREATE INDEX IF NOT EXISTS idx_environment_deployments_version ON environment_deployments(version)",
		"CREATE INDEX IF NOT EXISTS idx_environment_deployments_deployed_by ON environment_deployments(deployed_by)",

		// Environment promotions indexes
		"CREATE INDEX IF NOT EXISTS idx_environment_promotions_project_id ON environment_promotions(project_id)",
		"CREATE INDEX IF NOT EXISTS idx_environment_promotions_source_env ON environment_promotions(source_environment_id)",
		"CREATE INDEX IF NOT EXISTS idx_environment_promotions_target_env ON environment_promotions(target_environment_id)",
		"CREATE INDEX IF NOT EXISTS idx_environment_promotions_status ON environment_promotions(status)",
		"CREATE INDEX IF NOT EXISTS idx_environment_promotions_initiated_by ON environment_promotions(initiated_by)",
		"CREATE INDEX IF NOT EXISTS idx_environment_promotions_created_at ON environment_promotions(created_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_environment_promotions_workflow_execution ON environment_promotions(workflow_execution_id)",

		// Composite indexes for common queries
		"CREATE INDEX IF NOT EXISTS idx_environment_deployments_env_status_date ON environment_deployments(environment_id, status, deployed_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_environment_promotions_project_status ON environment_promotions(project_id, status, created_at DESC)",
	}

	for _, indexSQL := range indexes {
		if err := db.Exec(indexSQL).Error; err != nil {
			log.Printf("Warning: Failed to create index: %s - %v", indexSQL, err)
			// Continue with other indexes even if one fails
		}
	}

	return nil
}

// createPromotionConstraints creates database constraints for promotion tables
func createPromotionConstraints(db *gorm.DB) error {
	// For each constraint, check if it exists before creating it
	constraintChecks := []struct {
		name      string
		tableName string
		checkSQL  string
	}{
		{
			name:      "chk_deployment_status",
			tableName: "environment_deployments",
			checkSQL:  "status IN ('active', 'inactive', 'failed')",
		},
		{
			name:      "chk_deployment_health",
			tableName: "environment_deployments",
			checkSQL:  "health IN ('healthy', 'degraded', 'unhealthy', 'unknown')",
		},
		{
			name:      "chk_promotion_status",
			tableName: "environment_promotions",
			checkSQL:  "status IN ('pending', 'approved', 'running', 'success', 'failed', 'cancelled')",
		},
		{
			name:      "chk_promotion_type",
			tableName: "environment_promotions",
			checkSQL:  "promotion_type IN ('manual', 'automatic', 'scheduled')",
		},
		{
			name:      "chk_different_environments",
			tableName: "environment_promotions",
			checkSQL:  "source_environment_id != target_environment_id",
		},
		{
			name:      "chk_deployment_version_not_empty",
			tableName: "environment_deployments",
			checkSQL:  "length(trim(version)) > 0",
		},
		{
			name:      "chk_promotion_version_not_empty",
			tableName: "environment_promotions",
			checkSQL:  "length(trim(version)) > 0",
		},
	}

	for _, constraint := range constraintChecks {
		// Check if constraint exists
		var exists bool
		checkSQL := `
            SELECT EXISTS (
                SELECT 1 FROM pg_constraint 
                WHERE conname = ? AND conrelid = (
                    SELECT oid FROM pg_class WHERE relname = ?
                )
            )
        `
		if err := db.Raw(checkSQL, constraint.name, constraint.tableName).Scan(&exists).Error; err != nil {
			log.Printf("Warning: Failed to check constraint existence: %v", err)
			continue
		}

		// If constraint doesn't exist, create it
		if !exists {
			addConstraintSQL := fmt.Sprintf(
				"ALTER TABLE %s ADD CONSTRAINT %s CHECK (%s)",
				constraint.tableName,
				constraint.name,
				constraint.checkSQL,
			)

			if err := db.Exec(addConstraintSQL).Error; err != nil {
				log.Printf("Warning: Failed to create constraint: %s - %v", addConstraintSQL, err)
				// Continue with other constraints even if one fails
			}
		}
	}

	return nil
}

// SeedPromotionData seeds initial data for promotion tables (if needed)
func SeedPromotionData(db *gorm.DB) error {
	log.Println("Seeding promotion data...")

	// Check if we need to seed any default data
	var count int64
	db.Model(&models.EnvironmentDeployment{}).Count(&count)

	if count == 0 {
		log.Println("No existing deployment data found, skipping seed")
	}

	log.Println("Promotion data seeding completed")
	return nil
}

// DropPromotionTables drops promotion tables (for testing/cleanup)
func DropPromotionTables(db *gorm.DB) error {
	log.Println("Dropping promotion tables...")

	tables := []string{
		"environment_promotions",
		"environment_deployments",
	}

	for _, table := range tables {
		if err := db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s CASCADE", table)).Error; err != nil {
			return fmt.Errorf("failed to drop table %s: %w", table, err)
		}
	}

	log.Println("Promotion tables dropped successfully")
	return nil
}

// ValidatePromotionTables validates that promotion tables exist and have correct structure
func ValidatePromotionTables(db *gorm.DB) error {
	log.Println("Validating promotion tables...")

	// Check if tables exist
	tables := []string{"environment_deployments", "environment_promotions"}
	for _, table := range tables {
		var exists bool
		err := db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = ?)", table).Scan(&exists).Error
		if err != nil {
			return fmt.Errorf("failed to check if table %s exists: %w", table, err)
		}
		if !exists {
			return fmt.Errorf("table %s does not exist", table)
		}
	}

	// Validate table structures by attempting to query them
	var deploymentCount, promotionCount int64
	if err := db.Model(&models.EnvironmentDeployment{}).Count(&deploymentCount).Error; err != nil {
		return fmt.Errorf("failed to query environment_deployments table: %w", err)
	}

	if err := db.Model(&models.EnvironmentPromotion{}).Count(&promotionCount).Error; err != nil {
		return fmt.Errorf("failed to query environment_promotions table: %w", err)
	}

	log.Printf("Promotion tables validation completed - deployments: %d, promotions: %d", deploymentCount, promotionCount)
	return nil
}

// GetPromotionMigrationInfo returns information about promotion migrations
func GetPromotionMigrationInfo() map[string]interface{} {
	return map[string]interface{}{
		"tables": []string{
			"environment_deployments",
			"environment_promotions",
		},
		"version":     "1.0.0",
		"description": "Environment promotion and version tracking tables",
		"features": []string{
			"Version tracking per environment",
			"Promotion workflow management",
			"Deployment history",
			"Git integration",
			"RBAC support",
		},
	}
}
