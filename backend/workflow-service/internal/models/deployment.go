package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
)

// Deployment represents a single deployment operation
type Deployment struct {
	ID              uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ProjectID       string         `json:"projectId" gorm:"not null;index"`
	ApplicationID   string         `json:"applicationId" gorm:"not null;index"`
	ApplicationName string         `json:"applicationName,omitempty"`
	EnvironmentID   string         `json:"environmentId" gorm:"not null;index"`
	EnvironmentName string         `json:"environmentName,omitempty"`
	WorkflowID      string         `json:"workflowId,omitempty"`
	ExecutionID     string         `json:"executionId,omitempty"`
	Version         string         `json:"version,omitempty"`
	Status          string         `json:"status" gorm:"not null;default:'created';index"` // created, running, success, failed, cancelled
	Configuration   datatypes.JSON `json:"configuration,omitempty"  gorm:"serializer:json"`
	Variables       datatypes.JSON `json:"variables,omitempty"  gorm:"serializer:json"`
	Tags            []string       `json:"tags,omitempty"  gorm:"serializer:json"`
	AutoStart       bool           `json:"autoStart" gorm:"default:false"`
	NotifyOnSuccess bool           `json:"notifyOnSuccess" gorm:"default:false"`
	NotifyOnFailure bool           `json:"notifyOnFailure" gorm:"default:true"`
	UserID          string         `json:"userId,omitempty"`
	Error           string         `json:"error,omitempty"`
	ProgressPercent int            `json:"progressPercent" gorm:"default:0"`
	CreatedAt       time.Time      `json:"createdAt" gorm:"index"`
	UpdatedAt       time.Time      `json:"updatedAt"`
	StartedAt       *time.Time     `json:"startedAt,omitempty"`
	CompletedAt     *time.Time     `json:"completedAt,omitempty"`
}

// BatchDeployment represents a batch deployment operation
type BatchDeployment struct {
	ID            uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ProjectID     string         `json:"projectId" gorm:"not null;index"`
	Status        string         `json:"status" gorm:"not null;default:'created';index"` // created, running, success, failed, cancelled, partial
	Strategy      string         `json:"strategy,omitempty"`                             // parallel, sequential, canary
	MaxParallel   int            `json:"maxParallel" gorm:"default:5"`
	AutoRollback  bool           `json:"autoRollback" gorm:"default:false"`
	UserID        string         `json:"userId,omitempty"`
	DeploymentIDs []string       `json:"deploymentIds,omitempty"  gorm:"serializer:json"` // Array of deployment IDs
	Progress      datatypes.JSON `json:"progress,omitempty"  gorm:"serializer:json"`      // Progress information
	Results       datatypes.JSON `json:"results,omitempty"  gorm:"serializer:json"`       // Results summary
	CreatedAt     time.Time      `json:"createdAt" gorm:"index"`
	UpdatedAt     time.Time      `json:"updatedAt"`
	StartedAt     *time.Time     `json:"startedAt,omitempty"`
	CompletedAt   *time.Time     `json:"completedAt,omitempty"`
}

// MultiAppDeployment represents a multi-application deployment to a single environment
type MultiAppDeployment struct {
	ID            uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ProjectID     string         `json:"projectId" gorm:"not null;index"`
	EnvironmentID string         `json:"environmentId" gorm:"not null;index"`
	Status        string         `json:"status" gorm:"not null;default:'created';index"`
	Strategy      string         `json:"strategy,omitempty"` // parallel, sequential, dependency-based
	UserID        string         `json:"userId,omitempty"`
	Applications  datatypes.JSON `json:"applications,omitempty"  gorm:"serializer:json"`  // Array of application configurations
	DeploymentIDs datatypes.JSON `json:"deploymentIds,omitempty"  gorm:"serializer:json"` // Generated deployment IDs
	Progress      datatypes.JSON `json:"progress,omitempty"  gorm:"serializer:json"`
	Results       datatypes.JSON `json:"results,omitempty"  gorm:"serializer:json"`
	CreatedAt     time.Time      `json:"createdAt" gorm:"index"`
	UpdatedAt     time.Time      `json:"updatedAt"`
	StartedAt     *time.Time     `json:"startedAt,omitempty"`
	CompletedAt   *time.Time     `json:"completedAt,omitempty"`
}

// DeploymentAuditLog represents an audit log entry for deployment operations
type DeploymentAuditLog struct {
	ID         uuid.UUID              `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID     string                 `json:"userId" gorm:"not null;index"`
	Action     string                 `json:"action" gorm:"not null;index"` // created, started, cancelled, completed, etc.
	ProjectID  string                 `json:"projectId" gorm:"not null;index"`
	ResourceID string                 `json:"resourceId,omitempty"` // Deployment ID, Batch ID, etc.
	Metadata   map[string]interface{} `json:"metadata,omitempty"  gorm:"serializer:json"`
	IPAddress  string                 `json:"ipAddress,omitempty"`
	UserAgent  string                 `json:"userAgent,omitempty"`
	CreatedAt  time.Time              `json:"createdAt" gorm:"index"`
}

// DeploymentTemplate represents a reusable deployment template
type DeploymentTemplate struct {
	ID            uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name          string         `json:"name" gorm:"not null"`
	Description   string         `json:"description,omitempty"`
	ProjectID     string         `json:"projectId" gorm:"not null;index"`
	Category      string         `json:"category,omitempty"` // web-app, microservice, database, etc.
	Configuration datatypes.JSON `json:"configuration,omitempty" gorm:"serializer:json"`
	Variables     datatypes.JSON `json:"variables,omitempty"  gorm:"serializer:json"`
	Requirements  datatypes.JSON `json:"requirements,omitempty"  gorm:"serializer:json"`
	Tags          datatypes.JSON `json:"tags,omitempty"  gorm:"serializer:json"`
	IsPublic      bool           `json:"isPublic" gorm:"default:false"`
	UsageCount    int            `json:"usageCount" gorm:"default:0"`
	Rating        float64        `json:"rating" gorm:"default:0"`
	CreatedBy     string         `json:"createdBy" gorm:"not null"`
	CreatedAt     time.Time      `json:"createdAt" gorm:"index"`
	UpdatedAt     time.Time      `json:"updatedAt"`
}

// DeploymentStatistics represents deployment statistics for a project/time period
type DeploymentStatistics struct {
	ID                uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ProjectID         string         `json:"projectId" gorm:"not null;index"`
	TimeRange         string         `json:"timeRange" gorm:"not null"` // 24h, 7d, 30d
	TotalDeployments  int            `json:"totalDeployments"`
	SuccessfulDeploys int            `json:"successfulDeploys"`
	FailedDeploys     int            `json:"failedDeploys"`
	SuccessRate       float64        `json:"successRate"`
	AverageTime       float64        `json:"averageTime"` // in seconds
	TrendData         datatypes.JSON `json:"trendData,omitempty"  gorm:"serializer:json"`
	TopApplications   datatypes.JSON `json:"topApplications,omitempty"  gorm:"serializer:json"`
	TopEnvironments   datatypes.JSON `json:"topEnvironments,omitempty"  gorm:"serializer:json"`
	GeneratedAt       time.Time      `json:"generatedAt" gorm:"index"`
	UpdatedAt         time.Time      `json:"updatedAt"`
}

// DeploymentEvent represents real-time deployment events
type DeploymentEvent struct {
	ID           uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	DeploymentID uuid.UUID      `json:"deploymentId" gorm:"not null;index"`
	Type         string         `json:"type" gorm:"not null"` // status_change, log, error, progress
	Message      string         `json:"message,omitempty"`
	Data         datatypes.JSON `json:"data,omitempty"  gorm:"serializer:json"`
	Severity     string         `json:"severity,omitempty"` // info, warning, error
	Source       string         `json:"source,omitempty"`   // workflow, plugin, system
	CreatedAt    time.Time      `json:"createdAt" gorm:"index"`
}

// DeploymentMetrics represents deployment performance metrics
type DeploymentMetrics struct {
	ID           uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	DeploymentID uuid.UUID `json:"deploymentId" gorm:"not null;index"`
	MetricName   string    `json:"metricName" gorm:"not null"`
	MetricValue  float64   `json:"metricValue"`
	Unit         string    `json:"unit,omitempty"`
	Timestamp    time.Time `json:"timestamp" gorm:"index"`
}

// Table names
func (Deployment) TableName() string {
	return "deployments"
}

func (BatchDeployment) TableName() string {
	return "batch_deployments"
}

func (MultiAppDeployment) TableName() string {
	return "multi_app_deployments"
}

func (DeploymentAuditLog) TableName() string {
	return "deployment_audit_logs"
}

func (DeploymentTemplate) TableName() string {
	return "deployment_templates"
}

func (DeploymentStatistics) TableName() string {
	return "deployment_statistics"
}

func (DeploymentEvent) TableName() string {
	return "deployment_events"
}

func (DeploymentMetrics) TableName() string {
	return "deployment_metrics"
}

// Request models

// MultiAppDeploymentRequest represents a request to create a multi-app deployment
type MultiAppDeploymentRequest struct {
	ProjectID     string                 `json:"projectId" binding:"required"`
	EnvironmentID string                 `json:"environmentId" binding:"required"`
	Applications  []ApplicationConfig    `json:"applications" binding:"required"`
	Strategy      string                 `json:"strategy,omitempty"` // parallel, sequential, dependency-based
	WorkflowID    string                 `json:"workflowId,omitempty"`
	Parameters    map[string]interface{} `json:"parameters,omitempty"`
}

// ApplicationConfig represents configuration for a single application in multi-app deployment
type ApplicationConfig struct {
	ApplicationID string                 `json:"applicationId" binding:"required"`
	Version       string                 `json:"version,omitempty"`
	Configuration map[string]interface{} `json:"configuration,omitempty"`
	Parameters    map[string]interface{} `json:"parameters,omitempty"`
}
