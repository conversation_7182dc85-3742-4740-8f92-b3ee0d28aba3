package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// EnvironmentDeployment tracks version deployments per environment
type EnvironmentDeployment struct {
	ID            uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	EnvironmentID uuid.UUID `gorm:"type:uuid;not null;index" json:"environment_id"`
	ProjectID     uuid.UUID `gorm:"type:uuid;not null;index" json:"project_id"`
	Version       string    `gorm:"not null" json:"version"`
	GitCommit     string    `gorm:"size:255" json:"git_commit,omitempty"`
	GitBranch     string    `gorm:"size:255" json:"git_branch,omitempty"`
	GitTag        string    `gorm:"size:255" json:"git_tag,omitempty"`
	DeployedBy    uuid.UUID `gorm:"type:uuid;not null" json:"deployed_by"`
	DeployedAt    time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"deployed_at"`
	Status        string    `gorm:"size:50;default:'active'" json:"status"`
	Health        string    `gorm:"size:50;default:'unknown'" json:"health"`
	Services      string    `gorm:"type:jsonb" json:"services,omitempty"` // JSON array of deployed services
	Metadata      string    `gorm:"type:jsonb" json:"metadata,omitempty"` // Additional deployment metadata
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// EnvironmentPromotion tracks promotion workflows between environments
type EnvironmentPromotion struct {
	ID                  uuid.UUID  `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	SourceEnvironmentID uuid.UUID  `gorm:"type:uuid;not null" json:"source_environment_id"`
	TargetEnvironmentID uuid.UUID  `gorm:"type:uuid;not null" json:"target_environment_id"`
	ProjectID           uuid.UUID  `gorm:"type:uuid;not null;index" json:"project_id"`
	WorkflowID          uuid.UUID  `gorm:"type:uuid" json:"workflow_id,omitempty"`
	WorkflowExecutionID *uuid.UUID `gorm:"type:uuid" json:"workflow_execution_id,omitempty"`
	Version             string     `gorm:"not null" json:"version"`
	GitCommit           string     `gorm:"size:255" json:"git_commit,omitempty"`
	GitBranch           string     `gorm:"size:255" json:"git_branch,omitempty"`
	GitTag              string     `gorm:"size:255" json:"git_tag,omitempty"`
	PromotionType       string     `gorm:"size:50;default:'manual'" json:"promotion_type"` // manual, automatic, scheduled
	RequireApproval     bool       `gorm:"default:false" json:"require_approval"`
	ApprovalUsers       string     `gorm:"type:jsonb" json:"approval_users,omitempty"` // JSON array of user IDs
	ApprovedBy          *uuid.UUID `gorm:"type:uuid" json:"approved_by,omitempty"`
	ApprovedAt          *time.Time `json:"approved_at,omitempty"`
	InitiatedBy         uuid.UUID  `gorm:"type:uuid;not null" json:"initiated_by"`
	Status              string     `gorm:"size:50;default:'pending'" json:"status"` // pending, approved, running, success, failed, cancelled
	StatusMessage       string     `gorm:"size:500" json:"status_message,omitempty"`
	StartedAt           *time.Time `json:"started_at,omitempty"`
	CompletedAt         *time.Time `json:"completed_at,omitempty"`
	PromotionConfig     string     `gorm:"type:jsonb" json:"promotion_config,omitempty"` // Additional promotion configuration
	CreatedAt           time.Time  `json:"created_at"`
	UpdatedAt           time.Time  `json:"updated_at"`
}

// VersionMatrix represents the current state of versions across environments
type VersionMatrix struct {
	ProjectID    uuid.UUID                     `json:"project_id"`
	ProjectName  string                        `json:"project_name"`
	Environments map[string]EnvironmentVersion `json:"environments"`
	LastUpdated  time.Time                     `json:"last_updated"`
}

// EnvironmentVersion represents version information for a specific environment
type EnvironmentVersion struct {
	EnvironmentID   uuid.UUID         `json:"environment_id"`
	EnvironmentName string            `json:"environment_name"`
	CurrentVersion  string            `json:"current_version"`
	GitCommit       string            `json:"git_commit,omitempty"`
	GitBranch       string            `json:"git_branch,omitempty"`
	GitTag          string            `json:"git_tag,omitempty"`
	DeployedAt      time.Time         `json:"deployed_at"`
	DeployedBy      uuid.UUID         `json:"deployed_by"`
	DeployedByName  string            `json:"deployed_by_name"`
	Health          string            `json:"health"`
	Status          string            `json:"status"`
	Services        []DeployedService `json:"services,omitempty"`
}

// DeployedService represents a service deployed in an environment
type DeployedService struct {
	Name      string    `json:"name"`
	Version   string    `json:"version"`
	Status    string    `json:"status"`
	Health    string    `json:"health"`
	UpdatedAt time.Time `json:"updated_at"`
}

// PromotionRequest represents a request to promote a version between environments
type PromotionRequest struct {
	WorkflowID        uuid.UUID              `json:"workflow_id" validate:"required"`
	ProjectID         uuid.UUID              `json:"project_id" validate:"required"`
	SourceEnvironment uuid.UUID              `json:"source_environment" validate:"required"`
	TargetEnvironment uuid.UUID              `json:"target_environment" validate:"required"`
	Version           VersionInfo            `json:"version" validate:"required"`
	PromotionType     string                 `json:"promotion_type" validate:"required,oneof=manual automatic scheduled"`
	RequireApproval   bool                   `json:"require_approval"`
	ApprovalUsers     []uuid.UUID            `json:"approval_users,omitempty"`
	PromotionConfig   map[string]interface{} `json:"promotion_config,omitempty"`
}

// VersionInfo represents version information for promotion
type VersionInfo struct {
	Number    string `json:"number" validate:"required"`
	GitCommit string `json:"git_commit,omitempty"`
	GitBranch string `json:"git_branch,omitempty"`
	GitTag    string `json:"git_tag,omitempty"`
}

// PromotionResponse represents the response after starting a promotion
type PromotionResponse struct {
	PromotionID         uuid.UUID  `json:"promotion_id"`
	WorkflowExecutionID *uuid.UUID `json:"workflow_execution_id,omitempty"`
	Status              string     `json:"status"`
	Message             string     `json:"message"`
	RequiresApproval    bool       `json:"requires_approval"`
}

// PromotionHistory represents promotion history for a project
type PromotionHistory struct {
	ProjectID  uuid.UUID              `json:"project_id"`
	Promotions []EnvironmentPromotion `json:"promotions"`
	TotalCount int64                  `json:"total_count"`
	Page       int                    `json:"page"`
	PageSize   int                    `json:"page_size"`
}

// DeploymentHistory represents deployment history for an environment
type DeploymentHistory struct {
	EnvironmentID uuid.UUID               `json:"environment_id"`
	Deployments   []EnvironmentDeployment `json:"deployments"`
	TotalCount    int64                   `json:"total_count"`
	Page          int                     `json:"page"`
	PageSize      int                     `json:"page_size"`
}

// TableName methods for GORM
func (EnvironmentDeployment) TableName() string {
	return "environment_deployments"
}

func (EnvironmentPromotion) TableName() string {
	return "environment_promotions"
}

// BeforeCreate hooks
func (ed *EnvironmentDeployment) BeforeCreate(tx *gorm.DB) error {
	if ed.ID == uuid.Nil {
		ed.ID = uuid.New()
	}
	return nil
}

func (ep *EnvironmentPromotion) BeforeCreate(tx *gorm.DB) error {
	if ep.ID == uuid.Nil {
		ep.ID = uuid.New()
	}
	return nil
}

// Status constants
const (
	// Deployment statuses
	DeploymentStatusActive   = "active"
	DeploymentStatusInactive = "inactive"
	DeploymentStatusFailed   = "failed"

	// Health statuses
	HealthStatusHealthy   = "healthy"
	HealthStatusDegraded  = "degraded"
	HealthStatusUnhealthy = "unhealthy"
	HealthStatusUnknown   = "unknown"

	// Promotion statuses
	PromotionStatusPending   = "pending"
	PromotionStatusApproved  = "approved"
	PromotionStatusRunning   = "running"
	PromotionStatusSuccess   = "success"
	PromotionStatusFailed    = "failed"
	PromotionStatusCancelled = "cancelled"

	// Promotion types
	PromotionTypeManual    = "manual"
	PromotionTypeAutomatic = "automatic"
	PromotionTypeScheduled = "scheduled"
)
