package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/internal/models"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// EnvironmentClient handles communication with the environment service
type EnvironmentClient struct {
	baseURL    string
	httpClient *http.Client
	logger     *zap.Logger
}

// NewEnvironmentClient creates a new environment service client
func NewEnvironmentClient(baseURL string, logger *zap.Logger) *EnvironmentClient {
	return &EnvironmentClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
	}
}

// Environment represents an environment from the environment service
type Environment struct {
	ID        string                 `json:"id"`
	ProjectID string                 `json:"project_id"`
	Name      string                 `json:"name"`
	Type      string                 `json:"type"`
	Status    string                 `json:"status"`
	Health    string                 `json:"health"`
	Provider  ProviderConfig         `json:"provider"`
	Variables map[string]interface{} `json:"variables"`
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
}

// ProviderConfig represents provider configuration
type ProviderConfig struct {
	Type   string                 `json:"type"`
	Config map[string]interface{} `json:"config"`
}

// EnvironmentStatus represents environment health status
type EnvironmentStatus struct {
	EnvironmentID string                 `json:"environment_id"`
	Status        string                 `json:"status"`
	Health        string                 `json:"health"`
	LastCheck     time.Time              `json:"last_check"`
	Metrics       map[string]interface{} `json:"metrics"`
	Issues        []string               `json:"issues"`
}

// GetProjectEnvironments retrieves all environments for a project
func (c *EnvironmentClient) GetProjectEnvironments(ctx context.Context, projectID string) ([]Environment, error) {
	url := fmt.Sprintf("%s/api/v1/environments?project_id=%s", c.baseURL, projectID)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("environment service returned status %d", resp.StatusCode)
	}

	var response struct {
		Environments []Environment `json:"environments"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return response.Environments, nil
}

// GetEnvironment retrieves a specific environment
func (c *EnvironmentClient) GetEnvironment(ctx context.Context, environmentID string) (*Environment, error) {
	url := fmt.Sprintf("%s/api/v1/environments/%s", c.baseURL, environmentID)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("environment service returned status %d", resp.StatusCode)
	}

	var environment Environment
	if err := json.NewDecoder(resp.Body).Decode(&environment); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &environment, nil
}

// GetEnvironmentStatus retrieves environment health status
func (c *EnvironmentClient) GetEnvironmentStatus(ctx context.Context, environmentID string) (*EnvironmentStatus, error) {
	url := fmt.Sprintf("%s/api/v1/environments/%s/status", c.baseURL, environmentID)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("environment service returned status %d", resp.StatusCode)
	}

	var status EnvironmentStatus
	if err := json.NewDecoder(resp.Body).Decode(&status); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &status, nil
}

// TriggerDeployment triggers a deployment to an environment
func (c *EnvironmentClient) TriggerDeployment(ctx context.Context, environmentID string, deployment *DeploymentRequest) (*DeploymentResponse, error) {
	url := fmt.Sprintf("%s/api/v1/environments/%s/deploy", c.baseURL, environmentID)

	body, err := json.Marshal(deployment)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal deployment request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(body))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("environment service returned status %d", resp.StatusCode)
	}

	var response DeploymentResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &response, nil
}

// DeploymentRequest represents a deployment request
type DeploymentRequest struct {
	Version     string                 `json:"version"`
	GitCommit   string                 `json:"git_commit,omitempty"`
	GitBranch   string                 `json:"git_branch,omitempty"`
	GitTag      string                 `json:"git_tag,omitempty"`
	WorkflowID  uuid.UUID              `json:"workflow_id"`
	ExecutionID uuid.UUID              `json:"execution_id"`
	Variables   map[string]interface{} `json:"variables,omitempty"`
	DeployedBy  uuid.UUID              `json:"deployed_by"`
}

// DeploymentResponse represents a deployment response
type DeploymentResponse struct {
	DeploymentID uuid.UUID `json:"deployment_id"`
	Status       string    `json:"status"`
	Message      string    `json:"message"`
}

// GetCurrentDeployment gets the current deployment for an environment
func (c *EnvironmentClient) GetCurrentDeployment(ctx context.Context, environmentID string) (*models.EnvironmentDeployment, error) {
	url := fmt.Sprintf("%s/api/v1/environments/%s/current-deployment", c.baseURL, environmentID)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return nil, nil // No current deployment
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("environment service returned status %d", resp.StatusCode)
	}

	var deployment models.EnvironmentDeployment
	if err := json.NewDecoder(resp.Body).Decode(&deployment); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &deployment, nil
}

// ValidateEnvironmentAccess validates if an environment exists and is accessible
func (c *EnvironmentClient) ValidateEnvironmentAccess(ctx context.Context, environmentID, projectID string) error {
	env, err := c.GetEnvironment(ctx, environmentID)
	if err != nil {
		return fmt.Errorf("failed to get environment: %w", err)
	}

	if env.ProjectID != projectID {
		return fmt.Errorf("environment %s does not belong to project %s", environmentID, projectID)
	}

	return nil
}
