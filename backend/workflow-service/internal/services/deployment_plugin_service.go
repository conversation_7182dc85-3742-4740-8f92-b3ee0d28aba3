package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/internal/plugins"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// DeploymentPluginService handles deployment-specific plugin operations
type DeploymentPluginService struct {
	db            *gorm.DB
	pluginManager *plugins.PluginManager
	logger        *zap.Logger
}

// DeploymentPlugin represents a plugin that can be used for deployments
type DeploymentPlugin struct {
	ID                  string                  `json:"id"`
	Name                string                  `json:"name"`
	Version             string                  `json:"version"`
	Description         string                  `json:"description"`
	Type                string                  `json:"type"`
	SupportedArtifacts  []string                `json:"supportedArtifacts"`
	ConfigurationSchema []PluginConfigField     `json:"configurationSchema"`
	IsEnabled           bool                    `json:"isEnabled"`
	IsDefault           bool                    `json:"isDefault"`
	PluginInstance      *plugins.PluginInstance `json:"pluginInstance,omitempty"`
}

// PluginConfigField represents a configuration field for a plugin
type PluginConfigField struct {
	Key          string                 `json:"key"`
	Label        string                 `json:"label"`
	Type         string                 `json:"type"`
	Required     bool                   `json:"required"`
	DefaultValue interface{}            `json:"defaultValue,omitempty"`
	Description  string                 `json:"description,omitempty"`
	Options      []PluginConfigOption   `json:"options,omitempty"`
	Validation   map[string]interface{} `json:"validation,omitempty"`
}

// PluginConfigOption represents an option for select-type fields
type PluginConfigOption struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

// EnvironmentPluginMapping represents the mapping between environments and plugins
type EnvironmentPluginMapping struct {
	ID                 string                 `json:"id,omitempty" gorm:"primaryKey"`
	EnvironmentID      string                 `json:"environmentId" gorm:"index"`
	DefaultPluginID    string                 `json:"defaultPluginId"`
	AllowUserOverride  bool                   `json:"allowUserOverride"`
	AdminConfiguration []byte                 `json:"-" gorm:"column:admin_configuration;type:json"`
	AdminConfig        map[string]interface{} `json:"adminConfiguration" gorm:"-"`
	CreatedAt          int64                  `json:"createdAt"`
	UpdatedAt          int64                  `json:"updatedAt"`
}

// DeploymentPluginRequest represents a deployment request for plugins
type DeploymentPluginRequest struct {
	DeployableID      string                 `json:"deployableId"`
	EnvironmentID     string                 `json:"environmentId"`
	PluginID          string                 `json:"pluginId"`
	Configuration     map[string]interface{} `json:"configuration"`
	WorkflowID        string                 `json:"workflowId,omitempty"`
	UserConfiguration map[string]interface{} `json:"userConfiguration,omitempty"`
}

// DeploymentResult represents the result of a deployment
type DeploymentResult struct {
	Success      bool                   `json:"success"`
	DeploymentID string                 `json:"deploymentId"`
	WorkflowID   string                 `json:"workflowId"`
	Status       string                 `json:"status"`
	Message      string                 `json:"message"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// NewDeploymentPluginService creates a new deployment plugin service
func NewDeploymentPluginService(db *gorm.DB, pluginManager *plugins.PluginManager, logger *zap.Logger) *DeploymentPluginService {
	service := &DeploymentPluginService{
		db:            db,
		pluginManager: pluginManager,
		logger:        logger,
	}

	// Initialize database schema
	if err := service.initializeSchema(); err != nil {
		logger.Error("Failed to initialize deployment plugin schema", zap.Error(err))
	}

	return service
}

// initializeSchema creates the database tables if they don't exist
func (s *DeploymentPluginService) initializeSchema() error {
	return s.db.AutoMigrate(&EnvironmentPluginMapping{})
}

// GetAvailablePlugins returns all plugins that can be used for deployments
func (s *DeploymentPluginService) GetAvailablePlugins(ctx context.Context) ([]*DeploymentPlugin, error) {
	// Get all plugins from the plugin manager
	pluginInstances := s.pluginManager.ListPlugins()

	deploymentPlugins := make([]*DeploymentPlugin, 0)

	for _, instance := range pluginInstances {
		if !instance.Enabled {
			continue
		}

		// Convert plugin instance to deployment plugin
		deploymentPlugin := s.convertToDeploymentPlugin(instance)
		deploymentPlugins = append(deploymentPlugins, deploymentPlugin)
	}

	s.logger.Info("Retrieved available deployment plugins",
		zap.Int("count", len(deploymentPlugins)))

	return deploymentPlugins, nil
}

// GetCompatiblePlugins returns plugins compatible with specific artifact types
func (s *DeploymentPluginService) GetCompatiblePlugins(ctx context.Context, artifactTypes []string) ([]*DeploymentPlugin, error) {
	allPlugins, err := s.GetAvailablePlugins(ctx)
	if err != nil {
		return nil, err
	}

	compatible := make([]*DeploymentPlugin, 0)
	for _, plugin := range allPlugins {
		if s.isPluginCompatible(plugin, artifactTypes) {
			compatible = append(compatible, plugin)
		}
	}

	s.logger.Info("Found compatible deployment plugins",
		zap.Strings("artifactTypes", artifactTypes),
		zap.Int("compatibleCount", len(compatible)))

	return compatible, nil
}

// GetEnvironmentPluginMappings returns all environment-plugin mappings
func (s *DeploymentPluginService) GetEnvironmentPluginMappings(ctx context.Context) ([]*EnvironmentPluginMapping, error) {
	var mappings []*EnvironmentPluginMapping

	if err := s.db.Find(&mappings).Error; err != nil {
		return nil, fmt.Errorf("failed to get environment plugin mappings: %w", err)
	}

	// Process the JSON data for each mapping
	for _, mapping := range mappings {
		if mapping.AdminConfiguration != nil {
			var adminConfig map[string]interface{}
			if err := json.Unmarshal(mapping.AdminConfiguration, &adminConfig); err != nil {
				return nil, fmt.Errorf("failed to unmarshal admin configuration: %w", err)
			}
			mapping.AdminConfig = adminConfig
		} else {
			mapping.AdminConfig = make(map[string]interface{})
		}
	}

	return mappings, nil
}

// GetDefaultPluginForEnvironment returns the default plugin for a specific environment
func (s *DeploymentPluginService) GetDefaultPluginForEnvironment(ctx context.Context, environmentID string) (*DeploymentPlugin, error) {
	var mapping EnvironmentPluginMapping

	if err := s.db.Where("environment_id = ?", environmentID).First(&mapping).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // No mapping found
		}
		return nil, fmt.Errorf("failed to get environment plugin mapping: %w", err)
	}

	// Unmarshal the admin configuration JSON if present
	if mapping.AdminConfiguration != nil {
		var adminConfig map[string]interface{}
		if err := json.Unmarshal(mapping.AdminConfiguration, &adminConfig); err != nil {
			return nil, fmt.Errorf("failed to unmarshal admin configuration: %w", err)
		}
		mapping.AdminConfig = adminConfig
	} else {
		mapping.AdminConfig = make(map[string]interface{})
	}

	// Get the plugin instance
	pluginInstance, err := s.pluginManager.GetPlugin(mapping.DefaultPluginID)
	if err != nil {
		return nil, fmt.Errorf("failed to get plugin %s: %w", mapping.DefaultPluginID, err)
	}

	deploymentPlugin := s.convertToDeploymentPlugin(pluginInstance)
	deploymentPlugin.IsDefault = true

	return deploymentPlugin, nil
}

// SetEnvironmentPluginMapping creates or updates an environment-plugin mapping
func (s *DeploymentPluginService) SetEnvironmentPluginMapping(ctx context.Context, mapping *EnvironmentPluginMapping) error {
	// Validate that the plugin exists
	_, err := s.pluginManager.GetPlugin(mapping.DefaultPluginID)
	if err != nil {
		return fmt.Errorf("plugin %s not found: %w", mapping.DefaultPluginID, err)
	}

	// Generate ID if not provided
	if mapping.ID == "" {
		mapping.ID = fmt.Sprintf("%s-%s", mapping.EnvironmentID, mapping.DefaultPluginID)
	}

	// Marshal the admin configuration to JSON
	if mapping.AdminConfig != nil {
		adminConfigJSON, err := json.Marshal(mapping.AdminConfig)
		if err != nil {
			return fmt.Errorf("failed to marshal admin configuration: %w", err)
		}
		mapping.AdminConfiguration = adminConfigJSON
	}

	mapping.UpdatedAt = getCurrentTimeMillis()
	if mapping.CreatedAt == 0 {
		mapping.CreatedAt = mapping.UpdatedAt
	}

	if err := s.db.Save(mapping).Error; err != nil {
		return fmt.Errorf("failed to save environment plugin mapping: %w", err)
	}

	s.logger.Info("Environment plugin mapping saved",
		zap.String("environmentId", mapping.EnvironmentID),
		zap.String("pluginId", mapping.DefaultPluginID))

	return nil
}

// ExecuteDeployment executes a deployment using the specified plugin
func (s *DeploymentPluginService) ExecuteDeployment(ctx context.Context, request *DeploymentPluginRequest) (*DeploymentResult, error) {
	s.logger.Info("Executing deployment",
		zap.String("deployableId", request.DeployableID),
		zap.String("environmentId", request.EnvironmentID),
		zap.String("pluginId", request.PluginID))

	// Get the plugin instance
	pluginInstance, err := s.pluginManager.GetPlugin(request.PluginID)
	if err != nil {
		return nil, fmt.Errorf("plugin %s not found: %w", request.PluginID, err)
	}

	if !pluginInstance.Enabled {
		return nil, fmt.Errorf("plugin %s is disabled", request.PluginID)
	}

	// Merge admin configuration with user configuration
	finalConfig := make(map[string]interface{})

	// First apply admin configuration from environment mapping
	if mapping, err := s.getEnvironmentMapping(request.EnvironmentID); err == nil && mapping != nil {
		// Unmarshal the admin configuration JSON if needed
		if mapping.AdminConfiguration != nil && len(mapping.AdminConfig) == 0 {
			if err := json.Unmarshal(mapping.AdminConfiguration, &mapping.AdminConfig); err != nil {
				s.logger.Warn("Failed to unmarshal admin configuration", zap.Error(err))
			}
		}

		for k, v := range mapping.AdminConfig {
			finalConfig[k] = v
		}
	}

	// Then apply user configuration (can override admin config if allowed)
	for k, v := range request.Configuration {
		finalConfig[k] = v
	}

	// Create deployment workflow
	workflowID := request.WorkflowID
	if workflowID == "" {
		workflowID = s.generateWorkflowID(request.DeployableID, request.EnvironmentID)
	}

	// Execute the deployment through the plugin
	deploymentID := s.generateDeploymentID(request.DeployableID, request.EnvironmentID)

	// For now, we'll simulate the deployment execution
	// In a real implementation, this would trigger the actual plugin execution
	result := &DeploymentResult{
		Success:      true,
		DeploymentID: deploymentID,
		WorkflowID:   workflowID,
		Status:       "initiated",
		Message:      fmt.Sprintf("Deployment initiated using plugin %s", request.PluginID),
		Metadata: map[string]interface{}{
			"pluginId":      request.PluginID,
			"pluginType":    pluginInstance.Type,
			"configuration": finalConfig,
		},
	}

	s.logger.Info("Deployment initiated",
		zap.String("deploymentId", deploymentID),
		zap.String("workflowId", workflowID),
		zap.String("pluginId", request.PluginID))

	return result, nil
}

// ValidateConfiguration validates plugin configuration
func (s *DeploymentPluginService) ValidateConfiguration(ctx context.Context, pluginID string, configuration map[string]interface{}) (*ValidationResult, error) {
	pluginInstance, err := s.pluginManager.GetPlugin(pluginID)
	if err != nil {
		return nil, fmt.Errorf("plugin %s not found: %w", pluginID, err)
	}

	deploymentPlugin := s.convertToDeploymentPlugin(pluginInstance)

	// Validate configuration against schema
	errors := s.validateConfigurationFields(deploymentPlugin.ConfigurationSchema, configuration)

	result := &ValidationResult{
		Valid:  len(errors) == 0,
		Errors: errors,
	}

	return result, nil
}

// Helper methods

func (s *DeploymentPluginService) convertToDeploymentPlugin(instance *plugins.PluginInstance) *DeploymentPlugin {
	// Map the plugin instance to deployment plugin format
	description := fmt.Sprintf("Plugin %s version %s", instance.Name, instance.Version)

	deploymentPlugin := &DeploymentPlugin{
		ID:             instance.Name,
		Name:           instance.Name,
		Version:        instance.Version,
		Description:    description,
		Type:           instance.Type,
		IsEnabled:      instance.Enabled,
		IsDefault:      false,
		PluginInstance: instance,
	}

	// Set supported artifacts based on plugin type
	deploymentPlugin.SupportedArtifacts = s.getSupportedArtifactsForPluginType(instance.Type)

	// Set configuration schema based on plugin type
	deploymentPlugin.ConfigurationSchema = s.getConfigurationSchemaForPluginType(instance.Type)

	return deploymentPlugin
}

func (s *DeploymentPluginService) getSupportedArtifactsForPluginType(pluginType string) []string {
	// Define supported artifacts based on plugin type
	supportedArtifacts := map[string][]string{
		"helm":       {"HELM_CHART", "DOCKER_IMAGE", "CONTAINER", "APPLICATION", "MICROSERVICE"},
		"kubernetes": {"KUBERNETES_MANIFEST", "DOCKER_IMAGE", "CONTAINER"},
		"docker":     {"DOCKER_COMPOSE", "DOCKER_IMAGE", "CONTAINER", "APPLICATION", "MICROSERVICE"},
		"ansible":    {"JAR", "WAR", "EAR", "ZIP", "APPLICATION", "SERVICE", "INFRASTRUCTURE"},
		"terraform":  {"TERRAFORM_MODULE", "INFRASTRUCTURE"},
		"generic":    {"JAR", "WAR", "EAR", "ZIP", "HELM_CHART", "DOCKER_IMAGE", "DOCKER_COMPOSE", "KUBERNETES_MANIFEST", "TERRAFORM_MODULE", "APPLICATION", "MICROSERVICE", "SERVICE", "CONTAINER", "INFRASTRUCTURE"},
	}

	if artifacts, exists := supportedArtifacts[pluginType]; exists {
		return artifacts
	}

	// Default to generic support
	return supportedArtifacts["generic"]
}

func (s *DeploymentPluginService) getConfigurationSchemaForPluginType(pluginType string) []PluginConfigField {
	// Define configuration schemas based on plugin type
	schemas := map[string][]PluginConfigField{
		"helm": {
			{
				Key:          "namespace",
				Label:        "Kubernetes Namespace",
				Type:         "string",
				Required:     true,
				DefaultValue: "default",
				Description:  "Target Kubernetes namespace for deployment",
			},
			{
				Key:          "replicas",
				Label:        "Number of Replicas",
				Type:         "number",
				Required:     false,
				DefaultValue: 1,
				Validation:   map[string]interface{}{"min": 1, "max": 20},
			},
			{
				Key:          "createNamespace",
				Label:        "Create Namespace if Missing",
				Type:         "boolean",
				Required:     false,
				DefaultValue: false,
			},
		},
		"kubernetes": {
			{
				Key:          "namespace",
				Label:        "Kubernetes Namespace",
				Type:         "string",
				Required:     true,
				DefaultValue: "default",
			},
			{
				Key:          "strategy",
				Label:        "Deployment Strategy",
				Type:         "select",
				Required:     true,
				DefaultValue: "RollingUpdate",
				Options: []PluginConfigOption{
					{Label: "Rolling Update", Value: "RollingUpdate"},
					{Label: "Recreate", Value: "Recreate"},
				},
			},
		},
		"docker": {
			{
				Key:         "projectName",
				Label:       "Project Name",
				Type:        "string",
				Required:    false,
				Description: "Docker Compose project name (defaults to directory name)",
			},
			{
				Key:          "pullImages",
				Label:        "Pull Latest Images",
				Type:         "boolean",
				Required:     false,
				DefaultValue: true,
			},
		},
	}

	if schema, exists := schemas[pluginType]; exists {
		return schema
	}

	// Default generic schema
	return []PluginConfigField{
		{
			Key:         "command",
			Label:       "Deployment Command",
			Type:        "textarea",
			Required:    true,
			Description: "Command or script to execute for deployment",
		},
		{
			Key:         "workingDirectory",
			Label:       "Working Directory",
			Type:        "string",
			Required:    false,
			Description: "Directory to execute the command from",
		},
	}
}

func (s *DeploymentPluginService) isPluginCompatible(plugin *DeploymentPlugin, artifactTypes []string) bool {
	for _, artifactType := range artifactTypes {
		for _, supportedType := range plugin.SupportedArtifacts {
			if supportedType == artifactType {
				return true
			}
		}
	}
	return false
}

func (s *DeploymentPluginService) getEnvironmentMapping(environmentID string) (*EnvironmentPluginMapping, error) {
	var mapping EnvironmentPluginMapping
	err := s.db.Where("environment_id = ?", environmentID).First(&mapping).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	// Unmarshal the admin configuration JSON if present
	if mapping.AdminConfiguration != nil {
		var adminConfig map[string]interface{}
		if err := json.Unmarshal(mapping.AdminConfiguration, &adminConfig); err != nil {
			return nil, fmt.Errorf("failed to unmarshal admin configuration: %w", err)
		}
		mapping.AdminConfig = adminConfig
	} else {
		mapping.AdminConfig = make(map[string]interface{})
	}

	return &mapping, nil
}

func (s *DeploymentPluginService) validateConfigurationFields(schema []PluginConfigField, config map[string]interface{}) []string {
	var errors []string

	for _, field := range schema {
		value, exists := config[field.Key]

		if field.Required && !exists {
			errors = append(errors, fmt.Sprintf("Required field '%s' is missing", field.Key))
			continue
		}

		if !exists {
			continue
		}

		// Validate field type and constraints
		if err := s.validateFieldValue(field, value); err != nil {
			errors = append(errors, fmt.Sprintf("Field '%s': %s", field.Key, err.Error()))
		}
	}

	return errors
}

func (s *DeploymentPluginService) validateFieldValue(field PluginConfigField, value interface{}) error {
	switch field.Type {
	case "string":
		if _, ok := value.(string); !ok {
			return fmt.Errorf("expected string value")
		}
	case "number":
		var numVal float64
		switch v := value.(type) {
		case float64:
			numVal = v
		case int:
			numVal = float64(v)
		default:
			return fmt.Errorf("expected number value")
		}

		if validation := field.Validation; validation != nil {
			if min, exists := validation["min"]; exists {
				if minVal, ok := min.(float64); ok && numVal < minVal {
					return fmt.Errorf("value must be at least %v", minVal)
				}
			}
			if max, exists := validation["max"]; exists {
				if maxVal, ok := max.(float64); ok && numVal > maxVal {
					return fmt.Errorf("value must be at most %v", maxVal)
				}
			}
		}
	case "boolean":
		if _, ok := value.(bool); !ok {
			return fmt.Errorf("expected boolean value")
		}
	case "select":
		strVal, ok := value.(string)
		if !ok {
			return fmt.Errorf("expected string value for select field")
		}

		validOption := false
		for _, option := range field.Options {
			if option.Value == strVal {
				validOption = true
				break
			}
		}
		if !validOption {
			return fmt.Errorf("invalid option selected")
		}
	}

	return nil
}

func (s *DeploymentPluginService) generateWorkflowID(deployableID, environmentID string) string {
	return fmt.Sprintf("deploy-%s-%s-%d", deployableID, environmentID, getCurrentTimeMillis())
}

func (s *DeploymentPluginService) generateDeploymentID(deployableID, environmentID string) string {
	return fmt.Sprintf("deployment-%s-%s-%d", deployableID, environmentID, getCurrentTimeMillis())
}

func getCurrentTimeMillis() int64 {
	return time.Now().UnixNano() / int64(time.Millisecond)
}
