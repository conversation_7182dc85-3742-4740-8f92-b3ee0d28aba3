package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/internal/models"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// EnvironmentPromotionService handles environment promotion operations with RBAC
type EnvironmentPromotionService struct {
	db                  *gorm.DB
	logger              *zap.Logger
	executionService    *ExecutionService
	loggingService      *LoggingService
	environmentClient   *EnvironmentClient
	notificationService *NotificationService
}

// NewEnvironmentPromotionService creates a new environment promotion service
func NewEnvironmentPromotionService(
	db *gorm.DB,
	logger *zap.Logger,
	executionService *ExecutionService,
	loggingService *LoggingService,
	environmentClient *EnvironmentClient,
	notificationService *NotificationService,
) *EnvironmentPromotionService {
	return &EnvironmentPromotionService{
		db:                  db,
		logger:              logger,
		executionService:    executionService,
		loggingService:      loggingService,
		environmentClient:   environmentClient,
		notificationService: notificationService,
	}
}

// GetVersionMatrix returns the version matrix for a project with RBAC filtering
func (s *EnvironmentPromotionService) GetVersionMatrix(ctx context.Context, userID, projectID string) (*models.VersionMatrix, error) {
	// TODO: Add RBAC check - verify user has access to this project
	// For now, we'll implement basic functionality and add RBAC later

	s.logger.Info("Getting version matrix",
		zap.String("userID", userID),
		zap.String("projectID", projectID))

	// Get environments for the project
	environments, err := s.getProjectEnvironments(projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to get project environments: %w", err)
	}

	// Build version matrix
	matrix := &models.VersionMatrix{
		ProjectID:    uuid.MustParse(projectID),
		Environments: make(map[string]models.EnvironmentVersion),
		LastUpdated:  time.Now(),
	}

	// Get current deployments for each environment
	for _, env := range environments {
		envVersion, err := s.getCurrentEnvironmentVersion(env.ID.String())
		if err != nil {
			s.logger.Error("Failed to get environment version",
				zap.Error(err),
				zap.String("environmentID", env.ID.String()))
			continue
		}

		if envVersion != nil {
			matrix.Environments[env.Name] = *envVersion
		}
	}

	return matrix, nil
}

// StartPromotion initiates a promotion workflow with RBAC validation
func (s *EnvironmentPromotionService) StartPromotion(ctx context.Context, userID string, request *models.PromotionRequest) (*models.PromotionResponse, error) {
	// TODO: Add RBAC validation
	// - Validate user has access to the project
	// - Validate user has deploy permission for target environment
	// - Validate environments belong to the project

	s.logger.Info("Starting promotion",
		zap.String("userID", userID),
		zap.String("projectID", request.ProjectID.String()),
		zap.String("version", request.Version.Number))

	// Validate environments exist and belong to the project
	if err := s.validatePromotionEnvironments(ctx, request); err != nil {
		return nil, fmt.Errorf("environment validation failed: %w", err)
	}

	// TODO: Enrich version information with Git data via plugin system
	s.logger.Info("Version information will be enriched via Git plugins in the future")

	// Create promotion record
	promotion := &models.EnvironmentPromotion{
		SourceEnvironmentID: request.SourceEnvironment,
		TargetEnvironmentID: request.TargetEnvironment,
		ProjectID:           request.ProjectID,
		WorkflowID:          request.WorkflowID,
		Version:             request.Version.Number,
		GitCommit:           request.Version.GitCommit,
		GitBranch:           request.Version.GitBranch,
		GitTag:              request.Version.GitTag,
		PromotionType:       request.PromotionType,
		RequireApproval:     request.RequireApproval,
		InitiatedBy:         uuid.MustParse(userID),
		Status:              models.PromotionStatusPending,
	}

	// Set approval users if required
	if request.RequireApproval && len(request.ApprovalUsers) > 0 {
		approvalUsersJSON, err := json.Marshal(request.ApprovalUsers)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal approval users: %w", err)
		}
		promotion.ApprovalUsers = string(approvalUsersJSON)
	}

	// Set promotion config
	if request.PromotionConfig != nil {
		configJSON, err := json.Marshal(request.PromotionConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal promotion config: %w", err)
		}
		promotion.PromotionConfig = string(configJSON)
	}

	// Save promotion record
	if err := s.db.Create(promotion).Error; err != nil {
		return nil, fmt.Errorf("failed to create promotion record: %w", err)
	}

	// Start workflow execution if no approval required
	var workflowExecutionID *uuid.UUID
	if !request.RequireApproval {
		executionID, err := s.startPromotionWorkflow(ctx, promotion)
		if err != nil {
			// Update promotion status to failed
			s.updatePromotionStatus(promotion.ID, models.PromotionStatusFailed, err.Error())

			// Send failure notification
			s.sendPromotionNotification(ctx, promotion, "failed", err.Error())

			return nil, fmt.Errorf("failed to start promotion workflow: %w", err)
		}
		workflowExecutionID = &executionID
		promotion.WorkflowExecutionID = workflowExecutionID
		promotion.Status = models.PromotionStatusRunning
		now := time.Now()
		promotion.StartedAt = &now
		s.db.Save(promotion)

		// Send started notification
		s.sendPromotionNotification(ctx, promotion, "started", "")

		// TODO: Start monitoring the target environment via shared monitoring module
		s.logger.Info("Environment health monitoring will be implemented via shared monitoring module")
	} else {
		// Send approval required notification
		approvalUserStrings := make([]string, len(request.ApprovalUsers))
		for i, userID := range request.ApprovalUsers {
			approvalUserStrings[i] = userID.String()
		}
		s.sendPromotionApprovalNotification(ctx, promotion, approvalUserStrings)
	}

	s.logger.Info("Promotion started successfully",
		zap.String("promotionID", promotion.ID.String()),
		zap.String("status", promotion.Status))

	return &models.PromotionResponse{
		PromotionID:         promotion.ID,
		WorkflowExecutionID: workflowExecutionID,
		Status:              promotion.Status,
		Message:             "Promotion initiated successfully",
		RequiresApproval:    request.RequireApproval,
	}, nil
}

// GetPromotionStatus returns the status of a promotion with RBAC validation
func (s *EnvironmentPromotionService) GetPromotionStatus(ctx context.Context, userID, promotionID string) (*models.EnvironmentPromotion, error) {
	var promotion models.EnvironmentPromotion
	if err := s.db.Where("id = ?", promotionID).First(&promotion).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("promotion not found")
		}
		return nil, fmt.Errorf("failed to get promotion: %w", err)
	}

	// TODO: Add RBAC check - verify user has access to the project

	return &promotion, nil
}

// GetPromotionHistory returns promotion history for a project with RBAC filtering
func (s *EnvironmentPromotionService) GetPromotionHistory(ctx context.Context, userID, projectID string, page, pageSize int) (*models.PromotionHistory, error) {
	// TODO: Add RBAC check - verify user has access to this project

	// Calculate offset
	offset := (page - 1) * pageSize

	// Get promotions with pagination
	var promotions []models.EnvironmentPromotion
	var totalCount int64

	// Count total promotions
	if err := s.db.Model(&models.EnvironmentPromotion{}).
		Where("project_id = ?", projectID).
		Count(&totalCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count promotions: %w", err)
	}

	// Get paginated promotions
	if err := s.db.Where("project_id = ?", projectID).
		Order("created_at DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&promotions).Error; err != nil {
		return nil, fmt.Errorf("failed to get promotions: %w", err)
	}

	return &models.PromotionHistory{
		ProjectID:  uuid.MustParse(projectID),
		Promotions: promotions,
		TotalCount: totalCount,
		Page:       page,
		PageSize:   pageSize,
	}, nil
}

// GetDeploymentHistory returns deployment history for an environment with RBAC validation
func (s *EnvironmentPromotionService) GetDeploymentHistory(ctx context.Context, userID, environmentID string, page, pageSize int) (*models.DeploymentHistory, error) {
	// TODO: Add RBAC check - get environment project and verify user access

	// Calculate offset
	offset := (page - 1) * pageSize

	// Get deployments with pagination
	var deployments []models.EnvironmentDeployment
	var totalCount int64

	// Count total deployments
	if err := s.db.Model(&models.EnvironmentDeployment{}).
		Where("environment_id = ?", environmentID).
		Count(&totalCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count deployments: %w", err)
	}

	// Get paginated deployments
	if err := s.db.Where("environment_id = ?", environmentID).
		Order("deployed_at DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&deployments).Error; err != nil {
		return nil, fmt.Errorf("failed to get deployments: %w", err)
	}

	return &models.DeploymentHistory{
		EnvironmentID: uuid.MustParse(environmentID),
		Deployments:   deployments,
		TotalCount:    totalCount,
		Page:          page,
		PageSize:      pageSize,
	}, nil
}

// Helper methods

// getProjectEnvironments gets environments for a project (placeholder)
func (s *EnvironmentPromotionService) getProjectEnvironments(projectID string) ([]struct {
	ID   uuid.UUID
	Name string
}, error) {
	// TODO: Implement actual environment retrieval
	// This would query the environments table filtered by project_id
	return []struct {
		ID   uuid.UUID
		Name string
	}{}, nil
}

// getCurrentEnvironmentVersion gets the current version deployed in an environment
func (s *EnvironmentPromotionService) getCurrentEnvironmentVersion(environmentID string) (*models.EnvironmentVersion, error) {
	var deployment models.EnvironmentDeployment
	err := s.db.Where("environment_id = ? AND status = ?", environmentID, models.DeploymentStatusActive).
		Order("deployed_at DESC").
		First(&deployment).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // No deployment found
		}
		return nil, err
	}

	// Convert to EnvironmentVersion
	envVersion := &models.EnvironmentVersion{
		EnvironmentID:  deployment.EnvironmentID,
		CurrentVersion: deployment.Version,
		GitCommit:      deployment.GitCommit,
		GitBranch:      deployment.GitBranch,
		GitTag:         deployment.GitTag,
		DeployedAt:     deployment.DeployedAt,
		DeployedBy:     deployment.DeployedBy,
		Health:         deployment.Health,
		Status:         deployment.Status,
	}

	return envVersion, nil
}

// startPromotionWorkflow starts the workflow execution for promotion
func (s *EnvironmentPromotionService) startPromotionWorkflow(ctx context.Context, promotion *models.EnvironmentPromotion) (uuid.UUID, error) {
	// TODO: Implement workflow execution start
	// This would create a workflow execution with promotion parameters
	return uuid.New(), nil
}

// updatePromotionStatus updates the status of a promotion
func (s *EnvironmentPromotionService) updatePromotionStatus(promotionID uuid.UUID, status, message string) {
	s.db.Model(&models.EnvironmentPromotion{}).
		Where("id = ?", promotionID).
		Updates(map[string]interface{}{
			"status":         status,
			"status_message": message,
			"updated_at":     time.Now(),
		})
}

// validatePromotionEnvironments validates that environments exist and belong to the project
func (s *EnvironmentPromotionService) validatePromotionEnvironments(ctx context.Context, request *models.PromotionRequest) error {
	if s.environmentClient == nil {
		s.logger.Warn("Environment client not configured, skipping validation")
		return nil
	}

	// Validate source environment
	if err := s.environmentClient.ValidateEnvironmentAccess(ctx, request.SourceEnvironment.String(), request.ProjectID.String()); err != nil {
		return fmt.Errorf("source environment validation failed: %w", err)
	}

	// Validate target environment
	if err := s.environmentClient.ValidateEnvironmentAccess(ctx, request.TargetEnvironment.String(), request.ProjectID.String()); err != nil {
		return fmt.Errorf("target environment validation failed: %w", err)
	}

	return nil
}

// sendPromotionNotification sends notifications for promotion events
func (s *EnvironmentPromotionService) sendPromotionNotification(ctx context.Context, promotion *models.EnvironmentPromotion, status, message string) {
	if s.notificationService == nil {
		s.logger.Warn("Notification service not configured, skipping notification")
		return
	}

	// Get environment names (placeholder - would normally fetch from environment service)
	sourceEnvName := "source-env"
	targetEnvName := "target-env"
	projectName := "project-name"
	initiatedBy := "<EMAIL>"

	// TODO: Get actual environment names and project details
	// sourceEnv, _ := s.environmentClient.GetEnvironment(ctx, promotion.SourceEnvironmentID.String())
	// targetEnv, _ := s.environmentClient.GetEnvironment(ctx, promotion.TargetEnvironmentID.String())

	data := &PromotionNotificationData{
		PromotionID:       promotion.ID,
		ProjectID:         promotion.ProjectID,
		ProjectName:       projectName,
		SourceEnvironment: sourceEnvName,
		TargetEnvironment: targetEnvName,
		Version:           promotion.Version,
		InitiatedBy:       initiatedBy,
		Status:            status,
		StatusMessage:     message,
		WorkflowID:        promotion.WorkflowID,
		StartedAt:         time.Now(),
	}

	if promotion.WorkflowExecutionID != nil {
		data.WorkflowExecutionID = *promotion.WorkflowExecutionID
	}

	if promotion.StartedAt != nil {
		data.StartedAt = *promotion.StartedAt
	}

	if promotion.CompletedAt != nil {
		data.CompletedAt = promotion.CompletedAt
		duration := promotion.CompletedAt.Sub(data.StartedAt)
		data.Duration = duration.String()
	}

	// TODO: Get actual recipients from project configuration
	recipients := []string{"<EMAIL>"}

	var err error
	switch status {
	case "started":
		err = s.notificationService.SendPromotionStartedNotification(ctx, data, recipients)
	case "completed", "failed", "cancelled":
		err = s.notificationService.SendPromotionCompletedNotification(ctx, data, recipients)
	}

	if err != nil {
		s.logger.Error("Failed to send promotion notification",
			zap.Error(err),
			zap.String("promotionID", promotion.ID.String()),
			zap.String("status", status))
	}
}

// sendPromotionApprovalNotification sends approval required notifications
func (s *EnvironmentPromotionService) sendPromotionApprovalNotification(ctx context.Context, promotion *models.EnvironmentPromotion, approvalUsers []string) {
	if s.notificationService == nil {
		s.logger.Warn("Notification service not configured, skipping approval notification")
		return
	}

	// Get environment names (placeholder)
	sourceEnvName := "source-env"
	targetEnvName := "target-env"
	projectName := "project-name"
	initiatedBy := "<EMAIL>"

	data := &PromotionNotificationData{
		PromotionID:       promotion.ID,
		ProjectID:         promotion.ProjectID,
		ProjectName:       projectName,
		SourceEnvironment: sourceEnvName,
		TargetEnvironment: targetEnvName,
		Version:           promotion.Version,
		InitiatedBy:       initiatedBy,
		Status:            "approval_required",
		StartedAt:         time.Now(),
	}

	if err := s.notificationService.SendPromotionApprovalRequiredNotification(ctx, data, approvalUsers); err != nil {
		s.logger.Error("Failed to send approval notification",
			zap.Error(err),
			zap.String("promotionID", promotion.ID.String()))
	}
}
