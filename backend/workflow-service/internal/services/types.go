package services

import (
	"context"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/models"
)

// StartExecutionRequest represents a request to start a workflow execution
type StartExecutionRequest struct {
	WorkflowID    string                 `json:"workflowId" binding:"required"`
	ProjectID     string                 `json:"projectId" binding:"required"`
	EnvironmentID string                 `json:"environmentId" binding:"required"`
	Version       models.VersionInfo     `json:"version" binding:"required"`
	TriggerType   string                 `json:"triggerType" binding:"required"`
	TriggerBy     string                 `json:"triggerBy" binding:"required"`
	Parameters    map[string]interface{} `json:"parameters,omitempty"`
	Variables     map[string]interface{} `json:"variables,omitempty"`
	ScheduledAt   *time.Time             `json:"scheduledAt,omitempty"`
}

// ExecutionFilter represents filters for listing executions
type ExecutionFilter struct {
	ProjectID     string `json:"projectId,omitempty"`
	WorkflowID    string `json:"workflowId,omitempty"`
	EnvironmentID string `json:"environmentId,omitempty"`
	Status        string `json:"status,omitempty"`
	TriggerType   string `json:"triggerType,omitempty"`
	TriggerBy     string `json:"triggerBy,omitempty"`
	Limit         int    `json:"limit,omitempty"`
	Offset        int    `json:"offset,omitempty"`
}

// SecretsService interface for secrets operations
type SecretsService interface {
	GetSecret(ctx context.Context, projectID, secretName string) (string, error)
	ListSecrets(ctx context.Context, projectID string) ([]string, error)
}

// MockSecretsService provides a mock implementation for development
type MockSecretsService struct{}

func NewMockSecretsService() *MockSecretsService {
	return &MockSecretsService{}
}

func (s *MockSecretsService) GetSecret(ctx context.Context, projectID, secretName string) (string, error) {
	// Return mock secret values
	switch secretName {
	case "database-password":
		return "mock-db-password", nil
	case "api-key":
		return "mock-api-key", nil
	default:
		return "mock-secret-value", nil
	}
}

func (s *MockSecretsService) ListSecrets(ctx context.Context, projectID string) ([]string, error) {
	return []string{"database-password", "api-key", "ssl-cert"}, nil
}
