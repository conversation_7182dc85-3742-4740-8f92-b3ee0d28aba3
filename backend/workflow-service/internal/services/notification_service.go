package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

// HealthStatus represents environment health status
type HealthStatus struct {
	Overall   string          `json:"overall"`
	LastCheck time.Time       `json:"last_check"`
	Issues    []HealthIssue   `json:"issues"`
	Services  []ServiceHealth `json:"services"`
}

// HealthIssue represents a health issue
type HealthIssue struct {
	Component string `json:"component"`
	Message   string `json:"message"`
	Severity  string `json:"severity"`
}

// ServiceHealth represents service health status
type ServiceHealth struct {
	Name   string `json:"name"`
	Health string `json:"health"`
	Status string `json:"status"`
}

// NotificationService handles sending notifications for promotion events
type NotificationService struct {
	baseURL    string
	httpClient *http.Client
	logger     *zap.Logger
}

// NewNotificationService creates a new notification service client
func NewNotificationService(baseURL string, logger *zap.Logger) *NotificationService {
	return &NotificationService{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
	}
}

// NotificationRequest represents a notification request
type NotificationRequest struct {
	Type       string                 `json:"type"`
	Title      string                 `json:"title"`
	Message    string                 `json:"message"`
	Recipients []string               `json:"recipients"`
	Channels   []string               `json:"channels"`
	Priority   string                 `json:"priority"`
	Metadata   map[string]interface{} `json:"metadata"`
	TemplateID string                 `json:"template_id,omitempty"`
	Variables  map[string]interface{} `json:"variables,omitempty"`
}

// NotificationResponse represents a notification response
type NotificationResponse struct {
	ID        uuid.UUID `json:"id"`
	Status    string    `json:"status"`
	Message   string    `json:"message"`
	CreatedAt time.Time `json:"created_at"`
}

// PromotionNotificationData represents promotion-specific notification data
type PromotionNotificationData struct {
	PromotionID         uuid.UUID  `json:"promotion_id"`
	ProjectID           uuid.UUID  `json:"project_id"`
	ProjectName         string     `json:"project_name"`
	SourceEnvironment   string     `json:"source_environment"`
	TargetEnvironment   string     `json:"target_environment"`
	Version             string     `json:"version"`
	InitiatedBy         string     `json:"initiated_by"`
	Status              string     `json:"status"`
	StatusMessage       string     `json:"status_message,omitempty"`
	WorkflowID          uuid.UUID  `json:"workflow_id,omitempty"`
	WorkflowExecutionID uuid.UUID  `json:"workflow_execution_id,omitempty"`
	StartedAt           time.Time  `json:"started_at"`
	CompletedAt         *time.Time `json:"completed_at,omitempty"`
	Duration            string     `json:"duration,omitempty"`
	HealthStatus        string     `json:"health_status,omitempty"`
}

// SendPromotionStartedNotification sends notification when promotion starts
func (s *NotificationService) SendPromotionStartedNotification(ctx context.Context, data *PromotionNotificationData, recipients []string) error {
	notification := &NotificationRequest{
		Type:       "promotion_started",
		Title:      fmt.Sprintf("Environment Promotion Started: %s → %s", data.SourceEnvironment, data.TargetEnvironment),
		Message:    fmt.Sprintf("Promotion of version %s from %s to %s has started", data.Version, data.SourceEnvironment, data.TargetEnvironment),
		Recipients: recipients,
		Channels:   []string{"email", "slack", "teams"},
		Priority:   "normal",
		TemplateID: "promotion_started",
		Variables: map[string]interface{}{
			"project_name":       data.ProjectName,
			"source_environment": data.SourceEnvironment,
			"target_environment": data.TargetEnvironment,
			"version":            data.Version,
			"initiated_by":       data.InitiatedBy,
			"promotion_id":       data.PromotionID.String(),
			"started_at":         data.StartedAt.Format(time.RFC3339),
		},
		Metadata: map[string]interface{}{
			"promotion_id": data.PromotionID.String(),
			"project_id":   data.ProjectID.String(),
			"event_type":   "promotion_started",
		},
	}

	return s.sendNotification(ctx, notification)
}

// SendPromotionCompletedNotification sends notification when promotion completes
func (s *NotificationService) SendPromotionCompletedNotification(ctx context.Context, data *PromotionNotificationData, recipients []string) error {
	var title, message string
	var priority string

	switch data.Status {
	case "completed":
		title = fmt.Sprintf("✅ Environment Promotion Completed: %s → %s", data.SourceEnvironment, data.TargetEnvironment)
		message = fmt.Sprintf("Promotion of version %s from %s to %s completed successfully", data.Version, data.SourceEnvironment, data.TargetEnvironment)
		priority = "normal"
	case "failed":
		title = fmt.Sprintf("❌ Environment Promotion Failed: %s → %s", data.SourceEnvironment, data.TargetEnvironment)
		message = fmt.Sprintf("Promotion of version %s from %s to %s failed", data.Version, data.SourceEnvironment, data.TargetEnvironment)
		priority = "high"
	case "cancelled":
		title = fmt.Sprintf("⚠️ Environment Promotion Cancelled: %s → %s", data.SourceEnvironment, data.TargetEnvironment)
		message = fmt.Sprintf("Promotion of version %s from %s to %s was cancelled", data.Version, data.SourceEnvironment, data.TargetEnvironment)
		priority = "normal"
	default:
		title = fmt.Sprintf("Environment Promotion Updated: %s → %s", data.SourceEnvironment, data.TargetEnvironment)
		message = fmt.Sprintf("Promotion of version %s from %s to %s status: %s", data.Version, data.SourceEnvironment, data.TargetEnvironment, data.Status)
		priority = "normal"
	}

	if data.StatusMessage != "" {
		message += fmt.Sprintf("\nDetails: %s", data.StatusMessage)
	}

	variables := map[string]interface{}{
		"project_name":       data.ProjectName,
		"source_environment": data.SourceEnvironment,
		"target_environment": data.TargetEnvironment,
		"version":            data.Version,
		"initiated_by":       data.InitiatedBy,
		"promotion_id":       data.PromotionID.String(),
		"status":             data.Status,
		"started_at":         data.StartedAt.Format(time.RFC3339),
	}

	if data.CompletedAt != nil {
		variables["completed_at"] = data.CompletedAt.Format(time.RFC3339)
	}

	if data.Duration != "" {
		variables["duration"] = data.Duration
	}

	if data.StatusMessage != "" {
		variables["status_message"] = data.StatusMessage
	}

	if data.HealthStatus != "" {
		variables["health_status"] = data.HealthStatus
	}

	notification := &NotificationRequest{
		Type:       "promotion_completed",
		Title:      title,
		Message:    message,
		Recipients: recipients,
		Channels:   []string{"email", "slack", "teams"},
		Priority:   priority,
		TemplateID: "promotion_completed",
		Variables:  variables,
		Metadata: map[string]interface{}{
			"promotion_id": data.PromotionID.String(),
			"project_id":   data.ProjectID.String(),
			"event_type":   "promotion_completed",
			"status":       data.Status,
		},
	}

	return s.sendNotification(ctx, notification)
}

// SendPromotionApprovalRequiredNotification sends notification when approval is required
func (s *NotificationService) SendPromotionApprovalRequiredNotification(ctx context.Context, data *PromotionNotificationData, approvers []string) error {
	notification := &NotificationRequest{
		Type:       "promotion_approval_required",
		Title:      fmt.Sprintf("🔔 Approval Required: Environment Promotion %s → %s", data.SourceEnvironment, data.TargetEnvironment),
		Message:    fmt.Sprintf("Promotion of version %s from %s to %s requires your approval", data.Version, data.SourceEnvironment, data.TargetEnvironment),
		Recipients: approvers,
		Channels:   []string{"email", "slack", "teams"},
		Priority:   "high",
		TemplateID: "promotion_approval_required",
		Variables: map[string]interface{}{
			"project_name":       data.ProjectName,
			"source_environment": data.SourceEnvironment,
			"target_environment": data.TargetEnvironment,
			"version":            data.Version,
			"initiated_by":       data.InitiatedBy,
			"promotion_id":       data.PromotionID.String(),
			"approval_url":       fmt.Sprintf("/promotions/%s/approve", data.PromotionID.String()),
		},
		Metadata: map[string]interface{}{
			"promotion_id":    data.PromotionID.String(),
			"project_id":      data.ProjectID.String(),
			"event_type":      "promotion_approval_required",
			"requires_action": true,
		},
	}

	return s.sendNotification(ctx, notification)
}

// SendEnvironmentHealthAlert sends notification for environment health issues
func (s *NotificationService) SendEnvironmentHealthAlert(ctx context.Context, environmentID, environmentName string, healthStatus *HealthStatus, recipients []string) error {
	var title, message, priority string

	switch healthStatus.Overall {
	case "unhealthy":
		title = fmt.Sprintf("🚨 Environment Health Alert: %s is Unhealthy", environmentName)
		priority = "critical"
	case "degraded":
		title = fmt.Sprintf("⚠️ Environment Health Warning: %s is Degraded", environmentName)
		priority = "high"
	default:
		title = fmt.Sprintf("ℹ️ Environment Health Update: %s", environmentName)
		priority = "normal"
	}

	message = fmt.Sprintf("Environment %s health status: %s", environmentName, healthStatus.Overall)

	if len(healthStatus.Issues) > 0 {
		message += "\n\nIssues detected:"
		for _, issue := range healthStatus.Issues {
			message += fmt.Sprintf("\n- %s: %s", issue.Component, issue.Message)
		}
	}

	variables := map[string]interface{}{
		"environment_id":   environmentID,
		"environment_name": environmentName,
		"health_status":    healthStatus.Overall,
		"last_check":       healthStatus.LastCheck.Format(time.RFC3339),
		"issues_count":     len(healthStatus.Issues),
	}

	if len(healthStatus.Services) > 0 {
		variables["services_count"] = len(healthStatus.Services)
		var unhealthyServices []string
		for _, service := range healthStatus.Services {
			if service.Health != "healthy" {
				unhealthyServices = append(unhealthyServices, service.Name)
			}
		}
		if len(unhealthyServices) > 0 {
			variables["unhealthy_services"] = unhealthyServices
		}
	}

	notification := &NotificationRequest{
		Type:       "environment_health_alert",
		Title:      title,
		Message:    message,
		Recipients: recipients,
		Channels:   []string{"email", "slack", "teams"},
		Priority:   priority,
		TemplateID: "environment_health_alert",
		Variables:  variables,
		Metadata: map[string]interface{}{
			"environment_id": environmentID,
			"event_type":     "environment_health_alert",
			"health_status":  healthStatus.Overall,
		},
	}

	return s.sendNotification(ctx, notification)
}

// SendDeploymentNotification sends notification for deployment events
func (s *NotificationService) SendDeploymentNotification(ctx context.Context, environmentID, environmentName, version, status string, recipients []string) error {
	var title, priority string

	switch status {
	case "completed":
		title = fmt.Sprintf("✅ Deployment Completed: %s v%s", environmentName, version)
		priority = "normal"
	case "failed":
		title = fmt.Sprintf("❌ Deployment Failed: %s v%s", environmentName, version)
		priority = "high"
	case "started":
		title = fmt.Sprintf("🚀 Deployment Started: %s v%s", environmentName, version)
		priority = "normal"
	default:
		title = fmt.Sprintf("📦 Deployment Update: %s v%s", environmentName, version)
		priority = "normal"
	}

	message := fmt.Sprintf("Deployment of version %s to %s: %s", version, environmentName, status)

	notification := &NotificationRequest{
		Type:       "deployment_notification",
		Title:      title,
		Message:    message,
		Recipients: recipients,
		Channels:   []string{"email", "slack"},
		Priority:   priority,
		TemplateID: "deployment_notification",
		Variables: map[string]interface{}{
			"environment_id":   environmentID,
			"environment_name": environmentName,
			"version":          version,
			"status":           status,
			"timestamp":        time.Now().Format(time.RFC3339),
		},
		Metadata: map[string]interface{}{
			"environment_id": environmentID,
			"event_type":     "deployment_notification",
			"status":         status,
		},
	}

	return s.sendNotification(ctx, notification)
}

// sendNotification sends a notification to the notification service
func (s *NotificationService) sendNotification(ctx context.Context, notification *NotificationRequest) error {
	url := fmt.Sprintf("%s/api/v1/notifications", s.baseURL)

	body, err := json.Marshal(notification)
	if err != nil {
		return fmt.Errorf("failed to marshal notification: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(body))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send notification: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("notification service returned status %d", resp.StatusCode)
	}

	var response NotificationResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		s.logger.Warn("Failed to decode notification response", zap.Error(err))
		// Don't fail the operation if we can't decode the response
		return nil
	}

	s.logger.Info("Notification sent successfully",
		zap.String("notification_id", response.ID.String()),
		zap.String("type", notification.Type),
		zap.String("status", response.Status))

	return nil
}
