package services

import (
	"context"
	"fmt"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type ExecutionService struct {
	db             *gorm.DB
	loggingService *LoggingService
	secretsService SecretsService
}

func NewExecutionService(db *gorm.DB, loggingService *LoggingService, secretsService SecretsService) *ExecutionService {
	return &ExecutionService{
		db:             db,
		loggingService: loggingService,
		secretsService: secretsService,
	}
}

// StartExecution starts a new workflow execution
func (s *ExecutionService) StartExecution(ctx context.Context, req *StartExecutionRequest) (*models.WorkflowExecution, error) {
	// Get workflow definition
	var workflow models.Workflow
	if err := s.db.First(&workflow, "id = ?", req.WorkflowID).Error; err != nil {
		return nil, fmt.Errorf("workflow not found: %w", err)
	}

	// Get environment configuration
	var environment models.EnvironmentConfig
	if err := s.db.First(&environment, "id = ?", req.EnvironmentID).Error; err != nil {
		return nil, fmt.Errorf("environment not found: %w", err)
	}

	// Convert variables from interface{} to string
	variables := make(map[string]string)
	for k, v := range req.Variables {
		if str, ok := v.(string); ok {
			variables[k] = str
		} else {
			variables[k] = fmt.Sprintf("%v", v)
		}
	}

	// Create execution record
	execution := &models.WorkflowExecution{
		ID:             uuid.New().String(),
		WorkflowID:     req.WorkflowID,
		ProjectID:      req.ProjectID,
		EnvironmentID:  req.EnvironmentID,
		Version:        req.Version,
		Status:         models.ExecutionStatusPending,
		StartedAt:      time.Now(),
		TriggerType:    req.TriggerType,
		TriggerBy:      req.TriggerBy,
		Parameters:     req.Parameters,
		Variables:      variables,
		LogStreamID:    uuid.New().String(),
		MetricsEnabled: true,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Initialize steps from workflow definition
	execution.Steps = s.initializeSteps(workflow.Steps)

	// Save execution
	if err := s.db.Create(execution).Error; err != nil {
		return nil, fmt.Errorf("failed to create execution: %w", err)
	}

	// Start execution asynchronously
	go s.executeWorkflow(ctx, execution, &workflow, &environment)

	return execution, nil
}

// GetExecution retrieves an execution by ID
func (s *ExecutionService) GetExecution(ctx context.Context, id string) (*models.WorkflowExecution, error) {
	var execution models.WorkflowExecution
	if err := s.db.Preload("Workflow").Preload("Project").Preload("Environment").
		First(&execution, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("execution not found")
		}
		return nil, fmt.Errorf("failed to get execution: %w", err)
	}

	return &execution, nil
}

// ListExecutions retrieves executions with filtering
func (s *ExecutionService) ListExecutions(ctx context.Context, filter *ExecutionFilter) ([]*models.WorkflowExecution, int64, error) {
	query := s.db.Model(&models.WorkflowExecution{}).
		Preload("Workflow").Preload("Project").Preload("Environment")

	// Apply filters
	if filter.ProjectID != "" {
		query = query.Where("project_id = ?", filter.ProjectID)
	}
	if filter.WorkflowID != "" {
		query = query.Where("workflow_id = ?", filter.WorkflowID)
	}
	if filter.EnvironmentID != "" {
		query = query.Where("environment_id = ?", filter.EnvironmentID)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.TriggerType != "" {
		query = query.Where("trigger_type = ?", filter.TriggerType)
	}

	// Count total
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count executions: %w", err)
	}

	// Apply pagination and ordering
	query = query.Order("created_at DESC")
	if filter.Limit > 0 {
		query = query.Limit(filter.Limit)
	}
	if filter.Offset > 0 {
		query = query.Offset(filter.Offset)
	}

	var executions []*models.WorkflowExecution
	if err := query.Find(&executions).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list executions: %w", err)
	}

	return executions, total, nil
}

// CancelExecution cancels a running execution
func (s *ExecutionService) CancelExecution(ctx context.Context, id string, reason string) error {
	var execution models.WorkflowExecution
	if err := s.db.First(&execution, "id = ?", id).Error; err != nil {
		return fmt.Errorf("execution not found: %w", err)
	}

	if execution.Status != models.ExecutionStatusRunning && execution.Status != models.ExecutionStatusPending {
		return fmt.Errorf("cannot cancel execution in status: %s", execution.Status)
	}

	// Update execution status
	now := time.Now()
	execution.Status = models.ExecutionStatusCancelled
	execution.CompletedAt = &now
	execution.UpdatedAt = now
	execution.ErrorMessage = fmt.Sprintf("Cancelled: %s", reason)

	// Update running steps
	for i := range execution.Steps {
		if execution.Steps[i].Status == models.StepStatusRunning {
			execution.Steps[i].Status = models.StepStatusFailed
			execution.Steps[i].CompletedAt = &now
			execution.Steps[i].ErrorMessage = "Execution cancelled"
		}
	}

	if err := s.db.Save(&execution).Error; err != nil {
		return fmt.Errorf("failed to cancel execution: %w", err)
	}

	// Log cancellation
	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "warn",
		StepName:  "system",
		Message:   fmt.Sprintf("Execution cancelled: %s", reason),
	})

	return nil
}

// GetExecutionLogs retrieves logs for an execution
func (s *ExecutionService) GetExecutionLogs(ctx context.Context, id string, filter *LogFilter) ([]*models.LogEntry, error) {
	var execution models.WorkflowExecution
	if err := s.db.First(&execution, "id = ?", id).Error; err != nil {
		return nil, fmt.Errorf("execution not found: %w", err)
	}

	return s.loggingService.GetLogs(execution.LogStreamID, filter)
}

// Private methods

func (s *ExecutionService) initializeSteps(workflowSteps []models.WorkflowStep) []models.WorkflowStepExecution {
	steps := make([]models.WorkflowStepExecution, len(workflowSteps))

	for i, step := range workflowSteps {
		maxRetries := 0
		retryOnFailure := false
		if step.RetryPolicy != nil {
			maxRetries = step.RetryPolicy.MaxRetries
			retryOnFailure = maxRetries > 0
		}

		steps[i] = models.WorkflowStepExecution{
			ID:             uuid.New().String(),
			StepName:       step.Name,
			StepType:       step.Type,
			Status:         models.StepStatusPending,
			Config:         step.Config,
			RetryOnFailure: retryOnFailure,
			MaxRetries:     maxRetries,
		}
	}

	return steps
}

func (s *ExecutionService) executeWorkflow(ctx context.Context, execution *models.WorkflowExecution, workflow *models.Workflow, environment *models.EnvironmentConfig) {
	// Update execution status to running
	execution.Status = models.ExecutionStatusRunning
	execution.UpdatedAt = time.Now()
	s.db.Save(execution)

	// Log execution start
	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  "system",
		Message:   fmt.Sprintf("Starting workflow execution for %s in environment %s", workflow.Name, environment.Name),
	})

	// Execute steps sequentially
	for i := range execution.Steps {
		if err := s.executeStep(ctx, execution, &execution.Steps[i], environment); err != nil {
			// Mark execution as failed
			now := time.Now()
			execution.Status = models.ExecutionStatusFailed
			execution.CompletedAt = &now
			execution.UpdatedAt = now
			execution.ErrorMessage = err.Error()
			s.db.Save(execution)
			return
		}
	}

	// Mark execution as successful
	now := time.Now()
	execution.Status = models.ExecutionStatusSuccess
	execution.CompletedAt = &now
	execution.Duration = &[]int64{now.Sub(execution.StartedAt).Milliseconds()}[0]
	execution.UpdatedAt = now
	s.db.Save(execution)

	// Log completion
	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  "system",
		Message:   fmt.Sprintf("Workflow execution completed successfully in %dms", *execution.Duration),
	})
}

func (s *ExecutionService) executeStep(ctx context.Context, execution *models.WorkflowExecution, step *models.WorkflowStepExecution, environment *models.EnvironmentConfig) error {
	// Update step status
	now := time.Now()
	step.Status = models.StepStatusRunning
	step.StartedAt = &now

	// Log step start
	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  step.StepName,
		Message:   fmt.Sprintf("Starting step: %s", step.StepName),
	})

	// Execute step based on type
	var err error
	switch step.StepType {
	case "deploy":
		err = s.executeDeployStep(ctx, execution, step, environment)
	case "test":
		err = s.executeTestStep(ctx, execution, step, environment)
	case "script":
		err = s.executeScriptStep(ctx, execution, step, environment)
	case "approval":
		err = s.executeApprovalStep(ctx, execution, step, environment)
	default:
		err = fmt.Errorf("unsupported step type: %s", step.StepType)
	}

	// Update step completion
	completedAt := time.Now()
	step.CompletedAt = &completedAt
	step.Duration = &[]int64{completedAt.Sub(*step.StartedAt).Milliseconds()}[0]

	if err != nil {
		step.Status = models.StepStatusFailed
		step.ErrorMessage = err.Error()

		s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
			ID:        uuid.New().String(),
			Timestamp: time.Now(),
			Level:     "error",
			StepName:  step.StepName,
			Message:   fmt.Sprintf("Step failed: %s", err.Error()),
		})

		return err
	}

	step.Status = models.StepStatusSuccess
	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  step.StepName,
		Message:   fmt.Sprintf("Step completed successfully in %dms", *step.Duration),
	})

	// Save step progress
	s.db.Save(execution)

	return nil
}

// Environment-specific step execution methods

func (s *ExecutionService) executeDeployStep(ctx context.Context, execution *models.WorkflowExecution, step *models.WorkflowStepExecution, environment *models.EnvironmentConfig) error {
	// Log deployment start
	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  step.StepName,
		Message:   fmt.Sprintf("Deploying to %s environment (%s)", environment.Name, environment.Provider.Type),
	})

	// Get provider-specific deployment logic
	switch environment.Provider.Type {
	case models.ProviderGKE:
		return s.deployToGKE(ctx, execution, step, environment)
	case models.ProviderAKS:
		return s.deployToAKS(ctx, execution, step, environment)
	case models.ProviderEKS:
		return s.deployToEKS(ctx, execution, step, environment)
	case models.ProviderOpenShift:
		return s.deployToOpenShift(ctx, execution, step, environment)
	default:
		return fmt.Errorf("unsupported provider type: %s", environment.Provider.Type)
	}
}

func (s *ExecutionService) executeTestStep(ctx context.Context, execution *models.WorkflowExecution, step *models.WorkflowStepExecution, environment *models.EnvironmentConfig) error {
	// Log test start
	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  step.StepName,
		Message:   "Running tests against deployed services",
	})

	// Simulate test execution
	time.Sleep(2 * time.Second)

	// Log test results
	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  step.StepName,
		Message:   "All tests passed successfully",
	})

	return nil
}

func (s *ExecutionService) executeScriptStep(ctx context.Context, execution *models.WorkflowExecution, step *models.WorkflowStepExecution, environment *models.EnvironmentConfig) error {
	// Log script execution
	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  step.StepName,
		Message:   "Executing custom script",
	})

	// Get script from step config
	script, ok := step.Config["script"].(string)
	if !ok {
		return fmt.Errorf("script not found in step config")
	}

	// Log script content (masked for security)
	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "debug",
		StepName:  step.StepName,
		Message:   fmt.Sprintf("Script: %s", script),
	})

	// Simulate script execution
	time.Sleep(1 * time.Second)

	return nil
}

func (s *ExecutionService) executeApprovalStep(ctx context.Context, execution *models.WorkflowExecution, step *models.WorkflowStepExecution, environment *models.EnvironmentConfig) error {
	// Log approval requirement
	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  step.StepName,
		Message:   "Waiting for manual approval",
	})

	// In a real implementation, this would wait for approval
	// For now, we'll simulate automatic approval after a short delay
	time.Sleep(500 * time.Millisecond)

	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  step.StepName,
		Message:   "Approval granted automatically (demo mode)",
	})

	return nil
}

// Provider-specific deployment methods

func (s *ExecutionService) deployToGKE(ctx context.Context, execution *models.WorkflowExecution, step *models.WorkflowStepExecution, environment *models.EnvironmentConfig) error {
	// Log GKE deployment
	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  step.StepName,
		Message:   fmt.Sprintf("Deploying to GKE cluster: %s", environment.Provider.Config.Cluster),
	})

	// Simulate GKE deployment steps
	deploymentSteps := []string{
		"Authenticating with GCP",
		"Connecting to GKE cluster",
		"Applying Kubernetes manifests",
		"Waiting for pods to be ready",
		"Verifying deployment health",
	}

	for _, deployStep := range deploymentSteps {
		s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
			ID:        uuid.New().String(),
			Timestamp: time.Now(),
			Level:     "info",
			StepName:  step.StepName,
			Message:   deployStep,
		})
		time.Sleep(500 * time.Millisecond)
	}

	// Log successful deployment
	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  step.StepName,
		Message:   "GKE deployment completed successfully",
	})

	return nil
}

func (s *ExecutionService) deployToAKS(ctx context.Context, execution *models.WorkflowExecution, step *models.WorkflowStepExecution, environment *models.EnvironmentConfig) error {
	// Log AKS deployment
	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  step.StepName,
		Message:   fmt.Sprintf("Deploying to AKS cluster: %s", environment.Provider.Config.Cluster),
	})

	// Simulate AKS deployment
	time.Sleep(2 * time.Second)

	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  step.StepName,
		Message:   "AKS deployment completed successfully",
	})

	return nil
}

func (s *ExecutionService) deployToEKS(ctx context.Context, execution *models.WorkflowExecution, step *models.WorkflowStepExecution, environment *models.EnvironmentConfig) error {
	// Log EKS deployment
	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  step.StepName,
		Message:   fmt.Sprintf("Deploying to EKS cluster: %s", environment.Provider.Config.Cluster),
	})

	// Simulate EKS deployment
	time.Sleep(2 * time.Second)

	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  step.StepName,
		Message:   "EKS deployment completed successfully",
	})

	return nil
}

func (s *ExecutionService) deployToOpenShift(ctx context.Context, execution *models.WorkflowExecution, step *models.WorkflowStepExecution, environment *models.EnvironmentConfig) error {
	// Log OpenShift deployment
	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  step.StepName,
		Message:   fmt.Sprintf("Deploying to OpenShift cluster: %s", environment.Provider.Config.Cluster),
	})

	// Simulate OpenShift deployment
	time.Sleep(2 * time.Second)

	s.loggingService.EmitLog(execution.LogStreamID, &models.LogEntry{
		ID:        uuid.New().String(),
		Timestamp: time.Now(),
		Level:     "info",
		StepName:  step.StepName,
		Message:   "OpenShift deployment completed successfully",
	})

	return nil
}
