package services

import (
	"fmt"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/models"
	"gorm.io/gorm"
)

type LoggingService struct {
	db          *gorm.DB
	streams     map[string]*LogStream
	streamsMux  sync.RWMutex
	subscribers map[string][]chan *models.LogEntry
	subsMux     sync.RWMutex
}

type LogStream struct {
	ID        string
	CreatedAt time.Time
	Logs      []*models.LogEntry
	Mutex     sync.RWMutex
}

type LogFilter struct {
	Level    string    `form:"level"`
	StepName string    `form:"stepName"`
	Since    time.Time `form:"since"`
	Until    time.Time `form:"until"`
	Limit    int       `form:"limit"`
	Offset   int       `form:"offset"`
	Search   string    `form:"search"`
}

// Secret patterns for filtering
var secretPatterns = []*regexp.Regexp{
	regexp.MustCompile(`(?i)password[=:\s]+[^\s\n]+`),
	regexp.MustCompile(`(?i)token[=:\s]+[^\s\n]+`),
	regexp.MustCompile(`(?i)key[=:\s]+[^\s\n]+`),
	regexp.MustCompile(`(?i)secret[=:\s]+[^\s\n]+`),
	regexp.MustCompile(`(?i)Bearer\s+[^\s\n]+`),
	regexp.MustCompile(`(?i)Basic\s+[^\s\n]+`),
	regexp.MustCompile(`(?i)Authorization[=:\s]+[^\s\n]+`),
	regexp.MustCompile(`(?i)api[_-]?key[=:\s]+[^\s\n]+`),
	regexp.MustCompile(`(?i)access[_-]?token[=:\s]+[^\s\n]+`),
	regexp.MustCompile(`(?i)private[_-]?key[=:\s]+[^\s\n]+`),
	// Common environment variable patterns
	regexp.MustCompile(`(?i)[A-Z_]*PASSWORD[A-Z_]*=[^\s\n]+`),
	regexp.MustCompile(`(?i)[A-Z_]*TOKEN[A-Z_]*=[^\s\n]+`),
	regexp.MustCompile(`(?i)[A-Z_]*KEY[A-Z_]*=[^\s\n]+`),
	regexp.MustCompile(`(?i)[A-Z_]*SECRET[A-Z_]*=[^\s\n]+`),
}

func NewLoggingService(db *gorm.DB) *LoggingService {
	return &LoggingService{
		db:          db,
		streams:     make(map[string]*LogStream),
		subscribers: make(map[string][]chan *models.LogEntry),
	}
}

// CreateLogStream creates a new log stream
func (s *LoggingService) CreateLogStream(streamID string) {
	s.streamsMux.Lock()
	defer s.streamsMux.Unlock()

	s.streams[streamID] = &LogStream{
		ID:        streamID,
		CreatedAt: time.Now(),
		Logs:      make([]*models.LogEntry, 0),
	}
}

// EmitLog emits a log entry to a stream
func (s *LoggingService) EmitLog(streamID string, entry *models.LogEntry) {
	// Filter secrets from the log message
	filteredEntry := s.filterSecrets(entry)

	// Store in memory stream
	s.streamsMux.RLock()
	stream, exists := s.streams[streamID]
	s.streamsMux.RUnlock()

	if !exists {
		s.CreateLogStream(streamID)
		s.streamsMux.RLock()
		stream = s.streams[streamID]
		s.streamsMux.RUnlock()
	}

	stream.Mutex.Lock()
	stream.Logs = append(stream.Logs, filteredEntry)
	// Keep only last 1000 logs in memory
	if len(stream.Logs) > 1000 {
		stream.Logs = stream.Logs[len(stream.Logs)-1000:]
	}
	stream.Mutex.Unlock()

	// Persist to database
	go s.persistLog(streamID, filteredEntry)

	// Notify subscribers
	s.notifySubscribers(streamID, filteredEntry)
}

// Subscribe subscribes to a log stream
func (s *LoggingService) Subscribe(streamID string) <-chan *models.LogEntry {
	s.subsMux.Lock()
	defer s.subsMux.Unlock()

	ch := make(chan *models.LogEntry, 100) // Buffer for 100 log entries

	if s.subscribers[streamID] == nil {
		s.subscribers[streamID] = make([]chan *models.LogEntry, 0)
	}

	s.subscribers[streamID] = append(s.subscribers[streamID], ch)

	return ch
}

// Unsubscribe removes a subscription
func (s *LoggingService) Unsubscribe(streamID string, ch <-chan *models.LogEntry) {
	s.subsMux.Lock()
	defer s.subsMux.Unlock()

	subscribers := s.subscribers[streamID]
	for i, subscriber := range subscribers {
		if subscriber == ch {
			// Remove from slice
			s.subscribers[streamID] = append(subscribers[:i], subscribers[i+1:]...)
			close(subscriber)
			break
		}
	}
}

// GetLogs retrieves logs from a stream with filtering
func (s *LoggingService) GetLogs(streamID string, filter *LogFilter) ([]*models.LogEntry, error) {
	// First try to get from memory stream
	s.streamsMux.RLock()
	stream, exists := s.streams[streamID]
	s.streamsMux.RUnlock()

	var logs []*models.LogEntry

	if exists {
		stream.Mutex.RLock()
		logs = make([]*models.LogEntry, len(stream.Logs))
		copy(logs, stream.Logs)
		stream.Mutex.RUnlock()
	}

	// If not enough logs in memory or specific filtering needed, query database
	if len(logs) == 0 || filter != nil {
		dbLogs, err := s.getLogsFromDB(streamID, filter)
		if err != nil {
			return nil, err
		}
		logs = dbLogs
	}

	// Apply filters
	if filter != nil {
		logs = s.applyFilters(logs, filter)
	}

	return logs, nil
}

// GetLogStats returns statistics about logs
func (s *LoggingService) GetLogStats(streamID string) (*LogStats, error) {
	var stats LogStats

	// Query database for stats
	query := s.db.Model(&DBLogEntry{}).Where("stream_id = ?", streamID)

	if err := query.Count(&stats.Total).Error; err != nil {
		return nil, err
	}

	// Count by level
	var levelCounts []struct {
		Level string
		Count int64
	}

	if err := query.Select("level, count(*) as count").
		Group("level").
		Scan(&levelCounts).Error; err != nil {
		return nil, err
	}

	stats.ByLevel = make(map[string]int64)
	for _, lc := range levelCounts {
		stats.ByLevel[lc.Level] = lc.Count
	}

	return &stats, nil
}

// Private methods

func (s *LoggingService) filterSecrets(entry *models.LogEntry) *models.LogEntry {
	filtered := *entry
	message := entry.Message

	// Apply secret filtering patterns
	for _, pattern := range secretPatterns {
		message = pattern.ReplaceAllStringFunc(message, func(match string) string {
			// Extract the key part before = or :
			parts := regexp.MustCompile(`[=:\s]`).Split(match, 2)
			if len(parts) > 0 {
				return parts[0] + "=***REDACTED***"
			}
			return "***REDACTED***"
		})
	}

	// Additional context-aware filtering
	message = s.filterContextualSecrets(message)

	filtered.Message = message
	return &filtered
}

func (s *LoggingService) filterContextualSecrets(message string) string {
	// Filter base64 encoded strings that might be secrets
	base64Pattern := regexp.MustCompile(`[A-Za-z0-9+/]{20,}={0,2}`)
	message = base64Pattern.ReplaceAllStringFunc(message, func(match string) string {
		// Only redact if it looks like a secret (long base64 string)
		if len(match) > 40 {
			return "***REDACTED_BASE64***"
		}
		return match
	})

	// Filter JWT tokens
	jwtPattern := regexp.MustCompile(`eyJ[A-Za-z0-9+/=]+\.[A-Za-z0-9+/=]+\.[A-Za-z0-9+/=]+`)
	message = jwtPattern.ReplaceAllString(message, "***REDACTED_JWT***")

	// Filter URLs with credentials
	urlPattern := regexp.MustCompile(`https?://[^:]+:[^@]+@[^\s]+`)
	message = urlPattern.ReplaceAllString(message, "***REDACTED_URL_WITH_CREDS***")

	return message
}

func (s *LoggingService) persistLog(streamID string, entry *models.LogEntry) {
	dbEntry := &DBLogEntry{
		ID:        entry.ID,
		StreamID:  streamID,
		Timestamp: entry.Timestamp,
		Level:     entry.Level,
		StepName:  entry.StepName,
		Message:   entry.Message,
		Source:    entry.Source,
		Context:   entry.Context,
		Progress:  entry.Progress,
	}

	if err := s.db.Create(dbEntry).Error; err != nil {
		// Log error but don't fail the operation
		fmt.Printf("Failed to persist log entry: %v\n", err)
	}
}

func (s *LoggingService) notifySubscribers(streamID string, entry *models.LogEntry) {
	s.subsMux.RLock()
	subscribers := s.subscribers[streamID]
	s.subsMux.RUnlock()

	for _, ch := range subscribers {
		select {
		case ch <- entry:
		default:
			// Channel is full, skip this subscriber
		}
	}
}

func (s *LoggingService) getLogsFromDB(streamID string, filter *LogFilter) ([]*models.LogEntry, error) {
	query := s.db.Model(&DBLogEntry{}).Where("stream_id = ?", streamID)

	// Apply filters
	if filter != nil {
		if filter.Level != "" {
			query = query.Where("level = ?", filter.Level)
		}
		if filter.StepName != "" {
			query = query.Where("step_name = ?", filter.StepName)
		}
		if !filter.Since.IsZero() {
			query = query.Where("timestamp >= ?", filter.Since)
		}
		if !filter.Until.IsZero() {
			query = query.Where("timestamp <= ?", filter.Until)
		}
		if filter.Search != "" {
			query = query.Where("message ILIKE ?", "%"+filter.Search+"%")
		}
	}

	// Order and pagination
	query = query.Order("timestamp ASC")
	if filter != nil {
		if filter.Limit > 0 {
			query = query.Limit(filter.Limit)
		}
		if filter.Offset > 0 {
			query = query.Offset(filter.Offset)
		}
	}

	var dbEntries []DBLogEntry
	if err := query.Find(&dbEntries).Error; err != nil {
		return nil, err
	}

	// Convert to models.LogEntry
	logs := make([]*models.LogEntry, len(dbEntries))
	for i, entry := range dbEntries {
		logs[i] = &models.LogEntry{
			ID:        entry.ID,
			Timestamp: entry.Timestamp,
			Level:     entry.Level,
			StepName:  entry.StepName,
			Message:   entry.Message,
			Source:    entry.Source,
			Context:   entry.Context,
			Progress:  entry.Progress,
		}
	}

	return logs, nil
}

func (s *LoggingService) applyFilters(logs []*models.LogEntry, filter *LogFilter) []*models.LogEntry {
	filtered := make([]*models.LogEntry, 0)

	for _, log := range logs {
		// Apply filters
		if filter.Level != "" && log.Level != filter.Level {
			continue
		}
		if filter.StepName != "" && log.StepName != filter.StepName {
			continue
		}
		if !filter.Since.IsZero() && log.Timestamp.Before(filter.Since) {
			continue
		}
		if !filter.Until.IsZero() && log.Timestamp.After(filter.Until) {
			continue
		}
		if filter.Search != "" && !strings.Contains(strings.ToLower(log.Message), strings.ToLower(filter.Search)) {
			continue
		}

		filtered = append(filtered, log)
	}

	// Apply pagination
	if filter.Offset > 0 && filter.Offset < len(filtered) {
		filtered = filtered[filter.Offset:]
	}
	if filter.Limit > 0 && filter.Limit < len(filtered) {
		filtered = filtered[:filter.Limit]
	}

	return filtered
}

// Database model for persisting logs
type DBLogEntry struct {
	ID        string    `gorm:"primaryKey"`
	StreamID  string    `gorm:"index"`
	Timestamp time.Time `gorm:"index"`
	Level     string    `gorm:"index"`
	StepName  string    `gorm:"index"`
	Message   string    `gorm:"type:text"`
	Source    string
	Context   map[string]interface{} `gorm:"type:jsonb"`
	Progress  *int
	CreatedAt time.Time
}

type LogStats struct {
	Total   int64            `json:"total"`
	ByLevel map[string]int64 `json:"byLevel"`
}
