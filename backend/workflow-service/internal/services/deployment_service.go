package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/datatypes"

	"github.com/claudio/deploy-orchestrator/workflow-service/internal/models"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// DeploymentService handles comprehensive deployment operations
type DeploymentService struct {
	db                  *gorm.DB
	logger              *zap.Logger
	executionService    *ExecutionService
	loggingService      *LoggingService
	environmentClient   *EnvironmentClient
	notificationService *NotificationService
}

// NewDeploymentService creates a new deployment service
func NewDeploymentService(
	db *gorm.DB,
	logger *zap.Logger,
	executionService *ExecutionService,
	loggingService *LoggingService,
	environmentClient *EnvironmentClient,
	notificationService *NotificationService,
) *DeploymentService {
	service := &DeploymentService{
		db:                  db,
		logger:              logger,
		executionService:    executionService,
		loggingService:      loggingService,
		environmentClient:   environmentClient,
		notificationService: notificationService,
	}

	// Initialize database schema
	if err := service.initializeSchema(); err != nil {
		logger.Error("Failed to initialize deployment service schema", zap.Error(err))
	}

	return service
}

// Initialize database schema
func (s *DeploymentService) initializeSchema() error {
	return s.db.AutoMigrate(
		&models.Deployment{},
		&models.BatchDeployment{},
		&models.MultiAppDeployment{},
		&models.DeploymentAuditLog{},
		&models.DeploymentTemplate{},
		&models.DeploymentStatistics{},
	)
}

// Request/Response Models

type CreateDeploymentRequest struct {
	ID              uuid.UUID      `json:"id,omitempty"`
	ProjectID       string         `json:"projectId" binding:"required"`
	ApplicationID   string         `json:"applicationId" binding:"required"`
	EnvironmentID   string         `json:"environmentId" binding:"required"`
	WorkflowID      string         `json:"workflowId,omitempty"`
	Version         string         `json:"version,omitempty"`
	Configuration   datatypes.JSON `json:"configuration,omitempty"`
	Variables       datatypes.JSON `json:"variables,omitempty"`
	AutoStart       bool           `json:"autoStart,omitempty"`
	NotifyOnSuccess bool           `json:"notifyOnSuccess,omitempty"`
	NotifyOnFailure bool           `json:"notifyOnFailure,omitempty"`
	Tags            []string       `json:"tags,omitempty"`
	UserID          string         `json:"userId,omitempty"`
}

type BatchDeploymentRequest struct {
	ID           uuid.UUID                 `json:"id,omitempty"`
	ProjectID    string                    `json:"projectId" binding:"required"`
	Deployments  []CreateDeploymentRequest `json:"deployments" binding:"required"`
	Strategy     string                    `json:"strategy,omitempty"` // parallel, sequential, canary
	MaxParallel  int                       `json:"maxParallel,omitempty"`
	AutoRollback bool                      `json:"autoRollback,omitempty"`
	UserID       string                    `json:"userId,omitempty"`
}

type MultiAppDeploymentRequest struct {
	ID            uuid.UUID `json:"id,omitempty"`
	ProjectID     string    `json:"projectId" binding:"required"`
	EnvironmentID string    `json:"environmentId" binding:"required"`
	Applications  []struct {
		ApplicationID string                 `json:"applicationId"`
		Version       string                 `json:"version,omitempty"`
		Configuration map[string]interface{} `json:"configuration,omitempty"`
	} `json:"applications" binding:"required"`
	Strategy string `json:"strategy,omitempty"`
	UserID   string `json:"userId,omitempty"`
}

type PromotionRequest struct {
	ProjectID       string `json:"projectId" binding:"required"`
	ApplicationID   string `json:"applicationId" binding:"required"`
	FromEnvironment string `json:"fromEnvironment" binding:"required"`
	ToEnvironment   string `json:"toEnvironment" binding:"required"`
	Version         string `json:"version,omitempty"`
	UserID          string `json:"userId,omitempty"`
}

type DeploymentFilter struct {
	ProjectID   string `json:"projectId,omitempty"`
	Environment string `json:"environment,omitempty"`
	Application string `json:"application,omitempty"`
	Status      string `json:"status,omitempty"`
	StartDate   string `json:"startDate,omitempty"`
	EndDate     string `json:"endDate,omitempty"`
	Limit       int    `json:"limit,omitempty"`
	Offset      int    `json:"offset,omitempty"`
}

type AuditFilter struct {
	ProjectID    string `json:"projectId,omitempty"`
	DeploymentID string `json:"deploymentId,omitempty"`
	UserID       string `json:"userId,omitempty"`
	Action       string `json:"action,omitempty"`
	StartDate    string `json:"startDate,omitempty"`
	EndDate      string `json:"endDate,omitempty"`
	Limit        int    `json:"limit,omitempty"`
	Offset       int    `json:"offset,omitempty"`
}

type DeploymentValidationRequest struct {
	ProjectID     string                 `json:"projectId" binding:"required"`
	ApplicationID string                 `json:"applicationId" binding:"required"`
	EnvironmentID string                 `json:"environmentId" binding:"required"`
	Configuration map[string]interface{} `json:"configuration,omitempty"`
	Variables     map[string]interface{} `json:"variables,omitempty"`
}

type DeploymentValidationResult struct {
	Valid        bool                        `json:"valid"`
	Errors       []string                    `json:"errors,omitempty"`
	Warnings     []string                    `json:"warnings,omitempty"`
	Suggestions  []string                    `json:"suggestions,omitempty"`
	Requirements map[string]interface{}      `json:"requirements,omitempty"`
	Validation   map[string]ValidationResult `json:"validation,omitempty"`
}

type ValidationResult struct {
	Valid   bool     `json:"valid"`
	Message string   `json:"message,omitempty"`
	Errors  []string `json:"errors,omitempty"`
}

type DeploymentStatistics struct {
	ProjectID         string       `json:"projectId"`
	TimeRange         string       `json:"timeRange"`
	TotalDeployments  int          `json:"totalDeployments"`
	SuccessfulDeploys int          `json:"successfulDeploys"`
	FailedDeploys     int          `json:"failedDeploys"`
	SuccessRate       float64      `json:"successRate"`
	AverageTime       float64      `json:"averageTime"`
	TopApplications   []AppStat    `json:"topApplications"`
	TopEnvironments   []EnvStat    `json:"topEnvironments"`
	TrendData         []TrendPoint `json:"trendData"`
	UpdatedAt         time.Time    `json:"updatedAt"`
}

type AppStat struct {
	ApplicationID   string  `json:"applicationId"`
	ApplicationName string  `json:"applicationName"`
	DeploymentCount int     `json:"deploymentCount"`
	SuccessRate     float64 `json:"successRate"`
}

type EnvStat struct {
	EnvironmentID   string  `json:"environmentId"`
	EnvironmentName string  `json:"environmentName"`
	DeploymentCount int     `json:"deploymentCount"`
	SuccessRate     float64 `json:"successRate"`
}

type TrendPoint struct {
	Date    time.Time `json:"date"`
	Count   int       `json:"count"`
	Success int       `json:"success"`
	Failed  int       `json:"failed"`
}

// Core Methods

// CreateDeployment creates a new deployment
func (s *DeploymentService) CreateDeployment(ctx context.Context, req *CreateDeploymentRequest) (*models.Deployment, error) {
	// Generate ID if not provided
	if req.ID == uuid.Nil {
		req.ID = uuid.New()
	}

	// Validate environment exists
	env, err := s.environmentClient.GetEnvironment(ctx, req.EnvironmentID)
	if err != nil {
		s.logger.Error("Environment validation failed", zap.String("environmentId", req.EnvironmentID), zap.Error(err))
		return nil, fmt.Errorf("environment validation failed: %w", err)
	}

	// Create deployment model
	deployment := &models.Deployment{
		ID:              req.ID,
		ProjectID:       req.ProjectID,
		ApplicationID:   req.ApplicationID,
		EnvironmentID:   req.EnvironmentID,
		WorkflowID:      req.WorkflowID,
		Version:         req.Version,
		Status:          "created",
		Configuration:   req.Configuration,
		Variables:       req.Variables,
		AutoStart:       req.AutoStart,
		NotifyOnSuccess: req.NotifyOnSuccess,
		NotifyOnFailure: req.NotifyOnFailure,
		Tags:            req.Tags,
		UserID:          req.UserID,
		EnvironmentName: env.Name,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// Save to database
	if err := s.db.WithContext(ctx).Create(deployment).Error; err != nil {
		s.logger.Error("Failed to create deployment", zap.Error(err))
		return nil, fmt.Errorf("failed to create deployment: %w", err)
	}

	// Log audit event
	s.logAuditEvent(ctx, req.UserID, "deployment.created", req.ProjectID, deployment.ID.String(), map[string]interface{}{
		"applicationId": req.ApplicationID,
		"environmentId": req.EnvironmentID,
	})

	// Auto-start if requested
	if req.AutoStart {
		go func() {
			if _, err := s.StartDeployment(context.Background(), deployment.ID.String()); err != nil {
				s.logger.Error("Failed to auto-start deployment", zap.String("deploymentId", deployment.ID.String()), zap.Error(err))
			}
		}()
	}

	s.logger.Info("Deployment created successfully",
		zap.String("deploymentId", deployment.ID.String()),
		zap.String("projectId", req.ProjectID),
		zap.String("applicationId", req.ApplicationID),
		zap.String("environmentId", req.EnvironmentID))

	return deployment, nil
}

// ListDeployments lists deployments with filtering
func (s *DeploymentService) ListDeployments(ctx context.Context, filter *DeploymentFilter) ([]*models.Deployment, int64, error) {
	query := s.db.WithContext(ctx).Model(&models.Deployment{})

	// Apply filters
	if filter.ProjectID != "" {
		query = query.Where("project_id = ?", filter.ProjectID)
	}
	if filter.Environment != "" {
		query = query.Where("environment_id = ? OR environment_name = ?", filter.Environment, filter.Environment)
	}
	if filter.Application != "" {
		query = query.Where("application_id = ?", filter.Application)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.StartDate != "" {
		query = query.Where("created_at >= ?", filter.StartDate)
	}
	if filter.EndDate != "" {
		query = query.Where("created_at <= ?", filter.EndDate)
	}

	// Get total count - ensure we're using the correct model
	var total int64

	// Execute count query separately
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count deployments: %w", err)
	}

	// Apply pagination
	if filter.Limit > 0 {
		query = query.Limit(filter.Limit)
	}
	if filter.Offset > 0 {
		query = query.Offset(filter.Offset)
	}

	// Execute query
	var deployments []*models.Deployment
	result := query.Order("created_at DESC").Find(&deployments)
	if result.Error != nil {
		return nil, 0, fmt.Errorf("failed to list deployments: %w", result.Error)
	}

	// Always return an empty slice rather than nil when no deployments are found
	if deployments == nil {
		deployments = make([]*models.Deployment, 0)
	}

	return deployments, total, nil
}

// GetDeployment gets deployment by ID
func (s *DeploymentService) GetDeployment(ctx context.Context, deploymentID string) (*models.Deployment, error) {
	var deployment models.Deployment
	if err := s.db.WithContext(ctx).Where("id = ?", deploymentID).First(&deployment).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("deployment not found: %s", deploymentID)
		}
		return nil, fmt.Errorf("failed to get deployment: %w", err)
	}

	return &deployment, nil
}

// StartDeployment starts a deployment
func (s *DeploymentService) StartDeployment(ctx context.Context, deploymentID string) (*models.Deployment, error) {
	deployment, err := s.GetDeployment(ctx, deploymentID)
	if err != nil {
		return nil, err
	}

	if deployment.Status != "created" && deployment.Status != "failed" {
		return nil, fmt.Errorf("deployment cannot be started in current status: %s", deployment.Status)
	}

	// Update status to running
	deployment.Status = "running"
	deployment.StartedAt = &time.Time{}
	*deployment.StartedAt = time.Now()
	deployment.UpdatedAt = time.Now()

	if err := s.db.WithContext(ctx).Save(deployment).Error; err != nil {
		return nil, fmt.Errorf("failed to update deployment status: %w", err)
	}

	// Start workflow execution if workflow ID is provided
	if deployment.WorkflowID != "" {
		executionID, err := s.startWorkflowExecution(ctx, deployment)
		if err != nil {
			// Update status to failed
			deployment.Status = "failed"
			deployment.Error = err.Error()
			deployment.UpdatedAt = time.Now()
			s.db.WithContext(ctx).Save(deployment)

			s.logger.Error("Failed to start workflow execution",
				zap.String("deploymentId", deploymentID),
				zap.String("workflowId", deployment.WorkflowID),
				zap.Error(err))
			return nil, fmt.Errorf("failed to start workflow execution: %w", err)
		}

		deployment.ExecutionID = executionID.String()
		s.db.WithContext(ctx).Save(deployment)
	}

	// Log audit event
	s.logAuditEvent(ctx, deployment.UserID, "deployment.started", deployment.ProjectID, deploymentID, map[string]interface{}{
		"applicationId": deployment.ApplicationID,
		"environmentId": deployment.EnvironmentID,
	})

	s.logger.Info("Deployment started",
		zap.String("deploymentId", deploymentID),
		zap.String("executionId", deployment.ExecutionID))

	return deployment, nil
}

// CancelDeployment cancels a deployment
func (s *DeploymentService) CancelDeployment(ctx context.Context, deploymentID string) (*models.Deployment, error) {
	deployment, err := s.GetDeployment(ctx, deploymentID)
	if err != nil {
		return nil, err
	}

	if deployment.Status != "running" && deployment.Status != "pending" {
		return nil, fmt.Errorf("deployment cannot be cancelled in current status: %s", deployment.Status)
	}

	// Cancel workflow execution if it exists
	if deployment.ExecutionID != "" {
		if err := s.cancelWorkflowExecution(ctx, deployment.ExecutionID); err != nil {
			s.logger.Error("Failed to cancel workflow execution",
				zap.String("deploymentId", deploymentID),
				zap.String("executionId", deployment.ExecutionID),
				zap.Error(err))
		}
	}

	// Update status
	deployment.Status = "cancelled"
	deployment.UpdatedAt = time.Now()

	if err := s.db.WithContext(ctx).Save(deployment).Error; err != nil {
		return nil, fmt.Errorf("failed to update deployment status: %w", err)
	}

	// Log audit event
	s.logAuditEvent(ctx, deployment.UserID, "deployment.cancelled", deployment.ProjectID, deploymentID, nil)

	s.logger.Info("Deployment cancelled", zap.String("deploymentId", deploymentID))

	return deployment, nil
}

// CreateBatchDeployment creates a batch deployment
func (s *DeploymentService) CreateBatchDeployment(ctx context.Context, req *BatchDeploymentRequest) (*models.BatchDeployment, error) {
	// Generate ID if not provided
	if req.ID == uuid.Nil {
		req.ID = uuid.New()
	}

	// Create individual deployments
	var deploymentIDs []string
	for _, deployReq := range req.Deployments {
		deployReq.UserID = req.UserID // Inherit user ID
		deployment, err := s.CreateDeployment(ctx, &deployReq)
		if err != nil {
			s.logger.Error("Failed to create deployment in batch", zap.Error(err))
			continue // Continue with other deployments
		}
		deploymentIDs = append(deploymentIDs, deployment.ID.String())
	}

	// Create batch deployment record
	batchDeployment := &models.BatchDeployment{
		ID:            req.ID,
		ProjectID:     req.ProjectID,
		Status:        "created",
		Strategy:      req.Strategy,
		MaxParallel:   req.MaxParallel,
		AutoRollback:  req.AutoRollback,
		UserID:        req.UserID,
		DeploymentIDs: deploymentIDs,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if err := s.db.WithContext(ctx).Create(batchDeployment).Error; err != nil {
		return nil, fmt.Errorf("failed to create batch deployment: %w", err)
	}

	// Log audit event
	s.logAuditEvent(ctx, req.UserID, "batch_deployment.created", req.ProjectID, batchDeployment.ID.String(), map[string]interface{}{
		"deploymentCount": len(deploymentIDs),
		"strategy":        req.Strategy,
	})

	s.logger.Info("Batch deployment created",
		zap.String("batchId", batchDeployment.ID.String()),
		zap.Int("deploymentCount", len(deploymentIDs)))

	return batchDeployment, nil
}

// GetDeploymentStatistics gets deployment statistics
func (s *DeploymentService) GetDeploymentStatistics(ctx context.Context, projectID, timeRange string) (*DeploymentStatistics, error) {
	// Parse time range
	var startTime time.Time
	switch timeRange {
	case "24h":
		startTime = time.Now().Add(-24 * time.Hour)
	case "7d":
		startTime = time.Now().Add(-7 * 24 * time.Hour)
	case "30d":
		startTime = time.Now().Add(-30 * 24 * time.Hour)
	default:
		startTime = time.Now().Add(-7 * 24 * time.Hour) // Default to 7 days
	}

	query := s.db.WithContext(ctx).Model(&models.Deployment{}).Where("created_at >= ?", startTime)
	if projectID != "" {
		query = query.Where("project_id = ?", projectID)
	}

	// Get total counts
	var total int64
	var successful int64
	var failed int64

	query.Count(&total)
	query.Where("status = ?", "success").Count(&successful)
	query.Where("status = ?", "failed").Count(&failed)

	// Calculate success rate
	successRate := float64(0)
	if total > 0 {
		successRate = float64(successful) / float64(total) * 100
	}

	// Get average deployment time
	var avgTime float64
	s.db.WithContext(ctx).Model(&models.Deployment{}).
		Select("AVG(EXTRACT(EPOCH FROM (completed_at - started_at)))").
		Where("created_at >= ? AND started_at IS NOT NULL AND completed_at IS NOT NULL", startTime).
		Scan(&avgTime)

	stats := &DeploymentStatistics{
		ProjectID:         projectID,
		TimeRange:         timeRange,
		TotalDeployments:  int(total),
		SuccessfulDeploys: int(successful),
		FailedDeploys:     int(failed),
		SuccessRate:       successRate,
		AverageTime:       avgTime,
		UpdatedAt:         time.Now(),
	}

	return stats, nil
}

// ValidateDeploymentConfig validates deployment configuration
func (s *DeploymentService) ValidateDeploymentConfig(ctx context.Context, req *DeploymentValidationRequest) (*DeploymentValidationResult, error) {
	result := &DeploymentValidationResult{
		Valid:      true,
		Validation: make(map[string]ValidationResult),
	}

	// Validate environment exists and is accessible
	env, err := s.environmentClient.GetEnvironment(ctx, req.EnvironmentID)
	if err != nil {
		result.Valid = false
		result.Errors = append(result.Errors, fmt.Sprintf("Environment validation failed: %v", err))
		result.Validation["environment"] = ValidationResult{
			Valid:   false,
			Message: "Environment not found or inaccessible",
			Errors:  []string{err.Error()},
		}
	} else {
		result.Validation["environment"] = ValidationResult{
			Valid:   true,
			Message: fmt.Sprintf("Environment '%s' is valid and accessible", env.Name),
		}
	}

	// TODO: Add more validation logic
	// - Application exists and is valid
	// - Configuration schema validation
	// - Resource requirements validation
	// - Provider-specific validation

	return result, nil
}

// GetDeploymentAudit gets deployment audit logs
func (s *DeploymentService) GetDeploymentAudit(ctx context.Context, filter *AuditFilter) ([]*models.DeploymentAuditLog, int64, error) {
	query := s.db.WithContext(ctx).Model(&models.DeploymentAuditLog{})

	// Apply filters
	if filter.ProjectID != "" {
		query = query.Where("project_id = ?", filter.ProjectID)
	}
	if filter.DeploymentID != "" {
		query = query.Where("deployment_id = ?", filter.DeploymentID)
	}
	if filter.UserID != "" {
		query = query.Where("user_id = ?", filter.UserID)
	}
	if filter.Action != "" {
		query = query.Where("action = ?", filter.Action)
	}
	if filter.StartDate != "" {
		query = query.Where("created_at >= ?", filter.StartDate)
	}
	if filter.EndDate != "" {
		query = query.Where("created_at <= ?", filter.EndDate)
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count audit logs: %w", err)
	}

	// Apply pagination
	if filter.Limit > 0 {
		query = query.Limit(filter.Limit)
	}
	if filter.Offset > 0 {
		query = query.Offset(filter.Offset)
	}

	// Execute query
	var auditLogs []*models.DeploymentAuditLog
	if err := query.Order("created_at DESC").Find(&auditLogs).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get audit logs: %w", err)
	}

	return auditLogs, total, nil
}

// StreamDeploymentEvents streams real-time deployment events
func (s *DeploymentService) StreamDeploymentEvents(ctx context.Context, deploymentID string, eventChan chan<- string) {
	// Implementation for streaming events
	// This would typically connect to the logging service for real-time updates

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			// Get deployment status and send update
			deployment, err := s.GetDeployment(ctx, deploymentID)
			if err != nil {
				s.logger.Error("Failed to get deployment for streaming", zap.Error(err))
				continue
			}

			event := map[string]interface{}{
				"deploymentId": deploymentID,
				"status":       deployment.Status,
				"timestamp":    time.Now(),
			}

			eventJSON, _ := json.Marshal(event)
			select {
			case eventChan <- string(eventJSON):
			case <-ctx.Done():
				return
			}
		}
	}
}

// Helper methods

func (s *DeploymentService) startWorkflowExecution(ctx context.Context, deployment *models.Deployment) (uuid.UUID, error) {
	// This would start a workflow execution via the execution service
	// For now, return a mock execution ID
	return uuid.New(), nil
}

func (s *DeploymentService) cancelWorkflowExecution(ctx context.Context, executionID string) error {
	// This would cancel a workflow execution via the execution service
	return nil
}

func (s *DeploymentService) logAuditEvent(ctx context.Context, userID, action, projectID, resourceID string, metadata map[string]interface{}) {
	auditLog := &models.DeploymentAuditLog{
		ID:         uuid.New(),
		UserID:     userID,
		Action:     action,
		ProjectID:  projectID,
		ResourceID: resourceID,
		Metadata:   metadata,
		CreatedAt:  time.Now(),
	}

	if err := s.db.WithContext(ctx).Create(auditLog).Error; err != nil {
		s.logger.Error("Failed to create audit log", zap.Error(err))
	}
}
