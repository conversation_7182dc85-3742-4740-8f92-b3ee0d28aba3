package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/claudio/deploy-orchestrator/workflow-service/internal/models"
	"github.com/claudio/deploy-orchestrator/workflow-service/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// PromotionHandler handles environment promotion HTTP requests
type PromotionHandler struct {
	promotionService *services.EnvironmentPromotionService
	logger           *zap.Logger
}

// NewPromotionHandler creates a new promotion handler
func NewPromotionHandler(promotionService *services.EnvironmentPromotionService, logger *zap.Logger) *PromotionHandler {
	return &PromotionHandler{
		promotionService: promotionService,
		logger:           logger,
	}
}

// GetVersionMatrix handles GET /api/v1/projects/{projectId}/version-matrix
func (h *PromotionHandler) GetVersionMatrix(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project ID is required"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	matrix, err := h.promotionService.GetVersionMatrix(c.Request.Context(), userID.(string), projectID)
	if err != nil {
		h.logger.Error("Failed to get version matrix",
			zap.Error(err),
			zap.String("projectID", projectID),
			zap.String("userID", userID.(string)))

		if err.Error() == "access denied to project "+projectID {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to project"})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get version matrix"})
		return
	}

	c.JSON(http.StatusOK, matrix)
}

// StartPromotion handles POST /api/v1/promotions
func (h *PromotionHandler) StartPromotion(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Parse request body
	var request models.PromotionRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error("Invalid promotion request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	// Validate request
	if err := h.validatePromotionRequest(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid promotion request", "details": err.Error()})
		return
	}

	// Start promotion
	response, err := h.promotionService.StartPromotion(c.Request.Context(), userID.(string), &request)
	if err != nil {
		h.logger.Error("Failed to start promotion",
			zap.Error(err),
			zap.String("userID", userID.(string)),
			zap.String("projectID", request.ProjectID.String()))

		if err.Error() == "access denied to project "+request.ProjectID.String() {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to project"})
			return
		}

		if err.Error() == "insufficient permissions to deploy to target environment" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions for deployment"})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start promotion"})
		return
	}

	c.JSON(http.StatusCreated, response)
}

// GetPromotionStatus handles GET /api/v1/promotions/{promotionId}
func (h *PromotionHandler) GetPromotionStatus(c *gin.Context) {
	promotionID := c.Param("promotionId")
	if promotionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Promotion ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	promotion, err := h.promotionService.GetPromotionStatus(c.Request.Context(), userID.(string), promotionID)
	if err != nil {
		h.logger.Error("Failed to get promotion status",
			zap.Error(err),
			zap.String("promotionID", promotionID),
			zap.String("userID", userID.(string)))

		if err.Error() == "promotion not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Promotion not found"})
			return
		}

		if err.Error() == "access denied to promotion" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to promotion"})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get promotion status"})
		return
	}

	c.JSON(http.StatusOK, promotion)
}

// GetPromotionHistory handles GET /api/v1/promotions/history/{projectId}
func (h *PromotionHandler) GetPromotionHistory(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	history, err := h.promotionService.GetPromotionHistory(c.Request.Context(), userID.(string), projectID, page, pageSize)
	if err != nil {
		h.logger.Error("Failed to get promotion history",
			zap.Error(err),
			zap.String("projectID", projectID),
			zap.String("userID", userID.(string)))

		if err.Error() == "access denied to project "+projectID {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to project"})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get promotion history"})
		return
	}

	c.JSON(http.StatusOK, history)
}

// GetDeploymentHistory handles GET /api/v1/environments/{environmentId}/deployment-history
func (h *PromotionHandler) GetDeploymentHistory(c *gin.Context) {
	environmentID := c.Param("environmentId")
	if environmentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Environment ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	history, err := h.promotionService.GetDeploymentHistory(c.Request.Context(), userID.(string), environmentID, page, pageSize)
	if err != nil {
		h.logger.Error("Failed to get deployment history",
			zap.Error(err),
			zap.String("environmentID", environmentID),
			zap.String("userID", userID.(string)))

		if err.Error() == "access denied to environment" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to environment"})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get deployment history"})
		return
	}

	c.JSON(http.StatusOK, history)
}

// Helper methods

// validatePromotionRequest validates a promotion request
func (h *PromotionHandler) validatePromotionRequest(request *models.PromotionRequest) error {
	if request.WorkflowID == uuid.Nil {
		return fmt.Errorf("workflow ID is required")
	}

	if request.ProjectID == uuid.Nil {
		return fmt.Errorf("project ID is required")
	}

	if request.SourceEnvironment == uuid.Nil {
		return fmt.Errorf("source environment is required")
	}

	if request.TargetEnvironment == uuid.Nil {
		return fmt.Errorf("target environment is required")
	}

	if request.SourceEnvironment == request.TargetEnvironment {
		return fmt.Errorf("source and target environments must be different")
	}

	if request.Version.Number == "" {
		return fmt.Errorf("version number is required")
	}

	if request.PromotionType == "" {
		request.PromotionType = models.PromotionTypeManual
	}

	// Validate promotion type
	validTypes := map[string]bool{
		models.PromotionTypeManual:    true,
		models.PromotionTypeAutomatic: true,
		models.PromotionTypeScheduled: true,
	}

	if !validTypes[request.PromotionType] {
		return fmt.Errorf("invalid promotion type: %s", request.PromotionType)
	}

	return nil
}

// RegisterPromotionRoutes registers promotion routes with the router
func RegisterPromotionRoutes(router *gin.RouterGroup, handler *PromotionHandler) {
	// Version matrix
	router.GET("/projects/:projectId/version-matrix", handler.GetVersionMatrix)

	// Promotion management
	router.POST("/promotions", handler.StartPromotion)
	router.GET("/promotions/:promotionId", handler.GetPromotionStatus)
	router.GET("/promotions/history/:projectId", handler.GetPromotionHistory)

	// Deployment history
	router.GET("/environments/:environmentId/deployment-history", handler.GetDeploymentHistory)
}
