package handlers

import (
	"net/http"
	"strconv"

	"github.com/claudio/deploy-orchestrator/workflow-service/internal/plugins"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// PluginHandler handles plugin management API requests
type PluginHandler struct {
	pluginManager *plugins.PluginManager
	configParser  *plugins.ConfigParser
	logger        *zap.Logger
}

// NewPluginHandler creates a new plugin handler
func NewPluginHandler(pluginManager *plugins.PluginManager, logger *zap.Logger) *PluginHandler {
	return &PluginHandler{
		pluginManager: pluginManager,
		configParser:  plugins.NewConfigParser(),
		logger:        logger,
	}
}

// InstallPluginRequest represents a plugin installation request
type InstallPluginRequest struct {
	Name    string                 `json:"name" binding:"required"`
	Source  string                 `json:"source" binding:"required"`
	Config  map[string]interface{} `json:"config,omitempty"`
	Enabled bool                   `json:"enabled"`
}

// UpdatePluginConfigRequest represents a plugin configuration update request
type UpdatePluginConfigRequest struct {
	Config map[string]interface{} `json:"config" binding:"required"`
}

// ErrorResponse represents an API error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message,omitempty"`
}

// PluginResponse represents a plugin API response
type PluginResponse struct {
	*plugins.PluginInstance
	Health string `json:"health,omitempty"`
}

// ListPluginsResponse represents the response for listing plugins
type ListPluginsResponse struct {
	Plugins []*PluginResponse `json:"plugins"`
	Total   int               `json:"total"`
}

type HotReloadEvent struct {
	ID         string `json:"id"`
	PluginName string `json:"pluginName"`
	EventType  string `json:"eventType"`
	Timestamp  string `json:"timestamp"`
	Status     string `json:"status"`
	Message    string `json:"message"`
	Duration   string `json:"duration"`
}

type HotReloadEventsResponse struct {
	Events []HotReloadEvent `json:"events"`
	Total  int              `json:"total"`
}

// RegisterRoutes registers plugin management routes
func (h *PluginHandler) RegisterRoutes(router *gin.RouterGroup) {
	plugins := router.Group("/plugins")
	{
		plugins.GET("", h.ListPlugins)
		plugins.POST("", h.InstallPlugin)
		plugins.GET("/:name", h.GetPlugin)
		plugins.DELETE("/:name", h.UninstallPlugin)
		plugins.PUT("/:name/config", h.UpdatePluginConfig)
		plugins.POST("/:name/enable", h.EnablePlugin)
		plugins.POST("/:name/disable", h.DisablePlugin)
		plugins.POST("/:name/reload", h.ReloadPlugin)
		plugins.GET("/:name/status", h.GetPluginStatus)
		plugins.GET("/:name/logs", h.GetPluginLogs)
		plugins.GET("/:name/config-schema", h.GetPluginConfigSchema)

		// Hot reload events
		plugins.GET("/hot-reload/events", h.GetHotReloadEvents)

		// Configuration schemas
		plugins.GET("/config-schemas", h.GetAllPluginConfigSchemas)
	}
}

// ListPlugins lists all installed plugins
// @Summary List plugins
// @Description Get a list of all installed plugins
// @Tags plugins
// @Accept json
// @Produce json
// @Param enabled query bool false "Filter by enabled status"
// @Param type query string false "Filter by plugin type"
// @Success 200 {object} ListPluginsResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/plugins [get]
func (h *PluginHandler) ListPlugins(c *gin.Context) {
	// Get query parameters
	enabledParam := c.Query("enabled")
	typeParam := c.Query("type")

	// Get all plugins
	allPlugins := h.pluginManager.ListPlugins()

	// Filter plugins based on query parameters
	var filteredPlugins []*plugins.PluginInstance
	for _, plugin := range allPlugins {
		// Filter by enabled status
		if enabledParam != "" {
			enabled, err := strconv.ParseBool(enabledParam)
			if err == nil && plugin.Enabled != enabled {
				continue
			}
		}

		// Filter by type
		if typeParam != "" && plugin.Type != typeParam {
			continue
		}

		filteredPlugins = append(filteredPlugins, plugin)
	}

	// Convert to response format
	pluginResponses := make([]*PluginResponse, len(filteredPlugins))
	for i, plugin := range filteredPlugins {
		pluginResponses[i] = &PluginResponse{
			PluginInstance: plugin,
			Health:         h.getPluginHealth(plugin),
		}
	}

	response := ListPluginsResponse{
		Plugins: pluginResponses,
		Total:   len(pluginResponses),
	}

	c.JSON(http.StatusOK, response)
}

// InstallPlugin installs a new plugin
// @Summary Install plugin
// @Description Install a new plugin from source
// @Tags plugins
// @Accept json
// @Produce json
// @Param request body InstallPluginRequest true "Plugin installation request"
// @Success 201 {object} PluginResponse
// @Failure 400 {object} ErrorResponse
// @Failure 409 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/plugins [post]
func (h *PluginHandler) InstallPlugin(c *gin.Context) {
	var req InstallPluginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request",
			Message: err.Error(),
		})
		return
	}

	h.logger.Info("Installing plugin",
		zap.String("name", req.Name),
		zap.String("source", req.Source))

	// TODO: Handle different source types (registry://, git://, file://, local path)
	// For now, assume it's a local path
	pluginPath := req.Source

	// Load the plugin
	if err := h.pluginManager.LoadPlugin(req.Name, pluginPath, req.Config); err != nil {
		h.logger.Error("Failed to install plugin",
			zap.String("name", req.Name),
			zap.Error(err))

		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to install plugin",
			Message: err.Error(),
		})
		return
	}

	// Enable plugin if requested
	if req.Enabled {
		if err := h.pluginManager.EnablePlugin(req.Name); err != nil {
			h.logger.Warn("Failed to enable plugin after installation",
				zap.String("name", req.Name),
				zap.Error(err))
		}
	}

	// Get the installed plugin
	plugin, err := h.pluginManager.GetPlugin(req.Name)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Plugin installed but failed to retrieve",
			Message: err.Error(),
		})
		return
	}

	response := &PluginResponse{
		PluginInstance: plugin,
		Health:         h.getPluginHealth(plugin),
	}

	c.JSON(http.StatusCreated, response)
}

// GetPlugin gets details of a specific plugin
// @Summary Get plugin
// @Description Get details of a specific plugin
// @Tags plugins
// @Accept json
// @Produce json
// @Param name path string true "Plugin name"
// @Success 200 {object} PluginResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/plugins/{name} [get]
func (h *PluginHandler) GetPlugin(c *gin.Context) {
	name := c.Param("name")

	plugin, err := h.pluginManager.GetPlugin(name)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "Plugin not found",
			Message: err.Error(),
		})
		return
	}

	response := &PluginResponse{
		PluginInstance: plugin,
		Health:         h.getPluginHealth(plugin),
	}

	c.JSON(http.StatusOK, response)
}

// UninstallPlugin uninstalls a plugin
// @Summary Uninstall plugin
// @Description Uninstall a plugin
// @Tags plugins
// @Accept json
// @Produce json
// @Param name path string true "Plugin name"
// @Success 200 {object} map[string]string
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/plugins/{name} [delete]
func (h *PluginHandler) UninstallPlugin(c *gin.Context) {
	name := c.Param("name")

	if err := h.pluginManager.UnloadPlugin(name); err != nil {
		if err.Error() == "plugin "+name+" not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "Plugin not found",
				Message: err.Error(),
			})
			return
		}

		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to uninstall plugin",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Plugin uninstalled successfully",
		"plugin":  name,
	})
}

// UpdatePluginConfig updates plugin configuration
// @Summary Update plugin config
// @Description Update plugin configuration
// @Tags plugins
// @Accept json
// @Produce json
// @Param name path string true "Plugin name"
// @Param request body UpdatePluginConfigRequest true "Plugin configuration update"
// @Success 200 {object} PluginResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/plugins/{name}/config [put]
func (h *PluginHandler) UpdatePluginConfig(c *gin.Context) {
	name := c.Param("name")

	var req UpdatePluginConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request",
			Message: err.Error(),
		})
		return
	}

	plugin, err := h.pluginManager.GetPlugin(name)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "Plugin not found",
			Message: err.Error(),
		})
		return
	}

	// Update configuration
	plugin.Config = req.Config

	// Reload plugin to apply new configuration
	if err := h.pluginManager.ReloadPlugin(name); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to apply configuration",
			Message: err.Error(),
		})
		return
	}

	response := &PluginResponse{
		PluginInstance: plugin,
		Health:         h.getPluginHealth(plugin),
	}

	c.JSON(http.StatusOK, response)
}

// EnablePlugin enables a plugin
// @Summary Enable plugin
// @Description Enable a plugin
// @Tags plugins
// @Accept json
// @Produce json
// @Param name path string true "Plugin name"
// @Success 200 {object} PluginResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/plugins/{name}/enable [post]
func (h *PluginHandler) EnablePlugin(c *gin.Context) {
	name := c.Param("name")

	if err := h.pluginManager.EnablePlugin(name); err != nil {
		if err.Error() == "plugin "+name+" not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "Plugin not found",
				Message: err.Error(),
			})
			return
		}

		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to enable plugin",
			Message: err.Error(),
		})
		return
	}

	plugin, _ := h.pluginManager.GetPlugin(name)
	response := &PluginResponse{
		PluginInstance: plugin,
		Health:         h.getPluginHealth(plugin),
	}

	c.JSON(http.StatusOK, response)
}

// DisablePlugin disables a plugin
// @Summary Disable plugin
// @Description Disable a plugin
// @Tags plugins
// @Accept json
// @Produce json
// @Param name path string true "Plugin name"
// @Success 200 {object} PluginResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/plugins/{name}/disable [post]
func (h *PluginHandler) DisablePlugin(c *gin.Context) {
	name := c.Param("name")

	if err := h.pluginManager.DisablePlugin(name); err != nil {
		if err.Error() == "plugin "+name+" not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "Plugin not found",
				Message: err.Error(),
			})
			return
		}

		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to disable plugin",
			Message: err.Error(),
		})
		return
	}

	plugin, _ := h.pluginManager.GetPlugin(name)
	response := &PluginResponse{
		PluginInstance: plugin,
		Health:         h.getPluginHealth(plugin),
	}

	c.JSON(http.StatusOK, response)
}

// ReloadPlugin reloads a plugin (hot reload)
// @Summary Reload plugin
// @Description Reload a plugin (hot reload)
// @Tags plugins
// @Accept json
// @Produce json
// @Param name path string true "Plugin name"
// @Success 200 {object} PluginResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/plugins/{name}/reload [post]
func (h *PluginHandler) ReloadPlugin(c *gin.Context) {
	name := c.Param("name")

	if err := h.pluginManager.ReloadPlugin(name); err != nil {
		if err.Error() == "plugin "+name+" not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "Plugin not found",
				Message: err.Error(),
			})
			return
		}

		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to reload plugin",
			Message: err.Error(),
		})
		return
	}

	plugin, _ := h.pluginManager.GetPlugin(name)
	response := &PluginResponse{
		PluginInstance: plugin,
		Health:         h.getPluginHealth(plugin),
	}

	c.JSON(http.StatusOK, response)
}

// GetPluginStatus gets plugin status
// @Summary Get plugin status
// @Description Get detailed status of a plugin
// @Tags plugins
// @Accept json
// @Produce json
// @Param name path string true "Plugin name"
// @Success 200 {object} PluginResponse
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/plugins/{name}/status [get]
func (h *PluginHandler) GetPluginStatus(c *gin.Context) {
	name := c.Param("name")

	plugin, err := h.pluginManager.GetPlugin(name)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "Plugin not found",
			Message: err.Error(),
		})
		return
	}

	response := &PluginResponse{
		PluginInstance: plugin,
		Health:         h.getPluginHealth(plugin),
	}

	c.JSON(http.StatusOK, response)
}

// GetPluginLogs gets plugin logs
// @Summary Get plugin logs
// @Description Get logs for a specific plugin
// @Tags plugins
// @Accept json
// @Produce json
// @Param name path string true "Plugin name"
// @Param lines query int false "Number of log lines to return" default(100)
// @Param follow query bool false "Follow log output" default(false)
// @Success 200 {object} map[string]interface{}
// @Failure 404 {object} ErrorResponse
// @Router /api/v1/plugins/{name}/logs [get]
func (h *PluginHandler) GetPluginLogs(c *gin.Context) {
	name := c.Param("name")

	_, err := h.pluginManager.GetPlugin(name)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "Plugin not found",
			Message: err.Error(),
		})
		return
	}

	// Get query parameters
	lines := 100
	if linesParam := c.Query("lines"); linesParam != "" {
		if parsedLines, err := strconv.Atoi(linesParam); err == nil {
			lines = parsedLines
		}
	}

	follow := false
	if followParam := c.Query("follow"); followParam != "" {
		if parsedFollow, err := strconv.ParseBool(followParam); err == nil {
			follow = parsedFollow
		}
	}

	// TODO: Implement actual log retrieval
	// For now, return mock logs
	logs := []map[string]interface{}{
		{
			"timestamp": "2024-01-15T10:30:00Z",
			"level":     "info",
			"message":   "Plugin " + name + " initialized successfully",
		},
		{
			"timestamp": "2024-01-15T10:30:01Z",
			"level":     "debug",
			"message":   "Plugin " + name + " processing request",
		},
	}

	response := map[string]interface{}{
		"plugin": name,
		"lines":  lines,
		"follow": follow,
		"logs":   logs,
	}

	c.JSON(http.StatusOK, response)
}

// GetHotReloadEvents gets hot reload events
// @Summary Get hot reload events
// @Description Get hot reload events for plugins
// @Tags plugins
// @Accept json
// @Produce json
// @Param plugin query string false "Filter by plugin name"
// @Success 200 {object} HotReloadEventsResponse
// @Router /api/v1/plugins/hot-reload/events [get]
func (h *PluginHandler) GetHotReloadEvents(c *gin.Context) {
	pluginName := c.Query("plugin")

	// For now, return mock data since hot reload events tracking isn't fully implemented
	// In a real implementation, this would fetch from a hot reload events store
	events := []HotReloadEvent{
		{
			ID:         "event-1",
			PluginName: "example-plugin",
			EventType:  "reload",
			Timestamp:  "2024-01-15T10:30:00Z",
			Status:     "success",
			Message:    "Plugin reloaded successfully",
			Duration:   "1.2s",
		},
	}

	// Filter by plugin name if specified
	if pluginName != "" {
		var filteredEvents []HotReloadEvent
		for _, event := range events {
			if event.PluginName == pluginName {
				filteredEvents = append(filteredEvents, event)
			}
		}
		events = filteredEvents
	}

	response := HotReloadEventsResponse{
		Events: events,
		Total:  len(events),
	}

	c.JSON(http.StatusOK, response)
}

// getPluginHealth returns the health status of a plugin
// GetPluginConfigSchema gets the configuration schema for a specific plugin
// @Summary Get plugin configuration schema
// @Description Get the configuration schema for a specific plugin
// @Tags plugins
// @Accept json
// @Produce json
// @Param name path string true "Plugin name"
// @Success 200 {object} plugins.PluginConfigurationSchema
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/plugins/{name}/config-schema [get]
func (h *PluginHandler) GetPluginConfigSchema(c *gin.Context) {
	name := c.Param("name")

	plugin, err := h.pluginManager.GetPlugin(name)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "Plugin not found",
			Message: err.Error(),
		})
		return
	}

	// Parse plugin configuration from plugin.yaml
	pluginConfig, err := h.configParser.ParsePluginConfig(plugin.Path)
	if err != nil {
		h.logger.Error("Failed to parse plugin config",
			zap.String("plugin", name),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to parse plugin configuration",
			Message: err.Error(),
		})
		return
	}

	// Convert to frontend schema
	schema := h.configParser.ConvertToFrontendSchema(pluginConfig)

	c.JSON(http.StatusOK, schema)
}

// GetAllPluginConfigSchemas gets configuration schemas for all plugins
// @Summary Get all plugin configuration schemas
// @Description Get configuration schemas for all available plugins
// @Tags plugins
// @Accept json
// @Produce json
// @Param enabled query bool false "Filter by enabled status"
// @Success 200 {object} map[string]plugins.PluginConfigurationSchema
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/plugins/config-schemas [get]
func (h *PluginHandler) GetAllPluginConfigSchemas(c *gin.Context) {
	enabledParam := c.Query("enabled")

	allPlugins := h.pluginManager.ListPlugins()
	schemas := make(map[string]*plugins.PluginConfigurationSchema)

	for _, plugin := range allPlugins {
		// Filter by enabled status if specified
		if enabledParam != "" {
			enabled, err := strconv.ParseBool(enabledParam)
			if err == nil && plugin.Enabled != enabled {
				continue
			}
		}

		// Parse plugin configuration
		pluginConfig, err := h.configParser.ParsePluginConfig(plugin.Path)
		if err != nil {
			h.logger.Warn("Failed to parse plugin config",
				zap.String("plugin", plugin.Name),
				zap.Error(err))
			continue
		}

		// Convert to frontend schema
		schema := h.configParser.ConvertToFrontendSchema(pluginConfig)
		schemas[plugin.Name] = schema
	}

	c.JSON(http.StatusOK, schemas)
}

func (h *PluginHandler) getPluginHealth(plugin *plugins.PluginInstance) string {
	if !plugin.Enabled {
		return "disabled"
	}

	switch plugin.Status {
	case "running":
		return "healthy"
	case "error":
		return "unhealthy"
	case "loading":
		return "starting"
	default:
		return "unknown"
	}
}
