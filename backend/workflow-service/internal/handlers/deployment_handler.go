package handlers

import (
	"net/http"
	"strconv"

	"github.com/claudio/deploy-orchestrator/workflow-service/internal/models"

	"github.com/claudio/deploy-orchestrator/workflow-service/internal/services"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// DeploymentHandler handles comprehensive deployment operations
type DeploymentHandler struct {
	deploymentService       *services.DeploymentService
	environmentPromotionSvc *services.EnvironmentPromotionService
	deploymentPluginSvc     *services.DeploymentPluginService
	executionService        *services.ExecutionService
	logger                  *zap.Logger
}

// NewDeploymentHandler creates a new deployment handler
func NewDeploymentHandler(
	deploymentService *services.DeploymentService,
	environmentPromotionSvc *services.EnvironmentPromotionService,
	deploymentPluginSvc *services.DeploymentPluginService,
	executionService *services.ExecutionService,
	logger *zap.Logger,
) *DeploymentHandler {
	return &DeploymentHandler{
		deploymentService:       deploymentService,
		environmentPromotionSvc: environmentPromotionSvc,
		deploymentPluginSvc:     deploymentPluginSvc,
		executionService:        executionService,
		logger:                  logger,
	}
}

// RegisterRoutes registers deployment routes
func (h *DeploymentHandler) RegisterRoutes(router *gin.RouterGroup) {
	deployments := router.Group("/deployments")
	{
		// Deployment Management
		deployments.POST("", h.CreateDeployment)
		deployments.GET("", h.ListDeployments)
		deployments.GET("/:id", h.GetDeployment)
		deployments.PUT("/:id", h.UpdateDeployment)
		deployments.DELETE("/:id", h.DeleteDeployment)

		// Deployment Operations
		deployments.POST("/:id/start", h.StartDeployment)
		deployments.POST("/:id/cancel", h.CancelDeployment)
		deployments.POST("/:id/rollback", h.RollbackDeployment)
		deployments.POST("/:id/retry", h.RetryDeployment)
		deployments.GET("/:id/status", h.GetDeploymentStatus)
		deployments.GET("/:id/logs", h.GetDeploymentLogs)

		// Batch Operations
		deployments.POST("/batch", h.BatchDeploy)
		deployments.POST("/batch/:batchId/cancel", h.CancelBatchDeployment)
		deployments.GET("/batch/:batchId/status", h.GetBatchDeploymentStatus)

		// Multi-Application Deployments
		deployments.POST("/multi-app", h.CreateMultiAppDeployment)
		deployments.GET("/multi-app/:id", h.GetMultiAppDeployment)
		deployments.POST("/multi-app/:id/start", h.StartMultiAppDeployment)

		// Environment Matrix
		deployments.GET("/environments/matrix", h.GetEnvironmentMatrix)
		deployments.GET("/applications/:appId/environments", h.GetApplicationEnvironments)

		// Deployment Statistics
		deployments.GET("/statistics", h.GetDeploymentStatistics)
		deployments.GET("/statistics/trends", h.GetDeploymentTrends)
		deployments.GET("/statistics/success-rate", h.GetDeploymentSuccessRate)

		// Application Promotion
		deployments.POST("/promote", h.PromoteApplication)
		deployments.GET("/promotions", h.ListPromotions)
		deployments.GET("/promotions/:id", h.GetPromotionStatus)
		deployments.GET("/promotions/history", h.GetPromotionHistory)

		// Audit and History
		deployments.GET("/audit", h.GetDeploymentAudit)
		deployments.GET("/history", h.GetDeploymentHistory)
		deployments.GET("/history/application/:appId", h.GetApplicationDeploymentHistory)
		deployments.GET("/history/environment/:envId", h.GetEnvironmentDeploymentHistory)

		// Configuration and Validation
		deployments.POST("/validate", h.ValidateDeploymentConfig)
		deployments.GET("/templates", h.GetDeploymentTemplates)
		deployments.POST("/templates", h.CreateDeploymentTemplate)

		// Real-time Operations
		deployments.GET("/:id/events", h.StreamDeploymentEvents)
		deployments.GET("/live-status", h.GetLiveDeploymentStatus)
	}
}

// CreateDeployment creates a new deployment
func (h *DeploymentHandler) CreateDeployment(c *gin.Context) {
	var request services.CreateDeploymentRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error("Invalid request body", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	deployment, err := h.deploymentService.CreateDeployment(c.Request.Context(), &request)
	if err != nil {
		h.logger.Error("Failed to create deployment", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create deployment", "details": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, deployment)
}

// ListDeployments lists deployments with filtering
func (h *DeploymentHandler) ListDeployments(c *gin.Context) {
	filter := &services.DeploymentFilter{
		ProjectID:   c.Query("projectId"),
		Environment: c.Query("environment"),
		Application: c.Query("application"),
		Status:      c.Query("status"),
		StartDate:   c.Query("startDate"),
		EndDate:     c.Query("endDate"),
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			filter.Limit = l
		}
	}

	if offset := c.Query("offset"); offset != "" {
		if o, err := strconv.Atoi(offset); err == nil {
			filter.Offset = o
		}
	}

	deployments, total, err := h.deploymentService.ListDeployments(c.Request.Context(), filter)
	if err != nil {
		h.logger.Error("Failed to list deployments", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list deployments"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"deployments": deployments,
		"total":       total,
		"limit":       filter.Limit,
		"offset":      filter.Offset,
	})
}

// GetDeployment gets deployment details
func (h *DeploymentHandler) GetDeployment(c *gin.Context) {
	deploymentID := c.Param("id")

	deployment, err := h.deploymentService.GetDeployment(c.Request.Context(), deploymentID)
	if err != nil {
		h.logger.Error("Failed to get deployment", zap.String("deploymentId", deploymentID), zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "Deployment not found"})
		return
	}

	c.JSON(http.StatusOK, deployment)
}

// StartDeployment starts a deployment
func (h *DeploymentHandler) StartDeployment(c *gin.Context) {
	deploymentID := c.Param("id")

	deployment, err := h.deploymentService.StartDeployment(c.Request.Context(), deploymentID)
	if err != nil {
		h.logger.Error("Failed to start deployment", zap.String("deploymentId", deploymentID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start deployment"})
		return
	}

	c.JSON(http.StatusOK, deployment)
}

// CancelDeployment cancels a deployment
func (h *DeploymentHandler) CancelDeployment(c *gin.Context) {
	deploymentID := c.Param("id")

	deployment, err := h.deploymentService.CancelDeployment(c.Request.Context(), deploymentID)
	if err != nil {
		h.logger.Error("Failed to cancel deployment", zap.String("deploymentId", deploymentID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel deployment"})
		return
	}

	c.JSON(http.StatusOK, deployment)
}

// BatchDeploy performs batch deployment
func (h *DeploymentHandler) BatchDeploy(c *gin.Context) {
	var request services.BatchDeploymentRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error("Invalid batch deployment request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	batchDeployment, err := h.deploymentService.CreateBatchDeployment(c.Request.Context(), &request)
	if err != nil {
		h.logger.Error("Failed to create batch deployment", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create batch deployment"})
		return
	}

	c.JSON(http.StatusCreated, batchDeployment)
}

// GetEnvironmentMatrix gets environment deployment matrix
func (h *DeploymentHandler) GetEnvironmentMatrix(c *gin.Context) {
	projectID := c.Query("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project ID is required"})
		return
	}
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	matrix, err := h.environmentPromotionSvc.GetVersionMatrix(c.Request.Context(), userID.(string), projectID)
	if err != nil {
		h.logger.Error("Failed to get environment matrix", zap.String("projectId", projectID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get environment matrix"})
		return
	}

	c.JSON(http.StatusOK, matrix)
}

// GetDeploymentStatistics gets deployment statistics
func (h *DeploymentHandler) GetDeploymentStatistics(c *gin.Context) {
	projectID := c.Query("projectId")
	timeRange := c.Query("timeRange")
	if timeRange == "" {
		timeRange = "7d"
	}

	stats, err := h.deploymentService.GetDeploymentStatistics(c.Request.Context(), projectID, timeRange)
	if err != nil {
		h.logger.Error("Failed to get deployment statistics", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get deployment statistics"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// PromoteApplication promotes application between environments
func (h *DeploymentHandler) PromoteApplication(c *gin.Context) {
	var request models.PromotionRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error("Invalid promotion request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	promotion, err := h.environmentPromotionSvc.StartPromotion(c.Request.Context(), userID.(string), &request)
	if err != nil {
		h.logger.Error("Failed to start promotion", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start promotion"})
		return
	}

	c.JSON(http.StatusCreated, promotion)
}

// GetDeploymentAudit gets deployment audit trail
func (h *DeploymentHandler) GetDeploymentAudit(c *gin.Context) {
	filter := &services.AuditFilter{
		ProjectID:    c.Query("projectId"),
		DeploymentID: c.Query("deploymentId"),
		UserID:       c.Query("userId"),
		Action:       c.Query("action"),
		StartDate:    c.Query("startDate"),
		EndDate:      c.Query("endDate"),
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			filter.Limit = l
		}
	}

	auditLogs, total, err := h.deploymentService.GetDeploymentAudit(c.Request.Context(), filter)
	if err != nil {
		h.logger.Error("Failed to get deployment audit", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get deployment audit"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"auditLogs": auditLogs,
		"total":     total,
		"limit":     filter.Limit,
		"offset":    filter.Offset,
	})
}

// ValidateDeploymentConfig validates deployment configuration
func (h *DeploymentHandler) ValidateDeploymentConfig(c *gin.Context) {
	var request services.DeploymentValidationRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error("Invalid validation request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	result, err := h.deploymentService.ValidateDeploymentConfig(c.Request.Context(), &request)
	if err != nil {
		h.logger.Error("Failed to validate deployment config", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to validate configuration"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// StreamDeploymentEvents streams real-time deployment events
func (h *DeploymentHandler) StreamDeploymentEvents(c *gin.Context) {
	deploymentID := c.Param("id")

	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")

	// Stream deployment events
	eventChan := make(chan string, 100)

	go func() {
		defer close(eventChan)
		h.deploymentService.StreamDeploymentEvents(c.Request.Context(), deploymentID, eventChan)
	}()

	for event := range eventChan {
		c.SSEvent("deployment-event", event)
		c.Writer.Flush()
	}
}

// Placeholder implementations for remaining handlers
func (h *DeploymentHandler) UpdateDeployment(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) DeleteDeployment(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) RollbackDeployment(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) RetryDeployment(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) GetDeploymentStatus(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) GetDeploymentLogs(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) CancelBatchDeployment(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) GetBatchDeploymentStatus(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) CreateMultiAppDeployment(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) GetMultiAppDeployment(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) StartMultiAppDeployment(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) GetApplicationEnvironments(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) GetDeploymentTrends(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) GetDeploymentSuccessRate(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) ListPromotions(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) GetPromotionStatus(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) GetPromotionHistory(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) GetDeploymentHistory(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) GetApplicationDeploymentHistory(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) GetEnvironmentDeploymentHistory(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) GetDeploymentTemplates(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) CreateDeploymentTemplate(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *DeploymentHandler) GetLiveDeploymentStatus(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}
