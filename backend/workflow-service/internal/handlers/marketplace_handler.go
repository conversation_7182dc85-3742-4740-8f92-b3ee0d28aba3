package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// MarketplaceHandler handles marketplace HTTP requests
type MarketplaceHandler struct {
	logger *zap.Logger
}

// NewMarketplaceHandler creates a new marketplace handler
func NewMarketplaceHandler(logger *zap.Logger) *MarketplaceHandler {
	return &MarketplaceHandler{
		logger: logger,
	}
}

// MarketplaceTemplate represents a template in the marketplace
type MarketplaceTemplate struct {
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	Description string   `json:"description"`
	Category    string   `json:"category"`
	Tags        []string `json:"tags"`
	Author      string   `json:"author"`
	Version     string   `json:"version"`
	Rating      float64  `json:"rating"`
	Downloads   int      `json:"downloads"`
	Featured    bool     `json:"featured"`
	CreatedAt   string   `json:"createdAt"`
	UpdatedAt   string   `json:"updatedAt"`
	IconURL     string   `json:"iconUrl,omitempty"`
	Screenshots []string `json:"screenshots,omitempty"`
}

// MarketplaceResponse represents the marketplace response
type MarketplaceResponse struct {
	Plugins    []MarketplacePlugin `json:"plugins"`
	Categories []string            `json:"categories"`
	Tags       []string            `json:"tags"`
	Total      int                 `json:"total"`
}

// MarketplacePlugin represents a plugin in the marketplace
type MarketplacePlugin struct {
	Name             string   `json:"name"`
	Version          string   `json:"version"`
	Description      string   `json:"description"`
	Author           string   `json:"author"`
	License          string   `json:"license"`
	Type             string   `json:"type"`
	Provider         string   `json:"provider,omitempty"`
	Capabilities     []string `json:"capabilities"`
	Tags             []string `json:"tags"`
	Categories       []string `json:"categories"`
	DownloadURL      string   `json:"downloadUrl"`
	DocumentationURL string   `json:"documentationUrl,omitempty"`
	SourceURL        string   `json:"sourceUrl,omitempty"`
	Rating           float64  `json:"rating"`
	Downloads        int      `json:"downloads"`
	LastUpdated      string   `json:"lastUpdated"`
	Screenshots      []string `json:"screenshots,omitempty"`
	Installed        bool     `json:"installed"`
}

// GetMarketplace handles GET /api/v1/marketplace
func (h *MarketplaceHandler) GetMarketplace(c *gin.Context) {
	// Parse query parameters
	category := c.Query("category")
	search := c.Query("search")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// Mock marketplace data - plugins instead of templates
	allPlugins := []MarketplacePlugin{
		{
			Name:             "helm-openshift-deploy",
			Version:          "1.0.0",
			Description:      "Deploy applications to OpenShift using Helm charts with Bitbucket integration",
			Author:           "Platform Team",
			License:          "MIT",
			Type:             "deployment-provider",
			Provider:         "openshift",
			Capabilities:     []string{"deploy:helm", "deploy:openshift", "git:clone"},
			Tags:             []string{"helm", "openshift", "kubernetes", "deployment", "bitbucket"},
			Categories:       []string{"Deployment", "Kubernetes"},
			DownloadURL:      "/api/v1/plugins/helm-openshift-deploy/download",
			DocumentationURL: "/docs/plugins/helm-openshift-deploy",
			SourceURL:        "https://github.com/myorg/helm-openshift-plugin",
			Rating:           4.8,
			Downloads:        1250,
			LastUpdated:      "2024-01-15T14:30:00Z",
			Screenshots:      []string{"/assets/screenshots/helm-1.png", "/assets/screenshots/helm-2.png"},
			Installed:        true,
		},
		{
			Name:             "docker-registry-deploy",
			Version:          "2.1.0",
			Description:      "Deploy applications using Docker images from private registries",
			Author:           "DevOps Team",
			License:          "Apache-2.0",
			Type:             "deployment-provider",
			Provider:         "docker",
			Capabilities:     []string{"deploy:docker", "registry:pull", "container:run"},
			Tags:             []string{"docker", "containers", "registry", "deployment"},
			Categories:       []string{"Deployment", "Containers"},
			DownloadURL:      "/api/v1/plugins/docker-registry-deploy/download",
			DocumentationURL: "/docs/plugins/docker-registry-deploy",
			SourceURL:        "https://github.com/myorg/docker-deploy-plugin",
			Rating:           4.6,
			Downloads:        890,
			LastUpdated:      "2024-01-12T16:45:00Z",
			Screenshots:      []string{"/assets/screenshots/docker-1.png"},
			Installed:        false,
		},
		{
			Name:             "database-migration",
			Version:          "1.0.5",
			Description:      "Automated database schema migrations with rollback support",
			Author:           "Database Team",
			License:          "MIT",
			Type:             "utility-provider",
			Provider:         "database",
			Capabilities:     []string{"db:migrate", "db:rollback", "db:seed"},
			Tags:             []string{"database", "migration", "schema", "sql"},
			Categories:       []string{"Database", "Migration"},
			DownloadURL:      "/api/v1/plugins/database-migration/download",
			DocumentationURL: "/docs/plugins/database-migration",
			SourceURL:        "https://github.com/myorg/db-migration-plugin",
			Rating:           4.3,
			Downloads:        567,
			LastUpdated:      "2024-01-14T13:10:00Z",
			Installed:        false,
		},
		{
			Name:             "security-scanner",
			Version:          "1.1.2",
			Description:      "Comprehensive security scanning for containers and code",
			Author:           "Security Team",
			License:          "GPL-3.0",
			Type:             "security-provider",
			Provider:         "security",
			Capabilities:     []string{"scan:vulnerability", "scan:compliance", "scan:secrets"},
			Tags:             []string{"security", "scan", "vulnerability", "compliance"},
			Categories:       []string{"Security", "Scanning"},
			DownloadURL:      "/api/v1/plugins/security-scanner/download",
			DocumentationURL: "/docs/plugins/security-scanner",
			SourceURL:        "https://github.com/myorg/security-scanner-plugin",
			Rating:           4.7,
			Downloads:        723,
			LastUpdated:      "2024-01-13T10:20:00Z",
			Installed:        false,
		},
		{
			Name:             "backup-restore",
			Version:          "1.3.1",
			Description:      "Automated backup and restore procedures for applications and data",
			Author:           "Operations Team",
			License:          "MIT",
			Type:             "utility-provider",
			Provider:         "backup",
			Capabilities:     []string{"backup:create", "backup:restore", "backup:schedule"},
			Tags:             []string{"backup", "restore", "disaster-recovery", "operations"},
			Categories:       []string{"Operations", "Backup"},
			DownloadURL:      "/api/v1/plugins/backup-restore/download",
			DocumentationURL: "/docs/plugins/backup-restore",
			SourceURL:        "https://github.com/myorg/backup-restore-plugin",
			Rating:           4.4,
			Downloads:        445,
			LastUpdated:      "2024-01-11T15:30:00Z",
			Installed:        false,
		},
	}

	// Filter plugins
	var filteredPlugins []MarketplacePlugin
	for _, plugin := range allPlugins {
		// Filter by category
		if category != "" && !containsStringSlice(plugin.Categories, category) {
			continue
		}

		// Filter by search term
		if search != "" {
			searchLower := search
			if !contains(plugin.Name, searchLower) &&
				!contains(plugin.Description, searchLower) &&
				!containsAny(plugin.Tags, searchLower) {
				continue
			}
		}

		filteredPlugins = append(filteredPlugins, plugin)
	}

	// Pagination
	total := len(filteredPlugins)
	start := (page - 1) * pageSize
	end := start + pageSize

	if start >= total {
		filteredPlugins = []MarketplacePlugin{}
	} else {
		if end > total {
			end = total
		}
		filteredPlugins = filteredPlugins[start:end]
	}

	// Collect all categories and tags
	categoriesMap := make(map[string]bool)
	tagsMap := make(map[string]bool)

	for _, plugin := range allPlugins {
		for _, cat := range plugin.Categories {
			categoriesMap[cat] = true
		}
		for _, tag := range plugin.Tags {
			tagsMap[tag] = true
		}
	}

	// Convert maps to slices
	var categories []string
	for cat := range categoriesMap {
		categories = append(categories, cat)
	}

	var tags []string
	for tag := range tagsMap {
		tags = append(tags, tag)
	}

	response := MarketplaceResponse{
		Plugins:    filteredPlugins,
		Categories: categories,
		Tags:       tags,
		Total:      total,
	}

	c.JSON(http.StatusOK, response)
}

// RegisterMarketplaceRoutes registers marketplace routes
func (h *MarketplaceHandler) RegisterRoutes(router *gin.RouterGroup) {
	marketplace := router.Group("/marketplace")
	{
		marketplace.GET("", h.GetMarketplace)
	}
}

// Helper functions
func contains(str, substr string) bool {
	return len(str) >= len(substr) &&
		(str == substr ||
			(len(str) > len(substr) &&
				(str[:len(substr)] == substr ||
					str[len(str)-len(substr):] == substr ||
					containsSubstring(str, substr))))
}

func containsSubstring(str, substr string) bool {
	for i := 0; i <= len(str)-len(substr); i++ {
		if str[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func containsAny(slice []string, substr string) bool {
	for _, item := range slice {
		if contains(item, substr) {
			return true
		}
	}
	return false
}

func containsStringSlice(slice []string, target string) bool {
	for _, s := range slice {
		if s == target {
			return true
		}
	}
	return false
}
