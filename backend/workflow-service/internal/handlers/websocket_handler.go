package handlers

import (
	"context"
	"encoding/json"
	"net/http"
	"sync"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/models"
	"github.com/claudio/deploy-orchestrator/workflow-service/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

// WebSocketHandler handles WebSocket connections for real-time logs
type WebSocketHandler struct {
	loggingService *services.LoggingService
	logger         *zap.Logger
	upgrader       websocket.Upgrader
	connections    map[string]*WebSocketConnection
	connMutex      sync.RWMutex
}

// WebSocketConnection represents a WebSocket connection
type WebSocketConnection struct {
	ID           string
	Conn         *websocket.Conn
	ExecutionID  string
	StreamID     string
	LogChannel   <-chan *models.LogEntry
	Context      context.Context
	Cancel       context.CancelFunc
	LastActivity time.Time
}

// WebSocketMessage represents messages sent over WebSocket
type WebSocketMessage struct {
	Type        string                 `json:"type"`
	ExecutionID string                 `json:"executionId,omitempty"`
	StreamID    string                 `json:"streamId,omitempty"`
	Data        map[string]interface{} `json:"data,omitempty"`
}

// LogSubscriptionRequest represents a log subscription request
type LogSubscriptionRequest struct {
	Type        string `json:"type"`
	ExecutionID string `json:"executionId"`
}

// NewWebSocketHandler creates a new WebSocket handler
func NewWebSocketHandler(loggingService *services.LoggingService, logger *zap.Logger) *WebSocketHandler {
	upgrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			// Allow all origins for now - in production, implement proper CORS
			return true
		},
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
	}

	handler := &WebSocketHandler{
		loggingService: loggingService,
		logger:         logger,
		upgrader:       upgrader,
		connections:    make(map[string]*WebSocketConnection),
	}

	// Start cleanup routine
	go handler.cleanupRoutine()

	return handler
}

// HandleWebSocket handles WebSocket upgrade and connection
func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	// Upgrade connection
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		h.logger.Error("Failed to upgrade WebSocket connection", zap.Error(err))
		return
	}

	// Create connection context
	ctx, cancel := context.WithCancel(context.Background())

	// Create connection object
	wsConn := &WebSocketConnection{
		ID:           generateConnectionID(),
		Conn:         conn,
		Context:      ctx,
		Cancel:       cancel,
		LastActivity: time.Now(),
	}

	// Store connection
	h.connMutex.Lock()
	h.connections[wsConn.ID] = wsConn
	h.connMutex.Unlock()

	h.logger.Info("WebSocket connection established", zap.String("connectionId", wsConn.ID))

	// Handle the connection
	h.handleConnection(wsConn)
}

// handleConnection manages the WebSocket connection lifecycle
func (h *WebSocketHandler) handleConnection(wsConn *WebSocketConnection) {
	defer h.cleanup(wsConn)

	// Set up ping/pong handlers
	wsConn.Conn.SetPongHandler(func(string) error {
		wsConn.LastActivity = time.Now()
		return nil
	})

	// Start ping routine
	go h.pingRoutine(wsConn)

	// Handle messages
	for {
		select {
		case <-wsConn.Context.Done():
			return
		default:
			// Read message from client
			messageType, data, err := wsConn.Conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					h.logger.Error("WebSocket read error", zap.Error(err))
				}
				return
			}

			wsConn.LastActivity = time.Now()

			// Handle different message types
			if messageType == websocket.TextMessage {
				h.handleTextMessage(wsConn, data)
			}
		}
	}
}

// handleTextMessage processes text messages
func (h *WebSocketHandler) handleTextMessage(wsConn *WebSocketConnection, data []byte) {
	var msg LogSubscriptionRequest
	if err := json.Unmarshal(data, &msg); err != nil {
		h.logger.Error("Failed to parse WebSocket message", zap.Error(err))
		h.sendError(wsConn, "Invalid message format")
		return
	}

	switch msg.Type {
	case "subscribe-logs":
		h.handleLogSubscription(wsConn, msg)
	case "unsubscribe-logs":
		h.handleLogUnsubscription(wsConn)
	default:
		h.sendError(wsConn, "Unknown message type")
	}
}

// handleLogSubscription handles log subscription requests
func (h *WebSocketHandler) handleLogSubscription(wsConn *WebSocketConnection, msg LogSubscriptionRequest) {
	if msg.ExecutionID == "" {
		h.sendError(wsConn, "ExecutionID is required")
		return
	}

	h.logger.Info("Log subscription request",
		zap.String("connectionId", wsConn.ID),
		zap.String("executionId", msg.ExecutionID))

	// Unsubscribe from previous stream if any
	if wsConn.LogChannel != nil {
		h.loggingService.Unsubscribe(wsConn.StreamID, wsConn.LogChannel)
	}

	// Set execution and stream ID
	wsConn.ExecutionID = msg.ExecutionID
	wsConn.StreamID = "execution-" + msg.ExecutionID

	// Subscribe to log stream
	wsConn.LogChannel = h.loggingService.Subscribe(wsConn.StreamID)

	// Send confirmation
	h.sendMessage(wsConn, WebSocketMessage{
		Type: "subscription-confirmed",
		Data: map[string]interface{}{
			"executionId": msg.ExecutionID,
			"streamId":    wsConn.StreamID,
		},
	})

	// Start forwarding logs
	go h.forwardLogs(wsConn)

	// Send recent logs
	go h.sendRecentLogs(wsConn)
}

// handleLogUnsubscription handles log unsubscription requests
func (h *WebSocketHandler) handleLogUnsubscription(wsConn *WebSocketConnection) {
	if wsConn.LogChannel != nil {
		h.loggingService.Unsubscribe(wsConn.StreamID, wsConn.LogChannel)
		wsConn.LogChannel = nil
		wsConn.ExecutionID = ""
		wsConn.StreamID = ""

		h.sendMessage(wsConn, WebSocketMessage{
			Type: "unsubscription-confirmed",
		})
	}
}

// forwardLogs forwards log entries from the logging service to the WebSocket client
func (h *WebSocketHandler) forwardLogs(wsConn *WebSocketConnection) {
	for {
		select {
		case <-wsConn.Context.Done():
			return
		case logEntry, ok := <-wsConn.LogChannel:
			if !ok {
				return
			}

			// Send log entry to client
			h.sendMessage(wsConn, WebSocketMessage{
				Type: "log-entry",
				Data: map[string]interface{}{
					"id":          logEntry.ID,
					"timestamp":   logEntry.Timestamp,
					"level":       logEntry.Level,
					"message":     logEntry.Message,
					"stepName":    logEntry.StepName,
					"executionId": wsConn.ExecutionID,
					"source":      logEntry.Source,
					"context":     logEntry.Context,
					"progress":    logEntry.Progress,
					"isNew":       true,
				},
			})
		}
	}
}

// sendRecentLogs sends recent logs to the client
func (h *WebSocketHandler) sendRecentLogs(wsConn *WebSocketConnection) {
	// Get recent logs from the logging service
	logs, err := h.loggingService.GetLogs(wsConn.StreamID, &services.LogFilter{
		Limit: 50, // Send last 50 logs
	})
	if err != nil {
		h.logger.Error("Failed to get recent logs", zap.Error(err))
		return
	}

	// Send each log entry
	for _, logEntry := range logs {
		h.sendMessage(wsConn, WebSocketMessage{
			Type: "log-entry",
			Data: map[string]interface{}{
				"id":          logEntry.ID,
				"timestamp":   logEntry.Timestamp,
				"level":       logEntry.Level,
				"message":     logEntry.Message,
				"stepName":    logEntry.StepName,
				"executionId": wsConn.ExecutionID,
				"source":      logEntry.Source,
				"context":     logEntry.Context,
				"progress":    logEntry.Progress,
				"isNew":       false,
			},
		})
	}
}

// sendMessage sends a message to the WebSocket client
func (h *WebSocketHandler) sendMessage(wsConn *WebSocketConnection, message WebSocketMessage) {
	if err := wsConn.Conn.WriteJSON(message); err != nil {
		h.logger.Error("Failed to send WebSocket message", zap.Error(err))
		wsConn.Cancel()
	}
}

// sendError sends an error message to the WebSocket client
func (h *WebSocketHandler) sendError(wsConn *WebSocketConnection, errorMsg string) {
	h.sendMessage(wsConn, WebSocketMessage{
		Type: "error",
		Data: map[string]interface{}{
			"error": errorMsg,
		},
	})
}

// pingRoutine sends periodic ping messages
func (h *WebSocketHandler) pingRoutine(wsConn *WebSocketConnection) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-wsConn.Context.Done():
			return
		case <-ticker.C:
			if err := wsConn.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				h.logger.Error("Failed to send ping", zap.Error(err))
				wsConn.Cancel()
				return
			}
		}
	}
}

// cleanup cleans up connection resources
func (h *WebSocketHandler) cleanup(wsConn *WebSocketConnection) {
	h.logger.Info("Cleaning up WebSocket connection", zap.String("connectionId", wsConn.ID))

	// Cancel context
	wsConn.Cancel()

	// Unsubscribe from logs
	if wsConn.LogChannel != nil {
		h.loggingService.Unsubscribe(wsConn.StreamID, wsConn.LogChannel)
	}

	// Close connection
	wsConn.Conn.Close()

	// Remove from connections map
	h.connMutex.Lock()
	delete(h.connections, wsConn.ID)
	h.connMutex.Unlock()
}

// cleanupRoutine periodically cleans up stale connections
func (h *WebSocketHandler) cleanupRoutine() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		h.connMutex.RLock()
		var staleConnections []*WebSocketConnection
		for _, conn := range h.connections {
			if time.Since(conn.LastActivity) > 10*time.Minute {
				staleConnections = append(staleConnections, conn)
			}
		}
		h.connMutex.RUnlock()

		// Clean up stale connections
		for _, conn := range staleConnections {
			h.logger.Info("Cleaning up stale connection", zap.String("connectionId", conn.ID))
			h.cleanup(conn)
		}
	}
}

// GetConnectionStats returns connection statistics
func (h *WebSocketHandler) GetConnectionStats() map[string]interface{} {
	h.connMutex.RLock()
	defer h.connMutex.RUnlock()

	stats := map[string]interface{}{
		"totalConnections": len(h.connections),
		"activeStreams":    make(map[string]int),
	}

	streamCount := make(map[string]int)
	for _, conn := range h.connections {
		if conn.StreamID != "" {
			streamCount[conn.StreamID]++
		}
	}
	stats["activeStreams"] = streamCount

	return stats
}

// generateConnectionID generates a unique connection ID
func generateConnectionID() string {
	return "ws-" + time.Now().Format("20060102150405") + "-" + randomString(8)
}

// randomString generates a random string of specified length
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
