package handlers

import (
	"net/http"
	"strings"

	"github.com/claudio/deploy-orchestrator/workflow-service/internal/services"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// DeploymentPluginHandler handles deployment plugin API requests
type DeploymentPluginHandler struct {
	service *services.DeploymentPluginService
	logger  *zap.Logger
}

// NewDeploymentPluginHandler creates a new deployment plugin handler
func NewDeploymentPluginHandler(service *services.DeploymentPluginService, logger *zap.Logger) *DeploymentPluginHandler {
	return &DeploymentPluginHandler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes registers deployment plugin routes
func (h *DeploymentPluginHandler) RegisterRoutes(router *gin.RouterGroup) {
	deploymentPlugins := router.Group("/deployment-plugins")
	{
		deploymentPlugins.GET("", h.GetAvailablePlugins)
		deploymentPlugins.GET("/compatible", h.GetCompatiblePlugins)
		deploymentPlugins.GET("/environment-mappings", h.GetEnvironmentPluginMappings)
		deploymentPlugins.POST("/environment-mappings", h.SetEnvironmentPluginMapping)
		deploymentPlugins.GET("/environment/:environmentId/default", h.GetDefaultPluginForEnvironment)
		deploymentPlugins.POST("/deploy", h.ExecuteDeployment)
		deploymentPlugins.POST("/:pluginId/validate", h.ValidateConfiguration)
	}
}

// GetAvailablePlugins returns all available deployment plugins
// @Summary Get available deployment plugins
// @Description Get a list of all plugins that can be used for deployments
// @Tags deployment-plugins
// @Accept json
// @Produce json
// @Success 200 {object} []services.DeploymentPlugin
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/deployment-plugins [get]
func (h *DeploymentPluginHandler) GetAvailablePlugins(c *gin.Context) {
	ctx := c.Request.Context()

	plugins, err := h.service.GetAvailablePlugins(ctx)
	if err != nil {
		h.logger.Error("Failed to get available deployment plugins", zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get available deployment plugins",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, plugins)
}

// GetCompatiblePlugins returns plugins compatible with specific artifact types
// @Summary Get compatible deployment plugins
// @Description Get plugins that support the specified artifact types
// @Tags deployment-plugins
// @Accept json
// @Produce json
// @Param artifactTypes query string true "Comma-separated list of artifact types"
// @Success 200 {object} []services.DeploymentPlugin
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/deployment-plugins/compatible [get]
func (h *DeploymentPluginHandler) GetCompatiblePlugins(c *gin.Context) {
	ctx := c.Request.Context()

	artifactTypesParam := c.Query("artifactTypes")
	if artifactTypesParam == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing artifactTypes parameter",
			Message: "artifactTypes query parameter is required",
		})
		return
	}

	artifactTypes := strings.Split(artifactTypesParam, ",")
	for i, t := range artifactTypes {
		artifactTypes[i] = strings.TrimSpace(t)
	}

	plugins, err := h.service.GetCompatiblePlugins(ctx, artifactTypes)
	if err != nil {
		h.logger.Error("Failed to get compatible deployment plugins",
			zap.Strings("artifactTypes", artifactTypes),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get compatible deployment plugins",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, plugins)
}

// GetEnvironmentPluginMappings returns all environment-plugin mappings
// @Summary Get environment plugin mappings
// @Description Get all environment-plugin mappings
// @Tags deployment-plugins
// @Accept json
// @Produce json
// @Success 200 {object} []services.EnvironmentPluginMapping
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/deployment-plugins/environment-mappings [get]
func (h *DeploymentPluginHandler) GetEnvironmentPluginMappings(c *gin.Context) {
	ctx := c.Request.Context()

	mappings, err := h.service.GetEnvironmentPluginMappings(ctx)
	if err != nil {
		h.logger.Error("Failed to get environment plugin mappings", zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get environment plugin mappings",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, mappings)
}

// SetEnvironmentPluginMapping creates or updates an environment-plugin mapping
// @Summary Set environment plugin mapping
// @Description Create or update an environment-plugin mapping
// @Tags deployment-plugins
// @Accept json
// @Produce json
// @Param mapping body services.EnvironmentPluginMapping true "Environment plugin mapping"
// @Success 200 {object} services.EnvironmentPluginMapping
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/deployment-plugins/environment-mappings [post]
func (h *DeploymentPluginHandler) SetEnvironmentPluginMapping(c *gin.Context) {
	ctx := c.Request.Context()

	var mapping services.EnvironmentPluginMapping
	if err := c.ShouldBindJSON(&mapping); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
		})
		return
	}

	if mapping.EnvironmentID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing environment ID",
			Message: "environmentId is required",
		})
		return
	}

	if mapping.DefaultPluginID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing plugin ID",
			Message: "defaultPluginId is required",
		})
		return
	}

	if err := h.service.SetEnvironmentPluginMapping(ctx, &mapping); err != nil {
		h.logger.Error("Failed to set environment plugin mapping",
			zap.String("environmentId", mapping.EnvironmentID),
			zap.String("pluginId", mapping.DefaultPluginID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to set environment plugin mapping",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, mapping)
}

// GetDefaultPluginForEnvironment returns the default plugin for an environment
// @Summary Get default plugin for environment
// @Description Get the default deployment plugin for a specific environment
// @Tags deployment-plugins
// @Accept json
// @Produce json
// @Param environmentId path string true "Environment ID"
// @Success 200 {object} services.DeploymentPlugin
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/deployment-plugins/environment/{environmentId}/default [get]
func (h *DeploymentPluginHandler) GetDefaultPluginForEnvironment(c *gin.Context) {
	ctx := c.Request.Context()
	environmentID := c.Param("environmentId")

	if environmentID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing environment ID",
			Message: "environmentId parameter is required",
		})
		return
	}

	plugin, err := h.service.GetDefaultPluginForEnvironment(ctx, environmentID)
	if err != nil {
		h.logger.Error("Failed to get default plugin for environment",
			zap.String("environmentId", environmentID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get default plugin for environment",
			Message: err.Error(),
		})
		return
	}

	if plugin == nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "No default plugin found",
			Message: "No default plugin configured for environment " + environmentID,
		})
		return
	}

	c.JSON(http.StatusOK, plugin)
}

// ExecuteDeployment executes a deployment using the specified plugin
// @Summary Execute deployment
// @Description Execute a deployment using the specified plugin
// @Tags deployment-plugins
// @Accept json
// @Produce json
// @Param request body services.DeploymentPluginRequest true "Deployment request"
// @Success 200 {object} services.DeploymentResult
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/deployment-plugins/deploy [post]
func (h *DeploymentPluginHandler) ExecuteDeployment(c *gin.Context) {
	ctx := c.Request.Context()

	var request services.DeploymentPluginRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
		})
		return
	}

	// Validate required fields
	if request.DeployableID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing deployable ID",
			Message: "deployableId is required",
		})
		return
	}

	if request.EnvironmentID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing environment ID",
			Message: "environmentId is required",
		})
		return
	}

	if request.PluginID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing plugin ID",
			Message: "pluginId is required",
		})
		return
	}

	result, err := h.service.ExecuteDeployment(ctx, &request)
	if err != nil {
		h.logger.Error("Failed to execute deployment",
			zap.String("deployableId", request.DeployableID),
			zap.String("environmentId", request.EnvironmentID),
			zap.String("pluginId", request.PluginID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to execute deployment",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// ValidateConfiguration validates plugin configuration
// @Summary Validate plugin configuration
// @Description Validate configuration for a specific plugin
// @Tags deployment-plugins
// @Accept json
// @Produce json
// @Param pluginId path string true "Plugin ID"
// @Param configuration body map[string]interface{} true "Configuration to validate"
// @Success 200 {object} services.ValidationResult
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/deployment-plugins/{pluginId}/validate [post]
func (h *DeploymentPluginHandler) ValidateConfiguration(c *gin.Context) {
	ctx := c.Request.Context()
	pluginID := c.Param("pluginId")

	if pluginID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing plugin ID",
			Message: "pluginId parameter is required",
		})
		return
	}

	var configuration map[string]interface{}
	if err := c.ShouldBindJSON(&configuration); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
		})
		return
	}

	result, err := h.service.ValidateConfiguration(ctx, pluginID, configuration)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "Plugin not found",
				Message: err.Error(),
			})
			return
		}

		h.logger.Error("Failed to validate plugin configuration",
			zap.String("pluginId", pluginID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to validate plugin configuration",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}
