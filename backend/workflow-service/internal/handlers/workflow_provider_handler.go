package handlers

import (
	"fmt"
	"net/http"

	"github.com/claudio/deploy-orchestrator/shared/models"
	"github.com/claudio/deploy-orchestrator/workflow-service/internal/providers"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type WorkflowProviderHandler struct {
	providerRegistry *providers.ProviderRegistry
	logger           *zap.Logger
}

type ProviderListResponse struct {
	Providers []ProviderInfo `json:"providers"`
}

type ProviderInfo struct {
	Type         string        `json:"type"`
	Name         string        `json:"name"`
	Description  string        `json:"description"`
	ConfigFields []ConfigField `json:"configFields"`
}

type ConfigField struct {
	Name        string      `json:"name"`
	Type        string      `json:"type"`
	Required    bool        `json:"required"`
	Description string      `json:"description"`
	Default     interface{} `json:"default,omitempty"`
	Options     []string    `json:"options,omitempty"`
}

func NewWorkflowProviderHandler(providerRegistry *providers.ProviderRegistry, logger *zap.Logger) *WorkflowProviderHandler {
	return &WorkflowProviderHandler{
		providerRegistry: providerRegistry,
		logger:           logger,
	}
}

// GetProviders returns all available workflow providers
func (h *WorkflowProviderHandler) GetProviders(c *gin.Context) {
	h.logger.Info("Getting workflow providers")

	registeredProviders := h.providerRegistry.ListProviders()
	providerInfos := make([]ProviderInfo, 0, len(registeredProviders))

	for _, providerInfo := range registeredProviders {
		// Convert the registry ProviderInfo to our API ProviderInfo
		apiProviderInfo := ProviderInfo{
			Type:         string(providerInfo.Type),
			Name:         string(providerInfo.Type), // Use type as name for now
			Description:  fmt.Sprintf("Provider for %s", providerInfo.Type),
			ConfigFields: []ConfigField{}, // Will be enhanced later when we add metadata support
		}

		providerInfos = append(providerInfos, apiProviderInfo)
	}

	response := ProviderListResponse{
		Providers: providerInfos,
	}

	c.JSON(http.StatusOK, response)
}

// GetProvider returns details about a specific workflow provider
func (h *WorkflowProviderHandler) GetProvider(c *gin.Context) {
	providerType := c.Param("type")

	h.logger.Info("Getting workflow provider details", zap.String("type", providerType))

	// Convert string to ProviderType
	providerTypeEnum := models.ProviderType(providerType)

	executor, err := h.providerRegistry.GetProvider(providerTypeEnum)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Provider not found",
			"message": fmt.Sprintf("Provider type '%s' is not registered", providerType),
		})
		return
	}

	// Get provider capabilities and build a basic provider info
	capabilities := executor.GetCapabilities()

	providerInfo := ProviderInfo{
		Type:         providerType,
		Name:         string(providerTypeEnum),
		Description:  fmt.Sprintf("Provider for %s deployment operations", providerType),
		ConfigFields: []ConfigField{}, // Will be enhanced when we add metadata support to ProviderExecutor
	}

	// Add basic config fields based on capabilities
	configFields := []ConfigField{}
	for _, capability := range capabilities {
		// Add capability-specific config fields
		switch capability {
		case "deploy":
			configFields = append(configFields, ConfigField{
				Name:        "namespace",
				Type:        "string",
				Required:    false,
				Description: "Kubernetes namespace for deployment",
			})
		case "scale":
			configFields = append(configFields, ConfigField{
				Name:        "replicas",
				Type:        "number",
				Required:    false,
				Description: "Number of replicas",
			})
		}
	}

	providerInfo.ConfigFields = configFields

	c.JSON(http.StatusOK, providerInfo)
}
