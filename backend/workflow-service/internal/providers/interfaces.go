package providers

import (
	"context"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/models"
)

// ProviderExecutor defines the interface for provider-specific deployment executors
type ProviderExecutor interface {
	// GetProviderType returns the provider type this executor handles
	GetProviderType() models.ProviderType

	// GetCapabilities returns the capabilities supported by this provider
	GetCapabilities() []string

	// ValidateConfig validates the provider configuration
	ValidateConfig(config *models.ProviderConfig) error

	// GenerateSteps generates workflow steps based on deployment requirements
	GenerateSteps(deployment *DeploymentRequest) ([]*models.WorkflowStep, error)

	// Execute performs the actual deployment
	Execute(ctx context.Context, request *ExecutionRequest) (*ExecutionResult, error)

	// GetStatus checks the status of a deployment
	GetStatus(ctx context.Context, deploymentID string) (*DeploymentStatus, error)

	// Rollback performs a rollback operation
	Rollback(ctx context.Context, deploymentID string, targetVersion string) error

	// GetLogs retrieves logs from the provider
	GetLogs(ctx context.Context, deploymentID string, options *LogOptions) ([]*models.LogEntry, error)

	// Cleanup performs cleanup operations
	Cleanup(ctx context.Context, deploymentID string) error
}

// DeploymentRequest represents a deployment request
type DeploymentRequest struct {
	ID            string                    `json:"id"`
	WorkflowID    string                    `json:"workflowId"`
	ExecutionID   string                    `json:"executionId"`
	ProjectID     string                    `json:"projectId"`
	Environment   *models.EnvironmentConfig `json:"environment"`
	Version       models.VersionInfo        `json:"version"`
	Manifests     []string                  `json:"manifests"`
	Variables     map[string]string         `json:"variables"`
	Secrets       map[string]string         `json:"secrets"`
	Configuration map[string]interface{}    `json:"configuration"`
}

// ExecutionRequest represents a step execution request
type ExecutionRequest struct {
	StepID      string                    `json:"stepId"`
	StepName    string                    `json:"stepName"`
	StepType    string                    `json:"stepType"`
	ExecutionID string                    `json:"executionId"`
	Environment *models.EnvironmentConfig `json:"environment"`
	Config      map[string]interface{}    `json:"config"`
	Variables   map[string]string         `json:"variables"`
	Secrets     map[string]string         `json:"secrets"`
	Artifacts   []models.Artifact         `json:"artifacts"`
	LogCallback func(*models.LogEntry)    `json:"-"`
}

// ExecutionResult represents the result of a step execution
type ExecutionResult struct {
	Success      bool                   `json:"success"`
	Output       map[string]interface{} `json:"output"`
	Artifacts    []models.Artifact      `json:"artifacts"`
	Metrics      map[string]float64     `json:"metrics"`
	ErrorMessage string                 `json:"errorMessage,omitempty"`
	ErrorDetails string                 `json:"errorDetails,omitempty"`
}

// DeploymentStatus represents the status of a deployment
type DeploymentStatus struct {
	ID            string                   `json:"id"`
	Status        string                   `json:"status"`   // pending, running, success, failed
	Phase         string                   `json:"phase"`    // deploying, testing, verifying, completed
	Progress      int                      `json:"progress"` // 0-100
	Message       string                   `json:"message"`
	Services      []models.DeployedService `json:"services"`
	HealthChecks  []HealthCheck            `json:"healthChecks"`
	ResourceUsage ResourceUsage            `json:"resourceUsage"`
	LastUpdated   string                   `json:"lastUpdated"`
}

// HealthCheck represents a health check result
type HealthCheck struct {
	Name      string `json:"name"`
	Status    string `json:"status"` // healthy, unhealthy, unknown
	Message   string `json:"message"`
	Endpoint  string `json:"endpoint"`
	Timestamp string `json:"timestamp"`
}

// ResourceUsage represents resource usage information
type ResourceUsage struct {
	CPU     ResourceMetric `json:"cpu"`
	Memory  ResourceMetric `json:"memory"`
	Disk    ResourceMetric `json:"disk"`
	Network ResourceMetric `json:"network"`
}

// ResourceMetric represents a resource metric
type ResourceMetric struct {
	Used       float64 `json:"used"`
	Total      float64 `json:"total"`
	Unit       string  `json:"unit"`
	Percentage float64 `json:"percentage"`
}

// LogOptions represents options for retrieving logs
type LogOptions struct {
	Since     string `json:"since,omitempty"`
	Until     string `json:"until,omitempty"`
	Lines     int    `json:"lines,omitempty"`
	Follow    bool   `json:"follow,omitempty"`
	Container string `json:"container,omitempty"`
	Service   string `json:"service,omitempty"`
}

// ProviderCapability represents a capability that a provider supports
type ProviderCapability struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Required    bool   `json:"required"`
}

// Common provider capabilities
const (
	CapabilityDeploy        = "deploy"
	CapabilityRollback      = "rollback"
	CapabilityScaling       = "scaling"
	CapabilityHealthChecks  = "health-checks"
	CapabilityLogs          = "logs"
	CapabilityMetrics       = "metrics"
	CapabilitySecrets       = "secrets"
	CapabilityConfigMaps    = "config-maps"
	CapabilityLoadBalancing = "load-balancing"
	CapabilityAutoScaling   = "auto-scaling"
	CapabilityBlueGreen     = "blue-green"
	CapabilityCanary        = "canary"
	CapabilityMultiRegion   = "multi-region"
	CapabilityBackup        = "backup"
	CapabilityMonitoring    = "monitoring"
	CapabilityNetworking    = "networking"
	CapabilityStorage       = "storage"
	CapabilityServiceMesh   = "service-mesh"
	CapabilityImageScanning = "image-scanning"
	CapabilityCompliance    = "compliance"
)

// StepExecutor defines the interface for workflow step executors
type StepExecutor interface {
	// Execute executes a workflow step
	Execute(ctx context.Context, request *ExecutionRequest) (*ExecutionResult, error)

	// GetSupportedStepTypes returns the step types this executor supports
	GetSupportedStepTypes() []string

	// ValidateStep validates a step configuration
	ValidateStep(step *models.WorkflowStep) error
}

// BaseExecutor provides common functionality for all executors
type BaseExecutor struct {
	ProviderType models.ProviderType
	Logger       interface{} // Will be *zap.Logger in implementation
}

// GetProviderType returns the provider type
func (e *BaseExecutor) GetProviderType() models.ProviderType {
	return e.ProviderType
}

// LogInfo logs an info message and calls the log callback if provided
func (e *BaseExecutor) LogInfo(request *ExecutionRequest, message string) {
	if request.LogCallback != nil {
		request.LogCallback(&models.LogEntry{
			ID:        generateLogID(),
			Timestamp: getCurrentTime(),
			Level:     "info",
			StepName:  request.StepName,
			Message:   message,
			Source:    string(e.ProviderType),
		})
	}
}

// LogError logs an error message and calls the log callback if provided
func (e *BaseExecutor) LogError(request *ExecutionRequest, message string) {
	if request.LogCallback != nil {
		request.LogCallback(&models.LogEntry{
			ID:        generateLogID(),
			Timestamp: getCurrentTime(),
			Level:     "error",
			StepName:  request.StepName,
			Message:   message,
			Source:    string(e.ProviderType),
		})
	}
}

// LogWarn logs a warning message and calls the log callback if provided
func (e *BaseExecutor) LogWarn(request *ExecutionRequest, message string) {
	if request.LogCallback != nil {
		request.LogCallback(&models.LogEntry{
			ID:        generateLogID(),
			Timestamp: getCurrentTime(),
			Level:     "warn",
			StepName:  request.StepName,
			Message:   message,
			Source:    string(e.ProviderType),
		})
	}
}

// LogDebug logs a debug message and calls the log callback if provided
func (e *BaseExecutor) LogDebug(request *ExecutionRequest, message string) {
	if request.LogCallback != nil {
		request.LogCallback(&models.LogEntry{
			ID:        generateLogID(),
			Timestamp: getCurrentTime(),
			Level:     "debug",
			StepName:  request.StepName,
			Message:   message,
			Source:    string(e.ProviderType),
		})
	}
}

// Helper functions (to be implemented)
func generateLogID() string {
	// Implementation will use uuid.New().String()
	return "log-" + "placeholder"
}

func getCurrentTime() time.Time {
	// Implementation will use time.Now()
	return time.Now()
}
