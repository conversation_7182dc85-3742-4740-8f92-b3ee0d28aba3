package providers

import (
	"context"
	"fmt"
	"sync"

	"github.com/claudio/deploy-orchestrator/shared/models"
	"go.uber.org/zap"
)

// ProviderRegistry manages all provider executors
type ProviderRegistry struct {
	executors     map[models.ProviderType]ProviderExecutor
	stepExecutors map[string]StepExecutor
	templates     map[string]*WorkflowTemplate
	logger        *zap.Logger
	mutex         sync.RWMutex
}

// WorkflowTemplate represents a provider-specific workflow template
type WorkflowTemplate struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	Description  string                 `json:"description"`
	ProviderType models.ProviderType    `json:"providerType"`
	Category     string                 `json:"category"`
	Steps        []models.WorkflowStep  `json:"steps"`
	Variables    map[string]interface{} `json:"variables"`
	Requirements []string               `json:"requirements"`
}

// NewProviderRegistry creates a new provider registry
func NewProviderRegistry(logger *zap.Logger) *ProviderRegistry {
	return &ProviderRegistry{
		executors:     make(map[models.ProviderType]ProviderExecutor),
		stepExecutors: make(map[string]StepExecutor),
		templates:     make(map[string]*WorkflowTemplate),
		logger:        logger,
	}
}

// RegisterProvider registers a provider executor
func (r *ProviderRegistry) RegisterProvider(executor ProviderExecutor) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	providerType := executor.GetProviderType()
	if _, exists := r.executors[providerType]; exists {
		return fmt.Errorf("provider %s is already registered", providerType)
	}

	r.executors[providerType] = executor
	r.logger.Info("Provider registered",
		zap.String("provider", string(providerType)),
		zap.Strings("capabilities", executor.GetCapabilities()))

	return nil
}

// RegisterStepExecutor registers a step executor
func (r *ProviderRegistry) RegisterStepExecutor(stepType string, executor StepExecutor) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if _, exists := r.stepExecutors[stepType]; exists {
		return fmt.Errorf("step executor for type %s is already registered", stepType)
	}

	r.stepExecutors[stepType] = executor
	r.logger.Info("Step executor registered", zap.String("stepType", stepType))

	return nil
}

// RegisterTemplate registers a workflow template
func (r *ProviderRegistry) RegisterTemplate(template *WorkflowTemplate) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if _, exists := r.templates[template.ID]; exists {
		return fmt.Errorf("template %s is already registered", template.ID)
	}

	r.templates[template.ID] = template
	r.logger.Info("Workflow template registered",
		zap.String("templateId", template.ID),
		zap.String("provider", string(template.ProviderType)))

	return nil
}

// GetProvider returns a provider executor by type
func (r *ProviderRegistry) GetProvider(providerType models.ProviderType) (ProviderExecutor, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	executor, exists := r.executors[providerType]
	if !exists {
		return nil, fmt.Errorf("provider %s not found", providerType)
	}

	return executor, nil
}

// GetStepExecutor returns a step executor by step type
func (r *ProviderRegistry) GetStepExecutor(stepType string) (StepExecutor, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	executor, exists := r.stepExecutors[stepType]
	if !exists {
		return nil, fmt.Errorf("step executor for type %s not found", stepType)
	}

	return executor, nil
}

// GetTemplate returns a workflow template by ID
func (r *ProviderRegistry) GetTemplate(templateID string) (*WorkflowTemplate, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	template, exists := r.templates[templateID]
	if !exists {
		return nil, fmt.Errorf("template %s not found", templateID)
	}

	return template, nil
}

// ListProviders returns all registered providers
func (r *ProviderRegistry) ListProviders() []ProviderInfo {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	providers := make([]ProviderInfo, 0, len(r.executors))
	for providerType, executor := range r.executors {
		providers = append(providers, ProviderInfo{
			Type:         providerType,
			Capabilities: executor.GetCapabilities(),
		})
	}

	return providers
}

// ListStepExecutors returns all registered step executors
func (r *ProviderRegistry) ListStepExecutors() []StepExecutorInfo {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	executors := make([]StepExecutorInfo, 0, len(r.stepExecutors))
	for stepType, executor := range r.stepExecutors {
		executors = append(executors, StepExecutorInfo{
			StepType:           stepType,
			SupportedStepTypes: executor.GetSupportedStepTypes(),
		})
	}

	return executors
}

// ListTemplates returns all registered templates
func (r *ProviderRegistry) ListTemplates() []*WorkflowTemplate {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	templates := make([]*WorkflowTemplate, 0, len(r.templates))
	for _, template := range r.templates {
		templates = append(templates, template)
	}

	return templates
}

// GetTemplatesByProvider returns templates for a specific provider
func (r *ProviderRegistry) GetTemplatesByProvider(providerType models.ProviderType) []*WorkflowTemplate {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	templates := make([]*WorkflowTemplate, 0)
	for _, template := range r.templates {
		if template.ProviderType == providerType {
			templates = append(templates, template)
		}
	}

	return templates
}

// ExecuteStep executes a workflow step using the appropriate executor
func (r *ProviderRegistry) ExecuteStep(ctx context.Context, request *ExecutionRequest) (*ExecutionResult, error) {
	// First try to find a specific step executor
	if executor, err := r.GetStepExecutor(request.StepType); err == nil {
		return executor.Execute(ctx, request)
	}

	// Fall back to provider-specific execution
	if request.Environment != nil {
		if executor, err := r.GetProvider(request.Environment.Provider.Type); err == nil {
			return executor.Execute(ctx, request)
		}
	}

	return nil, fmt.Errorf("no executor found for step type %s", request.StepType)
}

// ValidateProviderConfig validates a provider configuration
func (r *ProviderRegistry) ValidateProviderConfig(providerType models.ProviderType, config *models.ProviderConfig) error {
	executor, err := r.GetProvider(providerType)
	if err != nil {
		return err
	}

	return executor.ValidateConfig(config)
}

// GenerateWorkflowSteps generates workflow steps for a deployment
func (r *ProviderRegistry) GenerateWorkflowSteps(deployment *DeploymentRequest) ([]*models.WorkflowStep, error) {
	executor, err := r.GetProvider(deployment.Environment.Provider.Type)
	if err != nil {
		return nil, err
	}

	return executor.GenerateSteps(deployment)
}

// GetProviderCapabilities returns the capabilities of a provider
func (r *ProviderRegistry) GetProviderCapabilities(providerType models.ProviderType) ([]string, error) {
	executor, err := r.GetProvider(providerType)
	if err != nil {
		return nil, err
	}

	return executor.GetCapabilities(), nil
}

// ProviderInfo contains information about a registered provider
type ProviderInfo struct {
	Type         models.ProviderType `json:"type"`
	Capabilities []string            `json:"capabilities"`
}

// StepExecutorInfo contains information about a registered step executor
type StepExecutorInfo struct {
	StepType           string   `json:"stepType"`
	SupportedStepTypes []string `json:"supportedStepTypes"`
}

// GetRegistryStats returns statistics about the registry
func (r *ProviderRegistry) GetRegistryStats() RegistryStats {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	return RegistryStats{
		ProvidersCount:     len(r.executors),
		StepExecutorsCount: len(r.stepExecutors),
		TemplatesCount:     len(r.templates),
	}
}

// RegistryStats contains statistics about the provider registry
type RegistryStats struct {
	ProvidersCount     int `json:"providersCount"`
	StepExecutorsCount int `json:"stepExecutorsCount"`
	TemplatesCount     int `json:"templatesCount"`
}

// InitializeDefaultProviders registers default provider executors
func (r *ProviderRegistry) InitializeDefaultProviders() error {
	// This will be implemented to register default providers
	// For now, we'll just log that initialization is starting
	r.logger.Info("Initializing default provider executors")

	// TODO: Register default providers:
	// - GKE executor
	// - AKS executor
	// - EKS executor
	// - OpenShift executor
	// - VM executor

	return nil
}

// InitializeDefaultTemplates registers default workflow templates
func (r *ProviderRegistry) InitializeDefaultTemplates() error {
	r.logger.Info("Initializing default workflow templates")

	// TODO: Register default templates for each provider
	// - Standard deployment templates
	// - Blue-green deployment templates
	// - Canary deployment templates
	// - Rollback templates

	return nil
}
