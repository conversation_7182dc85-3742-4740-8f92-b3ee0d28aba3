package plugins

import (
	"fmt"
	"io/ioutil"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v3"
)

// PluginConfig represents the plugin configuration from plugin.yaml
type PluginConfig struct {
	APIVersion string `yaml:"apiVersion"`
	Kind       string `yaml:"kind"`
	Metadata   struct {
		Name        string   `yaml:"name"`
		Version     string   `yaml:"version"`
		Description string   `yaml:"description"`
		Author      string   `yaml:"author"`
		License     string   `yaml:"license"`
		Tags        []string `yaml:"tags"`
	} `yaml:"metadata"`
	Spec struct {
		Type       string `yaml:"type"`
		Runtime    string `yaml:"runtime"`
		Entrypoint string `yaml:"entrypoint"`
		Provider   struct {
			Type         string   `yaml:"type"`
			Capabilities []string `yaml:"capabilities"`
		} `yaml:"provider"`
		ConfigSchema  map[string]interface{} `yaml:"configSchema"`
		Configuration struct {
			Schema map[string]interface{} `yaml:"schema"`
		} `yaml:"configuration"`
		HotReload struct {
			Enabled          bool     `yaml:"enabled"`
			WatchPaths       []string `yaml:"watchPaths"`
			ExcludePaths     []string `yaml:"excludePaths"`
			DebounceInterval string   `yaml:"debounceInterval"`
		} `yaml:"hotReload"`
		Dependencies []struct {
			Name    string `yaml:"name"`
			Version string `yaml:"version"`
		} `yaml:"dependencies"`
		Resources struct {
			Memory string `yaml:"memory"`
			CPU    string `yaml:"cpu"`
		} `yaml:"resources"`
	} `yaml:"spec"`
}

// PluginParameter represents a configuration parameter for the frontend
type PluginParameter struct {
	Name        string                `json:"name"`
	Type        string                `json:"type"`
	Label       string                `json:"label"`
	Description string                `json:"description"`
	Required    bool                  `json:"required"`
	Sensitive   bool                  `json:"sensitive"`
	Default     interface{}           `json:"default,omitempty"`
	Placeholder string                `json:"placeholder,omitempty"`
	Options     []ParameterOption     `json:"options,omitempty"`
	Validation  *ParameterValidation  `json:"validation,omitempty"`
	Properties  []PluginParameter     `json:"properties,omitempty"`
	Items       *PluginParameter      `json:"items,omitempty"`
	Conditional *ParameterConditional `json:"conditional,omitempty"`
}

// ParameterOption represents a select option
type ParameterOption struct {
	Value string `json:"value"`
	Label string `json:"label"`
}

// ParameterValidation represents validation rules
type ParameterValidation struct {
	Pattern   string   `json:"pattern,omitempty"`
	Min       *float64 `json:"min,omitempty"`
	Max       *float64 `json:"max,omitempty"`
	MinLength *int     `json:"minLength,omitempty"`
	MaxLength *int     `json:"maxLength,omitempty"`
	Enum      []string `json:"enum,omitempty"`
}

// ParameterConditional represents conditional field visibility
type ParameterConditional struct {
	Field    string      `json:"field"`
	Value    interface{} `json:"value"`
	Operator string      `json:"operator"` // equals, not_equals, contains, not_contains
}

// PluginConfigurationSchema represents the complete configuration schema for frontend
type PluginConfigurationSchema struct {
	PluginName   string            `json:"pluginName"`
	PluginType   string            `json:"pluginType"`
	Version      string            `json:"version"`
	Description  string            `json:"description"`
	Title        string            `json:"title"`
	Parameters   []PluginParameter `json:"parameters"`
	Capabilities []string          `json:"capabilities"`
}

// ConfigParser handles parsing plugin configurations
type ConfigParser struct{}

// NewConfigParser creates a new config parser
func NewConfigParser() *ConfigParser {
	return &ConfigParser{}
}

// ParsePluginConfig parses a plugin.yaml file and returns the configuration
func (cp *ConfigParser) ParsePluginConfig(pluginPath string) (*PluginConfig, error) {
	configPath := filepath.Join(pluginPath, "plugin.yaml")

	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read plugin config: %w", err)
	}

	var config PluginConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse plugin config: %w", err)
	}

	return &config, nil
}

// ConvertToFrontendSchema converts plugin config to frontend-compatible schema
func (cp *ConfigParser) ConvertToFrontendSchema(config *PluginConfig) *PluginConfigurationSchema {
	schema := &PluginConfigurationSchema{
		PluginName:   config.Metadata.Name,
		PluginType:   config.Spec.Type,
		Version:      config.Metadata.Version,
		Description:  config.Metadata.Description,
		Title:        fmt.Sprintf("Configure %s", config.Metadata.Name),
		Capabilities: config.Spec.Provider.Capabilities,
		Parameters:   []PluginParameter{},
	}

	// Parse configSchema properties (new format)
	if config.Spec.ConfigSchema != nil {
		if properties, ok := config.Spec.ConfigSchema["properties"].(map[string]interface{}); ok {
			schema.Parameters = cp.parseSchemaProperties(properties)
		}
	}

	// Parse configuration.schema properties (existing format)
	if config.Spec.Configuration.Schema != nil {
		if properties, ok := config.Spec.Configuration.Schema["properties"].(map[string]interface{}); ok {
			schema.Parameters = cp.parseSchemaProperties(properties)
		}
	}

	return schema
}

// parseSchemaProperties recursively parses schema properties
func (cp *ConfigParser) parseSchemaProperties(properties map[string]interface{}) []PluginParameter {
	var parameters []PluginParameter

	for name, prop := range properties {
		if propMap, ok := prop.(map[string]interface{}); ok {
			parameter := cp.parseSchemaProperty(name, propMap)
			parameters = append(parameters, parameter)
		}
	}

	return parameters
}

// parseSchemaProperty parses a single schema property
func (cp *ConfigParser) parseSchemaProperty(name string, prop map[string]interface{}) PluginParameter {
	parameter := PluginParameter{
		Name:  name,
		Label: cp.formatLabel(name),
	}

	// Type mapping
	if typeVal, ok := prop["type"].(string); ok {
		parameter.Type = cp.mapSchemaTypeToFormType(typeVal)
	} else {
		parameter.Type = "string"
	}

	// Basic properties
	if desc, ok := prop["description"].(string); ok {
		parameter.Description = desc
	}
	if required, ok := prop["required"].(bool); ok {
		parameter.Required = required
	}
	if sensitive, ok := prop["sensitive"].(bool); ok {
		parameter.Sensitive = sensitive
	}
	if defaultVal, ok := prop["default"]; ok {
		parameter.Default = defaultVal
	}
	if example, ok := prop["example"].(string); ok {
		parameter.Placeholder = example
	}
	if placeholder, ok := prop["placeholder"].(string); ok {
		parameter.Placeholder = placeholder
	}

	// Validation rules
	parameter.Validation = cp.parseValidation(prop)

	// Handle enum as select options
	if enum, ok := prop["enum"].([]interface{}); ok {
		parameter.Type = "select"
		parameter.Options = cp.convertEnumToOptions(enum)
	}

	// Handle object properties
	if parameter.Type == "object" {
		if properties, ok := prop["properties"].(map[string]interface{}); ok {
			parameter.Properties = cp.parseSchemaProperties(properties)
		}
	}

	// Handle array items
	if parameter.Type == "array" {
		if items, ok := prop["items"].(map[string]interface{}); ok {
			itemParam := cp.parseSchemaProperty("item", items)
			parameter.Items = &itemParam
		}
	}

	return parameter
}

// parseValidation extracts validation rules from property
func (cp *ConfigParser) parseValidation(prop map[string]interface{}) *ParameterValidation {
	validation := &ParameterValidation{}
	hasValidation := false

	if pattern, ok := prop["pattern"].(string); ok {
		validation.Pattern = pattern
		hasValidation = true
	}
	if min, ok := prop["minimum"].(float64); ok {
		validation.Min = &min
		hasValidation = true
	}
	if max, ok := prop["maximum"].(float64); ok {
		validation.Max = &max
		hasValidation = true
	}
	if minLen, ok := prop["minLength"].(int); ok {
		validation.MinLength = &minLen
		hasValidation = true
	}
	if maxLen, ok := prop["maxLength"].(int); ok {
		validation.MaxLength = &maxLen
		hasValidation = true
	}
	if enum, ok := prop["enum"].([]interface{}); ok {
		validation.Enum = cp.convertInterfaceSliceToStringSlice(enum)
		hasValidation = true
	}

	if hasValidation {
		return validation
	}
	return nil
}

// Helper functions
func (cp *ConfigParser) mapSchemaTypeToFormType(schemaType string) string {
	switch schemaType {
	case "string":
		return "string"
	case "number", "integer":
		return "number"
	case "boolean":
		return "boolean"
	case "object":
		return "object"
	case "array":
		return "array"
	default:
		return "string"
	}
}

func (cp *ConfigParser) formatLabel(name string) string {
	// Convert camelCase and snake_case to Title Case
	result := strings.ReplaceAll(name, "_", " ")
	result = strings.ReplaceAll(result, "-", " ")

	// Add spaces before capital letters
	var formatted strings.Builder
	for i, r := range result {
		if i > 0 && r >= 'A' && r <= 'Z' {
			formatted.WriteRune(' ')
		}
		formatted.WriteRune(r)
	}

	// Title case
	words := strings.Fields(formatted.String())
	for i, word := range words {
		if len(word) > 0 {
			words[i] = strings.ToUpper(word[:1]) + strings.ToLower(word[1:])
		}
	}

	return strings.Join(words, " ")
}

func (cp *ConfigParser) convertEnumToOptions(enum []interface{}) []ParameterOption {
	var options []ParameterOption
	for _, val := range enum {
		if strVal, ok := val.(string); ok {
			options = append(options, ParameterOption{
				Value: strVal,
				Label: cp.formatLabel(strVal),
			})
		}
	}
	return options
}

func (cp *ConfigParser) convertInterfaceSliceToStringSlice(slice []interface{}) []string {
	var result []string
	for _, val := range slice {
		if strVal, ok := val.(string); ok {
			result = append(result, strVal)
		}
	}
	return result
}
