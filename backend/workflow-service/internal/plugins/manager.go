package plugins

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"plugin"
	"strings"
	"sync"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/models"
	"github.com/claudio/deploy-orchestrator/workflow-service/internal/providers"
	"github.com/fsnotify/fsnotify"
	"go.uber.org/zap"
	"gopkg.in/yaml.v3"
)

// PluginInterface defines the interface that all plugins must implement
type PluginInterface interface {
	GetExecutor() providers.ProviderExecutor
	Initialize(ctx context.Context) error
	Shutdown(ctx context.Context) error
	Health(ctx context.Context) error
	GetInfo() PluginInfo
}

// PluginInfo contains plugin metadata
type PluginInfo struct {
	Name        string `json:"name"`
	Version     string `json:"version"`
	Type        string `json:"type"`
	Description string `json:"description"`
}

// PluginManager manages the lifecycle of plugins with hot reload support
type PluginManager struct {
	registry   *providers.ProviderRegistry
	plugins    map[string]*PluginInstance
	pluginsDir string
	logger     *zap.Logger
	mutex      sync.RWMutex
	hotReload  bool
	watchers   map[string]*fsnotify.Watcher
	ctx        context.Context
	cancel     context.CancelFunc
}

// PluginInstance represents a loaded plugin instance
type PluginInstance struct {
	Name        string                 `json:"name"`
	Version     string                 `json:"version"`
	Type        string                 `json:"type"`
	Path        string                 `json:"path"`
	Config      map[string]interface{} `json:"config"`
	Enabled     bool                   `json:"enabled"`
	LastReload  time.Time              `json:"lastReload"`
	ReloadCount int                    `json:"reloadCount"`
	Status      string                 `json:"status"` // loading, running, error, disabled
	Error       string                 `json:"error,omitempty"`
	HotReload   bool                   `json:"hotReload"`

	// Internal fields
	manifest     *PluginManifest
	executor     providers.ProviderExecutor
	pluginHandle *plugin.Plugin
	watcher      *fsnotify.Watcher
}

// PluginManifest represents the plugin.yaml configuration
type PluginManifest struct {
	APIVersion string `yaml:"apiVersion"`
	Kind       string `yaml:"kind"`
	Metadata   struct {
		Name        string `yaml:"name"`
		Version     string `yaml:"version"`
		Description string `yaml:"description"`
		Author      string `yaml:"author"`
		License     string `yaml:"license"`
	} `yaml:"metadata"`
	Spec struct {
		Type       string `yaml:"type"`
		Runtime    string `yaml:"runtime"`
		Entrypoint string `yaml:"entrypoint"`
		Provider   struct {
			Type         string   `yaml:"type"`
			Capabilities []string `yaml:"capabilities"`
		} `yaml:"provider"`
		ConfigSchema struct {
			Type       string                 `yaml:"type"`
			Properties map[string]interface{} `yaml:"properties"`
		} `yaml:"configSchema"`
		HotReload struct {
			Enabled          bool     `yaml:"enabled"`
			WatchPaths       []string `yaml:"watchPaths"`
			ExcludePaths     []string `yaml:"excludePaths"`
			DebounceInterval string   `yaml:"debounceInterval"`
		} `yaml:"hotReload"`
		Dependencies []struct {
			Name    string `yaml:"name"`
			Version string `yaml:"version"`
		} `yaml:"dependencies"`
		Resources struct {
			Memory string `yaml:"memory"`
			CPU    string `yaml:"cpu"`
		} `yaml:"resources"`
	} `yaml:"spec"`
}

// NewPluginManager creates a new plugin manager
func NewPluginManager(registry *providers.ProviderRegistry, pluginsDir string, logger *zap.Logger) *PluginManager {
	ctx, cancel := context.WithCancel(context.Background())

	return &PluginManager{
		registry:   registry,
		plugins:    make(map[string]*PluginInstance),
		pluginsDir: pluginsDir,
		logger:     logger,
		hotReload:  true,
		watchers:   make(map[string]*fsnotify.Watcher),
		ctx:        ctx,
		cancel:     cancel,
	}
}

// Start initializes the plugin manager and loads existing plugins
func (pm *PluginManager) Start() error {
	pm.logger.Info("Starting plugin manager", zap.String("pluginsDir", pm.pluginsDir))

	// Create plugins directory if it doesn't exist
	if err := os.MkdirAll(pm.pluginsDir, 0755); err != nil {
		return fmt.Errorf("failed to create plugins directory: %w", err)
	}

	// Load existing plugins
	if err := pm.loadExistingPlugins(); err != nil {
		pm.logger.Error("Failed to load existing plugins", zap.Error(err))
		// Don't return error, continue with empty plugin set
	}

	pm.logger.Info("Plugin manager started successfully")
	return nil
}

// Stop shuts down the plugin manager and all plugins
func (pm *PluginManager) Stop() error {
	pm.logger.Info("Stopping plugin manager")

	// Cancel context to stop all watchers
	pm.cancel()

	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// Stop all plugins
	for name, instance := range pm.plugins {
		if err := pm.unloadPluginUnsafe(name, instance); err != nil {
			pm.logger.Error("Failed to unload plugin",
				zap.String("plugin", name),
				zap.Error(err))
		}
	}

	// Close all watchers
	for _, watcher := range pm.watchers {
		watcher.Close()
	}

	pm.logger.Info("Plugin manager stopped")
	return nil
}

// LoadPlugin loads a plugin from the specified path
func (pm *PluginManager) LoadPlugin(name, path string, config map[string]interface{}) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.logger.Info("Loading plugin",
		zap.String("name", name),
		zap.String("path", path))

	// Check if plugin already exists
	if _, exists := pm.plugins[name]; exists {
		return fmt.Errorf("plugin %s already loaded", name)
	}

	// Load plugin manifest
	manifest, err := pm.loadManifest(path)
	if err != nil {
		return fmt.Errorf("failed to load plugin manifest: %w", err)
	}

	// Create plugin instance
	instance := &PluginInstance{
		Name:        name,
		Version:     manifest.Metadata.Version,
		Type:        manifest.Spec.Type,
		Path:        path,
		Config:      config,
		Enabled:     true,
		LastReload:  time.Now(),
		ReloadCount: 0,
		Status:      "loading",
		HotReload:   manifest.Spec.HotReload.Enabled,
		manifest:    manifest,
	}

	// Load the actual plugin
	if err := pm.loadPluginExecutor(instance); err != nil {
		instance.Status = "error"
		instance.Error = err.Error()
		pm.plugins[name] = instance
		return fmt.Errorf("failed to load plugin executor: %w", err)
	}

	// Register with provider registry
	if instance.executor != nil {
		if err := pm.registry.RegisterProvider(instance.executor); err != nil {
			instance.Status = "error"
			instance.Error = err.Error()
			pm.plugins[name] = instance
			return fmt.Errorf("failed to register provider: %w", err)
		}
	}

	// Set up hot reload if enabled
	if pm.hotReload && instance.HotReload {
		if err := pm.setupHotReload(instance); err != nil {
			pm.logger.Warn("Failed to setup hot reload",
				zap.String("plugin", name),
				zap.Error(err))
		}
	}

	instance.Status = "running"
	pm.plugins[name] = instance

	pm.logger.Info("Plugin loaded successfully",
		zap.String("name", name),
		zap.String("version", instance.Version),
		zap.Bool("hotReload", instance.HotReload))

	return nil
}

// UnloadPlugin unloads a plugin
func (pm *PluginManager) UnloadPlugin(name string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	instance, exists := pm.plugins[name]
	if !exists {
		return fmt.Errorf("plugin %s not found", name)
	}

	return pm.unloadPluginUnsafe(name, instance)
}

// unloadPluginUnsafe unloads a plugin without locking (internal use)
func (pm *PluginManager) unloadPluginUnsafe(name string, instance *PluginInstance) error {
	pm.logger.Info("Unloading plugin", zap.String("name", name))

	// Stop hot reload watcher
	if instance.watcher != nil {
		instance.watcher.Close()
		delete(pm.watchers, name)
	}

	// TODO: Unregister from provider registry
	// pm.registry.UnregisterProvider(name)

	// Remove from plugins map
	delete(pm.plugins, name)

	pm.logger.Info("Plugin unloaded", zap.String("name", name))
	return nil
}

// ReloadPlugin reloads a plugin (hot reload)
func (pm *PluginManager) ReloadPlugin(name string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	instance, exists := pm.plugins[name]
	if !exists {
		return fmt.Errorf("plugin %s not found", name)
	}

	pm.logger.Info("Reloading plugin", zap.String("name", name))

	// Reload plugin manifest
	manifest, err := pm.loadManifest(instance.Path)
	if err != nil {
		return fmt.Errorf("failed to reload plugin manifest: %w", err)
	}

	// Update manifest
	instance.manifest = manifest
	instance.Version = manifest.Metadata.Version

	// Reload plugin executor
	if err := pm.loadPluginExecutor(instance); err != nil {
		instance.Status = "error"
		instance.Error = err.Error()
		return fmt.Errorf("failed to reload plugin executor: %w", err)
	}

	// Update registry (would need registry support for replacement)
	// pm.registry.ReplaceProvider(name, instance.executor)

	instance.LastReload = time.Now()
	instance.ReloadCount++
	instance.Status = "running"
	instance.Error = ""

	pm.logger.Info("Plugin reloaded successfully",
		zap.String("name", name),
		zap.Int("reloadCount", instance.ReloadCount))

	return nil
}

// GetPlugin returns plugin information
func (pm *PluginManager) GetPlugin(name string) (*PluginInstance, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	instance, exists := pm.plugins[name]
	if !exists {
		return nil, fmt.Errorf("plugin %s not found", name)
	}

	return instance, nil
}

// ListPlugins returns all loaded plugins
func (pm *PluginManager) ListPlugins() []*PluginInstance {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	plugins := make([]*PluginInstance, 0, len(pm.plugins))
	for _, instance := range pm.plugins {
		plugins = append(plugins, instance)
	}

	return plugins
}

// EnablePlugin enables a plugin
func (pm *PluginManager) EnablePlugin(name string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	instance, exists := pm.plugins[name]
	if !exists {
		return fmt.Errorf("plugin %s not found", name)
	}

	instance.Enabled = true
	pm.logger.Info("Plugin enabled", zap.String("name", name))

	return nil
}

// DisablePlugin disables a plugin
func (pm *PluginManager) DisablePlugin(name string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	instance, exists := pm.plugins[name]
	if !exists {
		return fmt.Errorf("plugin %s not found", name)
	}

	instance.Enabled = false
	instance.Status = "disabled"
	pm.logger.Info("Plugin disabled", zap.String("name", name))

	return nil
}

// loadExistingPlugins loads all plugins from the plugins directory
func (pm *PluginManager) loadExistingPlugins() error {
	entries, err := os.ReadDir(pm.pluginsDir)
	if err != nil {
		return err
	}

	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		pluginPath := filepath.Join(pm.pluginsDir, entry.Name())
		manifestPath := filepath.Join(pluginPath, "plugin.yaml")

		// Check if plugin.yaml exists
		if _, err := os.Stat(manifestPath); os.IsNotExist(err) {
			pm.logger.Debug("Skipping directory without plugin.yaml",
				zap.String("path", pluginPath))
			continue
		}

		// Load plugin with empty config (would be loaded from config file)
		if err := pm.LoadPlugin(entry.Name(), pluginPath, make(map[string]interface{})); err != nil {
			pm.logger.Error("Failed to load plugin",
				zap.String("name", entry.Name()),
				zap.String("path", pluginPath),
				zap.Error(err))
		}
	}

	return nil
}

// loadManifest loads and parses the plugin manifest
func (pm *PluginManager) loadManifest(pluginPath string) (*PluginManifest, error) {
	manifestPath := filepath.Join(pluginPath, "plugin.yaml")

	data, err := os.ReadFile(manifestPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read plugin manifest: %w", err)
	}

	var manifest PluginManifest
	if err := yaml.Unmarshal(data, &manifest); err != nil {
		return nil, fmt.Errorf("failed to parse plugin manifest: %w", err)
	}

	return &manifest, nil
}

// loadPluginExecutor loads the plugin executor based on runtime
func (pm *PluginManager) loadPluginExecutor(instance *PluginInstance) error {
	switch instance.manifest.Spec.Runtime {
	case "go":
		return pm.loadGoPlugin(instance)
	case "binary":
		return pm.loadBinaryPlugin(instance)
	default:
		return fmt.Errorf("unsupported runtime: %s", instance.manifest.Spec.Runtime)
	}
}

// loadGoPlugin loads a Go plugin
func (pm *PluginManager) loadGoPlugin(instance *PluginInstance) error {
	pm.logger.Info("Loading Go plugin",
		zap.String("name", instance.Name),
		zap.String("entrypoint", instance.manifest.Spec.Entrypoint))

	// Build the plugin if needed
	if err := pm.buildGoPlugin(instance); err != nil {
		return fmt.Errorf("failed to build Go plugin: %w", err)
	}

	// Load the compiled plugin
	pluginPath := filepath.Join(instance.Path, instance.Name+".so")

	// Load the plugin
	p, err := plugin.Open(pluginPath)
	if err != nil {
		// Fallback to mock for development
		pm.logger.Warn("Failed to load compiled plugin, using mock",
			zap.String("plugin", instance.Name),
			zap.Error(err))

		instance.executor = &MockExecutor{
			providerType: models.ProviderType(instance.manifest.Spec.Provider.Type),
			capabilities: instance.manifest.Spec.Provider.Capabilities,
			logger:       pm.logger,
		}
		return nil
	}

	// Look for the NewPlugin function
	newPluginSymbol, err := p.Lookup("NewPlugin")
	if err != nil {
		return fmt.Errorf("plugin does not export NewPlugin function: %w", err)
	}

	// Cast to the expected function signature
	newPluginFunc, ok := newPluginSymbol.(func(map[string]interface{}, *zap.Logger) (PluginInterface, error))
	if !ok {
		return fmt.Errorf("NewPlugin function has incorrect signature")
	}

	// Create plugin instance
	pluginInstance, err := newPluginFunc(instance.Config, pm.logger)
	if err != nil {
		return fmt.Errorf("failed to create plugin instance: %w", err)
	}

	// Initialize plugin
	if err := pluginInstance.Initialize(pm.ctx); err != nil {
		return fmt.Errorf("failed to initialize plugin: %w", err)
	}

	// Get executor
	instance.executor = pluginInstance.GetExecutor()
	instance.pluginHandle = p

	return nil
}

// buildGoPlugin builds a Go plugin
func (pm *PluginManager) buildGoPlugin(instance *PluginInstance) error {
	// Check if plugin needs building
	sourcePath := filepath.Join(instance.Path, instance.manifest.Spec.Entrypoint)
	outputPath := filepath.Join(instance.Path, instance.Name+".so")

	// Check if source is newer than output
	sourceInfo, err := os.Stat(sourcePath)
	if err != nil {
		return fmt.Errorf("source file not found: %w", err)
	}

	outputInfo, err := os.Stat(outputPath)
	if err == nil && outputInfo.ModTime().After(sourceInfo.ModTime()) {
		// Output is newer, no need to rebuild
		return nil
	}

	pm.logger.Info("Building Go plugin",
		zap.String("plugin", instance.Name),
		zap.String("source", sourcePath))

	// Build command
	cmd := exec.Command("go", "build", "-buildmode=plugin", "-o", outputPath, sourcePath)
	cmd.Dir = instance.Path

	// Set environment variables
	cmd.Env = append(os.Environ(),
		"CGO_ENABLED=1",
		"GO111MODULE=on",
	)

	// Run build
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("build failed: %w\nOutput: %s", err, string(output))
	}

	pm.logger.Info("Go plugin built successfully",
		zap.String("plugin", instance.Name),
		zap.String("output", outputPath))

	return nil
}

// loadBinaryPlugin loads a prebuilt binary plugin
func (pm *PluginManager) loadBinaryPlugin(instance *PluginInstance) error {
	pm.logger.Info("Loading binary plugin",
		zap.String("name", instance.Name),
		zap.String("entrypoint", instance.manifest.Spec.Entrypoint))

	// Check if binary exists
	binaryPath := filepath.Join(instance.Path, instance.manifest.Spec.Entrypoint)
	if _, err := os.Stat(binaryPath); os.IsNotExist(err) {
		return fmt.Errorf("binary plugin not found: %s", binaryPath)
	}

	// Make sure binary is executable
	if err := os.Chmod(binaryPath, 0755); err != nil {
		pm.logger.Warn("Failed to make binary executable", zap.Error(err))
	}

	// Create a binary executor that wraps the binary plugin
	instance.executor = &BinaryExecutor{
		binaryPath:   binaryPath,
		providerType: models.ProviderType(instance.manifest.Spec.Provider.Type),
		capabilities: instance.manifest.Spec.Provider.Capabilities,
		logger:       pm.logger,
		config:       instance.Config,
	}

	pm.logger.Info("Binary plugin loaded successfully",
		zap.String("name", instance.Name),
		zap.String("binary", binaryPath))

	return nil
}

// setupHotReload sets up file watching for hot reload
func (pm *PluginManager) setupHotReload(instance *PluginInstance) error {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return err
	}

	// Add plugin directory to watcher
	if err := watcher.Add(instance.Path); err != nil {
		watcher.Close()
		return err
	}

	instance.watcher = watcher
	pm.watchers[instance.Name] = watcher

	// Start watching in a goroutine
	go pm.watchPlugin(instance.Name, watcher)

	return nil
}

// watchPlugin watches for file changes and triggers reload
func (pm *PluginManager) watchPlugin(name string, watcher *fsnotify.Watcher) {
	debounceTimer := time.NewTimer(0)
	debounceTimer.Stop()

	for {
		select {
		case <-pm.ctx.Done():
			return

		case event, ok := <-watcher.Events:
			if !ok {
				return
			}

			// Check if file should trigger reload
			if pm.shouldReload(event.Name) {
				pm.logger.Debug("File changed, scheduling reload",
					zap.String("plugin", name),
					zap.String("file", event.Name))

				// Debounce rapid file changes
				debounceTimer.Reset(2 * time.Second)
			}

		case err, ok := <-watcher.Errors:
			if !ok {
				return
			}
			pm.logger.Error("Plugin watcher error",
				zap.String("plugin", name),
				zap.Error(err))

		case <-debounceTimer.C:
			// Trigger reload
			if err := pm.ReloadPlugin(name); err != nil {
				pm.logger.Error("Failed to hot reload plugin",
					zap.String("plugin", name),
					zap.Error(err))
			}
		}
	}
}

// shouldReload checks if file change should trigger reload
func (pm *PluginManager) shouldReload(filename string) bool {
	ext := filepath.Ext(filename)

	// Watch Go files, YAML files, JSON configs
	watchExtensions := []string{".go", ".yaml", ".yml", ".json"}

	for _, watchExt := range watchExtensions {
		if ext == watchExt {
			return true
		}
	}

	return false
}

// BinaryExecutor wraps a prebuilt binary plugin
type BinaryExecutor struct {
	binaryPath   string
	providerType models.ProviderType
	capabilities []string
	logger       *zap.Logger
	config       map[string]interface{}
}

func (b *BinaryExecutor) GetProviderType() models.ProviderType {
	return b.providerType
}

func (b *BinaryExecutor) GetCapabilities() []string {
	return b.capabilities
}

func (b *BinaryExecutor) ValidateConfig(config *models.ProviderConfig) error {
	// Basic validation - could be enhanced to call binary for validation
	return nil
}

func (b *BinaryExecutor) GenerateSteps(deployment *providers.DeploymentRequest) ([]*models.WorkflowStep, error) {
	// For binary plugins, we'll generate basic steps based on capabilities
	// This could be enhanced to call the binary for step generation
	return []*models.WorkflowStep{
		{
			Name:      "deploy",
			Type:      string(b.providerType),
			Config:    map[string]interface{}{},
			DependsOn: []string{},
		},
	}, nil
}

func (b *BinaryExecutor) Execute(ctx context.Context, request *providers.ExecutionRequest) (*providers.ExecutionResult, error) {
	b.logger.Info("Executing binary plugin",
		zap.String("binary", b.binaryPath),
		zap.String("operation", request.StepType))

	// Prepare execution parameters
	params := map[string]interface{}{
		"operation":   request.StepType,
		"parameters":  request.Config,
		"stepId":      request.StepID,
		"stepName":    request.StepName,
		"executionId": request.ExecutionID,
		"variables":   request.Variables,
		"secrets":     request.Secrets,
	}

	// Add environment info if available
	if request.Environment != nil {
		params["environment"] = map[string]interface{}{
			"id":   request.Environment.ID,
			"name": request.Environment.Name,
			"type": request.Environment.Type,
		}
	}

	// Convert parameters to JSON for binary input
	paramsJSON, err := json.Marshal(params)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal parameters: %w", err)
	}

	// Execute binary plugin
	cmd := exec.CommandContext(ctx, b.binaryPath)
	cmd.Stdin = strings.NewReader(string(paramsJSON))

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return &providers.ExecutionResult{
			Success:      false,
			Output:       map[string]interface{}{"error": fmt.Sprintf("Binary plugin execution failed: %v", err)},
			ErrorMessage: fmt.Sprintf("Binary plugin execution failed: %v", err),
			ErrorDetails: stderr.String(),
		}, err
	}

	// Parse output
	var result map[string]interface{}
	if err := json.Unmarshal(stdout.Bytes(), &result); err != nil {
		// If output is not JSON, treat as plain text message
		return &providers.ExecutionResult{
			Success: true,
			Output:  map[string]interface{}{"message": stdout.String()},
		}, nil
	}

	// Convert to ExecutionResult
	success, _ := result["success"].(bool)
	output, _ := result["output"].(map[string]interface{})
	if output == nil {
		output = make(map[string]interface{})
	}

	errorMessage, _ := result["error"].(string)
	errorDetails, _ := result["errorDetails"].(string)

	// Handle legacy format
	if message, exists := result["message"].(string); exists {
		output["message"] = message
	}
	if data, exists := result["data"].(map[string]interface{}); exists {
		for k, v := range data {
			output[k] = v
		}
	}

	return &providers.ExecutionResult{
		Success:      success,
		Output:       output,
		ErrorMessage: errorMessage,
		ErrorDetails: errorDetails,
	}, nil
}

func (b *BinaryExecutor) GetStatus(ctx context.Context, deploymentID string) (*providers.DeploymentStatus, error) {
	b.logger.Info("Getting deployment status from binary plugin",
		zap.String("binary", b.binaryPath),
		zap.String("deploymentId", deploymentID))

	// Prepare parameters for status check
	params := map[string]interface{}{
		"operation":    "status",
		"deploymentId": deploymentID,
	}

	// Execute binary plugin for status
	result, err := b.executeBinaryCommand(ctx, params)
	if err != nil {
		return nil, err
	}

	// Parse status result
	status := &providers.DeploymentStatus{
		ID:     deploymentID,
		Status: "unknown",
	}

	if statusData, exists := result["status"].(map[string]interface{}); exists {
		if statusStr, ok := statusData["status"].(string); ok {
			status.Status = statusStr
		}
		if message, ok := statusData["message"].(string); ok {
			status.Message = message
		}
	}

	return status, nil
}

func (b *BinaryExecutor) Rollback(ctx context.Context, deploymentID string, targetVersion string) error {
	b.logger.Info("Performing rollback via binary plugin",
		zap.String("binary", b.binaryPath),
		zap.String("deploymentId", deploymentID),
		zap.String("targetVersion", targetVersion))

	// Prepare parameters for rollback
	params := map[string]interface{}{
		"operation":     "rollback",
		"deploymentId":  deploymentID,
		"targetVersion": targetVersion,
	}

	// Execute binary plugin for rollback
	result, err := b.executeBinaryCommand(ctx, params)
	if err != nil {
		return err
	}

	// Check if rollback was successful
	if success, exists := result["success"].(bool); exists && !success {
		if errorMsg, exists := result["error"].(string); exists {
			return fmt.Errorf("rollback failed: %s", errorMsg)
		}
		return fmt.Errorf("rollback failed")
	}

	return nil
}

func (b *BinaryExecutor) GetLogs(ctx context.Context, deploymentID string, options *providers.LogOptions) ([]*models.LogEntry, error) {
	b.logger.Info("Getting logs from binary plugin",
		zap.String("binary", b.binaryPath),
		zap.String("deploymentId", deploymentID))

	// Prepare parameters for log retrieval
	params := map[string]interface{}{
		"operation":    "logs",
		"deploymentId": deploymentID,
	}

	if options != nil {
		params["options"] = map[string]interface{}{
			"lines":     options.Lines,
			"since":     options.Since,
			"follow":    options.Follow,
			"container": options.Container,
		}
	}

	// Execute binary plugin for logs
	result, err := b.executeBinaryCommand(ctx, params)
	if err != nil {
		return nil, err
	}

	// Parse logs result
	var logs []*models.LogEntry
	if logsData, exists := result["logs"].([]interface{}); exists {
		for _, logData := range logsData {
			if logMap, ok := logData.(map[string]interface{}); ok {
				log := &models.LogEntry{}
				if id, ok := logMap["id"].(string); ok {
					log.ID = id
				}
				if message, ok := logMap["message"].(string); ok {
					log.Message = message
				}
				if level, ok := logMap["level"].(string); ok {
					log.Level = level
				}
				if source, ok := logMap["source"].(string); ok {
					log.Source = source
				}
				logs = append(logs, log)
			}
		}
	}

	return logs, nil
}

func (b *BinaryExecutor) Cleanup(ctx context.Context, deploymentID string) error {
	b.logger.Info("Performing cleanup via binary plugin",
		zap.String("binary", b.binaryPath),
		zap.String("deploymentId", deploymentID))

	// Prepare parameters for cleanup
	params := map[string]interface{}{
		"operation":    "cleanup",
		"deploymentId": deploymentID,
	}

	// Execute binary plugin for cleanup
	result, err := b.executeBinaryCommand(ctx, params)
	if err != nil {
		return err
	}

	// Check if cleanup was successful
	if success, exists := result["success"].(bool); exists && !success {
		if errorMsg, exists := result["error"].(string); exists {
			return fmt.Errorf("cleanup failed: %s", errorMsg)
		}
		return fmt.Errorf("cleanup failed")
	}

	return nil
}

// executeBinaryCommand is a helper method to execute binary commands
func (b *BinaryExecutor) executeBinaryCommand(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error) {
	// Convert parameters to JSON
	paramsJSON, err := json.Marshal(params)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal parameters: %w", err)
	}

	// Execute binary plugin
	cmd := exec.CommandContext(ctx, b.binaryPath)
	cmd.Stdin = strings.NewReader(string(paramsJSON))

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("binary plugin execution failed: %v, stderr: %s", err, stderr.String())
	}

	// Parse output
	var result map[string]interface{}
	if err := json.Unmarshal(stdout.Bytes(), &result); err != nil {
		return nil, fmt.Errorf("failed to parse binary plugin output: %w", err)
	}

	return result, nil
}

// MockExecutor provides a mock implementation for testing
type MockExecutor struct {
	providerType models.ProviderType
	capabilities []string
	logger       *zap.Logger
}

func (m *MockExecutor) GetProviderType() models.ProviderType {
	return m.providerType
}

func (m *MockExecutor) GetCapabilities() []string {
	return m.capabilities
}

func (m *MockExecutor) ValidateConfig(config *models.ProviderConfig) error {
	return nil
}

func (m *MockExecutor) GenerateSteps(deployment *providers.DeploymentRequest) ([]*models.WorkflowStep, error) {
	return []*models.WorkflowStep{}, nil
}

func (m *MockExecutor) Execute(ctx context.Context, request *providers.ExecutionRequest) (*providers.ExecutionResult, error) {
	return &providers.ExecutionResult{Success: true}, nil
}

func (m *MockExecutor) GetStatus(ctx context.Context, deploymentID string) (*providers.DeploymentStatus, error) {
	return &providers.DeploymentStatus{}, nil
}

func (m *MockExecutor) Rollback(ctx context.Context, deploymentID string, targetVersion string) error {
	return nil
}

func (m *MockExecutor) GetLogs(ctx context.Context, deploymentID string, options *providers.LogOptions) ([]*models.LogEntry, error) {
	return []*models.LogEntry{}, nil
}

func (m *MockExecutor) Cleanup(ctx context.Context, deploymentID string) error {
	return nil
}
