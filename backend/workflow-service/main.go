package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/auth"
	"github.com/claudio/deploy-orchestrator/shared/gateway"
	"github.com/claudio/deploy-orchestrator/shared/monitoring"
	"github.com/claudio/deploy-orchestrator/workflow-service/api"
	"github.com/claudio/deploy-orchestrator/workflow-service/config"
	"github.com/claudio/deploy-orchestrator/workflow-service/engine"
	"github.com/claudio/deploy-orchestrator/workflow-service/internal/handlers"
	"github.com/claudio/deploy-orchestrator/workflow-service/internal/migrations"
	"github.com/claudio/deploy-orchestrator/workflow-service/internal/plugins"
	"github.com/claudio/deploy-orchestrator/workflow-service/internal/providers"
	"github.com/claudio/deploy-orchestrator/workflow-service/internal/services"
	"github.com/claudio/deploy-orchestrator/workflow-service/middleware"
	"github.com/claudio/deploy-orchestrator/workflow-service/storage"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

const (
	serviceName = "workflow-service"
	version     = "1.0.0"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database
	db, err := storage.NewDatabase(cfg.Database.URL)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Run promotion table migrations
	if err := migrations.MigratePromotionTables(db.GetDB()); err != nil {
		log.Fatalf("Failed to run promotion migrations: %v", err)
	}

	// Initialize workflow engine
	workflowEngine := engine.NewWorkflowEngine(db)

	// Start instance manager
	ctx := context.Background()
	if err := workflowEngine.StartInstanceManager(ctx); err != nil {
		log.Fatalf("Failed to start instance manager: %v", err)
	}

	// Initialize authentication manager
	authManager, err := auth.NewAuthManagerFromSharedConfig(&cfg.Auth)
	if err != nil {
		log.Fatalf("Failed to initialize auth manager: %v", err)
	}

	// Initialize monitoring
	monitoringManager := monitoring.NewMonitoringManager(serviceName, version, "development")
	log.Println("Monitoring system initialized")

	// Initialize gateway registration
	zapLogger, _ := zap.NewProduction()
	gatewayClient := gateway.NewClientFromSharedConfig("workflow-service", cfg.Server.Port, &cfg.Gateway, zapLogger)
	gatewayClient.SafeRegister()
	log.Println("Gateway registration attempted")

	// Setup router
	router := gin.New()

	// Setup monitoring routes and middleware
	monitoringManager.SetupRoutes(router)
	monitoringManager.SetupMiddleware(router)

	// Use middleware
	router.Use(middleware.Logger())
	router.Use(middleware.ErrorHandler())
	router.Use(authManager.CORSMiddleware())
	router.Use(gin.Recovery())

	// Initialize logging service
	loggingService := services.NewLoggingService(db.GetDB())

	// Initialize provider registry
	providerRegistry := providers.NewProviderRegistry(zapLogger)

	// Initialize plugin manager
	pluginManager := plugins.NewPluginManager(providerRegistry, "./plugins", zapLogger)
	if err := pluginManager.Start(); err != nil {
		log.Printf("Failed to start plugin manager: %v", err)
	}

	// Initialize WebSocket handler
	wsHandler := handlers.NewWebSocketHandler(loggingService, zapLogger)

	// Initialize services
	secretsService := services.NewMockSecretsService()
	executionService := services.NewExecutionService(db.GetDB(), loggingService, secretsService)

	// Initialize handlers
	workflowHandler := api.NewWorkflowHandler(db, workflowEngine)
	templateHandler := api.NewTemplateHandler(db)
	executionHandler := api.NewExecutionHandler(db, workflowEngine)
	instanceHandler := api.NewInstanceHandler(workflowEngine, zapLogger)
	auditHandler := api.NewAuditHandler(db, workflowEngine.GetAuditLogger())
	instanceLabelingHandler := api.NewInstanceLabelingHandler(workflowEngine.GetInstanceManager(), workflowEngine.GetInstanceRegistry())
	// Initialize integration services
	environmentClient := services.NewEnvironmentClient("http://localhost:8083", zapLogger)     // Environment service URL
	notificationService := services.NewNotificationService("http://localhost:8084", zapLogger) // Notification service URL

	// Initialize promotion service with integrations (without custom monitoring and git services)
	promotionService := services.NewEnvironmentPromotionService(
		db.GetDB(),
		zapLogger,
		executionService,
		loggingService,
		environmentClient,
		notificationService,
	)

	// Initialize comprehensive deployment service
	deploymentService := services.NewDeploymentService(
		db.GetDB(),
		zapLogger,
		executionService,
		loggingService,
		environmentClient,
		notificationService,
	)

	pluginHandler := handlers.NewPluginHandler(pluginManager, zapLogger)

	// Initialize deployment plugin service and handler
	deploymentPluginService := services.NewDeploymentPluginService(db.GetDB(), pluginManager, zapLogger)
	deploymentPluginHandler := handlers.NewDeploymentPluginHandler(deploymentPluginService, zapLogger)

	// Initialize comprehensive deployment handler
	deploymentHandler := handlers.NewDeploymentHandler(
		deploymentService,
		promotionService,
		deploymentPluginService,
		executionService,
		zapLogger,
	)

	promotionHandler := handlers.NewPromotionHandler(promotionService, zapLogger)
	marketplaceHandler := handlers.NewMarketplaceHandler(zapLogger)

	// Initialize workflow provider handler
	workflowProviderHandler := handlers.NewWorkflowProviderHandler(providerRegistry, zapLogger)

	// Initialize permission middleware
	permissionMiddleware := authManager.PermissionMiddleware()

	// API routes
	v1 := router.Group("/api/v1")
	{
		// Add authentication middleware to protected routes
		if !cfg.Auth.DisableAuth {
			v1.Use(authManager.AuthMiddleware())
		} else {
			log.Println("Warning: Authentication is disabled")
		}

		// Workflow definitions
		workflows := v1.Group("/workflows")
		{
			workflows.GET("", workflowHandler.GetWorkflows)
			workflows.GET("/:id", workflowHandler.GetWorkflow)

			if !cfg.Auth.DisableAuth {
				workflows.POST("",
					permissionMiddleware.RequirePermission("workflow:create", auth.ProjectIDFromJSON("projectId")),
					workflowHandler.CreateWorkflow)
				workflows.PUT("/:id",
					permissionMiddleware.RequirePermission("workflow:update", auth.ProjectIDFromJSON("projectId")),
					workflowHandler.UpdateWorkflow)
				workflows.DELETE("/:id",
					permissionMiddleware.RequirePermission("workflow:delete", auth.ProjectIDFromJSON("projectId")),
					workflowHandler.DeleteWorkflow)
				workflows.POST("/:id/execute",
					permissionMiddleware.RequirePermission("workflow:execute", auth.ProjectIDFromJSON("projectId")),
					workflowHandler.ExecuteWorkflow)
			} else {
				workflows.POST("", workflowHandler.CreateWorkflow)
				workflows.PUT("/:id", workflowHandler.UpdateWorkflow)
				workflows.DELETE("/:id", workflowHandler.DeleteWorkflow)
				workflows.POST("/:id/execute", workflowHandler.ExecuteWorkflow)
			}
		}

		// Workflow templates
		templates := v1.Group("/templates")
		{
			if !cfg.Auth.DisableAuth {
				templates.GET("",
					permissionMiddleware.RequirePermission("workflow:view", nil),
					templateHandler.GetTemplates)
				templates.GET("/featured",
					permissionMiddleware.RequirePermission("workflow:view", nil),
					templateHandler.GetFeaturedTemplates)
				templates.GET("/categories",
					permissionMiddleware.RequirePermission("workflow:view", nil),
					templateHandler.GetTemplateCategories)
				templates.GET("/favorites",
					permissionMiddleware.RequirePermission("workflow:view", nil),
					templateHandler.GetUserFavorites)
				templates.GET("/:id",
					permissionMiddleware.RequirePermission("workflow:view", nil),
					templateHandler.GetTemplate)
				templates.GET("/:id/download",
					permissionMiddleware.RequirePermission("workflow:view", nil),
					templateHandler.DownloadTemplate)
				templates.GET("/:id/reviews",
					permissionMiddleware.RequirePermission("workflow:view", nil),
					templateHandler.GetTemplateReviews)
				templates.GET("/:id/versions",
					permissionMiddleware.RequirePermission("workflow:view", nil),
					templateHandler.GetTemplateVersions)
				templates.GET("/:id/export",
					permissionMiddleware.RequirePermission("template:export", nil),
					templateHandler.ExportTemplate)
				templates.POST("/import",
					permissionMiddleware.RequirePermission("template:import", nil),
					templateHandler.ImportTemplate)
				templates.POST("/from-workflow",
					permissionMiddleware.RequirePermission("template:publish", nil),
					templateHandler.CreateTemplateFromWorkflow)
				templates.POST("/:id/create-workflow",
					permissionMiddleware.RequirePermission("workflow:create", auth.ProjectIDFromJSON("projectId")),
					templateHandler.CreateWorkflowFromTemplate)
				templates.POST("/:id/rate",
					permissionMiddleware.RequirePermission("workflow:view", nil),
					templateHandler.RateTemplate)
				templates.POST("/:id/review",
					permissionMiddleware.RequirePermission("workflow:view", nil),
					templateHandler.CreateTemplateReview)
				templates.POST("/:id/favorite",
					permissionMiddleware.RequirePermission("workflow:view", nil),
					templateHandler.AddToFavorites)
				templates.POST("/:id/publish",
					permissionMiddleware.RequirePermission("template:publish", nil),
					templateHandler.PublishTemplate)
				templates.POST("/:id/versions",
					permissionMiddleware.RequirePermission("template:publish", nil),
					templateHandler.CreateTemplateVersion)
				templates.POST("/reviews/:reviewId/helpful",
					permissionMiddleware.RequirePermission("workflow:view", nil),
					templateHandler.MarkReviewHelpful)
				templates.DELETE("/:id/favorite",
					permissionMiddleware.RequirePermission("workflow:view", nil),
					templateHandler.RemoveFromFavorites)
			} else {
				templates.GET("", templateHandler.GetTemplates)
				templates.GET("/featured", templateHandler.GetFeaturedTemplates)
				templates.GET("/categories", templateHandler.GetTemplateCategories)
				templates.GET("/favorites", templateHandler.GetUserFavorites)
				templates.GET("/:id", templateHandler.GetTemplate)
				templates.GET("/:id/download", templateHandler.DownloadTemplate)
				templates.GET("/:id/reviews", templateHandler.GetTemplateReviews)
				templates.GET("/:id/versions", templateHandler.GetTemplateVersions)
				templates.GET("/:id/export", templateHandler.ExportTemplate)
				templates.POST("/import", templateHandler.ImportTemplate)
				templates.POST("/from-workflow", templateHandler.CreateTemplateFromWorkflow)
				templates.POST("/:id/create-workflow", templateHandler.CreateWorkflowFromTemplate)
				templates.POST("/:id/rate", templateHandler.RateTemplate)
				templates.POST("/:id/review", templateHandler.CreateTemplateReview)
				templates.POST("/:id/favorite", templateHandler.AddToFavorites)
				templates.POST("/:id/publish", templateHandler.PublishTemplate)
				templates.POST("/:id/versions", templateHandler.CreateTemplateVersion)
				templates.POST("/reviews/:reviewId/helpful", templateHandler.MarkReviewHelpful)
				templates.DELETE("/:id/favorite", templateHandler.RemoveFromFavorites)
			}

			if !cfg.Auth.DisableAuth {
				templates.POST("",
					permissionMiddleware.RequirePermission("workflow:create", auth.ProjectIDFromJSON("projectId")),
					templateHandler.CreateTemplate)
				templates.PUT("/:id",
					permissionMiddleware.RequirePermission("workflow:update", auth.ProjectIDFromJSON("projectId")),
					templateHandler.UpdateTemplate)
				templates.DELETE("/:id",
					permissionMiddleware.RequirePermission("workflow:delete", auth.ProjectIDFromJSON("projectId")),
					templateHandler.DeleteTemplate)
			} else {
				templates.POST("", templateHandler.CreateTemplate)
				templates.PUT("/:id", templateHandler.UpdateTemplate)
				templates.DELETE("/:id", templateHandler.DeleteTemplate)
			}
		}

		// Workflow executions
		executions := v1.Group("/executions")
		{
			if !cfg.Auth.DisableAuth {
				executions.GET("",
					permissionMiddleware.RequirePermission("workflow:view", nil),
					executionHandler.GetExecutions)
				executions.GET("/:id",
					permissionMiddleware.RequirePermission("workflow:view", nil),
					executionHandler.GetExecution)
				executions.GET("/:id/logs",
					permissionMiddleware.RequirePermission("workflow:view", nil),
					executionHandler.GetExecutionLogs)
				executions.POST("",
					permissionMiddleware.RequirePermission("workflow:execute", auth.ProjectIDFromJSON("projectId")),
					executionHandler.StartExecution)
				executions.POST("/:id/stop",
					permissionMiddleware.RequirePermission("workflow:execute", auth.ProjectIDFromJSON("projectId")),
					executionHandler.StopExecution)
				executions.POST("/:id/retry",
					permissionMiddleware.RequirePermission("workflow:execute", auth.ProjectIDFromJSON("projectId")),
					executionHandler.RetryExecution)
			} else {
				executions.GET("", executionHandler.GetExecutions)
				executions.GET("/:id", executionHandler.GetExecution)
				executions.GET("/:id/logs", executionHandler.GetExecutionLogs)
				executions.POST("", executionHandler.StartExecution)
				executions.POST("/:id/stop", executionHandler.StopExecution)
				executions.POST("/:id/retry", executionHandler.RetryExecution)
			}
		}

		// Monitoring endpoints
		monitoring := v1.Group("/monitoring")
		{
			monitoringHandler := api.NewMonitoringHandler(db, workflowEngine, zapLogger)

			if !cfg.Auth.DisableAuth {
				monitoring.GET("/dashboard",
					permissionMiddleware.RequirePermission("workflow:monitor", nil),
					monitoringHandler.GetExecutionDashboard)
				monitoring.GET("/executions/metrics",
					permissionMiddleware.RequirePermission("workflow:monitor", nil),
					monitoringHandler.GetAllExecutionMetrics)
				monitoring.GET("/executions/:id/metrics",
					permissionMiddleware.RequirePermission("workflow:monitor", nil),
					monitoringHandler.GetExecutionMetrics)
				monitoring.GET("/executions/:id/events",
					permissionMiddleware.RequirePermission("workflow:monitor", nil),
					monitoringHandler.GetExecutionEvents)
				monitoring.GET("/executions/:id/stream",
					permissionMiddleware.RequirePermission("workflow:monitor", nil),
					monitoringHandler.StreamExecutionEvents)
				monitoring.GET("/performance",
					permissionMiddleware.RequirePermission("template:analytics", nil),
					monitoringHandler.GetExecutionPerformance)
				monitoring.GET("/health",
					permissionMiddleware.RequirePermission("workflow:monitor", nil),
					monitoringHandler.GetSystemHealth)
			} else {
				monitoring.GET("/dashboard", monitoringHandler.GetExecutionDashboard)
				monitoring.GET("/executions/metrics", monitoringHandler.GetAllExecutionMetrics)
				monitoring.GET("/executions/:id/metrics", monitoringHandler.GetExecutionMetrics)
				monitoring.GET("/executions/:id/events", monitoringHandler.GetExecutionEvents)
				monitoring.GET("/executions/:id/stream", monitoringHandler.StreamExecutionEvents)
				monitoring.GET("/performance", monitoringHandler.GetExecutionPerformance)
				monitoring.GET("/health", monitoringHandler.GetSystemHealth)
			}
		}

		// Instance management endpoints
		instances := v1.Group("/instances")
		{
			instances.GET("", instanceHandler.ListInstances)
			instances.GET("/current", instanceHandler.GetCurrentInstance)
			instances.GET("/dashboard", instanceHandler.GetInstanceDashboard)
			instances.GET("/health", instanceHandler.GetInstanceHealth)
			instances.GET("/metrics", instanceHandler.GetInstanceMetrics)
			instances.GET("/:id", instanceHandler.GetInstance)
			instances.PUT("/current/labels", instanceHandler.UpdateInstanceLabels)
			instances.PUT("/current/capabilities", instanceHandler.UpdateInstanceCapabilities)
			instances.PUT("/current/status", instanceHandler.UpdateInstanceStatus)
			instances.POST("/execute", instanceHandler.ExecuteWorkflowDistributed)
			instances.POST("/:id/execute", instanceHandler.ExecuteWorkflowOnInstance)
			instances.GET("/executions/:executionId", instanceHandler.GetDistributedExecutionStatus)
			instances.POST("/executions/:executionId/stop", instanceHandler.StopDistributedExecution)

			// Instance labeling endpoints (admin only)
			instances.GET("/labels", instanceLabelingHandler.GetInstanceLabels)
			instances.GET("/by-labels", instanceLabelingHandler.GetInstancesByLabels)
			instances.PUT("/:id/labels", instanceLabelingHandler.UpdateInstanceLabels)
			instances.DELETE("/:id/labels/:key", instanceLabelingHandler.RemoveInstanceLabel)
			instances.POST("/bulk/labels", instanceLabelingHandler.BulkUpdateInstanceLabels)
		}

		// Audit and notification endpoints (admin only)
		audit := v1.Group("/audit")
		{
			audit.GET("/logs", auditHandler.GetAuditLogs)
		}

		notifications := v1.Group("/notifications")
		{
			notifications.GET("/rules", auditHandler.GetNotificationRules)
			notifications.POST("/rules", auditHandler.CreateNotificationRule)
			notifications.PUT("/rules/:id", auditHandler.UpdateNotificationRule)
			notifications.DELETE("/rules/:id", auditHandler.DeleteNotificationRule)
		}

		// WebSocket endpoints for real-time logs
		ws := v1.Group("/ws")
		{
			if !cfg.Auth.DisableAuth {
				ws.Use(authManager.AuthMiddleware())
			}
			ws.GET("/logs", wsHandler.HandleWebSocket)
		}

		// Plugin management endpoints (admin only)
		pluginHandler.RegisterRoutes(v1)

		// Deployment plugin endpoints
		deploymentPluginHandler.RegisterRoutes(v1)

		// Workflow provider endpoints
		providers := v1.Group("/providers")
		{
			providers.GET("", workflowProviderHandler.GetProviders)
			providers.GET("/:type", workflowProviderHandler.GetProvider)
		}

		// Comprehensive deployment endpoints
		if !cfg.Auth.DisableAuth {
			deploymentGroup := v1.Group("/")
			deploymentGroup.Use(permissionMiddleware.RequirePermission("deployment:access", nil))
			deploymentHandler.RegisterRoutes(deploymentGroup)
		} else {
			deploymentHandler.RegisterRoutes(v1)
		}

		// Marketplace endpoints
		marketplaceHandler.RegisterRoutes(v1)

		// Environment promotion endpoints
		if !cfg.Auth.DisableAuth {
			// Version matrix (requires project access)
			v1.GET("/projects/:projectId/version-matrix",
				permissionMiddleware.RequirePermission("workflow:view", auth.ProjectIDFromParam),
				promotionHandler.GetVersionMatrix)

			// Promotion management (requires deploy permission)
			v1.POST("/promotions",
				permissionMiddleware.RequirePermission("environment:deploy", auth.ProjectIDFromJSON("projectId")),
				promotionHandler.StartPromotion)
			v1.GET("/promotions/:promotionId",
				permissionMiddleware.RequirePermission("workflow:view", nil),
				promotionHandler.GetPromotionStatus)
			v1.GET("/promotions/history/:projectId",
				permissionMiddleware.RequirePermission("workflow:view", auth.ProjectIDFromParam),
				promotionHandler.GetPromotionHistory)

			// Deployment history (requires project access)
			v1.GET("/environments/:environmentId/deployment-history",
				permissionMiddleware.RequirePermission("workflow:view", nil),
				promotionHandler.GetDeploymentHistory)
		} else {
			// No auth - allow all promotion operations
			v1.GET("/projects/:projectId/version-matrix", promotionHandler.GetVersionMatrix)
			v1.POST("/promotions", promotionHandler.StartPromotion)
			v1.GET("/promotions/:promotionId", promotionHandler.GetPromotionStatus)
			v1.GET("/promotions/history/:projectId", promotionHandler.GetPromotionHistory)
			v1.GET("/environments/:environmentId/deployment-history", promotionHandler.GetDeploymentHistory)
		}
	}

	// Start server
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
		Handler: router,
	}

	// Graceful shutdown
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	log.Printf("Workflow service started on port %d", cfg.Server.Port)

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// Shutdown with timeout
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Stop plugin manager
	if err := pluginManager.Stop(); err != nil {
		log.Printf("Failed to stop plugin manager: %v", err)
	}

	// Stop instance manager
	if err := workflowEngine.StopInstanceManager(shutdownCtx); err != nil {
		log.Printf("Failed to stop instance manager: %v", err)
	}

	if err := server.Shutdown(shutdownCtx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("Server exited")
}
