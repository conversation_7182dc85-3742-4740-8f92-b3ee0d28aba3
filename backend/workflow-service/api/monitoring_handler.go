package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/engine"
	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"github.com/claudio/deploy-orchestrator/workflow-service/storage"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// MonitoringHandler handles monitoring-related HTTP requests
type MonitoringHandler struct {
	db     *storage.Database
	engine *engine.WorkflowEngine
	logger *zap.Logger
}

// NewMonitoringHandler creates a new monitoring handler
func NewMonitoringHandler(db *storage.Database, engine *engine.WorkflowEngine, logger *zap.Logger) *MonitoringHandler {
	return &MonitoringHandler{
		db:     db,
		engine: engine,
		logger: logger,
	}
}

// GetExecutionMetrics returns metrics for a specific execution
func (h *MonitoringHandler) GetExecutionMetrics(c *gin.Context) {
	executionID := c.Param("id")
	if executionID == "" {
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{"error": "Execution ID is required"})
		return
	}

	// Try to get real-time metrics first
	if metrics, exists := h.engine.GetExecutionMetrics(executionID); exists {
		c.JSON(http.StatusOK, metrics)
		return
	}

	// Fall back to database metrics
	metrics, err := h.db.GetExecutionMetrics(c.Request.Context(), executionID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Metrics not found"})
		return
	}

	c.JSON(http.StatusOK, metrics)
}

// GetAllExecutionMetrics returns metrics for all active executions
func (h *MonitoringHandler) GetAllExecutionMetrics(c *gin.Context) {
	metrics := h.engine.GetAllExecutionMetrics()
	c.JSON(http.StatusOK, metrics)
}

// GetExecutionEvents returns events for a specific execution
func (h *MonitoringHandler) GetExecutionEvents(c *gin.Context) {
	executionID := c.Param("id")
	if executionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Execution ID is required"})
		return
	}

	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")
	eventType := c.Query("type")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	var events []models.ExecutionEvent
	if eventType != "" {
		events, err = h.db.GetExecutionEventsByType(c.Request.Context(), executionID, eventType)
	} else {
		events, err = h.db.GetExecutionEvents(c.Request.Context(), executionID, limit, offset)
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve events"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"events": events,
		"total":  len(events),
	})
}

// StreamExecutionEvents streams real-time events for an execution using Server-Sent Events
func (h *MonitoringHandler) StreamExecutionEvents(c *gin.Context) {
	executionID := c.Param("id")
	if executionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Execution ID is required"})
		return
	}

	// Set headers for Server-Sent Events
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")

	// Subscribe to execution events
	eventChan := h.engine.SubscribeToExecution(executionID)
	defer h.engine.UnsubscribeFromExecution(executionID, eventChan)

	// Stream events
	for {
		select {
		case event := <-eventChan:
			if event == nil {
				return // Channel closed
			}
			c.SSEvent("event", event)
			c.Writer.Flush()
		case <-c.Request.Context().Done():
			return // Client disconnected
		}
	}
}

// GetExecutionDashboard returns dashboard data for monitoring
func (h *MonitoringHandler) GetExecutionDashboard(c *gin.Context) {
	// Check if user is admin
	isAdmin := false
	roles, exists := c.Get("roles")
	if exists {
		rolesList, ok := roles.([]string)
		if ok {
			for _, role := range rolesList {
				if role == "admin" {
					isAdmin = true
					break
				}
			}
		}
	}

	// Get project ID for filtering (for non-admin users)
	projectID := c.Query("projectId")

	// For non-admin users, require project ID
	if !isAdmin && projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project ID is required for non-admin users"})
		return
	}

	// Get active executions metrics
	activeMetrics := h.engine.GetAllExecutionMetrics()

	// Calculate dashboard statistics
	totalActive := len(activeMetrics)
	totalCompleted := 0
	totalFailed := 0
	averageExecutionTime := 0.0

	for _, metrics := range activeMetrics {
		totalCompleted += metrics.CompletedSteps
		totalFailed += metrics.FailedSteps
		if metrics.TotalExecutionTime > 0 {
			averageExecutionTime += metrics.TotalExecutionTime
		}
	}

	if totalActive > 0 {
		averageExecutionTime = averageExecutionTime / float64(totalActive)
	}

	// Get recent executions from database with project filtering
	recentExecutions, err := h.db.GetWorkflowExecutions(c.Request.Context(), "", projectID, "", 10, 0)
	if err != nil {
		h.logger.Error("Failed to get recent executions", zap.Error(err))
		recentExecutions = []models.WorkflowExecution{}
	}

	dashboard := gin.H{
		"statistics": gin.H{
			"totalActiveExecutions": totalActive,
			"totalCompletedSteps":   totalCompleted,
			"totalFailedSteps":      totalFailed,
			"averageExecutionTime":  averageExecutionTime,
		},
		"activeExecutions": activeMetrics,
		"recentExecutions": recentExecutions,
	}

	c.JSON(http.StatusOK, dashboard)
}

// GetExecutionPerformance returns performance analytics for executions
func (h *MonitoringHandler) GetExecutionPerformance(c *gin.Context) {
	// Check if user is admin
	isAdmin := false
	roles, exists := c.Get("roles")
	if exists {
		rolesList, ok := roles.([]string)
		if ok {
			for _, role := range rolesList {
				if role == "admin" {
					isAdmin = true
					break
				}
			}
		}
	}

	// Parse query parameters
	projectID := c.Query("projectId")
	workflowID := c.Query("workflowId")
	limitStr := c.DefaultQuery("limit", "100")

	// For non-admin users, require project ID
	if !isAdmin && projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project ID is required for non-admin users"})
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	// Get executions for analysis
	executions, err := h.db.GetWorkflowExecutions(c.Request.Context(), workflowID, projectID, "", limit, 0)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve executions"})
		return
	}

	// Calculate performance metrics
	var totalDuration int
	var successCount int
	var failureCount int
	executionTrends := make([]gin.H, 0)

	for _, execution := range executions {
		totalDuration += execution.Duration
		if execution.Status == "completed" {
			successCount++
		} else if execution.Status == "failed" {
			failureCount++
		}

		executionTrends = append(executionTrends, gin.H{
			"id":        execution.ID,
			"status":    execution.Status,
			"duration":  execution.Duration,
			"startedAt": execution.StartedAt,
		})
	}

	averageDuration := 0.0
	if len(executions) > 0 {
		averageDuration = float64(totalDuration) / float64(len(executions))
	}

	successRate := 0.0
	if len(executions) > 0 {
		successRate = float64(successCount) / float64(len(executions)) * 100
	}

	performance := gin.H{
		"totalExecutions": len(executions),
		"successCount":    successCount,
		"failureCount":    failureCount,
		"successRate":     successRate,
		"averageDuration": averageDuration,
		"executionTrends": executionTrends,
	}

	c.JSON(http.StatusOK, performance)
}

// GetSystemHealth returns overall system health metrics
func (h *MonitoringHandler) GetSystemHealth(c *gin.Context) {
	activeExecutions := h.engine.GetAllExecutionMetrics()

	// Calculate system load
	systemLoad := float64(len(activeExecutions)) / float64(h.engine.GetMaxConcurrentExecutions()) * 100

	health := gin.H{
		"status":           "healthy",
		"activeExecutions": len(activeExecutions),
		"maxConcurrent":    h.engine.GetMaxConcurrentExecutions(),
		"systemLoad":       systemLoad,
		"timestamp":        time.Now(),
	}

	// Determine health status based on system load
	if systemLoad > 90 {
		health["status"] = "critical"
	} else if systemLoad > 70 {
		health["status"] = "warning"
	}

	c.JSON(http.StatusOK, health)
}
