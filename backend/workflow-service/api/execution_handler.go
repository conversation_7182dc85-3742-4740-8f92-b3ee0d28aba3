package api

import (
	"net/http"
	"strconv"

	"github.com/claudio/deploy-orchestrator/workflow-service/engine"
	"github.com/claudio/deploy-orchestrator/workflow-service/storage"
	"github.com/gin-gonic/gin"
)

// ExecutionHandler handles workflow execution operations
type ExecutionHandler struct {
	db     *storage.Database
	engine *engine.WorkflowEngine
}

// NewExecutionHandler creates a new execution handler
func NewExecutionHandler(db *storage.Database, engine *engine.WorkflowEngine) *ExecutionHandler {
	return &ExecutionHandler{
		db:     db,
		engine: engine,
	}
}

// GetExecutions retrieves workflow executions
func (h *ExecutionHandler) GetExecutions(c *gin.Context) {
	workflowID := c.Query("workflowId")
	projectID := c.Query("projectId")
	status := c.Query("status")
	limitStr := c.Default<PERSON>uery("limit", "50")
	offsetStr := c.<PERSON>ult<PERSON>("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	// Check user permissions
	_, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Check if user is admin
	isAdmin := false
	roles, exists := c.Get("roles")
	if exists {
		rolesList, ok := roles.([]string)
		if ok {
			for _, role := range rolesList {
				if role == "admin" {
					isAdmin = true
					break
				}
			}
		}
	}

	// If user is not admin and no project ID is specified, return empty list
	if !isAdmin && projectID == "" {
		c.JSON(http.StatusOK, []interface{}{})
		return
	}

	executions, err := h.db.GetWorkflowExecutions(c.Request.Context(), workflowID, projectID, status, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve executions"})
		return
	}

	c.JSON(http.StatusOK, executions)
}

// GetExecution retrieves a specific workflow execution
func (h *ExecutionHandler) GetExecution(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Execution ID is required"})
		return
	}

	execution, err := h.db.GetWorkflowExecution(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Execution not found"})
		return
	}

	c.JSON(http.StatusOK, execution)
}

// GetExecutionLogs retrieves logs for a workflow execution
func (h *ExecutionHandler) GetExecutionLogs(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Execution ID is required"})
		return
	}

	// Get step executions for this workflow execution
	stepExecutions, err := h.db.GetStepExecutions(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve execution logs"})
		return
	}

	// Compile logs from all step executions
	type ExecutionLog struct {
		ExecutionID string      `json:"executionId"`
		StepID      string      `json:"stepId"`
		StepName    string      `json:"stepName"`
		Status      string      `json:"status"`
		StartedAt   string      `json:"startedAt"`
		CompletedAt string      `json:"completedAt"`
		Duration    int         `json:"duration"`
		Logs        interface{} `json:"logs"`
		Output      interface{} `json:"output"`
		Error       string      `json:"error,omitempty"`
	}

	logs := make([]ExecutionLog, 0, len(stepExecutions))
	for _, stepExec := range stepExecutions {
		log := ExecutionLog{
			ExecutionID: stepExec.ExecutionID,
			StepID:      stepExec.StepID,
			StepName:    stepExec.StepName,
			Status:      stepExec.Status,
			Duration:    stepExec.Duration,
			Logs:        stepExec.Logs,
			Output:      stepExec.Output,
			Error:       stepExec.ErrorMessage,
		}

		if stepExec.StartedAt != nil {
			log.StartedAt = stepExec.StartedAt.Format("2006-01-02T15:04:05Z")
		}

		if stepExec.CompletedAt != nil {
			log.CompletedAt = stepExec.CompletedAt.Format("2006-01-02T15:04:05Z")
		}

		logs = append(logs, log)
	}

	c.JSON(http.StatusOK, gin.H{
		"executionId": id,
		"logs":        logs,
	})
}

// StartExecution starts a new workflow execution
func (h *ExecutionHandler) StartExecution(c *gin.Context) {
	var request struct {
		WorkflowID  string                 `json:"workflowId"`
		Parameters  map[string]interface{} `json:"parameters"`
		TriggerType string                 `json:"triggerType"`
		TriggerData map[string]interface{} `json:"triggerData"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	if request.WorkflowID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Workflow ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	startedBy := userID.(string)
	if request.TriggerType == "" {
		request.TriggerType = "manual"
	}

	execution, err := h.engine.StartExecution(
		c.Request.Context(),
		request.WorkflowID,
		request.Parameters,
		startedBy,
		request.TriggerType,
		request.TriggerData,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, execution)
}

// StopExecution stops a running workflow execution
func (h *ExecutionHandler) StopExecution(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Execution ID is required"})
		return
	}

	err := h.engine.StopExecution(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Execution stopped successfully"})
}

// RetryExecution retries a failed workflow execution
func (h *ExecutionHandler) RetryExecution(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Execution ID is required"})
		return
	}

	// Get the original execution
	originalExecution, err := h.db.GetWorkflowExecution(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Execution not found"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	startedBy := userID.(string)

	// Start a new execution with the same parameters
	newExecution, err := h.engine.StartExecution(
		c.Request.Context(),
		originalExecution.WorkflowID,
		originalExecution.Parameters,
		startedBy,
		"retry",
		map[string]interface{}{
			"originalExecutionId": originalExecution.ID,
		},
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, newExecution)
}

// GetExecutionStatus returns the current status of an execution
func (h *ExecutionHandler) GetExecutionStatus(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Execution ID is required"})
		return
	}

	// Check if execution is currently running
	status, isRunning := h.engine.GetExecutionStatus(id)
	if isRunning {
		c.JSON(http.StatusOK, gin.H{
			"executionId": id,
			"status":      status,
			"isRunning":   true,
		})
		return
	}

	// Get execution from database
	execution, err := h.db.GetWorkflowExecution(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Execution not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"executionId": id,
		"status":      execution.Status,
		"isRunning":   false,
		"startedAt":   execution.StartedAt,
		"completedAt": execution.CompletedAt,
		"duration":    execution.Duration,
	})
}
