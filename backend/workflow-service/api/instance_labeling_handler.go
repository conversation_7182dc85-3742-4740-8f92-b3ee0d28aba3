package api

import (
	"log"
	"net/http"

	"github.com/claudio/deploy-orchestrator/shared/auth"
	"github.com/claudio/deploy-orchestrator/workflow-service/instance"
	"github.com/gin-gonic/gin"
)

// InstanceLabelingHandler handles workflow instance labeling operations
type InstanceLabelingHandler struct {
	instanceManager *instance.InstanceManager
	registry        instance.InstanceRegistry
}

// NewInstanceLabelingHandler creates a new instance labeling handler
func NewInstanceLabelingHandler(instanceManager *instance.InstanceManager, registry instance.InstanceRegistry) *InstanceLabelingHandler {
	return &InstanceLabelingHandler{
		instanceManager: instanceManager,
		registry:        registry,
	}
}

// GetInstanceLabels retrieves available instance labels
func (h *InstanceLabelingHandler) GetInstanceLabels(c *gin.Context) {
	// Add debugging to see what roles are available
	roles, exists := c.Get("roles")
	log.Printf("DEBUG: GetInstanceLabels - roles exist: %v, roles: %v", exists, roles)

	userID, userExists := c.Get("userID")
	log.Printf("DEBUG: GetInstanceLabels - userID exist: %v, userID: %v", userExists, userID)

	// Check if user is admin using shared helper
	isAdmin := auth.IsAdminFromContext(c)
	log.Printf("DEBUG: GetInstanceLabels - isAdmin: %v", isAdmin)

	if !isAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	// Get all instances and extract unique labels
	instances, err := h.registry.ListInstances(c.Request.Context(), nil)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve instances"})
		return
	}

	// Collect unique labels
	labelKeys := make(map[string]bool)
	labelValues := make(map[string]map[string]bool)

	for _, inst := range instances {
		for key, value := range inst.Labels {
			labelKeys[key] = true
			if labelValues[key] == nil {
				labelValues[key] = make(map[string]bool)
			}
			labelValues[key][value] = true
		}
	}

	// Convert to response format
	labels := make(map[string][]string)
	for key := range labelKeys {
		values := make([]string, 0)
		for value := range labelValues[key] {
			values = append(values, value)
		}
		labels[key] = values
	}

	c.JSON(http.StatusOK, gin.H{
		"labels": labels,
		"count":  len(instances),
	})
}

// UpdateInstanceLabels updates labels for a specific instance
func (h *InstanceLabelingHandler) UpdateInstanceLabels(c *gin.Context) {
	// Check if user is admin using shared helper
	if !auth.IsAdminFromContext(c) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	instanceID := c.Param("id")
	if instanceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Instance ID is required"})
		return
	}

	var request struct {
		Labels map[string]string `json:"labels"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Get the instance
	inst, err := h.registry.GetInstance(c.Request.Context(), instanceID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Instance not found"})
		return
	}

	// Update labels
	if inst.Labels == nil {
		inst.Labels = make(map[string]string)
	}

	for key, value := range request.Labels {
		inst.Labels[key] = value
	}

	// Update the instance (re-register with updated labels)
	if err := h.registry.Register(c.Request.Context(), inst); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update instance labels"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Instance labels updated successfully",
		"labels":  inst.Labels,
	})
}

// RemoveInstanceLabel removes a specific label from an instance
func (h *InstanceLabelingHandler) RemoveInstanceLabel(c *gin.Context) {
	// Check if user is admin using shared helper
	if !auth.IsAdminFromContext(c) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	instanceID := c.Param("id")
	labelKey := c.Param("key")

	if instanceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Instance ID is required"})
		return
	}

	if labelKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Label key is required"})
		return
	}

	// Get the instance
	inst, err := h.registry.GetInstance(c.Request.Context(), instanceID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Instance not found"})
		return
	}

	// Remove the label
	if inst.Labels != nil {
		delete(inst.Labels, labelKey)
	}

	// Update the instance (re-register with updated labels)
	if err := h.registry.Register(c.Request.Context(), inst); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove instance label"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Instance label removed successfully",
		"labels":  inst.Labels,
	})
}

// GetInstancesByLabels retrieves instances filtered by labels
func (h *InstanceLabelingHandler) GetInstancesByLabels(c *gin.Context) {
	// Check if user is admin using shared helper
	if !auth.IsAdminFromContext(c) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	// Parse label filters from query parameters
	labels := make(map[string]string)
	for key, values := range c.Request.URL.Query() {
		if len(values) > 0 && key != "limit" && key != "offset" {
			labels[key] = values[0]
		}
	}

	instances, err := h.registry.ListInstances(c.Request.Context(), labels)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve instances"})
		return
	}

	if instances == nil {
		instances = []*instance.Instance{}
	}

	c.JSON(http.StatusOK, gin.H{
		"instances": instances,
		"count":     len(instances),
		"filters":   labels,
	})
}

// BulkUpdateInstanceLabels updates labels for multiple instances
func (h *InstanceLabelingHandler) BulkUpdateInstanceLabels(c *gin.Context) {
	// Check if user is admin using shared helper
	if !auth.IsAdminFromContext(c) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	var request struct {
		InstanceIDs []string          `json:"instanceIds"`
		Labels      map[string]string `json:"labels"`
		Operation   string            `json:"operation"` // "add", "remove", "replace"
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	if len(request.InstanceIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "At least one instance ID is required"})
		return
	}

	if request.Operation == "" {
		request.Operation = "add"
	}

	results := make(map[string]interface{})
	successCount := 0
	errorCount := 0

	for _, instanceID := range request.InstanceIDs {
		inst, err := h.registry.GetInstance(c.Request.Context(), instanceID)
		if err != nil {
			results[instanceID] = gin.H{"error": "Instance not found"}
			errorCount++
			continue
		}

		if inst.Labels == nil {
			inst.Labels = make(map[string]string)
		}

		switch request.Operation {
		case "add":
			for key, value := range request.Labels {
				inst.Labels[key] = value
			}
		case "remove":
			for key := range request.Labels {
				delete(inst.Labels, key)
			}
		case "replace":
			inst.Labels = request.Labels
		}

		if err := h.registry.Register(c.Request.Context(), inst); err != nil {
			results[instanceID] = gin.H{"error": "Failed to update instance"}
			errorCount++
		} else {
			results[instanceID] = gin.H{"success": true, "labels": inst.Labels}
			successCount++
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message":      "Bulk update completed",
		"successCount": successCount,
		"errorCount":   errorCount,
		"results":      results,
	})
}
