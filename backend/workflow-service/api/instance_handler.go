package api

import (
	"net/http"

	"github.com/claudio/deploy-orchestrator/workflow-service/engine"
	"github.com/claudio/deploy-orchestrator/workflow-service/instance"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// InstanceHandler handles instance-related HTTP requests
type InstanceHandler struct {
	engine *engine.WorkflowEngine
	logger *zap.Logger
}

// NewInstanceHandler creates a new instance handler
func NewInstanceHandler(engine *engine.WorkflowEngine, logger *zap.Logger) *InstanceHandler {
	return &InstanceHandler{
		engine: engine,
		logger: logger,
	}
}

// GetCurrentInstance returns information about the current instance
func (h *InstanceHandler) GetCurrentInstance(c *gin.Context) {
	instance := h.engine.GetInstance()
	c.JSON(http.StatusOK, instance)
}

// ListInstances lists all available instances
func (h *InstanceHandler) ListInstances(c *gin.Context) {
	// Parse query parameters for label filtering
	labels := make(map[string]string)
	for key, values := range c.Request.URL.Query() {
		if len(values) > 0 && key != "limit" && key != "offset" {
			labels[key] = values[0]
		}
	}

	instances, err := h.engine.ListAvailableInstances(c.Request.Context(), labels)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list instances"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"instances": instances,
		"total":     len(instances),
	})
}

// GetInstance returns information about a specific instance
func (h *InstanceHandler) GetInstance(c *gin.Context) {
	instanceID := c.Param("id")
	if instanceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Instance ID is required"})
		return
	}

	instance, err := h.engine.GetInstanceStatus(c.Request.Context(), instanceID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Instance not found"})
		return
	}

	c.JSON(http.StatusOK, instance)
}

// UpdateInstanceLabels updates the labels of the current instance
func (h *InstanceHandler) UpdateInstanceLabels(c *gin.Context) {
	var request struct {
		Labels map[string]string `json:"labels" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if err := h.engine.UpdateInstanceLabels(c.Request.Context(), request.Labels); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update labels"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Labels updated successfully"})
}

// UpdateInstanceCapabilities updates the capabilities of the current instance
func (h *InstanceHandler) UpdateInstanceCapabilities(c *gin.Context) {
	var request struct {
		Capabilities []string `json:"capabilities" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if err := h.engine.UpdateInstanceCapabilities(c.Request.Context(), request.Capabilities); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update capabilities"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Capabilities updated successfully"})
}

// UpdateInstanceStatus updates the status of the current instance
func (h *InstanceHandler) UpdateInstanceStatus(c *gin.Context) {
	var request struct {
		Status string `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Validate status
	validStatuses := []string{"active", "inactive", "maintenance"}
	isValid := false
	for _, status := range validStatuses {
		if request.Status == status {
			isValid = true
			break
		}
	}

	if !isValid {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid status. Must be one of: active, inactive, maintenance"})
		return
	}

	if err := h.engine.UpdateInstanceStatus(c.Request.Context(), request.Status); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Status updated successfully"})
}

// ExecuteWorkflowDistributed executes a workflow on the best available instance
func (h *InstanceHandler) ExecuteWorkflowDistributed(c *gin.Context) {
	var request instance.ExecutionRequest

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	response, err := h.engine.ExecuteWorkflowDistributed(c.Request.Context(), &request)
	if err != nil {
		h.logger.Error("Failed to execute distributed workflow", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to execute workflow"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ExecuteWorkflowOnInstance executes a workflow on a specific instance
func (h *InstanceHandler) ExecuteWorkflowOnInstance(c *gin.Context) {
	instanceID := c.Param("id")
	if instanceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Instance ID is required"})
		return
	}

	var request instance.ExecutionRequest

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	response, err := h.engine.ExecuteWorkflowOnInstance(c.Request.Context(), instanceID, &request)
	if err != nil {
		h.logger.Error("Failed to execute workflow on instance",
			zap.String("instanceId", instanceID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to execute workflow"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetDistributedExecutionStatus gets the status of a distributed execution
func (h *InstanceHandler) GetDistributedExecutionStatus(c *gin.Context) {
	executionID := c.Param("executionId")
	if executionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Execution ID is required"})
		return
	}

	execution, err := h.engine.GetDistributedExecutionStatus(c.Request.Context(), executionID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Execution not found"})
		return
	}

	c.JSON(http.StatusOK, execution)
}

// StopDistributedExecution stops a distributed execution
func (h *InstanceHandler) StopDistributedExecution(c *gin.Context) {
	executionID := c.Param("executionId")
	if executionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Execution ID is required"})
		return
	}

	if err := h.engine.StopDistributedExecution(c.Request.Context(), executionID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to stop execution"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Execution stopped successfully"})
}

// GetInstanceMetrics gets metrics from all instances
func (h *InstanceHandler) GetInstanceMetrics(c *gin.Context) {
	metrics, err := h.engine.GetInstanceMetrics(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get instance metrics"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"metrics": metrics,
		"total":   len(metrics),
	})
}

// GetInstanceDashboard returns dashboard data for instance management
func (h *InstanceHandler) GetInstanceDashboard(c *gin.Context) {
	// Get all instances
	instances, err := h.engine.ListAvailableInstances(c.Request.Context(), nil)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get instances"})
		return
	}

	// Calculate statistics
	totalInstances := len(instances)
	activeInstances := 0
	totalCPUCores := 0
	totalMemoryMB := int64(0)
	osDistribution := make(map[string]int)
	archDistribution := make(map[string]int)

	for _, instance := range instances {
		if instance.Status == "active" {
			activeInstances++
		}
		totalCPUCores += instance.Resources.CPUCores
		totalMemoryMB += instance.Resources.MemoryMB
		osDistribution[instance.OS]++
		archDistribution[instance.Architecture]++
	}

	dashboard := gin.H{
		"statistics": gin.H{
			"totalInstances":  totalInstances,
			"activeInstances": activeInstances,
			"totalCPUCores":   totalCPUCores,
			"totalMemoryMB":   totalMemoryMB,
		},
		"distribution": gin.H{
			"operatingSystems": osDistribution,
			"architectures":    archDistribution,
		},
		"instances": instances,
	}

	c.JSON(http.StatusOK, dashboard)
}

// GetInstanceHealth returns health information for all instances
func (h *InstanceHandler) GetInstanceHealth(c *gin.Context) {
	instances, err := h.engine.ListAvailableInstances(c.Request.Context(), nil)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get instances"})
		return
	}

	healthData := make([]gin.H, 0, len(instances))
	for _, instance := range instances {
		health := gin.H{
			"instanceId":  instance.ID,
			"name":        instance.Name,
			"status":      instance.Status,
			"lastSeen":    instance.LastSeen,
			"cpuUsage":    instance.Resources.CPUUsage,
			"memoryUsage": instance.Resources.MemoryUsage,
			"loadAverage": instance.Resources.LoadAverage,
		}
		healthData = append(healthData, health)
	}

	c.JSON(http.StatusOK, gin.H{
		"instances": healthData,
		"timestamp": gin.H{},
	})
}
