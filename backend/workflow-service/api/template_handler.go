package api

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"github.com/claudio/deploy-orchestrator/workflow-service/storage"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// TemplateHandler handles workflow template operations
type TemplateHandler struct {
	db *storage.Database
}

// NewTemplateHandler creates a new template handler
func NewTemplateHandler(db *storage.Database) *TemplateHandler {
	return &TemplateHandler{
		db: db,
	}
}

// GetTemplates retrieves workflow templates with advanced filtering
func (h *TemplateHandler) GetTemplates(c *gin.Context) {
	category := c.Query("category")
	isPublicStr := c.Query("isPublic")
	isFeaturedStr := c.Query("isFeatured")
	search := c.Query("search")
	sortBy := c.Default<PERSON>uery("sortBy", "created_at")
	sortOrder := c.Default<PERSON>y("sortOrder", "desc")
	tags := c.QueryArray("tags")
	limitStr := c.Default<PERSON>uery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	var isPublic *bool
	if isPublicStr != "" {
		if isPublicStr == "true" {
			val := true
			isPublic = &val
		} else if isPublicStr == "false" {
			val := false
			isPublic = &val
		}
	}

	var isFeatured *bool
	if isFeaturedStr != "" {
		if isFeaturedStr == "true" {
			val := true
			isFeatured = &val
		} else if isFeaturedStr == "false" {
			val := false
			isFeatured = &val
		}
	}

	// Create filter options
	filters := map[string]interface{}{
		"category":   category,
		"isPublic":   isPublic,
		"isFeatured": isFeatured,
		"search":     search,
		"tags":       tags,
		"sortBy":     sortBy,
		"sortOrder":  sortOrder,
	}

	// Check if user is admin
	isAdmin := false
	roles, exists := c.Get("roles")
	if exists {
		rolesList, ok := roles.([]string)
		if ok {
			for _, role := range rolesList {
				if role == "admin" {
					isAdmin = true
					break
				}
			}
		}
	}

	// For non-admin users, only show public templates or ones they created
	if !isAdmin {
		userID, exists := c.Get("userID")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Force isPublic to true for non-admin users, unless they're viewing their own templates
		if isPublic == nil || !*isPublic {
			val := true
			filters["isPublic"] = &val
		}

		// Add user filter to also include templates created by the user
		filters["createdBy"] = userID.(string)
	}

	templates, err := h.db.GetWorkflowTemplatesWithFilters(c.Request.Context(), filters, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve templates"})
		return
	}
	if templates == nil {
		templates = []models.WorkflowTemplate{}
	}

	c.JSON(http.StatusOK, templates)
}

// GetTemplate retrieves a specific workflow template
func (h *TemplateHandler) GetTemplate(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	template, err := h.db.GetWorkflowTemplate(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}

	c.JSON(http.StatusOK, template)
}

// CreateTemplate creates a new workflow template
func (h *TemplateHandler) CreateTemplate(c *gin.Context) {
	var template models.WorkflowTemplate
	if err := c.ShouldBindJSON(&template); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template data"})
		return
	}

	// Generate UUID if not provided
	if template.ID == "" {
		template.ID = uuid.New().String()
	}

	// Get user ID from context for createdBy
	if userID, exists := c.Get("userID"); exists {
		template.CreatedBy = userID.(string)
	}

	if err := h.db.CreateWorkflowTemplate(c.Request.Context(), &template); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create template"})
		return
	}

	c.JSON(http.StatusCreated, template)
}

// UpdateTemplate updates an existing workflow template
func (h *TemplateHandler) UpdateTemplate(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	var template models.WorkflowTemplate
	if err := c.ShouldBindJSON(&template); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template data"})
		return
	}

	template.ID = id

	if err := h.db.UpdateWorkflowTemplate(c.Request.Context(), &template); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update template"})
		return
	}

	c.JSON(http.StatusOK, template)
}

// DeleteTemplate deletes a workflow template
func (h *TemplateHandler) DeleteTemplate(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	if err := h.db.DeleteWorkflowTemplate(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete template"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Template deleted successfully"})
}

// GetFeaturedTemplates retrieves featured workflow templates
func (h *TemplateHandler) GetFeaturedTemplates(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "10")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	filters := map[string]interface{}{
		"isFeatured": func() *bool { val := true; return &val }(),
		"isPublic":   func() *bool { val := true; return &val }(),
		"sortBy":     "rating",
		"sortOrder":  "desc",
	}

	templates, err := h.db.GetWorkflowTemplatesWithFilters(c.Request.Context(), filters, limit, 0)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve featured templates"})
		return
	}

	c.JSON(http.StatusOK, templates)
}

// GetTemplateCategories retrieves all template categories
func (h *TemplateHandler) GetTemplateCategories(c *gin.Context) {
	// For now, return predefined categories
	categories := []map[string]interface{}{
		{
			"id":          "deployment",
			"name":        "Deployment",
			"description": "Application deployment workflows",
			"icon":        "rocket",
			"color":       "#3B82F6",
		},
		{
			"id":          "ci-cd",
			"name":        "CI/CD",
			"description": "Continuous integration and deployment",
			"icon":        "refresh",
			"color":       "#10B981",
		},
		{
			"id":          "monitoring",
			"name":        "Monitoring",
			"description": "System monitoring and alerting",
			"icon":        "chart",
			"color":       "#F59E0B",
		},
		{
			"id":          "backup",
			"name":        "Backup",
			"description": "Data backup and recovery",
			"icon":        "database",
			"color":       "#8B5CF6",
		},
		{
			"id":          "security",
			"name":        "Security",
			"description": "Security scanning and compliance",
			"icon":        "shield",
			"color":       "#EF4444",
		},
		{
			"id":          "testing",
			"name":        "Testing",
			"description": "Automated testing workflows",
			"icon":        "check",
			"color":       "#06B6D4",
		},
	}

	c.JSON(http.StatusOK, categories)
}

// DownloadTemplate handles template download and tracks statistics
func (h *TemplateHandler) DownloadTemplate(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	template, err := h.db.GetWorkflowTemplate(c.Request.Context(), templateID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}

	// Increment download count
	template.DownloadCount++
	if err := h.db.UpdateWorkflowTemplate(c.Request.Context(), template); err != nil {
		// Log error but don't fail the download
		fmt.Printf("Failed to update download count: %v\n", err)
	}

	// Return the template
	c.JSON(http.StatusOK, template)
}

// CreateWorkflowFromTemplate creates a new workflow from a template
func (h *TemplateHandler) CreateWorkflowFromTemplate(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	var req struct {
		Name        string                 `json:"name" binding:"required"`
		Description string                 `json:"description"`
		ProjectID   string                 `json:"projectId" binding:"required"`
		Parameters  map[string]interface{} `json:"parameters"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	template, err := h.db.GetWorkflowTemplate(c.Request.Context(), templateID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}

	// Create workflow from template
	workflow := &models.WorkflowDefinition{
		ID:          "", // Will be generated
		Name:        req.Name,
		Description: req.Description,
		ProjectID:   req.ProjectID,
		Version:     "1.0.0",
		IsActive:    true,
		Steps:       template.Steps,
		Parameters:  template.Parameters,
		Variables:   template.Variables,
		Tags:        template.Tags,
	}

	// Apply parameter overrides
	if req.Parameters != nil {
		for key, value := range req.Parameters {
			workflow.Variables[key] = value
		}
	}

	if err := h.db.CreateWorkflowDefinition(c.Request.Context(), workflow); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create workflow"})
		return
	}

	// Increment usage count
	template.UsageCount++
	if err := h.db.UpdateWorkflowTemplate(c.Request.Context(), template); err != nil {
		// Log error but don't fail the creation
		fmt.Printf("Failed to update usage count: %v\n", err)
	}

	c.JSON(http.StatusCreated, workflow)
}

// CreateTemplateFromWorkflow creates a new template from an existing workflow
func (h *TemplateHandler) CreateTemplateFromWorkflow(c *gin.Context) {
	var req struct {
		WorkflowID  string   `json:"workflowId" binding:"required"`
		Name        string   `json:"name" binding:"required"`
		Description string   `json:"description"`
		Category    string   `json:"category" binding:"required"`
		Tags        []string `json:"tags"`
		IsPublic    bool     `json:"isPublic"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get the workflow
	workflow, err := h.db.GetWorkflowDefinition(c.Request.Context(), req.WorkflowID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Workflow not found"})
		return
	}

	// Get user ID from context for createdBy
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Create template from workflow
	template := &models.WorkflowTemplate{
		ID:          uuid.New().String(),
		Name:        req.Name,
		Description: req.Description,
		Category:    req.Category,
		Tags:        req.Tags,
		IsPublic:    req.IsPublic,
		IsFeatured:  false,
		Version:     "1.0.0",
		CreatedBy:   userID.(string),
		Steps:       workflow.Steps,
		Parameters:  workflow.Parameters,
		Variables:   workflow.Variables,
	}

	if err := h.db.CreateWorkflowTemplate(c.Request.Context(), template); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create template"})
		return
	}

	c.JSON(http.StatusCreated, template)
}

// ===== RATINGS & REVIEWS =====

// GetTemplateReviews retrieves reviews for a template
func (h *TemplateHandler) GetTemplateReviews(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")
	sortBy := c.DefaultQuery("sortBy", "helpful_count")
	sortOrder := c.DefaultQuery("sortOrder", "desc")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	reviews, err := h.db.GetTemplateReviews(c.Request.Context(), templateID, limit, offset, sortBy, sortOrder)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve reviews"})
		return
	}

	c.JSON(http.StatusOK, reviews)
}

// CreateTemplateReview creates a new review for a template
func (h *TemplateHandler) CreateTemplateReview(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	var review models.TemplateReview
	if err := c.ShouldBindJSON(&review); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid review data"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	review.ID = uuid.New().String()
	review.TemplateID = templateID
	review.UserID = userID.(string)

	// Get username from context if available
	if userName, exists := c.Get("userName"); exists {
		review.UserName = userName.(string)
	}

	if err := h.db.CreateTemplateReview(c.Request.Context(), &review); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create review"})
		return
	}

	// Update template rating
	if err := h.updateTemplateRating(c.Request.Context(), templateID); err != nil {
		// Log error but don't fail the review creation
		fmt.Printf("Failed to update template rating: %v\n", err)
	}

	c.JSON(http.StatusCreated, review)
}

// RateTemplate creates or updates a rating for a template
func (h *TemplateHandler) RateTemplate(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	var request struct {
		Rating int    `json:"rating" binding:"required,min=1,max=5"`
		Review string `json:"review"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid rating data"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	rating := &models.TemplateRating{
		ID:         uuid.New().String(),
		TemplateID: templateID,
		UserID:     userID.(string),
		Rating:     request.Rating,
		Review:     request.Review,
	}

	if err := h.db.CreateOrUpdateTemplateRating(c.Request.Context(), rating); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save rating"})
		return
	}

	// Update template rating
	if err := h.updateTemplateRating(c.Request.Context(), templateID); err != nil {
		// Log error but don't fail the rating creation
		fmt.Printf("Failed to update template rating: %v\n", err)
	}

	c.JSON(http.StatusCreated, rating)
}

// MarkReviewHelpful marks a review as helpful
func (h *TemplateHandler) MarkReviewHelpful(c *gin.Context) {
	reviewID := c.Param("reviewId")
	if reviewID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Review ID is required"})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	if err := h.db.MarkReviewHelpful(c.Request.Context(), reviewID, userID.(string)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to mark review as helpful"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Review marked as helpful"})
}

// updateTemplateRating recalculates and updates the template's average rating
func (h *TemplateHandler) updateTemplateRating(ctx context.Context, templateID string) error {
	avgRating, count, err := h.db.GetTemplateRatingStats(ctx, templateID)
	if err != nil {
		return err
	}

	template, err := h.db.GetWorkflowTemplate(ctx, templateID)
	if err != nil {
		return err
	}

	template.Rating = avgRating
	template.RatingCount = count

	return h.db.UpdateWorkflowTemplate(ctx, template)
}

// ===== COMMUNITY FEATURES =====

// AddToFavorites adds a template to user's favorites
func (h *TemplateHandler) AddToFavorites(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	favorite := &models.TemplateFavorite{
		ID:         uuid.New().String(),
		TemplateID: templateID,
		UserID:     userID.(string),
	}

	if err := h.db.AddTemplateFavorite(c.Request.Context(), favorite); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to add to favorites"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "Template added to favorites"})
}

// RemoveFromFavorites removes a template from user's favorites
func (h *TemplateHandler) RemoveFromFavorites(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	if err := h.db.RemoveTemplateFavorite(c.Request.Context(), templateID, userID.(string)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove from favorites"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Template removed from favorites"})
}

// GetUserFavorites gets user's favorite templates
func (h *TemplateHandler) GetUserFavorites(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	favorites, err := h.db.GetUserFavoriteTemplates(c.Request.Context(), userID.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve favorites"})
		return
	}

	c.JSON(http.StatusOK, favorites)
}

// ===== TEMPLATE VERSIONING =====

// GetTemplateVersions gets all versions of a template
func (h *TemplateHandler) GetTemplateVersions(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	versions, err := h.db.GetTemplateVersions(c.Request.Context(), templateID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve template versions"})
		return
	}

	c.JSON(http.StatusOK, versions)
}

// CreateTemplateVersion creates a new version of a template
func (h *TemplateHandler) CreateTemplateVersion(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	var version models.TemplateVersion
	if err := c.ShouldBindJSON(&version); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid version data"})
		return
	}

	version.ID = uuid.New().String()
	version.TemplateID = templateID

	if err := h.db.CreateTemplateVersion(c.Request.Context(), &version); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create template version"})
		return
	}

	c.JSON(http.StatusCreated, version)
}

// PublishTemplate publishes a template to the marketplace
func (h *TemplateHandler) PublishTemplate(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	template, err := h.db.GetWorkflowTemplate(c.Request.Context(), templateID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}

	// Check if user owns the template
	if template.CreatedBy != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "You can only publish your own templates"})
		return
	}

	// Make template public
	template.IsPublic = true

	if err := h.db.UpdateWorkflowTemplate(c.Request.Context(), template); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to publish template"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Template published successfully"})
}

// ===== TEMPLATE IMPORT/EXPORT =====

// ExportTemplate exports a template as JSON
func (h *TemplateHandler) ExportTemplate(c *gin.Context) {
	templateID := c.Param("id")
	if templateID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template ID is required"})
		return
	}

	template, err := h.db.GetWorkflowTemplate(c.Request.Context(), templateID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}

	// Create export format
	export := map[string]interface{}{
		"template":    template,
		"exported_at": time.Now(),
		"version":     "1.0",
	}

	// Set headers for file download
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s.json", template.Name))
	c.Header("Content-Type", "application/json")

	c.JSON(http.StatusOK, export)
}

// ImportTemplate imports a template from JSON
func (h *TemplateHandler) ImportTemplate(c *gin.Context) {
	var importData struct {
		Template models.WorkflowTemplate `json:"template"`
		Version  string                  `json:"version"`
	}

	if err := c.ShouldBindJSON(&importData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid import data"})
		return
	}

	// Generate new ID for imported template
	importData.Template.ID = uuid.New().String()

	// Get user ID from context
	if userID, exists := c.Get("userID"); exists {
		importData.Template.CreatedBy = userID.(string)
	}

	// Set as private by default
	importData.Template.IsPublic = false
	importData.Template.IsFeatured = false

	if err := h.db.CreateWorkflowTemplate(c.Request.Context(), &importData.Template); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to import template"})
		return
	}

	c.JSON(http.StatusCreated, importData.Template)
}
