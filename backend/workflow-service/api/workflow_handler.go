package api

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/claudio/deploy-orchestrator/shared/auth"
	"github.com/claudio/deploy-orchestrator/workflow-service/engine"
	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"github.com/claudio/deploy-orchestrator/workflow-service/storage"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// WorkflowHandler handles workflow definition operations
type WorkflowHandler struct {
	db         *storage.Database
	engine     *engine.WorkflowEngine
	authHelper *auth.AuthHelper
}

// NewWorkflowHandler creates a new workflow handler
func NewWorkflowHandler(db *storage.Database, engine *engine.WorkflowEngine) *WorkflowHandler {
	return &WorkflowHandler{
		db:         db,
		engine:     engine,
		authHelper: auth.NewAuthHelper(),
	}
}

// GetWorkflows retrieves workflow definitions
func (h *WorkflowHandler) GetWorkflows(c *gin.Context) {
	projectID := c.Query("projectId")
	limitStr := c.<PERSON>("limit", "50")
	offsetStr := c.<PERSON>("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	// Check user authentication using centralized auth helper
	authContext := h.authHelper.RequireAuthentication(c)
	if authContext == nil {
		// RequireAuthentication already sent the 401 response
		return
	}

	// Check if user is admin
	isAdmin := authContext.IsAdmin()

	// If user is not admin and no project ID is specified, return empty list
	if !isAdmin && projectID == "" {
		c.JSON(http.StatusOK, []models.WorkflowDefinition{})
		return
	}

	workflows, err := h.db.GetWorkflowDefinitions(c.Request.Context(), projectID, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve workflows"})
		return
	}

	if workflows == nil {
		workflows = []models.WorkflowDefinition{}
	}

	c.JSON(http.StatusOK, workflows)
}

// GetWorkflow retrieves a specific workflow definition
func (h *WorkflowHandler) GetWorkflow(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Workflow ID is required"})
		return
	}

	workflow, err := h.db.GetWorkflowDefinition(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Workflow not found"})
		return
	}

	c.JSON(http.StatusOK, workflow)
}

// CreateWorkflow creates a new workflow definition
func (h *WorkflowHandler) CreateWorkflow(c *gin.Context) {
	var workflow models.WorkflowDefinition
	if err := c.ShouldBindJSON(&workflow); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid workflow data"})
		return
	}

	// Generate UUID if not provided
	if workflow.ID == "" {
		workflow.ID = uuid.New().String()
	}

	// Set default version if not provided
	if workflow.Version == "" {
		workflow.Version = "1.0.0"
	}

	// Validate workflow structure
	if err := h.validateWorkflow(&workflow); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.db.CreateWorkflowDefinition(c.Request.Context(), &workflow); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create workflow"})
		return
	}

	c.JSON(http.StatusCreated, workflow)
}

// UpdateWorkflow updates an existing workflow definition
func (h *WorkflowHandler) UpdateWorkflow(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Workflow ID is required"})
		return
	}

	var workflow models.WorkflowDefinition
	if err := c.ShouldBindJSON(&workflow); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid workflow data"})
		return
	}

	workflow.ID = id

	// Validate workflow structure
	if err := h.validateWorkflow(&workflow); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.db.UpdateWorkflowDefinition(c.Request.Context(), &workflow); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update workflow"})
		return
	}

	c.JSON(http.StatusOK, workflow)
}

// DeleteWorkflow deletes a workflow definition
func (h *WorkflowHandler) DeleteWorkflow(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Workflow ID is required"})
		return
	}

	if err := h.db.DeleteWorkflowDefinition(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete workflow"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Workflow deleted successfully"})
}

// validateWorkflow validates the workflow structure
func (h *WorkflowHandler) validateWorkflow(workflow *models.WorkflowDefinition) error {
	if workflow.Name == "" {
		return fmt.Errorf("workflow name is required")
	}

	if len(workflow.Steps) == 0 {
		return fmt.Errorf("workflow must have at least one step")
	}

	// Validate step IDs are unique
	stepIDs := make(map[string]bool)
	for _, step := range workflow.Steps {
		if step.ID == "" {
			return fmt.Errorf("step ID is required")
		}
		if stepIDs[step.ID] {
			return fmt.Errorf("duplicate step ID: %s", step.ID)
		}
		stepIDs[step.ID] = true
	}

	// Validate dependencies exist
	for _, step := range workflow.Steps {
		for _, depID := range step.Dependencies {
			if !stepIDs[depID] {
				return fmt.Errorf("step %s depends on non-existent step: %s", step.ID, depID)
			}
		}
	}

	return nil
}

// ExecuteWorkflow starts a workflow execution
func (h *WorkflowHandler) ExecuteWorkflow(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Workflow ID is required"})
		return
	}

	var request struct {
		Parameters  map[string]interface{} `json:"parameters"`
		TriggerType string                 `json:"triggerType"`
		TriggerData map[string]interface{} `json:"triggerData"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Check user authentication using centralized auth helper
	authContext := h.authHelper.RequireAuthentication(c)
	if authContext == nil {
		// RequireAuthentication already sent the 401 response
		return
	}

	startedBy := authContext.UserID
	if request.TriggerType == "" {
		request.TriggerType = "manual"
	}

	execution, err := h.engine.StartExecution(
		c.Request.Context(),
		id,
		request.Parameters,
		startedBy,
		request.TriggerType,
		request.TriggerData,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, execution)
}
