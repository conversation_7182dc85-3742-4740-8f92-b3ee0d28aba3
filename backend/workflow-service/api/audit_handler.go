package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/audit"
	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"github.com/claudio/deploy-orchestrator/workflow-service/storage"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AuditHandler handles audit log operations
type AuditHandler struct {
	db          *storage.Database
	auditLogger *audit.AuditLogger
}

// NewAuditHandler creates a new audit handler
func NewAuditHandler(db *storage.Database, auditLogger *audit.AuditLogger) *AuditHandler {
	return &AuditHandler{
		db:          db,
		auditLogger: auditLogger,
	}
}

// GetAuditLogs retrieves audit logs with filtering
func (h *AuditHandler) GetAuditLogs(c *gin.Context) {
	// Check if user is admin
	isAdmin := false
	roles, exists := c.Get("roles")
	if exists {
		rolesList, ok := roles.([]string)
		if ok {
			for _, role := range rolesList {
				if role == "admin" {
					isAdmin = true
					break
				}
			}
		}
	}

	if !isAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	// Build filters
	filters := audit.AuditFilters{
		WorkflowID:  c.Query("workflowId"),
		ExecutionID: c.Query("executionId"),
		UserID:      c.Query("userId"),
		Action:      c.Query("action"),
		Resource:    c.Query("resource"),
		ProjectID:   c.Query("projectId"),
		Severity:    c.Query("severity"),
	}

	// Parse time filters
	if startTimeStr := c.Query("startTime"); startTimeStr != "" {
		if startTime, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			filters.StartTime = &startTime
		}
	}

	if endTimeStr := c.Query("endTime"); endTimeStr != "" {
		if endTime, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			filters.EndTime = &endTime
		}
	}

	auditLogs, err := h.auditLogger.GetAuditLogs(c.Request.Context(), filters, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve audit logs"})
		return
	}

	if auditLogs == nil {
		auditLogs = []models.WorkflowAuditLog{}
	}

	c.JSON(http.StatusOK, auditLogs)
}

// CreateNotificationRule creates a new notification rule
func (h *AuditHandler) CreateNotificationRule(c *gin.Context) {
	// Check if user is admin
	isAdmin := false
	roles, exists := c.Get("roles")
	if exists {
		rolesList, ok := roles.([]string)
		if ok {
			for _, role := range rolesList {
				if role == "admin" {
					isAdmin = true
					break
				}
			}
		}
	}

	if !isAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	var rule models.NotificationRule
	if err := c.ShouldBindJSON(&rule); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification rule data"})
		return
	}

	// Generate UUID if not provided
	if rule.ID == "" {
		rule.ID = uuid.New().String()
	}

	// Validate rule
	if rule.Name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Rule name is required"})
		return
	}

	if len(rule.EventTypes) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "At least one event type is required"})
		return
	}

	if len(rule.Recipients) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "At least one recipient is required"})
		return
	}

	if err := h.db.CreateNotificationRule(c.Request.Context(), &rule); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create notification rule"})
		return
	}

	c.JSON(http.StatusCreated, rule)
}

// GetNotificationRules retrieves notification rules
func (h *AuditHandler) GetNotificationRules(c *gin.Context) {
	// Check if user is admin
	isAdmin := false
	roles, exists := c.Get("roles")
	if exists {
		rolesList, ok := roles.([]string)
		if ok {
			for _, role := range rolesList {
				if role == "admin" {
					isAdmin = true
					break
				}
			}
		}
	}

	if !isAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	projectID := c.Query("projectId")
	rules, err := h.db.GetNotificationRules(c.Request.Context(), projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve notification rules"})
		return
	}

	if rules == nil {
		rules = []models.NotificationRule{}
	}

	c.JSON(http.StatusOK, rules)
}

// UpdateNotificationRule updates a notification rule
func (h *AuditHandler) UpdateNotificationRule(c *gin.Context) {
	// Check if user is admin
	isAdmin := false
	roles, exists := c.Get("roles")
	if exists {
		rolesList, ok := roles.([]string)
		if ok {
			for _, role := range rolesList {
				if role == "admin" {
					isAdmin = true
					break
				}
			}
		}
	}

	if !isAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Rule ID is required"})
		return
	}

	var rule models.NotificationRule
	if err := c.ShouldBindJSON(&rule); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification rule data"})
		return
	}

	rule.ID = id

	// Validate rule
	if rule.Name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Rule name is required"})
		return
	}

	if len(rule.EventTypes) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "At least one event type is required"})
		return
	}

	if len(rule.Recipients) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "At least one recipient is required"})
		return
	}

	if err := h.db.UpdateNotificationRule(c.Request.Context(), &rule); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update notification rule"})
		return
	}

	c.JSON(http.StatusOK, rule)
}

// DeleteNotificationRule deletes a notification rule
func (h *AuditHandler) DeleteNotificationRule(c *gin.Context) {
	// Check if user is admin
	isAdmin := false
	roles, exists := c.Get("roles")
	if exists {
		rolesList, ok := roles.([]string)
		if ok {
			for _, role := range rolesList {
				if role == "admin" {
					isAdmin = true
					break
				}
			}
		}
	}

	if !isAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Rule ID is required"})
		return
	}

	if err := h.db.DeleteNotificationRule(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete notification rule"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Notification rule deleted successfully"})
}
