package audit

import (
	"context"
	"encoding/json"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"github.com/claudio/deploy-orchestrator/workflow-service/storage"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// AuditLogger handles audit logging for workflow operations
type AuditLogger struct {
	db     *storage.Database
	logger *zap.Logger
}

// NewAuditLogger creates a new audit logger
func NewAuditLogger(db *storage.Database, logger *zap.Logger) *AuditLogger {
	return &AuditLogger{
		db:     db,
		logger: logger,
	}
}

// AuditContext contains context information for audit logging
type AuditContext struct {
	UserID    string
	IPAddress string
	UserAgent string
	ProjectID string
}

// LogWorkflowAction logs workflow-related actions
func (a *AuditLogger) LogWorkflowAction(ctx context.Context, auditCtx *AuditContext, action, workflowID string, details string, changes map[string]interface{}) error {
	return a.logAction(ctx, auditCtx, action, "workflow", workflowID, "", "", details, changes, "info")
}

// LogExecutionAction logs execution-related actions
func (a *AuditLogger) LogExecutionAction(ctx context.Context, auditCtx *AuditContext, action, workflowID, executionID string, details string, changes map[string]interface{}) error {
	return a.logAction(ctx, auditCtx, action, "execution", executionID, workflowID, "", details, changes, "info")
}

// LogStepAction logs step-related actions
func (a *AuditLogger) LogStepAction(ctx context.Context, auditCtx *AuditContext, action, workflowID, executionID, stepID string, details string, changes map[string]interface{}) error {
	return a.logAction(ctx, auditCtx, action, "step", stepID, workflowID, executionID, details, changes, "info")
}

// LogError logs error-level audit events
func (a *AuditLogger) LogError(ctx context.Context, auditCtx *AuditContext, action, resource, resourceID, workflowID, executionID string, details string, changes map[string]interface{}) error {
	return a.logAction(ctx, auditCtx, action, resource, resourceID, workflowID, executionID, details, changes, "error")
}

// LogWarning logs warning-level audit events
func (a *AuditLogger) LogWarning(ctx context.Context, auditCtx *AuditContext, action, resource, resourceID, workflowID, executionID string, details string, changes map[string]interface{}) error {
	return a.logAction(ctx, auditCtx, action, resource, resourceID, workflowID, executionID, details, changes, "warning")
}

// LogCritical logs critical-level audit events
func (a *AuditLogger) LogCritical(ctx context.Context, auditCtx *AuditContext, action, resource, resourceID, workflowID, executionID string, details string, changes map[string]interface{}) error {
	return a.logAction(ctx, auditCtx, action, resource, resourceID, workflowID, executionID, details, changes, "critical")
}

// logAction is the internal method that creates and saves audit log entries
func (a *AuditLogger) logAction(ctx context.Context, auditCtx *AuditContext, action, resource, resourceID, workflowID, executionID, details string, changes map[string]interface{}, severity string) error {
	auditLog := &models.WorkflowAuditLog{
		ID:          uuid.New().String(),
		WorkflowID:  workflowID,
		ExecutionID: executionID,
		UserID:      auditCtx.UserID,
		Action:      action,
		Resource:    resource,
		ResourceID:  resourceID,
		Details:     details,
		Changes:     changes,
		IPAddress:   auditCtx.IPAddress,
		UserAgent:   auditCtx.UserAgent,
		ProjectID:   auditCtx.ProjectID,
		Severity:    severity,
	}

	// Marshal changes to JSON
	if len(changes) > 0 {
		if changesJSON, err := json.Marshal(changes); err == nil {
			auditLog.ChangesJSON = changesJSON
		}
	}

	// Save to database
	if err := a.db.CreateWorkflowAuditLog(ctx, auditLog); err != nil {
		a.logger.Error("Failed to save audit log",
			zap.String("action", action),
			zap.String("resource", resource),
			zap.String("resourceId", resourceID),
			zap.Error(err))
		return err
	}

	a.logger.Debug("Audit log created",
		zap.String("id", auditLog.ID),
		zap.String("action", action),
		zap.String("resource", resource),
		zap.String("resourceId", resourceID),
		zap.String("userId", auditCtx.UserID),
		zap.String("severity", severity))

	return nil
}

// GetAuditLogs retrieves audit logs with filtering
func (a *AuditLogger) GetAuditLogs(ctx context.Context, filters AuditFilters, limit, offset int) ([]models.WorkflowAuditLog, error) {
	return a.db.GetWorkflowAuditLogs(ctx, filters, limit, offset)
}

// AuditFilters represents filters for audit log queries
type AuditFilters struct {
	WorkflowID  string
	ExecutionID string
	UserID      string
	Action      string
	Resource    string
	ProjectID   string
	Severity    string
	StartTime   *time.Time
	EndTime     *time.Time
}

// LogWorkflowCreated logs workflow creation
func (a *AuditLogger) LogWorkflowCreated(ctx context.Context, auditCtx *AuditContext, workflow *models.WorkflowDefinition) error {
	changes := map[string]interface{}{
		"name":        workflow.Name,
		"description": workflow.Description,
		"projectId":   workflow.ProjectID,
		"steps":       len(workflow.Steps),
	}
	return a.LogWorkflowAction(ctx, auditCtx, "create", workflow.ID, "Workflow created", changes)
}

// LogWorkflowUpdated logs workflow updates
func (a *AuditLogger) LogWorkflowUpdated(ctx context.Context, auditCtx *AuditContext, workflowID string, oldWorkflow, newWorkflow *models.WorkflowDefinition) error {
	changes := map[string]interface{}{}

	if oldWorkflow.Name != newWorkflow.Name {
		changes["name"] = map[string]interface{}{"old": oldWorkflow.Name, "new": newWorkflow.Name}
	}
	if oldWorkflow.Description != newWorkflow.Description {
		changes["description"] = map[string]interface{}{"old": oldWorkflow.Description, "new": newWorkflow.Description}
	}
	if len(oldWorkflow.Steps) != len(newWorkflow.Steps) {
		changes["steps"] = map[string]interface{}{"old": len(oldWorkflow.Steps), "new": len(newWorkflow.Steps)}
	}

	return a.LogWorkflowAction(ctx, auditCtx, "update", workflowID, "Workflow updated", changes)
}

// LogWorkflowDeleted logs workflow deletion
func (a *AuditLogger) LogWorkflowDeleted(ctx context.Context, auditCtx *AuditContext, workflowID string, workflowName string) error {
	changes := map[string]interface{}{
		"name": workflowName,
	}
	return a.LogWorkflowAction(ctx, auditCtx, "delete", workflowID, "Workflow deleted", changes)
}

// LogExecutionStarted logs execution start
func (a *AuditLogger) LogExecutionStarted(ctx context.Context, auditCtx *AuditContext, execution *models.WorkflowExecution) error {
	changes := map[string]interface{}{
		"workflowId":  execution.WorkflowID,
		"triggerType": execution.TriggerType,
		"startedBy":   execution.StartedBy,
	}
	return a.LogExecutionAction(ctx, auditCtx, "execute", execution.WorkflowID, execution.ID, "Workflow execution started", changes)
}

// LogExecutionCompleted logs execution completion
func (a *AuditLogger) LogExecutionCompleted(ctx context.Context, auditCtx *AuditContext, execution *models.WorkflowExecution, success bool) error {
	changes := map[string]interface{}{
		"status":   execution.Status,
		"duration": execution.Duration,
		"success":  success,
	}
	details := "Workflow execution completed successfully"
	if !success {
		details = "Workflow execution failed"
	}
	return a.LogExecutionAction(ctx, auditCtx, "complete", execution.WorkflowID, execution.ID, details, changes)
}

// LogExecutionCancelled logs execution cancellation
func (a *AuditLogger) LogExecutionCancelled(ctx context.Context, auditCtx *AuditContext, workflowID, executionID string) error {
	changes := map[string]interface{}{
		"status": "cancelled",
	}
	return a.LogExecutionAction(ctx, auditCtx, "cancel", workflowID, executionID, "Workflow execution cancelled", changes)
}

// LogStepStarted logs step execution start
func (a *AuditLogger) LogStepStarted(ctx context.Context, auditCtx *AuditContext, workflowID, executionID, stepID, stepName string) error {
	changes := map[string]interface{}{
		"stepName": stepName,
		"status":   "started",
	}
	return a.LogStepAction(ctx, auditCtx, "start", workflowID, executionID, stepID, "Step execution started", changes)
}

// LogStepCompleted logs step execution completion
func (a *AuditLogger) LogStepCompleted(ctx context.Context, auditCtx *AuditContext, workflowID, executionID, stepID, stepName string, success bool, duration time.Duration) error {
	changes := map[string]interface{}{
		"stepName": stepName,
		"status":   "completed",
		"success":  success,
		"duration": duration.Seconds(),
	}
	details := "Step execution completed successfully"
	if !success {
		details = "Step execution failed"
	}
	return a.LogStepAction(ctx, auditCtx, "complete", workflowID, executionID, stepID, details, changes)
}

// LogStepSkipped logs step skipping
func (a *AuditLogger) LogStepSkipped(ctx context.Context, auditCtx *AuditContext, workflowID, executionID, stepID, stepName, reason string) error {
	changes := map[string]interface{}{
		"stepName": stepName,
		"status":   "skipped",
		"reason":   reason,
	}
	return a.LogStepAction(ctx, auditCtx, "skip", workflowID, executionID, stepID, "Step execution skipped", changes)
}
