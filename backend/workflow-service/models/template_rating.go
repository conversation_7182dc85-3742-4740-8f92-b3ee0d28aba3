package models

import (
	"encoding/json"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/models"
)

// TemplateRating represents a user rating for a workflow template
type TemplateRating struct {
	ID         string `json:"id" gorm:"primaryKey;type:text"`
	TemplateID string `json:"templateId" gorm:"column:template_id;type:text;index;not null"`
	UserID     string `json:"userId" gorm:"column:user_id;type:text;index;not null"`
	Rating     int    `json:"rating" gorm:"not null;check:rating >= 1 AND rating <= 5"`
	Review     string `json:"review" gorm:"type:text"`
	IsHelpful  bool   `json:"isHelpful" gorm:"column:is_helpful;default:false"`
	models.Base
}

// TemplateReview represents a detailed review for a workflow template
type TemplateReview struct {
	ID           string   `json:"id" gorm:"primaryKey;type:text"`
	TemplateID   string   `json:"templateId" gorm:"column:template_id;type:text;index;not null"`
	UserID       string   `json:"userId" gorm:"column:user_id;type:text;index;not null"`
	UserName     string   `json:"userName" gorm:"column:user_name;type:text"`
	Title        string   `json:"title" gorm:"type:text;not null"`
	Content      string   `json:"content" gorm:"type:text;not null"`
	Rating       int      `json:"rating" gorm:"not null;check:rating >= 1 AND rating <= 5"`
	Pros         []string `json:"pros" gorm:"-"`
	ProsJSON     string   `json:"-" gorm:"column:pros;type:jsonb"`
	Cons         []string `json:"cons" gorm:"-"`
	ConsJSON     string   `json:"-" gorm:"column:cons;type:jsonb"`
	HelpfulCount int      `json:"helpfulCount" gorm:"column:helpful_count;default:0"`
	ReportCount  int      `json:"reportCount" gorm:"column:report_count;default:0"`
	IsVerified   bool     `json:"isVerified" gorm:"column:is_verified;default:false"`
	IsModerated  bool     `json:"isModerated" gorm:"column:is_moderated;default:false"`
	models.Base
}

// TemplateCategory represents a category for organizing templates
type TemplateCategory struct {
	ID          string `json:"id" gorm:"primaryKey;type:text"`
	Name        string `json:"name" gorm:"type:text;not null;unique"`
	Description string `json:"description" gorm:"type:text"`
	Icon        string `json:"icon" gorm:"type:text"`
	Color       string `json:"color" gorm:"type:text"`
	SortOrder   int    `json:"sortOrder" gorm:"column:sort_order;default:0"`
	IsActive    bool   `json:"isActive" gorm:"column:is_active;default:true"`
	models.Base
}

// TemplateTag represents a tag for templates
type TemplateTag struct {
	ID          string `json:"id" gorm:"primaryKey;type:text"`
	Name        string `json:"name" gorm:"type:text;not null;unique"`
	Description string `json:"description" gorm:"type:text"`
	Color       string `json:"color" gorm:"type:text"`
	UsageCount  int    `json:"usageCount" gorm:"column:usage_count;default:0"`
	models.Base
}

// TemplateFavorite represents a user's favorite template
type TemplateFavorite struct {
	ID         string `json:"id" gorm:"primaryKey;type:text"`
	TemplateID string `json:"templateId" gorm:"column:template_id;type:text;index;not null"`
	UserID     string `json:"userId" gorm:"column:user_id;type:text;index;not null"`
	models.Base
}

// TemplateDownload represents a template download record
type TemplateDownload struct {
	ID         string `json:"id" gorm:"primaryKey;type:text"`
	TemplateID string `json:"templateId" gorm:"column:template_id;type:text;index;not null"`
	UserID     string `json:"userId" gorm:"column:user_id;type:text;index"`
	IPAddress  string `json:"ipAddress" gorm:"column:ip_address;type:text"`
	UserAgent  string `json:"userAgent" gorm:"column:user_agent;type:text"`
	models.Base
}

// TemplateVersion represents a version of a template
type TemplateVersion struct {
	ID           string    `json:"id" gorm:"primaryKey;type:text"`
	TemplateID   string    `json:"templateId" gorm:"column:template_id;type:text;index;not null"`
	Version      string    `json:"version" gorm:"type:text;not null"`
	Description  string    `json:"description" gorm:"type:text"`
	ChangeLog    string    `json:"changeLog" gorm:"column:change_log;type:text"`
	IsStable     bool      `json:"isStable" gorm:"column:is_stable;default:false"`
	IsDeprecated bool      `json:"isDeprecated" gorm:"column:is_deprecated;default:false"`
	ReleaseDate  time.Time `json:"releaseDate" gorm:"column:release_date"`
	models.Base
}

// TableName overrides for all template-related models
func (TemplateRating) TableName() string {
	return "template_ratings"
}

func (TemplateReview) TableName() string {
	return "template_reviews"
}

func (TemplateCategory) TableName() string {
	return "template_categories"
}

func (TemplateTag) TableName() string {
	return "template_tags"
}

func (TemplateFavorite) TableName() string {
	return "template_favorites"
}

func (TemplateDownload) TableName() string {
	return "template_downloads"
}

func (TemplateVersion) TableName() string {
	return "template_versions"
}

// BeforeSave handles JSON marshaling for TemplateReview
func (r *TemplateReview) BeforeSave() error {
	if len(r.Pros) > 0 {
		data, err := json.Marshal(r.Pros)
		if err != nil {
			return err
		}
		r.ProsJSON = string(data)
	}

	if len(r.Cons) > 0 {
		data, err := json.Marshal(r.Cons)
		if err != nil {
			return err
		}
		r.ConsJSON = string(data)
	}

	return nil
}

// AfterFind handles JSON unmarshaling for TemplateReview
func (r *TemplateReview) AfterFind() error {
	if r.ProsJSON != "" {
		if err := json.Unmarshal([]byte(r.ProsJSON), &r.Pros); err != nil {
			return err
		}
	}

	if r.ConsJSON != "" {
		if err := json.Unmarshal([]byte(r.ConsJSON), &r.Cons); err != nil {
			return err
		}
	}

	return nil
}
