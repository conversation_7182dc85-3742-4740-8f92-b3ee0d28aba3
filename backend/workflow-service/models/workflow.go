package models

import (
	"encoding/json"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/models"
	"gorm.io/gorm"
)

// WorkflowDefinition represents a workflow definition
type WorkflowDefinition struct {
	ID           string                 `json:"id" gorm:"primaryKey;type:text"`
	Name         string                 `json:"name" gorm:"type:text;not null"`
	Description  string                 `json:"description" gorm:"type:text"`
	ProjectID    string                 `json:"projectId" gorm:"column:project_id;type:text;index"`
	Version      string                 `json:"version" gorm:"type:text;not null"`
	IsActive     bool                   `json:"isActive" gorm:"column:is_active;default:true"`
	Steps        []WorkflowStep         `json:"steps" gorm:"-"`
	StepsJSON    json.RawMessage        `json:"-" gorm:"column:steps;type:jsonb"`
	Parameters   []WorkflowParameter    `json:"parameters" gorm:"-"`
	ParamsJSON   json.RawMessage        `json:"-" gorm:"column:parameters;type:jsonb"`
	Triggers     []WorkflowTrigger      `json:"triggers" gorm:"-"`
	TriggersJSON json.RawMessage        `json:"-" gorm:"column:triggers;type:jsonb"`
	Variables    map[string]interface{} `json:"variables" gorm:"-"`
	VarsJSON     json.RawMessage        `json:"-" gorm:"column:variables;type:jsonb"`
	Tags         []string               `json:"tags" gorm:"-"`
	TagsJSON     json.RawMessage        `json:"-" gorm:"column:tags;type:jsonb"`
	models.Base
}

// WorkflowStep represents a single step in a workflow
type WorkflowStep struct {
	ID                 string                 `json:"id"`
	Name               string                 `json:"name"`
	Type               string                 `json:"type"` // script, http, deployment, condition, parallel, sequential
	Description        string                 `json:"description"`
	Config             map[string]interface{} `json:"config"`
	Dependencies       []string               `json:"dependencies"`                 // IDs of steps that must complete first
	Conditions         []StepCondition        `json:"conditions"`                   // Basic conditions for step execution
	AdvancedConditions []AdvancedCondition    `json:"advancedConditions,omitempty"` // Enhanced conditional logic
	OnSuccess          []string               `json:"onSuccess"`                    // Next steps if successful
	OnFailure          []string               `json:"onFailure"`                    // Next steps if failed
	Timeout            int                    `json:"timeout"`                      // Timeout in minutes
	RetryPolicy        RetryPolicy            `json:"retryPolicy"`
	Position           Position               `json:"position"` // For visual designer
}

// StepCondition represents a condition for step execution
type StepCondition struct {
	Variable string      `json:"variable"`
	Operator string      `json:"operator"` // eq, ne, gt, lt, gte, lte, contains, exists
	Value    interface{} `json:"value"`
	Logic    string      `json:"logic"` // and, or (for combining multiple conditions)
}

// RetryPolicy defines retry behavior for a step
type RetryPolicy struct {
	MaxAttempts       int     `json:"maxAttempts"`
	DelaySeconds      int     `json:"delaySeconds"`
	BackoffMultiplier float64 `json:"backoffMultiplier"`
}

// Position represents the visual position of a step in the designer
type Position struct {
	X int `json:"x"`
	Y int `json:"y"`
}

// WorkflowParameter represents a parameter that can be passed to a workflow
type WorkflowParameter struct {
	Name         string      `json:"name"`
	Type         string      `json:"type"` // string, number, boolean, array, object
	Description  string      `json:"description"`
	Required     bool        `json:"required"`
	DefaultValue interface{} `json:"defaultValue"`
	Options      []string    `json:"options"` // For enum-like parameters
}

// WorkflowTrigger represents a trigger that can start a workflow
type WorkflowTrigger struct {
	Type   string                 `json:"type"` // manual, webhook, schedule, event
	Config map[string]interface{} `json:"config"`
}

// WorkflowTemplate represents a reusable workflow template
type WorkflowTemplate struct {
	ID               string                 `json:"id" gorm:"primaryKey;type:text"`
	Name             string                 `json:"name" gorm:"type:text;not null"`
	Description      string                 `json:"description" gorm:"type:text"`
	Category         string                 `json:"category" gorm:"type:text"`
	IsPublic         bool                   `json:"isPublic" gorm:"column:is_public;default:false"`
	IsFeatured       bool                   `json:"isFeatured" gorm:"column:is_featured;default:false"`
	CreatedBy        string                 `json:"createdBy" gorm:"column:created_by;type:text"`
	AuthorName       string                 `json:"authorName" gorm:"column:author_name;type:text"`
	AuthorEmail      string                 `json:"authorEmail" gorm:"column:author_email;type:text"`
	Version          string                 `json:"version" gorm:"type:text;default:'1.0.0'"`
	Steps            []WorkflowStep         `json:"steps" gorm:"-"`
	StepsJSON        json.RawMessage        `json:"-" gorm:"column:steps;type:jsonb"`
	Parameters       []WorkflowParameter    `json:"parameters" gorm:"-"`
	ParamsJSON       json.RawMessage        `json:"-" gorm:"column:parameters;type:jsonb"`
	Variables        map[string]interface{} `json:"variables" gorm:"-"`
	VarsJSON         json.RawMessage        `json:"-" gorm:"column:variables;type:jsonb"`
	Tags             []string               `json:"tags" gorm:"-"`
	TagsJSON         json.RawMessage        `json:"-" gorm:"column:tags;type:jsonb"`
	UsageCount       int                    `json:"usageCount" gorm:"column:usage_count;default:0"`
	DownloadCount    int                    `json:"downloadCount" gorm:"column:download_count;default:0"`
	Rating           float64                `json:"rating" gorm:"column:rating;default:0"`
	RatingCount      int                    `json:"ratingCount" gorm:"column:rating_count;default:0"`
	Documentation    string                 `json:"documentation" gorm:"type:text"`
	Requirements     []string               `json:"requirements" gorm:"-"`
	RequirementsJSON json.RawMessage        `json:"-" gorm:"column:requirements;type:jsonb"`
	Screenshots      []string               `json:"screenshots" gorm:"-"`
	ScreenshotsJSON  json.RawMessage        `json:"-" gorm:"column:screenshots;type:jsonb"`
	License          string                 `json:"license" gorm:"type:text;default:'MIT'"`
	models.Base
}

// WorkflowExecution represents a workflow execution instance
type WorkflowExecution struct {
	ID            string                 `json:"id" gorm:"primaryKey;type:text"`
	WorkflowID    string                 `json:"workflowId" gorm:"column:workflow_id;type:text;index"`
	ProjectID     string                 `json:"projectId" gorm:"column:project_id;type:text;index"`
	Status        string                 `json:"status" gorm:"type:text;not null"` // pending, running, completed, failed, cancelled
	StartedBy     string                 `json:"startedBy" gorm:"column:started_by;type:text"`
	StartedAt     *time.Time             `json:"startedAt" gorm:"column:started_at"`
	CompletedAt   *time.Time             `json:"completedAt" gorm:"column:completed_at"`
	Duration      int                    `json:"duration"` // Duration in seconds
	Parameters    map[string]interface{} `json:"parameters" gorm:"-"`
	ParamsJSON    json.RawMessage        `json:"-" gorm:"column:parameters;type:jsonb"`
	Variables     map[string]interface{} `json:"variables" gorm:"-"`
	VarsJSON      json.RawMessage        `json:"-" gorm:"column:variables;type:jsonb"`
	SecretMapping map[string]string      `json:"secretMapping" gorm:"-"` // templateVar -> userSecretName
	SecretMapJSON json.RawMessage        `json:"-" gorm:"column:secret_mapping;type:jsonb"`
	CurrentStep   string                 `json:"currentStep" gorm:"column:current_step;type:text"`
	ErrorMessage  string                 `json:"errorMessage" gorm:"column:error_message;type:text"`
	TriggerType   string                 `json:"triggerType" gorm:"column:trigger_type;type:text"`
	TriggerData   map[string]interface{} `json:"triggerData" gorm:"-"`
	TriggerJSON   json.RawMessage        `json:"-" gorm:"column:trigger_data;type:jsonb"`
	models.Base
}

// StepExecution represents the execution of a single workflow step
type StepExecution struct {
	ID           string                 `json:"id" gorm:"primaryKey;type:text"`
	ExecutionID  string                 `json:"executionId" gorm:"column:execution_id;type:text;index"`
	StepID       string                 `json:"stepId" gorm:"column:step_id;type:text"`
	StepName     string                 `json:"stepName" gorm:"column:step_name;type:text"`
	Status       string                 `json:"status" gorm:"type:text;not null"` // pending, running, completed, failed, skipped
	StartedAt    *time.Time             `json:"startedAt" gorm:"column:started_at"`
	CompletedAt  *time.Time             `json:"completedAt" gorm:"column:completed_at"`
	Duration     int                    `json:"duration"` // Duration in seconds
	AttemptCount int                    `json:"attemptCount" gorm:"column:attempt_count;default:1"`
	Input        map[string]interface{} `json:"input" gorm:"-"`
	InputJSON    json.RawMessage        `json:"-" gorm:"column:input;type:jsonb"`
	Output       map[string]interface{} `json:"output" gorm:"-"`
	OutputJSON   json.RawMessage        `json:"-" gorm:"column:output;type:jsonb"`
	ErrorMessage string                 `json:"errorMessage" gorm:"column:error_message;type:text"`
	Logs         []StepLog              `json:"logs" gorm:"-"`
	LogsJSON     json.RawMessage        `json:"-" gorm:"column:logs;type:jsonb"`
	models.Base
}

// StepLog represents a log entry for a step execution
type StepLog struct {
	Timestamp time.Time              `json:"timestamp"`
	Level     string                 `json:"level"` // info, warn, error, debug
	Message   string                 `json:"message"`
	Source    string                 `json:"source,omitempty"` // script, system, user
	Data      map[string]interface{} `json:"data,omitempty"`
}

// ExecutionMetrics represents performance metrics for workflow execution
type ExecutionMetrics struct {
	ID                 string                 `json:"id" gorm:"primaryKey;type:text"`
	ExecutionID        string                 `json:"executionId" gorm:"column:execution_id;type:text;index"`
	TotalSteps         int                    `json:"totalSteps" gorm:"column:total_steps"`
	CompletedSteps     int                    `json:"completedSteps" gorm:"column:completed_steps"`
	FailedSteps        int                    `json:"failedSteps" gorm:"column:failed_steps"`
	SkippedSteps       int                    `json:"skippedSteps" gorm:"column:skipped_steps"`
	AverageStepTime    float64                `json:"averageStepTime" gorm:"column:average_step_time"`
	TotalExecutionTime float64                `json:"totalExecutionTime" gorm:"column:total_execution_time"`
	ResourceUsage      map[string]interface{} `json:"resourceUsage" gorm:"-"`
	ResourceJSON       json.RawMessage        `json:"-" gorm:"column:resource_usage;type:jsonb"`
	PerformanceData    map[string]interface{} `json:"performanceData" gorm:"-"`
	PerfDataJSON       json.RawMessage        `json:"-" gorm:"column:performance_data;type:jsonb"`
	models.Base
}

// ExecutionEvent represents real-time events during workflow execution
type ExecutionEvent struct {
	ID          string                 `json:"id" gorm:"primaryKey;type:text"`
	ExecutionID string                 `json:"executionId" gorm:"column:execution_id;type:text;index"`
	StepID      string                 `json:"stepId" gorm:"column:step_id;type:text"`
	EventType   string                 `json:"eventType" gorm:"column:event_type;type:text"` // step_started, step_completed, step_failed, execution_paused, etc.
	EventData   map[string]interface{} `json:"eventData" gorm:"-"`
	EventJSON   json.RawMessage        `json:"-" gorm:"column:event_data;type:jsonb"`
	Timestamp   time.Time              `json:"timestamp" gorm:"column:timestamp"`
	Severity    string                 `json:"severity" gorm:"column:severity;type:text;default:'info'"` // info, warning, error, critical
	Source      string                 `json:"source" gorm:"column:source;type:text"`                    // workflow-engine, step-executor, instance-manager
	UserID      string                 `json:"userId" gorm:"column:user_id;type:text;index"`
	ProjectID   string                 `json:"projectId" gorm:"column:project_id;type:text;index"`
	models.Base
}

// WorkflowEventTrigger represents event-based workflow triggers
type WorkflowEventTrigger struct {
	ID             string                 `json:"id" gorm:"primaryKey;type:text"`
	WorkflowID     string                 `json:"workflowId" gorm:"column:workflow_id;type:text;index"`
	EventType      string                 `json:"eventType" gorm:"column:event_type;type:text"` // webhook, schedule, manual, event
	TriggerData    map[string]interface{} `json:"triggerData" gorm:"-"`
	TriggerJSON    json.RawMessage        `json:"-" gorm:"column:trigger_data;type:jsonb"`
	Enabled        bool                   `json:"enabled" gorm:"default:true"`
	Conditions     []TriggerCondition     `json:"conditions" gorm:"-"`
	ConditionsJSON json.RawMessage        `json:"-" gorm:"column:conditions;type:jsonb"`
	models.Base
}

// TriggerCondition represents conditions for event triggers
type TriggerCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"` // eq, ne, gt, lt, contains, regex
	Value    interface{} `json:"value"`
	LogicOp  string      `json:"logicOp"` // and, or
}

// AdvancedCondition represents enhanced conditional logic
type AdvancedCondition struct {
	ID         string                 `json:"id"`
	Type       string                 `json:"type"`                 // expression, script, comparison, logical
	Expression string                 `json:"expression,omitempty"` // For expression-based conditions
	Script     string                 `json:"script,omitempty"`     // For script-based conditions
	Language   string                 `json:"language,omitempty"`   // For script conditions
	Left       interface{}            `json:"left,omitempty"`       // Left operand for comparisons
	Operator   string                 `json:"operator,omitempty"`   // ==, !=, >, <, >=, <=, contains, etc.
	Right      interface{}            `json:"right,omitempty"`      // Right operand for comparisons
	Conditions []AdvancedCondition    `json:"conditions,omitempty"` // For logical operators (AND, OR)
	LogicalOp  string                 `json:"logicalOp,omitempty"`  // AND, OR, NOT
	Variables  map[string]interface{} `json:"variables,omitempty"`  // Context variables
}

// WorkflowAuditLog represents audit trail for workflow operations
type WorkflowAuditLog struct {
	ID          string                 `json:"id" gorm:"primaryKey;type:text"`
	WorkflowID  string                 `json:"workflowId" gorm:"column:workflow_id;type:text;index"`
	ExecutionID string                 `json:"executionId" gorm:"column:execution_id;type:text;index"`
	StepID      string                 `json:"stepId" gorm:"column:step_id;type:text"`
	UserID      string                 `json:"userId" gorm:"column:user_id;type:text;index"`
	Action      string                 `json:"action" gorm:"type:text;not null;index"`   // create, update, delete, execute, pause, resume, cancel
	Resource    string                 `json:"resource" gorm:"type:text;not null;index"` // workflow, execution, step
	ResourceID  string                 `json:"resourceId" gorm:"column:resource_id;type:text;index"`
	Details     string                 `json:"details" gorm:"type:text"`
	Changes     map[string]interface{} `json:"changes" gorm:"-"` // What changed
	ChangesJSON json.RawMessage        `json:"-" gorm:"column:changes;type:jsonb"`
	IPAddress   string                 `json:"ipAddress" gorm:"column:ip_address;type:text"`
	UserAgent   string                 `json:"userAgent" gorm:"column:user_agent;type:text"`
	ProjectID   string                 `json:"projectId" gorm:"column:project_id;type:text;index"`
	Severity    string                 `json:"severity" gorm:"column:severity;type:text;default:'info'"` // info, warning, error, critical
	models.Base
}

// NotificationRule represents rules for sending notifications based on workflow events
type NotificationRule struct {
	ID             string                  `json:"id" gorm:"primaryKey;type:text"`
	Name           string                  `json:"name" gorm:"type:text;not null"`
	Description    string                  `json:"description" gorm:"type:text"`
	WorkflowID     string                  `json:"workflowId" gorm:"column:workflow_id;type:text;index"` // Empty for global rules
	ProjectID      string                  `json:"projectId" gorm:"column:project_id;type:text;index"`
	EventTypes     []string                `json:"eventTypes" gorm:"-"` // execution_started, execution_completed, execution_failed, step_failed
	EventTypesJSON json.RawMessage         `json:"-" gorm:"column:event_types;type:jsonb"`
	Conditions     []TriggerCondition      `json:"conditions" gorm:"-"`
	ConditionsJSON json.RawMessage         `json:"-" gorm:"column:conditions;type:jsonb"`
	Recipients     []NotificationRecipient `json:"recipients" gorm:"-"`
	RecipientsJSON json.RawMessage         `json:"-" gorm:"column:recipients;type:jsonb"`
	Template       string                  `json:"template" gorm:"type:text"` // Notification template
	Enabled        bool                    `json:"enabled" gorm:"default:true"`
	Cooldown       int                     `json:"cooldown" gorm:"default:0"` // Cooldown period in minutes
	models.Base
}

// NotificationRecipient represents a notification recipient
type NotificationRecipient struct {
	Type    string `json:"type"`    // email, slack, teams, webhook
	Address string `json:"address"` // email address, webhook URL, etc.
	UserID  string `json:"userId"`  // For user-based notifications
}

// TableName overrides the table name for WorkflowDefinition
func (WorkflowDefinition) TableName() string {
	return "workflow_definitions"
}

// TableName overrides the table name for WorkflowTemplate
func (WorkflowTemplate) TableName() string {
	return "workflow_templates"
}

// TableName overrides the table name for WorkflowExecution
func (WorkflowExecution) TableName() string {
	return "workflow_executions"
}

// TableName overrides the table name for StepExecution
func (StepExecution) TableName() string {
	return "step_executions"
}

// TableName overrides the table name for ExecutionMetrics
func (ExecutionMetrics) TableName() string {
	return "execution_metrics"
}

// TableName overrides the table name for ExecutionEvent
func (ExecutionEvent) TableName() string {
	return "execution_events"
}

// InstanceRecord represents an instance record in the database
type InstanceRecord struct {
	ID           string          `gorm:"primaryKey;type:text"`
	Name         string          `gorm:"type:text;not null"`
	Version      string          `gorm:"type:text"`
	OS           string          `gorm:"type:text"`
	Architecture string          `gorm:"type:text"`
	Hostname     string          `gorm:"type:text"`
	IPAddress    string          `gorm:"type:text"`
	Port         int             `gorm:"type:integer"`
	Labels       json.RawMessage `gorm:"type:jsonb"` // JSON string of labels
	Capabilities json.RawMessage `gorm:"type:jsonb"` // JSON string of capabilities
	Status       string          `gorm:"type:text"`
	LastSeen     time.Time       `gorm:"type:timestamp"`
	StartedAt    time.Time       `gorm:"type:timestamp"`
	Metadata     json.RawMessage `gorm:"type:jsonb"` // JSON string of metadata
	Resources    json.RawMessage `gorm:"type:jsonb"` // JSON string of resource info
	CreatedAt    time.Time       `gorm:"autoCreateTime"`
	UpdatedAt    time.Time       `gorm:"autoUpdateTime"`
}

// TableName returns the table name for InstanceRecord
func (InstanceRecord) TableName() string {
	return "workflow_instances"
}

// BeforeSave handles JSON marshaling before saving to database
func (w *WorkflowDefinition) BeforeSave(tx *gorm.DB) error {
	if len(w.Steps) > 0 {
		data, err := json.Marshal(w.Steps)
		if err != nil {
			return err
		}
		w.StepsJSON = json.RawMessage(data)
	}

	if len(w.Parameters) > 0 {
		data, err := json.Marshal(w.Parameters)
		if err != nil {
			return err
		}
		w.ParamsJSON = json.RawMessage(data)
	}

	if len(w.Triggers) > 0 {
		data, err := json.Marshal(w.Triggers)
		if err != nil {
			return err
		}
		w.TriggersJSON = json.RawMessage(data)
	}

	if len(w.Variables) > 0 {
		data, err := json.Marshal(w.Variables)
		if err != nil {
			return err
		}
		w.VarsJSON = json.RawMessage(data)
	}

	if len(w.Tags) > 0 {
		data, err := json.Marshal(w.Tags)
		if err != nil {
			return err
		}
		w.TagsJSON = json.RawMessage(data)
	}

	return nil
}

// AfterFind handles JSON unmarshaling after loading from database
func (w *WorkflowDefinition) AfterFind() error {
	if len(w.StepsJSON) > 0 {
		if err := json.Unmarshal(w.StepsJSON, &w.Steps); err != nil {
			return err
		}
	}

	if len(w.ParamsJSON) > 0 {
		if err := json.Unmarshal(w.ParamsJSON, &w.Parameters); err != nil {
			return err
		}
	}

	if len(w.TriggersJSON) > 0 {
		if err := json.Unmarshal(w.TriggersJSON, &w.Triggers); err != nil {
			return err
		}
	}

	if len(w.VarsJSON) > 0 {
		if err := json.Unmarshal(w.VarsJSON, &w.Variables); err != nil {
			return err
		}
	}

	if len(w.TagsJSON) > 0 {
		if err := json.Unmarshal(w.TagsJSON, &w.Tags); err != nil {
			return err
		}
	}

	return nil
}

// BeforeSave handles JSON marshaling for WorkflowTemplate before saving to database
func (t *WorkflowTemplate) BeforeSave(tx *gorm.DB) error {
	if len(t.Steps) > 0 {
		data, err := json.Marshal(t.Steps)
		if err != nil {
			return err
		}
		t.StepsJSON = json.RawMessage(data)
	}

	if len(t.Parameters) > 0 {
		data, err := json.Marshal(t.Parameters)
		if err != nil {
			return err
		}
		t.ParamsJSON = json.RawMessage(data)
	}

	if len(t.Variables) > 0 {
		data, err := json.Marshal(t.Variables)
		if err != nil {
			return err
		}
		t.VarsJSON = json.RawMessage(data)
	}

	if len(t.Tags) > 0 {
		data, err := json.Marshal(t.Tags)
		if err != nil {
			return err
		}
		t.TagsJSON = json.RawMessage(data)
	}

	if len(t.Requirements) > 0 {
		data, err := json.Marshal(t.Requirements)
		if err != nil {
			return err
		}
		t.RequirementsJSON = json.RawMessage(data)
	}

	if len(t.Screenshots) > 0 {
		data, err := json.Marshal(t.Screenshots)
		if err != nil {
			return err
		}
		t.ScreenshotsJSON = json.RawMessage(data)
	}

	return nil
}

// AfterFind handles JSON unmarshaling for WorkflowTemplate after loading from database
func (t *WorkflowTemplate) AfterFind() error {
	if len(t.StepsJSON) > 0 {
		if err := json.Unmarshal(t.StepsJSON, &t.Steps); err != nil {
			return err
		}
	}

	if len(t.ParamsJSON) > 0 {
		if err := json.Unmarshal(t.ParamsJSON, &t.Parameters); err != nil {
			return err
		}
	}

	if len(t.VarsJSON) > 0 {
		if err := json.Unmarshal(t.VarsJSON, &t.Variables); err != nil {
			return err
		}
	}

	if len(t.TagsJSON) > 0 {
		if err := json.Unmarshal(t.TagsJSON, &t.Tags); err != nil {
			return err
		}
	}

	if len(t.RequirementsJSON) > 0 {
		if err := json.Unmarshal(t.RequirementsJSON, &t.Requirements); err != nil {
			return err
		}
	}

	if len(t.ScreenshotsJSON) > 0 {
		if err := json.Unmarshal(t.ScreenshotsJSON, &t.Screenshots); err != nil {
			return err
		}
	}

	return nil
}

// BeforeSave handles JSON marshaling for WorkflowExecution before saving to database
func (e *WorkflowExecution) BeforeSave(tx *gorm.DB) error {
	if len(e.Parameters) > 0 {
		data, err := json.Marshal(e.Parameters)
		if err != nil {
			return err
		}
		e.ParamsJSON = json.RawMessage(data)
	}

	if len(e.Variables) > 0 {
		data, err := json.Marshal(e.Variables)
		if err != nil {
			return err
		}
		e.VarsJSON = json.RawMessage(data)
	}

	if len(e.SecretMapping) > 0 {
		data, err := json.Marshal(e.SecretMapping)
		if err != nil {
			return err
		}
		e.SecretMapJSON = json.RawMessage(data)
	}

	if len(e.TriggerData) > 0 {
		data, err := json.Marshal(e.TriggerData)
		if err != nil {
			return err
		}
		e.TriggerJSON = json.RawMessage(data)
	}

	return nil
}

// AfterFind handles JSON unmarshaling for WorkflowExecution after loading from database
func (e *WorkflowExecution) AfterFind() error {
	if len(e.ParamsJSON) > 0 {
		if err := json.Unmarshal(e.ParamsJSON, &e.Parameters); err != nil {
			return err
		}
	}

	if len(e.VarsJSON) > 0 {
		if err := json.Unmarshal(e.VarsJSON, &e.Variables); err != nil {
			return err
		}
	}

	if len(e.SecretMapJSON) > 0 {
		if err := json.Unmarshal(e.SecretMapJSON, &e.SecretMapping); err != nil {
			return err
		}
	}

	if len(e.TriggerJSON) > 0 {
		if err := json.Unmarshal(e.TriggerJSON, &e.TriggerData); err != nil {
			return err
		}
	}

	return nil
}
