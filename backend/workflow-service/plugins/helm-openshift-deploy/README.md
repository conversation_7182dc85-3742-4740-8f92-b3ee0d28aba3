# Helm OpenShift Deploy Plugin

A comprehensive Helm deployment plugin for OpenShift clusters with Bitbucket integration.

## Overview

This plugin enables automated deployment of Helm charts to OpenShift clusters by:
1. Cloning Helm charts from Bitbucket repositories
2. Authenticating with OpenShift clusters
3. Installing or upgrading Helm releases
4. Providing detailed deployment status and resource information

## Features

### **🚀 Core Deployment Features**
- ✅ **OpenShift Integration**: Native OpenShift authentication and project management
- ✅ **Bitbucket Integration**: Clone Helm charts directly from Bitbucket repositories
- ✅ **Environment-Specific Values**: Support for different values files (dev, qa, prod)
- ✅ **Install/Upgrade Logic**: Automatically detects and performs install or upgrade
- ✅ **Dry Run Support**: Validate deployments without executing
- ✅ **Resource Tracking**: Lists all deployed Kubernetes resources
- ✅ **Rollback Support**: Rollback to previous release versions
- ✅ **Timeout Configuration**: Configurable Helm operation timeouts
- ✅ **Extra Values**: Override values via parameters

### **🔐 Security & Configuration**
- ✅ **Dynamic Configuration Forms**: User-friendly forms instead of manual JSON
- ✅ **Smart Field Detection**: Automatically detects sensitive fields (passwords, tokens)
- ✅ **Secrets Integration**: Full integration with Deploy Orchestrator secrets service
- ✅ **Secret Selection**: Choose from available secrets or enter values manually
- ✅ **Password Visibility Toggle**: Show/hide password fields for manual entry
- ✅ **Real-time Validation**: Validation with helpful error messages and patterns

### **🎯 User Experience**
- ✅ **Intuitive UI**: Clean, responsive configuration interface
- ✅ **Field Labels**: Converts technical field names to user-friendly labels
- ✅ **Help Text**: Contextual help and examples for each field
- ✅ **Error Recovery**: Graceful handling of configuration errors
- ✅ **JSON Preview**: Optional JSON preview for advanced users
- ✅ **Hot Reload**: Plugin hot reload support for development

## Configuration

### Required Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `openshift_api_url` | string | OpenShift cluster API URL | `https://api.cluster.example.com:6443` |
| `openshift_project` | string | Target OpenShift project/namespace | `my-app-dev` |
| `username` | string | OpenShift username | `developer` |
| `password` | string | OpenShift password | `secret123` |
| `bitbucket_repo_url` | string | Bitbucket repository URL | `https://bitbucket.org/myorg/helm-charts.git` |
| `chart_path` | string | Path to Helm chart from repo root | `charts/my-application` |
| `values_path` | string | Path to values file | `values-dev.yaml` |

### Optional Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `release_name` | string | chart name | Helm release name |
| `helm_timeout` | string | `300s` | Helm operation timeout |
| `dry_run` | boolean | `false` | Perform dry run without deploying |
| `extra_values` | map | `{}` | Additional Helm values to override |

## Usage Examples

### Basic Deployment

```json
{
  "openshift_api_url": "https://api.cluster.example.com:6443",
  "openshift_project": "my-app-dev",
  "username": "developer",
  "password": "secret123",
  "bitbucket_repo_url": "https://bitbucket.org/myorg/helm-charts.git",
  "chart_path": "charts/my-application",
  "values_path": "values-dev.yaml"
}
```

### Environment-Specific Deployment

```json
{
  "openshift_api_url": "https://api.cluster-qa.example.com:6443",
  "openshift_project": "my-app-qa",
  "username": "qa-deployer",
  "password": "qa-secret",
  "bitbucket_repo_url": "https://bitbucket.org/myorg/helm-charts.git",
  "chart_path": "charts/my-application",
  "values_path": "values-qa.yaml",
  "release_name": "my-app-qa",
  "helm_timeout": "600s",
  "extra_values": {
    "image.tag": "v1.2.3",
    "replicas": "3"
  }
}
```

### Dry Run Validation

```json
{
  "openshift_api_url": "https://api.cluster.example.com:6443",
  "openshift_project": "my-app-prod",
  "username": "prod-deployer",
  "password": "prod-secret",
  "bitbucket_repo_url": "https://bitbucket.org/myorg/helm-charts.git",
  "chart_path": "charts/my-application",
  "values_path": "values-prod.yaml",
  "dry_run": true
}
```

## Supported Operations

### `deploy:helm`
Main deployment operation that performs install or upgrade.

**Parameters**: All configuration parameters
**Returns**: Detailed deployment result with status and resources

### `helm:status`
Get the current status of a Helm release.

**Parameters**:
- `release_name` (required): Name of the Helm release
- `namespace` (optional): Kubernetes namespace

**Returns**: Release status information

### `helm:rollback`
Rollback a Helm release to a previous version.

**Parameters**:
- `release_name` (required): Name of the Helm release
- `namespace` (optional): Kubernetes namespace
- `revision` (optional): Specific revision to rollback to

**Returns**: Rollback operation result

### `deploy:validate`
Validate deployment parameters without executing.

**Parameters**: All configuration parameters
**Returns**: Validation result with any errors

## Prerequisites

The following tools must be installed and available in PATH:

- **helm**: Helm CLI (v3.x)
- **oc**: OpenShift CLI
- **git**: Git CLI for repository cloning

## Repository Structure

Your Bitbucket repository should be structured like:

```
helm-charts/
├── charts/
│   └── my-application/
│       ├── Chart.yaml
│       ├── values.yaml
│       └── templates/
│           ├── deployment.yaml
│           ├── service.yaml
│           └── ...
├── values-dev.yaml
├── values-qa.yaml
├── values-prod.yaml
└── README.md
```

## Environment-Specific Values

Use different values files for different environments:

- `values-dev.yaml`: Development environment settings
- `values-qa.yaml`: QA environment settings
- `values-prod.yaml`: Production environment settings

Example values file structure:

```yaml
# values-dev.yaml
image:
  repository: myapp
  tag: "latest"
  pullPolicy: Always

replicas: 1

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

ingress:
  enabled: true
  host: myapp-dev.example.com
```

## Error Handling

The plugin provides detailed error messages for common issues:

- **Authentication failures**: Invalid OpenShift credentials
- **Repository access**: Bitbucket repository not accessible
- **Path validation**: Chart or values file not found
- **Helm errors**: Deployment failures with full output
- **Tool availability**: Missing required CLI tools

## Security Considerations

- **Credentials**: Store OpenShift passwords securely using secrets management
- **TLS**: Plugin uses `--insecure-skip-tls-verify=true` for OpenShift login
- **Repository Access**: Ensure Bitbucket repository is accessible from deployment environment
- **RBAC**: Ensure OpenShift user has appropriate permissions for target project

## Integration with Deploy Orchestrator

This plugin integrates seamlessly with the Deploy Orchestrator workflow system:

1. **Variable Support**: All parameters can be overridden via workflow variables
2. **Secret Integration**: Sensitive values (passwords) can be injected from secrets service
3. **Monitoring**: Deployment status is tracked and monitored
4. **Audit Logging**: All operations are logged for audit purposes
5. **Permission Control**: Access controlled via RBAC system

## 📚 Documentation

For comprehensive documentation, see the [docs](./docs/) directory:

- **[Installation Guide](./docs/INSTALLATION.md)** - Complete installation and setup instructions
- **[Quick Start Guide](./docs/QUICK_START.md)** - Get started in 5 minutes
- **[Configuration Reference](./docs/CONFIGURATION.md)** - Complete configuration options
- **[API Reference](./docs/API.md)** - Full API documentation
- **[Deployment Guide](./docs/DEPLOYMENT.md)** - Deployment patterns and workflows
- **[Best Practices](./docs/BEST_PRACTICES.md)** - Production-ready best practices
- **[Use Cases](./docs/USE_CASES.md)** - Real-world examples and scenarios
- **[Troubleshooting](./docs/TROUBLESHOOTING.md)** - Common issues and solutions

## 🚀 Quick Start

### **1. Build and Install**
```bash
cd plugins/helm-openshift-plugin

# Clean and build
make clean
make build

# Install to workflow service
make install

# Verify installation
ls -la ../../backend/workflow-service/plugins/helm-openshift-deploy/
```

### **2. Configure via Dynamic UI Form**
- Navigate to **Settings** → **Plugin Management** in Deploy Orchestrator
- Find `helm-openshift-deploy` plugin
- Click **Config** button
- Use the dynamic form with:
  - **Smart Field Detection**: Automatically detects password/secret fields
  - **Secrets Integration**: Select from available secrets or enter manually
  - **Validation**: Real-time validation with helpful error messages
  - **User-Friendly**: Converts technical names to readable labels

### **3. Configuration Options**
The plugin now supports both manual configuration and secrets integration:

**Manual Configuration:**
```json
{
  "openshift_api_url": "https://api.cluster.com:6443",
  "openshift_project": "my-app-dev",
  "username": "developer",
  "password": "manual-password",
  "bitbucket_repo_url": "https://bitbucket.org/org/charts.git",
  "chart_path": "charts/my-app",
  "values_path": "values-dev.yaml"
}
```

**With Secrets Integration:**
```json
{
  "openshift_api_url": "https://api.cluster.com:6443",
  "openshift_project": "my-app-prod",
  "username": "{{secret:openshift-username}}",
  "password": "{{secret:openshift-prod-password}}",
  "bitbucket_repo_url": "https://bitbucket.org/org/charts.git",
  "chart_path": "charts/my-app",
  "values_path": "values-prod.yaml"
}
```

### **4. Test Deployment**
```bash
curl -X POST http://localhost:8080/api/v1/plugins/helm-openshift-deploy/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "operation": "deploy:validate",
    "parameters": {
      "openshift_api_url": "https://api.cluster.com:6443",
      "openshift_project": "test-project",
      "username": "{{secret:openshift-username}}",
      "password": "{{secret:openshift-password}}",
      "bitbucket_repo_url": "https://bitbucket.org/org/charts.git",
      "chart_path": "charts/test-app",
      "values_path": "values-dev.yaml",
      "dry_run": true
    }
  }'
```

For detailed instructions, see the [Quick Start Guide](./docs/QUICK_START.md).
