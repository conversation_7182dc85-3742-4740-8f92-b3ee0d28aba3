package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/plugin"
)

// HelmOpenShiftPlugin implements Helm deployment to OpenShift clusters
type HelmOpenShiftPlugin struct {
	config *HelmOpenShiftConfig
}

// HelmOpenShiftConfig represents the plugin configuration
type HelmOpenShiftConfig struct {
	OpenShiftAPIURL  string `json:"openshift_api_url"`
	OpenShiftProject string `json:"openshift_project"`
	Username         string `json:"username"`
	Password         string `json:"password"`
	ReleaseName      string `json:"release_name"`
	HelmTimeout      string `json:"helm_timeout"`
	BitbucketRepoURL string `json:"bitbucket_repo_url"`
	ChartPath        string `json:"chart_path"`
	ValuesPath       string `json:"values_path"`
}

// DeploymentRequest represents a deployment request
type DeploymentRequest struct {
	OpenShiftAPIURL  string            `json:"openshift_api_url"`
	OpenShiftProject string            `json:"openshift_project"`
	Username         string            `json:"username"`
	Password         string            `json:"password"`
	ReleaseName      string            `json:"release_name"`
	HelmTimeout      string            `json:"helm_timeout"`
	BitbucketRepoURL string            `json:"bitbucket_repo_url"`
	ChartPath        string            `json:"chart_path"`
	ValuesPath       string            `json:"values_path"`
	ExtraValues      map[string]string `json:"extra_values"`
	DryRun           bool              `json:"dry_run"`
}

// DeploymentResult represents the result of a deployment
type DeploymentResult struct {
	Success       bool                 `json:"success"`
	ReleaseName   string               `json:"release_name"`
	Namespace     string               `json:"namespace"`
	Status        string               `json:"status"`
	Revision      int                  `json:"revision"`
	LastDeployed  string               `json:"last_deployed"`
	HelmOutput    string               `json:"helm_output"`
	Error         string               `json:"error,omitempty"`
	Resources     []KubernetesResource `json:"resources,omitempty"`
	ExecutionTime string               `json:"execution_time"`
}

// KubernetesResource represents a deployed Kubernetes resource
type KubernetesResource struct {
	Kind      string `json:"kind"`
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
	Status    string `json:"status"`
}

// NewHelmOpenShiftPlugin creates a new Helm OpenShift plugin instance
func NewHelmOpenShiftPlugin() *HelmOpenShiftPlugin {
	return &HelmOpenShiftPlugin{}
}

// GetMetadata returns plugin metadata
func (p *HelmOpenShiftPlugin) GetMetadata() plugin.Metadata {
	return plugin.Metadata{
		Name:        "helm-openshift-deploy",
		Version:     "1.0.0",
		Description: "Helm deployment plugin for OpenShift clusters with Bitbucket integration",
		Author:      "Deploy Orchestrator Team",
		Type:        "deployment-provider",
		Capabilities: []string{
			"deploy:helm",
			"deploy:openshift",
			"deploy:kubernetes",
			"git:clone",
			"helm:install",
			"helm:upgrade",
			"helm:status",
			"helm:rollback",
		},
		ConfigSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"openshift_api_url": map[string]interface{}{
					"type":        "string",
					"description": "OpenShift API URL (e.g., https://api.cluster.example.com:6443)",
					"pattern":     "^https?://.*",
				},
				"openshift_project": map[string]interface{}{
					"type":        "string",
					"description": "OpenShift project/namespace name",
					"pattern":     "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$",
				},
				"username": map[string]interface{}{
					"type":        "string",
					"description": "OpenShift username",
				},
				"password": map[string]interface{}{
					"type":        "string",
					"description": "OpenShift password",
					"sensitive":   true,
				},
				"release_name": map[string]interface{}{
					"type":        "string",
					"description": "Helm release name (optional, defaults to chart name)",
					"pattern":     "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$",
				},
				"helm_timeout": map[string]interface{}{
					"type":        "string",
					"default":     "300s",
					"description": "Helm operation timeout (e.g., 300s, 5m)",
				},
				"bitbucket_repo_url": map[string]interface{}{
					"type":        "string",
					"description": "Bitbucket repository URL containing Helm chart",
					"pattern":     "^https://.*\\.git$",
				},
				"chart_path": map[string]interface{}{
					"type":        "string",
					"description": "Path to Helm chart from repository root (e.g., charts/myapp)",
				},
				"values_path": map[string]interface{}{
					"type":        "string",
					"description": "Path to values file (e.g., values-dev.yaml, values-qa.yaml)",
				},
			},
			"required": []string{
				"openshift_api_url",
				"openshift_project",
				"username",
				"password",
				"bitbucket_repo_url",
				"chart_path",
				"values_path",
			},
		},
		Variables: []plugin.VariableDefinition{
			{
				Name:        "OPENSHIFT_API_URL",
				Type:        "string",
				Description: "OpenShift cluster API URL",
				Required:    true,
			},
			{
				Name:        "OPENSHIFT_PROJECT",
				Type:        "string",
				Description: "Target OpenShift project/namespace",
				Required:    true,
			},
			{
				Name:        "OPENSHIFT_USERNAME",
				Type:        "string",
				Description: "OpenShift username for authentication",
				Required:    true,
			},
			{
				Name:        "OPENSHIFT_PASSWORD",
				Type:        "string",
				Description: "OpenShift password for authentication",
				Required:    true,
				Sensitive:   true,
			},
			{
				Name:         "RELEASE_NAME",
				Type:         "string",
				Description:  "Helm release name",
				DefaultValue: "",
				Required:     false,
			},
			{
				Name:         "HELM_TIMEOUT",
				Type:         "string",
				Description:  "Helm operation timeout",
				DefaultValue: "300s",
				Required:     false,
				Validation: &plugin.Validation{
					Pattern: "^[0-9]+(s|m|h)$",
				},
			},
			{
				Name:        "BITBUCKET_REPO_URL",
				Type:        "string",
				Description: "Bitbucket repository URL containing Helm chart",
				Required:    true,
			},
			{
				Name:        "CHART_PATH",
				Type:        "string",
				Description: "Path to Helm chart from repository root",
				Required:    true,
			},
			{
				Name:        "VALUES_PATH",
				Type:        "string",
				Description: "Path to values file for environment-specific configuration",
				Required:    true,
			},
			{
				Name:         "DRY_RUN",
				Type:         "boolean",
				Description:  "Perform a dry run without actually deploying",
				DefaultValue: false,
				Required:     false,
			},
			{
				Name:         "FORCE_UPGRADE",
				Type:         "boolean",
				Description:  "Force upgrade even if no changes detected",
				DefaultValue: false,
				Required:     false,
			},
		},
	}
}

// Initialize initializes the plugin with configuration
func (p *HelmOpenShiftPlugin) Initialize(config map[string]interface{}) error {
	configBytes, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	var helmConfig HelmOpenShiftConfig
	if err := json.Unmarshal(configBytes, &helmConfig); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Set defaults
	if helmConfig.HelmTimeout == "" {
		helmConfig.HelmTimeout = "300s"
	}

	// Validate required fields
	if helmConfig.OpenShiftAPIURL == "" {
		return fmt.Errorf("openshift_api_url is required")
	}
	if helmConfig.OpenShiftProject == "" {
		return fmt.Errorf("openshift_project is required")
	}
	if helmConfig.Username == "" {
		return fmt.Errorf("username is required")
	}
	if helmConfig.Password == "" {
		return fmt.Errorf("password is required")
	}
	if helmConfig.BitbucketRepoURL == "" {
		return fmt.Errorf("bitbucket_repo_url is required")
	}
	if helmConfig.ChartPath == "" {
		return fmt.Errorf("chart_path is required")
	}
	if helmConfig.ValuesPath == "" {
		return fmt.Errorf("values_path is required")
	}

	p.config = &helmConfig
	log.Printf("Helm OpenShift plugin initialized for cluster: %s, project: %s",
		p.config.OpenShiftAPIURL, p.config.OpenShiftProject)
	return nil
}

// Execute executes a plugin operation
func (p *HelmOpenShiftPlugin) Execute(ctx context.Context, operation string, params map[string]interface{}) (map[string]interface{}, error) {
	switch operation {
	case "deploy:helm":
		return p.deployHelm(ctx, params)
	case "helm:status":
		return p.getHelmStatus(ctx, params)
	case "helm:rollback":
		return p.rollbackHelm(ctx, params)
	case "deploy:validate":
		return p.validateDeployment(ctx, params)
	default:
		return nil, fmt.Errorf("unsupported operation: %s", operation)
	}
}

// Cleanup cleans up plugin resources
func (p *HelmOpenShiftPlugin) Cleanup() error {
	log.Println("Helm OpenShift plugin cleanup completed")
	return nil
}

// Health returns plugin health status
func (p *HelmOpenShiftPlugin) Health() plugin.HealthStatus {
	if p.config == nil {
		return plugin.HealthStatus{
			Status:  "unhealthy",
			Message: "Plugin not initialized",
		}
	}

	// Check if required tools are available
	if err := p.checkRequiredTools(); err != nil {
		return plugin.HealthStatus{
			Status:  "unhealthy",
			Message: fmt.Sprintf("Required tools not available: %v", err),
		}
	}

	return plugin.HealthStatus{
		Status:  "healthy",
		Message: "Helm OpenShift plugin is operational",
	}
}

// Plugin entry point
func main() {
	plugin.Serve(NewHelmOpenShiftPlugin())
}

// deployHelm performs the main Helm deployment operation
func (p *HelmOpenShiftPlugin) deployHelm(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error) {
	startTime := time.Now()

	// Extract deployment request from parameters
	request, err := p.extractDeploymentRequest(params)
	if err != nil {
		return nil, fmt.Errorf("failed to extract deployment request: %w", err)
	}

	// Create temporary directory for Git operations
	tempDir, err := os.MkdirTemp("", "helm-deploy-*")
	if err != nil {
		return nil, fmt.Errorf("failed to create temp directory: %w", err)
	}
	defer os.RemoveAll(tempDir)

	log.Printf("Starting Helm deployment: release=%s, namespace=%s",
		request.ReleaseName, request.OpenShiftProject)

	// Step 1: Clone the Bitbucket repository
	repoDir := filepath.Join(tempDir, "repo")
	if err := p.cloneRepository(ctx, request.BitbucketRepoURL, repoDir); err != nil {
		return nil, fmt.Errorf("failed to clone repository: %w", err)
	}

	// Step 2: Login to OpenShift
	if err := p.loginToOpenShift(ctx, request); err != nil {
		return nil, fmt.Errorf("failed to login to OpenShift: %w", err)
	}

	// Step 3: Switch to the target project
	if err := p.switchToProject(ctx, request.OpenShiftProject); err != nil {
		return nil, fmt.Errorf("failed to switch to project: %w", err)
	}

	// Step 4: Prepare Helm chart and values
	chartPath := filepath.Join(repoDir, request.ChartPath)
	valuesPath := filepath.Join(repoDir, request.ValuesPath)

	if err := p.validatePaths(chartPath, valuesPath); err != nil {
		return nil, fmt.Errorf("path validation failed: %w", err)
	}

	// Step 5: Check if release exists
	releaseExists, err := p.checkReleaseExists(ctx, request.ReleaseName, request.OpenShiftProject)
	if err != nil {
		return nil, fmt.Errorf("failed to check release existence: %w", err)
	}

	// Step 6: Perform Helm deployment
	var result *DeploymentResult
	if releaseExists {
		log.Printf("Release %s exists, performing upgrade", request.ReleaseName)
		result, err = p.upgradeRelease(ctx, request, chartPath, valuesPath)
	} else {
		log.Printf("Release %s does not exist, performing install", request.ReleaseName)
		result, err = p.installRelease(ctx, request, chartPath, valuesPath)
	}

	if err != nil {
		return nil, fmt.Errorf("deployment failed: %w", err)
	}

	// Step 7: Get deployment status and resources
	if err := p.enrichResultWithStatus(ctx, result, request); err != nil {
		log.Printf("Warning: failed to enrich result with status: %v", err)
	}

	result.ExecutionTime = time.Since(startTime).String()

	return map[string]interface{}{
		"deployment_result": result,
	}, nil
}

// cloneRepository clones the repository using the Bitbucket plugin
func (p *HelmOpenShiftPlugin) cloneRepository(ctx context.Context, repoURL, targetDir string) error {
	log.Printf("Cloning repository: %s", repoURL)

	// TODO: Use Bitbucket plugin for Git operations
	// For now, fallback to direct git clone
	cmd := exec.CommandContext(ctx, "git", "clone", "--depth", "1", repoURL, targetDir)

	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("git clone failed: %w, stderr: %s", err, stderr.String())
	}

	log.Printf("Repository cloned successfully to: %s", targetDir)
	return nil
}

// loginToOpenShift logs into the OpenShift cluster
func (p *HelmOpenShiftPlugin) loginToOpenShift(ctx context.Context, request *DeploymentRequest) error {
	log.Printf("Logging into OpenShift cluster: %s", request.OpenShiftAPIURL)

	cmd := exec.CommandContext(ctx, "oc", "login",
		request.OpenShiftAPIURL,
		"-u", request.Username,
		"-p", request.Password,
		"--insecure-skip-tls-verify=true")

	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("oc login failed: %w, stderr: %s", err, stderr.String())
	}

	log.Printf("Successfully logged into OpenShift")
	return nil
}

// switchToProject switches to the target OpenShift project
func (p *HelmOpenShiftPlugin) switchToProject(ctx context.Context, project string) error {
	log.Printf("Switching to OpenShift project: %s", project)

	cmd := exec.CommandContext(ctx, "oc", "project", project)

	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("oc project failed: %w, stderr: %s", err, stderr.String())
	}

	log.Printf("Successfully switched to project: %s", project)
	return nil
}

// validatePaths validates that chart and values paths exist
func (p *HelmOpenShiftPlugin) validatePaths(chartPath, valuesPath string) error {
	// Check if chart directory exists
	if _, err := os.Stat(chartPath); os.IsNotExist(err) {
		return fmt.Errorf("chart path does not exist: %s", chartPath)
	}

	// Check if Chart.yaml exists in chart directory
	chartYaml := filepath.Join(chartPath, "Chart.yaml")
	if _, err := os.Stat(chartYaml); os.IsNotExist(err) {
		return fmt.Errorf("Chart.yaml not found in chart path: %s", chartPath)
	}

	// Check if values file exists
	if _, err := os.Stat(valuesPath); os.IsNotExist(err) {
		return fmt.Errorf("values file does not exist: %s", valuesPath)
	}

	log.Printf("Path validation successful - chart: %s, values: %s", chartPath, valuesPath)
	return nil
}

// checkReleaseExists checks if a Helm release already exists
func (p *HelmOpenShiftPlugin) checkReleaseExists(ctx context.Context, releaseName, namespace string) (bool, error) {
	cmd := exec.CommandContext(ctx, "helm", "status", releaseName, "-n", namespace)

	err := cmd.Run()
	if err != nil {
		// If helm status fails, the release doesn't exist
		return false, nil
	}

	return true, nil
}

// extractDeploymentRequest extracts deployment request from parameters
func (p *HelmOpenShiftPlugin) extractDeploymentRequest(params map[string]interface{}) (*DeploymentRequest, error) {
	request := &DeploymentRequest{}

	// Use config defaults and override with parameters
	if p.config != nil {
		request.OpenShiftAPIURL = p.config.OpenShiftAPIURL
		request.OpenShiftProject = p.config.OpenShiftProject
		request.Username = p.config.Username
		request.Password = p.config.Password
		request.ReleaseName = p.config.ReleaseName
		request.HelmTimeout = p.config.HelmTimeout
		request.BitbucketRepoURL = p.config.BitbucketRepoURL
		request.ChartPath = p.config.ChartPath
		request.ValuesPath = p.config.ValuesPath
	}

	// Override with parameters if provided
	if val, ok := params["openshift_api_url"].(string); ok && val != "" {
		request.OpenShiftAPIURL = val
	}
	if val, ok := params["openshift_project"].(string); ok && val != "" {
		request.OpenShiftProject = val
	}
	if val, ok := params["username"].(string); ok && val != "" {
		request.Username = val
	}
	if val, ok := params["password"].(string); ok && val != "" {
		request.Password = val
	}
	if val, ok := params["release_name"].(string); ok && val != "" {
		request.ReleaseName = val
	}
	if val, ok := params["helm_timeout"].(string); ok && val != "" {
		request.HelmTimeout = val
	}
	if val, ok := params["bitbucket_repo_url"].(string); ok && val != "" {
		request.BitbucketRepoURL = val
	}
	if val, ok := params["chart_path"].(string); ok && val != "" {
		request.ChartPath = val
	}
	if val, ok := params["values_path"].(string); ok && val != "" {
		request.ValuesPath = val
	}
	if val, ok := params["dry_run"].(bool); ok {
		request.DryRun = val
	}
	if val, ok := params["extra_values"].(map[string]string); ok {
		request.ExtraValues = val
	}

	// Set default release name if not provided
	if request.ReleaseName == "" {
		// Extract chart name from chart path
		chartName := filepath.Base(request.ChartPath)
		request.ReleaseName = chartName
	}

	// Set default timeout if not provided
	if request.HelmTimeout == "" {
		request.HelmTimeout = "300s"
	}

	// Validate required fields
	if request.OpenShiftAPIURL == "" {
		return nil, fmt.Errorf("openshift_api_url is required")
	}
	if request.OpenShiftProject == "" {
		return nil, fmt.Errorf("openshift_project is required")
	}
	if request.Username == "" {
		return nil, fmt.Errorf("username is required")
	}
	if request.Password == "" {
		return nil, fmt.Errorf("password is required")
	}
	if request.BitbucketRepoURL == "" {
		return nil, fmt.Errorf("bitbucket_repo_url is required")
	}
	if request.ChartPath == "" {
		return nil, fmt.Errorf("chart_path is required")
	}
	if request.ValuesPath == "" {
		return nil, fmt.Errorf("values_path is required")
	}

	return request, nil
}

// installRelease installs a new Helm release
func (p *HelmOpenShiftPlugin) installRelease(ctx context.Context, request *DeploymentRequest, chartPath, valuesPath string) (*DeploymentResult, error) {
	log.Printf("Installing Helm release: %s", request.ReleaseName)

	args := []string{
		"install", request.ReleaseName, chartPath,
		"-n", request.OpenShiftProject,
		"-f", valuesPath,
		"--timeout", request.HelmTimeout,
		"--wait",
	}

	// Add dry-run flag if requested
	if request.DryRun {
		args = append(args, "--dry-run")
	}

	// Add extra values
	for key, value := range request.ExtraValues {
		args = append(args, "--set", fmt.Sprintf("%s=%s", key, value))
	}

	cmd := exec.CommandContext(ctx, "helm", args...)

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err := cmd.Run()

	result := &DeploymentResult{
		Success:      err == nil,
		ReleaseName:  request.ReleaseName,
		Namespace:    request.OpenShiftProject,
		HelmOutput:   stdout.String(),
		LastDeployed: time.Now().Format(time.RFC3339),
	}

	if err != nil {
		result.Error = fmt.Sprintf("helm install failed: %v, stderr: %s", err, stderr.String())
		return result, fmt.Errorf(result.Error)
	}

	result.Status = "deployed"
	result.Revision = 1

	log.Printf("Helm release %s installed successfully", request.ReleaseName)
	return result, nil
}

// upgradeRelease upgrades an existing Helm release
func (p *HelmOpenShiftPlugin) upgradeRelease(ctx context.Context, request *DeploymentRequest, chartPath, valuesPath string) (*DeploymentResult, error) {
	log.Printf("Upgrading Helm release: %s", request.ReleaseName)

	args := []string{
		"upgrade", request.ReleaseName, chartPath,
		"-n", request.OpenShiftProject,
		"-f", valuesPath,
		"--timeout", request.HelmTimeout,
		"--wait",
	}

	// Add dry-run flag if requested
	if request.DryRun {
		args = append(args, "--dry-run")
	}

	// Add extra values
	for key, value := range request.ExtraValues {
		args = append(args, "--set", fmt.Sprintf("%s=%s", key, value))
	}

	cmd := exec.CommandContext(ctx, "helm", args...)

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err := cmd.Run()

	result := &DeploymentResult{
		Success:      err == nil,
		ReleaseName:  request.ReleaseName,
		Namespace:    request.OpenShiftProject,
		HelmOutput:   stdout.String(),
		LastDeployed: time.Now().Format(time.RFC3339),
	}

	if err != nil {
		result.Error = fmt.Sprintf("helm upgrade failed: %v, stderr: %s", err, stderr.String())
		return result, fmt.Errorf(result.Error)
	}

	result.Status = "deployed"

	// Get revision number
	if revision, err := p.getRevisionNumber(ctx, request.ReleaseName, request.OpenShiftProject); err == nil {
		result.Revision = revision
	}

	log.Printf("Helm release %s upgraded successfully", request.ReleaseName)
	return result, nil
}

// getRevisionNumber gets the current revision number of a Helm release
func (p *HelmOpenShiftPlugin) getRevisionNumber(ctx context.Context, releaseName, namespace string) (int, error) {
	cmd := exec.CommandContext(ctx, "helm", "history", releaseName, "-n", namespace, "--max", "1", "-o", "json")

	var stdout bytes.Buffer
	cmd.Stdout = &stdout

	if err := cmd.Run(); err != nil {
		return 0, fmt.Errorf("failed to get helm history: %w", err)
	}

	var history []struct {
		Revision int `json:"revision"`
	}

	if err := json.Unmarshal(stdout.Bytes(), &history); err != nil {
		return 0, fmt.Errorf("failed to parse helm history: %w", err)
	}

	if len(history) == 0 {
		return 0, fmt.Errorf("no history found for release %s", releaseName)
	}

	return history[0].Revision, nil
}

// enrichResultWithStatus enriches the deployment result with additional status information
func (p *HelmOpenShiftPlugin) enrichResultWithStatus(ctx context.Context, result *DeploymentResult, request *DeploymentRequest) error {
	// Get Helm release status
	if err := p.getHelmReleaseStatus(ctx, result, request); err != nil {
		log.Printf("Warning: failed to get Helm release status: %v", err)
	}

	// Get deployed Kubernetes resources
	if err := p.getDeployedResources(ctx, result, request); err != nil {
		log.Printf("Warning: failed to get deployed resources: %v", err)
	}

	return nil
}

// getHelmReleaseStatus gets the current status of the Helm release
func (p *HelmOpenShiftPlugin) getHelmReleaseStatus(ctx context.Context, result *DeploymentResult, request *DeploymentRequest) error {
	cmd := exec.CommandContext(ctx, "helm", "status", request.ReleaseName, "-n", request.OpenShiftProject, "-o", "json")

	var stdout bytes.Buffer
	cmd.Stdout = &stdout

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to get helm status: %w", err)
	}

	var status struct {
		Info struct {
			Status       string `json:"status"`
			LastDeployed string `json:"last_deployed"`
		} `json:"info"`
		Version int `json:"version"`
	}

	if err := json.Unmarshal(stdout.Bytes(), &status); err != nil {
		return fmt.Errorf("failed to parse helm status: %w", err)
	}

	result.Status = status.Info.Status
	result.Revision = status.Version
	if status.Info.LastDeployed != "" {
		result.LastDeployed = status.Info.LastDeployed
	}

	return nil
}

// getDeployedResources gets the list of deployed Kubernetes resources
func (p *HelmOpenShiftPlugin) getDeployedResources(ctx context.Context, result *DeploymentResult, request *DeploymentRequest) error {
	cmd := exec.CommandContext(ctx, "helm", "get", "manifest", request.ReleaseName, "-n", request.OpenShiftProject)

	var stdout bytes.Buffer
	cmd.Stdout = &stdout

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to get helm manifest: %w", err)
	}

	// Parse YAML manifests to extract resource information
	manifests := strings.Split(stdout.String(), "---")
	var resources []KubernetesResource

	for _, manifest := range manifests {
		manifest = strings.TrimSpace(manifest)
		if manifest == "" {
			continue
		}

		// Simple parsing to extract kind and name
		lines := strings.Split(manifest, "\n")
		var kind, name string

		for _, line := range lines {
			line = strings.TrimSpace(line)
			if strings.HasPrefix(line, "kind:") {
				kind = strings.TrimSpace(strings.TrimPrefix(line, "kind:"))
			}
			if strings.HasPrefix(line, "name:") && strings.Contains(line, "metadata:") == false {
				// Look for name under metadata
				continue
			}
			if strings.HasPrefix(line, "name:") && kind != "" {
				name = strings.TrimSpace(strings.TrimPrefix(line, "name:"))
				break
			}
		}

		if kind != "" && name != "" {
			resources = append(resources, KubernetesResource{
				Kind:      kind,
				Name:      name,
				Namespace: request.OpenShiftProject,
				Status:    "deployed",
			})
		}
	}

	result.Resources = resources
	return nil
}

// getHelmStatus gets the status of a Helm release
func (p *HelmOpenShiftPlugin) getHelmStatus(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error) {
	releaseName, ok := params["release_name"].(string)
	if !ok {
		return nil, fmt.Errorf("release_name parameter required")
	}

	namespace, ok := params["namespace"].(string)
	if !ok {
		namespace = p.config.OpenShiftProject
	}

	cmd := exec.CommandContext(ctx, "helm", "status", releaseName, "-n", namespace, "-o", "json")

	var stdout bytes.Buffer
	cmd.Stdout = &stdout

	if err := cmd.Run(); err != nil {
		return map[string]interface{}{
			"exists": false,
			"error":  err.Error(),
		}, nil
	}

	var status map[string]interface{}
	if err := json.Unmarshal(stdout.Bytes(), &status); err != nil {
		return nil, fmt.Errorf("failed to parse helm status: %w", err)
	}

	return map[string]interface{}{
		"exists": true,
		"status": status,
	}, nil
}

// rollbackHelm performs a Helm rollback operation
func (p *HelmOpenShiftPlugin) rollbackHelm(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error) {
	releaseName, ok := params["release_name"].(string)
	if !ok {
		return nil, fmt.Errorf("release_name parameter required")
	}

	namespace, ok := params["namespace"].(string)
	if !ok {
		namespace = p.config.OpenShiftProject
	}

	revision, _ := params["revision"].(int)

	args := []string{"rollback", releaseName}
	if revision > 0 {
		args = append(args, fmt.Sprintf("%d", revision))
	}
	args = append(args, "-n", namespace, "--wait")

	cmd := exec.CommandContext(ctx, "helm", args...)

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err := cmd.Run()

	result := map[string]interface{}{
		"success":      err == nil,
		"release_name": releaseName,
		"namespace":    namespace,
		"output":       stdout.String(),
	}

	if err != nil {
		result["error"] = fmt.Sprintf("helm rollback failed: %v, stderr: %s", err, stderr.String())
	}

	return result, nil
}

// validateDeployment validates deployment parameters without executing
func (p *HelmOpenShiftPlugin) validateDeployment(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error) {
	request, err := p.extractDeploymentRequest(params)
	if err != nil {
		return map[string]interface{}{
			"valid": false,
			"error": err.Error(),
		}, nil
	}

	// Perform basic validation
	validationErrors := []string{}

	// Check if required tools are available
	if err := p.checkRequiredTools(); err != nil {
		validationErrors = append(validationErrors, fmt.Sprintf("Required tools not available: %v", err))
	}

	// Validate URL format
	if !strings.HasPrefix(request.OpenShiftAPIURL, "https://") && !strings.HasPrefix(request.OpenShiftAPIURL, "http://") {
		validationErrors = append(validationErrors, "OpenShift API URL must start with http:// or https://")
	}

	// Validate timeout format
	if !strings.HasSuffix(request.HelmTimeout, "s") && !strings.HasSuffix(request.HelmTimeout, "m") && !strings.HasSuffix(request.HelmTimeout, "h") {
		validationErrors = append(validationErrors, "Helm timeout must end with s, m, or h")
	}

	result := map[string]interface{}{
		"valid":   len(validationErrors) == 0,
		"request": request,
	}

	if len(validationErrors) > 0 {
		result["errors"] = validationErrors
	}

	return result, nil
}

// checkRequiredTools checks if required tools are available
func (p *HelmOpenShiftPlugin) checkRequiredTools() error {
	tools := []string{"helm", "oc", "git"}

	for _, tool := range tools {
		if _, err := exec.LookPath(tool); err != nil {
			return fmt.Errorf("required tool '%s' not found in PATH", tool)
		}
	}

	return nil
}

// Ensure HelmOpenShiftPlugin implements the Plugin interface
var _ plugin.Plugin = (*HelmOpenShiftPlugin)(nil)
