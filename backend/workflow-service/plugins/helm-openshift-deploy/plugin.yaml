apiVersion: v1
kind: Plugin
metadata:
  name: helm-openshift-deploy
  version: "1.0.0"
  description: "Helm deployment plugin for OpenShift clusters with Bitbucket integration"
  author: "Deploy Orchestrator Team"
  tags:
    - helm
    - openshift
    - kubernetes
    - deployment
    - bitbucket
    - git

spec:
  type: deployment-provider
  runtime: binary
  entrypoint: helm-openshift-plugin
  # Alternative for Go plugin runtime:
  # runtime: go
  # entrypoint: plugin.go

  provider:
    type: deployment-provider
    capabilities:
      - deploy:helm
      - deploy:openshift
      - deploy:kubernetes
      - git:clone
      - helm:install
      - helm:upgrade
      - helm:status
      - helm:rollback

  hotReload:
    enabled: true
    watchPaths: ["*.go", "*.yaml"]
    debounceInterval: "2s"

  requirements:
    tools:
      - name: helm
        version: ">=3.0.0"
        description: "Helm CLI for Kubernetes package management"
      - name: oc
        version: ">=4.0.0"
        description: "OpenShift CLI for cluster interaction"
      - name: git
        version: ">=2.0.0"
        description: "Git CLI for repository operations"

  configuration:
    schema:
      type: object
      required:
        - openshift_api_url
        - openshift_project
        - username
        - password
        - bitbucket_repo_url
        - chart_path
        - values_path
      properties:
        openshift_api_url:
          type: string
          title: "OpenShift API URL"
          description: "OpenShift cluster API endpoint"
          pattern: "^https?://.*"
          examples:
            - "https://api.cluster.example.com:6443"
            - "https://openshift.company.com:8443"

        openshift_project:
          type: string
          title: "OpenShift Project"
          description: "Target OpenShift project/namespace"
          pattern: "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$"
          examples:
            - "my-app-dev"
            - "production-workloads"

        username:
          type: string
          title: "OpenShift Username"
          description: "Username for OpenShift authentication"
          examples:
            - "developer"
            - "service-account"

        password:
          type: password
          title: "OpenShift Password"
          description: "Password for OpenShift authentication"
          sensitive: true
          examples:
            - "secret123"

        release_name:
          type: string
          title: "Helm Release Name"
          description: "Name for the Helm release (optional, defaults to chart name)"
          pattern: "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$"
          examples:
            - "my-application"
            - "web-frontend"

        helm_timeout:
          type: string
          title: "Helm Timeout"
          description: "Timeout for Helm operations"
          default: "300s"
          pattern: "^[0-9]+(s|m|h)$"
          examples:
            - "300s"
            - "5m"
            - "1h"

        bitbucket_repo_url:
          type: string
          title: "Bitbucket Repository URL"
          description: "URL of the Bitbucket repository containing Helm charts"
          pattern: "^https://.*\\.git$"
          examples:
            - "https://bitbucket.org/myorg/helm-charts.git"
            - "https://company.bitbucket.org/projects/HELM/repos/charts.git"

        chart_path:
          type: string
          title: "Chart Path"
          description: "Path to Helm chart from repository root"
          examples:
            - "charts/my-application"
            - "helm/web-frontend"
            - "deployments/microservice"

        values_path:
          type: string
          title: "Values File Path"
          description: "Path to values file for environment-specific configuration"
          examples:
            - "values-dev.yaml"
            - "values-qa.yaml"
            - "values-prod.yaml"
            - "environments/dev/values.yaml"

  variables:
    - name: OPENSHIFT_API_URL
      type: string
      description: "OpenShift cluster API URL"
      required: true

    - name: OPENSHIFT_PROJECT
      type: string
      description: "Target OpenShift project/namespace"
      required: true

    - name: OPENSHIFT_USERNAME
      type: string
      description: "OpenShift username for authentication"
      required: true

    - name: OPENSHIFT_PASSWORD
      type: string
      description: "OpenShift password for authentication"
      required: true
      sensitive: true

    - name: RELEASE_NAME
      type: string
      description: "Helm release name"
      required: false
      default: ""

    - name: HELM_TIMEOUT
      type: string
      description: "Helm operation timeout"
      required: false
      default: "300s"
      validation:
        pattern: "^[0-9]+(s|m|h)$"

    - name: BITBUCKET_REPO_URL
      type: string
      description: "Bitbucket repository URL containing Helm chart"
      required: true

    - name: CHART_PATH
      type: string
      description: "Path to Helm chart from repository root"
      required: true

    - name: VALUES_PATH
      type: string
      description: "Path to values file for environment-specific configuration"
      required: true

    - name: DRY_RUN
      type: boolean
      description: "Perform a dry run without actually deploying"
      required: false
      default: false

    - name: FORCE_UPGRADE
      type: boolean
      description: "Force upgrade even if no changes detected"
      required: false
      default: false

  operations:
    - name: deploy:helm
      description: "Deploy Helm chart to OpenShift cluster"
      parameters:
        - name: openshift_api_url
          required: true
        - name: openshift_project
          required: true
        - name: username
          required: true
        - name: password
          required: true
        - name: bitbucket_repo_url
          required: true
        - name: chart_path
          required: true
        - name: values_path
          required: true
        - name: release_name
          required: false
        - name: helm_timeout
          required: false
        - name: dry_run
          required: false
        - name: extra_values
          required: false
      returns:
        type: object
        properties:
          deployment_result:
            type: object
            properties:
              success:
                type: boolean
              release_name:
                type: string
              namespace:
                type: string
              status:
                type: string
              revision:
                type: integer
              last_deployed:
                type: string
              helm_output:
                type: string
              error:
                type: string
              resources:
                type: array
              execution_time:
                type: string

    - name: helm:status
      description: "Get Helm release status"
      parameters:
        - name: release_name
          required: true
        - name: namespace
          required: false
      returns:
        type: object
        properties:
          exists:
            type: boolean
          status:
            type: object

    - name: helm:rollback
      description: "Rollback Helm release"
      parameters:
        - name: release_name
          required: true
        - name: namespace
          required: false
        - name: revision
          required: false
      returns:
        type: object
        properties:
          success:
            type: boolean
          release_name:
            type: string
          namespace:
            type: string
          output:
            type: string

    - name: deploy:validate
      description: "Validate deployment parameters"
      parameters:
        - name: openshift_api_url
          required: true
        - name: openshift_project
          required: true
        - name: username
          required: true
        - name: password
          required: true
        - name: bitbucket_repo_url
          required: true
        - name: chart_path
          required: true
        - name: values_path
          required: true
      returns:
        type: object
        properties:
          valid:
            type: boolean
          errors:
            type: array
          request:
            type: object

  examples:
    - name: "Basic Development Deployment"
      description: "Deploy to development environment"
      configuration:
        openshift_api_url: "https://api.dev-cluster.example.com:6443"
        openshift_project: "my-app-dev"
        username: "developer"
        password: "dev-secret"
        bitbucket_repo_url: "https://bitbucket.org/myorg/helm-charts.git"
        chart_path: "charts/my-application"
        values_path: "values-dev.yaml"

    - name: "Production Deployment with Custom Release"
      description: "Deploy to production with custom release name and timeout"
      configuration:
        openshift_api_url: "https://api.prod-cluster.example.com:6443"
        openshift_project: "my-app-prod"
        username: "prod-deployer"
        password: "prod-secret"
        bitbucket_repo_url: "https://bitbucket.org/myorg/helm-charts.git"
        chart_path: "charts/my-application"
        values_path: "values-prod.yaml"
        release_name: "my-app-production"
        helm_timeout: "600s"

    - name: "Dry Run Validation"
      description: "Validate deployment without executing"
      configuration:
        openshift_api_url: "https://api.cluster.example.com:6443"
        openshift_project: "my-app-staging"
        username: "staging-user"
        password: "staging-secret"
        bitbucket_repo_url: "https://bitbucket.org/myorg/helm-charts.git"
        chart_path: "charts/my-application"
        values_path: "values-staging.yaml"
        dry_run: true
