module github.com/claudio/deploy-orchestrator/plugins/helm-openshift-plugin

go 1.23.0

toolchain go1.23.4

replace (
	github.com/claudio/deploy-orchestrator/shared => ../../../shared
	github.com/claudio/deploy-orchestrator/workflow-service => ../../../workflow-service
)

require (
	github.com/claudio/deploy-orchestrator/shared v0.0.0
	github.com/claudio/deploy-orchestrator/workflow-service v0.0.0
	go.uber.org/zap v1.27.0
)

require (
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	go.uber.org/multierr v1.10.0 // indirect
	golang.org/x/text v0.25.0 // indirect
	gorm.io/gorm v1.26.1 // indirect
)
