# Bitbucket Git Plugin

A comprehensive Git repository integration plugin for Deploy Orchestrator that supports both Bitbucket Cloud and Bitbucket Data Center.

## Features

- **Multi-Platform Support**: Works with both Bitbucket Cloud and Bitbucket Data Center
- **Complete Git Operations**: Commit info, branch/tag management, repository validation
- **Webhook Integration**: Create and manage repository webhooks
- **Pull Request Management**: Create pull requests programmatically
- **Version Parsing**: Intelligent version detection (commit SHA, branch, or tag)
- **Repository Cloning**: Prepare clone operations with proper authentication

## Supported Operations

### Git Information
- `git:commit-info` - Get detailed commit information
- `git:branch-info` - Get branch information and metadata
- `git:tag-info` - Get tag information and metadata
- `git:list-branches` - List all repository branches
- `git:list-tags` - List all repository tags

### Repository Management
- `git:validate-repository` - Validate repository access and credentials
- `git:parse-version` - Parse and categorize version strings
- `git:clone` - Prepare repository clone operations

### Integration Features
- `git:create-webhook` - Create repository webhooks
- `git:pull-request` - Create pull requests

## Configuration

### Bitbucket Cloud Configuration

```yaml
server_type: "cloud"
username: "your-username"
app_password: "your-app-password"
```

### Bitbucket Data Center Configuration

```yaml
server_type: "datacenter"
base_url: "https://bitbucket.company.com/rest/api/1.0"
username: "service-account"
access_token: "your-personal-access-token"
```

## Installation

### Prerequisites

- Go 1.21 or later
- Git CLI
- curl (for API operations)
- Access to Bitbucket Cloud or Data Center

### Build and Install

```bash
# Clone the repository
git clone <repository-url>
cd plugins/bitbucket-plugin

# Build the plugin
make build

# Install to workflow service
make install

# Run tests
make test
```

### Quick Install Script

```bash
# Run the installation and test script
./install-and-test.sh
```

## Usage Examples

### Get Commit Information

```bash
curl -X POST http://localhost:8085/api/v1/plugins/bitbucket-git/execute \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "git:commit-info",
    "parameters": {
      "repository": {
        "owner": "myorg",
        "name": "myrepo"
      },
      "commit_sha": "abc123def456"
    }
  }'
```

### List Branches

```bash
curl -X POST http://localhost:8085/api/v1/plugins/bitbucket-git/execute \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "git:list-branches",
    "parameters": {
      "repository": {
        "owner": "myorg",
        "name": "myrepo"
      }
    }
  }'
```

### Create Webhook

```bash
curl -X POST http://localhost:8085/api/v1/plugins/bitbucket-git/execute \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "git:create-webhook",
    "parameters": {
      "repository": {
        "owner": "myorg",
        "name": "myrepo"
      },
      "webhook_url": "https://your-server.com/webhook",
      "events": ["repo:push", "pullrequest:created"],
      "secret": "webhook-secret"
    }
  }'
```

### Validate Repository Access

```bash
curl -X POST http://localhost:8085/api/v1/plugins/bitbucket-git/execute \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "git:validate-repository",
    "parameters": {
      "repository": {
        "owner": "myorg",
        "name": "myrepo"
      }
    }
  }'
```

## Authentication

### Bitbucket Cloud

1. **App Password** (Recommended):
   - Go to Bitbucket Settings → App passwords
   - Create a new app password with required permissions
   - Use username + app password for authentication

2. **Access Token**:
   - Use OAuth access tokens for API access

### Bitbucket Data Center

1. **Personal Access Token** (Recommended):
   - Go to Personal settings → Personal access tokens
   - Create a token with required permissions
   - Use username + token for authentication

2. **Username/Password**:
   - Use regular username and password (not recommended for production)

## Required Permissions

### Bitbucket Cloud App Password Permissions
- Repositories: Read, Write
- Pull requests: Read, Write
- Webhooks: Read, Write

### Bitbucket Data Center Token Permissions
- Repository: Read, Write
- Project: Read (if accessing project-level operations)

## Environment Variables

The plugin supports the following environment variables for workflow integration:

- `BITBUCKET_WORKSPACE` - Bitbucket workspace/organization name
- `BITBUCKET_REPO` - Repository name
- `BITBUCKET_BRANCH` - Default branch name (default: "main")
- `CLONE_DEPTH` - Git clone depth for shallow clones (default: 1)
- `WEBHOOK_SECRET` - Secret for webhook validation

## Error Handling

The plugin provides detailed error messages for common scenarios:

- **Authentication Errors**: Invalid credentials or insufficient permissions
- **Repository Not Found**: Repository doesn't exist or access denied
- **API Rate Limits**: Bitbucket API rate limit exceeded
- **Network Issues**: Connection timeouts or network errors

## Development

### Project Structure

```
bitbucket-plugin/
├── main.go              # Main plugin implementation
├── plugin.yaml          # Plugin manifest
├── go.mod               # Go module definition
├── Makefile             # Build and installation scripts
├── README.md            # This documentation
└── install-and-test.sh  # Installation and test script
```

### Building

```bash
# Development build
make build

# Multi-platform build
make build-all

# Run tests with coverage
make test-coverage

# Format and lint code
make fmt
make lint
```

### Testing

```bash
# Run unit tests
make test

# Run integration tests (requires Bitbucket access)
make test-integration

# Validate plugin configuration
make validate
```

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify credentials are correct
   - Check token/app password permissions
   - Ensure API URL is correct for Data Center

2. **Repository Not Found**
   - Verify repository owner and name
   - Check repository permissions
   - Ensure repository exists and is accessible

3. **API Rate Limits**
   - Implement request throttling
   - Use authentication to increase rate limits
   - Consider caching responses

### Debug Mode

Enable debug logging by setting the environment variable:

```bash
export BITBUCKET_PLUGIN_DEBUG=true
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and ensure they pass
6. Submit a pull request

## License

This plugin is part of the Deploy Orchestrator project and follows the same license terms.

## Support

For issues and questions:

1. Check the troubleshooting section
2. Review plugin logs for error details
3. Create an issue in the project repository
4. Contact the Deploy Orchestrator team
