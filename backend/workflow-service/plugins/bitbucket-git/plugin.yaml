apiVersion: v1
kind: Plugin
metadata:
  name: bitbucket-git
  version: "2.0.0"
  description: "Bitbucket Git repository integration plugin supporting both Cloud and Data Center"
  author: "Deploy Orchestrator Team"
  tags:
    - git
    - bitbucket
    - repository
    - version-control
    - webhook
    - cloud
    - datacenter

spec:
  type: git-provider
  runtime: binary
  entrypoint: bitbucket-plugin

  provider:
    type: git-provider
    capabilities:
      - git:commit-info
      - git:branch-info
      - git:tag-info
      - git:list-branches
      - git:list-tags
      - git:webhook
      - git:repository-validation
      - git:parse-version
      - git:clone
      - git:pull-request

  hotReload:
    enabled: true
    watchPaths: ["*.go", "*.yaml"]
    debounceInterval: "2s"

  requirements:
    tools:
      - name: git
        version: ">=2.0.0"
        description: "Git CLI for repository operations"
      - name: curl
        version: ">=7.0.0"
        description: "HTTP client for API operations"

  configuration:
    schema:
      type: object
      required:
        - username
        - server_type
      properties:
        base_url:
          type: string
          title: "Bitbucket API Base URL"
          description: "Bitbucket API base URL (Cloud: https://api.bitbucket.org/2.0, Data Center: https://your-server/rest/api/1.0)"
          default: "https://api.bitbucket.org/2.0"
          examples:
            - "https://api.bitbucket.org/2.0"
            - "https://bitbucket.company.com/rest/api/1.0"

        server_type:
          type: string
          title: "Server Type"
          description: "Bitbucket server type"
          default: "cloud"
          enum: ["cloud", "datacenter"]
          examples:
            - "cloud"
            - "datacenter"

        api_version:
          type: string
          title: "API Version"
          description: "API version (2.0 for Cloud, 1.0 for Data Center)"
          default: "2.0"
          examples:
            - "2.0"
            - "1.0"

        username:
          type: string
          title: "Username"
          description: "Bitbucket username"
          examples:
            - "john.doe"
            - "service-account"

        access_token:
          type: password
          title: "Access Token"
          description: "Bitbucket access token (for Cloud) or Personal Access Token (for Data Center)"
          sensitive: true

        app_password:
          type: password
          title: "App Password"
          description: "Bitbucket app password (for Cloud authentication)"
          sensitive: true

  variables:
    - name: BITBUCKET_WORKSPACE
      type: string
      description: "Bitbucket workspace/organization name"
      required: true

    - name: BITBUCKET_REPO
      type: string
      description: "Repository name"
      required: true

    - name: BITBUCKET_BRANCH
      type: string
      description: "Default branch name"
      default: "main"
      required: false

    - name: CLONE_DEPTH
      type: number
      description: "Git clone depth for shallow clones"
      default: 1
      required: false
      validation:
        min: 1
        max: 1000

    - name: WEBHOOK_SECRET
      type: string
      description: "Secret for webhook validation"
      required: false
      sensitive: true

  operations:
    - name: git:commit-info
      description: "Get commit information"
      parameters:
        - name: repository
          required: true
        - name: commit_sha
          required: true
      returns:
        type: object
        properties:
          commit:
            type: object

    - name: git:branch-info
      description: "Get branch information"
      parameters:
        - name: repository
          required: true
        - name: branch
          required: true
      returns:
        type: object
        properties:
          branch:
            type: object

    - name: git:tag-info
      description: "Get tag information"
      parameters:
        - name: repository
          required: true
        - name: tag
          required: true
      returns:
        type: object
        properties:
          tag:
            type: object

    - name: git:list-branches
      description: "List repository branches"
      parameters:
        - name: repository
          required: true
      returns:
        type: object
        properties:
          branches:
            type: array

    - name: git:list-tags
      description: "List repository tags"
      parameters:
        - name: repository
          required: true
      returns:
        type: object
        properties:
          tags:
            type: array

    - name: git:create-webhook
      description: "Create repository webhook"
      parameters:
        - name: repository
          required: true
        - name: webhook_url
          required: true
        - name: events
          required: false
        - name: secret
          required: false
      returns:
        type: object
        properties:
          webhook:
            type: object

    - name: git:validate-repository
      description: "Validate repository access"
      parameters:
        - name: repository
          required: true
      returns:
        type: object
        properties:
          valid:
            type: boolean

    - name: git:parse-version
      description: "Parse version information"
      parameters:
        - name: version
          required: true
      returns:
        type: object
        properties:
          version_info:
            type: object

    - name: git:clone
      description: "Prepare repository clone"
      parameters:
        - name: repository
          required: true
        - name: target_dir
          required: true
        - name: branch
          required: false
        - name: depth
          required: false
      returns:
        type: object
        properties:
          clone_url:
            type: string
          status:
            type: string

    - name: git:pull-request
      description: "Create pull request"
      parameters:
        - name: repository
          required: true
        - name: title
          required: true
        - name: source_branch
          required: true
        - name: target_branch
          required: false
        - name: description
          required: false
      returns:
        type: object
        properties:
          pull_request:
            type: object
          status:
            type: string

  examples:
    - name: "Bitbucket Cloud Configuration"
      description: "Configuration for Bitbucket Cloud"
      configuration:
        server_type: "cloud"
        username: "john.doe"
        app_password: "your-app-password"

    - name: "Bitbucket Data Center Configuration"
      description: "Configuration for Bitbucket Data Center"
      configuration:
        server_type: "datacenter"
        base_url: "https://bitbucket.company.com/rest/api/1.0"
        username: "service-account"
        access_token: "your-personal-access-token"
