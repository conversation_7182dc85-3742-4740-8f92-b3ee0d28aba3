package monitoring

import (
	"context"
	"encoding/json"
	"sync"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"github.com/claudio/deploy-orchestrator/workflow-service/storage"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// ExecutionMonitor provides real-time monitoring of workflow executions
type ExecutionMonitor struct {
	db         *storage.Database
	logger     *zap.Logger
	events     chan *models.ExecutionEvent
	metrics    map[string]*models.ExecutionMetrics
	mu         sync.RWMutex
	listeners  map[string][]chan *models.ExecutionEvent // executionID -> listeners
	listenerMu sync.RWMutex
}

// NewExecutionMonitor creates a new execution monitor
func NewExecutionMonitor(db *storage.Database, logger *zap.Logger) *ExecutionMonitor {
	monitor := &ExecutionMonitor{
		db:        db,
		logger:    logger,
		events:    make(chan *models.ExecutionEvent, 1000),
		metrics:   make(map[string]*models.ExecutionMetrics),
		listeners: make(map[string][]chan *models.ExecutionEvent),
	}

	// Start event processor
	go monitor.processEvents()

	return monitor
}

// StartExecution initializes monitoring for a new execution
func (m *ExecutionMonitor) StartExecution(executionID, workflowID string, totalSteps int) {
	m.mu.Lock()
	defer m.mu.Unlock()

	metrics := &models.ExecutionMetrics{
		ID:                 uuid.New().String(),
		ExecutionID:        executionID,
		TotalSteps:         totalSteps,
		CompletedSteps:     0,
		FailedSteps:        0,
		SkippedSteps:       0,
		AverageStepTime:    0,
		TotalExecutionTime: 0,
		ResourceUsage:      make(map[string]interface{}),
		PerformanceData:    make(map[string]interface{}),
	}

	m.metrics[executionID] = metrics

	// Emit execution started event
	event := &models.ExecutionEvent{
		ID:          uuid.New().String(),
		ExecutionID: executionID,
		EventType:   "execution_started",
		EventData: map[string]interface{}{
			"workflowId": workflowID,
			"totalSteps": totalSteps,
			"startedAt":  time.Now(),
		},
		Timestamp: time.Now(),
	}

	m.emitEvent(event)
}

// StepStarted records when a step starts execution
func (m *ExecutionMonitor) StepStarted(executionID, stepID, stepName string) {
	event := &models.ExecutionEvent{
		ID:          uuid.New().String(),
		ExecutionID: executionID,
		StepID:      stepID,
		EventType:   "step_started",
		EventData: map[string]interface{}{
			"stepName":  stepName,
			"startedAt": time.Now(),
		},
		Timestamp: time.Now(),
	}

	m.emitEvent(event)
}

// StepCompleted records when a step completes successfully
func (m *ExecutionMonitor) StepCompleted(executionID, stepID, stepName string, duration time.Duration, output map[string]interface{}) {
	m.mu.Lock()
	if metrics, exists := m.metrics[executionID]; exists {
		metrics.CompletedSteps++
		m.updateAverageStepTime(metrics, duration)
	}
	m.mu.Unlock()

	event := &models.ExecutionEvent{
		ID:          uuid.New().String(),
		ExecutionID: executionID,
		StepID:      stepID,
		EventType:   "step_completed",
		EventData: map[string]interface{}{
			"stepName":    stepName,
			"duration":    duration.Seconds(),
			"completedAt": time.Now(),
			"output":      output,
		},
		Timestamp: time.Now(),
	}

	m.emitEvent(event)
}

// StepFailed records when a step fails
func (m *ExecutionMonitor) StepFailed(executionID, stepID, stepName string, duration time.Duration, err error) {
	m.mu.Lock()
	if metrics, exists := m.metrics[executionID]; exists {
		metrics.FailedSteps++
		m.updateAverageStepTime(metrics, duration)
	}
	m.mu.Unlock()

	errorMessage := "unknown error"
	if err != nil {
		errorMessage = err.Error()
	}

	event := &models.ExecutionEvent{
		ID:          uuid.New().String(),
		ExecutionID: executionID,
		StepID:      stepID,
		EventType:   "step_failed",
		EventData: map[string]interface{}{
			"stepName": stepName,
			"duration": duration.Seconds(),
			"failedAt": time.Now(),
			"error":    errorMessage,
		},
		Timestamp: time.Now(),
	}

	m.emitEvent(event)
}

// StepSkipped records when a step is skipped
func (m *ExecutionMonitor) StepSkipped(executionID, stepID, stepName, reason string) {
	m.mu.Lock()
	if metrics, exists := m.metrics[executionID]; exists {
		metrics.SkippedSteps++
	}
	m.mu.Unlock()

	event := &models.ExecutionEvent{
		ID:          uuid.New().String(),
		ExecutionID: executionID,
		StepID:      stepID,
		EventType:   "step_skipped",
		EventData: map[string]interface{}{
			"stepName":  stepName,
			"reason":    reason,
			"skippedAt": time.Now(),
		},
		Timestamp: time.Now(),
	}

	m.emitEvent(event)
}

// ExecutionCompleted records when an execution completes
func (m *ExecutionMonitor) ExecutionCompleted(executionID string, totalDuration time.Duration, success bool) {
	m.mu.Lock()
	if metrics, exists := m.metrics[executionID]; exists {
		metrics.TotalExecutionTime = totalDuration.Seconds()
		// Save metrics to database
		go m.saveMetrics(metrics)
	}
	m.mu.Unlock()

	eventType := "execution_completed"
	if !success {
		eventType = "execution_failed"
	}

	event := &models.ExecutionEvent{
		ID:          uuid.New().String(),
		ExecutionID: executionID,
		EventType:   eventType,
		EventData: map[string]interface{}{
			"totalDuration": totalDuration.Seconds(),
			"success":       success,
			"completedAt":   time.Now(),
		},
		Timestamp: time.Now(),
	}

	m.emitEvent(event)

	// Clean up metrics after completion
	go func() {
		time.Sleep(5 * time.Minute) // Keep metrics for 5 minutes after completion
		m.mu.Lock()
		delete(m.metrics, executionID)
		m.mu.Unlock()
	}()
}

// GetMetrics returns current metrics for an execution
func (m *ExecutionMonitor) GetMetrics(executionID string) (*models.ExecutionMetrics, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	metrics, exists := m.metrics[executionID]
	return metrics, exists
}

// GetAllMetrics returns metrics for all active executions
func (m *ExecutionMonitor) GetAllMetrics() map[string]*models.ExecutionMetrics {
	m.mu.RLock()
	defer m.mu.RUnlock()

	result := make(map[string]*models.ExecutionMetrics)
	for k, v := range m.metrics {
		result[k] = v
	}
	return result
}

// Subscribe subscribes to events for a specific execution
func (m *ExecutionMonitor) Subscribe(executionID string) <-chan *models.ExecutionEvent {
	m.listenerMu.Lock()
	defer m.listenerMu.Unlock()

	listener := make(chan *models.ExecutionEvent, 100)
	if m.listeners[executionID] == nil {
		m.listeners[executionID] = make([]chan *models.ExecutionEvent, 0)
	}
	m.listeners[executionID] = append(m.listeners[executionID], listener)

	return listener
}

// Unsubscribe removes a listener for an execution
func (m *ExecutionMonitor) Unsubscribe(executionID string, listener <-chan *models.ExecutionEvent) {
	m.listenerMu.Lock()
	defer m.listenerMu.Unlock()

	if listeners, exists := m.listeners[executionID]; exists {
		for i, l := range listeners {
			if l == listener {
				// Remove listener from slice
				m.listeners[executionID] = append(listeners[:i], listeners[i+1:]...)
				close(l)
				break
			}
		}

		// Clean up empty listener list
		if len(m.listeners[executionID]) == 0 {
			delete(m.listeners, executionID)
		}
	}
}

// emitEvent sends an event to the processing channel
func (m *ExecutionMonitor) emitEvent(event *models.ExecutionEvent) {
	select {
	case m.events <- event:
		// Event queued successfully
	default:
		m.logger.Warn("Event queue full, dropping event", zap.String("eventType", event.EventType))
	}
}

// processEvents processes events from the queue
func (m *ExecutionMonitor) processEvents() {
	for event := range m.events {
		// Save event to database
		go m.saveEvent(event)

		// Notify listeners
		m.notifyListeners(event)
	}
}

// notifyListeners sends events to subscribed listeners
func (m *ExecutionMonitor) notifyListeners(event *models.ExecutionEvent) {
	m.listenerMu.RLock()
	listeners := m.listeners[event.ExecutionID]
	m.listenerMu.RUnlock()

	for _, listener := range listeners {
		select {
		case listener <- event:
			// Event sent successfully
		default:
			// Listener channel is full, skip
			m.logger.Warn("Listener channel full, skipping event",
				zap.String("executionId", event.ExecutionID),
				zap.String("eventType", event.EventType))
		}
	}
}

// saveEvent saves an event to the database
func (m *ExecutionMonitor) saveEvent(event *models.ExecutionEvent) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Marshal event data
	if len(event.EventData) > 0 {
		if data, err := json.Marshal(event.EventData); err == nil {
			event.EventJSON = data
		}
	}

	if err := m.db.CreateExecutionEvent(ctx, event); err != nil {
		m.logger.Error("Failed to save execution event",
			zap.String("eventId", event.ID),
			zap.Error(err))
	}
}

// saveMetrics saves metrics to the database
func (m *ExecutionMonitor) saveMetrics(metrics *models.ExecutionMetrics) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Marshal resource usage and performance data
	if len(metrics.ResourceUsage) > 0 {
		if data, err := json.Marshal(metrics.ResourceUsage); err == nil {
			metrics.ResourceJSON = data
		}
	}

	if len(metrics.PerformanceData) > 0 {
		if data, err := json.Marshal(metrics.PerformanceData); err == nil {
			metrics.PerfDataJSON = data
		}
	}

	if err := m.db.CreateExecutionMetrics(ctx, metrics); err != nil {
		m.logger.Error("Failed to save execution metrics",
			zap.String("executionId", metrics.ExecutionID),
			zap.Error(err))
	}
}

// updateAverageStepTime updates the average step time for an execution
func (m *ExecutionMonitor) updateAverageStepTime(metrics *models.ExecutionMetrics, duration time.Duration) {
	totalSteps := metrics.CompletedSteps + metrics.FailedSteps
	if totalSteps > 0 {
		currentTotal := metrics.AverageStepTime * float64(totalSteps-1)
		metrics.AverageStepTime = (currentTotal + duration.Seconds()) / float64(totalSteps)
	}
}
