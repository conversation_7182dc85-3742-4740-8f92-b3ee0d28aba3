package clients

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"go.uber.org/zap"
)

// SecretsClient handles communication with the secrets service
type SecretsClient struct {
	baseURL    string
	httpClient *http.Client
	logger     *zap.Logger
}

// WorkflowSecretsRequest represents a request for workflow secrets
type WorkflowSecretsRequest struct {
	WorkflowID    string            `json:"workflowId"`
	ExecutionID   string            `json:"executionId"`
	ProjectID     string            `json:"projectId"`
	Environment   string            `json:"environment,omitempty"`
	Service       string            `json:"service,omitempty"`
	StepName      string            `json:"stepName,omitempty"`
	SecretMapping map[string]string `json:"secretMapping,omitempty"` // templateVar -> userSecretName
}

// SecretVariable represents a secret variable response
type SecretVariable struct {
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Value       string                 `json:"value"`
	Path        string                 `json:"path,omitempty"`
	Format      string                 `json:"format,omitempty"`
	Environment string                 `json:"environment,omitempty"`
	Service     string                 `json:"service,omitempty"`
	Namespace   string                 `json:"namespace,omitempty"`
	Transform   map[string]interface{} `json:"transform,omitempty"`
	SecretID    string                 `json:"secretId"`
	VariableID  string                 `json:"variableId"`
}

// WorkflowSecretsResponse represents the response from secrets service
type WorkflowSecretsResponse struct {
	Variables []SecretVariable       `json:"variables"`
	Errors    []string               `json:"errors"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// NewSecretsClient creates a new secrets service client
func NewSecretsClient(baseURL string, logger *zap.Logger) *SecretsClient {
	return &SecretsClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
	}
}

// GetWorkflowSecrets retrieves secrets for a workflow execution
func (c *SecretsClient) GetWorkflowSecrets(ctx context.Context, req WorkflowSecretsRequest, authToken string) (*WorkflowSecretsResponse, error) {
	url := fmt.Sprintf("%s/api/v1/integration/workflow/secrets", c.baseURL)

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+authToken)

	c.logger.Debug("Requesting workflow secrets",
		zap.String("workflowId", req.WorkflowID),
		zap.String("executionId", req.ExecutionID),
		zap.String("projectId", req.ProjectID),
		zap.String("url", url))

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("secrets service returned status %d", resp.StatusCode)
	}

	var response WorkflowSecretsResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	c.logger.Info("Retrieved workflow secrets",
		zap.String("workflowId", req.WorkflowID),
		zap.String("executionId", req.ExecutionID),
		zap.Int("secretCount", len(response.Variables)),
		zap.Int("errorCount", len(response.Errors)))

	return &response, nil
}

// GetSecretsForStep is a convenience method to get secrets for a specific workflow step
func (c *SecretsClient) GetSecretsForStep(ctx context.Context, workflowID, executionID, projectID, stepName string, authToken string) (*WorkflowSecretsResponse, error) {
	req := WorkflowSecretsRequest{
		WorkflowID:  workflowID,
		ExecutionID: executionID,
		ProjectID:   projectID,
		StepName:    stepName,
	}

	return c.GetWorkflowSecrets(ctx, req, authToken)
}
