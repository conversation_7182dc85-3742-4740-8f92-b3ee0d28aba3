# Workflow Service - Complete Guide

## Table of Contents
- [Overview](#overview)
- [Quick Start](#quick-start)
- [Configuration](#configuration)
- [Instance Management](#instance-management)
- [Distributed Deployment](#distributed-deployment)
- [API Reference](#api-reference)
- [Monitoring & Observability](#monitoring--observability)
- [Troubleshooting](#troubleshooting)

## Overview

The Workflow Service is a scalable, multi-OS compatible workflow execution engine with advanced instance labeling and targeting capabilities. It supports distributed execution across multiple instances with intelligent load balancing and resource-aware routing.

### Key Features
- 🌐 **Multi-OS Support**: Linux, Windows, macOS
- 🏷️ **Instance Labeling**: Environment, region, zone, capabilities
- 🎯 **Smart Routing**: Resource and capability-based workflow execution
- 📊 **Real-time Monitoring**: Execution tracking and performance metrics
- 🔄 **Auto-scaling**: Horizontal scaling with load balancing
- 🛡️ **Enhanced Security**: Sandboxed script execution
- 🔧 **Advanced Conditions**: Expression and script-based logic

## Quick Start

### Single Instance Setup

1. **Build and Run**
```bash
cd backend/workflow-service
go build .
./workflow-service
```

2. **Using Docker**
```bash
docker build -t workflow-service .
docker run -p 8085:8085 workflow-service
```

3. **Test the Service**
```bash
curl http://localhost:8085/health
curl http://localhost:8085/api/v1/instances/current
```

### Distributed Setup

1. **Start Multi-Instance Environment**
```bash
docker-compose -f docker-compose.distributed.yml up -d
```

2. **Access Load Balancer**
```bash
curl http://localhost:8080/api/v1/instances
```

## Configuration

### Environment Variables

#### Instance Identity
```bash
INSTANCE_NAME=workflow-linux-1                    # Instance name
INSTANCE_LABELS=env=prod,region=us-east,zone=a   # Comma-separated labels
INSTANCE_CAPABILITIES=docker,kubernetes,scripts   # Comma-separated capabilities
```

#### Resource Limits
```bash
WORKFLOW_MAX_CONCURRENT_EXECUTIONS=10    # Max concurrent workflows
SCRIPT_EXECUTOR_MAX_MEMORY_MB=512        # Script memory limit
SCRIPT_EXECUTOR_TIMEOUT_MINUTES=15       # Script timeout
```

#### Database
```bash
POSTGRES_URL=******************************/db?sslmode=disable
```

#### Service Configuration
```bash
PORT=8085                    # Service port
CONFIG_PATH=/app/config.yaml # Config file path
ENVIRONMENT=production       # Environment label
REGION=us-east-1            # Region label
ZONE=a                      # Zone label
```

### Configuration File (config.yaml)

```yaml
server:
  port: 8085
  host: "0.0.0.0"

database:
  postgres:
    url: "${POSTGRES_URL}"
    maxConnections: 25
    maxIdleConnections: 5

workflow:
  maxConcurrentExecutions: 10
  executionTimeoutMinutes: 60
  retryAttempts: 3
  stepTimeoutMinutes: 30
  enableParallelExecution: true

  scriptExecutor:
    enabled: true
    allowedLanguages: ["bash", "python", "javascript", "powershell"]
    timeoutMinutes: 15
    maxMemoryMB: 256
    sandboxEnabled: true

  instance:
    name: ""  # Auto-generated if empty
    labels:
      environment: "development"
      region: "local"
      zone: "default"
    capabilities:
      - "workflow-execution"
      - "script-execution"
    heartbeatIntervalSeconds: 30
```

## Instance Management

### Instance Labels

Labels are key-value pairs that help categorize and route workflows:

```bash
# Standard Labels
environment: production|staging|development
region: us-east-1|us-west-2|eu-west-1
zone: a|b|c
os: linux|windows|darwin
arch: amd64|arm64
type: standard|highmem|compute

# Custom Labels
team: backend|frontend|devops
cost-center: engineering|marketing
version: v1.0|v2.0
```

### Instance Capabilities

Capabilities define what an instance can execute:

```bash
# Standard Capabilities
workflow-execution    # Basic workflow execution
script-execution     # Script step execution
docker              # Docker operations
kubernetes          # Kubernetes operations
high-memory         # High memory workloads
gpu                 # GPU-accelerated workloads

# Language-specific
python              # Python script execution
nodejs              # Node.js script execution
powershell          # PowerShell script execution
```

### Managing Instance Configuration

#### Update Labels
```bash
curl -X PUT http://localhost:8085/api/v1/instances/current/labels \
  -H "Content-Type: application/json" \
  -d '{
    "labels": {
      "environment": "production",
      "team": "backend",
      "version": "v2.0"
    }
  }'
```

#### Update Capabilities
```bash
curl -X PUT http://localhost:8085/api/v1/instances/current/capabilities \
  -H "Content-Type: application/json" \
  -d '{
    "capabilities": [
      "workflow-execution",
      "script-execution",
      "docker",
      "kubernetes"
    ]
  }'
```

#### Update Status
```bash
curl -X PUT http://localhost:8085/api/v1/instances/current/status \
  -H "Content-Type: application/json" \
  -d '{"status": "maintenance"}'
```

## Distributed Deployment

### Docker Compose Setup

The `docker-compose.distributed.yml` provides a complete multi-instance setup:

#### Instance Types
1. **Production Instance 1** (Zone A)
   - Port: 8085
   - Labels: `env=production,region=us-east-1,zone=a`
   - Capabilities: `docker,kubernetes,scripts`

2. **Production Instance 2** (Zone B)
   - Port: 8086
   - Labels: `env=production,region=us-east-1,zone=b`
   - Capabilities: `docker,kubernetes,scripts`

3. **Development Instance**
   - Port: 8087
   - Labels: `env=development,region=local`
   - Capabilities: `docker,scripts`

4. **High-Memory Instance**
   - Port: 8088
   - Labels: `env=production,type=highmem`
   - Memory: 4GB limit
   - Capabilities: `docker,kubernetes,scripts,high-memory`

### Gateway Service (Load Balancing & Routing)

The gateway service provides intelligent routing and service discovery:

#### Service Discovery
All workflow instances automatically register with the gateway service on startup.

#### Load Balanced Access
```bash
# Access workflow service through gateway (load balanced)
curl http://localhost:8080/workflow-service/api/v1/instances

# Execute workflows (automatically routed to best instance)
curl http://localhost:8080/workflow-service/api/v1/instances/execute
```

#### Direct Instance Access (for debugging)
```bash
# Access specific instances directly
curl http://localhost:8085/api/v1/workflows  # Instance 1
curl http://localhost:8086/api/v1/workflows  # Instance 2
curl http://localhost:8087/api/v1/workflows  # Dev Instance
curl http://localhost:8088/api/v1/workflows  # High-mem Instance
```

### Scaling Instances

```bash
# Scale production instances
docker-compose -f docker-compose.distributed.yml up -d --scale workflow-service-1=3

# Add more development instances
docker-compose -f docker-compose.distributed.yml up -d --scale workflow-service-dev=2
```

## API Reference

### Instance Management

#### List All Instances
```bash
GET /api/v1/instances
```

#### Get Current Instance
```bash
GET /api/v1/instances/current
```

#### Get Instance Dashboard
```bash
GET /api/v1/instances/dashboard
```

#### Get Instance Health
```bash
GET /api/v1/instances/health
```

### Workflow Execution

#### Execute on Best Instance
```bash
POST /api/v1/instances/execute
Content-Type: application/json

{
  "workflowId": "my-workflow",
  "parameters": {"key": "value"},
  "requirements": {
    "os": "linux",
    "labels": {"environment": "production"},
    "capabilities": ["docker"],
    "minMemoryMB": 1024
  }
}
```

#### Execute on Specific Instance
```bash
POST /api/v1/instances/{instanceId}/execute
Content-Type: application/json

{
  "workflowId": "my-workflow",
  "parameters": {"key": "value"}
}
```

#### Get Execution Status
```bash
GET /api/v1/instances/executions/{executionId}
```

#### Stop Execution
```bash
POST /api/v1/instances/executions/{executionId}/stop
```

### Monitoring

#### Execution Dashboard
```bash
GET /api/v1/monitoring/dashboard
```

#### Execution Metrics
```bash
GET /api/v1/monitoring/executions/{id}/metrics
```

#### Real-time Events (SSE)
```bash
GET /api/v1/monitoring/executions/{id}/stream
```

## Monitoring & Observability

### Health Checks

#### Service Health
```bash
curl http://localhost:8085/health
```

#### Instance Health
```bash
curl http://localhost:8085/api/v1/instances/health
```

### Metrics Collection

The service provides comprehensive metrics:

- **Instance Metrics**: CPU, memory, load average
- **Execution Metrics**: Success rate, duration, throughput
- **Resource Usage**: Current vs. maximum capacity
- **Performance Data**: Step execution times, bottlenecks

### Real-time Monitoring

#### WebSocket Streams
```javascript
const eventSource = new EventSource('/api/v1/monitoring/executions/123/stream');
eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Execution event:', data);
};
```

#### Dashboard Integration
Access the monitoring dashboard at:
```
http://localhost:8085/api/v1/monitoring/dashboard
```

## Troubleshooting

### Common Issues

#### Instance Not Registering
```bash
# Check database connection
curl http://localhost:8085/health

# Check instance configuration
curl http://localhost:8085/api/v1/instances/current

# Check logs
docker logs workflow-service-1
```

#### Workflow Not Executing
```bash
# Check instance availability
curl http://localhost:8085/api/v1/instances

# Check execution requirements
curl -X POST http://localhost:8085/api/v1/instances/execute \
  -d '{"workflowId": "test", "requirements": {"os": "linux"}}'

# Check instance capabilities
curl http://localhost:8085/api/v1/instances/current
```

#### Performance Issues
```bash
# Check instance metrics
curl http://localhost:8085/api/v1/instances/metrics

# Check execution performance
curl http://localhost:8085/api/v1/monitoring/performance

# Monitor resource usage
curl http://localhost:8085/api/v1/instances/health
```

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=debug
./workflow-service
```

### Database Issues

#### Reset Database
```bash
# Stop services
docker-compose -f docker-compose.distributed.yml down

# Remove volumes
docker volume rm deploy-orchestrator_postgres_data

# Restart
docker-compose -f docker-compose.distributed.yml up -d
```

#### Check Database Connection
```bash
# Test connection
psql postgres://postgres:postgres@localhost:5432/deploy_orchestrator

# Check tables
\dt
```

### Network Issues

#### Check Port Availability
```bash
netstat -tulpn | grep :8085
```

#### Test Instance Communication
```bash
# Test direct instance access
curl http://workflow-service-1:8085/health

# Test load balancer
curl http://nginx:80/api/v1/instances
```

## Best Practices

### Instance Configuration
1. Use meaningful labels for routing
2. Set appropriate resource limits
3. Configure capabilities accurately
4. Use environment-specific configurations

### Workflow Design
1. Specify execution requirements
2. Use appropriate step timeouts
3. Implement proper error handling
4. Design for distributed execution

### Monitoring
1. Set up health check alerts
2. Monitor resource usage trends
3. Track execution performance
4. Implement log aggregation

### Security
1. Enable script sandboxing
2. Use secure database connections
3. Implement proper authentication
4. Regular security updates

---

For more information, see the [API documentation](./api-docs.md) and [deployment examples](./examples/).
