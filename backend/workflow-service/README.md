# Workflow Service

A **scalable, multi-OS workflow execution engine** with advanced instance labeling, distributed execution, and intelligent load balancing capabilities.

## 🚀 Key Features

### Core Capabilities
- **🌐 Multi-OS Support**: Linux, Windows, macOS compatibility
- **🏷️ Instance Labeling**: Environment, region, zone, capability-based routing
- **⚖️ Smart Load Balancing**: Resource-aware workflow distribution
- **🔄 Distributed Execution**: Cross-instance workflow coordination
- **📊 Real-time Monitoring**: Execution tracking and performance metrics
- **🛡️ Enhanced Security**: Sandboxed script execution
- **🎯 Targeted Execution**: Route workflows to specific instances

### Advanced Features
- **Conditional Logic**: Expression and script-based conditions
- **Script Execution**: Multi-language support (Bash, Python, JavaScript, PowerShell)
- **Parallel Processing**: Independent step execution
- **Template System**: Reusable workflow templates with versioning
- **Auto-scaling**: Horizontal scaling with health monitoring
- **Fault Tolerance**: Automatic failover and recovery

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Documentation](#documentation)
- [Instance Management](#instance-management)
- [Distributed Deployment](#distributed-deployment)
- [API Reference](#api-reference)
- [Examples](#examples)
- [Contributing](#contributing)

## 🚀 Quick Start

### Single Instance Setup

```bash
# Clone and build
git clone <repository-url>
cd backend/workflow-service
go build .

# Run with default configuration
./workflow-service

# Test the service
curl http://localhost:8085/health
```

### Docker Single Instance

```bash
# Build and run
docker build -t workflow-service .
docker run -p 8085:8085 workflow-service

# Test
curl http://localhost:8085/api/v1/instances/current
```

### Multi-Instance Distributed Setup

```bash
# Start complete distributed environment
docker-compose -f docker-compose.distributed.yml up -d

# Access through load balancer
curl http://localhost:8080/api/v1/instances

# Check instance health
curl http://localhost:8080/api/v1/instances/health
```

## 📚 Documentation

| Document | Description |
|----------|-------------|
| **[HOWTO.md](./HOWTO.md)** | Complete setup and usage guide |
| **[DEPLOYMENT.md](./DEPLOYMENT.md)** | Production deployment strategies |
| **[API.md](./API.md)** | Complete API reference |
| **[examples/](./examples/)** | Practical usage examples |

## 🏷️ Instance Management

### Instance Labels

Categorize instances for intelligent routing:

```bash
# Environment-based
environment: production|staging|development
region: us-east-1|us-west-2|eu-west-1
zone: a|b|c

# Resource-based
type: standard|highmem|compute|gpu
os: linux|windows|darwin
arch: amd64|arm64

# Custom labels
team: backend|frontend|devops
cost-center: engineering|marketing
```

### Instance Capabilities

Define what each instance can execute:

```bash
# Standard capabilities
workflow-execution    # Basic workflow execution
script-execution     # Script step execution
docker              # Docker operations
kubernetes          # Kubernetes operations
high-memory         # High memory workloads
gpu                 # GPU-accelerated workloads
```

### Configuration Example

```bash
# Environment variables
export INSTANCE_NAME="workflow-prod-1"
export INSTANCE_LABELS="environment=production,region=us-east-1,zone=a,type=standard"
export INSTANCE_CAPABILITIES="workflow-execution,script-execution,docker,kubernetes"
export WORKFLOW_MAX_CONCURRENT_EXECUTIONS=10
```

## 🌐 Distributed Deployment

### Multi-Instance Architecture

The distributed setup includes:

1. **Production Instances** (Zones A & B)
   - High availability across zones
   - Docker and Kubernetes support
   - Production workload handling

2. **Development Instance**
   - Isolated development environment
   - Relaxed security for testing
   - Development workflow execution

3. **High-Memory Instance**
   - 4GB+ memory allocation
   - Data processing workloads
   - Memory-intensive operations

4. **Gateway Service**
   - Automatic service discovery
   - Load balancing and health checks
   - Centralized routing and authentication

### Smart Routing Examples

```bash
# Access through gateway service (load balanced)
curl -X POST http://localhost:8080/workflow-service/api/v1/instances/execute

# Direct instance access (for debugging)
curl -X POST http://localhost:8085/api/v1/instances/execute

# Instance discovery through gateway
curl http://localhost:8080/workflow-service/api/v1/instances
```

## 🔌 API Reference

### Instance Management

```bash
# List all instances
GET /api/v1/instances

# Get current instance info
GET /api/v1/instances/current

# Update instance labels
PUT /api/v1/instances/current/labels

# Update capabilities
PUT /api/v1/instances/current/capabilities
```

### Distributed Execution

```bash
# Execute on best available instance
POST /api/v1/instances/execute
{
  "workflowId": "my-workflow",
  "requirements": {
    "labels": {"environment": "production"},
    "capabilities": ["docker"],
    "minMemoryMB": 1024
  }
}

# Execute on specific instance
POST /api/v1/instances/{instanceId}/execute

# Get execution status
GET /api/v1/instances/executions/{executionId}
```

### Monitoring

```bash
# Instance dashboard
GET /api/v1/instances/dashboard

# Instance health
GET /api/v1/instances/health

# Real-time execution stream
GET /api/v1/monitoring/executions/{id}/stream
```

## 💡 Examples

### Environment-based Workflow

```json
{
  "workflowId": "deploy-application",
  "parameters": {
    "version": "v2.1.0",
    "environment": "production"
  },
  "requirements": {
    "labels": {
      "environment": "production",
      "region": "us-east-1"
    },
    "capabilities": ["kubernetes", "docker"]
  }
}
```

### Resource-specific Workflow

```json
{
  "workflowId": "data-processing",
  "parameters": {
    "dataset": "large-analytics"
  },
  "requirements": {
    "labels": {"type": "highmem"},
    "capabilities": ["high-memory", "python"],
    "minMemoryMB": 8192
  }
}
```

## 🛠️ Development

### Prerequisites

- Go 1.21+
- PostgreSQL 12+ (optional)
- Docker & Docker Compose

### Building

```bash
# Build service
go build .

# Run tests
go test ./...

# Build Docker image
docker build -t workflow-service .
```

### Configuration

```yaml
# config.yaml
server:
  port: 8085

database:
  postgres:
    url: "******************************/db?sslmode=disable"

workflow:
  maxConcurrentExecutions: 10
  executionTimeoutMinutes: 60

  scriptExecutor:
    enabled: true
    sandboxEnabled: true
    maxMemoryMB: 512

  instance:
    name: "auto-generated"
    labels:
      environment: "development"
      region: "local"
    capabilities:
      - "workflow-execution"
      - "script-execution"
```

## 🔧 Production Deployment

### Kubernetes

```bash
# Deploy to Kubernetes
kubectl apply -f k8s/

# Scale instances
kubectl scale deployment workflow-service --replicas=5

# Check status
kubectl get pods -l app=workflow-service
```

### Docker Swarm

```bash
# Initialize swarm
docker swarm init

# Deploy stack
docker stack deploy -c docker-compose.distributed.yml workflow

# Scale services
docker service scale workflow_workflow-service-1=3
```

## 📊 Monitoring & Observability

### Metrics Collection

- **Instance Metrics**: CPU, memory, load average
- **Execution Metrics**: Success rate, duration, throughput
- **Resource Usage**: Current vs. maximum capacity
- **Performance Data**: Step execution times, bottlenecks

### Integration

- **Prometheus**: Metrics collection and alerting
- **Grafana**: Visualization and dashboards
- **ELK Stack**: Log aggregation and analysis
- **Jaeger**: Distributed tracing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow Go best practices
- Add tests for new features
- Update documentation
- Ensure backward compatibility

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [HOWTO.md](./HOWTO.md)
- **API Reference**: [API.md](./API.md)
- **Examples**: [examples/](./examples/)
- **Issues**: GitHub Issues
- **Discussions**: GitHub Discussions

---

**Ready to scale your workflows?** Start with the [Quick Start](#quick-start) guide or explore the [examples](./examples/) for inspiration! 🚀
