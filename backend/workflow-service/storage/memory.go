package storage

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"github.com/google/uuid"
)

// InMemoryDatabase provides an in-memory implementation for testing
type InMemoryDatabase struct {
	workflows      map[string]*models.WorkflowDefinition
	templates      map[string]*models.WorkflowTemplate
	executions     map[string]*models.WorkflowExecution
	stepExecutions map[string]*models.StepExecution
	ratings        map[string]*models.TemplateRating
	reviews        map[string]*models.TemplateReview
	favorites      map[string]*models.TemplateFavorite
	mu             sync.RWMutex
}

// NewInMemoryDatabase creates a new in-memory database
func NewInMemoryDatabase() *InMemoryDatabase {
	db := &InMemoryDatabase{
		workflows:      make(map[string]*models.WorkflowDefinition),
		templates:      make(map[string]*models.WorkflowTemplate),
		executions:     make(map[string]*models.WorkflowExecution),
		stepExecutions: make(map[string]*models.StepExecution),
		ratings:        make(map[string]*models.TemplateRating),
		reviews:        make(map[string]*models.TemplateReview),
		favorites:      make(map[string]*models.TemplateFavorite),
	}

	// Add sample templates
	db.addSampleTemplates()

	return db
}

// Workflow Definition operations

func (db *InMemoryDatabase) CreateWorkflowDefinition(ctx context.Context, workflow *models.WorkflowDefinition) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if workflow.ID == "" {
		workflow.ID = uuid.New().String()
	}

	if workflow.CreatedAt.IsZero() {
		workflow.CreatedAt = time.Now()
	}
	workflow.UpdatedAt = time.Now()

	db.workflows[workflow.ID] = workflow
	return nil
}

func (db *InMemoryDatabase) GetWorkflowDefinition(ctx context.Context, id string) (*models.WorkflowDefinition, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	workflow, exists := db.workflows[id]
	if !exists {
		return nil, fmt.Errorf("workflow not found: %s", id)
	}

	// Return a copy to avoid race conditions
	workflowCopy := *workflow
	return &workflowCopy, nil
}

func (db *InMemoryDatabase) GetWorkflowDefinitions(ctx context.Context, projectID string, limit, offset int) ([]models.WorkflowDefinition, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	var workflows []models.WorkflowDefinition
	count := 0
	skipped := 0

	for _, workflow := range db.workflows {
		if projectID != "" && workflow.ProjectID != projectID {
			continue
		}

		if skipped < offset {
			skipped++
			continue
		}

		if limit > 0 && count >= limit {
			break
		}

		workflows = append(workflows, *workflow)
		count++
	}

	return workflows, nil
}

func (db *InMemoryDatabase) UpdateWorkflowDefinition(ctx context.Context, workflow *models.WorkflowDefinition) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if _, exists := db.workflows[workflow.ID]; !exists {
		return fmt.Errorf("workflow not found: %s", workflow.ID)
	}

	workflow.UpdatedAt = time.Now()
	db.workflows[workflow.ID] = workflow
	return nil
}

func (db *InMemoryDatabase) DeleteWorkflowDefinition(ctx context.Context, id string) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if _, exists := db.workflows[id]; !exists {
		return fmt.Errorf("workflow not found: %s", id)
	}

	delete(db.workflows, id)
	return nil
}

// Workflow Template operations

func (db *InMemoryDatabase) CreateWorkflowTemplate(ctx context.Context, template *models.WorkflowTemplate) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if template.ID == "" {
		template.ID = uuid.New().String()
	}

	if template.CreatedAt.IsZero() {
		template.CreatedAt = time.Now()
	}
	template.UpdatedAt = time.Now()

	db.templates[template.ID] = template
	return nil
}

func (db *InMemoryDatabase) GetWorkflowTemplate(ctx context.Context, id string) (*models.WorkflowTemplate, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	template, exists := db.templates[id]
	if !exists {
		return nil, fmt.Errorf("template not found: %s", id)
	}

	templateCopy := *template
	return &templateCopy, nil
}

func (db *InMemoryDatabase) GetWorkflowTemplates(ctx context.Context, category string, isPublic *bool, limit, offset int) ([]models.WorkflowTemplate, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	var templates []models.WorkflowTemplate
	count := 0
	skipped := 0

	for _, template := range db.templates {
		if category != "" && template.Category != category {
			continue
		}

		if isPublic != nil && template.IsPublic != *isPublic {
			continue
		}

		if skipped < offset {
			skipped++
			continue
		}

		if limit > 0 && count >= limit {
			break
		}

		templates = append(templates, *template)
		count++
	}

	return templates, nil
}

func (db *InMemoryDatabase) UpdateWorkflowTemplate(ctx context.Context, template *models.WorkflowTemplate) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if _, exists := db.templates[template.ID]; !exists {
		return fmt.Errorf("template not found: %s", template.ID)
	}

	template.UpdatedAt = time.Now()
	db.templates[template.ID] = template
	return nil
}

func (db *InMemoryDatabase) DeleteWorkflowTemplate(ctx context.Context, id string) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if _, exists := db.templates[id]; !exists {
		return fmt.Errorf("template not found: %s", id)
	}

	delete(db.templates, id)
	return nil
}

func (db *InMemoryDatabase) GetWorkflowTemplatesWithFilters(ctx context.Context, filters map[string]interface{}, limit, offset int) ([]*models.WorkflowTemplate, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	var result []*models.WorkflowTemplate

	for _, template := range db.templates {
		// Apply filters
		if category, ok := filters["category"].(string); ok && category != "" {
			if template.Category != category {
				continue
			}
		}

		if isPublic, ok := filters["isPublic"].(*bool); ok && isPublic != nil {
			if template.IsPublic != *isPublic {
				continue
			}
		}

		if isFeatured, ok := filters["isFeatured"].(*bool); ok && isFeatured != nil {
			if template.IsFeatured != *isFeatured {
				continue
			}
		}

		if search, ok := filters["search"].(string); ok && search != "" {
			searchLower := strings.ToLower(search)
			if !strings.Contains(strings.ToLower(template.Name), searchLower) &&
				!strings.Contains(strings.ToLower(template.Description), searchLower) {
				// Check tags
				found := false
				for _, tag := range template.Tags {
					if strings.Contains(strings.ToLower(tag), searchLower) {
						found = true
						break
					}
				}
				if !found {
					continue
				}
			}
		}

		if tags, ok := filters["tags"].([]string); ok && len(tags) > 0 {
			hasAllTags := true
			for _, filterTag := range tags {
				found := false
				for _, templateTag := range template.Tags {
					if templateTag == filterTag {
						found = true
						break
					}
				}
				if !found {
					hasAllTags = false
					break
				}
			}
			if !hasAllTags {
				continue
			}
		}

		templateCopy := *template
		result = append(result, &templateCopy)
	}

	// Apply sorting
	sortBy := "created_at"
	sortOrder := "desc"

	if sb, ok := filters["sortBy"].(string); ok && sb != "" {
		sortBy = sb
	}

	if so, ok := filters["sortOrder"].(string); ok && so != "" {
		sortOrder = so
	}

	// Sort results
	sort.Slice(result, func(i, j int) bool {
		switch sortBy {
		case "name":
			if sortOrder == "asc" {
				return result[i].Name < result[j].Name
			}
			return result[i].Name > result[j].Name
		case "rating":
			if sortOrder == "asc" {
				return result[i].Rating < result[j].Rating
			}
			return result[i].Rating > result[j].Rating
		case "usage_count":
			if sortOrder == "asc" {
				return result[i].UsageCount < result[j].UsageCount
			}
			return result[i].UsageCount > result[j].UsageCount
		case "download_count":
			if sortOrder == "asc" {
				return result[i].DownloadCount < result[j].DownloadCount
			}
			return result[i].DownloadCount > result[j].DownloadCount
		default: // created_at
			if sortOrder == "asc" {
				return result[i].CreatedAt.Before(result[j].CreatedAt)
			}
			return result[i].CreatedAt.After(result[j].CreatedAt)
		}
	})

	// Apply pagination
	start := offset
	if start > len(result) {
		start = len(result)
	}

	end := start + limit
	if end > len(result) {
		end = len(result)
	}

	if start >= len(result) {
		return []*models.WorkflowTemplate{}, nil
	}

	return result[start:end], nil
}

// addSampleTemplates adds sample workflow templates to the database
func (db *InMemoryDatabase) addSampleTemplates() {
	templates := []*models.WorkflowTemplate{
		{
			ID:          "template-1",
			Name:        "Docker Deployment Pipeline",
			Description: "Complete CI/CD pipeline for Docker applications with build, test, and deployment stages",
			Category:    "deployment",
			IsPublic:    true,
			IsFeatured:  true,
			CreatedBy:   "system",
			AuthorName:  "Deploy Orchestrator Team",
			AuthorEmail: "<EMAIL>",
			Version:     "1.2.0",
			Steps: []models.WorkflowStep{
				{
					ID:   "step-1",
					Name: "Build Docker Image",
					Type: "docker_build",
					Config: map[string]interface{}{
						"dockerfile": "Dockerfile",
						"context":    ".",
						"tags":       []string{"latest", "{{.version}}"},
					},
				},
				{
					ID:   "step-2",
					Name: "Run Tests",
					Type: "docker_run",
					Config: map[string]interface{}{
						"image":   "{{.image}}:latest",
						"command": "npm test",
					},
				},
				{
					ID:   "step-3",
					Name: "Deploy to Production",
					Type: "kubernetes_deploy",
					Config: map[string]interface{}{
						"namespace": "production",
						"manifest":  "k8s/deployment.yaml",
					},
				},
			},
			Parameters: []models.WorkflowParameter{
				{
					Name:         "version",
					Type:         "string",
					Description:  "Version tag for the deployment",
					Required:     true,
					DefaultValue: "1.0.0",
				},
				{
					Name:         "environment",
					Type:         "string",
					Description:  "Target environment",
					Required:     true,
					DefaultValue: "production",
				},
			},
			Variables: map[string]interface{}{
				"registry":  "docker.io",
				"namespace": "default",
			},
			Tags:          []string{"docker", "kubernetes", "ci-cd", "deployment"},
			UsageCount:    156,
			DownloadCount: 423,
			Rating:        4.8,
			RatingCount:   89,
			Documentation: "This template provides a complete Docker-based deployment pipeline with automated testing and Kubernetes deployment.",
			Requirements:  []string{"Docker", "Kubernetes", "kubectl"},
			Screenshots:   []string{},
			License:       "MIT",
		},
		{
			ID:          "template-2",
			Name:        "AWS Lambda Deployment",
			Description: "Serverless deployment pipeline for AWS Lambda functions with automated testing",
			Category:    "deployment",
			IsPublic:    true,
			IsFeatured:  true,
			CreatedBy:   "system",
			AuthorName:  "AWS Community",
			AuthorEmail: "<EMAIL>",
			Version:     "2.1.0",
			Steps: []models.WorkflowStep{
				{
					ID:   "step-1",
					Name: "Install Dependencies",
					Type: "shell",
					Config: map[string]interface{}{
						"command": "npm install",
					},
				},
				{
					ID:   "step-2",
					Name: "Run Unit Tests",
					Type: "shell",
					Config: map[string]interface{}{
						"command": "npm test",
					},
				},
				{
					ID:   "step-3",
					Name: "Package Lambda",
					Type: "shell",
					Config: map[string]interface{}{
						"command": "zip -r function.zip .",
					},
				},
				{
					ID:   "step-4",
					Name: "Deploy to AWS",
					Type: "aws_lambda_deploy",
					Config: map[string]interface{}{
						"function_name": "{{.function_name}}",
						"runtime":       "nodejs18.x",
						"handler":       "index.handler",
					},
				},
			},
			Parameters: []models.WorkflowParameter{
				{
					Name:        "function_name",
					Type:        "string",
					Description: "AWS Lambda function name",
					Required:    true,
				},
				{
					Name:         "aws_region",
					Type:         "string",
					Description:  "AWS region for deployment",
					Required:     true,
					DefaultValue: "us-east-1",
				},
			},
			Variables: map[string]interface{}{
				"timeout": 30,
				"memory":  128,
			},
			Tags:          []string{"aws", "lambda", "serverless", "nodejs"},
			UsageCount:    89,
			DownloadCount: 267,
			Rating:        4.6,
			RatingCount:   45,
			Documentation: "Deploy Node.js applications to AWS Lambda with automated testing and packaging.",
			Requirements:  []string{"AWS CLI", "Node.js", "npm"},
			Screenshots:   []string{},
			License:       "Apache-2.0",
		},
		{
			ID:          "template-3",
			Name:        "Database Backup & Restore",
			Description: "Automated database backup with optional restore functionality for PostgreSQL and MySQL",
			Category:    "backup",
			IsPublic:    true,
			IsFeatured:  false,
			CreatedBy:   "system",
			AuthorName:  "Database Admin Team",
			AuthorEmail: "<EMAIL>",
			Version:     "1.0.5",
			Steps: []models.WorkflowStep{
				{
					ID:   "step-1",
					Name: "Create Backup",
					Type: "database_backup",
					Config: map[string]interface{}{
						"database_type": "{{.db_type}}",
						"host":          "{{.db_host}}",
						"database":      "{{.db_name}}",
						"backup_path":   "/backups/{{.timestamp}}.sql",
					},
				},
				{
					ID:   "step-2",
					Name: "Compress Backup",
					Type: "shell",
					Config: map[string]interface{}{
						"command": "gzip /backups/{{.timestamp}}.sql",
					},
				},
				{
					ID:   "step-3",
					Name: "Upload to S3",
					Type: "aws_s3_upload",
					Config: map[string]interface{}{
						"bucket": "{{.backup_bucket}}",
						"key":    "database-backups/{{.timestamp}}.sql.gz",
					},
				},
			},
			Parameters: []models.WorkflowParameter{
				{
					Name:         "db_type",
					Type:         "string",
					Description:  "Database type (postgresql, mysql)",
					Required:     true,
					DefaultValue: "postgresql",
				},
				{
					Name:        "db_host",
					Type:        "string",
					Description: "Database host",
					Required:    true,
				},
				{
					Name:        "db_name",
					Type:        "string",
					Description: "Database name",
					Required:    true,
				},
				{
					Name:        "backup_bucket",
					Type:        "string",
					Description: "S3 bucket for backup storage",
					Required:    true,
				},
			},
			Variables: map[string]interface{}{
				"timestamp":      "{{.now | date \"2006-01-02-15-04-05\"}}",
				"retention_days": 30,
			},
			Tags:          []string{"database", "backup", "postgresql", "mysql", "aws", "s3"},
			UsageCount:    234,
			DownloadCount: 456,
			Rating:        4.7,
			RatingCount:   67,
			Documentation: "Automated database backup solution with compression and cloud storage integration.",
			Requirements:  []string{"Database client tools", "AWS CLI", "gzip"},
			Screenshots:   []string{},
			License:       "MIT",
		},
		{
			ID:          "template-4",
			Name:        "Security Vulnerability Scan",
			Description: "Comprehensive security scanning pipeline with SAST, DAST, and dependency checks",
			Category:    "security",
			IsPublic:    true,
			IsFeatured:  true,
			CreatedBy:   "system",
			AuthorName:  "Security Team",
			AuthorEmail: "<EMAIL>",
			Version:     "3.0.1",
			Steps: []models.WorkflowStep{
				{
					ID:   "step-1",
					Name: "SAST Scan",
					Type: "security_scan",
					Config: map[string]interface{}{
						"scan_type": "sast",
						"tool":      "sonarqube",
						"target":    ".",
					},
				},
				{
					ID:   "step-2",
					Name: "Dependency Check",
					Type: "security_scan",
					Config: map[string]interface{}{
						"scan_type": "dependency",
						"tool":      "npm audit",
						"target":    "package.json",
					},
				},
				{
					ID:   "step-3",
					Name: "Container Scan",
					Type: "security_scan",
					Config: map[string]interface{}{
						"scan_type": "container",
						"tool":      "trivy",
						"target":    "{{.image}}",
					},
				},
				{
					ID:   "step-4",
					Name: "Generate Report",
					Type: "report_generator",
					Config: map[string]interface{}{
						"format": "json",
						"output": "security-report.json",
					},
				},
			},
			Parameters: []models.WorkflowParameter{
				{
					Name:        "image",
					Type:        "string",
					Description: "Container image to scan",
					Required:    false,
				},
				{
					Name:         "severity_threshold",
					Type:         "string",
					Description:  "Minimum severity level to report",
					Required:     true,
					DefaultValue: "medium",
				},
			},
			Variables: map[string]interface{}{
				"fail_on_high":  true,
				"report_format": "json",
			},
			Tags:          []string{"security", "sast", "dast", "vulnerability", "scanning"},
			UsageCount:    78,
			DownloadCount: 189,
			Rating:        4.9,
			RatingCount:   34,
			Documentation: "Complete security scanning pipeline covering static analysis, dependency checks, and container scanning.",
			Requirements:  []string{"SonarQube", "Trivy", "npm"},
			Screenshots:   []string{},
			License:       "MIT",
		},
	}

	for _, template := range templates {
		db.templates[template.ID] = template
	}
}

// ===== TEMPLATE RATINGS & REVIEWS =====

// GetTemplateReviews retrieves reviews for a template
func (db *InMemoryDatabase) GetTemplateReviews(ctx context.Context, templateID string, limit, offset int, sortBy, sortOrder string) ([]models.TemplateReview, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	var result []models.TemplateReview

	for _, review := range db.reviews {
		if review.TemplateID == templateID {
			result = append(result, *review)
		}
	}

	// Sort results
	sort.Slice(result, func(i, j int) bool {
		switch sortBy {
		case "helpful_count":
			if sortOrder == "asc" {
				return result[i].HelpfulCount < result[j].HelpfulCount
			}
			return result[i].HelpfulCount > result[j].HelpfulCount
		case "rating":
			if sortOrder == "asc" {
				return result[i].Rating < result[j].Rating
			}
			return result[i].Rating > result[j].Rating
		default: // created_at
			if sortOrder == "asc" {
				return result[i].CreatedAt.Before(result[j].CreatedAt)
			}
			return result[i].CreatedAt.After(result[j].CreatedAt)
		}
	})

	// Apply pagination
	start := offset
	if start > len(result) {
		start = len(result)
	}

	end := start + limit
	if end > len(result) {
		end = len(result)
	}

	if start >= len(result) {
		return []models.TemplateReview{}, nil
	}

	return result[start:end], nil
}

// CreateTemplateReview creates a new template review
func (db *InMemoryDatabase) CreateTemplateReview(ctx context.Context, review *models.TemplateReview) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if review.ID == "" {
		review.ID = uuid.New().String()
	}

	reviewCopy := *review
	db.reviews[review.ID] = &reviewCopy

	return nil
}

// CreateOrUpdateTemplateRating creates or updates a template rating
func (db *InMemoryDatabase) CreateOrUpdateTemplateRating(ctx context.Context, rating *models.TemplateRating) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	// Find existing rating for this user and template
	var existingID string
	for id, r := range db.ratings {
		if r.TemplateID == rating.TemplateID && r.UserID == rating.UserID {
			existingID = id
			break
		}
	}

	if existingID != "" {
		// Update existing rating
		existing := db.ratings[existingID]
		existing.Rating = rating.Rating
		existing.Review = rating.Review
	} else {
		// Create new rating
		if rating.ID == "" {
			rating.ID = uuid.New().String()
		}
		ratingCopy := *rating
		db.ratings[rating.ID] = &ratingCopy
	}

	return nil
}

// MarkReviewHelpful marks a review as helpful
func (db *InMemoryDatabase) MarkReviewHelpful(ctx context.Context, reviewID, userID string) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if review, exists := db.reviews[reviewID]; exists {
		review.HelpfulCount++
	}

	return nil
}

// GetTemplateRatingStats gets average rating and count for a template
func (db *InMemoryDatabase) GetTemplateRatingStats(ctx context.Context, templateID string) (float64, int, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	var totalRating int
	var count int

	for _, rating := range db.ratings {
		if rating.TemplateID == templateID {
			totalRating += rating.Rating
			count++
		}
	}

	if count == 0 {
		return 0, 0, nil
	}

	avgRating := float64(totalRating) / float64(count)
	return avgRating, count, nil
}

// ===== COMMUNITY FEATURES =====

// AddTemplateFavorite adds a template to user's favorites
func (db *InMemoryDatabase) AddTemplateFavorite(ctx context.Context, favorite *models.TemplateFavorite) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if favorite.ID == "" {
		favorite.ID = uuid.New().String()
	}

	favoriteCopy := *favorite
	db.favorites[favorite.ID] = &favoriteCopy

	return nil
}

// RemoveTemplateFavorite removes a template from user's favorites
func (db *InMemoryDatabase) RemoveTemplateFavorite(ctx context.Context, templateID, userID string) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	for id, favorite := range db.favorites {
		if favorite.TemplateID == templateID && favorite.UserID == userID {
			delete(db.favorites, id)
			break
		}
	}

	return nil
}

// GetUserFavoriteTemplates gets user's favorite templates
func (db *InMemoryDatabase) GetUserFavoriteTemplates(ctx context.Context, userID string) ([]models.WorkflowTemplate, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	var result []models.WorkflowTemplate

	// Get favorite template IDs for user
	favoriteTemplateIDs := make(map[string]bool)
	for _, favorite := range db.favorites {
		if favorite.UserID == userID {
			favoriteTemplateIDs[favorite.TemplateID] = true
		}
	}

	// Get templates that are in favorites
	for templateID := range favoriteTemplateIDs {
		if template, exists := db.templates[templateID]; exists {
			result = append(result, *template)
		}
	}

	return result, nil
}

// ===== TEMPLATE VERSIONING =====

// GetTemplateVersions gets all versions of a template
func (db *InMemoryDatabase) GetTemplateVersions(ctx context.Context, templateID string) ([]models.TemplateVersion, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	var result []models.TemplateVersion

	// For in-memory implementation, we'll just return a mock version
	// In a real implementation, this would query a versions table
	version := models.TemplateVersion{
		ID:           uuid.New().String(),
		TemplateID:   templateID,
		Version:      "1.0.0",
		Description:  "Initial version",
		ChangeLog:    "Initial release",
		IsStable:     true,
		IsDeprecated: false,
		ReleaseDate:  time.Now(),
	}

	result = append(result, version)
	return result, nil
}

// CreateTemplateVersion creates a new template version
func (db *InMemoryDatabase) CreateTemplateVersion(ctx context.Context, version *models.TemplateVersion) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if version.ID == "" {
		version.ID = uuid.New().String()
	}

	// For in-memory implementation, we'll just store it as a simple record
	// In a real implementation, this would create a new version entry
	return nil
}

// Workflow Execution operations

func (db *InMemoryDatabase) CreateWorkflowExecution(ctx context.Context, execution *models.WorkflowExecution) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if execution.ID == "" {
		execution.ID = uuid.New().String()
	}

	if execution.CreatedAt.IsZero() {
		execution.CreatedAt = time.Now()
	}
	execution.UpdatedAt = time.Now()

	db.executions[execution.ID] = execution
	return nil
}

func (db *InMemoryDatabase) GetWorkflowExecution(ctx context.Context, id string) (*models.WorkflowExecution, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	execution, exists := db.executions[id]
	if !exists {
		return nil, fmt.Errorf("execution not found: %s", id)
	}

	executionCopy := *execution
	return &executionCopy, nil
}

func (db *InMemoryDatabase) GetWorkflowExecutions(ctx context.Context, workflowID, projectID string, status string, limit, offset int) ([]models.WorkflowExecution, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	var executions []models.WorkflowExecution
	count := 0
	skipped := 0

	for _, execution := range db.executions {
		if workflowID != "" && execution.WorkflowID != workflowID {
			continue
		}

		if projectID != "" && execution.ProjectID != projectID {
			continue
		}

		if status != "" && execution.Status != status {
			continue
		}

		if skipped < offset {
			skipped++
			continue
		}

		if limit > 0 && count >= limit {
			break
		}

		executions = append(executions, *execution)
		count++
	}

	return executions, nil
}

func (db *InMemoryDatabase) UpdateWorkflowExecution(ctx context.Context, execution *models.WorkflowExecution) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if _, exists := db.executions[execution.ID]; !exists {
		return fmt.Errorf("execution not found: %s", execution.ID)
	}

	execution.UpdatedAt = time.Now()
	db.executions[execution.ID] = execution
	return nil
}

// Step Execution operations

func (db *InMemoryDatabase) CreateStepExecution(ctx context.Context, stepExecution *models.StepExecution) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if stepExecution.ID == "" {
		stepExecution.ID = uuid.New().String()
	}

	if stepExecution.CreatedAt.IsZero() {
		stepExecution.CreatedAt = time.Now()
	}
	stepExecution.UpdatedAt = time.Now()

	db.stepExecutions[stepExecution.ID] = stepExecution
	return nil
}

func (db *InMemoryDatabase) GetStepExecution(ctx context.Context, id string) (*models.StepExecution, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	stepExecution, exists := db.stepExecutions[id]
	if !exists {
		return nil, fmt.Errorf("step execution not found: %s", id)
	}

	stepExecutionCopy := *stepExecution
	return &stepExecutionCopy, nil
}

func (db *InMemoryDatabase) GetStepExecutions(ctx context.Context, executionID string) ([]models.StepExecution, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	var stepExecutions []models.StepExecution

	for _, stepExecution := range db.stepExecutions {
		if stepExecution.ExecutionID == executionID {
			stepExecutions = append(stepExecutions, *stepExecution)
		}
	}

	return stepExecutions, nil
}

func (db *InMemoryDatabase) UpdateStepExecution(ctx context.Context, stepExecution *models.StepExecution) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if _, exists := db.stepExecutions[stepExecution.ID]; !exists {
		return fmt.Errorf("step execution not found: %s", stepExecution.ID)
	}

	stepExecution.UpdatedAt = time.Now()
	db.stepExecutions[stepExecution.ID] = stepExecution
	return nil
}
