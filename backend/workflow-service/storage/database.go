package storage

import (
	"context"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/db"
	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"gorm.io/gorm"
)

// Database represents the database connection and operations
type Database struct {
	dbAdapter  *db.GORMAdapter
	InMemoryDB *InMemoryDatabase
}

// GetDB returns the underlying gorm.DB instance
func (db *Database) GetDB() *gorm.DB {
	if db.dbAdapter != nil {
		return db.dbAdapter.DB
	}
	// Return nil when using in-memory database
	return nil
}

// CreateWorkflowAuditLog creates a new audit log entry
func (db *Database) CreateWorkflowAuditLog(ctx context.Context, auditLog *models.WorkflowAuditLog) error {
	if db.InMemoryDB != nil {
		// For in-memory database, store in memory (simplified)
		return nil
	}

	if db.dbAdapter == nil || db.dbAdapter.DB == nil {
		// If no database adapter is available, skip saving
		return nil
	}

	return db.dbAdapter.DB.Create(auditLog).Error
}

// GetWorkflowAuditLogs retrieves audit logs with filtering
func (db *Database) GetWorkflowAuditLogs(ctx context.Context, filters interface{}, limit, offset int) ([]models.WorkflowAuditLog, error) {
	var auditLogs []models.WorkflowAuditLog

	if db.InMemoryDB != nil {
		// For in-memory database, return empty list
		return auditLogs, nil
	}

	query := db.dbAdapter.DB.Model(&models.WorkflowAuditLog{})

	// Apply filters (simplified for now)
	// TODO: Implement proper filtering based on filters parameter

	return auditLogs, query.Limit(limit).Offset(offset).Order("created_at DESC").Find(&auditLogs).Error
}

// CreateNotificationRule creates a new notification rule
func (db *Database) CreateNotificationRule(ctx context.Context, rule *models.NotificationRule) error {
	if db.InMemoryDB != nil {
		// For in-memory database, store in memory (simplified)
		return nil
	}

	if db.dbAdapter == nil || db.dbAdapter.DB == nil {
		// If no database adapter is available, skip saving
		return nil
	}

	return db.dbAdapter.DB.Create(rule).Error
}

// GetNotificationRules retrieves notification rules for a project
func (db *Database) GetNotificationRules(ctx context.Context, projectID string) ([]models.NotificationRule, error) {
	var rules []models.NotificationRule

	if db.InMemoryDB != nil {
		// For in-memory database, return empty list
		return rules, nil
	}

	query := db.dbAdapter.DB.Model(&models.NotificationRule{}).Where("enabled = ?", true)

	// Get project-specific rules and global rules
	query = query.Where("project_id = ? OR project_id = ''", projectID)

	return rules, query.Find(&rules).Error
}

// UpdateNotificationRule updates a notification rule
func (db *Database) UpdateNotificationRule(ctx context.Context, rule *models.NotificationRule) error {
	if db.InMemoryDB != nil {
		// For in-memory database, store in memory (simplified)
		return nil
	}

	if db.dbAdapter == nil || db.dbAdapter.DB == nil {
		// If no database adapter is available, skip saving
		return nil
	}

	return db.dbAdapter.DB.Save(rule).Error
}

// DeleteNotificationRule deletes a notification rule
func (db *Database) DeleteNotificationRule(ctx context.Context, ruleID string) error {
	if db.InMemoryDB != nil {
		// For in-memory database, store in memory (simplified)
		return nil
	}

	if db.dbAdapter == nil || db.dbAdapter.DB == nil {
		// If no database adapter is available, skip saving
		return nil
	}

	return db.dbAdapter.DB.Delete(&models.NotificationRule{}, "id = ?", ruleID).Error
}

// NewDatabase creates a new database connection
func NewDatabase(postgresURL string) (*Database, error) {
	// If PostgreSQL URL is not provided, use in-memory database
	if postgresURL == "" {
		inMemoryDB := NewInMemoryDatabase()
		return &Database{
			InMemoryDB: inMemoryDB,
		}, nil
	}

	// Configure database connection
	config := db.Config{
		URL:           postgresURL,
		MaxRetries:    5,
		RetryInterval: 3 * time.Second,
	}

	// Create GORM adapter with options
	adapter, err := db.NewGORMAdapter(
		config,
		db.WithRetries(3, 2*time.Second),
		db.WithQueryTimeout(10*time.Second),
		db.WithAutoMigrations(
			&models.WorkflowDefinition{},
			&models.WorkflowTemplate{},
			&models.WorkflowExecution{},
			&models.StepExecution{},
			&models.ExecutionMetrics{},
			&models.ExecutionEvent{},
			&models.InstanceRecord{},
		),
	)
	if err != nil {
		return nil, err
	}

	return &Database{
		dbAdapter: adapter,
	}, nil
}

// Workflow Definition operations

// CreateWorkflowDefinition creates a new workflow definition
func (db *Database) CreateWorkflowDefinition(ctx context.Context, workflow *models.WorkflowDefinition) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateWorkflowDefinition(ctx, workflow)
	}

	if err := workflow.BeforeSave(nil); err != nil {
		return err
	}

	// Use GORM's Create method with json.RawMessage fields
	return db.dbAdapter.DB.WithContext(ctx).Create(workflow).Error
}

// GetWorkflowDefinition retrieves a workflow definition by ID
func (db *Database) GetWorkflowDefinition(ctx context.Context, id string) (*models.WorkflowDefinition, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetWorkflowDefinition(ctx, id)
	}

	var workflow models.WorkflowDefinition
	if err := db.dbAdapter.FindByID(ctx, &workflow, id); err != nil {
		return nil, err
	}

	if err := workflow.AfterFind(); err != nil {
		return nil, err
	}

	return &workflow, nil
}

// GetWorkflowDefinitions retrieves workflow definitions with optional filtering
func (db *Database) GetWorkflowDefinitions(ctx context.Context, projectID string, limit, offset int) ([]models.WorkflowDefinition, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetWorkflowDefinitions(ctx, projectID, limit, offset)
	}

	var workflows []models.WorkflowDefinition
	query := db.dbAdapter.DB.WithContext(ctx)

	if projectID != "" {
		query = query.Where("project_id = ?", projectID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&workflows).Error; err != nil {
		return nil, err
	}

	// Process JSON fields for each workflow
	for i := range workflows {
		if err := workflows[i].AfterFind(); err != nil {
			return nil, err
		}
	}

	return workflows, nil
}

// UpdateWorkflowDefinition updates an existing workflow definition
func (db *Database) UpdateWorkflowDefinition(ctx context.Context, workflow *models.WorkflowDefinition) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateWorkflowDefinition(ctx, workflow)
	}

	if err := workflow.BeforeSave(nil); err != nil {
		return err
	}

	// Use GORM's Save method with json.RawMessage fields
	return db.dbAdapter.DB.WithContext(ctx).Save(workflow).Error
}

// DeleteWorkflowDefinition deletes a workflow definition
func (db *Database) DeleteWorkflowDefinition(ctx context.Context, id string) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.DeleteWorkflowDefinition(ctx, id)
	}

	return db.dbAdapter.Delete(ctx, &models.WorkflowDefinition{}, id)
}

// Workflow Template operations

// CreateWorkflowTemplate creates a new workflow template
func (db *Database) CreateWorkflowTemplate(ctx context.Context, template *models.WorkflowTemplate) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateWorkflowTemplate(ctx, template)
	}

	return db.dbAdapter.Create(ctx, template)
}

// GetWorkflowTemplate retrieves a workflow template by ID
func (db *Database) GetWorkflowTemplate(ctx context.Context, id string) (*models.WorkflowTemplate, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetWorkflowTemplate(ctx, id)
	}

	var template models.WorkflowTemplate
	if err := db.dbAdapter.FindByID(ctx, &template, id); err != nil {
		return nil, err
	}

	return &template, nil
}

// GetWorkflowTemplates retrieves workflow templates with optional filtering
func (db *Database) GetWorkflowTemplates(ctx context.Context, category string, isPublic *bool, limit, offset int) ([]models.WorkflowTemplate, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetWorkflowTemplates(ctx, category, isPublic, limit, offset)
	}

	var templates []models.WorkflowTemplate
	query := db.dbAdapter.DB.WithContext(ctx)

	if category != "" {
		query = query.Where("category = ?", category)
	}

	if isPublic != nil {
		query = query.Where("is_public = ?", *isPublic)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&templates).Error; err != nil {
		return nil, err
	}

	return templates, nil
}

// GetWorkflowTemplatesWithFilters retrieves workflow templates with advanced filtering
func (db *Database) GetWorkflowTemplatesWithFilters(ctx context.Context, filters map[string]interface{}, limit, offset int) ([]models.WorkflowTemplate, error) {
	if db.InMemoryDB != nil {
		templates, err := db.InMemoryDB.GetWorkflowTemplatesWithFilters(ctx, filters, limit, offset)
		if err != nil {
			return nil, err
		}
		// Convert from []*WorkflowTemplate to []WorkflowTemplate
		result := make([]models.WorkflowTemplate, len(templates))
		for i, template := range templates {
			result[i] = *template
		}
		return result, nil
	}

	var templates []models.WorkflowTemplate
	query := db.dbAdapter.DB.WithContext(ctx)

	// Apply filters
	if category, ok := filters["category"].(string); ok && category != "" {
		query = query.Where("category = ?", category)
	}

	if isPublic, ok := filters["isPublic"].(*bool); ok && isPublic != nil {
		query = query.Where("is_public = ?", *isPublic)
	}

	if isFeatured, ok := filters["isFeatured"].(*bool); ok && isFeatured != nil {
		query = query.Where("is_featured = ?", *isFeatured)
	}

	if search, ok := filters["search"].(string); ok && search != "" {
		searchPattern := "%" + search + "%"
		query = query.Where("name ILIKE ? OR description ILIKE ? OR tags::text ILIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	if tags, ok := filters["tags"].([]string); ok && len(tags) > 0 {
		for _, tag := range tags {
			query = query.Where("tags::jsonb ? ?", tag)
		}
	}

	// Apply sorting
	sortBy := "created_at"
	sortOrder := "desc"

	if sb, ok := filters["sortBy"].(string); ok && sb != "" {
		sortBy = sb
	}

	if so, ok := filters["sortOrder"].(string); ok && so != "" {
		sortOrder = so
	}

	orderClause := sortBy + " " + sortOrder
	query = query.Order(orderClause)

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&templates).Error; err != nil {
		return nil, err
	}

	// Load JSON fields
	for i := range templates {
		if err := templates[i].AfterFind(); err != nil {
			return nil, err
		}
	}

	return templates, nil
}

// ===== TEMPLATE RATINGS & REVIEWS =====

// GetTemplateReviews retrieves reviews for a template
func (db *Database) GetTemplateReviews(ctx context.Context, templateID string, limit, offset int, sortBy, sortOrder string) ([]models.TemplateReview, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetTemplateReviews(ctx, templateID, limit, offset, sortBy, sortOrder)
	}

	var reviews []models.TemplateReview
	query := db.dbAdapter.DB.WithContext(ctx).Where("template_id = ?", templateID)

	// Apply sorting
	orderClause := sortBy + " " + sortOrder
	query = query.Order(orderClause)

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&reviews).Error; err != nil {
		return nil, err
	}

	// Load JSON fields
	for i := range reviews {
		if err := reviews[i].AfterFind(); err != nil {
			return nil, err
		}
	}

	return reviews, nil
}

// CreateTemplateReview creates a new template review
func (db *Database) CreateTemplateReview(ctx context.Context, review *models.TemplateReview) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateTemplateReview(ctx, review)
	}

	if err := review.BeforeSave(); err != nil {
		return err
	}

	return db.dbAdapter.Create(ctx, review)
}

// CreateOrUpdateTemplateRating creates or updates a template rating
func (db *Database) CreateOrUpdateTemplateRating(ctx context.Context, rating *models.TemplateRating) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateOrUpdateTemplateRating(ctx, rating)
	}

	// Check if rating already exists for this user and template
	var existingRating models.TemplateRating
	err := db.dbAdapter.DB.WithContext(ctx).Where("template_id = ? AND user_id = ?", rating.TemplateID, rating.UserID).First(&existingRating).Error

	if err == nil {
		// Update existing rating
		existingRating.Rating = rating.Rating
		existingRating.Review = rating.Review
		return db.dbAdapter.Update(ctx, &existingRating)
	}

	// Create new rating
	return db.dbAdapter.Create(ctx, rating)
}

// MarkReviewHelpful marks a review as helpful
func (db *Database) MarkReviewHelpful(ctx context.Context, reviewID, userID string) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.MarkReviewHelpful(ctx, reviewID, userID)
	}

	// Increment helpful count
	return db.dbAdapter.DB.WithContext(ctx).Model(&models.TemplateReview{}).
		Where("id = ?", reviewID).
		UpdateColumn("helpful_count", db.dbAdapter.DB.Raw("helpful_count + 1")).Error
}

// GetTemplateRatingStats gets average rating and count for a template
func (db *Database) GetTemplateRatingStats(ctx context.Context, templateID string) (float64, int, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetTemplateRatingStats(ctx, templateID)
	}

	var result struct {
		AvgRating float64
		Count     int
	}

	err := db.dbAdapter.DB.WithContext(ctx).
		Model(&models.TemplateRating{}).
		Select("AVG(rating) as avg_rating, COUNT(*) as count").
		Where("template_id = ?", templateID).
		Scan(&result).Error

	if err != nil {
		return 0, 0, err
	}

	return result.AvgRating, result.Count, nil
}

// ===== COMMUNITY FEATURES =====

// AddTemplateFavorite adds a template to user's favorites
func (db *Database) AddTemplateFavorite(ctx context.Context, favorite *models.TemplateFavorite) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.AddTemplateFavorite(ctx, favorite)
	}

	return db.dbAdapter.Create(ctx, favorite)
}

// RemoveTemplateFavorite removes a template from user's favorites
func (db *Database) RemoveTemplateFavorite(ctx context.Context, templateID, userID string) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.RemoveTemplateFavorite(ctx, templateID, userID)
	}

	return db.dbAdapter.DB.WithContext(ctx).
		Where("template_id = ? AND user_id = ?", templateID, userID).
		Delete(&models.TemplateFavorite{}).Error
}

// GetUserFavoriteTemplates gets user's favorite templates
func (db *Database) GetUserFavoriteTemplates(ctx context.Context, userID string) ([]models.WorkflowTemplate, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetUserFavoriteTemplates(ctx, userID)
	}

	var templates []models.WorkflowTemplate
	err := db.dbAdapter.DB.WithContext(ctx).
		Table("workflow_templates").
		Joins("JOIN template_favorites ON workflow_templates.id = template_favorites.template_id").
		Where("template_favorites.user_id = ?", userID).
		Find(&templates).Error

	if err != nil {
		return nil, err
	}

	// Load JSON fields
	for i := range templates {
		if err := templates[i].AfterFind(); err != nil {
			return nil, err
		}
	}

	return templates, nil
}

// ===== TEMPLATE VERSIONING =====

// GetTemplateVersions gets all versions of a template
func (db *Database) GetTemplateVersions(ctx context.Context, templateID string) ([]models.TemplateVersion, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetTemplateVersions(ctx, templateID)
	}

	var versions []models.TemplateVersion
	err := db.dbAdapter.DB.WithContext(ctx).
		Where("template_id = ?", templateID).
		Order("release_date DESC").
		Find(&versions).Error

	return versions, err
}

// CreateTemplateVersion creates a new template version
func (db *Database) CreateTemplateVersion(ctx context.Context, version *models.TemplateVersion) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateTemplateVersion(ctx, version)
	}

	return db.dbAdapter.Create(ctx, version)
}

// UpdateWorkflowTemplate updates an existing workflow template
func (db *Database) UpdateWorkflowTemplate(ctx context.Context, template *models.WorkflowTemplate) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateWorkflowTemplate(ctx, template)
	}

	return db.dbAdapter.Update(ctx, template)
}

// DeleteWorkflowTemplate deletes a workflow template
func (db *Database) DeleteWorkflowTemplate(ctx context.Context, id string) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.DeleteWorkflowTemplate(ctx, id)
	}

	return db.dbAdapter.Delete(ctx, &models.WorkflowTemplate{}, id)
}

// Workflow Execution operations

// CreateWorkflowExecution creates a new workflow execution
func (db *Database) CreateWorkflowExecution(ctx context.Context, execution *models.WorkflowExecution) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateWorkflowExecution(ctx, execution)
	}

	return db.dbAdapter.Create(ctx, execution)
}

// GetWorkflowExecution retrieves a workflow execution by ID
func (db *Database) GetWorkflowExecution(ctx context.Context, id string) (*models.WorkflowExecution, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetWorkflowExecution(ctx, id)
	}

	var execution models.WorkflowExecution
	if err := db.dbAdapter.FindByID(ctx, &execution, id); err != nil {
		return nil, err
	}

	return &execution, nil
}

// GetWorkflowExecutions retrieves workflow executions with optional filtering
func (db *Database) GetWorkflowExecutions(ctx context.Context, workflowID, projectID string, status string, limit, offset int) ([]models.WorkflowExecution, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetWorkflowExecutions(ctx, workflowID, projectID, status, limit, offset)
	}

	var executions []models.WorkflowExecution
	query := db.dbAdapter.DB.WithContext(ctx)

	if workflowID != "" {
		query = query.Where("workflow_id = ?", workflowID)
	}

	if projectID != "" {
		query = query.Where("project_id = ?", projectID)
	}

	if status != "" {
		query = query.Where("status = ?", status)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	query = query.Order("created_at DESC")

	if err := query.Find(&executions).Error; err != nil {
		return nil, err
	}

	return executions, nil
}

// UpdateWorkflowExecution updates an existing workflow execution
func (db *Database) UpdateWorkflowExecution(ctx context.Context, execution *models.WorkflowExecution) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateWorkflowExecution(ctx, execution)
	}

	return db.dbAdapter.Update(ctx, execution)
}

// Step Execution operations

// CreateStepExecution creates a new step execution
func (db *Database) CreateStepExecution(ctx context.Context, stepExecution *models.StepExecution) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateStepExecution(ctx, stepExecution)
	}

	return db.dbAdapter.Create(ctx, stepExecution)
}

// GetStepExecution retrieves a step execution by ID
func (db *Database) GetStepExecution(ctx context.Context, id string) (*models.StepExecution, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetStepExecution(ctx, id)
	}

	var stepExecution models.StepExecution
	if err := db.dbAdapter.FindByID(ctx, &stepExecution, id); err != nil {
		return nil, err
	}

	return &stepExecution, nil
}

// GetStepExecutions retrieves step executions for a workflow execution
func (db *Database) GetStepExecutions(ctx context.Context, executionID string) ([]models.StepExecution, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetStepExecutions(ctx, executionID)
	}

	var stepExecutions []models.StepExecution
	if err := db.dbAdapter.DB.WithContext(ctx).Where("execution_id = ?", executionID).Order("created_at ASC").Find(&stepExecutions).Error; err != nil {
		return nil, err
	}

	return stepExecutions, nil
}

// UpdateStepExecution updates an existing step execution
func (db *Database) UpdateStepExecution(ctx context.Context, stepExecution *models.StepExecution) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateStepExecution(ctx, stepExecution)
	}

	return db.dbAdapter.Update(ctx, stepExecution)
}

// Execution Metrics operations

// CreateExecutionMetrics creates new execution metrics
func (db *Database) CreateExecutionMetrics(ctx context.Context, metrics *models.ExecutionMetrics) error {
	if db.InMemoryDB != nil {
		// For in-memory database, store in memory (simplified)
		return nil
	}

	if db.dbAdapter == nil {
		// If no database adapter is available, skip saving
		return nil
	}

	// Additional safety check to ensure the adapter is not corrupted
	if db.dbAdapter.DB == nil {
		// Log the issue and skip saving
		return nil
	}

	return db.dbAdapter.Create(ctx, metrics)
}

// GetExecutionMetrics retrieves execution metrics by execution ID
func (db *Database) GetExecutionMetrics(ctx context.Context, executionID string) (*models.ExecutionMetrics, error) {
	if db.InMemoryDB != nil {
		// For in-memory database, return empty metrics
		return &models.ExecutionMetrics{ExecutionID: executionID}, nil
	}

	if db.dbAdapter == nil {
		// If no database adapter is available, return empty metrics
		return &models.ExecutionMetrics{ExecutionID: executionID}, nil
	}

	var metrics models.ExecutionMetrics
	if err := db.dbAdapter.DB.WithContext(ctx).Where("execution_id = ?", executionID).First(&metrics).Error; err != nil {
		return nil, err
	}
	return &metrics, nil
}

// UpdateExecutionMetrics updates execution metrics
func (db *Database) UpdateExecutionMetrics(ctx context.Context, metrics *models.ExecutionMetrics) error {
	if db.InMemoryDB != nil {
		// For in-memory database, store in memory (simplified)
		return nil
	}

	if db.dbAdapter == nil {
		// If no database adapter is available, skip saving
		return nil
	}

	// Additional safety check to ensure the adapter is not corrupted
	if db.dbAdapter.DB == nil {
		// Log the issue and skip saving
		return nil
	}

	return db.dbAdapter.Update(ctx, metrics)
}

// Execution Event operations

// CreateExecutionEvent creates a new execution event
func (db *Database) CreateExecutionEvent(ctx context.Context, event *models.ExecutionEvent) error {
	if db.InMemoryDB != nil {
		// For in-memory database, store in memory (simplified)
		return nil
	}

	if db.dbAdapter == nil {
		// If no database adapter is available, skip saving
		return nil
	}

	// Additional safety check to ensure the adapter is not corrupted
	if db.dbAdapter.DB == nil {
		// Log the issue and skip saving
		return nil
	}

	return db.dbAdapter.Create(ctx, event)
}

// GetExecutionEvents retrieves execution events for an execution
func (db *Database) GetExecutionEvents(ctx context.Context, executionID string, limit, offset int) ([]models.ExecutionEvent, error) {
	var events []models.ExecutionEvent

	if db.InMemoryDB != nil {
		// For in-memory database, return empty events
		return events, nil
	}

	if db.dbAdapter == nil {
		// If no database adapter is available, return empty events
		return events, nil
	}

	query := db.dbAdapter.DB.WithContext(ctx).Where("execution_id = ?", executionID).Order("timestamp DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&events).Error; err != nil {
		return nil, err
	}

	return events, nil
}

// GetExecutionEventsByType retrieves execution events by type
func (db *Database) GetExecutionEventsByType(ctx context.Context, executionID, eventType string) ([]models.ExecutionEvent, error) {
	var events []models.ExecutionEvent

	if db.InMemoryDB != nil {
		// For in-memory database, return empty events
		return events, nil
	}

	if db.dbAdapter == nil {
		// If no database adapter is available, return empty events
		return events, nil
	}

	if err := db.dbAdapter.DB.WithContext(ctx).
		Where("execution_id = ? AND event_type = ?", executionID, eventType).
		Order("timestamp DESC").
		Find(&events).Error; err != nil {
		return nil, err
	}

	return events, nil
}
