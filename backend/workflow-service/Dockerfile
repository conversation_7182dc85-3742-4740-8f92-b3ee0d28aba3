FROM golang:1.24-alpine AS builder

WORKDIR /app

# Copy shared module first
COPY shared ./shared

# Copy workflow service files
COPY workflow-service/go.mod workflow-service/go.sum ./

# Download dependencies
RUN go mod download

# Copy workflow service source code
COPY workflow-service .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o workflow-service .

FROM alpine:3.18

WORKDIR /app

# Create config directory
RUN mkdir -p /app/config

# Copy the binary from builder
COPY --from=builder /app/workflow-service .

# Copy config files
COPY --from=builder /app/config/config.yaml /app/config/

# Expose the application port
EXPOSE 8085

# Set default config path
ENV CONFIG_PATH=/app/config/config.yaml

# Run the application
CMD ["./workflow-service"]
