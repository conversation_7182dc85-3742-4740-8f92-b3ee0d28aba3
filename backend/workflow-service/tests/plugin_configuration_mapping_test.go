package tests

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/claudio/deploy-orchestrator/backend/shared/models"
	"github.com/claudio/deploy-orchestrator/backend/workflow-service/internal/services"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"go.uber.org/zap/zaptest"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// Migrate the schemas
	err = db.AutoMigrate(
		&models.Plugin{},
		&services.EnvironmentPluginMapping{},
	)
	require.NoError(t, err)

	return db
}

func TestPluginConfigurationMapping(t *testing.T) {
	logger := zaptest.NewLogger(t)
	db := setupTestDB(t)

	// Create test plugins with different configuration structures
	createTestPlugins(t, db)

	t.Run("Legacy Configuration Mapping", func(t *testing.T) {
		testLegacyConfigurationMapping(t, db, logger)
	})

	t.Run("New Nested Configuration Mapping", func(t *testing.T) {
		testNewNestedConfigurationMapping(t, db, logger)
	})

	t.Run("Mixed Configuration Mapping", func(t *testing.T) {
		testMixedConfigurationMapping(t, db, logger)
	})

	t.Run("Configuration Schema Validation", func(t *testing.T) {
		testConfigurationSchemaValidation(t, db, logger)
	})

	t.Run("Backward Compatibility", func(t *testing.T) {
		testBackwardCompatibility(t, db, logger)
	})
}

func createTestPlugins(t *testing.T, db *gorm.DB) {
	// Plugin with legacy flat configuration
	legacyPlugin := models.Plugin{
		Name:        "legacy-plugin",
		Version:     "1.0.0",
		Type:        "deployment",
		Description: "Plugin with legacy configuration structure",
		Enabled:     true,
		// No ConfigurationSchema - legacy format
	}
	require.NoError(t, db.Create(&legacyPlugin).Error)

	// Plugin with new nested configuration schema
	newConfigSchema := models.PluginConfigSchema{
		Type: "object",
		Properties: map[string]models.PluginConfigProperty{
			"deployment": {
				Type: "object",
				Properties: map[string]models.PluginConfigProperty{
					"strategy": {
						Type:        "string",
						Description: "Deployment strategy",
						Default:     "rolling",
						Enum:        []interface{}{"rolling", "recreate", "blue-green"},
					},
					"replicas": {
						Type:        "integer",
						Description: "Number of replicas",
						Default:     float64(3),
						Minimum:     float64(1),
						Maximum:     float64(10),
					},
				},
				Required: []string{"strategy"},
			},
			"networking": {
				Type: "object",
				Properties: map[string]models.PluginConfigProperty{
					"port": {
						Type:        "integer",
						Description: "Service port",
						Default:     float64(8080),
					},
					"protocol": {
						Type:        "string",
						Description: "Protocol type",
						Default:     "TCP",
						Enum:        []interface{}{"TCP", "UDP"},
					},
				},
			},
		},
		Required: []string{"deployment"},
	}

	newPlugin := models.Plugin{
		Name:                "new-plugin",
		Version:             "2.0.0",
		Type:                "deployment",
		Description:         "Plugin with new nested configuration structure",
		Enabled:             true,
		ConfigurationSchema: &newConfigSchema,
	}
	require.NoError(t, db.Create(&newPlugin).Error)

	// Plugin with mixed configuration (both old and new)
	mixedPlugin := models.Plugin{
		Name:        "mixed-plugin",
		Version:     "1.5.0",
		Type:        "deployment",
		Description: "Plugin with mixed configuration support",
		Enabled:     true,
		ConfigurationSchema: &models.PluginConfigSchema{
			Type: "object",
			Properties: map[string]models.PluginConfigProperty{
				"app": {
					Type: "object",
					Properties: map[string]models.PluginConfigProperty{
						"name": {
							Type:        "string",
							Description: "Application name",
						},
					},
				},
			},
		},
	}
	require.NoError(t, db.Create(&mixedPlugin).Error)
}

func testLegacyConfigurationMapping(t *testing.T, db *gorm.DB, logger *zap.Logger) {
	// Mock plugin manager for testing
	mockPluginManager := &MockPluginManager{
		plugins: map[string]*MockPluginInstance{
			"legacy-plugin": {
				Name:    "legacy-plugin",
				Version: "1.0.0",
				Type:    "deployment",
				Enabled: true,
			},
		},
	}

	service := services.NewDeploymentPluginService(db, mockPluginManager, logger)

	// Test legacy flat configuration
	legacyConfig := map[string]interface{}{
		"host":     "example.com",
		"port":     8080,
		"username": "admin",
		"password": "secret",
		"ssl":      true,
	}

	// Create environment mapping with legacy configuration
	mapping := &services.EnvironmentPluginMapping{
		ID:                "test-env-legacy",
		EnvironmentID:     "test-environment",
		DefaultPluginID:   "legacy-plugin",
		AllowUserOverride: true,
		AdminConfig:       legacyConfig,
	}

	err := service.SetEnvironmentPluginMapping(context.Background(), mapping)
	require.NoError(t, err)

	// Retrieve and verify the mapping
	retrieved, err := service.GetDefaultPluginForEnvironment(context.Background(), "test-environment")
	require.NoError(t, err)
	assert.NotNil(t, retrieved)
	assert.Equal(t, "legacy-plugin", retrieved.ID)

	// Verify configuration is properly mapped
	envMapping, err := service.GetEnvironmentPluginMappings(context.Background())
	require.NoError(t, err)
	assert.Len(t, envMapping, 1)
	assert.Equal(t, legacyConfig["host"], envMapping[0].AdminConfig["host"])
	assert.Equal(t, legacyConfig["port"], envMapping[0].AdminConfig["port"])
}

func testNewNestedConfigurationMapping(t *testing.T, db *gorm.DB, logger *zap.Logger) {
	mockPluginManager := &MockPluginManager{
		plugins: map[string]*MockPluginInstance{
			"new-plugin": {
				Name:    "new-plugin",
				Version: "2.0.0",
				Type:    "deployment",
				Enabled: true,
			},
		},
	}

	service := services.NewDeploymentPluginService(db, mockPluginManager, logger)

	// Test new nested configuration structure
	nestedConfig := map[string]interface{}{
		"deployment": map[string]interface{}{
			"strategy": "blue-green",
			"replicas": 5,
		},
		"networking": map[string]interface{}{
			"port":     9090,
			"protocol": "TCP",
		},
		"monitoring": map[string]interface{}{
			"enabled": true,
			"metrics": map[string]interface{}{
				"prometheus": true,
				"grafana":    true,
			},
		},
	}

	// Create environment mapping with nested configuration
	mapping := &services.EnvironmentPluginMapping{
		ID:                "test-env-new",
		EnvironmentID:     "test-environment-new",
		DefaultPluginID:   "new-plugin",
		AllowUserOverride: false,
		AdminConfig:       nestedConfig,
	}

	err := service.SetEnvironmentPluginMapping(context.Background(), mapping)
	require.NoError(t, err)

	// Retrieve and verify the nested configuration
	envMappings, err := service.GetEnvironmentPluginMappings(context.Background())
	require.NoError(t, err)

	var newMapping *services.EnvironmentPluginMapping
	for _, m := range envMappings {
		if m.EnvironmentID == "test-environment-new" {
			newMapping = m
			break
		}
	}
	require.NotNil(t, newMapping)

	// Verify nested structure is preserved
	deployment, ok := newMapping.AdminConfig["deployment"].(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, "blue-green", deployment["strategy"])
	assert.Equal(t, float64(5), deployment["replicas"])

	networking, ok := newMapping.AdminConfig["networking"].(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, float64(9090), networking["port"])
	assert.Equal(t, "TCP", networking["protocol"])
}

func testMixedConfigurationMapping(t *testing.T, db *gorm.DB, logger *zap.Logger) {
	mockPluginManager := &MockPluginManager{
		plugins: map[string]*MockPluginInstance{
			"mixed-plugin": {
				Name:    "mixed-plugin",
				Version: "1.5.0",
				Type:    "deployment",
				Enabled: true,
			},
		},
	}

	service := services.NewDeploymentPluginService(db, mockPluginManager, logger)

	// Test mixed configuration (both flat and nested)
	mixedConfig := map[string]interface{}{
		// Legacy flat configuration
		"timeout": 30,
		"retries": 3,

		// New nested configuration
		"app": map[string]interface{}{
			"name":    "test-application",
			"version": "1.0.0",
		},
		"database": map[string]interface{}{
			"host":     "db.example.com",
			"port":     5432,
			"ssl_mode": "require",
		},
	}

	// Create deployment request to test configuration merging
	request := &services.DeploymentPluginRequest{
		DeployableID:  "test-deployable",
		EnvironmentID: "test-environment-mixed",
		PluginID:      "mixed-plugin",
		Configuration: map[string]interface{}{
			"retries": 5, // Override admin config
			"app": map[string]interface{}{
				"debug": true, // Add to nested config
			},
		},
	}

	// Set up environment mapping
	mapping := &services.EnvironmentPluginMapping{
		ID:                "test-env-mixed",
		EnvironmentID:     "test-environment-mixed",
		DefaultPluginID:   "mixed-plugin",
		AllowUserOverride: true,
		AdminConfig:       mixedConfig,
	}

	err := service.SetEnvironmentPluginMapping(context.Background(), mapping)
	require.NoError(t, err)

	// Test that configuration merging works correctly
	// Note: This would require the actual plugin execution which needs more setup
	// For now, we verify the mapping storage and retrieval
	retrieved, err := service.GetDefaultPluginForEnvironment(context.Background(), "test-environment-mixed")
	require.NoError(t, err)
	assert.NotNil(t, retrieved)
	assert.Equal(t, "mixed-plugin", retrieved.ID)
}

func testConfigurationSchemaValidation(t *testing.T, db *gorm.DB, logger *zap.Logger) {
	mockPluginManager := &MockPluginManager{
		plugins: map[string]*MockPluginInstance{
			"new-plugin": {
				Name:    "new-plugin",
				Version: "2.0.0",
				Type:    "deployment",
				Enabled: true,
			},
		},
	}

	service := services.NewDeploymentPluginService(db, mockPluginManager, logger)

	// Test configuration validation against schema
	validConfig := map[string]interface{}{
		"deployment": map[string]interface{}{
			"strategy": "rolling",
			"replicas": 3,
		},
	}

	invalidConfig := map[string]interface{}{
		"deployment": map[string]interface{}{
			"strategy": "invalid-strategy", // Not in enum
			"replicas": 15,                 // Exceeds maximum
		},
	}

	// Validate valid configuration
	result, err := service.ValidateConfiguration(context.Background(), "new-plugin", validConfig)
	require.NoError(t, err)
	assert.True(t, result.Valid)
	assert.Empty(t, result.Errors)

	// Validate invalid configuration
	result, err = service.ValidateConfiguration(context.Background(), "new-plugin", invalidConfig)
	require.NoError(t, err)
	assert.False(t, result.Valid)
	assert.NotEmpty(t, result.Errors)
}

func testBackwardCompatibility(t *testing.T, db *gorm.DB, logger *zap.Logger) {
	mockPluginManager := &MockPluginManager{
		plugins: map[string]*MockPluginInstance{
			"legacy-plugin": {
				Name:    "legacy-plugin",
				Version: "1.0.0",
				Type:    "deployment",
				Enabled: true,
			},
		},
	}

	service := services.NewDeploymentPluginService(db, mockPluginManager, logger)

	// Test that plugins without ConfigurationSchema still work
	legacyConfig := map[string]interface{}{
		"host": "legacy.example.com",
		"port": 3306,
	}

	mapping := &services.EnvironmentPluginMapping{
		ID:                "test-env-backward-compat",
		EnvironmentID:     "test-environment-legacy",
		DefaultPluginID:   "legacy-plugin",
		AllowUserOverride: true,
		AdminConfig:       legacyConfig,
	}

	err := service.SetEnvironmentPluginMapping(context.Background(), mapping)
	require.NoError(t, err)

	// Verify backward compatibility
	retrieved, err := service.GetDefaultPluginForEnvironment(context.Background(), "test-environment-legacy")
	require.NoError(t, err)
	assert.NotNil(t, retrieved)
	assert.Equal(t, "legacy-plugin", retrieved.ID)

	// Verify that configuration validation gracefully handles plugins without schema
	result, err := service.ValidateConfiguration(context.Background(), "legacy-plugin", legacyConfig)
	require.NoError(t, err)
	// For legacy plugins without schema, validation should pass
	assert.True(t, result.Valid)
}

// Mock implementations for testing
type MockPluginManager struct {
	plugins map[string]*MockPluginInstance
}

func (m *MockPluginManager) GetPlugin(name string) (*services.PluginInstance, error) {
	if plugin, exists := m.plugins[name]; exists {
		return &services.PluginInstance{
			Name:    plugin.Name,
			Version: plugin.Version,
			Type:    plugin.Type,
			Enabled: plugin.Enabled,
		}, nil
	}
	return nil, fmt.Errorf("plugin %s not found", name)
}

func (m *MockPluginManager) GetAllPlugins() map[string]*services.PluginInstance {
	result := make(map[string]*services.PluginInstance)
	for name, plugin := range m.plugins {
		result[name] = &services.PluginInstance{
			Name:    plugin.Name,
			Version: plugin.Version,
			Type:    plugin.Type,
			Enabled: plugin.Enabled,
		}
	}
	return result
}

type MockPluginInstance struct {
	Name    string
	Version string
	Type    string
	Enabled bool
}

func getCurrentTimeMillis() int64 {
	return time.Now().UnixNano() / int64(time.Millisecond)
}
