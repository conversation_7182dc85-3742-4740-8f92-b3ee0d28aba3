package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/clients"
	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type SecretMappingIntegrationTestSuite struct {
	suite.Suite
	db     *gorm.DB
	router *gin.Engine
	server *httptest.Server
}

func (suite *SecretMappingIntegrationTestSuite) SetupSuite() {
	// Setup in-memory database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Auto-migrate models
	err = db.AutoMigrate(
		&models.WorkflowDefinition{},
		&models.WorkflowExecution{},
		&models.StepExecution{},
		&models.WorkflowTemplate{},
	)
	suite.Require().NoError(err)

	suite.db = db

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()

	// Setup test server
	suite.server = httptest.NewServer(suite.router)
}

func (suite *SecretMappingIntegrationTestSuite) TearDownSuite() {
	suite.server.Close()
}

func (suite *SecretMappingIntegrationTestSuite) SetupTest() {
	// Clean database before each test
	suite.db.Exec("DELETE FROM workflow_executions")
	suite.db.Exec("DELETE FROM workflow_definitions")
	suite.db.Exec("DELETE FROM workflow_templates")
}

func (suite *SecretMappingIntegrationTestSuite) TestCompleteSecretMappingFlow() {
	// 1. Create a workflow template with secret parameters
	template := &models.WorkflowTemplate{
		ID:          "ssh-deployment-template",
		Name:        "SSH Deployment Template",
		Description: "Template for SSH-based deployments",
		Parameters: []models.WorkflowParameter{
			{
				Name:        "TARGET_HOST",
				Type:        "string",
				Description: "Target host for deployment",
				Required:    true,
			},
			{
				Name:        "SSH_USERNAME",
				Type:        "secret",
				Description: "SSH username",
				Required:    true,
			},
			{
				Name:        "SSH_PRIVATE_KEY",
				Type:        "secret",
				Description: "SSH private key",
				Required:    true,
			},
		},
		Steps: []models.WorkflowStep{
			{
				ID:   "deploy-step",
				Name: "Deploy via SSH",
				Type: "script",
				Config: map[string]interface{}{
					"language": "bash",
					"script":   "ssh -i /tmp/key $SSH_USERNAME@$TARGET_HOST 'echo Deployment successful'",
				},
			},
		},
		CreatedBy:  "test-user",
		AuthorName: "Test Author",
		Version:    "1.0.0",
		IsPublic:   true,
		IsFeatured: false,
	}

	err := suite.db.Create(template).Error
	suite.Require().NoError(err)

	// 2. Execute template with secret mapping
	executionRequest := map[string]interface{}{
		"projectId": "test-project",
		"parameters": map[string]interface{}{
			"TARGET_HOST": "test.example.com",
		},
		"secretMapping": map[string]string{
			"SSH_USERNAME":    "DEV_USERNAME",
			"SSH_PRIVATE_KEY": "DEV_KEY",
		},
		"startedBy": "test-user",
	}

	// Mock secrets service response
	mockSecretsHandler := func(w http.ResponseWriter, r *http.Request) {
		var req clients.WorkflowSecretsRequest
		json.NewDecoder(r.Body).Decode(&req)

		// Verify secret mapping is passed correctly
		assert.Equal(suite.T(), "DEV_USERNAME", req.SecretMapping["SSH_USERNAME"])
		assert.Equal(suite.T(), "DEV_KEY", req.SecretMapping["SSH_PRIVATE_KEY"])

		// Return mock secrets response
		response := clients.WorkflowSecretsResponse{
			Variables: []clients.SecretVariable{
				{
					Name:     "SSH_USERNAME",
					Type:     "env",
					Value:    "dev-user",
					SecretID: "secret_1",
				},
				{
					Name:     "SSH_PRIVATE_KEY",
					Type:     "env",
					Value:    "dev-private-key",
					SecretID: "secret_2",
				},
			},
			Errors:   []string{},
			Metadata: map[string]interface{}{},
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}

	// Setup mock secrets service
	mockSecretsServer := httptest.NewServer(http.HandlerFunc(mockSecretsHandler))
	defer mockSecretsServer.Close()

	// 3. Make execution request
	jsonData, _ := json.Marshal(executionRequest)
	req, _ := http.NewRequest("POST", fmt.Sprintf("/api/v1/templates/%s/execute", template.ID), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer test-token")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// 4. Verify execution was created with secret mapping
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var executionResponse models.WorkflowExecution
	err = json.Unmarshal(w.Body.Bytes(), &executionResponse)
	suite.Require().NoError(err)

	// Verify secret mapping is stored
	assert.Equal(suite.T(), "DEV_USERNAME", executionResponse.SecretMapping["SSH_USERNAME"])
	assert.Equal(suite.T(), "DEV_KEY", executionResponse.SecretMapping["SSH_PRIVATE_KEY"])

	// 5. Verify execution is stored in database
	var dbExecution models.WorkflowExecution
	err = suite.db.First(&dbExecution, "id = ?", executionResponse.ID).Error
	suite.Require().NoError(err)

	assert.Equal(suite.T(), "test-project", dbExecution.ProjectID)
	assert.Equal(suite.T(), template.ID, dbExecution.WorkflowID)
	assert.NotNil(suite.T(), dbExecution.SecretMapping)
}

func (suite *SecretMappingIntegrationTestSuite) TestSecretMappingValidation() {
	// Test validation of secret mapping before execution
	template := &models.WorkflowTemplate{
		ID:   "validation-template",
		Name: "Validation Template",
		Parameters: []models.WorkflowParameter{
			{
				Name:     "REQUIRED_SECRET",
				Type:     "secret",
				Required: true,
			},
			{
				Name:     "OPTIONAL_SECRET",
				Type:     "secret",
				Required: false,
			},
		},
		CreatedBy:  "test-user",
		Version:    "1.0.0",
		IsPublic:   true,
		IsFeatured: false,
	}

	err := suite.db.Create(template).Error
	suite.Require().NoError(err)

	tests := []struct {
		name           string
		secretMapping  map[string]string
		expectedStatus int
		expectedError  string
	}{
		{
			name: "Valid mapping with required secret",
			secretMapping: map[string]string{
				"REQUIRED_SECRET": "USER_REQUIRED_SECRET",
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Missing required secret mapping",
			secretMapping:  map[string]string{},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "missing required secret mapping",
		},
		{
			name: "Valid mapping with both required and optional",
			secretMapping: map[string]string{
				"REQUIRED_SECRET": "USER_REQUIRED_SECRET",
				"OPTIONAL_SECRET": "USER_OPTIONAL_SECRET",
			},
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			executionRequest := map[string]interface{}{
				"projectId":     "test-project",
				"secretMapping": tt.secretMapping,
				"startedBy":     "test-user",
			}

			jsonData, _ := json.Marshal(executionRequest)
			req, _ := http.NewRequest("POST", fmt.Sprintf("/api/v1/templates/%s/execute", template.ID), bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("Authorization", "Bearer test-token")

			w := httptest.NewRecorder()
			suite.router.ServeHTTP(w, req)

			assert.Equal(suite.T(), tt.expectedStatus, w.Code)

			if tt.expectedError != "" {
				var errorResponse map[string]interface{}
				json.Unmarshal(w.Body.Bytes(), &errorResponse)
				assert.Contains(suite.T(), errorResponse["error"].(string), tt.expectedError)
			}
		})
	}
}

func (suite *SecretMappingIntegrationTestSuite) TestSecretMappingWithDifferentEnvironments() {
	// Test secret mapping with environment-specific secrets
	template := &models.WorkflowTemplate{
		ID:   "env-template",
		Name: "Environment Template",
		Parameters: []models.WorkflowParameter{
			{
				Name:     "DB_PASSWORD",
				Type:     "secret",
				Required: true,
			},
		},
		CreatedBy:  "test-user",
		Version:    "1.0.0",
		IsPublic:   true,
		IsFeatured: false,
	}

	err := suite.db.Create(template).Error
	suite.Require().NoError(err)

	environments := []string{"dev", "staging", "production"}

	for _, env := range environments {
		suite.Run(fmt.Sprintf("Environment_%s", env), func() {
			executionRequest := map[string]interface{}{
				"projectId": "test-project",
				"parameters": map[string]interface{}{
					"ENVIRONMENT": env,
				},
				"secretMapping": map[string]string{
					"DB_PASSWORD": fmt.Sprintf("%s_DB_PASSWORD", env),
				},
				"startedBy": "test-user",
			}

			// Mock environment-specific secrets response
			mockSecretsHandler := func(w http.ResponseWriter, r *http.Request) {
				var req clients.WorkflowSecretsRequest
				json.NewDecoder(r.Body).Decode(&req)

				expectedSecretName := fmt.Sprintf("%s_DB_PASSWORD", env)
				assert.Equal(suite.T(), expectedSecretName, req.SecretMapping["DB_PASSWORD"])

				response := clients.WorkflowSecretsResponse{
					Variables: []clients.SecretVariable{
						{
							Name:        "DB_PASSWORD",
							Type:        "env",
							Value:       fmt.Sprintf("%s-db-password", env),
							Environment: env,
							SecretID:    fmt.Sprintf("secret_%s", env),
						},
					},
					Errors:   []string{},
					Metadata: map[string]interface{}{},
				}

				w.Header().Set("Content-Type", "application/json")
				json.NewEncoder(w).Encode(response)
			}

			mockSecretsServer := httptest.NewServer(http.HandlerFunc(mockSecretsHandler))
			defer mockSecretsServer.Close()

			jsonData, _ := json.Marshal(executionRequest)
			req, _ := http.NewRequest("POST", fmt.Sprintf("/api/v1/templates/%s/execute", template.ID), bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("Authorization", "Bearer test-token")

			w := httptest.NewRecorder()
			suite.router.ServeHTTP(w, req)

			assert.Equal(suite.T(), http.StatusOK, w.Code)

			var executionResponse models.WorkflowExecution
			err = json.Unmarshal(w.Body.Bytes(), &executionResponse)
			suite.Require().NoError(err)

			expectedSecretName := fmt.Sprintf("%s_DB_PASSWORD", env)
			assert.Equal(suite.T(), expectedSecretName, executionResponse.SecretMapping["DB_PASSWORD"])
		})
	}
}

func (suite *SecretMappingIntegrationTestSuite) TestConcurrentExecutionsWithDifferentMappings() {
	// Test concurrent executions with different secret mappings
	template := &models.WorkflowTemplate{
		ID:   "concurrent-template",
		Name: "Concurrent Template",
		Parameters: []models.WorkflowParameter{
			{
				Name:     "API_KEY",
				Type:     "secret",
				Required: true,
			},
		},
		CreatedBy:  "test-user",
		Version:    "1.0.0",
		IsPublic:   true,
		IsFeatured: false,
	}

	err := suite.db.Create(template).Error
	suite.Require().NoError(err)

	// Create multiple concurrent executions with different secret mappings
	executions := []struct {
		projectId     string
		secretMapping map[string]string
	}{
		{
			projectId: "project-a",
			secretMapping: map[string]string{
				"API_KEY": "PROJECT_A_API_KEY",
			},
		},
		{
			projectId: "project-b",
			secretMapping: map[string]string{
				"API_KEY": "PROJECT_B_API_KEY",
			},
		},
		{
			projectId: "project-c",
			secretMapping: map[string]string{
				"API_KEY": "PROJECT_C_API_KEY",
			},
		},
	}

	// Execute all requests concurrently
	results := make(chan struct {
		projectId string
		success   bool
		execution models.WorkflowExecution
	}, len(executions))

	for _, exec := range executions {
		go func(projectId string, secretMapping map[string]string) {
			executionRequest := map[string]interface{}{
				"projectId":     projectId,
				"secretMapping": secretMapping,
				"startedBy":     "test-user",
			}

			jsonData, _ := json.Marshal(executionRequest)
			req, _ := http.NewRequest("POST", fmt.Sprintf("/api/v1/templates/%s/execute", template.ID), bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("Authorization", "Bearer test-token")

			w := httptest.NewRecorder()
			suite.router.ServeHTTP(w, req)

			success := w.Code == http.StatusOK
			var execution models.WorkflowExecution
			if success {
				json.Unmarshal(w.Body.Bytes(), &execution)
			}

			results <- struct {
				projectId string
				success   bool
				execution models.WorkflowExecution
			}{projectId, success, execution}
		}(exec.projectId, exec.secretMapping)
	}

	// Collect results
	for i := 0; i < len(executions); i++ {
		select {
		case result := <-results:
			assert.True(suite.T(), result.success, "Execution failed for project %s", result.projectId)
			assert.Equal(suite.T(), result.projectId, result.execution.ProjectID)

			// Verify secret mapping is correct for each project
			expectedSecretName := fmt.Sprintf("%s_API_KEY", result.projectId[8:]) // Extract A, B, C from project-a, etc.
			expectedSecretName = fmt.Sprintf("PROJECT_%s_API_KEY", expectedSecretName)
			assert.Equal(suite.T(), expectedSecretName, result.execution.SecretMapping["API_KEY"])

		case <-time.After(10 * time.Second):
			suite.T().Fatal("Timeout waiting for concurrent execution results")
		}
	}
}

func TestSecretMappingIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(SecretMappingIntegrationTestSuite))
}
