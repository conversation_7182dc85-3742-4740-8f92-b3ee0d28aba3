package unit

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/claudio/deploy-orchestrator/workflow-service/clients"
	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockSecretsClient is a mock implementation of the secrets client
type MockSecretsClient struct {
	mock.Mock
}

func (m *MockSecretsClient) GetWorkflowSecrets(ctx context.Context, req clients.WorkflowSecretsRequest, authToken string) (*clients.WorkflowSecretsResponse, error) {
	args := m.Called(ctx, req, authToken)
	return args.Get(0).(*clients.WorkflowSecretsResponse), args.Error(1)
}

func TestSecretMappingBasicFunctionality(t *testing.T) {
	tests := []struct {
		name           string
		secretMapping  map[string]string
		projectSecrets []clients.SecretVariable
		expectedResult []clients.SecretVariable
	}{
		{
			name: "Direct secret mapping",
			secretMapping: map[string]string{
				"SSH_USERNAME":    "DEV_USERNAME",
				"SSH_PRIVATE_KEY": "DEV_KEY",
			},
			projectSecrets: []clients.SecretVariable{
				{
					Name:     "DEV_USERNAME",
					Type:     "env",
					Value:    "dev-user",
					SecretID: "secret_1",
				},
				{
					Name:     "DEV_KEY",
					Type:     "env",
					Value:    "dev-private-key",
					SecretID: "secret_2",
				},
			},
			expectedResult: []clients.SecretVariable{
				{
					Name:     "SSH_USERNAME",
					Type:     "env",
					Value:    "dev-user",
					SecretID: "secret_1",
				},
				{
					Name:     "SSH_PRIVATE_KEY",
					Type:     "env",
					Value:    "dev-private-key",
					SecretID: "secret_2",
				},
			},
		},
		{
			name: "Partial secret mapping",
			secretMapping: map[string]string{
				"SSH_USERNAME": "QA_USERNAME",
			},
			projectSecrets: []clients.SecretVariable{
				{
					Name:     "QA_USERNAME",
					Type:     "env",
					Value:    "qa-user",
					SecretID: "secret_3",
				},
				{
					Name:     "EXISTING_SECRET",
					Type:     "env",
					Value:    "existing-value",
					SecretID: "secret_4",
				},
			},
			expectedResult: []clients.SecretVariable{
				{
					Name:     "SSH_USERNAME",
					Type:     "env",
					Value:    "qa-user",
					SecretID: "secret_3",
				},
				{
					Name:     "EXISTING_SECRET",
					Type:     "env",
					Value:    "existing-value",
					SecretID: "secret_4",
				},
			},
		},
		{
			name:          "Empty secret mapping",
			secretMapping: map[string]string{},
			projectSecrets: []clients.SecretVariable{
				{
					Name:     "ORIGINAL_SECRET",
					Type:     "env",
					Value:    "original-value",
					SecretID: "secret_5",
				},
			},
			expectedResult: []clients.SecretVariable{
				{
					Name:     "ORIGINAL_SECRET",
					Type:     "env",
					Value:    "original-value",
					SecretID: "secret_5",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockClient := &MockSecretsClient{}

			// Mock the secrets client response
			mockResponse := &clients.WorkflowSecretsResponse{
				Variables: tt.projectSecrets,
				Errors:    []string{},
				Metadata:  map[string]interface{}{},
			}

			mockClient.On("GetWorkflowSecrets", mock.Anything, mock.MatchedBy(func(req clients.WorkflowSecretsRequest) bool {
				return req.SecretMapping != nil && len(req.SecretMapping) == len(tt.secretMapping)
			}), mock.Anything).Return(mockResponse, nil)

			// Create request with secret mapping
			req := clients.WorkflowSecretsRequest{
				WorkflowID:    "test-workflow",
				ExecutionID:   "test-execution",
				ProjectID:     "test-project",
				SecretMapping: tt.secretMapping,
			}

			// Call the mocked client
			response, err := mockClient.GetWorkflowSecrets(context.Background(), req, "test-token")

			// Assertions
			assert.NoError(t, err)
			assert.NotNil(t, response)

			// Apply secret mapping transformation (simulating backend logic)
			transformedSecrets := applySecretMapping(response.Variables, tt.secretMapping)

			assert.Equal(t, len(tt.expectedResult), len(transformedSecrets))
			for i, expected := range tt.expectedResult {
				assert.Equal(t, expected.Name, transformedSecrets[i].Name)
				assert.Equal(t, expected.Value, transformedSecrets[i].Value)
				assert.Equal(t, expected.Type, transformedSecrets[i].Type)
			}

			mockClient.AssertExpectations(t)
		})
	}
}

func TestSecretMappingEdgeCases(t *testing.T) {
	tests := []struct {
		name           string
		secretMapping  map[string]string
		projectSecrets []clients.SecretVariable
		expectError    bool
		errorMessage   string
	}{
		{
			name: "Mapping to non-existent secret",
			secretMapping: map[string]string{
				"SSH_USERNAME": "NON_EXISTENT_SECRET",
			},
			projectSecrets: []clients.SecretVariable{
				{
					Name:     "EXISTING_SECRET",
					Type:     "env",
					Value:    "value",
					SecretID: "secret_1",
				},
			},
			expectError:  false, // Should not error, just filter out non-existent secrets
			errorMessage: "",
		},
		{
			name: "Duplicate template variable mapping",
			secretMapping: map[string]string{
				"SSH_USERNAME": "DEV_USERNAME",
				"SSH_USER":     "DEV_USERNAME", // Same secret mapped to different template vars
			},
			projectSecrets: []clients.SecretVariable{
				{
					Name:     "DEV_USERNAME",
					Type:     "env",
					Value:    "dev-user",
					SecretID: "secret_1",
				},
			},
			expectError:  false,
			errorMessage: "",
		},
		{
			name: "Case sensitivity in mapping",
			secretMapping: map[string]string{
				"SSH_USERNAME": "dev_username", // lowercase
			},
			projectSecrets: []clients.SecretVariable{
				{
					Name:     "DEV_USERNAME", // uppercase
					Type:     "env",
					Value:    "dev-user",
					SecretID: "secret_1",
				},
			},
			expectError:  false,
			errorMessage: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockClient := &MockSecretsClient{}

			mockResponse := &clients.WorkflowSecretsResponse{
				Variables: tt.projectSecrets,
				Errors:    []string{},
				Metadata:  map[string]interface{}{},
			}

			if tt.expectError {
				mockClient.On("GetWorkflowSecrets", mock.Anything, mock.Anything, mock.Anything).Return(nil, assert.AnError)
			} else {
				mockClient.On("GetWorkflowSecrets", mock.Anything, mock.Anything, mock.Anything).Return(mockResponse, nil)
			}

			req := clients.WorkflowSecretsRequest{
				WorkflowID:    "test-workflow",
				ExecutionID:   "test-execution",
				ProjectID:     "test-project",
				SecretMapping: tt.secretMapping,
			}

			response, err := mockClient.GetWorkflowSecrets(context.Background(), req, "test-token")

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMessage)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, response)
			}

			mockClient.AssertExpectations(t)
		})
	}
}

func TestWorkflowExecutionWithSecretMapping(t *testing.T) {
	// Test workflow execution model with secret mapping
	execution := &models.WorkflowExecution{
		ID:        "test-execution",
		ProjectID: "test-project",
		Parameters: map[string]interface{}{
			"TARGET_HOST": "test.example.com",
		},
		SecretMapping: map[string]string{
			"SSH_USERNAME":    "DEV_USERNAME",
			"SSH_PRIVATE_KEY": "DEV_KEY",
		},
	}

	// Test JSON marshaling/unmarshaling
	jsonData, err := json.Marshal(execution)
	assert.NoError(t, err)

	var unmarshaledExecution models.WorkflowExecution
	err = json.Unmarshal(jsonData, &unmarshaledExecution)
	assert.NoError(t, err)

	assert.Equal(t, execution.ID, unmarshaledExecution.ID)
	assert.Equal(t, execution.ProjectID, unmarshaledExecution.ProjectID)
	assert.Equal(t, execution.SecretMapping["SSH_USERNAME"], unmarshaledExecution.SecretMapping["SSH_USERNAME"])
	assert.Equal(t, execution.SecretMapping["SSH_PRIVATE_KEY"], unmarshaledExecution.SecretMapping["SSH_PRIVATE_KEY"])
}

// Helper function to simulate secret mapping transformation
func applySecretMapping(secrets []clients.SecretVariable, mapping map[string]string) []clients.SecretVariable {
	if len(mapping) == 0 {
		return secrets
	}

	var result []clients.SecretVariable
	secretsByName := make(map[string]clients.SecretVariable)

	// Index secrets by name
	for _, secret := range secrets {
		secretsByName[secret.Name] = secret
	}

	// Apply mapping
	for templateVar, userSecretName := range mapping {
		if secret, exists := secretsByName[userSecretName]; exists {
			mappedSecret := secret
			mappedSecret.Name = templateVar // Map to template variable name
			result = append(result, mappedSecret)
			delete(secretsByName, userSecretName) // Remove from original list
		}
	}

	// Add remaining unmapped secrets
	for _, secret := range secretsByName {
		result = append(result, secret)
	}

	return result
}

func TestSecretMappingValidation(t *testing.T) {
	tests := []struct {
		name              string
		templateVariables []string
		projectSecrets    []string
		secretMapping     map[string]string
		expectedValid     bool
		expectedMissing   []string
	}{
		{
			name:              "All required secrets mapped",
			templateVariables: []string{"SSH_USERNAME", "SSH_PRIVATE_KEY"},
			projectSecrets:    []string{"DEV_USERNAME", "DEV_KEY", "QA_USERNAME"},
			secretMapping: map[string]string{
				"SSH_USERNAME":    "DEV_USERNAME",
				"SSH_PRIVATE_KEY": "DEV_KEY",
			},
			expectedValid:   true,
			expectedMissing: []string{},
		},
		{
			name:              "Missing required secret mapping",
			templateVariables: []string{"SSH_USERNAME", "SSH_PRIVATE_KEY", "API_TOKEN"},
			projectSecrets:    []string{"DEV_USERNAME", "DEV_KEY"},
			secretMapping: map[string]string{
				"SSH_USERNAME":    "DEV_USERNAME",
				"SSH_PRIVATE_KEY": "DEV_KEY",
			},
			expectedValid:   false,
			expectedMissing: []string{"API_TOKEN"},
		},
		{
			name:              "Mapping to non-existent project secret",
			templateVariables: []string{"SSH_USERNAME"},
			projectSecrets:    []string{"DEV_USERNAME"},
			secretMapping: map[string]string{
				"SSH_USERNAME": "NON_EXISTENT_SECRET",
			},
			expectedValid:   false,
			expectedMissing: []string{"SSH_USERNAME"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isValid, missing := validateSecretMapping(tt.templateVariables, tt.projectSecrets, tt.secretMapping)

			assert.Equal(t, tt.expectedValid, isValid)
			assert.ElementsMatch(t, tt.expectedMissing, missing)
		})
	}
}

// Helper function for secret mapping validation
func validateSecretMapping(templateVars []string, projectSecrets []string, mapping map[string]string) (bool, []string) {
	projectSecretSet := make(map[string]bool)
	for _, secret := range projectSecrets {
		projectSecretSet[secret] = true
	}

	var missing []string
	for _, templateVar := range templateVars {
		if mappedSecret, exists := mapping[templateVar]; exists {
			if !projectSecretSet[mappedSecret] {
				missing = append(missing, templateVar)
			}
		} else {
			missing = append(missing, templateVar)
		}
	}

	return len(missing) == 0, missing
}
