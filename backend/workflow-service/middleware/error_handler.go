package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ErrorHandler returns a middleware that handles errors
func ErrorHandler() gin.HandlerFunc {
	logger, _ := zap.NewProduction()

	return func(c *gin.Context) {
		c.Next()

		// Handle any errors that occurred during request processing
		if len(c.Errors) > 0 {
			err := c.Errors.Last()

			logger.Error("Request error",
				zap.String("method", c.Request.Method),
				zap.String("path", c.Request.URL.Path),
				zap.Error(err),
			)

			// Return error response if not already sent
			if !c.Writer.Written() {
				switch err.Type {
				case gin.ErrorTypeBind:
					c.J<PERSON>(http.StatusBadRequest, gin.H{
						"error":   "Invalid request data",
						"details": err.Error(),
					})
				case gin.ErrorTypePublic:
					c.<PERSON>(http.StatusBadRequest, gin.H{
						"error": err.Error(),
					})
				default:
					c.<PERSON>(http.StatusInternalServerError, gin.H{
						"error": "Internal server error",
					})
				}
			}
		}
	}
}
