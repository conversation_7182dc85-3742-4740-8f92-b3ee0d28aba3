package instance

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"go.uber.org/zap"
)

// ExecutionCoordinator coordinates workflow execution across instances
type ExecutionCoordinator struct {
	registry InstanceRegistry
	logger   *zap.Logger
	client   *http.Client
}

// ExecutionRequest represents a request to execute a workflow on a specific instance
type ExecutionRequest struct {
	WorkflowID   string                 `json:"workflowId"`
	Parameters   map[string]interface{} `json:"parameters"`
	StartedBy    string                 `json:"startedBy"`
	TriggerType  string                 `json:"triggerType"`
	TriggerData  map[string]interface{} `json:"triggerData"`
	Requirements *ExecutionRequirements `json:"requirements,omitempty"`
}

// ExecutionResponse represents the response from a workflow execution request
type ExecutionResponse struct {
	ExecutionID string `json:"executionId"`
	InstanceID  string `json:"instanceId"`
	Status      string `json:"status"`
	Message     string `json:"message"`
}

// NewExecutionCoordinator creates a new execution coordinator
func NewExecutionCoordinator(registry InstanceRegistry, logger *zap.Logger) *ExecutionCoordinator {
	return &ExecutionCoordinator{
		registry: registry,
		logger:   logger,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// ExecuteWorkflow executes a workflow on the best available instance
func (ec *ExecutionCoordinator) ExecuteWorkflow(ctx context.Context, request *ExecutionRequest) (*ExecutionResponse, error) {
	// Find the best instance for execution
	instance, err := ec.findBestInstance(ctx, request.Requirements)
	if err != nil {
		return nil, fmt.Errorf("failed to find suitable instance: %w", err)
	}

	ec.logger.Info("Selected instance for workflow execution",
		zap.String("workflowId", request.WorkflowID),
		zap.String("instanceId", instance.ID),
		zap.String("instanceName", instance.Name),
		zap.String("os", instance.OS),
		zap.String("arch", instance.Architecture))

	// Execute workflow on the selected instance
	response, err := ec.executeOnInstance(ctx, instance, request)
	if err != nil {
		return nil, fmt.Errorf("failed to execute workflow on instance %s: %w", instance.ID, err)
	}

	response.InstanceID = instance.ID
	return response, nil
}

// ExecuteWorkflowOnInstance executes a workflow on a specific instance
func (ec *ExecutionCoordinator) ExecuteWorkflowOnInstance(ctx context.Context, instanceID string, request *ExecutionRequest) (*ExecutionResponse, error) {
	// Get the specific instance
	instance, err := ec.registry.GetInstance(ctx, instanceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get instance %s: %w", instanceID, err)
	}

	ec.logger.Info("Executing workflow on specific instance",
		zap.String("workflowId", request.WorkflowID),
		zap.String("instanceId", instance.ID),
		zap.String("instanceName", instance.Name))

	// Execute workflow on the specified instance
	response, err := ec.executeOnInstance(ctx, instance, request)
	if err != nil {
		return nil, fmt.Errorf("failed to execute workflow on instance %s: %w", instanceID, err)
	}

	response.InstanceID = instance.ID
	return response, nil
}

// ListAvailableInstances lists all available instances with their capabilities
func (ec *ExecutionCoordinator) ListAvailableInstances(ctx context.Context, labels map[string]string) ([]*Instance, error) {
	return ec.registry.ListInstances(ctx, labels)
}

// GetInstanceStatus gets the status of a specific instance
func (ec *ExecutionCoordinator) GetInstanceStatus(ctx context.Context, instanceID string) (*Instance, error) {
	return ec.registry.GetInstance(ctx, instanceID)
}

// findBestInstance finds the best instance for the given requirements
func (ec *ExecutionCoordinator) findBestInstance(ctx context.Context, requirements *ExecutionRequirements) (*Instance, error) {
	if requirements == nil {
		requirements = &ExecutionRequirements{}
	}

	return ec.registry.FindBestInstance(ctx, requirements)
}

// executeOnInstance executes a workflow on a specific instance
func (ec *ExecutionCoordinator) executeOnInstance(ctx context.Context, instance *Instance, request *ExecutionRequest) (*ExecutionResponse, error) {
	// Prepare the execution request
	requestBody := map[string]interface{}{
		"workflowId":  request.WorkflowID,
		"parameters":  request.Parameters,
		"startedBy":   request.StartedBy,
		"triggerType": request.TriggerType,
		"triggerData": request.TriggerData,
	}

	// Convert to JSON
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Build the URL for the target instance
	url := fmt.Sprintf("http://%s:%d/api/v1/executions", instance.IPAddress, instance.Port)

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Instance-ID", instance.ID)

	// Execute the request
	resp, err := ec.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	// Parse the response
	var response ExecutionResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("execution failed with status %d: %s", resp.StatusCode, response.Message)
	}

	return &response, nil
}

// GetExecutionStatus gets the status of an execution from the instance that's running it
func (ec *ExecutionCoordinator) GetExecutionStatus(ctx context.Context, executionID string) (*models.WorkflowExecution, error) {
	// In a real implementation, you would need to track which instance is running which execution
	// For now, we'll try to find it by querying all instances
	instances, err := ec.registry.ListInstances(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to list instances: %w", err)
	}

	for _, instance := range instances {
		execution, err := ec.getExecutionFromInstance(ctx, instance, executionID)
		if err != nil {
			// Log error but continue to next instance
			ec.logger.Debug("Failed to get execution from instance",
				zap.String("instanceId", instance.ID),
				zap.String("executionId", executionID),
				zap.Error(err))
			continue
		}

		if execution != nil {
			return execution, nil
		}
	}

	return nil, fmt.Errorf("execution %s not found on any instance", executionID)
}

// getExecutionFromInstance gets an execution from a specific instance
func (ec *ExecutionCoordinator) getExecutionFromInstance(ctx context.Context, instance *Instance, executionID string) (*models.WorkflowExecution, error) {
	url := fmt.Sprintf("http://%s:%d/api/v1/executions/%s", instance.IPAddress, instance.Port, executionID)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("X-Instance-ID", instance.ID)

	resp, err := ec.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return nil, nil // Execution not found on this instance
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("request failed with status %d", resp.StatusCode)
	}

	var execution models.WorkflowExecution
	if err := json.NewDecoder(resp.Body).Decode(&execution); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &execution, nil
}

// StopExecution stops an execution on the instance that's running it
func (ec *ExecutionCoordinator) StopExecution(ctx context.Context, executionID string) error {
	// Find the instance running this execution
	instances, err := ec.registry.ListInstances(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to list instances: %w", err)
	}

	for _, instance := range instances {
		err := ec.stopExecutionOnInstance(ctx, instance, executionID)
		if err == nil {
			ec.logger.Info("Execution stopped",
				zap.String("executionId", executionID),
				zap.String("instanceId", instance.ID))
			return nil
		}
	}

	return fmt.Errorf("execution %s not found on any instance", executionID)
}

// stopExecutionOnInstance stops an execution on a specific instance
func (ec *ExecutionCoordinator) stopExecutionOnInstance(ctx context.Context, instance *Instance, executionID string) error {
	url := fmt.Sprintf("http://%s:%d/api/v1/executions/%s/stop", instance.IPAddress, instance.Port, executionID)

	req, err := http.NewRequestWithContext(ctx, "POST", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("X-Instance-ID", instance.ID)

	resp, err := ec.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return fmt.Errorf("execution not found")
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("request failed with status %d", resp.StatusCode)
	}

	return nil
}

// GetInstanceMetrics gets metrics from all instances
func (ec *ExecutionCoordinator) GetInstanceMetrics(ctx context.Context) (map[string]interface{}, error) {
	instances, err := ec.registry.ListInstances(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to list instances: %w", err)
	}

	metrics := make(map[string]interface{})
	for _, instance := range instances {
		instanceMetrics, err := ec.getMetricsFromInstance(ctx, instance)
		if err != nil {
			ec.logger.Error("Failed to get metrics from instance",
				zap.String("instanceId", instance.ID),
				zap.Error(err))
			continue
		}
		metrics[instance.ID] = instanceMetrics
	}

	return metrics, nil
}

// getMetricsFromInstance gets metrics from a specific instance
func (ec *ExecutionCoordinator) getMetricsFromInstance(ctx context.Context, instance *Instance) (interface{}, error) {
	url := fmt.Sprintf("http://%s:%d/api/v1/monitoring/metrics", instance.IPAddress, instance.Port)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("X-Instance-ID", instance.ID)

	resp, err := ec.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("request failed with status %d", resp.StatusCode)
	}

	var metrics interface{}
	if err := json.NewDecoder(resp.Body).Decode(&metrics); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return metrics, nil
}
