package instance

import (
	"context"
	"fmt"
	"net"
	"os"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

// Instance represents a workflow service instance
type Instance struct {
	ID           string            `json:"id"`
	Name         string            `json:"name"`
	Version      string            `json:"version"`
	OS           string            `json:"os"`
	Architecture string            `json:"architecture"`
	Hostname     string            `json:"hostname"`
	IPAddress    string            `json:"ipAddress"`
	Port         int               `json:"port"`
	Labels       map[string]string `json:"labels"`
	Capabilities []string          `json:"capabilities"`
	Status       string            `json:"status"` // active, inactive, maintenance
	LastSeen     time.Time         `json:"lastSeen"`
	StartedAt    time.Time         `json:"startedAt"`
	Metadata     map[string]string `json:"metadata"`
	Resources    ResourceInfo      `json:"resources"`
}

// ResourceInfo represents system resource information
type ResourceInfo struct {
	CPUCores      int     `json:"cpuCores"`
	MemoryMB      int64   `json:"memoryMB"`
	DiskSpaceGB   int64   `json:"diskSpaceGB"`
	LoadAverage   float64 `json:"loadAverage"`
	MemoryUsage   float64 `json:"memoryUsage"`
	CPUUsage      float64 `json:"cpuUsage"`
	MaxExecutions int     `json:"maxExecutions"`
}

// InstanceManager manages workflow service instances
type InstanceManager struct {
	instance     *Instance
	registry     InstanceRegistry
	logger       *zap.Logger
	mu           sync.RWMutex
	stopCh       chan struct{}
	heartbeatInt time.Duration
}

// InstanceRegistry interface for instance registration and discovery
type InstanceRegistry interface {
	Register(ctx context.Context, instance *Instance) error
	Unregister(ctx context.Context, instanceID string) error
	UpdateHeartbeat(ctx context.Context, instanceID string) error
	GetInstance(ctx context.Context, instanceID string) (*Instance, error)
	ListInstances(ctx context.Context, labels map[string]string) ([]*Instance, error)
	FindBestInstance(ctx context.Context, requirements *ExecutionRequirements) (*Instance, error)
}

// ExecutionRequirements represents requirements for workflow execution
type ExecutionRequirements struct {
	OS           string            `json:"os,omitempty"`
	Architecture string            `json:"architecture,omitempty"`
	Labels       map[string]string `json:"labels,omitempty"`
	Capabilities []string          `json:"capabilities,omitempty"`
	MinCPUCores  int               `json:"minCpuCores,omitempty"`
	MinMemoryMB  int64             `json:"minMemoryMB,omitempty"`
	MinDiskGB    int64             `json:"minDiskGB,omitempty"`
	PreferLocal  bool              `json:"preferLocal,omitempty"`
}

// NewInstanceManager creates a new instance manager
func NewInstanceManager(registry InstanceRegistry, logger *zap.Logger) (*InstanceManager, error) {
	instance, err := createInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to create instance: %w", err)
	}

	return &InstanceManager{
		instance:     instance,
		registry:     registry,
		logger:       logger,
		stopCh:       make(chan struct{}),
		heartbeatInt: 30 * time.Second,
	}, nil
}

// Start starts the instance manager
func (im *InstanceManager) Start(ctx context.Context) error {
	// Register instance
	if err := im.registry.Register(ctx, im.instance); err != nil {
		return fmt.Errorf("failed to register instance: %w", err)
	}

	im.logger.Info("Instance registered",
		zap.String("instanceId", im.instance.ID),
		zap.String("name", im.instance.Name),
		zap.String("os", im.instance.OS),
		zap.String("arch", im.instance.Architecture))

	// Start heartbeat
	go im.startHeartbeat(ctx)

	return nil
}

// Stop stops the instance manager
func (im *InstanceManager) Stop(ctx context.Context) error {
	close(im.stopCh)

	// Unregister instance
	if err := im.registry.Unregister(ctx, im.instance.ID); err != nil {
		im.logger.Error("Failed to unregister instance", zap.Error(err))
		return err
	}

	im.logger.Info("Instance unregistered", zap.String("instanceId", im.instance.ID))
	return nil
}

// GetInstance returns the current instance
func (im *InstanceManager) GetInstance() *Instance {
	im.mu.RLock()
	defer im.mu.RUnlock()
	return im.instance
}

// UpdateLabels updates instance labels
func (im *InstanceManager) UpdateLabels(ctx context.Context, labels map[string]string) error {
	im.mu.Lock()
	if im.instance.Labels == nil {
		im.instance.Labels = make(map[string]string)
	}
	for k, v := range labels {
		im.instance.Labels[k] = v
	}
	im.mu.Unlock()

	return im.registry.Register(ctx, im.instance)
}

// UpdateCapabilities updates instance capabilities
func (im *InstanceManager) UpdateCapabilities(ctx context.Context, capabilities []string) error {
	im.mu.Lock()
	im.instance.Capabilities = capabilities
	im.mu.Unlock()

	return im.registry.Register(ctx, im.instance)
}

// UpdateStatus updates instance status
func (im *InstanceManager) UpdateStatus(ctx context.Context, status string) error {
	im.mu.Lock()
	im.instance.Status = status
	im.mu.Unlock()

	return im.registry.Register(ctx, im.instance)
}

// startHeartbeat starts the heartbeat routine
func (im *InstanceManager) startHeartbeat(ctx context.Context) {
	ticker := time.NewTicker(im.heartbeatInt)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-im.stopCh:
			return
		case <-ticker.C:
			im.updateResourceInfo()
			if err := im.registry.UpdateHeartbeat(ctx, im.instance.ID); err != nil {
				im.logger.Error("Failed to update heartbeat", zap.Error(err))
			}
		}
	}
}

// updateResourceInfo updates the instance resource information
func (im *InstanceManager) updateResourceInfo() {
	im.mu.Lock()
	defer im.mu.Unlock()

	// Update basic resource info (simplified for now)
	im.instance.Resources.CPUCores = runtime.NumCPU()
	im.instance.LastSeen = time.Now()

	// In a real implementation, you would gather actual system metrics
	// For now, we'll use placeholder values
	im.instance.Resources.LoadAverage = 0.5 // Would use actual load average
	im.instance.Resources.MemoryUsage = 0.3 // Would use actual memory usage
	im.instance.Resources.CPUUsage = 0.2    // Would use actual CPU usage
}

// createInstance creates a new instance with system information
func createInstance() (*Instance, error) {
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "unknown"
	}

	ipAddress := getLocalIP()

	// Get port from environment or use default
	port := 8085
	if portStr := os.Getenv("PORT"); portStr != "" {
		if p, err := fmt.Sscanf(portStr, "%d", &port); err != nil || p != 1 {
			port = 8085
		}
	}

	// Create instance with system information
	instance := &Instance{
		ID:           uuid.New().String(),
		Name:         getInstanceName(),
		Version:      getVersion(),
		OS:           runtime.GOOS,
		Architecture: runtime.GOARCH,
		Hostname:     hostname,
		IPAddress:    ipAddress,
		Port:         port,
		Labels:       getLabelsFromEnv(),
		Capabilities: getCapabilitiesFromEnv(),
		Status:       "active",
		LastSeen:     time.Now(),
		StartedAt:    time.Now(),
		Metadata:     getMetadataFromEnv(),
		Resources: ResourceInfo{
			CPUCores:      runtime.NumCPU(),
			MemoryMB:      getMemoryMB(),
			DiskSpaceGB:   getDiskSpaceGB(),
			MaxExecutions: getMaxExecutions(),
		},
	}

	return instance, nil
}

// getLocalIP gets the local IP address
func getLocalIP() string {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "127.0.0.1"
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)
	return localAddr.IP.String()
}

// getInstanceName gets the instance name from environment or generates one
func getInstanceName() string {
	if name := os.Getenv("INSTANCE_NAME"); name != "" {
		return name
	}
	if hostname, err := os.Hostname(); err == nil {
		return fmt.Sprintf("workflow-%s", hostname)
	}
	return fmt.Sprintf("workflow-%s", uuid.New().String()[:8])
}

// getVersion gets the service version
func getVersion() string {
	if version := os.Getenv("SERVICE_VERSION"); version != "" {
		return version
	}
	return "1.0.0"
}

// getLabelsFromEnv gets labels from environment variables
func getLabelsFromEnv() map[string]string {
	labels := make(map[string]string)

	// Add environment as a label
	if env := os.Getenv("ENVIRONMENT"); env != "" {
		labels["environment"] = env
	}

	// Add region as a label
	if region := os.Getenv("REGION"); region != "" {
		labels["region"] = region
	}

	// Add zone as a label
	if zone := os.Getenv("ZONE"); zone != "" {
		labels["zone"] = zone
	}

	// Parse custom labels from INSTANCE_LABELS env var
	if labelsStr := os.Getenv("INSTANCE_LABELS"); labelsStr != "" {
		for _, pair := range strings.Split(labelsStr, ",") {
			if kv := strings.SplitN(pair, "=", 2); len(kv) == 2 {
				labels[strings.TrimSpace(kv[0])] = strings.TrimSpace(kv[1])
			}
		}
	}

	return labels
}

// getCapabilitiesFromEnv gets capabilities from environment variables
func getCapabilitiesFromEnv() []string {
	capabilities := []string{"workflow-execution"}

	// Add OS-specific capabilities
	switch runtime.GOOS {
	case "linux":
		capabilities = append(capabilities, "docker", "kubernetes")
	case "windows":
		capabilities = append(capabilities, "powershell", "windows-containers")
	case "darwin":
		capabilities = append(capabilities, "docker")
	}

	// Parse custom capabilities from INSTANCE_CAPABILITIES env var
	if capStr := os.Getenv("INSTANCE_CAPABILITIES"); capStr != "" {
		for _, cap := range strings.Split(capStr, ",") {
			if cap = strings.TrimSpace(cap); cap != "" {
				capabilities = append(capabilities, cap)
			}
		}
	}

	return capabilities
}

// getMetadataFromEnv gets metadata from environment variables
func getMetadataFromEnv() map[string]string {
	metadata := make(map[string]string)

	// Add common metadata
	if cluster := os.Getenv("CLUSTER_NAME"); cluster != "" {
		metadata["cluster"] = cluster
	}

	if namespace := os.Getenv("NAMESPACE"); namespace != "" {
		metadata["namespace"] = namespace
	}

	if nodeID := os.Getenv("NODE_ID"); nodeID != "" {
		metadata["nodeId"] = nodeID
	}

	return metadata
}

// getMemoryMB gets available memory in MB (simplified)
func getMemoryMB() int64 {
	// In a real implementation, you would use system calls to get actual memory
	// For now, return a default value
	return 4096 // 4GB default
}

// getDiskSpaceGB gets available disk space in GB (simplified)
func getDiskSpaceGB() int64 {
	// In a real implementation, you would check actual disk space
	// For now, return a default value
	return 100 // 100GB default
}

// getMaxExecutions gets max concurrent executions from environment
func getMaxExecutions() int {
	if maxStr := os.Getenv("WORKFLOW_MAX_CONCURRENT_EXECUTIONS"); maxStr != "" {
		if max, err := fmt.Sscanf(maxStr, "%d", new(int)); err == nil && max == 1 {
			var result int
			fmt.Sscanf(maxStr, "%d", &result)
			return result
		}
	}
	return 10 // Default
}
