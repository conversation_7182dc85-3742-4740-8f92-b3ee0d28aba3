package instance

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// DatabaseInstanceRegistry implements InstanceRegistry using database storage
type DatabaseInstanceRegistry struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewDatabaseInstanceRegistry creates a new database instance registry
func NewDatabaseInstanceRegistry(db *gorm.DB, logger *zap.Logger) *DatabaseInstanceRegistry {
	return &DatabaseInstanceRegistry{
		db:     db,
		logger: logger,
	}
}

// Register registers an instance in the database
func (r *DatabaseInstanceRegistry) Register(ctx context.Context, instance *Instance) error {
	record, err := r.instanceToRecord(instance)
	if err != nil {
		return fmt.Errorf("failed to convert instance to record: %w", err)
	}

	// Use upsert to handle both create and update
	if err := r.db.WithContext(ctx).Save(record).Error; err != nil {
		return fmt.Errorf("failed to register instance: %w", err)
	}

	r.logger.Debug("Instance registered",
		zap.String("instanceId", instance.ID),
		zap.String("name", instance.Name))

	return nil
}

// Unregister removes an instance from the database
func (r *DatabaseInstanceRegistry) Unregister(ctx context.Context, instanceID string) error {
	if err := r.db.WithContext(ctx).Delete(&models.InstanceRecord{}, "id = ?", instanceID).Error; err != nil {
		return fmt.Errorf("failed to unregister instance: %w", err)
	}

	r.logger.Debug("Instance unregistered", zap.String("instanceId", instanceID))
	return nil
}

// UpdateHeartbeat updates the last seen timestamp for an instance
func (r *DatabaseInstanceRegistry) UpdateHeartbeat(ctx context.Context, instanceID string) error {
	if err := r.db.WithContext(ctx).Model(&models.InstanceRecord{}).
		Where("id = ?", instanceID).
		Update("last_seen", time.Now()).Error; err != nil {
		return fmt.Errorf("failed to update heartbeat: %w", err)
	}

	return nil
}

// GetInstance retrieves an instance by ID
func (r *DatabaseInstanceRegistry) GetInstance(ctx context.Context, instanceID string) (*Instance, error) {
	var record models.InstanceRecord
	if err := r.db.WithContext(ctx).First(&record, "id = ?", instanceID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("instance not found: %s", instanceID)
		}
		return nil, fmt.Errorf("failed to get instance: %w", err)
	}

	return r.recordToInstance(&record)
}

// ListInstances lists instances matching the given labels
func (r *DatabaseInstanceRegistry) ListInstances(ctx context.Context, labels map[string]string) ([]*Instance, error) {
	var records []models.InstanceRecord
	query := r.db.WithContext(ctx)

	// Filter by labels if provided
	if len(labels) > 0 {
		for key, value := range labels {
			query = query.Where("labels ->> ? = ?", key, value)
		}
	}

	// Only return active instances that have been seen recently (within 2 minutes)
	cutoff := time.Now().Add(-2 * time.Minute)
	query = query.Where("status = ? AND last_seen > ?", "active", cutoff)

	if err := query.Find(&records).Error; err != nil {
		return nil, fmt.Errorf("failed to list instances: %w", err)
	}

	instances := make([]*Instance, 0, len(records))
	for _, record := range records {
		instance, err := r.recordToInstance(&record)
		if err != nil {
			r.logger.Error("Failed to convert record to instance",
				zap.String("instanceId", record.ID),
				zap.Error(err))
			continue
		}
		instances = append(instances, instance)
	}

	return instances, nil
}

// FindBestInstance finds the best instance for the given requirements
func (r *DatabaseInstanceRegistry) FindBestInstance(ctx context.Context, requirements *ExecutionRequirements) (*Instance, error) {
	instances, err := r.ListInstances(ctx, requirements.Labels)
	if err != nil {
		return nil, err
	}

	if len(instances) == 0 {
		return nil, fmt.Errorf("no instances available")
	}

	// Filter instances by requirements
	candidates := make([]*Instance, 0)
	for _, instance := range instances {
		if r.matchesRequirements(instance, requirements) {
			candidates = append(candidates, instance)
		}
	}

	if len(candidates) == 0 {
		return nil, fmt.Errorf("no instances match requirements")
	}

	// Sort candidates by score (lower is better)
	sort.Slice(candidates, func(i, j int) bool {
		scoreI := r.calculateInstanceScore(candidates[i], requirements)
		scoreJ := r.calculateInstanceScore(candidates[j], requirements)
		return scoreI < scoreJ
	})

	return candidates[0], nil
}

// matchesRequirements checks if an instance matches the given requirements
func (r *DatabaseInstanceRegistry) matchesRequirements(instance *Instance, requirements *ExecutionRequirements) bool {
	// Check OS
	if requirements.OS != "" && instance.OS != requirements.OS {
		return false
	}

	// Check architecture
	if requirements.Architecture != "" && instance.Architecture != requirements.Architecture {
		return false
	}

	// Check capabilities
	for _, reqCap := range requirements.Capabilities {
		found := false
		for _, instCap := range instance.Capabilities {
			if instCap == reqCap {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Check resource requirements
	if requirements.MinCPUCores > 0 && instance.Resources.CPUCores < requirements.MinCPUCores {
		return false
	}

	if requirements.MinMemoryMB > 0 && instance.Resources.MemoryMB < requirements.MinMemoryMB {
		return false
	}

	if requirements.MinDiskGB > 0 && instance.Resources.DiskSpaceGB < requirements.MinDiskGB {
		return false
	}

	return true
}

// calculateInstanceScore calculates a score for instance selection (lower is better)
func (r *DatabaseInstanceRegistry) calculateInstanceScore(instance *Instance, requirements *ExecutionRequirements) float64 {
	score := 0.0

	// Factor in CPU usage (higher usage = higher score)
	score += instance.Resources.CPUUsage * 100

	// Factor in memory usage (higher usage = higher score)
	score += instance.Resources.MemoryUsage * 100

	// Factor in load average (higher load = higher score)
	score += instance.Resources.LoadAverage * 50

	// Prefer instances with more available capacity
	if instance.Resources.MaxExecutions > 0 {
		// This would need actual current execution count
		// For now, use a placeholder
		currentExecutions := 0.0 // Would get from monitoring
		utilizationRatio := currentExecutions / float64(instance.Resources.MaxExecutions)
		score += utilizationRatio * 200
	}

	// If prefer local is set, give bonus to local instances
	if requirements.PreferLocal {
		// This would check if the instance is on the same node/region
		// For now, just a placeholder
		score -= 10 // Bonus for local instances
	}

	return score
}

// instanceToRecord converts an Instance to an InstanceRecord
func (r *DatabaseInstanceRegistry) instanceToRecord(instance *Instance) (*models.InstanceRecord, error) {
	labelsJSON, err := json.Marshal(instance.Labels)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal labels: %w", err)
	}

	capabilitiesJSON, err := json.Marshal(instance.Capabilities)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal capabilities: %w", err)
	}

	metadataJSON, err := json.Marshal(instance.Metadata)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal metadata: %w", err)
	}

	resourcesJSON, err := json.Marshal(instance.Resources)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal resources: %w", err)
	}

	return &models.InstanceRecord{
		ID:           instance.ID,
		Name:         instance.Name,
		Version:      instance.Version,
		OS:           instance.OS,
		Architecture: instance.Architecture,
		Hostname:     instance.Hostname,
		IPAddress:    instance.IPAddress,
		Port:         instance.Port,
		Labels:       labelsJSON,
		Capabilities: capabilitiesJSON,
		Status:       instance.Status,
		LastSeen:     instance.LastSeen,
		StartedAt:    instance.StartedAt,
		Metadata:     metadataJSON,
		Resources:    resourcesJSON,
	}, nil
}

// recordToInstance converts an InstanceRecord to an Instance
func (r *DatabaseInstanceRegistry) recordToInstance(record *models.InstanceRecord) (*Instance, error) {
	var labels map[string]string
	if err := json.Unmarshal([]byte(record.Labels), &labels); err != nil {
		return nil, fmt.Errorf("failed to unmarshal labels: %w", err)
	}

	var capabilities []string
	if err := json.Unmarshal([]byte(record.Capabilities), &capabilities); err != nil {
		return nil, fmt.Errorf("failed to unmarshal capabilities: %w", err)
	}

	var metadata map[string]string
	if err := json.Unmarshal([]byte(record.Metadata), &metadata); err != nil {
		return nil, fmt.Errorf("failed to unmarshal metadata: %w", err)
	}

	var resources ResourceInfo
	if err := json.Unmarshal([]byte(record.Resources), &resources); err != nil {
		return nil, fmt.Errorf("failed to unmarshal resources: %w", err)
	}

	return &Instance{
		ID:           record.ID,
		Name:         record.Name,
		Version:      record.Version,
		OS:           record.OS,
		Architecture: record.Architecture,
		Hostname:     record.Hostname,
		IPAddress:    record.IPAddress,
		Port:         record.Port,
		Labels:       labels,
		Capabilities: capabilities,
		Status:       record.Status,
		LastSeen:     record.LastSeen,
		StartedAt:    record.StartedAt,
		Metadata:     metadata,
		Resources:    resources,
	}, nil
}

// CleanupStaleInstances removes instances that haven't been seen for a while
func (r *DatabaseInstanceRegistry) CleanupStaleInstances(ctx context.Context, timeout time.Duration) error {
	cutoff := time.Now().Add(-timeout)

	result := r.db.WithContext(ctx).
		Delete(&models.InstanceRecord{}, "last_seen < ?", cutoff)

	if result.Error != nil {
		return fmt.Errorf("failed to cleanup stale instances: %w", result.Error)
	}

	if result.RowsAffected > 0 {
		r.logger.Info("Cleaned up stale instances",
			zap.Int64("count", result.RowsAffected),
			zap.Duration("timeout", timeout))
	}

	return nil
}

// InMemoryInstanceRegistry implements InstanceRegistry using in-memory storage
type InMemoryInstanceRegistry struct {
	instances map[string]*Instance
	mu        sync.RWMutex
	logger    *zap.Logger
}

// NewInMemoryInstanceRegistry creates a new in-memory instance registry
func NewInMemoryInstanceRegistry(logger *zap.Logger) *InMemoryInstanceRegistry {
	return &InMemoryInstanceRegistry{
		instances: make(map[string]*Instance),
		logger:    logger,
	}
}

// Register registers an instance in memory
func (r *InMemoryInstanceRegistry) Register(ctx context.Context, instance *Instance) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	r.instances[instance.ID] = instance
	r.logger.Debug("Instance registered",
		zap.String("instanceId", instance.ID),
		zap.String("name", instance.Name))

	return nil
}

// Unregister removes an instance from memory
func (r *InMemoryInstanceRegistry) Unregister(ctx context.Context, instanceID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	delete(r.instances, instanceID)
	r.logger.Debug("Instance unregistered", zap.String("instanceId", instanceID))
	return nil
}

// UpdateHeartbeat updates the last seen timestamp for an instance
func (r *InMemoryInstanceRegistry) UpdateHeartbeat(ctx context.Context, instanceID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if instance, exists := r.instances[instanceID]; exists {
		instance.LastSeen = time.Now()
		return nil
	}

	return fmt.Errorf("instance not found: %s", instanceID)
}

// GetInstance retrieves an instance by ID
func (r *InMemoryInstanceRegistry) GetInstance(ctx context.Context, instanceID string) (*Instance, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if instance, exists := r.instances[instanceID]; exists {
		return instance, nil
	}

	return nil, fmt.Errorf("instance not found: %s", instanceID)
}

// ListInstances lists instances matching the given labels
func (r *InMemoryInstanceRegistry) ListInstances(ctx context.Context, labels map[string]string) ([]*Instance, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var result []*Instance
	cutoff := time.Now().Add(-2 * time.Minute)

	for _, instance := range r.instances {
		// Only return active instances that have been seen recently
		if instance.Status != "active" || instance.LastSeen.Before(cutoff) {
			continue
		}

		// Filter by labels if provided
		if len(labels) > 0 {
			matches := true
			for key, value := range labels {
				if instanceValue, exists := instance.Labels[key]; !exists || instanceValue != value {
					matches = false
					break
				}
			}
			if !matches {
				continue
			}
		}

		result = append(result, instance)
	}

	return result, nil
}

// FindBestInstance finds the best instance for the given requirements
func (r *InMemoryInstanceRegistry) FindBestInstance(ctx context.Context, requirements *ExecutionRequirements) (*Instance, error) {
	instances, err := r.ListInstances(ctx, requirements.Labels)
	if err != nil {
		return nil, err
	}

	if len(instances) == 0 {
		return nil, fmt.Errorf("no instances available")
	}

	// Filter instances by requirements
	candidates := make([]*Instance, 0)
	for _, instance := range instances {
		if r.matchesRequirements(instance, requirements) {
			candidates = append(candidates, instance)
		}
	}

	if len(candidates) == 0 {
		return nil, fmt.Errorf("no instances match requirements")
	}

	// Sort candidates by score (lower is better)
	sort.Slice(candidates, func(i, j int) bool {
		scoreI := r.calculateInstanceScore(candidates[i], requirements)
		scoreJ := r.calculateInstanceScore(candidates[j], requirements)
		return scoreI < scoreJ
	})

	return candidates[0], nil
}

// matchesRequirements checks if an instance matches the given requirements (same logic as database version)
func (r *InMemoryInstanceRegistry) matchesRequirements(instance *Instance, requirements *ExecutionRequirements) bool {
	// Check OS
	if requirements.OS != "" && instance.OS != requirements.OS {
		return false
	}

	// Check architecture
	if requirements.Architecture != "" && instance.Architecture != requirements.Architecture {
		return false
	}

	// Check capabilities
	for _, reqCap := range requirements.Capabilities {
		found := false
		for _, instCap := range instance.Capabilities {
			if instCap == reqCap {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Check resource requirements
	if requirements.MinCPUCores > 0 && instance.Resources.CPUCores < requirements.MinCPUCores {
		return false
	}

	if requirements.MinMemoryMB > 0 && instance.Resources.MemoryMB < requirements.MinMemoryMB {
		return false
	}

	if requirements.MinDiskGB > 0 && instance.Resources.DiskSpaceGB < requirements.MinDiskGB {
		return false
	}

	return true
}

// calculateInstanceScore calculates a score for instance selection (same logic as database version)
func (r *InMemoryInstanceRegistry) calculateInstanceScore(instance *Instance, requirements *ExecutionRequirements) float64 {
	score := 0.0

	// Factor in CPU usage (higher usage = higher score)
	score += instance.Resources.CPUUsage * 100

	// Factor in memory usage (higher usage = higher score)
	score += instance.Resources.MemoryUsage * 100

	// Factor in load average (higher load = higher score)
	score += instance.Resources.LoadAverage * 50

	// Prefer instances with more available capacity
	if instance.Resources.MaxExecutions > 0 {
		// This would need actual current execution count
		// For now, use a placeholder
		currentExecutions := 0.0 // Would get from monitoring
		utilizationRatio := currentExecutions / float64(instance.Resources.MaxExecutions)
		score += utilizationRatio * 200
	}

	// If prefer local is set, give bonus to local instances
	if requirements.PreferLocal {
		// This would check if the instance is on the same node/region
		// For now, just a placeholder
		score -= 10 // Bonus for local instances
	}

	return score
}

// CleanupStaleInstances removes instances that haven't been seen for a while
func (r *InMemoryInstanceRegistry) CleanupStaleInstances(ctx context.Context, timeout time.Duration) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	cutoff := time.Now().Add(-timeout)
	count := 0

	for id, instance := range r.instances {
		if instance.LastSeen.Before(cutoff) {
			delete(r.instances, id)
			count++
		}
	}

	if count > 0 {
		r.logger.Info("Cleaned up stale instances",
			zap.Int("count", count),
			zap.Duration("timeout", timeout))
	}

	return nil
}
