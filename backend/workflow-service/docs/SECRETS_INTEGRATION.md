# Secrets Integration in Workflow Service

## Overview

The workflow service integrates with the secrets service to provide secure access to sensitive data during workflow execution. Secrets are automatically retrieved and injected into workflow steps based on project permissions and step configurations.

## How It Works

### 1. Secret Setup Phase

Before secrets can be used in workflows, they must be:

1. **Created in Secrets Service**
2. **Bound to Projects** with appropriate access controls
3. **Configured as Variables** with specific types and contexts

### 2. Workflow Execution Phase

During workflow execution, the workflow engine:

1. **Retrieves secrets** for each step before execution
2. **Filters secrets** based on project, environment, service, and step context
3. **Injects secrets** into step configuration based on secret type
4. **Logs usage** for audit purposes

## Secret Variable Types

### Environment Variables (`env`)
Secrets injected as environment variables in the step execution context.

**Example Secret Configuration:**
```json
{
  "name": "DATABASE_PASSWORD",
  "type": "env",
  "value": "super-secret-password",
  "environment": "production",
  "service": "web-service"
}
```

**Workflow Step Usage:**
```json
{
  "id": "deploy-step",
  "name": "Deploy Application",
  "type": "script",
  "config": {
    "script": "echo $DATABASE_PASSWORD | docker login -u user --password-stdin"
  }
}
```

**Result:** The `DATABASE_PASSWORD` environment variable is available in the script execution.

### Configuration Files (`file`)
Secrets written to files before step execution.

**Example Secret Configuration:**
```json
{
  "name": "database_config",
  "type": "file",
  "path": "/app/config/database.json",
  "format": "json",
  "value": "{\"password\": \"super-secret-password\"}",
  "environment": "production"
}
```

**Result:** A file is created at `/app/config/database.json` with the secret content.

### Configuration Data (`config`)
Secrets available as structured configuration data.

**Example Secret Configuration:**
```json
{
  "name": "redis_config",
  "type": "config",
  "format": "yaml",
  "value": "host: redis.example.com\npassword: secret-password"
}
```

**Result:** Configuration data is available in the step's config section.

### Volume Mounts (`mount`)
Secrets mounted as volumes in containerized environments.

**Example Secret Configuration:**
```json
{
  "name": "ssl_certificates",
  "type": "mount",
  "path": "/etc/ssl/certs",
  "value": "certificate-content"
}
```

**Result:** Secret content is mounted at the specified path.

## Workflow Step Examples

### Script Step with Environment Variables
```json
{
  "id": "database-backup",
  "name": "Backup Database",
  "type": "script",
  "config": {
    "language": "bash",
    "script": "pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME > backup.sql"
  }
}
```

### HTTP Step with Authentication
```json
{
  "id": "api-call",
  "name": "Call External API",
  "type": "http",
  "config": {
    "url": "https://api.example.com/data",
    "method": "GET",
    "headers": {
      "Authorization": "Bearer ${API_TOKEN}"
    }
  }
}
```

### Deployment Step with Configuration Files
```json
{
  "id": "deploy-app",
  "name": "Deploy Application",
  "type": "deployment",
  "config": {
    "service": "web-service",
    "environment": "production",
    "configFiles": [
      "/app/config/database.json",
      "/app/config/redis.yaml"
    ]
  }
}
```

## Secret Filtering and Context

Secrets are filtered based on multiple criteria:

### Project Scope
- Only secrets bound to the workflow's project are accessible
- Cross-project secret access is not allowed

### Environment Filtering
- Secrets can be scoped to specific environments (`production`, `staging`, `development`)
- Global secrets (empty environment) are available in all environments

### Service Filtering
- Secrets can be scoped to specific services
- Global secrets (empty service) are available to all services

### Step Context
- Secrets can be filtered by step name for fine-grained control
- Useful for step-specific credentials or configurations

## Security Features

### Encryption at Rest
- All secret values are encrypted in the database
- Encryption keys are managed securely

### Access Control
- Project-based access control
- Role-based permissions for secret management
- Audit logging for all secret access

### Secure Transmission
- Secrets are transmitted over HTTPS
- Authentication required for all secret requests

### Audit Logging
- All secret retrievals are logged with:
  - User ID and workflow execution details
  - Timestamp and secret identifiers
  - Success/failure status

## Configuration

### Workflow Service Configuration
```yaml
secrets:
  service_url: "http://localhost:8087"
  timeout: "30s"
  retry_attempts: 3
```

### Authentication
The workflow service authenticates with the secrets service using:
- JWT tokens from the user context
- Service-to-service authentication (planned)

## Error Handling

### Secret Retrieval Failures
- Workflow execution continues even if secret retrieval fails
- Warnings are logged for debugging
- Steps may fail if required secrets are missing

### Permission Denied
- Access denied errors are logged
- Workflow execution continues without the restricted secrets

### Service Unavailable
- Temporary failures are retried
- Workflow execution continues after timeout

## Best Practices

### Secret Organization
1. **Use descriptive names** for secrets and variables
2. **Scope secrets appropriately** to environments and services
3. **Rotate secrets regularly** using the rotation features

### Workflow Design
1. **Handle missing secrets gracefully** in workflow steps
2. **Use environment-specific workflows** for different deployment targets
3. **Minimize secret exposure** by using the most restrictive scope possible

### Security
1. **Never log secret values** in workflow steps
2. **Use file-based secrets** for large configuration data
3. **Implement proper cleanup** for temporary secret files

## Troubleshooting

### Common Issues

**Secrets not available in step:**
- Check project binding and permissions
- Verify environment and service filters
- Review audit logs for access attempts

**Authentication failures:**
- Ensure valid JWT token in workflow context
- Check secrets service connectivity
- Verify service configuration

**Performance issues:**
- Monitor secret retrieval latency
- Consider caching for frequently accessed secrets
- Review secret filtering efficiency

### Debugging

Enable debug logging to see secret retrieval details:
```yaml
logging:
  level: debug
  loggers:
    secrets-client: debug
```

Check audit logs in the secrets service for detailed access information.
