# Workflow Service API Reference

## Base URL
```
http://localhost:8085/api/v1
```

## Authentication
Currently, the API does not require authentication. In production, implement proper authentication mechanisms.

## Instance Management

### List All Instances
```http
GET /instances
```

**Query Parameters:**
- `environment` (optional): Filter by environment label
- `region` (optional): Filter by region label
- `zone` (optional): Filter by zone label
- Any custom label key as query parameter

**Response:**
```json
{
  "instances": [
    {
      "id": "uuid",
      "name": "workflow-linux-1",
      "version": "1.0.0",
      "os": "linux",
      "architecture": "amd64",
      "hostname": "container-host",
      "ipAddress": "**********",
      "port": 8085,
      "labels": {
        "environment": "production",
        "region": "us-east-1",
        "zone": "a"
      },
      "capabilities": [
        "workflow-execution",
        "script-execution",
        "docker"
      ],
      "status": "active",
      "lastSeen": "2024-01-15T10:30:00Z",
      "startedAt": "2024-01-15T09:00:00Z",
      "resources": {
        "cpuCores": 4,
        "memoryMB": 8192,
        "diskSpaceGB": 100,
        "loadAverage": 0.5,
        "memoryUsage": 0.3,
        "cpuUsage": 0.2,
        "maxExecutions": 10
      }
    }
  ],
  "total": 1
}
```

### Get Current Instance
```http
GET /instances/current
```

**Response:** Single instance object (same structure as above)

### Get Specific Instance
```http
GET /instances/{instanceId}
```

**Response:** Single instance object

### Update Instance Labels
```http
PUT /instances/current/labels
```

**Request Body:**
```json
{
  "labels": {
    "environment": "production",
    "team": "backend",
    "version": "v2.0"
  }
}
```

**Response:**
```json
{
  "message": "Labels updated successfully"
}
```

### Update Instance Capabilities
```http
PUT /instances/current/capabilities
```

**Request Body:**
```json
{
  "capabilities": [
    "workflow-execution",
    "script-execution",
    "docker",
    "kubernetes"
  ]
}
```

### Update Instance Status
```http
PUT /instances/current/status
```

**Request Body:**
```json
{
  "status": "maintenance"
}
```

**Valid statuses:** `active`, `inactive`, `maintenance`

## Distributed Execution

### Execute Workflow on Best Instance
```http
POST /instances/execute
```

**Request Body:**
```json
{
  "workflowId": "my-workflow",
  "parameters": {
    "key": "value",
    "environment": "production"
  },
  "startedBy": "<EMAIL>",
  "triggerType": "manual",
  "triggerData": {
    "source": "api"
  },
  "requirements": {
    "os": "linux",
    "architecture": "amd64",
    "labels": {
      "environment": "production",
      "region": "us-east-1"
    },
    "capabilities": [
      "docker",
      "kubernetes"
    ],
    "minCpuCores": 2,
    "minMemoryMB": 1024,
    "minDiskGB": 10,
    "preferLocal": false
  }
}
```

**Response:**
```json
{
  "executionId": "execution-uuid",
  "instanceId": "instance-uuid",
  "status": "started",
  "message": "Workflow execution started successfully"
}
```

### Execute Workflow on Specific Instance
```http
POST /instances/{instanceId}/execute
```

**Request Body:** Same as above (without requirements)

### Get Distributed Execution Status
```http
GET /instances/executions/{executionId}
```

**Response:**
```json
{
  "id": "execution-uuid",
  "workflowId": "my-workflow",
  "status": "running",
  "startedAt": "2024-01-15T10:30:00Z",
  "completedAt": null,
  "duration": 0,
  "parameters": {
    "key": "value"
  },
  "output": {},
  "errorMessage": null,
  "instanceId": "instance-uuid"
}
```

### Stop Distributed Execution
```http
POST /instances/executions/{executionId}/stop
```

**Response:**
```json
{
  "message": "Execution stopped successfully"
}
```

## Monitoring

### Instance Dashboard
```http
GET /instances/dashboard
```

**Response:**
```json
{
  "statistics": {
    "totalInstances": 4,
    "activeInstances": 3,
    "totalCPUCores": 16,
    "totalMemoryMB": 32768
  },
  "distribution": {
    "operatingSystems": {
      "linux": 4
    },
    "architectures": {
      "amd64": 4
    }
  },
  "instances": [...]
}
```

### Instance Health
```http
GET /instances/health
```

**Response:**
```json
{
  "instances": [
    {
      "instanceId": "uuid",
      "name": "workflow-linux-1",
      "status": "active",
      "lastSeen": "2024-01-15T10:30:00Z",
      "cpuUsage": 0.2,
      "memoryUsage": 0.3,
      "loadAverage": 0.5
    }
  ],
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Instance Metrics
```http
GET /instances/metrics
```

**Response:**
```json
{
  "metrics": {
    "instance-uuid-1": {
      "executions": 5,
      "successRate": 0.8,
      "averageDuration": 120.5,
      "resourceUtilization": {
        "cpu": 0.2,
        "memory": 0.3
      }
    }
  },
  "total": 1
}
```

## Execution Monitoring

### Execution Dashboard
```http
GET /monitoring/dashboard
```

**Response:**
```json
{
  "statistics": {
    "totalActiveExecutions": 3,
    "totalCompletedSteps": 15,
    "totalFailedSteps": 1,
    "averageExecutionTime": 180.5
  },
  "activeExecutions": {
    "execution-uuid": {
      "id": "metrics-uuid",
      "executionId": "execution-uuid",
      "totalSteps": 5,
      "completedSteps": 3,
      "failedSteps": 0,
      "skippedSteps": 0,
      "averageStepTime": 30.2,
      "totalExecutionTime": 90.6
    }
  },
  "recentExecutions": [...]
}
```

### Execution Metrics
```http
GET /monitoring/executions/{executionId}/metrics
```

### Execution Events
```http
GET /monitoring/executions/{executionId}/events
```

**Query Parameters:**
- `limit` (default: 50): Number of events to return
- `offset` (default: 0): Offset for pagination
- `type` (optional): Filter by event type

**Response:**
```json
{
  "events": [
    {
      "id": "event-uuid",
      "executionId": "execution-uuid",
      "stepId": "step-uuid",
      "eventType": "step_started",
      "eventData": {
        "stepName": "Build Application",
        "startedAt": "2024-01-15T10:30:00Z"
      },
      "timestamp": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 1
}
```

### Real-time Event Stream (SSE)
```http
GET /monitoring/executions/{executionId}/stream
```

**Response:** Server-Sent Events stream
```
event: event
data: {"id":"event-uuid","executionId":"execution-uuid","eventType":"step_completed",...}

event: event  
data: {"id":"event-uuid","executionId":"execution-uuid","eventType":"step_started",...}
```

### Performance Analytics
```http
GET /monitoring/performance
```

**Query Parameters:**
- `projectId` (optional): Filter by project
- `workflowId` (optional): Filter by workflow
- `limit` (default: 100): Number of executions to analyze

**Response:**
```json
{
  "totalExecutions": 100,
  "successCount": 85,
  "failureCount": 15,
  "successRate": 85.0,
  "averageDuration": 180.5,
  "executionTrends": [
    {
      "id": "execution-uuid",
      "status": "completed",
      "duration": 120,
      "startedAt": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### System Health
```http
GET /monitoring/health
```

**Response:**
```json
{
  "status": "healthy",
  "activeExecutions": 3,
  "maxConcurrent": 10,
  "systemLoad": 30.0,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Standard Workflow APIs

### List Workflows
```http
GET /workflows
```

### Get Workflow
```http
GET /workflows/{workflowId}
```

### Create Workflow
```http
POST /workflows
```

### Update Workflow
```http
PUT /workflows/{workflowId}
```

### Delete Workflow
```http
DELETE /workflows/{workflowId}
```

### Execute Workflow (Local)
```http
POST /executions
```

**Request Body:**
```json
{
  "workflowId": "my-workflow",
  "parameters": {
    "key": "value"
  },
  "startedBy": "<EMAIL>",
  "triggerType": "manual"
}
```

### Get Execution
```http
GET /executions/{executionId}
```

### Stop Execution
```http
POST /executions/{executionId}/stop
```

### Get Execution Logs
```http
GET /executions/{executionId}/logs
```

## Error Responses

All endpoints return appropriate HTTP status codes and error messages:

### 400 Bad Request
```json
{
  "error": "Invalid request body"
}
```

### 404 Not Found
```json
{
  "error": "Instance not found"
}
```

### 500 Internal Server Error
```json
{
  "error": "Failed to execute workflow"
}
```

## Rate Limiting

The API implements rate limiting:
- General API: 10 requests/second
- Instance management: 5 requests/second
- Burst allowance: 20 requests

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 9
X-RateLimit-Reset: 1642248600
```

## WebSocket Support

Real-time monitoring supports WebSocket connections for live updates:

```javascript
const ws = new WebSocket('ws://localhost:8085/api/v1/monitoring/executions/stream');
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Real-time update:', data);
};
```

---

For more examples and use cases, see [examples/README.md](./examples/README.md).
