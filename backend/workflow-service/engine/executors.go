package engine

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"go.uber.org/zap"
)

// ScriptExecutor executes script steps
type ScriptExecutor struct {
	logger *zap.Logger
}

func (e *ScriptExecutor) GetType() string {
	return "script"
}

func (e *ScriptExecutor) Execute(ctx context.Context, step *models.WorkflowStep, execCtx *ExecutionContext) (*StepResult, error) {
	script, ok := step.Config["script"].(string)
	if !ok {
		return &StepResult{Success: false, Error: fmt.Errorf("script not specified")}, nil
	}

	language, ok := step.Config["language"].(string)
	if !ok {
		language = "bash" // Default to bash
	}

	var cmd *exec.Cmd
	switch language {
	case "bash":
		cmd = exec.CommandContext(ctx, "bash", "-c", script)
	case "python":
		cmd = exec.CommandContext(ctx, "python3", "-c", script)
	case "javascript":
		cmd = exec.CommandContext(ctx, "node", "-e", script)
	case "powershell":
		cmd = exec.CommandContext(ctx, "powershell", "-Command", script)
	default:
		return &StepResult{Success: false, Error: fmt.Errorf("unsupported script language: %s", language)}, nil
	}

	// Set environment variables from execution context
	env := []string{}
	for k, v := range execCtx.Variables {
		if str, ok := v.(string); ok {
			env = append(env, fmt.Sprintf("%s=%s", k, str))
		}
	}
	cmd.Env = env

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	logs := []models.StepLog{
		{
			Timestamp: time.Now(),
			Level:     "info",
			Message:   fmt.Sprintf("Executing %s script", language),
		},
	}

	err := cmd.Run()

	output := make(map[string]interface{})
	output["stdout"] = stdout.String()
	output["stderr"] = stderr.String()
	output["exitCode"] = 0

	if err != nil {
		if exitError, ok := err.(*exec.ExitError); ok {
			output["exitCode"] = exitError.ExitCode()
		}
		logs = append(logs, models.StepLog{
			Timestamp: time.Now(),
			Level:     "error",
			Message:   fmt.Sprintf("Script execution failed: %v", err),
		})
		return &StepResult{Success: false, Output: output, Error: err, Logs: logs}, nil
	}

	logs = append(logs, models.StepLog{
		Timestamp: time.Now(),
		Level:     "info",
		Message:   "Script execution completed successfully",
	})

	return &StepResult{Success: true, Output: output, Logs: logs}, nil
}

// HTTPExecutor executes HTTP request steps
type HTTPExecutor struct {
	logger *zap.Logger
}

func (e *HTTPExecutor) GetType() string {
	return "http"
}

func (e *HTTPExecutor) Execute(ctx context.Context, step *models.WorkflowStep, execCtx *ExecutionContext) (*StepResult, error) {
	url, ok := step.Config["url"].(string)
	if !ok {
		return &StepResult{Success: false, Error: fmt.Errorf("url not specified")}, nil
	}

	method, ok := step.Config["method"].(string)
	if !ok {
		method = "GET"
	}

	var body io.Reader
	if bodyData, exists := step.Config["body"]; exists {
		if bodyStr, ok := bodyData.(string); ok {
			body = strings.NewReader(bodyStr)
		} else {
			bodyBytes, _ := json.Marshal(bodyData)
			body = bytes.NewReader(bodyBytes)
		}
	}

	req, err := http.NewRequestWithContext(ctx, method, url, body)
	if err != nil {
		return &StepResult{Success: false, Error: err}, nil
	}

	// Set headers
	if headers, exists := step.Config["headers"]; exists {
		if headerMap, ok := headers.(map[string]interface{}); ok {
			for k, v := range headerMap {
				if str, ok := v.(string); ok {
					req.Header.Set(k, str)
				}
			}
		}
	}

	logs := []models.StepLog{
		{
			Timestamp: time.Now(),
			Level:     "info",
			Message:   fmt.Sprintf("Making %s request to %s", method, url),
		},
	}

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		logs = append(logs, models.StepLog{
			Timestamp: time.Now(),
			Level:     "error",
			Message:   fmt.Sprintf("HTTP request failed: %v", err),
		})
		return &StepResult{Success: false, Error: err, Logs: logs}, nil
	}
	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return &StepResult{Success: false, Error: err, Logs: logs}, nil
	}

	output := map[string]interface{}{
		"statusCode": resp.StatusCode,
		"headers":    resp.Header,
		"body":       string(responseBody),
	}

	success := resp.StatusCode >= 200 && resp.StatusCode < 300
	if !success {
		logs = append(logs, models.StepLog{
			Timestamp: time.Now(),
			Level:     "error",
			Message:   fmt.Sprintf("HTTP request returned status %d", resp.StatusCode),
		})
	} else {
		logs = append(logs, models.StepLog{
			Timestamp: time.Now(),
			Level:     "info",
			Message:   fmt.Sprintf("HTTP request completed with status %d", resp.StatusCode),
		})
	}

	return &StepResult{Success: success, Output: output, Logs: logs}, nil
}

// DeploymentExecutor executes deployment steps
type DeploymentExecutor struct {
	logger *zap.Logger
}

func (e *DeploymentExecutor) GetType() string {
	return "deployment"
}

func (e *DeploymentExecutor) Execute(ctx context.Context, step *models.WorkflowStep, execCtx *ExecutionContext) (*StepResult, error) {
	deploymentID, ok := step.Config["deploymentId"].(string)
	if !ok {
		return &StepResult{Success: false, Error: fmt.Errorf("deploymentId not specified")}, nil
	}

	logs := []models.StepLog{
		{
			Timestamp: time.Now(),
			Level:     "info",
			Message:   fmt.Sprintf("Starting deployment: %s", deploymentID),
			Source:    "system",
		},
	}

	// Get deployment service URL from config or environment
	deploymentServiceURL := os.Getenv("DEPLOYMENT_SERVICE_URL")
	if deploymentServiceURL == "" {
		deploymentServiceURL = "http://localhost:8082" // Default for local development
	}

	// Call deployment service to execute the deployment
	success, deploymentOutput, err := e.executeDeployment(ctx, deploymentServiceURL, deploymentID, step.Config)

	if err != nil {
		logs = append(logs, models.StepLog{
			Timestamp: time.Now(),
			Level:     "error",
			Message:   fmt.Sprintf("Deployment failed: %v", err),
			Source:    "system",
		})
		return &StepResult{Success: false, Error: err, Logs: logs}, nil
	}

	output := map[string]interface{}{
		"deploymentId": deploymentID,
		"status":       "completed",
		"message":      "Deployment completed successfully",
		"details":      deploymentOutput,
	}

	if success {
		logs = append(logs, models.StepLog{
			Timestamp: time.Now(),
			Level:     "info",
			Message:   "Deployment completed successfully",
			Source:    "system",
		})
	} else {
		logs = append(logs, models.StepLog{
			Timestamp: time.Now(),
			Level:     "warning",
			Message:   "Deployment completed with warnings",
			Source:    "system",
		})
	}

	return &StepResult{Success: success, Output: output, Logs: logs}, nil
}

// executeDeployment calls the deployment service to execute a deployment
func (e *DeploymentExecutor) executeDeployment(ctx context.Context, serviceURL, deploymentID string, config map[string]interface{}) (bool, map[string]interface{}, error) {
	// Create request payload
	requestBody := map[string]interface{}{
		"deploymentId": deploymentID,
		"config":       config,
	}

	// Convert to JSON
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return false, nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", serviceURL+"/api/v1/deployments/"+deploymentID+"/execute", bytes.NewBuffer(jsonData))
	if err != nil {
		return false, nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	// Execute request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return false, nil, fmt.Errorf("failed to execute deployment request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Parse response
	var response map[string]interface{}
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return false, nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// Check if deployment was successful
	success := resp.StatusCode == http.StatusOK
	if !success {
		return false, response, fmt.Errorf("deployment service returned status %d", resp.StatusCode)
	}

	return true, response, nil
}

// ConditionExecutor executes conditional steps
type ConditionExecutor struct {
	logger *zap.Logger
}

func (e *ConditionExecutor) GetType() string {
	return "condition"
}

func (e *ConditionExecutor) Execute(ctx context.Context, step *models.WorkflowStep, execCtx *ExecutionContext) (*StepResult, error) {
	condition, ok := step.Config["condition"].(string)
	if !ok {
		return &StepResult{Success: false, Error: fmt.Errorf("condition not specified")}, nil
	}

	// Simple condition evaluation (can be enhanced with a proper expression parser)
	result := e.evaluateCondition(condition, execCtx.Variables)

	logs := []models.StepLog{
		{
			Timestamp: time.Now(),
			Level:     "info",
			Message:   fmt.Sprintf("Condition '%s' evaluated to: %t", condition, result),
		},
	}

	output := map[string]interface{}{
		"condition": condition,
		"result":    result,
	}

	return &StepResult{Success: true, Output: output, Logs: logs}, nil
}

func (e *ConditionExecutor) evaluateCondition(condition string, variables map[string]interface{}) bool {
	// Simple condition evaluation - in a real implementation, use a proper expression parser
	// For now, just check if a variable exists and is truthy
	if value, exists := variables[condition]; exists {
		if boolVal, ok := value.(bool); ok {
			return boolVal
		}
		if strVal, ok := value.(string); ok {
			return strVal != ""
		}
		return true
	}
	return false
}

// ParallelExecutor executes steps in parallel
type ParallelExecutor struct {
	engine *WorkflowEngine
	logger *zap.Logger
}

func (e *ParallelExecutor) GetType() string {
	return "parallel"
}

func (e *ParallelExecutor) Execute(ctx context.Context, step *models.WorkflowStep, execCtx *ExecutionContext) (*StepResult, error) {
	steps, ok := step.Config["steps"].([]interface{})
	if !ok {
		return &StepResult{Success: false, Error: fmt.Errorf("steps not specified for parallel execution")}, nil
	}

	logs := []models.StepLog{
		{
			Timestamp: time.Now(),
			Level:     "info",
			Message:   fmt.Sprintf("Starting parallel execution of %d steps", len(steps)),
		},
	}

	// Convert to WorkflowStep structs
	parallelSteps := make([]models.WorkflowStep, 0, len(steps))
	for _, stepData := range steps {
		if stepBytes, err := json.Marshal(stepData); err == nil {
			var workflowStep models.WorkflowStep
			if err := json.Unmarshal(stepBytes, &workflowStep); err == nil {
				parallelSteps = append(parallelSteps, workflowStep)
			}
		}
	}

	// Execute steps in parallel (simplified implementation)
	results := make(chan *StepResult, len(parallelSteps))
	errors := make(chan error, len(parallelSteps))

	for _, parallelStep := range parallelSteps {
		go func(s models.WorkflowStep) {
			if executor, exists := e.engine.executors[s.Type]; exists {
				result, err := executor.Execute(ctx, &s, execCtx)
				if err != nil {
					errors <- err
				} else {
					results <- result
				}
			} else {
				errors <- fmt.Errorf("no executor for step type: %s", s.Type)
			}
		}(parallelStep)
	}

	// Wait for all steps to complete
	successCount := 0
	for i := 0; i < len(parallelSteps); i++ {
		select {
		case result := <-results:
			if result.Success {
				successCount++
			}
		case err := <-errors:
			logs = append(logs, models.StepLog{
				Timestamp: time.Now(),
				Level:     "error",
				Message:   fmt.Sprintf("Parallel step failed: %v", err),
			})
		case <-ctx.Done():
			return &StepResult{Success: false, Error: ctx.Err(), Logs: logs}, nil
		}
	}

	allSuccess := successCount == len(parallelSteps)
	logs = append(logs, models.StepLog{
		Timestamp: time.Now(),
		Level:     "info",
		Message:   fmt.Sprintf("Parallel execution completed: %d/%d steps successful", successCount, len(parallelSteps)),
	})

	output := map[string]interface{}{
		"totalSteps":      len(parallelSteps),
		"successfulSteps": successCount,
		"allSuccessful":   allSuccess,
	}

	return &StepResult{Success: allSuccess, Output: output, Logs: logs}, nil
}

// SequentialExecutor executes steps sequentially
type SequentialExecutor struct {
	engine *WorkflowEngine
	logger *zap.Logger
}

func (e *SequentialExecutor) GetType() string {
	return "sequential"
}

func (e *SequentialExecutor) Execute(ctx context.Context, step *models.WorkflowStep, execCtx *ExecutionContext) (*StepResult, error) {
	steps, ok := step.Config["steps"].([]interface{})
	if !ok {
		return &StepResult{Success: false, Error: fmt.Errorf("steps not specified for sequential execution")}, nil
	}

	logs := []models.StepLog{
		{
			Timestamp: time.Now(),
			Level:     "info",
			Message:   fmt.Sprintf("Starting sequential execution of %d steps", len(steps)),
		},
	}

	// Convert to WorkflowStep structs
	sequentialSteps := make([]models.WorkflowStep, 0, len(steps))
	for _, stepData := range steps {
		if stepBytes, err := json.Marshal(stepData); err == nil {
			var workflowStep models.WorkflowStep
			if err := json.Unmarshal(stepBytes, &workflowStep); err == nil {
				sequentialSteps = append(sequentialSteps, workflowStep)
			}
		}
	}

	// Execute steps sequentially
	for i, seqStep := range sequentialSteps {
		if executor, exists := e.engine.executors[seqStep.Type]; exists {
			result, err := executor.Execute(ctx, &seqStep, execCtx)
			if err != nil || !result.Success {
				logs = append(logs, models.StepLog{
					Timestamp: time.Now(),
					Level:     "error",
					Message:   fmt.Sprintf("Sequential step %d failed", i+1),
				})
				return &StepResult{Success: false, Error: err, Logs: logs}, nil
			}
			logs = append(logs, result.Logs...)
		} else {
			err := fmt.Errorf("no executor for step type: %s", seqStep.Type)
			return &StepResult{Success: false, Error: err, Logs: logs}, nil
		}
	}

	logs = append(logs, models.StepLog{
		Timestamp: time.Now(),
		Level:     "info",
		Message:   "Sequential execution completed successfully",
	})

	output := map[string]interface{}{
		"totalSteps": len(sequentialSteps),
		"completed":  true,
	}

	return &StepResult{Success: true, Output: output, Logs: logs}, nil
}
