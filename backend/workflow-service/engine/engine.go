package engine

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/audit"
	"github.com/claudio/deploy-orchestrator/workflow-service/clients"
	"github.com/claudio/deploy-orchestrator/workflow-service/instance"
	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"github.com/claudio/deploy-orchestrator/workflow-service/monitoring"
	"github.com/claudio/deploy-orchestrator/workflow-service/notifications"
	"github.com/claudio/deploy-orchestrator/workflow-service/storage"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// WorkflowEngine manages workflow execution
type WorkflowEngine struct {
	db                *storage.Database
	logger            *zap.Logger
	monitor           *monitoring.ExecutionMonitor
	registry          instance.InstanceRegistry
	instanceManager   *instance.InstanceManager
	coordinator       *instance.ExecutionCoordinator
	auditLogger       *audit.AuditLogger
	notificationSvc   *notifications.NotificationService
	executors         map[string]StepExecutor
	runningExecutions sync.Map // map[string]*ExecutionContext
	maxConcurrent     int
	currentExecutions int
	mu                sync.RWMutex
}

// ExecutionContext holds the context for a workflow execution
type ExecutionContext struct {
	Execution     *models.WorkflowExecution
	Workflow      *models.WorkflowDefinition
	Variables     map[string]interface{}
	StepStates    map[string]string // stepID -> status
	CancelFunc    context.CancelFunc
	Logger        *zap.Logger
	SecretsClient *clients.SecretsClient
	AuthToken     string
	mu            sync.RWMutex
}

// StepExecutor interface for different step types
type StepExecutor interface {
	Execute(ctx context.Context, step *models.WorkflowStep, execCtx *ExecutionContext) (*StepResult, error)
	GetType() string
}

// StepResult represents the result of a step execution
type StepResult struct {
	Success bool
	Output  map[string]interface{}
	Error   error
	Logs    []models.StepLog
}

// NewWorkflowEngine creates a new workflow engine
func NewWorkflowEngine(db *storage.Database) *WorkflowEngine {
	logger, _ := zap.NewProduction()

	// Initialize execution monitor
	monitor := monitoring.NewExecutionMonitor(db, logger)

	// Initialize instance registry based on database type
	var registry instance.InstanceRegistry
	if db.GetDB() != nil {
		// Using real database
		registry = instance.NewDatabaseInstanceRegistry(db.GetDB(), logger)
	} else {
		// Using in-memory database
		registry = instance.NewInMemoryInstanceRegistry(logger)
	}

	// Initialize instance manager
	instanceManager, err := instance.NewInstanceManager(registry, logger)
	if err != nil {
		logger.Fatal("Failed to create instance manager", zap.Error(err))
	}

	// Initialize execution coordinator
	coordinator := instance.NewExecutionCoordinator(registry, logger)

	// Initialize audit logger
	auditLogger := audit.NewAuditLogger(db, logger)

	// Initialize notification service (notification service URL can be configured)
	notificationSvc := notifications.NewNotificationService(db, logger, "")

	engine := &WorkflowEngine{
		db:              db,
		logger:          logger,
		monitor:         monitor,
		registry:        registry,
		instanceManager: instanceManager,
		coordinator:     coordinator,
		auditLogger:     auditLogger,
		notificationSvc: notificationSvc,
		executors:       make(map[string]StepExecutor),
		maxConcurrent:   10,
	}

	// Register built-in step executors
	engine.RegisterExecutor(&ScriptExecutor{logger: logger})
	engine.RegisterExecutor(&HTTPExecutor{logger: logger})
	engine.RegisterExecutor(&DeploymentExecutor{logger: logger})
	engine.RegisterExecutor(&ConditionExecutor{logger: logger})
	engine.RegisterExecutor(&ParallelExecutor{engine: engine, logger: logger})
	engine.RegisterExecutor(&SequentialExecutor{engine: engine, logger: logger})

	// Register enhanced executors
	engine.RegisterExecutor(&AdvancedConditionExecutor{logger: logger})
	engine.RegisterExecutor(NewEnhancedScriptExecutor(logger))

	return engine
}

// RegisterExecutor registers a step executor
func (e *WorkflowEngine) RegisterExecutor(executor StepExecutor) {
	e.executors[executor.GetType()] = executor
}

// StartExecution starts a new workflow execution
func (e *WorkflowEngine) StartExecution(ctx context.Context, workflowID string, parameters map[string]interface{}, startedBy string, triggerType string, triggerData map[string]interface{}) (*models.WorkflowExecution, error) {
	// Check concurrent execution limit
	e.mu.Lock()
	if e.currentExecutions >= e.maxConcurrent {
		e.mu.Unlock()
		return nil, fmt.Errorf("maximum concurrent executions reached (%d)", e.maxConcurrent)
	}
	e.currentExecutions++
	e.mu.Unlock()

	// Get workflow definition
	workflow, err := e.db.GetWorkflowDefinition(ctx, workflowID)
	if err != nil {
		e.mu.Lock()
		e.currentExecutions--
		e.mu.Unlock()
		return nil, fmt.Errorf("failed to get workflow definition: %w", err)
	}

	// Create execution record
	execution := &models.WorkflowExecution{
		ID:          uuid.New().String(),
		WorkflowID:  workflowID,
		ProjectID:   workflow.ProjectID,
		Status:      "pending",
		StartedBy:   startedBy,
		StartedAt:   timePtr(time.Now()),
		Parameters:  parameters,
		Variables:   make(map[string]interface{}),
		TriggerType: triggerType,
		TriggerData: triggerData,
	}

	// Initialize variables with workflow defaults and parameters
	for k, v := range workflow.Variables {
		execution.Variables[k] = v
	}
	for k, v := range parameters {
		execution.Variables[k] = v
	}

	// Save execution to database
	if err := e.db.CreateWorkflowExecution(ctx, execution); err != nil {
		e.mu.Lock()
		e.currentExecutions--
		e.mu.Unlock()
		return nil, fmt.Errorf("failed to create execution record: %w", err)
	}

	// Initialize monitoring for this execution
	e.monitor.StartExecution(execution.ID, workflowID, len(workflow.Steps))

	// Log audit event for execution start
	auditCtx := &audit.AuditContext{
		UserID:    startedBy,
		ProjectID: workflow.ProjectID,
	}
	e.auditLogger.LogExecutionStarted(context.Background(), auditCtx, execution)

	// Create and send execution started event
	event := &models.ExecutionEvent{
		ID:          uuid.New().String(),
		ExecutionID: execution.ID,
		EventType:   "execution_started",
		EventData: map[string]interface{}{
			"workflowId":   workflowID,
			"workflowName": workflow.Name,
			"startedBy":    startedBy,
			"triggerType":  triggerType,
		},
		Timestamp: time.Now(),
		Severity:  "info",
		Source:    "workflow-engine",
		UserID:    startedBy,
		ProjectID: workflow.ProjectID,
	}
	e.notificationSvc.ProcessEvent(context.Background(), event)

	// Start execution in background
	go e.executeWorkflow(execution, workflow)

	return execution, nil
}

// executeWorkflow executes a workflow in a separate goroutine
func (e *WorkflowEngine) executeWorkflow(execution *models.WorkflowExecution, workflow *models.WorkflowDefinition) {
	defer func() {
		e.mu.Lock()
		e.currentExecutions--
		e.mu.Unlock()
		e.runningExecutions.Delete(execution.ID)
	}()

	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(60)*time.Minute) // Default timeout
	defer cancel()

	// Initialize secrets client (URL should come from config)
	secretsClient := clients.NewSecretsClient("http://localhost:8087", e.logger)

	execCtx := &ExecutionContext{
		Execution:     execution,
		Workflow:      workflow,
		Variables:     execution.Variables,
		StepStates:    make(map[string]string),
		CancelFunc:    cancel,
		Logger:        e.logger.With(zap.String("executionId", execution.ID)),
		SecretsClient: secretsClient,
		AuthToken:     "", // TODO: Get auth token from context
	}

	e.runningExecutions.Store(execution.ID, execCtx)

	// Update execution status to running
	execution.Status = "running"
	e.db.UpdateWorkflowExecution(ctx, execution)

	execCtx.Logger.Info("Starting workflow execution",
		zap.String("workflowId", workflow.ID),
		zap.String("workflowName", workflow.Name))

	// Execute workflow steps
	err := e.executeSteps(ctx, workflow.Steps, execCtx)

	// Update final execution status
	execution.CompletedAt = timePtr(time.Now())
	totalDuration := execution.CompletedAt.Sub(*execution.StartedAt)
	execution.Duration = int(totalDuration.Seconds())

	success := err == nil
	if err != nil {
		execution.Status = "failed"
		execution.ErrorMessage = err.Error()
		execCtx.Logger.Error("Workflow execution failed", zap.Error(err))
	} else {
		execution.Status = "completed"
		execCtx.Logger.Info("Workflow execution completed successfully")
	}

	// Notify monitor of execution completion
	e.monitor.ExecutionCompleted(execution.ID, totalDuration, success)

	// Log audit event for execution completion
	auditCtx := &audit.AuditContext{
		UserID:    execution.StartedBy,
		ProjectID: execution.ProjectID,
	}
	e.auditLogger.LogExecutionCompleted(context.Background(), auditCtx, execution, success)

	// Create and send execution completed/failed event
	eventType := "execution_completed"
	severity := "info"
	if !success {
		eventType = "execution_failed"
		severity = "error"
	}

	event := &models.ExecutionEvent{
		ID:          uuid.New().String(),
		ExecutionID: execution.ID,
		EventType:   eventType,
		EventData: map[string]interface{}{
			"workflowId":   execution.WorkflowID,
			"workflowName": workflow.Name,
			"duration":     execution.Duration,
			"success":      success,
			"error":        execution.ErrorMessage,
		},
		Timestamp: time.Now(),
		Severity:  severity,
		Source:    "workflow-engine",
		UserID:    execution.StartedBy,
		ProjectID: execution.ProjectID,
	}
	e.notificationSvc.ProcessEvent(context.Background(), event)

	e.db.UpdateWorkflowExecution(ctx, execution)
}

// executeSteps executes a list of workflow steps
func (e *WorkflowEngine) executeSteps(ctx context.Context, steps []models.WorkflowStep, execCtx *ExecutionContext) error {
	// Build dependency graph
	stepMap := make(map[string]*models.WorkflowStep)
	dependents := make(map[string][]string) // stepID -> list of steps that depend on it

	for i := range steps {
		stepMap[steps[i].ID] = &steps[i]
		dependents[steps[i].ID] = make([]string, 0)
	}

	// Build reverse dependency map (dependents)
	for i := range steps {
		for _, depID := range steps[i].Dependencies {
			if _, exists := dependents[depID]; exists {
				dependents[depID] = append(dependents[depID], steps[i].ID)
			}
		}
	}

	// Find steps with no dependencies (entry points)
	entrySteps := make([]*models.WorkflowStep, 0)
	for i := range steps {
		if len(steps[i].Dependencies) == 0 {
			entrySteps = append(entrySteps, &steps[i])
		}
	}

	if len(entrySteps) == 0 {
		return fmt.Errorf("no entry steps found (steps without dependencies)")
	}

	execCtx.Logger.Info("Starting workflow execution",
		zap.Int("totalSteps", len(steps)),
		zap.Int("entrySteps", len(entrySteps)))

	// Execute all steps using proper dependency resolution
	return e.executeAllSteps(ctx, stepMap, dependents, execCtx)
}

// executeAllSteps executes all workflow steps in proper dependency order
func (e *WorkflowEngine) executeAllSteps(ctx context.Context, stepMap map[string]*models.WorkflowStep, dependents map[string][]string, execCtx *ExecutionContext) error {
	// Find entry steps (steps with no dependencies and not referenced by OnSuccess/OnFailure)
	readySteps := make([]*models.WorkflowStep, 0)
	referencedSteps := make(map[string]bool)

	// Mark all steps that are referenced by OnSuccess or OnFailure
	for _, step := range stepMap {
		for _, onSuccessStepID := range step.OnSuccess {
			referencedSteps[onSuccessStepID] = true
		}
		for _, onFailureStepID := range step.OnFailure {
			referencedSteps[onFailureStepID] = true
		}
	}

	// Entry steps are those with no dependencies and not referenced by OnSuccess/OnFailure
	for _, step := range stepMap {
		if len(step.Dependencies) == 0 && !referencedSteps[step.ID] {
			readySteps = append(readySteps, step)
		}
	}

	// Execute steps in dependency order
	for len(readySteps) > 0 {
		// Get next step to execute
		currentStep := readySteps[0]
		readySteps = readySteps[1:]

		execCtx.Logger.Info("Executing step",
			zap.String("stepId", currentStep.ID),
			zap.String("stepName", currentStep.Name),
			zap.String("stepType", currentStep.Type))

		// Execute the step
		if err := e.executeStep(ctx, currentStep, execCtx); err != nil {
			execCtx.Logger.Error("Step execution failed",
				zap.String("stepId", currentStep.ID),
				zap.String("stepName", currentStep.Name),
				zap.Error(err))
			return err
		}

		// Check if step completed successfully
		execCtx.mu.RLock()
		stepStatus := execCtx.StepStates[currentStep.ID]
		execCtx.mu.RUnlock()

		if stepStatus == "completed" {
			execCtx.Logger.Info("Step completed successfully, checking for dependent and OnSuccess steps",
				zap.String("stepId", currentStep.ID),
				zap.String("stepName", currentStep.Name))

			// Process OnSuccess steps first
			for _, onSuccessStepID := range currentStep.OnSuccess {
				if onSuccessStep, exists := stepMap[onSuccessStepID]; exists {
					execCtx.mu.RLock()
					_, alreadyExecuted := execCtx.StepStates[onSuccessStepID]
					execCtx.mu.RUnlock()

					if !alreadyExecuted {
						execCtx.Logger.Info("Adding OnSuccess step to execution queue",
							zap.String("onSuccessStepId", onSuccessStepID),
							zap.String("onSuccessStepName", onSuccessStep.Name),
							zap.String("triggeredBy", currentStep.ID))
						readySteps = append(readySteps, onSuccessStep)
					}
				} else {
					execCtx.Logger.Warn("OnSuccess step not found in workflow",
						zap.String("onSuccessStepId", onSuccessStepID),
						zap.String("parentStepId", currentStep.ID))
				}
			}

			// Find steps that can now be executed (all dependencies met)
			for _, dependentStepID := range dependents[currentStep.ID] {
				dependentStep := stepMap[dependentStepID]

				// Check if all dependencies are completed
				allDepsCompleted := true
				for _, depID := range dependentStep.Dependencies {
					execCtx.mu.RLock()
					depStatus, exists := execCtx.StepStates[depID]
					execCtx.mu.RUnlock()

					if !exists || depStatus != "completed" {
						allDepsCompleted = false
						break
					}
				}

				// If all dependencies are completed and step hasn't been executed yet
				execCtx.mu.RLock()
				_, alreadyExecuted := execCtx.StepStates[dependentStepID]
				execCtx.mu.RUnlock()

				if allDepsCompleted && !alreadyExecuted {
					execCtx.Logger.Info("Adding dependent step to execution queue",
						zap.String("dependentStepId", dependentStepID),
						zap.String("dependentStepName", dependentStep.Name),
						zap.String("triggeredBy", currentStep.ID))
					readySteps = append(readySteps, dependentStep)
				}
			}
		} else if stepStatus == "failed" {
			execCtx.Logger.Info("Step failed, checking for OnFailure steps",
				zap.String("stepId", currentStep.ID),
				zap.String("stepName", currentStep.Name))

			// Process OnFailure steps
			hasOnFailureSteps := false
			for _, onFailureStepID := range currentStep.OnFailure {
				if onFailureStep, exists := stepMap[onFailureStepID]; exists {
					execCtx.mu.RLock()
					_, alreadyExecuted := execCtx.StepStates[onFailureStepID]
					execCtx.mu.RUnlock()

					if !alreadyExecuted {
						execCtx.Logger.Info("Adding OnFailure step to execution queue",
							zap.String("onFailureStepId", onFailureStepID),
							zap.String("onFailureStepName", onFailureStep.Name),
							zap.String("triggeredBy", currentStep.ID))
						readySteps = append(readySteps, onFailureStep)
						hasOnFailureSteps = true
					}
				} else {
					execCtx.Logger.Warn("OnFailure step not found in workflow",
						zap.String("onFailureStepId", onFailureStepID),
						zap.String("parentStepId", currentStep.ID))
				}
			}

			// If no OnFailure steps are defined, stop the workflow execution
			if !hasOnFailureSteps {
				return fmt.Errorf("step %s failed, stopping workflow execution", currentStep.Name)
			}
		}
	}

	execCtx.Logger.Info("All workflow steps completed")
	return nil
}

// executeStepWithDependencies executes a step and its dependent steps
func (e *WorkflowEngine) executeStepWithDependencies(ctx context.Context, step *models.WorkflowStep, stepMap map[string]*models.WorkflowStep, execCtx *ExecutionContext) error {
	// Check if step already executed
	execCtx.mu.RLock()
	if status, exists := execCtx.StepStates[step.ID]; exists {
		execCtx.mu.RUnlock()
		if status == "completed" || status == "failed" || status == "skipped" {
			return nil // Already processed
		}
	} else {
		execCtx.mu.RUnlock()
	}

	// Check dependencies
	for _, depID := range step.Dependencies {
		execCtx.mu.RLock()
		depStatus, exists := execCtx.StepStates[depID]
		execCtx.mu.RUnlock()

		if !exists || depStatus != "completed" {
			// Dependency not completed, execute it first
			if depStep, exists := stepMap[depID]; exists {
				if err := e.executeStepWithDependencies(ctx, depStep, stepMap, execCtx); err != nil {
					return err
				}
			}
		}
	}

	// Execute the step
	return e.executeStep(ctx, step, execCtx)
}

// injectSecretsIntoStep retrieves secrets for a step and injects them into the execution context
func (e *WorkflowEngine) injectSecretsIntoStep(ctx context.Context, step *models.WorkflowStep, execCtx *ExecutionContext) error {
	if execCtx.SecretsClient == nil {
		// No secrets client configured, skip secret injection
		return nil
	}

	// Get secrets for this step with secret mapping
	secretsReq := clients.WorkflowSecretsRequest{
		WorkflowID:    execCtx.Execution.WorkflowID,
		ExecutionID:   execCtx.Execution.ID,
		ProjectID:     execCtx.Execution.ProjectID,
		StepName:      step.Name,
		SecretMapping: execCtx.Execution.SecretMapping,
	}

	secretsResp, err := execCtx.SecretsClient.GetWorkflowSecrets(ctx, secretsReq, execCtx.AuthToken)
	if err != nil {
		execCtx.Logger.Warn("Failed to retrieve secrets for step",
			zap.String("stepId", step.ID),
			zap.String("stepName", step.Name),
			zap.Error(err))
		// Don't fail the step execution, just log the warning
		return nil
	}

	if len(secretsResp.Variables) > 0 {
		execCtx.Logger.Info("Injecting secrets into step",
			zap.String("stepId", step.ID),
			zap.String("stepName", step.Name),
			zap.Int("secretCount", len(secretsResp.Variables)))

		// Inject secrets into step configuration based on type
		for _, secret := range secretsResp.Variables {
			switch secret.Type {
			case "env":
				// Add to environment variables in step config
				if step.Config == nil {
					step.Config = make(map[string]interface{})
				}
				if step.Config["env"] == nil {
					step.Config["env"] = make(map[string]interface{})
				}
				if envMap, ok := step.Config["env"].(map[string]interface{}); ok {
					envMap[secret.Name] = secret.Value
				}

			case "file":
				// Add to files configuration
				if step.Config == nil {
					step.Config = make(map[string]interface{})
				}
				if step.Config["files"] == nil {
					step.Config["files"] = make([]interface{}, 0)
				}
				if filesList, ok := step.Config["files"].([]interface{}); ok {
					fileConfig := map[string]interface{}{
						"path":    secret.Path,
						"content": secret.Value,
						"format":  secret.Format,
					}
					step.Config["files"] = append(filesList, fileConfig)
				}

			case "config":
				// Add to configuration variables
				if step.Config == nil {
					step.Config = make(map[string]interface{})
				}
				if step.Config["config"] == nil {
					step.Config["config"] = make(map[string]interface{})
				}
				if configMap, ok := step.Config["config"].(map[string]interface{}); ok {
					configMap[secret.Name] = secret.Value
				}

			case "mount":
				// Add to volume mounts
				if step.Config == nil {
					step.Config = make(map[string]interface{})
				}
				if step.Config["mounts"] == nil {
					step.Config["mounts"] = make([]interface{}, 0)
				}
				if mountsList, ok := step.Config["mounts"].([]interface{}); ok {
					mountConfig := map[string]interface{}{
						"path":    secret.Path,
						"content": secret.Value,
						"name":    secret.Name,
					}
					step.Config["mounts"] = append(mountsList, mountConfig)
				}
			}
		}
	}

	// Log any errors from secrets retrieval
	if len(secretsResp.Errors) > 0 {
		for _, errMsg := range secretsResp.Errors {
			execCtx.Logger.Warn("Secret retrieval error",
				zap.String("stepId", step.ID),
				zap.String("stepName", step.Name),
				zap.String("error", errMsg))
		}
	}

	return nil
}

// executeStep executes a single workflow step
func (e *WorkflowEngine) executeStep(ctx context.Context, step *models.WorkflowStep, execCtx *ExecutionContext) error {
	execCtx.mu.Lock()
	execCtx.StepStates[step.ID] = "running"
	execCtx.Execution.CurrentStep = step.ID
	execCtx.mu.Unlock()

	// Update execution in database
	e.db.UpdateWorkflowExecution(ctx, execCtx.Execution)

	stepLogger := execCtx.Logger.With(
		zap.String("stepId", step.ID),
		zap.String("stepName", step.Name),
		zap.String("stepType", step.Type))

	stepLogger.Info("Starting step execution")

	// Notify monitor that step is starting
	e.monitor.StepStarted(execCtx.Execution.ID, step.ID, step.Name)

	// Log audit event for step start
	auditCtx := &audit.AuditContext{
		UserID:    execCtx.Execution.StartedBy,
		ProjectID: execCtx.Execution.ProjectID,
	}
	e.auditLogger.LogStepStarted(context.Background(), auditCtx, execCtx.Execution.WorkflowID, execCtx.Execution.ID, step.ID, step.Name)

	// Create and send step started event
	stepStartEvent := &models.ExecutionEvent{
		ID:          uuid.New().String(),
		ExecutionID: execCtx.Execution.ID,
		StepID:      step.ID,
		EventType:   "step_started",
		EventData: map[string]interface{}{
			"stepName": step.Name,
			"stepType": step.Type,
		},
		Timestamp: time.Now(),
		Severity:  "info",
		Source:    "workflow-engine",
		UserID:    execCtx.Execution.StartedBy,
		ProjectID: execCtx.Execution.ProjectID,
	}
	e.notificationSvc.ProcessEvent(context.Background(), stepStartEvent)

	// Check conditions
	if !e.evaluateConditions(step.Conditions, execCtx.Variables) {
		execCtx.mu.Lock()
		execCtx.StepStates[step.ID] = "skipped"
		execCtx.mu.Unlock()
		stepLogger.Info("Step skipped due to conditions")
		e.monitor.StepSkipped(execCtx.Execution.ID, step.ID, step.Name, "conditions not met")
		return nil
	}

	// Create step execution record
	stepExecution := &models.StepExecution{
		ID:          uuid.New().String(),
		ExecutionID: execCtx.Execution.ID,
		StepID:      step.ID,
		StepName:    step.Name,
		Status:      "running",
		StartedAt:   timePtr(time.Now()),
		Input:       step.Config,
	}

	e.db.CreateStepExecution(ctx, stepExecution)

	// Get executor for step type
	executor, exists := e.executors[step.Type]
	if !exists {
		err := fmt.Errorf("no executor found for step type: %s", step.Type)
		stepExecution.Status = "failed"
		stepExecution.ErrorMessage = err.Error()
		stepExecution.CompletedAt = timePtr(time.Now())
		stepDuration := stepExecution.CompletedAt.Sub(*stepExecution.StartedAt)
		stepExecution.Duration = int(stepDuration.Seconds())
		e.db.UpdateStepExecution(ctx, stepExecution)

		execCtx.mu.Lock()
		execCtx.StepStates[step.ID] = "failed"
		execCtx.mu.Unlock()

		stepLogger.Error("No executor found for step type", zap.Error(err))
		e.monitor.StepFailed(execCtx.Execution.ID, step.ID, step.Name, stepDuration, err)

		// Don't return error - let the caller handle OnFailure logic
		return nil
	}

	// Execute step with timeout
	stepCtx := ctx
	if step.Timeout > 0 {
		var cancel context.CancelFunc
		stepCtx, cancel = context.WithTimeout(ctx, time.Duration(step.Timeout)*time.Minute)
		defer cancel()
	}

	// Execute with retry policy
	var result *StepResult
	var err error
	maxAttempts := step.RetryPolicy.MaxAttempts
	if maxAttempts <= 0 {
		maxAttempts = 1
	}

	for attempt := 1; attempt <= maxAttempts; attempt++ {
		stepExecution.AttemptCount = attempt
		e.db.UpdateStepExecution(ctx, stepExecution)

		// Inject secrets into step before execution (only on first attempt)
		if attempt == 1 {
			if secretErr := e.injectSecretsIntoStep(stepCtx, step, execCtx); secretErr != nil {
				stepLogger.Warn("Failed to inject secrets into step", zap.Error(secretErr))
				// Continue with execution even if secret injection fails
			}
		}

		result, err = executor.Execute(stepCtx, step, execCtx)
		if err == nil && result.Success {
			break // Success, no need to retry
		}

		if attempt < maxAttempts {
			delay := time.Duration(step.RetryPolicy.DelaySeconds) * time.Second
			if step.RetryPolicy.BackoffMultiplier > 1 {
				delay = time.Duration(float64(delay) * step.RetryPolicy.BackoffMultiplier)
			}
			stepLogger.Warn("Step failed, retrying",
				zap.Int("attempt", attempt),
				zap.Int("maxAttempts", maxAttempts),
				zap.Duration("delay", delay),
				zap.Error(err))
			time.Sleep(delay)
		}
	}

	// Update step execution result
	stepExecution.CompletedAt = timePtr(time.Now())
	stepDuration := stepExecution.CompletedAt.Sub(*stepExecution.StartedAt)
	stepExecution.Duration = int(stepDuration.Seconds())

	// Prepare audit context for step completion logging (reuse from earlier)
	// auditCtx was already declared above

	if err != nil || !result.Success {
		stepExecution.Status = "failed"
		if err != nil {
			stepExecution.ErrorMessage = err.Error()
		} else if result.Error != nil {
			stepExecution.ErrorMessage = result.Error.Error()
		}

		execCtx.mu.Lock()
		execCtx.StepStates[step.ID] = "failed"
		execCtx.mu.Unlock()

		stepLogger.Error("Step execution failed", zap.Error(err))
		e.monitor.StepFailed(execCtx.Execution.ID, step.ID, step.Name, stepDuration, err)

		// Log audit event for step failure
		e.auditLogger.LogStepCompleted(context.Background(), auditCtx, execCtx.Execution.WorkflowID, execCtx.Execution.ID, step.ID, step.Name, false, stepDuration)

		// Create and send step failed event
		stepFailEvent := &models.ExecutionEvent{
			ID:          uuid.New().String(),
			ExecutionID: execCtx.Execution.ID,
			StepID:      step.ID,
			EventType:   "step_failed",
			EventData: map[string]interface{}{
				"stepName": step.Name,
				"stepType": step.Type,
				"duration": stepDuration.Seconds(),
				"error":    stepExecution.ErrorMessage,
			},
			Timestamp: time.Now(),
			Severity:  "error",
			Source:    "workflow-engine",
			UserID:    execCtx.Execution.StartedBy,
			ProjectID: execCtx.Execution.ProjectID,
		}
		e.notificationSvc.ProcessEvent(context.Background(), stepFailEvent)
	} else {
		stepExecution.Status = "completed"
		stepExecution.Output = result.Output

		// Update execution variables with step output
		execCtx.mu.Lock()
		for k, v := range result.Output {
			execCtx.Variables[k] = v
		}
		execCtx.StepStates[step.ID] = "completed"
		execCtx.mu.Unlock()

		stepLogger.Info("Step execution completed successfully")
		e.monitor.StepCompleted(execCtx.Execution.ID, step.ID, step.Name, stepDuration, result.Output)

		// Log audit event for step completion
		e.auditLogger.LogStepCompleted(context.Background(), auditCtx, execCtx.Execution.WorkflowID, execCtx.Execution.ID, step.ID, step.Name, true, stepDuration)

		// Create and send step completed event
		stepCompleteEvent := &models.ExecutionEvent{
			ID:          uuid.New().String(),
			ExecutionID: execCtx.Execution.ID,
			StepID:      step.ID,
			EventType:   "step_completed",
			EventData: map[string]interface{}{
				"stepName": step.Name,
				"stepType": step.Type,
				"duration": stepDuration.Seconds(),
				"output":   result.Output,
			},
			Timestamp: time.Now(),
			Severity:  "info",
			Source:    "workflow-engine",
			UserID:    execCtx.Execution.StartedBy,
			ProjectID: execCtx.Execution.ProjectID,
		}
		e.notificationSvc.ProcessEvent(context.Background(), stepCompleteEvent)
	}

	if len(result.Logs) > 0 {
		stepExecution.Logs = result.Logs
	}

	e.db.UpdateStepExecution(ctx, stepExecution)

	// Don't return error for failed steps - let the caller handle OnFailure logic
	// The step state is already set to "failed" above
	return nil
}

// evaluateConditions evaluates step conditions
func (e *WorkflowEngine) evaluateConditions(conditions []models.StepCondition, variables map[string]interface{}) bool {
	if len(conditions) == 0 {
		return true // No conditions means always execute
	}

	// Simple condition evaluation (can be enhanced)
	for _, condition := range conditions {
		value, exists := variables[condition.Variable]
		if !exists && condition.Operator != "exists" {
			return false
		}

		switch condition.Operator {
		case "exists":
			if !exists {
				return false
			}
		case "eq":
			if value != condition.Value {
				return false
			}
		case "ne":
			if value == condition.Value {
				return false
			}
			// Add more operators as needed
		}
	}

	return true
}

// StopExecution stops a running workflow execution
func (e *WorkflowEngine) StopExecution(ctx context.Context, executionID string) error {
	if execCtx, exists := e.runningExecutions.Load(executionID); exists {
		if ec, ok := execCtx.(*ExecutionContext); ok {
			ec.CancelFunc()
			ec.Execution.Status = "cancelled"
			ec.Execution.CompletedAt = timePtr(time.Now())
			return e.db.UpdateWorkflowExecution(ctx, ec.Execution)
		}
	}
	return fmt.Errorf("execution not found or not running: %s", executionID)
}

// GetExecutionStatus returns the current status of an execution
func (e *WorkflowEngine) GetExecutionStatus(executionID string) (string, bool) {
	if execCtx, exists := e.runningExecutions.Load(executionID); exists {
		if ec, ok := execCtx.(*ExecutionContext); ok {
			return ec.Execution.Status, true
		}
	}
	return "", false
}

// GetExecutionMetrics returns metrics for a specific execution
func (e *WorkflowEngine) GetExecutionMetrics(executionID string) (*models.ExecutionMetrics, bool) {
	return e.monitor.GetMetrics(executionID)
}

// GetAllExecutionMetrics returns metrics for all active executions
func (e *WorkflowEngine) GetAllExecutionMetrics() map[string]*models.ExecutionMetrics {
	return e.monitor.GetAllMetrics()
}

// SubscribeToExecution subscribes to events for a specific execution
func (e *WorkflowEngine) SubscribeToExecution(executionID string) <-chan *models.ExecutionEvent {
	return e.monitor.Subscribe(executionID)
}

// UnsubscribeFromExecution removes a listener for an execution
func (e *WorkflowEngine) UnsubscribeFromExecution(executionID string, listener <-chan *models.ExecutionEvent) {
	e.monitor.Unsubscribe(executionID, listener)
}

// GetMaxConcurrentExecutions returns the maximum number of concurrent executions
func (e *WorkflowEngine) GetMaxConcurrentExecutions() int {
	return e.maxConcurrent
}

// StartInstanceManager starts the instance manager
func (e *WorkflowEngine) StartInstanceManager(ctx context.Context) error {
	return e.instanceManager.Start(ctx)
}

// StopInstanceManager stops the instance manager
func (e *WorkflowEngine) StopInstanceManager(ctx context.Context) error {
	return e.instanceManager.Stop(ctx)
}

// GetInstance returns the current instance information
func (e *WorkflowEngine) GetInstance() *instance.Instance {
	return e.instanceManager.GetInstance()
}

// UpdateInstanceLabels updates the instance labels
func (e *WorkflowEngine) UpdateInstanceLabels(ctx context.Context, labels map[string]string) error {
	return e.instanceManager.UpdateLabels(ctx, labels)
}

// UpdateInstanceCapabilities updates the instance capabilities
func (e *WorkflowEngine) UpdateInstanceCapabilities(ctx context.Context, capabilities []string) error {
	return e.instanceManager.UpdateCapabilities(ctx, capabilities)
}

// UpdateInstanceStatus updates the instance status
func (e *WorkflowEngine) UpdateInstanceStatus(ctx context.Context, status string) error {
	return e.instanceManager.UpdateStatus(ctx, status)
}

// ExecuteWorkflowDistributed executes a workflow on the best available instance
func (e *WorkflowEngine) ExecuteWorkflowDistributed(ctx context.Context, request *instance.ExecutionRequest) (*instance.ExecutionResponse, error) {
	return e.coordinator.ExecuteWorkflow(ctx, request)
}

// ExecuteWorkflowOnInstance executes a workflow on a specific instance
func (e *WorkflowEngine) ExecuteWorkflowOnInstance(ctx context.Context, instanceID string, request *instance.ExecutionRequest) (*instance.ExecutionResponse, error) {
	return e.coordinator.ExecuteWorkflowOnInstance(ctx, instanceID, request)
}

// ListAvailableInstances lists all available instances
func (e *WorkflowEngine) ListAvailableInstances(ctx context.Context, labels map[string]string) ([]*instance.Instance, error) {
	return e.coordinator.ListAvailableInstances(ctx, labels)
}

// GetInstanceStatus gets the status of a specific instance
func (e *WorkflowEngine) GetInstanceStatus(ctx context.Context, instanceID string) (*instance.Instance, error) {
	return e.coordinator.GetInstanceStatus(ctx, instanceID)
}

// GetDistributedExecutionStatus gets the status of a distributed execution
func (e *WorkflowEngine) GetDistributedExecutionStatus(ctx context.Context, executionID string) (*models.WorkflowExecution, error) {
	return e.coordinator.GetExecutionStatus(ctx, executionID)
}

// StopDistributedExecution stops a distributed execution
func (e *WorkflowEngine) StopDistributedExecution(ctx context.Context, executionID string) error {
	return e.coordinator.StopExecution(ctx, executionID)
}

// GetInstanceMetrics gets metrics from all instances
func (e *WorkflowEngine) GetInstanceMetrics(ctx context.Context) (map[string]interface{}, error) {
	return e.coordinator.GetInstanceMetrics(ctx)
}

// GetAuditLogger returns the audit logger
func (e *WorkflowEngine) GetAuditLogger() *audit.AuditLogger {
	return e.auditLogger
}

// GetInstanceManager returns the instance manager
func (e *WorkflowEngine) GetInstanceManager() *instance.InstanceManager {
	return e.instanceManager
}

// GetInstanceRegistry returns the instance registry
func (e *WorkflowEngine) GetInstanceRegistry() instance.InstanceRegistry {
	return e.registry
}

// Helper function to create time pointer
func timePtr(t time.Time) *time.Time {
	return &t
}
