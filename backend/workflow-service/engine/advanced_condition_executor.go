package engine

import (
	"context"
	"encoding/json"
	"fmt"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"go.uber.org/zap"
)

// AdvancedConditionExecutor executes advanced conditional logic
type AdvancedConditionExecutor struct {
	logger *zap.Logger
}

func (e *AdvancedConditionExecutor) GetType() string {
	return "advanced_condition"
}

func (e *AdvancedConditionExecutor) Execute(ctx context.Context, step *models.WorkflowStep, execCtx *ExecutionContext) (*StepResult, error) {
	logs := []models.StepLog{
		{
			Timestamp: time.Now(),
			Level:     "info",
			Message:   "Evaluating advanced conditions",
			Source:    "system",
		},
	}

	// Get conditions from step config
	conditionsData, ok := step.Config["conditions"]
	if !ok {
		return &StepResult{
			Success: false,
			Error:   fmt.Errorf("no conditions specified"),
			Logs:    logs,
		}, nil
	}

	// Parse conditions
	var conditions []models.AdvancedCondition
	if conditionsBytes, err := json.Marshal(conditionsData); err != nil {
		return &StepResult{
			Success: false,
			Error:   fmt.Errorf("failed to marshal conditions: %w", err),
			Logs:    logs,
		}, nil
	} else if err := json.Unmarshal(conditionsBytes, &conditions); err != nil {
		return &StepResult{
			Success: false,
			Error:   fmt.Errorf("failed to parse conditions: %w", err),
			Logs:    logs,
		}, nil
	}

	// Evaluate all conditions
	result := true
	evaluationResults := make(map[string]interface{})

	for i, condition := range conditions {
		conditionResult, err := e.evaluateCondition(ctx, condition, execCtx)
		if err != nil {
			logs = append(logs, models.StepLog{
				Timestamp: time.Now(),
				Level:     "error",
				Message:   fmt.Sprintf("Error evaluating condition %d: %v", i, err),
				Source:    "system",
			})
			return &StepResult{
				Success: false,
				Error:   err,
				Logs:    logs,
			}, nil
		}

		evaluationResults[fmt.Sprintf("condition_%d", i)] = conditionResult
		logs = append(logs, models.StepLog{
			Timestamp: time.Now(),
			Level:     "info",
			Message:   fmt.Sprintf("Condition %d evaluated to: %v", i, conditionResult),
			Source:    "system",
		})

		// For now, all conditions must be true (AND logic)
		// TODO: Implement more sophisticated logical operations
		if !conditionResult {
			result = false
		}
	}

	output := map[string]interface{}{
		"result":              result,
		"evaluationResults":   evaluationResults,
		"conditionsEvaluated": len(conditions),
	}

	logs = append(logs, models.StepLog{
		Timestamp: time.Now(),
		Level:     "info",
		Message:   fmt.Sprintf("All conditions evaluated. Final result: %v", result),
		Source:    "system",
	})

	return &StepResult{
		Success: result,
		Output:  output,
		Logs:    logs,
	}, nil
}

// evaluateCondition evaluates a single advanced condition
func (e *AdvancedConditionExecutor) evaluateCondition(ctx context.Context, condition models.AdvancedCondition, execCtx *ExecutionContext) (bool, error) {
	switch condition.Type {
	case "expression":
		return e.evaluateExpression(condition.Expression, execCtx)
	case "script":
		return e.evaluateScript(ctx, condition.Script, condition.Language, execCtx)
	case "comparison":
		return e.evaluateComparison(condition, execCtx)
	case "logical":
		return e.evaluateLogical(ctx, condition, execCtx)
	default:
		return false, fmt.Errorf("unsupported condition type: %s", condition.Type)
	}
}

// evaluateExpression evaluates a simple expression
func (e *AdvancedConditionExecutor) evaluateExpression(expression string, execCtx *ExecutionContext) (bool, error) {
	// Simple expression evaluation
	// Replace variables in the expression with their values
	evaluatedExpression := expression

	execCtx.mu.RLock()
	for key, value := range execCtx.Variables {
		placeholder := fmt.Sprintf("${%s}", key)
		if strings.Contains(evaluatedExpression, placeholder) {
			valueStr := fmt.Sprintf("%v", value)
			evaluatedExpression = strings.ReplaceAll(evaluatedExpression, placeholder, valueStr)
		}
	}
	execCtx.mu.RUnlock()

	// For now, implement basic boolean evaluation
	// TODO: Implement a proper expression parser
	switch strings.ToLower(strings.TrimSpace(evaluatedExpression)) {
	case "true", "1", "yes":
		return true, nil
	case "false", "0", "no":
		return false, nil
	default:
		// Try to evaluate as a comparison
		if strings.Contains(evaluatedExpression, "==") {
			parts := strings.Split(evaluatedExpression, "==")
			if len(parts) == 2 {
				left := strings.TrimSpace(parts[0])
				right := strings.TrimSpace(parts[1])
				return left == right, nil
			}
		}
		return false, fmt.Errorf("unable to evaluate expression: %s", evaluatedExpression)
	}
}

// evaluateScript evaluates a script-based condition
func (e *AdvancedConditionExecutor) evaluateScript(ctx context.Context, script, language string, execCtx *ExecutionContext) (bool, error) {
	if language == "" {
		language = "bash"
	}

	var cmd *exec.Cmd
	switch language {
	case "bash":
		cmd = exec.CommandContext(ctx, "bash", "-c", script)
	case "python":
		cmd = exec.CommandContext(ctx, "python3", "-c", script)
	case "javascript":
		cmd = exec.CommandContext(ctx, "node", "-e", script)
	default:
		return false, fmt.Errorf("unsupported script language: %s", language)
	}

	// Set environment variables from execution context
	env := []string{}
	execCtx.mu.RLock()
	for k, v := range execCtx.Variables {
		if str, ok := v.(string); ok {
			env = append(env, fmt.Sprintf("%s=%s", k, str))
		}
	}
	execCtx.mu.RUnlock()
	cmd.Env = env

	err := cmd.Run()
	// Script conditions return true if exit code is 0
	return err == nil, nil
}

// evaluateComparison evaluates a comparison condition
func (e *AdvancedConditionExecutor) evaluateComparison(condition models.AdvancedCondition, execCtx *ExecutionContext) (bool, error) {
	left := e.resolveValue(condition.Left, execCtx)
	right := e.resolveValue(condition.Right, execCtx)

	switch condition.Operator {
	case "==", "eq":
		return e.compareValues(left, right, "eq")
	case "!=", "ne":
		return e.compareValues(left, right, "ne")
	case ">", "gt":
		return e.compareValues(left, right, "gt")
	case "<", "lt":
		return e.compareValues(left, right, "lt")
	case ">=", "gte":
		return e.compareValues(left, right, "gte")
	case "<=", "lte":
		return e.compareValues(left, right, "lte")
	case "contains":
		return e.containsValue(left, right)
	case "exists":
		return left != nil, nil
	default:
		return false, fmt.Errorf("unsupported operator: %s", condition.Operator)
	}
}

// evaluateLogical evaluates logical conditions (AND, OR, NOT)
func (e *AdvancedConditionExecutor) evaluateLogical(ctx context.Context, condition models.AdvancedCondition, execCtx *ExecutionContext) (bool, error) {
	switch condition.LogicalOp {
	case "AND":
		for _, subCondition := range condition.Conditions {
			result, err := e.evaluateCondition(ctx, subCondition, execCtx)
			if err != nil {
				return false, err
			}
			if !result {
				return false, nil
			}
		}
		return true, nil
	case "OR":
		for _, subCondition := range condition.Conditions {
			result, err := e.evaluateCondition(ctx, subCondition, execCtx)
			if err != nil {
				return false, err
			}
			if result {
				return true, nil
			}
		}
		return false, nil
	case "NOT":
		if len(condition.Conditions) != 1 {
			return false, fmt.Errorf("NOT operator requires exactly one condition")
		}
		result, err := e.evaluateCondition(ctx, condition.Conditions[0], execCtx)
		if err != nil {
			return false, err
		}
		return !result, nil
	default:
		return false, fmt.Errorf("unsupported logical operator: %s", condition.LogicalOp)
	}
}

// resolveValue resolves a value from variables or returns the literal value
func (e *AdvancedConditionExecutor) resolveValue(value interface{}, execCtx *ExecutionContext) interface{} {
	if str, ok := value.(string); ok {
		if strings.HasPrefix(str, "${") && strings.HasSuffix(str, "}") {
			varName := str[2 : len(str)-1]
			execCtx.mu.RLock()
			if varValue, exists := execCtx.Variables[varName]; exists {
				execCtx.mu.RUnlock()
				return varValue
			}
			execCtx.mu.RUnlock()
		}
	}
	return value
}

// compareValues compares two values based on the operator
func (e *AdvancedConditionExecutor) compareValues(left, right interface{}, operator string) (bool, error) {
	// Handle nil values
	if left == nil && right == nil {
		return operator == "eq", nil
	}
	if left == nil || right == nil {
		return operator == "ne", nil
	}

	// Try to convert to numbers for numeric comparison
	if leftNum, leftOk := e.toFloat64(left); leftOk {
		if rightNum, rightOk := e.toFloat64(right); rightOk {
			switch operator {
			case "eq":
				return leftNum == rightNum, nil
			case "ne":
				return leftNum != rightNum, nil
			case "gt":
				return leftNum > rightNum, nil
			case "lt":
				return leftNum < rightNum, nil
			case "gte":
				return leftNum >= rightNum, nil
			case "lte":
				return leftNum <= rightNum, nil
			}
		}
	}

	// String comparison
	leftStr := fmt.Sprintf("%v", left)
	rightStr := fmt.Sprintf("%v", right)

	switch operator {
	case "eq":
		return leftStr == rightStr, nil
	case "ne":
		return leftStr != rightStr, nil
	case "gt":
		return leftStr > rightStr, nil
	case "lt":
		return leftStr < rightStr, nil
	case "gte":
		return leftStr >= rightStr, nil
	case "lte":
		return leftStr <= rightStr, nil
	default:
		return false, fmt.Errorf("unsupported comparison operator: %s", operator)
	}
}

// containsValue checks if left contains right
func (e *AdvancedConditionExecutor) containsValue(left, right interface{}) (bool, error) {
	leftStr := fmt.Sprintf("%v", left)
	rightStr := fmt.Sprintf("%v", right)
	return strings.Contains(leftStr, rightStr), nil
}

// toFloat64 attempts to convert a value to float64
func (e *AdvancedConditionExecutor) toFloat64(value interface{}) (float64, bool) {
	switch v := value.(type) {
	case float64:
		return v, true
	case float32:
		return float64(v), true
	case int:
		return float64(v), true
	case int64:
		return float64(v), true
	case int32:
		return float64(v), true
	case string:
		if f, err := strconv.ParseFloat(v, 64); err == nil {
			return f, true
		}
	}
	return 0, false
}
