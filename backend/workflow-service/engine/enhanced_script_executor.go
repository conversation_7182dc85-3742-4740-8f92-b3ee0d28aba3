package engine

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"go.uber.org/zap"
)

// EnhancedScriptExecutor executes scripts with enhanced capabilities
type EnhancedScriptExecutor struct {
	logger    *zap.Logger
	workDir   string
	timeout   time.Duration
	maxMemory int64 // Maximum memory in bytes
}

// NewEnhancedScriptExecutor creates a new enhanced script executor
func NewEnhancedScriptExecutor(logger *zap.Logger) *EnhancedScriptExecutor {
	return &EnhancedScriptExecutor{
		logger:    logger,
		workDir:   "/tmp/workflow-scripts",
		timeout:   5 * time.Minute,
		maxMemory: 512 * 1024 * 1024, // 512MB
	}
}

func (e *EnhancedScriptExecutor) GetType() string {
	return "enhanced_script"
}

func (e *EnhancedScriptExecutor) Execute(ctx context.Context, step *models.WorkflowStep, execCtx *ExecutionContext) (*StepResult, error) {
	script, ok := step.Config["script"].(string)
	if !ok {
		return &StepResult{Success: false, Error: fmt.Errorf("script not specified")}, nil
	}

	language, ok := step.Config["language"].(string)
	if !ok {
		language = "bash" // Default to bash
	}

	// Enhanced configuration options
	workingDir := e.getConfigString(step.Config, "workingDir", e.workDir)
	timeout := e.getConfigDuration(step.Config, "timeout", e.timeout)
	allowNetworking := e.getConfigBool(step.Config, "allowNetworking", false)
	allowFileSystem := e.getConfigBool(step.Config, "allowFileSystem", true)
	maxMemory := e.getConfigInt64(step.Config, "maxMemory", e.maxMemory)

	logs := []models.StepLog{
		{
			Timestamp: time.Now(),
			Level:     "info",
			Message:   fmt.Sprintf("Executing %s script with enhanced security", language),
			Source:    "system",
			Data: map[string]interface{}{
				"language":        language,
				"workingDir":      workingDir,
				"timeout":         timeout.String(),
				"allowNetworking": allowNetworking,
				"allowFileSystem": allowFileSystem,
				"maxMemory":       maxMemory,
			},
		},
	}

	// Create secure execution environment
	scriptFile, cleanup, err := e.createSecureScript(script, language, workingDir)
	if err != nil {
		return &StepResult{
			Success: false,
			Error:   fmt.Errorf("failed to create secure script: %w", err),
			Logs:    logs,
		}, nil
	}
	defer cleanup()

	// Create execution context with timeout
	execCtx_script, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// Build command based on language
	cmd, err := e.buildCommand(execCtx_script, language, scriptFile, allowNetworking, allowFileSystem)
	if err != nil {
		return &StepResult{
			Success: false,
			Error:   fmt.Errorf("failed to build command: %w", err),
			Logs:    logs,
		}, nil
	}

	// Set environment variables from execution context
	env := e.buildEnvironment(execCtx, step.Config)
	cmd.Env = env

	// Set working directory
	cmd.Dir = workingDir

	// Capture output
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	// Execute with resource monitoring
	startTime := time.Now()
	err = cmd.Run()
	duration := time.Since(startTime)

	// Parse output and create result
	output := map[string]interface{}{
		"stdout":     stdout.String(),
		"stderr":     stderr.String(),
		"exitCode":   0,
		"duration":   duration.Seconds(),
		"language":   language,
		"workingDir": workingDir,
	}

	success := true
	if err != nil {
		success = false
		if exitError, ok := err.(*exec.ExitError); ok {
			output["exitCode"] = exitError.ExitCode()
		}
		logs = append(logs, models.StepLog{
			Timestamp: time.Now(),
			Level:     "error",
			Message:   fmt.Sprintf("Script execution failed: %v", err),
			Source:    "system",
			Data: map[string]interface{}{
				"error":    err.Error(),
				"duration": duration.Seconds(),
			},
		})
	} else {
		logs = append(logs, models.StepLog{
			Timestamp: time.Now(),
			Level:     "info",
			Message:   "Script executed successfully",
			Source:    "system",
			Data: map[string]interface{}{
				"duration": duration.Seconds(),
			},
		})
	}

	// Try to parse JSON output if requested
	if parseJSON := e.getConfigBool(step.Config, "parseJSON", false); parseJSON {
		if jsonOutput, err := e.parseJSONOutput(stdout.String()); err == nil {
			output["parsedOutput"] = jsonOutput
		}
	}

	return &StepResult{
		Success: success,
		Output:  output,
		Error:   err,
		Logs:    logs,
	}, nil
}

// createSecureScript creates a script file in a secure location
func (e *EnhancedScriptExecutor) createSecureScript(script, language, workDir string) (string, func(), error) {
	// Ensure work directory exists
	if err := os.MkdirAll(workDir, 0755); err != nil {
		return "", nil, fmt.Errorf("failed to create work directory: %w", err)
	}

	// Create temporary script file
	var extension string
	switch language {
	case "bash":
		extension = ".sh"
	case "python":
		extension = ".py"
	case "javascript":
		extension = ".js"
	case "powershell":
		extension = ".ps1"
	default:
		extension = ".txt"
	}

	scriptFile := filepath.Join(workDir, fmt.Sprintf("script_%d%s", time.Now().UnixNano(), extension))

	if err := ioutil.WriteFile(scriptFile, []byte(script), 0755); err != nil {
		return "", nil, fmt.Errorf("failed to write script file: %w", err)
	}

	cleanup := func() {
		os.Remove(scriptFile)
	}

	return scriptFile, cleanup, nil
}

// buildCommand builds the execution command based on language and security settings
func (e *EnhancedScriptExecutor) buildCommand(ctx context.Context, language, scriptFile string, allowNetworking, allowFileSystem bool) (*exec.Cmd, error) {
	var cmd *exec.Cmd

	switch language {
	case "bash":
		if allowFileSystem {
			cmd = exec.CommandContext(ctx, "bash", scriptFile)
		} else {
			// Restricted bash execution
			cmd = exec.CommandContext(ctx, "bash", "-r", scriptFile)
		}
	case "python":
		args := []string{scriptFile}
		if !allowNetworking {
			// Add network restrictions for Python
			args = append([]string{"-c", "import sys; sys.modules['socket'] = None; sys.modules['urllib'] = None; exec(open('" + scriptFile + "').read())"}, args...)
		}
		cmd = exec.CommandContext(ctx, "python3", args...)
	case "javascript":
		cmd = exec.CommandContext(ctx, "node", scriptFile)
	case "powershell":
		cmd = exec.CommandContext(ctx, "powershell", "-ExecutionPolicy", "Bypass", "-File", scriptFile)
	default:
		return nil, fmt.Errorf("unsupported script language: %s", language)
	}

	return cmd, nil
}

// buildEnvironment builds the environment variables for script execution
func (e *EnhancedScriptExecutor) buildEnvironment(execCtx *ExecutionContext, config map[string]interface{}) []string {
	env := os.Environ() // Start with system environment

	// Add execution context variables
	execCtx.mu.RLock()
	for k, v := range execCtx.Variables {
		if str, ok := v.(string); ok {
			env = append(env, fmt.Sprintf("%s=%s", k, str))
		} else {
			// Convert non-string values to JSON
			if jsonBytes, err := json.Marshal(v); err == nil {
				env = append(env, fmt.Sprintf("%s=%s", k, string(jsonBytes)))
			}
		}
	}
	execCtx.mu.RUnlock()

	// Add custom environment variables from config
	if envVars, ok := config["environment"].(map[string]interface{}); ok {
		for k, v := range envVars {
			env = append(env, fmt.Sprintf("%s=%v", k, v))
		}
	}

	return env
}

// parseJSONOutput attempts to parse JSON from script output
func (e *EnhancedScriptExecutor) parseJSONOutput(output string) (interface{}, error) {
	output = strings.TrimSpace(output)
	if output == "" {
		return nil, fmt.Errorf("empty output")
	}

	var result interface{}
	if err := json.Unmarshal([]byte(output), &result); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}

	return result, nil
}

// Helper functions for configuration parsing
func (e *EnhancedScriptExecutor) getConfigString(config map[string]interface{}, key, defaultValue string) string {
	if value, ok := config[key].(string); ok {
		return value
	}
	return defaultValue
}

func (e *EnhancedScriptExecutor) getConfigBool(config map[string]interface{}, key string, defaultValue bool) bool {
	if value, ok := config[key].(bool); ok {
		return value
	}
	return defaultValue
}

func (e *EnhancedScriptExecutor) getConfigInt64(config map[string]interface{}, key string, defaultValue int64) int64 {
	if value, ok := config[key].(float64); ok {
		return int64(value)
	}
	if value, ok := config[key].(int64); ok {
		return value
	}
	return defaultValue
}

func (e *EnhancedScriptExecutor) getConfigDuration(config map[string]interface{}, key string, defaultValue time.Duration) time.Duration {
	if value, ok := config[key].(string); ok {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	if value, ok := config[key].(float64); ok {
		return time.Duration(value) * time.Second
	}
	return defaultValue
}
