package notifications

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"text/template"
	"time"

	"github.com/claudio/deploy-orchestrator/workflow-service/models"
	"github.com/claudio/deploy-orchestrator/workflow-service/storage"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// NotificationService handles workflow event notifications
type NotificationService struct {
	db              *storage.Database
	logger          *zap.Logger
	notificationURL string
	httpClient      *http.Client
	cooldownTracker map[string]time.Time // Track cooldown periods
}

// NewNotificationService creates a new notification service
func NewNotificationService(db *storage.Database, logger *zap.Logger, notificationURL string) *NotificationService {
	return &NotificationService{
		db:              db,
		logger:          logger,
		notificationURL: notificationURL,
		httpClient:      &http.Client{Timeout: 10 * time.Second},
		cooldownTracker: make(map[string]time.Time),
	}
}

// ProcessEvent processes workflow events and sends notifications based on rules
func (ns *NotificationService) ProcessEvent(ctx context.Context, event *models.ExecutionEvent) error {
	// Get notification rules for this event
	rules, err := ns.getApplicableRules(ctx, event)
	if err != nil {
		ns.logger.Error("Failed to get notification rules", zap.Error(err))
		return err
	}

	for _, rule := range rules {
		if err := ns.processRule(ctx, rule, event); err != nil {
			ns.logger.Error("Failed to process notification rule",
				zap.String("ruleId", rule.ID),
				zap.String("ruleName", rule.Name),
				zap.Error(err))
		}
	}

	return nil
}

// getApplicableRules finds notification rules that apply to the given event
func (ns *NotificationService) getApplicableRules(ctx context.Context, event *models.ExecutionEvent) ([]models.NotificationRule, error) {
	// Get all enabled rules for the project or global rules
	rules, err := ns.db.GetNotificationRules(ctx, event.ProjectID)
	if err != nil {
		return nil, err
	}

	var applicableRules []models.NotificationRule
	for _, rule := range rules {
		if ns.ruleApplies(rule, event) {
			applicableRules = append(applicableRules, rule)
		}
	}

	return applicableRules, nil
}

// ruleApplies checks if a notification rule applies to the given event
func (ns *NotificationService) ruleApplies(rule models.NotificationRule, event *models.ExecutionEvent) bool {
	// Check if rule is enabled
	if !rule.Enabled {
		return false
	}

	// Check cooldown
	if ns.isInCooldown(rule.ID) {
		return false
	}

	// Check event type
	eventTypeMatches := false
	for _, eventType := range rule.EventTypes {
		if eventType == event.EventType {
			eventTypeMatches = true
			break
		}
	}
	if !eventTypeMatches {
		return false
	}

	// Check conditions
	if len(rule.Conditions) > 0 {
		return ns.evaluateConditions(rule.Conditions, event)
	}

	return true
}

// isInCooldown checks if a rule is in cooldown period
func (ns *NotificationService) isInCooldown(ruleID string) bool {
	if lastSent, exists := ns.cooldownTracker[ruleID]; exists {
		// Check if cooldown period has passed (assuming cooldown is in minutes)
		// For now, using a default 5-minute cooldown
		return time.Since(lastSent) < 5*time.Minute
	}
	return false
}

// evaluateConditions evaluates rule conditions against the event
func (ns *NotificationService) evaluateConditions(conditions []models.TriggerCondition, event *models.ExecutionEvent) bool {
	for _, condition := range conditions {
		if !ns.evaluateCondition(condition, event) {
			return false
		}
	}
	return true
}

// evaluateCondition evaluates a single condition
func (ns *NotificationService) evaluateCondition(condition models.TriggerCondition, event *models.ExecutionEvent) bool {
	var fieldValue interface{}

	// Get field value from event
	switch condition.Field {
	case "eventType":
		fieldValue = event.EventType
	case "severity":
		fieldValue = event.Severity
	case "source":
		fieldValue = event.Source
	case "userId":
		fieldValue = event.UserID
	case "projectId":
		fieldValue = event.ProjectID
	default:
		// Check in event data
		if event.EventData != nil {
			fieldValue = event.EventData[condition.Field]
		}
	}

	// Evaluate condition
	switch condition.Operator {
	case "eq":
		return fieldValue == condition.Value
	case "ne":
		return fieldValue != condition.Value
	case "contains":
		if str, ok := fieldValue.(string); ok {
			if searchStr, ok := condition.Value.(string); ok {
				return strings.Contains(str, searchStr)
			}
		}
	case "regex":
		// TODO: Implement regex matching
		return false
	}

	return false
}

// processRule processes a notification rule and sends notifications
func (ns *NotificationService) processRule(ctx context.Context, rule models.NotificationRule, event *models.ExecutionEvent) error {
	// Generate notification content
	content, err := ns.generateNotificationContent(rule, event)
	if err != nil {
		return fmt.Errorf("failed to generate notification content: %w", err)
	}

	// Send notifications to all recipients
	for _, recipient := range rule.Recipients {
		if err := ns.sendNotification(ctx, recipient, content, event); err != nil {
			ns.logger.Error("Failed to send notification",
				zap.String("ruleId", rule.ID),
				zap.String("recipientType", recipient.Type),
				zap.String("recipientAddress", recipient.Address),
				zap.Error(err))
		}
	}

	// Update cooldown tracker
	ns.cooldownTracker[rule.ID] = time.Now()

	return nil
}

// generateNotificationContent generates notification content using templates
func (ns *NotificationService) generateNotificationContent(rule models.NotificationRule, event *models.ExecutionEvent) (string, error) {
	// Use template if provided, otherwise use default
	templateStr := rule.Template
	if templateStr == "" {
		templateStr = ns.getDefaultTemplate(event.EventType)
	}

	// Parse template
	tmpl, err := template.New("notification").Parse(templateStr)
	if err != nil {
		return "", fmt.Errorf("failed to parse template: %w", err)
	}

	// Prepare template data
	data := map[string]interface{}{
		"Event":       event,
		"EventType":   event.EventType,
		"Timestamp":   event.Timestamp.Format(time.RFC3339),
		"Severity":    event.Severity,
		"Source":      event.Source,
		"ExecutionID": event.ExecutionID,
		"StepID":      event.StepID,
		"EventData":   event.EventData,
	}

	// Execute template
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("failed to execute template: %w", err)
	}

	return buf.String(), nil
}

// getDefaultTemplate returns default notification templates
func (ns *NotificationService) getDefaultTemplate(eventType string) string {
	templates := map[string]string{
		"execution_started":   "Workflow execution started: {{.ExecutionID}} at {{.Timestamp}}",
		"execution_completed": "Workflow execution completed: {{.ExecutionID}} at {{.Timestamp}}",
		"execution_failed":    "Workflow execution failed: {{.ExecutionID}} at {{.Timestamp}}",
		"step_started":        "Step started: {{.StepID}} in execution {{.ExecutionID}} at {{.Timestamp}}",
		"step_completed":      "Step completed: {{.StepID}} in execution {{.ExecutionID}} at {{.Timestamp}}",
		"step_failed":         "Step failed: {{.StepID}} in execution {{.ExecutionID}} at {{.Timestamp}}",
	}

	if template, exists := templates[eventType]; exists {
		return template
	}

	return "Workflow event: {{.EventType}} for execution {{.ExecutionID}} at {{.Timestamp}}"
}

// sendNotification sends a notification to a specific recipient
func (ns *NotificationService) sendNotification(ctx context.Context, recipient models.NotificationRecipient, content string, event *models.ExecutionEvent) error {
	switch recipient.Type {
	case "email":
		return ns.sendEmailNotification(ctx, recipient, content, event)
	case "slack":
		return ns.sendSlackNotification(ctx, recipient, content, event)
	case "teams":
		return ns.sendTeamsNotification(ctx, recipient, content, event)
	case "webhook":
		return ns.sendWebhookNotification(ctx, recipient, content, event)
	default:
		return fmt.Errorf("unsupported notification type: %s", recipient.Type)
	}
}

// sendEmailNotification sends email notification via notification service
func (ns *NotificationService) sendEmailNotification(ctx context.Context, recipient models.NotificationRecipient, content string, event *models.ExecutionEvent) error {
	// Create notification payload for the notification service
	payload := map[string]interface{}{
		"id":        uuid.New().String(),
		"type":      "email",
		"message":   content,
		"recipient": recipient.Address,
		"status":    "pending",
	}

	return ns.sendNotificationPayload(ctx, payload)
}

// sendSlackNotification sends Slack notification
func (ns *NotificationService) sendSlackNotification(ctx context.Context, recipient models.NotificationRecipient, content string, event *models.ExecutionEvent) error {
	payload := map[string]interface{}{
		"id":        uuid.New().String(),
		"type":      "slack",
		"message":   content,
		"recipient": recipient.Address,
		"status":    "pending",
	}

	return ns.sendNotificationPayload(ctx, payload)
}

// sendTeamsNotification sends Teams notification
func (ns *NotificationService) sendTeamsNotification(ctx context.Context, recipient models.NotificationRecipient, content string, event *models.ExecutionEvent) error {
	payload := map[string]interface{}{
		"id":        uuid.New().String(),
		"type":      "teams",
		"message":   content,
		"recipient": recipient.Address,
		"status":    "pending",
	}

	return ns.sendNotificationPayload(ctx, payload)
}

// sendWebhookNotification sends webhook notification
func (ns *NotificationService) sendWebhookNotification(ctx context.Context, recipient models.NotificationRecipient, content string, event *models.ExecutionEvent) error {
	payload := map[string]interface{}{
		"event":   event,
		"message": content,
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal webhook payload: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", recipient.Address, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create webhook request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := ns.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send webhook: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return fmt.Errorf("webhook returned error status: %d", resp.StatusCode)
	}

	return nil
}

// sendNotificationPayload sends notification payload to the notification service
func (ns *NotificationService) sendNotificationPayload(ctx context.Context, payload map[string]interface{}) error {
	if ns.notificationURL == "" {
		ns.logger.Warn("Notification service URL not configured, skipping notification")
		return nil
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal notification: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", ns.notificationURL+"/api/v1/notifications", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create notification request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := ns.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send notification: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return fmt.Errorf("notification service returned error status: %d", resp.StatusCode)
	}

	ns.logger.Debug("Notification sent successfully",
		zap.String("notificationId", payload["id"].(string)),
		zap.String("type", payload["type"].(string)),
		zap.String("recipient", payload["recipient"].(string)))

	return nil
}
