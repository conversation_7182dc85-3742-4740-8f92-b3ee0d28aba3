# SSH Deployment Template - Usage Guide

## Overview
This template allows you to deploy applications to remote Linux servers via SSH. You need to provide your own SSH credentials and target host information.

## Required Secrets Setup

Before using this template, you must create the following secrets in your project:

### 1. SSH Username
```bash
POST /api/v1/secrets
{
  "name": "my_ssh_username",
  "description": "SSH username for my servers",
  "type": "username", 
  "value": "your-ssh-username",
  "scopeId": "your_scope_id"
}

# Bind to project
POST /api/v1/projects/your_project_id/variables
{
  "secretId": "your_ssh_username_secret_id",
  "name": "SSH_USERNAME",
  "type": "env"
}
```

### 2. SSH Private Key
```bash
POST /api/v1/secrets
{
  "name": "my_ssh_private_key",
  "description": "SSH private key for my servers",
  "type": "private_key",
  "value": "-----BEGIN OPENSSH PRIVATE KEY-----\nYOUR_PRIVATE_KEY_CONTENT\n-----END OPENSSH PRIVATE KEY-----",
  "scopeId": "your_scope_id"
}

# Bind to project
POST /api/v1/projects/your_project_id/variables
{
  "secretId": "your_ssh_private_key_secret_id",
  "name": "SSH_PRIVATE_KEY", 
  "type": "env"
}
```

## Required Parameters

When executing this template, provide these parameters:

- **TARGET_HOST**: Your server hostname or IP address
- **SSH_PORT**: SSH port (default: 22)
- **APPLICATION_NAME**: Name of your application
- **DEPLOYMENT_PATH**: Path where to deploy (default: /opt/myapp)

## Example Usage

```bash
POST /api/v1/workflows/ssh-deployment-template/execute
{
  "projectId": "your_project_id",
  "parameters": {
    "TARGET_HOST": "your-server.example.com",
    "SSH_PORT": 22,
    "APPLICATION_NAME": "my-web-app",
    "DEPLOYMENT_PATH": "/opt/my-web-app"
  },
  "startedBy": "your_user_id"
}
```

## Prerequisites

1. **SSH Access**: Ensure your SSH key has access to the target host
2. **Sudo Permissions**: The SSH user should have sudo access for service management
3. **Application Files**: Your application files should be available in the workflow context
4. **Service Configuration**: The target host should have systemd service configured for your application

## Security Notes

- Your SSH credentials are never shared with other users
- Secrets are automatically injected into the workflow steps
- SSH private keys are temporarily written to files and immediately cleaned up
- All secret access is logged for audit purposes

## Troubleshooting

### SSH Connection Failed
- Verify your SSH_USERNAME and SSH_PRIVATE_KEY secrets are correctly configured
- Check that the TARGET_HOST is accessible from the workflow execution environment
- Ensure the SSH_PORT is correct

### Permission Denied
- Verify your SSH user has the necessary permissions on the target host
- Check that your SSH private key matches the public key on the target host

### Service Start Failed
- Ensure your application's systemd service is properly configured
- Check that the DEPLOYMENT_PATH exists and is writable by your SSH user
