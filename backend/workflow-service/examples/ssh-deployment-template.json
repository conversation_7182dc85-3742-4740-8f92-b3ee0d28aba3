{"id": "ssh-deployment-template", "name": "SSH Deployment Template", "description": "Template for deploying applications via SSH to remote hosts", "isTemplate": true, "templateMetadata": {"category": "deployment", "tags": ["ssh", "deployment", "linux"], "author": "template-creator", "version": "1.0.0"}, "variables": {"TARGET_HOST": {"type": "string", "description": "Target host for deployment", "required": true, "default": "example.com"}, "SSH_PORT": {"type": "number", "description": "SSH port number", "required": false, "default": 22}, "SSH_USERNAME": {"type": "string", "description": "SSH username (will be provided by user's secrets)", "required": true, "secretHint": "SSH_USERNAME"}, "SSH_PRIVATE_KEY": {"type": "secret", "description": "SSH private key (will be provided by user's secrets)", "required": true, "secretHint": "SSH_PRIVATE_KEY"}, "DEPLOYMENT_PATH": {"type": "string", "description": "Path on remote host for deployment", "required": false, "default": "/opt/myapp"}, "APPLICATION_NAME": {"type": "string", "description": "Name of the application to deploy", "required": true}}, "steps": [{"id": "validate-connection", "name": "Validate SSH Connection", "type": "script", "config": {"language": "bash", "script": "#!/bin/bash\n\n# SSH credentials will be injected as environment variables\n# SSH_USERNAME and SSH_PRIVATE_KEY come from user's secrets\n\necho \"Testing SSH connection to $TARGET_HOST:$SSH_PORT\"\n\n# Create temporary SSH key file\necho \"$SSH_PRIVATE_KEY\" > /tmp/ssh_key\nchmod 600 /tmp/ssh_key\n\n# Test connection\nssh -i /tmp/ssh_key -p $SSH_PORT -o StrictHostKeyChecking=no $SSH_USERNAME@$TARGET_HOST 'echo \"SSH connection successful\"'\n\nif [ $? -eq 0 ]; then\n    echo \"✅ SSH connection to $TARGET_HOST successful\"\nelse\n    echo \"❌ SSH connection to $TARGET_HOST failed\"\n    exit 1\nfi\n\n# Cleanup\nrm -f /tmp/ssh_key"}, "dependencies": [], "onSuccess": ["prepare-deployment"], "onFailure": ["notify-failure"]}, {"id": "prepare-deployment", "name": "Prepare Deployment Directory", "type": "script", "config": {"language": "bash", "script": "#!/bin/bash\n\necho \"Preparing deployment directory on $TARGET_HOST\"\n\n# Create SSH key file\necho \"$SSH_PRIVATE_KEY\" > /tmp/ssh_key\nchmod 600 /tmp/ssh_key\n\n# Create deployment directory\nssh -i /tmp/ssh_key -p $SSH_PORT -o StrictHostKeyChecking=no $SSH_USERNAME@$TARGET_HOST \\\n    \"sudo mkdir -p $DEPLOYMENT_PATH && sudo chown $SSH_USERNAME:$SSH_USERNAME $DEPLOYMENT_PATH\"\n\necho \"✅ Deployment directory prepared: $DEPLOYMENT_PATH\"\n\n# Cleanup\nrm -f /tmp/ssh_key"}, "dependencies": ["validate-connection"], "onSuccess": ["deploy-application"], "onFailure": ["notify-failure"]}, {"id": "deploy-application", "name": "Deploy Application", "type": "script", "config": {"language": "bash", "script": "#!/bin/bash\n\necho \"Deploying $APPLICATION_NAME to $TARGET_HOST:$DEPLOYMENT_PATH\"\n\n# Create SSH key file\necho \"$SSH_PRIVATE_KEY\" > /tmp/ssh_key\nchmod 600 /tmp/ssh_key\n\n# Copy application files (assuming they're in current directory)\nscp -i /tmp/ssh_key -P $SSH_PORT -r ./* $SSH_USERNAME@$TARGET_HOST:$DEPLOYMENT_PATH/\n\n# Set permissions and restart service\nssh -i /tmp/ssh_key -p $SSH_PORT -o StrictHostKeyChecking=no $SSH_USERNAME@$TARGET_HOST \\\n    \"cd $DEPLOYMENT_PATH && chmod +x start.sh && sudo systemctl restart $APPLICATION_NAME\"\n\necho \"✅ Application $APPLICATION_NAME deployed successfully\"\n\n# Cleanup\nrm -f /tmp/ssh_key"}, "dependencies": ["prepare-deployment"], "onSuccess": ["verify-deployment"], "onFailure": ["notify-failure"]}, {"id": "verify-deployment", "name": "Verify Deployment", "type": "script", "config": {"language": "bash", "script": "#!/bin/bash\n\necho \"Verifying deployment of $APPLICATION_NAME on $TARGET_HOST\"\n\n# Create SSH key file\necho \"$SSH_PRIVATE_KEY\" > /tmp/ssh_key\nchmod 600 /tmp/ssh_key\n\n# Check if service is running\nssh -i /tmp/ssh_key -p $SSH_PORT -o StrictHostKeyChecking=no $SSH_USERNAME@$TARGET_HOST \\\n    \"sudo systemctl is-active $APPLICATION_NAME\"\n\nif [ $? -eq 0 ]; then\n    echo \"✅ $APPLICATION_NAME is running successfully on $TARGET_HOST\"\nelse\n    echo \"❌ $APPLICATION_NAME is not running on $TARGET_HOST\"\n    exit 1\nfi\n\n# Cleanup\nrm -f /tmp/ssh_key"}, "dependencies": ["deploy-application"], "onSuccess": ["notify-success"], "onFailure": ["notify-failure"]}, {"id": "notify-success", "name": "Notify Success", "type": "script", "config": {"language": "bash", "script": "echo \"🎉 Deployment of $APPLICATION_NAME to $TARGET_HOST completed successfully!\""}, "dependencies": ["verify-deployment"]}, {"id": "notify-failure", "name": "Notify Failure", "type": "script", "config": {"language": "bash", "script": "echo \"💥 Deployment of $APPLICATION_NAME to $TARGET_HOST failed. Check logs for details.\""}, "dependencies": []}]}