# Secret Mapping Example: SSH Deployment Template

This example demonstrates how users can map their existing secrets to template variables when executing workflow templates.

## Scenario

- **Template Creator**: Creates an SSH deployment template expecting `SSH_USERNAME` and `SSH_PRIVATE_KEY`
- **User A**: Has secrets named `DEV_USERNAME`, `DEV_KEY`, `QA_USERNAME`, `QA_KEY`
- **User B**: Has secrets named `PROD_SSH_USER`, `PROD_SSH_KEY`

Both users want to use the same template with their own secrets.

## Template Definition

The template creator builds a template with standard variable names:

```json
{
  "id": "ssh-deployment-template",
  "name": "SSH Deployment Template",
  "variables": {
    "TARGET_HOST": {
      "type": "string",
      "description": "Target host for deployment",
      "required": true
    },
    "SSH_USERNAME": {
      "type": "string",
      "description": "SSH username (will be mapped from user's secrets)",
      "required": true,
      "secretHint": "SSH_USERNAME"
    },
    "SSH_PRIVATE_KEY": {
      "type": "secret",
      "description": "SSH private key (will be mapped from user's secrets)",
      "required": true,
      "secretHint": "SSH_PRIVATE_KEY"
    }
  },
  "steps": [
    {
      "id": "deploy-step",
      "name": "Deploy via SSH",
      "type": "script",
      "config": {
        "language": "bash",
        "script": "echo \"$SSH_PRIVATE_KEY\" > /tmp/key\nchmod 600 /tmp/key\nssh -i /tmp/key $SSH_USERNAME@$TARGET_HOST 'echo Deployment successful'"
      }
    }
  ]
}
```

## Project A's Secret Setup

Project A has development and QA secrets (User A has access to Project A):

```bash
# Project A's secrets (NOT user-specific)
POST /api/v1/secrets
{
  "name": "DEV_USERNAME",
  "value": "dev-user",
  "scopeId": "project_a_scope"  # PROJECT scope, not user scope
}

POST /api/v1/secrets
{
  "name": "DEV_KEY",
  "value": "-----BEGIN OPENSSH PRIVATE KEY-----\ndev_private_key_content\n-----END OPENSSH PRIVATE KEY-----",
  "scopeId": "project_a_scope"  # PROJECT scope
}

POST /api/v1/secrets
{
  "name": "QA_USERNAME",
  "value": "qa-user",
  "scopeId": "project_a_scope"  # PROJECT scope
}

POST /api/v1/secrets
{
  "name": "QA_KEY",
  "value": "-----BEGIN OPENSSH PRIVATE KEY-----\nqa_private_key_content\n-----END OPENSSH PRIVATE KEY-----",
  "scopeId": "project_a_scope"  # PROJECT scope
}
```

**Bind secrets to project:**
```bash
# Bind DEV secrets to Project A
POST /api/v1/projects/project_a/variables
{
  "secretId": "sec_dev_username",
  "name": "DEV_USERNAME",
  "type": "env",
  "projectId": "project_a"
}

POST /api/v1/projects/project_a/variables
{
  "secretId": "sec_dev_key",
  "name": "DEV_KEY",
  "type": "env",
  "projectId": "project_a"
}

# Bind QA secrets to Project A
POST /api/v1/projects/project_a/variables
{
  "secretId": "sec_qa_username",
  "name": "QA_USERNAME",
  "type": "env",
  "projectId": "project_a"
}

POST /api/v1/projects/project_a/variables
{
  "secretId": "sec_qa_key",
  "name": "QA_KEY",
  "type": "env",
  "projectId": "project_a"
}
```

**User A's Project Access:**
```bash
# User A has permission to access Project A
# Therefore User A can access ALL secrets bound to Project A:
# - DEV_USERNAME, DEV_KEY, QA_USERNAME, QA_KEY
```

## User A's Template Execution with Secret Mapping

### Deploy to Development Environment

```bash
POST /api/v1/workflows/ssh-deployment-template/execute
{
  "projectId": "project_a",
  "parameters": {
    "TARGET_HOST": "dev.project-a.com"
  },
  "secretMapping": {
    "SSH_USERNAME": "DEV_USERNAME",
    "SSH_PRIVATE_KEY": "DEV_KEY"
  },
  "startedBy": "user_a"
}
```

**What happens:**
1. Template expects `SSH_USERNAME` and `SSH_PRIVATE_KEY`
2. Secret mapping maps:
   - `SSH_USERNAME` → Project A's `DEV_USERNAME` secret
   - `SSH_PRIVATE_KEY` → Project A's `DEV_KEY` secret
3. Workflow executes with Project A's dev credentials
4. User A can access these secrets because they have permission to Project A

### Deploy to QA Environment

```bash
POST /api/v1/workflows/ssh-deployment-template/execute
{
  "projectId": "user_a_project",
  "parameters": {
    "TARGET_HOST": "qa.user-a.com"
  },
  "secretMapping": {
    "SSH_USERNAME": "QA_USERNAME",
    "SSH_PRIVATE_KEY": "QA_KEY"
  },
  "startedBy": "user_a"
}
```

**What happens:**
1. Same template, different secret mapping
2. Secret mapping maps:
   - `SSH_USERNAME` → User A's `QA_USERNAME` secret
   - `SSH_PRIVATE_KEY` → User A's `QA_KEY` secret
3. Workflow executes with User A's QA credentials

## User B's Secret Setup

User B has production secrets with different names:

```bash
# User B's existing secrets
POST /api/v1/secrets
{
  "name": "PROD_SSH_USER",
  "value": "ubuntu",
  "scopeId": "user_b_scope"
}

POST /api/v1/secrets
{
  "name": "PROD_SSH_KEY",
  "value": "-----BEGIN OPENSSH PRIVATE KEY-----\nprod_private_key_content\n-----END OPENSSH PRIVATE KEY-----",
  "scopeId": "user_b_scope"
}
```

**Bind secrets to project:**
```bash
POST /api/v1/projects/user_b_project/variables
{
  "secretId": "sec_prod_user",
  "name": "PROD_SSH_USER",
  "type": "env"
}

POST /api/v1/projects/user_b_project/variables
{
  "secretId": "sec_prod_key",
  "name": "PROD_SSH_KEY",
  "type": "env"
}
```

## User B's Template Execution

```bash
POST /api/v1/workflows/ssh-deployment-template/execute
{
  "projectId": "user_b_project",
  "parameters": {
    "TARGET_HOST": "prod.user-b.com"
  },
  "secretMapping": {
    "SSH_USERNAME": "PROD_SSH_USER",
    "SSH_PRIVATE_KEY": "PROD_SSH_KEY"
  },
  "startedBy": "user_b"
}
```

**What happens:**
1. Template expects `SSH_USERNAME` and `SSH_PRIVATE_KEY`
2. Secret mapping maps:
   - `SSH_USERNAME` → User B's `PROD_SSH_USER` secret
   - `SSH_PRIVATE_KEY` → User B's `PROD_SSH_KEY` secret
3. Workflow executes with User B's production credentials

## Backend Processing Flow

### 1. Workflow Execution Request
```go
// User's execution request includes secret mapping
execution := &models.WorkflowExecution{
    ProjectID: "user_a_project",
    Parameters: map[string]interface{}{
        "TARGET_HOST": "dev.user-a.com",
    },
    SecretMapping: map[string]string{
        "SSH_USERNAME": "DEV_USERNAME",
        "SSH_PRIVATE_KEY": "DEV_KEY",
    },
}
```

### 2. Secret Retrieval with Mapping
```go
// Workflow engine calls secrets service with mapping
secretsReq := clients.WorkflowSecretsRequest{
    WorkflowID:    "workflow_123",
    ExecutionID:   "exec_456",
    ProjectID:     "user_a_project",
    SecretMapping: map[string]string{
        "SSH_USERNAME": "DEV_USERNAME",
        "SSH_PRIVATE_KEY": "DEV_KEY",
    },
}
```

### 3. Secrets Service Processing
```go
// Secrets service filters by user's secret names
userSecretNames := []string{"DEV_USERNAME", "DEV_KEY"}
query = query.Joins("JOIN secrets ON secret_variables.secret_id = secrets.id").
    Where("secrets.name IN ?", userSecretNames)

// Maps secret names back to template variable names in response
for templateVar, userSecretName := range req.SecretMapping {
    if variable.Secret.Name == userSecretName {
        variableName = templateVar // "SSH_USERNAME" instead of "DEV_USERNAME"
        break
    }
}
```

### 4. Secret Injection
```go
// Workflow engine injects secrets with template variable names
step.Config["env"]["SSH_USERNAME"] = "dev-user"
step.Config["env"]["SSH_PRIVATE_KEY"] = "-----BEGIN OPENSSH PRIVATE KEY-----..."
```

## Benefits of Secret Mapping

### ✅ **Template Reusability**
- One template works for all users
- No need to modify templates for different secret names

### ✅ **User Flexibility**
- Users keep their existing secret naming conventions
- No need to rename or recreate secrets

### ✅ **Environment Support**
- Same user can map different secrets for different environments
- Easy switching between dev/qa/prod credentials

### ✅ **Security Isolation**
- Each user's secrets remain isolated to their project
- No cross-user secret access

### ✅ **Backward Compatibility**
- Works with existing secret binding approach
- Secret mapping is optional - falls back to direct name matching

## UI/UX Implementation

### Template Execution Form
```typescript
interface SecretMappingConfig {
  templateVariable: string;
  userSecrets: string[];
  selectedSecret?: string;
}

// UI shows mapping form for each template secret variable
const secretMappings: SecretMappingConfig[] = [
  {
    templateVariable: "SSH_USERNAME",
    userSecrets: ["DEV_USERNAME", "QA_USERNAME", "PROD_SSH_USER"],
    selectedSecret: "DEV_USERNAME"
  },
  {
    templateVariable: "SSH_PRIVATE_KEY",
    userSecrets: ["DEV_KEY", "QA_KEY", "PROD_SSH_KEY"],
    selectedSecret: "DEV_KEY"
  }
];
```

This enhanced secret mapping system provides **maximum flexibility** while maintaining **strong security** and **template reusability**! 🎉
