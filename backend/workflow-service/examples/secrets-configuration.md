# Secrets Configuration for Web Application Deployment Workflow

This document shows the secrets that need to be configured for the `workflow-with-secrets.json` example to work properly.

## Required Secrets

### 1. Docker Registry Credentials

**Environment Variables for Build Step:**

```bash
# Create Docker registry username secret
POST /api/v1/secrets
{
  "name": "docker_registry_username",
  "description": "Docker registry username for image builds",
  "type": "username",
  "value": "deploy-user",
  "scopeId": "scope_production"
}

# Create Docker registry password secret
POST /api/v1/secrets
{
  "name": "docker_registry_password", 
  "description": "Docker registry password for image builds",
  "type": "password",
  "value": "super-secret-registry-password",
  "scopeId": "scope_production"
}

# Create Docker registry URL secret
POST /api/v1/secrets
{
  "name": "docker_registry_url",
  "description": "Docker registry URL",
  "type": "url", 
  "value": "registry.example.com",
  "scopeId": "scope_production"
}
```

**Bind to Project as Environment Variables:**

```bash
# Bind Docker username
POST /api/v1/projects/proj_web_app/variables
{
  "secretId": "sec_docker_username",
  "name": "DOCKER_USERNAME",
  "type": "env",
  "environment": "",
  "service": "web-service"
}

# Bind Docker password  
POST /api/v1/projects/proj_web_app/variables
{
  "secretId": "sec_docker_password",
  "name": "DOCKER_PASSWORD", 
  "type": "env",
  "environment": "",
  "service": "web-service"
}

# Bind Docker registry
POST /api/v1/projects/proj_web_app/variables
{
  "secretId": "sec_docker_registry",
  "name": "DOCKER_REGISTRY",
  "type": "env", 
  "environment": "",
  "service": "web-service"
}
```

### 2. Database Credentials

**Environment Variables for Test Step:**

```bash
# Create database credentials
POST /api/v1/secrets
{
  "name": "database_credentials",
  "description": "Database connection credentials",
  "type": "database",
  "value": "postgresql://testuser:<EMAIL>:5432/testdb",
  "scopeId": "scope_production"
}
```

**Bind as Individual Environment Variables:**

```bash
# Database username
POST /api/v1/projects/proj_web_app/variables
{
  "secretId": "sec_db_creds",
  "name": "DB_USERNAME",
  "type": "env",
  "environment": "",
  "service": "web-service",
  "transform": {
    "extract": "username"
  }
}

# Database password
POST /api/v1/projects/proj_web_app/variables  
{
  "secretId": "sec_db_creds",
  "name": "DB_PASSWORD",
  "type": "env",
  "environment": "",
  "service": "web-service",
  "transform": {
    "extract": "password"
  }
}

# Database host
POST /api/v1/projects/proj_web_app/variables
{
  "secretId": "sec_db_creds", 
  "name": "DB_HOST",
  "type": "env",
  "environment": "",
  "service": "web-service",
  "transform": {
    "extract": "host"
  }
}

# Database port
POST /api/v1/projects/proj_web_app/variables
{
  "secretId": "sec_db_creds",
  "name": "DB_PORT", 
  "type": "env",
  "environment": "",
  "service": "web-service",
  "transform": {
    "extract": "port"
  }
}

# Database name
POST /api/v1/projects/proj_web_app/variables
{
  "secretId": "sec_db_creds",
  "name": "DB_NAME",
  "type": "env", 
  "environment": "",
  "service": "web-service",
  "transform": {
    "extract": "database"
  }
}
```

### 3. Configuration Files

**Database Configuration File:**

```bash
# Create database config secret
POST /api/v1/secrets
{
  "name": "database_config",
  "description": "Database configuration file",
  "type": "config",
  "value": "{\"host\": \"db.example.com\", \"port\": 5432, \"database\": \"webapp\", \"username\": \"webapp_user\", \"password\": \"webapp_secret\", \"ssl\": true}",
  "scopeId": "scope_production"
}

# Bind as configuration file
POST /api/v1/projects/proj_web_app/variables
{
  "secretId": "sec_db_config",
  "name": "database_config",
  "type": "file",
  "path": "/app/config/database.json",
  "format": "json",
  "environment": "production",
  "service": "web-service"
}
```

**Redis Configuration File:**

```bash
# Create Redis config secret
POST /api/v1/secrets
{
  "name": "redis_config",
  "description": "Redis configuration",
  "type": "config", 
  "value": "host: redis.example.com\nport: 6379\npassword: redis-secret-password\nssl: true\ndb: 0",
  "scopeId": "scope_production"
}

# Bind as configuration file
POST /api/v1/projects/proj_web_app/variables
{
  "secretId": "sec_redis_config",
  "name": "redis_config",
  "type": "file",
  "path": "/app/config/redis.yaml",
  "format": "yaml",
  "environment": "production", 
  "service": "web-service"
}
```

### 4. API Tokens

**Staging API Token:**

```bash
# Create staging API token
POST /api/v1/secrets
{
  "name": "staging_api_token",
  "description": "API token for staging environment",
  "type": "token",
  "value": "staging-jwt-token-here",
  "scopeId": "scope_staging"
}

# Bind as environment variable
POST /api/v1/projects/proj_web_app/variables
{
  "secretId": "sec_staging_token",
  "name": "STAGING_API_TOKEN",
  "type": "env",
  "environment": "staging",
  "service": "web-service"
}
```

**Monitoring API Token:**

```bash
# Create monitoring API token
POST /api/v1/secrets
{
  "name": "monitoring_api_token",
  "description": "API token for monitoring service",
  "type": "token",
  "value": "monitoring-api-token-here",
  "scopeId": "scope_production"
}

# Bind as environment variable
POST /api/v1/projects/proj_web_app/variables
{
  "secretId": "sec_monitoring_token",
  "name": "MONITORING_API_TOKEN", 
  "type": "env",
  "environment": "production",
  "service": "web-service"
}
```

### 5. SSL Certificates

**SSL Certificate Mount:**

```bash
# Create SSL certificate secret
POST /api/v1/secrets
{
  "name": "ssl_certificates",
  "description": "SSL certificates for production",
  "type": "certificate",
  "value": "-----BEGIN CERTIFICATE-----\nMIIC...\n-----END CERTIFICATE-----",
  "scopeId": "scope_production"
}

# Bind as volume mount
POST /api/v1/projects/proj_web_app/variables
{
  "secretId": "sec_ssl_certs",
  "name": "ssl_certificates",
  "type": "mount",
  "path": "/etc/ssl/certs",
  "environment": "production",
  "service": "web-service"
}
```

### 6. Notification Tokens

**Slack Webhook Token:**

```bash
# Create Slack webhook token
POST /api/v1/secrets
{
  "name": "slack_webhook_token",
  "description": "Slack webhook token for notifications",
  "type": "token", 
  "value": "xoxb-slack-webhook-token",
  "scopeId": "scope_production"
}

# Bind as environment variable
POST /api/v1/projects/proj_web_app/variables
{
  "secretId": "sec_slack_token",
  "name": "SLACK_WEBHOOK_TOKEN",
  "type": "env",
  "environment": "",
  "service": "web-service"
}
```

## Secret Scoping Strategy

### Environment-Specific Secrets
- **Staging secrets** are scoped to `environment: "staging"`
- **Production secrets** are scoped to `environment: "production"`  
- **Global secrets** (like Docker registry) have empty environment scope

### Service-Specific Secrets
- All secrets are scoped to `service: "web-service"`
- This prevents other services from accessing these credentials

### Step-Specific Secrets
- Some secrets can be further scoped to specific step names
- Useful for highly sensitive operations

## Security Best Practices

1. **Principle of Least Privilege**: Each secret is scoped to the minimum required context
2. **Environment Isolation**: Staging and production secrets are completely separate
3. **Service Isolation**: Secrets are scoped to specific services
4. **Audit Trail**: All secret access is logged for security monitoring
5. **Rotation**: Secrets should be rotated regularly using the rotation features

## Testing the Configuration

After setting up all secrets, test the workflow:

```bash
# Execute the workflow
POST /api/v1/workflows/web-app-deployment/execute
{
  "parameters": {
    "BUILD_VERSION": "1.2.3",
    "ENVIRONMENT": "production"
  },
  "startedBy": "user123"
}
```

Monitor the execution logs to verify that secrets are being injected correctly into each step.
