# Workflow Service Examples

This directory contains practical examples for using the Workflow Service in various scenarios.

## Example <PERSON><PERSON>rios

### 1. Environment-based Routing

#### Development Workflow
```json
{
  "workflowId": "dev-build-test",
  "parameters": {
    "branch": "feature/new-feature",
    "runTests": true
  },
  "requirements": {
    "labels": {
      "environment": "development"
    },
    "capabilities": ["docker", "script-execution"]
  }
}
```

#### Production Deployment
```json
{
  "workflowId": "prod-deployment",
  "parameters": {
    "version": "v2.1.0",
    "environment": "production"
  },
  "requirements": {
    "labels": {
      "environment": "production",
      "region": "us-east-1"
    },
    "capabilities": ["kubernetes", "docker"],
    "minMemoryMB": 2048
  }
}
```

### 2. Resource-specific Workflows

#### High-Memory Data Processing
```json
{
  "workflowId": "data-processing-large",
  "parameters": {
    "dataset": "customer-analytics-2024",
    "outputFormat": "parquet"
  },
  "requirements": {
    "labels": {
      "type": "highmem"
    },
    "capabilities": ["high-memory", "python"],
    "minMemoryMB": 8192,
    "minCPUCores": 4
  }
}
```

#### GPU-accelerated ML Training
```json
{
  "workflowId": "ml-model-training",
  "parameters": {
    "model": "transformer",
    "epochs": 100,
    "batchSize": 32
  },
  "requirements": {
    "labels": {
      "type": "gpu",
      "gpu-type": "nvidia-v100"
    },
    "capabilities": ["gpu", "python", "cuda"],
    "minMemoryMB": 16384
  }
}
```

### 3. Multi-region Deployment

#### Global Service Deployment
```bash
#!/bin/bash

# Deploy to US East
curl -X POST http://localhost:8080/api/v1/instances/execute \
  -H "Content-Type: application/json" \
  -d '{
    "workflowId": "service-deployment",
    "parameters": {
      "region": "us-east-1",
      "service": "api-gateway"
    },
    "requirements": {
      "labels": {
        "region": "us-east-1",
        "environment": "production"
      }
    }
  }'

# Deploy to EU West
curl -X POST http://localhost:8080/api/v1/instances/execute \
  -H "Content-Type: application/json" \
  -d '{
    "workflowId": "service-deployment", 
    "parameters": {
      "region": "eu-west-1",
      "service": "api-gateway"
    },
    "requirements": {
      "labels": {
        "region": "eu-west-1",
        "environment": "production"
      }
    }
  }'
```

### 4. Advanced Workflow Definitions

#### CI/CD Pipeline with Conditional Logic
```json
{
  "id": "cicd-pipeline",
  "name": "Complete CI/CD Pipeline",
  "description": "Build, test, and deploy application",
  "steps": [
    {
      "id": "checkout",
      "name": "Checkout Code",
      "type": "script",
      "config": {
        "language": "bash",
        "script": "git clone $REPO_URL && cd $PROJECT_NAME"
      }
    },
    {
      "id": "build",
      "name": "Build Application",
      "type": "script",
      "dependencies": ["checkout"],
      "config": {
        "language": "bash",
        "script": "docker build -t $IMAGE_NAME:$BUILD_NUMBER ."
      }
    },
    {
      "id": "test",
      "name": "Run Tests",
      "type": "enhanced_script",
      "dependencies": ["build"],
      "config": {
        "language": "bash",
        "script": "docker run --rm $IMAGE_NAME:$BUILD_NUMBER npm test",
        "parseJSON": true,
        "timeout": "10m"
      }
    },
    {
      "id": "security-scan",
      "name": "Security Scan",
      "type": "script",
      "dependencies": ["build"],
      "config": {
        "language": "bash",
        "script": "trivy image $IMAGE_NAME:$BUILD_NUMBER"
      }
    },
    {
      "id": "deploy-staging",
      "name": "Deploy to Staging",
      "type": "deployment",
      "dependencies": ["test", "security-scan"],
      "advancedConditions": [
        {
          "type": "logical",
          "logicalOp": "AND",
          "conditions": [
            {
              "type": "comparison",
              "left": "${test.output.success}",
              "operator": "==",
              "right": true
            },
            {
              "type": "expression",
              "expression": "${BRANCH} == 'main'"
            }
          ]
        }
      ],
      "config": {
        "deploymentId": "staging-deployment",
        "environment": "staging"
      }
    },
    {
      "id": "integration-tests",
      "name": "Integration Tests",
      "type": "script",
      "dependencies": ["deploy-staging"],
      "config": {
        "language": "bash",
        "script": "npm run test:integration -- --env=staging"
      }
    },
    {
      "id": "deploy-production",
      "name": "Deploy to Production",
      "type": "deployment",
      "dependencies": ["integration-tests"],
      "advancedConditions": [
        {
          "type": "script",
          "language": "bash",
          "script": "[ \"$MANUAL_APPROVAL\" = \"true\" ]"
        }
      ],
      "config": {
        "deploymentId": "production-deployment",
        "environment": "production"
      }
    }
  ],
  "parameters": {
    "REPO_URL": "https://github.com/company/app.git",
    "PROJECT_NAME": "my-app",
    "IMAGE_NAME": "company/my-app",
    "BUILD_NUMBER": "{{.BuildNumber}}",
    "BRANCH": "{{.Branch}}",
    "MANUAL_APPROVAL": "false"
  }
}
```

#### Data Pipeline with Error Handling
```json
{
  "id": "data-pipeline",
  "name": "ETL Data Pipeline",
  "description": "Extract, transform, and load data",
  "steps": [
    {
      "id": "extract",
      "name": "Extract Data",
      "type": "enhanced_script",
      "config": {
        "language": "python",
        "script": "import pandas as pd\ndata = pd.read_sql(query, connection)\nprint(json.dumps({'rows': len(data)}))",
        "parseJSON": true,
        "maxMemory": 2048,
        "timeout": "30m"
      }
    },
    {
      "id": "validate",
      "name": "Validate Data",
      "type": "advanced_condition",
      "dependencies": ["extract"],
      "config": {
        "conditions": [
          {
            "type": "comparison",
            "left": "${extract.output.rows}",
            "operator": ">",
            "right": 0
          }
        ]
      }
    },
    {
      "id": "transform",
      "name": "Transform Data",
      "type": "enhanced_script",
      "dependencies": ["validate"],
      "config": {
        "language": "python",
        "script": "# Data transformation logic here",
        "allowFileSystem": true,
        "workingDir": "/tmp/data-pipeline"
      }
    },
    {
      "id": "load",
      "name": "Load Data",
      "type": "enhanced_script",
      "dependencies": ["transform"],
      "config": {
        "language": "python",
        "script": "# Load transformed data to destination"
      }
    },
    {
      "id": "cleanup",
      "name": "Cleanup Temporary Files",
      "type": "script",
      "dependencies": ["load"],
      "config": {
        "language": "bash",
        "script": "rm -rf /tmp/data-pipeline/*"
      }
    }
  ],
  "onFailure": [
    {
      "id": "notify-failure",
      "name": "Notify on Failure",
      "type": "script",
      "config": {
        "language": "bash",
        "script": "curl -X POST $SLACK_WEBHOOK -d '{\"text\": \"Data pipeline failed\"}'"
      }
    }
  ]
}
```

### 5. Instance Management Scripts

#### Health Check Script
```bash
#!/bin/bash
# health-check.sh

INSTANCES=$(curl -s http://localhost:8080/api/v1/instances | jq -r '.instances[].id')

echo "Checking instance health..."
for instance in $INSTANCES; do
  health=$(curl -s http://localhost:8080/api/v1/instances/$instance | jq -r '.status')
  echo "Instance $instance: $health"
done
```

#### Load Balancing Test
```bash
#!/bin/bash
# load-test.sh

echo "Testing load balancing across instances..."

for i in {1..10}; do
  response=$(curl -s -X POST http://localhost:8080/api/v1/instances/execute \
    -H "Content-Type: application/json" \
    -d '{
      "workflowId": "test-workflow",
      "parameters": {"iteration": '$i'}
    }')
  
  instance=$(echo $response | jq -r '.instanceId')
  execution=$(echo $response | jq -r '.executionId')
  
  echo "Request $i: Instance $instance, Execution $execution"
done
```

#### Instance Configuration Update
```bash
#!/bin/bash
# update-instance-config.sh

INSTANCE_ID=$1
ENVIRONMENT=$2

if [ -z "$INSTANCE_ID" ] || [ -z "$ENVIRONMENT" ]; then
  echo "Usage: $0 <instance-id> <environment>"
  exit 1
fi

# Update instance labels
curl -X PUT http://localhost:8080/api/v1/instances/$INSTANCE_ID/labels \
  -H "Content-Type: application/json" \
  -d '{
    "labels": {
      "environment": "'$ENVIRONMENT'",
      "updated": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"
    }
  }'

echo "Updated instance $INSTANCE_ID to environment $ENVIRONMENT"
```

### 6. Monitoring Examples

#### Prometheus Metrics Collection
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'workflow-service'
    static_configs:
      - targets: ['workflow-service-1:8085', 'workflow-service-2:8085']
    metrics_path: '/metrics'
    scrape_interval: 10s
```

#### Grafana Dashboard Query Examples
```promql
# Instance CPU Usage
rate(workflow_instance_cpu_usage[5m])

# Execution Success Rate
rate(workflow_executions_total{status="completed"}[5m]) / rate(workflow_executions_total[5m])

# Average Execution Duration
rate(workflow_execution_duration_seconds_sum[5m]) / rate(workflow_execution_duration_seconds_count[5m])

# Instance Load Distribution
count by (instance_id) (workflow_executions_total)
```

### 7. Kubernetes Deployment

#### Kubernetes Manifests
```yaml
# workflow-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: workflow-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: workflow-service
  template:
    metadata:
      labels:
        app: workflow-service
    spec:
      containers:
      - name: workflow-service
        image: workflow-service:latest
        ports:
        - containerPort: 8085
        env:
        - name: INSTANCE_LABELS
          value: "environment=production,region=us-east-1,platform=kubernetes"
        - name: INSTANCE_CAPABILITIES
          value: "workflow-execution,script-execution,kubernetes"
        - name: POSTGRES_URL
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8085
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8085
          initialDelaySeconds: 5
          periodSeconds: 5
```

These examples demonstrate the flexibility and power of the distributed workflow service. Use them as starting points for your own implementations!
