{"id": "web-app-deployment", "name": "Web Application Deployment with Secrets", "description": "Complete deployment workflow demonstrating secret usage", "projectId": "proj_web_app", "version": "1.0.0", "variables": {"environment": "production", "service_name": "web-service", "deployment_target": "kubernetes"}, "steps": [{"id": "validate-environment", "name": "Validate Environment", "type": "script", "config": {"language": "bash", "script": "echo 'Validating environment: $ENVIRONMENT'\necho 'Service: $SERVICE_NAME'\necho 'Target: $DEPLOYMENT_TARGET'"}, "conditions": [], "dependencies": [], "onSuccess": ["build-application"], "onFailure": ["notify-failure"]}, {"id": "build-application", "name": "Build Application", "type": "script", "config": {"language": "bash", "script": "echo 'Building application...'\n# Docker registry credentials injected as env vars\ndocker login $DOCKER_REGISTRY -u $DOCKER_USERNAME -p $DOCKER_PASSWORD\ndocker build -t $DOCKER_REGISTRY/$SERVICE_NAME:$BUILD_VERSION .\ndocker push $DOCKER_REGISTRY/$SERVICE_NAME:$BUILD_VERSION"}, "dependencies": ["validate-environment"], "onSuccess": ["run-tests"], "onFailure": ["notify-failure"]}, {"id": "run-tests", "name": "Run Integration Tests", "type": "script", "config": {"language": "bash", "script": "echo 'Running integration tests...'\n# Database credentials available as env vars\nexport TEST_DB_URL=\"postgresql://$DB_USERNAME:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME\"\nnpm run test:integration"}, "dependencies": ["build-application"], "onSuccess": ["deploy-to-staging"], "onFailure": ["notify-failure"]}, {"id": "deploy-to-staging", "name": "Deploy to Staging", "type": "deployment", "config": {"environment": "staging", "service": "web-service", "target": "kubernetes", "namespace": "staging", "configFiles": ["/app/config/database.json", "/app/config/redis.yaml", "/app/config/app-settings.json"]}, "dependencies": ["run-tests"], "onSuccess": ["run-smoke-tests"], "onFailure": ["notify-failure"]}, {"id": "run-smoke-tests", "name": "Run Smoke Tests", "type": "http", "config": {"url": "https://staging.example.com/health", "method": "GET", "headers": {"Authorization": "Bearer $STAGING_API_TOKEN"}, "expectedStatus": 200, "timeout": 30}, "dependencies": ["deploy-to-staging"], "onSuccess": ["deploy-to-production"], "onFailure": ["rollback-staging"]}, {"id": "deploy-to-production", "name": "Deploy to Production", "type": "deployment", "config": {"environment": "production", "service": "web-service", "target": "kubernetes", "namespace": "production", "configFiles": ["/app/config/database.json", "/app/config/redis.yaml", "/app/config/app-settings.json"], "volumes": [{"name": "ssl-certs", "mountPath": "/etc/ssl/certs"}]}, "dependencies": ["run-smoke-tests"], "onSuccess": ["verify-production"], "onFailure": ["rollback-production"]}, {"id": "verify-production", "name": "Verify Production Deployment", "type": "script", "config": {"language": "bash", "script": "echo 'Verifying production deployment...'\n# Use monitoring API credentials\ncurl -H \"Authorization: Bearer $MONITORING_API_TOKEN\" \\\n     -H \"Content-Type: application/json\" \\\n     -d '{\"service\": \"web-service\", \"environment\": \"production\"}' \\\n     https://monitoring.example.com/api/v1/health-check"}, "dependencies": ["deploy-to-production"], "onSuccess": ["notify-success"], "onFailure": ["notify-failure"]}, {"id": "rollback-staging", "name": "Rollback Staging", "type": "deployment", "config": {"environment": "staging", "service": "web-service", "action": "rollback", "target": "kubernetes", "namespace": "staging"}, "dependencies": [], "onSuccess": ["notify-failure"], "onFailure": ["notify-failure"]}, {"id": "rollback-production", "name": "Rollback Production", "type": "deployment", "config": {"environment": "production", "service": "web-service", "action": "rollback", "target": "kubernetes", "namespace": "production"}, "dependencies": [], "onSuccess": ["notify-failure"], "onFailure": ["notify-failure"]}, {"id": "notify-success", "name": "Notify Success", "type": "http", "config": {"url": "https://hooks.slack.com/services/webhook", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer $SLACK_WEBHOOK_TOKEN"}, "body": {"text": "✅ Web application deployed successfully to production!", "channel": "#deployments", "username": "Deploy <PERSON>"}}, "dependencies": ["verify-production"], "onSuccess": [], "onFailure": []}, {"id": "notify-failure", "name": "Notify Failure", "type": "http", "config": {"url": "https://hooks.slack.com/services/webhook", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer $SLACK_WEBHOOK_TOKEN"}, "body": {"text": "❌ Web application deployment failed. Please check the logs.", "channel": "#deployments", "username": "Deploy <PERSON>"}}, "dependencies": [], "onSuccess": [], "onFailure": []}], "triggers": [{"type": "webhook", "config": {"path": "/webhook/deploy", "method": "POST"}}, {"type": "schedule", "config": {"cron": "0 2 * * 1"}}]}