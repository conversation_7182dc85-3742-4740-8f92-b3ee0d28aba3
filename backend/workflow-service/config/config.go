package config

import (
	sharedConfig "github.com/claudio/deploy-orchestrator/shared/config"
)

// Config represents the application configuration
type Config struct {
	// Common configuration sections from shared module
	Server   sharedConfig.ServerConfig  `mapstructure:"server" yaml:"server"`
	Database sharedConfig.DBConfig      `mapstructure:"database" yaml:"database"`
	Logging  sharedConfig.LoggingConfig `mapstructure:"logging" yaml:"logging"`
	Tracing  sharedConfig.TracingConfig `mapstructure:"tracing" yaml:"tracing"`
	Auth     sharedConfig.AuthConfig    `mapstructure:"auth" yaml:"auth"`
	Service  sharedConfig.ServiceConfig `mapstructure:"service" yaml:"service"`
	Gateway  sharedConfig.GatewayConfig `mapstructure:"gateway" yaml:"gateway"`

	// Service-specific configuration
	Monitoring MonitoringConfig `mapstructure:"monitoring" yaml:"monitoring"`
	Workflow   WorkflowConfig   `mapstructure:"workflow" yaml:"workflow"`
}

// MonitoringConfig represents monitoring configuration
type MonitoringConfig struct {
	Enabled            bool `yaml:"enabled"`
	MetricsEnabled     bool `yaml:"metricsEnabled"`
	TracingEnabled     bool `yaml:"tracingEnabled"`
	HealthCheckEnabled bool `yaml:"healthCheckEnabled"`
}

// ServerConfig represents server configuration
type ServerConfig struct {
	Port int `yaml:"port"`
}

// DatabaseConfig represents database configuration
type DatabaseConfig struct {
	URL string `yaml:"url"`
}

// WorkflowConfig represents workflow engine configuration
type WorkflowConfig struct {
	MaxConcurrentExecutions int            `yaml:"maxConcurrentExecutions"`
	ExecutionTimeout        int            `yaml:"executionTimeoutMinutes"`
	RetryAttempts           int            `yaml:"retryAttempts"`
	StepTimeout             int            `yaml:"stepTimeoutMinutes"`
	EnableParallelExecution bool           `yaml:"enableParallelExecution"`
	ScriptExecutor          ScriptConfig   `yaml:"scriptExecutor"`
	Instance                InstanceConfig `yaml:"instance"`
}

// InstanceConfig represents instance configuration
type InstanceConfig struct {
	Name              string            `yaml:"name"`
	Labels            map[string]string `yaml:"labels"`
	Capabilities      []string          `yaml:"capabilities"`
	HeartbeatInterval int               `yaml:"heartbeatIntervalSeconds"`
}

// ScriptConfig represents script execution configuration
type ScriptConfig struct {
	Enabled          bool     `yaml:"enabled"`
	AllowedLanguages []string `yaml:"allowedLanguages"`
	Timeout          int      `yaml:"timeoutMinutes"`
	MaxMemoryMB      int      `yaml:"maxMemoryMB"`
	SandboxEnabled   bool     `yaml:"sandboxEnabled"`
}

// Load loads configuration from file and environment variables
func Load() (*Config, error) {
	config := &Config{
		Server: sharedConfig.ServerConfig{
			Port: 8088, // Default port for workflow service (changed from 8085 to avoid conflict with integration service)
		},
		Tracing: sharedConfig.TracingConfig{
			ServiceName: "workflow-service",
		},
		Service: sharedConfig.ServiceConfig{
			Name:    "workflow-service",
			Version: "1.0.0",
		},
		Workflow: WorkflowConfig{
			MaxConcurrentExecutions: 10,
			ExecutionTimeout:        60,
			RetryAttempts:           3,
			StepTimeout:             30,
			EnableParallelExecution: true,
			ScriptExecutor: ScriptConfig{
				Enabled:          true,
				AllowedLanguages: []string{"bash", "python", "javascript"},
				Timeout:          15,
				MaxMemoryMB:      256,
				SandboxEnabled:   true,
			},
		},
	}

	// Use shared config loading
	if err := sharedConfig.LoadConfig(config, "workflow-service"); err != nil {
		return nil, err
	}

	return config, nil
}
