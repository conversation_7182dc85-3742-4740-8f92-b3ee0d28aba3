server:
  port: 8085

database:
  url: "postgres://deploy:deploy@localhost:5432/workflow_service?sslmode=disable"
  max_retries: 5
  retry_interval: 3
  max_open_conns: 25
  max_idle_conns: 10
  query_timeout: 10
  log_level: "info"

auth:
  jwt_secret: "default-secret-key-in-production"
  jwt_expiration_minutes: 60
  access_token_expiry: "1h"
  refresh_token_expiry: "168h"
  admin_service_url: "http://localhost:8086"
  api_key_header: "X-API-Key"
  disable: false

# Gateway Configuration
# Configure microservice registration with API Gateway
gateway:
  url: "http://localhost:8000"
  token: ""
  enabled: true
  service_version: "1.0.0"
  environment: "development"
  region: "local"
  retry_attempts: 3
  health_check_path: "/health"
  tags: ["api", "workflow", "microservice"]

monitoring:
  enabled: true
  metricsEnabled: true
  tracingEnabled: true
  healthCheckEnabled: true

workflow:
  maxConcurrentExecutions: 10
  executionTimeoutMinutes: 60
  retryAttempts: 3
  stepTimeoutMinutes: 30
  enableParallelExecution: true

  scriptExecutor:
    enabled: true
    allowedLanguages:
      - "bash"
      - "python"
      - "javascript"
      - "powershell"
    timeoutMinutes: 15
    maxMemoryMB: 256
    sandboxEnabled: true

  instance:
    name: ""  # Will be auto-generated if empty
    labels:
      environment: "development"
      region: "local"
      zone: "default"
    capabilities:
      - "workflow-execution"
      - "script-execution"
    heartbeatIntervalSeconds: 30
