# Workflow Service Deployment Guide

## Deployment Options

### 1. Single Instance (Development)

#### Local Development
```bash
# Clone and build
git clone <repository>
cd backend/workflow-service
go build .

# Set environment variables
export POSTGRES_URL="postgres://postgres:postgres@localhost:5432/deploy_orchestrator?sslmode=disable"
export INSTANCE_NAME="dev-local"
export INSTANCE_LABELS="environment=development,region=local"

# Run
./workflow-service
```

#### Docker Single Instance
```bash
# Build image
docker build -t workflow-service .

# Run with PostgreSQL
docker run -d --name postgres \
  -e POSTGRES_DB=deploy_orchestrator \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres \
  -p 5432:5432 postgres:15-alpine

docker run -d --name workflow-service \
  -p 8085:8085 \
  -e POSTGRES_URL="postgres://postgres:<EMAIL>:5432/deploy_orchestrator?sslmode=disable" \
  -e INSTANCE_NAME="docker-single" \
  -e INSTANCE_LABELS="environment=development,platform=docker" \
  workflow-service
```

### 2. Multi-Instance (Production)

#### Docker Compose Distributed
```bash
# Start complete distributed setup
docker-compose -f docker-compose.distributed.yml up -d

# Check status
docker-compose -f docker-compose.distributed.yml ps

# View logs
docker-compose -f docker-compose.distributed.yml logs -f workflow-service-1

# Scale instances
docker-compose -f docker-compose.distributed.yml up -d --scale workflow-service-1=3
```

#### Manual Multi-Instance Setup
```bash
# Instance 1 - Production Zone A
docker run -d --name workflow-prod-1 \
  -p 8085:8085 \
  -e POSTGRES_URL="******************************************/deploy_orchestrator?sslmode=disable" \
  -e INSTANCE_NAME="workflow-prod-1" \
  -e INSTANCE_LABELS="environment=production,region=us-east-1,zone=a" \
  -e INSTANCE_CAPABILITIES="workflow-execution,script-execution,docker,kubernetes" \
  -e WORKFLOW_MAX_CONCURRENT_EXECUTIONS=5 \
  workflow-service

# Instance 2 - Production Zone B
docker run -d --name workflow-prod-2 \
  -p 8086:8085 \
  -e POSTGRES_URL="******************************************/deploy_orchestrator?sslmode=disable" \
  -e INSTANCE_NAME="workflow-prod-2" \
  -e INSTANCE_LABELS="environment=production,region=us-east-1,zone=b" \
  -e INSTANCE_CAPABILITIES="workflow-execution,script-execution,docker,kubernetes" \
  -e WORKFLOW_MAX_CONCURRENT_EXECUTIONS=5 \
  workflow-service

# High-Memory Instance
docker run -d --name workflow-highmem \
  -p 8087:8085 \
  -m 4g \
  -e POSTGRES_URL="******************************************/deploy_orchestrator?sslmode=disable" \
  -e INSTANCE_NAME="workflow-highmem-1" \
  -e INSTANCE_LABELS="environment=production,type=highmem,region=us-east-1" \
  -e INSTANCE_CAPABILITIES="workflow-execution,script-execution,docker,kubernetes,high-memory" \
  -e WORKFLOW_MAX_CONCURRENT_EXECUTIONS=10 \
  -e SCRIPT_EXECUTOR_MAX_MEMORY_MB=2048 \
  workflow-service
```

### 3. Kubernetes Deployment

#### Namespace and ConfigMap
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: workflow-system

---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: workflow-config
  namespace: workflow-system
data:
  config.yaml: |
    server:
      port: 8085
      host: "0.0.0.0"
    workflow:
      maxConcurrentExecutions: 10
      executionTimeoutMinutes: 60
      scriptExecutor:
        enabled: true
        sandboxEnabled: true
        maxMemoryMB: 512
```

#### Deployment
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: workflow-service
  namespace: workflow-system
  labels:
    app: workflow-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: workflow-service
  template:
    metadata:
      labels:
        app: workflow-service
    spec:
      containers:
      - name: workflow-service
        image: workflow-service:latest
        ports:
        - containerPort: 8085
          name: http
        env:
        - name: POSTGRES_URL
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: url
        - name: INSTANCE_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: INSTANCE_LABELS
          value: "environment=production,platform=kubernetes,region=us-east-1"
        - name: INSTANCE_CAPABILITIES
          value: "workflow-execution,script-execution,kubernetes"
        - name: WORKFLOW_MAX_CONCURRENT_EXECUTIONS
          value: "5"
        volumeMounts:
        - name: config
          mountPath: /app/config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8085
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8085
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config
        configMap:
          name: workflow-config

---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: workflow-service
  namespace: workflow-system
spec:
  selector:
    app: workflow-service
  ports:
  - port: 8085
    targetPort: 8085
    name: http
  type: ClusterIP

---
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: workflow-service
  namespace: workflow-system
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: workflow.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: workflow-service
            port:
              number: 8085
```

#### HorizontalPodAutoscaler
```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: workflow-service-hpa
  namespace: workflow-system
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: workflow-service
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 4. Cloud Deployments

#### AWS ECS
```json
{
  "family": "workflow-service",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "workflow-service",
      "image": "your-account.dkr.ecr.region.amazonaws.com/workflow-service:latest",
      "portMappings": [
        {
          "containerPort": 8085,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "INSTANCE_LABELS",
          "value": "environment=production,platform=ecs,region=us-east-1"
        },
        {
          "name": "INSTANCE_CAPABILITIES",
          "value": "workflow-execution,script-execution"
        }
      ],
      "secrets": [
        {
          "name": "POSTGRES_URL",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:postgres-url"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/workflow-service",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:8085/health || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3
      }
    }
  ]
}
```

#### Google Cloud Run
```yaml
# cloudrun.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: workflow-service
  annotations:
    run.googleapis.com/ingress: all
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "10"
        run.googleapis.com/cpu-throttling: "false"
    spec:
      containerConcurrency: 100
      containers:
      - image: gcr.io/project-id/workflow-service:latest
        ports:
        - containerPort: 8085
        env:
        - name: POSTGRES_URL
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: url
        - name: INSTANCE_LABELS
          value: "environment=production,platform=cloudrun,region=us-central1"
        - name: INSTANCE_CAPABILITIES
          value: "workflow-execution,script-execution"
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8085
          initialDelaySeconds: 30
          periodSeconds: 10
```

## Database Setup

### PostgreSQL Setup

#### Docker PostgreSQL
```bash
docker run -d --name postgres \
  -e POSTGRES_DB=deploy_orchestrator \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres \
  -p 5432:5432 \
  -v postgres_data:/var/lib/postgresql/data \
  postgres:15-alpine
```

#### Kubernetes PostgreSQL
```yaml
# postgres.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: workflow-system
spec:
  serviceName: postgres
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        env:
        - name: POSTGRES_DB
          value: deploy_orchestrator
        - name: POSTGRES_USER
          value: postgres
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
```

### Database Migration

The service automatically handles database migrations on startup. For manual migration:

```bash
# Connect to database
psql postgres://postgres:postgres@localhost:5432/deploy_orchestrator

# Check tables
\dt

# Verify instance table
SELECT * FROM workflow_instances;
```

## Gateway Service Integration

### Service Discovery

The workflow service instances automatically register with the gateway service:

```go
// In main.go - Gateway registration
gatewayClient := gateway.NewClientFromEnv("workflow-service", cfg.Server.Port, zapLogger)
gatewayClient.SafeRegister()
```

### Load Balancing

The gateway service handles:
- **Automatic Service Discovery**: Instances register on startup
- **Health Checks**: Unhealthy instances are removed from rotation
- **Load Balancing**: Requests distributed across healthy instances
- **Routing**: Path-based routing to different services

### Gateway Configuration

Configure the gateway service to route workflow requests:

```yaml
# gateway-service config
services:
  workflow-service:
    path: "/workflow-service"
    healthCheck: "/health"
    loadBalancing: "round-robin"
    instances: [] # Auto-discovered
```

## Monitoring Setup

### Prometheus Configuration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'workflow-service'
    static_configs:
      - targets:
        - 'workflow-service-1:8085'
        - 'workflow-service-2:8085'
        - 'workflow-service-dev:8085'
        - 'workflow-service-highmem:8085'
    metrics_path: '/metrics'
    scrape_interval: 10s
```

### Grafana Dashboard
Import the provided dashboard JSON or create custom dashboards for:
- Instance health and status
- Execution metrics and performance
- Resource utilization
- Error rates and success rates

## Security Considerations

### Network Security
- Use TLS/SSL for all communications
- Implement proper firewall rules
- Use VPC/private networks in cloud environments

### Authentication
- Implement API authentication
- Use service accounts for inter-service communication
- Rotate credentials regularly

### Container Security
- Use non-root users in containers
- Scan images for vulnerabilities
- Keep base images updated

### Database Security
- Use encrypted connections
- Implement proper access controls
- Regular security updates

## Backup and Recovery

### Database Backup
```bash
# Backup
pg_dump postgres://postgres:postgres@localhost:5432/deploy_orchestrator > backup.sql

# Restore
psql postgres://postgres:postgres@localhost:5432/deploy_orchestrator < backup.sql
```

### Configuration Backup
- Version control all configuration files
- Backup environment variable configurations
- Document deployment procedures

## Performance Tuning

### Database Optimization
- Configure connection pooling
- Optimize query performance
- Monitor database metrics

### Instance Optimization
- Adjust concurrent execution limits
- Configure appropriate resource limits
- Monitor and tune garbage collection

### Network Optimization
- Use connection pooling
- Implement caching where appropriate
- Optimize load balancer configuration

---

For troubleshooting and maintenance, see [HOWTO.md](./HOWTO.md).
