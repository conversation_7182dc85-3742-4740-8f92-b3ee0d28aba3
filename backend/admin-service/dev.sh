#!/bin/bash

# Check if air is installed
if ! command -v air &> /dev/null; then
    echo "Installing air for hot reload..."
    go install github.com/cosmtrek/air@latest
    
    # Check if installation was successful
    if ! command -v air &> /dev/null; then
        echo "Failed to install air. Please install it manually:"
        echo "go install github.com/cosmtrek/air@latest"
        echo "Running without hot reload..."
        ./run.sh
        exit 1
    fi
fi

# Check if config directory exists, if not create it
if [ ! -d "./config" ]; then
  mkdir -p ./config
fi

# Check if config file exists, if not create a default one
if [ ! -f "./config/config.yaml" ]; then
  echo "Config file not found, creating a default config..."
  go run -mod=mod main.go -generate-config
fi

echo "Starting admin service in development mode using config.yaml..."

# Run with hot reload
echo "Starting in development mode with hot reload..."
air
