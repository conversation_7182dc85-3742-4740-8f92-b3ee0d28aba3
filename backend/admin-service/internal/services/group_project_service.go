package services

import (
	"context"
	"fmt"
	"time"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type GroupProjectService struct {
	db *gorm.DB
}

func NewGroupProjectService(db *gorm.DB) *GroupProjectService {
	return &GroupProjectService{
		db: db,
	}
}

// AssignGroupToProject assigns a group to a project
func (s *GroupProjectService) AssignGroupToProject(ctx context.Context, groupID, projectID, createdBy string) (*models.GroupProject, error) {
	// Check if assignment already exists
	var existing models.GroupProject
	if err := s.db.Where("group_id = ? AND project_id = ?", groupID, projectID).First(&existing).Error; err == nil {
		return &existing, nil // Already exists
	}

	// Verify group exists
	var group models.Group
	if err := s.db.First(&group, "id = ?", groupID).Error; err != nil {
		return nil, fmt.Errorf("group not found: %w", err)
	}

	// Verify project exists
	var project models.Project
	if err := s.db.First(&project, "id = ?", projectID).Error; err != nil {
		return nil, fmt.Errorf("project not found: %w", err)
	}

	// Create assignment
	assignment := &models.GroupProject{
		ID:        uuid.New().String(),
		GroupID:   groupID,
		ProjectID: projectID,
		CreatedBy: createdBy,
		BaseModel: models.BaseModel{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	if err := s.db.Create(assignment).Error; err != nil {
		return nil, fmt.Errorf("failed to create group-project assignment: %w", err)
	}

	return assignment, nil
}

// RemoveGroupFromProject removes a group's access to a project
func (s *GroupProjectService) RemoveGroupFromProject(ctx context.Context, groupID, projectID string) error {
	result := s.db.Where("group_id = ? AND project_id = ?", groupID, projectID).Delete(&models.GroupProject{})
	if result.Error != nil {
		return fmt.Errorf("failed to remove group-project assignment: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("group-project assignment not found")
	}

	return nil
}

// GetGroupProjects returns all projects assigned to a group
func (s *GroupProjectService) GetGroupProjects(ctx context.Context, groupID string) ([]models.Project, error) {
	var projects []models.Project

	err := s.db.Table("projects p").
		Select("p.*").
		Joins("JOIN group_projects gp ON p.id = gp.project_id").
		Where("gp.group_id = ? AND p.deleted_at IS NULL", groupID).
		Find(&projects).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get group projects: %w", err)
	}

	return projects, nil
}

// GetProjectGroups returns all groups assigned to a project
func (s *GroupProjectService) GetProjectGroups(ctx context.Context, projectID string) ([]models.Group, error) {
	var groups []models.Group

	err := s.db.Table("groups g").
		Select("g.*").
		Joins("JOIN group_projects gp ON g.id = gp.group_id").
		Where("gp.project_id = ? AND g.deleted_at IS NULL", projectID).
		Find(&groups).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get project groups: %w", err)
	}

	return groups, nil
}

// GetUserProjects returns all projects accessible to a user through their groups
func (s *GroupProjectService) GetUserProjects(ctx context.Context, userID string) ([]models.Project, error) {
	var projects []models.Project

	err := s.db.Table("projects p").
		Select("p.*").
		Joins("JOIN user_project_access upa ON p.id = upa.project_id").
		Where("upa.user_id = ? AND p.deleted_at IS NULL", userID).
		Find(&projects).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get user projects: %w", err)
	}

	return projects, nil
}

// CheckUserProjectAccess checks if a user has access to a specific project
func (s *GroupProjectService) CheckUserProjectAccess(ctx context.Context, userID, projectID string) (bool, error) {
	var count int64

	err := s.db.Table("user_project_access").
		Where("user_id = ? AND project_id = ?", userID, projectID).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check user project access: %w", err)
	}

	return count > 0, nil
}

// GetAllAssignments returns all group-project assignments with pagination
func (s *GroupProjectService) GetAllAssignments(ctx context.Context, limit, offset int) ([]models.GroupProject, int64, error) {
	var assignments []models.GroupProject
	var total int64

	// Get total count
	if err := s.db.Model(&models.GroupProject{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count assignments: %w", err)
	}

	// Get assignments with pagination
	err := s.db.Preload("Group").Preload("Project").
		Limit(limit).Offset(offset).
		Order("created_at DESC").
		Find(&assignments).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get assignments: %w", err)
	}

	return assignments, total, nil
}

// BulkAssignGroupsToProject assigns multiple groups to a project
func (s *GroupProjectService) BulkAssignGroupsToProject(ctx context.Context, groupIDs []string, projectID, createdBy string) ([]models.GroupProject, error) {
	var assignments []models.GroupProject

	// Verify project exists
	var project models.Project
	if err := s.db.First(&project, "id = ?", projectID).Error; err != nil {
		return nil, fmt.Errorf("project not found: %w", err)
	}

	// Process each group
	for _, groupID := range groupIDs {
		assignment, err := s.AssignGroupToProject(ctx, groupID, projectID, createdBy)
		if err != nil {
			return nil, fmt.Errorf("failed to assign group %s to project: %w", groupID, err)
		}
		assignments = append(assignments, *assignment)
	}

	return assignments, nil
}

// BulkAssignProjectsToGroup assigns multiple projects to a group
func (s *GroupProjectService) BulkAssignProjectsToGroup(ctx context.Context, projectIDs []string, groupID, createdBy string) ([]models.GroupProject, error) {
	var assignments []models.GroupProject

	// Verify group exists
	var group models.Group
	if err := s.db.First(&group, "id = ?", groupID).Error; err != nil {
		return nil, fmt.Errorf("group not found: %w", err)
	}

	// Process each project
	for _, projectID := range projectIDs {
		assignment, err := s.AssignGroupToProject(ctx, groupID, projectID, createdBy)
		if err != nil {
			return nil, fmt.Errorf("failed to assign project %s to group: %w", projectID, err)
		}
		assignments = append(assignments, *assignment)
	}

	return assignments, nil
}

// GetUserProjectMatrix returns a matrix view of user-project access
func (s *GroupProjectService) GetUserProjectMatrix(ctx context.Context) (map[string]interface{}, error) {
	var results []struct {
		UserID      string `json:"userId"`
		UserName    string `json:"userName"`
		ProjectID   string `json:"projectId"`
		ProjectName string `json:"projectName"`
		GroupName   string `json:"groupName"`
		RoleName    string `json:"roleName"`
	}

	err := s.db.Table("user_project_access upa").
		Select("upa.user_id, u.name as user_name, upa.project_id, upa.project_name, upa.group_name, upa.role_name").
		Joins("JOIN users u ON upa.user_id = u.id").
		Where("u.deleted_at IS NULL").
		Find(&results).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get user project matrix: %w", err)
	}

	// Organize data into matrix format
	matrix := make(map[string]interface{})
	users := make(map[string]map[string]interface{})
	projects := make(map[string]map[string]interface{})

	for _, result := range results {
		// Add user data
		if _, exists := users[result.UserID]; !exists {
			users[result.UserID] = map[string]interface{}{
				"id":       result.UserID,
				"name":     result.UserName,
				"projects": make([]map[string]interface{}, 0),
			}
		}

		// Add project data
		if _, exists := projects[result.ProjectID]; !exists {
			projects[result.ProjectID] = map[string]interface{}{
				"id":   result.ProjectID,
				"name": result.ProjectName,
			}
		}

		// Add project to user's access list
		userProjects := users[result.UserID]["projects"].([]map[string]interface{})
		userProjects = append(userProjects, map[string]interface{}{
			"projectId":   result.ProjectID,
			"projectName": result.ProjectName,
			"groupName":   result.GroupName,
			"roleName":    result.RoleName,
		})
		users[result.UserID]["projects"] = userProjects
	}

	matrix["users"] = users
	matrix["projects"] = projects

	return matrix, nil
}
