package handlers

import (
	"net/http"
	"strconv"

	"github.com/claudio/deploy-orchestrator/admin-service/internal/services"
	"github.com/gin-gonic/gin"
)

type GroupProjectHandler struct {
	groupProjectService *services.GroupProjectService
}

func NewGroupProjectHandler(groupProjectService *services.GroupProjectService) *GroupProjectHandler {
	return &GroupProjectHandler{
		groupProjectService: groupProjectService,
	}
}

// AssignGroupToProject assigns a group to a project
func (h *GroupProjectHandler) AssignGroupToProject(c *gin.Context) {
	var req struct {
		GroupID   string `json:"groupId" binding:"required"`
		ProjectID string `json:"projectId" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get current user for audit
	userID, _ := c.Get("userID")

	assignment, err := h.groupProjectService.AssignGroupToProject(c.Request.Context(), req.GroupID, req.ProjectID, userID.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, assignment)
}

// RemoveGroupFromProject removes a group's access to a project
func (h *GroupProjectHandler) RemoveGroupFromProject(c *gin.Context) {
	groupID := c.Param("groupId")
	projectID := c.Param("projectId")

	if groupID == "" || projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "groupId and projectId are required"})
		return
	}

	err := h.groupProjectService.RemoveGroupFromProject(c.Request.Context(), groupID, projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Group access removed from project"})
}

// GetGroupProjects returns all projects assigned to a group
func (h *GroupProjectHandler) GetGroupProjects(c *gin.Context) {
	groupID := c.Param("groupId")
	if groupID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "groupId is required"})
		return
	}

	projects, err := h.groupProjectService.GetGroupProjects(c.Request.Context(), groupID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"projects": projects})
}

// GetProjectGroups returns all groups assigned to a project
func (h *GroupProjectHandler) GetProjectGroups(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "projectId is required"})
		return
	}

	groups, err := h.groupProjectService.GetProjectGroups(c.Request.Context(), projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"groups": groups})
}

// GetUserProjects returns all projects accessible to a user through their groups
func (h *GroupProjectHandler) GetUserProjects(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "userId is required"})
		return
	}

	projects, err := h.groupProjectService.GetUserProjects(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"projects": projects})
}

// GetCurrentUserProjects returns projects accessible to the current authenticated user
func (h *GroupProjectHandler) GetCurrentUserProjects(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	projects, err := h.groupProjectService.GetUserProjects(c.Request.Context(), userID.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"projects": projects})
}

// CheckUserProjectAccess checks if a user has access to a specific project
func (h *GroupProjectHandler) CheckUserProjectAccess(c *gin.Context) {
	userID := c.Param("userId")
	projectID := c.Param("projectId")

	if userID == "" || projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "userId and projectId are required"})
		return
	}

	hasAccess, err := h.groupProjectService.CheckUserProjectAccess(c.Request.Context(), userID, projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"hasAccess": hasAccess})
}

// GetAllGroupProjectAssignments returns all group-project assignments with pagination
func (h *GroupProjectHandler) GetAllGroupProjectAssignments(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 50
	}

	offset := (page - 1) * limit

	assignments, total, err := h.groupProjectService.GetAllAssignments(c.Request.Context(), limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"assignments": assignments,
		"pagination": gin.H{
			"page":  page,
			"limit": limit,
			"total": total,
		},
	})
}

// BulkAssignGroupsToProject assigns multiple groups to a project
func (h *GroupProjectHandler) BulkAssignGroupsToProject(c *gin.Context) {
	var req struct {
		GroupIDs  []string `json:"groupIds" binding:"required"`
		ProjectID string   `json:"projectId" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get current user for audit
	userID, _ := c.Get("userID")

	assignments, err := h.groupProjectService.BulkAssignGroupsToProject(c.Request.Context(), req.GroupIDs, req.ProjectID, userID.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"assignments": assignments})
}

// BulkAssignProjectsToGroup assigns multiple projects to a group
func (h *GroupProjectHandler) BulkAssignProjectsToGroup(c *gin.Context) {
	var req struct {
		ProjectIDs []string `json:"projectIds" binding:"required"`
		GroupID    string   `json:"groupId" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get current user for audit
	userID, _ := c.Get("userID")

	assignments, err := h.groupProjectService.BulkAssignProjectsToGroup(c.Request.Context(), req.ProjectIDs, req.GroupID, userID.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"assignments": assignments})
}

// GetUserProjectMatrix returns a matrix view of user-project access
func (h *GroupProjectHandler) GetUserProjectMatrix(c *gin.Context) {
	matrix, err := h.groupProjectService.GetUserProjectMatrix(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"matrix": matrix})
}
