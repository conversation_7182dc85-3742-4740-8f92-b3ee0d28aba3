.PHONY: setup build run dev docker docker-compose test clean

# Default target
all: build

# Setup the project
setup:
	@echo "Setting up the project..."
	@./setup.sh

# Build the application
build:
	@echo "Building the application..."
	@go build -o bin/admin-service main.go

# Run the application
run:
	@echo "Running the application..."
	@./run.sh

# Run in development mode with hot reload
dev:
	@echo "Running in development mode..."
	@./dev.sh

# Build docker image
docker:
	@echo "Building Docker image..."
	@docker build -t admin-service .

# Run with docker-compose
docker-compose:
	@echo "Starting services with Docker Compose..."
	@docker-compose up -d

# Run tests
test:
	@echo "Running tests..."
	@go test -v ./...

# Clean up
clean:
	@echo "Cleaning up..."
	@rm -rf bin tmp
	@go clean

# Help information
help:
	@echo "Available targets:"
	@echo "  setup         - Set up the project environment"
	@echo "  build         - Build the application"
	@echo "  run           - Run the application"
	@echo "  dev           - Run in development mode with hot reload"
	@echo "  docker        - Build Docker image"
	@echo "  docker-compose - Run with Docker Compose"
	@echo "  test          - Run tests"
	@echo "  clean         - Clean up build artifacts"
