package models

import (
	"gorm.io/gorm"
)

// IdentityProviderType represents the type of identity provider
type IdentityProviderType string

const (
	// IdpOIDC represents an OIDC identity provider
	IdpOIDC IdentityProviderType = "oidc"
	// IdpSAML represents a SAML identity provider
	IdpSAML IdentityProviderType = "saml"
	// IdpLDAP represents an LDAP identity provider
	IdpLDAP IdentityProviderType = "ldap"
)

// IdentityProvider is the base model for all identity providers
type IdentityProvider struct {
	BaseModel
	Name        string               `json:"name" gorm:"uniqueIndex;not null"`
	Description string               `json:"description"`
	Type        IdentityProviderType `json:"type" gorm:"not null"`
	Enabled     bool                 `json:"enabled" gorm:"default:false"`
	IsDefault   bool                 `json:"isDefault" gorm:"default:false"`
}

// OIDCProvider represents an OIDC identity provider configuration
type OIDCProvider struct {
	IdentityProvider
	IssuerURL     string `json:"issuerUrl" gorm:"not null"`
	ClientID      string `json:"clientId" gorm:"not null"`
	ClientSecret  string `json:"clientSecret" gorm:"not null"`
	RedirectURL   string `json:"redirectUrl" gorm:"not null"`
	Scopes        string `json:"scopes" gorm:"default:'openid profile email'"`
	GroupsClaim   string `json:"groupsClaim" gorm:"default:'groups'"`
	UsernameClaim string `json:"usernameClaim" gorm:"default:'preferred_username'"`
	EmailClaim    string `json:"emailClaim" gorm:"default:'email'"`
}

// TableName overrides the table name for OIDCProvider
func (OIDCProvider) TableName() string {
	return "oidc_providers"
}

// SAMLProvider represents a SAML identity provider configuration
type SAMLProvider struct {
	IdentityProvider
	EntityID        string `json:"entityId" gorm:"not null"`
	MetadataURL     string `json:"metadataUrl"`
	ACSURL          string `json:"acsUrl" gorm:"not null"`
	SPCertificate   string `json:"spCertificate" gorm:"type:text"`
	SPPrivateKey    string `json:"spPrivateKey" gorm:"type:text"`
	GroupsAttribute string `json:"groupsAttribute" gorm:"default:'groups'"`
}

// TableName overrides the table name for SAMLProvider
func (SAMLProvider) TableName() string {
	return "saml_providers"
}

// LDAPProvider represents an LDAP identity provider configuration
type LDAPProvider struct {
	IdentityProvider
	URL          string `json:"url" gorm:"not null"`
	BindDN       string `json:"bindDn"`
	BindPassword string `json:"bindPassword"`
	BaseDN       string `json:"baseDn" gorm:"not null"`
	UserFilter   string `json:"userFilter" gorm:"default:'(uid=%s)'"`
	GroupFilter  string `json:"groupFilter" gorm:"default:'(member=%s)'"`
	GroupsAttr   string `json:"groupsAttr" gorm:"default:'memberOf'"`
	UseSSL       bool   `json:"useSSL" gorm:"default:false"`
	StartTLS     bool   `json:"startTLS" gorm:"default:false"`
	InsecureSkip bool   `json:"insecureSkip" gorm:"default:false"`
}

// TableName overrides the table name for LDAPProvider
func (LDAPProvider) TableName() string {
	return "ldap_providers"
}

// MigrateIdentityProviders runs GORM auto-migration for identity provider tables
func MigrateIdentityProviders(db *gorm.DB) error {
	return db.AutoMigrate(
		&OIDCProvider{},
		&SAMLProvider{},
		&LDAPProvider{},
	)
}
