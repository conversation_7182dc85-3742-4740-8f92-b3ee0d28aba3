package models

import (
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// PluginPermission represents a specific permission for plugin functionality
type PluginPermission struct {
	ID          string    `json:"id" gorm:"primaryKey;type:varchar(100)"`
	Name        string    `json:"name" gorm:"not null;type:varchar(255)"`
	Description string    `json:"description" gorm:"type:text"`
	Category    string    `json:"category" gorm:"not null;type:varchar(50)"`  // plugin, provider, template, deployment
	Resource    string    `json:"resource" gorm:"not null;type:varchar(255)"` // e.g., 'openshift-plugin', 'gke-provider'
	Action      string    `json:"action" gorm:"not null;type:varchar(100)"`   // e.g., 'view', 'install', 'configure', 'deploy'
	Scope       string    `json:"scope" gorm:"not null;type:varchar(50)"`     // global, project, environment
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// PluginRolePermission represents the many-to-many relationship between roles and plugin permissions
type PluginRolePermission struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	RoleID       string    `json:"roleId" gorm:"not null;type:varchar(100);index"`
	PermissionID string    `json:"permissionId" gorm:"not null;type:varchar(100);index"`
	CreatedAt    time.Time `json:"createdAt"`

	// Foreign key relationships
	Role       Role             `json:"role" gorm:"foreignKey:RoleID;references:ID"`
	Permission PluginPermission `json:"permission" gorm:"foreignKey:PermissionID;references:ID"`
}

// PluginGroupPermission represents plugin permissions granted to groups for specific projects
type PluginGroupPermission struct {
	ID            uint       `json:"id" gorm:"primaryKey"`
	GroupID       string     `json:"groupId" gorm:"not null;type:varchar(100);index"`
	PermissionID  string     `json:"permissionId" gorm:"not null;type:varchar(100);index"`
	ProjectID     *string    `json:"projectId" gorm:"type:varchar(100);index"`     // nullable for global permissions
	EnvironmentID *string    `json:"environmentId" gorm:"type:varchar(100);index"` // nullable for non-environment permissions
	GrantedAt     time.Time  `json:"grantedAt"`
	GrantedBy     string     `json:"grantedBy" gorm:"not null;type:varchar(100)"`
	ExpiresAt     *time.Time `json:"expiresAt"` // nullable for permanent permissions

	// Foreign key relationships
	Group      Group            `json:"group" gorm:"foreignKey:GroupID;references:ID"`
	Permission PluginPermission `json:"permission" gorm:"foreignKey:PermissionID;references:ID"`
	Project    *Project         `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

// PluginUserPermission represents direct plugin permissions granted to users (overrides)
type PluginUserPermission struct {
	ID            uint       `json:"id" gorm:"primaryKey"`
	UserID        string     `json:"userId" gorm:"not null;type:varchar(100);index"`
	PermissionID  string     `json:"permissionId" gorm:"not null;type:varchar(100);index"`
	ProjectID     *string    `json:"projectId" gorm:"type:varchar(100);index"`
	EnvironmentID *string    `json:"environmentId" gorm:"type:varchar(100);index"`
	GrantedAt     time.Time  `json:"grantedAt"`
	GrantedBy     string     `json:"grantedBy" gorm:"not null;type:varchar(100)"`
	ExpiresAt     *time.Time `json:"expiresAt"`

	// Foreign key relationships
	User       User             `json:"user" gorm:"foreignKey:UserID;references:ID"`
	Permission PluginPermission `json:"permission" gorm:"foreignKey:PermissionID;references:ID"`
	Project    *Project         `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

// PluginAccessControl represents plugin-specific access control configuration
type PluginAccessControl struct {
	ID                        uint      `json:"id" gorm:"primaryKey"`
	PluginName                string    `json:"pluginName" gorm:"not null;unique;type:varchar(255)"`
	RequiredPermissions       string    `json:"requiredPermissions" gorm:"type:text"`       // JSON array of permission requirements
	PublicFeatures            string    `json:"publicFeatures" gorm:"type:text"`            // JSON array of public features
	AdminOnlyFeatures         string    `json:"adminOnlyFeatures" gorm:"type:text"`         // JSON array of admin-only features
	ProjectScopedFeatures     string    `json:"projectScopedFeatures" gorm:"type:text"`     // JSON array of project-scoped features
	EnvironmentScopedFeatures string    `json:"environmentScopedFeatures" gorm:"type:text"` // JSON array of environment-scoped features
	CreatedAt                 time.Time `json:"createdAt"`
	UpdatedAt                 time.Time `json:"updatedAt"`
}

// PluginAuditLog represents audit logs for plugin-related actions
type PluginAuditLog struct {
	ID             uint      `json:"id" gorm:"primaryKey"`
	UserID         string    `json:"userId" gorm:"not null;type:varchar(100);index"`
	UserName       string    `json:"userName" gorm:"not null;type:varchar(255)"`
	Action         string    `json:"action" gorm:"not null;type:varchar(100);index"`
	Resource       string    `json:"resource" gorm:"not null;type:varchar(255);index"`
	ResourceType   string    `json:"resourceType" gorm:"not null;type:varchar(50);index"` // plugin, template, provider, deployment
	Details        string    `json:"details" gorm:"type:text"`                            // JSON object with action details
	Timestamp      time.Time `json:"timestamp" gorm:"index"`
	IPAddress      string    `json:"ipAddress" gorm:"type:varchar(45)"`
	UserAgent      string    `json:"userAgent" gorm:"type:text"`
	PermissionUsed string    `json:"permissionUsed" gorm:"type:varchar(100)"`
	ProjectID      *string   `json:"projectId" gorm:"type:varchar(100);index"`
	EnvironmentID  *string   `json:"environmentId" gorm:"type:varchar(100);index"`

	// Foreign key relationships
	User    User     `json:"user" gorm:"foreignKey:UserID;references:ID"`
	Project *Project `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

// PluginPermissionPolicy represents permission policies with rules and conditions
type PluginPermissionPolicy struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"not null;unique;type:varchar(255)"`
	Description string    `json:"description" gorm:"type:text"`
	Enabled     bool      `json:"enabled" gorm:"default:true"`
	Rules       string    `json:"rules" gorm:"type:text"`     // JSON array of policy rules
	AppliesTo   string    `json:"appliesTo" gorm:"type:text"` // JSON object defining scope
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
	CreatedBy   string    `json:"createdBy" gorm:"not null;type:varchar(100)"`
}

// PluginTemplate represents workflow templates provided by plugins
type PluginTemplate struct {
	ID          string    `json:"id" gorm:"primaryKey;type:varchar(100)"`
	Name        string    `json:"name" gorm:"not null;type:varchar(255)"`
	Description string    `json:"description" gorm:"type:text"`
	PluginName  string    `json:"pluginName" gorm:"not null;type:varchar(255);index"`
	Provider    string    `json:"provider" gorm:"not null;type:varchar(100)"`
	Category    string    `json:"category" gorm:"not null;type:varchar(100)"`
	Tags        string    `json:"tags" gorm:"type:text"`       // JSON array of tags
	Parameters  string    `json:"parameters" gorm:"type:text"` // JSON array of template parameters
	Steps       string    `json:"steps" gorm:"type:text"`      // JSON array of workflow steps
	Manifests   string    `json:"manifests" gorm:"type:text"`  // JSON object of manifest templates
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// PluginProvider represents deployment providers available through plugins
type PluginProvider struct {
	ID           string    `json:"id" gorm:"primaryKey;type:varchar(100)"`
	Type         string    `json:"type" gorm:"not null;unique;type:varchar(100)"`
	Name         string    `json:"name" gorm:"not null;type:varchar(255)"`
	Description  string    `json:"description" gorm:"type:text"`
	PluginName   string    `json:"pluginName" gorm:"not null;type:varchar(255);index"`
	Capabilities string    `json:"capabilities" gorm:"type:text"`                               // JSON array of capabilities
	ConfigSchema string    `json:"configSchema" gorm:"type:text"`                               // JSON schema for provider configuration
	Status       string    `json:"status" gorm:"not null;type:varchar(50);default:'available'"` // available, installed, error
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

// PluginGroupAccess represents group-based access to plugins with project scope
type PluginGroupAccess struct {
	ID                  uint      `json:"id" gorm:"primaryKey"`
	GroupID             string    `json:"groupId" gorm:"not null;type:varchar(100);index"`
	PluginName          string    `json:"pluginName" gorm:"not null;type:varchar(255);index"`
	CanView             bool      `json:"canView" gorm:"default:false"`
	CanInstall          bool      `json:"canInstall" gorm:"default:false"`
	CanConfigure        bool      `json:"canConfigure" gorm:"default:false"`
	CanManage           bool      `json:"canManage" gorm:"default:false"`
	CanDeploy           bool      `json:"canDeploy" gorm:"default:false"`
	AllowedTemplates    string    `json:"allowedTemplates" gorm:"type:text"`    // JSON array of template IDs
	AllowedProviders    string    `json:"allowedProviders" gorm:"type:text"`    // JSON array of provider types
	AllowedProjects     string    `json:"allowedProjects" gorm:"type:text"`     // JSON array of project IDs
	AllowedEnvironments string    `json:"allowedEnvironments" gorm:"type:text"` // JSON array of environment IDs
	CreatedAt           time.Time `json:"createdAt"`
	UpdatedAt           time.Time `json:"updatedAt"`

	// Foreign key relationships
	Group Group `json:"group" gorm:"foreignKey:GroupID;references:ID"`
}

// TableName methods for custom table names
func (PluginPermission) TableName() string {
	return "plugin_permissions"
}

func (PluginRolePermission) TableName() string {
	return "plugin_role_permissions"
}

func (PluginGroupPermission) TableName() string {
	return "plugin_group_permissions"
}

func (PluginUserPermission) TableName() string {
	return "plugin_user_permissions"
}

func (PluginAccessControl) TableName() string {
	return "plugin_access_controls"
}

func (PluginAuditLog) TableName() string {
	return "plugin_audit_logs"
}

func (PluginPermissionPolicy) TableName() string {
	return "plugin_permission_policies"
}

func (PluginTemplate) TableName() string {
	return "plugin_templates"
}

func (PluginProvider) TableName() string {
	return "plugin_providers"
}

func (PluginGroupAccess) TableName() string {
	return "plugin_group_access"
}

// Configuration represents a key-value configuration store
type Configuration struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Key       string    `json:"key" gorm:"not null;unique;type:varchar(255)"`
	Value     string    `json:"value" gorm:"type:text"`
	Type      string    `json:"type" gorm:"not null;type:varchar(100);index"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

func (Configuration) TableName() string {
	return "configurations"
}

// BeforeCreate hooks
func (p *PluginPermission) BeforeCreate(tx *gorm.DB) error {
	if p.ID == "" {
		p.ID = p.Category + ":" + p.Action
		if p.Resource != "" {
			p.ID = p.Resource + ":" + p.Action
		}
	}
	return nil
}

func (p *PluginTemplate) BeforeCreate(tx *gorm.DB) error {
	if p.ID == "" {
		p.ID = p.PluginName + ":" + p.Name
	}
	return nil
}

func (p *PluginProvider) BeforeCreate(tx *gorm.DB) error {
	if p.ID == "" {
		p.ID = p.Type
	}
	return nil
}

// Validation methods
func (p *PluginPermission) Validate() error {
	if p.Name == "" {
		return fmt.Errorf("permission name is required")
	}
	if p.Category == "" {
		return fmt.Errorf("permission category is required")
	}
	if p.Action == "" {
		return fmt.Errorf("permission action is required")
	}
	if p.Scope == "" {
		return fmt.Errorf("permission scope is required")
	}
	return nil
}

// Helper methods for JSON fields
func (p *PluginAccessControl) GetRequiredPermissions() ([]map[string]interface{}, error) {
	var permissions []map[string]interface{}
	if p.RequiredPermissions == "" {
		return permissions, nil
	}
	err := json.Unmarshal([]byte(p.RequiredPermissions), &permissions)
	return permissions, err
}

func (p *PluginAccessControl) SetRequiredPermissions(permissions []map[string]interface{}) error {
	data, err := json.Marshal(permissions)
	if err != nil {
		return err
	}
	p.RequiredPermissions = string(data)
	return nil
}

func (p *PluginGroupAccess) GetAllowedTemplates() ([]string, error) {
	var templates []string
	if p.AllowedTemplates == "" {
		return templates, nil
	}
	err := json.Unmarshal([]byte(p.AllowedTemplates), &templates)
	return templates, err
}

func (p *PluginGroupAccess) SetAllowedTemplates(templates []string) error {
	data, err := json.Marshal(templates)
	if err != nil {
		return err
	}
	p.AllowedTemplates = string(data)
	return nil
}
