package models

import (
	"gorm.io/gorm"
)

// Project represents a project in the system
type Project struct {
	BaseModel
	Name        string   `json:"name" gorm:"uniqueIndex;not null"`
	Description string   `json:"description"`
	IsActive    bool     `json:"isActive" gorm:"default:true"`
	Groups      []*Group `json:"groups" gorm:"many2many:project_groups;"`
}

// TableName overrides the table name for Project
func (Project) TableName() string {
	return "projects"
}

// ProjectGroup is the join table for projects and groups
type ProjectGroup struct {
	ProjectID string `gorm:"primaryKey;type:uuid"`
	GroupID   string `gorm:"primaryKey;type:uuid"`
}

// TableName overrides the table name for ProjectGroup
func (ProjectGroup) TableName() string {
	return "project_groups"
}

// MigrateProjects runs GORM auto-migration for Project tables
func MigrateProjects(db *gorm.DB) error {
	return db.AutoMigrate(
		&Project{},
		&ProjectGroup{},
	)
}
