package models

import (
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// SAMLAssertion represents a SAML assertion for a user
type SAMLAssertion struct {
	Username  string    `json:"username" gorm:"primaryKey"`
	Assertion string    `json:"assertion" gorm:"type:text"`
	ExpiresAt time.Time `json:"expiresAt" gorm:"index"`
	CreatedAt time.Time `json:"createdAt" gorm:"autoCreateTime"`
}

// TableName overrides the table name for SAMLAssertion
func (SAMLAssertion) TableName() string {
	return "saml_assertions"
}

// OAuthToken represents an OAuth token for a user
type OAuthToken struct {
	ID           string    `json:"id" gorm:"primaryKey"`
	Username     string    `json:"username" gorm:"index"`
	Provider     string    `json:"provider" gorm:"index"`
	AccessToken  string    `json:"accessToken" gorm:"type:text"`
	RefreshToken string    `json:"refreshToken" gorm:"type:text"`
	ExpiresAt    time.Time `json:"expiresAt" gorm:"index"`
	CreatedAt    time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// TableName overrides the table name for OAuthToken
func (OAuthToken) TableName() string {
	return "oauth_tokens"
}

// MigrateAuthTokens runs GORM auto-migration for auth token tables
func MigrateAuthTokens(db *gorm.DB) error {
	return db.AutoMigrate(
		&SAMLAssertion{},
		&OAuthToken{},
	)
}

// StoreSAMLAssertion stores a SAML assertion for a user
func StoreSAMLAssertion(db *gorm.DB, username, assertion string, expiresAt time.Time) error {
	samlAssertion := SAMLAssertion{
		Username:  username,
		Assertion: assertion,
		ExpiresAt: expiresAt,
	}

	// Use upsert to handle existing assertions
	return db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "username"}},
		DoUpdates: clause.AssignmentColumns([]string{"assertion", "expires_at"}),
	}).Create(&samlAssertion).Error
}

// GetSAMLAssertion retrieves a valid SAML assertion for a user
func GetSAMLAssertion(db *gorm.DB, username string) (*SAMLAssertion, error) {
	var assertion SAMLAssertion
	err := db.Where("username = ? AND expires_at > ?", username, time.Now()).
		Order("expires_at DESC").
		First(&assertion).Error
	if err != nil {
		return nil, err
	}
	return &assertion, nil
}

// StoreOAuthToken stores an OAuth token for a user
func StoreOAuthToken(db *gorm.DB, token *OAuthToken) error {
	// Use upsert to handle existing tokens
	return db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "id"}},
		DoUpdates: clause.AssignmentColumns([]string{"access_token", "refresh_token", "expires_at", "updated_at"}),
	}).Create(token).Error
}

// GetOAuthToken retrieves a valid OAuth token for a user
func GetOAuthToken(db *gorm.DB, username, provider string) (*OAuthToken, error) {
	var token OAuthToken
	err := db.Where("username = ? AND provider = ? AND expires_at > ?", username, provider, time.Now()).
		Order("expires_at DESC").
		First(&token).Error
	if err != nil {
		return nil, err
	}
	return &token, nil
}

// GetRefreshToken retrieves a refresh token for a user
func GetRefreshToken(db *gorm.DB, username, provider string) (*OAuthToken, error) {
	var token OAuthToken
	err := db.Where("username = ? AND provider = ? AND refresh_token != ?", username, provider, "").
		Order("expires_at DESC").
		First(&token).Error
	if err != nil {
		return nil, err
	}
	return &token, nil
}
