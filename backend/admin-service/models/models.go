package models

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// BaseModel contains common fields for all models
type BaseModel struct {
	ID        string         `json:"id" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	CreatedAt time.Time      `json:"createdAt"`
	UpdatedAt time.Time      `json:"updatedAt"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// User represents a user in the system
type User struct {
	BaseModel
	Username       string     `json:"username" gorm:"uniqueIndex;not null"`
	Email          string     `json:"email" gorm:"uniqueIndex;not null"`
	HashedPassword string     `json:"-" gorm:"not null"`
	FirstName      string     `json:"firstName"`
	LastName       string     `json:"lastName"`
	IsAdmin        bool       `json:"isAdmin" gorm:"default:false"`
	IsActive       bool       `json:"isActive" gorm:"default:true"`
	LastLogin      *time.Time `json:"lastLogin"`
	Groups         []*Group   `json:"groups" gorm:"many2many:user_groups;"`
	Roles          []*Role    `json:"roles" gorm:"many2many:user_roles;"`
}

// TableName overrides the table name for User
func (User) TableName() string {
	return "users"
}

// Group represents a group of users, often imported from an identity provider
type Group struct {
	BaseModel
	Name       string  `json:"name" gorm:"uniqueIndex:idx_group_name_source;not null"`
	Source     string  `json:"source" gorm:"uniqueIndex:idx_group_name_source;not null"` // e.g. ldap, saml, oidc
	ExternalID string  `json:"externalId" gorm:"index"`                                  // ID from IdP
	Users      []*User `json:"users" gorm:"many2many:user_groups;"`
	Roles      []*Role `json:"roles" gorm:"many2many:group_roles;"`
}

// Role represents a set of permissions
type Role struct {
	BaseModel
	Name        string        `json:"name" gorm:"uniqueIndex;not null"`
	Description string        `json:"description"`
	Permissions []*Permission `json:"permissions" gorm:"many2many:role_permissions;"`
	Groups      []*Group      `json:"groups" gorm:"many2many:group_roles;"`
	Users       []*User       `json:"users" gorm:"many2many:user_roles;"`
}

// Permission represents an action or resource access
type Permission struct {
	BaseModel
	Name        string  `json:"name" gorm:"uniqueIndex;not null"`
	Description string  `json:"description"`
	Category    string  `json:"category" gorm:"index"`
	Roles       []*Role `json:"roles" gorm:"many2many:role_permissions;"`
}

// UserGroup is the join table for users and groups
type UserGroup struct {
	UserID  string `gorm:"primaryKey;type:uuid"`
	GroupID string `gorm:"primaryKey;type:uuid"`
}

// GroupRole is the join table for groups and roles
type GroupRole struct {
	GroupID string `gorm:"primaryKey;type:uuid"`
	RoleID  string `gorm:"primaryKey;type:uuid"`
}

// RolePermission is the join table for roles and permissions
type RolePermission struct {
	RoleID       string `gorm:"primaryKey;type:uuid"`
	PermissionID string `gorm:"primaryKey;type:uuid"`
}

// UserRole is the join table for users and roles (for direct user-role assignment)
type UserRole struct {
	UserID string `gorm:"primaryKey;type:uuid"`
	RoleID string `gorm:"primaryKey;type:uuid"`
}

// RoleProject is the join table for roles and projects (DEPRECATED - use GroupProject instead)
type RoleProject struct {
	RoleID    string `gorm:"primaryKey;type:uuid"`
	ProjectID string `gorm:"primaryKey;type:uuid"`
}

// GroupProject represents the assignment of a group to a project (NEW GROUP-PROJECT MODEL)
type GroupProject struct {
	ID        string   `json:"id" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	GroupID   string   `json:"groupId" gorm:"column:group_id;type:uuid;not null;index"`
	ProjectID string   `json:"projectId" gorm:"column:project_id;type:uuid;not null;index"`
	CreatedBy string   `json:"createdBy" gorm:"column:created_by;type:uuid"`
	Group     *Group   `json:"group" gorm:"foreignKey:GroupID;references:ID"`
	Project   *Project `json:"project" gorm:"foreignKey:ProjectID;references:ID"`
	BaseModel
}

// TableName overrides the table name
func (GroupProject) TableName() string {
	return "group_projects"
}

// MigrateRBAC runs GORM auto-migration for RBAC tables
func MigrateRBAC(db *gorm.DB) error {
	// First, fix any existing join table column types
	if err := fixJoinTableColumnTypes(db); err != nil {
		return err
	}

	return db.AutoMigrate(
		&Group{},
		&Role{},
		&Permission{},
		&UserGroup{},
		&GroupRole{},
		&RolePermission{},
		&UserRole{},
		&GroupProject{}, // NEW: Group-to-Project assignments
		// Note: RoleProject table will be dropped by migration 006_group_projects.sql
	)
}

// fixJoinTableColumnTypes fixes column types in join tables to be UUID
func fixJoinTableColumnTypes(db *gorm.DB) error {
	// List of tables and their columns that need to be fixed
	fixes := []struct {
		table  string
		column string
	}{
		{"user_groups", "user_id"},
		{"user_groups", "group_id"},
		{"group_roles", "group_id"},
		{"group_roles", "role_id"},
		{"role_permissions", "role_id"},
		{"role_permissions", "permission_id"},
		{"user_roles", "user_id"},
		{"user_roles", "role_id"},
		{"role_projects", "role_id"},
		{"role_projects", "project_id"},
		{"project_groups", "project_id"},
		{"project_groups", "group_id"},
	}

	for _, fix := range fixes {
		// Check if table exists
		if !db.Migrator().HasTable(fix.table) {
			continue
		}

		// Check if column exists
		if !db.Migrator().HasColumn(fix.table, fix.column) {
			continue
		}

		// Get current column type
		columnTypes, err := db.Migrator().ColumnTypes(fix.table)
		if err != nil {
			continue
		}

		for _, ct := range columnTypes {
			if ct.Name() == fix.column {
				// If it's already UUID, skip
				if ct.DatabaseTypeName() == "uuid" {
					continue
				}

				// Convert text/varchar columns to UUID
				if ct.DatabaseTypeName() == "text" || ct.DatabaseTypeName() == "varchar" {
					sql := fmt.Sprintf("ALTER TABLE %s ALTER COLUMN %s TYPE uuid USING %s::uuid", fix.table, fix.column, fix.column)
					if err := db.Exec(sql).Error; err != nil {
						// Log the error but don't fail the migration
						// This allows the system to work even if some conversions fail
						fmt.Printf("Warning: Failed to convert %s.%s to UUID: %v\n", fix.table, fix.column, err)
					}
				}
				break
			}
		}
	}

	return nil
}

// MigrateAll runs all migrations
func MigrateAll(db *gorm.DB) error {
	if err := db.AutoMigrate(&User{}); err != nil {
		return err
	}

	if err := MigrateRBAC(db); err != nil {
		return err
	}

	if err := MigrateProjects(db); err != nil {
		return err
	}

	if err := MigrateIdentityProviders(db); err != nil {
		return err
	}

	if err := MigrateRoleMappings(db); err != nil {
		return err
	}

	return nil
}
