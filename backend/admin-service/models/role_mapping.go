package models

import (
	"time"

	"gorm.io/gorm"
)

// RoleMapping represents a mapping between an identity provider group and a role
type RoleMapping struct {
	ID                 string         `json:"id" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	IdentityProviderID string         `json:"identityProviderId" gorm:"not null"`
	ProviderType       string         `json:"providerType" gorm:"not null"` // OIDC, SAML, LDAP
	GroupPattern       string         `json:"groupPattern" gorm:"not null"` // Pattern to match a group from the identity provider
	RoleID             string         `json:"roleId" gorm:"not null"`       // Role ID to assign
	ProjectID          string         `json:"projectId"`                    // Optional project ID (null for global roles)
	CreatedAt          time.Time      `json:"createdAt"`
	UpdatedAt          time.Time      `json:"updatedAt"`
	DeletedAt          gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName overrides the table name for RoleMapping
func (RoleMapping) TableName() string {
	return "role_mappings"
}

// MigrateRoleMappings runs GORM auto-migration for role mapping tables
func MigrateRoleMappings(db *gorm.DB) error {
	return db.AutoMigrate(&RoleMapping{})
}
