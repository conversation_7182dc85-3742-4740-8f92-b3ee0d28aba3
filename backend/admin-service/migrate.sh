#!/bin/bash

echo "Running database migrations for admin-service..."

# Check if config directory exists, if not create it
if [ ! -d "./config" ]; then
  mkdir -p ./config
fi

# Check if config file exists, if not create a default one
if [ ! -f "./config/config.yaml" ]; then
  echo "Config file not found, creating a default config..."
  go run -mod=mod main.go -generate-config
fi

# Run the migration
echo "Executing database migrations..."
go run migrate.go

echo "Migration completed!"
