package middleware

import (
	"context"
	"net/http"

	"github.com/claudio/deploy-orchestrator/admin-service/service"
	"github.com/gin-gonic/gin"
)

// PluginPermissionMiddleware provides centralized permission checking for all plugins
type PluginPermissionMiddleware struct {
	pluginPermissionService *service.PluginPermissionService
}

// NewPluginPermissionMiddleware creates a new plugin permission middleware
func NewPluginPermissionMiddleware(pluginPermissionService *service.PluginPermissionService) *PluginPermissionMiddleware {
	return &PluginPermissionMiddleware{
		pluginPermissionService: pluginPermissionService,
	}
}

// RequirePluginPermission creates a middleware that checks plugin-specific permissions
// This is the CENTRALIZED way for all plugins to check permissions
func (m *PluginPermissionMiddleware) RequirePluginPermission(action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from context (set by auth middleware)
		userID, exists := c.Get("userID")
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Check if user is admin (admins bypass all plugin permission checks)
		if m.isAdmin(c) {
			c.Next()
			return
		}

		// Get plugin name from URL parameter or header
		pluginName := m.getPluginName(c)
		if pluginName == "" {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Plugin name not specified"})
			return
		}

		// Get project ID if available
		projectID := c.Param("projectId")
		if projectID == "" {
			projectID = c.GetHeader("X-Project-ID")
		}

		// Create permission check request
		request := &service.PermissionCheckRequest{
			Action:       action,
			Resource:     pluginName,
			ResourceType: "plugin",
		}

		// Add scope if project ID is available
		if projectID != "" {
			request.Scope = &service.PermissionScope{
				ProjectID: &projectID,
			}
		}

		// Check permission using centralized service
		response, err := m.pluginPermissionService.CheckPermission(c.Request.Context(), userID.(string), request)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to check plugin permission",
				"details": err.Error(),
			})
			return
		}

		if !response.Allowed {
			// Log the denied access attempt
			m.logDeniedAccess(c, userID.(string), pluginName, action, response.Reason)

			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"error":     "Plugin permission denied",
				"plugin":    pluginName,
				"action":    action,
				"reason":    response.Reason,
				"projectId": projectID,
			})
			return
		}

		// Log successful access
		m.logSuccessfulAccess(c, userID.(string), pluginName, action)

		// Store permission context for the handler
		c.Set("pluginName", pluginName)
		c.Set("pluginAction", action)
		c.Set("pluginPermissions", response.GrantedPermissions)

		c.Next()
	}
}

// RequireAnyPluginPermission checks if user has ANY of the specified permissions
func (m *PluginPermissionMiddleware) RequireAnyPluginPermission(actions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from context
		userID, exists := c.Get("userID")
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Check if user is admin
		if m.isAdmin(c) {
			c.Next()
			return
		}

		// Get plugin name
		pluginName := m.getPluginName(c)
		if pluginName == "" {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Plugin name not specified"})
			return
		}

		// Check each action until one is allowed
		for _, action := range actions {
			request := &service.PermissionCheckRequest{
				Action:       action,
				Resource:     pluginName,
				ResourceType: "plugin",
			}

			// Add scope if project ID is available
			if projectID := c.Param("projectId"); projectID != "" {
				request.Scope = &service.PermissionScope{
					ProjectID: &projectID,
				}
			}

			response, err := m.pluginPermissionService.CheckPermission(c.Request.Context(), userID.(string), request)
			if err == nil && response.Allowed {
				// Log successful access
				m.logSuccessfulAccess(c, userID.(string), pluginName, action)

				// Store permission context
				c.Set("pluginName", pluginName)
				c.Set("pluginAction", action)
				c.Set("pluginPermissions", response.GrantedPermissions)

				c.Next()
				return
			}
		}

		// None of the actions were allowed
		c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
			"error":   "Plugin permission denied",
			"plugin":  pluginName,
			"actions": actions,
			"reason":  "User does not have any of the required permissions",
		})
	}
}

// Helper methods

func (m *PluginPermissionMiddleware) isAdmin(c *gin.Context) bool {
	roles, exists := c.Get("roles")
	if exists {
		rolesList, ok := roles.([]string)
		if ok {
			for _, role := range rolesList {
				if role == "admin" {
					return true
				}
			}
		}
	}
	return false
}

func (m *PluginPermissionMiddleware) getPluginName(c *gin.Context) string {
	// Try URL parameter first
	if pluginName := c.Param("pluginName"); pluginName != "" {
		return pluginName
	}

	// Try header
	if pluginName := c.GetHeader("X-Plugin-Name"); pluginName != "" {
		return pluginName
	}

	// Try query parameter
	if pluginName := c.Query("plugin"); pluginName != "" {
		return pluginName
	}

	return ""
}

func (m *PluginPermissionMiddleware) logSuccessfulAccess(c *gin.Context, userID, pluginName, action string) {
	// Log successful plugin access
	ctx := context.WithValue(c.Request.Context(), "client_ip", c.ClientIP())
	ctx = context.WithValue(ctx, "user_agent", c.GetHeader("User-Agent"))

	details := map[string]interface{}{
		"success":   true,
		"projectId": c.Param("projectId"),
		"endpoint":  c.Request.URL.Path,
		"method":    c.Request.Method,
	}

	m.pluginPermissionService.LogPluginAction(ctx, userID, action, pluginName, details)
}

func (m *PluginPermissionMiddleware) logDeniedAccess(c *gin.Context, userID, pluginName, action, reason string) {
	// Log denied plugin access attempt
	ctx := context.WithValue(c.Request.Context(), "client_ip", c.ClientIP())
	ctx = context.WithValue(ctx, "user_agent", c.GetHeader("User-Agent"))

	details := map[string]interface{}{
		"success":   false,
		"reason":    reason,
		"projectId": c.Param("projectId"),
		"endpoint":  c.Request.URL.Path,
		"method":    c.Request.Method,
	}

	m.pluginPermissionService.LogPluginAction(ctx, userID, action, pluginName, details)
}

// Convenience methods for common plugin actions

func (m *PluginPermissionMiddleware) RequirePluginView() gin.HandlerFunc {
	return m.RequirePluginPermission("view")
}

func (m *PluginPermissionMiddleware) RequirePluginDeploy() gin.HandlerFunc {
	return m.RequirePluginPermission("deploy")
}

func (m *PluginPermissionMiddleware) RequirePluginConfigure() gin.HandlerFunc {
	return m.RequirePluginPermission("configure")
}

func (m *PluginPermissionMiddleware) RequirePluginManage() gin.HandlerFunc {
	return m.RequirePluginPermission("manage")
}

func (m *PluginPermissionMiddleware) RequirePluginInstall() gin.HandlerFunc {
	return m.RequirePluginPermission("install")
}
