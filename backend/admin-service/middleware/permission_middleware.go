package middleware

import (
	"net/http"

	"github.com/claudio/deploy-orchestrator/admin-service/service"
	"github.com/gin-gonic/gin"
)

// PermissionMiddleware provides middleware for checking permissions
type PermissionMiddleware struct {
	permissionService *service.PermissionService
}

// NewPermissionMiddleware creates a new permission middleware
func NewPermissionMiddleware(permissionService *service.PermissionService) *PermissionMiddleware {
	return &PermissionMiddleware{
		permissionService: permissionService,
	}
}

// RequirePermission creates a middleware that checks if a user has a specific permission
func (m *PermissionMiddleware) RequirePermission(permissionName string, getProjectID func(*gin.Context) string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from context (set by auth middleware)
		userID, exists := c.Get("userID")
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Check if user is admin (admins bypass permission checks)
		roles, exists := c.Get("roles")
		if exists {
			rolesList, ok := roles.([]string)
			if ok {
				for _, role := range rolesList {
					if role == "admin" {
						c.Next()
						return
					}
				}
			}
		}

		// Get project ID if needed
		var projectID string
		if getProjectID != nil {
			projectID = getProjectID(c)
		}

		// Check permission
		hasPermission, err := m.permissionService.CheckPermission(c.Request.Context(), userID.(string), permissionName, projectID)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to check permission",
				"details": err.Error(),
			})
			return
		}

		if !hasPermission {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"error":      "Permission denied",
				"permission": permissionName,
				"projectId":  projectID,
			})
			return
		}

		c.Next()
	}
}

// ProjectAccessMiddleware creates a middleware that checks if a user has access to a project
func (m *PermissionMiddleware) ProjectAccessMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from context (set by auth middleware)
		userID, exists := c.Get("userID")
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Check if user is admin (admins bypass access checks)
		roles, exists := c.Get("roles")
		if exists {
			rolesList, ok := roles.([]string)
			if ok {
				for _, role := range rolesList {
					if role == "admin" {
						c.Next()
						return
					}
				}
			}
		}

		// Get project ID from URL parameter
		projectID := c.Param("projectId")
		if projectID == "" {
			// If no project ID in URL, let the handler deal with it
			c.Next()
			return
		}

		// Check if user has access to the project
		hasAccess, err := m.permissionService.CheckUserHasProjectAccess(c.Request.Context(), userID.(string), projectID)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to check project access",
				"details": err.Error(),
			})
			return
		}

		if !hasAccess {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"error":     "Access denied",
				"projectId": projectID,
			})
			return
		}

		c.Next()
	}
}

// RequireAdmin creates a middleware that checks if a user is an admin
func (m *PermissionMiddleware) RequireAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if user is admin
		roles, exists := c.Get("roles")
		if !exists {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Role information not available"})
			return
		}

		rolesList, ok := roles.([]string)
		if !ok {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": "Invalid role information format"})
			return
		}

		isAdmin := false
		for _, role := range rolesList {
			if role == "admin" {
				isAdmin = true
				break
			}
		}

		if !isAdmin {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
			return
		}

		c.Next()
	}
}
