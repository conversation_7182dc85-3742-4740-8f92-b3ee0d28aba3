package middleware

import (
	"log"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware creates a middleware for JWT authentication
func AuthMiddleware(jwtManager *JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			return
		}

		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
			return
		}

		tokenString := parts[1]
		claims, err := jwtManager.VerifyToken(tokenString)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid token", "details": err.Error()})
			return
		}

		// Check that it's an access token, not a refresh token
		if claims.TokenType != AccessToken {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid token type"})
			return
		}

		// Set user information in context
		c.Set("userID", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("email", claims.Email)
		c.Set("roles", claims.Roles)

		c.Next()
	}
}

// AdminMiddleware creates a middleware that checks if the user is an admin
func AdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Add debugging to see what's happening
		userID, userExists := c.Get("userID")
		log.Printf("DEBUG: AdminMiddleware - userID exists: %v, userID: %v", userExists, userID)

		roles, exists := c.Get("roles")
		log.Printf("DEBUG: AdminMiddleware - roles exist: %v, roles: %v", exists, roles)

		if !exists {
			log.Printf("DEBUG: AdminMiddleware - No roles found in context")
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Role information not available"})
			return
		}

		rolesList, ok := roles.([]string)
		if !ok {
			log.Printf("DEBUG: AdminMiddleware - Invalid role format, type: %T", roles)
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": "Invalid role information format"})
			return
		}

		log.Printf("DEBUG: AdminMiddleware - Checking roles: %v", rolesList)
		isAdmin := false
		for _, role := range rolesList {
			log.Printf("DEBUG: AdminMiddleware - Checking role: %s", role)
			if role == "admin" {
				isAdmin = true
				break
			}
		}

		log.Printf("DEBUG: AdminMiddleware - isAdmin: %v", isAdmin)
		if !isAdmin {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
			return
		}

		c.Next()
	}
}

// CORSMiddleware adds CORS headers to allow cross-origin requests
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}
