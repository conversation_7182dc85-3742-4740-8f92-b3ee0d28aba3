package middleware

import (
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// TokenType defines the type of token
type TokenType string

const (
	// AccessToken is a short-lived token used for regular authentication
	AccessToken TokenType = "access"

	// RefreshToken is a long-lived token used to obtain new access tokens
	RefreshToken TokenType = "refresh"
)

// <PERSON><PERSON><PERSON> represents the JWT claims
type Claims struct {
	UserID    string    `json:"userId"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	Roles     []string  `json:"roles"`
	TokenType TokenType `json:"tokenType"`
	jwt.RegisteredClaims
}

// JWTManager handles JWT token generation and validation
type J<PERSON><PERSON>anager struct {
	secretKey     string
	accessExpiry  time.Duration
	refreshExpiry time.Duration
}

// NewJWTManager creates a new instance of JWT manager
func NewJWTManager(secretKey string, accessExpiry, refreshExpiry time.Duration) *JWTManager {
	return &JWTManager{
		secretKey:     secretKey,
		accessExpiry:  accessExpiry,
		refreshExpiry: refreshExpiry,
	}
}

// GenerateAccessToken generates a new access token for a user
func (m *JWTManager) GenerateAccessToken(userID, username, email string, roles []string) (string, error) {
	return m.generateToken(userID, username, email, roles, AccessToken, m.accessExpiry)
}

// GenerateRefreshToken generates a new refresh token for a user
func (m *JWTManager) GenerateRefreshToken(userID, username, email string, roles []string) (string, error) {
	return m.generateToken(userID, username, email, roles, RefreshToken, m.refreshExpiry)
}

// generateToken generates a new token with the provided claims
func (m *JWTManager) generateToken(userID, username, email string, roles []string, tokenType TokenType, expiry time.Duration) (string, error) {
	now := time.Now()
	claims := Claims{
		UserID:    userID,
		Username:  username,
		Email:     email,
		Roles:     roles,
		TokenType: tokenType,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(expiry)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedToken, err := token.SignedString([]byte(m.secretKey))
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return signedToken, nil
}

// VerifyToken validates a token and returns its claims
func (m *JWTManager) VerifyToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(m.secretKey), nil
	})

	if err != nil {
		return nil, fmt.Errorf("invalid token: %w", err)
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token claims")
}
