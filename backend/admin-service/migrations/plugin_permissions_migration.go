package migrations

import (
	"encoding/json"
	"log"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"gorm.io/gorm"
)

// MigratePluginPermissions creates all plugin permission related tables and seeds default data
func MigratePluginPermissions(db *gorm.DB) error {
	log.Println("Starting plugin permissions migration...")

	// Create tables
	err := db.AutoMigrate(
		&models.PluginPermission{},
		&models.PluginRolePermission{},
		&models.PluginGroupPermission{},
		&models.PluginUserPermission{},
		&models.PluginAccessControl{},
		&models.PluginAuditLog{},
		&models.PluginPermissionPolicy{},
		&models.PluginTemplate{},
		&models.PluginProvider{},
		&models.PluginGroupAccess{},
		&models.Configuration{},
	)
	if err != nil {
		return err
	}

	log.Println("Plugin permission tables created successfully")

	// Seed default permissions
	if err := seedDefaultPermissions(db); err != nil {
		return err
	}

	// Seed default role permissions
	if err := seedDefaultRolePermissions(db); err != nil {
		return err
	}

	// Seed default plugin access controls
	if err := seedDefaultPluginAccessControls(db); err != nil {
		return err
	}

	log.Println("Plugin permissions migration completed successfully")
	return nil
}

// seedDefaultPermissions creates the default plugin permissions
func seedDefaultPermissions(db *gorm.DB) error {
	log.Println("Seeding default plugin permissions...")

	permissions := []models.PluginPermission{
		// Plugin Management Permissions
		{
			ID:          "plugin:view",
			Name:        "View Plugins",
			Description: "View installed plugins and their status",
			Category:    "plugin",
			Resource:    "*",
			Action:      "view",
			Scope:       "global",
		},
		{
			ID:          "plugin:install",
			Name:        "Install Plugins",
			Description: "Install new plugins from marketplace or custom sources",
			Category:    "plugin",
			Resource:    "*",
			Action:      "install",
			Scope:       "global",
		},
		{
			ID:          "plugin:uninstall",
			Name:        "Uninstall Plugins",
			Description: "Remove installed plugins",
			Category:    "plugin",
			Resource:    "*",
			Action:      "uninstall",
			Scope:       "global",
		},
		{
			ID:          "plugin:configure",
			Name:        "Configure Plugins",
			Description: "Modify plugin configuration and settings",
			Category:    "plugin",
			Resource:    "*",
			Action:      "configure",
			Scope:       "global",
		},
		{
			ID:          "plugin:manage",
			Name:        "Manage Plugins",
			Description: "Enable, disable, reload, and manage plugin lifecycle",
			Category:    "plugin",
			Resource:    "*",
			Action:      "manage",
			Scope:       "global",
		},
		{
			ID:          "plugin:logs",
			Name:        "View Plugin Logs",
			Description: "Access plugin logs and debugging information",
			Category:    "plugin",
			Resource:    "*",
			Action:      "logs",
			Scope:       "global",
		},
		{
			ID:          "plugin:metrics",
			Name:        "View Plugin Metrics",
			Description: "Access plugin performance metrics and statistics",
			Category:    "plugin",
			Resource:    "*",
			Action:      "metrics",
			Scope:       "global",
		},

		// Provider Permissions
		{
			ID:          "provider:view",
			Name:        "View Providers",
			Description: "View available deployment providers",
			Category:    "provider",
			Resource:    "*",
			Action:      "view",
			Scope:       "global",
		},
		{
			ID:          "provider:configure",
			Name:        "Configure Providers",
			Description: "Configure provider settings and credentials",
			Category:    "provider",
			Resource:    "*",
			Action:      "configure",
			Scope:       "project",
		},
		{
			ID:          "provider:deploy",
			Name:        "Deploy with Providers",
			Description: "Deploy applications using providers",
			Category:    "provider",
			Resource:    "*",
			Action:      "deploy",
			Scope:       "environment",
		},

		// Template Permissions
		{
			ID:          "template:view",
			Name:        "View Templates",
			Description: "View available deployment templates",
			Category:    "template",
			Resource:    "*",
			Action:      "view",
			Scope:       "global",
		},
		{
			ID:          "template:deploy",
			Name:        "Deploy Templates",
			Description: "Deploy applications using templates",
			Category:    "template",
			Resource:    "*",
			Action:      "deploy",
			Scope:       "environment",
		},
		{
			ID:          "template:create",
			Name:        "Create Templates",
			Description: "Create new deployment templates",
			Category:    "template",
			Resource:    "*",
			Action:      "create",
			Scope:       "global",
		},
		{
			ID:          "template:edit",
			Name:        "Edit Templates",
			Description: "Modify existing deployment templates",
			Category:    "template",
			Resource:    "*",
			Action:      "edit",
			Scope:       "global",
		},

		// Deployment Permissions
		{
			ID:          "deployment:create",
			Name:        "Create Deployments",
			Description: "Create new deployments",
			Category:    "deployment",
			Resource:    "*",
			Action:      "create",
			Scope:       "environment",
		},
		{
			ID:          "deployment:view",
			Name:        "View Deployments",
			Description: "View deployment status and history",
			Category:    "deployment",
			Resource:    "*",
			Action:      "view",
			Scope:       "environment",
		},
		{
			ID:          "deployment:manage",
			Name:        "Manage Deployments",
			Description: "Start, stop, and manage deployments",
			Category:    "deployment",
			Resource:    "*",
			Action:      "manage",
			Scope:       "environment",
		},
		{
			ID:          "deployment:rollback",
			Name:        "Rollback Deployments",
			Description: "Rollback deployments to previous versions",
			Category:    "deployment",
			Resource:    "*",
			Action:      "rollback",
			Scope:       "environment",
		},
	}

	for _, permission := range permissions {
		var existing models.PluginPermission
		if err := db.Where("id = ?", permission.ID).First(&existing).Error; err == gorm.ErrRecordNotFound {
			if err := db.Create(&permission).Error; err != nil {
				return err
			}
			log.Printf("Created permission: %s", permission.ID)
		}
	}

	return nil
}

// seedDefaultRolePermissions assigns default permissions to roles
func seedDefaultRolePermissions(db *gorm.DB) error {
	log.Println("Seeding default role permissions...")

	rolePermissions := map[string][]string{
		"admin": {
			"plugin:view", "plugin:install", "plugin:uninstall", "plugin:configure", "plugin:manage",
			"plugin:logs", "plugin:metrics", "provider:view", "provider:configure", "provider:deploy",
			"template:view", "template:deploy", "template:create", "template:edit",
			"deployment:create", "deployment:view", "deployment:manage", "deployment:rollback",
		},
		"developer": {
			"plugin:view", "plugin:logs", "provider:view", "provider:deploy",
			"template:view", "template:deploy", "deployment:create", "deployment:view", "deployment:manage",
		},
		"operator": {
			"plugin:view", "plugin:logs", "plugin:metrics", "provider:view", "provider:deploy",
			"template:view", "template:deploy", "deployment:view", "deployment:manage", "deployment:rollback",
		},
		"viewer": {
			"plugin:view", "provider:view", "template:view", "deployment:view",
		},
	}

	for roleName, permissions := range rolePermissions {
		// Find the role
		var role models.Role
		if err := db.Where("name = ?", roleName).First(&role).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				log.Printf("Role %s not found, skipping permission assignment", roleName)
				continue
			}
			return err
		}

		// Assign permissions to role
		for _, permissionID := range permissions {
			var existing models.PluginRolePermission
			if err := db.Where("role_id = ? AND permission_id = ?", role.ID, permissionID).First(&existing).Error; err == gorm.ErrRecordNotFound {
				rolePermission := models.PluginRolePermission{
					RoleID:       role.ID,
					PermissionID: permissionID,
				}
				if err := db.Create(&rolePermission).Error; err != nil {
					return err
				}
				log.Printf("Assigned permission %s to role %s", permissionID, roleName)
			}
		}
	}

	return nil
}

// seedDefaultPluginAccessControls creates default access control configurations for known plugins
func seedDefaultPluginAccessControls(db *gorm.DB) error {
	log.Println("Seeding default plugin access controls...")

	// OpenShift Plugin Access Control
	openshiftRequiredPermissions := []map[string]interface{}{
		{
			"action":     "view",
			"permission": "plugin:view",
			"scope":      "global",
		},
		{
			"action":     "deploy",
			"permission": "provider:deploy",
			"scope":      "environment",
		},
		{
			"action":     "configure",
			"permission": "plugin:configure",
			"scope":      "global",
		},
	}

	openshiftPublicFeatures := []string{"info", "capabilities"}
	openshiftAdminOnlyFeatures := []string{"install", "uninstall", "hot-reload"}

	openshiftProjectScopedFeatures := []map[string]interface{}{
		{
			"feature":            "deploy",
			"requiredPermission": "provider:deploy",
			"appliesToProjects":  "all",
		},
	}

	openshiftEnvironmentScopedFeatures := []map[string]interface{}{
		{
			"feature":               "template-deploy",
			"requiredPermission":    "template:deploy",
			"appliesToEnvironments": "all",
			"appliesToProviders":    []string{"openshift"},
		},
	}

	openshiftAccessControl := models.PluginAccessControl{
		PluginName: "openshift-plugin",
	}

	// Convert to JSON strings
	if reqPermsJSON, err := json.Marshal(openshiftRequiredPermissions); err == nil {
		openshiftAccessControl.RequiredPermissions = string(reqPermsJSON)
	}
	if publicFeaturesJSON, err := json.Marshal(openshiftPublicFeatures); err == nil {
		openshiftAccessControl.PublicFeatures = string(publicFeaturesJSON)
	}
	if adminOnlyFeaturesJSON, err := json.Marshal(openshiftAdminOnlyFeatures); err == nil {
		openshiftAccessControl.AdminOnlyFeatures = string(adminOnlyFeaturesJSON)
	}
	if projectScopedFeaturesJSON, err := json.Marshal(openshiftProjectScopedFeatures); err == nil {
		openshiftAccessControl.ProjectScopedFeatures = string(projectScopedFeaturesJSON)
	}
	if environmentScopedFeaturesJSON, err := json.Marshal(openshiftEnvironmentScopedFeatures); err == nil {
		openshiftAccessControl.EnvironmentScopedFeatures = string(environmentScopedFeaturesJSON)
	}

	// Create or update
	var existing models.PluginAccessControl
	if err := db.Where("plugin_name = ?", "openshift-plugin").First(&existing).Error; err == gorm.ErrRecordNotFound {
		if err := db.Create(&openshiftAccessControl).Error; err != nil {
			return err
		}
		log.Println("Created access control for openshift-plugin")
	}

	return nil
}
