-- Fix join table column types to be UUID instead of TEXT
-- This migration converts text columns to UUID in join tables

-- Fix user_groups table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'user_groups' AND column_name = 'user_id' 
               AND data_type != 'uuid') THEN
        ALTER TABLE user_groups ALTER COLUMN user_id TYPE uuid USING user_id::uuid;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'user_groups' AND column_name = 'group_id' 
               AND data_type != 'uuid') THEN
        ALTER TABLE user_groups ALTER COLUMN group_id TYPE uuid USING group_id::uuid;
    END IF;
END $$;

-- Fix group_roles table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'group_roles' AND column_name = 'group_id' 
               AND data_type != 'uuid') THEN
        ALTER TABLE group_roles ALTER COLUMN group_id TYPE uuid USING group_id::uuid;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'group_roles' AND column_name = 'role_id' 
               AND data_type != 'uuid') THEN
        ALTER TABLE group_roles ALTER COLUMN role_id TYPE uuid USING role_id::uuid;
    END IF;
END $$;

-- Fix role_permissions table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'role_permissions' AND column_name = 'role_id' 
               AND data_type != 'uuid') THEN
        ALTER TABLE role_permissions ALTER COLUMN role_id TYPE uuid USING role_id::uuid;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'role_permissions' AND column_name = 'permission_id' 
               AND data_type != 'uuid') THEN
        ALTER TABLE role_permissions ALTER COLUMN permission_id TYPE uuid USING permission_id::uuid;
    END IF;
END $$;

-- Fix user_roles table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'user_roles' AND column_name = 'user_id' 
               AND data_type != 'uuid') THEN
        ALTER TABLE user_roles ALTER COLUMN user_id TYPE uuid USING user_id::uuid;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'user_roles' AND column_name = 'role_id' 
               AND data_type != 'uuid') THEN
        ALTER TABLE user_roles ALTER COLUMN role_id TYPE uuid USING role_id::uuid;
    END IF;
END $$;

-- Fix role_projects table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'role_projects' AND column_name = 'role_id' 
               AND data_type != 'uuid') THEN
        ALTER TABLE role_projects ALTER COLUMN role_id TYPE uuid USING role_id::uuid;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'role_projects' AND column_name = 'project_id' 
               AND data_type != 'uuid') THEN
        ALTER TABLE role_projects ALTER COLUMN project_id TYPE uuid USING project_id::uuid;
    END IF;
END $$;

-- Fix project_groups table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'project_groups' AND column_name = 'project_id' 
               AND data_type != 'uuid') THEN
        ALTER TABLE project_groups ALTER COLUMN project_id TYPE uuid USING project_id::uuid;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'project_groups' AND column_name = 'group_id' 
               AND data_type != 'uuid') THEN
        ALTER TABLE project_groups ALTER COLUMN group_id TYPE uuid USING group_id::uuid;
    END IF;
END $$;
