package service

import (
	"context"
	"fmt"
	"time"

	"github.com/claudio/deploy-orchestrator/admin-service/config"
	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"gorm.io/gorm"
)

// PermissionInitService handles initialization of permissions and roles from configuration
type PermissionInitService struct {
	db     *gorm.DB
	config *config.AdminConfig
}

// NewPermissionInitService creates a new PermissionInitService
func NewPermissionInitService(db *gorm.DB, cfg *config.AdminConfig) *PermissionInitService {
	return &PermissionInitService{
		db:     db,
		config: cfg,
	}
}

// InitializePermissionsAndRoles initializes permissions and roles from configuration
func (s *PermissionInitService) InitializePermissionsAndRoles(ctx context.Context) error {
	// Initialize permissions first
	if err := s.initializePermissions(ctx); err != nil {
		return fmt.Errorf("failed to initialize permissions: %w", err)
	}

	// Then initialize roles
	if err := s.initializeRoles(ctx); err != nil {
		return fmt.Errorf("failed to initialize roles: %w", err)
	}

	return nil
}

// initializePermissions creates permissions from configuration if they don't exist
func (s *PermissionInitService) initializePermissions(ctx context.Context) error {
	for _, permConfig := range s.config.InitialPermissions {
		// Check if permission already exists
		var existingPerm models.Permission
		err := s.db.WithContext(ctx).Where("name = ?", permConfig.Name).First(&existingPerm).Error

		if err == nil {
			// Permission already exists, skip
			continue
		}

		if err != gorm.ErrRecordNotFound {
			return fmt.Errorf("failed to check existing permission %s: %w", permConfig.Name, err)
		}

		// Create new permission
		permission := models.Permission{
			Name:        permConfig.Name,
			Description: permConfig.Description,
			Category:    permConfig.Category,
			BaseModel: models.BaseModel{
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
		}

		if err := s.db.WithContext(ctx).Create(&permission).Error; err != nil {
			return fmt.Errorf("failed to create permission %s: %w", permConfig.Name, err)
		}

		fmt.Printf("Created permission: %s\n", permConfig.Name)
	}

	return nil
}

// initializeRoles creates roles from configuration if they don't exist
func (s *PermissionInitService) initializeRoles(ctx context.Context) error {
	for _, roleConfig := range s.config.InitialRoles {
		// Check if role already exists
		var existingRole models.Role
		err := s.db.WithContext(ctx).Where("name = ?", roleConfig.Name).First(&existingRole).Error

		if err == nil {
			// Role already exists, update permissions if needed
			if err := s.updateRolePermissions(ctx, &existingRole, roleConfig.Permissions); err != nil {
				return fmt.Errorf("failed to update permissions for role %s: %w", roleConfig.Name, err)
			}
			continue
		}

		if err != gorm.ErrRecordNotFound {
			return fmt.Errorf("failed to check existing role %s: %w", roleConfig.Name, err)
		}

		// Create new role
		role := models.Role{
			Name:        roleConfig.Name,
			Description: roleConfig.Description,
			BaseModel: models.BaseModel{
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
		}

		if err := s.db.WithContext(ctx).Create(&role).Error; err != nil {
			return fmt.Errorf("failed to create role %s: %w", roleConfig.Name, err)
		}

		// Assign permissions to role
		if err := s.assignPermissionsToRole(ctx, &role, roleConfig.Permissions); err != nil {
			return fmt.Errorf("failed to assign permissions to role %s: %w", roleConfig.Name, err)
		}

		fmt.Printf("Created role: %s with %d permissions\n", roleConfig.Name, len(roleConfig.Permissions))
	}

	return nil
}

// assignPermissionsToRole assigns permissions to a role
func (s *PermissionInitService) assignPermissionsToRole(ctx context.Context, role *models.Role, permissionNames []string) error {
	for _, permName := range permissionNames {
		// Find permission by name
		var permission models.Permission
		if err := s.db.WithContext(ctx).Where("name = ?", permName).First(&permission).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				fmt.Printf("Warning: Permission %s not found for role %s\n", permName, role.Name)
				continue
			}
			return fmt.Errorf("failed to find permission %s: %w", permName, err)
		}

		// Check if role-permission assignment already exists
		var existingAssignment models.RolePermission
		err := s.db.WithContext(ctx).Where("role_id = ? AND permission_id = ?", role.ID, permission.ID).First(&existingAssignment).Error

		if err == nil {
			// Assignment already exists, skip
			continue
		}

		if err != gorm.ErrRecordNotFound {
			return fmt.Errorf("failed to check existing role-permission assignment: %w", err)
		}

		// Create role-permission assignment
		rolePermission := models.RolePermission{
			RoleID:       role.ID,
			PermissionID: permission.ID,
		}

		if err := s.db.WithContext(ctx).Create(&rolePermission).Error; err != nil {
			return fmt.Errorf("failed to assign permission %s to role %s: %w", permName, role.Name, err)
		}
	}

	return nil
}

// updateRolePermissions updates permissions for an existing role
func (s *PermissionInitService) updateRolePermissions(ctx context.Context, role *models.Role, permissionNames []string) error {
	// Get current permissions for the role
	var currentPermissions []models.Permission
	if err := s.db.WithContext(ctx).
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Where("role_permissions.role_id = ?", role.ID).
		Find(&currentPermissions).Error; err != nil {
		return fmt.Errorf("failed to get current permissions for role %s: %w", role.Name, err)
	}

	// Create a map of current permission names
	currentPermMap := make(map[string]bool)
	for _, perm := range currentPermissions {
		currentPermMap[perm.Name] = true
	}

	// Add missing permissions
	for _, permName := range permissionNames {
		if !currentPermMap[permName] {
			// Find permission by name
			var permission models.Permission
			if err := s.db.WithContext(ctx).Where("name = ?", permName).First(&permission).Error; err != nil {
				if err == gorm.ErrRecordNotFound {
					fmt.Printf("Warning: Permission %s not found for role %s\n", permName, role.Name)
					continue
				}
				return fmt.Errorf("failed to find permission %s: %w", permName, err)
			}

			// Create role-permission assignment
			rolePermission := models.RolePermission{
				RoleID:       role.ID,
				PermissionID: permission.ID,
			}

			if err := s.db.WithContext(ctx).Create(&rolePermission).Error; err != nil {
				return fmt.Errorf("failed to assign permission %s to role %s: %w", permName, role.Name, err)
			}

			fmt.Printf("Added permission %s to existing role %s\n", permName, role.Name)
		}
	}

	return nil
}

// GetPermissionsByCategory returns permissions grouped by category
func (s *PermissionInitService) GetPermissionsByCategory(ctx context.Context) (map[string][]models.Permission, error) {
	var permissions []models.Permission
	if err := s.db.WithContext(ctx).Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get permissions: %w", err)
	}

	result := make(map[string][]models.Permission)
	for _, perm := range permissions {
		category := perm.Category
		if category == "" {
			category = "other"
		}
		result[category] = append(result[category], perm)
	}

	return result, nil
}

// ValidatePermissionConfiguration validates the permission configuration
func (s *PermissionInitService) ValidatePermissionConfiguration() error {
	// Check for duplicate permission names
	permNames := make(map[string]bool)
	for _, perm := range s.config.InitialPermissions {
		if permNames[perm.Name] {
			return fmt.Errorf("duplicate permission name: %s", perm.Name)
		}
		permNames[perm.Name] = true
	}

	// Check for duplicate role names
	roleNames := make(map[string]bool)
	for _, role := range s.config.InitialRoles {
		if roleNames[role.Name] {
			return fmt.Errorf("duplicate role name: %s", role.Name)
		}
		roleNames[role.Name] = true

		// Check if all permissions referenced by roles exist in the permission list
		for _, permName := range role.Permissions {
			if !permNames[permName] {
				return fmt.Errorf("role %s references unknown permission: %s", role.Name, permName)
			}
		}
	}

	return nil
}

// GetInitializationStatus returns the status of permission and role initialization
func (s *PermissionInitService) GetInitializationStatus(ctx context.Context) (map[string]interface{}, error) {
	status := make(map[string]interface{})

	// Count existing permissions
	var permCount int64
	if err := s.db.WithContext(ctx).Model(&models.Permission{}).Count(&permCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count permissions: %w", err)
	}

	// Count existing roles
	var roleCount int64
	if err := s.db.WithContext(ctx).Model(&models.Role{}).Count(&roleCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count roles: %w", err)
	}

	status["permissions"] = map[string]interface{}{
		"configured": len(s.config.InitialPermissions),
		"existing":   permCount,
	}

	status["roles"] = map[string]interface{}{
		"configured": len(s.config.InitialRoles),
		"existing":   roleCount,
	}

	return status, nil
}
