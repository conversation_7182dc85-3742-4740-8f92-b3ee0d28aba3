package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"gorm.io/gorm"
)

// PluginPermissionService handles plugin permission operations
type PluginPermissionService struct {
	db *gorm.DB
}

// NewPluginPermissionService creates a new plugin permission service
func NewPluginPermissionService(db *gorm.DB) *PluginPermissionService {
	return &PluginPermissionService{db: db}
}

// PermissionMatrix represents the complete permission matrix for a user
type PermissionMatrix struct {
	UserID  string                  `json:"userId"`
	Plugins map[string]PluginAccess `json:"plugins"`
}

// PluginAccess represents access permissions for a specific plugin
type PluginAccess struct {
	CanView      bool                      `json:"canView"`
	CanInstall   bool                      `json:"canInstall"`
	CanConfigure bool                      `json:"canConfigure"`
	CanManage    bool                      `json:"canManage"`
	CanDeploy    bool                      `json:"canDeploy"`
	Features     map[string]FeatureAccess  `json:"features"`
	Templates    map[string]TemplateAccess `json:"templates"`
	Providers    map[string]ProviderAccess `json:"providers"`
}

// FeatureAccess represents access to a specific plugin feature
type FeatureAccess struct {
	Accessible bool   `json:"accessible"`
	Reason     string `json:"reason,omitempty"`
	Scope      string `json:"scope,omitempty"`
}

// TemplateAccess represents access to deployment templates
type TemplateAccess struct {
	CanView             bool     `json:"canView"`
	CanDeploy           bool     `json:"canDeploy"`
	AllowedProjects     []string `json:"allowedProjects"`
	AllowedEnvironments []string `json:"allowedEnvironments"`
}

// ProviderAccess represents access to deployment providers
type ProviderAccess struct {
	CanView             bool     `json:"canView"`
	CanConfigure        bool     `json:"canConfigure"`
	CanDeploy           bool     `json:"canDeploy"`
	AllowedProjects     []string `json:"allowedProjects"`
	AllowedEnvironments []string `json:"allowedEnvironments"`
}

// SecurityContext represents the current user's security context
type SecurityContext struct {
	UserID       string               `json:"userId"`
	UserGroups   []string             `json:"userGroups"`
	UserRoles    []string             `json:"userRoles"`
	Permissions  []string             `json:"permissions"`
	Restrictions SecurityRestrictions `json:"restrictions"`
}

// SecurityRestrictions represents access restrictions for the user
type SecurityRestrictions struct {
	AllowedProjects     []string `json:"allowedProjects"`
	AllowedEnvironments []string `json:"allowedEnvironments"`
	AllowedProviders    []string `json:"allowedProviders"`
}

// PermissionCheckRequest represents a permission check request
type PermissionCheckRequest struct {
	Action       string                 `json:"action"`
	Resource     string                 `json:"resource"`
	ResourceType string                 `json:"resourceType"`
	Scope        *PermissionScope       `json:"scope,omitempty"`
	Context      map[string]interface{} `json:"context,omitempty"`
}

// PermissionScope represents the scope of a permission check
type PermissionScope struct {
	ProjectID     *string `json:"projectId,omitempty"`
	EnvironmentID *string `json:"environmentId,omitempty"`
}

// PermissionCheckResponse represents the result of a permission check
type PermissionCheckResponse struct {
	Allowed             bool                    `json:"allowed"`
	Reason              string                  `json:"reason,omitempty"`
	RequiredPermissions []string                `json:"requiredPermissions"`
	GrantedPermissions  []string                `json:"grantedPermissions"`
	Restrictions        *PermissionRestrictions `json:"restrictions,omitempty"`
	AuditLogID          *uint                   `json:"auditLogId,omitempty"`
}

// PermissionRestrictions represents restrictions on the permission
type PermissionRestrictions struct {
	Projects     []string `json:"projects,omitempty"`
	Environments []string `json:"environments,omitempty"`
	Providers    []string `json:"providers,omitempty"`
	Templates    []string `json:"templates,omitempty"`
}

// GetUserPermissionMatrix generates the complete permission matrix for a user
func (s *PluginPermissionService) GetUserPermissionMatrix(ctx context.Context, userID string) (*PermissionMatrix, error) {
	// Get user with roles and groups
	var user models.User
	if err := s.db.Preload("Roles").Preload("Groups").Where("id = ?", userID).First(&user).Error; err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Check if user is admin
	isAdmin := s.isUserAdmin(&user)

	matrix := &PermissionMatrix{
		UserID:  userID,
		Plugins: make(map[string]PluginAccess),
	}

	// If user is admin, provide full access to all plugins
	if isAdmin {
		// Create a default plugin access for admins
		adminAccess := PluginAccess{
			CanView:      true,
			CanInstall:   true,
			CanConfigure: true,
			CanManage:    true,
			CanDeploy:    true,
			Features:     make(map[string]FeatureAccess),
			Templates:    make(map[string]TemplateAccess),
			Providers:    make(map[string]ProviderAccess),
		}

		// Add some default plugins for admins
		defaultPlugins := []string{"openshift-plugin", "kubernetes-plugin", "docker-plugin", "deployment-plugin"}
		for _, pluginName := range defaultPlugins {
			matrix.Plugins[pluginName] = adminAccess
		}

		return matrix, nil
	}

	// Get all available plugins (from plugin access controls or installed plugins)
	var pluginAccessControls []models.PluginAccessControl
	if err := s.db.Find(&pluginAccessControls).Error; err != nil {
		return nil, fmt.Errorf("failed to get plugin access controls: %w", err)
	}

	// For each plugin, calculate user's access
	for _, pac := range pluginAccessControls {
		pluginAccess := s.calculatePluginAccess(ctx, &user, &pac, isAdmin)
		matrix.Plugins[pac.PluginName] = pluginAccess
	}

	return matrix, nil
}

// GetUserSecurityContext returns the security context for a user
func (s *PluginPermissionService) GetUserSecurityContext(ctx context.Context, userID string) (*SecurityContext, error) {
	// Get user with roles and groups
	var user models.User
	if err := s.db.Preload("Roles").Preload("Groups").Where("id = ?", userID).First(&user).Error; err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Extract group and role names
	var userGroups []string
	var userRoles []string

	for _, group := range user.Groups {
		userGroups = append(userGroups, group.Name)
	}

	for _, role := range user.Roles {
		userRoles = append(userRoles, role.Name)
	}

	// Get all permissions for the user
	permissions, err := s.getUserPermissions(ctx, &user)
	if err != nil {
		return nil, fmt.Errorf("failed to get user permissions: %w", err)
	}

	// Get user restrictions
	restrictions, err := s.getUserRestrictions(ctx, &user)
	if err != nil {
		return nil, fmt.Errorf("failed to get user restrictions: %w", err)
	}

	return &SecurityContext{
		UserID:       userID,
		UserGroups:   userGroups,
		UserRoles:    userRoles,
		Permissions:  permissions,
		Restrictions: *restrictions,
	}, nil
}

// CheckPermission checks if a user has a specific permission
func (s *PluginPermissionService) CheckPermission(ctx context.Context, userID string, request *PermissionCheckRequest) (*PermissionCheckResponse, error) {
	// Get user
	var user models.User
	if err := s.db.Preload("Roles").Preload("Groups").Where("id = ?", userID).First(&user).Error; err != nil {
		return &PermissionCheckResponse{
			Allowed: false,
			Reason:  "User not found",
		}, nil
	}

	// Check if user is admin (admins have all permissions)
	if s.isUserAdmin(&user) {
		return &PermissionCheckResponse{
			Allowed:            true,
			GrantedPermissions: []string{"admin"},
		}, nil
	}

	// Get required permission for the action
	requiredPermission := s.getRequiredPermission(request.Action, request.ResourceType)
	if requiredPermission == "" {
		return &PermissionCheckResponse{
			Allowed: false,
			Reason:  "Unknown action or resource type",
		}, nil
	}

	// Check if user has the required permission
	hasPermission, grantedVia, err := s.userHasPermission(ctx, &user, requiredPermission, request.Scope)
	if err != nil {
		return &PermissionCheckResponse{
			Allowed: false,
			Reason:  fmt.Sprintf("Error checking permission: %v", err),
		}, nil
	}

	response := &PermissionCheckResponse{
		Allowed:             hasPermission,
		RequiredPermissions: []string{requiredPermission},
	}

	if hasPermission {
		response.GrantedPermissions = []string{grantedVia}
	} else {
		response.Reason = fmt.Sprintf("Missing required permission: %s", requiredPermission)
	}

	// Log the permission check
	auditLogID, err := s.logPermissionCheck(ctx, &user, request, response)
	if err != nil {
		log.Printf("Failed to log permission check: %v", err)
	} else {
		response.AuditLogID = &auditLogID
	}

	return response, nil
}

// LogPluginAction logs a plugin-related action for audit purposes
func (s *PluginPermissionService) LogPluginAction(ctx context.Context, userID string, action, resource string, details map[string]interface{}) error {
	// Get user
	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Convert details to JSON
	detailsJSON, err := json.Marshal(details)
	if err != nil {
		return fmt.Errorf("failed to marshal details: %w", err)
	}

	// Extract additional context from details
	var projectID, environmentID *string
	if pid, ok := details["projectId"].(string); ok && pid != "" {
		projectID = &pid
	}
	if eid, ok := details["environmentId"].(string); ok && eid != "" {
		environmentID = &eid
	}

	// Determine resource type
	resourceType := "plugin"
	if strings.Contains(resource, "template") {
		resourceType = "template"
	} else if strings.Contains(resource, "provider") {
		resourceType = "provider"
	} else if strings.Contains(resource, "deployment") {
		resourceType = "deployment"
	}

	// Create audit log entry
	auditLog := models.PluginAuditLog{
		UserID:        userID,
		UserName:      user.Username,
		Action:        action,
		Resource:      resource,
		ResourceType:  resourceType,
		Details:       string(detailsJSON),
		Timestamp:     time.Now(),
		IPAddress:     s.getIPFromContext(ctx),
		UserAgent:     s.getUserAgentFromContext(ctx),
		ProjectID:     projectID,
		EnvironmentID: environmentID,
	}

	if err := s.db.Create(&auditLog).Error; err != nil {
		return fmt.Errorf("failed to create audit log: %w", err)
	}

	return nil
}

// GetPluginAuditLogs retrieves audit logs for plugin actions
func (s *PluginPermissionService) GetPluginAuditLogs(ctx context.Context, pluginName *string, userID *string, limit int) ([]models.PluginAuditLog, error) {
	query := s.db.Model(&models.PluginAuditLog{}).Preload("User").Preload("Project")

	if pluginName != nil {
		query = query.Where("resource LIKE ?", "%"+*pluginName+"%")
	}

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	var logs []models.PluginAuditLog
	if err := query.Order("timestamp DESC").Limit(limit).Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("failed to get audit logs: %w", err)
	}

	return logs, nil
}

// Helper methods

// isUserAdmin checks if a user has admin role
func (s *PluginPermissionService) isUserAdmin(user *models.User) bool {
	for _, role := range user.Roles {
		if role.Name == "admin" {
			return true
		}
	}
	return false
}

// calculatePluginAccess calculates a user's access to a specific plugin
func (s *PluginPermissionService) calculatePluginAccess(ctx context.Context, user *models.User, pac *models.PluginAccessControl, isAdmin bool) PluginAccess {
	access := PluginAccess{
		Features:  make(map[string]FeatureAccess),
		Templates: make(map[string]TemplateAccess),
		Providers: make(map[string]ProviderAccess),
	}

	// Admin has full access
	if isAdmin {
		access.CanView = true
		access.CanInstall = true
		access.CanConfigure = true
		access.CanManage = true
		access.CanDeploy = true
		return access
	}

	// Check basic plugin permissions
	access.CanView = s.userHasBasicPermission(user, "plugin:view")
	access.CanInstall = s.userHasBasicPermission(user, "plugin:install")
	access.CanConfigure = s.userHasBasicPermission(user, "plugin:configure")
	access.CanManage = s.userHasBasicPermission(user, "plugin:manage")
	access.CanDeploy = s.userHasBasicPermission(user, "provider:deploy")

	// TODO: Calculate feature, template, and provider access based on group permissions
	// This would involve checking PluginGroupAccess records for the user's groups

	return access
}

// userHasBasicPermission checks if user has a basic permission through roles
func (s *PluginPermissionService) userHasBasicPermission(user *models.User, permissionID string) bool {
	for _, role := range user.Roles {
		var rolePermission models.PluginRolePermission
		if err := s.db.Where("role_id = ? AND permission_id = ?", role.ID, permissionID).First(&rolePermission).Error; err == nil {
			return true
		}
	}
	return false
}

// getUserPermissions gets all permissions for a user
func (s *PluginPermissionService) getUserPermissions(ctx context.Context, user *models.User) ([]string, error) {
	var permissions []string

	// Get permissions from roles
	for _, role := range user.Roles {
		var rolePermissions []models.PluginRolePermission
		if err := s.db.Where("role_id = ?", role.ID).Find(&rolePermissions).Error; err != nil {
			return nil, err
		}

		for _, rp := range rolePermissions {
			permissions = append(permissions, rp.PermissionID)
		}
	}

	// TODO: Add permissions from groups and direct user permissions

	return permissions, nil
}

// getUserRestrictions gets access restrictions for a user
func (s *PluginPermissionService) getUserRestrictions(ctx context.Context, user *models.User) (*SecurityRestrictions, error) {
	// TODO: Implement based on group project assignments and environment access
	return &SecurityRestrictions{
		AllowedProjects:     []string{"*"}, // Placeholder - implement based on group assignments
		AllowedEnvironments: []string{"*"}, // Placeholder
		AllowedProviders:    []string{"*"}, // Placeholder
	}, nil
}

// getRequiredPermission maps actions to required permissions
func (s *PluginPermissionService) getRequiredPermission(action, resourceType string) string {
	permissionMap := map[string]map[string]string{
		"plugin": {
			"view":      "plugin:view",
			"install":   "plugin:install",
			"configure": "plugin:configure",
			"manage":    "plugin:manage",
			"uninstall": "plugin:uninstall",
		},
		"provider": {
			"view":      "provider:view",
			"configure": "provider:configure",
			"deploy":    "provider:deploy",
		},
		"template": {
			"view":   "template:view",
			"deploy": "template:deploy",
			"create": "template:create",
			"edit":   "template:edit",
		},
		"deployment": {
			"create":   "deployment:create",
			"view":     "deployment:view",
			"manage":   "deployment:manage",
			"rollback": "deployment:rollback",
		},
	}

	if resourcePerms, ok := permissionMap[resourceType]; ok {
		if perm, ok := resourcePerms[action]; ok {
			return perm
		}
	}

	return ""
}

// userHasPermission checks if user has a specific permission with scope
func (s *PluginPermissionService) userHasPermission(ctx context.Context, user *models.User, permission string, scope *PermissionScope) (bool, string, error) {
	// Check role-based permissions first
	if s.userHasBasicPermission(user, permission) {
		return true, "role", nil
	}

	// TODO: Check group-based permissions with project/environment scope
	// TODO: Check direct user permissions

	return false, "", nil
}

// logPermissionCheck logs a permission check for audit
func (s *PluginPermissionService) logPermissionCheck(ctx context.Context, user *models.User, request *PermissionCheckRequest, response *PermissionCheckResponse) (uint, error) {
	details := map[string]interface{}{
		"action":       request.Action,
		"resource":     request.Resource,
		"resourceType": request.ResourceType,
		"allowed":      response.Allowed,
		"reason":       response.Reason,
	}

	if request.Scope != nil {
		details["scope"] = request.Scope
	}

	detailsJSON, _ := json.Marshal(details)

	auditLog := models.PluginAuditLog{
		UserID:       user.ID,
		UserName:     user.Username,
		Action:       "permission_check",
		Resource:     request.Resource,
		ResourceType: request.ResourceType,
		Details:      string(detailsJSON),
		Timestamp:    time.Now(),
		IPAddress:    s.getIPFromContext(ctx),
		UserAgent:    s.getUserAgentFromContext(ctx),
	}

	if request.Scope != nil {
		auditLog.ProjectID = request.Scope.ProjectID
		auditLog.EnvironmentID = request.Scope.EnvironmentID
	}

	if err := s.db.Create(&auditLog).Error; err != nil {
		return 0, err
	}

	return auditLog.ID, nil
}

// getIPFromContext extracts IP address from context
func (s *PluginPermissionService) getIPFromContext(ctx context.Context) string {
	if ip, ok := ctx.Value("client_ip").(string); ok {
		return ip
	}
	return "unknown"
}

// getUserAgentFromContext extracts user agent from context
func (s *PluginPermissionService) getUserAgentFromContext(ctx context.Context) string {
	if ua, ok := ctx.Value("user_agent").(string); ok {
		return ua
	}
	return "unknown"
}
