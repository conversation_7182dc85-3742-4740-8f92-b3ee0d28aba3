package service

import (
	"context"
	"fmt"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"gorm.io/gorm"
)

// PermissionService handles permission-related operations
type PermissionService struct {
	db *gorm.DB
}

// NewPermissionService creates a new permission service
func NewPermissionService(db *gorm.DB) *PermissionService {
	return &PermissionService{db: db}
}

// CheckUserHasProjectAccess checks if a user has access to a specific project
// Access is granted if:
// 1. The user is an admin
// 2. The user belongs to a group that is assigned to the project
// 3. The user has a role that is assigned to the project
func (s *PermissionService) CheckUserHasProjectAccess(ctx context.Context, userID, projectID string) (bool, error) {
	// Check if user exists
	var user models.User
	if err := s.db.WithContext(ctx).First(&user, "id = ?", userID).Error; err != nil {
		return false, fmt.Errorf("failed to find user: %w", err)
	}

	// If user is admin, they have access to all projects
	if user.IsAdmin {
		return true, nil
	}

	// Check if user belongs to any group that is assigned to the project
	var count int64
	err := s.db.WithContext(ctx).Model(&models.ProjectGroup{}).
		Joins("JOIN user_groups ON project_groups.group_id = user_groups.group_id").
		Where("project_groups.project_id = ? AND user_groups.user_id = ?", projectID, userID).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check group access: %w", err)
	}

	if count > 0 {
		return true, nil
	}

	// Check if user has access through group-project assignments (NEW MODEL)
	err = s.db.WithContext(ctx).Model(&models.GroupProject{}).
		Joins("JOIN user_groups ON group_projects.group_id = user_groups.group_id").
		Where("group_projects.project_id = ? AND user_groups.user_id = ? AND user_groups.deleted_at IS NULL", projectID, userID).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check group-project access: %w", err)
	}

	return count > 0, nil
}

// GetAccessibleProjectsForUser returns all projects that a user has access to
func (s *PermissionService) GetAccessibleProjectsForUser(ctx context.Context, userID string) ([]models.Project, error) {
	// Check if user exists
	var user models.User
	if err := s.db.WithContext(ctx).First(&user, "id = ?", userID).Error; err != nil {
		return nil, fmt.Errorf("failed to find user: %w", err)
	}

	// If user is admin, return all projects
	if user.IsAdmin {
		var projects []models.Project
		if err := s.db.WithContext(ctx).Find(&projects).Error; err != nil {
			return nil, fmt.Errorf("failed to get all projects: %w", err)
		}
		return projects, nil
	}

	// Get projects through group membership
	var projectIDs []string

	// Get projects through user's groups
	err := s.db.WithContext(ctx).Model(&models.ProjectGroup{}).
		Select("DISTINCT project_groups.project_id").
		Joins("JOIN user_groups ON project_groups.group_id = user_groups.group_id").
		Where("user_groups.user_id = ?", userID).
		Pluck("project_groups.project_id", &projectIDs).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get projects through groups: %w", err)
	}

	// Get projects through user's group-project assignments (NEW MODEL)
	var groupProjectIDs []string
	err = s.db.WithContext(ctx).Model(&models.GroupProject{}).
		Select("DISTINCT group_projects.project_id").
		Joins("JOIN user_groups ON group_projects.group_id = user_groups.group_id").
		Where("user_groups.user_id = ? AND user_groups.deleted_at IS NULL", userID).
		Pluck("group_projects.project_id", &groupProjectIDs).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get projects through group assignments: %w", err)
	}

	// Combine project IDs from both sources
	for _, id := range groupProjectIDs {
		found := false
		for _, existingID := range projectIDs {
			if existingID == id {
				found = true
				break
			}
		}
		if !found {
			projectIDs = append(projectIDs, id)
		}
	}

	// If no projects found, return empty slice
	if len(projectIDs) == 0 {
		return []models.Project{}, nil
	}

	// Get the actual projects
	var projects []models.Project
	if err := s.db.WithContext(ctx).Where("id IN ?", projectIDs).Find(&projects).Error; err != nil {
		return nil, fmt.Errorf("failed to get projects by IDs: %w", err)
	}

	return projects, nil
}

// CheckPermission checks if a user has a specific permission
func (s *PermissionService) CheckPermission(ctx context.Context, userID, permissionName string, projectID string) (bool, error) {
	// Check if user exists
	var user models.User
	if err := s.db.WithContext(ctx).First(&user, "id = ?", userID).Error; err != nil {
		return false, fmt.Errorf("failed to find user: %w", err)
	}

	// If user is admin, they have all permissions
	if user.IsAdmin {
		return true, nil
	}

	// Check if the permission exists
	var permission models.Permission
	if err := s.db.WithContext(ctx).Where("name = ?", permissionName).First(&permission).Error; err != nil {
		return false, fmt.Errorf("permission not found: %w", err)
	}

	// If projectID is provided, first check if user has access to the project
	if projectID != "" {
		hasAccess, err := s.CheckUserHasProjectAccess(ctx, userID, projectID)
		if err != nil {
			return false, err
		}
		if !hasAccess {
			return false, nil
		}
	}

	// Check if user has the permission through their roles
	var count int64
	err := s.db.WithContext(ctx).Model(&models.RolePermission{}).
		Joins("JOIN user_roles ON role_permissions.role_id = user_roles.role_id").
		Joins("JOIN permissions ON role_permissions.permission_id = permissions.id").
		Where("user_roles.user_id = ? AND permissions.name = ?", userID, permissionName).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check direct permission: %w", err)
	}

	if count > 0 {
		return true, nil
	}

	// Check if user has the permission through their groups
	err = s.db.WithContext(ctx).Model(&models.RolePermission{}).
		Joins("JOIN group_roles ON role_permissions.role_id = group_roles.role_id").
		Joins("JOIN user_groups ON group_roles.group_id = user_groups.group_id").
		Joins("JOIN permissions ON role_permissions.permission_id = permissions.id").
		Where("user_groups.user_id = ? AND permissions.name = ?", userID, permissionName).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check group permission: %w", err)
	}

	return count > 0, nil
}
