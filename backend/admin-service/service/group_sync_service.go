package service

import (
	"context"
	"fmt"
	"log"
	"regexp"

	"github.com/claudio/deploy-orchestrator/admin-service/config"
	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"github.com/claudio/deploy-orchestrator/admin-service/service/provider"
	"gorm.io/gorm"
)

// IdentityProviderType represents the type of IdP
type IdentityProviderType string

const (
	IdpLDAP IdentityProviderType = "ldap"
	IdpSAML IdentityProviderType = "saml"
	IdpOIDC IdentityProviderType = "oidc"
)

// SyncResult contains the results of a group synchronization operation
type SyncResult struct {
	GroupsImported int      `json:"groupsImported"`
	UsersAssigned  int      `json:"usersAssigned"`
	RolesAssigned  int      `json:"rolesAssigned"`
	Errors         []string `json:"errors"`
}

// GroupSyncService syncs user groups from identity providers
type GroupSyncService struct {
	DB                    *gorm.DB
	IdentityProviderSvc   *IdentityProviderService
	Config                *config.AdminConfig
	ldapProviderInstances map[string]*provider.LDAPProvider
	samlProviderInstances map[string]*provider.SAMLProvider
	oidcProviderInstances map[string]*provider.OIDCProvider
}

// NewGroupSyncService creates a new group sync service
func NewGroupSyncService(db *gorm.DB, idpSvc *IdentityProviderService, cfg *config.AdminConfig) *GroupSyncService {
	return &GroupSyncService{
		DB:                    db,
		IdentityProviderSvc:   idpSvc,
		Config:                cfg,
		ldapProviderInstances: make(map[string]*provider.LDAPProvider),
		samlProviderInstances: make(map[string]*provider.SAMLProvider),
		oidcProviderInstances: make(map[string]*provider.OIDCProvider),
	}
}

// SyncUserGroups syncs a user's groups from the IdP and updates DB
func (s *GroupSyncService) SyncUserGroups(ctx context.Context, user *models.User, idpType IdentityProviderType, idpGroups []string) error {
	// For each group from IdP, find or create in DB, and assign user
	var groupModels []*models.Group
	for _, groupName := range idpGroups {
		var group models.Group
		err := s.DB.Where("name = ? AND source = ?", groupName, string(idpType)).First(&group).Error
		if err == gorm.ErrRecordNotFound {
			group = models.Group{Name: groupName, Source: string(idpType)}
			if err := s.DB.Create(&group).Error; err != nil {
				return fmt.Errorf("failed to create group: %w", err)
			}
		} else if err != nil {
			return fmt.Errorf("db error: %w", err)
		}
		groupModels = append(groupModels, &group)
	}

	// Replace user's group memberships with these
	if err := s.DB.Model(user).Association("Groups").Replace(groupModels); err != nil {
		return fmt.Errorf("failed to update user groups: %w", err)
	}

	// Apply role mappings based on the user's groups
	if err := s.applyRoleMappings(ctx, user, idpType, idpGroups); err != nil {
		log.Printf("Warning: Failed to apply role mappings: %v", err)
		// Continue execution even if role mappings fail
	}

	log.Printf("Synced %d groups for user %s from %s provider", len(groupModels), user.Username, idpType)
	return nil
}

// FetchGroupsForUser fetches groups for a user from the specified identity provider
func (s *GroupSyncService) FetchGroupsForUser(ctx context.Context, username string, idpType IdentityProviderType, providerID string) ([]string, error) {
	switch idpType {
	case IdpLDAP:
		return s.fetchLDAPGroupsForUser(ctx, username, providerID)
	case IdpSAML:
		return s.fetchSAMLGroupsForUser(ctx, username, providerID)
	case IdpOIDC:
		return s.fetchOIDCGroupsForUser(ctx, username, providerID)
	default:
		return nil, fmt.Errorf("unsupported identity provider type: %s", idpType)
	}
}

// fetchLDAPGroupsForUser fetches groups for a user from an LDAP provider
func (s *GroupSyncService) fetchLDAPGroupsForUser(ctx context.Context, username string, providerID string) ([]string, error) {
	// Get the LDAP provider instance or create a new one
	ldapProvider, err := s.getLDAPProvider(ctx, providerID)
	if err != nil {
		return nil, err
	}

	// Fetch groups for the user
	return ldapProvider.FetchGroupsForUser(ctx, username)
}

// fetchSAMLGroupsForUser fetches groups for a user from a SAML provider
func (s *GroupSyncService) fetchSAMLGroupsForUser(ctx context.Context, username string, providerID string) ([]string, error) {
	// Get the SAML provider instance or create a new one
	samlProvider, err := s.getSAMLProvider(ctx, providerID)
	if err != nil {
		return nil, err
	}

	// Fetch groups for the user
	return samlProvider.FetchGroupsForUser(ctx, username)
}

// fetchOIDCGroupsForUser fetches groups for a user from an OIDC provider
func (s *GroupSyncService) fetchOIDCGroupsForUser(ctx context.Context, username string, providerID string) ([]string, error) {
	// Get the OIDC provider instance or create a new one
	oidcProvider, err := s.getOIDCProvider(ctx, providerID)
	if err != nil {
		return nil, err
	}

	// Fetch groups for the user
	return oidcProvider.FetchGroupsForUser(ctx, username)
}

// getLDAPProvider gets or creates an LDAP provider instance
func (s *GroupSyncService) getLDAPProvider(ctx context.Context, providerID string) (*provider.LDAPProvider, error) {
	// Check if we already have an instance for this provider
	if p, ok := s.ldapProviderInstances[providerID]; ok {
		return p, nil
	}

	// Get the provider configuration from the database
	var ldapProvider models.LDAPProvider
	if err := s.DB.First(&ldapProvider, "id = ?", providerID).Error; err != nil {
		return nil, fmt.Errorf("failed to get LDAP provider: %w", err)
	}

	// Create a new LDAP provider instance
	ldapConfig := &config.LDAPConfig{
		Enabled:      ldapProvider.Enabled,
		URL:          ldapProvider.URL,
		BindDN:       ldapProvider.BindDN,
		BindPassword: ldapProvider.BindPassword,
		BaseDN:       ldapProvider.BaseDN,
		UserFilter:   ldapProvider.UserFilter,
		GroupFilter:  ldapProvider.GroupFilter,
		GroupsAttr:   ldapProvider.GroupsAttr,
		UseSSL:       ldapProvider.UseSSL,
		StartTLS:     ldapProvider.StartTLS,
		InsecureSkip: ldapProvider.InsecureSkip,
	}

	// Create the provider
	p := provider.NewLDAPProvider(ldapConfig)

	// Cache the provider instance
	s.ldapProviderInstances[providerID] = p

	return p, nil
}

// getSAMLProvider gets or creates a SAML provider instance
func (s *GroupSyncService) getSAMLProvider(ctx context.Context, providerID string) (*provider.SAMLProvider, error) {
	// Check if we already have an instance for this provider
	if p, ok := s.samlProviderInstances[providerID]; ok {
		return p, nil
	}

	// Get the provider configuration from the database
	var samlProvider models.SAMLProvider
	if err := s.DB.First(&samlProvider, "id = ?", providerID).Error; err != nil {
		return nil, fmt.Errorf("failed to get SAML provider: %w", err)
	}

	// Create a new SAML provider instance
	samlConfig := &config.SAMLConfig{
		Enabled:         samlProvider.Enabled,
		EntityID:        samlProvider.EntityID,
		MetadataURL:     samlProvider.MetadataURL,
		ACSURL:          samlProvider.ACSURL,
		SPCertificate:   samlProvider.SPCertificate,
		SPPrivateKey:    samlProvider.SPPrivateKey,
		GroupsAttribute: samlProvider.GroupsAttribute,
	}

	// Create the provider
	p, err := provider.NewSAMLProvider(samlConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create SAML provider: %w", err)
	}

	// Cache the provider instance
	s.samlProviderInstances[providerID] = p

	return p, nil
}

// getOIDCProvider gets or creates an OIDC provider instance
func (s *GroupSyncService) getOIDCProvider(ctx context.Context, providerID string) (*provider.OIDCProvider, error) {
	// Check if we already have an instance for this provider
	if p, ok := s.oidcProviderInstances[providerID]; ok {
		return p, nil
	}

	// Get the provider configuration from the database
	var oidcProvider models.OIDCProvider
	if err := s.DB.First(&oidcProvider, "id = ?", providerID).Error; err != nil {
		return nil, fmt.Errorf("failed to get OIDC provider: %w", err)
	}

	// Create a new OIDC provider instance
	oidcConfig := &config.OIDCConfig{
		Enabled:       oidcProvider.Enabled,
		IssuerURL:     oidcProvider.IssuerURL,
		ClientID:      oidcProvider.ClientID,
		ClientSecret:  oidcProvider.ClientSecret,
		RedirectURL:   oidcProvider.RedirectURL,
		Scopes:        oidcProvider.Scopes,
		GroupsClaim:   oidcProvider.GroupsClaim,
		UsernameClaim: oidcProvider.UsernameClaim,
		EmailClaim:    oidcProvider.EmailClaim,
	}

	// Create the provider
	p, err := provider.NewOIDCProvider(oidcConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create OIDC provider: %w", err)
	}

	// Cache the provider instance
	s.oidcProviderInstances[providerID] = p

	return p, nil
}

// SyncAllUsers syncs all users' groups from the specified identity provider
func (s *GroupSyncService) SyncAllUsers(ctx context.Context, idpType IdentityProviderType, providerID string, dryRun bool) (*SyncResult, error) {
	result := &SyncResult{}

	// Get all users
	var users []models.User
	if err := s.DB.Find(&users).Error; err != nil {
		return nil, fmt.Errorf("failed to get users: %w", err)
	}

	// Start a transaction if not in dry-run mode
	var tx *gorm.DB
	if !dryRun {
		tx = s.DB.Begin()
		if tx.Error != nil {
			return nil, fmt.Errorf("failed to start transaction: %w", tx.Error)
		}
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
				log.Printf("Recovered from panic in SyncAllUsers: %v", r)
			}
		}()
	}

	// Sync each user's groups
	for _, user := range users {
		// Fetch groups for the user from the identity provider
		groups, err := s.FetchGroupsForUser(ctx, user.Username, idpType, providerID)
		if err != nil {
			// Log the error but continue with other users
			result.Errors = append(result.Errors, fmt.Sprintf("failed to fetch groups for user %s: %v", user.Username, err))
			continue
		}

		// In dry-run mode, just count the groups
		if dryRun {
			result.GroupsImported += len(groups)
			result.UsersAssigned++
			continue
		}

		// Sync the user's groups
		if err := s.SyncUserGroups(ctx, &user, idpType, groups); err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("failed to sync groups for user %s: %v", user.Username, err))
			continue
		}

		result.GroupsImported += len(groups)
		result.UsersAssigned++
	}

	// Commit the transaction if not in dry-run mode
	if !dryRun && tx != nil {
		if err := tx.Commit().Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("failed to commit transaction: %w", err)
		}
	}

	return result, nil
}

// SyncUser syncs a specific user's groups from the specified identity provider
func (s *GroupSyncService) SyncUser(ctx context.Context, username string, idpType IdentityProviderType, providerID string, dryRun bool) (*SyncResult, error) {
	result := &SyncResult{}

	// Get the user
	var user models.User
	if err := s.DB.Where("username = ?", username).First(&user).Error; err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Fetch groups for the user from the identity provider
	groups, err := s.FetchGroupsForUser(ctx, username, idpType, providerID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch groups for user: %w", err)
	}

	// In dry-run mode, just return the groups that would be synced
	if dryRun {
		result.GroupsImported = len(groups)
		result.UsersAssigned = 1
		return result, nil
	}

	// Start a transaction
	tx := s.DB.Begin()
	if tx.Error != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			log.Printf("Recovered from panic in SyncUser: %v", r)
		}
	}()

	// Sync the user's groups
	if err := s.SyncUserGroups(ctx, &user, idpType, groups); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to sync groups: %w", err)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	result.GroupsImported = len(groups)
	result.UsersAssigned = 1
	return result, nil
}

// GetProviderUsers gets all users that are associated with a specific identity provider
func (s *GroupSyncService) GetProviderUsers(ctx context.Context, idpType IdentityProviderType, providerID string) ([]models.User, error) {
	var users []models.User

	// Query users that have groups from this provider
	err := s.DB.Joins("JOIN user_groups ON users.id = user_groups.user_id").
		Joins("JOIN groups ON user_groups.group_id = groups.id").
		Where("groups.source = ?", string(idpType)).
		Group("users.id").
		Find(&users).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get users: %w", err)
	}

	return users, nil
}

// applyRoleMappings applies role mappings based on the user's groups
func (s *GroupSyncService) applyRoleMappings(ctx context.Context, user *models.User, idpType IdentityProviderType, idpGroups []string) error {
	// Get all role mappings for this identity provider type
	var roleMappings []models.RoleMapping
	if err := s.DB.Where("provider_type = ?", string(idpType)).Find(&roleMappings).Error; err != nil {
		return fmt.Errorf("failed to get role mappings: %w", err)
	}

	if len(roleMappings) == 0 {
		log.Printf("No role mappings found for provider type %s", idpType)
		return nil
	}

	// Find matching role mappings for each group
	var matchedRoleIDs []string
	for _, groupName := range idpGroups {
		for _, mapping := range roleMappings {
			// Check if the group matches the pattern
			matched, err := regexp.MatchString(mapping.GroupPattern, groupName)
			if err != nil {
				log.Printf("Warning: Invalid regex pattern %s: %v", mapping.GroupPattern, err)
				continue
			}

			if matched {
				log.Printf("Group %s matched pattern %s for role %s", groupName, mapping.GroupPattern, mapping.RoleID)
				matchedRoleIDs = append(matchedRoleIDs, mapping.RoleID)
			}
		}
	}

	if len(matchedRoleIDs) == 0 {
		log.Printf("No matching role mappings found for user %s", user.Username)
		return nil
	}

	// Get the roles
	var roles []models.Role
	if err := s.DB.Where("id IN ?", matchedRoleIDs).Find(&roles).Error; err != nil {
		return fmt.Errorf("failed to get roles: %w", err)
	}

	// Assign roles to user
	for _, role := range roles {
		// Check if user already has this role
		var userRole models.UserRole
		err := s.DB.Where("user_id = ? AND role_id = ?", user.ID, role.ID).First(&userRole).Error
		if err == nil {
			// User already has this role, skip
			continue
		} else if err != gorm.ErrRecordNotFound {
			return fmt.Errorf("failed to check user role: %w", err)
		}

		// Assign role to user
		userRole = models.UserRole{
			UserID: user.ID,
			RoleID: role.ID,
		}
		if err := s.DB.Create(&userRole).Error; err != nil {
			return fmt.Errorf("failed to assign role to user: %w", err)
		}

		log.Printf("Assigned role %s to user %s based on group mapping", role.Name, user.Username)
	}

	return nil
}
