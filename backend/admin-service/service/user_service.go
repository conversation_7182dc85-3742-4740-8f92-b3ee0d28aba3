package service

import (
	"context"
	"errors"
	"time"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// UserService handles user-related operations
type UserService struct {
	db *gorm.DB
}

// NewUserService creates a new instance of the user service
func NewUserService(db *gorm.DB) *UserService {
	return &UserService{
		db: db,
	}
}

// GetUserGroups returns the groups a user belongs to
func (s *UserService) GetUserGroups(ctx context.Context, userID string) ([]models.Group, error) {
	var user models.User
	if err := s.db.First(&user, "id = ?", userID).Error; err != nil {
		return nil, err
	}

	var groups []models.Group
	if err := s.db.Model(&user).Association("Groups").Find(&groups); err != nil {
		return nil, err
	}

	return groups, nil
}

// DB exposes the underlying *gorm.DB for use in handlers.
func (s *UserService) DB() *gorm.DB {
	return s.db
}

// ListUsers retrieves all users from the database
func (s *UserService) ListUsers(ctx context.Context) ([]models.User, error) {
	var users []models.User
	result := s.db.WithContext(ctx).Find(&users)
	return users, result.Error
}

// GetUser retrieves a user by ID
func (s *UserService) GetUser(ctx context.Context, id string) (*models.User, error) {
	var user models.User
	result := s.db.WithContext(ctx).First(&user, "id = ?", id)
	if result.Error != nil {
		return nil, result.Error
	}
	return &user, nil
}

// GetUserByUsername retrieves a user by username
func (s *UserService) GetUserByUsername(ctx context.Context, username string) (*models.User, error) {
	var user models.User
	result := s.db.WithContext(ctx).Where("username = ?", username).First(&user)
	if result.Error != nil {
		return nil, result.Error
	}
	return &user, nil
}

// GetUserByEmail retrieves a user by email
func (s *UserService) GetUserByEmail(ctx context.Context, email string) (*models.User, error) {
	var user models.User
	result := s.db.WithContext(ctx).Where("email = ?", email).First(&user)
	if result.Error != nil {
		return nil, result.Error
	}
	return &user, nil
}

// CreateUser creates a new user in the database
func (s *UserService) CreateUser(ctx context.Context, user *models.User, password string) error {
	// Check if username already exists
	var existingUser models.User
	result := s.db.WithContext(ctx).Where("username = ?", user.Username).First(&existingUser)
	if result.Error == nil {
		return errors.New("username already exists")
	} else if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return result.Error
	}

	// Check if email already exists
	result = s.db.WithContext(ctx).Where("email = ?", user.Email).First(&existingUser)
	if result.Error == nil {
		return errors.New("email already exists")
	} else if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return result.Error
	}

	// Hash the password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	user.HashedPassword = string(hashedPassword)

	// Create the user
	return s.db.WithContext(ctx).Create(user).Error
}

// UpdateUser updates an existing user in the database
func (s *UserService) UpdateUser(ctx context.Context, id string, updates map[string]interface{}) error {
	// Check if user exists
	var user models.User
	result := s.db.WithContext(ctx).First(&user, "id = ?", id)
	if result.Error != nil {
		return result.Error
	}

	// Check if password needs to be updated
	if password, ok := updates["password"].(string); ok {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
		if err != nil {
			return err
		}
		updates["hashed_password"] = string(hashedPassword)
		delete(updates, "password")
	}

	// Update the user
	return s.db.WithContext(ctx).Model(&user).Updates(updates).Error
}

// DeleteUser deletes a user from the database
func (s *UserService) DeleteUser(ctx context.Context, id string) error {
	result := s.db.WithContext(ctx).Unscoped().Delete(&models.User{}, "id = ?", id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

// UpdateLastLogin updates the last login timestamp for a user
func (s *UserService) UpdateLastLogin(ctx context.Context, id string) error {
	now := time.Now()
	return s.db.WithContext(ctx).Model(&models.User{}).Where("id = ?", id).Update("last_login", now).Error
}
