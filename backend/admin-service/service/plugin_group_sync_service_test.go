package service

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// Test models that work with SQLite (no UUID generation)
type TestSyncUser struct {
	ID        string `gorm:"primaryKey"`
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time       `gorm:"index"`
	Username  string           `gorm:"uniqueIndex;not null"`
	Email     string           `gorm:"uniqueIndex;not null"`
	Groups    []*TestSyncGroup `gorm:"many2many:user_groups;foreignKey:ID;joinForeignKey:user_id;References:ID;joinReferences:group_id"`
}

type TestSyncGroup struct {
	ID        string `gorm:"primaryKey"`
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time      `gorm:"index"`
	Name      string          `gorm:"uniqueIndex;not null"`
	Source    string          `gorm:"not null"`
	Users     []*TestSyncUser `gorm:"many2many:user_groups;foreignKey:ID;joinForeignKey:group_id;References:ID;joinReferences:user_id"`
}

type TestSyncProject struct {
	ID          string `gorm:"primaryKey"`
	CreatedAt   time.Time
	UpdatedAt   time.Time
	DeletedAt   *time.Time `gorm:"index"`
	Name        string     `gorm:"not null"`
	Description string
}

func (TestSyncUser) TableName() string    { return "users" }
func (TestSyncGroup) TableName() string   { return "groups" }
func (TestSyncProject) TableName() string { return "projects" }

func setupPluginGroupSyncTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// For SQLite, we need to handle UUID generation differently
	db.Exec("PRAGMA foreign_keys = ON")

	// Migrate all tables using test models
	err = db.AutoMigrate(
		&TestSyncUser{},
		&TestSyncGroup{},
		&TestSyncProject{},
		&models.Configuration{},
		&models.PluginGroupAccess{},
	)
	require.NoError(t, err)

	// Drop and recreate join tables with correct column names
	db.Exec(`DROP TABLE IF EXISTS user_groups`)

	db.Exec(`CREATE TABLE user_groups (
		user_id TEXT NOT NULL,
		group_id TEXT NOT NULL,
		PRIMARY KEY (user_id, group_id)
	)`)

	return db
}

func createPluginGroupSyncTestData(t *testing.T, db *gorm.DB) (string, string, string) {
	// Create groups using test models
	devGroup := TestSyncGroup{
		ID:        "dev-group",
		Name:      "project-a-developers",
		Source:    "local",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	opsGroup := TestSyncGroup{
		ID:        "ops-group",
		Name:      "project-a-operators",
		Source:    "local",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	viewerGroup := TestSyncGroup{
		ID:        "viewer-group",
		Name:      "project-a-viewers",
		Source:    "local",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	require.NoError(t, db.Create(&devGroup).Error)
	require.NoError(t, db.Create(&opsGroup).Error)
	require.NoError(t, db.Create(&viewerGroup).Error)

	// Create users
	devUser := TestSyncUser{
		ID:        "dev-user",
		Username:  "developer",
		Email:     "<EMAIL>",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Groups:    []*TestSyncGroup{&devGroup},
	}
	opsUser := TestSyncUser{
		ID:        "ops-user",
		Username:  "operator",
		Email:     "<EMAIL>",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Groups:    []*TestSyncGroup{&opsGroup},
	}
	viewerUser := TestSyncUser{
		ID:        "viewer-user",
		Username:  "viewer",
		Email:     "<EMAIL>",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Groups:    []*TestSyncGroup{&viewerGroup},
	}

	require.NoError(t, db.Create(&devUser).Error)
	require.NoError(t, db.Create(&opsUser).Error)
	require.NoError(t, db.Create(&viewerUser).Error)

	// Create project
	project := TestSyncProject{
		ID:          "test-project",
		Name:        "Test Project",
		Description: "Test project for plugin group sync",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	require.NoError(t, db.Create(&project).Error)

	return devUser.ID, opsUser.ID, viewerUser.ID
}

func TestPluginGroupSyncService_GetDefaultPluginGroupMappings(t *testing.T) {
	db := setupPluginGroupSyncTestDB(t)
	groupSyncService := &GroupSyncService{} // Mock
	pluginPermissionService := &PluginPermissionService{}
	service := NewPluginGroupSyncService(db, groupSyncService, pluginPermissionService)

	mappings := service.getDefaultPluginGroupMappings()

	assert.NotEmpty(t, mappings)
	assert.Len(t, mappings, 4) // developers, operators, admins, viewers

	// Check developer mapping
	devMapping := mappings[0]
	assert.Equal(t, ".*-developers?", devMapping.GroupPattern)
	assert.Equal(t, "*", devMapping.PluginName)
	assert.Equal(t, "deploy", devMapping.AccessLevel)
	assert.Contains(t, devMapping.AllowedTemplates, "basic-deploy")

	// Check admin mapping
	adminMapping := mappings[2]
	assert.Equal(t, ".*-admins?", adminMapping.GroupPattern)
	assert.Equal(t, "admin", adminMapping.AccessLevel)
}

func TestPluginGroupSyncService_CreatePluginGroupMapping(t *testing.T) {
	db := setupPluginGroupSyncTestDB(t)
	groupSyncService := &GroupSyncService{}
	pluginPermissionService := &PluginPermissionService{}
	service := NewPluginGroupSyncService(db, groupSyncService, pluginPermissionService)

	ctx := context.Background()

	t.Run("Create valid mapping", func(t *testing.T) {
		mapping := &PluginGroupMapping{
			GroupPattern:     "test-developers",
			PluginName:       "test-plugin",
			AccessLevel:      "deploy",
			AllowedProjects:  []string{"project-1", "project-2"},
			AllowedTemplates: []string{"basic-deploy"},
			AllowedProviders: []string{"kubernetes"},
		}

		err := service.CreatePluginGroupMapping(ctx, mapping)
		require.NoError(t, err)

		// Verify mapping was stored
		var config models.Configuration
		err = db.Where("type = ? AND key LIKE ?", "plugin_group_mapping", "%test-plugin%").First(&config).Error
		require.NoError(t, err)

		var storedMapping PluginGroupMapping
		err = json.Unmarshal([]byte(config.Value), &storedMapping)
		require.NoError(t, err)
		assert.Equal(t, mapping.GroupPattern, storedMapping.GroupPattern)
		assert.Equal(t, mapping.PluginName, storedMapping.PluginName)
		assert.Equal(t, mapping.AccessLevel, storedMapping.AccessLevel)
	})

	t.Run("Create invalid mapping", func(t *testing.T) {
		mapping := &PluginGroupMapping{
			GroupPattern: "", // Invalid: empty pattern
			PluginName:   "test-plugin",
			AccessLevel:  "deploy",
		}

		err := service.CreatePluginGroupMapping(ctx, mapping)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "group pattern is required")
	})

	t.Run("Create mapping with invalid access level", func(t *testing.T) {
		mapping := &PluginGroupMapping{
			GroupPattern: "test-group",
			PluginName:   "test-plugin",
			AccessLevel:  "invalid-level",
		}

		err := service.CreatePluginGroupMapping(ctx, mapping)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid access level")
	})
}

func TestPluginGroupSyncService_ApplyPluginAccess(t *testing.T) {
	db := setupPluginGroupSyncTestDB(t)
	groupSyncService := &GroupSyncService{}
	pluginPermissionService := &PluginPermissionService{}
	service := NewPluginGroupSyncService(db, groupSyncService, pluginPermissionService)

	devUserID, _, _ := createPluginGroupSyncTestData(t, db)
	ctx := context.Background()

	// Get user and group
	var user models.User
	require.NoError(t, db.Preload("Groups").Where("id = ?", devUserID).First(&user).Error)
	group := user.Groups[0]

	t.Run("Apply deploy access level", func(t *testing.T) {
		mapping := &PluginGroupMapping{
			GroupPattern:     ".*-developers",
			PluginName:       "test-plugin",
			AccessLevel:      "deploy",
			AllowedProjects:  []string{"project-1"},
			AllowedTemplates: []string{"basic-deploy"},
			AllowedProviders: []string{"kubernetes"},
		}

		err := service.applyPluginAccess(ctx, &user, group, mapping)
		require.NoError(t, err)

		// Verify plugin access was created
		var pluginAccess models.PluginGroupAccess
		err = db.Where("group_id = ? AND plugin_name = ?", group.ID, "test-plugin").First(&pluginAccess).Error
		require.NoError(t, err)

		assert.True(t, pluginAccess.CanView)
		assert.False(t, pluginAccess.CanInstall)
		assert.False(t, pluginAccess.CanConfigure)
		assert.False(t, pluginAccess.CanManage)
		assert.True(t, pluginAccess.CanDeploy)

		// Check allowed resources
		var allowedTemplates []string
		err = json.Unmarshal([]byte(pluginAccess.AllowedTemplates), &allowedTemplates)
		require.NoError(t, err)
		assert.Contains(t, allowedTemplates, "basic-deploy")
	})

	t.Run("Apply manage access level", func(t *testing.T) {
		mapping := &PluginGroupMapping{
			GroupPattern:     ".*-operators",
			PluginName:       "test-plugin-2",
			AccessLevel:      "manage",
			AllowedProjects:  []string{"*"},
			AllowedTemplates: []string{"*"},
			AllowedProviders: []string{"*"},
		}

		err := service.applyPluginAccess(ctx, &user, group, mapping)
		require.NoError(t, err)

		// Verify plugin access was created
		var pluginAccess models.PluginGroupAccess
		err = db.Where("group_id = ? AND plugin_name = ?", group.ID, "test-plugin-2").First(&pluginAccess).Error
		require.NoError(t, err)

		assert.True(t, pluginAccess.CanView)
		assert.False(t, pluginAccess.CanInstall)
		assert.True(t, pluginAccess.CanConfigure)
		assert.True(t, pluginAccess.CanManage)
		assert.True(t, pluginAccess.CanDeploy)
	})

	t.Run("Apply admin access level", func(t *testing.T) {
		mapping := &PluginGroupMapping{
			GroupPattern:     ".*-admins",
			PluginName:       "test-plugin-3",
			AccessLevel:      "admin",
			AllowedProjects:  []string{"*"},
			AllowedTemplates: []string{"*"},
			AllowedProviders: []string{"*"},
		}

		err := service.applyPluginAccess(ctx, &user, group, mapping)
		require.NoError(t, err)

		// Verify plugin access was created
		var pluginAccess models.PluginGroupAccess
		err = db.Where("group_id = ? AND plugin_name = ?", group.ID, "test-plugin-3").First(&pluginAccess).Error
		require.NoError(t, err)

		assert.True(t, pluginAccess.CanView)
		assert.True(t, pluginAccess.CanInstall)
		assert.True(t, pluginAccess.CanConfigure)
		assert.True(t, pluginAccess.CanManage)
		assert.True(t, pluginAccess.CanDeploy)
	})

	t.Run("Apply view access level", func(t *testing.T) {
		mapping := &PluginGroupMapping{
			GroupPattern:     ".*-viewers",
			PluginName:       "test-plugin-4",
			AccessLevel:      "view",
			AllowedProjects:  []string{},
			AllowedTemplates: []string{},
			AllowedProviders: []string{},
		}

		err := service.applyPluginAccess(ctx, &user, group, mapping)
		require.NoError(t, err)

		// Verify plugin access was created
		var pluginAccess models.PluginGroupAccess
		err = db.Where("group_id = ? AND plugin_name = ?", group.ID, "test-plugin-4").First(&pluginAccess).Error
		require.NoError(t, err)

		assert.True(t, pluginAccess.CanView)
		assert.False(t, pluginAccess.CanInstall)
		assert.False(t, pluginAccess.CanConfigure)
		assert.False(t, pluginAccess.CanManage)
		assert.False(t, pluginAccess.CanDeploy)
	})
}

func TestPluginGroupSyncService_SyncPluginGroupAccess(t *testing.T) {
	db := setupPluginGroupSyncTestDB(t)
	groupSyncService := &GroupSyncService{}
	pluginPermissionService := &PluginPermissionService{}
	service := NewPluginGroupSyncService(db, groupSyncService, pluginPermissionService)

	devUserID, _, _ := createPluginGroupSyncTestData(t, db)
	ctx := context.Background()

	t.Run("Sync user plugin access", func(t *testing.T) {
		err := service.SyncPluginGroupAccess(ctx, devUserID)
		require.NoError(t, err)

		// Verify plugin access was created based on default mappings
		var pluginAccesses []models.PluginGroupAccess
		err = db.Where("group_id = ?", "dev-group").Find(&pluginAccesses).Error
		require.NoError(t, err)

		// Should have created access for the developer group based on default mappings
		assert.NotEmpty(t, pluginAccesses)
	})
}

func TestPluginGroupSyncService_GetUserPluginAccess(t *testing.T) {
	db := setupPluginGroupSyncTestDB(t)
	groupSyncService := &GroupSyncService{}
	pluginPermissionService := &PluginPermissionService{}
	service := NewPluginGroupSyncService(db, groupSyncService, pluginPermissionService)

	devUserID, _, _ := createPluginGroupSyncTestData(t, db)
	ctx := context.Background()

	// Create some plugin access records
	pluginAccess1 := models.PluginGroupAccess{
		GroupID:    "dev-group",
		PluginName: "plugin-1",
		CanView:    true,
		CanDeploy:  true,
	}
	pluginAccess2 := models.PluginGroupAccess{
		GroupID:      "dev-group",
		PluginName:   "plugin-2",
		CanView:      true,
		CanConfigure: true,
		CanManage:    true,
		CanDeploy:    true,
	}

	require.NoError(t, db.Create(&pluginAccess1).Error)
	require.NoError(t, db.Create(&pluginAccess2).Error)

	t.Run("Get user plugin access", func(t *testing.T) {
		accessMap, err := service.GetUserPluginAccess(ctx, devUserID)
		require.NoError(t, err)
		assert.Len(t, accessMap, 2)

		// Check plugin-1 access
		if access, exists := accessMap["plugin-1"]; exists {
			assert.True(t, access.CanView)
			assert.True(t, access.CanDeploy)
			assert.False(t, access.CanManage)
		}

		// Check plugin-2 access
		if access, exists := accessMap["plugin-2"]; exists {
			assert.True(t, access.CanView)
			assert.True(t, access.CanConfigure)
			assert.True(t, access.CanManage)
			assert.True(t, access.CanDeploy)
		}
	})
}

func TestPluginGroupSyncService_IsHigherAccess(t *testing.T) {
	db := setupPluginGroupSyncTestDB(t)
	groupSyncService := &GroupSyncService{}
	pluginPermissionService := &PluginPermissionService{}
	service := NewPluginGroupSyncService(db, groupSyncService, pluginPermissionService)

	t.Run("Compare access levels", func(t *testing.T) {
		viewAccess := &models.PluginGroupAccess{
			CanView: true,
		}

		deployAccess := &models.PluginGroupAccess{
			CanView:   true,
			CanDeploy: true,
		}

		manageAccess := &models.PluginGroupAccess{
			CanView:      true,
			CanDeploy:    true,
			CanConfigure: true,
			CanManage:    true,
		}

		adminAccess := &models.PluginGroupAccess{
			CanView:      true,
			CanDeploy:    true,
			CanConfigure: true,
			CanManage:    true,
			CanInstall:   true,
		}

		// Test higher access comparisons
		assert.True(t, service.isHigherAccess(deployAccess, viewAccess))
		assert.True(t, service.isHigherAccess(manageAccess, deployAccess))
		assert.True(t, service.isHigherAccess(adminAccess, manageAccess))

		// Test lower access comparisons
		assert.False(t, service.isHigherAccess(viewAccess, deployAccess))
		assert.False(t, service.isHigherAccess(deployAccess, manageAccess))
		assert.False(t, service.isHigherAccess(manageAccess, adminAccess))

		// Test equal access
		assert.False(t, service.isHigherAccess(viewAccess, viewAccess))
	})
}

func TestPluginGroupSyncService_MatchesPattern(t *testing.T) {
	db := setupPluginGroupSyncTestDB(t)
	groupSyncService := &GroupSyncService{}
	pluginPermissionService := &PluginPermissionService{}
	service := NewPluginGroupSyncService(db, groupSyncService, pluginPermissionService)

	t.Run("Pattern matching", func(t *testing.T) {
		// Test wildcard pattern
		matched, err := service.matchesPattern("any-group", "*")
		require.NoError(t, err)
		assert.True(t, matched)

		// Test substring matching
		matched, err = service.matchesPattern("project-a-developers", "developers")
		require.NoError(t, err)
		assert.True(t, matched)

		matched, err = service.matchesPattern("project-a-operators", "developers")
		require.NoError(t, err)
		assert.False(t, matched)

		// Test case insensitive matching
		matched, err = service.matchesPattern("Project-A-Developers", "developers")
		require.NoError(t, err)
		assert.True(t, matched)
	})
}
