package service

import (
	"context"
	"testing"
	"time"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// Test models that work with SQLite (no UUID generation)
type TestUser struct {
	ID             string `gorm:"primaryKey"`
	CreatedAt      time.Time
	UpdatedAt      time.Time
	DeletedAt      *time.Time   `gorm:"index"`
	Username       string       `gorm:"uniqueIndex;not null"`
	Email          string       `gorm:"uniqueIndex;not null"`
	HashedPassword string       `gorm:"not null"`
	IsAdmin        bool         `gorm:"default:false"`
	IsActive       bool         `gorm:"default:true"`
	Roles          []*TestRole  `gorm:"many2many:user_roles;foreignKey:ID;joinForeignKey:user_id;References:ID;joinReferences:role_id"`
	Groups         []*TestGroup `gorm:"many2many:user_groups;foreignKey:ID;joinForeignKey:user_id;References:ID;joinReferences:group_id"`
}

type TestRole struct {
	ID          string `gorm:"primaryKey"`
	CreatedAt   time.Time
	UpdatedAt   time.Time
	DeletedAt   *time.Time `gorm:"index"`
	Name        string     `gorm:"uniqueIndex;not null"`
	Description string
	Users       []*TestUser `gorm:"many2many:user_roles;"`
}

type TestGroup struct {
	ID        string `gorm:"primaryKey"`
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time  `gorm:"index"`
	Name      string      `gorm:"uniqueIndex;not null"`
	Source    string      `gorm:"not null"`
	Users     []*TestUser `gorm:"many2many:user_groups;"`
}

type TestProject struct {
	ID          string `gorm:"primaryKey"`
	CreatedAt   time.Time
	UpdatedAt   time.Time
	DeletedAt   *time.Time `gorm:"index"`
	Name        string     `gorm:"not null"`
	Description string
}

func (TestUser) TableName() string    { return "users" }
func (TestRole) TableName() string    { return "roles" }
func (TestGroup) TableName() string   { return "groups" }
func (TestProject) TableName() string { return "projects" }

func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// For SQLite, we need to handle UUID generation differently
	// Let's use string IDs for testing
	db.Exec("PRAGMA foreign_keys = ON")

	// Migrate all tables using test models
	err = db.AutoMigrate(
		&TestUser{},
		&TestRole{},
		&TestGroup{},
		&TestProject{},
		&models.PluginPermission{},
		&models.PluginRolePermission{},
		&models.PluginGroupPermission{},
		&models.PluginUserPermission{},
		&models.PluginAccessControl{},
		&models.PluginAuditLog{},
		&models.PluginGroupAccess{},
	)
	require.NoError(t, err)

	// Drop and recreate join tables with correct column names
	db.Exec(`DROP TABLE IF EXISTS user_roles`)
	db.Exec(`DROP TABLE IF EXISTS user_groups`)

	db.Exec(`CREATE TABLE user_roles (
		user_id TEXT NOT NULL,
		role_id TEXT NOT NULL,
		PRIMARY KEY (user_id, role_id)
	)`)

	db.Exec(`CREATE TABLE user_groups (
		user_id TEXT NOT NULL,
		group_id TEXT NOT NULL,
		PRIMARY KEY (user_id, group_id)
	)`)

	return db
}

func createTestData(t *testing.T, db *gorm.DB) (string, string, string) {
	// Create roles using test models
	adminRole := TestRole{
		ID:          "admin",
		Name:        "admin",
		Description: "Administrator",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	developerRole := TestRole{
		ID:          "developer",
		Name:        "developer",
		Description: "Developer",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	require.NoError(t, db.Create(&adminRole).Error)
	require.NoError(t, db.Create(&developerRole).Error)

	// Create groups
	devGroup := TestGroup{
		ID:        "dev-group",
		Name:      "developers",
		Source:    "local",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	require.NoError(t, db.Create(&devGroup).Error)

	// Create users
	adminUser := TestUser{
		ID:             "admin-user",
		Username:       "admin",
		Email:          "<EMAIL>",
		HashedPassword: "hashed",
		IsAdmin:        true,
		IsActive:       true,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
		Roles:          []*TestRole{&adminRole},
	}
	devUser := TestUser{
		ID:             "dev-user",
		Username:       "developer",
		Email:          "<EMAIL>",
		HashedPassword: "hashed",
		IsAdmin:        false,
		IsActive:       true,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
		Roles:          []*TestRole{&developerRole},
		Groups:         []*TestGroup{&devGroup},
	}
	require.NoError(t, db.Create(&adminUser).Error)
	require.NoError(t, db.Create(&devUser).Error)

	// Create project
	project := TestProject{
		ID:          "test-project",
		Name:        "Test Project",
		Description: "Test project for plugin permissions",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	require.NoError(t, db.Create(&project).Error)

	// Create plugin permissions
	permissions := []models.PluginPermission{
		{
			ID:          "plugin:view",
			Name:        "View Plugins",
			Description: "View installed plugins",
			Category:    "plugin",
			Resource:    "*",
			Action:      "view",
			Scope:       "global",
		},
		{
			ID:          "plugin:install",
			Name:        "Install Plugins",
			Description: "Install new plugins",
			Category:    "plugin",
			Resource:    "*",
			Action:      "install",
			Scope:       "global",
		},
		{
			ID:          "provider:deploy",
			Name:        "Deploy with Providers",
			Description: "Deploy using providers",
			Category:    "provider",
			Resource:    "*",
			Action:      "deploy",
			Scope:       "environment",
		},
	}

	for _, perm := range permissions {
		require.NoError(t, db.Create(&perm).Error)
	}

	// Create role permissions
	rolePermissions := []models.PluginRolePermission{
		{RoleID: "admin", PermissionID: "plugin:view"},
		{RoleID: "admin", PermissionID: "plugin:install"},
		{RoleID: "admin", PermissionID: "provider:deploy"},
		{RoleID: "developer", PermissionID: "plugin:view"},
		{RoleID: "developer", PermissionID: "provider:deploy"},
	}

	for _, rp := range rolePermissions {
		require.NoError(t, db.Create(&rp).Error)
	}

	// Create plugin access control
	pluginAccessControl := models.PluginAccessControl{
		PluginName:          "openshift-plugin",
		RequiredPermissions: `[{"action":"view","permission":"plugin:view","scope":"global"}]`,
		PublicFeatures:      `["info","capabilities"]`,
		AdminOnlyFeatures:   `["install","uninstall"]`,
	}
	require.NoError(t, db.Create(&pluginAccessControl).Error)

	// Create plugin group access
	pluginGroupAccess := models.PluginGroupAccess{
		GroupID:             "dev-group",
		PluginName:          "openshift-plugin",
		CanView:             true,
		CanDeploy:           true,
		AllowedProjects:     `["test-project"]`,
		AllowedTemplates:    `["basic-deploy"]`,
		AllowedProviders:    `["openshift"]`,
		AllowedEnvironments: `["development","staging"]`,
	}
	require.NoError(t, db.Create(&pluginGroupAccess).Error)

	return "admin-user", "dev-user", "test-project"
}

func TestPluginPermissionService_GetUserPermissionMatrix(t *testing.T) {
	db := setupTestDB(t)
	service := NewPluginPermissionService(db)
	adminUserID, devUserID, _ := createTestData(t, db)

	ctx := context.Background()

	t.Run("Admin user should have full access", func(t *testing.T) {
		matrix, err := service.GetUserPermissionMatrix(ctx, adminUserID)
		require.NoError(t, err)
		assert.NotNil(t, matrix)
		assert.Equal(t, adminUserID, matrix.UserID)

		// Admin should have access to openshift-plugin
		if pluginAccess, exists := matrix.Plugins["openshift-plugin"]; exists {
			assert.True(t, pluginAccess.CanView)
			assert.True(t, pluginAccess.CanInstall)
			assert.True(t, pluginAccess.CanConfigure)
			assert.True(t, pluginAccess.CanManage)
			assert.True(t, pluginAccess.CanDeploy)
		}
	})

	t.Run("Developer user should have limited access", func(t *testing.T) {
		matrix, err := service.GetUserPermissionMatrix(ctx, devUserID)
		require.NoError(t, err)
		assert.NotNil(t, matrix)
		assert.Equal(t, devUserID, matrix.UserID)

		// Developer should have limited access to openshift-plugin
		if pluginAccess, exists := matrix.Plugins["openshift-plugin"]; exists {
			assert.True(t, pluginAccess.CanView)
			assert.False(t, pluginAccess.CanInstall)
			assert.False(t, pluginAccess.CanConfigure)
			assert.False(t, pluginAccess.CanManage)
			assert.True(t, pluginAccess.CanDeploy)
		}
	})

	t.Run("Non-existent user should return error", func(t *testing.T) {
		_, err := service.GetUserPermissionMatrix(ctx, "non-existent")
		assert.Error(t, err)
	})
}

func TestPluginPermissionService_GetUserSecurityContext(t *testing.T) {
	db := setupTestDB(t)
	service := NewPluginPermissionService(db)
	adminUserID, devUserID, _ := createTestData(t, db)

	ctx := context.Background()

	t.Run("Admin user security context", func(t *testing.T) {
		securityContext, err := service.GetUserSecurityContext(ctx, adminUserID)
		require.NoError(t, err)
		assert.NotNil(t, securityContext)
		assert.Equal(t, adminUserID, securityContext.UserID)
		assert.Contains(t, securityContext.UserRoles, "admin")
		assert.Contains(t, securityContext.Permissions, "plugin:view")
		assert.Contains(t, securityContext.Permissions, "plugin:install")
	})

	t.Run("Developer user security context", func(t *testing.T) {
		securityContext, err := service.GetUserSecurityContext(ctx, devUserID)
		require.NoError(t, err)
		assert.NotNil(t, securityContext)
		assert.Equal(t, devUserID, securityContext.UserID)
		assert.Contains(t, securityContext.UserRoles, "developer")
		assert.Contains(t, securityContext.UserGroups, "developers")
		assert.Contains(t, securityContext.Permissions, "plugin:view")
		assert.NotContains(t, securityContext.Permissions, "plugin:install")
	})
}

func TestPluginPermissionService_CheckPermission(t *testing.T) {
	db := setupTestDB(t)
	service := NewPluginPermissionService(db)
	adminUserID, devUserID, projectID := createTestData(t, db)

	ctx := context.Background()

	t.Run("Admin can install plugins", func(t *testing.T) {
		request := &PermissionCheckRequest{
			Action:       "install",
			Resource:     "openshift-plugin",
			ResourceType: "plugin",
		}

		response, err := service.CheckPermission(ctx, adminUserID, request)
		require.NoError(t, err)
		assert.True(t, response.Allowed)
		assert.Contains(t, response.GrantedPermissions, "admin")
	})

	t.Run("Developer cannot install plugins", func(t *testing.T) {
		request := &PermissionCheckRequest{
			Action:       "install",
			Resource:     "openshift-plugin",
			ResourceType: "plugin",
		}

		response, err := service.CheckPermission(ctx, devUserID, request)
		require.NoError(t, err)
		assert.False(t, response.Allowed)
		assert.Contains(t, response.Reason, "Missing required permission")
	})

	t.Run("Developer can view plugins", func(t *testing.T) {
		request := &PermissionCheckRequest{
			Action:       "view",
			Resource:     "openshift-plugin",
			ResourceType: "plugin",
		}

		response, err := service.CheckPermission(ctx, devUserID, request)
		require.NoError(t, err)
		assert.True(t, response.Allowed)
		assert.Contains(t, response.GrantedPermissions, "role")
	})

	t.Run("Developer can deploy with scope", func(t *testing.T) {
		request := &PermissionCheckRequest{
			Action:       "deploy",
			Resource:     "openshift-provider",
			ResourceType: "provider",
			Scope: &PermissionScope{
				ProjectID: &projectID,
			},
		}

		response, err := service.CheckPermission(ctx, devUserID, request)
		require.NoError(t, err)
		assert.True(t, response.Allowed)
	})
}

func TestPluginPermissionService_LogPluginAction(t *testing.T) {
	db := setupTestDB(t)
	service := NewPluginPermissionService(db)
	_, devUserID, projectID := createTestData(t, db)

	ctx := context.WithValue(context.Background(), "client_ip", "***********")
	ctx = context.WithValue(ctx, "user_agent", "test-agent")

	t.Run("Log plugin action successfully", func(t *testing.T) {
		details := map[string]interface{}{
			"pluginName":    "openshift-plugin",
			"projectId":     projectID,
			"environmentId": "dev-env",
			"success":       true,
		}

		err := service.LogPluginAction(ctx, devUserID, "deploy", "openshift-plugin", details)
		require.NoError(t, err)

		// Verify audit log was created
		var auditLog models.PluginAuditLog
		err = db.Where("user_id = ? AND action = ? AND resource = ?", devUserID, "deploy", "openshift-plugin").First(&auditLog).Error
		require.NoError(t, err)
		assert.Equal(t, "deploy", auditLog.Action)
		assert.Equal(t, "openshift-plugin", auditLog.Resource)
		assert.Equal(t, "plugin", auditLog.ResourceType)
		assert.Equal(t, "***********", auditLog.IPAddress)
		assert.Equal(t, "test-agent", auditLog.UserAgent)
	})
}

func TestPluginPermissionService_GetPluginAuditLogs(t *testing.T) {
	db := setupTestDB(t)
	service := NewPluginPermissionService(db)
	_, devUserID, _ := createTestData(t, db)

	ctx := context.Background()

	// Create some audit logs
	auditLogs := []models.PluginAuditLog{
		{
			UserID:       devUserID,
			UserName:     "developer",
			Action:       "view",
			Resource:     "openshift-plugin",
			ResourceType: "plugin",
			Details:      `{"success":true}`,
			Timestamp:    time.Now(),
			IPAddress:    "***********",
		},
		{
			UserID:       devUserID,
			UserName:     "developer",
			Action:       "deploy",
			Resource:     "openshift-plugin",
			ResourceType: "plugin",
			Details:      `{"success":true}`,
			Timestamp:    time.Now(),
			IPAddress:    "***********",
		},
	}

	for _, log := range auditLogs {
		require.NoError(t, db.Create(&log).Error)
	}

	t.Run("Get all audit logs", func(t *testing.T) {
		logs, err := service.GetPluginAuditLogs(ctx, nil, nil, 100)
		require.NoError(t, err)
		assert.Len(t, logs, 2)
	})

	t.Run("Get audit logs for specific plugin", func(t *testing.T) {
		pluginName := "openshift-plugin"
		logs, err := service.GetPluginAuditLogs(ctx, &pluginName, nil, 100)
		require.NoError(t, err)
		assert.Len(t, logs, 2)
		for _, log := range logs {
			assert.Contains(t, log.Resource, "openshift-plugin")
		}
	})

	t.Run("Get audit logs for specific user", func(t *testing.T) {
		logs, err := service.GetPluginAuditLogs(ctx, nil, &devUserID, 100)
		require.NoError(t, err)
		assert.Len(t, logs, 2)
		for _, log := range logs {
			assert.Equal(t, devUserID, log.UserID)
		}
	})

	t.Run("Limit audit logs", func(t *testing.T) {
		logs, err := service.GetPluginAuditLogs(ctx, nil, nil, 1)
		require.NoError(t, err)
		assert.Len(t, logs, 1)
	})
}
