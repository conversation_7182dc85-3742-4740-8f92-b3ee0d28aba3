package service

import (
	"context"
	"errors"
	"fmt"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ProjectService handles project-related operations
type ProjectService struct {
	DB *gorm.DB
}

// NewProjectService creates a new project service
func NewProjectService(db *gorm.DB) *ProjectService {
	return &ProjectService{DB: db}
}

// CreateProject creates a new project
func (s *ProjectService) CreateProject(ctx context.Context, project *models.Project) error {
	// Generate a UUID if not provided
	if project.ID == "" {
		project.ID = uuid.New().String()
	}

	// Create the project
	if err := s.DB.Create(project).Error; err != nil {
		return fmt.Errorf("failed to create project: %w", err)
	}

	return nil
}

// GetProject retrieves a project by ID
func (s *ProjectService) GetProject(ctx context.Context, id string) (*models.Project, error) {
	var project models.Project
	if err := s.DB.First(&project, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("project not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get project: %w", err)
	}

	return &project, nil
}

// GetProjectWithGroups retrieves a project by ID with its associated groups
func (s *ProjectService) GetProjectWithGroups(ctx context.Context, id string) (*models.Project, error) {
	var project models.Project
	if err := s.DB.Preload("Groups").First(&project, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("project not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get project: %w", err)
	}

	return &project, nil
}

// ListProjects retrieves all projects
func (s *ProjectService) ListProjects(ctx context.Context) ([]models.Project, error) {
	var projects []models.Project
	if err := s.DB.Find(&projects).Error; err != nil {
		return nil, fmt.Errorf("failed to list projects: %w", err)
	}

	return projects, nil
}

// UpdateProject updates a project
func (s *ProjectService) UpdateProject(ctx context.Context, project *models.Project) error {
	// Check if the project exists
	var existingProject models.Project
	if err := s.DB.First(&existingProject, "id = ?", project.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("project not found: %w", err)
		}
		return fmt.Errorf("failed to get project: %w", err)
	}

	// Update the project
	if err := s.DB.Model(&existingProject).Updates(project).Error; err != nil {
		return fmt.Errorf("failed to update project: %w", err)
	}

	return nil
}

// DeleteProject deletes a project
func (s *ProjectService) DeleteProject(ctx context.Context, id string) error {
	// Check if the project exists
	var project models.Project
	if err := s.DB.First(&project, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("project not found: %w", err)
		}
		return fmt.Errorf("failed to get project: %w", err)
	}

	// Delete the project
	if err := s.DB.Unscoped().Delete(&project).Error; err != nil {
		return fmt.Errorf("failed to delete project: %w", err)
	}

	return nil
}

// AssignGroupToProject assigns a group to a project
func (s *ProjectService) AssignGroupToProject(ctx context.Context, projectID, groupID string) error {
	// Check if the project exists
	var project models.Project
	if err := s.DB.First(&project, "id = ?", projectID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("project not found: %w", err)
		}
		return fmt.Errorf("failed to get project: %w", err)
	}

	// Check if the group exists
	var group models.Group
	if err := s.DB.First(&group, "id = ?", groupID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("group not found: %w", err)
		}
		return fmt.Errorf("failed to get group: %w", err)
	}

	// Assign the group to the project
	if err := s.DB.Model(&project).Association("Groups").Append(&group); err != nil {
		return fmt.Errorf("failed to assign group to project: %w", err)
	}

	return nil
}

// RemoveGroupFromProject removes a group from a project
func (s *ProjectService) RemoveGroupFromProject(ctx context.Context, projectID, groupID string) error {
	// Check if the project exists
	var project models.Project
	if err := s.DB.First(&project, "id = ?", projectID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("project not found: %w", err)
		}
		return fmt.Errorf("failed to get project: %w", err)
	}

	// Check if the group exists
	var group models.Group
	if err := s.DB.First(&group, "id = ?", groupID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("group not found: %w", err)
		}
		return fmt.Errorf("failed to get group: %w", err)
	}

	// Remove the group from the project
	if err := s.DB.Model(&project).Association("Groups").Unscoped().Delete(&group); err != nil {
		return fmt.Errorf("failed to remove group from project: %w", err)
	}

	return nil
}

// GetProjectsForUser retrieves all projects that a user has access to
func (s *ProjectService) GetProjectsForUser(ctx context.Context, userID string) ([]models.Project, error) {
	var projects []models.Project

	// Get all groups that the user belongs to
	var user models.User
	if err := s.DB.Preload("Groups").First(&user, "id = ?", userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// If the user is an admin, return all projects
	if user.IsAdmin {
		if err := s.DB.Find(&projects).Error; err != nil {
			return nil, fmt.Errorf("failed to list projects: %w", err)
		}
		return projects, nil
	}

	// Get all group IDs that the user belongs to
	var groupIDs []string
	for _, group := range user.Groups {
		groupIDs = append(groupIDs, group.ID)
	}

	// If the user doesn't belong to any groups, return an empty slice
	if len(groupIDs) == 0 {
		return []models.Project{}, nil
	}

	// Get all projects that have any of these groups
	if err := s.DB.Distinct().Joins("JOIN project_groups ON projects.id = project_groups.project_id").
		Where("project_groups.group_id IN ?", groupIDs).
		Find(&projects).Error; err != nil {
		return nil, fmt.Errorf("failed to list projects for user: %w", err)
	}

	return projects, nil
}

// GetGroupsForProject retrieves all groups assigned to a project
func (s *ProjectService) GetGroupsForProject(ctx context.Context, projectID string) ([]models.Group, error) {
	var project models.Project
	if err := s.DB.Preload("Groups").First(&project, "id = ?", projectID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("project not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get project: %w", err)
	}

	groups := make([]models.Group, len(project.Groups))
	for i, g := range project.Groups {
		groups[i] = *g
	}

	return groups, nil
}
