package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"strings"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"gorm.io/gorm"
)

// PluginGroupSyncService handles synchronization of plugin permissions with identity provider groups
type PluginGroupSyncService struct {
	db                      *gorm.DB
	groupSyncService        *GroupSyncService
	pluginPermissionService *PluginPermissionService
}

// NewPluginGroupSyncService creates a new plugin group sync service
func NewPluginGroupSyncService(
	db *gorm.DB,
	groupSyncService *GroupSyncService,
	pluginPermissionService *PluginPermissionService,
) *PluginGroupSyncService {
	return &PluginGroupSyncService{
		db:                      db,
		groupSyncService:        groupSyncService,
		pluginPermissionService: pluginPermissionService,
	}
}

// PluginGroupSyncResult represents the result of plugin group synchronization
type PluginGroupSyncResult struct {
	GroupsProcessed     int      `json:"groupsProcessed"`
	PluginAccessCreated int      `json:"pluginAccessCreated"`
	PluginAccessUpdated int      `json:"pluginAccessUpdated"`
	PermissionsGranted  int      `json:"permissionsGranted"`
	ProjectAssignments  int      `json:"projectAssignments"`
	Errors              []string `json:"errors"`
}

// PluginGroupMapping represents mapping configuration for plugin access based on groups
type PluginGroupMapping struct {
	GroupPattern     string                 `json:"groupPattern"`     // Regex pattern to match group names
	PluginName       string                 `json:"pluginName"`       // Target plugin name
	AccessLevel      string                 `json:"accessLevel"`      // view, deploy, configure, manage
	AllowedProjects  []string               `json:"allowedProjects"`  // Project IDs or patterns
	AllowedTemplates []string               `json:"allowedTemplates"` // Template IDs or patterns
	AllowedProviders []string               `json:"allowedProviders"` // Provider types
	Conditions       map[string]interface{} `json:"conditions"`       // Additional conditions
}

// SyncPluginGroupAccess synchronizes plugin access based on group memberships
func (s *PluginGroupSyncService) SyncPluginGroupAccess(ctx context.Context, userID string) error {
	// Get user with groups
	var user models.User
	if err := s.db.Preload("Groups").Where("id = ?", userID).First(&user).Error; err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Get plugin group mappings from configuration
	mappings, err := s.getPluginGroupMappings(ctx)
	if err != nil {
		return fmt.Errorf("failed to get plugin group mappings: %w", err)
	}

	// Process each group and apply plugin access
	for _, group := range user.Groups {
		if err := s.processGroupForPluginAccess(ctx, &user, group, mappings); err != nil {
			log.Printf("Error processing group %s for user %s: %v", group.Name, user.Username, err)
		}
	}

	return nil
}

// SyncAllUsersPluginAccess synchronizes plugin access for all users
func (s *PluginGroupSyncService) SyncAllUsersPluginAccess(ctx context.Context, dryRun bool) (*PluginGroupSyncResult, error) {
	result := &PluginGroupSyncResult{}

	// Get all users with their groups
	var users []models.User
	if err := s.db.Preload("Groups").Find(&users).Error; err != nil {
		return nil, fmt.Errorf("failed to get users: %w", err)
	}

	// Get plugin group mappings
	mappings, err := s.getPluginGroupMappings(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get plugin group mappings: %w", err)
	}

	// Start transaction if not dry run
	var tx *gorm.DB
	if !dryRun {
		tx = s.db.Begin()
		if tx.Error != nil {
			return nil, fmt.Errorf("failed to start transaction: %w", tx.Error)
		}
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
	}

	// Process each user
	for _, user := range users {
		userResult, err := s.syncUserPluginAccess(ctx, &user, mappings, dryRun)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("user %s: %v", user.Username, err))
			continue
		}

		// Aggregate results
		result.GroupsProcessed += userResult.GroupsProcessed
		result.PluginAccessCreated += userResult.PluginAccessCreated
		result.PluginAccessUpdated += userResult.PluginAccessUpdated
		result.PermissionsGranted += userResult.PermissionsGranted
		result.ProjectAssignments += userResult.ProjectAssignments
	}

	// Commit transaction if not dry run
	if !dryRun && tx != nil {
		if err := tx.Commit().Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("failed to commit transaction: %w", err)
		}
	}

	return result, nil
}

// SyncGroupPluginAccess synchronizes plugin access for a specific group
func (s *PluginGroupSyncService) SyncGroupPluginAccess(ctx context.Context, groupID string, dryRun bool) (*PluginGroupSyncResult, error) {
	result := &PluginGroupSyncResult{}

	// Get group with users
	var group models.Group
	if err := s.db.Preload("Users").Where("id = ?", groupID).First(&group).Error; err != nil {
		return nil, fmt.Errorf("failed to get group: %w", err)
	}

	// Get plugin group mappings
	mappings, err := s.getPluginGroupMappings(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get plugin group mappings: %w", err)
	}

	// Start transaction if not dry run
	var tx *gorm.DB
	if !dryRun {
		tx = s.db.Begin()
		if tx.Error != nil {
			return nil, fmt.Errorf("failed to start transaction: %w", tx.Error)
		}
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
	}

	// Process each user in the group
	for _, user := range group.Users {
		if err := s.processGroupForPluginAccess(ctx, user, &group, mappings); err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("user %s: %v", user.Username, err))
			continue
		}
		result.GroupsProcessed++
	}

	// Commit transaction if not dry run
	if !dryRun && tx != nil {
		if err := tx.Commit().Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("failed to commit transaction: %w", err)
		}
	}

	return result, nil
}

// CreatePluginGroupMapping creates a new plugin group mapping
func (s *PluginGroupSyncService) CreatePluginGroupMapping(ctx context.Context, mapping *PluginGroupMapping) error {
	// Validate the mapping
	if err := s.validatePluginGroupMapping(mapping); err != nil {
		return fmt.Errorf("invalid mapping: %w", err)
	}

	// Convert to JSON and store in a configuration table or file
	// For now, we'll store it in a simple configuration table
	mappingJSON, err := json.Marshal(mapping)
	if err != nil {
		return fmt.Errorf("failed to marshal mapping: %w", err)
	}

	// Store in a configuration table (you might want to create a dedicated table for this)
	config := models.Configuration{
		Key:   fmt.Sprintf("plugin_group_mapping_%s_%s", mapping.PluginName, mapping.GroupPattern),
		Value: string(mappingJSON),
		Type:  "plugin_group_mapping",
	}

	if err := s.db.Create(&config).Error; err != nil {
		return fmt.Errorf("failed to create mapping: %w", err)
	}

	return nil
}

// Helper methods

// getPluginGroupMappings retrieves all plugin group mappings from configuration
func (s *PluginGroupSyncService) getPluginGroupMappings(ctx context.Context) ([]PluginGroupMapping, error) {
	var configs []models.Configuration
	if err := s.db.Where("type = ?", "plugin_group_mapping").Find(&configs).Error; err != nil {
		return nil, fmt.Errorf("failed to get plugin group mappings: %w", err)
	}

	var mappings []PluginGroupMapping
	for _, config := range configs {
		var mapping PluginGroupMapping
		if err := json.Unmarshal([]byte(config.Value), &mapping); err != nil {
			log.Printf("Warning: Failed to unmarshal plugin group mapping %s: %v", config.Key, err)
			continue
		}
		mappings = append(mappings, mapping)
	}

	// If no mappings found in database, return default mappings
	if len(mappings) == 0 {
		return s.getDefaultPluginGroupMappings(), nil
	}

	return mappings, nil
}

// getDefaultPluginGroupMappings returns default plugin group mappings
func (s *PluginGroupSyncService) getDefaultPluginGroupMappings() []PluginGroupMapping {
	return []PluginGroupMapping{
		{
			GroupPattern:     ".*-developers?",
			PluginName:       "*",
			AccessLevel:      "deploy",
			AllowedProjects:  []string{"*"},
			AllowedTemplates: []string{"basic-deploy", "s2i-deploy"},
			AllowedProviders: []string{"*"},
		},
		{
			GroupPattern:     ".*-operators?",
			PluginName:       "*",
			AccessLevel:      "manage",
			AllowedProjects:  []string{"*"},
			AllowedTemplates: []string{"*"},
			AllowedProviders: []string{"*"},
		},
		{
			GroupPattern:     ".*-admins?",
			PluginName:       "*",
			AccessLevel:      "admin",
			AllowedProjects:  []string{"*"},
			AllowedTemplates: []string{"*"},
			AllowedProviders: []string{"*"},
		},
		{
			GroupPattern:     ".*-viewers?",
			PluginName:       "*",
			AccessLevel:      "view",
			AllowedProjects:  []string{"*"},
			AllowedTemplates: []string{},
			AllowedProviders: []string{},
		},
	}
}

// syncUserPluginAccess synchronizes plugin access for a single user
func (s *PluginGroupSyncService) syncUserPluginAccess(ctx context.Context, user *models.User, mappings []PluginGroupMapping, dryRun bool) (*PluginGroupSyncResult, error) {
	result := &PluginGroupSyncResult{}

	for _, group := range user.Groups {
		if err := s.processGroupForPluginAccess(ctx, user, group, mappings); err != nil {
			return nil, err
		}
		result.GroupsProcessed++
	}

	return result, nil
}

// processGroupForPluginAccess processes a group and applies plugin access based on mappings
func (s *PluginGroupSyncService) processGroupForPluginAccess(ctx context.Context, user *models.User, group *models.Group, mappings []PluginGroupMapping) error {
	for _, mapping := range mappings {
		// Check if group matches the pattern
		matched, err := s.matchesPattern(group.Name, mapping.GroupPattern)
		if err != nil {
			log.Printf("Warning: Invalid pattern %s: %v", mapping.GroupPattern, err)
			continue
		}

		if !matched {
			continue
		}

		// Apply plugin access based on mapping
		if err := s.applyPluginAccess(ctx, user, group, &mapping); err != nil {
			return fmt.Errorf("failed to apply plugin access: %w", err)
		}
	}

	return nil
}

// applyPluginAccess applies plugin access based on a mapping
func (s *PluginGroupSyncService) applyPluginAccess(ctx context.Context, user *models.User, group *models.Group, mapping *PluginGroupMapping) error {
	// Get or create plugin group access record
	var pluginAccess models.PluginGroupAccess
	err := s.db.Where("group_id = ? AND plugin_name = ?", group.ID, mapping.PluginName).First(&pluginAccess).Error

	if err == gorm.ErrRecordNotFound {
		// Create new plugin access
		pluginAccess = models.PluginGroupAccess{
			GroupID:    group.ID,
			PluginName: mapping.PluginName,
		}
	} else if err != nil {
		return fmt.Errorf("failed to get plugin access: %w", err)
	}

	// Set access levels based on mapping
	switch strings.ToLower(mapping.AccessLevel) {
	case "admin":
		pluginAccess.CanView = true
		pluginAccess.CanInstall = true
		pluginAccess.CanConfigure = true
		pluginAccess.CanManage = true
		pluginAccess.CanDeploy = true
	case "manage":
		pluginAccess.CanView = true
		pluginAccess.CanInstall = false
		pluginAccess.CanConfigure = true
		pluginAccess.CanManage = true
		pluginAccess.CanDeploy = true
	case "configure":
		pluginAccess.CanView = true
		pluginAccess.CanInstall = false
		pluginAccess.CanConfigure = true
		pluginAccess.CanManage = false
		pluginAccess.CanDeploy = true
	case "deploy":
		pluginAccess.CanView = true
		pluginAccess.CanInstall = false
		pluginAccess.CanConfigure = false
		pluginAccess.CanManage = false
		pluginAccess.CanDeploy = true
	case "view":
		pluginAccess.CanView = true
		pluginAccess.CanInstall = false
		pluginAccess.CanConfigure = false
		pluginAccess.CanManage = false
		pluginAccess.CanDeploy = false
	default:
		return fmt.Errorf("unknown access level: %s", mapping.AccessLevel)
	}

	// Set allowed resources
	if err := pluginAccess.SetAllowedTemplates(mapping.AllowedTemplates); err != nil {
		return fmt.Errorf("failed to set allowed templates: %w", err)
	}

	// Convert and set other allowed resources
	allowedProjects, _ := json.Marshal(mapping.AllowedProjects)
	pluginAccess.AllowedProjects = string(allowedProjects)

	allowedProviders, _ := json.Marshal(mapping.AllowedProviders)
	pluginAccess.AllowedProviders = string(allowedProviders)

	// Save or update the plugin access
	if pluginAccess.ID == 0 {
		if err := s.db.Create(&pluginAccess).Error; err != nil {
			return fmt.Errorf("failed to create plugin access: %w", err)
		}
		log.Printf("Created plugin access for group %s to plugin %s with level %s", group.Name, mapping.PluginName, mapping.AccessLevel)
	} else {
		if err := s.db.Save(&pluginAccess).Error; err != nil {
			return fmt.Errorf("failed to update plugin access: %w", err)
		}
		log.Printf("Updated plugin access for group %s to plugin %s with level %s", group.Name, mapping.PluginName, mapping.AccessLevel)
	}

	return nil
}

// matchesPattern checks if a string matches a pattern (supports basic regex)
func (s *PluginGroupSyncService) matchesPattern(text, pattern string) (bool, error) {
	// Handle wildcard pattern
	if pattern == "*" {
		return true, nil
	}

	// Try regex matching first (case insensitive)
	if matched, err := regexp.MatchString("(?i)"+pattern, text); err == nil {
		return matched, nil
	}

	// Fall back to simple string matching for non-regex patterns
	return strings.Contains(strings.ToLower(text), strings.ToLower(pattern)), nil
}

// validatePluginGroupMapping validates a plugin group mapping
func (s *PluginGroupSyncService) validatePluginGroupMapping(mapping *PluginGroupMapping) error {
	if mapping.GroupPattern == "" {
		return fmt.Errorf("group pattern is required")
	}
	if mapping.PluginName == "" {
		return fmt.Errorf("plugin name is required")
	}
	if mapping.AccessLevel == "" {
		return fmt.Errorf("access level is required")
	}

	validAccessLevels := []string{"view", "deploy", "configure", "manage", "admin"}
	found := false
	for _, level := range validAccessLevels {
		if strings.ToLower(mapping.AccessLevel) == level {
			found = true
			break
		}
	}
	if !found {
		return fmt.Errorf("invalid access level: %s", mapping.AccessLevel)
	}

	return nil
}

// GetUserPluginAccess gets plugin access for a user based on their group memberships
func (s *PluginGroupSyncService) GetUserPluginAccess(ctx context.Context, userID string) (map[string]models.PluginGroupAccess, error) {
	// Get user with groups
	var user models.User
	if err := s.db.Preload("Groups").Where("id = ?", userID).First(&user).Error; err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Get plugin access for all user's groups
	var groupIDs []string
	for _, group := range user.Groups {
		groupIDs = append(groupIDs, group.ID)
	}

	var pluginAccesses []models.PluginGroupAccess
	if err := s.db.Where("group_id IN ?", groupIDs).Find(&pluginAccesses).Error; err != nil {
		return nil, fmt.Errorf("failed to get plugin accesses: %w", err)
	}

	// Aggregate access by plugin (taking the highest level of access)
	accessMap := make(map[string]models.PluginGroupAccess)
	for _, access := range pluginAccesses {
		existing, exists := accessMap[access.PluginName]
		if !exists || s.isHigherAccess(&access, &existing) {
			accessMap[access.PluginName] = access
		}
	}

	return accessMap, nil
}

// isHigherAccess determines if one access level is higher than another
func (s *PluginGroupSyncService) isHigherAccess(a, b *models.PluginGroupAccess) bool {
	// Define access level hierarchy
	getAccessScore := func(access *models.PluginGroupAccess) int {
		score := 0
		if access.CanView {
			score += 1
		}
		if access.CanDeploy {
			score += 2
		}
		if access.CanConfigure {
			score += 4
		}
		if access.CanManage {
			score += 8
		}
		if access.CanInstall {
			score += 16
		}
		return score
	}

	return getAccessScore(a) > getAccessScore(b)
}
