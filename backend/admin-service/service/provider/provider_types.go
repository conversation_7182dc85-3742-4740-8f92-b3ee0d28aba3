package provider

import (
	"context"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
)

// IdentityProviderType represents the type of identity provider
type IdentityProviderType string

const (
	// IdpOIDC represents an OpenID Connect identity provider
	IdpOIDC IdentityProviderType = "oidc"
	// IdpSAML represents a SAML identity provider
	IdpSAML IdentityProviderType = "saml"
	// IdpLDAP represents an LDAP identity provider
	IdpLDAP IdentityProviderType = "ldap"
)

// OIDCProviderData represents the data structure for an OpenID Connect provider
type OIDCProviderData struct {
	ID           string               `json:"id"`
	Name         string               `json:"name"`
	Type         IdentityProviderType `json:"type"`
	ClientID     string               `json:"clientId"`
	ClientSecret string               `json:"clientSecret,omitempty"`
	IssuerURL    string               `json:"issuerUrl"`
	AuthURL      string               `json:"authUrl"`
	TokenURL     string               `json:"tokenUrl"`
	UserInfoURL  string               `json:"userInfoUrl"`
	JwksURL      string               `json:"jwksUrl"`
	Scopes       []string             `json:"scopes"`
	Description  string               `json:"description,omitempty"`
	LogoURL      string               `json:"logoUrl,omitempty"`
	Enabled      bool                 `json:"enabled"`
	IsDefault    bool                 `json:"isDefault"`
	IdpOIDC      IdentityProviderType `json:"-"`
}

// SAMLProvider represents a SAML provider
type SAMLProviderConfig struct {
	ID          string               `json:"id"`
	Name        string               `json:"name"`
	Type        IdentityProviderType `json:"type"`
	MetadataURL string               `json:"metadataUrl"`
	EntityID    string               `json:"entityId"`
	ACSURL      string               `json:"acsUrl"`
	Certificate string               `json:"certificate,omitempty"`
	Description string               `json:"description,omitempty"`
	LogoURL     string               `json:"logoUrl,omitempty"`
	Enabled     bool                 `json:"enabled"`
	IsDefault   bool                 `json:"isDefault"`
	IdpSAML     IdentityProviderType `json:"-"`
}

// LDAPProviderData represents the data structure for an LDAP provider
type LDAPProviderData struct {
	ID                string               `json:"id"`
	Name              string               `json:"name"`
	Type              IdentityProviderType `json:"type"`
	URL               string               `json:"url"`
	BindDN            string               `json:"bindDn"`
	BindPassword      string               `json:"bindPassword,omitempty"`
	UserSearchBase    string               `json:"userSearchBase"`
	UserSearchFilter  string               `json:"userSearchFilter"`
	GroupSearchBase   string               `json:"groupSearchBase"`
	GroupSearchFilter string               `json:"groupSearchFilter"`
	Description       string               `json:"description,omitempty"`
	LogoURL           string               `json:"logoUrl,omitempty"`
	Enabled           bool                 `json:"enabled"`
	IsDefault         bool                 `json:"isDefault"`
	IdpLDAP           IdentityProviderType `json:"-"`
}

// OIDCProviderInterface defines the interface for OIDC provider operations
type OIDCProviderInterface interface {
	// ListProviders returns a list of all OIDC providers
	ListProviders(ctx context.Context) ([]OIDCProviderData, error)

	// GetAuthorizationURL returns the authorization URL for the specified provider
	GetAuthorizationURL(ctx context.Context, state string, providerID string) (string, error)

	// HandleCallback processes the callback from the OIDC provider
	HandleCallback(ctx context.Context, code string, state string) (*models.User, error)

	// FetchGroupsForUser fetches the groups for a user from the OIDC provider
	FetchGroupsForUser(ctx context.Context, username string) ([]string, error)
}

// SAMLProviderInterface defines the interface for SAML provider operations
type SAMLProviderInterface interface {
	// ListProviders returns a list of all SAML providers
	ListProviders(ctx context.Context) ([]SAMLProviderConfig, error)

	// GetAuthorizationURL returns the authorization URL for the specified provider
	GetAuthorizationURL(ctx context.Context, state string, providerID string) (string, error)

	// HandleCallback processes the callback from the SAML provider
	HandleCallback(ctx context.Context, code string, state string) (*models.User, error)

	// FetchGroupsForUser fetches the groups for a user from the SAML provider
	FetchGroupsForUser(ctx context.Context, username string) ([]string, error)
}

// LDAPProviderInterface defines the interface for LDAP provider operations
type LDAPProviderInterface interface {
	// ListProviders returns a list of all LDAP providers
	ListProviders(ctx context.Context) ([]LDAPProviderData, error)

	// Authenticate verifies user credentials against LDAP
	Authenticate(ctx context.Context, username, password string) (*models.User, error)

	// FetchGroupsForUser fetches the groups for a user from the LDAP provider
	FetchGroupsForUser(ctx context.Context, username string) ([]string, error)
}
