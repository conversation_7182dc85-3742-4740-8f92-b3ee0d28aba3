FROM golang:1.21-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates curl

WORKDIR /app

# Copy go mod files for both admin-service and shared
COPY go.mod go.sum ./
COPY ../shared/go.mod ../shared/go.sum ../shared/

# Download all dependencies
RUN go mod download

# Copy the source code
COPY . .
COPY ../shared ../shared

# Build the Go app
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o admin-service .

# Start a new stage from scratch
FROM alpine:latest

# Install runtime dependencies
RUN apk --no-cache add ca-certificates curl

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

WORKDIR /app

# Copy the binary from builder
COPY --from=builder /app/admin-service .

# Create config directory
RUN mkdir -p /app/config

# Copy default config file if it exists
COPY --from=builder /app/config/ /app/config/ 2>/dev/null || true

# Change ownership to non-root user
<PERSON><PERSON> chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Set environment variables that can be overridden
ENV SERVER_PORT=8080
ENV DB_HOST=postgres
ENV LOG_LEVEL=info

EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Run the application
CMD ["./admin-service"]
