package config

// IdentityProviderConfig holds configuration for identity providers
type IdentityProviderConfig struct {
	OIDC OIDCConfig `mapstructure:"oidc" yaml:"oidc"`
	SAML SAMLConfig `mapstructure:"saml" yaml:"saml"`
	LDAP LDAPConfig `mapstructure:"ldap" yaml:"ldap"`
}

// OIDCConfig holds configuration for OIDC identity provider
type OIDCConfig struct {
	Enabled       bool   `mapstructure:"enabled" yaml:"enabled" env:"OIDC_ENABLED" default:"false"`
	IssuerURL     string `mapstructure:"issuer_url" yaml:"issuer_url" env:"OIDC_ISSUER_URL" default:""`
	ClientID      string `mapstructure:"client_id" yaml:"client_id" env:"OIDC_CLIENT_ID" default:""`
	ClientSecret  string `mapstructure:"client_secret" yaml:"client_secret" env:"OIDC_CLIENT_SECRET" default:""`
	RedirectURL   string `mapstructure:"redirect_url" yaml:"redirect_url" env:"OIDC_REDIRECT_URL" default:""`
	Scopes        string `mapstructure:"scopes" yaml:"scopes" env:"OIDC_SCOPES" default:"openid profile email"`
	GroupsClaim   string `mapstructure:"groups_claim" yaml:"groups_claim" env:"OIDC_GROUPS_CLAIM" default:"groups"`
	UsernameClaim string `mapstructure:"username_claim" yaml:"username_claim" env:"OIDC_USERNAME_CLAIM" default:"preferred_username"`
	EmailClaim    string `mapstructure:"email_claim" yaml:"email_claim" env:"OIDC_EMAIL_CLAIM" default:"email"`
}

// SAMLConfig holds configuration for SAML identity provider
type SAMLConfig struct {
	Enabled         bool   `mapstructure:"enabled" yaml:"enabled" env:"SAML_ENABLED" default:"false"`
	EntityID        string `mapstructure:"entity_id" yaml:"entity_id" env:"SAML_ENTITY_ID" default:""`
	MetadataURL     string `mapstructure:"metadata_url" yaml:"metadata_url" env:"SAML_METADATA_URL" default:""`
	ACSURL          string `mapstructure:"acs_url" yaml:"acs_url" env:"SAML_ACS_URL" default:""`
	SPCertificate   string `mapstructure:"sp_certificate" yaml:"sp_certificate" env:"SAML_SP_CERTIFICATE" default:""`
	SPPrivateKey    string `mapstructure:"sp_private_key" yaml:"sp_private_key" env:"SAML_SP_PRIVATE_KEY" default:""`
	GroupsAttribute string `mapstructure:"groups_attribute" yaml:"groups_attribute" env:"SAML_GROUPS_ATTRIBUTE" default:"groups"`
}

// LDAPConfig holds configuration for LDAP identity provider
type LDAPConfig struct {
	Enabled      bool   `mapstructure:"enabled" yaml:"enabled" env:"LDAP_ENABLED" default:"false"`
	URL          string `mapstructure:"url" yaml:"url" env:"LDAP_URL" default:""`
	BindDN       string `mapstructure:"bind_dn" yaml:"bind_dn" env:"LDAP_BIND_DN" default:""`
	BindPassword string `mapstructure:"bind_password" yaml:"bind_password" env:"LDAP_BIND_PASSWORD" default:""`
	BaseDN       string `mapstructure:"base_dn" yaml:"base_dn" env:"LDAP_BASE_DN" default:""`
	UserFilter   string `mapstructure:"user_filter" yaml:"user_filter" env:"LDAP_USER_FILTER" default:"(uid=%s)"`
	GroupFilter  string `mapstructure:"group_filter" yaml:"group_filter" env:"LDAP_GROUP_FILTER" default:"(member=%s)"`
	GroupsAttr   string `mapstructure:"groups_attr" yaml:"groups_attr" env:"LDAP_GROUPS_ATTR" default:"memberOf"`
	UseSSL       bool   `mapstructure:"use_ssl" yaml:"use_ssl" env:"LDAP_USE_SSL" default:"false"`
	StartTLS     bool   `mapstructure:"start_tls" yaml:"start_tls" env:"LDAP_START_TLS" default:"false"`
	InsecureSkip bool   `mapstructure:"insecure_skip" yaml:"insecure_skip" env:"LDAP_INSECURE_SKIP" default:"false"`
}
