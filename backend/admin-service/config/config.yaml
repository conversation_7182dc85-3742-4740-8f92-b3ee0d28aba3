# Simplified Admin Service Configuration

# Server settings
server:
  port: 8086
  host: "0.0.0.0"
  read_timeout: 30
  write_timeout: 30
  shutdown_timeout: 10
  trusted_proxies: ""
  tls_cert_file: ""
  tls_key_file: ""

# Database settings
database:
  url: "postgres://deploy:deploy@localhost:5432/admin_service?sslmode=disable"
  max_retries: 5
  retry_interval: 3
  max_open_conns: 25
  max_idle_conns: 10
  query_timeout: 10
  log_level: "info"

# Authentication settings
auth:
  jwt_secret: "default-secret-key-in-production"
  jwt_expiration_minutes: 60
  access_token_expiry: "1h"
  refresh_token_expiry: "168h"
  admin_service_url: "http://localhost:8086"
  api_key_header: "X-API-Key"
  disable: false

# Logging configuration
logging:
  level: "info"
  format: "json"
  output: "stdout"
  time_format: "2006-01-02T15:04:05Z07:00"

# Tracing configuration
tracing:
  enabled: true
  service_name: "admin-service"
  endpoint: "localhost:4317"

# Service information
service:
  name: "admin-service"
  version: "1.0.0"

# Token expiry times (custom to this service)
access_token_expiry_minutes: 1440  # 24 hours
refresh_token_expiry_minutes: 10080  # 1 week

# Default user settings
default_admin:
  username: "admin"
  password: "admin"
  email: "<EMAIL>"

# Initial permissions configuration
initial_permissions:
  # User Management Permissions
  - name: "user:create"
    description: "Create new users"
    category: "user"
  - name: "user:read"
    description: "View user information"
    category: "user"
  - name: "user:update"
    description: "Update user information"
    category: "user"
  - name: "user:delete"
    description: "Delete users"
    category: "user"

  # Project Management Permissions
  - name: "project:create"
    description: "Create new projects"
    category: "project"
  - name: "project:read"
    description: "View project information"
    category: "project"
  - name: "project:update"
    description: "Update project information"
    category: "project"
  - name: "project:delete"
    description: "Delete projects"
    category: "project"

  # Environment Management Permissions
  - name: "environment:view"
    description: "View environments for assigned projects"
    category: "environment"
  - name: "environment:create"
    description: "Create new environments"
    category: "environment"
  - name: "environment:update"
    description: "Update existing environments"
    category: "environment"
  - name: "environment:delete"
    description: "Delete environments"
    category: "environment"
  - name: "environment:deploy"
    description: "Deploy to environments"
    category: "environment"

  # Workflow Management Permissions
  - name: "workflow:view"
    description: "View workflows for assigned projects"
    category: "workflow"
  - name: "workflow:create"
    description: "Create new workflows"
    category: "workflow"
  - name: "workflow:update"
    description: "Update existing workflows"
    category: "workflow"
  - name: "workflow:delete"
    description: "Delete workflows"
    category: "workflow"
  - name: "workflow:execute"
    description: "Execute workflows"
    category: "workflow"
  - name: "workflow:monitor"
    description: "Monitor workflow executions"
    category: "workflow"

  # Template Management Permissions
  - name: "template:view"
    description: "View workflow templates"
    category: "template"
  - name: "template:create"
    description: "Create workflow templates"
    category: "template"
  - name: "template:update"
    description: "Update workflow templates"
    category: "template"
  - name: "template:delete"
    description: "Delete workflow templates"
    category: "template"
  - name: "template:analytics"
    description: "View template analytics and usage statistics"
    category: "template"

  # Secret Management Permissions
  - name: "secret:view"
    description: "View secrets for assigned projects"
    category: "secret"
  - name: "secret:create"
    description: "Create new secrets"
    category: "secret"
  - name: "secret:update"
    description: "Update existing secrets"
    category: "secret"
  - name: "secret:delete"
    description: "Delete secrets"
    category: "secret"

  # Provider Access Permissions
  - name: "provider:view"
    description: "View provider configurations"
    category: "provider"
  - name: "provider:create"
    description: "Create new provider configurations"
    category: "provider"
  - name: "provider:update"
    description: "Update provider configurations"
    category: "provider"
  - name: "provider:delete"
    description: "Delete provider configurations"
    category: "provider"
  - name: "provider:access"
    description: "Access provider services and features"
    category: "provider"

  # Plugin Management Permissions
  - name: "plugin:view"
    description: "View plugin information and status"
    category: "plugin"
  - name: "plugin:install"
    description: "Install new plugins"
    category: "plugin"
  - name: "plugin:configure"
    description: "Configure plugin settings"
    category: "plugin"
  - name: "plugin:uninstall"
    description: "Uninstall plugins"
    category: "plugin"
  - name: "plugin:execute"
    description: "Execute plugin operations"
    category: "plugin"

  # Audit & Monitoring Permissions
  - name: "audit:view"
    description: "View audit logs"
    category: "audit"
  - name: "monitoring:view"
    description: "View monitoring data"
    category: "monitoring"
    
  # Application Management Permissions
  - name: "application:create"
    description: "Create new applications"
    category: "application"
  - name: "application:update"
    description: "Update existingapplications"
    category: "application"
  - name: "application:delete"
    description: "Delete applications"
    category: "application"
  - name: "application:view"
    description: "View application details"
    category: "application"
  - name: "application-group:create"
    description: "Create new application groups"
    category: "application"
  - name: "application-group:update"
    description: "Update existing application groups"
    category: "application"
  - name: "application-group:delete"
    description: "Delete application groups"
    category: "application"
  - name: "application-group:view"
    description: "View application groups"
    category: "application"
  - name: "deploy:create"
    description: "Create new deployments"
    category: "application"
  - name: "deploy:update"
    description: "Update existing deployments"
    category: "application"
  - name: "deploy:delete"
    description: "Delete deployments"
    category: "application"
  - name: "deploy:view"
    description: "View deployment details"
    category: "application"
  - name: "component:create"
    description: "Create new components"
    category: "application"
  - name: "component:update"
    description: "Update existing components"
    category: "application"
  - name: "component:delete"
    description: "Delete components"
    category: "application"
  - name: "component:view"
    description: "View component details"
    category: "application"
  - name: "deployable:create"
    description: "Create new deployable artifacts"
    category: "application"
  - name: "deployable:update"
    description: "Update existing deployable artifacts"
    category: "application"
  - name: "deployable:view"
    description: "View deployable artifacts"
    category: "application"
  - name: "deployable:delete"
    description: "Delete existing deployable artifacts"
    category: "application"



  # Admin Permissions
  - name: "admin:full"
    description: "Full administrative access to all resources"
    category: "admin"

# Initial roles configuration
initial_roles:
  - name: "admin"
    description: "System administrator with full access"
    permissions:
      - "admin:full"
      - "user:create"
      - "user:read"
      - "user:update"
      - "user:delete"
      - "project:create"
      - "project:read"
      - "project:update"
      - "project:delete"
      - "environment:view"
      - "environment:create"
      - "environment:update"
      - "environment:delete"
      - "environment:deploy"
      - "workflow:view"
      - "workflow:create"
      - "workflow:update"
      - "workflow:delete"
      - "workflow:execute"
      - "workflow:monitor"
      - "template:view"
      - "template:create"
      - "template:update"
      - "template:delete"
      - "template:analytics"
      - "secret:view"
      - "secret:create"
      - "secret:update"
      - "secret:delete"
      - "provider:view"
      - "provider:create"
      - "provider:update"
      - "provider:delete"
      - "provider:access"
      - "plugin:view"
      - "plugin:install"
      - "plugin:configure"
      - "plugin:uninstall"
      - "plugin:execute"
      - "audit:view"
      - "monitoring:view"
      - "application:create"
      - "application:update"
      - "application:delete"
      - "application:view"
      - "application-group:create"
      - "application-group:update"
      - "application-group:delete"
      - "application-group:view"
      - "deploy:create"
      - "deploy:update"
      - "deploy:delete"
      - "deploy:view"
      - "component:create"
      - "component:update"
      - "component:delete"
      - "component:view"
      - "deployable:create"
      - "deployable:update"
      - "deployable:view"
      - "deployable:delete"

  - name: "developer"
    description: "Developer role with project-scoped access"
    permissions:
      - "project:read"
      - "environment:view"
      - "environment:create"
      - "environment:update"
      - "environment:deploy"
      - "workflow:view"
      - "workflow:create"
      - "workflow:update"
      - "workflow:execute"
      - "workflow:monitor"
      - "template:view"
      - "template:create"
      - "secret:view"
      - "secret:create"
      - "secret:update"
      - "provider:view"
      - "provider:access"
      - "plugin:view"
      - "plugin:execute"


  - name: "qa"
    description: "QA role with testing and monitoring access"
    permissions:
      - "project:read"
      - "environment:view"
      - "environment:deploy"
      - "workflow:view"
      - "workflow:execute"
      - "workflow:monitor"
      - "template:view"
      - "secret:view"
      - "monitoring:view"

  - name: "viewer"
    description: "Read-only access to assigned projects"
    permissions:
      - "project:read"
      - "environment:view"
      - "workflow:view"
      - "template:view"
      - "monitoring:view"

# Identity provider configuration
identity_providers:
  # OIDC configuration
  oidc:
    enabled: false
    issuer_url: "https://accounts.google.com"
    client_id: "your-client-id"
    client_secret: "your-client-secret"
    redirect_url: "http://localhost:8080/api/auth/callback"
    scopes: "openid profile email"
    groups_claim: "groups"
    username_claim: "preferred_username"
    email_claim: "email"

  # SAML configuration
  saml:
    enabled: false
    entity_id: "http://localhost:8080/saml/metadata"
    metadata_url: ""
    acs_url: "http://localhost:8080/saml/acs"
    sp_certificate: ""
    sp_private_key: ""
    groups_attribute: "groups"

  # LDAP configuration
  ldap:
    enabled: false
    url: "ldap://localhost:389"
    bind_dn: "cn=admin,dc=example,dc=org"
    bind_password: "admin"
    base_dn: "dc=example,dc=org"
    user_filter: "(uid=%s)"
    group_filter: "(member=%s)"
    groups_attr: "memberOf"
    use_ssl: false
    start_tls: false
    insecure_skip: false

gateway:
  url: "http://localhost:8000"
  token: ""
  enabled: true
  service_version: "1.0.0"
  environment: "development"
  region: "local"
  retry_attempts: 3
  health_check_path: "/health"
  tags: ["api", "admin", "microservice"]

# Plugin permissions configuration
plugin_permissions:
  # Core settings (can be overridden by environment variables)
  cache_ttl: "300s"                    # PLUGIN_PERMISSION_CACHE_TTL
  audit_retention_days: 90             # PLUGIN_AUDIT_RETENTION_DAYS
  group_sync_interval: "1h"            # PLUGIN_GROUP_SYNC_INTERVAL
  default_access_level: "view"         # PLUGIN_DEFAULT_ACCESS_LEVEL

  # Security settings
  security:
    audit_logging:
      enabled: true
      log_level: "info"
      include_request_body: false
      include_sensitive_data: false
      retention_days: 90

    session_timeout:
      enabled: true
      timeout_minutes: 480  # 8 hours
      warning_minutes: 30

    rate_limiting:
      enabled: true
      requests_per_minute: 100
      burst_limit: 200

  # Feature flags
  features:
    plugin_marketplace:
      enabled: true
      allow_external_sources: false
      require_approval: true

    hot_reload:
      enabled: true
      allow_in_production: false

    plugin_metrics:
      enabled: true
      collect_usage_stats: true
      share_anonymous_data: false

    advanced_permissions:
      enabled: true
      allow_custom_roles: true
      allow_temporary_access: true