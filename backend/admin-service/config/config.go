package config

import (
	"fmt"
	"time"

	sharedConfig "github.com/claudio/deploy-orchestrator/shared/config"
)

// InitialPermission represents a permission to be created at startup
type InitialPermission struct {
	Name        string `mapstructure:"name" yaml:"name"`
	Description string `mapstructure:"description" yaml:"description"`
	Category    string `mapstructure:"category" yaml:"category"`
}

// InitialRole represents a role to be created at startup
type InitialRole struct {
	Name        string   `mapstructure:"name" yaml:"name"`
	Description string   `mapstructure:"description" yaml:"description"`
	Permissions []string `mapstructure:"permissions" yaml:"permissions"`
}

// PluginPermissionsConfig holds plugin permission system configuration
type PluginPermissionsConfig struct {
	CacheTTL           string `mapstructure:"cache_ttl" yaml:"cache_ttl" env:"PLUGIN_PERMISSION_CACHE_TTL" default:"300s"`
	AuditRetentionDays int    `mapstructure:"audit_retention_days" yaml:"audit_retention_days" env:"PLUGIN_AUDIT_RETENTION_DAYS" default:"90"`
	GroupSyncInterval  string `mapstructure:"group_sync_interval" yaml:"group_sync_interval" env:"PLUGIN_GROUP_SYNC_INTERVAL" default:"1h"`
	DefaultAccessLevel string `mapstructure:"default_access_level" yaml:"default_access_level" env:"PLUGIN_DEFAULT_ACCESS_LEVEL" default:"view"`

	// Security settings
	Security struct {
		AuditLogging struct {
			Enabled              bool   `mapstructure:"enabled" yaml:"enabled" default:"true"`
			LogLevel             string `mapstructure:"log_level" yaml:"log_level" default:"info"`
			IncludeRequestBody   bool   `mapstructure:"include_request_body" yaml:"include_request_body" default:"false"`
			IncludeSensitiveData bool   `mapstructure:"include_sensitive_data" yaml:"include_sensitive_data" default:"false"`
			RetentionDays        int    `mapstructure:"retention_days" yaml:"retention_days" default:"90"`
		} `mapstructure:"audit_logging" yaml:"audit_logging"`

		SessionTimeout struct {
			Enabled        bool `mapstructure:"enabled" yaml:"enabled" default:"true"`
			TimeoutMinutes int  `mapstructure:"timeout_minutes" yaml:"timeout_minutes" default:"480"`
			WarningMinutes int  `mapstructure:"warning_minutes" yaml:"warning_minutes" default:"30"`
		} `mapstructure:"session_timeout" yaml:"session_timeout"`

		RateLimiting struct {
			Enabled           bool `mapstructure:"enabled" yaml:"enabled" default:"true"`
			RequestsPerMinute int  `mapstructure:"requests_per_minute" yaml:"requests_per_minute" default:"100"`
			BurstLimit        int  `mapstructure:"burst_limit" yaml:"burst_limit" default:"200"`
		} `mapstructure:"rate_limiting" yaml:"rate_limiting"`
	} `mapstructure:"security" yaml:"security"`

	// Feature flags
	Features struct {
		PluginMarketplace struct {
			Enabled              bool `mapstructure:"enabled" yaml:"enabled" default:"true"`
			AllowExternalSources bool `mapstructure:"allow_external_sources" yaml:"allow_external_sources" default:"false"`
			RequireApproval      bool `mapstructure:"require_approval" yaml:"require_approval" default:"true"`
		} `mapstructure:"plugin_marketplace" yaml:"plugin_marketplace"`

		HotReload struct {
			Enabled           bool `mapstructure:"enabled" yaml:"enabled" default:"true"`
			AllowInProduction bool `mapstructure:"allow_in_production" yaml:"allow_in_production" default:"false"`
		} `mapstructure:"hot_reload" yaml:"hot_reload"`

		PluginMetrics struct {
			Enabled            bool `mapstructure:"enabled" yaml:"enabled" default:"true"`
			CollectUsageStats  bool `mapstructure:"collect_usage_stats" yaml:"collect_usage_stats" default:"true"`
			ShareAnonymousData bool `mapstructure:"share_anonymous_data" yaml:"share_anonymous_data" default:"false"`
		} `mapstructure:"plugin_metrics" yaml:"plugin_metrics"`

		AdvancedPermissions struct {
			Enabled              bool `mapstructure:"enabled" yaml:"enabled" default:"true"`
			AllowCustomRoles     bool `mapstructure:"allow_custom_roles" yaml:"allow_custom_roles" default:"true"`
			AllowTemporaryAccess bool `mapstructure:"allow_temporary_access" yaml:"allow_temporary_access" default:"true"`
		} `mapstructure:"advanced_permissions" yaml:"advanced_permissions"`
	} `mapstructure:"features" yaml:"features"`
}

// AdminConfig holds the configuration for the simplified admin service
type AdminConfig struct {
	// Common configuration sections from shared module
	Database sharedConfig.DBConfig      `mapstructure:"database" yaml:"database"`
	Server   sharedConfig.ServerConfig  `mapstructure:"server" yaml:"server"`
	Logging  sharedConfig.LoggingConfig `mapstructure:"logging" yaml:"logging"`
	Tracing  sharedConfig.TracingConfig `mapstructure:"tracing" yaml:"tracing"`
	Auth     sharedConfig.AuthConfig    `mapstructure:"auth" yaml:"auth"`
	Service  sharedConfig.ServiceConfig `mapstructure:"service" yaml:"service"`
	Gateway  sharedConfig.GatewayConfig `mapstructure:"gateway" yaml:"gateway"`

	// Identity provider configuration
	IdentityProviders IdentityProviderConfig `mapstructure:"identity_providers" yaml:"identity_providers"`

	// Plugin permissions configuration
	PluginPermissions PluginPermissionsConfig `mapstructure:"plugin_permissions" yaml:"plugin_permissions"`

	// Custom fields for this service
	DefaultAdmin struct {
		Username string `mapstructure:"username" yaml:"username" env:"DEFAULT_ADMIN_USERNAME" default:"admin"`
		Password string `mapstructure:"password" yaml:"password" env:"DEFAULT_ADMIN_PASSWORD" default:"admin"`
		Email    string `mapstructure:"email" yaml:"email" env:"DEFAULT_ADMIN_EMAIL" default:"<EMAIL>"`
	} `mapstructure:"default_admin" yaml:"default_admin"`

	// Initial permissions and roles configuration
	InitialPermissions []InitialPermission `mapstructure:"initial_permissions" yaml:"initial_permissions"`
	InitialRoles       []InitialRole       `mapstructure:"initial_roles" yaml:"initial_roles"`

	// Token expiry configurations
	AccessTokenExpiryMinutes  int `mapstructure:"access_token_expiry_minutes" yaml:"access_token_expiry_minutes" env:"ACCESS_TOKEN_EXPIRY_MINUTES" default:"1440"`     // 24h = 1440 minutes
	RefreshTokenExpiryMinutes int `mapstructure:"refresh_token_expiry_minutes" yaml:"refresh_token_expiry_minutes" env:"REFRESH_TOKEN_EXPIRY_MINUTES" default:"10080"` // 1 week = 10080 minutes

	// Computed runtime values (not directly from config)
	AccessTokenExpiryDuration  time.Duration `mapstructure:"-" yaml:"-"`
	RefreshTokenExpiryDuration time.Duration `mapstructure:"-" yaml:"-"`
}

// LoadConfig loads the simplified admin service configuration from file and environment variables
func LoadConfig() (*AdminConfig, error) {
	config := &AdminConfig{
		Server: sharedConfig.ServerConfig{
			Port: 8080, // Default port for simplified admin service
		},
		Tracing: sharedConfig.TracingConfig{
			ServiceName: "admin-service",
		},
		Service: sharedConfig.ServiceConfig{
			Name:    "admin-service",
			Version: "1.0.0",
		},
	}

	if err := sharedConfig.LoadConfig(config, "admin-service"); err != nil {
		return nil, fmt.Errorf("failed to load admin service config: %w", err)
	}

	// Convert minutes to duration values for easier usage
	config.AccessTokenExpiryDuration = time.Duration(config.AccessTokenExpiryMinutes) * time.Minute
	config.RefreshTokenExpiryDuration = time.Duration(config.RefreshTokenExpiryMinutes) * time.Minute

	return config, nil
}

// GetDatabaseURI returns the connection string for the database
func (c *AdminConfig) GetDatabaseURI() string {
	// If a full URL is provided, use it directly, otherwise build from parts
	if c.Database.URL != "" {
		return c.Database.URL
	}

	// Otherwise, construct a default PostgreSQL connection string
	return "postgres://postgres:postgres@localhost:5432/admin_service?sslmode=disable"
}

// SaveDefaultConfig saves a default configuration file if one doesn't exist
func SaveDefaultConfig() error {
	config := &AdminConfig{
		Server: sharedConfig.ServerConfig{
			Port: 8080,
			Host: "0.0.0.0",
		},
		Auth: sharedConfig.AuthConfig{
			JWTSecret:            "default-secret-key-change-in-production",
			JWTExpirationMinutes: 1440, // 24 hours
		},
		Tracing: sharedConfig.TracingConfig{
			ServiceName: "admin-service",
		},
		Service: sharedConfig.ServiceConfig{
			Name:    "admin-service",
			Version: "1.0.0",
		},
		IdentityProviders: IdentityProviderConfig{
			OIDC: OIDCConfig{
				Enabled:       false,
				IssuerURL:     "https://accounts.google.com",
				ClientID:      "your-client-id",
				ClientSecret:  "your-client-secret",
				RedirectURL:   "http://localhost:8080/api/auth/callback",
				Scopes:        "openid profile email",
				GroupsClaim:   "groups",
				UsernameClaim: "preferred_username",
				EmailClaim:    "email",
			},
			SAML: SAMLConfig{
				Enabled:         false,
				EntityID:        "http://localhost:8080/saml/metadata",
				MetadataURL:     "",
				ACSURL:          "http://localhost:8080/saml/acs",
				SPCertificate:   "",
				SPPrivateKey:    "",
				GroupsAttribute: "groups",
			},
			LDAP: LDAPConfig{
				Enabled:      false,
				URL:          "ldap://localhost:389",
				BindDN:       "cn=admin,dc=example,dc=org",
				BindPassword: "admin",
				BaseDN:       "dc=example,dc=org",
				UserFilter:   "(uid=%s)",
				GroupFilter:  "(member=%s)",
				GroupsAttr:   "memberOf",
				UseSSL:       false,
				StartTLS:     false,
				InsecureSkip: false,
			},
		},
		PluginPermissions: PluginPermissionsConfig{
			CacheTTL:           "300s",
			AuditRetentionDays: 90,
			GroupSyncInterval:  "1h",
			DefaultAccessLevel: "view",
		},
	}

	return sharedConfig.SaveDefaultConfig(config, "admin-service")
}
