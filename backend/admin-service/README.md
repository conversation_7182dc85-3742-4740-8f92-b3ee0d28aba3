# Admin Service

A lightweight authentication and user management service written in Go. This service is part of the deploy-orchestrator backend microservices architecture.

## Features

- User authentication with JWT tokens
- User management (create, read, update, delete)
- Role-based access control (admin vs regular users)
- Token refresh functionality
- Database integration with PostgreSQL
- RESTful API design

## API Endpoints

### Authentication

- **POST /api/auth/login** - Authenticate a user and get tokens
- **POST /api/auth/refresh** - Refresh an access token using a refresh token

### User Management

- **GET /api/users** - List all users (authenticated)
- **GET /api/users/:id** - Get a specific user (authenticated)
- **POST /api/users** - Create a new user (authenticated)
- **PUT /api/users/:id** - Update an existing user (authenticated)
- **DELETE /api/users/:id** - Delete a user (authenticated)

### Admin

- **GET /api/admin/system/status** - Get system status (admin only)

## Getting Started

### Prerequisites

- Go 1.18 or higher
- PostgreSQL database
- Docker (optional)

### Configuration

The service is configured using a YAML configuration file located at `./config/config.yaml`. 
Environment variables can also be used to override configuration values.

Here's a sample configuration file:

```yaml
# Server settings
server:
  server_port: 8080
  server_host: "0.0.0.0"
  environment: "development"

# Database settings
database:
  db_host: "localhost"
  db_port: 5432
  db_user: "postgres"
  db_password: "postgres"
  db_name: "admin_service"
  db_ssl_mode: "disable"

# Authentication settings
auth:
  jwt_secret: "your-secret-key"
  access_token_expiry: "24h"
  refresh_token_expiry: "168h"

# Default admin settings
default_admin:
  username: "admin"
  password: "admin"
  email: "<EMAIL>"
```

You can generate a default configuration file by running:

```bash
go run main.go -generate-config
```

### Running Locally

1. Clone the repository
2. Run the setup script:

```bash
./setup.sh
```

3. Start the service:

```bash
./run.sh
```

### Development Mode

For development with hot reload (automatically rebuilds when code changes):

```bash
./dev.sh
```

### Using Makefile

We provide a Makefile with common operations:

```bash
# Setup the project
make setup

# Build the application
make build

# Run the application
make run

# Run in development mode (hot reload)
make dev

# Build docker image
make docker

# Run with docker-compose
make docker-compose

# Run tests
make test

# Show all available commands
make help
```

### Building and Running with Docker

```bash
# Build the Docker image
docker build -t admin-service .

# Run the container
docker run -p 8080:8080 -e JWT_SECRET=your-secret-key admin-service
```

### Using Docker Compose

The easiest way to run the service with its PostgreSQL database:

```bash
# Start the services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop the services
docker-compose down
```

## Default Admin User

On first startup, the service creates a default admin user:

- Username: admin
- Password: admin
- Email: <EMAIL>

**Note:** Change the default admin password immediately in production environments.

## License

This project is licensed under the MIT License.
