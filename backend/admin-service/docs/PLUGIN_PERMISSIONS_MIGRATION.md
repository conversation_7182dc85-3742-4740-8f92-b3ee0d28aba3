# Plugin Permission System Migration Guide

## Overview

This guide helps you integrate the Plugin Permission System into your existing Deploy Orchestrator setup.

## Prerequisites

- Go 1.19+
- PostgreSQL database
- Existing admin-service setup
- GORM v1.25+

## Step 1: Database Migration

### Automatic Migration

The plugin permission tables will be created automatically when the service starts:

```go
// In your main.go or database initialization
func initDatabase() error {
    // ... existing database setup ...

    // Run plugin permission migrations
    err := migrations.RunPluginPermissionsMigration(db)
    if err != nil {
        return fmt.Errorf("failed to run plugin permissions migration: %w", err)
    }

    return nil
}
```

### Manual Migration (if needed)

If you prefer manual control, you can run the migration separately:

```bash
# Connect to your PostgreSQL database
psql -h localhost -U your_user -d deploy_orchestrator

# Run the migration SQL (generated by GORM)
\i backend/admin-service/migrations/plugin_permissions.sql
```

## Step 2: Service Integration

### Update main.go

```go
package main

import (
    "github.com/claudio/deploy-orchestrator/admin-service/service"
    "github.com/claudio/deploy-orchestrator/admin-service/api"
    // ... other imports
)

func main() {
    // ... existing setup ...

    // Initialize plugin permission services
    pluginPermissionService := service.NewPluginPermissionService(db)
    groupSyncService := service.NewGroupSyncService(db) // Your existing service
    pluginGroupSyncService := service.NewPluginGroupSyncService(
        db,
        groupSyncService,
        pluginPermissionService,
    )

    // Initialize handlers
    pluginPermissionHandler := api.NewPluginPermissionHandler(pluginPermissionService)

    // Register routes
    apiGroup := router.Group("/api/v1")
    pluginPermissionHandler.RegisterRoutes(apiGroup)

    // ... rest of setup
}
```

### Configuration

The plugin permissions system is configured through the main `config.yaml` file in the admin-service. The following settings can be configured either in the YAML file or through environment variables:

**In config.yaml:**
```yaml
plugin_permissions:
  cache_ttl: "300s"                    # PLUGIN_PERMISSION_CACHE_TTL
  audit_retention_days: 90             # PLUGIN_AUDIT_RETENTION_DAYS
  group_sync_interval: "1h"            # PLUGIN_GROUP_SYNC_INTERVAL
  default_access_level: "view"         # PLUGIN_DEFAULT_ACCESS_LEVEL

  security:
    audit_logging:
      enabled: true
      log_level: "info"
      retention_days: 90

    session_timeout:
      enabled: true
      timeout_minutes: 480  # 8 hours
      warning_minutes: 30

    rate_limiting:
      enabled: true
      requests_per_minute: 100
      burst_limit: 200

  features:
    plugin_marketplace:
      enabled: true
      allow_external_sources: false
      require_approval: true

    hot_reload:
      enabled: true
      allow_in_production: false

    plugin_metrics:
      enabled: true
      collect_usage_stats: true
      share_anonymous_data: false

    advanced_permissions:
      enabled: true
      allow_custom_roles: true
      allow_temporary_access: true
```

**Environment Variables (override config.yaml):**
```bash
# .env or docker-compose.yml
PLUGIN_PERMISSION_CACHE_TTL=300s
PLUGIN_AUDIT_RETENTION_DAYS=90
PLUGIN_GROUP_SYNC_INTERVAL=1h
PLUGIN_DEFAULT_ACCESS_LEVEL=view
```

## Step 3: Authentication Middleware Integration

### Update your authentication middleware

```go
// middleware/auth.go
func AuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // ... existing auth logic ...

        // Set user context for plugin permissions
        c.Set("user_id", user.ID)
        c.Set("is_admin", user.IsAdmin)
        c.Set("user_roles", user.Roles)
        c.Set("user_groups", user.Groups)

        c.Next()
    }
}
```

## Step 4: Plugin Integration

### For Plugin Developers

If you're developing plugins, integrate permission checks:

```go
// In your plugin handler
func (h *PluginHandler) DeployHandler(c *gin.Context) {
    userID := c.GetString("user_id")

    // Check permission
    request := service.PermissionCheckRequest{
        Action:       "deploy",
        Resource:     "my-plugin",
        ResourceType: "plugin",
        ProjectID:    c.Param("projectId"),
    }

    response, err := h.pluginPermissionService.CheckPermission(c, userID, request)
    if err != nil {
        c.JSON(500, gin.H{"error": "Permission check failed"})
        return
    }

    if !response.Allowed {
        c.JSON(403, gin.H{"error": response.Reason})
        return
    }

    // Log the action
    h.pluginPermissionService.LogPluginAction(c, service.PluginActionLog{
        UserID:       userID,
        Action:       "deploy",
        Resource:     "my-plugin",
        ResourceType: "plugin",
        Details: map[string]interface{}{
            "projectId": c.Param("projectId"),
            "success":   true,
        },
        IPAddress: c.ClientIP(),
        UserAgent: c.GetHeader("User-Agent"),
    })

    // Proceed with deployment
    // ...
}
```

## Step 5: Frontend Integration

### API Client Updates

```typescript
// frontend/src/services/plugin-permissions.service.ts
export class PluginPermissionService {
    async getUserPermissionMatrix(): Promise<PermissionMatrix> {
        const response = await this.http.get('/api/v1/plugin-permissions/matrix');
        return response.data;
    }

    async checkPermission(request: PermissionCheckRequest): Promise<PermissionCheckResponse> {
        const response = await this.http.post('/api/v1/plugin-permissions/check', request);
        return response.data;
    }

    async getAuditLogs(filters?: AuditLogFilters): Promise<PluginAuditLog[]> {
        const params = new URLSearchParams(filters);
        const response = await this.http.get(`/api/v1/plugin-permissions/audit-logs?${params}`);
        return response.data.logs;
    }
}
```

### Permission Guards

```typescript
// frontend/src/guards/plugin-permission.guard.ts
@Injectable()
export class PluginPermissionGuard implements CanActivate {
    constructor(private permissionService: PluginPermissionService) {}

    async canActivate(route: ActivatedRouteSnapshot): Promise<boolean> {
        const requiredPermission = route.data['permission'];
        const pluginName = route.params['pluginName'];

        const request: PermissionCheckRequest = {
            action: requiredPermission.action,
            resource: pluginName,
            resourceType: 'plugin',
            projectId: route.params['projectId'],
        };

        const response = await this.permissionService.checkPermission(request);
        return response.allowed;
    }
}
```

## Step 6: Group Sync Configuration

### Configure Identity Provider Sync

```go
// Configure automatic group sync
func setupGroupSync(pluginGroupSyncService *service.PluginGroupSyncService) {
    // Create custom group mappings
    mappings := []service.PluginGroupMapping{
        {
            GroupPattern:     "project-.*-developers",
            PluginName:       "deployment-plugin",
            AccessLevel:      "deploy",
            AllowedProjects:  []string{"project-*"},
            AllowedTemplates: []string{"basic-deploy", "advanced-deploy"},
            AllowedProviders: []string{"kubernetes", "docker"},
        },
        {
            GroupPattern:     "platform-team",
            PluginName:       "*",
            AccessLevel:      "admin",
            AllowedProjects:  []string{"*"},
            AllowedTemplates: []string{"*"},
            AllowedProviders: []string{"*"},
        },
    }

    // Store mappings
    for _, mapping := range mappings {
        err := pluginGroupSyncService.CreatePluginGroupMapping(context.Background(), &mapping)
        if err != nil {
            log.Printf("Failed to create group mapping: %v", err)
        }
    }
}
```

### Schedule Periodic Sync

```go
// Setup periodic group sync
func setupPeriodicSync(pluginGroupSyncService *service.PluginGroupSyncService) {
    ticker := time.NewTicker(1 * time.Hour)
    go func() {
        for range ticker.C {
            result, err := pluginGroupSyncService.SyncAllUsersPluginAccess(context.Background(), false)
            if err != nil {
                log.Printf("Group sync failed: %v", err)
                continue
            }

            log.Printf("Group sync completed: %d groups processed, %d permissions created",
                result.GroupsProcessed, result.PluginAccessCreated)
        }
    }()
}
```

## Step 7: Testing

### Test the Integration

```bash
# Test database migration
go run main.go --migrate-only

# Test API endpoints
curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:8080/api/v1/plugin-permissions/matrix

# Test permission check
curl -X POST \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"action":"deploy","resource":"my-plugin","resourceType":"plugin"}' \
     http://localhost:8080/api/v1/plugin-permissions/check

# Test group sync
curl -X POST \
     -H "Authorization: Bearer $TOKEN" \
     http://localhost:8080/api/v1/admin/sync-plugin-permissions
```

### Run Test Suite

```bash
# Run all plugin permission tests
cd backend/admin-service
go test ./service -v -run TestPlugin
go test ./api -v -run TestPlugin

# Test with coverage
go test ./... -cover -coverprofile=coverage.out
go tool cover -html=coverage.out
```

## Step 8: Monitoring and Maintenance

### Health Checks

```go
// Add health check endpoint
func (h *PluginPermissionHandler) HealthCheck(c *gin.Context) {
    // Check database connectivity
    err := h.service.HealthCheck(c)
    if err != nil {
        c.JSON(500, gin.H{"status": "unhealthy", "error": err.Error()})
        return
    }

    c.JSON(200, gin.H{"status": "healthy"})
}
```

### Metrics Collection

```go
// Add metrics for monitoring
var (
    permissionChecks = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "plugin_permission_checks_total",
            Help: "Total number of plugin permission checks",
        },
        []string{"action", "resource", "allowed"},
    )

    groupSyncDuration = prometheus.NewHistogram(
        prometheus.HistogramOpts{
            Name: "plugin_group_sync_duration_seconds",
            Help: "Duration of plugin group sync operations",
        },
    )
)
```

## Troubleshooting

### Common Issues

1. **Migration Fails**
   ```bash
   # Check database permissions
   GRANT ALL PRIVILEGES ON DATABASE deploy_orchestrator TO your_user;

   # Check table conflicts
   SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename LIKE 'plugin_%';
   ```

2. **Permission Checks Fail**
   ```bash
   # Check user context in middleware
   # Verify group memberships are loaded
   # Check default permissions are created
   ```

3. **Group Sync Issues**
   ```bash
   # Check group patterns
   # Verify identity provider integration
   # Check configuration table entries
   ```

### Logs and Debugging

```bash
# Enable debug logging
export LOG_LEVEL=debug

# Check specific logs
tail -f logs/admin-service.log | grep "plugin"

# Database query logging
export GORM_LOG_LEVEL=info
```

## Rollback Plan

If you need to rollback the plugin permission system:

```sql
-- Backup data first
pg_dump -t plugin_* deploy_orchestrator > plugin_permissions_backup.sql

-- Drop plugin permission tables
DROP TABLE IF EXISTS plugin_group_access;
DROP TABLE IF EXISTS plugin_audit_logs;
DROP TABLE IF EXISTS plugin_access_controls;
DROP TABLE IF EXISTS plugin_user_permissions;
DROP TABLE IF EXISTS plugin_group_permissions;
DROP TABLE IF EXISTS plugin_role_permissions;
DROP TABLE IF EXISTS plugin_permissions;
DROP TABLE IF EXISTS plugin_permission_policies;
DROP TABLE IF EXISTS plugin_templates;
DROP TABLE IF EXISTS plugin_providers;
DROP TABLE IF EXISTS configurations;
```

## Support

For issues or questions:

1. Check the logs for error messages
2. Verify database connectivity and permissions
3. Test with a simple permission check
4. Review the API documentation
5. Check the test files for usage examples

The plugin permission system is designed to be backward compatible and should not affect existing functionality when properly integrated.
