# Centralized Plugin Permission System

## Overview

The Deploy Orchestrator provides a **centralized plugin permission system** that eliminates the need for individual plugins to implement their own permission checking logic. This system provides consistent, secure, and auditable access control across all plugins.

## ✅ **Benefits of Centralized Approach**

1. **🔒 Consistent Security** - All plugins use the same permission logic
2. **🚀 Easy Integration** - Single middleware for all permission checks
3. **📊 Centralized Auditing** - All plugin actions logged in one place
4. **🛠️ Easy Maintenance** - Update permission logic in one place
5. **🎯 Admin Bypass** - Ad<PERSON> automatically get access to all plugins
6. **📈 Performance** - Cached permission matrices for fast checks

## 🚀 **Quick Start for Plugin Developers**

### **Option 1: Middleware Approach (Recommended)**

```go
// In your plugin's route registration
func RegisterMyPluginRoutes(router *gin.RouterGroup, pluginPermissionService *service.PluginPermissionService) {
    // Create centralized middleware
    pluginMiddleware := middleware.NewPluginPermissionMiddleware(pluginPermissionService)
    
    // All routes under this group require plugin permissions
    myPluginRoutes := router.Group("/plugins/my-plugin")
    {
        // Anyone with 'view' permission can access
        myPluginRoutes.GET("/status", 
            pluginMiddleware.RequirePluginView(), 
            myPluginHandler.GetStatus)
        
        // Only users with 'deploy' permission can deploy
        myPluginRoutes.POST("/deploy", 
            pluginMiddleware.RequirePluginDeploy(), 
            myPluginHandler.Deploy)
        
        // Only users with 'configure' permission can configure
        myPluginRoutes.PUT("/config", 
            pluginMiddleware.RequirePluginConfigure(), 
            myPluginHandler.UpdateConfig)
        
        // Only users with 'manage' permission can manage
        myPluginRoutes.DELETE("/instances/:id", 
            pluginMiddleware.RequirePluginManage(), 
            myPluginHandler.DeleteInstance)
    }
}
```

### **Option 2: Service Approach**

```go
// In your plugin handler
type MyPluginHandler struct {
    pluginPermissionService *service.PluginPermissionService
}

func (h *MyPluginHandler) Deploy(c *gin.Context) {
    userID := c.GetString("userID")
    
    // Single line permission check - centralized!
    if !h.hasPluginPermission(c, userID, "my-plugin", "deploy") {
        c.JSON(403, gin.H{"error": "Permission denied"})
        return
    }
    
    // Your plugin logic here
    // ...
}

// Helper method for permission checking
func (h *MyPluginHandler) hasPluginPermission(c *gin.Context, userID, pluginName, action string) bool {
    request := &service.PermissionCheckRequest{
        Action:       action,
        Resource:     pluginName,
        ResourceType: "plugin",
        ProjectID:    c.Param("projectId"),
    }
    
    response, err := h.pluginPermissionService.CheckPermission(c.Request.Context(), userID, request)
    return err == nil && response.Allowed
}
```

## 🎯 **Available Permission Levels**

The centralized system supports these permission levels:

1. **`view`** - Can view plugin status and information
2. **`deploy`** - Can trigger deployments and executions
3. **`configure`** - Can modify plugin configuration
4. **`manage`** - Can install/uninstall and manage plugin lifecycle
5. **`admin`** - Full access to all plugin features

## 🔧 **Advanced Usage**

### **Multiple Permission Requirements**

```go
// Require ANY of the specified permissions
myPluginRoutes.POST("/advanced-action", 
    pluginMiddleware.RequireAnyPluginPermission("deploy", "manage"), 
    myPluginHandler.AdvancedAction)

// Require specific permission with custom action
myPluginRoutes.POST("/custom-action", 
    pluginMiddleware.RequirePluginPermission("custom-action"), 
    myPluginHandler.CustomAction)
```

### **Project-Scoped Permissions**

```go
// Permissions automatically respect project scope
myPluginRoutes.POST("/projects/:projectId/deploy", 
    pluginMiddleware.RequirePluginDeploy(), 
    myPluginHandler.ProjectDeploy)
```

### **Permission Context in Handlers**

```go
func (h *MyPluginHandler) SomeAction(c *gin.Context) {
    // Access permission context set by middleware
    pluginName := c.GetString("pluginName")
    action := c.GetString("pluginAction")
    permissions := c.Get("pluginPermissions")
    
    // Your logic here
}
```

## 🏗️ **Integration with Existing Plugins**

### **For Core System Plugins**

Core plugins (OpenShift, Kubernetes, Docker) already use centralized permissions through:

1. **Admin Service Permission Middleware**
2. **Plugin Permission Service**
3. **Automatic Permission Matrix Evaluation**

### **For New Custom Plugins**

1. **Use the centralized middleware** (recommended)
2. **Register your plugin** in the plugin access control system
3. **Define permission levels** your plugin supports
4. **Let the system handle** all permission checking

## 📊 **Automatic Features**

When you use the centralized system, you get these features automatically:

### **✅ Admin Bypass**
```go
// Admins automatically get access to ALL plugins
// No special code needed in your plugin
```

### **✅ Audit Logging**
```go
// All plugin actions are automatically logged
// Including successful access and denied attempts
```

### **✅ Permission Caching**
```go
// Permission matrices are cached for performance
// No need to worry about database hits
```

### **✅ Project Scoping**
```go
// Permissions automatically respect project boundaries
// Users only see plugins for their assigned projects
```

### **✅ Group Sync**
```go
// Permissions automatically sync from identity providers
// LDAP/SAML/OIDC groups map to plugin permissions
```

## 🔍 **Plugin Registration**

To register your plugin with the centralized system:

```go
// In your plugin initialization
func RegisterMyPlugin(pluginPermissionService *service.PluginPermissionService) error {
    // Define your plugin's access control
    accessControl := &models.PluginAccessControl{
        PluginName:    "my-plugin",
        PluginVersion: "1.0.0",
        Description:   "My custom plugin for Deploy Orchestrator",
        
        // Define supported permission levels
        SupportedActions: []string{"view", "deploy", "configure", "manage"},
        
        // Define supported templates (optional)
        SupportedTemplates: []string{"basic-deploy", "advanced-deploy"},
        
        // Define supported providers (optional)
        SupportedProviders: []string{"kubernetes", "docker"},
        
        // Define project scoping
        ProjectScoped: true,
        
        // Define environment scoping
        EnvironmentScoped: true,
    }
    
    return pluginPermissionService.RegisterPlugin(context.Background(), accessControl)
}
```

## 🚨 **Migration from Manual Permission Checks**

If you have existing plugins with manual permission checks:

### **Before (Manual)**
```go
func (h *MyPluginHandler) Deploy(c *gin.Context) {
    // Manual permission checking (DON'T DO THIS)
    userID := c.GetString("userID")
    
    // Custom permission logic
    var user models.User
    if err := h.db.Preload("Roles").Where("id = ?", userID).First(&user).Error; err != nil {
        c.JSON(401, gin.H{"error": "User not found"})
        return
    }
    
    // Manual role checking
    hasPermission := false
    for _, role := range user.Roles {
        if role.Name == "admin" || role.Name == "deployer" {
            hasPermission = true
            break
        }
    }
    
    if !hasPermission {
        c.JSON(403, gin.H{"error": "Permission denied"})
        return
    }
    
    // Plugin logic...
}
```

### **After (Centralized)**
```go
func (h *MyPluginHandler) Deploy(c *gin.Context) {
    // Centralized permission checking (DO THIS)
    // Permission already checked by middleware - just implement your logic!
    
    // Plugin logic...
}

// Route registration with middleware
myPluginRoutes.POST("/deploy", 
    pluginMiddleware.RequirePluginDeploy(), 
    myPluginHandler.Deploy)
```

## 🎯 **Best Practices**

1. **Always use middleware** for route-level permissions
2. **Use service approach** only for complex conditional logic
3. **Register your plugin** in the access control system
4. **Define clear permission levels** for your plugin features
5. **Respect project scoping** in your plugin logic
6. **Test with different user roles** to ensure proper access control

## 🔧 **Configuration**

The centralized system is configured through the main `config.yaml`:

```yaml
plugin_permissions:
  cache_ttl: "300s"
  default_access_level: "view"
  
  security:
    audit_logging:
      enabled: true
      log_level: "info"
```

## 📞 **Support**

For questions about integrating with the centralized plugin permission system:

1. Check the existing core plugins for examples
2. Review the test files for usage patterns
3. Use the centralized middleware for consistent behavior
4. Contact the platform team for custom permission requirements

The centralized approach ensures that all plugins have consistent, secure, and maintainable permission checking without duplicating code across plugins.
