# Plugin Permission System

## Overview

The Plugin Permission System provides comprehensive access control for plugins in the Deploy Orchestrator platform. It supports role-based, group-based, and user-specific permissions with automatic synchronization from identity providers.

## Architecture

### Core Components

1. **PluginPermissionService** - Main service for permission management
2. **PluginGroupSyncService** - Handles automatic group-to-plugin permission mapping
3. **PluginPermissionHandler** - REST API endpoints
4. **Models** - Database models for permissions, audit logs, and access control

### Permission Levels

- **view** - Can view plugin information and capabilities
- **deploy** - Can deploy using plugin templates (includes view)
- **configure** - Can configure plugin settings (includes deploy)
- **manage** - Can manage plugin lifecycle (includes configure)
- **admin** - Full plugin administration (includes manage + install/uninstall)

## Database Models

### Core Permission Models

```go
// PluginPermission - Defines available permissions
type PluginPermission struct {
    ID          string `json:"id" gorm:"primaryKey"`
    Name        string `json:"name"`
    Description string `json:"description"`
    Category    string `json:"category"`
    Resource    string `json:"resource"`
    Action      string `json:"action"`
    Scope       string `json:"scope"`
}

// PluginRolePermission - Role-based permissions
type PluginRolePermission struct {
    RoleID       string `json:"roleId"`
    PermissionID string `json:"permissionId"`
    Granted      bool   `json:"granted"`
    GrantedBy    string `json:"grantedBy"`
}

// PluginGroupAccess - Group-based plugin access
type PluginGroupAccess struct {
    GroupID             string `json:"groupId"`
    PluginName          string `json:"pluginName"`
    CanView             bool   `json:"canView"`
    CanInstall          bool   `json:"canInstall"`
    CanConfigure        bool   `json:"canConfigure"`
    CanManage           bool   `json:"canManage"`
    CanDeploy           bool   `json:"canDeploy"`
    AllowedTemplates    string `json:"allowedTemplates"`    // JSON array
    AllowedProviders    string `json:"allowedProviders"`    // JSON array
    AllowedProjects     string `json:"allowedProjects"`     // JSON array
    AllowedEnvironments string `json:"allowedEnvironments"` // JSON array
}
```

### Audit and Control Models

```go
// PluginAuditLog - Audit trail for plugin actions
type PluginAuditLog struct {
    UserID         string    `json:"userId"`
    UserName       string    `json:"userName"`
    Action         string    `json:"action"`
    Resource       string    `json:"resource"`
    ResourceType   string    `json:"resourceType"`
    Details        string    `json:"details"`
    Timestamp      time.Time `json:"timestamp"`
    IPAddress      string    `json:"ipAddress"`
    PermissionUsed string    `json:"permissionUsed"`
    ProjectID      *string   `json:"projectId"`
    EnvironmentID  *string   `json:"environmentId"`
}

// PluginAccessControl - Plugin-specific access rules
type PluginAccessControl struct {
    PluginName                string `json:"pluginName"`
    RequiredPermissions       string `json:"requiredPermissions"`       // JSON
    PublicFeatures            string `json:"publicFeatures"`            // JSON
    AdminOnlyFeatures         string `json:"adminOnlyFeatures"`         // JSON
    ProjectScopedFeatures     string `json:"projectScopedFeatures"`     // JSON
    EnvironmentScopedFeatures string `json:"environmentScopedFeatures"` // JSON
}
```

## Group Sync System

### Default Group Mappings

The system provides default group-to-plugin mappings based on group name patterns:

```go
var defaultMappings = []PluginGroupMapping{
    {
        GroupPattern:     ".*-developers?",
        PluginName:       "*",
        AccessLevel:      "deploy",
        AllowedProjects:  []string{"*"},
        AllowedTemplates: []string{"basic-deploy", "s2i-deploy"},
        AllowedProviders: []string{"*"},
    },
    {
        GroupPattern:     ".*-operators?",
        PluginName:       "*",
        AccessLevel:      "manage",
        AllowedProjects:  []string{"*"},
        AllowedTemplates: []string{"*"},
        AllowedProviders: []string{"*"},
    },
    {
        GroupPattern:     ".*-admins?",
        PluginName:       "*",
        AccessLevel:      "admin",
        AllowedProjects:  []string{"*"},
        AllowedTemplates: []string{"*"},
        AllowedProviders: []string{"*"},
    },
    {
        GroupPattern:     ".*-viewers?",
        PluginName:       "*",
        AccessLevel:      "view",
        AllowedProjects:  []string{"*"},
        AllowedTemplates: []string{},
        AllowedProviders: []string{},
    },
}
```

### Pattern Matching

- Supports regex patterns with case-insensitive matching
- Wildcard `*` matches any group
- Examples:
  - `.*-developers?` matches `project-a-developers`, `team-developer`, etc.
  - `admin.*` matches `admin-team`, `administrators`, etc.

## API Endpoints

### Permission Management

```http
GET /api/v1/plugin-permissions/matrix
# Returns user's complete permission matrix

GET /api/v1/plugin-permissions/context
# Returns user's security context (roles, permissions, groups)

POST /api/v1/plugin-permissions/check
# Checks if user has specific permission
```

### Audit and Logging

```http
POST /api/v1/plugin-permissions/audit-logs
# Log plugin action

GET /api/v1/plugin-permissions/audit-logs
# Get audit logs (admin only)
# Query params: pluginName, userId, action, limit, offset
```

### Access Control

```http
GET /api/v1/plugin-permissions/access-control/{pluginName}
# Get plugin access control configuration
```

## Usage Examples

### Check Permission

```go
// Check if user can install plugins
request := service.PermissionCheckRequest{
    Action:       "install",
    Resource:     "my-plugin",
    ResourceType: "plugin",
}

response, err := pluginPermissionService.CheckPermission(ctx, userID, request)
if err != nil {
    return err
}

if response.Allowed {
    // User can install the plugin
    fmt.Println("Permission granted:", response.GrantedPermissions)
} else {
    // Permission denied
    fmt.Println("Permission denied:", response.Reason)
}
```

### Sync Group Permissions

```go
// Sync permissions for a specific user
err := pluginGroupSyncService.SyncPluginGroupAccess(ctx, userID)
if err != nil {
    return fmt.Errorf("failed to sync permissions: %w", err)
}

// Sync permissions for all users (dry run)
result, err := pluginGroupSyncService.SyncAllUsersPluginAccess(ctx, true)
if err != nil {
    return err
}

fmt.Printf("Would process %d groups and create %d permissions\n",
    result.GroupsProcessed, result.PluginAccessCreated)
```

### Log Plugin Action

```go
// Log a plugin deployment action
err := pluginPermissionService.LogPluginAction(ctx, service.PluginActionLog{
    UserID:       userID,
    Action:       "deploy",
    Resource:     "my-plugin",
    ResourceType: "plugin",
    Details: map[string]interface{}{
        "projectId":     "project-123",
        "environmentId": "prod",
        "success":       true,
        "duration":      "30s",
    },
    IPAddress: "*************",
    UserAgent: "Deploy-CLI/1.0",
})
```

## Configuration

The plugin permissions system is configured through the main `config.yaml` file in the admin-service:

```yaml
plugin_permissions:
  # Core settings (can be overridden by environment variables)
  cache_ttl: "300s"                    # PLUGIN_PERMISSION_CACHE_TTL
  audit_retention_days: 90             # PLUGIN_AUDIT_RETENTION_DAYS
  group_sync_interval: "1h"            # PLUGIN_GROUP_SYNC_INTERVAL
  default_access_level: "view"         # PLUGIN_DEFAULT_ACCESS_LEVEL

  security:
    audit_logging:
      enabled: true
      log_level: "info"
      retention_days: 90

    session_timeout:
      enabled: true
      timeout_minutes: 480  # 8 hours
      warning_minutes: 30

    rate_limiting:
      enabled: true
      requests_per_minute: 100
      burst_limit: 200

  features:
    plugin_marketplace:
      enabled: true
      allow_external_sources: false
      require_approval: true

    hot_reload:
      enabled: true
      allow_in_production: false

    plugin_metrics:
      enabled: true
      collect_usage_stats: true
      share_anonymous_data: false

    advanced_permissions:
      enabled: true
      allow_custom_roles: true
      allow_temporary_access: true
```

### Environment Variables (override config.yaml)

```bash
# Database configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=deploy_orchestrator
DB_USER=admin
DB_PASSWORD=secret

# Plugin permission settings
PLUGIN_PERMISSION_CACHE_TTL=300s
PLUGIN_AUDIT_RETENTION_DAYS=90
PLUGIN_GROUP_SYNC_INTERVAL=1h
PLUGIN_DEFAULT_ACCESS_LEVEL=view
```

### Database Migration

The plugin permission tables are automatically created through GORM migrations:

```go
// Run migrations
err := db.AutoMigrate(
    &models.PluginPermission{},
    &models.PluginRolePermission{},
    &models.PluginGroupPermission{},
    &models.PluginUserPermission{},
    &models.PluginAccessControl{},
    &models.PluginAuditLog{},
    &models.PluginPermissionPolicy{},
    &models.PluginTemplate{},
    &models.PluginProvider{},
    &models.PluginGroupAccess{},
    &models.Configuration{},
)
```

## Testing

The system includes comprehensive tests:

- **Unit Tests**: Service layer testing with SQLite in-memory database
- **Integration Tests**: API endpoint testing with mock authentication
- **Test Coverage**: All major functionality covered

Run tests:

```bash
# Run all tests
go test ./...

# Run specific test suites
go test ./service -v
go test ./api -v

# Run with coverage
go test ./... -cover
```

## Security Considerations

1. **Authentication Required**: All endpoints require valid user authentication
2. **Admin-Only Operations**: Audit log access restricted to administrators
3. **Permission Validation**: All actions validated against user permissions
4. **Audit Trail**: Complete audit trail for all plugin operations
5. **Input Validation**: All inputs validated and sanitized
6. **SQL Injection Protection**: Using GORM with parameterized queries

## Future Enhancements

1. **Permission Caching**: Redis-based permission caching for performance
2. **Real-time Sync**: WebSocket-based real-time permission updates
3. **Advanced Policies**: More sophisticated policy rules and conditions
4. **Permission Analytics**: Usage analytics and permission optimization
5. **Multi-tenant Support**: Tenant-scoped permissions and isolation
