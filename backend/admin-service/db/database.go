package db

import (
	"fmt"
	"log"
	"os"

	"golang.org/x/crypto/bcrypt"

	"github.com/claudio/deploy-orchestrator/admin-service/config"
	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// InitDatabase initializes the database connection and runs migrations
func InitDatabase(cfg *config.AdminConfig) (*gorm.DB, error) {
	// Configure GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}

	// Create database connection
	dsn := cfg.GetDatabaseURI()
	db, err := gorm.Open(postgres.Open(dsn), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Run all migrations
	if err := RunMigrations(db); err != nil {
		return nil, fmt.Errorf("failed to run migrations: %w", err)
	}

	// Seed test data in development mode
	// Check if we're in development mode by looking at environment variables
	if os.Getenv("APP_ENV") == "development" || os.Getenv("ENV") == "development" {
		if err := SeedTestData(db); err != nil {
			log.Printf("Warning: Failed to seed test data: %v", err)
		}
	}

	return db, nil
}

// SeedAdminUser creates a default admin user if no users exist
func SeedAdminUser(db *gorm.DB) error {
	// Get the config to read default admin user settings
	cfg, err := config.LoadConfig()
	if err != nil {
		return fmt.Errorf("failed to load config for admin user: %w", err)
	}

	var count int64
	if err := db.Model(&models.User{}).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to count users: %w", err)
	}

	// Only seed if there are no users
	if count == 0 {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(cfg.DefaultAdmin.Password), bcrypt.DefaultCost)
		if err != nil {
			return err
		}
		admin := models.User{
			Username:       cfg.DefaultAdmin.Username,
			Email:          cfg.DefaultAdmin.Email,
			HashedPassword: string(hashedPassword), // password: admin
			FirstName:      "Admin",
			LastName:       "User",
			IsAdmin:        true,
			IsActive:       true,
		}

		if err := db.Create(&admin).Error; err != nil {
			return fmt.Errorf("failed to create admin user: %w", err)
		}

		// Assign to 'admins' group if it exists
		var adminsGroup models.Group
		if err := db.Where("name = ? AND source = ?", "admins", "local").First(&adminsGroup).Error; err == nil {
			db.Model(&admin).Association("Groups").Append(&adminsGroup)
		}

		// Assign to 'admin' role if it exists
		var adminRole models.Role
		if err := db.Where("name = ?", "admin").First(&adminRole).Error; err == nil {
			db.Model(&admin).Association("Roles").Append(&adminRole)
		}

		log.Println("Created default admin user and assigned to 'admins' group and 'admin' role")
	}

	return nil
}

// SeedRBAC seeds default roles, permissions, and groups for the RBAC system
func SeedRBAC(db *gorm.DB) error {
	// Define default permissions
	permissions := []models.Permission{
		// User permissions
		{Name: "user:read", Description: "Read user information"},
		{Name: "user:write", Description: "Modify user information"},
		{Name: "user:delete", Description: "Delete users"},

		// Group permissions
		{Name: "group:read", Description: "Read group information"},
		{Name: "group:write", Description: "Modify group information"},
		{Name: "group:delete", Description: "Delete groups"},

		// Role permissions
		{Name: "role:read", Description: "Read role information"},
		{Name: "role:write", Description: "Modify role information"},
		{Name: "role:delete", Description: "Delete roles"},

		// Permission permissions
		{Name: "permission:read", Description: "Read permission information"},
		{Name: "permission:write", Description: "Modify permission information"},

		// Project permissions
		{Name: "project:read", Description: "Read project information"},
		{Name: "project:write", Description: "Modify project information"},
		{Name: "project:delete", Description: "Delete projects"},
		{Name: "project:assign", Description: "Assign entities to projects"},

		// Identity provider permissions
		{Name: "idp:read", Description: "Read identity provider information"},
		{Name: "idp:write", Description: "Modify identity provider information"},
		{Name: "idp:delete", Description: "Delete identity providers"},
		{Name: "idp:configure", Description: "Configure identity providers"},

		// Workflow permissions
		{Name: "workflow:view", Description: "View workflows and templates"},
		{Name: "workflow:create", Description: "Create workflows"},
		{Name: "workflow:update", Description: "Update workflows"},
		{Name: "workflow:delete", Description: "Delete workflows"},
		{Name: "workflow:execute", Description: "Execute workflows"},
		{Name: "workflow:monitor", Description: "Monitor workflow executions"},

		// Template permissions
		{Name: "template:publish", Description: "Publish workflow templates"},
		{Name: "template:analytics", Description: "View template analytics"},
		{Name: "template:export", Description: "Export workflow templates"},
		{Name: "template:import", Description: "Import workflow templates"},

		// Admin permissions
		{Name: "admin:all", Description: "Full admin access"},
	}

	for _, perm := range permissions {
		var existing models.Permission
		db.Where("name = ?", perm.Name).FirstOrCreate(&existing, perm)
	}

	// Define default roles
	roles := []struct {
		Name        string
		Description string
		Perms       []string
	}{
		{"admin", "Administrator with all permissions", []string{"admin:all"}},
		{"user_manager", "User manager", []string{"user:read", "user:write", "user:delete"}},
		{"group_manager", "Group manager", []string{"group:read", "group:write", "group:delete"}},
		{"role_manager", "Role manager", []string{"role:read", "role:write", "role:delete"}},
		{"project_manager", "Project manager", []string{"project:read", "project:write", "project:delete", "project:assign"}},
		{"project_viewer", "Project viewer", []string{"project:read"}},
		{"idp_manager", "Identity Provider manager", []string{"idp:read", "idp:write", "idp:delete", "idp:configure"}},
		{"workflow_admin", "Workflow administrator", []string{"workflow:view", "workflow:create", "workflow:update", "workflow:delete", "workflow:execute", "workflow:monitor", "template:publish", "template:analytics", "template:export", "template:import"}},
		{"workflow_manager", "Workflow manager", []string{"workflow:view", "workflow:create", "workflow:update", "workflow:execute", "workflow:monitor", "template:publish"}},
		{"workflow_user", "Workflow user", []string{"workflow:view", "workflow:execute"}},
		{"workflow_viewer", "Workflow viewer", []string{"workflow:view", "workflow:monitor"}},
	}

	for _, role := range roles {
		var r models.Role
		db.Where("name = ?", role.Name).FirstOrCreate(&r, models.Role{Name: role.Name, Description: role.Description})
		// Assign permissions
		var perms []models.Permission
		for _, pname := range role.Perms {
			var p models.Permission
			db.Where("name = ?", pname).First(&p)
			if p.ID != "" {
				perms = append(perms, p)
			}
		}
		db.Model(&r).Association("Permissions").Replace(perms)
	}

	// Define default groups
	groups := []struct {
		Name   string
		Source string
	}{
		{"admins", "local"},
		{"users", "local"},
	}

	for _, group := range groups {
		var g models.Group
		db.Where("name = ? AND source = ?", group.Name, group.Source).FirstOrCreate(&g, models.Group{Name: group.Name, Source: group.Source})
	}

	return nil
}
