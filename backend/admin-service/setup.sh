#!/bin/bash

# Setup script for the admin service

# Verify Go installation
if ! command -v go &> /dev/null; then
    echo "Error: Go is not installed. Please install Go before continuing."
    exit 1
fi

# Verify PostgreSQL
if ! command -v psql &> /dev/null; then
    echo "Warning: PostgreSQL client not found. You may need to install PostgreSQL."
    echo "You can download PostgreSQL from: https://www.postgresql.org/download/"
fi

# Install dependencies
echo "Installing Go dependencies..."
go mod tidy

# Create database if it doesn't exist
if command -v psql &> /dev/null; then
    echo "Would you like to set up the PostgreSQL database? (y/n)"
    read answer
    
    if [ "$answer" = "y" ] || [ "$answer" = "Y" ]; then
        echo "Please enter PostgreSQL superuser (usually postgres):"
        read pguser
        
        echo "Creating database..."
        psql -U $pguser -c "CREATE DATABASE admin_service;" || echo "Database may already exist or connection failed"
        psql -U $pguser -c "GRANT ALL PRIVILEGES ON DATABASE admin_service TO postgres;" || echo "Failed to set permissions"
        
        echo "Database setup complete!"
    fi
fi

# Generate default config file
echo "Generating default config file..."
mkdir -p config
go run main.go -generate-config || {
    echo "Failed to generate config file. Creating a basic one manually..."
    cat > config/config.yaml << EOL
# Server settings
server:
  server_port: 8080
  server_host: "0.0.0.0"
  environment: "development"

# Database settings
database:
  db_host: "localhost"
  db_port: 5432
  db_user: "postgres"
  db_password: "postgres"
  db_name: "admin_service"
  db_ssl_mode: "disable"

# Authentication settings
auth:
  jwt_secret: "change-me-in-production"
  access_token_expiry: "24h"
  refresh_token_expiry: "168h"

# Default admin
default_admin:
  username: "admin"
  password: "admin"
  email: "<EMAIL>"
EOL
}

echo "----------------------------------------------"
echo "Setup complete!"
echo "1. Update the .env file with your settings"
echo "2. Run the service with ./run.sh"
echo "----------------------------------------------"
