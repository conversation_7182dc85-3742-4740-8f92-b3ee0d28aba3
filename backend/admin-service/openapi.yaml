openapi: 3.0.3
info:
  title: Admin Service RBAC API
  version: 1.0.0
  description: |
    API for user, group, role, and permission management with RBAC assignments and queries.
servers:
  - url: /api
paths:
  /rbac/groups:
    get:
      summary: List all groups
      responses:
        '200': { description: List of groups }
    post:
      summary: Create a group
      requestBody:
        required: true
        content:
          application/json:
            schema: { $ref: '#/components/schemas/Group' }
      responses:
        '201': { description: Group created }
  /rbac/groups/{id}:
    put:
      summary: Update a group
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: string }
      requestBody:
        required: true
        content:
          application/json:
            schema: { $ref: '#/components/schemas/Group' }
      responses:
        '200': { description: Group updated }
    delete:
      summary: Delete a group
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: string }
      responses:
        '204': { description: Group deleted }
  /rbac/roles:
    get:
      summary: List all roles
      responses:
        '200': { description: List of roles }
    post:
      summary: Create a role
      requestBody:
        required: true
        content:
          application/json:
            schema: { $ref: '#/components/schemas/Role' }
      responses:
        '201': { description: Role created }
  /rbac/roles/{id}:
    put:
      summary: Update a role
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: string }
      requestBody:
        required: true
        content:
          application/json:
            schema: { $ref: '#/components/schemas/Role' }
      responses:
        '200': { description: Role updated }
    delete:
      summary: Delete a role
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: string }
      responses:
        '204': { description: Role deleted }
  /rbac/permissions:
    get:
      summary: List all permissions
      responses:
        '200': { description: List of permissions }
    post:
      summary: Create a permission
      requestBody:
        required: true
        content:
          application/json:
            schema: { $ref: '#/components/schemas/Permission' }
      responses:
        '201': { description: Permission created }
  /rbac/permissions/{id}:
    put:
      summary: Update a permission
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: string }
      requestBody:
        required: true
        content:
          application/json:
            schema: { $ref: '#/components/schemas/Permission' }
      responses:
        '200': { description: Permission updated }
    delete:
      summary: Delete a permission
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: string }
      responses:
        '204': { description: Permission deleted }
  /rbac/assign/user-group:
    post:
      summary: Assign user to group
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userId: { type: string }
                groupId: { type: string }
      responses:
        '204': { description: User assigned to group }
  /rbac/assign/group-role:
    post:
      summary: Assign group to role
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                groupId: { type: string }
                roleId: { type: string }
      responses:
        '204': { description: Group assigned to role }
  /rbac/assign/role-permission:
    post:
      summary: Assign permission to role
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                roleId: { type: string }
                permissionId: { type: string }
      responses:
        '204': { description: Permission assigned to role }
  /rbac/users/{id}/permissions:
    get:
      summary: Get all effective permissions for a user
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: string }
      responses:
        '200': { description: List of permissions }
  /rbac/users/{id}/roles:
    get:
      summary: Get all roles for a user
      parameters:
        - in: path
          name: id
          required: true
          schema: { type: string }
      responses:
        '200': { description: List of roles }
  /rbac/groups/{groupId}/roles/{roleId}:
    post:
      summary: Assign a role to a group
      parameters:
        - in: path
          name: groupId
          required: true
          schema: { type: string }
        - in: path
          name: roleId
          required: true
          schema: { type: string }
      responses:
        '204': { description: Role assigned to group }
    delete:
      summary: Remove a role from a group
      parameters:
        - in: path
          name: groupId
          required: true
          schema: { type: string }
        - in: path
          name: roleId
          required: true
          schema: { type: string }
      responses:
        '204': { description: Role removed from group }
  /rbac/groups/{groupId}/roles:
    get:
      summary: List all roles for a group
      parameters:
        - in: path
          name: groupId
          required: true
          schema: { type: string }
      responses:
        '200':
          description: List of roles for the group
          content:
            application/json:
              schema:
                type: array
                items: { $ref: '#/components/schemas/Role' }
components:
  schemas:
    Group:
      type: object
      properties:
        id: { type: string }
        name: { type: string }
        source: { type: string }
        externalId: { type: string }
        users:
          type: array
          items: { $ref: '#/components/schemas/User' }
        roles:
          type: array
          items: { $ref: '#/components/schemas/Role' }
    Role:
      type: object
      properties:
        id: { type: string }
        name: { type: string }
        description: { type: string }
        permissions:
          type: array
          items: { $ref: '#/components/schemas/Permission' }
        groups:
          type: array
          items: { $ref: '#/components/schemas/Group' }
        users:
          type: array
          items: { $ref: '#/components/schemas/User' }
    Permission:
      type: object
      properties:
        id: { type: string }
        name: { type: string }
        description: { type: string }
    User:
      type: object
      properties:
        id: { type: string }
        username: { type: string }
        email: { type: string }
        firstName: { type: string }
        lastName: { type: string }
        isAdmin: { type: boolean }
        isActive: { type: boolean }
        lastLogin: { type: string, format: date-time }
        groups:
          type: array
          items: { $ref: '#/components/schemas/Group' }
        roles:
          type: array
          items: { $ref: '#/components/schemas/Role' }
