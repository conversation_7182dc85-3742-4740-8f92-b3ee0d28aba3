version: '3.8'

services:
  postgres:
    image: postgres:14-alpine
    container_name: admin-service-db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: admin_service
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  admin-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: admin-service
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./config:/app/config
    environment:
      # Only override essential settings via environment variables
      # The rest will be read from config/config.yaml
      DB_HOST: postgres
    ports:
      - "8080:8080"
    restart: unless-stopped

volumes:
  postgres_data:
