package api

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"github.com/claudio/deploy-orchestrator/admin-service/service"
	"github.com/claudio/deploy-orchestrator/admin-service/service/provider"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ExternalLoginRequest contains the information needed to initiate an external login
type ExternalLoginRequest struct {
	ProviderID   string                        `json:"providerId" binding:"required"`
	ProviderType provider.IdentityProviderType `json:"providerType" binding:"required"`
}

// ExternalLoginResponse contains the information needed to redirect the user to the identity provider
type ExternalLoginResponse struct {
	RedirectURL string `json:"redirectUrl"`
	State       string `json:"state"`
}

// ExternalLoginCallbackRequest contains the information returned from the identity provider
type ExternalLoginCallbackRequest struct {
	Code  string `json:"code" binding:"required"`
	State string `json:"state" binding:"required"`
}

// InitiateExternalLogin initiates an external login flow
func (h *Auth<PERSON>andler) InitiateExternalLogin(c *gin.Context) {
	var req ExternalLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Invalid external login request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// Generate a state parameter to prevent CSRF
	state := uuid.New().String()

	// Store the state in a temporary session or database
	// This is a simplified example - in a real implementation, you would store this securely
	// and associate it with the current session
	stateKey := fmt.Sprintf("external_login_state:%s", state)
	h.stateStore[stateKey] = time.Now().Add(10 * time.Minute).Unix() // 10 minute expiry

	var redirectURL string
	var err error

	// Get the redirect URL based on the provider type
	switch req.ProviderType {
	case provider.IdpOIDC:
		if h.oidcProvider == nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "OIDC provider not configured"})
			return
		}
		redirectURL, err = h.oidcProvider.GetAuthorizationURL(c, state, req.ProviderID)
	case provider.IdpSAML:
		if h.samlProvider == nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "SAML provider not configured"})
			return
		}
		redirectURL, err = h.samlProvider.GetAuthorizationURL(c, state, req.ProviderID)
	case provider.IdpLDAP:
		// LDAP doesn't use a redirect flow, but we'll include it for completeness
		c.JSON(http.StatusBadRequest, gin.H{"error": "LDAP does not support external login flow"})
		return
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "Unsupported provider type"})
		return
	}

	if err != nil {
		log.Printf("Failed to get authorization URL: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initiate external login"})
		return
	}

	c.JSON(http.StatusOK, ExternalLoginResponse{
		RedirectURL: redirectURL,
		State:       state,
	})
}

// HandleExternalLoginCallback handles the callback from an external identity provider
func (h *AuthHandler) HandleExternalLoginCallback(c *gin.Context) {
	var req ExternalLoginCallbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Invalid callback request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// Validate the state parameter
	stateKey := fmt.Sprintf("external_login_state:%s", req.State)
	expiryTime, exists := h.stateStore[stateKey]
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid state parameter"})
		return
	}

	// Check if the state has expired
	if time.Now().Unix() > expiryTime {
		delete(h.stateStore, stateKey)
		c.JSON(http.StatusBadRequest, gin.H{"error": "State parameter expired"})
		return
	}

	// Clean up the state
	delete(h.stateStore, stateKey)

	// Determine the provider type from the state
	// In a real implementation, you would store this information with the state
	// For this example, we'll try to extract it from the request context
	providerType := c.GetString("provider_type")
	if providerType == "" {
		// Try to determine from the request path or headers
		providerType = c.Query("provider_type")
		if providerType == "" {
			providerType = c.GetHeader("X-Provider-Type")
			if providerType == "" {
				// Default to OIDC
				providerType = string(provider.IdpOIDC)
			}
		}
	}

	var user *models.User
	var err error

	// Process the callback based on the provider type
	switch provider.IdentityProviderType(providerType) {
	case provider.IdpOIDC:
		if h.oidcProvider == nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "OIDC provider not configured"})
			return
		}
		user, err = h.oidcProvider.HandleCallback(c, req.Code, req.State)
	case provider.IdpSAML:
		if h.samlProvider == nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "SAML provider not configured"})
			return
		}
		user, err = h.samlProvider.HandleCallback(c, req.Code, req.State)
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "Unsupported provider type"})
		return
	}

	if err != nil {
		log.Printf("Failed to handle callback: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process external login"})
		return
	}

	if user == nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication failed"})
		return
	}

	// Sync user groups if needed
	if h.groupSyncService != nil {
		// Get the provider type from the context
		idpType := provider.IdentityProviderType(providerType)

		// Fetch groups from the identity provider
		var idpGroups []string
		var fetchErr error

		switch idpType {
		case provider.IdpOIDC:
			idpGroups, fetchErr = h.oidcProvider.FetchGroupsForUser(c, user.Username)
		case provider.IdpSAML:
			idpGroups, fetchErr = h.samlProvider.FetchGroupsForUser(c, user.Username)
		case provider.IdpLDAP:
			// LDAP doesn't use the external login flow, but we'll include it for completeness
			log.Printf("LDAP group sync not applicable in external login flow")
		}

		if fetchErr != nil {
			log.Printf("Warning: Failed to fetch groups from %s provider: %v", idpType, fetchErr)
		} else if len(idpGroups) > 0 {
			// Sync the groups to the user
			// Convert provider type to service.IdentityProviderType
			if syncErr := h.groupSyncService.SyncUserGroups(c, user, service.IdentityProviderType(string(idpType)), idpGroups); syncErr != nil {
				log.Printf("Warning: Failed to sync user groups: %v", syncErr)
			} else {
				log.Printf("Successfully synced %d groups for user %s from %s provider",
					len(idpGroups), user.Username, idpType)
			}
		}
	}

	// Get user roles
	var roles []string
	if user.IsAdmin {
		roles = append(roles, "admin")
	}

	// Get roles from user's groups
	if err := h.userService.DB().Preload("Groups.Roles").First(&user, user.ID).Error; err != nil {
		log.Printf("Warning: Failed to load user groups and roles: %v", err)
	} else {
		// Extract roles from groups
		for _, group := range user.Groups {
			for _, role := range group.Roles {
				// Add role name to roles list if not already present
				roleExists := false
				for _, existingRole := range roles {
					if existingRole == role.Name {
						roleExists = true
						break
					}
				}
				if !roleExists {
					roles = append(roles, role.Name)
				}
			}
		}
	}

	log.Printf("User %s has roles: %v", user.Username, roles)

	// Generate access token
	token, err := h.jwtManager.GenerateAccessToken(user.ID, user.Username, user.Email, roles)
	if err != nil {
		log.Printf("Failed to generate token: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Authentication service error"})
		return
	}

	// Generate refresh token
	refreshToken, err := h.jwtManager.GenerateRefreshToken(user.ID, user.Username, user.Email, roles)
	if err != nil {
		log.Printf("Failed to generate refresh token: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Authentication service error"})
		return
	}

	// Calculate expiration time
	expiresAt := time.Now().Add(24 * time.Hour)

	// Update the user's last login time
	if err := h.userService.UpdateLastLogin(c, user.ID); err != nil {
		// Just log the error, don't fail the login
		log.Printf("Failed to update last login time: %v", err)
	}

	// Fetch user's groups for response
	var groups []string
	userGroups, err := h.userService.GetUserGroups(c, user.ID)
	if err == nil && userGroups != nil {
		groups = make([]string, len(userGroups))
		for i, g := range userGroups {
			groups[i] = g.Name
		}
	}

	// Return the response
	c.JSON(http.StatusOK, LoginResponse{
		Token:        token,
		RefreshToken: refreshToken,
		ExpiresAt:    expiresAt,
		UserID:       user.ID,
		Username:     user.Username,
		Email:        user.Email,
		FirstName:    user.FirstName,
		LastName:     user.LastName,
		IsAdmin:      user.IsAdmin,
		Groups:       groups,
	})
}
