package api

import (
	"net/http"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"github.com/claudio/deploy-orchestrator/admin-service/service"
	"github.com/gin-gonic/gin"
)

// ProjectHandler handles HTTP requests for project management
type ProjectHandler struct {
	ProjectService    *service.ProjectService
	PermissionService *service.PermissionService
}

// NewProjectHandler creates a new project handler
func NewProjectHandler(projectService *service.ProjectService, permissionService *service.PermissionService) *ProjectHandler {
	return &ProjectHandler{
		ProjectService:    projectService,
		PermissionService: permissionService,
	}
}

// CreateProject handles the creation of a new project
// POST /api/v1/projects
func (h *ProjectHandler) CreateProject(c *gin.Context) {
	var project models.Project
	if err := c.ShouldBindJSON(&project); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.ProjectService.CreateProject(c.Request.Context(), &project); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, project)
}

// GetProject retrieves a project by ID
// GET /api/projects/:id
func (h *ProjectHandler) GetProject(c *gin.Context) {
	id := c.Param("projectId")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "project ID is required"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Check if user is admin
	isAdmin := false
	roles, exists := c.Get("roles")
	if exists {
		rolesList, ok := roles.([]string)
		if ok {
			for _, role := range rolesList {
				if role == "admin" {
					isAdmin = true
					break
				}
			}
		}
	}

	// Get the project
	project, err := h.ProjectService.GetProjectWithGroups(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// If user is not admin, check if they have access to the project
	if !isAdmin {
		hasAccess, err := h.PermissionService.CheckUserHasProjectAccess(c.Request.Context(), userID.(string), id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check project access: " + err.Error()})
			return
		}
		if !hasAccess {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to this project"})
			return
		}
	}

	c.JSON(http.StatusOK, project)
}

// ListProjects retrieves all projects that the user has access to
// GET /api/projects
func (h *ProjectHandler) ListProjects(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Check if user is admin
	isAdmin := false
	roles, exists := c.Get("roles")
	if exists {
		rolesList, ok := roles.([]string)
		if ok {
			for _, role := range rolesList {
				if role == "admin" {
					isAdmin = true
					break
				}
			}
		}
	}

	var projects []models.Project
	var err error

	// If user is admin, return all projects
	if isAdmin {
		projects, err = h.ProjectService.ListProjects(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
	} else {
		// For normal users, return only projects they have access to
		projects, err = h.PermissionService.GetAccessibleProjectsForUser(c.Request.Context(), userID.(string))
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
	}

	c.JSON(http.StatusOK, projects)
}

// UpdateProject updates a project
// PUT /api/projects/:id
func (h *ProjectHandler) UpdateProject(c *gin.Context) {
	id := c.Param("projectId")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "project ID is required"})
		return
	}

	var project models.Project
	if err := c.ShouldBindJSON(&project); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	project.ID = id
	if err := h.ProjectService.UpdateProject(c.Request.Context(), &project); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, project)
}

// DeleteProject deletes a project
// DELETE /api/projects/:id
func (h *ProjectHandler) DeleteProject(c *gin.Context) {
	id := c.Param("projectId")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "project ID is required"})
		return
	}

	if err := h.ProjectService.DeleteProject(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// AssignGroupToProject assigns a group to a project
// POST /api/projects/:projectId/groups/:groupId
func (h *ProjectHandler) AssignGroupToProject(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "project ID is required"})
		return
	}

	groupID := c.Param("groupId")
	if groupID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "group ID is required"})
		return
	}

	if err := h.ProjectService.AssignGroupToProject(c.Request.Context(), projectID, groupID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// RemoveGroupFromProject removes a group from a project
// DELETE /api/projects/:projectId/groups/:groupId
func (h *ProjectHandler) RemoveGroupFromProject(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "project ID is required"})
		return
	}

	groupID := c.Param("groupId")
	if groupID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "group ID is required"})
		return
	}

	if err := h.ProjectService.RemoveGroupFromProject(c.Request.Context(), projectID, groupID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetProjectsForUser retrieves all projects that a user has access to
// GET /api/users/:userId/projects
func (h *ProjectHandler) GetProjectsForUser(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "user ID is required"})
		return
	}

	projects, err := h.ProjectService.GetProjectsForUser(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, projects)
}

// CheckUserProjectAccess checks if a user has access to a specific project
// GET /api/users/:userId/projects/:projectId/access
func (h *ProjectHandler) CheckUserProjectAccess(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "user ID is required"})
		return
	}

	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "project ID is required"})
		return
	}

	// Check if user is admin
	isAdmin := false
	roles, exists := c.Get("roles")
	if exists {
		rolesList, ok := roles.([]string)
		if ok {
			for _, role := range rolesList {
				if role == "admin" {
					isAdmin = true
					break
				}
			}
		}
	}

	// If user is admin, they have access to all projects
	if isAdmin {
		c.JSON(http.StatusOK, gin.H{"access": true, "reason": "User is admin"})
		return
	}

	// Check if user has access to the project
	hasAccess, err := h.PermissionService.CheckUserHasProjectAccess(c.Request.Context(), userID, projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check project access: " + err.Error()})
		return
	}

	if hasAccess {
		c.JSON(http.StatusOK, gin.H{"access": true, "reason": "User has access to project"})
	} else {
		c.JSON(http.StatusForbidden, gin.H{"access": false, "reason": "User does not have access to project"})
	}
}

// GetGroupsForProject retrieves all groups assigned to a project
// GET /api/projects/:projectId/groups
func (h *ProjectHandler) GetGroupsForProject(c *gin.Context) {
	projectID := c.Param("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "project ID is required"})
		return
	}

	groups, err := h.ProjectService.GetGroupsForProject(c.Request.Context(), projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, groups)
}
