package api

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"github.com/claudio/deploy-orchestrator/admin-service/service"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// Test models that work with SQLite (no UUID generation)
type TestAPIUser struct {
	ID             string `gorm:"primaryKey"`
	CreatedAt      time.Time
	UpdatedAt      time.Time
	DeletedAt      *time.Time     `gorm:"index"`
	Username       string         `gorm:"uniqueIndex;not null"`
	Email          string         `gorm:"uniqueIndex;not null"`
	HashedPassword string         `gorm:"not null"`
	IsAdmin        bool           `gorm:"default:false"`
	IsActive       bool           `gorm:"default:true"`
	Roles          []*TestAPIRole `gorm:"many2many:user_roles;foreignKey:ID;joinForeignKey:user_id;References:ID;joinReferences:role_id"`
}

type TestAPIRole struct {
	ID          string `gorm:"primaryKey"`
	CreatedAt   time.Time
	UpdatedAt   time.Time
	DeletedAt   *time.Time `gorm:"index"`
	Name        string     `gorm:"uniqueIndex;not null"`
	Description string
	Users       []*TestAPIUser `gorm:"many2many:user_roles;foreignKey:ID;joinForeignKey:role_id;References:ID;joinReferences:user_id"`
}

func (TestAPIUser) TableName() string { return "users" }
func (TestAPIRole) TableName() string { return "roles" }

// Test plugin models without foreign key relationships
type TestPluginGroupAccess struct {
	ID                  uint      `json:"id" gorm:"primaryKey"`
	GroupID             string    `json:"groupId" gorm:"not null;type:varchar(100);index"`
	PluginName          string    `json:"pluginName" gorm:"not null;type:varchar(255);index"`
	CanView             bool      `json:"canView" gorm:"default:false"`
	CanInstall          bool      `json:"canInstall" gorm:"default:false"`
	CanConfigure        bool      `json:"canConfigure" gorm:"default:false"`
	CanManage           bool      `json:"canManage" gorm:"default:false"`
	CanDeploy           bool      `json:"canDeploy" gorm:"default:false"`
	AllowedTemplates    string    `json:"allowedTemplates" gorm:"type:text"`
	AllowedProviders    string    `json:"allowedProviders" gorm:"type:text"`
	AllowedProjects     string    `json:"allowedProjects" gorm:"type:text"`
	AllowedEnvironments string    `json:"allowedEnvironments" gorm:"type:text"`
	CreatedAt           time.Time `json:"createdAt"`
	UpdatedAt           time.Time `json:"updatedAt"`
}

func (TestPluginGroupAccess) TableName() string { return "plugin_group_access" }

// Test audit log model without foreign key relationships
type TestPluginAuditLog struct {
	ID             uint      `json:"id" gorm:"primaryKey"`
	UserID         string    `json:"userId" gorm:"not null;type:varchar(100);index"`
	UserName       string    `json:"userName" gorm:"not null;type:varchar(255)"`
	Action         string    `json:"action" gorm:"not null;type:varchar(100);index"`
	Resource       string    `json:"resource" gorm:"not null;type:varchar(255);index"`
	ResourceType   string    `json:"resourceType" gorm:"not null;type:varchar(50);index"`
	Details        string    `json:"details" gorm:"type:text"`
	Timestamp      time.Time `json:"timestamp" gorm:"index"`
	IPAddress      string    `json:"ipAddress" gorm:"type:varchar(45)"`
	UserAgent      string    `json:"userAgent" gorm:"type:text"`
	PermissionUsed string    `json:"permissionUsed" gorm:"type:varchar(100)"`
	ProjectID      *string   `json:"projectId" gorm:"type:varchar(100);index"`
	EnvironmentID  *string   `json:"environmentId" gorm:"type:varchar(100);index"`
}

func (TestPluginAuditLog) TableName() string { return "plugin_audit_logs" }

func setupPluginPermissionAPITestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// For SQLite, we need to handle UUID generation differently
	db.Exec("PRAGMA foreign_keys = ON")

	// Migrate all tables using test models
	err = db.AutoMigrate(
		&TestAPIUser{},
		&TestAPIRole{},
		&models.PluginPermission{},
		&models.PluginRolePermission{},
		&models.PluginAccessControl{},
		&TestPluginAuditLog{},
		&TestPluginGroupAccess{},
	)
	require.NoError(t, err)

	// Drop and recreate join tables with correct column names
	db.Exec(`DROP TABLE IF EXISTS user_roles`)
	db.Exec(`DROP TABLE IF EXISTS user_groups`)

	db.Exec(`CREATE TABLE user_roles (
		user_id TEXT NOT NULL,
		role_id TEXT NOT NULL,
		PRIMARY KEY (user_id, role_id)
	)`)

	db.Exec(`CREATE TABLE user_groups (
		user_id TEXT NOT NULL,
		group_id TEXT NOT NULL,
		PRIMARY KEY (user_id, group_id)
	)`)

	return db
}

func createPluginPermissionAPITestData(t *testing.T, db *gorm.DB) (string, string) {
	// Create roles using test models
	adminRole := TestAPIRole{
		ID:          "admin",
		Name:        "admin",
		Description: "Administrator",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	developerRole := TestAPIRole{
		ID:          "developer",
		Name:        "developer",
		Description: "Developer",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	require.NoError(t, db.Create(&adminRole).Error)
	require.NoError(t, db.Create(&developerRole).Error)

	// Create users
	adminUser := TestAPIUser{
		ID:             "admin-user",
		Username:       "admin",
		Email:          "<EMAIL>",
		HashedPassword: "hashed",
		IsAdmin:        true,
		IsActive:       true,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
		Roles:          []*TestAPIRole{&adminRole},
	}
	devUser := TestAPIUser{
		ID:             "dev-user",
		Username:       "developer",
		Email:          "<EMAIL>",
		HashedPassword: "hashed",
		IsAdmin:        false,
		IsActive:       true,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
		Roles:          []*TestAPIRole{&developerRole},
	}
	require.NoError(t, db.Create(&adminUser).Error)
	require.NoError(t, db.Create(&devUser).Error)

	// Create plugin permissions
	permissions := []models.PluginPermission{
		{
			ID:          "plugin:view",
			Name:        "View Plugins",
			Description: "View installed plugins",
			Category:    "plugin",
			Resource:    "*",
			Action:      "view",
			Scope:       "global",
		},
		{
			ID:          "plugin:install",
			Name:        "Install Plugins",
			Description: "Install new plugins",
			Category:    "plugin",
			Resource:    "*",
			Action:      "install",
			Scope:       "global",
		},
	}

	for _, perm := range permissions {
		require.NoError(t, db.Create(&perm).Error)
	}

	// Create role permissions
	rolePermissions := []models.PluginRolePermission{
		{RoleID: "admin", PermissionID: "plugin:view"},
		{RoleID: "admin", PermissionID: "plugin:install"},
		{RoleID: "developer", PermissionID: "plugin:view"},
	}

	for _, rp := range rolePermissions {
		require.NoError(t, db.Create(&rp).Error)
	}

	// Create plugin access control
	pluginAccessControl := models.PluginAccessControl{
		PluginName:          "test-plugin",
		RequiredPermissions: `[{"action":"view","permission":"plugin:view","scope":"global"}]`,
		PublicFeatures:      `["info","capabilities"]`,
		AdminOnlyFeatures:   `["install","uninstall"]`,
	}
	require.NoError(t, db.Create(&pluginAccessControl).Error)

	return adminUser.ID, devUser.ID
}

func setupPluginPermissionAPIRouter(t *testing.T, db *gorm.DB) *gin.Engine {
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Create service
	pluginPermissionService := service.NewPluginPermissionService(db)
	handler := NewPluginPermissionHandler(pluginPermissionService)

	// Add middleware to set user context
	router.Use(func(c *gin.Context) {
		// Mock authentication middleware
		userID := c.GetHeader("X-User-ID")
		isAdmin := c.GetHeader("X-Is-Admin") == "true"

		if userID != "" {
			c.Set("user_id", userID)
			c.Set("is_admin", isAdmin)
		}
		c.Next()
	})

	// Register routes
	apiGroup := router.Group("/api/v1")
	handler.RegisterRoutes(apiGroup)

	return router
}

func TestPluginPermissionHandler_GetUserPermissionMatrix(t *testing.T) {
	db := setupPluginPermissionAPITestDB(t)
	adminUserID, devUserID := createPluginPermissionAPITestData(t, db)
	router := setupPluginPermissionAPIRouter(t, db)

	t.Run("Admin user permission matrix", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/plugin-permissions/matrix", nil)
		req.Header.Set("X-User-ID", adminUserID)
		req.Header.Set("X-Is-Admin", "true")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var matrix service.PermissionMatrix
		err := json.Unmarshal(w.Body.Bytes(), &matrix)
		require.NoError(t, err)
		assert.Equal(t, adminUserID, matrix.UserID)
		assert.NotEmpty(t, matrix.Plugins)
	})

	t.Run("Developer user permission matrix", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/plugin-permissions/matrix", nil)
		req.Header.Set("X-User-ID", devUserID)
		req.Header.Set("X-Is-Admin", "false")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var matrix service.PermissionMatrix
		err := json.Unmarshal(w.Body.Bytes(), &matrix)
		require.NoError(t, err)
		assert.Equal(t, devUserID, matrix.UserID)
	})

	t.Run("Unauthenticated request", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/plugin-permissions/matrix", nil)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

func TestPluginPermissionHandler_GetUserSecurityContext(t *testing.T) {
	db := setupPluginPermissionAPITestDB(t)
	adminUserID, devUserID := createPluginPermissionAPITestData(t, db)
	router := setupPluginPermissionAPIRouter(t, db)

	t.Run("Admin user security context", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/plugin-permissions/context", nil)
		req.Header.Set("X-User-ID", adminUserID)
		req.Header.Set("X-Is-Admin", "true")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var securityContext service.SecurityContext
		err := json.Unmarshal(w.Body.Bytes(), &securityContext)
		require.NoError(t, err)
		assert.Equal(t, adminUserID, securityContext.UserID)
		assert.Contains(t, securityContext.UserRoles, "admin")
		assert.Contains(t, securityContext.Permissions, "plugin:view")
	})

	t.Run("Developer user security context", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/plugin-permissions/context", nil)
		req.Header.Set("X-User-ID", devUserID)
		req.Header.Set("X-Is-Admin", "false")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var securityContext service.SecurityContext
		err := json.Unmarshal(w.Body.Bytes(), &securityContext)
		require.NoError(t, err)
		assert.Equal(t, devUserID, securityContext.UserID)
		assert.Contains(t, securityContext.UserRoles, "developer")
	})
}

func TestPluginPermissionHandler_CheckPermission(t *testing.T) {
	db := setupPluginPermissionAPITestDB(t)
	adminUserID, devUserID := createPluginPermissionAPITestData(t, db)
	router := setupPluginPermissionAPIRouter(t, db)

	t.Run("Admin can install plugins", func(t *testing.T) {
		requestBody := service.PermissionCheckRequest{
			Action:       "install",
			Resource:     "test-plugin",
			ResourceType: "plugin",
		}

		body, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", "/api/v1/plugin-permissions/check", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-User-ID", adminUserID)
		req.Header.Set("X-Is-Admin", "true")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response service.PermissionCheckResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.True(t, response.Allowed)
		assert.Contains(t, response.GrantedPermissions, "admin")
	})

	t.Run("Developer cannot install plugins", func(t *testing.T) {
		requestBody := service.PermissionCheckRequest{
			Action:       "install",
			Resource:     "test-plugin",
			ResourceType: "plugin",
		}

		body, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", "/api/v1/plugin-permissions/check", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-User-ID", devUserID)
		req.Header.Set("X-Is-Admin", "false")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response service.PermissionCheckResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.False(t, response.Allowed)
		assert.Contains(t, response.Reason, "Missing required permission")
	})

	t.Run("Developer can view plugins", func(t *testing.T) {
		requestBody := service.PermissionCheckRequest{
			Action:       "view",
			Resource:     "test-plugin",
			ResourceType: "plugin",
		}

		body, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", "/api/v1/plugin-permissions/check", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-User-ID", devUserID)
		req.Header.Set("X-Is-Admin", "false")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response service.PermissionCheckResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.True(t, response.Allowed)
		assert.Contains(t, response.GrantedPermissions, "role")
	})

	t.Run("Invalid request body", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "/api/v1/plugin-permissions/check", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-User-ID", devUserID)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestPluginPermissionHandler_LogPluginAction(t *testing.T) {
	db := setupPluginPermissionAPITestDB(t)
	_, devUserID := createPluginPermissionAPITestData(t, db)
	router := setupPluginPermissionAPIRouter(t, db)

	t.Run("Log plugin action successfully", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"action":   "deploy",
			"resource": "test-plugin",
			"details": map[string]interface{}{
				"projectId":     "test-project",
				"environmentId": "dev-env",
				"success":       true,
			},
		}

		body, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", "/api/v1/plugin-permissions/audit-logs", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-User-ID", devUserID)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.Equal(t, "Action logged successfully", response["message"])

		// Verify audit log was created
		var auditLog TestPluginAuditLog
		err = db.Where("user_id = ? AND action = ? AND resource = ?", devUserID, "deploy", "test-plugin").First(&auditLog).Error
		require.NoError(t, err)
		assert.Equal(t, "deploy", auditLog.Action)
		assert.Equal(t, "test-plugin", auditLog.Resource)
	})

	t.Run("Missing required fields", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"action": "deploy",
			// Missing resource field
		}

		body, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", "/api/v1/plugin-permissions/audit-logs", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-User-ID", devUserID)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestPluginPermissionHandler_GetPluginAuditLogs(t *testing.T) {
	db := setupPluginPermissionAPITestDB(t)
	_, devUserID := createPluginPermissionAPITestData(t, db)
	router := setupPluginPermissionAPIRouter(t, db)

	// Create some audit logs
	auditLogs := []TestPluginAuditLog{
		{
			UserID:       devUserID,
			UserName:     "developer",
			Action:       "view",
			Resource:     "test-plugin",
			ResourceType: "plugin",
			Details:      `{"success":true}`,
			IPAddress:    "***********",
			Timestamp:    time.Now(),
		},
		{
			UserID:       devUserID,
			UserName:     "developer",
			Action:       "deploy",
			Resource:     "test-plugin",
			ResourceType: "plugin",
			Details:      `{"success":true}`,
			IPAddress:    "***********",
			Timestamp:    time.Now(),
		},
	}

	for _, log := range auditLogs {
		require.NoError(t, db.Create(&log).Error)
	}

	t.Run("Get all audit logs (admin only)", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/plugin-permissions/audit-logs", nil)
		req.Header.Set("X-User-ID", "admin-user")
		req.Header.Set("X-Is-Admin", "true")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		logs, ok := response["logs"].([]interface{})
		require.True(t, ok)
		assert.Len(t, logs, 2)
	})

	t.Run("Get audit logs with plugin filter", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/plugin-permissions/audit-logs?pluginName=test-plugin", nil)
		req.Header.Set("X-User-ID", "admin-user")
		req.Header.Set("X-Is-Admin", "true")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		logs, ok := response["logs"].([]interface{})
		require.True(t, ok)
		assert.Len(t, logs, 2)
	})

	t.Run("Get audit logs with limit", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/plugin-permissions/audit-logs?limit=1", nil)
		req.Header.Set("X-User-ID", "admin-user")
		req.Header.Set("X-Is-Admin", "true")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		logs, ok := response["logs"].([]interface{})
		require.True(t, ok)
		assert.Len(t, logs, 1)
	})
}

func TestPluginPermissionHandler_GetPluginAccessControl(t *testing.T) {
	db := setupPluginPermissionAPITestDB(t)
	_, devUserID := createPluginPermissionAPITestData(t, db)
	router := setupPluginPermissionAPIRouter(t, db)

	t.Run("Get plugin access control", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/plugin-permissions/access-control/test-plugin", nil)
		req.Header.Set("X-User-ID", devUserID)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.Equal(t, "test-plugin", response["pluginName"])
		assert.NotEmpty(t, response["requiredPermissions"])
		assert.NotEmpty(t, response["publicFeatures"])
	})

	t.Run("Missing plugin name", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/plugin-permissions/access-control/", nil)
		req.Header.Set("X-User-ID", devUserID)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}
