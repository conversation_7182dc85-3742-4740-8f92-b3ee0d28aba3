package api

import (
	"net/http"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// RBACHandler provides CRUD and assignment endpoints for roles, groups, permissions
type RBAC<PERSON>andler struct {
	DB *gorm.DB
}

func NewRBACHandler(db *gorm.DB) *RBACHandler {
	return &RBACHandler{DB: db}
}

// --- GROUPS ---
func (h *RBACHandler) ListGroups(c *gin.Context) {
	var groups []models.Group
	h.DB.Preload("Users").Preload("Roles").Find(&groups)
	c.<PERSON>(http.StatusOK, groups)
}

func (h *RBACHandler) CreateGroup(c *gin.Context) {
	var group models.Group
	if err := c.ShouldBindJSON(&group); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	h.DB.Create(&group)
	c.<PERSON>(http.StatusCreated, group)
}

func (h *RB<PERSON><PERSON>andler) UpdateGroup(c *gin.Context) {
	id := c.Param("groupId")
	var group models.Group
	if err := h.DB.First(&group, "id = ?", id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Group not found"})
		return
	}
	if err := c.ShouldBindJSON(&group); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	h.DB.Save(&group)
	c.JSON(http.StatusOK, group)
}

func (h *RBACHandler) DeleteGroup(c *gin.Context) {
	id := c.Param("groupId")
	h.DB.Delete(&models.Group{}, "id = ?", id)
	c.Status(http.StatusNoContent)
}

// --- ROLES ---
func (h *RBACHandler) ListRoles(c *gin.Context) {
	var roles []models.Role
	h.DB.Preload("Permissions").Preload("Groups").Find(&roles)
	c.JSON(http.StatusOK, roles)
}

func (h *RBACHandler) CreateRole(c *gin.Context) {
	var role models.Role
	if err := c.ShouldBindJSON(&role); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	h.DB.Create(&role)
	c.JSON(http.StatusCreated, role)
}

func (h *RBACHandler) GetRole(c *gin.Context) {
	id := c.Param("roleId")
	var role models.Role
	if err := h.DB.Preload("Permissions").First(&role, "id = ?", id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Role not found"})
		return
	}
	c.JSON(http.StatusOK, role)
}

func (h *RBACHandler) UpdateRole(c *gin.Context) {
	id := c.Param("roleId")
	var role models.Role
	if err := h.DB.First(&role, "id = ?", id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Role not found"})
		return
	}
	if err := c.ShouldBindJSON(&role); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	h.DB.Save(&role)
	c.JSON(http.StatusOK, role)
}

func (h *RBACHandler) DeleteRole(c *gin.Context) {
	id := c.Param("roleId")
	h.DB.Delete(&models.Role{}, "id = ?", id)
	c.Status(http.StatusNoContent)
}

// --- PERMISSIONS ---
func (h *RBACHandler) ListPermissions(c *gin.Context) {
	var perms []models.Permission
	h.DB.Find(&perms)
	c.JSON(http.StatusOK, perms)
}

func (h *RBACHandler) CreatePermission(c *gin.Context) {
	var perm models.Permission
	if err := c.ShouldBindJSON(&perm); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	h.DB.Create(&perm)
	c.JSON(http.StatusCreated, perm)
}

func (h *RBACHandler) UpdatePermission(c *gin.Context) {
	id := c.Param("id")
	var perm models.Permission
	if err := h.DB.First(&perm, "id = ?", id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Permission not found"})
		return
	}
	if err := c.ShouldBindJSON(&perm); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	h.DB.Save(&perm)
	c.JSON(http.StatusOK, perm)
}

func (h *RBACHandler) DeletePermission(c *gin.Context) {
	id := c.Param("id")
	h.DB.Delete(&models.Permission{}, "id = ?", id)
	c.Status(http.StatusNoContent)
}

// --- ASSIGNMENTS ---
// Assign users to groups
func (h *RBACHandler) AssignUserToGroup(c *gin.Context) {
	var req struct {
		UserID  string `json:"userId"`
		GroupID string `json:"groupId"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	var user models.User
	if err := h.DB.First(&user, "id = ?", req.UserID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}
	var group models.Group
	if err := h.DB.First(&group, "id = ?", req.GroupID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Group not found"})
		return
	}
	h.DB.Model(&user).Association("Groups").Append(&group)
	c.Status(http.StatusNoContent)
}

// Assign groups to roles
func (h *RBACHandler) AssignGroupToRole(c *gin.Context) {
	var req struct {
		GroupID string `json:"groupId"`
		RoleID  string `json:"roleId"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	var group models.Group
	if err := h.DB.First(&group, "id = ?", req.GroupID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Group not found"})
		return
	}
	var role models.Role
	if err := h.DB.First(&role, "id = ?", req.RoleID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Role not found"})
		return
	}
	h.DB.Model(&group).Association("Roles").Append(&role)
	c.Status(http.StatusNoContent)
}

// Assign permissions to roles
func (h *RBACHandler) AssignPermissionToRole(c *gin.Context) {
	var req struct {
		RoleID       string `json:"roleId"`
		PermissionID string `json:"permissionId"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	var role models.Role
	if err := h.DB.First(&role, "id = ?", req.RoleID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Role not found"})
		return
	}
	var perm models.Permission
	if err := h.DB.First(&perm, "id = ?", req.PermissionID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Permission not found"})
		return
	}
	h.DB.Model(&role).Association("Permissions").Append(&perm)
	c.Status(http.StatusNoContent)
}

// AssignGroupToRoleWithBody assigns a group to a role using request body
func (h *RBACHandler) AssignGroupToRoleWithBody(c *gin.Context) {
	groupId := c.Param("groupId")

	var req struct {
		RoleID    string `json:"roleId" binding:"required"`
		ProjectID string `json:"projectId"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var group models.Group
	if err := h.DB.First(&group, "id = ?", groupId).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Group not found"})
		return
	}
	var role models.Role
	if err := h.DB.First(&role, "id = ?", req.RoleID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Role not found"})
		return
	}

	// For now, we'll handle project-specific assignments through role mappings
	// This is a simple group-role assignment without project context
	h.DB.Model(&group).Association("Roles").Append(&role)
	c.Status(http.StatusNoContent)
}

// GetGroupRoles gets all roles assigned to a group
func (h *RBACHandler) GetGroupRoles(c *gin.Context) {
	groupId := c.Param("groupId")

	var group models.Group
	if err := h.DB.Preload("Roles").First(&group, "id = ?", groupId).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Group not found"})
		return
	}

	c.JSON(http.StatusOK, group.Roles)
}

// GetGroupProjects gets all projects assigned to a group
func (h *RBACHandler) GetGroupProjects(c *gin.Context) {
	groupId := c.Param("groupId")

	if groupId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Group ID is required"})
		return
	}

	// Check if group exists
	var group models.Group
	if err := h.DB.First(&group, "id = ?", groupId).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Group not found"})
		return
	}

	// Get all projects that this group is assigned to
	var projects []models.Project
	if err := h.DB.Joins("JOIN project_groups ON projects.id = project_groups.project_id").
		Where("project_groups.group_id = ?", groupId).
		Find(&projects).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get projects for group"})
		return
	}

	c.JSON(http.StatusOK, projects)
}

// RemoveRoleFromGroup removes a role from a group
func (h *RBACHandler) RemoveRoleFromGroup(c *gin.Context) {
	groupId := c.Param("groupId")
	roleId := c.Param("roleId")
	var group models.Group
	if err := h.DB.First(&group, "id = ?", groupId).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Group not found"})
		return
	}
	var role models.Role
	if err := h.DB.First(&role, "id = ?", roleId).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Role not found"})
		return
	}
	h.DB.Model(&group).Association("Roles").Delete(&role)
	c.Status(http.StatusNoContent)
}

// ListRolesForGroup lists all roles assigned to a group
func (h *RBACHandler) ListRolesForGroup(c *gin.Context) {
	groupId := c.Param("groupId")
	var group models.Group
	if err := h.DB.Preload("Roles").First(&group, "id = ?", groupId).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Group not found"})
		return
	}
	c.JSON(http.StatusOK, group.Roles)
}
