package api

import (
	"context"
	"log"
	"net/http"
	"time"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"github.com/claudio/deploy-orchestrator/admin-service/service"
	"github.com/claudio/deploy-orchestrator/admin-service/service/provider"
	"github.com/claudio/deploy-orchestrator/shared/auth"
	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// AuthHandler handles authentication endpoints
type AuthHandler struct {
	userService      *service.UserService
	jwtManager       *auth.JWTManager
	groupSyncService *service.GroupSyncService
	oidcProvider     provider.OIDCProviderInterface
	samlProvider     provider.SAMLProviderInterface
	ldapProvider     provider.LDAPProviderInterface
	stateStore       map[string]int64 // Stores state parameters for external login
}

// NewAuthHandler creates a new instance of the auth handler
func NewAuthHandler(
	userService *service.UserService,
	jwtManager *auth.JWTManager,
	groupSyncService *service.GroupSyncService,
	oidcProvider provider.OIDCProviderInterface,
	samlProvider provider.SAMLProviderInterface,
	ldapProvider provider.LDAPProviderInterface,
) *AuthHandler {
	return &AuthHandler{
		userService:      userService,
		jwtManager:       jwtManager,
		groupSyncService: groupSyncService,
		oidcProvider:     oidcProvider,
		samlProvider:     samlProvider,
		ldapProvider:     ldapProvider,
		stateStore:       make(map[string]int64),
	}
}

// LoginRequest contains the login credentials from a user
type LoginRequest struct {
	Username     string `json:"username" binding:"required"`
	Password     string `json:"password" binding:"required"`
	AuthProvider string `json:"authProvider,omitempty"`
	ProviderID   string `json:"providerId,omitempty"`
}

// LoginResponse represents the response after successful authentication
type LoginResponse struct {
	Token        string    `json:"token"`
	RefreshToken string    `json:"refreshToken"`
	ExpiresAt    time.Time `json:"expiresAt"`
	UserID       string    `json:"userId"`
	Username     string    `json:"username"`
	Email        string    `json:"email"`
	FirstName    string    `json:"firstName"`
	LastName     string    `json:"lastName"`
	IsAdmin      bool      `json:"isAdmin"`
	Groups       []string  `json:"groups,omitempty"`
}

// authenticateUser handles the username/password authentication
func (h *AuthHandler) authenticateUser(ctx context.Context, username, password string) (*models.User, error) {
	// Check if it's a username or email login
	var user *models.User
	var err error

	// Try to find by username first
	user, err = h.userService.GetUserByUsername(ctx, username)
	if err != nil {
		// If not found by username, try email
		if err == gorm.ErrRecordNotFound {
			user, err = h.userService.GetUserByEmail(ctx, username)
			if err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}

	// Verify password
	err = bcrypt.CompareHashAndPassword([]byte(user.HashedPassword), []byte(password))
	if err != nil {
		return nil, err
	}

	return user, nil
}

// Login handles user authentication and token generation
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request", "details": err.Error()})
		return
	}

	var user *models.User
	var err error

	// Check if we're using LDAP authentication
	if req.AuthProvider == string(provider.IdpLDAP) {
		// Get the LDAP provider ID from the request if available
		var ldapProviderID string
		if req.ProviderID != "" {
			ldapProviderID = req.ProviderID
		}

		// Create a new context with the LDAP provider ID
		ctxWithProviderID := context.WithValue(c.Request.Context(), "ldap_provider_id", ldapProviderID)

		// Authenticate against LDAP
		user, err = h.ldapProvider.Authenticate(ctxWithProviderID, req.Username, req.Password)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication failed", "details": err.Error()})
			return
		}

		// Check if user exists in the database
		existingUser, err := h.userService.GetUserByUsername(c, user.Username)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				// User doesn't exist, create a new one
				user.HashedPassword = "" // We don't store LDAP passwords
				// Create user with empty password since LDAP handles authentication
				err = h.userService.CreateUser(c, user, "")
				if err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user", "details": err.Error()})
					return
				}
			} else {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check user", "details": err.Error()})
				return
			}
		} else {
			// User exists, update with latest info from LDAP
			user.ID = existingUser.ID
			user.IsAdmin = existingUser.IsAdmin               // Preserve admin status
			user.HashedPassword = existingUser.HashedPassword // Preserve password if any

			// Update user in database - create update map
			updates := map[string]interface{}{
				"first_name": user.FirstName,
				"last_name":  user.LastName,
				"email":      user.Email,
				"is_active":  user.IsActive,
			}

			err = h.userService.UpdateUser(c, user.ID, updates)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user", "details": err.Error()})
				return
			}
		}

		// Sync LDAP groups if available
		if h.groupSyncService != nil {
			// Get the LDAP provider ID from the request if available
			var ldapProviderID string
			if req.ProviderID != "" {
				ldapProviderID = req.ProviderID
			}

			// Create a new context with the LDAP provider ID
			ctxWithProviderID := context.WithValue(c.Request.Context(), "ldap_provider_id", ldapProviderID)

			// Fetch groups from LDAP
			ldapGroups, fetchErr := h.ldapProvider.FetchGroupsForUser(ctxWithProviderID, user.Username)
			if fetchErr != nil {
				// Just log the error, don't fail the login
				log.Printf("Warning: Failed to fetch groups from LDAP: %v", fetchErr)
			} else if len(ldapGroups) > 0 {
				// Sync the groups to the user
				if syncErr := h.groupSyncService.SyncUserGroups(c, user, service.IdpLDAP, ldapGroups); syncErr != nil {
					log.Printf("Warning: Failed to sync user groups: %v", syncErr)
				} else {
					log.Printf("Successfully synced %d groups for user %s from LDAP", len(ldapGroups), user.Username)
				}
			}
		}
	} else {
		// Regular database authentication
		user, err = h.authenticateUser(c, req.Username, req.Password)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication failed", "details": err.Error()})
			return
		}
	}

	// Get user roles if needed (simplified implementation)
	var roles []string
	if user.IsAdmin {
		roles = append(roles, "admin")
	}

	// Add debugging to see what roles are being set
	log.Printf("DEBUG: Login - User: %s, IsAdmin: %v, Roles: %v", user.Username, user.IsAdmin, roles)

	// Generate access token
	token, err := h.jwtManager.GenerateAccessToken(user.ID, user.Username, user.Email, roles)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token", "details": err.Error()})
		return
	}

	// Calculate expiration time
	expiresAt := time.Now().Add(auth.AccessTokenExpiry)

	// Generate refresh token
	refreshToken, err := h.jwtManager.GenerateRefreshToken(user.ID, user.Username, user.Email, roles)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate refresh token", "details": err.Error()})
		return
	}

	// Update the user's last login time
	if err := h.userService.UpdateLastLogin(c, user.ID); err != nil {
		// Just log the error, don't fail the login
		log.Printf("Failed to update last login time: %v", err)
	}

	// Fetch user's groups for response
	var groups []string
	userGroups, err := h.userService.GetUserGroups(c, user.ID)
	if err == nil && userGroups != nil {
		groups = make([]string, len(userGroups))
		for i, g := range userGroups {
			groups[i] = g.Name
		}
	}

	// Return the response
	c.JSON(http.StatusOK, LoginResponse{
		Token:        token,
		RefreshToken: refreshToken,
		ExpiresAt:    expiresAt,
		UserID:       user.ID,
		Username:     user.Username,
		Email:        user.Email,
		FirstName:    user.FirstName,
		LastName:     user.LastName,
		IsAdmin:      user.IsAdmin,
		Groups:       groups,
	})
}

// RefreshTokenRequest contains the refresh token
type RefreshTokenRequest struct {
	RefreshToken string `json:"refreshToken" binding:"required"`
}

// RefreshToken generates a new JWT token using a refresh token
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request", "details": err.Error()})
		return
	}

	// Validate refresh token
	claims, err := h.jwtManager.VerifyToken(req.RefreshToken)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid refresh token", "details": err.Error()})
		return
	}

	// Check that it's actually a refresh token
	if claims.TokenType != auth.RefreshToken {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token type", "details": "Expected refresh token"})
		return
	}

	// Get user details
	user, err := h.userService.GetUser(c, claims.UserID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user details", "details": err.Error()})
		return
	}

	// Get user roles (simplified implementation)
	var roles []string

	// Generate new access token
	token, err := h.jwtManager.GenerateAccessToken(user.ID, user.Username, user.Email, roles)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token", "details": err.Error()})
		return
	}

	// Calculate expiration time
	expiresAt := time.Now().Add(auth.AccessTokenExpiry)

	// Generate new refresh token
	refreshToken, err := h.jwtManager.GenerateRefreshToken(user.ID, user.Username, user.Email, roles)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate refresh token", "details": err.Error()})
		return
	}

	// Fetch user's groups for response
	var groups []string
	userGroups, err := h.userService.GetUserGroups(c, user.ID)
	if err == nil && userGroups != nil {
		groups = make([]string, len(userGroups))
		for i, g := range userGroups {
			groups[i] = g.Name
		}
	}

	// Return the response
	c.JSON(http.StatusOK, LoginResponse{
		Token:        token,
		RefreshToken: refreshToken,
		ExpiresAt:    expiresAt,
		UserID:       user.ID,
		Username:     user.Username,
		Email:        user.Email,
		FirstName:    user.FirstName,
		LastName:     user.LastName,
		IsAdmin:      user.IsAdmin,
		Groups:       groups,
	})
}
