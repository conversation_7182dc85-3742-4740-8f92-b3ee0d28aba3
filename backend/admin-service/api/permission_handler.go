package api

import (
	"net/http"

	"github.com/claudio/deploy-orchestrator/admin-service/service"
	"github.com/gin-gonic/gin"
)

// PermissionHandler handles permission check endpoints
type PermissionHandler struct {
	PermissionService *service.PermissionService
}

// NewPermissionHandler creates a new permission handler
func NewPermissionHandler(permissionService *service.PermissionService) *PermissionHandler {
	return &PermissionHandler{
		PermissionService: permissionService,
	}
}

// CheckPermission handles GET /api/v1/permissions/check
// Query parameters: userId, permission, projectId (optional)
func (h *PermissionHandler) CheckPermission(c *gin.Context) {
	userID := c.Query("userId")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "userId parameter is required"})
		return
	}

	permission := c.Query("permission")
	if permission == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "permission parameter is required"})
		return
	}

	projectID := c.Query("projectId") // Optional

	// Check permission using the permission service
	hasPermission, err := h.PermissionService.CheckPermission(c.Request.Context(), userID, permission, projectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to check permission",
			"details": err.Error(),
		})
		return
	}

	// Return appropriate HTTP status code based on permission check result
	// The HTTPPermissionService expects 200 for true, 403 for false
	if hasPermission {
		c.JSON(http.StatusOK, gin.H{
			"hasPermission": true,
			"userId":        userID,
			"permission":    permission,
			"projectId":     projectID,
		})
	} else {
		c.JSON(http.StatusForbidden, gin.H{
			"hasPermission": false,
			"userId":        userID,
			"permission":    permission,
			"projectId":     projectID,
		})
	}
}
