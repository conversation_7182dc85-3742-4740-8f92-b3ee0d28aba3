package api

import (
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

// EnabledProviderResponse represents a simplified identity provider for the login page
type EnabledProviderResponse struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	Description string `json:"description,omitempty"`
	LogoURL     string `json:"logoUrl,omitempty"`
	IsDefault   bool   `json:"isDefault"`
}

// GetEnabledProviders returns a list of enabled identity providers for the login page
func (h *AuthHandler) GetEnabledProviders(c *gin.Context) {
	var response []EnabledProviderResponse

	// Get OIDC providers
	if h.oidcProvider != nil {
		oidcProviders, err := h.oidcProvider.ListProviders(c)
		if err != nil {
			log.Printf("Failed to get OIDC providers: %v", err)
		} else {
			// Add enabled OIDC providers to the response
			for _, p := range oidcProviders {
				if p.Enabled {
					response = append(response, EnabledProviderResponse{
						ID:          p.ID,
						Name:        p.Name,
						Type:        "oidc",
						Description: p.Description,
						LogoURL:     p.LogoURL,
						IsDefault:   p.IsDefault,
					})
				}
			}
		}
	}

	// Get SAML providers
	if h.samlProvider != nil {
		samlProviders, err := h.samlProvider.ListProviders(c)
		if err != nil {
			log.Printf("Failed to get SAML providers: %v", err)
		} else {
			// Add enabled SAML providers to the response
			for _, p := range samlProviders {
				if p.Enabled {
					response = append(response, EnabledProviderResponse{
						ID:          p.ID,
						Name:        p.Name,
						Type:        "saml",
						Description: p.Description,
						LogoURL:     p.LogoURL,
						IsDefault:   p.IsDefault,
					})
				}
			}
		}
	}

	// Get LDAP providers
	if h.ldapProvider != nil {
		ldapProviders, err := h.ldapProvider.ListProviders(c)
		if err != nil {
			log.Printf("Failed to get LDAP providers: %v", err)
		} else {
			// Add enabled LDAP providers to the response
			for _, p := range ldapProviders {
				if p.Enabled {
					response = append(response, EnabledProviderResponse{
						ID:          p.ID,
						Name:        p.Name,
						Type:        "ldap",
						Description: p.Description,
						LogoURL:     p.LogoURL,
						IsDefault:   p.IsDefault,
					})
				}
			}
		}
	}

	c.JSON(http.StatusOK, response)
}
