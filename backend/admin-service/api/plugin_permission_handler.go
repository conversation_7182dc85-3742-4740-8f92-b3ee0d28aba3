package api

import (
	"context"
	"net/http"
	"strconv"

	"github.com/claudio/deploy-orchestrator/admin-service/service"
	"github.com/gin-gonic/gin"
)

// PluginPermissionHandler handles plugin permission related HTTP requests
type PluginPermissionHandler struct {
	permissionService *service.PluginPermissionService
}

// NewPluginPermissionHandler creates a new plugin permission handler
func NewPluginPermissionHandler(permissionService *service.PluginPermissionService) *PluginPermissionHandler {
	return &PluginPermissionHandler{
		permissionService: permissionService,
	}
}

// RegisterRoutes registers all plugin permission routes
func (h *PluginPermissionHandler) RegisterRoutes(router *gin.RouterGroup) {
	pluginPermissions := router.Group("/plugin-permissions")
	{
		// Permission matrix and context
		pluginPermissions.GET("/matrix", h.GetUserPermissionMatrix)
		pluginPermissions.GET("/context", h.GetUserSecurityContext)

		// Permission checking
		pluginPermissions.POST("/check", h.CheckPermission)

		// User permissions (admin or self)
		pluginPermissions.GET("/user", h.GetUserPluginPermissions)

		// Audit logs (admin only)
		pluginPermissions.GET("/audit-logs", h.GetPluginAuditLogs)
		pluginPermissions.POST("/audit-logs", h.LogPluginAction)

		// Plugin access control (admin only)
		pluginPermissions.GET("/access-control/:pluginName", h.GetPluginAccessControl)
		pluginPermissions.PUT("/access-control/:pluginName", h.UpdatePluginAccessControl)
	}
}

// GetUserPermissionMatrix returns the complete permission matrix for the current user
func (h *PluginPermissionHandler) GetUserPermissionMatrix(c *gin.Context) {
	userID := getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := context.WithValue(context.Background(), "client_ip", c.ClientIP())
	ctx = context.WithValue(ctx, "user_agent", c.GetHeader("User-Agent"))

	matrix, err := h.permissionService.GetUserPermissionMatrix(ctx, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get permission matrix", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, matrix)
}

// GetUserSecurityContext returns the security context for the current user
func (h *PluginPermissionHandler) GetUserSecurityContext(c *gin.Context) {
	userID := getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	ctx := context.WithValue(context.Background(), "client_ip", c.ClientIP())
	ctx = context.WithValue(ctx, "user_agent", c.GetHeader("User-Agent"))

	securityContext, err := h.permissionService.GetUserSecurityContext(ctx, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get security context", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, securityContext)
}

// CheckPermission checks if the current user has a specific permission
func (h *PluginPermissionHandler) CheckPermission(c *gin.Context) {
	userID := getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var request service.PermissionCheckRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	ctx := context.WithValue(context.Background(), "client_ip", c.ClientIP())
	ctx = context.WithValue(ctx, "user_agent", c.GetHeader("User-Agent"))

	response, err := h.permissionService.CheckPermission(ctx, userID, &request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check permission", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetUserPluginPermissions returns plugin permissions for a user
func (h *PluginPermissionHandler) GetUserPluginPermissions(c *gin.Context) {
	currentUserID := getUserIDFromContext(c)
	if currentUserID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get target user ID from query parameter
	targetUserID := c.Query("userId")
	if targetUserID == "" {
		targetUserID = currentUserID
	}

	// Check if current user can access target user's permissions
	if targetUserID != currentUserID && !isAdmin(c) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Cannot access other user's permissions"})
		return
	}

	ctx := context.WithValue(context.Background(), "client_ip", c.ClientIP())
	ctx = context.WithValue(ctx, "user_agent", c.GetHeader("User-Agent"))

	matrix, err := h.permissionService.GetUserPermissionMatrix(ctx, targetUserID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user permissions", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, matrix)
}

// GetPluginAuditLogs returns audit logs for plugin actions
func (h *PluginPermissionHandler) GetPluginAuditLogs(c *gin.Context) {
	// Parse query parameters
	pluginName := c.Query("pluginName")
	userID := c.Query("userId")
	limitStr := c.DefaultQuery("limit", "100")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 1000 {
		limit = 100
	}

	ctx := context.WithValue(context.Background(), "client_ip", c.ClientIP())
	ctx = context.WithValue(ctx, "user_agent", c.GetHeader("User-Agent"))

	var pluginNamePtr, userIDPtr *string
	if pluginName != "" {
		pluginNamePtr = &pluginName
	}
	if userID != "" {
		userIDPtr = &userID
	}

	logs, err := h.permissionService.GetPluginAuditLogs(ctx, pluginNamePtr, userIDPtr, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get audit logs", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"logs": logs})
}

// LogPluginAction logs a plugin action for audit purposes
func (h *PluginPermissionHandler) LogPluginAction(c *gin.Context) {
	userID := getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var request struct {
		Action   string                 `json:"action" binding:"required"`
		Resource string                 `json:"resource" binding:"required"`
		Details  map[string]interface{} `json:"details"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	ctx := context.WithValue(context.Background(), "client_ip", c.ClientIP())
	ctx = context.WithValue(ctx, "user_agent", c.GetHeader("User-Agent"))

	if request.Details == nil {
		request.Details = make(map[string]interface{})
	}

	err := h.permissionService.LogPluginAction(ctx, userID, request.Action, request.Resource, request.Details)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to log action", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Action logged successfully"})
}

// GetPluginAccessControl returns access control configuration for a plugin
func (h *PluginPermissionHandler) GetPluginAccessControl(c *gin.Context) {
	pluginName := c.Param("pluginName")
	if pluginName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Plugin name is required"})
		return
	}

	// Return a placeholder response for now
	accessControl := struct {
		PluginName                string                   `json:"pluginName"`
		RequiredPermissions       []map[string]interface{} `json:"requiredPermissions"`
		PublicFeatures            []string                 `json:"publicFeatures"`
		AdminOnlyFeatures         []string                 `json:"adminOnlyFeatures"`
		ProjectScopedFeatures     []map[string]interface{} `json:"projectScopedFeatures"`
		EnvironmentScopedFeatures []map[string]interface{} `json:"environmentScopedFeatures"`
	}{
		PluginName: pluginName,
		RequiredPermissions: []map[string]interface{}{
			{
				"action":     "view",
				"permission": "plugin:view",
				"scope":      "global",
			},
		},
		PublicFeatures:    []string{"info", "capabilities"},
		AdminOnlyFeatures: []string{"install", "uninstall"},
		ProjectScopedFeatures: []map[string]interface{}{
			{
				"feature":            "deploy",
				"requiredPermission": "provider:deploy",
				"appliesToProjects":  "all",
			},
		},
		EnvironmentScopedFeatures: []map[string]interface{}{
			{
				"feature":               "template-deploy",
				"requiredPermission":    "template:deploy",
				"appliesToEnvironments": "all",
			},
		},
	}

	c.JSON(http.StatusOK, accessControl)
}

// UpdatePluginAccessControl updates access control configuration for a plugin (admin only)
func (h *PluginPermissionHandler) UpdatePluginAccessControl(c *gin.Context) {
	pluginName := c.Param("pluginName")
	if pluginName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Plugin name is required"})
		return
	}

	var request struct {
		RequiredPermissions       []map[string]interface{} `json:"requiredPermissions"`
		PublicFeatures            []string                 `json:"publicFeatures"`
		AdminOnlyFeatures         []string                 `json:"adminOnlyFeatures"`
		ProjectScopedFeatures     []map[string]interface{} `json:"projectScopedFeatures"`
		EnvironmentScopedFeatures []map[string]interface{} `json:"environmentScopedFeatures"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// TODO: Implement UpdatePluginAccessControl in service
	c.JSON(http.StatusOK, gin.H{"message": "Plugin access control updated successfully"})
}

// Helper functions
func getUserIDFromContext(c *gin.Context) string {
	if userID, exists := c.Get("userID"); exists {
		if uid, ok := userID.(string); ok {
			return uid
		}
	}
	return ""
}

func isAdmin(c *gin.Context) bool {
	// Check roles for admin role
	if roles, exists := c.Get("roles"); exists {
		if rolesList, ok := roles.([]string); ok {
			for _, role := range rolesList {
				if role == "admin" {
					return true
				}
			}
		}
	}
	return false
}
