package api

import (
	"log"
	"net/http"
	"regexp"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// RoleMappingHandler handles endpoints for identity provider group to role mappings
type RoleMappingHandler struct {
	DB *gorm.DB
}

// NewRoleMappingHandler creates a new instance of RoleMappingHandler
func NewRoleMappingHandler(db *gorm.DB) *RoleMappingHandler {
	return &RoleMappingHandler{DB: db}
}

// CreateRoleMapping creates a new role mapping
func (h *RoleMappingHandler) CreateRoleMapping(c *gin.Context) {
	var mapping models.RoleMapping
	if err := c.Should<PERSON>ind<PERSON>(&mapping); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate the group pattern
	if _, err := regexp.Compile(mapping.GroupPattern); err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid group pattern: " + err.Error()})
		return
	}

	// Check if role exists
	var role models.Role
	if err := h.DB.First(&role, "id = ?", mapping.RoleID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Role not found"})
		} else {
			log.Printf("Error finding role: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find role"})
		}
		return
	}

	// Check if project exists (if provided)
	if mapping.ProjectID != "" {
		var project models.Project
		if err := h.DB.First(&project, "id = ?", mapping.ProjectID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
			} else {
				log.Printf("Error finding project: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find project"})
			}
			return
		}
	}

	// Create the mapping
	if err := h.DB.Create(&mapping).Error; err != nil {
		log.Printf("Error creating role mapping: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create role mapping"})
		return
	}

	c.JSON(http.StatusCreated, mapping)
}

// GetRoleMappings gets all role mappings
func (h *RoleMappingHandler) GetRoleMappings(c *gin.Context) {
	var mappings []models.RoleMapping
	if err := h.DB.Find(&mappings).Error; err != nil {
		log.Printf("Error getting role mappings: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get role mappings"})
		return
	}

	c.JSON(http.StatusOK, mappings)
}

// GetRoleMapping gets a role mapping by ID
func (h *RoleMappingHandler) GetRoleMapping(c *gin.Context) {
	id := c.Param("id")
	var mapping models.RoleMapping
	if err := h.DB.First(&mapping, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Role mapping not found"})
		} else {
			log.Printf("Error finding role mapping: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find role mapping"})
		}
		return
	}

	c.JSON(http.StatusOK, mapping)
}

// UpdateRoleMapping updates a role mapping
func (h *RoleMappingHandler) UpdateRoleMapping(c *gin.Context) {
	id := c.Param("id")
	var mapping models.RoleMapping
	if err := h.DB.First(&mapping, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Role mapping not found"})
		} else {
			log.Printf("Error finding role mapping: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find role mapping"})
		}
		return
	}

	// Bind the updated data
	if err := c.ShouldBindJSON(&mapping); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate the group pattern
	if _, err := regexp.Compile(mapping.GroupPattern); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid group pattern: " + err.Error()})
		return
	}

	// Update the mapping
	if err := h.DB.Save(&mapping).Error; err != nil {
		log.Printf("Error updating role mapping: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update role mapping"})
		return
	}

	c.JSON(http.StatusOK, mapping)
}

// DeleteRoleMapping deletes a role mapping
func (h *RoleMappingHandler) DeleteRoleMapping(c *gin.Context) {
	id := c.Param("id")
	result := h.DB.Delete(&models.RoleMapping{}, "id = ?", id)
	if result.Error != nil {
		log.Printf("Error deleting role mapping: %v", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete role mapping"})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Role mapping not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Role mapping deleted successfully"})
}
