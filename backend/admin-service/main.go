package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/claudio/deploy-orchestrator/admin-service/api"
	"github.com/claudio/deploy-orchestrator/admin-service/config"
	db_pkg "github.com/claudio/deploy-orchestrator/admin-service/db"
	"github.com/claudio/deploy-orchestrator/admin-service/internal/handlers"
	"github.com/claudio/deploy-orchestrator/admin-service/internal/services"
	"github.com/claudio/deploy-orchestrator/admin-service/middleware"
	"github.com/claudio/deploy-orchestrator/admin-service/migrations"
	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"github.com/claudio/deploy-orchestrator/admin-service/service"
	"github.com/claudio/deploy-orchestrator/admin-service/service/provider"
	"github.com/claudio/deploy-orchestrator/shared/auth"
	"github.com/claudio/deploy-orchestrator/shared/gateway"
	"github.com/claudio/deploy-orchestrator/shared/logging"
	"github.com/claudio/deploy-orchestrator/shared/monitoring"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Provider interfaces are implemented in the service/provider package

// Real implementations of handlers are in the api package

func main() {
	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		fmt.Printf("Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logger with configuration
	logging.InitLogger(cfg.Logging)
	logger := logging.Default().Named("admin-service")

	// Log startup information
	logger.Info("Starting admin service",
		logging.String("version", cfg.Service.Version),
		logging.String("environment", "development"), // Replace with actual environment
	)

	// Create default config file if it doesn't exist
	if err := config.SaveDefaultConfig(); err != nil {
		logger.Warn("Failed to save default config", logging.Error(err))
	}

	// Initialize database
	var db *gorm.DB
	var dbErr error
	if cfg.Database.URL != "" {
		logger.Info("Connecting to database",
			logging.String("url", cfg.Database.URL),
			logging.Int("maxRetries", cfg.Database.MaxRetries),
			logging.Int("retryInterval", cfg.Database.RetryInterval),
		)
		// Import the db package
		db, dbErr = db_pkg.InitDatabase(cfg)
	} else {
		logger.Info("Database URL not configured, using in-memory database")
		// Create a simple in-memory database
		db = &gorm.DB{}
	}

	if dbErr != nil {
		logger.Error("Failed to initialize database", logging.Error(dbErr))
		os.Exit(1)
	}

	logger.Info("Database connection established successfully")

	// Run database migrations
	logger.Info("Running database migrations...")
	if err := models.MigrateAll(db); err != nil {
		logger.Error("Failed to run database migrations", logging.Error(err))
		os.Exit(1)
	}
	logger.Info("Database migrations completed successfully")

	// Run plugin permission migrations
	logger.Info("Running plugin permission migrations...")
	if err := migrations.MigratePluginPermissions(db); err != nil {
		logger.Error("Failed to run plugin permission migrations", logging.Error(err))
		os.Exit(1)
	}
	logger.Info("Plugin permission migrations completed successfully")

	// Initialize monitoring system
	monitoringManager := monitoring.NewMonitoringManager("admin-service", cfg.Service.Version, "development")
	monitoringManager.AddDatabase(db, "postgres")
	logger.Info("Monitoring system initialized")

	// Initialize gateway registration
	zapLogger, _ := zap.NewProduction()
	gatewayClient := gateway.NewClientFromSharedConfig("workflow-service", cfg.Server.Port, &cfg.Gateway, zapLogger)
	gatewayClient.SafeRegister()
	logger.Info("Gateway registration attempted")

	// Initialize default data (users, roles, permissions)
	if err := db_pkg.SeedAdminUser(db); err != nil {
		logger.Error("Failed to initialize admin user", logging.Error(err))
		os.Exit(1)
	}

	if err := db_pkg.SeedRBAC(db); err != nil {
		logger.Error("Failed to initialize RBAC data", logging.Error(err))
		os.Exit(1)
	}

	// Initialize permissions and roles from configuration
	permissionInitService := service.NewPermissionInitService(db, cfg)

	// Validate configuration first
	if err := permissionInitService.ValidatePermissionConfiguration(); err != nil {
		logger.Error("Invalid permission configuration", logging.Error(err))
		os.Exit(1)
	}

	// Initialize permissions and roles
	if err := permissionInitService.InitializePermissionsAndRoles(context.Background()); err != nil {
		logger.Error("Failed to initialize permissions and roles from configuration", logging.Error(err))
		os.Exit(1)
	}

	logger.Info("Permissions and roles initialized from configuration")

	// Seed default projects
	if err := db_pkg.SeedProjects(db); err != nil {
		logger.Warn("Failed to seed default projects", logging.Error(err))
		// Don't exit on project seeding failure
	}

	logger.Info("Default data initialized successfully")

	// Initialize shared authentication manager with existing JWT manager
	authConfig := &auth.Config{
		JWTSecretKey:       cfg.Auth.JWTSecret,
		AccessTokenExpiry:  time.Duration(cfg.Auth.JWTExpirationMinutes) * time.Minute,
		RefreshTokenExpiry: time.Hour * 24 * 7, // 7 days
		AdminServiceURL:    fmt.Sprintf("http://localhost:%d", cfg.Server.Port),
	}
	authManager, err := auth.NewAuthManager(authConfig)
	if err != nil {
		logger.Error("Failed to create auth manager", logging.Error(err))
		os.Exit(1)
	}

	// Initialize services
	userService := service.NewUserService(db)
	identityProviderService := service.NewIdentityProviderService(db)
	permissionService := service.NewPermissionService(db)
	groupSyncService := service.NewGroupSyncService(db, identityProviderService, cfg)

	// Initialize plugin permission services
	pluginPermissionService := service.NewPluginPermissionService(db)
	pluginGroupSyncService := service.NewPluginGroupSyncService(db, groupSyncService, pluginPermissionService)

	if err := identityProviderService.Initialize(context.Background()); err != nil {
		logger.Error("Warning: Failed to initialize identity provider service: %v", logging.Error(err))
	}
	// Initialize identity provider services with real implementations
	oidcProviderImpl, err := provider.NewOIDCProvider(&cfg.IdentityProviders.OIDC)
	if err != nil {
		logger.Error("Failed to initialize OIDC provider", logging.Error(err))
	}
	// Create adapter that implements the OIDCProviderInterface
	oidcProvider := provider.NewOIDCProviderAdapter(oidcProviderImpl)

	samlProviderImpl, err := provider.NewSAMLProvider(&cfg.IdentityProviders.SAML)
	if err != nil {
		logger.Error("Failed to initialize SAML provider", logging.Error(err))
	}
	// Create adapter that implements the SAMLProviderInterface
	samlProvider := provider.NewSAMLProviderAdapter(samlProviderImpl)

	ldapProviderImpl := provider.NewLDAPProvider(&cfg.IdentityProviders.LDAP)
	// Create adapter that implements the LDAPProviderInterface
	ldapProvider := provider.NewLDAPProviderAdapter(ldapProviderImpl)

	// Initialize group sync service
	idpService := service.NewIdentityProviderService(db)

	// Set the identity provider service on the LDAP provider adapter
	ldapProvider.SetIdentityProviderService(idpService)
	ldapProvider.SetDB(db)

	// Use shared authentication middleware
	authMiddleware := authManager.AuthMiddleware()
	adminMiddleware := authManager.AdminMiddleware()

	// Initialize permission middleware
	permMiddleware := middleware.NewPermissionMiddleware(permissionService)

	// Set up the Gin router
	if cfg.Logging.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Setup monitoring routes and middleware
	monitoringManager.SetupRoutes(router)
	monitoringManager.SetupMiddleware(router)

	// Apply global middleware
	// Use simple logging middleware
	router.Use(func(c *gin.Context) {
		start := time.Now()
		c.Next()
		duration := time.Since(start)
		logger.Info("Request",
			logging.String("method", c.Request.Method),
			logging.String("path", c.Request.URL.Path),
			logging.Int("status", c.Writer.Status()),
			logging.Duration("duration", duration),
		)
	})

	// Use simple error handling middleware
	router.Use(func(c *gin.Context) {
		c.Next()
		if len(c.Errors) > 0 {
			logger.Error("Request error", logging.String("error", c.Errors.Last().Error()))
		}
	})

	// Apply global middleware
	router.Use(authManager.CORSMiddleware())

	// Initialize handlers
	authHandler := api.NewAuthHandler(userService, authManager.JWTManager, groupSyncService, oidcProvider, samlProvider, ldapProvider)
	rbacHandler := api.NewRBACHandler(db)
	roleMappingHandler := api.NewRoleMappingHandler(db)

	// Initialize plugin permission handler
	pluginPermissionHandler := api.NewPluginPermissionHandler(pluginPermissionService)

	// Initialize project service and handler
	projectService := service.NewProjectService(db)
	projectHandler := api.NewProjectHandler(projectService, permissionService)

	// Initialize group-project service and handler
	groupProjectService := services.NewGroupProjectService(db)
	groupProjectHandler := handlers.NewGroupProjectHandler(groupProjectService)

	// Initialize permission handler
	permissionHandler := api.NewPermissionHandler(permissionService)

	// Use real implementations of handlers
	userHandler := api.NewUserHandler(userService)

	identityProviderHandler := api.NewIdentityProviderHandler(identityProviderService)

	groupSyncHandler := api.NewGroupSyncHandler(db, groupSyncService)

	// Public routes - health checks are now handled by monitoring manager

	// Public auth routes - both at /api and /api/v1
	router.POST("/api/auth/login", authHandler.Login)
	router.POST("/api/auth/refresh", authHandler.RefreshToken)
	router.POST("/api/auth/external/initiate", authHandler.InitiateExternalLogin)
	router.POST("/api/auth/external/callback", authHandler.HandleExternalLoginCallback)

	// Also register at /api/v1 for compatibility
	router.POST("/api/v1/auth/login", authHandler.Login)
	router.POST("/api/v1/auth/refresh", authHandler.RefreshToken)
	router.POST("/api/v1/auth/external/initiate", authHandler.InitiateExternalLogin)
	router.POST("/api/v1/auth/external/callback", authHandler.HandleExternalLoginCallback)

	// Identity provider public endpoints
	router.GET("/api/identity-providers/enabled", authHandler.GetEnabledProviders)
	router.GET("/api/v1/identity-providers/enabled", authHandler.GetEnabledProviders)

	// Permission check endpoint - also available at the expected path for other services
	router.GET("/api/v1/permissions/check", authMiddleware, permissionHandler.CheckPermission)

	// API routes (protected) - versioned admin service API
	apiGroup := router.Group("/api/v1/admin-service")
	apiGroup.Use(authMiddleware)
	{
		// Permission check endpoint - used by other services for authorization
		apiGroup.GET("/permissions/check", permissionHandler.CheckPermission)

		// Current user project access - available to all authenticated users
		apiGroup.GET("/auth/user/projects", groupProjectHandler.GetCurrentUserProjects)

		// User routes - require appropriate permissions
		apiGroup.GET("/users", permMiddleware.RequirePermission("user:read", func(c *gin.Context) string { return "" }), userHandler.ListUsers)
		apiGroup.GET("/users/:userId", permMiddleware.RequirePermission("user:read", func(c *gin.Context) string { return "" }), userHandler.GetUser)

		// Admin-only routes
		adminRoutes := apiGroup.Group("")
		adminRoutes.Use(adminMiddleware)
		{
			// Permission management - use RBAC handler for permissions
			// The RBACHandler doesn't have a GetPermission method, so we'll use a custom handler
			adminRoutes.GET("/permissions", rbacHandler.ListPermissions)
			adminRoutes.GET("/permissions/by-category", func(c *gin.Context) {
				var permissions []models.Permission
				if err := rbacHandler.DB.Find(&permissions).Error; err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get permissions"})
					return
				}

				// Group permissions by category
				result := make(map[string][]models.Permission)
				for _, perm := range permissions {
					category := perm.Category
					if category == "" {
						category = "other"
					}
					result[category] = append(result[category], perm)
				}
				c.JSON(http.StatusOK, result)
			})
			// For individual permission endpoints, we'll use a custom handler
			adminRoutes.GET("/permissions/:id", func(c *gin.Context) {
				id := c.Param("id")
				var permission models.Permission
				if err := rbacHandler.DB.First(&permission, "id = ?", id).Error; err != nil {
					c.JSON(http.StatusNotFound, gin.H{"error": "Permission not found"})
					return
				}
				c.JSON(http.StatusOK, permission)
			})
			adminRoutes.POST("/permissions", rbacHandler.CreatePermission)
			adminRoutes.PUT("/permissions/:id", rbacHandler.UpdatePermission)
			adminRoutes.DELETE("/permissions/:id", rbacHandler.DeletePermission)

			// Role-Project assignment endpoints REMOVED - replaced with Group-Project model

			// Group-Project assignment endpoints (NEW GROUP-PROJECT MODEL) - ADMIN ONLY
			adminRoutes.POST("/group-projects", groupProjectHandler.AssignGroupToProject)
			adminRoutes.DELETE("/group-projects/:groupId/:projectId", groupProjectHandler.RemoveGroupFromProject)
			adminRoutes.GET("/groups/:groupId/projects", groupProjectHandler.GetGroupProjects)
			// Note: /projects/:projectId/groups is handled by projectHandler.GetGroupsForProject on line 381 with permission middleware
			// Note: /users/:userId/projects is handled by projectHandler.GetProjectsForUser on line 389 with permission middleware
			adminRoutes.GET("/users/:userId/project-access/:projectId", groupProjectHandler.CheckUserProjectAccess)
			adminRoutes.GET("/group-projects", groupProjectHandler.GetAllGroupProjectAssignments)
			adminRoutes.POST("/group-projects/bulk-groups", groupProjectHandler.BulkAssignGroupsToProject)
			adminRoutes.POST("/group-projects/bulk-projects", groupProjectHandler.BulkAssignProjectsToGroup)
			adminRoutes.GET("/group-projects/matrix", groupProjectHandler.GetUserProjectMatrix)

			// RBAC endpoints
			adminRoutes.GET("/groups", rbacHandler.ListGroups)
			adminRoutes.POST("/groups", rbacHandler.CreateGroup)
			adminRoutes.PUT("/groups/:groupId", rbacHandler.UpdateGroup)
			adminRoutes.DELETE("/groups/:groupId", rbacHandler.DeleteGroup)
			adminRoutes.GET("/roles", rbacHandler.ListRoles)
			adminRoutes.POST("/roles", rbacHandler.CreateRole)
			adminRoutes.GET("/roles/:roleId", rbacHandler.GetRole)
			adminRoutes.PUT("/roles/:roleId", rbacHandler.UpdateRole)
			adminRoutes.DELETE("/roles/:roleId", rbacHandler.DeleteRole)

			// Group-Role assignment
			adminRoutes.POST("/groups/:groupId/roles/:roleId", rbacHandler.AssignGroupToRole)
			adminRoutes.POST("/groups/:groupId/roles", rbacHandler.AssignGroupToRoleWithBody)
			adminRoutes.DELETE("/groups/:groupId/roles/:roleId", rbacHandler.RemoveRoleFromGroup)
			adminRoutes.GET("/groups/:groupId/roles", rbacHandler.GetGroupRoles)
			// Note: /groups/:groupId/projects is handled by groupProjectHandler.GetGroupProjects on line 335
			adminRoutes.POST("/roles/:roleId/permissions/:permissionId", rbacHandler.AssignPermissionToRole)

			// Role mapping endpoints for identity provider groups
			adminRoutes.GET("/role-mappings", roleMappingHandler.GetRoleMappings)
			adminRoutes.GET("/role-mappings/:id", roleMappingHandler.GetRoleMapping)
			adminRoutes.POST("/role-mappings", roleMappingHandler.CreateRoleMapping)
			adminRoutes.PUT("/role-mappings/:id", roleMappingHandler.UpdateRoleMapping)
			adminRoutes.DELETE("/role-mappings/:id", roleMappingHandler.DeleteRoleMapping)

			// Project management endpoints
			// Normal users can view projects they have access to
			apiGroup.GET("/projects", permMiddleware.RequirePermission("project:read", func(c *gin.Context) string { return "" }), projectHandler.ListProjects)
			apiGroup.GET("/projects/:projectId", permMiddleware.ProjectAccessMiddleware(), projectHandler.GetProject)

			// Only admins can create, update, delete projects
			adminRoutes.POST("/projects", permMiddleware.RequirePermission("project:create", func(c *gin.Context) string { return "" }), projectHandler.CreateProject)
			adminRoutes.PUT("/projects/:projectId", permMiddleware.RequirePermission("project:update", func(c *gin.Context) string { return c.Param("projectId") }), projectHandler.UpdateProject)
			adminRoutes.DELETE("/projects/:projectId", permMiddleware.RequirePermission("project:delete", func(c *gin.Context) string { return c.Param("projectId") }), projectHandler.DeleteProject)

			// Project group assignment endpoints - admin only
			adminRoutes.GET("/projects/:projectId/groups", permMiddleware.RequirePermission("project:read", func(c *gin.Context) string { return c.Param("projectId") }), projectHandler.GetGroupsForProject)
			adminRoutes.POST("/projects/:projectId/groups/:groupId", permMiddleware.RequirePermission("project:update", func(c *gin.Context) string { return c.Param("projectId") }), projectHandler.AssignGroupToProject)
			adminRoutes.DELETE("/projects/:projectId/groups/:groupId", permMiddleware.RequirePermission("project:update", func(c *gin.Context) string { return c.Param("projectId") }), projectHandler.RemoveGroupFromProject)

			// User management - admin only
			adminRoutes.POST("/users", permMiddleware.RequirePermission("user:create", func(c *gin.Context) string { return "" }), userHandler.CreateUser)

			// User project access endpoints
			apiGroup.GET("/users/:userId/projects", permMiddleware.RequirePermission("user:read", func(c *gin.Context) string { return "" }), projectHandler.GetProjectsForUser)
			apiGroup.GET("/users/:userId/projects/:projectId/access", permMiddleware.ProjectAccessMiddleware(), projectHandler.CheckUserProjectAccess)

			adminRoutes.POST("/sync-all-users", groupSyncHandler.SyncAllUsers)
			adminRoutes.POST("/sync-user-by-username", groupSyncHandler.SyncUserByUsername)

			// Identity provider management
			identityProviderHandler.RegisterRoutes(adminRoutes)

			// Plugin permission management
			pluginPermissionHandler.RegisterRoutes(apiGroup)

			// Plugin group sync endpoints (admin only)
			adminRoutes.POST("/plugin-permissions/sync-all-users", func(c *gin.Context) {
				result, err := pluginGroupSyncService.SyncAllUsersPluginAccess(c.Request.Context(), false)
				if err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to sync plugin permissions", "details": err.Error()})
					return
				}
				c.JSON(http.StatusOK, result)
			})

			adminRoutes.POST("/plugin-permissions/sync-user/:userId", func(c *gin.Context) {
				userID := c.Param("userId")
				if err := pluginGroupSyncService.SyncPluginGroupAccess(c.Request.Context(), userID); err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to sync user plugin permissions", "details": err.Error()})
					return
				}
				c.JSON(http.StatusOK, gin.H{"message": "User plugin permissions synced successfully"})
			})

			// Other admin endpoints
		}
	}

	// Add missing endpoints that frontend expects
	// Audit logs endpoint - proxy to audit service or implement locally
	router.GET("/api/v1/audit", func(c *gin.Context) {
		// For now, return empty audit logs
		// TODO: Implement proper audit log retrieval
		c.JSON(http.StatusOK, gin.H{
			"auditLogs": []interface{}{},
			"total":     0,
		})
	})

	// Settings endpoint
	router.GET("/api/v1/settings", func(c *gin.Context) {
		// Return basic system settings
		// TODO: Implement proper settings management
		c.JSON(http.StatusOK, gin.H{
			"settings": map[string]interface{}{
				"systemName":  "Deploy Orchestrator",
				"version":     "1.0.0",
				"environment": "development",
				"maintenance": false,
				"features": map[string]bool{
					"audit":         true,
					"monitoring":    true,
					"notifications": true,
					"multiTenant":   false,
				},
			},
		})
	})

	// Start the server
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
		Handler: router,
	}

	// Start server in a goroutine so it doesn't block
	go func() {
		logger.Info("Starting admin service",
			logging.String("host", cfg.Server.Host),
			logging.Int("port", cfg.Server.Port),
		)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", logging.Error(err))
		}
	}()

	// Set up graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info("Shutting down server...")

	// Deregister from gateway
	gatewayClient.SafeDeregister()
	logger.Info("Deregistered from gateway")

	// Create a deadline to wait for
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Shutdown monitoring system
	if err := monitoringManager.Shutdown(ctx); err != nil {
		logger.Error("Failed to shutdown monitoring system", logging.Error(err))
	}

	if err := srv.Shutdown(ctx); err != nil {
		logger.Fatal("Server forced to shutdown", logging.Error(err))
	}

	logger.Info("Server exited properly")
}
