package config

import (
	"fmt"

	sharedConfig "github.com/claudio/deploy-orchestrator/shared/config"
)

// NotificationServiceConfig holds the configuration for the notification service
type NotificationServiceConfig struct {
	// Common configuration sections
	Database sharedConfig.DBConfig      `mapstructure:"database" yaml:"database"`
	Server   sharedConfig.ServerConfig  `mapstructure:"server" yaml:"server"`
	Logging  sharedConfig.LoggingConfig `mapstructure:"logging" yaml:"logging"`
	Tracing  sharedConfig.TracingConfig `mapstructure:"tracing" yaml:"tracing"`
	Auth     sharedConfig.AuthConfig    `mapstructure:"auth" yaml:"auth"`
	Gateway  sharedConfig.GatewayConfig `mapstructure:"gateway" yaml:"gateway"`

	// Notification service specific configuration
	SMTPHost         string `mapstructure:"smtp_host" yaml:"smtp_host" env:"SMTP_HOST" default:""`
	SMTPPort         int    `mapstructure:"smtp_port" yaml:"smtp_port" env:"SMTP_PORT" default:"587"`
	SMTPUsername     string `mapstructure:"smtp_username" yaml:"smtp_username" env:"SMTP_USERNAME" default:""`
	SMTPPassword     string `mapstructure:"smtp_password" yaml:"smtp_password" env:"SMTP_PASSWORD" default:""`
	SMTPFrom         string `mapstructure:"smtp_from" yaml:"smtp_from" env:"SMTP_FROM" default:"<EMAIL>"`
	SMTPTLS          bool   `mapstructure:"smtp_tls" yaml:"smtp_tls" env:"SMTP_TLS" default:"true"`
	MaxRetries       int    `mapstructure:"max_retries" yaml:"max_retries" env:"MAX_RETRIES" default:"3"`
	RetryIntervalSec int    `mapstructure:"retry_interval_sec" yaml:"retry_interval_sec" env:"RETRY_INTERVAL_SEC" default:"60"`
	WorkerCount      int    `mapstructure:"worker_count" yaml:"worker_count" env:"WORKER_COUNT" default:"5"`
	BatchSize        int    `mapstructure:"batch_size" yaml:"batch_size" env:"BATCH_SIZE" default:"50"`
	SlackWebhookURL  string `mapstructure:"slack_webhook_url" yaml:"slack_webhook_url" env:"SLACK_WEBHOOK_URL" default:""`
	TeamsWebhookURL  string `mapstructure:"teams_webhook_url" yaml:"teams_webhook_url" env:"TEAMS_WEBHOOK_URL" default:""`
	TemplateDir      string `mapstructure:"template_dir" yaml:"template_dir" env:"TEMPLATE_DIR" default:"./templates"`
}

// LoadConfig loads the notification service configuration from file and environment variables
func LoadConfig() (*NotificationServiceConfig, error) {
	config := &NotificationServiceConfig{
		Server: sharedConfig.ServerConfig{
			Port: 8083, // Default port for notification service
		},
		Tracing: sharedConfig.TracingConfig{
			ServiceName: "notification-service",
		},
	}

	if err := sharedConfig.LoadConfig(config, "notification-service"); err != nil {
		return nil, fmt.Errorf("failed to load notification service config: %w", err)
	}

	return config, nil
}

// SaveDefaultConfig saves a default configuration file if one doesn't exist
func SaveDefaultConfig() error {
	config := &NotificationServiceConfig{
		Server: sharedConfig.ServerConfig{
			Port: 8083,
		},
		Tracing: sharedConfig.TracingConfig{
			ServiceName: "notification-service",
		},
	}

	return sharedConfig.SaveDefaultConfig(config, "notification-service")
}
