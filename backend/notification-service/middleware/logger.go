package middleware

import (
	"log"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// Logger is a middleware for logging HTTP requests
func Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Start timer
		start := time.Now()

		// Generate request ID
		requestID := uuid.New().String()
		c.Set("requestID", requestID)
		c.<PERSON><PERSON>("X-Request-ID", requestID)

		// Process request
		c.Next()

		// Calculate response time
		duration := time.Since(start)

		// Log request details
		log.Printf(
			"[%s] | %s | %d | %s | %s | %s",
			requestID,
			c.Request.Method,
			c.Writer.Status(),
			c.Request.URL.Path,
			c.ClientIP(),
			duration,
		)
	}
}
