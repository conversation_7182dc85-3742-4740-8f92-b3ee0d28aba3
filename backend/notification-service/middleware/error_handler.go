package middleware

import (
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

// ErrorHandler is a middleware for handling errors
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Process request
		c.Next()

		// Check if there were any errors
		if len(c.Errors) > 0 {
			// Get the first error
			err := c.Errors.Last()

			// Log the error
			log.Printf("Error: %v", err.Err)

			// Check if we already have a response
			if c.Writer.Status() != http.StatusOK {
				return
			}

			// Return a generic error response
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "An internal server error occurred",
			})
		}
	}
}
