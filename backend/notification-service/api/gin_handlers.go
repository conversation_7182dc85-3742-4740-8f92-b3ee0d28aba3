package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/claudio/deploy-orchestrator/notification-service/storage"
	"github.com/claudio/deploy-orchestrator/shared/auth"
	"github.com/claudio/deploy-orchestrator/shared/models"
)

// NotificationHandler handles HTTP requests for notifications
type NotificationHandler struct {
	db         *storage.Database
	authHelper *auth.AuthHelper
}

// NewNotificationHandler creates a new NotificationHandler
func NewNotificationHandler(db *storage.Database) *NotificationHandler {
	return &NotificationHandler{
		db:         db,
		authHelper: auth.NewAuthHelper(),
	}
}

// GetNotifications retrieves all notifications
func (h *NotificationHandler) GetNotifications(c *gin.Context) {
	// Check if filtering by user is requested
	userID := c.Query("userId")
	notificationType := c.Query("type")
	status := c.Query("status")

	// Parse pagination parameters
	limit := 10 // Default limit
	offset := 0 // Default offset

	// Try to parse limit and offset from query parameters if provided
	if limitParam := c.Query("limit"); limitParam != "" {
		if parsedLimit, err := parseIntParameter(limitParam, limit); err == nil {
			limit = parsedLimit
		}
	}

	if offsetParam := c.Query("offset"); offsetParam != "" {
		if parsedOffset, err := parseIntParameter(offsetParam, offset); err == nil {
			offset = parsedOffset
		}
	}

	var notifications []models.Notification
	var err error

	if userID != "" {
		notifications, err = h.db.GetNotificationsByUserID(c.Request.Context(), userID)
	} else {
		notifications, err = h.db.GetNotifications(c.Request.Context(), userID, notificationType, status, limit, offset)
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve notifications"})
		return
	}

	c.JSON(http.StatusOK, notifications)
}

// GetNotification retrieves a notification by ID
func (h *NotificationHandler) GetNotification(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Notification ID is required"})
		return
	}

	notification, err := h.db.GetNotificationByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve notification"})
		return
	}

	if notification == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Notification not found"})
		return
	}

	c.JSON(http.StatusOK, notification)
}

// CreateNotification creates a new notification
func (h *NotificationHandler) CreateNotification(c *gin.Context) {
	var notification models.Notification
	if err := c.ShouldBindJSON(&notification); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification data"})
		return
	}

	// Generate UUID if not provided
	if notification.ID == "" {
		notification.ID = uuid.New().String()
	}

	if err := h.db.CreateNotification(c.Request.Context(), &notification); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create notification"})
		return
	}

	c.JSON(http.StatusCreated, notification)
}

// UpdateNotification updates an existing notification
func (h *NotificationHandler) UpdateNotification(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Notification ID is required"})
		return
	}

	var notification models.Notification
	if err := c.ShouldBindJSON(&notification); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification data"})
		return
	}

	// Make sure ID in URL matches ID in body
	notification.ID = id

	if err := h.db.UpdateNotification(c.Request.Context(), &notification); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update notification"})
		return
	}

	c.JSON(http.StatusOK, notification)
}

// DeleteNotification deletes a notification
func (h *NotificationHandler) DeleteNotification(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Notification ID is required"})
		return
	}

	if err := h.db.DeleteNotification(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete notification"})
		return
	}

	c.Status(http.StatusNoContent)
}

// MarkAsRead marks a notification as read
func (h *NotificationHandler) MarkAsRead(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Notification ID is required"})
		return
	}

	if err := h.db.MarkNotificationAsRead(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to mark notification as read"})
		return
	}

	c.Status(http.StatusNoContent)
}

// parseIntParameter parses a string parameter to an integer with a fallback value
func parseIntParameter(param string, fallback int) (int, error) {
	if param == "" {
		return fallback, nil
	}

	val, err := strconv.Atoi(param)
	if err != nil {
		return fallback, err
	}

	return val, nil
}
