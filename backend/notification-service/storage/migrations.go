package storage

import (
	"log"

	"github.com/claudio/deploy-orchestrator/shared/db"
	sharedModels "github.com/claudio/deploy-orchestrator/shared/models"
	"gorm.io/gorm"
)

// Database represents a connection to the database using GORM
type Database struct {
	DB         *gorm.DB
	InMemoryDB *InMemoryDatabase
}

// NewDatabase creates a new database connection using GORM
func NewDatabase(postgresURL string) (*Database, error) {
	config := db.Config{
		URL:           postgresURL,
		MaxRetries:    5,
		RetryInterval: 2,
	}

	gormDB, err := db.Connect(config)
	if err != nil {
		return nil, err
	}

	// Run migrations immediately
	if err := RunMigrations(gormDB); err != nil {
		return nil, err
	}

	return &Database{DB: gormDB}, nil
}

// Close closes the database connection
func (d *Database) Close() error {
	// If using in-memory database, no need to close anything
	if d.InMemoryDB != nil {
		return nil
	}

	// Close the real database connection
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// RunMigrations performs all necessary database migrations
func RunMigrations(grm *gorm.DB) error {
	log.Println("Running notification-service database migrations...")

	// Use the shared RunMigrations function to run migrations for all models
	return db.RunMigrations(grm,
		&sharedModels.Notification{},
		&sharedModels.Integration{}, // If integrations are part of this service
	)
}

// SeedTestData populates the database with initial test data
// Only for testing purposes
func SeedTestData(db *gorm.DB) error {
	log.Println("Seeding notification database with test data...")

	// Create sample notifications
	testNotifications := []sharedModels.Notification{
		{
			ID:        "notif-test-1",
			Type:      "email",
			Message:   "Test notification 1",
			Recipient: "<EMAIL>",
			Status:    "sent",
		},
		{
			ID:        "notif-test-2",
			Type:      "slack",
			Message:   "Test notification 2",
			Recipient: "#deployments",
			Status:    "pending",
		},
	}

	// Add the test notifications to the database
	for _, notification := range testNotifications {
		if result := db.Create(&notification); result.Error != nil {
			return result.Error
		}
	}

	return nil
}
