package storage

import (
	"context"
	"errors"
	"log"
	"sync"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/models"
	"github.com/google/uuid"
)

// InMemoryDatabase provides a simple in-memory implementation for testing
type InMemoryDatabase struct {
	notifications map[string]models.Notification
	mu            sync.RWMutex // For thread-safety
}

// NewInMemoryDatabase creates a new in-memory database
func NewInMemoryDatabase() *InMemoryDatabase {
	log.Println("Using in-memory database for notification-service")
	return &InMemoryDatabase{
		notifications: make(map[string]models.Notification),
	}
}

// CreateNotification creates a new notification in memory
func (db *InMemoryDatabase) CreateNotification(ctx context.Context, notification *models.Notification) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if notification.ID == "" {
		notification.ID = uuid.New().String()
	}

	if notification.CreatedAt.IsZero() {
		notification.CreatedAt = time.Now()
	}

	db.notifications[notification.ID] = *notification
	return nil
}

// GetNotifications retrieves all notifications from memory with optional filtering
func (db *InMemoryDatabase) GetNotifications(ctx context.Context, recipientFilter, typeFilter, statusFilter string, limit, offset int) ([]models.Notification, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	var results []models.Notification

	// Apply filters
	for _, notification := range db.notifications {
		match := true

		if recipientFilter != "" && notification.Recipient != recipientFilter {
			match = false
		}

		if typeFilter != "" && notification.Type != typeFilter {
			match = false
		}

		if statusFilter != "" && notification.Status != statusFilter {
			match = false
		}

		if match {
			results = append(results, notification)
		}
	}

	// Sort by created_at in descending order (newest first)
	// A proper implementation would sort here

	// Apply pagination
	totalCount := len(results)
	if offset >= totalCount {
		return []models.Notification{}, nil
	}

	end := offset + limit
	if end > totalCount {
		end = totalCount
	}

	if offset < totalCount {
		results = results[offset:end]
	}

	return results, nil
}

// GetNotificationByID retrieves a notification by ID from memory
func (db *InMemoryDatabase) GetNotificationByID(ctx context.Context, id string) (*models.Notification, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	notification, exists := db.notifications[id]
	if !exists {
		return nil, nil
	}

	return &notification, nil
}

// UpdateNotificationStatus updates the status of a notification in memory
func (db *InMemoryDatabase) UpdateNotificationStatus(ctx context.Context, id string, status string) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	notification, exists := db.notifications[id]
	if !exists {
		return errors.New("notification not found")
	}

	notification.Status = status
	db.notifications[id] = notification

	return nil
}

// UpdateNotification updates an existing notification in memory
func (db *InMemoryDatabase) UpdateNotification(ctx context.Context, notification *models.Notification) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if _, exists := db.notifications[notification.ID]; !exists {
		return errors.New("notification not found")
	}

	db.notifications[notification.ID] = *notification
	return nil
}

// DeleteNotification deletes a notification from memory
func (db *InMemoryDatabase) DeleteNotification(ctx context.Context, id string) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if _, exists := db.notifications[id]; !exists {
		return errors.New("notification not found")
	}

	delete(db.notifications, id)
	return nil
}

// MarkAllAsRead marks all notifications for a recipient as read in memory
func (db *InMemoryDatabase) MarkAllAsRead(ctx context.Context, recipient string) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	for id, notification := range db.notifications {
		if notification.Recipient == recipient && notification.Status != "read" {
			notification.Status = "read"
			db.notifications[id] = notification
		}
	}

	return nil
}

// CountUnreadNotifications counts unread notifications for a recipient from memory
func (db *InMemoryDatabase) CountUnreadNotifications(ctx context.Context, recipient string) (int, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	count := 0
	for _, notification := range db.notifications {
		if notification.Recipient == recipient && notification.Status != "read" {
			count++
		}
	}

	return count, nil
}

// TestConnection for in-memory database always returns nil
func (db *InMemoryDatabase) TestConnection() error {
	return nil
}

// Close does nothing for in-memory database
func (db *InMemoryDatabase) Close() error {
	return nil
}

// GetIntegrationByType retrieves an integration by type from memory
func (db *InMemoryDatabase) GetIntegrationByType(ctx context.Context, integrationType string) (*models.Integration, error) {
	// In a real implementation, you would store integrations in memory
	// Since this is just a fallback, we'll return nil to indicate nothing found
	return nil, nil
}
