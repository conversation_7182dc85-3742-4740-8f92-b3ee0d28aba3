package storage

import (
	"context"
	"errors"
	"fmt"

	"github.com/claudio/deploy-orchestrator/shared/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// CreateNotification creates a new notification
func (db *Database) CreateNotification(ctx context.Context, notification *models.Notification) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateNotification(ctx, notification)
	}

	if notification.ID == "" {
		notification.ID = uuid.New().String()
	}

	return db.DB.WithContext(ctx).Create(notification).Error
}

// GetNotifications retrieves all notifications with optional filtering
func (db *Database) GetNotifications(ctx context.Context, recipientFilter, typeFilter, statusFilter string, limit, offset int) ([]models.Notification, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetNotifications(ctx, recipientFilter, typeFilter, statusFilter, limit, offset)
	}

	var notifications []models.Notification
	query := db.DB.WithContext(ctx).Model(&models.Notification{})

	// Apply filters if provided
	if recipientFilter != "" {
		query = query.Where("recipient = ?", recipientFilter)
	}

	if typeFilter != "" {
		query = query.Where("type = ?", typeFilter)
	}

	if statusFilter != "" {
		query = query.Where("status = ?", statusFilter)
	}

	// Apply pagination
	query = query.Limit(limit).Offset(offset).Order("created_at DESC")

	// Execute the query
	if err := query.Find(&notifications).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve notifications: %w", err)
	}

	return notifications, nil
}

// GetNotificationByID retrieves a notification by ID
func (db *Database) GetNotificationByID(ctx context.Context, id string) (*models.Notification, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetNotificationByID(ctx, id)
	}

	var notification models.Notification
	if err := db.DB.WithContext(ctx).Where("id = ?", id).First(&notification).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // Notification not found
		}
		return nil, fmt.Errorf("notification not found: %w", err)
	}

	return &notification, nil
}

// UpdateNotification updates an existing notification
func (db *Database) UpdateNotification(ctx context.Context, notification *models.Notification) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateNotification(ctx, notification)
	}

	return db.DB.WithContext(ctx).Save(notification).Error
}

// DeleteNotification soft-deletes a notification
func (db *Database) DeleteNotification(ctx context.Context, id string) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.DeleteNotification(ctx, id)
	}

	return db.DB.WithContext(ctx).Delete(&models.Notification{ID: id}).Error
}

// GetIntegrationByType retrieves an integration by type
func (db *Database) GetIntegrationByType(ctx context.Context, integrationType string) (*models.Integration, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetIntegrationByType(ctx, integrationType)
	}

	var integration models.Integration
	if err := db.DB.WithContext(ctx).Where("type = ? AND enabled = ?", integrationType, true).First(&integration).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // Integration not found
		}
		return nil, fmt.Errorf("integration not found: %w", err)
	}

	return &integration, nil
}

// GetNotificationsByUserID retrieves all notifications for a specific user
func (db *Database) GetNotificationsByUserID(ctx context.Context, userID string) ([]models.Notification, error) {
	// Use the existing GetNotifications method with the userID as the recipient filter
	return db.GetNotifications(ctx, userID, "", "", 100, 0) // Default limit 100, offset 0
}

// MarkNotificationAsRead marks a notification as read
func (db *Database) MarkNotificationAsRead(ctx context.Context, id string) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateNotificationStatus(ctx, id, "read")
	}

	// Update the status in the database
	return db.DB.WithContext(ctx).Model(&models.Notification{}).
		Where("id = ?", id).
		Update("status", "read").
		Error
}
