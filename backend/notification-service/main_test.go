package main

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/claudio/deploy-orchestrator/shared/handlers"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupTestRouter() *gin.Engine {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a new router
	router := gin.New()

	// Add health check endpoint
	serviceInfo := handlers.ServiceInfo{
		Name:    serviceName,
		Version: version,
	}
	router.GET("/health", handlers.NewHealthHandler(serviceInfo))

	return router
}

func TestHealthEndpoint(t *testing.T) {
	router := setupTestRouter()

	// Create a test request
	req, err := http.NewRequest("GET", "/health", nil)
	assert.NoError(t, err)

	// Create a response recorder
	w := httptest.NewRecorder()

	// Serve the request
	router.ServeHTTP(w, req)

	// Assert the response
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse the response body
	var response handlers.HealthResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check the response values
	assert.Equal(t, serviceName, response.Service)
	assert.Equal(t, "healthy", response.Status)
	assert.Equal(t, version, response.Version)
}
