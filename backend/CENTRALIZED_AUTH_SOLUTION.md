# Centralized Authentication Solution

## Problem

The issue was that when JWT tokens expired, backend services were not properly detecting the expiration and returning appropriate 401 errors. Instead, they were checking for roles in the context without first verifying authentication, leading to:

1. `roles, exists := c.Get("roles")` returning `nil` instead of proper 401 error
2. Users staying "logged in" with expired tokens but seeing no data
3. No automatic redirect to login page on token expiration
4. Inconsistent authentication handling across services

## Solution

### 1. Enhanced Shared Auth Middleware (`backend/shared/auth/middleware.go`)

**Improvements:**
- Better error logging with specific error codes
- Structured error responses with error codes for frontend handling
- Validation of required claims (userID, username, email)
- Specific error codes: `TOKEN_EXPIRED`, `TOKEN_INVALID`, `TOKEN_MALFORMED`, `AUTH_HEADER_MISSING`, etc.

### 2. New Centralized Auth Helper (`backend/shared/auth/auth_helper.go`)

**Features:**
- `RequireAuthentication()`: Validates complete auth context and returns 401 if invalid
- `GetOptionalAuthentication()`: Returns auth context if available, nil if not (no abort)
- `AuthContext` struct with helper methods: `IsAdmin()`, `HasRole()`, `HasAnyRole()`
- Consistent error responses across all services

**Usage Example:**
```go
// In any handler
authContext := h.authHelper.RequireAuthentication(c)
if authContext == nil {
    // 401 response already sent
    return
}

// Now safely use authentication data
isAdmin := authContext.IsAdmin()
userID := authContext.UserID
```

### 3. Updated Workflow Service

**Changes:**
- Added `authHelper` to `WorkflowHandler` struct
- Updated `GetWorkflows()` to use centralized auth checking
- Updated `ExecuteWorkflow()` to use centralized auth checking
- Removed manual role checking logic

### 4. Enhanced Frontend Error Handling

**Deploy Orchestrator (`error.interceptor.ts`):**
- Detects specific error codes from backend
- Attempts token refresh for recoverable errors (`TOKEN_EXPIRED`, `TOKEN_INVALID`, `AUTH_REQUIRED`)
- Immediate logout for unrecoverable errors (`TOKEN_MALFORMED`, `TOKEN_CLAIMS_INVALID`)
- Better logging for debugging

**Main UI (`jwt.interceptor.ts`):**
- Similar error code detection and handling
- Improved logging for token refresh process
- Graceful handling of different token response formats

## Benefits

1. **Consistent Authentication**: All services now use the same authentication validation logic
2. **Proper Token Expiration Handling**: Expired tokens immediately return 401 with specific error codes
3. **Better User Experience**: Automatic token refresh or proper redirect to login
4. **Easier Debugging**: Structured error responses and comprehensive logging
5. **Maintainable Code**: Centralized auth logic reduces duplication

## Implementation Status

✅ **Completed:**
- Enhanced shared auth middleware with error codes
- Created centralized auth helper
- Updated workflow service to use centralized auth
- Enhanced frontend error interceptors

🔄 **Next Steps:**
- Update other services (deployment-service, audit-service, etc.) to use centralized auth helper
- Add unit tests for auth helper
- Update service documentation

## Usage Guidelines

### For Backend Services:

1. **Add auth helper to handler struct:**
```go
type MyHandler struct {
    authHelper *auth.AuthHelper
    // other fields
}

func NewMyHandler() *MyHandler {
    return &MyHandler{
        authHelper: auth.NewAuthHelper(),
    }
}
```

2. **Use in handlers:**
```go
func (h *MyHandler) MyEndpoint(c *gin.Context) {
    authContext := h.authHelper.RequireAuthentication(c)
    if authContext == nil {
        return // 401 already sent
    }
    
    // Use authContext.UserID, authContext.IsAdmin(), etc.
}
```

### For Frontend:

The error interceptors automatically handle token expiration and refresh. No changes needed in components.

## Error Codes Reference

| Code | Description | Frontend Action |
|------|-------------|-----------------|
| `TOKEN_EXPIRED` | JWT token has expired | Attempt refresh |
| `TOKEN_INVALID` | JWT token is invalid/corrupted | Attempt refresh |
| `TOKEN_MALFORMED` | JWT token format is wrong | Logout immediately |
| `TOKEN_CLAIMS_INVALID` | Required claims missing | Logout immediately |
| `AUTH_HEADER_MISSING` | No Authorization header | Attempt refresh |
| `AUTH_REQUIRED` | Authentication required | Attempt refresh |
