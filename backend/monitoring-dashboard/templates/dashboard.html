<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deploy Orchestrator - Monitoring Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
            border-radius: 10px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid #667eea;
        }
        
        .summary-card h3 {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .summary-card p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .overall-status {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            text-transform: uppercase;
        }
        
        .status-healthy {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-degraded {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-unhealthy {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .service-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.2s ease;
        }
        
        .service-card:hover {
            transform: translateY(-2px);
        }
        
        .service-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .service-name {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .service-url {
            color: #666;
            font-size: 0.9rem;
            word-break: break-all;
        }
        
        .service-status {
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .service-details {
            padding: 0 20px 20px;
            font-size: 0.9rem;
            color: #666;
        }
        
        .service-details div {
            margin-bottom: 5px;
        }
        
        .response-time {
            font-weight: bold;
        }
        
        .timestamp {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 0.9rem;
        }
        
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            margin: 20px auto;
            display: block;
        }
        
        .refresh-btn:hover {
            background: #5a6fd8;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .services-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Deploy Orchestrator</h1>
            <p>Monitoring Dashboard</p>
        </div>
        
        <div class="overall-status">
            <div class="status-badge status-{{.summary.overall}}">
                System Status: {{.summary.overall}}
            </div>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>{{.summary.total}}</h3>
                <p>Total Services</p>
            </div>
            <div class="summary-card">
                <h3>{{.summary.healthy}}</h3>
                <p>Healthy</p>
            </div>
            <div class="summary-card">
                <h3>{{.summary.degraded}}</h3>
                <p>Degraded</p>
            </div>
            <div class="summary-card">
                <h3>{{.summary.unhealthy}}</h3>
                <p>Unhealthy</p>
            </div>
        </div>
        
        <div class="services-grid">
            {{range .services}}
            <div class="service-card">
                <div class="service-header">
                    <div class="service-name">{{.Name}}</div>
                    <div class="service-url">{{.URL}}</div>
                </div>
                <div class="service-status">
                    <div class="status-badge status-{{.Status}}">{{.Status}}</div>
                    <div class="response-time">{{.ResponseTime}}</div>
                </div>
                <div class="service-details">
                    <div><strong>Last Checked:</strong> {{.LastChecked.Format "15:04:05"}}</div>
                    {{if .Details}}
                        {{if .Details.service}}
                            <div><strong>Service:</strong> {{.Details.service}}</div>
                        {{end}}
                        {{if .Details.version}}
                            <div><strong>Version:</strong> {{.Details.version}}</div>
                        {{end}}
                        {{if .Details.uptime}}
                            <div><strong>Uptime:</strong> {{.Details.uptime}}</div>
                        {{end}}
                    {{end}}
                </div>
            </div>
            {{end}}
        </div>
        
        <button class="refresh-btn" onclick="location.reload()">Refresh Status</button>
        
        <div class="timestamp">
            Last updated: {{.timestamp}}
        </div>
    </div>
    
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
