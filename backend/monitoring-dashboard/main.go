package main

import (
	"context"
	"encoding/json"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
)

// ServiceStatus represents the status of a service
type ServiceStatus struct {
	Name         string                 `json:"name"`
	URL          string                 `json:"url"`
	Status       string                 `json:"status"`
	LastChecked  time.Time              `json:"last_checked"`
	ResponseTime time.Duration          `json:"response_time"`
	Details      map[string]interface{} `json:"details,omitempty"`
}

// MonitoringDashboard manages the monitoring dashboard
type MonitoringDashboard struct {
	services []ServiceConfig
}

// ServiceConfig represents a service configuration
type ServiceConfig struct {
	Name       string `json:"name"`
	HealthURL  string `json:"health_url"`
	MetricsURL string `json:"metrics_url"`
}

// NewMonitoringDashboard creates a new monitoring dashboard
func NewMonitoringDashboard() *MonitoringDashboard {
	return &MonitoringDashboard{
		services: []ServiceConfig{
			{
				Name:       "Admin Service",
				HealthURL:  "http://localhost:8086/health",
				MetricsURL: "http://localhost:8086/metrics",
			},
			{
				Name:       "Deployment Service",
				HealthURL:  "http://localhost:8080/health",
				MetricsURL: "http://localhost:8080/metrics",
			},
			{
				Name:       "Scheduling Service",
				HealthURL:  "http://localhost:8081/health",
				MetricsURL: "http://localhost:8081/metrics",
			},
			{
				Name:       "Integration Service",
				HealthURL:  "http://localhost:8083/health",
				MetricsURL: "http://localhost:8083/metrics",
			},
			{
				Name:       "Notification Service",
				HealthURL:  "http://localhost:8082/health",
				MetricsURL: "http://localhost:8082/metrics",
			},
			{
				Name:       "Audit Service",
				HealthURL:  "http://localhost:8084/health",
				MetricsURL: "http://localhost:8084/metrics",
			},
			{
				Name:       "Workflow Service",
				HealthURL:  "http://localhost:8085/health",
				MetricsURL: "http://localhost:8085/metrics",
			},
			{
				Name:       "Audit Service",
				HealthURL:  "http://localhost:8087/health",
				MetricsURL: "http://localhost:8087/metrics",
			},
		},
	}
}

// CheckServiceHealth checks the health of a service
func (md *MonitoringDashboard) CheckServiceHealth(service ServiceConfig) ServiceStatus {
	start := time.Now()
	status := ServiceStatus{
		Name:        service.Name,
		URL:         service.HealthURL,
		LastChecked: start,
	}

	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	resp, err := client.Get(service.HealthURL)
	if err != nil {
		status.Status = "unhealthy"
		status.ResponseTime = time.Since(start)
		return status
	}
	defer resp.Body.Close()

	status.ResponseTime = time.Since(start)

	if resp.StatusCode == http.StatusOK {
		status.Status = "healthy"

		// Try to parse response for additional details
		var details map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&details); err == nil {
			status.Details = details
		}
	} else if resp.StatusCode == http.StatusPartialContent {
		status.Status = "degraded"
	} else {
		status.Status = "unhealthy"
	}

	return status
}

// GetAllServiceStatus gets the status of all services
func (md *MonitoringDashboard) GetAllServiceStatus() []ServiceStatus {
	statuses := make([]ServiceStatus, len(md.services))

	for i, service := range md.services {
		statuses[i] = md.CheckServiceHealth(service)
	}

	return statuses
}

// DashboardHandler handles the dashboard page
func (md *MonitoringDashboard) DashboardHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		statuses := md.GetAllServiceStatus()

		// Calculate overall system health
		healthy := 0
		degraded := 0
		unhealthy := 0

		for _, status := range statuses {
			switch status.Status {
			case "healthy":
				healthy++
			case "degraded":
				degraded++
			case "unhealthy":
				unhealthy++
			}
		}

		overallStatus := "healthy"
		if unhealthy > 0 {
			overallStatus = "unhealthy"
		} else if degraded > 0 {
			overallStatus = "degraded"
		}

		data := gin.H{
			"services": statuses,
			"summary": gin.H{
				"total":     len(statuses),
				"healthy":   healthy,
				"degraded":  degraded,
				"unhealthy": unhealthy,
				"overall":   overallStatus,
			},
			"timestamp": time.Now().Format(time.RFC3339),
		}

		c.HTML(http.StatusOK, "dashboard.html", data)
	}
}

// APIHandler handles the API endpoint for service status
func (md *MonitoringDashboard) APIHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		statuses := md.GetAllServiceStatus()
		c.JSON(http.StatusOK, gin.H{
			"services":  statuses,
			"timestamp": time.Now().Format(time.RFC3339),
		})
	}
}

// HealthHandler handles the health check for the dashboard itself
func (md *MonitoringDashboard) HealthHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   "monitoring-dashboard",
			"timestamp": time.Now().Format(time.RFC3339),
		})
	}
}

func main() {
	// Initialize dashboard
	dashboard := NewMonitoringDashboard()

	// Set up Gin router
	gin.SetMode(gin.ReleaseMode)
	router := gin.New()

	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// Load HTML templates
	router.LoadHTMLGlob("templates/*")

	// Serve static files
	router.Static("/static", "./static")

	// Routes
	router.GET("/", dashboard.DashboardHandler())
	router.GET("/health", dashboard.HealthHandler())
	router.GET("/api/status", dashboard.APIHandler())

	// Start server
	srv := &http.Server{
		Addr:    ":9090",
		Handler: router,
	}

	go func() {
		log.Println("Starting monitoring dashboard on :9090")
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down monitoring dashboard...")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Monitoring dashboard exited properly")
}
