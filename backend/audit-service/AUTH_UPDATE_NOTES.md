# Authentication Update Notes for audit-service

## Changes Required:

1. **Import shared auth package:**
   ```go
   import "github.com/claudio/deploy-orchestrator/shared/auth"
   ```

2. **Initialize auth manager in main():**
   ```go
   authManager, err := auth.NewAuthManagerFromEnv()
   if err != nil {
       log.Fatalf("Failed to create auth manager: %v", err)
   }
   ```

3. **Replace middleware usage:**
   ```go
   // Replace old CORS middleware
   router.Use(authManager.CORSMiddleware())
   
   // Replace old auth middleware
   v1.Use(authManager.AuthMiddleware())
   
   // Add permission middleware where needed
   permissionMiddleware := authManager.PermissionMiddleware()
   ```

4. **Update route protection:**
   ```go
   // For admin-only routes
   adminRoutes.Use(authManager.AdminMiddleware())
   
   // For permission-based routes
   routes.Use(permissionMiddleware.RequirePermission("resource:action", auth.ProjectIDFromParam))
   ```

5. **Use context helpers:**
   ```go
   userID, exists := auth.GetUserIDFromContext(c)
   isAdmin := auth.IsAdminFromContext(c)
   ```

## Environment Variables Required:

- JWT_SECRET_KEY: Secret key for JWT signing
- ADMIN_SERVICE_URL: URL of the admin service for permission checks
- ACCESS_TOKEN_EXPIRY: Token expiry duration (optional, defaults to 1h)
- REFRESH_TOKEN_EXPIRY: Refresh token expiry (optional, defaults to 7 days)

## Testing:

After updating, test the following:
1. Health endpoint (should work without auth)
2. Protected endpoints (should require valid JWT)
3. Admin endpoints (should require admin role)
4. Permission-based endpoints (should check specific permissions)

