package storage

import (
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/models"
	"github.com/google/uuid"
)

// InMemoryDatabase provides a simple in-memory implementation for testing
type InMemoryDatabase struct {
	auditLogs map[string]models.AuditLog
	mu        sync.RWMutex // For thread-safety
}

// NewInMemoryDatabase creates a new in-memory database
func NewInMemoryDatabase() *InMemoryDatabase {
	log.Println("Using in-memory database for audit-service")
	return &InMemoryDatabase{
		auditLogs: make(map[string]models.AuditLog),
	}
}

// CreateAuditLog creates a new audit log entry in memory
func (db *InMemoryDatabase) CreateAuditLog(log models.AuditLog) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if log.ID == "" {
		log.ID = uuid.New().String()
	}

	if log.CreatedAt.IsZero() {
		log.CreatedAt = time.Now()
	}

	db.auditLogs[log.ID] = log
	return nil
}

// GetAuditLogs retrieves audit logs with optional filtering from memory
func (db *InMemoryDatabase) GetAuditLogs(userID, action, resource string, limit, offset int) ([]models.AuditLog, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	var results []models.AuditLog

	// Apply filters
	for _, log := range db.auditLogs {
		match := true

		if userID != "" && log.UserID != userID {
			match = false
		}

		if action != "" && log.Action != action {
			match = false
		}

		if resource != "" && log.Resource != resource {
			match = false
		}

		if match {
			results = append(results, log)
		}
	}

	// Sort by created_at in descending order (newest first)
	// A proper implementation would sort here

	// Apply pagination
	totalCount := len(results)
	if offset >= totalCount {
		return []models.AuditLog{}, nil
	}

	end := offset + limit
	if end > totalCount {
		end = totalCount
	}

	if offset < totalCount {
		results = results[offset:end]
	}

	return results, nil
}

// GetAuditLogByID retrieves a specific audit log by ID from memory
func (db *InMemoryDatabase) GetAuditLogByID(id string) (*models.AuditLog, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	log, exists := db.auditLogs[id]
	if !exists {
		return nil, fmt.Errorf("audit log not found")
	}

	return &log, nil
}

// TestConnection for in-memory database always returns nil
func (db *InMemoryDatabase) TestConnection() error {
	return nil
}
