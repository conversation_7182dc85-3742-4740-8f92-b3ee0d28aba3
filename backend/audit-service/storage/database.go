package storage

import (
	"fmt"

	"github.com/claudio/deploy-orchestrator/shared/models"
)

// CreateAuditLog creates a new audit log entry
func (db *Database) CreateAuditLog(log models.AuditLog) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateAuditLog(log)
	}

	result := db.DB.Create(&log)
	return result.Error
}

// GetAuditLogs retrieves audit logs with optional filtering
func (db *Database) GetAuditLogs(userID, action, resource string, limit, offset int) ([]models.AuditLog, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetAuditLogs(userID, action, resource, limit, offset)
	}

	var logs []models.AuditLog
	query := db.DB.Model(&models.AuditLog{})

	// Apply filters if provided
	if userID != "" {
		query = query.Where("user_id = ?", userID)
	}

	if action != "" {
		query = query.Where("action = ?", action)
	}

	if resource != "" {
		query = query.Where("resource = ?", resource)
	}

	// Apply pagination
	query = query.Limit(limit).Offset(offset).Order("created_at DESC")

	// Execute the query
	if err := query.Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve audit logs: %w", err)
	}

	return logs, nil
}

// GetAuditLogByID retrieves a specific audit log by ID
func (db *Database) GetAuditLogByID(id string) (*models.AuditLog, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetAuditLogByID(id)
	}

	var log models.AuditLog
	if err := db.DB.Where("id = ?", id).First(&log).Error; err != nil {
		return nil, fmt.Errorf("audit log not found: %w", err)
	}
	return &log, nil
}

// GetAuditLogsByEntityID retrieves audit logs filtered by entity ID
func (db *Database) GetAuditLogsByEntityID(entityID string, limit, offset int) ([]models.AuditLog, error) {
	// Use entity_id as the resource filter
	return db.GetAuditLogs("", "", entityID, limit, offset)
}

// GetAuditLogsByUserID retrieves audit logs filtered by user ID
func (db *Database) GetAuditLogsByUserID(userID string, limit, offset int) ([]models.AuditLog, error) {
	// Use userID as the user filter
	return db.GetAuditLogs(userID, "", "", limit, offset)
}

// GetAuditLogsByAction retrieves audit logs filtered by action
func (db *Database) GetAuditLogsByAction(action string, limit, offset int) ([]models.AuditLog, error) {
	// Use action as the action filter
	return db.GetAuditLogs("", action, "", limit, offset)
}
