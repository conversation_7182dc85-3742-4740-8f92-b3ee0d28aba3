package storage

import (
	"log"

	"github.com/claudio/deploy-orchestrator/shared/db"
	sharedModels "github.com/claudio/deploy-orchestrator/shared/models"
	"gorm.io/gorm"
)

// Database represents a connection to the database using GORM
type Database struct {
	DB         *gorm.DB
	InMemoryDB *InMemoryDatabase
}

// NewDatabase creates a new database connection using GORM
func NewDatabase(postgresURL string) (*Database, error) {
	config := db.Config{
		URL:           postgresURL,
		MaxRetries:    5,
		RetryInterval: 2,
	}

	gormDB, err := db.Connect(config)
	if err != nil {
		return nil, err
	}

	// Run migrations immediately
	if err := RunMigrations(gormDB); err != nil {
		return nil, err
	}

	return &Database{DB: gormDB}, nil
}

// Close closes the database connection
func (d *Database) Close() error {
	// If using in-memory database, no need to close anything
	if d.InMemoryDB != nil {
		return nil
	}

	// Close the real database connection
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// RunMigrations performs all necessary database migrations
func RunMigrations(grm *gorm.DB) error {
	log.Println("Running audit-service database migrations...")

	// Use the shared RunMigrations function to run migrations for all models
	return db.RunMigrations(grm,
		&sharedModels.AuditLog{},
	)
}

// SeedTestData populates the database with initial test data
// Only for testing purposes
func SeedTestData(db *gorm.DB) error {
	log.Println("Seeding audit database with test data...")

	// Create sample audit log entries
	testLogs := []sharedModels.AuditLog{
		{
			ID:         "audit-test-1",
			UserID:     "user-test-1",
			Action:     "LOGIN",
			Resource:   "AUTH",
			ResourceID: "session-1",
			Details:    "Test login from integration test",
			IPAddress:  "127.0.0.1",
		},
		{
			ID:         "audit-test-2",
			UserID:     "user-test-1",
			Action:     "CREATE",
			Resource:   "DEPLOYMENT",
			ResourceID: "deploy-test-1",
			Details:    "Created test deployment",
			IPAddress:  "127.0.0.1",
		},
	}

	// Add the test logs to the database
	for _, log := range testLogs {
		if result := db.Create(&log); result.Error != nil {
			return result.Error
		}
	}

	return nil
}
