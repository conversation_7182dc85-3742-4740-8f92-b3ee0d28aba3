package config

import (
	"fmt"

	sharedConfig "github.com/claudio/deploy-orchestrator/shared/config"
)

// AuditServiceConfig holds the configuration for the audit service
type AuditServiceConfig struct {
	// Common configuration sections
	Database sharedConfig.DBConfig      `mapstructure:"database" yaml:"database"`
	Server   sharedConfig.ServerConfig  `mapstructure:"server" yaml:"server"`
	Logging  sharedConfig.LoggingConfig `mapstructure:"logging" yaml:"logging"`
	Tracing  sharedConfig.TracingConfig `mapstructure:"tracing" yaml:"tracing"`
	Auth     sharedConfig.AuthConfig    `mapstructure:"auth" yaml:"auth"`
	Gateway  sharedConfig.GatewayConfig `mapstructure:"gateway" yaml:"gateway"`

	// Audit service specific configuration
	RetentionDays     int  `mapstructure:"retention_days" yaml:"retention_days" env:"RETENTION_DAYS" default:"90"`
	EnableCompression bool `mapstructure:"enable_compression" yaml:"enable_compression" env:"ENABLE_COMPRESSION" default:"true"`
	BatchSize         int  `mapstructure:"batch_size" yaml:"batch_size" env:"BATCH_SIZE" default:"100"`
	BatchIntervalSec  int  `mapstructure:"batch_interval_sec" yaml:"batch_interval_sec" env:"BATCH_INTERVAL_SEC" default:"5"`
}

// LoadConfig loads the audit service configuration from file and environment variables
func LoadConfig() (*AuditServiceConfig, error) {
	config := &AuditServiceConfig{
		Server: sharedConfig.ServerConfig{
			Port: 8081, // Default port for audit service
		},
		Tracing: sharedConfig.TracingConfig{
			ServiceName: "audit-service",
		},
	}

	if err := sharedConfig.LoadConfig(config, "audit-service"); err != nil {
		return nil, fmt.Errorf("failed to load audit service config: %w", err)
	}

	return config, nil
}

// SaveDefaultConfig saves a default configuration file if one doesn't exist
func SaveDefaultConfig() error {
	config := &AuditServiceConfig{
		Server: sharedConfig.ServerConfig{
			Port: 8081,
		},
		Tracing: sharedConfig.TracingConfig{
			ServiceName: "audit-service",
		},
	}

	return sharedConfig.SaveDefaultConfig(config, "audit-service")
}
