package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/claudio/deploy-orchestrator/audit-service/storage"
	"github.com/claudio/deploy-orchestrator/shared/models"
)

// AuditHandler handles HTTP requests for audit logs
type AuditHandler struct {
	db *storage.Database
}

// NewAuditHandler creates a new AuditHandler
func NewAuditHandler(db *storage.Database) *AuditHandler {
	return &AuditHandler{db: db}
}

// GetAuditLogs retrieves audit logs with optional filtering
func (h *AuditHandler) GetAuditLogs(c *gin.Context) {
	// Parse query parameters
	entityID := c.Query("entityId")
	userID := c.Query("userId")
	action := c.Query("action")

	// Parse pagination parameters
	limit, _ := strconv.Atoi(c.<PERSON>fault<PERSON>("limit", "100"))
	offset, _ := strconv.Atoi(c<PERSON>("offset", "0"))

	var auditLogs []models.AuditLog
	var err error

	if entityID != "" {
		auditLogs, err = h.db.GetAuditLogsByEntityID(entityID, limit, offset)
	} else if userID != "" {
		auditLogs, err = h.db.GetAuditLogsByUserID(userID, limit, offset)
	} else if action != "" {
		auditLogs, err = h.db.GetAuditLogsByAction(action, limit, offset)
	} else {
		auditLogs, err = h.db.GetAuditLogs("", "", "", limit, offset)
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve audit logs"})
		return
	}

	c.JSON(http.StatusOK, auditLogs)
}

// GetAuditLog retrieves an audit log by ID
func (h *AuditHandler) GetAuditLog(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Audit log ID is required"})
		return
	}

	auditLog, err := h.db.GetAuditLogByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve audit log"})
		return
	}

	if auditLog == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Audit log not found"})
		return
	}

	c.JSON(http.StatusOK, auditLog)
}

// CreateAuditLog creates a new audit log
func (h *AuditHandler) CreateAuditLog(c *gin.Context) {
	var auditLog models.AuditLog
	if err := c.ShouldBindJSON(&auditLog); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid audit log data"})
		return
	}

	// Generate UUID if not provided
	if auditLog.ID == "" {
		auditLog.ID = uuid.New().String()
	}

	if err := h.db.CreateAuditLog(auditLog); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create audit log"})
		return
	}

	c.JSON(http.StatusCreated, auditLog)
}
