-- Migration: Group-to-Project Assignment Model
-- This replaces the role-to-project model with group-to-project assignments
-- for better granular access control

-- Create group_projects table
CREATE TABLE IF NOT EXISTS group_projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    
    -- Ensure unique group-project combinations
    UNIQUE(group_id, project_id)
);

-- Create indexes for performance
CREATE INDEX idx_group_projects_group_id ON group_projects(group_id);
CREATE INDEX idx_group_projects_project_id ON group_projects(project_id);
CREATE INDEX idx_group_projects_created_at ON group_projects(created_at);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_group_projects_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_group_projects_updated_at
    BEFORE UPDATE ON group_projects
    FOR EACH ROW
    EXECUTE FUNCTION update_group_projects_updated_at();

-- Drop old role_projects table and related structures
DROP TABLE IF EXISTS role_projects CASCADE;

-- Drop any related functions or triggers for role_projects
DROP FUNCTION IF EXISTS update_role_projects_updated_at() CASCADE;

-- Create view for easy querying of user project access
CREATE OR REPLACE VIEW user_project_access AS
SELECT DISTINCT
    ug.user_id,
    gp.project_id,
    p.name as project_name,
    g.name as group_name,
    r.name as role_name,
    gp.created_at as access_granted_at
FROM user_groups ug
JOIN group_projects gp ON ug.group_id = gp.group_id
JOIN projects p ON gp.project_id = p.id
JOIN groups g ON ug.group_id = g.id
JOIN group_roles gr ON g.id = gr.group_id
JOIN roles r ON gr.role_id = r.id
WHERE ug.deleted_at IS NULL
  AND g.deleted_at IS NULL
  AND p.deleted_at IS NULL;

-- Create function to get user's accessible projects
CREATE OR REPLACE FUNCTION get_user_projects(user_uuid UUID)
RETURNS TABLE(
    project_id UUID,
    project_name TEXT,
    group_name TEXT,
    role_name TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT
        upa.project_id,
        upa.project_name,
        upa.group_name,
        upa.role_name
    FROM user_project_access upa
    WHERE upa.user_id = user_uuid
    ORDER BY upa.project_name;
END;
$$ LANGUAGE plpgsql;

-- Create function to check if user has access to specific project
CREATE OR REPLACE FUNCTION user_has_project_access(user_uuid UUID, proj_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM user_project_access upa
        WHERE upa.user_id = user_uuid 
          AND upa.project_id = proj_id
    );
END;
$$ LANGUAGE plpgsql;

-- Create function to get projects for a specific group
CREATE OR REPLACE FUNCTION get_group_projects(group_uuid UUID)
RETURNS TABLE(
    project_id UUID,
    project_name TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id as project_id,
        p.name as project_name
    FROM group_projects gp
    JOIN projects p ON gp.project_id = p.id
    WHERE gp.group_id = group_uuid
      AND p.deleted_at IS NULL
    ORDER BY p.name;
END;
$$ LANGUAGE plpgsql;

-- Insert audit log entry for this migration
INSERT INTO audit_logs (
    id,
    user_id,
    action,
    resource_type,
    resource_id,
    details,
    ip_address,
    user_agent,
    created_at
) VALUES (
    gen_random_uuid(),
    NULL, -- System migration
    'MIGRATION',
    'DATABASE',
    'group_projects',
    '{"migration": "006_group_projects", "description": "Implemented Group-to-Project assignment model", "changes": ["created group_projects table", "dropped role_projects table", "created user_project_access view", "created helper functions"]}',
    '127.0.0.1',
    'Database Migration Script',
    NOW()
);

-- Add comments for documentation
COMMENT ON TABLE group_projects IS 'Maps groups to projects for access control. Users inherit project access through their group memberships.';
COMMENT ON COLUMN group_projects.group_id IS 'Reference to the group that has access to the project';
COMMENT ON COLUMN group_projects.project_id IS 'Reference to the project that the group can access';
COMMENT ON VIEW user_project_access IS 'Consolidated view showing which users have access to which projects through their group memberships';
COMMENT ON FUNCTION get_user_projects(UUID) IS 'Returns all projects accessible to a specific user through their group memberships';
COMMENT ON FUNCTION user_has_project_access(UUID, UUID) IS 'Checks if a specific user has access to a specific project';
COMMENT ON FUNCTION get_group_projects(UUID) IS 'Returns all projects accessible to a specific group';
