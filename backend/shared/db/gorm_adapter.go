// Package db provides shared database utilities for the Deploy Orchestrator services
package db

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"gorm.io/gorm"
)

// Common error definitions
var (
	ErrNotFound        = errors.New("record not found")
	ErrDuplicateRecord = errors.New("duplicate record")
	ErrValidation      = errors.New("validation failed")
	ErrTransaction     = errors.New("transaction failed")
	ErrConnection      = errors.New("database connection failed")
)

// GORMAdapter provides a standardized interface for database operations using GORM
type GORMAdapter struct {
	DB             *gorm.DB
	InMemoryMode   bool
	retryAttempts  int
	retryInterval  time.Duration
	queryTimeout   time.Duration
	autoMigrations []interface{}
}

// GORMAdapterOption defines functional options for configuring GORMAdapter
type GORMAdapterOption func(*GORMAdapter)

// WithRetries sets the retry configuration for database operations
func WithRetries(attempts int, interval time.Duration) GORMAdapterOption {
	return func(a *GORMAdapter) {
		a.retryAttempts = attempts
		a.retryInterval = interval
	}
}

// WithQueryTimeout sets timeout for database operations
func WithQueryTimeout(timeout time.Duration) GORMAdapterOption {
	return func(a *GORMAdapter) {
		a.queryTimeout = timeout
	}
}

// WithAutoMigrations sets models for automatic migrations
func WithAutoMigrations(models ...interface{}) GORMAdapterOption {
	return func(a *GORMAdapter) {
		a.autoMigrations = models
	}
}

// NewGORMAdapter creates a new GORM adapter instance
func NewGORMAdapter(config Config, opts ...GORMAdapterOption) (*GORMAdapter, error) {
	// Create adapter with default settings
	adapter := &GORMAdapter{
		retryAttempts:  3,
		retryInterval:  1 * time.Second,
		queryTimeout:   5 * time.Second,
		autoMigrations: []interface{}{},
	}

	// Apply options
	for _, opt := range opts {
		opt(adapter)
	}

	// Connect to the database
	gormDB, err := Connect(config)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrConnection, err)
	}

	adapter.DB = gormDB

	// Run auto migrations if configured
	if len(adapter.autoMigrations) > 0 {
		if err := RunMigrations(gormDB, adapter.autoMigrations...); err != nil {
			return nil, fmt.Errorf("failed to run migrations: %w", err)
		}
	}

	return adapter, nil
}

// Close closes the database connection
func (a *GORMAdapter) Close() error {
	if a.DB != nil {
		sqlDB, err := a.DB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// TestConnection tests the database connection with ping
func (a *GORMAdapter) TestConnection() error {
	sqlDB, err := a.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}

// Create saves a new record to the database
func (a *GORMAdapter) Create(ctx context.Context, model interface{}) error {
	ctx, cancel := context.WithTimeout(ctx, a.queryTimeout)
	defer cancel()

	err := a.DB.WithContext(ctx).Create(model).Error
	if err != nil {
		// Handle duplicate key errors
		if IsDuplicateKeyError(err) {
			return fmt.Errorf("%w: %v", ErrDuplicateRecord, err)
		}
		return err
	}
	return nil
}

// FindByID retrieves a record by its ID
func (a *GORMAdapter) FindByID(ctx context.Context, model interface{}, id string) error {
	ctx, cancel := context.WithTimeout(ctx, a.queryTimeout)
	defer cancel()

	err := a.DB.WithContext(ctx).First(model, "id = ?", id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrNotFound
		}
		return err
	}
	return nil
}

// Update updates an existing record
func (a *GORMAdapter) Update(ctx context.Context, model interface{}) error {
	ctx, cancel := context.WithTimeout(ctx, a.queryTimeout)
	defer cancel()

	err := a.DB.WithContext(ctx).Save(model).Error
	if err != nil {
		return err
	}
	return nil
}

// Delete removes a record by ID
func (a *GORMAdapter) Delete(ctx context.Context, model interface{}, id string) error {
	ctx, cancel := context.WithTimeout(ctx, a.queryTimeout)
	defer cancel()

	err := a.DB.WithContext(ctx).Delete(model, "id = ?", id).Error
	if err != nil {
		return err
	}
	return nil
}

// List retrieves multiple records with optional filtering and pagination
func (a *GORMAdapter) List(ctx context.Context, dest interface{}, filters map[string]interface{}, page, pageSize int, sort string) error {
	ctx, cancel := context.WithTimeout(ctx, a.queryTimeout)
	defer cancel()

	query := a.DB.WithContext(ctx)

	// Apply filters
	for field, value := range filters {
		query = query.Where(field+" = ?", value)
	}

	// Apply sorting if provided
	if sort != "" {
		query = query.Order(sort)
	}

	// Apply pagination if requested
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	err := query.Find(dest).Error
	if err != nil {
		return err
	}
	return nil
}

// Transaction executes operations within a database transaction
func (a *GORMAdapter) Transaction(ctx context.Context, fn func(tx *gorm.DB) error) error {
	ctx, cancel := context.WithTimeout(ctx, a.queryTimeout*3) // Longer timeout for transactions
	defer cancel()

	return a.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		err := fn(tx)
		if err != nil {
			return err
		}
		return nil
	})
}

// Upsert creates or updates a record
func (a *GORMAdapter) Upsert(ctx context.Context, model interface{}, uniqueFields []string) error {
	ctx, cancel := context.WithTimeout(ctx, a.queryTimeout)
	defer cancel()

	// Use a raw SQL approach for upserting
	// This is a simpler alternative that works without the clause package
	err := a.DB.WithContext(ctx).Create(model).Error
	if err != nil && IsDuplicateKeyError(err) {
		// If it's a duplicate key error, perform an update instead
		return a.Update(ctx, model)
	}

	return err
}

// Count returns the number of records matching the filters
func (a *GORMAdapter) Count(ctx context.Context, model interface{}, filters map[string]interface{}) (int64, error) {
	ctx, cancel := context.WithTimeout(ctx, a.queryTimeout)
	defer cancel()

	var count int64
	query := a.DB.WithContext(ctx).Model(model)

	// Apply filters
	for field, value := range filters {
		query = query.Where(field+" = ?", value)
	}

	err := query.Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// ExecuteWithRetry executes a database operation with retry logic
func (a *GORMAdapter) ExecuteWithRetry(operation func() error) error {
	var err error

	for attempt := 1; attempt <= a.retryAttempts; attempt++ {
		err = operation()
		if err == nil {
			return nil
		}

		// Don't retry if this is a client error rather than a transient error
		if !IsRetryableError(err) {
			return err
		}

		if attempt < a.retryAttempts {
			log.Printf("Transient database error, retrying (attempt %d/%d): %v",
				attempt, a.retryAttempts, err)
			time.Sleep(a.retryInterval)
		}
	}

	return fmt.Errorf("operation failed after %d attempts: %w", a.retryAttempts, err)
}

// IsDuplicateKeyError checks if an error is a duplicate key error
func IsDuplicateKeyError(err error) bool {
	// This implementation is PostgreSQL-specific
	// Other databases may require different implementations
	return err != nil && (err.Error() == "ERROR: duplicate key value violates unique constraint" ||
		err.Error() == "pq: duplicate key value violates unique constraint")
}

// IsRetryableError determines if an error should trigger a retry
func IsRetryableError(err error) bool {
	// Check for common transient errors that warrant a retry
	if err == nil {
		return false
	}

	// SQL connection errors
	if errors.Is(err, sql.ErrConnDone) || errors.Is(err, sql.ErrTxDone) {
		return true
	}

	// Check error string for common transient issues
	errMsg := err.Error()
	retryablePrefixes := []string{
		"deadlock",
		"connection reset",
		"connection refused",
		"too many connections",
		"server closed the connection",
		"timeout",
	}

	for _, prefix := range retryablePrefixes {
		if strings.Contains(strings.ToLower(errMsg), prefix) {
			return true
		}
	}

	return false
}
