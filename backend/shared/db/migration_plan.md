# GORM Adapter Migration Plan

## Overview

This document outlines the plan for migrating all services to use the new shared GORM database adapter. This migration will standardize database access across all services, reduce code duplication, and improve maintainability.

## Implementation Steps for Each Service

For each service, follow these steps:

1. **Update Import Paths**:
   ```go
   // OLD
   "github.com/claudio/deploy-orchestrator/shared/db"
   // NEW
   "github.com/claudio/deploy-orchestrator/shared/db"
   ```

2. **Modify Database Struct**:
   ```go
   // OLD
   type Database struct {
     DB *gorm.DB
     InMemoryDB *InMemoryDatabase
   }
   
   // NEW
   type Database struct {
     dbAdapter *db.GORMAdapter
     InMemoryDB *InMemoryDatabase
   }
   ```

3. **Update NewDatabase Function**:
   ```go
   // OLD
   func NewDatabase(postgresURL string) (*Database, error) {
     // Direct connection to database
     config := db.Config{
       URL: postgresURL,
       // ...
     }
     gormDB, err := db.Connect(config)
     // ...
   }
   
   // NEW
   func NewDatabase(postgresURL string) (*Database, error) {
     // Use the shared adapter
     config := db.Config{
       URL: postgresURL,
       // ...
     }
     adapter, err := db.NewGORMAdapter(
       config,
       db.WithRetries(3, 2*time.Second),
       db.WithQueryTimeout(10*time.Second),
       db.WithAutoMigrations(
         // Add service-specific models here
       ),
     )
     // ...
   }
   ```

4. **Modify Database Methods**:
   ```go
   // OLD
   func (db *Database) CreateEntity(ctx context.Context, entity *models.Entity) error {
     if db.InMemoryDB != nil {
       return db.InMemoryDB.CreateEntity(entity)
     }
     
     return db.DB.Create(entity).Error
   }
   
   // NEW
   func (db *Database) CreateEntity(ctx context.Context, entity *models.Entity) error {
     if db.InMemoryDB != nil {
       return db.InMemoryDB.CreateEntity(entity)
     }
     
     return db.dbAdapter.Create(ctx, entity)
   }
   ```

5. **Update Testing Code**:
   - Ensure that tests work with the new adapter
   - Update mock implementations if necessary

## Service-Specific Considerations

### Admin Service
- Include all identity provider models in auto-migrations
- Update user authentication methods to use the new adapter

### Deployment Service
- Ensure deployment status update methods use transaction support
- Update deployment history tracking

### Notification Service
- Update notification batching to use the adapter's batch operations

### Scheduling Service
- Ensure scheduler lock mechanisms work with the new adapter

### Audit Service
- Optimize audit log bulk writing for performance

### Integration Service
- Update webhook handling to use the adapter's transaction support

## Testing Strategy

1. Migrate one service at a time
2. Test thoroughly before moving to the next service
3. Use the service's own test suite to verify functionality
4. Run integration tests to ensure cross-service communication works

## Rollout Plan

1. Deploy the shared/db package with the new adapter
2. Update each service in this order:
   a. admin-service
   b. audit-service
   c. deployment-service
   d. notification-service
   e. scheduling-service
   f. integration-service
3. Update all import paths to use the new organization pattern

## Fallback Strategy

If issues are discovered:
1. Maintain backward compatibility in the shared adapter
2. Roll back individual services if needed
3. Document any service-specific workarounds
