package storage

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/db"
	"github.com/claudio/deploy-orchestrator/shared/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Database represents a connection to the database
type Database struct {
	dbAdapter  *db.GORMAdapter
	InMemoryDB *InMemoryDatabase
}

// NewDatabase creates a new database connection
func NewDatabase(postgresURL string) (*Database, error) {
	// If PostgreSQL URL is not provided, use in-memory database
	if postgresURL == "" {
		log.Println("No PostgreSQL URL provided, using in-memory database")
		inMemoryDB := NewInMemoryDatabase()
		return &Database{
			InMemoryDB: inMemoryDB,
		}, nil
	}

	// Configure database connection
	config := db.Config{
		URL:           postgresURL,
		MaxRetries:    5,
		RetryInterval: 3 * time.Second,
	}

	// Create GORM adapter with options
	adapter, err := db.NewGORMAdapter(
		config,
		db.WithRetries(3, 2*time.Second),
		db.WithQueryTimeout(10*time.Second),
		db.WithAutoMigrations(
			&models.Deployment{},
			&models.DeploymentHistory{},
			&models.Environment{},
			&models.ApplicationConfig{},
		),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create database adapter: %w", err)
	}

	return &Database{
		dbAdapter: adapter,
	}, nil
}

// Close closes the database connection
func (db *Database) Close() error {
	if db.dbAdapter != nil {
		return db.dbAdapter.Close()
	}
	return nil
}

// TestConnection tests the database connection
func (db *Database) TestConnection() error {
	if db.dbAdapter != nil {
		return db.dbAdapter.TestConnection()
	}
	return nil
}

// CreateDeployment creates a new deployment
func (db *Database) CreateDeployment(ctx context.Context, deployment *models.Deployment) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateDeployment(ctx, deployment)
	}

	if deployment.ID == "" {
		deployment.ID = uuid.New().String()
	}

	// Convert steps to JSON if provided
	if len(deployment.Steps) > 0 {
		stepsJSON, err := json.Marshal(deployment.Steps)
		if err != nil {
			return err
		}
		deployment.StepsJSON = string(stepsJSON)
	}

	return db.dbAdapter.Create(ctx, deployment)
}

// GetDeployments retrieves all deployments with optional filtering
func (db *Database) GetDeployments(ctx context.Context, application, environment string, limit, offset int) ([]models.Deployment, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetDeployments(ctx, application, environment, limit, offset)
	}

	filters := make(map[string]interface{})
	
	// Apply filters if provided
	if application != "" {
		filters["application"] = application
	}

	if environment != "" {
		filters["environment"] = environment
	}

	// Calculate page from offset and limit
	page := 1
	if limit > 0 {
		page = (offset / limit) + 1
	}

	var deployments []models.Deployment
	if err := db.dbAdapter.List(ctx, &deployments, filters, page, limit, "created_at DESC"); err != nil {
		return nil, fmt.Errorf("failed to retrieve deployments: %w", err)
	}

	// Process the steps from JSON
	for i := range deployments {
		if deployments[i].StepsJSON != "" {
			if err := json.Unmarshal([]byte(deployments[i].StepsJSON), &deployments[i].Steps); err != nil {
				log.Printf("Error unmarshaling steps for deployment %s: %v", deployments[i].ID, err)
			}
		}
	}

	return deployments, nil
}

// GetDeploymentByID retrieves a deployment by its ID
func (db *Database) GetDeploymentByID(ctx context.Context, id string) (*models.Deployment, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetDeploymentByID(ctx, id)
	}

	var deployment models.Deployment
	if err := db.dbAdapter.FindByID(ctx, &deployment, id); err != nil {
		if errors.Is(err, db.ErrNotFound) {
			return nil, fmt.Errorf("deployment not found with id %s", id)
		}
		return nil, err
	}

	// Parse steps from JSON
	if deployment.StepsJSON != "" {
		if err := json.Unmarshal([]byte(deployment.StepsJSON), &deployment.Steps); err != nil {
			log.Printf("Error unmarshaling steps for deployment %s: %v", deployment.ID, err)
		}
	}

	return &deployment, nil
}

// UpdateDeployment updates an existing deployment
func (db *Database) UpdateDeployment(ctx context.Context, deployment *models.Deployment) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateDeployment(ctx, deployment)
	}

	// Convert steps to JSON if provided
	if len(deployment.Steps) > 0 {
		stepsJSON, err := json.Marshal(deployment.Steps)
		if err != nil {
			return err
		}
		deployment.StepsJSON = string(stepsJSON)
	}

	return db.dbAdapter.Update(ctx, deployment)
}

// UpdateDeploymentStatus updates the status of a deployment using a transaction
func (db *Database) UpdateDeploymentStatus(ctx context.Context, id, status, message string) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateDeploymentStatus(ctx, id, status, message)
	}

	return db.dbAdapter.Transaction(ctx, func(tx *gorm.DB) error {
		// Get the current deployment
		var deployment models.Deployment
		if err := tx.Where("id = ?", id).First(&deployment).Error; err != nil {
			return err
		}

		// Update status and message
		deployment.Status = status
		deployment.StatusMessage = message
		deployment.UpdatedAt = time.Now()

		// Save changes
		if err := tx.Save(&deployment).Error; err != nil {
			return err
		}

		// Create a history entry
		history := models.DeploymentHistory{
			ID:            uuid.New().String(),
			DeploymentID:  id,
			Status:        status,
			StatusMessage: message,
			CreatedAt:     time.Now(),
		}

		if err := tx.Create(&history).Error; err != nil {
			return err
		}

		return nil
	})
}

// CreateEnvironment creates a new environment
func (db *Database) CreateEnvironment(ctx context.Context, env *models.Environment) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateEnvironment(ctx, env)
	}

	if env.ID == "" {
		env.ID = uuid.New().String()
	}

	return db.dbAdapter.Create(ctx, env)
}

// GetEnvironments retrieves all environments
func (db *Database) GetEnvironments(ctx context.Context) ([]models.Environment, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetEnvironments(ctx)
	}

	var environments []models.Environment
	if err := db.dbAdapter.List(ctx, &environments, nil, 0, 0, "name ASC"); err != nil {
		return nil, fmt.Errorf("failed to retrieve environments: %w", err)
	}

	return environments, nil
}

// GetEnvironmentByID retrieves an environment by its ID
func (db *Database) GetEnvironmentByID(ctx context.Context, id string) (*models.Environment, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetEnvironmentByID(ctx, id)
	}

	var environment models.Environment
	if err := db.dbAdapter.FindByID(ctx, &environment, id); err != nil {
		if errors.Is(err, db.ErrNotFound) {
			return nil, fmt.Errorf("environment not found with id %s", id)
		}
		return nil, err
	}

	return &environment, nil
}

// UpdateEnvironment updates an existing environment
func (db *Database) UpdateEnvironment(ctx context.Context, env *models.Environment) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateEnvironment(ctx, env)
	}

	return db.dbAdapter.Update(ctx, env)
}

// DeleteEnvironment deletes an environment
func (db *Database) DeleteEnvironment(ctx context.Context, id string) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.DeleteEnvironment(ctx, id)
	}

	return db.dbAdapter.Delete(ctx, &models.Environment{}, id)
}

// GetDeploymentHistory retrieves the history of a deployment
func (db *Database) GetDeploymentHistory(ctx context.Context, deploymentID string) ([]models.DeploymentHistory, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetDeploymentHistory(ctx, deploymentID)
	}

	var history []models.DeploymentHistory
	filters := map[string]interface{}{"deployment_id": deploymentID}
	
	if err := db.dbAdapter.List(ctx, &history, filters, 0, 0, "created_at ASC"); err != nil {
		return nil, fmt.Errorf("failed to retrieve deployment history: %w", err)
	}

	return history, nil
}
