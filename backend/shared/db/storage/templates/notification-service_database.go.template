package storage

import (
	"context"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/db"
	"github.com/claudio/deploy-orchestrator/shared/models"
	"github.com/google/uuid"
)

// Database represents a connection to the database
type Database struct {
	dbAdapter  *db.GORMAdapter
	InMemoryDB *InMemoryDatabase
}

// NewDatabase creates a new database connection
func NewDatabase(postgresURL string) (*Database, error) {
	// If PostgreSQL URL is not provided, use in-memory database
	if postgresURL == "" {
		log.Println("No PostgreSQL URL provided, using in-memory database")
		inMemoryDB := NewInMemoryDatabase()
		return &Database{
			InMemoryDB: inMemoryDB,
		}, nil
	}

	// Configure database connection
	config := db.Config{
		URL:           postgresURL,
		MaxRetries:    5,
		RetryInterval: 3 * time.Second,
	}

	// Create GORM adapter with options
	adapter, err := db.NewGORMAdapter(
		config,
		db.WithRetries(3, 2*time.Second),
		db.WithQueryTimeout(10*time.Second),
		db.WithAutoMigrations(
			&models.Notification{},
			&models.NotificationConfig{},
			&models.NotificationTemplate{},
		),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create database adapter: %w", err)
	}

	return &Database{
		dbAdapter: adapter,
	}, nil
}

// Close closes the database connection
func (db *Database) Close() error {
	if db.dbAdapter != nil {
		return db.dbAdapter.Close()
	}
	return nil
}

// TestConnection tests the database connection
func (db *Database) TestConnection() error {
	if db.dbAdapter != nil {
		return db.dbAdapter.TestConnection()
	}
	return nil
}

// CreateNotification creates a new notification
func (db *Database) CreateNotification(ctx context.Context, notification *models.Notification) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateNotification(ctx, notification)
	}

	if notification.ID == "" {
		notification.ID = uuid.New().String()
	}

	return db.dbAdapter.Create(ctx, notification)
}

// GetNotifications retrieves all notifications with optional filtering
func (db *Database) GetNotifications(ctx context.Context, recipientFilter, typeFilter, statusFilter string, limit, offset int) ([]models.Notification, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetNotifications(ctx, recipientFilter, typeFilter, statusFilter, limit, offset)
	}

	filters := make(map[string]interface{})
	
	// Apply filters if provided
	if recipientFilter != "" {
		filters["recipient"] = recipientFilter
	}

	if typeFilter != "" {
		filters["type"] = typeFilter
	}

	if statusFilter != "" {
		filters["status"] = statusFilter
	}

	// Calculate page from offset and limit
	page := 1
	if limit > 0 {
		page = (offset / limit) + 1
	}

	var notifications []models.Notification
	if err := db.dbAdapter.List(ctx, &notifications, filters, page, limit, "created_at DESC"); err != nil {
		return nil, fmt.Errorf("failed to retrieve notifications: %w", err)
	}

	return notifications, nil
}

// GetNotificationByID retrieves a notification by its ID
func (db *Database) GetNotificationByID(ctx context.Context, id string) (*models.Notification, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetNotificationByID(ctx, id)
	}

	var notification models.Notification
	if err := db.dbAdapter.FindByID(ctx, &notification, id); err != nil {
		if errors.Is(err, db.ErrNotFound) {
			return nil, fmt.Errorf("notification not found with id %s", id)
		}
		return nil, err
	}

	return &notification, nil
}

// UpdateNotification updates an existing notification
func (db *Database) UpdateNotification(ctx context.Context, notification *models.Notification) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateNotification(ctx, notification)
	}

	return db.dbAdapter.Update(ctx, notification)
}

// UpdateNotificationStatus updates the status of a notification
func (db *Database) UpdateNotificationStatus(ctx context.Context, id, status string) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateNotificationStatus(ctx, id, status)
	}

	notification, err := db.GetNotificationByID(ctx, id)
	if err != nil {
		return err
	}

	notification.Status = status
	notification.UpdatedAt = time.Now()

	return db.dbAdapter.Update(ctx, notification)
}

// CreateNotificationTemplate creates a new notification template
func (db *Database) CreateNotificationTemplate(ctx context.Context, template *models.NotificationTemplate) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateNotificationTemplate(ctx, template)
	}

	if template.ID == "" {
		template.ID = uuid.New().String()
	}

	return db.dbAdapter.Create(ctx, template)
}

// GetNotificationTemplates retrieves all notification templates
func (db *Database) GetNotificationTemplates(ctx context.Context) ([]models.NotificationTemplate, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetNotificationTemplates(ctx)
	}

	var templates []models.NotificationTemplate
	if err := db.dbAdapter.List(ctx, &templates, nil, 0, 0, "name ASC"); err != nil {
		return nil, fmt.Errorf("failed to retrieve notification templates: %w", err)
	}

	return templates, nil
}

// GetNotificationTemplateByID retrieves a notification template by its ID
func (db *Database) GetNotificationTemplateByID(ctx context.Context, id string) (*models.NotificationTemplate, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetNotificationTemplateByID(ctx, id)
	}

	var template models.NotificationTemplate
	if err := db.dbAdapter.FindByID(ctx, &template, id); err != nil {
		if errors.Is(err, db.ErrNotFound) {
			return nil, fmt.Errorf("notification template not found with id %s", id)
		}
		return nil, err
	}

	return &template, nil
}

// GetNotificationTemplateByType retrieves a notification template by its type
func (db *Database) GetNotificationTemplateByType(ctx context.Context, templateType string) (*models.NotificationTemplate, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetNotificationTemplateByType(ctx, templateType)
	}

	filters := map[string]interface{}{"type": templateType}
	
	var templates []models.NotificationTemplate
	if err := db.dbAdapter.List(ctx, &templates, filters, 1, 1, ""); err != nil {
		return nil, err
	}
	
	if len(templates) == 0 {
		return nil, fmt.Errorf("notification template not found with type %s", templateType)
	}
	
	return &templates[0], nil
}

// UpdateNotificationTemplate updates an existing notification template
func (db *Database) UpdateNotificationTemplate(ctx context.Context, template *models.NotificationTemplate) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateNotificationTemplate(ctx, template)
	}

	return db.dbAdapter.Update(ctx, template)
}

// DeleteNotificationTemplate deletes a notification template
func (db *Database) DeleteNotificationTemplate(ctx context.Context, id string) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.DeleteNotificationTemplate(ctx, id)
	}

	return db.dbAdapter.Delete(ctx, &models.NotificationTemplate{}, id)
}

// BatchProcessNotifications processes multiple notifications in a batch for better performance
func (db *Database) BatchProcessNotifications(ctx context.Context, notifications []models.Notification) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		for _, notification := range notifications {
			if err := db.InMemoryDB.CreateNotification(ctx, &notification); err != nil {
				return err
			}
		}
		return nil
	}

	// Use a transaction for batch processing
	return db.dbAdapter.Transaction(ctx, func(tx *gorm.DB) error {
		for i := range notifications {
			if notifications[i].ID == "" {
				notifications[i].ID = uuid.New().String()
			}
			if err := tx.Create(&notifications[i]).Error; err != nil {
				return err
			}
		}
		return nil
	})
}
