package storage

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/db"
	"github.com/claudio/deploy-orchestrator/shared/models"
)

// Database represents a connection to the database
type Database struct {
	dbAdapter  *db.GORMAdapter
	InMemoryDB *InMemoryDatabase
}

// NewDatabase creates a new database connection
func NewDatabase(postgresURL string) (*Database, error) {
	// If PostgreSQL URL is not provided, use in-memory database
	if postgresURL == "" {
		log.Println("No PostgreSQL URL provided, using in-memory database")
		inMemoryDB := NewInMemoryDatabase()
		return &Database{
			InMemoryDB: inMemoryDB,
		}, nil
	}

	// Configure database connection
	config := db.Config{
		URL:           postgresURL,
		MaxRetries:    5,
		RetryInterval: 3 * time.Second,
	}

	// Create GORM adapter with options
	adapter, err := db.NewGORMAdapter(
		config,
		db.WithRetries(3, 2*time.Second),
		db.WithQueryTimeout(10*time.Second),
		db.WithAutoMigrations(
			&models.AuditLog{},
		),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create database adapter: %w", err)
	}

	return &Database{
		dbAdapter: adapter,
	}, nil
}

// Close closes the database connection
func (db *Database) Close() error {
	if db.dbAdapter != nil {
		return db.dbAdapter.Close()
	}
	return nil
}

// TestConnection tests the database connection
func (db *Database) TestConnection() error {
	if db.dbAdapter != nil {
		return db.dbAdapter.TestConnection()
	}
	return nil
}

// CreateAuditLog creates a new audit log entry
func (db *Database) CreateAuditLog(log models.AuditLog) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateAuditLog(log)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return db.dbAdapter.Create(ctx, &log)
}

// GetAuditLogs retrieves audit logs with optional filtering
func (db *Database) GetAuditLogs(userID, action, resource string, limit, offset int) ([]models.AuditLog, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetAuditLogs(userID, action, resource, limit, offset)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	filters := make(map[string]interface{})
	
	// Apply filters if provided
	if userID != "" {
		filters["user_id"] = userID
	}

	if action != "" {
		filters["action"] = action
	}

	if resource != "" {
		filters["resource"] = resource
	}

	// Determine page from offset and limit
	page := 1
	if limit > 0 {
		page = (offset / limit) + 1
	}

	var logs []models.AuditLog
	if err := db.dbAdapter.List(ctx, &logs, filters, page, limit, "created_at DESC"); err != nil {
		return nil, fmt.Errorf("failed to retrieve audit logs: %w", err)
	}

	return logs, nil
}

// GetAuditLogByID retrieves a specific audit log by ID
func (db *Database) GetAuditLogByID(id string) (*models.AuditLog, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetAuditLogByID(id)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var log models.AuditLog
	if err := db.dbAdapter.FindByID(ctx, &log, id); err != nil {
		return nil, fmt.Errorf("audit log not found: %w", err)
	}
	
	return &log, nil
}

// GetAuditLogsByEntityID retrieves audit logs filtered by entity ID
func (db *Database) GetAuditLogsByEntityID(entityID string, limit, offset int) ([]models.AuditLog, error) {
	// Use entity_id as the resource filter
	return db.GetAuditLogs("", "", entityID, limit, offset)
}

// GetAuditLogsByUserID retrieves audit logs filtered by user ID
func (db *Database) GetAuditLogsByUserID(userID string, limit, offset int) ([]models.AuditLog, error) {
	// Use userID as the user filter
	return db.GetAuditLogs(userID, "", "", limit, offset)
}

// GetAuditLogsByAction retrieves audit logs filtered by action
func (db *Database) GetAuditLogsByAction(action string, limit, offset int) ([]models.AuditLog, error) {
	// Use action as the action filter
	return db.GetAuditLogs("", action, "", limit, offset)
}
