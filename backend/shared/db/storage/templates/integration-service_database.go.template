package storage

import (
	"context"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/db"
	"github.com/claudio/deploy-orchestrator/shared/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Database represents a connection to the database
type Database struct {
	dbAdapter  *db.GORMAdapter
	InMemoryDB *InMemoryDatabase
}

// NewDatabase creates a new database connection
func NewDatabase(postgresURL string) (*Database, error) {
	// If PostgreSQL URL is not provided, use in-memory database
	if postgresURL == "" {
		log.Println("No PostgreSQL URL provided, using in-memory database")
		inMemoryDB := NewInMemoryDatabase()
		return &Database{
			InMemoryDB: inMemoryDB,
		}, nil
	}

	// Configure database connection
	config := db.Config{
		URL:           postgresURL,
		MaxRetries:    5,
		RetryInterval: 3 * time.Second,
	}

	// Create GORM adapter with options
	adapter, err := db.NewGORMAdapter(
		config,
		db.WithRetries(3, 2*time.Second),
		db.WithQueryTimeout(10*time.Second),
		db.WithAutoMigrations(
			&models.Integration{},
			&models.Webhook{},
			&models.WebhookLog{},
			&models.IntegrationConfig{},
		),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create database adapter: %w", err)
	}

	return &Database{
		dbAdapter: adapter,
	}, nil
}

// Close closes the database connection
func (db *Database) Close() error {
	if db.dbAdapter != nil {
		return db.dbAdapter.Close()
	}
	return nil
}

// TestConnection tests the database connection
func (db *Database) TestConnection() error {
	if db.dbAdapter != nil {
		return db.dbAdapter.TestConnection()
	}
	return nil
}

// GetIntegration retrieves an integration by ID
func (db *Database) GetIntegration(ctx context.Context, id string) (*models.Integration, error) {
	// Check if we're using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetIntegration(ctx, id)
	}

	var integration models.Integration
	if err := db.dbAdapter.FindByID(ctx, &integration, id); err != nil {
		if errors.Is(err, db.ErrNotFound) {
			return nil, nil // Return nil, nil when not found to be consistent with other services
		}
		return nil, err
	}
	return &integration, nil
}

// ListIntegrations retrieves all integrations with optional filtering
func (db *Database) ListIntegrations(ctx context.Context, filter map[string]interface{}) ([]models.Integration, error) {
	// Check if we're using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.ListIntegrations(ctx, filter)
	}

	var integrations []models.Integration
	if err := db.dbAdapter.List(ctx, &integrations, filter, 0, 0, "name ASC"); err != nil {
		return nil, fmt.Errorf("failed to list integrations: %w", err)
	}
	
	return integrations, nil
}

// CreateIntegration creates a new integration
func (db *Database) CreateIntegration(ctx context.Context, integration *models.Integration) error {
	// Check if we're using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateIntegration(ctx, integration)
	}

	if integration.ID == "" {
		integration.ID = uuid.New().String()
	}

	return db.dbAdapter.Create(ctx, integration)
}

// UpdateIntegration updates an existing integration
func (db *Database) UpdateIntegration(ctx context.Context, integration *models.Integration) error {
	// Check if we're using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateIntegration(ctx, integration)
	}

	return db.dbAdapter.Update(ctx, integration)
}

// DeleteIntegration deletes an integration
func (db *Database) DeleteIntegration(ctx context.Context, id string) error {
	// Check if we're using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.DeleteIntegration(ctx, id)
	}

	return db.dbAdapter.Delete(ctx, &models.Integration{}, id)
}

// CreateWebhook creates a new webhook
func (db *Database) CreateWebhook(ctx context.Context, webhook *models.Webhook) error {
	// Check if we're using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateWebhook(ctx, webhook)
	}

	if webhook.ID == "" {
		webhook.ID = uuid.New().String()
	}

	return db.dbAdapter.Create(ctx, webhook)
}

// GetWebhook retrieves a webhook by ID
func (db *Database) GetWebhook(ctx context.Context, id string) (*models.Webhook, error) {
	// Check if we're using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetWebhook(ctx, id)
	}

	var webhook models.Webhook
	if err := db.dbAdapter.FindByID(ctx, &webhook, id); err != nil {
		if errors.Is(err, db.ErrNotFound) {
			return nil, fmt.Errorf("webhook not found with id %s", id)
		}
		return nil, err
	}
	
	return &webhook, nil
}

// ListWebhooks lists all webhooks with optional filtering
func (db *Database) ListWebhooks(ctx context.Context, filter map[string]interface{}, limit, offset int) ([]models.Webhook, error) {
	// Check if we're using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.ListWebhooks(ctx, filter, limit, offset)
	}

	// Calculate page from offset and limit
	page := 1
	if limit > 0 {
		page = (offset / limit) + 1
	}

	var webhooks []models.Webhook
	if err := db.dbAdapter.List(ctx, &webhooks, filter, page, limit, "created_at DESC"); err != nil {
		return nil, fmt.Errorf("failed to list webhooks: %w", err)
	}
	
	return webhooks, nil
}

// UpdateWebhook updates a webhook
func (db *Database) UpdateWebhook(ctx context.Context, webhook *models.Webhook) error {
	// Check if we're using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateWebhook(ctx, webhook)
	}

	return db.dbAdapter.Update(ctx, webhook)
}

// DeleteWebhook deletes a webhook
func (db *Database) DeleteWebhook(ctx context.Context, id string) error {
	// Check if we're using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.DeleteWebhook(ctx, id)
	}

	return db.dbAdapter.Delete(ctx, &models.Webhook{}, id)
}

// LogWebhookExecution creates a log entry for a webhook execution
func (db *Database) LogWebhookExecution(ctx context.Context, log *models.WebhookLog) error {
	// Check if we're using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.LogWebhookExecution(ctx, log)
	}

	if log.ID == "" {
		log.ID = uuid.New().String()
	}

	return db.dbAdapter.Create(ctx, log)
}

// GetWebhookLogs retrieves logs for a webhook
func (db *Database) GetWebhookLogs(ctx context.Context, webhookID string, limit, offset int) ([]models.WebhookLog, error) {
	// Check if we're using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetWebhookLogs(ctx, webhookID, limit, offset)
	}

	filters := map[string]interface{}{"webhook_id": webhookID}
	
	// Calculate page from offset and limit
	page := 1
	if limit > 0 {
		page = (offset / limit) + 1
	}

	var logs []models.WebhookLog
	if err := db.dbAdapter.List(ctx, &logs, filters, page, limit, "created_at DESC"); err != nil {
		return nil, fmt.Errorf("failed to get webhook logs: %w", err)
	}
	
	return logs, nil
}

// ProcessWebhookWithTransaction processes a webhook within a transaction
func (db *Database) ProcessWebhookWithTransaction(ctx context.Context, webhookID string, processFunc func(context.Context, *gorm.DB) error) error {
	// Check if we're using in-memory database
	if db.InMemoryDB != nil {
		return processFunc(ctx, nil)
	}

	return db.dbAdapter.Transaction(ctx, func(tx *gorm.DB) error {
		// Get the webhook with a lock
		var webhook models.Webhook
		if err := tx.Where("id = ?", webhookID).First(&webhook).Error; err != nil {
			return err
		}

		// Mark webhook as processing
		webhook.Status = "processing"
		webhook.UpdatedAt = time.Now()
		if err := tx.Save(&webhook).Error; err != nil {
			return err
		}

		// Process the webhook using the provided function
		if err := processFunc(ctx, tx); err != nil {
			// Mark webhook as failed
			webhook.Status = "failed"
			webhook.StatusMessage = err.Error()
			webhook.UpdatedAt = time.Now()
			tx.Save(&webhook)
			return err
		}

		// Mark webhook as succeeded
		webhook.Status = "completed"
		webhook.UpdatedAt = time.Now()
		return tx.Save(&webhook).Error
	})
}
