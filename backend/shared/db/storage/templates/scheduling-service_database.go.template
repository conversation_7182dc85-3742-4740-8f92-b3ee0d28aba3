package storage

import (
	"context"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/db"
	"github.com/claudio/deploy-orchestrator/shared/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Database represents a connection to the database
type Database struct {
	dbAdapter  *db.GORMAdapter
	InMemoryDB *InMemoryDatabase
}

// NewDatabase creates a new database connection
func NewDatabase(postgresURL string) (*Database, error) {
	// If PostgreSQL URL is not provided, use in-memory database
	if postgresURL == "" {
		log.Println("No PostgreSQL URL provided, using in-memory database")
		inMemoryDB := NewInMemoryDatabase()
		return &Database{
			InMemoryDB: inMemoryDB,
		}, nil
	}

	// Configure database connection
	config := db.Config{
		URL:           postgresURL,
		MaxRetries:    5,
		RetryInterval: 3 * time.Second,
	}

	// Create GORM adapter with options
	adapter, err := db.NewGORMAdapter(
		config,
		db.WithRetries(3, 2*time.Second),
		db.WithQueryTimeout(10*time.Second),
		db.WithAutoMigrations(
			&models.Schedule{},
			&models.ScheduleExecution{},
			&models.MaintenanceWindow{},
		),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create database adapter: %w", err)
	}

	return &Database{
		dbAdapter: adapter,
	}, nil
}

// Close closes the database connection
func (db *Database) Close() error {
	if db.dbAdapter != nil {
		return db.dbAdapter.Close()
	}
	return nil
}

// TestConnection tests the database connection
func (db *Database) TestConnection() error {
	if db.dbAdapter != nil {
		return db.dbAdapter.TestConnection()
	}
	return nil
}

// CreateSchedule creates a new schedule
func (db *Database) CreateSchedule(ctx context.Context, schedule *models.Schedule) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateSchedule(ctx, schedule)
	}

	if schedule.ID == "" {
		schedule.ID = uuid.New().String()
	}

	return db.dbAdapter.Create(ctx, schedule)
}

// GetSchedules retrieves all schedules
func (db *Database) GetSchedules(ctx context.Context) ([]models.Schedule, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetSchedules(ctx)
	}

	var schedules []models.Schedule
	if err := db.dbAdapter.List(ctx, &schedules, nil, 0, 0, "next_run_at ASC"); err != nil {
		return nil, fmt.Errorf("failed to retrieve schedules: %w", err)
	}

	return schedules, nil
}

// GetActiveSchedules retrieves all active schedules
func (db *Database) GetActiveSchedules(ctx context.Context) ([]models.Schedule, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetActiveSchedules(ctx)
	}

	filters := map[string]interface{}{"active": true}
	
	var schedules []models.Schedule
	if err := db.dbAdapter.List(ctx, &schedules, filters, 0, 0, "next_run_at ASC"); err != nil {
		return nil, fmt.Errorf("failed to retrieve active schedules: %w", err)
	}

	return schedules, nil
}

// GetScheduleByID retrieves a schedule by its ID
func (db *Database) GetScheduleByID(ctx context.Context, id string) (*models.Schedule, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetScheduleByID(ctx, id)
	}

	var schedule models.Schedule
	if err := db.dbAdapter.FindByID(ctx, &schedule, id); err != nil {
		if errors.Is(err, db.ErrNotFound) {
			return nil, fmt.Errorf("schedule not found with id %s", id)
		}
		return nil, err
	}

	return &schedule, nil
}

// UpdateSchedule updates an existing schedule
func (db *Database) UpdateSchedule(ctx context.Context, schedule *models.Schedule) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateSchedule(ctx, schedule)
	}

	return db.dbAdapter.Update(ctx, schedule)
}

// DeleteSchedule deletes a schedule
func (db *Database) DeleteSchedule(ctx context.Context, id string) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.DeleteSchedule(ctx, id)
	}

	return db.dbAdapter.Delete(ctx, &models.Schedule{}, id)
}

// UpdateNextRunAt updates the next run time for a schedule
func (db *Database) UpdateNextRunAt(ctx context.Context, id string, nextRunAt time.Time) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateNextRunAt(ctx, id, nextRunAt)
	}

	schedule, err := db.GetScheduleByID(ctx, id)
	if err != nil {
		return err
	}

	schedule.NextRunAt = nextRunAt
	schedule.UpdatedAt = time.Now()

	return db.dbAdapter.Update(ctx, schedule)
}

// AcquireScheduleLock attempts to acquire a lock on a schedule to prevent concurrent processing
func (db *Database) AcquireScheduleLock(ctx context.Context, id string) (bool, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.AcquireScheduleLock(ctx, id)
	}

	var success bool
	err := db.dbAdapter.Transaction(ctx, func(tx *gorm.DB) error {
		var schedule models.Schedule
		if err := tx.Where("id = ? AND locked = ?", id, false).First(&schedule).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				success = false
				return nil
			}
			return err
		}

		// Update the lock status
		schedule.Locked = true
		schedule.LockedAt = time.Now()
		if err := tx.Save(&schedule).Error; err != nil {
			return err
		}

		success = true
		return nil
	})

	return success, err
}

// ReleaseScheduleLock releases a lock on a schedule
func (db *Database) ReleaseScheduleLock(ctx context.Context, id string) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.ReleaseScheduleLock(ctx, id)
	}

	schedule, err := db.GetScheduleByID(ctx, id)
	if err != nil {
		return err
	}

	schedule.Locked = false
	schedule.LockedAt = time.Time{}
	schedule.UpdatedAt = time.Now()

	return db.dbAdapter.Update(ctx, schedule)
}

// CreateScheduleExecution creates a record of a schedule execution
func (db *Database) CreateScheduleExecution(ctx context.Context, execution *models.ScheduleExecution) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateScheduleExecution(ctx, execution)
	}

	if execution.ID == "" {
		execution.ID = uuid.New().String()
	}

	return db.dbAdapter.Create(ctx, execution)
}

// GetScheduleExecutionsByScheduleID retrieves execution history for a schedule
func (db *Database) GetScheduleExecutionsByScheduleID(ctx context.Context, scheduleID string, limit, offset int) ([]models.ScheduleExecution, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetScheduleExecutionsByScheduleID(ctx, scheduleID, limit, offset)
	}

	filters := map[string]interface{}{"schedule_id": scheduleID}
	
	// Calculate page from offset and limit
	page := 1
	if limit > 0 {
		page = (offset / limit) + 1
	}

	var executions []models.ScheduleExecution
	if err := db.dbAdapter.List(ctx, &executions, filters, page, limit, "created_at DESC"); err != nil {
		return nil, fmt.Errorf("failed to retrieve schedule executions: %w", err)
	}

	return executions, nil
}

// CreateMaintenanceWindow creates a new maintenance window
func (db *Database) CreateMaintenanceWindow(ctx context.Context, window *models.MaintenanceWindow) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateMaintenanceWindow(ctx, window)
	}

	if window.ID == "" {
		window.ID = uuid.New().String()
	}

	return db.dbAdapter.Create(ctx, window)
}

// GetActiveMaintenanceWindows retrieves active maintenance windows
func (db *Database) GetActiveMaintenanceWindows(ctx context.Context) ([]models.MaintenanceWindow, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetActiveMaintenanceWindows(ctx)
	}

	now := time.Now()
	
	var windows []models.MaintenanceWindow
	if err := db.dbAdapter.Transaction(ctx, func(tx *gorm.DB) error {
		if err := tx.Where("start_time <= ? AND end_time >= ?", now, now).Find(&windows).Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		return nil, fmt.Errorf("failed to retrieve active maintenance windows: %w", err)
	}

	return windows, nil
}
