package storage

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/claudio/deploy-orchestrator/admin-service/models"
	"github.com/claudio/deploy-orchestrator/shared/db"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// Database represents a connection to the database
type Database struct {
	dbAdapter  *db.GORMAdapter
	InMemoryDB *InMemoryDatabase
}

// NewDatabase creates a new database connection
func NewDatabase(postgresURL string) (*Database, error) {
	// If PostgreSQL URL is not provided, use in-memory database
	if postgresURL == "" {
		log.Println("No PostgreSQL URL provided, using in-memory database")
		inMemoryDB := NewInMemoryDatabase()
		return &Database{
			InMemoryDB: inMemoryDB,
		}, nil
	}

	// Configure database connection
	config := db.Config{
		URL:           postgresURL,
		MaxRetries:    5,
		RetryInterval: 3 * time.Second,
	}

	// Create GORM adapter with options
	adapter, err := db.NewGORMAdapter(
		config,
		db.WithRetries(3, 2*time.Second),
		db.WithQueryTimeout(10*time.Second),
		db.WithAutoMigrations(
			&models.User{},
			&models.Role{},
			&models.Project{},
			&models.UserProjectRole{},
			&models.Permission{},
			&models.RolePermission{},
			&models.EnvironmentPermission{},
			&models.SystemSetting{},
			&models.LDAPConfig{},
			&models.SAMLConfig{},
			&models.OIDCConfig{},
			&models.APIKey{},
		),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create database adapter: %w", err)
	}

	// Initialize with admin user if needed
	if err := initializeAdminUser(adapter.DB); err != nil {
		log.Printf("Error initializing admin user: %v", err)
	}

	return &Database{
		dbAdapter: adapter,
	}, nil
}

// Close closes the database connection
func (db *Database) Close() error {
	if db.dbAdapter != nil {
		return db.dbAdapter.Close()
	}
	return nil
}

// TestConnection tests the database connection
func (db *Database) TestConnection() error {
	if db.dbAdapter != nil {
		return db.dbAdapter.TestConnection()
	}
	return nil
}

// initializeAdminUser creates an admin user if one doesn't exist
func initializeAdminUser(db *gorm.DB) error {
	// Check if admin user exists
	var count int64
	db.Model(&models.User{}).Count(&count)

	if count == 0 {
		// Create default admin user
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte("admin"), bcrypt.DefaultCost)
		if err != nil {
			return fmt.Errorf("failed to hash password: %v", err)
		}

		adminUser := models.User{
			ID:             uuid.New().String(),
			Username:       "admin",
			Email:          "<EMAIL>",
			FirstName:      "Admin",
			LastName:       "User",
			HashedPassword: string(hashedPassword),
			Active:         true,
		}

		if err := db.Create(&adminUser).Error; err != nil {
			return fmt.Errorf("failed to create admin user: %v", err)
		}

		log.Println("Created default admin user")
	}

	return nil
}

// ===== User Management =====

// CreateUser creates a new user
func (db *Database) CreateUser(ctx context.Context, user *models.User) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateUser(user)
	}

	if user.ID == "" {
		user.ID = uuid.New().String()
	}

	return db.dbAdapter.Create(ctx, user)
}

// GetUserByID gets a user by ID
func (db *Database) GetUserByID(ctx context.Context, id string) (*models.User, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetUserByID(id)
	}

	user := &models.User{}
	if err := db.dbAdapter.FindByID(ctx, user, id); err != nil {
		if errors.Is(err, db.ErrNotFound) {
			return nil, fmt.Errorf("user not found with id %s", id)
		}
		return nil, err
	}
	return user, nil
}

// GetUserByUsername gets a user by username
func (db *Database) GetUserByUsername(ctx context.Context, username string) (*models.User, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetUserByUsername(username)
	}

	var user models.User
	filters := map[string]interface{}{"username": username}
	
	var users []*models.User
	if err := db.dbAdapter.List(ctx, &users, filters, 1, 1, ""); err != nil {
		return nil, err
	}
	
	if len(users) == 0 {
		return nil, fmt.Errorf("user not found with username %s", username)
	}
	
	return users[0], nil
}

// UpdateUser updates a user
func (db *Database) UpdateUser(ctx context.Context, user *models.User) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateUser(user)
	}

	return db.dbAdapter.Update(ctx, user)
}

// DeleteUser deletes a user
func (db *Database) DeleteUser(ctx context.Context, id string) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.DeleteUser(id)
	}

	return db.dbAdapter.Delete(ctx, &models.User{}, id)
}

// ListUsers lists all users with pagination
func (db *Database) ListUsers(ctx context.Context, page, pageSize int) ([]*models.User, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.ListUsers(page, pageSize)
	}

	var users []*models.User
	if err := db.dbAdapter.List(ctx, &users, nil, page, pageSize, "created_at desc"); err != nil {
		return nil, err
	}
	
	return users, nil
}

// ===== Role Management =====

// CreateRole creates a new role
func (db *Database) CreateRole(ctx context.Context, role *models.Role) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateRole(role)
	}

	if role.ID == "" {
		role.ID = uuid.New().String()
	}

	return db.dbAdapter.Create(ctx, role)
}

// GetRoleByID gets a role by ID
func (db *Database) GetRoleByID(ctx context.Context, id string) (*models.Role, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetRoleByID(id)
	}

	role := &models.Role{}
	if err := db.dbAdapter.FindByID(ctx, role, id); err != nil {
		if errors.Is(err, db.ErrNotFound) {
			return nil, fmt.Errorf("role not found with id %s", id)
		}
		return nil, err
	}
	
	return role, nil
}

// UpdateRole updates a role
func (db *Database) UpdateRole(ctx context.Context, role *models.Role) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateRole(role)
	}

	return db.dbAdapter.Update(ctx, role)
}

// DeleteRole deletes a role
func (db *Database) DeleteRole(ctx context.Context, id string) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.DeleteRole(id)
	}

	return db.dbAdapter.Delete(ctx, &models.Role{}, id)
}

// ListRoles lists all roles with pagination
func (db *Database) ListRoles(ctx context.Context, page, pageSize int) ([]*models.Role, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.ListRoles(page, pageSize)
	}

	var roles []*models.Role
	if err := db.dbAdapter.List(ctx, &roles, nil, page, pageSize, "name asc"); err != nil {
		return nil, err
	}
	
	return roles, nil
}

// ===== Permission Management =====

// CreatePermission creates a new permission
func (db *Database) CreatePermission(ctx context.Context, permission *models.Permission) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreatePermission(permission)
	}

	if permission.ID == "" {
		permission.ID = uuid.New().String()
	}

	return db.dbAdapter.Create(ctx, permission)
}

// GetPermissionByID gets a permission by ID
func (db *Database) GetPermissionByID(ctx context.Context, id string) (*models.Permission, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetPermissionByID(id)
	}

	permission := &models.Permission{}
	if err := db.dbAdapter.FindByID(ctx, permission, id); err != nil {
		if errors.Is(err, db.ErrNotFound) {
			return nil, fmt.Errorf("permission not found with id %s", id)
		}
		return nil, err
	}
	
	return permission, nil
}

// ListPermissions lists all permissions
func (db *Database) ListPermissions(ctx context.Context) ([]*models.Permission, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.ListPermissions()
	}

	var permissions []*models.Permission
	if err := db.dbAdapter.List(ctx, &permissions, nil, 0, 0, "resource asc, action asc"); err != nil {
		return nil, err
	}
	
	return permissions, nil
}

// ===== Project Management =====

// CreateProject creates a new project
func (db *Database) CreateProject(ctx context.Context, project *models.Project) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateProject(project)
	}

	if project.ID == "" {
		project.ID = uuid.New().String()
	}

	return db.dbAdapter.Create(ctx, project)
}

// GetProjectByID gets a project by ID
func (db *Database) GetProjectByID(ctx context.Context, id string) (*models.Project, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetProjectByID(id)
	}

	project := &models.Project{}
	if err := db.dbAdapter.FindByID(ctx, project, id); err != nil {
		if errors.Is(err, db.ErrNotFound) {
			return nil, fmt.Errorf("project not found with id %s", id)
		}
		return nil, err
	}
	
	return project, nil
}

// UpdateProject updates a project
func (db *Database) UpdateProject(ctx context.Context, project *models.Project) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateProject(project)
	}

	return db.dbAdapter.Update(ctx, project)
}

// DeleteProject deletes a project
func (db *Database) DeleteProject(ctx context.Context, id string) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.DeleteProject(id)
	}

	return db.dbAdapter.Delete(ctx, &models.Project{}, id)
}

// ListProjects lists all projects with pagination
func (db *Database) ListProjects(ctx context.Context, page, pageSize int) ([]*models.Project, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.ListProjects(page, pageSize)
	}

	var projects []*models.Project
	if err := db.dbAdapter.List(ctx, &projects, nil, page, pageSize, "name asc"); err != nil {
		return nil, err
	}
	
	return projects, nil
}

// ===== Identity Provider Management =====

// CreateLDAPConfig creates a new LDAP configuration
func (db *Database) CreateLDAPConfig(ctx context.Context, config *models.LDAPConfig) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateLDAPConfig(config)
	}

	if config.ID == "" {
		config.ID = uuid.New().String()
	}

	return db.dbAdapter.Create(ctx, config)
}

// GetLDAPConfigByID gets an LDAP configuration by ID
func (db *Database) GetLDAPConfigByID(ctx context.Context, id string) (*models.LDAPConfig, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetLDAPConfigByID(id)
	}

	config := &models.LDAPConfig{}
	if err := db.dbAdapter.FindByID(ctx, config, id); err != nil {
		if errors.Is(err, db.ErrNotFound) {
			return nil, fmt.Errorf("LDAP config not found with id %s", id)
		}
		return nil, err
	}
	
	return config, nil
}

// ListLDAPConfigs lists all LDAP configurations
func (db *Database) ListLDAPConfigs(ctx context.Context) ([]*models.LDAPConfig, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.ListLDAPConfigs()
	}

	var configs []*models.LDAPConfig
	if err := db.dbAdapter.List(ctx, &configs, nil, 0, 0, "name asc"); err != nil {
		return nil, err
	}
	
	return configs, nil
}

// Similar methods would be implemented for SAMLConfig and OIDCConfig...

// ===== API Key Management =====

// CreateAPIKey creates a new API key
func (db *Database) CreateAPIKey(ctx context.Context, apiKey *models.APIKey) error {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateAPIKey(apiKey)
	}

	if apiKey.ID == "" {
		apiKey.ID = uuid.New().String()
	}

	return db.dbAdapter.Create(ctx, apiKey)
}

// GetAPIKeyByID gets an API key by ID
func (db *Database) GetAPIKeyByID(ctx context.Context, id string) (*models.APIKey, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetAPIKeyByID(id)
	}

	apiKey := &models.APIKey{}
	if err := db.dbAdapter.FindByID(ctx, apiKey, id); err != nil {
		if errors.Is(err, db.ErrNotFound) {
			return nil, fmt.Errorf("API key not found with id %s", id)
		}
		return nil, err
	}
	
	return apiKey, nil
}

// GetAPIKeyByToken gets an API key by token
func (db *Database) GetAPIKeyByToken(ctx context.Context, token string) (*models.APIKey, error) {
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetAPIKeyByToken(token)
	}

	var apiKeys []*models.APIKey
	filters := map[string]interface{}{"token": token}
	
	if err := db.dbAdapter.List(ctx, &apiKeys, filters, 1, 1, ""); err != nil {
		return nil, err
	}
	
	if len(apiKeys) == 0 {
		return nil, fmt.Errorf("API key not found with token %s", token)
	}
	
	return apiKeys[0], nil
}

// Remaining methods would be implemented in a similar pattern...
