package db

import (
	"fmt"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/logging"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Config represents database configuration
type Config struct {
	URL           string
	MaxRetries    int
	RetryInterval time.Duration
	LogLevel      string
}

// DefaultConfig returns a default database configuration
func DefaultConfig() Config {
	return Config{
		URL:           "postgres://postgres:postgres@localhost:5432/deploy_orchestrator?sslmode=disable",
		MaxRetries:    5,
		RetryInterval: 3 * time.Second,
		LogLevel:      "info",
	}
}

// Connect establishes a connection to the database
func Connect(config Config) (*gorm.DB, error) {
	var db *gorm.DB
	var err error

	// Get logger
	log := logging.Default().Named("db")

	// Configure GORM logger
	var gormLogLevel logger.LogLevel
	switch config.LogLevel {
	case "silent":
		gormLogLevel = logger.Silent
	case "error":
		gormLogLevel = logger.Error
	case "warn":
		gormLogLevel = logger.Warn
	default:
		gormLogLevel = logger.Info
	}

	// Create GORM logger that uses our structured logger
	gormLogger := logger.New(
		&logging.GormLogWriter{Logger: log},
		logger.Config{
			SlowThreshold:             200 * time.Millisecond,
			LogLevel:                  gormLogLevel,
			IgnoreRecordNotFoundError: true,
			Colorful:                  false,
		},
	)

	// Try to connect with retries
	for attempt := 1; attempt <= config.MaxRetries; attempt++ {
		db, err = gorm.Open(postgres.Open(config.URL), &gorm.Config{
			Logger: gormLogger,
		})

		if err == nil {
			break
		}

		log.Error("Failed to connect to database",
			logging.Int("attempt", attempt),
			logging.Int("maxRetries", config.MaxRetries),
			logging.Error(err),
		)

		if attempt < config.MaxRetries {
			log.Info("Retrying database connection",
				logging.Duration("retryIn", config.RetryInterval),
			)
			time.Sleep(config.RetryInterval)
		}
	}

	if err != nil {
		return nil, fmt.Errorf("failed to connect to database after %d attempts: %w",
			config.MaxRetries, err)
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	// Set connection pool parameters
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	return db, nil
}
