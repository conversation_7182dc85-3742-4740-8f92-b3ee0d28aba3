package db

import (
	"time"

	"github.com/claudio/deploy-orchestrator/shared/logging"
	"gorm.io/gorm"
)

// BaseModel contains common fields for all database models
type BaseModel struct {
	ID        string     `gorm:"primaryKey;type:uuid" json:"id"`
	CreatedAt time.Time  `json:"createdAt"`
	UpdatedAt time.Time  `json:"updatedAt"`
	DeletedAt *time.Time `gorm:"index" json:"deletedAt,omitempty"`
}

// RunMigrations runs database migrations for the given models
func RunMigrations(db *gorm.DB, models ...interface{}) error {
	logger := logging.Default().Named("migrations")
	logger.Info("Running database migrations...")

	if err := db.AutoMigrate(models...); err != nil {
		logger.Error("Error running migrations", logging.Error(err))
		return err
	}

	logger.Info("Database migrations completed successfully")
	return nil
}

// SeedData populates the database with initial data
func SeedData(db *gorm.DB, seedFunction func(db *gorm.DB) error) error {
	logger := logging.Default().Named("migrations")
	logger.Info("Seeding database with initial data...")

	if err := seedFunction(db); err != nil {
		logger.Error("Error seeding data", logging.Error(err))
		return err
	}

	logger.Info("Database seeding completed successfully")
	return nil
}
