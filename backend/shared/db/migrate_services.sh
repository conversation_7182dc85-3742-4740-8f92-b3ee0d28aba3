#!/bin/bash
# Script to migrate services to use the new GORM adapter

set -e

BASE_DIR="./backend"
SERVICES=("admin-service" "audit-service" "deployment-service" "notification-service" "scheduling-service" "integration-service")

echo "Starting migration to the new GORM adapter for all services..."

# Function to update import paths in a file
update_imports() {
  local file=$1
  
  # Update import paths
  sed -i '' 's|"github.com/claudio/deploy-orchestrator/|"github.com/claudio/deploy-orchestrator/|g' "$file"
}

# Function to migrate a single service
migrate_service() {
  local service=$1
  local service_dir="$BASE_DIR/$service"
  
  echo "Migrating $service..."
  
  # Create backup of storage directory
  echo "Creating backup of $service storage files..."
  mkdir -p "$service_dir/storage/backup"
  cp "$service_dir/storage/"*.go "$service_dir/storage/backup/"
  
  # Update import paths in all Go files
  echo "Updating import paths in $service..."
  find "$service_dir" -name "*.go" -type f -exec bash -c "update_imports {}" \;
  
  # Copy the template database file for this service
  echo "Creating new database implementation for $service..."
  cp "$BASE_DIR/shared/db/templates/${service}_database.go.template" "$service_dir/storage/database_new.go"
  
  echo "Migration for $service completed. Please manually review and test the changes."
  echo "------------------------------------------------------------"
}

# Function to verify database implementation
verify_database_implementation() {
  local service=$1
  local service_dir="$BASE_DIR/$service"
  
  echo "Verifying $service implementation..."
  
  # Compile the service to check for errors
  pushd "$service_dir" > /dev/null
  if go build -o /dev/null; then
    echo "✅ $service compiles successfully"
  else
    echo "❌ $service has compilation errors"
  fi
  popd > /dev/null
}

# Confirm before proceeding
echo "This script will migrate all services to use the new GORM adapter."
echo "Make sure you have backed up your work and committed any changes to version control."
read -p "Continue? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
  echo "Migration aborted."
  exit 1
fi

# Check that all template files exist
for service in "${SERVICES[@]}"; do
  template_file="$BASE_DIR/shared/db/templates/${service}_database.go.template"
  if [ ! -f "$template_file" ]; then
    echo "Error: Template file for $service not found: $template_file"
    exit 1
  fi
done

# Migrate each service
for service in "${SERVICES[@]}"; do
  migrate_service "$service"
done

echo "All services have been migrated to the new GORM adapter."

# Verify each service
echo "Verifying implementations..."
for service in "${SERVICES[@]}"; do
  verify_database_implementation "$service"
done

echo "Migration completed."
echo "Next steps:"
echo "1. Rename database_new.go to database.go in each service's storage directory when ready"
echo "2. Run tests for each service"
echo "3. Deploy services one by one"
