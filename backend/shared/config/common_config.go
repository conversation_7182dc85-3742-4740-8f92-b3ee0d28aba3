package config

// DBConfig holds common database configuration parameters
type DBConfig struct {
	URL           string `mapstructure:"url" yaml:"url" env:"DB_URL" default:""`
	MaxRetries    int    `mapstructure:"max_retries" yaml:"max_retries" env:"DB_MAX_RETRIES" default:"5"`
	RetryInterval int    `mapstructure:"retry_interval" yaml:"retry_interval" env:"DB_RETRY_INTERVAL" default:"3"`
	MaxOpenConns  int    `mapstructure:"max_open_conns" yaml:"max_open_conns" env:"DB_MAX_OPEN_CONNS" default:"25"`
	MaxIdleConns  int    `mapstructure:"max_idle_conns" yaml:"max_idle_conns" env:"DB_MAX_IDLE_CONNS" default:"10"`
	QueryTimeout  int    `mapstructure:"query_timeout" yaml:"query_timeout" env:"DB_QUERY_TIMEOUT" default:"10"`
	LogLevel      string `mapstructure:"log_level" yaml:"log_level" env:"DB_LOG_LEVEL" default:"info"`
}

// ServerConfig holds common web server configuration parameters
type ServerConfig struct {
	Port            int    `mapstructure:"port" yaml:"port" env:"SERVER_PORT"`
	Host            string `mapstructure:"host" yaml:"host" env:"SERVER_HOST" default:"0.0.0.0"`
	ReadTimeout     int    `mapstructure:"read_timeout" yaml:"read_timeout" env:"SERVER_READ_TIMEOUT" default:"30"`
	WriteTimeout    int    `mapstructure:"write_timeout" yaml:"write_timeout" env:"SERVER_WRITE_TIMEOUT" default:"30"`
	ShutdownTimeout int    `mapstructure:"shutdown_timeout" yaml:"shutdown_timeout" env:"SERVER_SHUTDOWN_TIMEOUT" default:"10"`
	IdleTimeout     int    `mapstructure:"idle_timeout" yaml:"idle_timeout" env:"SERVER_IDLE_TIMEOUT" default:"60"`
	TrustedProxies  string `mapstructure:"trusted_proxies" yaml:"trusted_proxies" env:"SERVER_TRUSTED_PROXIES" default:""`
	TLSCertFile     string `mapstructure:"tls_cert_file" yaml:"tls_cert_file" env:"SERVER_TLS_CERT_FILE" default:""`
	TLSKeyFile      string `mapstructure:"tls_key_file" yaml:"tls_key_file" env:"SERVER_TLS_KEY_FILE" default:""`
}

// LoggingConfig holds common logging configuration parameters
type LoggingConfig struct {
	Level      string `mapstructure:"level" yaml:"level" env:"LOG_LEVEL" default:"info"`
	Format     string `mapstructure:"format" yaml:"format" env:"LOG_FORMAT" default:"json"`
	Output     string `mapstructure:"output" yaml:"output" env:"LOG_OUTPUT" default:"stdout"`
	TimeFormat string `mapstructure:"time_format" yaml:"time_format" env:"LOG_TIME_FORMAT" default:"2006-01-02T15:04:05Z07:00"`
	MaxSize    int    `mapstructure:"max_size" yaml:"max_size" default:"100"`
	MaxBackups int    `mapstructure:"max_backups" yaml:"max_backups" default:"3"`
	MaxAge     int    `mapstructure:"max_age" yaml:"max_age" default:"28"`
	Compress   bool   `mapstructure:"compress" yaml:"compress" default:"true"`
}

// TracingConfig holds common tracing configuration parameters
type TracingConfig struct {
	Enabled     bool   `mapstructure:"enabled" yaml:"enabled" env:"TRACING_ENABLED" default:"false"`
	ServiceName string `mapstructure:"service_name" yaml:"service_name" env:"TRACING_SERVICE_NAME"`
	Endpoint    string `mapstructure:"endpoint" yaml:"endpoint" env:"TRACING_ENDPOINT" default:"localhost:4317"`
}

type MonitoringConfig struct {
	Enabled bool   `mapstructure:"enabled" yaml:"enabled" env:"MONITORING_ENABLED"`
	Port    int    `mapstructure:"port" yaml:"port" env:"MONITORING_PORT"`
	Path    string `mapstructure:"path" yaml:"path" env:"MONITORING_PATH"`
}

// AuthConfig holds common authentication configuration parameters
type AuthConfig struct {
	// JWT Configuration
	JWTSecret            string `mapstructure:"jwt_secret" yaml:"jwt_secret" env:"JWT_SECRET_KEY" default:""`
	JWTExpirationMinutes int    `mapstructure:"jwt_expiration_minutes" yaml:"jwt_expiration_minutes" env:"AUTH_JWT_EXPIRATION_MINUTES" default:"60"`

	// Token Expiry Configuration (in duration format like "1h", "24h")
	AccessTokenExpiry  string `mapstructure:"access_token_expiry" yaml:"access_token_expiry" env:"ACCESS_TOKEN_EXPIRY" default:"1h"`
	RefreshTokenExpiry string `mapstructure:"refresh_token_expiry" yaml:"refresh_token_expiry" env:"REFRESH_TOKEN_EXPIRY" default:"168h"`

	// Service URLs
	AdminServiceURL string `mapstructure:"admin_service_url" yaml:"admin_service_url" env:"ADMIN_SERVICE_URL" default:"http://admin-service:8080"`

	// Legacy/Additional Configuration
	APIKeyHeader string `mapstructure:"api_key_header" yaml:"api_key_header" env:"AUTH_API_KEY_HEADER" default:"X-API-Key"`
	DisableAuth  bool   `mapstructure:"disable" yaml:"disable" env:"AUTH_DISABLE" default:"false"`
}

type ServiceConfig struct {
	Name    string `mapstructure:"name" yaml:"name" env:"NAME"`
	Version string `mapstructure:"version" yaml:"version" env:"VERSION"`
}

type EncryptionConfig struct {
	// Master encryption key (should be from external key management)
	MasterKey string `mapstructure:"master_key" yaml:"master_key" env:"ENCRYPTION_MASTER_KEY"`

	// Key derivation settings
	Algorithm string `mapstructure:"algorithm" yaml:"algorithm" env:"ENCRYPTION_ALGORITHM" default:"AES-256-GCM"`
	KeySize   int    `mapstructure:"key_size" yaml:"key_size" env:"ENCRYPTION_KEY_SIZE" default:"32"`
	SaltSize  int    `mapstructure:"salt_size" yaml:"salt_size" env:"ENCRYPTION_SALT_SIZE" default:"16"`

	// Key rotation settings
	RotationInterval string `mapstructure:"rotation_interval" yaml:"rotation_interval" env:"ENCRYPTION_ROTATION_INTERVAL" default:"30d"`
	MaxKeyAge        string `mapstructure:"max_key_age" yaml:"max_key_age" env:"ENCRYPTION_MAX_KEY_AGE" default:"90d"`

	// Transit encryption
	TLSEnabled    bool   `mapstructure:"tls_enabled" yaml:"tls_enabled" env:"ENCRYPTION_TLS_ENABLED" default:"true"`
	TLSMinVersion string `mapstructure:"tls_min_version" yaml:"tls_min_version" env:"ENCRYPTION_TLS_MIN_VERSION" default:"1.2"`

	// Hardware security module support
	HSMEnabled bool   `mapstructure:"hsm_enabled" yaml:"hsm_enabled" env:"ENCRYPTION_HSM_ENABLED" default:"false"`
	HSMConfig  string `mapstructure:"hsm_config" yaml:"hsm_config" env:"ENCRYPTION_HSM_CONFIG"`
}

// GatewayConfig holds common gateway registration configuration parameters
type GatewayConfig struct {
	// Gateway service URL for registration
	URL string `mapstructure:"url" yaml:"url" env:"GATEWAY_URL" default:""`

	// Authentication token for gateway registration
	AuthToken string `mapstructure:"auth_token" yaml:"auth_token" env:"GATEWAY_AUTH_TOKEN" default:""`

	Timeout int `yaml:"timeout" env:"GATEWAY_TIMEOUT"` // Add this field

	// Service registration settings
	Enabled     bool   `mapstructure:"enabled" yaml:"enabled" env:"GATEWAY_ENABLED" default:"true"`
	Version     string `mapstructure:"service_version" yaml:"service_version" env:"SERVICE_VERSION" default:"1.0.0"`
	Environment string `mapstructure:"environment" yaml:"environment" env:"ENVIRONMENT" default:"development"`
	Region      string `mapstructure:"region" yaml:"region" env:"REGION" default:"local"`

	// Registration behavior
	RetryAttempts   int    `mapstructure:"retry_attempts" yaml:"retry_attempts" env:"GATEWAY_RETRY_ATTEMPTS" default:"3"`
	HealthCheckPath string `mapstructure:"health_check_path" yaml:"health_check_path" env:"GATEWAY_HEALTH_CHECK_PATH" default:"/health"`

	// Service tags for discovery
	Tags []string `mapstructure:"tags" yaml:"tags" env:"GATEWAY_TAGS" default:""`
}

// Gateway configuration interface methods
func (g *GatewayConfig) GetGatewayURL() string {
	return g.URL
}

func (g *GatewayConfig) GetGatewayAuthToken() string {
	return g.AuthToken
}

func (g *GatewayConfig) GetGatewayEnabled() bool {
	return g.Enabled
}

func (g *GatewayConfig) GetGatewayVersion() string {
	return g.Version
}

func (g *GatewayConfig) GetGatewayEnvironment() string {
	return g.Environment
}

func (g *GatewayConfig) GetGatewayRegion() string {
	return g.Region
}

func (g *GatewayConfig) GetGatewayTags() []string {
	return g.Tags
}
