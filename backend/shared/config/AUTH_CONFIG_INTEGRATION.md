# 🔐 Shared Authentication Configuration Integration

## 📋 **Overview**

The shared authentication package now supports configuration via **config.yaml files** in addition to environment variables. This provides more flexible and maintainable configuration management across all services.

## 🔧 **Configuration Options**

### **Priority Order (Highest to Lowest):**
1. **Environment Variables** (highest priority)
2. **config.yaml file values**
3. **Default values** (lowest priority)

### **Configuration Fields**

| YAML Field | Environment Variable | Default Value | Description |
|------------|---------------------|---------------|-------------|
| `auth_jwt_secret` | `JWT_SECRET_KEY` | `""` | JWT signing secret key |
| `auth_jwt_expiration_minutes` | `AUTH_JWT_EXPIRATION_MINUTES` | `60` | Legacy: JWT expiration in minutes |
| `auth_access_token_expiry` | `ACCESS_TOKEN_EXPIRY` | `"1h"` | Access token expiry (Go duration) |
| `auth_refresh_token_expiry` | `REFRESH_TOKEN_EXPIRY` | `"168h"` | Refresh token expiry (Go duration) |
| `auth_admin_service_url` | `ADMIN_SERVICE_URL` | `"http://admin-service:8080"` | Admin service URL |
| `auth_api_key_header` | `AUTH_API_KEY_HEADER` | `"X-API-Key"` | API key header name |
| `auth_disable` | `AUTH_DISABLE` | `false` | Disable authentication |

## 🚀 **Usage Examples**

### **1. Using Shared Config in Services**

```go
package main

import (
    "log"
    
    "github.com/claudio/deploy-orchestrator/shared/auth"
    "github.com/claudio/deploy-orchestrator/shared/config"
    "github.com/claudio/deploy-orchestrator/your-service/config" // Your service config
)

func main() {
    // Load your service configuration (which includes shared AuthConfig)
    cfg, err := config.LoadConfig()
    if err != nil {
        log.Fatalf("Failed to load config: %v", err)
    }

    // Create auth manager from shared config
    authManager, err := auth.NewAuthManagerFromSharedConfig(&cfg.Auth)
    if err != nil {
        log.Fatalf("Failed to create auth manager: %v", err)
    }

    // Use auth manager in your service
    router.Use(authManager.AuthMiddleware())
    adminRoutes.Use(authManager.AdminMiddleware())
}
```

### **2. Service Configuration Structure**

```go
// In your service's config package
type Config struct {
    Service    config.ServiceConfig    `mapstructure:"service"`
    Server     config.ServerConfig     `mapstructure:"server"`
    Database   config.DBConfig         `mapstructure:"database"`
    Auth       config.AuthConfig       `mapstructure:"auth"`      // Shared auth config
    Logging    config.LoggingConfig    `mapstructure:"logging"`
    // ... other service-specific config
}
```

### **3. Example config.yaml**

```yaml
# Service Configuration
name: "my-service"
version: "1.0.0"

# Authentication Configuration
auth_jwt_secret: "your-production-secret-key"
auth_access_token_expiry: "1h"
auth_refresh_token_expiry: "168h"  # 7 days
auth_admin_service_url: "http://admin-service:8080"
auth_disable: false

# Server Configuration
server_port: 8080
server_host: "0.0.0.0"

# Database Configuration
db_url: "postgres://user:pass@localhost:5432/db"
```

### **4. Environment Variable Override**

```bash
# Environment variables take precedence over config.yaml
export JWT_SECRET_KEY="production-secret-from-env"
export ACCESS_TOKEN_EXPIRY="30m"
export ADMIN_SERVICE_URL="http://prod-admin:8080"
```

## 🔄 **Migration Guide**

### **From Environment-Only to Shared Config**

**Before:**
```go
// Old way - environment variables only
authManager, err := auth.NewAuthManagerFromEnv()
```

**After:**
```go
// New way - shared config with environment fallbacks
authManager, err := auth.NewAuthManagerFromSharedConfig(&cfg.Auth)
```

### **Backward Compatibility**

The old `NewAuthManagerFromEnv()` function still works and is fully supported:

```go
// This still works for existing services
authManager, err := auth.NewAuthManagerFromEnv()
```

## 🧪 **Testing**

### **Test Configuration**

```go
func TestWithSharedConfig(t *testing.T) {
    testConfig := &config.AuthConfig{
        JWTSecret:          "test-secret-key",
        AccessTokenExpiry:  "15m",
        RefreshTokenExpiry: "1h",
        AdminServiceURL:    "http://test-admin:8080",
        DisableAuth:        false,
    }

    authManager, err := auth.NewAuthManagerFromSharedConfig(testConfig)
    require.NoError(t, err)
    
    // Use authManager in tests
}
```

### **Environment Variable Testing**

```go
func TestWithEnvOverrides(t *testing.T) {
    // Set test environment variables
    os.Setenv("JWT_SECRET_KEY", "test-env-secret")
    os.Setenv("ACCESS_TOKEN_EXPIRY", "5m")
    defer func() {
        os.Unsetenv("JWT_SECRET_KEY")
        os.Unsetenv("ACCESS_TOKEN_EXPIRY")
    }()

    config := &config.AuthConfig{
        JWTSecret:         "config-secret",  // Will be overridden
        AccessTokenExpiry: "1h",             // Will be overridden
        AdminServiceURL:   "http://test:8080",
    }

    authManager, err := auth.NewAuthManagerFromSharedConfig(config)
    require.NoError(t, err)
    
    // Environment variables take precedence
    assert.Equal(t, "test-env-secret", authManager.Config.JWTSecretKey)
    assert.Equal(t, 5*time.Minute, authManager.Config.AccessTokenExpiry)
}
```

## 🔧 **Configuration Validation**

The shared config integration includes comprehensive validation:

```go
// Automatic validation when creating auth manager
authManager, err := auth.NewAuthManagerFromSharedConfig(config)
if err != nil {
    // Handle validation errors:
    // - Empty JWT secret
    // - Invalid duration formats
    // - Missing admin service URL
    log.Fatalf("Auth config validation failed: %v", err)
}
```

## 📝 **Best Practices**

### **1. Production Configuration**
```yaml
# Use environment variables for sensitive data
auth_jwt_secret: "${JWT_SECRET_KEY}"
auth_admin_service_url: "${ADMIN_SERVICE_URL}"

# Set reasonable token expiry times
auth_access_token_expiry: "1h"
auth_refresh_token_expiry: "168h"  # 7 days
```

### **2. Development Configuration**
```yaml
# Use shorter expiry times for development
auth_access_token_expiry: "30m"
auth_refresh_token_expiry: "24h"

# Use local service URLs
auth_admin_service_url: "http://localhost:8080"
```

### **3. Testing Configuration**
```yaml
# Use very short expiry times for tests
auth_access_token_expiry: "5m"
auth_refresh_token_expiry: "30m"

# Use test-specific secrets
auth_jwt_secret: "test-secret-key"
```

## 🔄 **Configuration Conversion**

### **Convert Between Formats**

```go
// Convert auth.Config to shared config.AuthConfig
authConfig := &auth.Config{
    JWTSecretKey:       "secret",
    AccessTokenExpiry:  time.Hour,
    RefreshTokenExpiry: 24 * time.Hour,
    AdminServiceURL:    "http://admin:8080",
}

sharedConfig := authConfig.ToSharedConfig()

// Convert shared config.AuthConfig to auth.Config
authConfigFromShared := auth.LoadConfigFromSharedConfig(sharedConfig)
```

## 🚨 **Security Considerations**

1. **Never commit secrets to config files**
2. **Use environment variables for production secrets**
3. **Validate configuration on startup**
4. **Use appropriate token expiry times**
5. **Secure admin service URLs with HTTPS in production**

## 📚 **Additional Resources**

- [Shared Auth Package Documentation](../auth/README.md)
- [Configuration Examples](./example_config.yaml)
- [Integration Tests](../auth/config_integration_test.go)
- [Service Migration Guide](../../SHARED_AUTH_MIGRATION_COMPLETE.md)
