# Example configuration file demonstrating shared authentication configuration
# This file shows how to configure authentication settings via config.yaml
# instead of relying solely on environment variables

# Service Configuration
name: "example-service"
version: "1.0.0"

# Server Configuration
server_port: 8080
server_host: "0.0.0.0"
server_read_timeout: 30
server_write_timeout: 30
server_shutdown_timeout: 10

# Database Configuration
db_url: "postgres://user:password@localhost:5432/deploy_orchestrator"
db_max_retries: 5
db_retry_interval: 3
db_max_open_conns: 25
db_max_idle_conns: 10
db_query_timeout: 10
db_log_level: "info"

# Logging Configuration
log_level: "info"
log_format: "json"
log_output: "stdout"
log_time_format: "2006-01-02T15:04:05Z07:00"

# Authentication Configuration
# These settings will be used by the shared auth package
auth_jwt_secret: "your-production-jwt-secret-key-here"
auth_jwt_expiration_minutes: 60  # Legacy field for backward compatibility

# Token Expiry Configuration (preferred - uses Go duration format)
auth_access_token_expiry: "1h"     # 1 hour
auth_refresh_token_expiry: "168h"  # 7 days (168 hours)

# Service URLs
auth_admin_service_url: "http://admin-service:8080"

# Additional Auth Configuration
auth_api_key_header: "X-API-Key"
auth_disable: false

# Tracing Configuration
tracing_enabled: false
tracing_service_name: "example-service"
tracing_endpoint: "localhost:4317"

# Encryption Configuration
encryption_key: "your-encryption-key-here"

# Gateway Configuration
# Configure microservice registration with API Gateway
gateway_url: "http://localhost:8000"
gateway_auth_token: ""
gateway_enabled: true
gateway_service_version: "1.0.0"
gateway_environment: "development"
gateway_region: "local"
gateway_retry_attempts: 3
gateway_health_check_path: "/health"
gateway_tags: ["api", "microservice"]

---
# Alternative configuration examples for different environments

# Development Environment
development:
  auth_jwt_secret: "dev-secret-key-not-for-production"
  auth_access_token_expiry: "30m"    # Shorter tokens for development
  auth_refresh_token_expiry: "24h"   # Shorter refresh for development
  auth_admin_service_url: "http://localhost:8080"
  auth_disable: false
  log_level: "debug"

# Production Environment
production:
  auth_jwt_secret: "${JWT_SECRET_KEY}"  # Use environment variable
  auth_access_token_expiry: "1h"
  auth_refresh_token_expiry: "168h"
  auth_admin_service_url: "${ADMIN_SERVICE_URL}"
  auth_disable: false
  log_level: "info"

# Testing Environment
testing:
  auth_jwt_secret: "test-secret-key"
  auth_access_token_expiry: "15m"    # Short tokens for testing
  auth_refresh_token_expiry: "1h"    # Short refresh for testing
  auth_admin_service_url: "http://test-admin:8080"
  auth_disable: false
  log_level: "debug"

# Staging Environment
staging:
  auth_jwt_secret: "${JWT_SECRET_KEY}"
  auth_access_token_expiry: "45m"
  auth_refresh_token_expiry: "72h"   # 3 days
  auth_admin_service_url: "${ADMIN_SERVICE_URL}"
  auth_disable: false
  log_level: "info"
