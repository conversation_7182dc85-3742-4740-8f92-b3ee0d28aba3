# Gateway Configuration Integration

This document explains how to configure microservice gateway registration using the shared config system, allowing services to be configured via YAML files instead of relying solely on environment variables.

## Overview

The shared config system now includes `GatewayConfig` which allows microservices to configure their gateway registration through YAML configuration files. This eliminates the "Gateway URL not configured, skipping registration" message when services can't find the `GATEWAY_URL` environment variable.

## Configuration Structure

### Shared Gateway Configuration

```go
type GatewayConfig struct {
    // Gateway service URL for registration
    URL string `mapstructure:"gateway_url" yaml:"gateway_url" env:"GATEWAY_URL" default:""`
    
    // Authentication token for gateway registration
    AuthToken string `mapstructure:"gateway_auth_token" yaml:"gateway_auth_token" env:"GATEWAY_AUTH_TOKEN" default:""`
    
    // Service registration settings
    Enabled     bool   `mapstructure:"gateway_enabled" yaml:"gateway_enabled" env:"GATEWAY_ENABLED" default:"true"`
    Version     string `mapstructure:"gateway_service_version" yaml:"gateway_service_version" env:"SERVICE_VERSION" default:"1.0.0"`
    Environment string `mapstructure:"gateway_environment" yaml:"gateway_environment" env:"ENVIRONMENT" default:"development"`
    Region      string `mapstructure:"gateway_region" yaml:"gateway_region" env:"REGION" default:"local"`
    
    // Registration behavior
    RetryAttempts    int    `mapstructure:"gateway_retry_attempts" yaml:"gateway_retry_attempts" env:"GATEWAY_RETRY_ATTEMPTS" default:"3"`
    HealthCheckPath  string `mapstructure:"gateway_health_check_path" yaml:"gateway_health_check_path" env:"GATEWAY_HEALTH_CHECK_PATH" default:"/health"`
    
    // Service tags for discovery
    Tags []string `mapstructure:"gateway_tags" yaml:"gateway_tags" env:"GATEWAY_TAGS" default:""`
}
```

## Integration Steps

### 1. Update Service Configuration

Add the gateway configuration to your service's config struct:

```go
type Config struct {
    // Common configuration sections from shared module
    Server   sharedConfig.ServerConfig  `mapstructure:"server" yaml:"server"`
    Database sharedConfig.DBConfig      `mapstructure:"database" yaml:"database"`
    Logging  sharedConfig.LoggingConfig `mapstructure:"logging" yaml:"logging"`
    Tracing  sharedConfig.TracingConfig `mapstructure:"tracing" yaml:"tracing"`
    Auth     sharedConfig.AuthConfig    `mapstructure:"auth" yaml:"auth"`
    Service  sharedConfig.ServiceConfig `mapstructure:"service" yaml:"service"`
    Gateway  sharedConfig.GatewayConfig `mapstructure:"gateway" yaml:"gateway"`  // Add this line

    // Service-specific configuration
    // ... your other config fields
}
```

### 2. Update Configuration Loading

Use the shared config loading system:

```go
func LoadConfig() (*Config, error) {
    config := &Config{
        Server: sharedConfig.ServerConfig{
            Port: 8085, // Your service's default port
        },
        Tracing: sharedConfig.TracingConfig{
            ServiceName: "your-service-name",
        },
        Service: sharedConfig.ServiceConfig{
            Name:    "your-service-name",
            Version: "1.0.0",
        },
        // ... other default values
    }

    // Use shared config loading
    if err := sharedConfig.LoadConfig(config, "your-service-name"); err != nil {
        return nil, err
    }

    return config, nil
}
```

### 3. Update Gateway Registration

Replace the old environment-based gateway registration:

```go
// OLD: Environment-based registration
gatewayClient := gateway.NewClientFromEnv("your-service", cfg.Server.Port, logger)

// NEW: Shared config-based registration
gatewayClient := gateway.NewClientFromSharedConfig("your-service", cfg.Server.Port, &cfg.Gateway, logger)
```

### 4. Add Configuration to YAML File

Create or update your service's `config/config.yaml` file:

```yaml
# Gateway Configuration
# Configure microservice registration with API Gateway
gateway:
  gateway_url: "http://localhost:8000"
  gateway_auth_token: ""
  gateway_enabled: true
  gateway_service_version: "1.0.0"
  gateway_environment: "development"
  gateway_region: "local"
  gateway_retry_attempts: 3
  gateway_health_check_path: "/health"
  gateway_tags: ["api", "your-service", "microservice"]
```

## Configuration Options

### Gateway URL
- **YAML**: `gateway_url`
- **Environment**: `GATEWAY_URL`
- **Default**: `""` (empty, registration will be skipped)
- **Example**: `"http://localhost:8000"`

### Authentication Token
- **YAML**: `gateway_auth_token`
- **Environment**: `GATEWAY_AUTH_TOKEN`
- **Default**: `""` (no authentication)
- **Example**: `"your-jwt-token-here"`

### Enable/Disable Registration
- **YAML**: `gateway_enabled`
- **Environment**: `GATEWAY_ENABLED`
- **Default**: `true`
- **Example**: `false` (to disable registration)

### Service Version
- **YAML**: `gateway_service_version`
- **Environment**: `SERVICE_VERSION`
- **Default**: `"1.0.0"`
- **Example**: `"2.1.0"`

### Environment
- **YAML**: `gateway_environment`
- **Environment**: `ENVIRONMENT`
- **Default**: `"development"`
- **Example**: `"production"`

### Region
- **YAML**: `gateway_region`
- **Environment**: `REGION`
- **Default**: `"local"`
- **Example**: `"us-west-2"`

### Retry Attempts
- **YAML**: `gateway_retry_attempts`
- **Environment**: `GATEWAY_RETRY_ATTEMPTS`
- **Default**: `3`
- **Example**: `5`

### Health Check Path
- **YAML**: `gateway_health_check_path`
- **Environment**: `GATEWAY_HEALTH_CHECK_PATH`
- **Default**: `"/health"`
- **Example**: `"/api/health"`

### Service Tags
- **YAML**: `gateway_tags`
- **Environment**: `GATEWAY_TAGS`
- **Default**: `[]` (empty array)
- **Example**: `["api", "critical", "user-facing"]`

## Environment-Specific Configuration

You can override YAML settings with environment variables:

```bash
# Override gateway URL for production
export GATEWAY_URL="https://gateway.production.com"

# Disable gateway registration for testing
export GATEWAY_ENABLED="false"

# Set production environment
export ENVIRONMENT="production"
export REGION="us-east-1"
```

## Benefits

1. **No More "Gateway URL not configured" Messages**: Services can be configured via YAML files
2. **Environment Variable Support**: Still supports environment variable overrides
3. **Consistent Configuration**: Uses the same shared config system as other settings
4. **Default Values**: Sensible defaults for all configuration options
5. **Flexible Deployment**: Easy to configure for different environments

## Migration Guide

For existing services using environment-based gateway registration:

1. Add `Gateway sharedConfig.GatewayConfig` to your config struct
2. Replace `gateway.NewClientFromEnv()` with `gateway.NewClientFromSharedConfig()`
3. Add gateway configuration to your YAML file
4. Test that registration works with the new configuration

The old environment variables will still work as overrides, ensuring backward compatibility.

## Example Services

The following services have been updated to use the new gateway configuration:

- `workflow-service`: Updated to use shared gateway config
- More services can be updated following the same pattern

## Troubleshooting

### Service Not Registering
1. Check that `gateway_enabled` is `true`
2. Verify `gateway_url` is set correctly
3. Ensure the gateway service is running and accessible
4. Check service logs for registration errors

### Configuration Not Loading
1. Verify the YAML file exists in the expected location
2. Check YAML syntax and indentation
3. Ensure the config struct has the correct mapstructure tags
4. Verify the shared config loading is called correctly
