// Package config provides shared configuration utilities for microservices
package config

import (
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"strings"

	"github.com/spf13/viper"
)

// LoadConfig loads configuration from a YAML file and environment variables
// It follows these loading priorities:
// 1. Environment variables (highest priority)
// 2. Config file values (lower priority)
// Default values in the struct are used if no value is provided
func LoadConfig(configStruct interface{}, serviceName string) error {
	// Initialize viper
	v := viper.New()

	// Set default config name and paths
	v.SetConfigName("config")
	v.SetConfigType("yaml")

	// Search for config in multiple locations
	v.AddConfigPath(".")                                  // Current directory
	v.AddConfigPath(fmt.Sprintf("./config"))              // ./config subdirectory
	v.AddConfigPath(fmt.Sprintf("/etc/%s/", serviceName)) // /etc/[service-name]/

	// Allow environment variables to override config values
	v.AutomaticEnv()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Set default values from struct tags
	setDefaultsFromStruct(v, configStruct)

	// Try to read config file
	if err := v.ReadInConfig(); err != nil {
		// It's okay if config file doesn't exist, we can use env vars and defaults
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return fmt.Errorf("error reading config file: %w", err)
		}
		// Log that we're using defaults and env vars only
		fmt.Printf("No config file found, using environment variables and defaults\n")
	} else {
		fmt.Printf("Using config file: %s\n", v.ConfigFileUsed())
	}

	// Unmarshal config into the provided struct
	if err := v.Unmarshal(configStruct); err != nil {
		return fmt.Errorf("unable to decode config into struct: %w", err)
	}

	return nil
}

// setDefaultsFromStruct sets default values in Viper based on struct tags
func setDefaultsFromStruct(v *viper.Viper, configStruct interface{}) {
	val := reflect.ValueOf(configStruct).Elem()
	typ := val.Type()

	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)

		// Check for mapstructure tag which viper uses
		key := field.Tag.Get("mapstructure")
		if key == "" {
			key = strings.ToLower(field.Name)
		}

		// Check for default tag
		defaultValue := field.Tag.Get("default")
		if defaultValue != "" {
			v.SetDefault(key, defaultValue)
		}

		// Check for env tag to bind to specific environment variable
		envVar := field.Tag.Get("env")
		if envVar != "" {
			v.BindEnv(key, envVar)
		} else {
			// Use the key with standard naming convention for env vars
			envKey := strings.ToUpper(strings.ReplaceAll(key, ".", "_"))
			v.BindEnv(key, envKey)
		}
	}
}

// SaveDefaultConfig saves a default configuration file if one doesn't exist
func SaveDefaultConfig(configStruct interface{}, serviceName string) error {
	// Define config paths
	configDirs := []string{
		".",
		"./config",
		fmt.Sprintf("/etc/%s", serviceName),
	}

	// Check if config file already exists in any location
	for _, dir := range configDirs {
		configPath := filepath.Join(dir, "config.yaml")
		if _, err := os.Stat(configPath); err == nil {
			// Config file already exists, don't overwrite
			return nil
		}
	}

	// No config file found, create one in current directory
	configDir := "./config"

	// Create config directory if it doesn't exist
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	// Initialize viper for writing
	v := viper.New()
	v.SetConfigName("config")
	v.SetConfigType("yaml")
	v.AddConfigPath(configDir)

	// Set values from struct fields
	setViperValuesFromStruct(v, configStruct)

	// Save config file
	configPath := filepath.Join(configDir, "config.yaml")
	if err := v.WriteConfigAs(configPath); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	fmt.Printf("Created default config file: %s\n", configPath)
	return nil
}

// setViperValuesFromStruct sets viper values based on struct field values
func setViperValuesFromStruct(v *viper.Viper, configStruct interface{}) {
	val := reflect.ValueOf(configStruct).Elem()
	typ := val.Type()

	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		value := val.Field(i)

		// Get field name for config key
		key := field.Tag.Get("mapstructure")
		if key == "" {
			key = strings.ToLower(field.Name)
		}

		// Skip nil pointer fields
		if value.Kind() == reflect.Ptr && value.IsNil() {
			continue
		}

		// Set value in viper
		if value.CanInterface() {
			v.Set(key, value.Interface())
		}
	}
}
