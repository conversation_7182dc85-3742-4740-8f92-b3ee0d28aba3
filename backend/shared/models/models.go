package models

import (
	"time"

	"gorm.io/gorm"
)

// Base contains common columns for all tables
type Base struct {
	CreatedAt time.Time      `json:"createdAt" gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time      `json:"updatedAt" gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// Deployment represents a deployment configuration
type Deployment struct {
	ID          string   `json:"id" gorm:"primaryKey;type:text"`
	Name        string   `json:"name" gorm:"uniqueIndex;type:text;not null"`
	Application string   `json:"application" gorm:"type:text;not null"`
	Version     string   `json:"version" gorm:"type:text;not null"`
	Environment string   `json:"environment" gorm:"type:text;not null"`
	Status      string   `json:"status" gorm:"type:text;not null"`
	Steps       []string `json:"steps" gorm:"-"` // Stored as <PERSON><PERSON><PERSON> in DB
	StepsJSON   string   `json:"-" gorm:"column:steps;type:jsonb"`
	Base
}

// TableName overrides the table name
func (Deployment) TableName() string {
	return "deployments"
}

// Schedule represents a deployment schedule
type Schedule struct {
	ID             string `json:"id" gorm:"primaryKey;type:text"`
	Name           string `json:"name" gorm:"uniqueIndex;type:text;not null"`
	DeploymentID   string `json:"deploymentId" gorm:"column:deployment_id;type:text;index;not null"`
	CronExpression string `json:"cronExpression" gorm:"column:cron_expression;type:text;not null"`
	Active         bool   `json:"active" gorm:"default:true"`
	Base
}

// TableName overrides the table name
func (Schedule) TableName() string {
	return "schedules"
}

// Notification represents a notification message
type Notification struct {
	ID        string `json:"id" gorm:"primaryKey;type:text"`
	Type      string `json:"type" gorm:"type:text;not null"`
	Message   string `json:"message" gorm:"type:text;not null"`
	Recipient string `json:"recipient" gorm:"type:text;not null"`
	Status    string `json:"status" gorm:"type:text;not null"`
	Base
}

// TableName overrides the table name
func (Notification) TableName() string {
	return "notifications"
}

// Integration represents an external system integration
type Integration struct {
	ID       string `json:"id" gorm:"primaryKey;type:text"`
	Name     string `json:"name" gorm:"uniqueIndex;type:text;not null"`
	Type     string `json:"type" gorm:"type:text;not null"`
	Endpoint string `json:"endpoint" gorm:"type:text;not null"`
	AuthType string `json:"authType" gorm:"column:auth_type;type:text;not null"`
	Enabled  bool   `json:"enabled" gorm:"default:true"`
	Base
}

// TableName overrides the table name
func (Integration) TableName() string {
	return "integrations"
}

// AuditLog represents a system audit log entry
type AuditLog struct {
	ID         string `json:"id" gorm:"primaryKey;type:text"`
	UserID     string `json:"userId" gorm:"column:user_id;type:text;index"`
	Action     string `json:"action" gorm:"type:text;not null;index"`
	Resource   string `json:"resource" gorm:"type:text;not null;index"`
	ResourceID string `json:"resourceId" gorm:"column:resource_id;type:text;index"`
	Details    string `json:"details" gorm:"type:text"`
	IPAddress  string `json:"ipAddress" gorm:"column:ip_address;type:text"`
	Base
}

// TableName overrides the table name
func (AuditLog) TableName() string {
	return "audit_logs"
}

// GroupProject represents the assignment of a group to a project
type GroupProject struct {
	ID        string `json:"id" gorm:"primaryKey;type:text"`
	GroupID   string `json:"groupId" gorm:"column:group_id;type:text;not null;index"`
	ProjectID string `json:"projectId" gorm:"column:project_id;type:text;not null;index"`
	CreatedBy string `json:"createdBy" gorm:"column:created_by;type:text"`
	Base
}

// TableName overrides the table name
func (GroupProject) TableName() string {
	return "group_projects"
}

// UserProjectAccess represents a user's access to projects through groups
type UserProjectAccess struct {
	UserID          string `json:"userId" gorm:"column:user_id"`
	ProjectID       string `json:"projectId" gorm:"column:project_id"`
	ProjectName     string `json:"projectName" gorm:"column:project_name"`
	GroupName       string `json:"groupName" gorm:"column:group_name"`
	RoleName        string `json:"roleName" gorm:"column:role_name"`
	AccessGrantedAt string `json:"accessGrantedAt" gorm:"column:access_granted_at"`
}

// TableName overrides the table name
func (UserProjectAccess) TableName() string {
	return "user_project_access"
}

// Project represents a project in the system
type Project struct {
	ID          string `json:"id" gorm:"primaryKey;type:text"`
	Name        string `json:"name" gorm:"uniqueIndex;type:text;not null"`
	Description string `json:"description" gorm:"type:text"`
	Status      string `json:"status" gorm:"type:text;not null;default:'active'"`
	Base
}

// TableName overrides the table name
func (Project) TableName() string {
	return "projects"
}

// Workflow represents a workflow definition
type Workflow struct {
	ID          string                 `json:"id" gorm:"primaryKey;type:text"`
	Name        string                 `json:"name" gorm:"type:text;not null"`
	Description string                 `json:"description" gorm:"type:text"`
	ProjectID   string                 `json:"projectId" gorm:"column:project_id;type:text;index;not null"`
	Steps       []WorkflowStep         `json:"steps" gorm:"-"`
	StepsJSON   string                 `json:"-" gorm:"column:steps;type:jsonb"`
	Variables   map[string]interface{} `json:"variables" gorm:"type:jsonb"`
	Status      string                 `json:"status" gorm:"type:text;not null;default:'active'"`
	Base
}

// TableName overrides the table name
func (Workflow) TableName() string {
	return "workflows"
}

// WorkflowStep represents a step in a workflow
type WorkflowStep struct {
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Config      map[string]interface{} `json:"config"`
	DependsOn   []string               `json:"dependsOn,omitempty"`
	Condition   string                 `json:"condition,omitempty"`
	RetryPolicy *RetryPolicy           `json:"retryPolicy,omitempty"`
}

// RetryPolicy defines retry behavior for workflow steps
type RetryPolicy struct {
	MaxRetries  int    `json:"maxRetries"`
	Delay       string `json:"delay"`
	BackoffType string `json:"backoffType"`
}

// User represents a user in the system
type User struct {
	ID             string `json:"id" gorm:"primaryKey;type:text"`
	Username       string `json:"username" gorm:"uniqueIndex;type:text;not null"`
	Email          string `json:"email" gorm:"uniqueIndex;type:text;not null"`
	HashedPassword string `json:"-" gorm:"type:text;not null"`
	FirstName      string `json:"firstName" gorm:"type:text"`
	LastName       string `json:"lastName" gorm:"type:text"`
	IsAdmin        bool   `json:"isAdmin" gorm:"default:false"`
	IsActive       bool   `json:"isActive" gorm:"default:true"`
	Base
}

// TableName overrides the table name for User
func (User) TableName() string {
	return "users"
}

// Group represents a group of users, often imported from an identity provider
type Group struct {
	ID         string `json:"id" gorm:"primaryKey;type:text"`
	Name       string `json:"name" gorm:"uniqueIndex:idx_group_name_source;type:text;not null"`
	Source     string `json:"source" gorm:"uniqueIndex:idx_group_name_source;type:text;not null"` // e.g. ldap, saml, oidc
	ExternalID string `json:"externalId" gorm:"index;type:text"`                                  // ID from IdP
	Base
}

// TableName overrides the table name for Group
func (Group) TableName() string {
	return "groups"
}

// Role represents a set of permissions
type Role struct {
	ID          string `json:"id" gorm:"primaryKey;type:text"`
	Name        string `json:"name" gorm:"uniqueIndex;type:text;not null"`
	Description string `json:"description" gorm:"type:text"`
	Base
}

// TableName overrides the table name for Role
func (Role) TableName() string {
	return "roles"
}

// Permission represents an action or resource access
type Permission struct {
	ID          string `json:"id" gorm:"primaryKey;type:text"`
	Name        string `json:"name" gorm:"uniqueIndex;type:text;not null"`
	Description string `json:"description" gorm:"type:text"`
	Category    string `json:"category" gorm:"index;type:text"`
	Base
}

// TableName overrides the table name for Permission
func (Permission) TableName() string {
	return "permissions"
}
