package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// EnvironmentType represents the type of deployment environment
type EnvironmentType string

const (
	EnvironmentTypeKubernetes EnvironmentType = "kubernetes"
	EnvironmentTypeVM         EnvironmentType = "vm"
	EnvironmentTypeServerless EnvironmentType = "serverless"
	EnvironmentTypeContainer  EnvironmentType = "container"
)

// ProviderType represents the cloud/infrastructure provider
type ProviderType string

const (
	ProviderGKE       ProviderType = "gke"
	ProviderAKS       ProviderType = "aks"
	ProviderEKS       ProviderType = "eks"
	ProviderOpenShift ProviderType = "openshift"
	ProviderBareMetal ProviderType = "bare-metal"
	ProviderAWS       ProviderType = "aws"
	ProviderAzure     ProviderType = "azure"
	ProviderGCP       ProviderType = "gcp"
)

// AuthMethod represents authentication method for the environment
type AuthMethod string

const (
	AuthServiceAccount AuthMethod = "service-account"
	AuthOAuth          AuthMethod = "oauth"
	AuthCertificate    AuthMethod = "certificate"
	AuthToken          AuthMethod = "token"
	AuthBasic          AuthMethod = "basic"
)

// EnvironmentStatus represents the current status of an environment
type EnvironmentStatus string

const (
	EnvironmentStatusActive      EnvironmentStatus = "active"
	EnvironmentStatusInactive    EnvironmentStatus = "inactive"
	EnvironmentStatusMaintenance EnvironmentStatus = "maintenance"
	EnvironmentStatusError       EnvironmentStatus = "error"
)

// ProviderConfig holds provider-specific configuration
type ProviderConfig struct {
	// Kubernetes providers
	Cluster         string `json:"cluster,omitempty"`
	ClusterEndpoint string `json:"cluster_endpoint,omitempty"` // Kubernetes cluster endpoint
	Namespace       string `json:"namespace,omitempty"`        // Kubernetes namespace
	Zone            string `json:"zone,omitempty"`
	Region          string `json:"region,omitempty"`
	Project         string `json:"project,omitempty"`
	ResourceGroup   string `json:"resourceGroup,omitempty"`
	SubscriptionID  string `json:"subscriptionId,omitempty"`

	// Generic
	Endpoint   string     `json:"endpoint,omitempty"`
	AuthMethod AuthMethod `json:"authMethod"`

	// Auth credentials (encrypted)
	Credentials map[string]interface{} `json:"credentials,omitempty"`

	// Additional provider-specific settings
	Extra map[string]interface{} `json:"extra,omitempty"`
}

// EnvironmentProvider represents the provider configuration for an environment
type EnvironmentProvider struct {
	Type   ProviderType   `json:"type"`
	Config ProviderConfig `json:"config"`
}

// ResourceConfig defines resource limits and requests
type ResourceConfig struct {
	CPU     string `json:"cpu"`
	Memory  string `json:"memory"`
	Storage string `json:"storage"`

	// Kubernetes specific
	Replicas    *int32 `json:"replicas,omitempty"`
	MinReplicas *int32 `json:"minReplicas,omitempty"`
	MaxReplicas *int32 `json:"maxReplicas,omitempty"`

	// VM specific
	InstanceType string `json:"instanceType,omitempty"`
	DiskSize     string `json:"diskSize,omitempty"`
}

// NetworkingConfig defines networking and security settings
type NetworkingConfig struct {
	Ingress      []string `json:"ingress,omitempty"`
	LoadBalancer bool     `json:"loadBalancer,omitempty"`
	SSL          bool     `json:"ssl,omitempty"`

	// Network policies
	AllowedPorts   []int    `json:"allowedPorts,omitempty"`
	AllowedSources []string `json:"allowedSources,omitempty"`

	// DNS and domains
	CustomDomains []string `json:"customDomains,omitempty"`

	// Service mesh
	ServiceMesh bool `json:"serviceMesh,omitempty"`
}

// HealthCheckConfig defines health check settings
type HealthCheckConfig struct {
	Enabled  bool   `json:"enabled"`
	Endpoint string `json:"endpoint,omitempty"`
	Interval int    `json:"interval,omitempty"` // seconds
	Timeout  int    `json:"timeout,omitempty"`  // seconds
}

// EnvironmentVariables represents environment variables as a JSONB field
type EnvironmentVariables map[string]string

// StringSlice represents a slice of strings as a JSONB field
type StringSlice []string

// SecretMappingSlice represents a slice of SecretMapping as a JSONB field
type SecretMappingSlice []SecretMapping

// EnvironmentConfig represents a deployment environment configuration
type EnvironmentConfig struct {
	ID        string          `json:"id" gorm:"primaryKey"`
	ProjectID string          `json:"projectId" gorm:"not null;index"`
	Name      string          `json:"name" gorm:"not null"`
	Type      EnvironmentType `json:"type" gorm:"not null"`

	// Provider configuration
	Provider EnvironmentProvider `json:"provider" gorm:"type:jsonb"`

	// Resource configuration
	Resources ResourceConfig `json:"resources" gorm:"type:jsonb"`

	// Networking configuration
	Networking NetworkingConfig `json:"networking" gorm:"type:jsonb"`

	// Environment variables (non-sensitive)
	Variables EnvironmentVariables `json:"variables" gorm:"type:jsonb"`

	// Secret mappings
	SecretMappings SecretMappingSlice `json:"secretMappings" gorm:"type:jsonb"`

	// Health and monitoring
	HealthCheck HealthCheckConfig `json:"healthCheck" gorm:"type:jsonb"`

	// Deployment settings
	DeploymentStrategy string `json:"deploymentStrategy"` // rolling, blue-green, canary

	// Status and metadata
	Status      EnvironmentStatus `json:"status" gorm:"default:'inactive'"`
	Description string            `json:"description"`
	Tags        StringSlice       `json:"tags" gorm:"type:jsonb"`

	// Timestamps
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`

	// Relations - Note: No foreign key constraint as projects are managed by admin-service
	// Project validation is done via API calls to admin-service
	Project Project `json:"project,omitempty" gorm:"-"`
}

// SecretMapping defines how secrets are mapped in this environment
type SecretMapping struct {
	SecretID     string `json:"secretId"`
	VariableName string `json:"variableName"`
	MountPath    string `json:"mountPath,omitempty"`
	Format       string `json:"format,omitempty"` // env, file, volume
}

// EnvironmentHealth represents the current health status
type EnvironmentHealth struct {
	ID            string    `json:"id" gorm:"primaryKey"`
	EnvironmentID string    `json:"environmentId" gorm:"not null;index"`
	Status        string    `json:"status" gorm:"not null"` // healthy, degraded, unhealthy
	LastCheck     time.Time `json:"lastCheck" gorm:"not null"`

	// Health metrics stored as JSONB
	Metrics map[string]interface{} `json:"metrics" gorm:"type:jsonb"`

	// Issues and alerts stored as JSONB
	Issues []HealthIssue `json:"issues,omitempty" gorm:"type:jsonb"`

	// Timestamps
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

// HealthIssue represents a health problem
type HealthIssue struct {
	Type       string     `json:"type"`
	Severity   string     `json:"severity"` // low, medium, high, critical
	Message    string     `json:"message"`
	DetectedAt time.Time  `json:"detectedAt"`
	ResolvedAt *time.Time `json:"resolvedAt,omitempty"`
}

// TableName returns the table name for EnvironmentConfig
func (EnvironmentConfig) TableName() string {
	return "environment_configs"
}

// TableName returns the table name for EnvironmentHealth
func (EnvironmentHealth) TableName() string {
	return "environment_health"
}

// Value implements the driver.Valuer interface for EnvironmentProvider
func (ep EnvironmentProvider) Value() (driver.Value, error) {
	return json.Marshal(ep)
}

// Scan implements the sql.Scanner interface for EnvironmentProvider
func (ep *EnvironmentProvider) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into EnvironmentProvider", value)
	}

	return json.Unmarshal(bytes, ep)
}

// Value implements the driver.Valuer interface for ResourceConfig
func (rc ResourceConfig) Value() (driver.Value, error) {
	return json.Marshal(rc)
}

// Scan implements the sql.Scanner interface for ResourceConfig
func (rc *ResourceConfig) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into ResourceConfig", value)
	}

	return json.Unmarshal(bytes, rc)
}

// Value implements the driver.Valuer interface for NetworkingConfig
func (nc NetworkingConfig) Value() (driver.Value, error) {
	return json.Marshal(nc)
}

// Scan implements the sql.Scanner interface for NetworkingConfig
func (nc *NetworkingConfig) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into NetworkingConfig", value)
	}

	return json.Unmarshal(bytes, nc)
}

// Value implements the driver.Valuer interface for HealthCheckConfig
func (hc HealthCheckConfig) Value() (driver.Value, error) {
	return json.Marshal(hc)
}

// Scan implements the sql.Scanner interface for HealthCheckConfig
func (hc *HealthCheckConfig) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into HealthCheckConfig", value)
	}

	return json.Unmarshal(bytes, hc)
}

// Value implements the driver.Valuer interface for EnvironmentVariables
func (ev EnvironmentVariables) Value() (driver.Value, error) {
	return json.Marshal(ev)
}

// Scan implements the sql.Scanner interface for EnvironmentVariables
func (ev *EnvironmentVariables) Scan(value interface{}) error {
	if value == nil {
		*ev = make(EnvironmentVariables)
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into EnvironmentVariables", value)
	}

	return json.Unmarshal(bytes, ev)
}

// Value implements the driver.Valuer interface for StringSlice
func (ss StringSlice) Value() (driver.Value, error) {
	return json.Marshal(ss)
}

// Scan implements the sql.Scanner interface for StringSlice
func (ss *StringSlice) Scan(value interface{}) error {
	if value == nil {
		*ss = make(StringSlice, 0)
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into StringSlice", value)
	}

	return json.Unmarshal(bytes, ss)
}

// Value implements the driver.Valuer interface for SecretMappingSlice
func (sms SecretMappingSlice) Value() (driver.Value, error) {
	return json.Marshal(sms)
}

// Scan implements the sql.Scanner interface for SecretMappingSlice
func (sms *SecretMappingSlice) Scan(value interface{}) error {
	if value == nil {
		*sms = make(SecretMappingSlice, 0)
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into SecretMappingSlice", value)
	}

	// Handle empty or null JSON
	if len(bytes) == 0 || string(bytes) == "null" {
		*sms = make(SecretMappingSlice, 0)
		return nil
	}

	// Try to unmarshal as array first
	err := json.Unmarshal(bytes, sms)
	if err != nil {
		// If it fails, try to unmarshal as a single object
		var singleMapping SecretMapping
		if err2 := json.Unmarshal(bytes, &singleMapping); err2 == nil {
			// Successfully unmarshaled as single object, convert to slice
			*sms = SecretMappingSlice{singleMapping}
			return nil
		}

		// If both fail, initialize as empty slice and log the issue
		*sms = make(SecretMappingSlice, 0)
		fmt.Printf("Warning: Could not unmarshal secret_mappings JSON: %s, data: %s\n", err.Error(), string(bytes))
		return nil
	}

	return nil
}
