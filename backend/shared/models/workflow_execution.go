package models

import (
	"time"
)

// ExecutionStatus represents the status of a workflow execution
type ExecutionStatus string

const (
	ExecutionStatusPending   ExecutionStatus = "pending"
	ExecutionStatusRunning   ExecutionStatus = "running"
	ExecutionStatusSuccess   ExecutionStatus = "success"
	ExecutionStatusFailed    ExecutionStatus = "failed"
	ExecutionStatusCancelled ExecutionStatus = "cancelled"
	ExecutionStatusPaused    ExecutionStatus = "paused"
)

// StepStatus represents the status of a workflow step
type StepStatus string

const (
	StepStatusPending StepStatus = "pending"
	StepStatusRunning StepStatus = "running"
	StepStatusSuccess StepStatus = "success"
	StepStatusFailed  StepStatus = "failed"
	StepStatusSkipped StepStatus = "skipped"
)

// VersionInfo contains version and build information
type VersionInfo struct {
	Number    string `json:"number"` // e.g., "1.2.3" or "build-456"
	GitCommit string `json:"gitCommit,omitempty"`
	GitBranch string `json:"gitBranch,omitempty"`
	GitTag    string `json:"gitTag,omitempty"`
	BuildID   string `json:"buildId,omitempty"`

	// Artifact information
	Artifacts []Artifact `json:"artifacts,omitempty"`
}

// Artifact represents a build artifact
type Artifact struct {
	ID       string            `json:"id"`
	Name     string            `json:"name"`
	Type     string            `json:"type"` // docker-image, binary, package, etc.
	Location string            `json:"location"`
	Size     int64             `json:"size"`
	Checksum string            `json:"checksum"`
	Metadata map[string]string `json:"metadata,omitempty"`
}

// WorkflowExecution represents a single execution of a workflow
type WorkflowExecution struct {
	ID            string `json:"id" gorm:"primaryKey"`
	WorkflowID    string `json:"workflowId" gorm:"not null;index"`
	ProjectID     string `json:"projectId" gorm:"not null;index"`
	EnvironmentID string `json:"environmentId" gorm:"not null;index"`

	// Version information
	Version VersionInfo `json:"version" gorm:"type:jsonb"`

	// Execution details
	Status      ExecutionStatus `json:"status" gorm:"default:'pending'"`
	StartedAt   time.Time       `json:"startedAt"`
	CompletedAt *time.Time      `json:"completedAt,omitempty"`
	Duration    *int64          `json:"duration,omitempty"` // milliseconds

	// Execution context
	TriggerType string `json:"triggerType"` // manual, scheduled, webhook, promotion
	TriggerBy   string `json:"triggerBy"`   // user ID or system

	// Parameters and variables
	Parameters map[string]interface{} `json:"parameters" gorm:"type:jsonb"`
	Variables  map[string]string      `json:"variables" gorm:"type:jsonb"`

	// Step tracking
	Steps []WorkflowStepExecution `json:"steps" gorm:"type:jsonb"`

	// Deployment results
	DeployedServices []DeployedService `json:"deployedServices" gorm:"type:jsonb"`

	// Logs and monitoring
	LogStreamID    string `json:"logStreamId"`
	MetricsEnabled bool   `json:"metricsEnabled"`

	// Error information
	ErrorMessage string `json:"errorMessage,omitempty"`
	ErrorDetails string `json:"errorDetails,omitempty"`

	// Timestamps
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`

	// Relations
	Workflow    Workflow          `json:"workflow,omitempty" gorm:"foreignKey:WorkflowID"`
	Project     Project           `json:"project,omitempty" gorm:"foreignKey:ProjectID"`
	Environment EnvironmentConfig `json:"environment,omitempty" gorm:"foreignKey:EnvironmentID"`
}

// WorkflowStepExecution represents the execution of a single workflow step
type WorkflowStepExecution struct {
	ID       string     `json:"id"`
	StepName string     `json:"stepName"`
	StepType string     `json:"stepType"` // deploy, test, script, approval, etc.
	Status   StepStatus `json:"status"`

	// Timing
	StartedAt   *time.Time `json:"startedAt,omitempty"`
	CompletedAt *time.Time `json:"completedAt,omitempty"`
	Duration    *int64     `json:"duration,omitempty"` // milliseconds

	// Step configuration
	Config map[string]interface{} `json:"config,omitempty"`

	// Execution results
	Output  map[string]interface{} `json:"output,omitempty"`
	Logs    []LogEntry             `json:"logs,omitempty"`
	Metrics map[string]float64     `json:"metrics,omitempty"`

	// Artifacts produced by this step
	Artifacts []Artifact `json:"artifacts,omitempty"`

	// Error information
	ErrorMessage string `json:"errorMessage,omitempty"`
	ErrorDetails string `json:"errorDetails,omitempty"`

	// Retry information
	RetryCount     int  `json:"retryCount"`
	MaxRetries     int  `json:"maxRetries"`
	RetryOnFailure bool `json:"retryOnFailure"`
}

// DeployedService represents a service deployed by the workflow
type DeployedService struct {
	Name      string            `json:"name"`
	Type      string            `json:"type"` // kubernetes, vm, serverless
	Version   string            `json:"version"`
	Status    string            `json:"status"`
	Endpoints []ServiceEndpoint `json:"endpoints,omitempty"`

	// Resource information
	Resources struct {
		CPU      string `json:"cpu,omitempty"`
		Memory   string `json:"memory,omitempty"`
		Replicas int    `json:"replicas,omitempty"`
	} `json:"resources"`

	// Health check
	HealthCheck struct {
		Status    string    `json:"status"`
		LastCheck time.Time `json:"lastCheck"`
		Endpoint  string    `json:"endpoint,omitempty"`
	} `json:"healthCheck"`

	// Metadata
	Labels      map[string]string `json:"labels,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`
}

// ServiceEndpoint represents an endpoint exposed by a deployed service
type ServiceEndpoint struct {
	Name     string `json:"name"`
	URL      string `json:"url"`
	Type     string `json:"type"` // http, https, grpc, tcp
	Port     int    `json:"port"`
	Protocol string `json:"protocol"`
	Public   bool   `json:"public"`
}

// LogEntry represents a single log entry from workflow execution
type LogEntry struct {
	ID        string    `json:"id"`
	Timestamp time.Time `json:"timestamp"`
	Level     string    `json:"level"` // debug, info, warn, error
	StepName  string    `json:"stepName"`
	Message   string    `json:"message"`

	// Additional context
	Source   string                 `json:"source,omitempty"`   // which component generated the log
	Context  map[string]interface{} `json:"context,omitempty"`  // additional context data
	Progress *int                   `json:"progress,omitempty"` // 0-100 for progress indication

	// UI hints
	IsNew       bool `json:"isNew,omitempty"`       // for highlighting new logs
	IsImportant bool `json:"isImportant,omitempty"` // for highlighting important logs
}

// ExecutionSummary provides a summary view of workflow execution
type ExecutionSummary struct {
	ID           string          `json:"id"`
	WorkflowName string          `json:"workflowName"`
	ProjectName  string          `json:"projectName"`
	Environment  string          `json:"environment"`
	Version      string          `json:"version"`
	Status       ExecutionStatus `json:"status"`
	StartedAt    time.Time       `json:"startedAt"`
	Duration     *int64          `json:"duration,omitempty"`
	TriggerBy    string          `json:"triggerBy"`

	// Step summary
	TotalSteps     int `json:"totalSteps"`
	CompletedSteps int `json:"completedSteps"`
	FailedSteps    int `json:"failedSteps"`

	// Deployment summary
	ServicesDeployed int `json:"servicesDeployed"`

	// Health status
	HealthStatus string `json:"healthStatus,omitempty"`
}

// TableName returns the table name for WorkflowExecution
func (WorkflowExecution) TableName() string {
	return "workflow_executions"
}
