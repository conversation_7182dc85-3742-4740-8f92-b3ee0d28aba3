package testing

import (
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/auth"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestAuthManager provides a test authentication manager with known credentials
type TestAuthManager struct {
	*auth.AuthManager
	TestJWTSecret string
	TestUsers     map[string]TestUser
}

// TestUser represents a test user with known credentials
type TestUser struct {
	ID       string   `json:"id"`
	Username string   `json:"username"`
	Email    string   `json:"email"`
	Roles    []string `json:"roles"`
}

// NewTestAuthManager creates a new test authentication manager
func NewTestAuthManager(t *testing.T) *TestAuthManager {
	testSecret := "test-secret-key-for-testing-only"

	config := &auth.Config{
		JWTSecretKey:       testSecret,
		AccessTokenExpiry:  time.Hour,
		RefreshTokenExpiry: time.Hour * 24,
		AdminServiceURL:    "http://localhost:8080", // Mock admin service
	}

	authManager, err := auth.NewAuthManager(config)
	require.NoError(t, err, "Failed to create test auth manager")

	testUsers := map[string]TestUser{
		"admin": {
			ID:       "admin-user-id",
			Username: "admin",
			Email:    "<EMAIL>",
			Roles:    []string{"admin"},
		},
		"user": {
			ID:       "regular-user-id",
			Username: "user",
			Email:    "<EMAIL>",
			Roles:    []string{"user"},
		},
		"developer": {
			ID:       "developer-user-id",
			Username: "developer",
			Email:    "<EMAIL>",
			Roles:    []string{"user", "developer"},
		},
		"moderator": {
			ID:       "moderator-user-id",
			Username: "moderator",
			Email:    "<EMAIL>",
			Roles:    []string{"user", "moderator"},
		},
	}

	return &TestAuthManager{
		AuthManager:   authManager,
		TestJWTSecret: testSecret,
		TestUsers:     testUsers,
	}
}

// GenerateTestToken generates a test JWT token for a given user
func (tam *TestAuthManager) GenerateTestToken(userKey string) (string, error) {
	user, exists := tam.TestUsers[userKey]
	if !exists {
		return "", assert.AnError
	}

	return tam.JWTManager.GenerateAccessToken(user.ID, user.Username, user.Email, user.Roles)
}

// CreateAuthenticatedRequest creates an HTTP request with authentication header
func (tam *TestAuthManager) CreateAuthenticatedRequest(t *testing.T, method, url, userKey string) *http.Request {
	token, err := tam.GenerateTestToken(userKey)
	require.NoError(t, err, "Failed to generate test token")

	req, err := http.NewRequest(method, url, nil)
	require.NoError(t, err, "Failed to create request")

	req.Header.Set("Authorization", "Bearer "+token)
	return req
}

// CreateUnauthenticatedRequest creates an HTTP request without authentication
func (tam *TestAuthManager) CreateUnauthenticatedRequest(t *testing.T, method, url string) *http.Request {
	req, err := http.NewRequest(method, url, nil)
	require.NoError(t, err, "Failed to create request")
	return req
}

// CreateInvalidTokenRequest creates an HTTP request with an invalid token
func (tam *TestAuthManager) CreateInvalidTokenRequest(t *testing.T, method, url string) *http.Request {
	req, err := http.NewRequest(method, url, nil)
	require.NoError(t, err, "Failed to create request")

	req.Header.Set("Authorization", "Bearer invalid-token")
	return req
}

// TestAuthMiddleware tests the authentication middleware
func (tam *TestAuthManager) TestAuthMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add auth middleware
	router.Use(tam.AuthMiddleware())

	// Add test endpoint
	router.GET("/protected", func(c *gin.Context) {
		userID, _ := auth.GetUserIDFromContext(c)
		c.JSON(http.StatusOK, gin.H{"userID": userID})
	})

	t.Run("Valid Token", func(t *testing.T) {
		req := tam.CreateAuthenticatedRequest(t, "GET", "/protected", "user")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "regular-user-id")
	})

	t.Run("No Token", func(t *testing.T) {
		req := tam.CreateUnauthenticatedRequest(t, "GET", "/protected")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("Invalid Token", func(t *testing.T) {
		req := tam.CreateInvalidTokenRequest(t, "GET", "/protected")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// TestAdminMiddleware tests the admin middleware
func (tam *TestAuthManager) TestAdminMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add auth and admin middleware
	router.Use(tam.AuthMiddleware())
	router.Use(tam.AdminMiddleware())

	// Add test endpoint
	router.GET("/admin", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "admin access granted"})
	})

	t.Run("Admin User", func(t *testing.T) {
		req := tam.CreateAuthenticatedRequest(t, "GET", "/admin", "admin")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Regular User", func(t *testing.T) {
		req := tam.CreateAuthenticatedRequest(t, "GET", "/admin", "user")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)
	})
}

// TestRoleMiddleware tests the role-based middleware
func (tam *TestAuthManager) TestRoleMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add auth and role middleware
	router.Use(tam.AuthMiddleware())
	router.Use(tam.RequireRoleMiddleware("developer", "moderator"))

	// Add test endpoint
	router.GET("/dev", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "developer access granted"})
	})

	t.Run("Developer User", func(t *testing.T) {
		req := tam.CreateAuthenticatedRequest(t, "GET", "/dev", "developer")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Moderator User", func(t *testing.T) {
		req := tam.CreateAuthenticatedRequest(t, "GET", "/dev", "moderator")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Regular User", func(t *testing.T) {
		req := tam.CreateAuthenticatedRequest(t, "GET", "/dev", "user")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)
	})
}

// AssertAuthenticationRequired is a helper to test that an endpoint requires authentication
func (tam *TestAuthManager) AssertAuthenticationRequired(t *testing.T, router *gin.Engine, method, path string) {
	req := tam.CreateUnauthenticatedRequest(t, method, path)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusUnauthorized, w.Code, "Endpoint should require authentication")
}

// AssertAdminRequired is a helper to test that an endpoint requires admin access
func (tam *TestAuthManager) AssertAdminRequired(t *testing.T, router *gin.Engine, method, path string) {
	// Test with regular user
	req := tam.CreateAuthenticatedRequest(t, method, path, "user")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusForbidden, w.Code, "Endpoint should require admin access")

	// Test with admin user
	req = tam.CreateAuthenticatedRequest(t, method, path, "admin")
	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.NotEqual(t, http.StatusForbidden, w.Code, "Admin should have access")
}

// AssertRoleRequired is a helper to test that an endpoint requires specific roles
func (tam *TestAuthManager) AssertRoleRequired(t *testing.T, router *gin.Engine, method, path string, allowedRoles []string) {
	// Test with user that doesn't have required role
	req := tam.CreateAuthenticatedRequest(t, method, path, "user")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusForbidden, w.Code, "Endpoint should require specific role")

	// Test with users that have required roles
	for _, role := range allowedRoles {
		if userKey, exists := tam.findUserWithRole(role); exists {
			req = tam.CreateAuthenticatedRequest(t, method, path, userKey)
			w = httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.NotEqual(t, http.StatusForbidden, w.Code, "User with role %s should have access", role)
		}
	}
}

// findUserWithRole finds a test user that has the specified role
func (tam *TestAuthManager) findUserWithRole(role string) (string, bool) {
	for userKey, user := range tam.TestUsers {
		for _, userRole := range user.Roles {
			if userRole == role {
				return userKey, true
			}
		}
	}
	return "", false
}
