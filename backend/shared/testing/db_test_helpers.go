package testing

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// TestDB provides utilities for testing with databases
type TestDB struct {
	DB      *gorm.DB
	cleanup func()
}

// NewTestDB creates a new in-memory SQLite database for testing
func NewTestDB(t *testing.T) *TestDB {
	// Create in-memory SQLite database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // Reduce noise in tests
	})
	require.NoError(t, err, "Failed to create test database")

	// Get underlying SQL DB for cleanup
	sqlDB, err := db.DB()
	require.NoError(t, err, "Failed to get underlying SQL DB")

	return &TestDB{
		DB: db,
		cleanup: func() {
			sqlDB.Close()
		},
	}
}

// Close closes the test database
func (tdb *TestDB) Close() {
	if tdb.cleanup != nil {
		tdb.cleanup()
	}
}

// AutoMigrate runs auto-migration for the given models
func (tdb *TestDB) AutoMigrate(t *testing.T, models ...interface{}) {
	err := tdb.DB.AutoMigrate(models...)
	require.NoError(t, err, "Failed to auto-migrate test database")
}

// Seed populates the database with test data
func (tdb *TestDB) Seed(t *testing.T, data ...interface{}) {
	for _, item := range data {
		err := tdb.DB.Create(item).Error
		require.NoError(t, err, "Failed to seed test data: %+v", item)
	}
}

// Clear removes all data from specified tables
func (tdb *TestDB) Clear(t *testing.T, models ...interface{}) {
	for _, model := range models {
		err := tdb.DB.Unscoped().Where("1 = 1").Delete(model).Error
		require.NoError(t, err, "Failed to clear test data for model: %T", model)
	}
}

// AssertCount asserts that a table has the expected number of records
func (tdb *TestDB) AssertCount(t *testing.T, model interface{}, expectedCount int64) {
	var count int64
	err := tdb.DB.Model(model).Count(&count).Error
	require.NoError(t, err, "Failed to count records")
	require.Equal(t, expectedCount, count, "Unexpected record count")
}

// AssertExists asserts that a record exists with the given conditions
func (tdb *TestDB) AssertExists(t *testing.T, model interface{}, conditions ...interface{}) {
	err := tdb.DB.Where(conditions[0], conditions[1:]...).First(model).Error
	require.NoError(t, err, "Expected record not found")
}

// AssertNotExists asserts that no record exists with the given conditions
func (tdb *TestDB) AssertNotExists(t *testing.T, model interface{}, conditions ...interface{}) {
	err := tdb.DB.Where(conditions[0], conditions[1:]...).First(model).Error
	require.Error(t, err, "Record should not exist")
	require.Equal(t, gorm.ErrRecordNotFound, err, "Expected record not found error")
}

// Transaction runs a function within a database transaction and rolls it back
func (tdb *TestDB) Transaction(t *testing.T, fn func(*gorm.DB)) {
	tx := tdb.DB.Begin()
	require.NoError(t, tx.Error, "Failed to begin transaction")

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
		tx.Rollback()
	}()

	fn(tx)
}

// TestDataBuilder helps build test data with relationships
type TestDataBuilder struct {
	db   *gorm.DB
	data map[string]interface{}
}

// NewTestDataBuilder creates a new test data builder
func (tdb *TestDB) NewDataBuilder() *TestDataBuilder {
	return &TestDataBuilder{
		db:   tdb.DB,
		data: make(map[string]interface{}),
	}
}

// Create creates a record and stores it with the given key
func (tdb *TestDataBuilder) Create(t *testing.T, key string, model interface{}) *TestDataBuilder {
	err := tdb.db.Create(model).Error
	require.NoError(t, err, "Failed to create test data for key: %s", key)
	tdb.data[key] = model
	return tdb
}

// Get retrieves a previously created record by key
func (tdb *TestDataBuilder) Get(key string) interface{} {
	return tdb.data[key]
}

// GetAs retrieves a previously created record by key and casts it to the specified type
func (tdb *TestDataBuilder) GetAs(key string, target interface{}) bool {
	if value, exists := tdb.data[key]; exists {
		// This is a simple type assertion - in a real implementation,
		// you might want to use reflection for more robust type conversion
		switch v := value.(type) {
		case interface{}:
			if typed, ok := v.(interface{}); ok {
				*target.(*interface{}) = typed
				return true
			}
		}
	}
	return false
}

// DatabaseTestSuite provides a complete test suite for database operations
type DatabaseTestSuite struct {
	TestDB *TestDB
	Models []interface{}
}

// NewDatabaseTestSuite creates a new database test suite
func NewDatabaseTestSuite(t *testing.T, models ...interface{}) *DatabaseTestSuite {
	testDB := NewTestDB(t)
	testDB.AutoMigrate(t, models...)

	return &DatabaseTestSuite{
		TestDB: testDB,
		Models: models,
	}
}

// Setup prepares the database for testing
func (dts *DatabaseTestSuite) Setup(t *testing.T) {
	// Clear all tables
	dts.TestDB.Clear(t, dts.Models...)
}

// Teardown cleans up after testing
func (dts *DatabaseTestSuite) Teardown(t *testing.T) {
	dts.TestDB.Close()
}

// RunInTransaction runs a test function within a transaction that gets rolled back
func (dts *DatabaseTestSuite) RunInTransaction(t *testing.T, testFunc func(*testing.T, *gorm.DB)) {
	dts.TestDB.Transaction(t, func(tx *gorm.DB) {
		testFunc(t, tx)
	})
}

// MockPermissionService provides a mock implementation for testing
type MockPermissionService struct {
	Permissions   map[string]map[string]bool // userID -> permission -> allowed
	ProjectAccess map[string][]string        // userID -> []projectIDs
}

// NewMockPermissionService creates a new mock permission service
func NewMockPermissionService() *MockPermissionService {
	return &MockPermissionService{
		Permissions:   make(map[string]map[string]bool),
		ProjectAccess: make(map[string][]string),
	}
}

// SetPermission sets a permission for a user
func (mps *MockPermissionService) SetPermission(userID, permission string, allowed bool) {
	if mps.Permissions[userID] == nil {
		mps.Permissions[userID] = make(map[string]bool)
	}
	mps.Permissions[userID][permission] = allowed
}

// SetProjectAccess sets project access for a user
func (mps *MockPermissionService) SetProjectAccess(userID string, projectIDs []string) {
	mps.ProjectAccess[userID] = projectIDs
}

// CheckPermission implements the PermissionService interface
func (mps *MockPermissionService) CheckPermission(ctx interface{}, userID, permissionName, projectID string) (bool, error) {
	if userPerms, exists := mps.Permissions[userID]; exists {
		// Check specific permission
		if allowed, exists := userPerms[permissionName]; exists {
			return allowed, nil
		}

		// Check project-specific permission
		projectPermission := fmt.Sprintf("%s:%s", projectID, permissionName)
		if allowed, exists := userPerms[projectPermission]; exists {
			return allowed, nil
		}
	}

	return false, nil
}

// CheckUserHasProjectAccess implements the PermissionService interface
func (mps *MockPermissionService) CheckUserHasProjectAccess(ctx interface{}, userID, projectID string) (bool, error) {
	if projects, exists := mps.ProjectAccess[userID]; exists {
		for _, pid := range projects {
			if pid == projectID {
				return true, nil
			}
		}
	}
	return false, nil
}

// TestWithMockPermissions creates a test helper that sets up mock permissions
func TestWithMockPermissions(userID string, permissions map[string]bool, projectAccess []string) func(*MockPermissionService) {
	return func(mps *MockPermissionService) {
		for permission, allowed := range permissions {
			mps.SetPermission(userID, permission, allowed)
		}
		if len(projectAccess) > 0 {
			mps.SetProjectAccess(userID, projectAccess)
		}
	}
}
