package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestServer provides utilities for testing HTTP endpoints
type TestServer struct {
	Router *gin.Engine
	Auth   *TestAuthManager
}

// NewTestServer creates a new test server with authentication
func NewTestServer(t *testing.T) *TestServer {
	gin.SetMode(gin.TestMode)

	return &TestServer{
		Router: gin.New(),
		Auth:   NewTestAuthManager(t),
	}
}

// SetupBasicAuth sets up basic authentication middleware on the router
func (ts *TestServer) SetupBasicAuth() {
	ts.Router.Use(ts.Auth.AuthMiddleware())
}

// SetupCORS sets up CORS middleware on the router
func (ts *TestServer) SetupCORS() {
	ts.Router.Use(ts.Auth.CORSMiddleware())
}

// JSONRequest represents a JSON request for testing
type JSONRequest struct {
	Method  string
	Path    string
	Body    interface{}
	Headers map[string]string
	UserKey string // For authenticated requests
}

// JSONResponse represents a JSON response for testing
type JSONResponse struct {
	StatusCode int
	Body       map[string]interface{}
	Headers    map[string]string
}

// MakeJSONRequest makes a JSON request to the test server
func (ts *TestServer) MakeJSONRequest(t *testing.T, req JSONRequest) *JSONResponse {
	var bodyReader *bytes.Reader

	if req.Body != nil {
		bodyBytes, err := json.Marshal(req.Body)
		require.NoError(t, err, "Failed to marshal request body")
		bodyReader = bytes.NewReader(bodyBytes)
	} else {
		bodyReader = bytes.NewReader([]byte{})
	}

	httpReq, err := http.NewRequest(req.Method, req.Path, bodyReader)
	require.NoError(t, err, "Failed to create HTTP request")

	// Set content type for JSON requests
	if req.Body != nil {
		httpReq.Header.Set("Content-Type", "application/json")
	}

	// Add custom headers
	for key, value := range req.Headers {
		httpReq.Header.Set(key, value)
	}

	// Add authentication if user key is provided
	if req.UserKey != "" {
		token, err := ts.Auth.GenerateTestToken(req.UserKey)
		require.NoError(t, err, "Failed to generate test token")
		httpReq.Header.Set("Authorization", "Bearer "+token)
	}

	// Make the request
	w := httptest.NewRecorder()
	ts.Router.ServeHTTP(w, httpReq)

	// Parse response
	response := &JSONResponse{
		StatusCode: w.Code,
		Headers:    make(map[string]string),
	}

	// Copy response headers
	for key, values := range w.Header() {
		if len(values) > 0 {
			response.Headers[key] = values[0]
		}
	}

	// Parse JSON body if present
	if w.Body.Len() > 0 {
		err := json.Unmarshal(w.Body.Bytes(), &response.Body)
		if err != nil {
			// If JSON parsing fails, store raw body as string
			response.Body = map[string]interface{}{
				"raw": w.Body.String(),
			}
		}
	}

	return response
}

// AssertJSONResponse asserts that a JSON response matches expectations
func (ts *TestServer) AssertJSONResponse(t *testing.T, response *JSONResponse, expectedStatus int, expectedBody map[string]interface{}) {
	assert.Equal(t, expectedStatus, response.StatusCode, "Unexpected status code")

	if expectedBody != nil {
		for key, expectedValue := range expectedBody {
			actualValue, exists := response.Body[key]
			assert.True(t, exists, "Expected key '%s' not found in response", key)
			assert.Equal(t, expectedValue, actualValue, "Unexpected value for key '%s'", key)
		}
	}
}

// TestEndpoint represents a test case for an endpoint
type TestEndpoint struct {
	Name           string
	Request        JSONRequest
	ExpectedStatus int
	ExpectedBody   map[string]interface{}
	Setup          func(t *testing.T, ts *TestServer) // Optional setup function
	Cleanup        func(t *testing.T, ts *TestServer) // Optional cleanup function
}

// RunEndpointTests runs a series of endpoint tests
func (ts *TestServer) RunEndpointTests(t *testing.T, tests []TestEndpoint) {
	for _, test := range tests {
		t.Run(test.Name, func(t *testing.T) {
			// Run setup if provided
			if test.Setup != nil {
				test.Setup(t, ts)
			}

			// Make the request
			response := ts.MakeJSONRequest(t, test.Request)

			// Assert the response
			ts.AssertJSONResponse(t, response, test.ExpectedStatus, test.ExpectedBody)

			// Run cleanup if provided
			if test.Cleanup != nil {
				test.Cleanup(t, ts)
			}
		})
	}
}

// HealthCheckTest creates a standard health check test
func HealthCheckTest() TestEndpoint {
	return TestEndpoint{
		Name: "Health Check",
		Request: JSONRequest{
			Method: "GET",
			Path:   "/health",
		},
		ExpectedStatus: http.StatusOK,
		ExpectedBody: map[string]interface{}{
			"status": "healthy",
		},
	}
}

// AuthenticationTests creates standard authentication tests
func AuthenticationTests(protectedPath string) []TestEndpoint {
	return []TestEndpoint{
		{
			Name: "No Authentication - Should Fail",
			Request: JSONRequest{
				Method: "GET",
				Path:   protectedPath,
			},
			ExpectedStatus: http.StatusUnauthorized,
		},
		{
			Name: "Invalid Token - Should Fail",
			Request: JSONRequest{
				Method: "GET",
				Path:   protectedPath,
				Headers: map[string]string{
					"Authorization": "Bearer invalid-token",
				},
			},
			ExpectedStatus: http.StatusUnauthorized,
		},
		{
			Name: "Valid Token - Should Succeed",
			Request: JSONRequest{
				Method:  "GET",
				Path:    protectedPath,
				UserKey: "user",
			},
			ExpectedStatus: http.StatusOK,
		},
	}
}

// AdminAuthorizationTests creates standard admin authorization tests
func AdminAuthorizationTests(adminPath string) []TestEndpoint {
	return []TestEndpoint{
		{
			Name: "Regular User - Should Fail",
			Request: JSONRequest{
				Method:  "GET",
				Path:    adminPath,
				UserKey: "user",
			},
			ExpectedStatus: http.StatusForbidden,
		},
		{
			Name: "Admin User - Should Succeed",
			Request: JSONRequest{
				Method:  "GET",
				Path:    adminPath,
				UserKey: "admin",
			},
			ExpectedStatus: http.StatusOK,
		},
	}
}

// CRUDTests creates standard CRUD operation tests
func CRUDTests(basePath string, sampleData interface{}) []TestEndpoint {
	return []TestEndpoint{
		{
			Name: "Create Resource",
			Request: JSONRequest{
				Method:  "POST",
				Path:    basePath,
				Body:    sampleData,
				UserKey: "user",
			},
			ExpectedStatus: http.StatusCreated,
		},
		{
			Name: "List Resources",
			Request: JSONRequest{
				Method:  "GET",
				Path:    basePath,
				UserKey: "user",
			},
			ExpectedStatus: http.StatusOK,
		},
		{
			Name: "Get Resource",
			Request: JSONRequest{
				Method:  "GET",
				Path:    basePath + "/test-id",
				UserKey: "user",
			},
			ExpectedStatus: http.StatusOK,
		},
		{
			Name: "Update Resource",
			Request: JSONRequest{
				Method:  "PUT",
				Path:    basePath + "/test-id",
				Body:    sampleData,
				UserKey: "user",
			},
			ExpectedStatus: http.StatusOK,
		},
		{
			Name: "Delete Resource",
			Request: JSONRequest{
				Method:  "DELETE",
				Path:    basePath + "/test-id",
				UserKey: "user",
			},
			ExpectedStatus: http.StatusOK,
		},
	}
}

// AssertErrorResponse asserts that a response contains an error
func AssertErrorResponse(t *testing.T, response *JSONResponse, expectedStatus int, expectedErrorMessage string) {
	assert.Equal(t, expectedStatus, response.StatusCode, "Unexpected status code")

	if expectedErrorMessage != "" {
		errorMsg, exists := response.Body["error"]
		assert.True(t, exists, "Expected error message not found")
		assert.Contains(t, fmt.Sprintf("%v", errorMsg), expectedErrorMessage, "Unexpected error message")
	}
}

// AssertSuccessResponse asserts that a response indicates success
func AssertSuccessResponse(t *testing.T, response *JSONResponse, expectedStatus int) {
	assert.Equal(t, expectedStatus, response.StatusCode, "Unexpected status code")
	assert.True(t, response.StatusCode >= 200 && response.StatusCode < 300, "Response should indicate success")
}
