# Structured Logging for Deploy Orchestrator

This package provides a structured logging solution for the Deploy Orchestrator services using the Zap logging library.

## Features

- Log levels (debug, info, warn, error, fatal)
- Structured logging with fields
- JSON and console output formats
- Request ID tracking
- Context-aware logging
- Integration with Gin middleware
- Integration with GORM

## Usage

### Basic Usage

```go
import "github.com/claudio/deploy-orchestrator/shared/logging"

// Get the default logger
logger := logging.Default()

// Log messages with different levels
logger.Debug("Debug message")
logger.Info("Info message")
logger.Warn("Warning message")
logger.Error("Error message", logging.Error(err))

// Log with structured fields
logger.Info("User logged in",
    logging.String("userId", "123"),
    logging.String("username", "john.doe"),
    logging.Int("loginAttempts", 1),
)

// Create a named logger for a specific component
dbLogger := logger.Named("database")
dbLogger.Info("Connected to database")
```

### Middleware Integration

```go
import (
    "github.com/claudio/deploy-orchestrator/shared/logging"
    "github.com/gin-gonic/gin"
)

func SetupRouter() *gin.Engine {
    // Initialize logger
    logger := logging.Default()
    
    // Create router
    router := gin.New()
    
    // Use logging middleware
    router.Use(logging.Middleware(logger))
    router.Use(logging.ErrorHandler(logger))
    
    // Add routes
    router.GET("/", func(c *gin.Context) {
        // Get logger from context
        reqLogger := logging.FromGinContext(c)
        reqLogger.Info("Handling request")
        
        c.JSON(200, gin.H{"message": "Hello, world!"})
    })
    
    return router
}
```

### Database Integration

```go
import (
    "github.com/claudio/deploy-orchestrator/shared/config"
    "github.com/claudio/deploy-orchestrator/shared/db"
    "github.com/claudio/deploy-orchestrator/shared/logging"
)

func InitDatabase(cfg config.DBConfig) (*gorm.DB, error) {
    // Initialize logger
    logger := logging.Default().Named("db")
    
    // Create database config
    dbConfig := db.Config{
        URL:           cfg.URL,
        MaxRetries:    cfg.MaxRetries,
        RetryInterval: time.Duration(cfg.RetryInterval) * time.Second,
        LogLevel:      cfg.LogLevel,
    }
    
    // Connect to database
    return db.Connect(dbConfig)
}
```

## Configuration

The logging system can be configured using the `LoggingConfig` struct:

```go
import (
    "github.com/claudio/deploy-orchestrator/shared/config"
    "github.com/claudio/deploy-orchestrator/shared/logging"
)

func main() {
    // Configure logging
    cfg := config.LoggingConfig{
        Level:      "debug",  // debug, info, warn, error, fatal
        Format:     "json",   // json or console
        Output:     "stdout", // stdout, stderr, or file path
        TimeFormat: "2006-01-02T15:04:05Z07:00",
    }
    
    // Initialize logger
    logging.InitLogger(cfg)
    
    // Use logger
    logger := logging.Default()
    logger.Info("Application started")
}
```

## Best Practices

1. Use structured logging with fields instead of string formatting
2. Use appropriate log levels
3. Add context to logs (request ID, user ID, etc.)
4. Use named loggers for different components
5. Configure log level based on environment (debug in development, info in production)
