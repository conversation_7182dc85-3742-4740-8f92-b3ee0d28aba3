package logging

import (
	"github.com/claudio/deploy-orchestrator/shared/config"
)

// InitLogger initializes the default logger with the given configuration
func InitLogger(cfg config.LoggingConfig) {
	defaultLogger = NewLogger(cfg)
}

// Fields is a map of field names to values
type Fields map[string]interface{}

// WithFields creates a logger with the given fields
func WithFields(fields Fields) Logger {
	logger := Default()
	if len(fields) == 0 {
		return logger
	}

	zapFields := make([]Field, 0, len(fields))
	for k, v := range fields {
		zapFields = append(zapFields, Any(k, v))
	}

	return logger.With(zapFields...)
}
