// Package logging provides a structured logging implementation for the deploy-orchestrator services
package logging

import (
	"context"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"github.com/claudio/deploy-orchestrator/shared/config"
)

// Field represents a structured log field
type Field = zapcore.Field

// Logger is the interface that wraps the basic logging methods
type Logger interface {
	Debug(msg string, fields ...Field)
	Info(msg string, fields ...Field)
	Warn(msg string, fields ...Field)
	Error(msg string, fields ...Field)
	Fatal(msg string, fields ...Field)
	With(fields ...Field) Logger
	WithContext(ctx context.Context) Logger
	Named(name string) Logger
	Sync() error
}

// zapLogger implements the Logger interface using zap
type zapLogger struct {
	logger *zap.Logger
}

// contextKey is the key used to store the logger in the context
type contextKey struct{}

var (
	// loggerKey is the key used to store the logger in the context
	loggerKey = contextKey{}
	// defaultLogger is the default logger instance
	defaultLogger Logger
)

// init initializes the default logger
func init() {
	defaultLogger = NewLogger(config.LoggingConfig{
		Level:      "info",
		Format:     "json",
		Output:     "stdout",
		TimeFormat: time.RFC3339,
	})
}

// NewLogger creates a new logger with the given configuration
func NewLogger(cfg config.LoggingConfig) Logger {
	// Parse log level
	level := zap.InfoLevel
	switch cfg.Level {
	case "debug":
		level = zap.DebugLevel
	case "info":
		level = zap.InfoLevel
	case "warn":
		level = zap.WarnLevel
	case "error":
		level = zap.ErrorLevel
	case "fatal":
		level = zap.FatalLevel
	}

	// Configure encoder
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.TimeKey = "timestamp"
	encoderConfig.EncodeTime = func(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
		enc.AppendString(t.Format(cfg.TimeFormat))
	}
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	// Configure output
	var output zapcore.WriteSyncer
	switch cfg.Output {
	case "stdout":
		output = zapcore.AddSync(os.Stdout)
	case "stderr":
		output = zapcore.AddSync(os.Stderr)
	default:
		// Assume it's a file path
		file, err := os.OpenFile(cfg.Output, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err != nil {
			// Fall back to stdout if file can't be opened
			output = zapcore.AddSync(os.Stdout)
		} else {
			output = zapcore.AddSync(file)
		}
	}

	// Configure encoder format
	var encoder zapcore.Encoder
	if cfg.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// Create core
	core := zapcore.NewCore(encoder, output, level)

	// Create logger
	logger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1), zap.AddStacktrace(zap.ErrorLevel))

	return &zapLogger{logger: logger}
}

// Debug logs a message at debug level
func (l *zapLogger) Debug(msg string, fields ...Field) {
	l.logger.Debug(msg, fields...)
}

// Info logs a message at info level
func (l *zapLogger) Info(msg string, fields ...Field) {
	l.logger.Info(msg, fields...)
}

// Warn logs a message at warn level
func (l *zapLogger) Warn(msg string, fields ...Field) {
	l.logger.Warn(msg, fields...)
}

// Error logs a message at error level
func (l *zapLogger) Error(msg string, fields ...Field) {
	l.logger.Error(msg, fields...)
}

// Fatal logs a message at fatal level and then calls os.Exit(1)
func (l *zapLogger) Fatal(msg string, fields ...Field) {
	l.logger.Fatal(msg, fields...)
}

// With returns a logger with the given fields added to the context
func (l *zapLogger) With(fields ...Field) Logger {
	return &zapLogger{logger: l.logger.With(fields...)}
}

// WithContext returns a logger with the given context
func (l *zapLogger) WithContext(ctx context.Context) Logger {
	if ctx == nil {
		return l
	}

	if ctxLogger, ok := ctx.Value(loggerKey).(Logger); ok {
		return ctxLogger
	}

	return l
}

// Named returns a logger with the given name
func (l *zapLogger) Named(name string) Logger {
	return &zapLogger{logger: l.logger.Named(name)}
}

// Sync flushes any buffered log entries
func (l *zapLogger) Sync() error {
	return l.logger.Sync()
}

// Default returns the default logger
func Default() Logger {
	return defaultLogger
}

// WithContext returns a context with the logger attached
func WithContext(ctx context.Context, logger Logger) context.Context {
	return context.WithValue(ctx, loggerKey, logger)
}

// FromContext returns the logger from the context
func FromContext(ctx context.Context) Logger {
	if ctx == nil {
		return defaultLogger
	}

	if logger, ok := ctx.Value(loggerKey).(Logger); ok {
		return logger
	}

	return defaultLogger
}

// FromGinContext returns the logger from the gin context
func FromGinContext(c *gin.Context) Logger {
	return FromContext(c.Request.Context())
}

// String creates a field with a string value
func String(key, val string) Field {
	return zap.String(key, val)
}

// Int creates a field with an int value
func Int(key string, val int) Field {
	return zap.Int(key, val)
}

// Int64 creates a field with an int64 value
func Int64(key string, val int64) Field {
	return zap.Int64(key, val)
}

// Float64 creates a field with a float64 value
func Float64(key string, val float64) Field {
	return zap.Float64(key, val)
}

// Bool creates a field with a bool value
func Bool(key string, val bool) Field {
	return zap.Bool(key, val)
}

// Error creates a field with an error value
func Error(err error) Field {
	return zap.Error(err)
}

// Duration creates a field with a duration value
func Duration(key string, val time.Duration) Field {
	return zap.Duration(key, val)
}

// Any creates a field with any value
func Any(key string, val interface{}) Field {
	return zap.Any(key, val)
}
