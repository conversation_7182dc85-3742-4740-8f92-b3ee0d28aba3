package logging

import (
	"context"
	"encoding/json"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// SecretAuditEvent represents a secret access audit event
type SecretAuditEvent struct {
	Timestamp     time.Time              `json:"timestamp"`
	EventType     string                 `json:"event_type"`
	UserID        string                 `json:"user_id"`
	Username      string                 `json:"username"`
	ProjectID     string                 `json:"project_id"`
	SecretID      string                 `json:"secret_id,omitempty"`
	SecretName    string                 `json:"secret_name,omitempty"`
	WorkflowID    string                 `json:"workflow_id,omitempty"`
	ExecutionID   string                 `json:"execution_id,omitempty"`
	TemplateID    string                 `json:"template_id,omitempty"`
	SourceIP      string                 `json:"source_ip"`
	UserAgent     string                 `json:"user_agent"`
	Success       bool                   `json:"success"`
	ErrorMessage  string                 `json:"error_message,omitempty"`
	SecretMapping map[string]string      `json:"secret_mapping,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// SecretAuditLogger handles audit logging for secret operations
type SecretAuditLogger struct {
	logger *zap.Logger
}

// NewSecretAuditLogger creates a new secret audit logger
func NewSecretAuditLogger() *SecretAuditLogger {
	config := zap.NewProductionConfig()
	config.OutputPaths = []string{
		"stdout",
		"/var/log/deploy-orchestrator/secret-audit.log",
	}
	config.ErrorOutputPaths = []string{
		"stderr",
		"/var/log/deploy-orchestrator/secret-audit-errors.log",
	}

	// Add custom fields for audit logging
	config.InitialFields = map[string]interface{}{
		"service": "secret-audit",
		"version": "1.0.0",
	}

	logger, err := config.Build()
	if err != nil {
		panic(err)
	}

	return &SecretAuditLogger{
		logger: logger,
	}
}

// LogSecretRetrieval logs secret retrieval events
func (sal *SecretAuditLogger) LogSecretRetrieval(ctx context.Context, event SecretAuditEvent) {
	event.EventType = "secret_retrieval"
	event.Timestamp = time.Now()

	sal.logEvent(event)
}

// LogSecretMappingValidation logs secret mapping validation events
func (sal *SecretAuditLogger) LogSecretMappingValidation(ctx context.Context, event SecretAuditEvent) {
	event.EventType = "secret_mapping_validation"
	event.Timestamp = time.Now()

	sal.logEvent(event)
}

// LogUnauthorizedAccess logs unauthorized secret access attempts
func (sal *SecretAuditLogger) LogUnauthorizedAccess(ctx context.Context, event SecretAuditEvent) {
	event.EventType = "unauthorized_access"
	event.Timestamp = time.Now()
	event.Success = false

	sal.logEvent(event)
}

// LogCrossProjectAccess logs cross-project access attempts
func (sal *SecretAuditLogger) LogCrossProjectAccess(ctx context.Context, event SecretAuditEvent) {
	event.EventType = "cross_project_access"
	event.Timestamp = time.Now()
	event.Success = false

	sal.logEvent(event)
}

// LogWorkflowSecretInjection logs secret injection into workflows
func (sal *SecretAuditLogger) LogWorkflowSecretInjection(ctx context.Context, event SecretAuditEvent) {
	event.EventType = "workflow_secret_injection"
	event.Timestamp = time.Now()

	sal.logEvent(event)
}

// LogSecretRotation logs secret rotation events
func (sal *SecretAuditLogger) LogSecretRotation(ctx context.Context, event SecretAuditEvent) {
	event.EventType = "secret_rotation"
	event.Timestamp = time.Now()

	sal.logEvent(event)
}

// LogSecretCreation logs secret creation events
func (sal *SecretAuditLogger) LogSecretCreation(ctx context.Context, event SecretAuditEvent) {
	event.EventType = "secret_creation"
	event.Timestamp = time.Now()

	sal.logEvent(event)
}

// LogSecretDeletion logs secret deletion events
func (sal *SecretAuditLogger) LogSecretDeletion(ctx context.Context, event SecretAuditEvent) {
	event.EventType = "secret_deletion"
	event.Timestamp = time.Now()

	sal.logEvent(event)
}

// LogSecretUpdate logs secret update events
func (sal *SecretAuditLogger) LogSecretUpdate(ctx context.Context, event SecretAuditEvent) {
	event.EventType = "secret_update"
	event.Timestamp = time.Now()

	sal.logEvent(event)
}

// logEvent logs the audit event with appropriate log level
func (sal *SecretAuditLogger) logEvent(event SecretAuditEvent) {
	fields := []zapcore.Field{
		zap.String("event_type", event.EventType),
		zap.String("user_id", event.UserID),
		zap.String("username", event.Username),
		zap.String("project_id", event.ProjectID),
		zap.String("source_ip", event.SourceIP),
		zap.String("user_agent", event.UserAgent),
		zap.Bool("success", event.Success),
	}

	// Add optional fields
	if event.SecretID != "" {
		fields = append(fields, zap.String("secret_id", event.SecretID))
	}
	if event.SecretName != "" {
		fields = append(fields, zap.String("secret_name", event.SecretName))
	}
	if event.WorkflowID != "" {
		fields = append(fields, zap.String("workflow_id", event.WorkflowID))
	}
	if event.ExecutionID != "" {
		fields = append(fields, zap.String("execution_id", event.ExecutionID))
	}
	if event.TemplateID != "" {
		fields = append(fields, zap.String("template_id", event.TemplateID))
	}
	if event.ErrorMessage != "" {
		fields = append(fields, zap.String("error_message", event.ErrorMessage))
	}
	if len(event.SecretMapping) > 0 {
		mappingJSON, _ := json.Marshal(event.SecretMapping)
		fields = append(fields, zap.String("secret_mapping", string(mappingJSON)))
	}
	if len(event.Metadata) > 0 {
		metadataJSON, _ := json.Marshal(event.Metadata)
		fields = append(fields, zap.String("metadata", string(metadataJSON)))
	}

	// Log with appropriate level based on event type and success
	switch {
	case !event.Success && (event.EventType == "unauthorized_access" || event.EventType == "cross_project_access"):
		sal.logger.Error("Security audit event", fields...)
	case !event.Success:
		sal.logger.Warn("Failed audit event", fields...)
	case event.EventType == "secret_deletion" || event.EventType == "secret_rotation":
		sal.logger.Warn("Important audit event", fields...)
	default:
		sal.logger.Info("Audit event", fields...)
	}
}

// Close closes the audit logger
func (sal *SecretAuditLogger) Close() error {
	return sal.logger.Sync()
}

// Helper functions for creating audit events

// NewSecretRetrievalEvent creates a new secret retrieval audit event
func NewSecretRetrievalEvent(userID, username, projectID, secretName, sourceIP, userAgent string, success bool, errorMsg string) SecretAuditEvent {
	return SecretAuditEvent{
		UserID:       userID,
		Username:     username,
		ProjectID:    projectID,
		SecretName:   secretName,
		SourceIP:     sourceIP,
		UserAgent:    userAgent,
		Success:      success,
		ErrorMessage: errorMsg,
	}
}

// NewSecretMappingEvent creates a new secret mapping audit event
func NewSecretMappingEvent(userID, username, projectID, templateID, workflowID, executionID, sourceIP, userAgent string, secretMapping map[string]string, success bool, errorMsg string) SecretAuditEvent {
	return SecretAuditEvent{
		UserID:        userID,
		Username:      username,
		ProjectID:     projectID,
		TemplateID:    templateID,
		WorkflowID:    workflowID,
		ExecutionID:   executionID,
		SourceIP:      sourceIP,
		UserAgent:     userAgent,
		SecretMapping: secretMapping,
		Success:       success,
		ErrorMessage:  errorMsg,
	}
}

// NewUnauthorizedAccessEvent creates a new unauthorized access audit event
func NewUnauthorizedAccessEvent(userID, username, projectID, secretName, sourceIP, userAgent, errorMsg string) SecretAuditEvent {
	return SecretAuditEvent{
		UserID:       userID,
		Username:     username,
		ProjectID:    projectID,
		SecretName:   secretName,
		SourceIP:     sourceIP,
		UserAgent:    userAgent,
		Success:      false,
		ErrorMessage: errorMsg,
	}
}

// NewWorkflowSecretInjectionEvent creates a new workflow secret injection audit event
func NewWorkflowSecretInjectionEvent(userID, username, projectID, workflowID, executionID, sourceIP, userAgent string, secretMapping map[string]string, success bool, errorMsg string) SecretAuditEvent {
	return SecretAuditEvent{
		UserID:        userID,
		Username:      username,
		ProjectID:     projectID,
		WorkflowID:    workflowID,
		ExecutionID:   executionID,
		SourceIP:      sourceIP,
		UserAgent:     userAgent,
		SecretMapping: secretMapping,
		Success:       success,
		ErrorMessage:  errorMsg,
	}
}
