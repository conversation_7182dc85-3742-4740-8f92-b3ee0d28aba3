package middleware

import (
	"github.com/claudio/deploy-orchestrator/shared/logging"
	"github.com/gin-gonic/gin"
)

// LoggerMiddleware returns a middleware for logging HTTP requests
func LoggerMiddleware() gin.HandlerFunc {
	return logging.Middleware(logging.Default().Named("http"))
}

// ErrorHandlerMiddleware returns a middleware for handling errors
func ErrorHandlerMiddleware() gin.HandlerFunc {
	return logging.ErrorHandler(logging.Default().Named("error"))
}

// AuthMiddleware returns a middleware for authentication
func AuthMiddleware() gin.HandlerFunc {
	logger := logging.Default().Named("auth")
	
	return func(c *gin.Context) {
		// Get the logger from context
		reqLogger := logging.FromGinContext(c)
		
		// Get token from header
		token := c.<PERSON>eader("Authorization")
		if token == "" {
			reqLogger.Warn("Missing authorization token")
			c.J<PERSON>(401, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}
		
		// Validate token (simplified example)
		if token != "valid-token" {
			reqLogger.Warn("Invalid authorization token", 
				logging.String("token", token),
			)
			c.JSON(401, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}
		
		// Set user info in context
		c.Set("userId", "user-123")
		c.Set("username", "john.doe")
		
		// Add user info to logger
		userLogger := reqLogger.With(
			logging.String("userId", "user-123"),
			logging.String("username", "john.doe"),
		)
		
		// Update logger in context
		c.Request = c.Request.WithContext(
			logging.WithContext(c.Request.Context(), userLogger),
		)
		
		logger.Debug("User authenticated", 
			logging.String("userId", "user-123"),
		)
		
		c.Next()
	}
}
