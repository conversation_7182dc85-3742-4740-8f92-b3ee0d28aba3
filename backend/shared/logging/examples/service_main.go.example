package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/claudio/deploy-orchestrator/service/api"
	"github.com/claudio/deploy-orchestrator/service/config"
	"github.com/claudio/deploy-orchestrator/service/middleware"
	"github.com/claudio/deploy-orchestrator/service/storage"
	"github.com/claudio/deploy-orchestrator/shared/handlers"
	"github.com/claudio/deploy-orchestrator/shared/logging"
	"github.com/gin-gonic/gin"
)

const (
	serviceName = "example-service"
	version     = "1.0.0"
)

func main() {
	// Load configuration from YAML and environment variables
	cfg, err := config.LoadConfig()
	if err != nil {
		// Use standard log for startup errors before logger is initialized
		fmt.Printf("Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logger with configuration
	logging.InitLogger(cfg.Logging)
	logger := logging.Default().Named(serviceName)

	// Log startup information
	logger.Info("Starting service",
		logging.String("version", version),
		logging.String("environment", cfg.Environment),
	)

	// Create default config file if it doesn't exist
	if err := config.SaveDefaultConfig(); err != nil {
		logger.Warn("Failed to save default config", logging.Error(err))
	}

	// Initialize appropriate Gin mode based on logging level
	if cfg.Logging.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// Initialize database connection using config
	var db *storage.Database
	var inMemoryDb *storage.InMemoryDatabase
	var useInMemory bool

	// Try database connection
	if cfg.Database.URL != "" {
		logger.Info("Connecting to database", logging.String("url", cfg.Database.URL))
		db, err = storage.NewDatabase(cfg.Database.URL)
		if err != nil {
			logger.Error("Failed to connect to database, falling back to in-memory storage",
				logging.Error(err),
			)
			useInMemory = true
		} else {
			logger.Info("Successfully connected to the database")
		}
	} else {
		logger.Info("Database URL not configured, using in-memory storage")
		useInMemory = true
	}

	// Fall back to in-memory if needed
	if useInMemory {
		inMemoryDb = storage.NewInMemoryDatabase()
		db = &storage.Database{
			InMemoryDB: inMemoryDb,
		}
	}

	// Setup router
	router := gin.New()

	// Use middleware
	router.Use(middleware.LoggerMiddleware())
	router.Use(middleware.ErrorHandlerMiddleware())
	router.Use(middleware.CORS())
	router.Use(gin.Recovery())

	// Health check endpoint - no auth required
	serviceInfo := handlers.ServiceInfo{
		Name:    serviceName,
		Version: version,
	}
	router.GET("/health", handlers.NewHealthHandler(serviceInfo))

	// Initialize handlers with database connection
	exampleHandler := api.NewExampleHandler(db)

	// API routes
	v1 := router.Group("/api/v1")
	{
		// Add authentication middleware to protected routes
		if !cfg.Auth.DisableAuth {
			v1.Use(middleware.AuthMiddleware())
		} else {
			logger.Warn("Authentication is disabled")
		}

		examples := v1.Group("/examples")
		{
			examples.GET("", exampleHandler.GetExamples)
			examples.GET("/:id", exampleHandler.GetExample)
			examples.POST("", exampleHandler.CreateExample)
			examples.PUT("/:id", exampleHandler.UpdateExample)
			examples.DELETE("/:id", exampleHandler.DeleteExample)
		}
	}

	// Create an HTTP server with configuration
	serverAddr := fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port)
	srv := &http.Server{
		Addr:         serverAddr,
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Start the server in a goroutine
	go func() {
		logger.Info("Starting server", logging.String("address", serverAddr))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", logging.Error(err))
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info("Shutdown signal received, shutting down server...")

	// Create a deadline for the shutdown
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(cfg.Server.ShutdownTimeout)*time.Second)
	defer cancel()

	// Shutdown the server
	if err := srv.Shutdown(ctx); err != nil {
		logger.Fatal("Server forced to shutdown", logging.Error(err))
	}

	// Close database connection if it exists
	if db != nil {
		if err := db.Close(); err != nil {
			logger.Error("Error closing database connection", logging.Error(err))
		}
	}

	// Sync logger before exit
	_ = logger.Sync()
	logger.Info("Server gracefully stopped")
}
