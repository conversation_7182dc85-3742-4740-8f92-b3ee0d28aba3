package api

import (
	"net/http"

	"github.com/claudio/deploy-orchestrator/service/storage"
	"github.com/claudio/deploy-orchestrator/shared/logging"
	"github.com/gin-gonic/gin"
)

// ExampleHandler handles API requests for examples
type ExampleHandler struct {
	db     *storage.Database
	logger logging.Logger
}

// NewExampleHandler creates a new example handler
func NewExampleHandler(db *storage.Database) *ExampleHandler {
	return &ExampleHandler{
		db:     db,
		logger: logging.Default().Named("example-handler"),
	}
}

// GetExamples returns a list of examples
func (h *ExampleHandler) GetExamples(c *gin.Context) {
	// Get logger from context
	logger := logging.FromGinContext(c)
	
	// Log request parameters
	limit := c.DefaultQuery("limit", "10")
	offset := c.Default<PERSON>uery("offset", "0")
	logger.Debug("Getting examples",
		logging.String("limit", limit),
		logging.String("offset", offset),
	)
	
	// Example database operation
	examples, err := h.db.GetExamples(c.Request.Context(), limit, offset)
	if err != nil {
		logger.Error("Failed to get examples", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get examples"})
		return
	}
	
	// Log success
	logger.Info("Retrieved examples",
		logging.Int("count", len(examples)),
	)
	
	c.JSON(http.StatusOK, examples)
}

// GetExample returns a single example by ID
func (h *ExampleHandler) GetExample(c *gin.Context) {
	// Get logger from context
	logger := logging.FromGinContext(c)
	
	// Get ID from path
	id := c.Param("id")
	logger.Debug("Getting example", logging.String("id", id))
	
	// Example database operation
	example, err := h.db.GetExample(c.Request.Context(), id)
	if err != nil {
		logger.Error("Failed to get example", 
			logging.String("id", id),
			logging.Error(err),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get example"})
		return
	}
	
	// Check if example exists
	if example == nil {
		logger.Warn("Example not found", logging.String("id", id))
		c.JSON(http.StatusNotFound, gin.H{"error": "Example not found"})
		return
	}
	
	// Log success
	logger.Info("Retrieved example", logging.String("id", id))
	
	c.JSON(http.StatusOK, example)
}

// CreateExample creates a new example
func (h *ExampleHandler) CreateExample(c *gin.Context) {
	// Get logger from context
	logger := logging.FromGinContext(c)
	
	// Parse request body
	var example Example
	if err := c.ShouldBindJSON(&example); err != nil {
		logger.Warn("Invalid request body", logging.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}
	
	// Log request data
	logger.Debug("Creating example", 
		logging.String("name", example.Name),
	)
	
	// Example database operation
	id, err := h.db.CreateExample(c.Request.Context(), &example)
	if err != nil {
		logger.Error("Failed to create example", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create example"})
		return
	}
	
	// Log success
	logger.Info("Created example", 
		logging.String("id", id),
		logging.String("name", example.Name),
	)
	
	example.ID = id
	c.JSON(http.StatusCreated, example)
}

// UpdateExample updates an existing example
func (h *ExampleHandler) UpdateExample(c *gin.Context) {
	// Get logger from context
	logger := logging.FromGinContext(c)
	
	// Get ID from path
	id := c.Param("id")
	
	// Parse request body
	var example Example
	if err := c.ShouldBindJSON(&example); err != nil {
		logger.Warn("Invalid request body", logging.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}
	
	// Log request data
	logger.Debug("Updating example", 
		logging.String("id", id),
		logging.String("name", example.Name),
	)
	
	// Example database operation
	err := h.db.UpdateExample(c.Request.Context(), id, &example)
	if err != nil {
		logger.Error("Failed to update example", 
			logging.String("id", id),
			logging.Error(err),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update example"})
		return
	}
	
	// Log success
	logger.Info("Updated example", logging.String("id", id))
	
	c.JSON(http.StatusOK, example)
}

// DeleteExample deletes an example
func (h *ExampleHandler) DeleteExample(c *gin.Context) {
	// Get logger from context
	logger := logging.FromGinContext(c)
	
	// Get ID from path
	id := c.Param("id")
	logger.Debug("Deleting example", logging.String("id", id))
	
	// Example database operation
	err := h.db.DeleteExample(c.Request.Context(), id)
	if err != nil {
		logger.Error("Failed to delete example", 
			logging.String("id", id),
			logging.Error(err),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete example"})
		return
	}
	
	// Log success
	logger.Info("Deleted example", logging.String("id", id))
	
	c.JSON(http.StatusNoContent, nil)
}

// Example represents an example entity
type Example struct {
	ID          string `json:"id"`
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	CreatedAt   string `json:"createdAt,omitempty"`
	UpdatedAt   string `json:"updatedAt,omitempty"`
}
