package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// HealthResponse represents the structure of health endpoint responses
type HealthResponse struct {
	Service string `json:"service"`
	Status  string `json:"status"`
	Version string `json:"version"`
}

// ServiceInfo contains information about a service
type ServiceInfo struct {
	Name    string
	Version string
}

// NewHealthHandler creates a standard health check handler
func NewHealthHandler(info ServiceInfo) gin.HandlerFunc {
	return func(c *gin.Context) {
		response := HealthResponse{
			Service: info.Name,
			Status:  "healthy",
			Version: info.Version,
		}

		c.JSON(http.StatusOK, response)
	}
}
