package monitoring

import (
	"context"
	"fmt"
	"net/http"
	"runtime"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// HealthStatus represents the health status of a component
type HealthStatus string

const (
	HealthStatusHealthy   HealthStatus = "healthy"
	HealthStatusUnhealthy HealthStatus = "unhealthy"
	HealthStatusDegraded  HealthStatus = "degraded"
)

// HealthCheck represents a single health check
type HealthCheck struct {
	Name      string                 `json:"name"`
	Status    HealthStatus           `json:"status"`
	Message   string                 `json:"message,omitempty"`
	Duration  time.Duration          `json:"duration"`
	Timestamp time.Time              `json:"timestamp"`
	Details   map[string]interface{} `json:"details,omitempty"`
	Error     string                 `json:"error,omitempty"`
}

// HealthResponse represents the overall health response
type HealthResponse struct {
	Status    HealthStatus           `json:"status"`
	Timestamp time.Time              `json:"timestamp"`
	Service   ServiceInfo            `json:"service"`
	Checks    map[string]HealthCheck `json:"checks"`
	Summary   HealthSummary          `json:"summary"`
}

// ServiceInfo contains basic service information
type ServiceInfo struct {
	Name        string    `json:"name"`
	Version     string    `json:"version"`
	Environment string    `json:"environment"`
	StartTime   time.Time `json:"startTime"`
	Uptime      string    `json:"uptime"`
}

// HealthSummary provides a summary of health checks
type HealthSummary struct {
	Total     int `json:"total"`
	Healthy   int `json:"healthy"`
	Unhealthy int `json:"unhealthy"`
	Degraded  int `json:"degraded"`
}

// HealthChecker interface for implementing custom health checks
type HealthChecker interface {
	Check(ctx context.Context) HealthCheck
}

// HealthManager manages all health checks for a service
type HealthManager struct {
	serviceName string
	version     string
	environment string
	startTime   time.Time
	checkers    map[string]HealthChecker
	mutex       sync.RWMutex
}

// NewHealthManager creates a new health manager
func NewHealthManager(serviceName, version, environment string) *HealthManager {
	return &HealthManager{
		serviceName: serviceName,
		version:     version,
		environment: environment,
		startTime:   time.Now(),
		checkers:    make(map[string]HealthChecker),
	}
}

// AddChecker adds a health checker
func (hm *HealthManager) AddChecker(name string, checker HealthChecker) {
	hm.mutex.Lock()
	defer hm.mutex.Unlock()
	hm.checkers[name] = checker
}

// RemoveChecker removes a health checker
func (hm *HealthManager) RemoveChecker(name string) {
	hm.mutex.Lock()
	defer hm.mutex.Unlock()
	delete(hm.checkers, name)
}

// CheckHealth performs all health checks and returns the result
func (hm *HealthManager) CheckHealth(ctx context.Context) HealthResponse {
	hm.mutex.RLock()
	checkers := make(map[string]HealthChecker)
	for name, checker := range hm.checkers {
		checkers[name] = checker
	}
	hm.mutex.RUnlock()

	checks := make(map[string]HealthCheck)
	summary := HealthSummary{}

	// Run all health checks
	for name, checker := range checkers {
		check := checker.Check(ctx)
		checks[name] = check
		summary.Total++

		switch check.Status {
		case HealthStatusHealthy:
			summary.Healthy++
		case HealthStatusUnhealthy:
			summary.Unhealthy++
		case HealthStatusDegraded:
			summary.Degraded++
		}
	}

	// Determine overall status
	overallStatus := HealthStatusHealthy
	if summary.Unhealthy > 0 {
		overallStatus = HealthStatusUnhealthy
	} else if summary.Degraded > 0 {
		overallStatus = HealthStatusDegraded
	}

	return HealthResponse{
		Status:    overallStatus,
		Timestamp: time.Now(),
		Service: ServiceInfo{
			Name:        hm.serviceName,
			Version:     hm.version,
			Environment: hm.environment,
			StartTime:   hm.startTime,
			Uptime:      time.Since(hm.startTime).String(),
		},
		Checks:  checks,
		Summary: summary,
	}
}

// HealthHandler returns a Gin handler for health checks
func (hm *HealthManager) HealthHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
		defer cancel()

		health := hm.CheckHealth(ctx)

		statusCode := http.StatusOK
		if health.Status == HealthStatusUnhealthy {
			statusCode = http.StatusServiceUnavailable
		} else if health.Status == HealthStatusDegraded {
			statusCode = http.StatusPartialContent
		}

		c.JSON(statusCode, health)
	}
}

// ReadinessHandler returns a simple readiness check
func (hm *HealthManager) ReadinessHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
		defer cancel()

		health := hm.CheckHealth(ctx)

		if health.Status == HealthStatusUnhealthy {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status": "not ready",
				"reason": "unhealthy dependencies",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"status": "ready",
		})
	}
}

// LivenessHandler returns a simple liveness check
func (hm *HealthManager) LivenessHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Simple liveness check - if we can respond, we're alive
		c.JSON(http.StatusOK, gin.H{
			"status":    "alive",
			"timestamp": time.Now(),
			"uptime":    time.Since(hm.startTime).String(),
		})
	}
}

// DatabaseHealthChecker checks database connectivity
type DatabaseHealthChecker struct {
	db   *gorm.DB
	name string
}

// NewDatabaseHealthChecker creates a new database health checker
func NewDatabaseHealthChecker(db *gorm.DB, name string) *DatabaseHealthChecker {
	return &DatabaseHealthChecker{
		db:   db,
		name: name,
	}
}

// Check performs the database health check
func (dhc *DatabaseHealthChecker) Check(ctx context.Context) HealthCheck {
	start := time.Now()
	check := HealthCheck{
		Name:      dhc.name,
		Timestamp: start,
		Details:   make(map[string]interface{}),
	}

	// Get the underlying sql.DB
	sqlDB, err := dhc.db.DB()
	if err != nil {
		check.Status = HealthStatusUnhealthy
		check.Error = fmt.Sprintf("Failed to get database connection: %v", err)
		check.Duration = time.Since(start)
		return check
	}

	// Check if we can ping the database
	if err := sqlDB.PingContext(ctx); err != nil {
		check.Status = HealthStatusUnhealthy
		check.Error = fmt.Sprintf("Database ping failed: %v", err)
		check.Duration = time.Since(start)
		return check
	}

	// Get database stats
	stats := sqlDB.Stats()
	check.Details["open_connections"] = stats.OpenConnections
	check.Details["in_use"] = stats.InUse
	check.Details["idle"] = stats.Idle
	check.Details["max_open_connections"] = stats.MaxOpenConnections

	// Check connection pool health
	if stats.OpenConnections > 0 && stats.InUse == stats.OpenConnections {
		check.Status = HealthStatusDegraded
		check.Message = "All database connections are in use"
	} else {
		check.Status = HealthStatusHealthy
		check.Message = "Database connection is healthy"
	}

	check.Duration = time.Since(start)
	return check
}

// MemoryHealthChecker checks memory usage
type MemoryHealthChecker struct {
	maxMemoryMB int64
}

// NewMemoryHealthChecker creates a new memory health checker
func NewMemoryHealthChecker(maxMemoryMB int64) *MemoryHealthChecker {
	return &MemoryHealthChecker{
		maxMemoryMB: maxMemoryMB,
	}
}

// Check performs the memory health check
func (mhc *MemoryHealthChecker) Check(ctx context.Context) HealthCheck {
	start := time.Now()
	check := HealthCheck{
		Name:      "memory",
		Timestamp: start,
		Details:   make(map[string]interface{}),
	}

	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	allocMB := int64(m.Alloc / 1024 / 1024)
	sysMB := int64(m.Sys / 1024 / 1024)

	check.Details["alloc_mb"] = allocMB
	check.Details["sys_mb"] = sysMB
	check.Details["num_gc"] = m.NumGC
	check.Details["goroutines"] = runtime.NumGoroutine()

	if mhc.maxMemoryMB > 0 && allocMB > mhc.maxMemoryMB {
		check.Status = HealthStatusUnhealthy
		check.Message = fmt.Sprintf("Memory usage (%d MB) exceeds limit (%d MB)", allocMB, mhc.maxMemoryMB)
	} else if mhc.maxMemoryMB > 0 && allocMB > mhc.maxMemoryMB*8/10 {
		check.Status = HealthStatusDegraded
		check.Message = fmt.Sprintf("Memory usage (%d MB) is high", allocMB)
	} else {
		check.Status = HealthStatusHealthy
		check.Message = fmt.Sprintf("Memory usage is normal (%d MB)", allocMB)
	}

	check.Duration = time.Since(start)
	return check
}
