package monitoring

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestTracerConcurrency tests that the tracer is thread-safe
func TestTracerConcurrency(t *testing.T) {
	logger := NewLogger("test-service", "debug", "development")
	tracer := NewTracer("test-service", logger)

	// Number of concurrent goroutines
	numGoroutines := 100
	numOperationsPerGoroutine := 10

	var wg sync.WaitGroup
	wg.Add(numGoroutines)

	// Channel to collect any panics
	panicChan := make(chan interface{}, numGoroutines)

	// Start multiple goroutines that create and finish spans concurrently
	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			defer wg.Done()
			defer func() {
				if r := recover(); r != nil {
					panicChan <- r
				}
			}()

			ctx := context.Background()

			for j := 0; j < numOperationsPerGoroutine; j++ {
				// Create a span
				operation := fmt.Sprintf("operation-%d-%d", goroutineID, j)
				span, newCtx := tracer.StartSpan(ctx, operation)

				// Set some tags
				span.SetTag("goroutine_id", goroutineID)
				span.SetTag("operation_id", j)
				span.SetTag("test", true)

				// Log an event
				span.LogEvent("info", "test event", map[string]interface{}{
					"goroutine": goroutineID,
					"operation": j,
				})

				// Get span from context
				retrievedSpan := tracer.SpanFromContext(newCtx)
				assert.NotNil(t, retrievedSpan)
				assert.Equal(t, span.SpanID, retrievedSpan.SpanID)

				// Simulate some work
				time.Sleep(time.Millisecond)

				// Finish the span
				tracer.FinishSpan(span)
			}
		}(i)
	}

	// Wait for all goroutines to complete
	wg.Wait()

	// Close the panic channel and check for panics
	close(panicChan)
	var panics []interface{}
	for panic := range panicChan {
		panics = append(panics, panic)
	}

	// Assert no panics occurred
	assert.Empty(t, panics, "Expected no panics, but got: %v", panics)

	// Get active spans (should be safe to call)
	activeSpans := tracer.GetActiveSpans()
	assert.NotNil(t, activeSpans)

	t.Logf("Test completed successfully with %d goroutines, %d operations each",
		numGoroutines, numOperationsPerGoroutine)
	t.Logf("Active spans remaining: %d", len(activeSpans))
}

// TestTracerConcurrentAccess tests concurrent read/write access to spans
func TestTracerConcurrentAccess(t *testing.T) {
	logger := NewLogger("test-service", "debug", "development")
	tracer := NewTracer("test-service", logger)

	ctx := context.Background()
	span, spanCtx := tracer.StartSpan(ctx, "test-operation")

	var wg sync.WaitGroup
	numReaders := 50
	numWriters := 10

	// Start reader goroutines
	wg.Add(numReaders)
	for i := 0; i < numReaders; i++ {
		go func(readerID int) {
			defer wg.Done()
			for j := 0; j < 100; j++ {
				// Read operations
				retrievedSpan := tracer.SpanFromContext(spanCtx)
				if retrievedSpan != nil {
					_ = retrievedSpan.SpanID
					_ = retrievedSpan.Operation
				}

				activeSpans := tracer.GetActiveSpans()
				_ = len(activeSpans)

				time.Sleep(time.Microsecond)
			}
		}(i)
	}

	// Start writer goroutines
	wg.Add(numWriters)
	for i := 0; i < numWriters; i++ {
		go func(writerID int) {
			defer wg.Done()
			for j := 0; j < 50; j++ {
				// Write operations
				newSpan, _ := tracer.StartSpan(ctx, fmt.Sprintf("writer-%d-op-%d", writerID, j))
				newSpan.SetTag("writer_id", writerID)
				newSpan.SetTag("operation_id", j)

				time.Sleep(time.Microsecond)

				tracer.FinishSpan(newSpan)
			}
		}(i)
	}

	wg.Wait()

	// Finish the original span
	tracer.FinishSpan(span)

	t.Log("Concurrent access test completed successfully")
}
