package monitoring

import (
	"context"
	"os"
	"runtime"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// LogLevel represents the log level
type LogLevel string

const (
	LogLevelDebug LogLevel = "debug"
	LogLevelInfo  LogLevel = "info"
	LogLevelWarn  LogLevel = "warn"
	LogLevelError LogLevel = "error"
	LogLevelFatal LogLevel = "fatal"
)

// ContextKey represents a context key type
type ContextKey string

const (
	ContextKeyRequestID ContextKey = "request_id"
	ContextKeyUserID    ContextKey = "user_id"
	ContextKeySessionID ContextKey = "session_id"
	ContextKeyOperation ContextKey = "operation"
	ContextKeyTraceID   ContextKey = "trace_id"
)

// Logger represents a structured logger
type Logger struct {
	*logrus.Logger
	serviceName string
	version     string
	environment string
}

// NewLogger creates a new structured logger
func NewLogger(serviceName, version, environment string) *Logger {
	logger := logrus.New()

	// Set JSON formatter for structured logging
	logger.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: time.RFC3339,
		FieldMap: logrus.FieldMap{
			logrus.FieldKeyTime:  "timestamp",
			logrus.FieldKeyLevel: "level",
			logrus.FieldKeyMsg:   "message",
			logrus.FieldKeyFunc:  "function",
			logrus.FieldKeyFile:  "file",
		},
	})

	// Set log level from environment
	level := strings.ToLower(os.Getenv("LOG_LEVEL"))
	switch level {
	case "debug":
		logger.SetLevel(logrus.DebugLevel)
	case "info":
		logger.SetLevel(logrus.InfoLevel)
	case "warn":
		logger.SetLevel(logrus.WarnLevel)
	case "error":
		logger.SetLevel(logrus.ErrorLevel)
	case "fatal":
		logger.SetLevel(logrus.FatalLevel)
	default:
		logger.SetLevel(logrus.InfoLevel)
	}

	// Set output
	logger.SetOutput(os.Stdout)

	// Enable caller reporting
	logger.SetReportCaller(true)

	return &Logger{
		Logger:      logger,
		serviceName: serviceName,
		version:     version,
		environment: environment,
	}
}

// WithContext creates a logger with context values
func (l *Logger) WithContext(ctx context.Context) *logrus.Entry {
	entry := l.Logger.WithFields(logrus.Fields{
		"service":     l.serviceName,
		"version":     l.version,
		"environment": l.environment,
	})

	// Add context values if they exist
	if requestID := ctx.Value(ContextKeyRequestID); requestID != nil {
		entry = entry.WithField("request_id", requestID)
	}
	if userID := ctx.Value(ContextKeyUserID); userID != nil {
		entry = entry.WithField("user_id", userID)
	}
	if sessionID := ctx.Value(ContextKeySessionID); sessionID != nil {
		entry = entry.WithField("session_id", sessionID)
	}
	if operation := ctx.Value(ContextKeyOperation); operation != nil {
		entry = entry.WithField("operation", operation)
	}
	if traceID := ctx.Value(ContextKeyTraceID); traceID != nil {
		entry = entry.WithField("trace_id", traceID)
	}

	return entry
}

// WithFields creates a logger with additional fields
func (l *Logger) WithFields(fields map[string]interface{}) *logrus.Entry {
	logrusFields := logrus.Fields{
		"service":     l.serviceName,
		"version":     l.version,
		"environment": l.environment,
	}

	for k, v := range fields {
		logrusFields[k] = v
	}

	return l.Logger.WithFields(logrusFields)
}

// WithError creates a logger with error information
func (l *Logger) WithError(err error) *logrus.Entry {
	entry := l.Logger.WithFields(logrus.Fields{
		"service":     l.serviceName,
		"version":     l.version,
		"environment": l.environment,
	})

	if err != nil {
		entry = entry.WithError(err)

		// Add stack trace for debugging
		if l.Logger.Level <= logrus.DebugLevel {
			entry = entry.WithField("stack_trace", getStackTrace())
		}
	}

	return entry
}

// LoggingMiddleware returns a Gin middleware for request logging
func (l *Logger) LoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		// Generate request ID if not present
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = uuid.New().String()
		}

		// Add request ID to context
		ctx := context.WithValue(c.Request.Context(), ContextKeyRequestID, requestID)
		c.Request = c.Request.WithContext(ctx)

		// Add request ID to response headers
		c.Header("X-Request-ID", requestID)

		// Log request start
		l.WithContext(ctx).WithFields(map[string]interface{}{
			"method":     c.Request.Method,
			"path":       c.Request.URL.Path,
			"query":      c.Request.URL.RawQuery,
			"user_agent": c.Request.UserAgent(),
			"remote_ip":  c.ClientIP(),
		}).Info("Request started")

		// Process request
		c.Next()

		// Calculate request duration
		duration := time.Since(start)

		// Determine log level based on status code
		statusCode := c.Writer.Status()
		logLevel := logrus.InfoLevel
		if statusCode >= 400 && statusCode < 500 {
			logLevel = logrus.WarnLevel
		} else if statusCode >= 500 {
			logLevel = logrus.ErrorLevel
		}

		// Log request completion
		entry := l.WithContext(ctx).WithFields(map[string]interface{}{
			"method":        c.Request.Method,
			"path":          c.Request.URL.Path,
			"status_code":   statusCode,
			"duration_ms":   duration.Milliseconds(),
			"response_size": c.Writer.Size(),
		})

		// Add error information if present
		if len(c.Errors) > 0 {
			entry = entry.WithField("errors", c.Errors.String())
		}

		entry.Log(logLevel, "Request completed")
	}
}

// DatabaseLoggingHook creates a GORM logger hook
func (l *Logger) DatabaseLoggingHook() func(operation, query string, duration time.Duration, err error) {
	return func(operation, query string, duration time.Duration, err error) {
		entry := l.WithFields(map[string]interface{}{
			"operation":   operation,
			"duration_ms": duration.Milliseconds(),
			"query":       query,
		})

		if err != nil {
			entry.WithError(err).Error("Database operation failed")
		} else if duration > 1*time.Second {
			entry.Warn("Slow database operation")
		} else {
			entry.Debug("Database operation completed")
		}
	}
}

// BusinessOperationLogger logs business operations
func (l *Logger) BusinessOperationLogger(ctx context.Context, operation string) func(status string, err error, fields map[string]interface{}) {
	start := time.Now()

	// Add operation to context
	ctx = context.WithValue(ctx, ContextKeyOperation, operation)

	l.WithContext(ctx).WithField("operation", operation).Info("Business operation started")

	return func(status string, err error, fields map[string]interface{}) {
		duration := time.Since(start)

		logFields := map[string]interface{}{
			"operation":   operation,
			"status":      status,
			"duration_ms": duration.Milliseconds(),
		}

		// Add additional fields
		for k, v := range fields {
			logFields[k] = v
		}

		entry := l.WithContext(ctx).WithFields(logFields)

		if err != nil {
			entry.WithError(err).Error("Business operation failed")
		} else {
			entry.Info("Business operation completed")
		}
	}
}

// SecurityLogger logs security-related events
func (l *Logger) SecurityLogger(ctx context.Context, event string, userID string, details map[string]interface{}) {
	logFields := map[string]interface{}{
		"event_type": "security",
		"event":      event,
		"user_id":    userID,
	}

	for k, v := range details {
		logFields[k] = v
	}

	l.WithContext(ctx).WithFields(logFields).Warn("Security event")
}

// AuditLogger logs audit events
func (l *Logger) AuditLogger(ctx context.Context, action, resource, resourceID, userID string, details map[string]interface{}) {
	logFields := map[string]interface{}{
		"event_type":  "audit",
		"action":      action,
		"resource":    resource,
		"resource_id": resourceID,
		"user_id":     userID,
	}

	for k, v := range details {
		logFields[k] = v
	}

	l.WithContext(ctx).WithFields(logFields).Info("Audit event")
}

// getStackTrace returns the current stack trace
func getStackTrace() string {
	buf := make([]byte, 1024)
	for {
		n := runtime.Stack(buf, false)
		if n < len(buf) {
			return string(buf[:n])
		}
		buf = make([]byte, 2*len(buf))
	}
}

// AddContextValue adds a value to the context
func AddContextValue(ctx context.Context, key ContextKey, value interface{}) context.Context {
	return context.WithValue(ctx, key, value)
}

// GetContextValue gets a value from the context
func GetContextValue(ctx context.Context, key ContextKey) interface{} {
	return ctx.Value(key)
}

// GetRequestID gets the request ID from context
func GetRequestID(ctx context.Context) string {
	if requestID := ctx.Value(ContextKeyRequestID); requestID != nil {
		return requestID.(string)
	}
	return ""
}
