package monitoring

import (
	"context"
	"runtime"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// MonitoringManager manages all monitoring components
type MonitoringManager struct {
	ServiceName string
	Version     string
	Environment string
	StartTime   time.Time

	Health  *HealthManager
	Metrics *MetricsManager
	Logger  *Logger
	Tracer  *Tracer
}

// NewMonitoringManager creates a new monitoring manager
func NewMonitoringManager(serviceName, version, environment string) *MonitoringManager {
	startTime := time.Now()

	// Initialize components
	health := NewHealthManager(serviceName, version, environment)
	metrics := NewMetricsManager(serviceName)
	logger := NewLogger(serviceName, version, environment)
	tracer := NewTracer(serviceName, logger)

	// Set application info in metrics
	metrics.SetAppInfo(version, environment)

	// Add basic health checks
	health.AddChecker("memory", NewMemoryHealthChecker(1024)) // 1GB limit

	mm := &MonitoringManager{
		ServiceName: serviceName,
		Version:     version,
		Environment: environment,
		StartTime:   startTime,
		Health:      health,
		Metrics:     metrics,
		Logger:      logger,
		Tracer:      tracer,
	}

	// Start background metrics collection
	mm.startMetricsCollection()

	return mm
}

// AddDatabase adds database monitoring
func (mm *MonitoringManager) AddDatabase(db *gorm.DB, name string) {
	// Add database health check
	mm.Health.AddChecker(name, NewDatabaseHealthChecker(db, name))

	// Start database metrics collection
	go mm.collectDatabaseMetrics(db, name)
}

// SetupRoutes sets up monitoring routes
func (mm *MonitoringManager) SetupRoutes(router *gin.Engine) {
	// Health check routes
	router.GET("/health", mm.Health.HealthHandler())
	router.GET("/health/ready", mm.Health.ReadinessHandler())
	router.GET("/health/live", mm.Health.LivenessHandler())

	// Metrics route
	router.GET("/metrics", mm.Metrics.MetricsHandler())

	// Debug routes (only in development)
	if mm.Environment == "development" {
		debug := router.Group("/debug")
		{
			debug.GET("/spans", mm.debugSpansHandler())
			debug.GET("/info", mm.debugInfoHandler())
		}
	}
}

// SetupMiddleware sets up monitoring middleware
func (mm *MonitoringManager) SetupMiddleware(router *gin.Engine) {
	// Add middleware in order
	router.Use(mm.Logger.LoggingMiddleware())
	router.Use(mm.Tracer.TracingMiddleware())
	router.Use(mm.Metrics.HTTPMiddleware())
}

// startMetricsCollection starts background metrics collection
func (mm *MonitoringManager) startMetricsCollection() {
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				mm.collectSystemMetrics()
			}
		}
	}()
}

// collectSystemMetrics collects system-level metrics
func (mm *MonitoringManager) collectSystemMetrics() {
	// Collect memory stats
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	mm.Metrics.RecordMemoryUsage(m.Alloc, m.Sys, runtime.NumGoroutine())
	mm.Metrics.RecordAppUptime(time.Since(mm.StartTime))
}

// collectDatabaseMetrics collects database metrics
func (mm *MonitoringManager) collectDatabaseMetrics(db *gorm.DB, name string) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			sqlDB, err := db.DB()
			if err != nil {
				continue
			}

			stats := sqlDB.Stats()
			mm.Metrics.RecordDBConnection(name, stats.OpenConnections, stats.InUse)
		}
	}
}

// LogBusinessOperation logs a business operation with tracing and metrics
func (mm *MonitoringManager) LogBusinessOperation(ctx context.Context, operation string) func(status string, err error, fields map[string]interface{}) {
	// Start tracing
	span, newCtx := mm.Tracer.BusinessOperationTracing(ctx, operation)

	// Start metrics timer
	timer := mm.Metrics.StartBusinessOperation(operation)

	// Start logging
	logFinisher := mm.Logger.BusinessOperationLogger(newCtx, operation)

	return func(status string, err error, fields map[string]interface{}) {
		// Finish metrics
		timer.Finish(status)

		// Finish logging
		logFinisher(status, err, fields)

		// Finish tracing
		if err != nil {
			span.SetError(err)
		}
		if fields != nil {
			for k, v := range fields {
				span.SetTag(k, v)
			}
		}
		span.SetTag("status", status)
		mm.Tracer.FinishSpan(span)
	}
}

// LogDatabaseOperation logs a database operation with tracing and metrics
func (mm *MonitoringManager) LogDatabaseOperation(ctx context.Context, database, operation, query string) func(err error) {
	// Start tracing
	span, _ := mm.Tracer.DatabaseTracing(ctx, operation, query)

	start := time.Now()

	return func(err error) {
		duration := time.Since(start)
		status := "success"
		if err != nil {
			status = "error"
			span.SetError(err)
		}

		// Record metrics
		mm.Metrics.RecordDBQuery(database, operation, status, duration)

		// Finish tracing
		mm.Tracer.FinishSpan(span)
	}
}

// LogSecurityEvent logs a security event
func (mm *MonitoringManager) LogSecurityEvent(ctx context.Context, event, userID string, details map[string]interface{}) {
	mm.Logger.SecurityLogger(ctx, event, userID, details)

	// Also record as metric
	mm.Metrics.RecordBusinessOperation("security_event", "logged", 0)
}

// LogAuditEvent logs an audit event
func (mm *MonitoringManager) LogAuditEvent(ctx context.Context, action, resource, resourceID, userID string, details map[string]interface{}) {
	mm.Logger.AuditLogger(ctx, action, resource, resourceID, userID, details)

	// Also record as metric
	mm.Metrics.RecordBusinessOperation("audit_event", "logged", 0)
}

// debugSpansHandler returns debug information about active spans
func (mm *MonitoringManager) debugSpansHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		spans := mm.Tracer.GetActiveSpans()
		c.JSON(200, gin.H{
			"active_spans": len(spans),
			"spans":        spans,
		})
	}
}

// debugInfoHandler returns debug information about the service
func (mm *MonitoringManager) debugInfoHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		var m runtime.MemStats
		runtime.ReadMemStats(&m)

		c.JSON(200, gin.H{
			"service": gin.H{
				"name":        mm.ServiceName,
				"version":     mm.Version,
				"environment": mm.Environment,
				"start_time":  mm.StartTime,
				"uptime":      time.Since(mm.StartTime).String(),
			},
			"runtime": gin.H{
				"go_version":   runtime.Version(),
				"goroutines":   runtime.NumGoroutine(),
				"memory_alloc": m.Alloc,
				"memory_sys":   m.Sys,
				"gc_runs":      m.NumGC,
			},
		})
	}
}

// Shutdown gracefully shuts down monitoring components
func (mm *MonitoringManager) Shutdown(ctx context.Context) error {
	mm.Logger.WithContext(ctx).Info("Shutting down monitoring components")

	// Log final metrics
	mm.collectSystemMetrics()

	// Log shutdown event
	mm.Logger.WithContext(ctx).Info("Monitoring shutdown completed")

	return nil
}

// GetLogger returns the logger instance
func (mm *MonitoringManager) GetLogger() *Logger {
	return mm.Logger
}

// GetMetrics returns the metrics manager
func (mm *MonitoringManager) GetMetrics() *MetricsManager {
	return mm.Metrics
}

// GetTracer returns the tracer instance
func (mm *MonitoringManager) GetTracer() *Tracer {
	return mm.Tracer
}

// GetHealth returns the health manager
func (mm *MonitoringManager) GetHealth() *HealthManager {
	return mm.Health
}

// EnvironmentHealth represents environment health status
type EnvironmentHealth struct {
	EnvironmentID  string               `json:"environment_id"`
	Status         string               `json:"status"` // healthy, degraded, unhealthy
	LastCheck      time.Time            `json:"last_check"`
	Services       []ServiceHealth      `json:"services"`
	Infrastructure InfrastructureHealth `json:"infrastructure"`
	Issues         []HealthIssue        `json:"issues"`
}

// ServiceHealth represents individual service health
type ServiceHealth struct {
	Name          string    `json:"name"`
	Status        string    `json:"status"`
	ResponseTime  float64   `json:"response_time_ms"`
	Replicas      int       `json:"replicas"`
	ReadyReplicas int       `json:"ready_replicas"`
	LastCheck     time.Time `json:"last_check"`
}

// InfrastructureHealth represents infrastructure metrics
type InfrastructureHealth struct {
	CPU     float64 `json:"cpu_usage_percent"`
	Memory  float64 `json:"memory_usage_percent"`
	Disk    float64 `json:"disk_usage_percent"`
	Network struct {
		InboundMbps  float64 `json:"inbound_mbps"`
		OutboundMbps float64 `json:"outbound_mbps"`
	} `json:"network"`
}

// HealthIssue represents a health issue
type HealthIssue struct {
	Component string    `json:"component"`
	Severity  string    `json:"severity"` // critical, warning, info
	Message   string    `json:"message"`
	Timestamp time.Time `json:"timestamp"`
}

// MonitorEnvironmentPromotion starts monitoring an environment during promotion
func (mm *MonitoringManager) MonitorEnvironmentPromotion(ctx context.Context, promotionID, environmentID string) error {
	mm.Logger.WithContext(ctx).Info("Starting environment promotion monitoring",
		map[string]interface{}{
			"promotion_id":   promotionID,
			"environment_id": environmentID,
		})

	// Start a background monitoring goroutine
	go func() {
		ticker := time.NewTicker(1 * time.Minute)
		defer ticker.Stop()

		// Monitor for 30 minutes
		timeout := time.After(30 * time.Minute)

		for {
			select {
			case <-ctx.Done():
				mm.Logger.WithContext(ctx).Info("Environment promotion monitoring cancelled",
					map[string]interface{}{
						"promotion_id":   promotionID,
						"environment_id": environmentID,
					})
				return
			case <-timeout:
				mm.Logger.WithContext(ctx).Info("Environment promotion monitoring completed",
					map[string]interface{}{
						"promotion_id":   promotionID,
						"environment_id": environmentID,
					})
				return
			case <-ticker.C:
				health, err := mm.GetEnvironmentHealth(ctx, environmentID)
				if err != nil {
					mm.Logger.WithContext(ctx).Error("Failed to get environment health",
						map[string]interface{}{
							"promotion_id":   promotionID,
							"environment_id": environmentID,
							"error":          err.Error(),
						})
					continue
				}

				// Record health metrics
				mm.Metrics.RecordCustomMetric("environment_health_status",
					mm.healthStatusToFloat(health.Status),
					map[string]string{
						"environment_id": environmentID,
						"promotion_id":   promotionID,
					})

				// Check for issues
				if health.Status == "degraded" || health.Status == "unhealthy" {
					mm.Logger.WithContext(ctx).Warn("Environment health issue detected during promotion",
						map[string]interface{}{
							"promotion_id":   promotionID,
							"environment_id": environmentID,
							"health_status":  health.Status,
							"issues":         health.Issues,
						})
				}
			}
		}
	}()

	return nil
}

// GetEnvironmentHealth gets current environment health status
func (mm *MonitoringManager) GetEnvironmentHealth(ctx context.Context, environmentID string) (*EnvironmentHealth, error) {
	// This is a mock implementation - in real scenarios, this would:
	// 1. Query Kubernetes/Docker health endpoints
	// 2. Check service discovery health
	// 3. Query monitoring systems (Prometheus, Datadog, etc.)
	// 4. Aggregate infrastructure metrics

	health := &EnvironmentHealth{
		EnvironmentID: environmentID,
		Status:        "healthy",
		LastCheck:     time.Now(),
		Services: []ServiceHealth{
			{
				Name:          "api-service",
				Status:        "healthy",
				ResponseTime:  45.2,
				Replicas:      3,
				ReadyReplicas: 3,
				LastCheck:     time.Now(),
			},
			{
				Name:          "worker-service",
				Status:        "healthy",
				ResponseTime:  12.8,
				Replicas:      2,
				ReadyReplicas: 2,
				LastCheck:     time.Now(),
			},
		},
		Infrastructure: InfrastructureHealth{
			CPU:    25.5,
			Memory: 68.2,
			Disk:   45.0,
			Network: struct {
				InboundMbps  float64 `json:"inbound_mbps"`
				OutboundMbps float64 `json:"outbound_mbps"`
			}{
				InboundMbps:  12.5,
				OutboundMbps: 8.3,
			},
		},
		Issues: []HealthIssue{},
	}

	// Simulate some health logic
	if health.Infrastructure.CPU > 80 {
		health.Status = "degraded"
		health.Issues = append(health.Issues, HealthIssue{
			Component: "infrastructure",
			Severity:  "warning",
			Message:   "High CPU usage detected",
			Timestamp: time.Now(),
		})
	}

	if health.Infrastructure.Memory > 90 {
		health.Status = "unhealthy"
		health.Issues = append(health.Issues, HealthIssue{
			Component: "infrastructure",
			Severity:  "critical",
			Message:   "Critical memory usage",
			Timestamp: time.Now(),
		})
	}

	mm.Logger.WithContext(ctx).Debug("Retrieved environment health",
		map[string]interface{}{
			"environment_id": environmentID,
			"status":         health.Status,
			"services_count": len(health.Services),
			"issues_count":   len(health.Issues),
		})

	return health, nil
}

// healthStatusToFloat converts health status to float for metrics
func (mm *MonitoringManager) healthStatusToFloat(status string) float64 {
	switch status {
	case "healthy":
		return 1.0
	case "degraded":
		return 0.5
	case "unhealthy":
		return 0.0
	default:
		return -1.0
	}
}
