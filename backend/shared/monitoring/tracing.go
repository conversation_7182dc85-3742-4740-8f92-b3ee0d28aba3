package monitoring

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// Span represents a trace span
type Span struct {
	TraceID   string                 `json:"trace_id"`
	SpanID    string                 `json:"span_id"`
	ParentID  string                 `json:"parent_id,omitempty"`
	Operation string                 `json:"operation"`
	Service   string                 `json:"service"`
	StartTime time.Time              `json:"start_time"`
	EndTime   *time.Time             `json:"end_time,omitempty"`
	Duration  *time.Duration         `json:"duration,omitempty"`
	Tags      map[string]interface{} `json:"tags"`
	Logs      []SpanLog              `json:"logs"`
	Status    SpanStatus             `json:"status"`
	Error     string                 `json:"error,omitempty"`
}

// SpanLog represents a log entry within a span
type SpanLog struct {
	Timestamp time.Time              `json:"timestamp"`
	Level     string                 `json:"level"`
	Message   string                 `json:"message"`
	Fields    map[string]interface{} `json:"fields,omitempty"`
}

// SpanStatus represents the status of a span
type SpanStatus string

const (
	SpanStatusOK    SpanStatus = "ok"
	SpanStatusError SpanStatus = "error"
)

// Tracer manages distributed tracing
type Tracer struct {
	serviceName string
	spans       map[string]*Span
	spansMutex  sync.RWMutex
	logger      *Logger
}

// NewTracer creates a new tracer
func NewTracer(serviceName string, logger *Logger) *Tracer {
	return &Tracer{
		serviceName: serviceName,
		spans:       make(map[string]*Span),
		logger:      logger,
	}
}

// StartSpan starts a new span
func (t *Tracer) StartSpan(ctx context.Context, operation string) (*Span, context.Context) {
	traceID := GetTraceID(ctx)
	if traceID == "" {
		traceID = uuid.New().String()
	}

	spanID := uuid.New().String()
	parentID := GetSpanID(ctx)

	span := &Span{
		TraceID:   traceID,
		SpanID:    spanID,
		ParentID:  parentID,
		Operation: operation,
		Service:   t.serviceName,
		StartTime: time.Now(),
		Tags:      make(map[string]interface{}),
		Logs:      make([]SpanLog, 0),
		Status:    SpanStatusOK,
	}

	// Store span (thread-safe)
	t.spansMutex.Lock()
	t.spans[spanID] = span
	t.spansMutex.Unlock()

	// Add span to context
	ctx = context.WithValue(ctx, ContextKeyTraceID, traceID)
	ctx = context.WithValue(ctx, "span_id", spanID)

	// Log span start
	t.logger.WithContext(ctx).WithFields(map[string]interface{}{
		"trace_id":  traceID,
		"span_id":   spanID,
		"parent_id": parentID,
		"operation": operation,
	}).Debug("Span started")

	return span, ctx
}

// FinishSpan finishes a span
func (t *Tracer) FinishSpan(span *Span) {
	if span.EndTime != nil {
		return // Already finished
	}

	endTime := time.Now()
	duration := endTime.Sub(span.StartTime)

	span.EndTime = &endTime
	span.Duration = &duration

	// Log span completion
	ctx := context.WithValue(context.Background(), ContextKeyTraceID, span.TraceID)
	ctx = context.WithValue(ctx, "span_id", span.SpanID)

	logFields := map[string]interface{}{
		"trace_id":    span.TraceID,
		"span_id":     span.SpanID,
		"operation":   span.Operation,
		"duration_ms": duration.Milliseconds(),
		"status":      span.Status,
	}

	if span.Status == SpanStatusError {
		logFields["error"] = span.Error
		t.logger.WithContext(ctx).WithFields(logFields).Error("Span completed with error")
	} else {
		t.logger.WithContext(ctx).WithFields(logFields).Debug("Span completed successfully")
	}

	// Clean up span from memory after some time
	go func() {
		time.Sleep(5 * time.Minute)
		t.spansMutex.Lock()
		delete(t.spans, span.SpanID)
		t.spansMutex.Unlock()
	}()
}

// SetTag sets a tag on the span (thread-safe)
func (span *Span) SetTag(key string, value interface{}) {
	if span == nil || span.Tags == nil {
		return
	}
	// Note: Individual span operations are not mutex-protected as spans
	// are typically accessed by a single goroutine during their lifecycle
	span.Tags[key] = value
}

// SetError sets an error on the span
func (span *Span) SetError(err error) {
	if span == nil {
		return
	}
	span.Status = SpanStatusError
	if err != nil {
		span.Error = err.Error()
		span.SetTag("error", true)
		span.SetTag("error.message", err.Error())
	}
}

// LogEvent logs an event to the span
func (span *Span) LogEvent(level, message string, fields map[string]interface{}) {
	if span == nil {
		return
	}
	log := SpanLog{
		Timestamp: time.Now(),
		Level:     level,
		Message:   message,
		Fields:    fields,
	}
	span.Logs = append(span.Logs, log)
}

// TracingMiddleware returns a Gin middleware for distributed tracing
func (t *Tracer) TracingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract trace ID from headers
		traceID := c.GetHeader("X-Trace-ID")
		if traceID == "" {
			traceID = uuid.New().String()
		}

		// Start span for HTTP request
		operation := fmt.Sprintf("%s %s", c.Request.Method, c.FullPath())
		if c.FullPath() == "" {
			operation = fmt.Sprintf("%s %s", c.Request.Method, c.Request.URL.Path)
		}

		span, ctx := t.StartSpan(c.Request.Context(), operation)

		// Set HTTP-specific tags
		span.SetTag("http.method", c.Request.Method)
		span.SetTag("http.url", c.Request.URL.String())
		span.SetTag("http.user_agent", c.Request.UserAgent())
		span.SetTag("http.remote_addr", c.ClientIP())

		// Add trace ID to response headers
		c.Header("X-Trace-ID", traceID)

		// Update request context
		c.Request = c.Request.WithContext(ctx)

		// Process request
		c.Next()

		// Set response tags
		span.SetTag("http.status_code", c.Writer.Status())
		span.SetTag("http.response_size", c.Writer.Size())

		// Check for errors
		if len(c.Errors) > 0 {
			span.SetError(c.Errors.Last())
		} else if c.Writer.Status() >= 400 {
			span.SetError(fmt.Errorf("HTTP %d", c.Writer.Status()))
		}

		// Finish span
		t.FinishSpan(span)
	}
}

// HTTPClientTracing adds tracing to HTTP client requests
func (t *Tracer) HTTPClientTracing(ctx context.Context, req *http.Request) (*Span, *http.Request) {
	operation := fmt.Sprintf("HTTP %s %s", req.Method, req.URL.Host)
	span, newCtx := t.StartSpan(ctx, operation)

	// Set HTTP client tags
	span.SetTag("http.method", req.Method)
	span.SetTag("http.url", req.URL.String())
	span.SetTag("component", "http-client")

	// Add trace headers
	traceID := GetTraceID(newCtx)
	if traceID != "" {
		req.Header.Set("X-Trace-ID", traceID)
		req.Header.Set("X-Parent-Span-ID", span.SpanID)
	}

	// Update request context
	req = req.WithContext(newCtx)

	return span, req
}

// DatabaseTracing creates a span for database operations
func (t *Tracer) DatabaseTracing(ctx context.Context, operation, query string) (*Span, context.Context) {
	spanOperation := fmt.Sprintf("db.%s", operation)
	span, newCtx := t.StartSpan(ctx, spanOperation)

	// Set database tags
	span.SetTag("db.type", "postgresql")
	span.SetTag("db.operation", operation)
	span.SetTag("component", "database")

	// Only log query in debug mode to avoid sensitive data
	if t.logger.Level <= 5 { // Debug level
		span.SetTag("db.statement", query)
	}

	return span, newCtx
}

// BusinessOperationTracing creates a span for business operations
func (t *Tracer) BusinessOperationTracing(ctx context.Context, operation string) (*Span, context.Context) {
	span, newCtx := t.StartSpan(ctx, operation)

	// Set business operation tags
	span.SetTag("component", "business-logic")
	span.SetTag("operation.type", "business")

	return span, newCtx
}

// GetTraceID gets the trace ID from context
func GetTraceID(ctx context.Context) string {
	if traceID := ctx.Value(ContextKeyTraceID); traceID != nil {
		return traceID.(string)
	}
	return ""
}

// GetSpanID gets the span ID from context
func GetSpanID(ctx context.Context) string {
	if spanID := ctx.Value("span_id"); spanID != nil {
		return spanID.(string)
	}
	return ""
}

// SpanFromContext gets the current span from context
func (t *Tracer) SpanFromContext(ctx context.Context) *Span {
	spanID := GetSpanID(ctx)
	if spanID != "" {
		t.spansMutex.RLock()
		span := t.spans[spanID]
		t.spansMutex.RUnlock()
		return span
	}
	return nil
}

// GetActiveSpans returns all active spans (for debugging)
func (t *Tracer) GetActiveSpans() map[string]*Span {
	t.spansMutex.RLock()
	defer t.spansMutex.RUnlock()

	// Return a copy to avoid concurrent access issues
	spans := make(map[string]*Span, len(t.spans))
	for k, v := range t.spans {
		spans[k] = v
	}
	return spans
}
