package monitoring

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// MetricsManager manages Prometheus metrics for a service
type MetricsManager struct {
	serviceName string
	registry    *prometheus.Registry

	// HTTP metrics
	httpRequestsTotal    *prometheus.CounterVec
	httpRequestDuration  *prometheus.HistogramVec
	httpRequestsInFlight *prometheus.GaugeVec

	// Database metrics
	dbConnectionsOpen  *prometheus.GaugeVec
	dbConnectionsInUse *prometheus.GaugeVec
	dbQueryDuration    *prometheus.HistogramVec
	dbQueriesTotal     *prometheus.CounterVec

	// Application metrics
	appInfo        *prometheus.GaugeVec
	appUptime      *prometheus.GaugeVec
	appMemoryUsage *prometheus.GaugeVec
	appGoroutines  *prometheus.GaugeVec

	// Business metrics
	businessOperationsTotal   *prometheus.CounterVec
	businessOperationDuration *prometheus.HistogramVec

	// Secret mapping specific metrics
	secretRetrievalTotal         *prometheus.CounterVec
	secretRetrievalDuration      *prometheus.HistogramVec
	secretMappingValidationTotal *prometheus.CounterVec
	secretMappingErrors          *prometheus.CounterVec
	unauthorizedAccessAttempts   *prometheus.CounterVec
	workflowSecretInjections     *prometheus.CounterVec
}

// NewMetricsManager creates a new metrics manager
func NewMetricsManager(serviceName string) *MetricsManager {
	registry := prometheus.NewRegistry()

	mm := &MetricsManager{
		serviceName: serviceName,
		registry:    registry,
	}

	mm.initializeMetrics()
	mm.registerMetrics()

	return mm
}

// initializeMetrics initializes all Prometheus metrics
func (mm *MetricsManager) initializeMetrics() {
	// HTTP metrics
	mm.httpRequestsTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "http_requests_total",
			Help: "Total number of HTTP requests",
		},
		[]string{"service", "method", "endpoint", "status_code"},
	)

	mm.httpRequestDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "http_request_duration_seconds",
			Help:    "HTTP request duration in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"service", "method", "endpoint", "status_code"},
	)

	mm.httpRequestsInFlight = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "http_requests_in_flight",
			Help: "Number of HTTP requests currently being processed",
		},
		[]string{"service"},
	)

	// Database metrics
	mm.dbConnectionsOpen = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "db_connections_open",
			Help: "Number of open database connections",
		},
		[]string{"service", "database"},
	)

	mm.dbConnectionsInUse = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "db_connections_in_use",
			Help: "Number of database connections currently in use",
		},
		[]string{"service", "database"},
	)

	mm.dbQueryDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "db_query_duration_seconds",
			Help:    "Database query duration in seconds",
			Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0},
		},
		[]string{"service", "database", "operation"},
	)

	mm.dbQueriesTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "db_queries_total",
			Help: "Total number of database queries",
		},
		[]string{"service", "database", "operation", "status"},
	)

	// Application metrics
	mm.appInfo = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "app_info",
			Help: "Application information",
		},
		[]string{"service", "version", "environment"},
	)

	mm.appUptime = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "app_uptime_seconds",
			Help: "Application uptime in seconds",
		},
		[]string{"service"},
	)

	mm.appMemoryUsage = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "app_memory_usage_bytes",
			Help: "Application memory usage in bytes",
		},
		[]string{"service", "type"},
	)

	mm.appGoroutines = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "app_goroutines",
			Help: "Number of goroutines",
		},
		[]string{"service"},
	)

	// Business metrics
	mm.businessOperationsTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "business_operations_total",
			Help: "Total number of business operations",
		},
		[]string{"service", "operation", "status"},
	)

	mm.businessOperationDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "business_operation_duration_seconds",
			Help:    "Business operation duration in seconds",
			Buckets: []float64{0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0},
		},
		[]string{"service", "operation"},
	)

	// Secret mapping metrics
	mm.secretRetrievalTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "secret_retrieval_total",
			Help: "Total number of secret retrieval operations",
		},
		[]string{"service", "project_id", "status"},
	)

	mm.secretRetrievalDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "secret_retrieval_duration_seconds",
			Help:    "Duration of secret retrieval operations",
			Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0},
		},
		[]string{"service", "project_id"},
	)

	mm.secretMappingValidationTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "secret_mapping_validation_total",
			Help: "Total number of secret mapping validation operations",
		},
		[]string{"service", "template_id", "project_id", "status"},
	)

	mm.secretMappingErrors = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "secret_mapping_errors_total",
			Help: "Total number of secret mapping errors",
		},
		[]string{"service", "error_type", "project_id"},
	)

	mm.unauthorizedAccessAttempts = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "unauthorized_secret_access_attempts_total",
			Help: "Total number of unauthorized secret access attempts",
		},
		[]string{"service", "user_id", "project_id", "source_ip"},
	)

	mm.workflowSecretInjections = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "workflow_secret_injections_total",
			Help: "Total number of secret injections into workflow steps",
		},
		[]string{"service", "workflow_id", "project_id", "status"},
	)
}

// registerMetrics registers all metrics with the registry
func (mm *MetricsManager) registerMetrics() {
	mm.registry.MustRegister(
		mm.httpRequestsTotal,
		mm.httpRequestDuration,
		mm.httpRequestsInFlight,
		mm.dbConnectionsOpen,
		mm.dbConnectionsInUse,
		mm.dbQueryDuration,
		mm.dbQueriesTotal,
		mm.appInfo,
		mm.appUptime,
		mm.appMemoryUsage,
		mm.appGoroutines,
		mm.businessOperationsTotal,
		mm.businessOperationDuration,
		mm.secretRetrievalTotal,
		mm.secretRetrievalDuration,
		mm.secretMappingValidationTotal,
		mm.secretMappingErrors,
		mm.unauthorizedAccessAttempts,
		mm.workflowSecretInjections,
	)
}

// GetRegistry returns the Prometheus registry
func (mm *MetricsManager) GetRegistry() *prometheus.Registry {
	return mm.registry
}

// MetricsHandler returns a Gin handler for Prometheus metrics
func (mm *MetricsManager) MetricsHandler() gin.HandlerFunc {
	handler := promhttp.HandlerFor(mm.registry, promhttp.HandlerOpts{})
	return gin.WrapH(handler)
}

// HTTPMiddleware returns a Gin middleware for HTTP metrics collection
func (mm *MetricsManager) HTTPMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		// Increment in-flight requests
		mm.httpRequestsInFlight.WithLabelValues(mm.serviceName).Inc()
		defer mm.httpRequestsInFlight.WithLabelValues(mm.serviceName).Dec()

		// Process request
		c.Next()

		// Record metrics
		duration := time.Since(start).Seconds()
		statusCode := strconv.Itoa(c.Writer.Status())
		endpoint := c.FullPath()
		if endpoint == "" {
			endpoint = "unknown"
		}

		mm.httpRequestsTotal.WithLabelValues(
			mm.serviceName,
			c.Request.Method,
			endpoint,
			statusCode,
		).Inc()

		mm.httpRequestDuration.WithLabelValues(
			mm.serviceName,
			c.Request.Method,
			endpoint,
			statusCode,
		).Observe(duration)
	}
}

// RecordDBConnection records database connection metrics
func (mm *MetricsManager) RecordDBConnection(database string, open, inUse int) {
	mm.dbConnectionsOpen.WithLabelValues(mm.serviceName, database).Set(float64(open))
	mm.dbConnectionsInUse.WithLabelValues(mm.serviceName, database).Set(float64(inUse))
}

// RecordDBQuery records database query metrics
func (mm *MetricsManager) RecordDBQuery(database, operation, status string, duration time.Duration) {
	mm.dbQueriesTotal.WithLabelValues(mm.serviceName, database, operation, status).Inc()
	mm.dbQueryDuration.WithLabelValues(mm.serviceName, database, operation).Observe(duration.Seconds())
}

// SetAppInfo sets application information
func (mm *MetricsManager) SetAppInfo(version, environment string) {
	mm.appInfo.WithLabelValues(mm.serviceName, version, environment).Set(1)
}

// RecordAppUptime records application uptime
func (mm *MetricsManager) RecordAppUptime(uptime time.Duration) {
	mm.appUptime.WithLabelValues(mm.serviceName).Set(uptime.Seconds())
}

// RecordMemoryUsage records memory usage metrics
func (mm *MetricsManager) RecordMemoryUsage(allocBytes, sysBytes uint64, goroutines int) {
	mm.appMemoryUsage.WithLabelValues(mm.serviceName, "alloc").Set(float64(allocBytes))
	mm.appMemoryUsage.WithLabelValues(mm.serviceName, "sys").Set(float64(sysBytes))
	mm.appGoroutines.WithLabelValues(mm.serviceName).Set(float64(goroutines))
}

// RecordBusinessOperation records business operation metrics
func (mm *MetricsManager) RecordBusinessOperation(operation, status string, duration time.Duration) {
	mm.businessOperationsTotal.WithLabelValues(mm.serviceName, operation, status).Inc()
	mm.businessOperationDuration.WithLabelValues(mm.serviceName, operation).Observe(duration.Seconds())
}

// BusinessOperationTimer helps time business operations
type BusinessOperationTimer struct {
	mm        *MetricsManager
	operation string
	start     time.Time
}

// StartBusinessOperation starts timing a business operation
func (mm *MetricsManager) StartBusinessOperation(operation string) *BusinessOperationTimer {
	return &BusinessOperationTimer{
		mm:        mm,
		operation: operation,
		start:     time.Now(),
	}
}

// Finish finishes timing the business operation
func (bot *BusinessOperationTimer) Finish(status string) {
	duration := time.Since(bot.start)
	bot.mm.RecordBusinessOperation(bot.operation, status, duration)
}

// Secret mapping specific methods
func (mm *MetricsManager) RecordSecretRetrieval(projectID, status string, duration time.Duration) {
	mm.secretRetrievalTotal.WithLabelValues(mm.serviceName, projectID, status).Inc()
	mm.secretRetrievalDuration.WithLabelValues(mm.serviceName, projectID).Observe(duration.Seconds())
}

func (mm *MetricsManager) RecordSecretMappingValidation(templateID, projectID, status string) {
	mm.secretMappingValidationTotal.WithLabelValues(mm.serviceName, templateID, projectID, status).Inc()
}

func (mm *MetricsManager) RecordSecretMappingError(errorType, projectID string) {
	mm.secretMappingErrors.WithLabelValues(mm.serviceName, errorType, projectID).Inc()
}

func (mm *MetricsManager) RecordUnauthorizedAccess(userID, projectID, sourceIP string) {
	mm.unauthorizedAccessAttempts.WithLabelValues(mm.serviceName, userID, projectID, sourceIP).Inc()
}

func (mm *MetricsManager) RecordWorkflowSecretInjection(workflowID, projectID, status string) {
	mm.workflowSecretInjections.WithLabelValues(mm.serviceName, workflowID, projectID, status).Inc()
}

// RecordCustomMetric records a custom metric with labels
func (mm *MetricsManager) RecordCustomMetric(name string, value float64, labels map[string]string) {
	// Create a gauge metric for custom metrics
	gauge := prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: name,
			Help: "Custom metric: " + name,
		},
		getLabelNames(labels),
	)

	// Register the metric if not already registered
	if err := mm.registry.Register(gauge); err != nil {
		// If already registered, try to get the existing metric
		if are, ok := err.(prometheus.AlreadyRegisteredError); ok {
			if existingGauge, ok := are.ExistingCollector.(*prometheus.GaugeVec); ok {
				gauge = existingGauge
			}
		}
	}

	// Set the value with labels
	labelValues := getLabelValues(labels)
	gauge.WithLabelValues(labelValues...).Set(value)
}

// Helper function to get label names from map
func getLabelNames(labels map[string]string) []string {
	names := make([]string, 0, len(labels))
	for name := range labels {
		names = append(names, name)
	}
	return names
}

// Helper function to get label values from map
func getLabelValues(labels map[string]string) []string {
	values := make([]string, 0, len(labels))
	for _, value := range labels {
		values = append(values, value)
	}
	return values
}
