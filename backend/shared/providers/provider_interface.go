package providers

import (
	"context"
	"time"
)

// ProviderType represents the type of infrastructure provider
type ProviderType string

// Core provider types - easily extensible
const (
	// Kubernetes providers
	ProviderGKE       ProviderType = "gke"
	ProviderAKS       ProviderType = "aks"
	ProviderEKS       ProviderType = "eks"
	ProviderOpenShift ProviderType = "openshift"
	ProviderK3s       ProviderType = "k3s"
	ProviderMicroK8s  ProviderType = "microk8s"
	ProviderRancher   ProviderType = "rancher"

	// Cloud VM providers
	ProviderGCE          ProviderType = "gce"
	ProviderEC2          ProviderType = "ec2"
	ProviderAzureVM      ProviderType = "azure-vm"
	ProviderDigitalOcean ProviderType = "digitalocean"
	ProviderLinode       ProviderType = "linode"
	ProviderVultr        ProviderType = "vultr"

	// Container platforms
	ProviderDockerSwarm ProviderType = "docker-swarm"
	ProviderNomad       ProviderType = "nomad"
	ProviderMesos       ProviderType = "mesos"

	// Serverless platforms
	ProviderLambda            ProviderType = "lambda"
	ProviderCloudFunctions    ProviderType = "cloud-functions"
	ProviderAzureFunctions    ProviderType = "azure-functions"
	ProviderCloudflareWorkers ProviderType = "cloudflare-workers"
	ProviderVercel            ProviderType = "vercel"
	ProviderNetlify           ProviderType = "netlify"

	// Edge computing
	ProviderCloudflareEdge ProviderType = "cloudflare-edge"
	ProviderAWSWavelength  ProviderType = "aws-wavelength"
	ProviderAzureEdge      ProviderType = "azure-edge"

	// On-premise/Hybrid
	ProviderBareMetal ProviderType = "bare-metal"
	ProviderVMware    ProviderType = "vmware"
	ProviderHyperV    ProviderType = "hyper-v"
	ProviderProxmox   ProviderType = "proxmox"
	ProviderOpenStack ProviderType = "openstack"

	// CI/CD platforms
	ProviderGitHubActions ProviderType = "github-actions"
	ProviderGitLabCI      ProviderType = "gitlab-ci"
	ProviderJenkins       ProviderType = "jenkins"
	ProviderCircleCI      ProviderType = "circleci"
	ProviderTravisCI      ProviderType = "travis-ci"
)

// AuthMethod represents authentication methods
type AuthMethod string

const (
	AuthServiceAccount AuthMethod = "service-account"
	AuthOAuth          AuthMethod = "oauth"
	AuthAPIKey         AuthMethod = "api-key"
	AuthToken          AuthMethod = "token"
	AuthCertificate    AuthMethod = "certificate"
	AuthBasic          AuthMethod = "basic"
	AuthSSH            AuthMethod = "ssh"
	AuthIAM            AuthMethod = "iam"
	AuthMTLS           AuthMethod = "mtls"
)

// DeploymentStrategy represents deployment strategies
type DeploymentStrategy string

const (
	StrategyRolling   DeploymentStrategy = "rolling"
	StrategyBlueGreen DeploymentStrategy = "blue-green"
	StrategyCanary    DeploymentStrategy = "canary"
	StrategyRecreate  DeploymentStrategy = "recreate"
	StrategyImmediate DeploymentStrategy = "immediate"
)

// ProviderCapability represents what a provider can do
type ProviderCapability string

const (
	CapabilityContainers        ProviderCapability = "containers"
	CapabilityLoadBalancing     ProviderCapability = "load-balancing"
	CapabilityAutoScaling       ProviderCapability = "auto-scaling"
	CapabilityPersistentStorage ProviderCapability = "persistent-storage"
	CapabilityNetworking        ProviderCapability = "networking"
	CapabilityMonitoring        ProviderCapability = "monitoring"
	CapabilityLogging           ProviderCapability = "logging"
	CapabilitySecrets           ProviderCapability = "secrets"
	CapabilityIngress           ProviderCapability = "ingress"
	CapabilityServiceMesh       ProviderCapability = "service-mesh"
	CapabilityGPU               ProviderCapability = "gpu"
	CapabilitySpotInstances     ProviderCapability = "spot-instances"
	CapabilityMultiRegion       ProviderCapability = "multi-region"
	CapabilityBackup            ProviderCapability = "backup"
	CapabilityDisasterRecovery  ProviderCapability = "disaster-recovery"
)

// ConfigField represents a configuration field for the provider
type ConfigField struct {
	Name        string      `json:"name"`
	Type        string      `json:"type"` // string, number, boolean, select, multiselect, password, file
	Label       string      `json:"label"`
	Description string      `json:"description"`
	Required    bool        `json:"required"`
	Default     interface{} `json:"default,omitempty"`
	Options     []Option    `json:"options,omitempty"` // for select/multiselect
	Validation  Validation  `json:"validation,omitempty"`
	Sensitive   bool        `json:"sensitive"`           // for passwords, tokens, etc.
	Group       string      `json:"group,omitempty"`     // for grouping fields in UI
	DependsOn   string      `json:"dependsOn,omitempty"` // conditional fields
	Placeholder string      `json:"placeholder,omitempty"`
}

type Option struct {
	Value string `json:"value"`
	Label string `json:"label"`
}

type Validation struct {
	Pattern   string `json:"pattern,omitempty"` // regex pattern
	Min       *int   `json:"min,omitempty"`     // for numbers
	Max       *int   `json:"max,omitempty"`     // for numbers
	MinLength *int   `json:"minLength,omitempty"`
	MaxLength *int   `json:"maxLength,omitempty"`
}

// ProviderInfo contains metadata about a provider
type ProviderInfo struct {
	Type          ProviderType         `json:"type"`
	Name          string               `json:"name"`
	Description   string               `json:"description"`
	Category      string               `json:"category"` // kubernetes, vm, serverless, etc.
	Icon          string               `json:"icon,omitempty"`
	Documentation string               `json:"documentation,omitempty"`
	Capabilities  []ProviderCapability `json:"capabilities"`
	AuthMethods   []AuthMethod         `json:"authMethods"`
	Strategies    []DeploymentStrategy `json:"strategies"`
	ConfigFields  []ConfigField        `json:"configFields"`
	Regions       []Region             `json:"regions,omitempty"`
	InstanceTypes []InstanceType       `json:"instanceTypes,omitempty"`
	Versions      []Version            `json:"versions,omitempty"`
}

type Region struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	Location  string `json:"location"`
	Available bool   `json:"available"`
}

type InstanceType struct {
	ID           string   `json:"id"`
	Name         string   `json:"name"`
	CPU          string   `json:"cpu"`
	Memory       string   `json:"memory"`
	Storage      string   `json:"storage,omitempty"`
	Network      string   `json:"network,omitempty"`
	GPU          string   `json:"gpu,omitempty"`
	PricePerHour *float64 `json:"pricePerHour,omitempty"`
}

type Version struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	Supported  bool   `json:"supported"`
	Default    bool   `json:"default"`
	Deprecated bool   `json:"deprecated"`
}

// ConnectionTestResult represents the result of testing a connection
type ConnectionTestResult struct {
	Success  bool              `json:"success"`
	Message  string            `json:"message"`
	Details  string            `json:"details,omitempty"`
	Latency  time.Duration     `json:"latency,omitempty"`
	Metadata map[string]string `json:"metadata,omitempty"`
	TestedAt time.Time         `json:"testedAt"`
}

// DeploymentResult represents the result of a deployment
type DeploymentResult struct {
	Success    bool                   `json:"success"`
	Message    string                 `json:"message"`
	Services   []DeployedService      `json:"services,omitempty"`
	Endpoints  []ServiceEndpoint      `json:"endpoints,omitempty"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
	DeployedAt time.Time              `json:"deployedAt"`
}

type DeployedService struct {
	Name      string            `json:"name"`
	Type      string            `json:"type"`
	Version   string            `json:"version"`
	Status    string            `json:"status"`
	Replicas  int               `json:"replicas,omitempty"`
	Resources map[string]string `json:"resources,omitempty"`
	Labels    map[string]string `json:"labels,omitempty"`
}

type ServiceEndpoint struct {
	Name     string `json:"name"`
	URL      string `json:"url"`
	Type     string `json:"type"` // http, https, grpc, tcp
	Port     int    `json:"port"`
	Protocol string `json:"protocol"`
	Public   bool   `json:"public"`
}

// Provider interface that all providers must implement
type Provider interface {
	// GetInfo returns metadata about this provider
	GetInfo() ProviderInfo

	// ValidateConfig validates the provider configuration
	ValidateConfig(config map[string]interface{}) error

	// TestConnection tests connectivity to the provider
	TestConnection(ctx context.Context, config map[string]interface{}) (*ConnectionTestResult, error)

	// Deploy deploys services to this provider
	Deploy(ctx context.Context, config map[string]interface{}, deployment DeploymentRequest) (*DeploymentResult, error)

	// GetStatus gets the current status of deployed services
	GetStatus(ctx context.Context, config map[string]interface{}, deploymentID string) (*DeploymentStatus, error)

	// GetLogs retrieves logs from deployed services
	GetLogs(ctx context.Context, config map[string]interface{}, deploymentID string, options LogOptions) ([]LogEntry, error)

	// Scale scales the deployed services
	Scale(ctx context.Context, config map[string]interface{}, deploymentID string, replicas int) error

	// Delete removes deployed services
	Delete(ctx context.Context, config map[string]interface{}, deploymentID string) error

	// GetMetrics retrieves metrics from deployed services
	GetMetrics(ctx context.Context, config map[string]interface{}, deploymentID string) (map[string]interface{}, error)
}

// DeploymentRequest represents a deployment request
type DeploymentRequest struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Version     string                 `json:"version"`
	Services    []ServiceSpec          `json:"services"`
	Environment map[string]string      `json:"environment,omitempty"`
	Secrets     map[string]string      `json:"secrets,omitempty"`
	Strategy    DeploymentStrategy     `json:"strategy"`
	Resources   ResourceRequirements   `json:"resources,omitempty"`
	Networking  NetworkingSpec         `json:"networking,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

type ServiceSpec struct {
	Name      string                 `json:"name"`
	Image     string                 `json:"image"`
	Tag       string                 `json:"tag"`
	Ports     []PortSpec             `json:"ports,omitempty"`
	Command   []string               `json:"command,omitempty"`
	Args      []string               `json:"args,omitempty"`
	Env       map[string]string      `json:"env,omitempty"`
	Volumes   []VolumeSpec           `json:"volumes,omitempty"`
	Resources ResourceRequirements   `json:"resources,omitempty"`
	Health    HealthCheckSpec        `json:"health,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

type PortSpec struct {
	Name       string `json:"name,omitempty"`
	Port       int    `json:"port"`
	TargetPort int    `json:"targetPort,omitempty"`
	Protocol   string `json:"protocol,omitempty"`
	Public     bool   `json:"public,omitempty"`
}

type VolumeSpec struct {
	Name      string `json:"name"`
	MountPath string `json:"mountPath"`
	Type      string `json:"type"` // emptyDir, hostPath, persistentVolume, secret, configMap
	Source    string `json:"source,omitempty"`
	ReadOnly  bool   `json:"readOnly,omitempty"`
}

type ResourceRequirements struct {
	CPU     string `json:"cpu,omitempty"`
	Memory  string `json:"memory,omitempty"`
	Storage string `json:"storage,omitempty"`
	GPU     string `json:"gpu,omitempty"`
}

type NetworkingSpec struct {
	Ingress      []IngressSpec `json:"ingress,omitempty"`
	LoadBalancer bool          `json:"loadBalancer,omitempty"`
	ServiceMesh  bool          `json:"serviceMesh,omitempty"`
}

type IngressSpec struct {
	Host        string            `json:"host"`
	Paths       []IngressPath     `json:"paths"`
	TLS         bool              `json:"tls,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`
}

type IngressPath struct {
	Path        string `json:"path"`
	ServiceName string `json:"serviceName"`
	ServicePort int    `json:"servicePort"`
}

type HealthCheckSpec struct {
	Type                string   `json:"type"` // http, tcp, exec
	Path                string   `json:"path,omitempty"`
	Port                int      `json:"port,omitempty"`
	Command             []string `json:"command,omitempty"`
	InitialDelaySeconds int      `json:"initialDelaySeconds,omitempty"`
	PeriodSeconds       int      `json:"periodSeconds,omitempty"`
	TimeoutSeconds      int      `json:"timeoutSeconds,omitempty"`
	FailureThreshold    int      `json:"failureThreshold,omitempty"`
}

// DeploymentStatus represents the status of a deployment
type DeploymentStatus struct {
	ID        string            `json:"id"`
	Status    string            `json:"status"` // pending, running, success, failed
	Services  []ServiceStatus   `json:"services"`
	Endpoints []ServiceEndpoint `json:"endpoints,omitempty"`
	Message   string            `json:"message,omitempty"`
	UpdatedAt time.Time         `json:"updatedAt"`
}

type ServiceStatus struct {
	Name            string    `json:"name"`
	Status          string    `json:"status"`
	ReadyReplicas   int       `json:"readyReplicas"`
	DesiredReplicas int       `json:"desiredReplicas"`
	Message         string    `json:"message,omitempty"`
	UpdatedAt       time.Time `json:"updatedAt"`
}

// LogOptions for retrieving logs
type LogOptions struct {
	Since     *time.Time `json:"since,omitempty"`
	Until     *time.Time `json:"until,omitempty"`
	Lines     *int       `json:"lines,omitempty"`
	Follow    bool       `json:"follow,omitempty"`
	Service   string     `json:"service,omitempty"`
	Container string     `json:"container,omitempty"`
}

type LogEntry struct {
	Timestamp time.Time `json:"timestamp"`
	Service   string    `json:"service"`
	Container string    `json:"container,omitempty"`
	Level     string    `json:"level,omitempty"`
	Message   string    `json:"message"`
}
