package providers

import (
	"fmt"
	"sort"
	"sync"
)

// ProviderRegistry manages all available providers
type ProviderRegistry struct {
	providers map[ProviderType]Provider
	mutex     sync.RWMutex
}

// Global registry instance
var globalRegistry = &ProviderRegistry{
	providers: make(map[ProviderType]Provider),
}

// GetRegistry returns the global provider registry
func GetRegistry() *ProviderRegistry {
	return globalRegistry
}

// Register registers a new provider
func (r *ProviderRegistry) Register(provider Provider) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	info := provider.GetInfo()
	if info.Type == "" {
		return fmt.Errorf("provider type cannot be empty")
	}

	if _, exists := r.providers[info.Type]; exists {
		return fmt.Errorf("provider %s is already registered", info.Type)
	}

	r.providers[info.Type] = provider
	return nil
}

// Unregister removes a provider from the registry
func (r *ProviderRegistry) Unregister(providerType ProviderType) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	delete(r.providers, providerType)
}

// Get retrieves a provider by type
func (r *ProviderRegistry) Get(providerType ProviderType) (Provider, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	provider, exists := r.providers[providerType]
	if !exists {
		return nil, fmt.Errorf("provider %s not found", providerType)
	}

	return provider, nil
}

// List returns all registered providers
func (r *ProviderRegistry) List() []Provider {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	providers := make([]Provider, 0, len(r.providers))
	for _, provider := range r.providers {
		providers = append(providers, provider)
	}

	return providers
}

// ListByCategory returns providers filtered by category
func (r *ProviderRegistry) ListByCategory(category string) []Provider {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var providers []Provider
	for _, provider := range r.providers {
		info := provider.GetInfo()
		if info.Category == category {
			providers = append(providers, provider)
		}
	}

	return providers
}

// GetInfo returns information about all registered providers
func (r *ProviderRegistry) GetInfo() []ProviderInfo {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	infos := make([]ProviderInfo, 0, len(r.providers))
	for _, provider := range r.providers {
		infos = append(infos, provider.GetInfo())
	}

	// Sort by category and then by name
	sort.Slice(infos, func(i, j int) bool {
		if infos[i].Category != infos[j].Category {
			return infos[i].Category < infos[j].Category
		}
		return infos[i].Name < infos[j].Name
	})

	return infos
}

// GetCategories returns all available categories
func (r *ProviderRegistry) GetCategories() []string {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	categorySet := make(map[string]bool)
	for _, provider := range r.providers {
		info := provider.GetInfo()
		categorySet[info.Category] = true
	}

	categories := make([]string, 0, len(categorySet))
	for category := range categorySet {
		categories = append(categories, category)
	}

	sort.Strings(categories)
	return categories
}

// HasCapability checks if any provider supports a specific capability
func (r *ProviderRegistry) HasCapability(capability ProviderCapability) bool {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	for _, provider := range r.providers {
		info := provider.GetInfo()
		for _, cap := range info.Capabilities {
			if cap == capability {
				return true
			}
		}
	}

	return false
}

// GetProvidersByCapability returns providers that support a specific capability
func (r *ProviderRegistry) GetProvidersByCapability(capability ProviderCapability) []Provider {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var providers []Provider
	for _, provider := range r.providers {
		info := provider.GetInfo()
		for _, cap := range info.Capabilities {
			if cap == capability {
				providers = append(providers, provider)
				break
			}
		}
	}

	return providers
}

// Exists checks if a provider type is registered
func (r *ProviderRegistry) Exists(providerType ProviderType) bool {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	_, exists := r.providers[providerType]
	return exists
}

// Count returns the number of registered providers
func (r *ProviderRegistry) Count() int {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return len(r.providers)
}

// Convenience functions for global registry

// Register registers a provider in the global registry
func Register(provider Provider) error {
	return globalRegistry.Register(provider)
}

// Get retrieves a provider from the global registry
func Get(providerType ProviderType) (Provider, error) {
	return globalRegistry.Get(providerType)
}

// List returns all providers from the global registry
func List() []Provider {
	return globalRegistry.List()
}

// ListByCategory returns providers by category from the global registry
func ListByCategory(category string) []Provider {
	return globalRegistry.ListByCategory(category)
}

// GetInfo returns provider information from the global registry
func GetInfo() []ProviderInfo {
	return globalRegistry.GetInfo()
}

// GetCategories returns all categories from the global registry
func GetCategories() []string {
	return globalRegistry.GetCategories()
}

// Exists checks if a provider exists in the global registry
func Exists(providerType ProviderType) bool {
	return globalRegistry.Exists(providerType)
}

// Provider categories
const (
	CategoryKubernetes = "kubernetes"
	CategoryVM         = "vm"
	CategoryServerless = "serverless"
	CategoryContainer  = "container"
	CategoryEdge       = "edge"
	CategoryHybrid     = "hybrid"
	CategoryCICD       = "cicd"
)

// GetProviderCategories returns all predefined categories
func GetProviderCategories() []string {
	return []string{
		CategoryKubernetes,
		CategoryVM,
		CategoryServerless,
		CategoryContainer,
		CategoryEdge,
		CategoryHybrid,
		CategoryCICD,
	}
}

// ProviderFactory is a function that creates a new provider instance
type ProviderFactory func() Provider

// FactoryRegistry manages provider factories for dynamic loading
type FactoryRegistry struct {
	factories map[ProviderType]ProviderFactory
	mutex     sync.RWMutex
}

var globalFactoryRegistry = &FactoryRegistry{
	factories: make(map[ProviderType]ProviderFactory),
}

// RegisterFactory registers a provider factory
func RegisterFactory(providerType ProviderType, factory ProviderFactory) {
	globalFactoryRegistry.mutex.Lock()
	defer globalFactoryRegistry.mutex.Unlock()
	globalFactoryRegistry.factories[providerType] = factory
}

// CreateProvider creates a new provider instance using the factory
func CreateProvider(providerType ProviderType) (Provider, error) {
	globalFactoryRegistry.mutex.RLock()
	factory, exists := globalFactoryRegistry.factories[providerType]
	globalFactoryRegistry.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("no factory registered for provider type %s", providerType)
	}

	return factory(), nil
}

// Auto-registration helper
func init() {
	// This will be called when the package is imported
	// Individual provider packages can register themselves here
}

// ProviderMetadata contains additional metadata for providers
type ProviderMetadata struct {
	Version      string                 `json:"version"`
	Author       string                 `json:"author"`
	License      string                 `json:"license"`
	Homepage     string                 `json:"homepage"`
	Repository   string                 `json:"repository"`
	Tags         []string               `json:"tags"`
	Experimental bool                   `json:"experimental"`
	Deprecated   bool                   `json:"deprecated"`
	Replacement  string                 `json:"replacement,omitempty"`
	MinVersion   string                 `json:"minVersion,omitempty"` // minimum orchestrator version
	MaxVersion   string                 `json:"maxVersion,omitempty"` // maximum orchestrator version
	Dependencies []string               `json:"dependencies,omitempty"`
	Conflicts    []string               `json:"conflicts,omitempty"`
	Extra        map[string]interface{} `json:"extra,omitempty"`
}

// ExtendedProviderInfo includes metadata
type ExtendedProviderInfo struct {
	ProviderInfo
	Metadata ProviderMetadata `json:"metadata"`
}

// ProviderWithMetadata extends the Provider interface with metadata
type ProviderWithMetadata interface {
	Provider
	GetMetadata() ProviderMetadata
}

// GetExtendedInfo returns extended information including metadata
func (r *ProviderRegistry) GetExtendedInfo() []ExtendedProviderInfo {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	infos := make([]ExtendedProviderInfo, 0, len(r.providers))
	for _, provider := range r.providers {
		extendedInfo := ExtendedProviderInfo{
			ProviderInfo: provider.GetInfo(),
		}

		// Check if provider supports metadata
		if providerWithMeta, ok := provider.(ProviderWithMetadata); ok {
			extendedInfo.Metadata = providerWithMeta.GetMetadata()
		}

		infos = append(infos, extendedInfo)
	}

	return infos
}
