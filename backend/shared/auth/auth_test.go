package auth

import (
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestJWTManager(t *testing.T) {
	secretKey := "test-secret-key"
	jwtManager := NewJWTManager(secretKey)

	t.Run("Generate and Verify Access Token", func(t *testing.T) {
		userID := "user-123"
		username := "testuser"
		email := "<EMAIL>"
		roles := []string{"user", "developer"}

		// Generate token
		token, err := jwtManager.GenerateAccessToken(userID, username, email, roles)
		require.NoError(t, err)
		require.NotEmpty(t, token)

		// Verify token
		claims, err := jwtManager.VerifyToken(token)
		require.NoError(t, err)
		require.NotNil(t, claims)

		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, username, claims.Username)
		assert.Equal(t, email, claims.Email)
		assert.Equal(t, roles, claims.Roles)
		assert.Equal(t, AccessToken, claims.TokenType)
	})

	t.Run("Generate and Verify Refresh Token", func(t *testing.T) {
		userID := "user-456"
		username := "refreshuser"
		email := "<EMAIL>"
		roles := []string{"user"}

		// Generate refresh token
		token, err := jwtManager.GenerateRefreshToken(userID, username, email, roles)
		require.NoError(t, err)
		require.NotEmpty(t, token)

		// Verify token
		claims, err := jwtManager.VerifyToken(token)
		require.NoError(t, err)
		require.NotNil(t, claims)

		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, RefreshToken, claims.TokenType)
	})

	t.Run("Refresh Tokens", func(t *testing.T) {
		userID := "user-789"
		username := "refreshtestuser"
		email := "<EMAIL>"
		roles := []string{"user", "admin"}

		// Generate initial refresh token
		refreshToken, err := jwtManager.GenerateRefreshToken(userID, username, email, roles)
		require.NoError(t, err)

		// Refresh tokens
		newAccessToken, newRefreshToken, err := jwtManager.RefreshTokens(refreshToken)
		require.NoError(t, err)
		require.NotEmpty(t, newAccessToken)
		require.NotEmpty(t, newRefreshToken)

		// Verify new access token
		accessClaims, err := jwtManager.VerifyToken(newAccessToken)
		require.NoError(t, err)
		assert.Equal(t, AccessToken, accessClaims.TokenType)
		assert.Equal(t, userID, accessClaims.UserID)

		// Verify new refresh token
		refreshClaims, err := jwtManager.VerifyToken(newRefreshToken)
		require.NoError(t, err)
		assert.Equal(t, RefreshToken, refreshClaims.TokenType)
		assert.Equal(t, userID, refreshClaims.UserID)
	})

	t.Run("Invalid Token", func(t *testing.T) {
		_, err := jwtManager.VerifyToken("invalid-token")
		assert.Error(t, err)
	})

	t.Run("Token with Wrong Secret", func(t *testing.T) {
		wrongManager := NewJWTManager("wrong-secret")
		token, err := wrongManager.GenerateAccessToken("user", "test", "<EMAIL>", []string{"user"})
		require.NoError(t, err)

		// Try to verify with correct manager
		_, err = jwtManager.VerifyToken(token)
		assert.Error(t, err)
	})
}

func TestAuthMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	jwtManager := NewJWTManager("test-secret")

	// Generate test token
	token, err := jwtManager.GenerateAccessToken("user-123", "testuser", "<EMAIL>", []string{"user"})
	require.NoError(t, err)

	t.Run("Valid Token", func(t *testing.T) {
		router := gin.New()
		router.Use(AuthMiddleware(jwtManager))

		router.GET("/protected", func(c *gin.Context) {
			userID, _ := GetUserIDFromContext(c)
			c.JSON(http.StatusOK, gin.H{"userID": userID})
		})

		req, _ := http.NewRequest("GET", "/protected", nil)
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "user-123")
	})

	t.Run("No Token", func(t *testing.T) {
		router := gin.New()
		router.Use(AuthMiddleware(jwtManager))

		router.GET("/protected", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "success"})
		})

		req, _ := http.NewRequest("GET", "/protected", nil)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("Invalid Token", func(t *testing.T) {
		router := gin.New()
		router.Use(AuthMiddleware(jwtManager))

		router.GET("/protected", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "success"})
		})

		req, _ := http.NewRequest("GET", "/protected", nil)
		req.Header.Set("Authorization", "Bearer invalid-token")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("Refresh Token Rejected", func(t *testing.T) {
		// Generate refresh token
		refreshToken, err := jwtManager.GenerateRefreshToken("user-123", "testuser", "<EMAIL>", []string{"user"})
		require.NoError(t, err)

		router := gin.New()
		router.Use(AuthMiddleware(jwtManager))

		router.GET("/protected", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "success"})
		})

		req, _ := http.NewRequest("GET", "/protected", nil)
		req.Header.Set("Authorization", "Bearer "+refreshToken)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

func TestAdminMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	jwtManager := NewJWTManager("test-secret")

	t.Run("Admin User", func(t *testing.T) {
		// Generate admin token
		token, err := jwtManager.GenerateAccessToken("admin-123", "admin", "<EMAIL>", []string{"admin"})
		require.NoError(t, err)

		router := gin.New()
		router.Use(AuthMiddleware(jwtManager))
		router.Use(AdminMiddleware())

		router.GET("/admin", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "admin access"})
		})

		req, _ := http.NewRequest("GET", "/admin", nil)
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Regular User", func(t *testing.T) {
		// Generate regular user token
		token, err := jwtManager.GenerateAccessToken("user-123", "user", "<EMAIL>", []string{"user"})
		require.NoError(t, err)

		router := gin.New()
		router.Use(AuthMiddleware(jwtManager))
		router.Use(AdminMiddleware())

		router.GET("/admin", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "admin access"})
		})

		req, _ := http.NewRequest("GET", "/admin", nil)
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)
	})
}

func TestRequireRoleMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	jwtManager := NewJWTManager("test-secret")

	t.Run("User with Required Role", func(t *testing.T) {
		// Generate developer token
		token, err := jwtManager.GenerateAccessToken("dev-123", "developer", "<EMAIL>", []string{"user", "developer"})
		require.NoError(t, err)

		router := gin.New()
		router.Use(AuthMiddleware(jwtManager))
		router.Use(RequireRoleMiddleware("developer", "moderator"))

		router.GET("/dev", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "developer access"})
		})

		req, _ := http.NewRequest("GET", "/dev", nil)
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("User without Required Role", func(t *testing.T) {
		// Generate regular user token
		token, err := jwtManager.GenerateAccessToken("user-123", "user", "<EMAIL>", []string{"user"})
		require.NoError(t, err)

		router := gin.New()
		router.Use(AuthMiddleware(jwtManager))
		router.Use(RequireRoleMiddleware("developer", "moderator"))

		router.GET("/dev", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "developer access"})
		})

		req, _ := http.NewRequest("GET", "/dev", nil)
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)
	})
}

func TestConfig(t *testing.T) {
	t.Run("Default Config", func(t *testing.T) {
		config := DefaultConfig()

		assert.NotEmpty(t, config.JWTSecretKey)
		assert.Equal(t, AccessTokenExpiry, config.AccessTokenExpiry)
		assert.Equal(t, RefreshTokenExpiry, config.RefreshTokenExpiry)
		assert.NotEmpty(t, config.AdminServiceURL)
	})

	t.Run("Config Validation", func(t *testing.T) {
		// Valid config
		config := &Config{
			JWTSecretKey:       "valid-secret-key",
			AccessTokenExpiry:  time.Hour,
			RefreshTokenExpiry: time.Hour * 24,
			AdminServiceURL:    "http://admin:8080",
		}

		err := config.Validate()
		assert.NoError(t, err)

		// Invalid config - empty secret
		config.JWTSecretKey = ""
		err = config.Validate()
		assert.Error(t, err)

		// Invalid config - default secret
		config.JWTSecretKey = "default-secret-key-change-in-production"
		err = config.Validate()
		assert.Error(t, err)
	})

	t.Run("New Auth Manager", func(t *testing.T) {
		config := &Config{
			JWTSecretKey:       "test-secret-key",
			AccessTokenExpiry:  time.Hour,
			RefreshTokenExpiry: time.Hour * 24,
			AdminServiceURL:    "http://admin:8080",
		}

		authManager, err := NewAuthManager(config)
		require.NoError(t, err)
		require.NotNil(t, authManager)
		require.NotNil(t, authManager.JWTManager)
		require.NotNil(t, authManager.PermissionService)
	})
}

func TestContextHelpers(t *testing.T) {
	gin.SetMode(gin.TestMode)

	jwtManager := NewJWTManager("test-secret")
	token, err := jwtManager.GenerateAccessToken("user-123", "testuser", "<EMAIL>", []string{"user", "developer"})
	require.NoError(t, err)

	router := gin.New()
	router.Use(AuthMiddleware(jwtManager))

	router.GET("/test", func(c *gin.Context) {
		userID, exists := GetUserIDFromContext(c)
		assert.True(t, exists)
		assert.Equal(t, "user-123", userID)

		roles, exists := GetUserRolesFromContext(c)
		assert.True(t, exists)
		assert.Contains(t, roles, "user")
		assert.Contains(t, roles, "developer")

		isAdmin := IsAdminFromContext(c)
		assert.False(t, isAdmin)

		hasDeveloper := HasRoleFromContext(c, "developer")
		assert.True(t, hasDeveloper)

		hasAdmin := HasRoleFromContext(c, "admin")
		assert.False(t, hasAdmin)

		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	req, _ := http.NewRequest("GET", "/test", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}
