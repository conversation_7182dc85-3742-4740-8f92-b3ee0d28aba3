package auth

import (
	"context"
	"fmt"

	"github.com/claudio/deploy-orchestrator/shared/models"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// ProjectAccessService handles project access validation using the group-project model
type ProjectAccessService struct {
	db *gorm.DB
}

// NewProjectAccessService creates a new ProjectAccessService
func NewProjectAccessService(db *gorm.DB) *ProjectAccessService {
	return &ProjectAccessService{
		db: db,
	}
}

// GetUserProjects returns all projects accessible to a user through their group memberships
func (s *ProjectAccessService) GetUserProjects(ctx context.Context, userID string) ([]models.UserProjectAccess, error) {
	var projects []models.UserProjectAccess

	err := s.db.WithContext(ctx).
		Table("user_project_access").
		Where("user_id = ?", userID).
		Find(&projects).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get user projects: %w", err)
	}

	return projects, nil
}

// CheckUserProjectAccess checks if a user has access to a specific project
func (s *ProjectAccessService) CheckUserProjectAccess(ctx context.Context, userID, projectID string) (bool, error) {
	var count int64

	err := s.db.WithContext(ctx).
		Table("user_project_access").
		Where("user_id = ? AND project_id = ?", userID, projectID).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check user project access: %w", err)
	}

	return count > 0, nil
}

// GetUserProjectIDs returns just the project IDs accessible to a user
func (s *ProjectAccessService) GetUserProjectIDs(ctx context.Context, userID string) ([]string, error) {
	var projectIDs []string

	err := s.db.WithContext(ctx).
		Table("user_project_access").
		Select("project_id").
		Where("user_id = ?", userID).
		Pluck("project_id", &projectIDs).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get user project IDs: %w", err)
	}

	return projectIDs, nil
}

// FilterProjectsByUserAccess filters a list of project IDs to only include those the user has access to
func (s *ProjectAccessService) FilterProjectsByUserAccess(ctx context.Context, userID string, projectIDs []string) ([]string, error) {
	if len(projectIDs) == 0 {
		return []string{}, nil
	}

	var accessibleProjectIDs []string

	err := s.db.WithContext(ctx).
		Table("user_project_access").
		Select("project_id").
		Where("user_id = ? AND project_id IN ?", userID, projectIDs).
		Pluck("project_id", &accessibleProjectIDs).Error

	if err != nil {
		return nil, fmt.Errorf("failed to filter projects by user access: %w", err)
	}

	return accessibleProjectIDs, nil
}

// GetGroupProjects returns all projects assigned to a group
func (s *ProjectAccessService) GetGroupProjects(ctx context.Context, groupID string) ([]models.Project, error) {
	var projects []models.Project

	err := s.db.WithContext(ctx).
		Table("projects p").
		Select("p.*").
		Joins("JOIN group_projects gp ON p.id = gp.project_id").
		Where("gp.group_id = ? AND p.deleted_at IS NULL", groupID).
		Find(&projects).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get group projects: %w", err)
	}

	return projects, nil
}

// GetProjectGroups returns all groups assigned to a project
func (s *ProjectAccessService) GetProjectGroups(ctx context.Context, projectID string) ([]models.Group, error) {
	var groups []models.Group

	err := s.db.WithContext(ctx).
		Table("groups g").
		Select("g.*").
		Joins("JOIN group_projects gp ON g.id = gp.group_id").
		Where("gp.project_id = ? AND g.deleted_at IS NULL", projectID).
		Find(&groups).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get project groups: %w", err)
	}

	return groups, nil
}

// ValidateUserProjectAccess validates that a user has access to a project and returns an error if not
func (s *ProjectAccessService) ValidateUserProjectAccess(ctx context.Context, userID, projectID string) error {
	hasAccess, err := s.CheckUserProjectAccess(ctx, userID, projectID)
	if err != nil {
		return fmt.Errorf("failed to check project access: %w", err)
	}

	if !hasAccess {
		return fmt.Errorf("user %s does not have access to project %s", userID, projectID)
	}

	return nil
}

// BuildProjectFilter builds a GORM where clause for filtering by user's accessible projects
func (s *ProjectAccessService) BuildProjectFilter(ctx context.Context, userID string, projectColumn string) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		subQuery := s.db.WithContext(ctx).
			Table("user_project_access").
			Select("project_id").
			Where("user_id = ?", userID)

		return db.Where(fmt.Sprintf("%s IN (?)", projectColumn), subQuery)
	}
}

// GetUserAccessibleResourcesQuery returns a query that filters resources by user's project access
// This is useful for filtering environments, workflows, secrets, etc. by project access
func (s *ProjectAccessService) GetUserAccessibleResourcesQuery(ctx context.Context, userID, resourceTable, projectColumn string) *gorm.DB {
	return s.db.WithContext(ctx).
		Table(resourceTable).
		Where(fmt.Sprintf("%s IN (?)", projectColumn),
			s.db.Table("user_project_access").
				Select("project_id").
				Where("user_id = ?", userID))
}

// IsUserAdmin checks if a user has admin role (admins bypass project access checks)
func (s *ProjectAccessService) IsUserAdmin(ctx context.Context, userID string) (bool, error) {
	var count int64

	err := s.db.WithContext(ctx).
		Table("user_groups ug").
		Joins("JOIN group_roles gr ON ug.group_id = gr.group_id").
		Joins("JOIN roles r ON gr.role_id = r.id").
		Where("ug.user_id = ? AND r.name = ? AND ug.deleted_at IS NULL", userID, "admin").
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check admin status: %w", err)
	}

	return count > 0, nil
}

// GetUserPermissions returns all permissions for a user across all their roles
func (s *ProjectAccessService) GetUserPermissions(ctx context.Context, userID string) ([]string, error) {
	var permissions []string

	err := s.db.WithContext(ctx).
		Table("role_permissions rp").
		Select("DISTINCT rp.permission").
		Joins("JOIN group_roles gr ON rp.role_id = gr.role_id").
		Joins("JOIN user_groups ug ON gr.group_id = ug.group_id").
		Where("ug.user_id = ? AND ug.deleted_at IS NULL", userID).
		Pluck("permission", &permissions).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get user permissions: %w", err)
	}

	return permissions, nil
}

// CheckUserPermission checks if a user has a specific permission
func (s *ProjectAccessService) CheckUserPermission(ctx context.Context, userID, permission string) (bool, error) {
	var count int64

	err := s.db.WithContext(ctx).
		Table("role_permissions rp").
		Joins("JOIN group_roles gr ON rp.role_id = gr.role_id").
		Joins("JOIN user_groups ug ON gr.group_id = ug.group_id").
		Where("ug.user_id = ? AND rp.permission = ? AND ug.deleted_at IS NULL", userID, permission).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check user permission: %w", err)
	}

	return count > 0, nil
}

// ProjectAccessMiddleware creates a middleware that validates project access for requests
func (s *ProjectAccessService) ProjectAccessMiddleware(projectParamName string) func(*gin.Context) {
	return func(c *gin.Context) {
		userID, exists := c.Get("userID")
		if !exists {
			c.AbortWithStatusJSON(401, gin.H{"error": "Authentication required"})
			return
		}

		projectID := c.Param(projectParamName)
		if projectID == "" {
			c.AbortWithStatusJSON(400, gin.H{"error": "Project ID required"})
			return
		}

		// Check if user is admin (admins bypass project access checks)
		isAdmin, err := s.IsUserAdmin(c.Request.Context(), userID.(string))
		if err != nil {
			c.AbortWithStatusJSON(500, gin.H{"error": "Failed to check admin status"})
			return
		}

		if isAdmin {
			c.Next()
			return
		}

		// Check project access for non-admin users
		hasAccess, err := s.CheckUserProjectAccess(c.Request.Context(), userID.(string), projectID)
		if err != nil {
			c.AbortWithStatusJSON(500, gin.H{"error": "Failed to check project access"})
			return
		}

		if !hasAccess {
			c.AbortWithStatusJSON(403, gin.H{"error": "Access denied: No permission for this project"})
			return
		}

		c.Next()
	}
}
