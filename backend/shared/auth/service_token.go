package auth

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// ServiceTokenManager handles service-to-service authentication tokens
type ServiceTokenManager struct {
	jwtManager *JWTManager
}

// NewServiceTokenManager creates a new service token manager
func NewServiceTokenManager(jwtManager *JWTManager) *ServiceTokenManager {
	return &ServiceTokenManager{
		jwtManager: jwtManager,
	}
}

// GenerateServiceToken generates a JWT token for service-to-service authentication
func (stm *ServiceTokenManager) GenerateServiceToken(serviceName, serviceVersion string) (string, error) {
	// Create service-specific claims
	userID := fmt.Sprintf("service:%s", serviceName)
	username := serviceName
	email := fmt.Sprintf("%<EMAIL>", serviceName)
	roles := []string{"service", "microservice"}

	// Generate a long-lived access token for service registration (24 hours)
	return stm.jwtManager.GenerateAccessTokenWithExpiry(
		userID,
		username,
		email,
		roles,
		24*time.Hour, // 24 hours expiry for service tokens
	)
}

// GenerateServiceTokenWithSecret generates a service token using a specific secret
func GenerateServiceTokenWithSecret(serviceName, serviceVersion, secret string) (string, error) {
	// Create JWT manager with the provided secret
	jwtManager := NewJWTManager(string([]byte(secret)))

	// Create service token manager
	stm := NewServiceTokenManager(jwtManager)

	// Generate the token
	return stm.GenerateServiceToken(serviceName, serviceVersion)
}

// VerifyServiceToken verifies a service token and returns service information
func (stm *ServiceTokenManager) VerifyServiceToken(tokenString string) (*Claims, error) {
	claims, err := stm.jwtManager.VerifyToken(tokenString)
	if err != nil {
		return nil, fmt.Errorf("invalid service token: %w", err)
	}

	// Verify it's a service token
	if claims.TokenType != AccessToken {
		return nil, fmt.Errorf("invalid token type for service authentication")
	}

	// Check if it's a service account (userID starts with "service:")
	if len(claims.UserID) < 8 || claims.UserID[:8] != "service:" {
		return nil, fmt.Errorf("token is not a service token")
	}

	// Check if service role is present
	hasServiceRole := false
	for _, role := range claims.Roles {
		if role == "service" || role == "microservice" {
			hasServiceRole = true
			break
		}
	}

	if !hasServiceRole {
		return nil, fmt.Errorf("token does not have service role")
	}

	return claims, nil
}

// ExtractServiceName extracts the service name from a service token
func (stm *ServiceTokenManager) ExtractServiceName(tokenString string) (string, error) {
	claims, err := stm.VerifyServiceToken(tokenString)
	if err != nil {
		return "", err
	}

	// Extract service name from userID (format: "service:service-name")
	if len(claims.UserID) > 8 {
		return claims.UserID[8:], nil // Remove "service:" prefix
	}

	return "", fmt.Errorf("invalid service token format")
}

// IsServiceToken checks if a token is a valid service token
func (stm *ServiceTokenManager) IsServiceToken(tokenString string) bool {
	_, err := stm.VerifyServiceToken(tokenString)
	return err == nil
}

// ServiceTokenClaims represents the claims for a service token
type ServiceTokenClaims struct {
	ServiceName    string `json:"service_name"`
	ServiceVersion string `json:"service_version"`
	jwt.RegisteredClaims
}

// GenerateSimpleServiceToken generates a simple service token with custom claims
func GenerateSimpleServiceToken(serviceName, serviceVersion, secret string, expiry time.Duration) (string, error) {
	claims := ServiceTokenClaims{
		ServiceName:    serviceName,
		ServiceVersion: serviceVersion,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(expiry)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "deploy-orchestrator",
			Subject:   fmt.Sprintf("service:%s", serviceName),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(secret))
}

// VerifySimpleServiceToken verifies a simple service token
func VerifySimpleServiceToken(tokenString, secret string) (*ServiceTokenClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &ServiceTokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(secret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("invalid token: %w", err)
	}

	if claims, ok := token.Claims.(*ServiceTokenClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token claims")
}
