package auth

import (
	"fmt"
)

// ResourceType represents different resource types in the system
type ResourceType string

// Common resource types
const (
	ResourceDeployment       ResourceType = "deployment"
	ResourceSchedule         ResourceType = "schedule"
	ResourceIntegration      ResourceType = "integration"
	ResourceProject          ResourceType = "project"
	ResourceUser             ResourceType = "user"
	ResourceRole             ResourceType = "role"
	ResourceUserGroup        ResourceType = "usergroup"
	ResourceSetting          ResourceType = "setting"
	ResourceApiKey           ResourceType = "apikey"
	ResourceIdentityProvider ResourceType = "identity-provider"
)

// ActionType represents different actions that can be performed on resources
type ActionType string

// Common action types
const (
	ActionCreate  ActionType = "create"
	ActionRead    ActionType = "read"
	ActionUpdate  ActionType = "update"
	ActionDelete  ActionType = "delete"
	ActionExecute ActionType = "execute"
)

// Permission represents a permission in the system
type Permission struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Resource    string `json:"resource"`
	Action      string `json:"action"`
}

// PermissionString formats a permission string from resource and action
func PermissionString(resource ResourceType, action ActionType) string {
	return fmt.Sprintf("%s:%s", resource, action)
}

// PermissionChecker interface defines methods for checking permissions
type PermissionChecker interface {
	// HasPermission checks if a user has permission to perform an action on a resource
	HasPermission(userID, projectID string, resource ResourceType, action ActionType, environment string) (bool, string)

	// HasPermissionByName checks if a user has a specific named permission
	HasPermissionByName(userID, projectID, permissionName, environment string) (bool, string)

	// GetUserProjectRoles returns all roles assigned to a user for a project
	GetUserProjectRoles(userID, projectID string) ([]string, error)

	// GetRolePermissions returns all permissions for a role
	GetRolePermissions(roleID string) ([]Permission, error)
}
