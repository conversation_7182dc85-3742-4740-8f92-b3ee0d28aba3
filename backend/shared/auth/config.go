package auth

import (
	"fmt"
	"os"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/config"
	"github.com/gin-gonic/gin"
)

// Config holds authentication configuration
type Config struct {
	JWTSecretKey       string        `json:"jwt_secret_key"`
	AccessTokenExpiry  time.Duration `json:"access_token_expiry"`
	RefreshTokenExpiry time.Duration `json:"refresh_token_expiry"`
	AdminServiceURL    string        `json:"admin_service_url"`
}

// DefaultConfig returns a default authentication configuration
func DefaultConfig() *Config {
	return &Config{
		JWTSecretKey:       getEnvOrDefault("JWT_SECRET_KEY", "default-secret-key-change-in-production"),
		AccessTokenExpiry:  AccessTokenExpiry,
		RefreshTokenExpiry: RefreshTokenExpiry,
		AdminServiceURL:    getEnvOrDefault("ADMIN_SERVICE_URL", "http://admin-service:8080"),
	}
}

// LoadConfigFromEnv loads authentication configuration from environment variables
func LoadConfigFromEnv() *Config {
	config := DefaultConfig()

	// Override with environment variables if present
	if secretKey := os.Getenv("JWT_SECRET_KEY"); secretKey != "" {
		config.JWTSecretKey = secretKey
	}

	if adminURL := os.Getenv("ADMIN_SERVICE_URL"); adminURL != "" {
		config.AdminServiceURL = adminURL
	}

	// Parse duration from environment variables
	if accessExpiry := os.Getenv("ACCESS_TOKEN_EXPIRY"); accessExpiry != "" {
		if duration, err := time.ParseDuration(accessExpiry); err == nil {
			config.AccessTokenExpiry = duration
		}
	}

	if refreshExpiry := os.Getenv("REFRESH_TOKEN_EXPIRY"); refreshExpiry != "" {
		if duration, err := time.ParseDuration(refreshExpiry); err == nil {
			config.RefreshTokenExpiry = duration
		}
	}

	return config
}

// Validate checks if the configuration is valid
func (c *Config) Validate() error {
	if c.JWTSecretKey == "" {
		return fmt.Errorf("JWT secret key is required")
	}

	if c.JWTSecretKey == "default-secret-key-change-in-production" {
		return fmt.Errorf("JWT secret key must be changed from default value in production")
	}

	if c.AccessTokenExpiry <= 0 {
		return fmt.Errorf("access token expiry must be positive")
	}

	if c.RefreshTokenExpiry <= 0 {
		return fmt.Errorf("refresh token expiry must be positive")
	}

	if c.AdminServiceURL == "" {
		return fmt.Errorf("admin service URL is required")
	}

	return nil
}

// NewJWTManagerFromConfig creates a new JWT manager from configuration
func (c *Config) NewJWTManagerFromConfig() *JWTManager {
	return &JWTManager{
		secretKey: []byte(c.JWTSecretKey),
	}
}

// NewHTTPPermissionServiceFromConfig creates a new HTTP permission service from configuration
func (c *Config) NewHTTPPermissionServiceFromConfig() *HTTPPermissionService {
	return NewHTTPPermissionService(c.AdminServiceURL)
}

// getEnvOrDefault returns the environment variable value or a default value
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// AuthManager provides a centralized way to manage authentication components
type AuthManager struct {
	Config            *Config
	JWTManager        *JWTManager
	PermissionService PermissionService
}

// NewAuthManager creates a new authentication manager
func NewAuthManager(config *Config) (*AuthManager, error) {
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid auth config: %w", err)
	}

	jwtManager := config.NewJWTManagerFromConfig()
	permissionService := config.NewHTTPPermissionServiceFromConfig()

	return &AuthManager{
		Config:            config,
		JWTManager:        jwtManager,
		PermissionService: permissionService,
	}, nil
}

// NewAuthManagerFromEnv creates a new authentication manager from environment variables
func NewAuthManagerFromEnv() (*AuthManager, error) {
	config := LoadConfigFromEnv()
	return NewAuthManager(config)
}

// AuthMiddleware returns the authentication middleware
func (am *AuthManager) AuthMiddleware() gin.HandlerFunc {
	return AuthMiddleware(am.JWTManager)
}

// OptionalAuthMiddleware returns the optional authentication middleware
func (am *AuthManager) OptionalAuthMiddleware() gin.HandlerFunc {
	return OptionalAuthMiddleware(am.JWTManager)
}

// AdminMiddleware returns the admin middleware
func (am *AuthManager) AdminMiddleware() gin.HandlerFunc {
	return AdminMiddleware()
}

// RequireRoleMiddleware returns the role requirement middleware
func (am *AuthManager) RequireRoleMiddleware(roles ...string) gin.HandlerFunc {
	return RequireRoleMiddleware(roles...)
}

// PermissionMiddleware returns the permission middleware
func (am *AuthManager) PermissionMiddleware() *PermissionMiddleware {
	return NewPermissionMiddleware(am.PermissionService)
}

// CORSMiddleware returns the CORS middleware
func (am *AuthManager) CORSMiddleware() gin.HandlerFunc {
	return CORSMiddleware()
}

// NewAuthManagerFromSharedConfig creates a new authentication manager from shared config
func NewAuthManagerFromSharedConfig(authConfig *config.AuthConfig) (*AuthManager, error) {
	// Parse duration strings
	accessTokenExpiry, err := time.ParseDuration(authConfig.AccessTokenExpiry)
	if err != nil {
		return nil, fmt.Errorf("invalid access token expiry duration '%s': %w", authConfig.AccessTokenExpiry, err)
	}

	refreshTokenExpiry, err := time.ParseDuration(authConfig.RefreshTokenExpiry)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token expiry duration '%s': %w", authConfig.RefreshTokenExpiry, err)
	}

	// Create auth config from shared config
	authManagerConfig := &Config{
		JWTSecretKey:       authConfig.JWTSecret,
		AccessTokenExpiry:  accessTokenExpiry,
		RefreshTokenExpiry: refreshTokenExpiry,
		AdminServiceURL:    authConfig.AdminServiceURL,
	}

	// Fallback to environment variables if config values are empty
	if authManagerConfig.JWTSecretKey == "" {
		authManagerConfig.JWTSecretKey = getEnvOrDefault("JWT_SECRET_KEY", "default-secret-key-change-in-production")
	}

	if authManagerConfig.AdminServiceURL == "" {
		authManagerConfig.AdminServiceURL = getEnvOrDefault("ADMIN_SERVICE_URL", "http://admin-service:8080")
	}

	return NewAuthManager(authManagerConfig)
}

// ToSharedConfig converts auth Config to shared config AuthConfig
func (c *Config) ToSharedConfig() *config.AuthConfig {
	return &config.AuthConfig{
		JWTSecret:            c.JWTSecretKey,
		JWTExpirationMinutes: int(c.AccessTokenExpiry.Minutes()),
		AccessTokenExpiry:    c.AccessTokenExpiry.String(),
		RefreshTokenExpiry:   c.RefreshTokenExpiry.String(),
		AdminServiceURL:      c.AdminServiceURL,
		APIKeyHeader:         "X-API-Key", // Default value
		DisableAuth:          false,       // Default value
	}
}

// LoadConfigFromSharedConfig loads authentication configuration from shared config with environment variable fallbacks
func LoadConfigFromSharedConfig(authConfig *config.AuthConfig) *Config {
	// Parse duration strings with fallbacks
	accessTokenExpiry := AccessTokenExpiry
	if authConfig.AccessTokenExpiry != "" {
		if duration, err := time.ParseDuration(authConfig.AccessTokenExpiry); err == nil {
			accessTokenExpiry = duration
		}
	}

	refreshTokenExpiry := RefreshTokenExpiry
	if authConfig.RefreshTokenExpiry != "" {
		if duration, err := time.ParseDuration(authConfig.RefreshTokenExpiry); err == nil {
			refreshTokenExpiry = duration
		}
	}

	// Create config with shared config values and environment variable fallbacks
	config := &Config{
		JWTSecretKey:       authConfig.JWTSecret,
		AccessTokenExpiry:  accessTokenExpiry,
		RefreshTokenExpiry: refreshTokenExpiry,
		AdminServiceURL:    authConfig.AdminServiceURL,
	}

	// Override with environment variables if present (environment takes precedence)
	if secretKey := os.Getenv("JWT_SECRET_KEY"); secretKey != "" {
		config.JWTSecretKey = secretKey
	}

	if adminURL := os.Getenv("ADMIN_SERVICE_URL"); adminURL != "" {
		config.AdminServiceURL = adminURL
	}

	// Parse duration from environment variables (environment takes precedence)
	if accessExpiry := os.Getenv("ACCESS_TOKEN_EXPIRY"); accessExpiry != "" {
		if duration, err := time.ParseDuration(accessExpiry); err == nil {
			config.AccessTokenExpiry = duration
		}
	}

	if refreshExpiry := os.Getenv("REFRESH_TOKEN_EXPIRY"); refreshExpiry != "" {
		if duration, err := time.ParseDuration(refreshExpiry); err == nil {
			config.RefreshTokenExpiry = duration
		}
	}

	// Use defaults if still empty
	if config.JWTSecretKey == "" {
		config.JWTSecretKey = getEnvOrDefault("JWT_SECRET_KEY", "default-secret-key-change-in-production")
	}

	if config.AdminServiceURL == "" {
		config.AdminServiceURL = getEnvOrDefault("ADMIN_SERVICE_URL", "http://admin-service:8080")
	}

	return config
}
