package auth

import (
	"os"
	"testing"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/config"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSharedConfigIntegration(t *testing.T) {
	t.Run("NewAuthManagerFromSharedConfig", func(t *testing.T) {
		// Create a shared config
		sharedConfig := &config.AuthConfig{
			JWTSecret:          "test-secret-key",
			AccessTokenExpiry:  "30m",
			RefreshTokenExpiry: "24h",
			AdminServiceURL:    "http://test-admin:8080",
			DisableAuth:        false,
		}

		// Create auth manager from shared config
		authManager, err := NewAuthManagerFromSharedConfig(sharedConfig)
		require.NoError(t, err)
		require.NotNil(t, authManager)

		// Verify the configuration was properly converted
		assert.Equal(t, "test-secret-key", authManager.Config.JWTSecretKey)
		assert.Equal(t, 30*time.Minute, authManager.Config.AccessTokenExpiry)
		assert.Equal(t, 24*time.Hour, authManager.Config.RefreshTokenExpiry)
		assert.Equal(t, "http://test-admin:8080", authManager.Config.AdminServiceURL)

		// Verify JWT manager is properly initialized
		assert.NotNil(t, authManager.JWTManager)
		assert.NotNil(t, authManager.PermissionService)
	})

	t.Run("NewAuthManagerFromSharedConfig_WithInvalidDuration", func(t *testing.T) {
		sharedConfig := &config.AuthConfig{
			JWTSecret:          "test-secret-key",
			AccessTokenExpiry:  "invalid-duration",
			RefreshTokenExpiry: "24h",
			AdminServiceURL:    "http://test-admin:8080",
		}

		authManager, err := NewAuthManagerFromSharedConfig(sharedConfig)
		assert.Error(t, err)
		assert.Nil(t, authManager)
		assert.Contains(t, err.Error(), "invalid access token expiry duration")
	})

	t.Run("LoadConfigFromSharedConfig", func(t *testing.T) {
		sharedConfig := &config.AuthConfig{
			JWTSecret:          "shared-secret",
			AccessTokenExpiry:  "45m",
			RefreshTokenExpiry: "72h",
			AdminServiceURL:    "http://shared-admin:9090",
		}

		authConfig := LoadConfigFromSharedConfig(sharedConfig)
		require.NotNil(t, authConfig)

		assert.Equal(t, "shared-secret", authConfig.JWTSecretKey)
		assert.Equal(t, 45*time.Minute, authConfig.AccessTokenExpiry)
		assert.Equal(t, 72*time.Hour, authConfig.RefreshTokenExpiry)
		assert.Equal(t, "http://shared-admin:9090", authConfig.AdminServiceURL)
	})

	t.Run("LoadConfigFromSharedConfig_WithEnvironmentOverrides", func(t *testing.T) {
		// Set environment variables
		os.Setenv("JWT_SECRET_KEY", "env-secret")
		os.Setenv("ACCESS_TOKEN_EXPIRY", "15m")
		os.Setenv("ADMIN_SERVICE_URL", "http://env-admin:7070")
		defer func() {
			os.Unsetenv("JWT_SECRET_KEY")
			os.Unsetenv("ACCESS_TOKEN_EXPIRY")
			os.Unsetenv("ADMIN_SERVICE_URL")
		}()

		sharedConfig := &config.AuthConfig{
			JWTSecret:          "shared-secret",
			AccessTokenExpiry:  "45m",
			RefreshTokenExpiry: "72h",
			AdminServiceURL:    "http://shared-admin:9090",
		}

		authConfig := LoadConfigFromSharedConfig(sharedConfig)
		require.NotNil(t, authConfig)

		// Environment variables should take precedence
		assert.Equal(t, "env-secret", authConfig.JWTSecretKey)
		assert.Equal(t, 15*time.Minute, authConfig.AccessTokenExpiry)
		assert.Equal(t, "http://env-admin:7070", authConfig.AdminServiceURL)
		// This should remain from shared config since no env var was set
		assert.Equal(t, 72*time.Hour, authConfig.RefreshTokenExpiry)
	})

	t.Run("ToSharedConfig", func(t *testing.T) {
		authConfig := &Config{
			JWTSecretKey:       "auth-secret",
			AccessTokenExpiry:  20 * time.Minute,
			RefreshTokenExpiry: 48 * time.Hour,
			AdminServiceURL:    "http://auth-admin:8888",
		}

		sharedConfig := authConfig.ToSharedConfig()
		require.NotNil(t, sharedConfig)

		assert.Equal(t, "auth-secret", sharedConfig.JWTSecret)
		assert.Equal(t, 20, sharedConfig.JWTExpirationMinutes)
		assert.Equal(t, "20m0s", sharedConfig.AccessTokenExpiry)
		assert.Equal(t, "48h0m0s", sharedConfig.RefreshTokenExpiry)
		assert.Equal(t, "http://auth-admin:8888", sharedConfig.AdminServiceURL)
		assert.Equal(t, "X-API-Key", sharedConfig.APIKeyHeader)
		assert.False(t, sharedConfig.DisableAuth)
	})

	t.Run("LoadConfigFromSharedConfig_WithEmptyValues", func(t *testing.T) {
		sharedConfig := &config.AuthConfig{
			JWTSecret:          "",
			AccessTokenExpiry:  "",
			RefreshTokenExpiry: "",
			AdminServiceURL:    "",
		}

		authConfig := LoadConfigFromSharedConfig(sharedConfig)
		require.NotNil(t, authConfig)

		// Should use defaults
		assert.Equal(t, "default-secret-key-change-in-production", authConfig.JWTSecretKey)
		assert.Equal(t, AccessTokenExpiry, authConfig.AccessTokenExpiry)
		assert.Equal(t, RefreshTokenExpiry, authConfig.RefreshTokenExpiry)
		assert.Equal(t, "http://admin-service:8080", authConfig.AdminServiceURL)
	})

	t.Run("LoadConfigFromSharedConfig_WithInvalidDurations", func(t *testing.T) {
		sharedConfig := &config.AuthConfig{
			JWTSecret:          "test-secret",
			AccessTokenExpiry:  "invalid",
			RefreshTokenExpiry: "also-invalid",
			AdminServiceURL:    "http://test-admin:8080",
		}

		authConfig := LoadConfigFromSharedConfig(sharedConfig)
		require.NotNil(t, authConfig)

		// Should fall back to defaults for invalid durations
		assert.Equal(t, "test-secret", authConfig.JWTSecretKey)
		assert.Equal(t, AccessTokenExpiry, authConfig.AccessTokenExpiry)
		assert.Equal(t, RefreshTokenExpiry, authConfig.RefreshTokenExpiry)
		assert.Equal(t, "http://test-admin:8080", authConfig.AdminServiceURL)
	})

	t.Run("RoundTripConversion", func(t *testing.T) {
		// Start with a shared config
		originalShared := &config.AuthConfig{
			JWTSecret:            "round-trip-secret",
			JWTExpirationMinutes: 90,
			AccessTokenExpiry:    "1h30m",
			RefreshTokenExpiry:   "168h",
			AdminServiceURL:      "http://round-trip:8080",
			APIKeyHeader:         "X-Custom-Key",
			DisableAuth:          true,
		}

		// Convert to auth config
		authConfig := LoadConfigFromSharedConfig(originalShared)

		// Convert back to shared config
		convertedShared := authConfig.ToSharedConfig()

		// Verify key values are preserved
		assert.Equal(t, originalShared.JWTSecret, convertedShared.JWTSecret)
		assert.Equal(t, originalShared.AdminServiceURL, convertedShared.AdminServiceURL)
		assert.Equal(t, "1h30m0s", convertedShared.AccessTokenExpiry)
		assert.Equal(t, "168h0m0s", convertedShared.RefreshTokenExpiry)
		// Note: APIKeyHeader and DisableAuth are set to defaults in ToSharedConfig
		assert.Equal(t, "X-API-Key", convertedShared.APIKeyHeader)
		assert.False(t, convertedShared.DisableAuth)
	})
}

func TestSharedConfigValidation(t *testing.T) {
	t.Run("ValidSharedConfig", func(t *testing.T) {
		sharedConfig := &config.AuthConfig{
			JWTSecret:          "valid-secret-key",
			AccessTokenExpiry:  "1h",
			RefreshTokenExpiry: "24h",
			AdminServiceURL:    "http://valid-admin:8080",
		}

		authManager, err := NewAuthManagerFromSharedConfig(sharedConfig)
		assert.NoError(t, err)
		assert.NotNil(t, authManager)
	})

	t.Run("EmptyJWTSecret", func(t *testing.T) {
		sharedConfig := &config.AuthConfig{
			JWTSecret:          "",
			AccessTokenExpiry:  "1h",
			RefreshTokenExpiry: "24h",
			AdminServiceURL:    "http://test-admin:8080",
		}

		authManager, err := NewAuthManagerFromSharedConfig(sharedConfig)
		assert.Error(t, err)
		assert.Nil(t, authManager)
		assert.Contains(t, err.Error(), "JWT secret key must be changed from default value")
	})

	t.Run("EmptyAdminServiceURL", func(t *testing.T) {
		// Clear environment variable to ensure no fallback
		originalURL := os.Getenv("ADMIN_SERVICE_URL")
		os.Unsetenv("ADMIN_SERVICE_URL")
		defer func() {
			if originalURL != "" {
				os.Setenv("ADMIN_SERVICE_URL", originalURL)
			}
		}()

		sharedConfig := &config.AuthConfig{
			JWTSecret:          "valid-secret",
			AccessTokenExpiry:  "1h",
			RefreshTokenExpiry: "24h",
			AdminServiceURL:    "",
		}

		// This should succeed because it falls back to default URL
		authManager, err := NewAuthManagerFromSharedConfig(sharedConfig)
		assert.NoError(t, err)
		assert.NotNil(t, authManager)
		// Verify it used the default URL
		assert.Equal(t, "http://admin-service:8080", authManager.Config.AdminServiceURL)
	})
}
