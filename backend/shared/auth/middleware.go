package auth

import (
	"log"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware creates a middleware for JWT authentication
func AuthMiddleware(jwtManager *JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			// Add debug logging for missing auth header
			log.Printf("AUTH DEBUG: Missing Authorization header for %s %s", c.Request.Method, c.Request.URL.Path)
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error": "Authorization header required",
				"code":  "AUTH_HEADER_MISSING",
			})
			return
		}

		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
			// Add debug logging for invalid header format
			log.Printf("AUTH DEBUG: Invalid Authorization header format for %s %s: %s", c.Request.Method, c.Request.URL.Path, authHeader)
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid authorization header format",
				"code":  "AUTH_HEADER_INVALID",
			})
			return
		}

		tokenString := parts[1]
		claims, err := jwtManager.VerifyToken(tokenString)
		if err != nil {
			// Add debug logging for token verification failure
			log.Printf("AUTH DEBUG: Token verification failed for %s %s: %v", c.Request.Method, c.Request.URL.Path, err)

			// Determine the specific error type for better frontend handling
			errorCode := "TOKEN_INVALID"
			if strings.Contains(err.Error(), "expired") {
				errorCode = "TOKEN_EXPIRED"
			} else if strings.Contains(err.Error(), "malformed") {
				errorCode = "TOKEN_MALFORMED"
			}

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error":   "Invalid token",
				"code":    errorCode,
				"details": err.Error(),
			})
			return
		}

		// Check that it's an access token, not a refresh token
		if claims.TokenType != AccessToken {
			log.Printf("AUTH DEBUG: Invalid token type for %s %s: %s", c.Request.Method, c.Request.URL.Path, claims.TokenType)
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid token type",
				"code":  "TOKEN_TYPE_INVALID",
			})
			return
		}

		// Validate that required claims are present
		if claims.UserID == "" || claims.Username == "" || claims.Email == "" {
			log.Printf("AUTH DEBUG: Missing required claims for %s %s", c.Request.Method, c.Request.URL.Path)
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid token claims",
				"code":  "TOKEN_CLAIMS_INVALID",
			})
			return
		}

		// Set user information in context
		c.Set("userID", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("email", claims.Email)
		c.Set("roles", claims.Roles)

		log.Printf("AUTH DEBUG: Successfully authenticated user %s for %s %s", claims.Username, c.Request.Method, c.Request.URL.Path)
		c.Next()
	}
}

// AdminMiddleware creates a middleware that checks if the user is an admin
func AdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		roles, exists := c.Get("roles")
		if !exists {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Role information not available"})
			return
		}

		rolesList, ok := roles.([]string)
		if !ok {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": "Invalid role information format"})
			return
		}

		isAdmin := false
		for _, role := range rolesList {
			if role == "admin" {
				isAdmin = true
				break
			}
		}

		if !isAdmin {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
			return
		}

		c.Next()
	}
}

// RequireRoleMiddleware creates a middleware that checks if the user has any of the specified roles
func RequireRoleMiddleware(requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		roles, exists := c.Get("roles")
		if !exists {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Role information not available"})
			return
		}

		rolesList, ok := roles.([]string)
		if !ok {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": "Invalid role information format"})
			return
		}

		hasRequiredRole := false
		for _, userRole := range rolesList {
			for _, requiredRole := range requiredRoles {
				if userRole == requiredRole {
					hasRequiredRole = true
					break
				}
			}
			if hasRequiredRole {
				break
			}
		}

		if !hasRequiredRole {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"error":         "Insufficient permissions",
				"requiredRoles": requiredRoles,
			})
			return
		}

		c.Next()
	}
}

// OptionalAuthMiddleware creates a middleware for optional JWT authentication
// This middleware will set user context if a valid token is provided, but won't fail if no token is present
func OptionalAuthMiddleware(jwtManager *JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			// No auth header, continue without setting user context
			c.Next()
			return
		}

		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
			// Invalid format, continue without setting user context
			c.Next()
			return
		}

		tokenString := parts[1]
		claims, err := jwtManager.VerifyToken(tokenString)
		if err != nil {
			// Invalid token, continue without setting user context
			c.Next()
			return
		}

		// Check that it's an access token, not a refresh token
		if claims.TokenType != AccessToken {
			// Invalid token type, continue without setting user context
			c.Next()
			return
		}

		// Set user information in context
		c.Set("userID", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("email", claims.Email)
		c.Set("roles", claims.Roles)

		c.Next()
	}
}

// CORSMiddleware adds CORS headers to allow cross-origin requests
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// GetUserIDFromContext extracts the user ID from the Gin context
func GetUserIDFromContext(c *gin.Context) (string, bool) {
	userID, exists := c.Get("userID")
	if !exists {
		return "", false
	}

	userIDStr, ok := userID.(string)
	return userIDStr, ok
}

// GetUserRolesFromContext extracts the user roles from the Gin context
func GetUserRolesFromContext(c *gin.Context) ([]string, bool) {
	roles, exists := c.Get("roles")
	if !exists {
		return nil, false
	}

	rolesList, ok := roles.([]string)
	return rolesList, ok
}

// IsAdminFromContext checks if the current user is an admin
func IsAdminFromContext(c *gin.Context) bool {
	roles, ok := GetUserRolesFromContext(c)
	if !ok {
		return false
	}

	for _, role := range roles {
		if role == "admin" {
			return true
		}
	}
	return false
}

// HasRoleFromContext checks if the current user has a specific role
func HasRoleFromContext(c *gin.Context, role string) bool {
	roles, ok := GetUserRolesFromContext(c)
	if !ok {
		return false
	}

	for _, userRole := range roles {
		if userRole == role {
			return true
		}
	}
	return false
}
