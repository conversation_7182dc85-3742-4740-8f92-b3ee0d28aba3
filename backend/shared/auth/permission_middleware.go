package auth

import (
	"context"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

// PermissionService interface defines the contract for permission checking
type PermissionService interface {
	CheckPermission(ctx context.Context, userID, permissionName, projectID string) (bool, error)
	CheckUserHasProjectAccess(ctx context.Context, userID, projectID string) (bool, error)
}

// PermissionMiddleware provides middleware for checking permissions
type PermissionMiddleware struct {
	permissionService PermissionService
}

// NewPermissionMiddleware creates a new permission middleware
func NewPermissionMiddleware(permissionService PermissionService) *PermissionMiddleware {
	return &PermissionMiddleware{
		permissionService: permissionService,
	}
}

// RequirePermission creates a middleware that checks if a user has a specific permission
func (m *PermissionMiddleware) RequirePermission(permissionName string, getProjectID func(*gin.Context) string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from context (set by auth middleware)
		userID, exists := GetUserIDFromContext(c)
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Check if user is admin (admins bypass permission checks)
		if IsAdminFromContext(c) {
			c.Next()
			return
		}

		// Get project ID if needed
		var projectID string
		if getProjectID != nil {
			projectID = getProjectID(c)
		}

		// Create a context with the authorization header for service-to-service calls
		ctx := c.Request.Context()
		if authHeader := c.GetHeader("Authorization"); authHeader != "" {
			ctx = context.WithValue(ctx, "Authorization", authHeader)
		}

		// Check permission
		hasPermission, err := m.permissionService.CheckPermission(ctx, userID, permissionName, projectID)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to check permission",
				"details": err.Error(),
			})
			return
		}

		if !hasPermission {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"error":      "Permission denied",
				"permission": permissionName,
				"projectId":  projectID,
			})
			return
		}

		c.Next()
	}
}

// ProjectAccessMiddleware creates a middleware that checks if a user has access to a project
func (m *PermissionMiddleware) ProjectAccessMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from context (set by auth middleware)
		userID, exists := GetUserIDFromContext(c)
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Check if user is admin (admins bypass access checks)
		if IsAdminFromContext(c) {
			c.Next()
			return
		}

		// Get project ID from URL parameter
		projectID := c.Param("projectId")
		if projectID == "" {
			// If no project ID in URL, let the handler deal with it
			c.Next()
			return
		}

		// Check if user has access to the project
		hasAccess, err := m.permissionService.CheckUserHasProjectAccess(c.Request.Context(), userID, projectID)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to check project access",
				"details": err.Error(),
			})
			return
		}

		if !hasAccess {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"error":     "Access denied",
				"projectId": projectID,
			})
			return
		}

		c.Next()
	}
}

// RequireAdmin creates a middleware that checks if a user is an admin
func (m *PermissionMiddleware) RequireAdmin() gin.HandlerFunc {
	return AdminMiddleware()
}

// HTTPPermissionService implements PermissionService using HTTP calls to admin service
type HTTPPermissionService struct {
	adminServiceURL string
	httpClient      *http.Client
}

// NewHTTPPermissionService creates a new HTTP-based permission service
func NewHTTPPermissionService(adminServiceURL string) *HTTPPermissionService {
	return &HTTPPermissionService{
		adminServiceURL: adminServiceURL,
		httpClient:      &http.Client{},
	}
}

// CheckPermission checks if a user has a specific permission via HTTP call to admin service
func (s *HTTPPermissionService) CheckPermission(ctx context.Context, userID, permissionName, projectID string) (bool, error) {
	url := fmt.Sprintf("%s/api/v1/permissions/check", s.adminServiceURL)

	// Create request with query parameters
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return false, fmt.Errorf("failed to create request: %w", err)
	}

	q := req.URL.Query()
	q.Add("userId", userID)
	q.Add("permission", permissionName)
	if projectID != "" {
		q.Add("projectId", projectID)
	}
	req.URL.RawQuery = q.Encode()

	// Forward authorization header if available in context
	if authHeader, ok := ctx.Value("Authorization").(string); ok && authHeader != "" {
		req.Header.Set("Authorization", authHeader)
	}

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return false, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		return true, nil
	case http.StatusForbidden:
		return false, nil
	default:
		return false, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}
}

// CheckUserHasProjectAccess checks if a user has access to a project via HTTP call to admin service
func (s *HTTPPermissionService) CheckUserHasProjectAccess(ctx context.Context, userID, projectID string) (bool, error) {
	url := fmt.Sprintf("%s/api/v1/projects/%s/access", s.adminServiceURL, projectID)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return false, fmt.Errorf("failed to create request: %w", err)
	}

	q := req.URL.Query()
	q.Add("userId", userID)
	req.URL.RawQuery = q.Encode()

	// Forward authorization header if available in context
	if authHeader, ok := ctx.Value("Authorization").(string); ok && authHeader != "" {
		req.Header.Set("Authorization", authHeader)
	}

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return false, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	switch resp.StatusCode {
	case http.StatusOK:
		return true, nil
	case http.StatusForbidden:
		return false, nil
	default:
		return false, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}
}

// ProjectIDFromParam extracts project ID from URL parameter
func ProjectIDFromParam(c *gin.Context) string {
	return c.Param("projectId")
}

// ProjectIDFromQuery extracts project ID from query parameter
func ProjectIDFromQuery(c *gin.Context) string {
	return c.Query("projectId")
}

// ProjectIDFromJSON extracts project ID from JSON body (requires binding first)
func ProjectIDFromJSON(fieldName string) func(*gin.Context) string {
	return func(c *gin.Context) string {
		if value, exists := c.Get(fieldName); exists {
			if projectID, ok := value.(string); ok {
				return projectID
			}
		}
		return ""
	}
}
