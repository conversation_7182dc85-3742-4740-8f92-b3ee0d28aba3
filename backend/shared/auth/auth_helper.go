package auth

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// AuthContext represents the authenticated user context
type AuthContext struct {
	UserID   string   `json:"userId"`
	Username string   `json:"username"`
	Email    string   `json:"email"`
	Roles    []string `json:"roles"`
}

// AuthHelper provides centralized authentication checking utilities
type AuthHelper struct{}

// NewAuthHelper creates a new AuthHelper instance
func NewAuthHelper() *AuthHelper {
	return &AuthHelper{}
}

// RequireAuthentication checks if the user is authenticated and returns the auth context
// If not authenticated, it aborts the request with 401 and returns nil
func (h *AuthHelper) RequireAuthentication(c *gin.Context) *AuthContext {
	userID, userIDExists := c.Get("userID")
	username, usernameExists := c.Get("username")
	email, emailExists := c.Get("email")
	roles, rolesExists := c.Get("roles")

	// Check if all required authentication context is present
	if !userIDExists || !usernameExists || !emailExists || !rolesExists {
		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
			"code":  "AUTH_REQUIRED",
		})
		return nil
	}

	// Validate that the values are of the expected types
	userIDStr, ok := userID.(string)
	if !ok || userIDStr == "" {
		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid authentication context",
			"code":  "AUTH_INVALID",
		})
		return nil
	}

	usernameStr, ok := username.(string)
	if !ok || usernameStr == "" {
		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid authentication context",
			"code":  "AUTH_INVALID",
		})
		return nil
	}

	emailStr, ok := email.(string)
	if !ok || emailStr == "" {
		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid authentication context",
			"code":  "AUTH_INVALID",
		})
		return nil
	}

	rolesSlice, ok := roles.([]string)
	if !ok {
		c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
			"error": "Invalid authentication context",
			"code":  "AUTH_INVALID",
		})
		return nil
	}

	return &AuthContext{
		UserID:   userIDStr,
		Username: usernameStr,
		Email:    emailStr,
		Roles:    rolesSlice,
	}
}

// GetOptionalAuthentication returns the auth context if available, or nil if not authenticated
// This does not abort the request and allows for optional authentication
func (h *AuthHelper) GetOptionalAuthentication(c *gin.Context) *AuthContext {
	userID, userIDExists := c.Get("userID")
	username, usernameExists := c.Get("username")
	email, emailExists := c.Get("email")
	roles, rolesExists := c.Get("roles")

	// If any required context is missing, return nil (not authenticated)
	if !userIDExists || !usernameExists || !emailExists || !rolesExists {
		return nil
	}

	// Validate that the values are of the expected types
	userIDStr, ok := userID.(string)
	if !ok || userIDStr == "" {
		return nil
	}

	usernameStr, ok := username.(string)
	if !ok || usernameStr == "" {
		return nil
	}

	emailStr, ok := email.(string)
	if !ok || emailStr == "" {
		return nil
	}

	rolesSlice, ok := roles.([]string)
	if !ok {
		return nil
	}

	return &AuthContext{
		UserID:   userIDStr,
		Username: usernameStr,
		Email:    emailStr,
		Roles:    rolesSlice,
	}
}

// HasRole checks if the authenticated user has a specific role
func (ac *AuthContext) HasRole(role string) bool {
	for _, userRole := range ac.Roles {
		if userRole == role {
			return true
		}
	}
	return false
}

// HasAnyRole checks if the authenticated user has any of the specified roles
func (ac *AuthContext) HasAnyRole(roles ...string) bool {
	for _, requiredRole := range roles {
		if ac.HasRole(requiredRole) {
			return true
		}
	}
	return false
}

// IsAdmin checks if the authenticated user has admin role
func (ac *AuthContext) IsAdmin() bool {
	return ac.HasRole("admin")
}
