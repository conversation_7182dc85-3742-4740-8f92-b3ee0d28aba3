# Shared Authentication Package

This package provides a comprehensive authentication and authorization system for the Deploy Orchestrator platform.

## Features

- **JWT Token Management**: Generate, verify, and refresh JWT tokens
- **Authentication Middleware**: Protect endpoints with JWT authentication
- **Authorization Middleware**: Role-based and permission-based access control
- **Permission System**: Integration with admin service for fine-grained permissions
- **CORS Support**: Cross-origin request handling
- **Configuration Management**: Environment-based configuration

## Quick Start

### 1. Basic Setup

```go
package main

import (
    "log"
    
    "github.com/gin-gonic/gin"
    "github.com/claudio/deploy-orchestrator/shared/auth"
)

func main() {
    // Create auth manager from environment variables
    authManager, err := auth.NewAuthManagerFromEnv()
    if err != nil {
        log.Fatal("Failed to create auth manager:", err)
    }

    // Create Gin router
    router := gin.Default()

    // Add CORS middleware
    router.Use(authManager.CORSMiddleware())

    // Public endpoints (no authentication required)
    router.GET("/health", healthHandler)

    // Protected endpoints
    api := router.Group("/api/v1")
    api.Use(authManager.AuthMiddleware()) // Require authentication
    {
        api.GET("/profile", getUserProfile)
        
        // Admin-only endpoints
        admin := api.Group("/admin")
        admin.Use(authManager.AdminMiddleware())
        {
            admin.GET("/users", listUsers)
            admin.POST("/users", createUser)
        }
        
        // Role-based endpoints
        moderator := api.Group("/moderate")
        moderator.Use(authManager.RequireRoleMiddleware("admin", "moderator"))
        {
            moderator.POST("/approve", approveContent)
        }
    }

    router.Run(":8080")
}
```

### 2. Permission-Based Authorization

```go
// Setup permission middleware
permMiddleware := authManager.PermissionMiddleware()

// Protect endpoints with specific permissions
api.GET("/projects/:projectId/deployments", 
    permMiddleware.RequirePermission("deployment:read", auth.ProjectIDFromParam),
    getDeployments)

api.POST("/projects/:projectId/deployments",
    permMiddleware.RequirePermission("deployment:create", auth.ProjectIDFromParam),
    createDeployment)

// Project access middleware
api.Use("/projects/:projectId", permMiddleware.ProjectAccessMiddleware())
```

### 3. Optional Authentication

```go
// Some endpoints may work with or without authentication
router.GET("/public-data", authManager.OptionalAuthMiddleware(), getPublicData)

func getPublicData(c *gin.Context) {
    // Check if user is authenticated
    if userID, exists := auth.GetUserIDFromContext(c); exists {
        // Return personalized data
        c.JSON(200, gin.H{"message": "Hello " + userID, "data": "personalized"})
    } else {
        // Return public data
        c.JSON(200, gin.H{"message": "Hello anonymous", "data": "public"})
    }
}
```

## Configuration

### Environment Variables

```bash
# JWT Configuration
JWT_SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRY=1h
REFRESH_TOKEN_EXPIRY=168h  # 7 days

# Admin Service Configuration
ADMIN_SERVICE_URL=http://admin-service:8080
```

### Programmatic Configuration

```go
config := &auth.Config{
    JWTSecretKey:       "your-secret-key",
    AccessTokenExpiry:  time.Hour,
    RefreshTokenExpiry: time.Hour * 24 * 7,
    AdminServiceURL:    "http://admin-service:8080",
}

authManager, err := auth.NewAuthManager(config)
if err != nil {
    log.Fatal(err)
}
```

## Token Management

### Generating Tokens

```go
jwtManager := authManager.JWTManager

// Generate access token
accessToken, err := jwtManager.GenerateAccessToken(
    "user-123", 
    "john.doe", 
    "<EMAIL>", 
    []string{"user", "developer"},
)

// Generate refresh token
refreshToken, err := jwtManager.GenerateRefreshToken(
    "user-123", 
    "john.doe", 
    "<EMAIL>", 
    []string{"user", "developer"},
)
```

### Verifying Tokens

```go
claims, err := jwtManager.VerifyToken(tokenString)
if err != nil {
    // Invalid token
    return
}

fmt.Printf("User ID: %s\n", claims.UserID)
fmt.Printf("Username: %s\n", claims.Username)
fmt.Printf("Roles: %v\n", claims.Roles)
```

### Refreshing Tokens

```go
newAccessToken, newRefreshToken, err := jwtManager.RefreshTokens(oldRefreshToken)
if err != nil {
    // Invalid refresh token
    return
}
```

## Helper Functions

### Context Helpers

```go
// Get user information from context
userID, exists := auth.GetUserIDFromContext(c)
roles, exists := auth.GetUserRolesFromContext(c)

// Check user roles
isAdmin := auth.IsAdminFromContext(c)
hasModerator := auth.HasRoleFromContext(c, "moderator")
```

### Project ID Extractors

```go
// From URL parameter
auth.ProjectIDFromParam(c)  // /projects/:projectId

// From query parameter
auth.ProjectIDFromQuery(c)  // ?projectId=123

// From JSON body (after binding)
auth.ProjectIDFromJSON("projectId")(c)
```

## Custom Permission Service

You can implement your own permission service:

```go
type MyPermissionService struct {
    // your implementation
}

func (s *MyPermissionService) CheckPermission(ctx context.Context, userID, permissionName, projectID string) (bool, error) {
    // your permission checking logic
    return true, nil
}

func (s *MyPermissionService) CheckUserHasProjectAccess(ctx context.Context, userID, projectID string) (bool, error) {
    // your project access checking logic
    return true, nil
}

// Use custom permission service
authManager.PermissionService = &MyPermissionService{}
```

## Error Handling

The middleware returns standard HTTP status codes:

- `401 Unauthorized`: Missing or invalid token
- `403 Forbidden`: Valid token but insufficient permissions
- `500 Internal Server Error`: Permission check failed

## Security Best Practices

1. **Use strong secret keys**: Never use default values in production
2. **Set appropriate token expiry**: Balance security and user experience
3. **Validate all inputs**: Always validate user inputs and permissions
4. **Use HTTPS**: Always use HTTPS in production
5. **Rotate secrets**: Regularly rotate JWT secret keys
6. **Monitor auth events**: Log authentication and authorization events

## Integration with Services

Each service should:

1. Import the shared auth package
2. Create an auth manager on startup
3. Apply appropriate middleware to routes
4. Use context helpers to access user information

See the examples in individual service implementations for specific usage patterns.
