package auth

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// Constants for JWT
const (
	AccessTokenExpiry  = time.Hour * 1      // Access tokens expire after 1 hour
	RefreshTokenExpiry = time.Hour * 24 * 7 // Refresh tokens expire after 7 days
)

// TokenType defines the token type
type TokenType string

const (
	// AccessToken is used for API access
	AccessToken TokenType = "access"
	// RefreshToken is used to get a new access token
	RefreshToken TokenType = "refresh"
)

// Claims extends standard JWT claims with our custom fields
// This replaces both CustomClaims and Claims from different implementations
type Claims struct {
	UserID    string    `json:"userId"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	Roles     []string  `json:"roles"`
	TokenType TokenType `json:"tokenType"`
	jwt.RegisteredClaims
}

// JWTManager handles JWT operations
type JWTManager struct {
	secretKey []byte
}

// NewJWTManager creates a new JWT manager with the provided secret key
func NewJWTManager(secretKey string) *JWTManager {
	return &JWTManager{
		secretKey: []byte(secretKey),
	}
}

// GetSecretKey returns the secret key for token signing
// This is added to allow custom token generation with extended expiration
func (m *JWTManager) GetSecretKey() []byte {
	return m.secretKey
}

// GenerateAccessToken generates a new access token for the user
func (m *JWTManager) GenerateAccessToken(userID, username, email string, roles []string) (string, error) {
	return m.generateToken(userID, username, email, roles, AccessToken, AccessTokenExpiry)
}

// GenerateRefreshToken generates a new refresh token for the user
func (m *JWTManager) GenerateRefreshToken(userID, username, email string, roles []string) (string, error) {
	return m.generateToken(userID, username, email, roles, RefreshToken, RefreshTokenExpiry)
}

// GenerateAccessTokenWithExpiry generates a new access token with a custom expiration time
func (m *JWTManager) GenerateAccessTokenWithExpiry(userID, username, email string, roles []string, expiry time.Duration) (string, error) {
	return m.generateToken(userID, username, email, roles, AccessToken, expiry)
}

// GenerateRefreshTokenWithExpiry generates a new refresh token with a custom expiration time
func (m *JWTManager) GenerateRefreshTokenWithExpiry(userID, username, email string, roles []string, expiry time.Duration) (string, error) {
	return m.generateToken(userID, username, email, roles, RefreshToken, expiry)
}

// generateToken creates a new JWT token
func (m *JWTManager) generateToken(userID, username, email string, roles []string, tokenType TokenType, expiry time.Duration) (string, error) {
	now := time.Now()
	claims := Claims{
		UserID:    userID,
		Username:  username,
		Email:     email,
		Roles:     roles,
		TokenType: tokenType,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(expiry)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "deploy-orchestrator",
			Subject:   userID,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(m.secretKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, nil
}

// VerifyToken verifies the JWT token and returns the claims
func (m *JWTManager) VerifyToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate the algorithm
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return m.secretKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("invalid token: %w", err)
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token claims")
}

// RefreshTokens validates a refresh token and generates new access and refresh tokens
func (m *JWTManager) RefreshTokens(refreshToken string) (accessToken string, newRefreshToken string, err error) {
	claims, err := m.VerifyToken(refreshToken)
	if err != nil {
		return "", "", err
	}

	// Ensure it's a refresh token
	if claims.TokenType != RefreshToken {
		return "", "", fmt.Errorf("invalid token type: expected refresh token")
	}

	// Determine if this was a token with extended expiry
	// Calculate how much time was originally allocated
	expiryTime := claims.ExpiresAt.Time
	issuedTime := claims.IssuedAt.Time
	originalDuration := expiryTime.Sub(issuedTime)
	isExtended := originalDuration > RefreshTokenExpiry

	// Generate new tokens - if it was an extended token, generate extended tokens
	if isExtended {
		// For extended tokens, calculate the appropriate extended durations
		// If original refresh token was set with RememberMe, it would be 4x standard duration
		// We need to maintain the same ratio for the new tokens
		refreshTokenMultiplier := originalDuration / RefreshTokenExpiry
		accessTokenMultiplier := refreshTokenMultiplier

		// Apply multipliers to generate tokens with appropriate extended expiry
		accessTokenExpiry := AccessTokenExpiry * accessTokenMultiplier
		refreshTokenExpiry := RefreshTokenExpiry * refreshTokenMultiplier

		// Generate extended tokens
		accessToken, err = m.GenerateAccessTokenWithExpiry(claims.UserID, claims.Username, claims.Email, claims.Roles, accessTokenExpiry)
		if err != nil {
			return "", "", err
		}

		newRefreshToken, err = m.GenerateRefreshTokenWithExpiry(claims.UserID, claims.Username, claims.Email, claims.Roles, refreshTokenExpiry)
		if err != nil {
			return "", "", err
		}
	} else {
		// Generate standard tokens
		accessToken, err = m.GenerateAccessToken(claims.UserID, claims.Username, claims.Email, claims.Roles)
		if err != nil {
			return "", "", err
		}

		newRefreshToken, err = m.GenerateRefreshToken(claims.UserID, claims.Username, claims.Email, claims.Roles)
		if err != nil {
			return "", "", err
		}
	}

	return accessToken, newRefreshToken, nil
}
