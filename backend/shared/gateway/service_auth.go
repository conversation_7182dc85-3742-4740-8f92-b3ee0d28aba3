package gateway

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// ServiceClaims represents JWT claims for service authentication
type ServiceClaims struct {
	UserID    string   `json:"user_id"`
	Username  string   `json:"username"`
	Email     string   `json:"email"`
	Roles     []string `json:"roles"`
	TokenType string   `json:"token_type"`
	jwt.RegisteredClaims
}

// GenerateServiceJWT generates a proper JWT token for service authentication
func GenerateServiceJWT(serviceName, serviceVersion, jwtSecret string) (string, error) {
	if jwtSecret == "" {
		return "", fmt.Errorf("JWT secret not provided")
	}

	now := time.Now()

	// Create service-specific claims that match the expected format
	claims := ServiceClaims{
		UserID:    fmt.Sprintf("service:%s", serviceName),
		Username:  serviceName,
		Email:     fmt.Sprintf("%<EMAIL>", serviceName),
		Roles:     []string{"service", "microservice"},
		TokenType: "access_token",
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(24 * time.Hour)), // 24 hours
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "deploy-orchestrator",
			Subject:   fmt.Sprintf("service:%s", serviceName),
		},
	}

	// Create token with claims
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign token with secret
	tokenString, err := token.SignedString([]byte(jwtSecret))
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, nil
}

// VerifyServiceJWT verifies a service JWT token
func VerifyServiceJWT(tokenString, jwtSecret string) (*ServiceClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &ServiceClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate the algorithm
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(jwtSecret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("invalid token: %w", err)
	}

	if claims, ok := token.Claims.(*ServiceClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token claims")
}

// IsServiceToken checks if a token represents a service account
func IsServiceToken(claims *ServiceClaims) bool {
	// Check if userID starts with "service:"
	if len(claims.UserID) < 8 || claims.UserID[:8] != "service:" {
		return false
	}

	// Check if service role is present
	for _, role := range claims.Roles {
		if role == "service" || role == "microservice" {
			return true
		}
	}

	return false
}

// ExtractServiceName extracts service name from service token claims
func ExtractServiceName(claims *ServiceClaims) string {
	if len(claims.UserID) > 8 && claims.UserID[:8] == "service:" {
		return claims.UserID[8:] // Remove "service:" prefix
	}
	return ""
}
