package gateway

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"go.uber.org/zap"
)

// Client handles service registration with the API Gateway
type Client struct {
	gatewayURL  string
	serviceName string
	instanceID  string
	serviceURL  string
	authToken   string
	client      *http.Client
	logger      *zap.Logger
	registered  bool
}

// Instance represents a service instance for registration
type Instance struct {
	ID       string            `json:"id"`
	URL      string            `json:"url"`
	Version  string            `json:"version"`
	Tags     []string          `json:"tags"`
	Metadata map[string]string `json:"metadata"`
}

// Config holds configuration for gateway client
type Config struct {
	GatewayURL  string
	ServiceName string
	ServicePort int
	AuthToken   string
	Version     string
	Tags        []string
	Environment string
	Region      string
}

// NewClient creates a new gateway client
func NewClient(config Config, logger *zap.Logger) *Client {
	instanceID := generateInstanceID()
	serviceURL := fmt.Sprintf("http://localhost:%d", config.ServicePort)

	return &Client{
		gatewayURL:  config.GatewayURL,
		serviceName: config.ServiceName,
		instanceID:  instanceID,
		serviceURL:  serviceURL,
		authToken:   config.AuthToken,
		logger:      logger,
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// NewClientFromEnv creates a gateway client from environment variables
func NewClientFromEnv(serviceName string, servicePort int, logger *zap.Logger) *Client {
	config := Config{
		GatewayURL:  os.Getenv("GATEWAY_URL"),
		ServiceName: serviceName,
		ServicePort: servicePort,
		AuthToken:   os.Getenv("GATEWAY_AUTH_TOKEN"),
		Version:     getEnv("SERVICE_VERSION", "1.0.0"),
		Tags:        []string{"api", "microservice"},
		Environment: getEnv("ENVIRONMENT", "development"),
		Region:      getEnv("REGION", "local"),
	}

	return NewClient(config, logger)
}

// Register registers the service instance with the gateway
func (c *Client) Register() error {
	if c.gatewayURL == "" {
		c.logger.Info("Gateway URL not configured, skipping registration")
		return nil
	}

	instance := Instance{
		ID:      c.instanceID,
		URL:     c.serviceURL,
		Version: getEnv("SERVICE_VERSION", "1.0.0"),
		Tags:    []string{"api", "microservice"},
		Metadata: map[string]string{
			"environment": getEnv("ENVIRONMENT", "development"),
			"region":      getEnv("REGION", "local"),
			"started_at":  time.Now().UTC().Format(time.RFC3339),
			"hostname":    getHostname(),
			"pid":         fmt.Sprintf("%d", os.Getpid()),
		},
	}

	jsonData, err := json.Marshal(instance)
	if err != nil {
		return fmt.Errorf("failed to marshal instance data: %w", err)
	}

	url := fmt.Sprintf("%s/gateway/services/%s/instances", c.gatewayURL, c.serviceName)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if c.authToken != "" {
		req.Header.Set("Authorization", "Bearer "+c.authToken)
	}

	resp, err := c.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to register service: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusCreated {
		c.registered = true
		c.logger.Info("Service registered with gateway",
			zap.String("service", c.serviceName),
			zap.String("instance_id", c.instanceID),
			zap.String("url", c.serviceURL))
		return nil
	}

	return fmt.Errorf("registration failed with status: %d", resp.StatusCode)
}

// Deregister removes the service instance from the gateway
func (c *Client) Deregister() error {
	if !c.registered || c.gatewayURL == "" {
		return nil
	}

	url := fmt.Sprintf("%s/gateway/services/%s/instances/%s",
		c.gatewayURL, c.serviceName, c.instanceID)

	req, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create deregister request: %w", err)
	}

	if c.authToken != "" {
		req.Header.Set("Authorization", "Bearer "+c.authToken)
	}

	resp, err := c.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to deregister service: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		c.registered = false
		c.logger.Info("Service deregistered from gateway",
			zap.String("service", c.serviceName),
			zap.String("instance_id", c.instanceID))
		return nil
	}

	return fmt.Errorf("deregistration failed with status: %d", resp.StatusCode)
}

// RegisterWithRetry attempts to register with retry logic
func (c *Client) RegisterWithRetry(maxRetries int) error {
	var lastErr error
	for i := 0; i < maxRetries; i++ {
		if err := c.Register(); err == nil {
			return nil
		} else {
			lastErr = err
			c.logger.Warn("Registration attempt failed, retrying",
				zap.Int("attempt", i+1),
				zap.Int("max_retries", maxRetries),
				zap.Error(err))
			time.Sleep(time.Duration(i+1) * time.Second)
		}
	}
	return fmt.Errorf("failed to register after %d retries: %w", maxRetries, lastErr)
}

// SafeRegister registers with the gateway but doesn't fail if registration fails
func (c *Client) SafeRegister() {
	if err := c.RegisterWithRetry(3); err != nil {
		c.logger.Warn("Failed to register with gateway, service will continue without registration",
			zap.Error(err))
	}
}

// SafeDeregister deregisters from the gateway but doesn't fail if deregistration fails
func (c *Client) SafeDeregister() {
	if err := c.Deregister(); err != nil {
		c.logger.Warn("Failed to deregister from gateway",
			zap.Error(err))
	}
}

// IsRegistered returns whether the service is currently registered
func (c *Client) IsRegistered() bool {
	return c.registered
}

// GetInstanceID returns the instance ID
func (c *Client) GetInstanceID() string {
	return c.instanceID
}

// GetServiceName returns the service name
func (c *Client) GetServiceName() string {
	return c.serviceName
}

// Helper functions

func generateInstanceID() string {
	hostname := getHostname()
	pid := os.Getpid()
	timestamp := time.Now().Unix()
	return fmt.Sprintf("%s-%d-%d", hostname, pid, timestamp)
}

func getHostname() string {
	if hostname, err := os.Hostname(); err == nil {
		return hostname
	}
	return "unknown"
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// SharedGatewayConfig interface for extracting gateway configuration from shared config
type SharedGatewayConfig interface {
	GetGatewayURL() string
	GetGatewayAuthToken() string
	GetGatewayEnabled() bool
	GetGatewayVersion() string
	GetGatewayEnvironment() string
	GetGatewayRegion() string
	GetGatewayTags() []string
}

// generateServiceToken generates a JWT token for service authentication
func generateServiceToken(serviceName, serviceVersion, jwtSecret string) (string, error) {
	if jwtSecret == "" {
		return "", fmt.Errorf("JWT secret not provided")
	}

	// Generate proper JWT token for service authentication
	return GenerateServiceJWT(serviceName, serviceVersion, jwtSecret)
}

// NewClientFromSharedConfig creates a gateway client from shared config
// This allows services to configure gateway registration via YAML files
func NewClientFromSharedConfig(serviceName string, servicePort int, gatewayConfig SharedGatewayConfig, logger *zap.Logger) *Client {
	// Check if gateway registration is enabled
	if !gatewayConfig.GetGatewayEnabled() {
		logger.Info("Gateway registration disabled in configuration")
		return &Client{logger: logger} // Return inactive client
	}

	// Parse tags from comma-separated string if needed
	tags := gatewayConfig.GetGatewayTags()
	if len(tags) == 0 {
		tags = []string{"api", "microservice"}
	}

	authToken := gatewayConfig.GetGatewayAuthToken()

	// If no auth token is provided, try to generate a service token
	if authToken == "" {
		// Try to get JWT secret from environment or use default
		jwtSecret := os.Getenv("JWT_SECRET")
		if jwtSecret == "" {
			jwtSecret = "default-secret-key-in-production" // Match gateway default
		}

		// Generate service token
		if token, err := generateServiceToken(serviceName, gatewayConfig.GetGatewayVersion(), jwtSecret); err == nil {
			authToken = token
			logger.Info("Generated service authentication token for gateway registration")
		} else {
			logger.Warn("Failed to generate service token, registration may fail", zap.Error(err))
		}
	}

	config := Config{
		GatewayURL:  gatewayConfig.GetGatewayURL(),
		ServiceName: serviceName,
		ServicePort: servicePort,
		AuthToken:   authToken,
		Version:     gatewayConfig.GetGatewayVersion(),
		Tags:        tags,
		Environment: gatewayConfig.GetGatewayEnvironment(),
		Region:      gatewayConfig.GetGatewayRegion(),
	}

	return NewClient(config, logger)
}

// IntegrateWithService provides a simple way to integrate gateway registration
// into existing services. Call this in your main function.
func IntegrateWithService(serviceName string, servicePort int, logger *zap.Logger) *Client {
	client := NewClientFromEnv(serviceName, servicePort, logger)
	client.SafeRegister()
	return client
}
