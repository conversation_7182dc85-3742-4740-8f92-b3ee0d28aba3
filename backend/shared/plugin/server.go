package plugin

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
)

// SimplePluginServer implements a basic HTTP-based plugin server
type SimplePluginServer struct {
	plugin Plugin
	port   string
}

// NewSimplePluginServer creates a new simple plugin server
func NewSimplePluginServer(plugin Plugin) *SimplePluginServer {
	port := os.Getenv("PLUGIN_PORT")
	if port == "" {
		port = "8200" // Changed from 8090 to 8200 to avoid conflicts with external providers
	}

	return &SimplePluginServer{
		plugin: plugin,
		port:   port,
	}
}

// Serve starts serving the plugin over HTTP
func (s *SimplePluginServer) Serve(plugin Plugin) {
	s.plugin = plugin

	http.HandleFunc("/metadata", s.handleMetadata)
	http.HandleFunc("/initialize", s.handleInitialize)
	http.HandleFunc("/execute", s.handleExecute)
	http.HandleFunc("/health", s.handleHealth)
	http.HandleFunc("/cleanup", s.handleCleanup)

	// Handle graceful shutdown
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		log.Println("Plugin shutting down...")
		if err := s.plugin.Cleanup(); err != nil {
			log.Printf("Error during cleanup: %v", err)
		}
		os.Exit(0)
	}()

	log.Printf("Plugin server starting on port %s", s.port)
	if err := http.ListenAndServe(":"+s.port, nil); err != nil {
		log.Fatalf("Plugin server failed: %v", err)
	}
}

func (s *SimplePluginServer) handleMetadata(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	metadata := s.plugin.GetMetadata()
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(metadata)
}

func (s *SimplePluginServer) handleInitialize(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var config map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&config); err != nil {
		http.Error(w, fmt.Sprintf("Invalid JSON: %v", err), http.StatusBadRequest)
		return
	}

	if err := s.plugin.Initialize(config); err != nil {
		http.Error(w, fmt.Sprintf("Initialization failed: %v", err), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"status": "initialized"})
}

func (s *SimplePluginServer) handleExecute(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var request struct {
		Operation string                 `json:"operation"`
		Params    map[string]interface{} `json:"params"`
	}

	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, fmt.Sprintf("Invalid JSON: %v", err), http.StatusBadRequest)
		return
	}

	result, err := s.plugin.Execute(r.Context(), request.Operation, request.Params)
	if err != nil {
		http.Error(w, fmt.Sprintf("Execution failed: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(result)
}

func (s *SimplePluginServer) handleHealth(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	health := s.plugin.Health()
	w.Header().Set("Content-Type", "application/json")

	if health.Status == "healthy" {
		w.WriteHeader(http.StatusOK)
	} else {
		w.WriteHeader(http.StatusServiceUnavailable)
	}

	json.NewEncoder(w).Encode(health)
}

func (s *SimplePluginServer) handleCleanup(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	if err := s.plugin.Cleanup(); err != nil {
		http.Error(w, fmt.Sprintf("Cleanup failed: %v", err), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"status": "cleaned"})
}

// Serve is the main entry point for plugins
func Serve(plugin Plugin) {
	server := NewSimplePluginServer(plugin)
	server.Serve(plugin)
}
