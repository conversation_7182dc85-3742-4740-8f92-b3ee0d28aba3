package plugin

import (
	"context"
)

// Plugin defines the interface that all plugins must implement
type Plugin interface {
	// GetMetadata returns plugin metadata
	GetMetadata() Metadata

	// Initialize initializes the plugin with configuration
	Initialize(config map[string]interface{}) error

	// Execute executes a plugin operation
	Execute(ctx context.Context, operation string, params map[string]interface{}) (map[string]interface{}, error)

	// Cleanup cleans up plugin resources
	Cleanup() error

	// Health returns plugin health status
	Health() HealthStatus
}

// Metadata represents plugin metadata
type Metadata struct {
	Name         string                 `json:"name"`
	Version      string                 `json:"version"`
	Description  string                 `json:"description"`
	Author       string                 `json:"author"`
	Type         string                 `json:"type"`
	Capabilities []string               `json:"capabilities"`
	ConfigSchema map[string]interface{} `json:"config_schema"`
	Variables    []VariableDefinition   `json:"variables,omitempty"`
}

// VariableDefinition represents a plugin variable that can be overridden
type VariableDefinition struct {
	Name         string      `json:"name"`
	Type         string      `json:"type"` // string, number, boolean, array, object
	Description  string      `json:"description"`
	DefaultValue interface{} `json:"default_value,omitempty"`
	Required     bool        `json:"required"`
	Sensitive    bool        `json:"sensitive"` // If true, value should be treated as secret
	Validation   *Validation `json:"validation,omitempty"`
}

// Validation represents validation rules for variables
type Validation struct {
	Pattern string   `json:"pattern,omitempty"` // Regex pattern for string validation
	Min     *float64 `json:"min,omitempty"`     // Minimum value for numbers
	Max     *float64 `json:"max,omitempty"`     // Maximum value for numbers
	Options []string `json:"options,omitempty"` // Valid options for enum-like validation
}

// HealthStatus represents plugin health status
type HealthStatus struct {
	Status  string `json:"status"` // healthy, unhealthy, degraded
	Message string `json:"message"`
}

// ExecutionContext provides context for plugin execution
type ExecutionContext struct {
	Variables map[string]interface{} `json:"variables"`
	Secrets   map[string]string      `json:"secrets"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// PluginServer interface for serving plugins
type PluginServer interface {
	Serve(plugin Plugin)
}

// PluginClient interface for plugin clients
type PluginClient interface {
	GetPlugin() Plugin
	Kill()
}
