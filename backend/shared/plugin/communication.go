package plugin

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// PluginCommunicationBus handles communication between plugins
type PluginCommunicationBus struct {
	plugins     map[string]Plugin
	subscribers map[string][]EventSubscriber
	sharedStore map[string]interface{}
	mutex       sync.RWMutex
}

// EventSubscriber represents a plugin that subscribes to events
type EventSubscriber struct {
	PluginName string
	Handler    func(event Event) error
}

// Event represents an event that can be published between plugins
type Event struct {
	Type      string                 `json:"type"`
	Source    string                 `json:"source"`
	Target    string                 `json:"target,omitempty"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
	ID        string                 `json:"id"`
}

// PluginCallRequest represents a direct call between plugins
type PluginCallRequest struct {
	TargetPlugin string                 `json:"target_plugin"`
	Operation    string                 `json:"operation"`
	Parameters   map[string]interface{} `json:"parameters"`
	Context      map[string]interface{} `json:"context"`
}

// PluginCallResponse represents the response from a plugin call
type PluginCallResponse struct {
	Success bool                   `json:"success"`
	Data    map[string]interface{} `json:"data"`
	Error   string                 `json:"error,omitempty"`
}

// NewPluginCommunicationBus creates a new plugin communication bus
func NewPluginCommunicationBus() *PluginCommunicationBus {
	return &PluginCommunicationBus{
		plugins:     make(map[string]Plugin),
		subscribers: make(map[string][]EventSubscriber),
		sharedStore: make(map[string]interface{}),
	}
}

// RegisterPlugin registers a plugin with the communication bus
func (bus *PluginCommunicationBus) RegisterPlugin(name string, plugin Plugin) error {
	bus.mutex.Lock()
	defer bus.mutex.Unlock()

	if _, exists := bus.plugins[name]; exists {
		return fmt.Errorf("plugin %s already registered", name)
	}

	bus.plugins[name] = plugin
	return nil
}

// UnregisterPlugin removes a plugin from the communication bus
func (bus *PluginCommunicationBus) UnregisterPlugin(name string) error {
	bus.mutex.Lock()
	defer bus.mutex.Unlock()

	if _, exists := bus.plugins[name]; !exists {
		return fmt.Errorf("plugin %s not found", name)
	}

	delete(bus.plugins, name)

	// Remove all subscriptions for this plugin
	for eventType, subscribers := range bus.subscribers {
		var newSubscribers []EventSubscriber
		for _, subscriber := range subscribers {
			if subscriber.PluginName != name {
				newSubscribers = append(newSubscribers, subscriber)
			}
		}
		bus.subscribers[eventType] = newSubscribers
	}

	return nil
}

// CallPlugin makes a direct call to another plugin
func (bus *PluginCommunicationBus) CallPlugin(ctx context.Context, request PluginCallRequest) (*PluginCallResponse, error) {
	bus.mutex.RLock()
	targetPlugin, exists := bus.plugins[request.TargetPlugin]
	bus.mutex.RUnlock()

	if !exists {
		return &PluginCallResponse{
			Success: false,
			Error:   fmt.Sprintf("plugin %s not found", request.TargetPlugin),
		}, nil
	}

	// Execute the operation on the target plugin
	result, err := targetPlugin.Execute(ctx, request.Operation, request.Parameters)
	if err != nil {
		return &PluginCallResponse{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	return &PluginCallResponse{
		Success: true,
		Data:    result,
	}, nil
}

// PublishEvent publishes an event to all subscribers
func (bus *PluginCommunicationBus) PublishEvent(event Event) error {
	bus.mutex.RLock()
	subscribers, exists := bus.subscribers[event.Type]
	bus.mutex.RUnlock()

	if !exists {
		// No subscribers for this event type
		return nil
	}

	// Set timestamp and ID if not provided
	if event.Timestamp.IsZero() {
		event.Timestamp = time.Now()
	}
	if event.ID == "" {
		event.ID = fmt.Sprintf("%s-%d", event.Type, time.Now().UnixNano())
	}

	// Notify all subscribers
	for _, subscriber := range subscribers {
		// Skip if target is specified and doesn't match
		if event.Target != "" && event.Target != subscriber.PluginName {
			continue
		}

		go func(sub EventSubscriber) {
			if err := sub.Handler(event); err != nil {
				// Log error but don't fail the entire publish operation
				fmt.Printf("Error handling event %s in plugin %s: %v\n", event.Type, sub.PluginName, err)
			}
		}(subscriber)
	}

	return nil
}

// SubscribeToEvent subscribes a plugin to an event type
func (bus *PluginCommunicationBus) SubscribeToEvent(eventType, pluginName string, handler func(event Event) error) error {
	bus.mutex.Lock()
	defer bus.mutex.Unlock()

	// Check if plugin is registered
	if _, exists := bus.plugins[pluginName]; !exists {
		return fmt.Errorf("plugin %s not registered", pluginName)
	}

	subscriber := EventSubscriber{
		PluginName: pluginName,
		Handler:    handler,
	}

	bus.subscribers[eventType] = append(bus.subscribers[eventType], subscriber)
	return nil
}

// UnsubscribeFromEvent removes a plugin's subscription to an event type
func (bus *PluginCommunicationBus) UnsubscribeFromEvent(eventType, pluginName string) error {
	bus.mutex.Lock()
	defer bus.mutex.Unlock()

	subscribers, exists := bus.subscribers[eventType]
	if !exists {
		return fmt.Errorf("no subscribers for event type %s", eventType)
	}

	var newSubscribers []EventSubscriber
	found := false
	for _, subscriber := range subscribers {
		if subscriber.PluginName != pluginName {
			newSubscribers = append(newSubscribers, subscriber)
		} else {
			found = true
		}
	}

	if !found {
		return fmt.Errorf("plugin %s not subscribed to event type %s", pluginName, eventType)
	}

	bus.subscribers[eventType] = newSubscribers
	return nil
}

// SetSharedData stores data that can be accessed by multiple plugins
func (bus *PluginCommunicationBus) SetSharedData(key string, value interface{}) {
	bus.mutex.Lock()
	defer bus.mutex.Unlock()
	bus.sharedStore[key] = value
}

// GetSharedData retrieves shared data
func (bus *PluginCommunicationBus) GetSharedData(key string) (interface{}, bool) {
	bus.mutex.RLock()
	defer bus.mutex.RUnlock()
	value, exists := bus.sharedStore[key]
	return value, exists
}

// DeleteSharedData removes shared data
func (bus *PluginCommunicationBus) DeleteSharedData(key string) {
	bus.mutex.Lock()
	defer bus.mutex.Unlock()
	delete(bus.sharedStore, key)
}

// ListPlugins returns a list of registered plugins
func (bus *PluginCommunicationBus) ListPlugins() []string {
	bus.mutex.RLock()
	defer bus.mutex.RUnlock()

	var plugins []string
	for name := range bus.plugins {
		plugins = append(plugins, name)
	}
	return plugins
}

// GetPluginMetadata returns metadata for a specific plugin
func (bus *PluginCommunicationBus) GetPluginMetadata(name string) (Metadata, error) {
	bus.mutex.RLock()
	plugin, exists := bus.plugins[name]
	bus.mutex.RUnlock()

	if !exists {
		return Metadata{}, fmt.Errorf("plugin %s not found", name)
	}

	return plugin.GetMetadata(), nil
}

// Global communication bus instance
var globalCommunicationBus = NewPluginCommunicationBus()

// GetGlobalCommunicationBus returns the global plugin communication bus
func GetGlobalCommunicationBus() *PluginCommunicationBus {
	return globalCommunicationBus
}

// Common event types for plugin communication
const (
	EventTypeGitCloneCompleted    = "git.clone.completed"
	EventTypeGitCloneFailed       = "git.clone.failed"
	EventTypeDeploymentStarted    = "deployment.started"
	EventTypeDeploymentCompleted  = "deployment.completed"
	EventTypeDeploymentFailed     = "deployment.failed"
	EventTypeEnvironmentCreated   = "environment.created"
	EventTypeEnvironmentUpdated   = "environment.updated"
	EventTypeEnvironmentDeleted   = "environment.deleted"
	EventTypeSecretsUpdated       = "secrets.updated"
	EventTypeConfigurationChanged = "configuration.changed"
)

// Helper functions for common plugin communication patterns

// PublishGitCloneCompleted publishes a git clone completed event
func PublishGitCloneCompleted(source, targetDir, commitHash string, metadata map[string]interface{}) error {
	event := Event{
		Type:   EventTypeGitCloneCompleted,
		Source: source,
		Data: map[string]interface{}{
			"target_dir":  targetDir,
			"commit_hash": commitHash,
			"metadata":    metadata,
		},
	}
	return globalCommunicationBus.PublishEvent(event)
}

// PublishDeploymentCompleted publishes a deployment completed event
func PublishDeploymentCompleted(source, environmentID, deploymentID string, result map[string]interface{}) error {
	event := Event{
		Type:   EventTypeDeploymentCompleted,
		Source: source,
		Data: map[string]interface{}{
			"environment_id": environmentID,
			"deployment_id":  deploymentID,
			"result":         result,
		},
	}
	return globalCommunicationBus.PublishEvent(event)
}
