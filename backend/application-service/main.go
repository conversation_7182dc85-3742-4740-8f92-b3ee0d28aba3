package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/claudio/deploy-orchestrator/application-service/storage"
	"github.com/claudio/deploy-orchestrator/shared/auth"

	"github.com/claudio/deploy-orchestrator/application-service/config"
	"github.com/claudio/deploy-orchestrator/application-service/internal/handlers"
	"github.com/claudio/deploy-orchestrator/application-service/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"go.uber.org/zap"
	"gorm.io/gorm"

	sharedConfig "github.com/claudio/deploy-orchestrator/shared/config"
	// Import shared modules
	"github.com/claudio/deploy-orchestrator/shared/gateway"
	"github.com/claudio/deploy-orchestrator/shared/logging"
	"github.com/claudio/deploy-orchestrator/shared/monitoring"
)

func main() {
	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		fmt.Printf("Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logger - use the shared config type
	logging.InitLogger(sharedConfig.LoggingConfig{
		Level:      cfg.Logging.Level,
		Format:     cfg.Logging.Format,
		Output:     cfg.Logging.Output,
		TimeFormat: "2006-01-02T15:04:05Z07:00", // Default time format
	})
	// Initialize logger with configuration
	logger := logging.Default().Named("deployable-service")

	// Log startup information
	logger.Info("Starting deployable service",
		logging.String("version", cfg.Service.Version),
		logging.Int("port", cfg.Server.Port),
	)

	if cfg.Logging.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// Initialize database
	var db *gorm.DB
	var dbErr error
	if cfg.Database.URL != "" {
		logger.Info("Connecting to database",
			logging.String("url", cfg.Database.URL),
			logging.Int("maxRetries", cfg.Database.MaxRetries),
			logging.Int("retryInterval", cfg.Database.RetryInterval),
		)
		db, dbErr = storage.InitDatabase(cfg)
	} else {
		logger.Info("Database URL not configured, using in-memory database")
		db = &gorm.DB{}
	}

	if dbErr != nil {
		logger.Error("Failed to initialize database", logging.Error(dbErr))
		os.Exit(1)
	}

	logger.Info("Database connection established successfully")

	// Run database migrations
	logger.Info("Running database migrations...")

	// Initialize monitoring system
	monitoringManager := monitoring.NewMonitoringManager("deployable-service", cfg.Service.Version, cfg.Gateway.Environment)
	monitoringManager.AddDatabase(db, "postgres")
	logger.Info("Monitoring system initialized")

	// Start monitoring server
	if cfg.Monitoring.Enabled {
		go func() {
			metricsAddr := fmt.Sprintf(":%d", cfg.Monitoring.Port)
			logger.Info("Starting metrics server", logging.String("address", metricsAddr))
			metricsMux := http.NewServeMux()
			// Use promhttp directly for native HTTP handler
			metricsMux.Handle(cfg.Monitoring.Path, promhttp.HandlerFor(monitoringManager.Metrics.GetRegistry(), promhttp.HandlerOpts{}))

			metricsServer := &http.Server{
				Addr:    metricsAddr,
				Handler: metricsMux,
			}

			if err := metricsServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				logger.Error("Metrics server error", logging.Error(err))
			}
		}()
	}

	// Initialize authentication
	authConfig := &auth.Config{
		JWTSecretKey:       cfg.Auth.JWTSecret,
		AccessTokenExpiry:  time.Duration(cfg.Auth.JWTExpirationMinutes) * time.Minute,
		RefreshTokenExpiry: 7 * 24 * time.Hour, // 7 days
		AdminServiceURL:    cfg.Auth.AdminServiceURL,
	}

	authManager, err := auth.NewAuthManager(authConfig)
	if err != nil {
		logger.Error("Failed to create auth manager", logging.Error(err))
		os.Exit(1)
	}

	// Use shared authentication middleware
	authMiddleware := authManager.AuthMiddleware()

	// Initialize permission service and middleware
	permissionService := auth.NewHTTPPermissionService(cfg.Auth.AdminServiceURL)
	permissionMiddleware := auth.NewPermissionMiddleware(permissionService)

	// Initialize services
	projectValidator := services.NewProjectValidator(cfg.Auth.AdminServiceURL, logger, cfg.Auth.DisableAuth)
	deployableService := services.NewDeployableService(db, projectValidator, logger)

	// Initialize handlers
	deployableHandler := handlers.NewDeployableHandler(deployableService, logger)

	// Register with gateway if enabled
	if cfg.Gateway.Enabled {
		// Create zap logger for gateway client
		zapLogger, _ := zap.NewProduction()
		defer zapLogger.Sync()

		gatewayClient := gateway.NewClientFromSharedConfig("deployable-service", cfg.Server.Port, &cfg.Gateway, zapLogger)
		gatewayClient.SafeRegister()
		logger.Info("Successfully registered with gateway")
	}

	// Setup router
	router := gin.Default()

	// Setup monitoring routes and middleware
	monitoringManager.SetupRoutes(router)
	monitoringManager.SetupMiddleware(router)

	// Apply global middleware
	router.Use(func(c *gin.Context) {
		start := time.Now()
		c.Next()
		duration := time.Since(start)
		logger.Info("Request",
			logging.String("method", c.Request.Method),
			logging.String("path", c.Request.URL.Path),
			logging.Int("status", c.Writer.Status()),
			logging.Duration("duration", duration),
		)
	})

	// API routes
	api := router.Group("/api/v1")

	// Apply permission middleware only if auth is not disabled
	createPermCheck := func(permission string) gin.HandlerFunc {
		if cfg.Auth.DisableAuth {
			return func(c *gin.Context) { c.Next() }
		}
		return permissionMiddleware.RequirePermission(permission, nil)
	}

	{
		// Apply authentication middleware to all API routes only if auth is not disabled
		if !cfg.Auth.DisableAuth {
			api.Use(authMiddleware)
		} else {
			logger.Info("Authentication is disabled - skipping auth middleware")
			// Add a fake user context for testing
			api.Use(func(c *gin.Context) {
				c.Set("userID", "test-user-id")
				c.Set("user", map[string]interface{}{
					"id":       "test-user-id",
					"username": "test-user",
					"roles":    []string{"admin"},
				})
				c.Next()
			})
		}

		// Unified Deployables API - replaces both applications and components
		deployables := api.Group("/deployables")
		{
			// Core CRUD operations
			deployables.POST("", createPermCheck("deployable:create"), deployableHandler.CreateDeployable)
			deployables.GET("", createPermCheck("deployable:view"), deployableHandler.ListDeployables)
			deployables.GET("/hierarchy", createPermCheck("deployable:view"), deployableHandler.GetHierarchy)
			deployables.GET("/:id", createPermCheck("deployable:view"), deployableHandler.GetDeployable)
			deployables.PUT("/:id", createPermCheck("deployable:update"), deployableHandler.UpdateDeployable)
			deployables.DELETE("/:id", createPermCheck("deployable:delete"), deployableHandler.DeleteDeployable)

			// Deployment operations
			deployables.POST("/deploy", createPermCheck("deploy:create"), deployableHandler.DeployDeployable)
			deployables.POST("/promote", createPermCheck("deploy:promote"), deployableHandler.PromoteDeployment)
			deployables.POST("/rollback", createPermCheck("deploy:rollback"), deployableHandler.RollbackDeployment)
			deployables.POST("/scale", createPermCheck("deploy:scale"), deployableHandler.ScaleDeployment)

			// Status and monitoring
			deployables.GET("/:id/deployments", createPermCheck("deploy:view"), deployableHandler.GetDeployments)
			deployables.GET("/:id/metrics", createPermCheck("deploy:view"), deployableHandler.GetMetrics)
			deployables.GET("/status", createPermCheck("deploy:view"), deployableHandler.GetEnvironmentStatus)
		}

		// Applications API - Compatibility layer for frontend
		applications := api.Group("/applications")
		{
			// Redirect to deployables with type=application filter
			applications.GET("", createPermCheck("deployable:view"), func(c *gin.Context) {
				// Extract query parameters from original request
				projectId := c.Query("projectId")
				if projectId == "" {
					c.JSON(http.StatusBadRequest, gin.H{"error": "projectId query parameter is required"})
					return
				}

				// Call deployables API with application type filter
				c.Request.URL.RawQuery = "projectId=" + projectId + "&type=application"
				deployableHandler.ListDeployables(c)
			})

			applications.GET("/:id", createPermCheck("deployable:view"), deployableHandler.GetDeployable)
			applications.POST("", createPermCheck("deployable:create"), deployableHandler.CreateDeployable)
			applications.PUT("/:id", createPermCheck("deployable:update"), deployableHandler.UpdateDeployable)
			applications.DELETE("/:id", createPermCheck("deployable:delete"), deployableHandler.DeleteDeployable)
		}

		// Application Groups API - Compatibility layer for frontend
		applicationGroups := api.Group("/application-groups")
		{
			applicationGroups.GET("", createPermCheck("deployable:view"), func(c *gin.Context) {
				// Extract query parameters from original request
				projectId := c.Query("projectId")
				if projectId == "" {
					c.JSON(http.StatusBadRequest, gin.H{"error": "projectId query parameter is required"})
					return
				}

				// Call deployables API with application type filter and parent filtering
				c.Request.URL.RawQuery = "projectId=" + projectId + "&type=application&parentId="
				deployableHandler.ListDeployables(c)
			})
		}
	}

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Server.IdleTimeout) * time.Second,
	}

	// Start server in a goroutine
	go func() {
		logger.Info("Starting HTTP server",
			logging.String("address", server.Addr),
		)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", logging.Error(err))
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// Create a context with timeout for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown the server gracefully
	if err := server.Shutdown(ctx); err != nil {
		logger.Fatal("Server forced to shutdown", logging.Error(err))
	}

	logger.Info("Server exited")
}
