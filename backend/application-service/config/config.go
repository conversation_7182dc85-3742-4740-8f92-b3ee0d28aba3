package config

import (
	"fmt"

	sharedConfig "github.com/claudio/deploy-orchestrator/shared/config"
)

// Config represents the configuration for the application service
type Config struct {
	Service      sharedConfig.ServiceConfig    `mapstructure:"service" yaml:"service"`
	Server       sharedConfig.ServerConfig     `mapstructure:"server" yaml:"server"`
	Database     sharedConfig.DBConfig         `mapstructure:"db" yaml:"db"`
	Logging      sharedConfig.LoggingConfig    `mapstructure:"logging" yaml:"logging"`
	Auth         sharedConfig.AuthConfig       `mapstructure:"auth" yaml:"auth"`
	Gateway      sharedConfig.GatewayConfig    `mapstructure:"gateway" yaml:"gateway"`
	Monitoring   sharedConfig.MonitoringConfig `mapstructure:"monitoring" yaml:"monitoring"`
	AdminService AdminServiceConfig            `mapstructure:"adminService" yaml:"adminService"`
}

// AdminServiceConfig contains configuration for admin service integration
type AdminServiceConfig struct {
	URL string `mapstructure:"url" yaml:"url" default:"http://localhost:8086"`
}

// LoadConfig loads configuration from environment variables and config file
func LoadConfig() (*Config, error) {
	cfg := &Config{}

	// Use the shared config loader
	if err := sharedConfig.LoadConfig(cfg, "application-service"); err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	return cfg, nil
}
