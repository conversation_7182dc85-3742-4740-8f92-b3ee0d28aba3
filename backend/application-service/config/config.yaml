# Application Service Configuration
service:
  name: "application-service"
  version: "1.0.0"

# Server Configuration
server:
  host: "0.0.0.0"
  port: 8089
  read_timeout: "30"
  write_timeout: "30"
  idle_timeout: "60"

# Database Configuration
db:
  url: "postgres://postgres:postgres@localhost:5432/deploy_orchestrator?sslmode=disable"
  max_retries: 5
  retry_interval: 3
  max_open_conns: 25
  max_idle_conns: 10
  query_timeout: 10
  log_level: "info"

# Monitoring Configuration
monitoring:
  enabled: true
  port: 9083
  path: "/metrics"
  metrics_path: "/metrics"
  health_path: "/health"

# Authentication Configuration
auth:
  jwt_secret: "default-secret-key-in-production"
  jwt_expiration_minutes: 60
  access_token_expiry: "1h"
  refresh_token_expiry: "168h"
  admin_service_url: "http://localhost:8086"
  api_key_header: "X-API-Key"
  disable: false

# Gateway Configuration
gateway:
  url: "http://localhost:8000"
  token: ""
  enabled: true
  service_version: "1.0.0"
  environment: "development"
  region: "local"
  retry_attempts: 3
  health_check_path: "/health"
  tags: ["api", "application-service", "microservice"]

# Logging Configuration
logging:
  level: "info"
  format: "json"
  output: "stdout"

# Admin Service Configuration
adminService:
  url: "http://localhost:8086"

# Health Checks Configuration
health_checks:
  enabled: true
  interval: "5m"
  timeout: "30s"
  retry_attempts: 3