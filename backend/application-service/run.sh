#!/bin/bash

# Application Service Development Runner
# This script sets up the development environment and runs the application service

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting Application Service Development Environment${NC}"

# Set default environment variables if not set
export SERVICE_NAME=${SERVICE_NAME:-"application-service"}
export SERVICE_VERSION=${SERVICE_VERSION:-"1.0.0"}
export SERVICE_PORT=${SERVICE_PORT:-8085}
export SERVICE_HOST=${SERVICE_HOST:-"0.0.0.0"}

export DB_HOST=${DB_HOST:-"localhost"}
export DB_PORT=${DB_PORT:-"5432"}
export DB_USERNAME=${DB_USERNAME:-"postgres"}
export DB_PASSWORD=${DB_PASSWORD:-"postgres"}
export DB_NAME=${DB_NAME:-"application_service"}
export DB_SSL_MODE=${DB_SSL_MODE:-"disable"}

export JWT_SECRET=${JWT_SECRET:-"your-secret-key-change-in-production"}
export TOKEN_EXPIRY=${TOKEN_EXPIRY:-"24h"}
export REFRESH_EXPIRY=${REFRESH_EXPIRY:-"7d"}

export LOG_LEVEL=${LOG_LEVEL:-"info"}
export LOG_FORMAT=${LOG_FORMAT:-"json"}

export MONITORING_ENABLED=${MONITORING_ENABLED:-"true"}
export METRICS_PORT=${METRICS_PORT:-9085}

export ADMIN_SERVICE_URL=${ADMIN_SERVICE_URL:-"http://localhost:8080"}

echo -e "${YELLOW}Environment Configuration:${NC}"
echo "  Service: $SERVICE_NAME:$SERVICE_PORT"
echo "  Database: $DB_HOST:$DB_PORT/$DB_NAME"
echo "  Monitoring: Port $METRICS_PORT"
echo "  Admin Service: $ADMIN_SERVICE_URL"
echo ""

# Check if database is accessible
echo -e "${YELLOW}Checking database connectivity...${NC}"
until PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USERNAME" -d "postgres" -c '\q' 2>/dev/null; do
  echo -e "${RED}Database is unavailable - sleeping${NC}"
  sleep 2
done

echo -e "${GREEN}Database is available${NC}"

# Create database if it doesn't exist
echo -e "${YELLOW}Creating database if not exists...${NC}"
PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USERNAME" -d "postgres" -c "CREATE DATABASE $DB_NAME;" 2>/dev/null || echo "Database $DB_NAME already exists"

# Install/update dependencies
echo -e "${YELLOW}Installing dependencies...${NC}"
go mod tidy
go mod download

# Run the application
echo -e "${GREEN}Starting Application Service...${NC}"
echo ""
exec go run main.go
