package main

import (
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/claudio/deploy-orchestrator/application-service/config"
	"github.com/claudio/deploy-orchestrator/application-service/internal/models"
	"github.com/claudio/deploy-orchestrator/shared/db"
	"gorm.io/gorm"
)

// Legacy models for migration
type LegacyApplicationGroup struct {
	ID          string `gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	Name        string `gorm:"not null"`
	Description string
	ProjectID   string `gorm:"not null"`
	IsActive    bool   `gorm:"default:true"`
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

type LegacyApplication struct {
	ID             string `gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	Name           string `gorm:"not null"`
	Description    string
	GroupID        string `gorm:"not null"`
	ProjectID      string `gorm:"not null"`
	Repository     string
	Branch         string `gorm:"default:main"`
	BuildCommand   string
	StartCommand   string
	HealthEndpoint string
	Port           int
	IsActive       bool `gorm:"default:true"`
	CreatedAt      time.Time
	UpdatedAt      time.Time
}

type LegacyApplicationComponent struct {
	ID             string `gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	Name           string `gorm:"not null"`
	Description    string
	ApplicationID  string `gorm:"not null"`
	ProjectID      string `gorm:"not null"`
	Type           string `gorm:"not null"`
	Image          string
	Version        string
	Port           int
	HealthEndpoint string
	ConfigTemplate string `gorm:"type:text"`
	IsActive       bool   `gorm:"default:true"`
	CreatedAt      time.Time
	UpdatedAt      time.Time
}

func (LegacyApplicationGroup) TableName() string     { return "application_groups" }
func (LegacyApplication) TableName() string          { return "applications" }
func (LegacyApplicationComponent) TableName() string { return "application_components" }

func main() {
	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	dbConfig := db.Config{
		URL:           cfg.Database.URL,
		MaxRetries:    cfg.Database.MaxRetries,
		RetryInterval: time.Duration(cfg.Database.RetryInterval) * time.Second,
		LogLevel:      cfg.Database.LogLevel,
	}

	database, err := db.Connect(dbConfig)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	log.Println("Starting migration from legacy Application/Component to unified Deployable structure...")

	// Check if migration is needed
	if !needsMigration(database) {
		log.Println("No legacy data found or migration already completed. Exiting.")
		return
	}

	// Run migration
	if err := migrateToDeployable(database); err != nil {
		log.Fatalf("Migration failed: %v", err)
	}

	log.Println("Migration completed successfully!")
}

func needsMigration(db *gorm.DB) bool {
	// Check if legacy tables exist and have data
	var groupCount, appCount, componentCount int64

	if db.Migrator().HasTable(&LegacyApplicationGroup{}) {
		db.Model(&LegacyApplicationGroup{}).Count(&groupCount)
	}

	if db.Migrator().HasTable(&LegacyApplication{}) {
		db.Model(&LegacyApplication{}).Count(&appCount)
	}

	if db.Migrator().HasTable(&LegacyApplicationComponent{}) {
		db.Model(&LegacyApplicationComponent{}).Count(&componentCount)
	}

	return groupCount > 0 || appCount > 0 || componentCount > 0
}

func migrateToDeployable(db *gorm.DB) error {
	// Start transaction
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Migrate Application Groups to root Deployables
	if err := migrateApplicationGroups(tx); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to migrate application groups: %w", err)
	}

	// Migrate Applications to Deployables
	if err := migrateApplications(tx); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to migrate applications: %w", err)
	}

	// Migrate Components to child Deployables
	if err := migrateComponents(tx); err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to migrate components: %w", err)
	}

	// Commit transaction
	return tx.Commit().Error
}

func migrateApplicationGroups(tx *gorm.DB) error {
	var groups []LegacyApplicationGroup
	if err := tx.Find(&groups).Error; err != nil {
		return err
	}

	log.Printf("Migrating %d application groups...", len(groups))

	for _, group := range groups {
		deployable := &models.Deployable{
			BaseModel: models.BaseModel{
				ID:        group.ID,
				CreatedAt: group.CreatedAt,
				UpdatedAt: group.UpdatedAt,
			},
			Name:        group.Name,
			Description: group.Description,
			Type:        models.DeployableTypeApplication,
			ProjectID:   group.ProjectID,
			ParentID:    nil, // Root level
			IsActive:    group.IsActive,
			// Set defaults for required fields
			MinReplicas: 1,
			MaxReplicas: 10,
			Labels:      make(map[string]string),
			Annotations: make(map[string]string),
		}

		// Add metadata to identify migrated groups
		deployable.Labels["migrated-from"] = "application-group"
		deployable.Labels["original-id"] = group.ID

		if err := tx.Create(deployable).Error; err != nil {
			return fmt.Errorf("failed to migrate group %s: %w", group.Name, err)
		}

		log.Printf("Migrated application group: %s", group.Name)
	}

	return nil
}

func migrateApplications(tx *gorm.DB) error {
	var applications []LegacyApplication
	if err := tx.Find(&applications).Error; err != nil {
		return err
	}

	log.Printf("Migrating %d applications...", len(applications))

	for _, app := range applications {
		// Find parent deployable (former group)
		var parentDeployable models.Deployable
		if err := tx.Where("id = ? AND labels->>'migrated-from' = ?", app.GroupID, "application-group").First(&parentDeployable).Error; err != nil {
			log.Printf("Warning: Could not find parent group for application %s, creating as root", app.Name)
		}

		var parentID *string
		if parentDeployable.ID != "" {
			parentID = &parentDeployable.ID
		}

		deployable := &models.Deployable{
			BaseModel: models.BaseModel{
				ID:        app.ID,
				CreatedAt: app.CreatedAt,
				UpdatedAt: app.UpdatedAt,
			},
			Name:           app.Name,
			Description:    app.Description,
			Type:           models.DeployableTypeApplication,
			ProjectID:      app.ProjectID,
			ParentID:       parentID,
			IsActive:       app.IsActive,
			Repository:     app.Repository,
			Branch:         app.Branch,
			BuildCommand:   app.BuildCommand,
			StartCommand:   app.StartCommand,
			HealthEndpoint: app.HealthEndpoint,
			Port:           app.Port,
			// Set defaults
			MinReplicas: 1,
			MaxReplicas: 10,
			Labels:      make(map[string]string),
			Annotations: make(map[string]string),
		}

		// Add metadata to identify migrated applications
		deployable.Labels["migrated-from"] = "application"
		deployable.Labels["original-id"] = app.ID
		deployable.Labels["original-group-id"] = app.GroupID

		if err := tx.Create(deployable).Error; err != nil {
			return fmt.Errorf("failed to migrate application %s: %w", app.Name, err)
		}

		log.Printf("Migrated application: %s", app.Name)
	}

	return nil
}

func migrateComponents(tx *gorm.DB) error {
	var components []LegacyApplicationComponent
	if err := tx.Find(&components).Error; err != nil {
		return err
	}

	log.Printf("Migrating %d components...", len(components))

	for _, component := range components {
		// Find parent deployable (former application)
		var parentDeployable models.Deployable
		if err := tx.Where("id = ? AND labels->>'migrated-from' = ?", component.ApplicationID, "application").First(&parentDeployable).Error; err != nil {
			log.Printf("Warning: Could not find parent application for component %s, creating as root", component.Name)
		}

		var parentID *string
		if parentDeployable.ID != "" {
			parentID = &parentDeployable.ID
		}

		// Map component type to deployable type
		deployableType := mapComponentTypeToDeployableType(component.Type)

		deployable := &models.Deployable{
			BaseModel: models.BaseModel{
				ID:        component.ID,
				CreatedAt: component.CreatedAt,
				UpdatedAt: component.UpdatedAt,
			},
			Name:           component.Name,
			Description:    component.Description,
			Type:           deployableType,
			ProjectID:      component.ProjectID,
			ParentID:       parentID,
			IsActive:       component.IsActive,
			Image:          component.Image,
			ImageTag:       component.Version,
			Port:           component.Port,
			HealthEndpoint: component.HealthEndpoint,
			ConfigTemplate: component.ConfigTemplate,
			// Set defaults
			MinReplicas: 1,
			MaxReplicas: 10,
			Labels:      make(map[string]string),
			Annotations: make(map[string]string),
		}

		// Add metadata to identify migrated components
		deployable.Labels["migrated-from"] = "component"
		deployable.Labels["original-id"] = component.ID
		deployable.Labels["original-application-id"] = component.ApplicationID
		deployable.Labels["original-type"] = component.Type

		if err := tx.Create(deployable).Error; err != nil {
			return fmt.Errorf("failed to migrate component %s: %w", component.Name, err)
		}

		log.Printf("Migrated component: %s", component.Name)
	}

	return nil
}

func mapComponentTypeToDeployableType(componentType string) models.DeployableType {
	switch strings.ToLower(componentType) {
	case "microservice":
		return models.DeployableTypeMicroservice
	case "service":
		return models.DeployableTypeService
	case "database":
		return models.DeployableTypeDatabase
	case "cache":
		return models.DeployableTypeCache
	case "queue":
		return models.DeployableTypeQueue
	case "worker":
		return models.DeployableTypeWorker
	case "api":
		return models.DeployableTypeAPI
	case "frontend":
		return models.DeployableTypeFrontend
	case "library":
		return models.DeployableTypeLibrary
	case "package":
		return models.DeployableTypePackage
	default:
		return models.DeployableTypeComponent
	}
}
