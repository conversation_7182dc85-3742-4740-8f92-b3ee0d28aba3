# Application Service

The Application Service provides comprehensive application-centric management capabilities for the Deploy Orchestrator platform. It manages application groups, applications, components, and deployments with project-based access control.

## Features

### Core Functionality
- **Application Groups**: Organize applications into logical groups within projects
- **Applications**: Manage application definitions with repository, build, and deployment configurations
- **Components**: Handle microservices and components within applications
- **Deployments**: Track and manage application/component deployments across environments
- **Metrics**: Collect and expose application performance metrics

### Security & Authorization
- **Project-based Access Control**: Integration with admin-service for permission validation
- **JWT Authentication**: Secure API access using shared authentication middleware
- **Fine-grained Permissions**: Support for create, read, update, delete, deploy, and manage permissions
- **Admin Override**: Admin users have access to all projects and resources

### Monitoring & Observability
- **Health Checks**: Built-in health endpoint for service monitoring
- **Prometheus Metrics**: Comprehensive metrics collection for monitoring
- **Structured Logging**: JSON-formatted logs with correlation IDs
- **Request Tracing**: Full request lifecycle tracking

## API Endpoints

### Application Groups
- `POST /api/v1/application-service/groups` - Create application group
- `GET /api/v1/application-service/groups` - List accessible application groups
- `GET /api/v1/application-service/groups/{id}` - Get application group details

### Applications
- `POST /api/v1/application-service/applications` - Create application
- `GET /api/v1/application-service/applications` - List accessible applications
- `GET /api/v1/application-service/applications/{id}` - Get application details
- `GET /api/v1/application-service/applications/{id}/metrics` - Get application metrics

### Components
- `POST /api/v1/application-service/components` - Create application component

### Deployments
- `POST /api/v1/application-service/deployments` - Deploy application/component
- `GET /api/v1/application-service/deployments` - List deployments
- `PUT /api/v1/application-service/deployments/{id}/status` - Update deployment status

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `SERVICE_NAME` | `application-service` | Service name for registration |
| `SERVICE_VERSION` | `1.0.0` | Service version |
| `SERVICE_PORT` | `8085` | HTTP server port |
| `SERVICE_HOST` | `0.0.0.0` | HTTP server bind address |
| `DB_HOST` | `localhost` | Database host |
| `DB_PORT` | `5432` | Database port |
| `DB_USERNAME` | `postgres` | Database username |
| `DB_PASSWORD` | `postgres` | Database password |
| `DB_NAME` | `application_service` | Database name |
| `DB_SSL_MODE` | `disable` | Database SSL mode |
| `JWT_SECRET` | `your-secret-key` | JWT signing secret |
| `TOKEN_EXPIRY` | `24h` | JWT token expiry |
| `REFRESH_EXPIRY` | `7d` | Refresh token expiry |
| `LOG_LEVEL` | `info` | Logging level |
| `LOG_FORMAT` | `json` | Log output format |
| `MONITORING_ENABLED` | `true` | Enable Prometheus metrics |
| `METRICS_PORT` | `9085` | Metrics server port |
| `ADMIN_SERVICE_URL` | `http://localhost:8080` | Admin service URL for permissions |

## Database Schema

### Tables
- `application_groups` - Application group definitions
- `applications` - Application configurations
- `application_components` - Component definitions
- `deployments` - Deployment records and status
- `application_metrics` - Performance metrics data

### Key Relationships
- Applications belong to Groups (many-to-one)
- Components belong to Applications (many-to-one)
- Deployments can be for Applications OR Components (one-to-many)
- All entities are project-scoped for access control

## Development

### Prerequisites
- Go 1.21+
- PostgreSQL 13+
- Access to shared modules in parent project

### Running Locally

1. **Start Dependencies**:
   ```bash
   # Start PostgreSQL
   docker run -d --name postgres \
     -e POSTGRES_PASSWORD=postgres \
     -p 5432:5432 postgres:13
   
   # Start Admin Service (for permissions)
   cd ../admin-service && ./run.sh
   ```

2. **Run Application Service**:
   ```bash
   ./run.sh
   ```

3. **Verify Service**:
   ```bash
   curl http://localhost:8085/health
   ```

### Building

```bash
# Build binary
go build -o application-service .

# Build Docker image
docker build -t application-service .
```

### Testing

```bash
# Run unit tests
go test ./...

# Run integration tests
go test -tags=integration ./...
```

## Deployment

### Docker Compose

```yaml
services:
  application-service:
    image: application-service:latest
    ports:
      - "8085:8085"
      - "9085:9085"
    environment:
      - DB_HOST=postgres
      - DB_PASSWORD=your-db-password
      - JWT_SECRET=your-jwt-secret
      - ADMIN_SERVICE_URL=http://admin-service:8080
    depends_on:
      - postgres
      - admin-service
```

### Kubernetes

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: application-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: application-service
  template:
    metadata:
      labels:
        app: application-service
    spec:
      containers:
      - name: application-service
        image: application-service:latest
        ports:
        - containerPort: 8085
        - containerPort: 9085
        env:
        - name: DB_HOST
          value: "postgres-service"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: jwt-secret
        readinessProbe:
          httpGet:
            path: /health
            port: 8085
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /health
            port: 8085
          initialDelaySeconds: 30
          periodSeconds: 10
```

## Integration

### Admin Service Integration
- Validates project access for all operations
- Checks user permissions for create/deploy/manage actions
- Supports admin user override for full platform access
- Caches permission responses for performance

### Gateway Integration
- Registers with API gateway for service discovery
- Supports load balancing and routing
- Provides health checks for circuit breaker patterns

### Monitoring Integration
- Exports Prometheus metrics on `/metrics` endpoint
- Integrates with shared monitoring dashboard
- Provides custom application deployment metrics

## Security Considerations

### Authentication
- All API endpoints require valid JWT tokens
- Tokens are validated using shared JWT manager
- Support for token refresh and expiration handling

### Authorization
- Project-based access control for all resources
- Action-specific permissions (create, deploy, manage)
- Admin users bypass project restrictions
- Service-to-service authentication for admin-service calls

### Data Protection
- Database connections use SSL in production
- Sensitive configuration via environment variables
- Request logging excludes sensitive data
- Rate limiting and request size limits

## Performance

### Database Optimization
- Indexed queries for project-based filtering
- Connection pooling with configurable limits
- Prepared statements for common queries
- Soft deletes for audit trail maintenance

### Caching Strategy
- Permission caching for admin-service calls
- Application configuration caching
- Metrics aggregation for dashboard performance
- Redis integration for distributed caching (future)

### Scalability
- Stateless service design for horizontal scaling
- Database read replicas support
- Background job processing for deployments
- Event-driven architecture for real-time updates

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   - Verify admin-service connectivity
   - Check user project assignments
   - Validate JWT token claims

2. **Database Connection Issues**
   - Check database connectivity
   - Verify SSL mode configuration
   - Check connection pool settings

3. **Deployment Failures**
   - Verify environment-service integration
   - Check provider configurations
   - Review deployment logs

### Debugging

```bash
# Enable debug logging
export LOG_LEVEL=debug

# Check service health
curl -v http://localhost:8085/health

# View metrics
curl http://localhost:9085/metrics

# Check database connectivity
./check-db.sh
```

## Contributing

1. Follow Go coding standards
2. Add tests for new features
3. Update documentation
4. Ensure proper error handling
5. Add appropriate logging
6. Validate permission checks
