package models

import (
	"time"

	"gorm.io/gorm"
)

// Legacy Deployment struct for compatibility during migration
type LegacyDeployment struct {
	BaseModel
	DeployableID string             `json:"deployableId" gorm:"not null;index"`
	Version      string             `json:"version" gorm:"not null"`
	Environment  string             `json:"environment" gorm:"not null"`
	Status       DeploymentStatus   `json:"status" gorm:"default:'pending'"`
	Strategy     DeploymentStrategy `json:"strategy" gorm:"default:'rolling_update'"`
	ConfigData   string             `json:"configData" gorm:"type:text"`
	StartedAt    *time.Time         `json:"startedAt,omitempty"`
	CompletedAt  *time.Time         `json:"completedAt,omitempty"`
	Error        string             `json:"error,omitempty"`
}

// DeployableType represents the type of deployable entity
type DeployableType string

const (
	DeployableTypeApplication  DeployableType = "application"
	DeployableTypeMicroservice DeployableType = "microservice"
	DeployableTypeService      DeployableType = "service"
	DeployableTypeDatabase     DeployableType = "database"
	DeployableTypeCache        DeployableType = "cache"
	DeployableTypeQueue        DeployableType = "queue"
	DeployableTypeWorker       DeployableType = "worker"
	DeployableTypeAPI          DeployableType = "api"
	DeployableTypeFrontend     DeployableType = "frontend"
	DeployableTypeLibrary      DeployableType = "library"
	DeployableTypePackage      DeployableType = "package"
	DeployableTypeComponent    DeployableType = "component"
)

// Deployable represents a unified entity that can be deployed
type Deployable struct {
	BaseModel
	Name        string         `json:"name" gorm:"not null;uniqueIndex:idx_deployable_name_project"`
	Description string         `json:"description"`
	Type        DeployableType `json:"type" gorm:"not null"`
	ProjectID   string         `json:"projectId" gorm:"not null;index;uniqueIndex:idx_deployable_name_project"`
	ParentID    *string        `json:"parentId,omitempty" gorm:"index"`
	IsActive    bool           `json:"isActive" gorm:"default:true"`

	// Repository information
	Repository string   `json:"repository"`
	Branch     string   `json:"branch" gorm:"default:main"`
	Tags       []string `json:"tags" gorm:"serializer:json"`

	// Build configuration
	BuildCommand string `json:"buildCommand"`
	BuildPath    string `json:"buildPath"`
	Dockerfile   string `json:"dockerfile"`
	BuildArgs    string `json:"buildArgs" gorm:"type:text"`

	// Runtime configuration
	StartCommand   string `json:"startCommand"`
	HealthEndpoint string `json:"healthEndpoint"`
	Port           int    `json:"port"`
	Image          string `json:"image"`
	ImageTag       string `json:"imageTag"`

	// Environment configuration
	EnvironmentConfig string   `json:"environmentConfig" gorm:"type:text"`
	ConfigTemplate    string   `json:"configTemplate" gorm:"type:text"`
	SecretNames       []string `json:"secretNames" gorm:"serializer:json"`

	// Resource requirements
	CPURequest    string `json:"cpuRequest"`
	CPULimit      string `json:"cpuLimit"`
	MemoryRequest string `json:"memoryRequest"`
	MemoryLimit   string `json:"memoryLimit"`

	// Scaling configuration
	MinReplicas      int  `json:"minReplicas" gorm:"default:1"`
	MaxReplicas      int  `json:"maxReplicas" gorm:"default:10"`
	AutoscaleEnabled bool `json:"autoscaleEnabled" gorm:"default:false"`

	// Workflow integration
	DeployWorkflowID   *string `json:"deployWorkflowId,omitempty"`
	PromoteWorkflowID  *string `json:"promoteWorkflowId,omitempty"`
	RollbackWorkflowID *string `json:"rollbackWorkflowId,omitempty"`

	// Plugin configuration
	DeploymentPlugin string `json:"deploymentPlugin"`
	MonitoringPlugin string `json:"monitoringPlugin"`
	SecurityPlugin   string `json:"securityPlugin"`

	// Metadata
	Labels      map[string]string `json:"labels" gorm:"serializer:json"`
	Annotations map[string]string `json:"annotations" gorm:"serializer:json"`

	// Relationships
	Parent      *Deployable            `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Children    []Deployable           `json:"children,omitempty" gorm:"foreignKey:ParentID"`
	Versions    []DeployableVersion    `json:"versions,omitempty" gorm:"foreignKey:DeployableID"`
	Deployments []DeployableDeployment `json:"deployments,omitempty" gorm:"foreignKey:DeployableID"`
	Metrics     []DeployableMetrics    `json:"metrics,omitempty" gorm:"foreignKey:DeployableID"`
}

// DeployableVersion represents a version of a deployable
type DeployableVersion struct {
	BaseModel
	DeployableID string                 `json:"deployableId" gorm:"not null;index;uniqueIndex:idx_version_deployable"`
	Version      string                 `json:"version" gorm:"not null;uniqueIndex:idx_version_deployable"`
	ProjectID    string                 `json:"projectId" gorm:"not null;index"`
	Description  string                 `json:"description"`
	GitCommit    string                 `json:"gitCommit"`
	GitTag       string                 `json:"gitTag"`
	BuildNumber  string                 `json:"buildNumber"`
	ImageURL     string                 `json:"imageUrl"`
	IsActive     bool                   `json:"isActive" gorm:"default:true"`
	IsStable     bool                   `json:"isStable" gorm:"default:false"`
	Changelog    string                 `json:"changelog" gorm:"type:text"`
	Deployable   Deployable             `json:"deployable" gorm:"foreignKey:DeployableID"`
	Deployments  []DeployableDeployment `json:"deployments" gorm:"foreignKey:Version;foreignKeyReferences:Version"`
}

// Deployment now references unified Deployable instead of Application/Component
type DeployableDeployment struct {
	BaseModel
	DeployableID         string             `json:"deployableId" gorm:"not null;index"`
	ProjectID            string             `json:"projectId" gorm:"not null;index"`
	EnvironmentID        string             `json:"environmentId" gorm:"not null;index"`
	Version              string             `json:"version" gorm:"not null"`
	Status               DeploymentStatus   `json:"status" gorm:"type:varchar(20);default:pending"`
	Strategy             DeploymentStrategy `json:"strategy" gorm:"type:varchar(20);default:rolling"`
	Configuration        string             `json:"configuration" gorm:"type:text"`
	EnvironmentOverrides string             `json:"environmentOverrides" gorm:"type:text"`
	DeployedBy           string             `json:"deployedBy" gorm:"not null"`
	DeployedAt           *time.Time         `json:"deployedAt,omitempty"`
	CompletedAt          *time.Time         `json:"completedAt,omitempty"`
	RollbackTo           *string            `json:"rollbackTo,omitempty"`
	HealthStatus         HealthStatus       `json:"healthStatus" gorm:"type:varchar(20);default:unknown"`
	LastHealthCheck      *time.Time         `json:"lastHealthCheck,omitempty"`
	Logs                 string             `json:"logs" gorm:"type:text"`
	WorkflowExecutionID  *string            `json:"workflowExecutionId,omitempty"`

	// Resource status
	Replicas        int `json:"replicas" gorm:"default:1"`
	ReadyReplicas   int `json:"readyReplicas" gorm:"default:0"`
	UpdatedReplicas int `json:"updatedReplicas" gorm:"default:0"`

	// Relationships
	Deployable Deployable `json:"deployable" gorm:"foreignKey:DeployableID"`
}

// DeployableMetrics represents metrics for a deployable
type DeployableMetrics struct {
	BaseModel
	DeployableID  string                `json:"deployableId" gorm:"not null;index"`
	DeploymentID  *string               `json:"deploymentId,omitempty" gorm:"index"`
	ProjectID     string                `json:"projectId" gorm:"not null;index"`
	EnvironmentID string                `json:"environmentId" gorm:"not null;index"`
	MetricType    string                `json:"metricType" gorm:"not null"` // cpu, memory, requests, errors, latency, etc.
	Value         float64               `json:"value"`
	Unit          string                `json:"unit"`
	Timestamp     time.Time             `json:"timestamp" gorm:"index"`
	Labels        map[string]string     `json:"labels" gorm:"serializer:json"`
	Deployable    Deployable            `json:"deployable" gorm:"foreignKey:DeployableID"`
	Deployment    *DeployableDeployment `json:"deployment,omitempty" gorm:"foreignKey:DeploymentID"`
}

// DeployableEnvironmentStatus represents the status of a deployable in a specific environment
type DeployableEnvironmentStatus struct {
	BaseModel
	DeployableID      string       `json:"deployableId" gorm:"not null;index;uniqueIndex:idx_deployable_env"`
	EnvironmentID     string       `json:"environmentId" gorm:"not null;index;uniqueIndex:idx_deployable_env"`
	ProjectID         string       `json:"projectId" gorm:"not null;index"`
	CurrentVersion    *string      `json:"currentVersion,omitempty"`
	DesiredVersion    *string      `json:"desiredVersion,omitempty"`
	Status            HealthStatus `json:"status" gorm:"type:varchar(20);default:unknown"`
	LastDeploymentID  *string      `json:"lastDeploymentId,omitempty"`
	LastDeployedAt    *time.Time   `json:"lastDeployedAt,omitempty"`
	LastHealthCheck   *time.Time   `json:"lastHealthCheck,omitempty"`
	ConfigurationHash string       `json:"configurationHash"`
	Replicas          int          `json:"replicas" gorm:"default:1"`
	ReadyReplicas     int          `json:"readyReplicas" gorm:"default:0"`

	// Relationships
	Deployable     Deployable            `json:"deployable" gorm:"foreignKey:DeployableID"`
	LastDeployment *DeployableDeployment `json:"lastDeployment,omitempty" gorm:"foreignKey:LastDeploymentID"`
}

// TableName overrides
func (Deployable) TableName() string                  { return "deployables" }
func (DeployableVersion) TableName() string           { return "deployable_versions" }
func (DeployableDeployment) TableName() string        { return "deployable_deployments" }
func (DeployableMetrics) TableName() string           { return "deployable_metrics" }
func (DeployableEnvironmentStatus) TableName() string { return "deployable_environment_status" }

// MigrateDeployableModels runs GORM auto-migration for all deployable models
func MigrateDeployableModels(db *gorm.DB) error {
	return db.AutoMigrate(
		&Deployable{},
		&DeployableVersion{},
		&DeployableDeployment{},
		&DeployableMetrics{},
		&DeployableEnvironmentStatus{},
	)
}

// IsRoot returns true if this deployable has no parent
func (d *Deployable) IsRoot() bool {
	return d.ParentID == nil
}

// IsLeaf returns true if this deployable has no children
func (d *Deployable) IsLeaf() bool {
	return len(d.Children) == 0
}

// HasChildren returns true if this deployable has child deployables
func (d *Deployable) HasChildren() bool {
	return len(d.Children) > 0
}

// GetFullPath returns the full path from root to this deployable
func (d *Deployable) GetFullPath() string {
	if d.Parent == nil {
		return d.Name
	}
	return d.Parent.GetFullPath() + "/" + d.Name
}
