package models

import (
	"time"
)

// BaseModel contains common fields for all models
type BaseModel struct {
	ID        string    `json:"id" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

// DeploymentStatus represents the status of a deployment
type DeploymentStatus string

const (
	DeploymentStatusPending    DeploymentStatus = "pending"
	DeploymentStatusInProgress DeploymentStatus = "in_progress"
	DeploymentStatusSucceeded  DeploymentStatus = "succeeded"
	DeploymentStatusFailed     DeploymentStatus = "failed"
	DeploymentStatusRolledBack DeploymentStatus = "rolled_back"
)

// DeploymentStrategy represents the deployment strategy
type DeploymentStrategy string

const (
	DeploymentStrategyRollingUpdate DeploymentStrategy = "rolling_update"
	DeploymentStrategyBlueGreen     DeploymentStrategy = "blue_green"
	DeploymentStrategyCanary        DeploymentStrategy = "canary"
	DeploymentStrategyRecreate      DeploymentStrategy = "recreate"
)

// HealthStatus represents the health status of a deployable
type HealthStatus string

const (
	HealthStatusHealthy   HealthStatus = "healthy"
	HealthStatusUnhealthy HealthStatus = "unhealthy"
	HealthStatusUnknown   HealthStatus = "unknown"
	HealthStatusDegraded  HealthStatus = "degraded"
)
