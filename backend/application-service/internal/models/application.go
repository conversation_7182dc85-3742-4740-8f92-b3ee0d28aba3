package models

import (
	"time"

	"gorm.io/gorm"
)

// ApplicationGroup represents a group of related applications
type ApplicationGroup struct {
	BaseModel
	Name         string        `json:"name" gorm:"not null;uniqueIndex:idx_app_group_name_project"`
	Description  string        `json:"description"`
	ProjectID    string        `json:"projectId" gorm:"not null;index;uniqueIndex:idx_app_group_name_project"`
	IsActive     bool          `json:"isActive" gorm:"default:true"`
	Applications []Application `json:"applications" gorm:"foreignKey:GroupID"`
}

// Application represents an application within a group
type Application struct {
	BaseModel
	Name           string                  `json:"name" gorm:"not null;uniqueIndex:idx_app_name_group"`
	Description    string                  `json:"description"`
	GroupID        string                  `json:"groupId" gorm:"not null;index;uniqueIndex:idx_app_name_group"`
	ProjectID      string                  `json:"projectId" gorm:"not null;index"`
	Repository     string                  `json:"repository"`
	Branch         string                  `json:"branch" gorm:"default:main"`
	BuildCommand   string                  `json:"buildCommand"`
	StartCommand   string                  `json:"startCommand"`
	HealthEndpoint string                  `json:"healthEndpoint"`
	Port           int                     `json:"port"`
	IsActive       bool                    `json:"isActive" gorm:"default:true"`
	Group          ApplicationGroup        `json:"group" gorm:"foreignKey:GroupID"`
	Components     []ApplicationComponent  `json:"components" gorm:"foreignKey:ApplicationID"`
	Deployments    []ApplicationDeployment `json:"deployments" gorm:"foreignKey:ApplicationID"`
}

// ApplicationVersion represents a version of an application
type ApplicationVersion struct {
	BaseModel
	ApplicationID string                  `json:"applicationId" gorm:"not null;index;uniqueIndex:idx_version_app"`
	Version       string                  `json:"version" gorm:"not null;uniqueIndex:idx_version_app"`
	ProjectID     string                  `json:"projectId" gorm:"not null;index"`
	Description   string                  `json:"description"`
	GitCommit     string                  `json:"gitCommit"`
	GitTag        string                  `json:"gitTag"`
	BuildNumber   string                  `json:"buildNumber"`
	IsActive      bool                    `json:"isActive" gorm:"default:true"`
	IsStable      bool                    `json:"isStable" gorm:"default:false"`
	Application   Application             `json:"application" gorm:"foreignKey:ApplicationID"`
	Deployments   []ApplicationDeployment `json:"deployments" gorm:"foreignKey:Version;foreignKeyReferences:Version"`
}

// ApplicationComponent represents a deployable component of an application
type ApplicationComponent struct {
	BaseModel
	Name           string                  `json:"name" gorm:"not null;uniqueIndex:idx_component_name_app"`
	Description    string                  `json:"description"`
	ApplicationID  string                  `json:"applicationId" gorm:"not null;index;uniqueIndex:idx_component_name_app"`
	ProjectID      string                  `json:"projectId" gorm:"not null;index"`
	Type           string                  `json:"type" gorm:"not null"` // microservice, database, cache, etc.
	Image          string                  `json:"image"`
	Version        string                  `json:"version"`
	Port           int                     `json:"port"`
	HealthEndpoint string                  `json:"healthEndpoint"`
	ConfigTemplate string                  `json:"configTemplate" gorm:"type:text"`
	IsActive       bool                    `json:"isActive" gorm:"default:true"`
	Application    Application             `json:"application" gorm:"foreignKey:ApplicationID"`
	Deployments    []ApplicationDeployment `json:"deployments" gorm:"foreignKey:ComponentID"`
}

// ApplicationDeployment represents a deployment of an application or component
type ApplicationDeployment struct {
	BaseModel
	ApplicationID   *string               `json:"applicationId,omitempty" gorm:"index"`
	ComponentID     *string               `json:"componentId,omitempty" gorm:"index"`
	ProjectID       string                `json:"projectId" gorm:"not null;index"`
	EnvironmentID   string                `json:"environmentId" gorm:"not null;index"`
	Version         string                `json:"version" gorm:"not null"`
	Status          DeploymentStatus      `json:"status" gorm:"type:varchar(20);default:pending"`
	Strategy        DeploymentStrategy    `json:"strategy" gorm:"type:varchar(20);default:rolling"`
	Configuration   string                `json:"configuration" gorm:"type:text"`
	DeployedBy      string                `json:"deployedBy" gorm:"not null"`
	DeployedAt      *time.Time            `json:"deployedAt,omitempty"`
	RollbackTo      *string               `json:"rollbackTo,omitempty"`
	HealthStatus    HealthStatus          `json:"healthStatus" gorm:"type:varchar(20);default:unknown"`
	LastHealthCheck *time.Time            `json:"lastHealthCheck,omitempty"`
	Logs            string                `json:"logs" gorm:"type:text"`
	Application     *Application          `json:"application,omitempty" gorm:"foreignKey:ApplicationID"`
	Component       *ApplicationComponent `json:"component,omitempty" gorm:"foreignKey:ComponentID"`
}

// ApplicationMetrics represents metrics for an application
type ApplicationMetrics struct {
	BaseModel
	ApplicationID string                `json:"applicationId" gorm:"not null;index"`
	ComponentID   *string               `json:"componentId,omitempty" gorm:"index"`
	DeploymentID  string                `json:"deploymentId" gorm:"not null;index"`
	ProjectID     string                `json:"projectId" gorm:"not null;index"`
	EnvironmentID string                `json:"environmentId" gorm:"not null;index"`
	MetricType    string                `json:"metricType" gorm:"not null"` // cpu, memory, requests, errors, etc.
	Value         float64               `json:"value"`
	Unit          string                `json:"unit"`
	Timestamp     time.Time             `json:"timestamp" gorm:"index"`
	Application   Application           `json:"application" gorm:"foreignKey:ApplicationID"`
	Component     *ApplicationComponent `json:"component,omitempty" gorm:"foreignKey:ComponentID"`
	Deployment    ApplicationDeployment `json:"deployment" gorm:"foreignKey:DeploymentID"`
}

// ApplicationArtifact represents build artifacts for a version
type ApplicationArtifact struct {
	BaseModel
	VersionID   string `json:"versionId" gorm:"not null;index"`
	Name        string `json:"name" gorm:"not null"`
	Type        string `json:"type" gorm:"not null"` // docker, helm, binary, etc.
	Location    string `json:"location" gorm:"not null"`
	Size        int64  `json:"size"`
	Checksum    string `json:"checksum"`
	DownloadURL string `json:"downloadUrl"`
	IsPublic    bool   `json:"isPublic" gorm:"default:false"`
}

// DeploymentConfiguration represents deployment configuration for applications
type DeploymentConfiguration struct {
	BaseModel
	ApplicationID  *string               `json:"applicationId,omitempty" gorm:"index"`
	ComponentID    *string               `json:"componentId,omitempty" gorm:"index"`
	ProjectID      string                `json:"projectId" gorm:"not null;index"`
	Name           string                `json:"name" gorm:"not null"`
	Description    string                `json:"description"`
	WorkflowID     string                `json:"workflowId" gorm:"not null;index"`
	ProviderType   string                `json:"providerType" gorm:"not null"`
	Parameters     string                `json:"parameters" gorm:"type:jsonb"`
	SecretMappings string                `json:"secretMappings" gorm:"type:jsonb"`
	IsDefault      bool                  `json:"isDefault" gorm:"default:false"`
	IsActive       bool                  `json:"isActive" gorm:"default:true"`
	Application    *Application          `json:"application,omitempty" gorm:"foreignKey:ApplicationID"`
	Component      *ApplicationComponent `json:"component,omitempty" gorm:"foreignKey:ComponentID"`
}

// EnvironmentVersion tracks which versions are deployed to which environments
type EnvironmentVersion struct {
	BaseModel
	ApplicationID *string               `json:"applicationId,omitempty" gorm:"index"`
	ComponentID   *string               `json:"componentId,omitempty" gorm:"index"`
	ProjectID     string                `json:"projectId" gorm:"not null;index"`
	EnvironmentID string                `json:"environmentId" gorm:"not null;index"`
	Version       string                `json:"version" gorm:"not null"`
	DeploymentID  string                `json:"deploymentId" gorm:"not null;index"`
	Status        string                `json:"status" gorm:"not null;default:active"`
	DeployedAt    time.Time             `json:"deployedAt"`
	DeployedBy    string                `json:"deployedBy" gorm:"not null"`
	PromotedFrom  *string               `json:"promotedFrom,omitempty"` // Source environment ID
	Application   *Application          `json:"application,omitempty" gorm:"foreignKey:ApplicationID"`
	Component     *ApplicationComponent `json:"component,omitempty" gorm:"foreignKey:ComponentID"`
}

// TableName overrides
func (ApplicationGroup) TableName() string        { return "application_groups" }
func (Application) TableName() string             { return "applications" }
func (ApplicationVersion) TableName() string      { return "application_versions" }
func (ApplicationComponent) TableName() string    { return "application_components" }
func (ApplicationDeployment) TableName() string   { return "deployments" }
func (ApplicationMetrics) TableName() string      { return "application_metrics" }
func (ApplicationArtifact) TableName() string     { return "application_artifacts" }
func (DeploymentConfiguration) TableName() string { return "deployment_configurations" }
func (EnvironmentVersion) TableName() string      { return "environment_versions" }

// MigrateApplicationModels runs GORM auto-migration for all application models
func MigrateApplicationModels(db *gorm.DB) error {
	return db.AutoMigrate(
		&ApplicationGroup{},
		&Application{},
		&ApplicationVersion{},
		&ApplicationComponent{},
		&ApplicationDeployment{},
		&ApplicationMetrics{},
		&ApplicationArtifact{},
		&DeploymentConfiguration{},
		&EnvironmentVersion{},
	)
}
