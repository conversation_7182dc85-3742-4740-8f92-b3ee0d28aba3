package handlers

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/claudio/deploy-orchestrator/application-service/internal/models"
	"github.com/claudio/deploy-orchestrator/application-service/internal/services"
	"github.com/claudio/deploy-orchestrator/shared/logging"
	"github.com/gin-gonic/gin"
)

// DeployableHandler handles HTTP requests for deployables
type DeployableHandler struct {
	service *services.DeployableService
	logger  logging.Logger
}

// NewDeployableHandler creates a new deployable handler
func NewDeployableHandler(service *services.DeployableService, logger logging.Logger) *DeployableHandler {
	return &DeployableHandler{
		service: service,
		logger:  logger,
	}
}

// getAuthTokenFromHeader extracts the JWT token from the Authorization header
func (h *DeployableHandler) getAuthTokenFromHeader(c *gin.Context) string {
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		parts := strings.Split(authHeader, " ")
		if len(parts) == 2 && strings.ToLower(parts[0]) == "bearer" {
			return parts[1]
		}
	}
	return ""
}

// getUserID extracts user ID from context
func (h *DeployableHandler) getUserID(c *gin.Context) string {
	if userID, exists := c.Get("userID"); exists {
		if id, ok := userID.(string); ok {
			return id
		}
	}
	return "unknown"
}

// CreateDeployable creates a new deployable
func (h *DeployableHandler) CreateDeployable(c *gin.Context) {
	var req services.CreateDeployableRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", logging.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	userID := h.getUserID(c)
	authToken := h.getAuthTokenFromHeader(c)

	deployable, err := h.service.CreateDeployable(c.Request.Context(), &req, userID, authToken)
	if err != nil {
		h.logger.Error("Failed to create deployable", logging.Error(err))

		if strings.Contains(err.Error(), "already exists") {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
			return
		}

		if strings.Contains(err.Error(), "validation failed") || strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create deployable"})
		return
	}

	c.JSON(http.StatusCreated, deployable)
}

// GetDeployable retrieves a specific deployable
func (h *DeployableHandler) GetDeployable(c *gin.Context) {
	id := c.Param("id")
	projectID := c.Query("projectId")

	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "projectId query parameter is required"})
		return
	}

	includeChildren := c.Query("includeChildren") == "true"
	includeMetrics := c.Query("includeMetrics") == "true"

	deployable, err := h.service.GetDeployable(c.Request.Context(), id, projectID, includeChildren, includeMetrics)
	if err != nil {
		h.logger.Error("Failed to get deployable", logging.Error(err))

		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "Deployable not found"})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get deployable"})
		return
	}

	c.JSON(http.StatusOK, deployable)
}

// ListDeployables retrieves deployables with filtering
func (h *DeployableHandler) ListDeployables(c *gin.Context) {
	opts := &services.ListDeployablesOptions{
		ProjectID:       c.Query("projectId"),
		IncludeChildren: c.Query("includeChildren") == "true",
		IncludeMetrics:  c.Query("includeMetrics") == "true",
		Page:            1,
		PageSize:        50,
	}

	if opts.ProjectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "projectId query parameter is required"})
		return
	}

	// Parse optional filters
	if parentID := c.Query("parentId"); parentID != "" {
		opts.ParentID = &parentID
	}

	if deployableType := c.Query("type"); deployableType != "" {
		t := models.DeployableType(deployableType)
		opts.Type = &t
	}

	if isActive := c.Query("isActive"); isActive != "" {
		active := isActive == "true"
		opts.IsActive = &active
	}

	// Parse pagination
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			opts.Page = p
		}
	}

	if pageSize := c.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			opts.PageSize = ps
		}
	}

	deployables, total, err := h.service.ListDeployables(c.Request.Context(), opts)
	if err != nil {
		h.logger.Error("Failed to list deployables", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list deployables"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"deployables": deployables,
		"total":       total,
		"page":        opts.Page,
		"pageSize":    opts.PageSize,
	})
}

// UpdateDeployable updates an existing deployable
func (h *DeployableHandler) UpdateDeployable(c *gin.Context) {
	id := c.Param("id")
	projectID := c.Query("projectId")

	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "projectId query parameter is required"})
		return
	}

	var req services.UpdateDeployableRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", logging.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	userID := h.getUserID(c)

	deployable, err := h.service.UpdateDeployable(c.Request.Context(), id, projectID, &req, userID)
	if err != nil {
		h.logger.Error("Failed to update deployable", logging.Error(err))

		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "Deployable not found"})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update deployable"})
		return
	}

	c.JSON(http.StatusOK, deployable)
}

// DeleteDeployable deletes a deployable
func (h *DeployableHandler) DeleteDeployable(c *gin.Context) {
	id := c.Param("id")
	projectID := c.Query("projectId")

	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "projectId query parameter is required"})
		return
	}

	userID := h.getUserID(c)

	err := h.service.DeleteDeployable(c.Request.Context(), id, projectID, userID)
	if err != nil {
		h.logger.Error("Failed to delete deployable", logging.Error(err))

		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "Deployable not found"})
			return
		}

		if strings.Contains(err.Error(), "with children") {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete deployable with children"})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete deployable"})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// DeployDeployable creates a new deployment
func (h *DeployableHandler) DeployDeployable(c *gin.Context) {
	var req services.DeployRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", logging.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	userID := h.getUserID(c)

	deployment, err := h.service.DeployDeployable(c.Request.Context(), &req, userID)
	if err != nil {
		h.logger.Error("Failed to deploy deployable", logging.Error(err))

		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "Deployable not found"})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to deploy deployable"})
		return
	}

	c.JSON(http.StatusCreated, deployment)
}

// GetDeployments retrieves deployments for a deployable
func (h *DeployableHandler) GetDeployments(c *gin.Context) {
	deployableID := c.Param("id")
	projectID := c.Query("projectId")

	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "projectId query parameter is required"})
		return
	}

	var environmentID *string
	if envID := c.Query("environmentId"); envID != "" {
		environmentID = &envID
	}

	limit := 50 // default limit
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	deployments, err := h.service.GetDeployments(c.Request.Context(), deployableID, projectID, environmentID, limit)
	if err != nil {
		h.logger.Error("Failed to get deployments", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get deployments"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"deployments": deployments,
	})
}

// GetEnvironmentStatus retrieves environment status for deployables
func (h *DeployableHandler) GetEnvironmentStatus(c *gin.Context) {
	projectID := c.Query("projectId")

	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "projectId query parameter is required"})
		return
	}

	var deployableID *string
	if id := c.Query("deployableId"); id != "" {
		deployableID = &id
	}

	var environmentID *string
	if id := c.Query("environmentId"); id != "" {
		environmentID = &id
	}

	statuses, err := h.service.GetEnvironmentStatus(c.Request.Context(), projectID, deployableID, environmentID)
	if err != nil {
		h.logger.Error("Failed to get environment status", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get environment status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"statuses": statuses,
	})
}

// GetMetrics retrieves metrics for a deployable
func (h *DeployableHandler) GetMetrics(c *gin.Context) {
	deployableID := c.Param("id")
	projectID := c.Query("projectId")

	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "projectId query parameter is required"})
		return
	}

	var environmentID *string
	if envID := c.Query("environmentId"); envID != "" {
		environmentID = &envID
	}

	var metricType *string
	if mt := c.Query("metricType"); mt != "" {
		metricType = &mt
	}

	// Parse time range
	var from, to time.Time
	if fromStr := c.Query("from"); fromStr != "" {
		if f, err := time.Parse(time.RFC3339, fromStr); err == nil {
			from = f
		}
	}
	if toStr := c.Query("to"); toStr != "" {
		if t, err := time.Parse(time.RFC3339, toStr); err == nil {
			to = t
		}
	}

	// Default to last 24 hours if no time range specified
	if from.IsZero() && to.IsZero() {
		to = time.Now()
		from = to.Add(-24 * time.Hour)
	}

	metrics, err := h.service.GetMetrics(c.Request.Context(), deployableID, projectID, environmentID, metricType, from, to)
	if err != nil {
		h.logger.Error("Failed to get metrics", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get metrics"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"metrics": metrics,
		"from":    from,
		"to":      to,
	})
}

// GetHierarchy retrieves the hierarchical structure of deployables
func (h *DeployableHandler) GetHierarchy(c *gin.Context) {
	projectID := c.Query("projectId")

	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "projectId query parameter is required"})
		return
	}

	// Get root deployables (no parent)
	opts := &services.ListDeployablesOptions{
		ProjectID:       projectID,
		ParentID:        new(string), // empty string pointer for null parent_id
		IncludeChildren: true,
		IncludeMetrics:  false,
		Page:            0, // Get all
		PageSize:        0,
	}

	deployables, _, err := h.service.ListDeployables(c.Request.Context(), opts)
	if err != nil {
		h.logger.Error("Failed to get deployable hierarchy", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get deployable hierarchy"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"hierarchy": deployables,
	})
}

// PromoteDeployment promotes a deployment from one environment to another
func (h *DeployableHandler) PromoteDeployment(c *gin.Context) {
	var req services.PromoteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", logging.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	userID := h.getUserID(c)

	// Get current deployment in source environment
	deployments, err := h.service.GetDeployments(c.Request.Context(), req.DeployableID, "", &req.FromEnvironmentID, 1)
	if err != nil || len(deployments) == 0 {
		h.logger.Error("Failed to get source deployment", logging.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "No deployment found in source environment"})
		return
	}

	sourceDeployment := deployments[0]

	// Create promotion deployment
	deployReq := &services.DeployRequest{
		DeployableID:         req.DeployableID,
		EnvironmentID:        req.ToEnvironmentID,
		Version:              sourceDeployment.Version,
		Strategy:             req.Strategy,
		Configuration:        sourceDeployment.Configuration,
		EnvironmentOverrides: sourceDeployment.EnvironmentOverrides,
		TriggerWorkflow:      req.TriggerWorkflow,
	}

	deployment, err := h.service.DeployDeployable(c.Request.Context(), deployReq, userID)
	if err != nil {
		h.logger.Error("Failed to promote deployment", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to promote deployment"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"deployment":   deployment,
		"promotedFrom": sourceDeployment,
	})
}

// RollbackDeployment rolls back a deployment to a previous version
func (h *DeployableHandler) RollbackDeployment(c *gin.Context) {
	var req services.RollbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", logging.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	userID := h.getUserID(c)

	// Get current deployment to rollback from
	deployments, err := h.service.GetDeployments(c.Request.Context(), req.DeployableID, "", &req.EnvironmentID, 1)
	if err != nil || len(deployments) == 0 {
		h.logger.Error("Failed to get current deployment", logging.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": "No current deployment found"})
		return
	}

	currentDeployment := deployments[0]

	// Create rollback deployment
	deployReq := &services.DeployRequest{
		DeployableID:         req.DeployableID,
		EnvironmentID:        req.EnvironmentID,
		Version:              req.ToVersion,
		Strategy:             models.DeploymentStrategyRollingUpdate, // Use rolling for rollback
		Configuration:        currentDeployment.Configuration,
		EnvironmentOverrides: currentDeployment.EnvironmentOverrides,
		TriggerWorkflow:      req.TriggerWorkflow,
	}

	deployment, err := h.service.DeployDeployable(c.Request.Context(), deployReq, userID)
	if err != nil {
		h.logger.Error("Failed to rollback deployment", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to rollback deployment"})
		return
	}

	// Mark as rollback
	rollbackTo := currentDeployment.Version
	deployment.RollbackTo = &rollbackTo

	c.JSON(http.StatusCreated, gin.H{
		"deployment":     deployment,
		"rolledBackFrom": currentDeployment,
	})
}

// ScaleDeployment scales a deployment
func (h *DeployableHandler) ScaleDeployment(c *gin.Context) {
	var req services.ScaleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", logging.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// TODO: Implement scaling logic
	// This would typically interact with the deployment platform (K8s, Docker, etc.)

	h.logger.Info("Scaling deployment",
		logging.String("deployableId", req.DeployableID),
		logging.String("environmentId", req.EnvironmentID),
		logging.Int("replicas", req.Replicas),
	)

	c.JSON(http.StatusOK, gin.H{
		"message":       "Scaling initiated",
		"deployableId":  req.DeployableID,
		"environmentId": req.EnvironmentID,
		"replicas":      req.Replicas,
	})
}
