package handlers

import (
	"net/http"

	"github.com/claudio/deploy-orchestrator/application-service/internal/models"
	"github.com/claudio/deploy-orchestrator/application-service/internal/services"
	"github.com/claudio/deploy-orchestrator/shared/logging"
	"github.com/gin-gonic/gin"
)

// DeploymentHandler handles deployment-related HTTP requests
type DeploymentHandler struct {
	deploymentManager *services.DeploymentManager
	logger            logging.Logger
}

// NewDeploymentHandler creates a new deployment handler
func NewDeploymentHandler(deploymentManager *services.DeploymentManager, logger logging.Logger) *DeploymentHandler {
	return &DeploymentHandler{
		deploymentManager: deploymentManager,
		logger:            logger,
	}
}

// CreateDeployment handles POST /deployments
func (h *DeploymentHandler) CreateDeployment(c *gin.Context) {
	var req services.DeploymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Get user from context
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Set deployed by
	req.DeployedBy = userID.(string)

	// Create deployment
	response, err := h.deploymentManager.CreateDeployment(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to create deployment", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create deployment", "details": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, response)
}

// GetEnvironmentVersions handles GET /deployments/environment-versions
func (h *DeploymentHandler) GetEnvironmentVersions(c *gin.Context) {
	projectID := c.Query("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project ID is required"})
		return
	}

	// Get environment IDs from query
	environmentIDs := c.QueryArray("environmentId")

	// Get environment versions
	versions, err := h.deploymentManager.GetEnvironmentVersions(c.Request.Context(), projectID, environmentIDs)
	if err != nil {
		h.logger.Error("Failed to get environment versions", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get environment versions", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"versions": versions,
		"total":    len(versions),
	})
}

// GetVersionMatrix handles GET /deployments/version-matrix
func (h *DeploymentHandler) GetVersionMatrix(c *gin.Context) {
	projectID := c.Query("projectId")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project ID is required"})
		return
	}

	// Get version matrix
	matrix, err := h.deploymentManager.GetVersionMatrix(c.Request.Context(), projectID)
	if err != nil {
		h.logger.Error("Failed to get version matrix", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get version matrix", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"matrix": matrix,
	})
}

// UpdateDeploymentStatus handles PUT /deployments/:id/status
func (h *DeploymentHandler) UpdateDeploymentStatus(c *gin.Context) {
	deploymentID := c.Param("id")
	if deploymentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Deployment ID is required"})
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Convert status string to enum
	var status models.DeploymentStatus
	switch req.Status {
	case "pending":
		status = models.DeploymentStatusPending
	case "in_progress":
		status = models.DeploymentStatusInProgress
	case "succeeded":
		status = models.DeploymentStatusSucceeded
	case "failed":
		status = models.DeploymentStatusFailed
	case "rolled_back":
		status = models.DeploymentStatusRolledBack
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid status"})
		return
	}

	// Update deployment status
	err := h.deploymentManager.UpdateDeploymentStatus(c.Request.Context(), deploymentID, status)
	if err != nil {
		h.logger.Error("Failed to update deployment status", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update deployment status", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Deployment status updated successfully",
		"status":  req.Status,
	})
}

// PromoteVersion handles POST /deployments/promote
func (h *DeploymentHandler) PromoteVersion(c *gin.Context) {
	var req struct {
		ApplicationID       *string                `json:"applicationId,omitempty"`
		ComponentID         *string                `json:"componentId,omitempty"`
		ProjectID           string                 `json:"projectId" binding:"required"`
		SourceEnvironmentID string                 `json:"sourceEnvironmentId" binding:"required"`
		TargetEnvironmentID string                 `json:"targetEnvironmentId" binding:"required"`
		Version             string                 `json:"version" binding:"required"`
		WorkflowID          string                 `json:"workflowId" binding:"required"`
		ProviderType        string                 `json:"providerType" binding:"required"`
		Parameters          map[string]interface{} `json:"parameters"`
		SecretMappings      map[string]string      `json:"secretMappings"`
		Strategy            string                 `json:"strategy,omitempty"`
		Configuration       map[string]interface{} `json:"configuration,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Get user from context
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Create deployment request for promotion
	deployReq := &services.DeploymentRequest{
		ProjectID:      req.ProjectID,
		EnvironmentID:  req.TargetEnvironmentID,
		Version:        req.Version,
		WorkflowID:     req.WorkflowID,
		ProviderType:   req.ProviderType,
		Parameters:     req.Parameters,
		SecretMappings: req.SecretMappings,
		DeployedBy:     userID.(string),
		Strategy:       req.Strategy,
		Configuration:  req.Configuration,
	}

	if req.ApplicationID != nil {
		deployReq.ApplicationIDs = []string{*req.ApplicationID}
	}
	if req.ComponentID != nil {
		deployReq.ComponentIDs = []string{*req.ComponentID}
	}

	// Create deployment
	response, err := h.deploymentManager.CreateDeployment(c.Request.Context(), deployReq)
	if err != nil {
		h.logger.Error("Failed to create promotion deployment", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create promotion deployment", "details": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":    "Version promoted successfully",
		"deployment": response,
	})
}
