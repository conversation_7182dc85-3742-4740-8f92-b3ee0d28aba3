package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/claudio/deploy-orchestrator/application-service/internal/models"
	"github.com/claudio/deploy-orchestrator/application-service/internal/services"

	"github.com/gorilla/mux"
)

// DeploymentHandlers handles deployment-related HTTP requests
type DeploymentHandlers struct {
	deploymentManager   *services.DeploymentManager
	workflowExecutor    *services.WorkflowExecutor
	promotionManager    *services.PromotionManager
	rollbackManager     *services.RollbackManager
	historyService      *services.DeploymentHistoryService
	notificationService *services.NotificationService
	pluginManager       *services.PluginManager
}

// NewDeploymentHandlers creates new deployment handlers
func NewDeploymentHandlers(
	deploymentManager *services.DeploymentManager,
	workflowExecutor *services.WorkflowExecutor,
	promotionManager *services.PromotionManager,
	rollbackManager *services.RollbackManager,
	historyService *services.DeploymentHistoryService,
	notificationService *services.NotificationService,
	pluginManager *services.PluginManager,
) *DeploymentHandlers {
	return &DeploymentHandlers{
		deploymentManager:   deploymentManager,
		workflowExecutor:    workflowExecutor,
		promotionManager:    promotionManager,
		rollbackManager:     rollbackManager,
		historyService:      historyService,
		notificationService: notificationService,
		pluginManager:       pluginManager,
	}
}

// CreateDeployment handles POST /deployments
func (h *DeploymentHandlers) CreateDeployment(w http.ResponseWriter, r *http.Request) {
	var req services.BulkDeploymentRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	deployment, err := h.deploymentManager.CreateDeployment(r.Context(), &req)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Send deployment started notification
	go h.sendDeploymentNotification(r.Context(), "deployment_started", deployment)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(deployment)
}

// GetEnvironmentVersions handles GET /deployments/environment-versions
func (h *DeploymentHandlers) GetEnvironmentVersions(w http.ResponseWriter, r *http.Request) {
	projectID := r.URL.Query().Get("projectId")
	if projectID == "" {
		http.Error(w, "projectId is required", http.StatusBadRequest)
		return
	}

	environmentIDs := r.URL.Query()["environmentId"]

	versions, err := h.deploymentManager.GetEnvironmentVersions(r.Context(), projectID, environmentIDs)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	response := map[string]interface{}{
		"versions": versions,
		"total":    len(versions),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// GetVersionMatrix handles GET /deployments/version-matrix
func (h *DeploymentHandlers) GetVersionMatrix(w http.ResponseWriter, r *http.Request) {
	projectID := r.URL.Query().Get("projectId")
	if projectID == "" {
		http.Error(w, "projectId is required", http.StatusBadRequest)
		return
	}

	matrix, err := h.deploymentManager.GetVersionMatrix(r.Context(), projectID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	response := map[string]interface{}{
		"matrix": matrix,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// UpdateDeploymentStatus handles PUT /deployments/{id}/status
func (h *DeploymentHandlers) UpdateDeploymentStatus(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	deploymentID := vars["id"]

	var req struct {
		Status string `json:"status"`
	}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if err := h.deploymentManager.UpdateDeploymentStatus(r.Context(), deploymentID, models.DeploymentStatus(req.Status)); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Update deployment history
	go h.historyService.UpdateDeploymentStatus(deploymentID, req.Status)

	// Send status notification
	go h.sendStatusNotification(r.Context(), deploymentID, req.Status)

	response := map[string]interface{}{
		"message": "Deployment status updated successfully",
		"status":  req.Status,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// CreatePromotion handles POST /deployments/promote
func (h *DeploymentHandlers) CreatePromotion(w http.ResponseWriter, r *http.Request) {
	var req services.PromotionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	promotion, err := h.promotionManager.CreatePromotion(r.Context(), req)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Send promotion notification
	go h.sendPromotionNotification(r.Context(), "promotion_created", promotion)

	response := map[string]interface{}{
		"message":   "Promotion created successfully",
		"promotion": promotion,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// ApprovePromotion handles POST /promotions/{id}/approve
func (h *DeploymentHandlers) ApprovePromotion(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	promotionID := vars["id"]

	var req services.PromotionApproval
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}
	req.PromotionID = promotionID

	if err := h.promotionManager.ApprovePromotion(r.Context(), req); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Send approval notification
	go h.sendPromotionApprovalNotification(r.Context(), req)

	response := map[string]interface{}{
		"message": "Promotion approval processed successfully",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// GetPromotions handles GET /promotions
func (h *DeploymentHandlers) GetPromotions(w http.ResponseWriter, r *http.Request) {
	filter := services.PromotionFilter{
		ProjectID: r.URL.Query().Get("projectId"),
		Status:    r.URL.Query()["status"],
	}

	if limit := r.URL.Query().Get("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			filter.Limit = l
		}
	}

	if offset := r.URL.Query().Get("offset"); offset != "" {
		if o, err := strconv.Atoi(offset); err == nil {
			filter.Offset = o
		}
	}

	promotions, total, err := h.promotionManager.GetPromotions(filter)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	response := map[string]interface{}{
		"promotions": promotions,
		"total":      total,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// CreateRollback handles POST /rollbacks
func (h *DeploymentHandlers) CreateRollback(w http.ResponseWriter, r *http.Request) {
	var req services.DeploymentRollbackRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	rollback, err := h.rollbackManager.CreateRollback(r.Context(), req)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Send rollback notification
	go h.sendRollbackNotification(r.Context(), "rollback_started", rollback)

	response := map[string]interface{}{
		"message":  "Rollback created successfully",
		"rollback": rollback,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// GetRollbackOptions handles GET /rollbacks/options
func (h *DeploymentHandlers) GetRollbackOptions(w http.ResponseWriter, r *http.Request) {
	projectID := r.URL.Query().Get("projectId")
	environmentID := r.URL.Query().Get("environmentId")

	if projectID == "" || environmentID == "" {
		http.Error(w, "projectId and environmentId are required", http.StatusBadRequest)
		return
	}

	var applicationID, componentID *string
	if appID := r.URL.Query().Get("applicationId"); appID != "" {
		applicationID = &appID
	}
	if compID := r.URL.Query().Get("componentId"); compID != "" {
		componentID = &compID
	}

	options, err := h.rollbackManager.GetRollbackOptions(projectID, environmentID, applicationID, componentID)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	response := map[string]interface{}{
		"options": options,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// GetDeploymentHistory handles GET /deployments/history
func (h *DeploymentHandlers) GetDeploymentHistory(w http.ResponseWriter, r *http.Request) {
	filter := services.DeploymentHistoryFilter{
		ProjectID:     r.URL.Query().Get("projectId"),
		EnvironmentID: r.URL.Query().Get("environmentId"),
		ApplicationID: r.URL.Query().Get("applicationId"),
		ComponentID:   r.URL.Query().Get("componentId"),
		Status:        r.URL.Query()["status"],
		EventType:     r.URL.Query()["eventType"],
		DeployedBy:    r.URL.Query().Get("deployedBy"),
	}

	if limit := r.URL.Query().Get("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			filter.Limit = l
		}
	}

	if offset := r.URL.Query().Get("offset"); offset != "" {
		if o, err := strconv.Atoi(offset); err == nil {
			filter.Offset = o
		}
	}

	// Parse date filters
	if fromDate := r.URL.Query().Get("fromDate"); fromDate != "" {
		if t, err := time.Parse(time.RFC3339, fromDate); err == nil {
			filter.FromDate = &t
		}
	}

	if toDate := r.URL.Query().Get("toDate"); toDate != "" {
		if t, err := time.Parse(time.RFC3339, toDate); err == nil {
			filter.ToDate = &t
		}
	}

	history, total, err := h.historyService.GetDeploymentHistory(filter)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	response := map[string]interface{}{
		"history": history,
		"total":   total,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// GetDeploymentStats handles GET /deployments/stats
func (h *DeploymentHandlers) GetDeploymentStats(w http.ResponseWriter, r *http.Request) {
	projectID := r.URL.Query().Get("projectId")
	if projectID == "" {
		http.Error(w, "projectId is required", http.StatusBadRequest)
		return
	}

	environmentID := r.URL.Query().Get("environmentId")

	var fromDate, toDate *time.Time
	if from := r.URL.Query().Get("fromDate"); from != "" {
		if t, err := time.Parse(time.RFC3339, from); err == nil {
			fromDate = &t
		}
	}

	if to := r.URL.Query().Get("toDate"); to != "" {
		if t, err := time.Parse(time.RFC3339, to); err == nil {
			toDate = &t
		}
	}

	stats, err := h.historyService.GetDeploymentStats(projectID, environmentID, fromDate, toDate)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}

// GetPlugins handles GET /plugins
func (h *DeploymentHandlers) GetPlugins(w http.ResponseWriter, r *http.Request) {
	if h.pluginManager == nil {
		http.Error(w, "Plugin manager not available", http.StatusServiceUnavailable)
		return
	}

	plugins := h.pluginManager.ListPlugins()

	response := map[string]interface{}{
		"plugins": plugins,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// Helper methods for notifications
func (h *DeploymentHandlers) sendDeploymentNotification(ctx context.Context, eventType string, deployment *services.DeploymentResponse) {
	if h.notificationService == nil {
		return
	}

	event := services.NotificationEvent{
		ProjectID:  "", // Will need to be passed separately
		EventType:  eventType,
		EntityType: "deployment",
		EntityID:   deployment.DeploymentID,
		Title:      fmt.Sprintf("Deployment %s", eventType),
		Message:    fmt.Sprintf("Deployment %s has %s", deployment.DeploymentID, eventType),
		Severity:   "info",
		Metadata: map[string]interface{}{
			"deployment_id":      deployment.DeploymentID,
			"workflow_execution": deployment.WorkflowExecution,
			"status":             deployment.Status,
		},
	}

	h.notificationService.SendDeploymentNotification(ctx, event)
}

func (h *DeploymentHandlers) sendStatusNotification(ctx context.Context, deploymentID, status string) {
	// Implementation for status notifications
}

func (h *DeploymentHandlers) sendPromotionNotification(ctx context.Context, eventType string, promotion *services.Promotion) {
	// Implementation for promotion notifications
}

func (h *DeploymentHandlers) sendPromotionApprovalNotification(ctx context.Context, approval services.PromotionApproval) {
	// Implementation for promotion approval notifications
}

func (h *DeploymentHandlers) sendRollbackNotification(ctx context.Context, eventType string, rollback *services.Rollback) {
	// Implementation for rollback notifications
}
