package handlers

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/claudio/deploy-orchestrator/application-service/internal/models"
	"github.com/claudio/deploy-orchestrator/application-service/internal/services"
	"github.com/claudio/deploy-orchestrator/shared/logging"
	"github.com/gin-gonic/gin"
)

// ApplicationHandler handles HTTP requests for applications
type ApplicationHandler struct {
	service *services.ApplicationService
	logger  logging.Logger
}

// NewApplicationHandler creates a new application handler
func NewApplicationHandler(service *services.ApplicationService, logger logging.Logger) *ApplicationHandler {
	return &ApplicationHandler{
		service: service,
		logger:  logger,
	}
}

// getAuthTokenFromHeader extracts the JWT token from the Authorization header
func (h *ApplicationHandler) getAuthTokenFromHeader(c *gin.Context) string {
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		parts := strings.Split(authHeader, " ")
		if len(parts) == 2 && strings.ToLower(parts[0]) == "bearer" {
			return parts[1]
		}
	}
	return ""
}

// CreateApplicationGroup creates a new application group
func (h *ApplicationHandler) CreateApplicationGroup(c *gin.Context) {
	var req services.CreateApplicationGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", logging.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	token := h.getAuthTokenFromHeader(c)

	group, err := h.service.CreateApplicationGroup(c.Request.Context(), &req, userID.(string), token)
	if err != nil {
		h.logger.Error("Failed to create application group", logging.Error(err))
		if err.Error() == "permission denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, group)
}

// GetApplicationGroups retrieves application groups
func (h *ApplicationHandler) GetApplicationGroups(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	token := h.getAuthTokenFromHeader(c)

	// Check for optional projectId query parameter
	var projectID *string
	if pID := c.Query("projectId"); pID != "" {
		projectID = &pID
	}

	groups, err := h.service.GetApplicationGroups(c.Request.Context(), userID.(string), token, projectID)
	if err != nil {
		h.logger.Error("Failed to retrieve application groups", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, groups)
}

// GetApplicationGroup retrieves a specific application group
func (h *ApplicationHandler) GetApplicationGroup(c *gin.Context) {
	groupID := c.Param("id")
	if groupID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Group ID is required"})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	token := h.getAuthTokenFromHeader(c)

	group, err := h.service.GetApplicationGroup(c.Request.Context(), groupID, userID.(string), token)
	if err != nil {
		h.logger.Error("Failed to retrieve application group", logging.Error(err))
		if err.Error() == "application group not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err.Error() == "permission denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, group)
}

// CreateApplication creates a new application
func (h *ApplicationHandler) CreateApplication(c *gin.Context) {
	var req services.CreateApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", logging.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	token := h.getAuthTokenFromHeader(c)

	application, err := h.service.CreateApplication(c.Request.Context(), &req, userID.(string), token)
	if err != nil {
		h.logger.Error("Failed to create application", logging.Error(err))
		if err.Error() == "permission denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, application)
}

// GetApplications retrieves applications
func (h *ApplicationHandler) GetApplications(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	token := h.getAuthTokenFromHeader(c)

	// Check for optional groupId query parameter
	var groupID *string
	if gID := c.Query("groupId"); gID != "" {
		groupID = &gID
	}

	applications, err := h.service.GetApplications(c.Request.Context(), userID.(string), token, groupID)
	if err != nil {
		h.logger.Error("Failed to retrieve applications", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, applications)
}

// GetApplication retrieves a specific application
func (h *ApplicationHandler) GetApplication(c *gin.Context) {
	applicationID := c.Param("id")
	if applicationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Application ID is required"})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	token := h.getAuthTokenFromHeader(c)

	application, err := h.service.GetApplication(c.Request.Context(), applicationID, userID.(string), token)
	if err != nil {
		h.logger.Error("Failed to retrieve application", logging.Error(err))
		if err.Error() == "application not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err.Error() == "permission denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, application)
}

// CreateComponent creates a new application component
func (h *ApplicationHandler) CreateComponent(c *gin.Context) {
	var req services.CreateComponentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", logging.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	token := h.getAuthTokenFromHeader(c)

	component, err := h.service.CreateComponent(c.Request.Context(), &req, userID.(string), token)
	if err != nil {
		h.logger.Error("Failed to create component", logging.Error(err))
		if err.Error() == "permission denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, component)
}

// DeployApplication deploys an application or component
func (h *ApplicationHandler) DeployApplication(c *gin.Context) {
	var req services.DeploymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", logging.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	token := h.getAuthTokenFromHeader(c)

	deployment, err := h.service.DeployApplication(c.Request.Context(), &req, userID.(string), token)
	if err != nil {
		h.logger.Error("Failed to deploy application", logging.Error(err))
		if err.Error() == "permission denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, deployment)
}

// GetDeployments retrieves deployments
func (h *ApplicationHandler) GetDeployments(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	token := h.getAuthTokenFromHeader(c)

	// Check for optional query parameters
	var applicationID, componentID *string
	if appID := c.Query("applicationId"); appID != "" {
		applicationID = &appID
	}
	if compID := c.Query("componentId"); compID != "" {
		componentID = &compID
	}

	deployments, err := h.service.GetDeployments(c.Request.Context(), userID.(string), token, applicationID, componentID)
	if err != nil {
		h.logger.Error("Failed to retrieve deployments", logging.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, deployments)
}

// UpdateDeploymentStatus updates deployment status
func (h *ApplicationHandler) UpdateDeploymentStatus(c *gin.Context) {
	deploymentID := c.Param("id")
	if deploymentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Deployment ID is required"})
		return
	}

	var req struct {
		Status       models.DeploymentStatus `json:"status" binding:"required"`
		HealthStatus models.HealthStatus     `json:"healthStatus"`
		Logs         string                  `json:"logs"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", logging.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	token := h.getAuthTokenFromHeader(c)

	err := h.service.UpdateDeploymentStatus(c.Request.Context(), deploymentID, req.Status, req.HealthStatus, req.Logs, userID.(string), token)
	if err != nil {
		h.logger.Error("Failed to update deployment status", logging.Error(err))
		if err.Error() == "deployment not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err.Error() == "permission denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Deployment status updated successfully"})
}

// GetApplicationMetrics retrieves metrics for an application
func (h *ApplicationHandler) GetApplicationMetrics(c *gin.Context) {
	applicationID := c.Param("id")
	if applicationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Application ID is required"})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	token := h.getAuthTokenFromHeader(c)

	// First validate access to the application
	_, err := h.service.GetApplication(c.Request.Context(), applicationID, userID.(string), token)
	if err != nil {
		h.logger.Error("Failed to validate application access", logging.Error(err))
		if err.Error() == "application not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err.Error() == "permission denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Parse query parameters for filtering
	metricType := c.Query("type")
	limitStr := c.DefaultQuery("limit", "100")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 1000 {
		limit = 100
	}

	// TODO: Implement metrics retrieval
	// For now, return mock data structure
	metrics := gin.H{
		"applicationId": applicationID,
		"metrics": []gin.H{
			{
				"type":      "cpu",
				"value":     75.5,
				"unit":      "percent",
				"timestamp": "2025-05-28T10:00:00Z",
			},
			{
				"type":      "memory",
				"value":     1024,
				"unit":      "MB",
				"timestamp": "2025-05-28T10:00:00Z",
			},
		},
		"limit":      limit,
		"metricType": metricType,
	}

	c.JSON(http.StatusOK, metrics)
}
