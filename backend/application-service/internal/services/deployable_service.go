package services

import (
	"context"
	"fmt"
	"time"

	"github.com/claudio/deploy-orchestrator/application-service/internal/models"
	"github.com/claudio/deploy-orchestrator/shared/logging"
	"gorm.io/gorm"
)

// DeployableService handles deployable-related operations
type DeployableService struct {
	db               *gorm.DB
	projectValidator *ProjectValidator
	logger           logging.Logger
}

// RepositoryInfo represents repository information from frontend
type RepositoryInfo struct {
	URL            string `json:"url"`
	Branch         string `json:"branch"`
	Provider       string `json:"provider"`
	AccessToken    string `json:"accessToken,omitempty"`
	DeployKey      string `json:"deployKey,omitempty"`
	BuildPath      string `json:"buildPath,omitempty"`
	DockerfilePath string `json:"dockerfilePath,omitempty"`
}

// CreateDeployableRequest represents a request to create a deployable
type CreateDeployableRequest struct {
	Name        string                `json:"name" binding:"required"`
	Description string                `json:"description"`
	Type        models.DeployableType `json:"type" binding:"required"`
	ProjectID   string                `json:"projectId" binding:"required"`
	ParentID    *string               `json:"parentId,omitempty"`

	// Repository information
	Repository *RepositoryInfo `json:"repository,omitempty"`
	Tags       []string        `json:"tags"`

	// Build configuration
	BuildCommand string `json:"buildCommand"`
	BuildPath    string `json:"buildPath"`
	Dockerfile   string `json:"dockerfile"`
	BuildArgs    string `json:"buildArgs"`

	// Runtime configuration
	StartCommand   string `json:"startCommand"`
	HealthEndpoint string `json:"healthEndpoint"`
	Port           int    `json:"port"`
	Image          string `json:"image"`
	ImageTag       string `json:"imageTag"`

	// Environment configuration
	EnvironmentConfig string   `json:"environmentConfig"`
	ConfigTemplate    string   `json:"configTemplate"`
	SecretNames       []string `json:"secretNames"`

	// Resource requirements
	CPURequest    string `json:"cpuRequest"`
	CPULimit      string `json:"cpuLimit"`
	MemoryRequest string `json:"memoryRequest"`
	MemoryLimit   string `json:"memoryLimit"`

	// Scaling configuration
	MinReplicas      int  `json:"minReplicas"`
	MaxReplicas      int  `json:"maxReplicas"`
	AutoscaleEnabled bool `json:"autoscaleEnabled"`

	// Workflow integration
	DeployWorkflowID   *string `json:"deployWorkflowId,omitempty"`
	PromoteWorkflowID  *string `json:"promoteWorkflowId,omitempty"`
	RollbackWorkflowID *string `json:"rollbackWorkflowId,omitempty"`

	// Plugin configuration
	DeploymentPlugin string `json:"deploymentPlugin"`
	MonitoringPlugin string `json:"monitoringPlugin"`
	SecurityPlugin   string `json:"securityPlugin"`

	// Metadata
	Labels      map[string]string `json:"labels"`
	Annotations map[string]string `json:"annotations"`
}

// NewDeployableService creates a new deployable service
func NewDeployableService(db *gorm.DB, projectValidator *ProjectValidator, logger logging.Logger) *DeployableService {
	return &DeployableService{
		db:               db,
		projectValidator: projectValidator,
		logger:           logger,
	}
}

// CreateDeployable creates a new deployable
func (s *DeployableService) CreateDeployable(ctx context.Context, req *CreateDeployableRequest, userID, authToken string) (*models.Deployable, error) {
	// Validate project exists and is active
	if err := s.projectValidator.ValidateProject(ctx, req.ProjectID, authToken); err != nil {
		return nil, fmt.Errorf("project validation failed: %w", err)
	}

	// Validate parent if specified
	if req.ParentID != nil {
		var parent models.Deployable
		if err := s.db.Where("id = ? AND project_id = ?", *req.ParentID, req.ProjectID).First(&parent).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, fmt.Errorf("parent deployable not found")
			}
			return nil, fmt.Errorf("failed to validate parent: %w", err)
		}
	}

	// Check if deployable name already exists in project
	var existing models.Deployable
	if err := s.db.Where("name = ? AND project_id = ?", req.Name, req.ProjectID).First(&existing).Error; err == nil {
		return nil, fmt.Errorf("deployable '%s' already exists in project", req.Name)
	}

	// Set defaults
	var repositoryURL, repositoryBranch, buildPath string
	if req.Repository != nil {
		repositoryURL = req.Repository.URL
		repositoryBranch = req.Repository.Branch
		if repositoryBranch == "" {
			repositoryBranch = "main"
		}
		buildPath = req.Repository.BuildPath
	} else {
		repositoryBranch = "main"
	}

	if req.MinReplicas == 0 {
		req.MinReplicas = 1
	}
	if req.MaxReplicas == 0 {
		req.MaxReplicas = 10
	}

	deployable := &models.Deployable{
		Name:               req.Name,
		Description:        req.Description,
		Type:               req.Type,
		ProjectID:          req.ProjectID,
		ParentID:           req.ParentID,
		IsActive:           true,
		Repository:         repositoryURL,
		Branch:             repositoryBranch,
		Tags:               req.Tags,
		BuildCommand:       req.BuildCommand,
		BuildPath:          buildPath,
		Dockerfile:         req.Dockerfile,
		BuildArgs:          req.BuildArgs,
		StartCommand:       req.StartCommand,
		HealthEndpoint:     req.HealthEndpoint,
		Port:               req.Port,
		Image:              req.Image,
		ImageTag:           req.ImageTag,
		EnvironmentConfig:  req.EnvironmentConfig,
		ConfigTemplate:     req.ConfigTemplate,
		SecretNames:        req.SecretNames,
		CPURequest:         req.CPURequest,
		CPULimit:           req.CPULimit,
		MemoryRequest:      req.MemoryRequest,
		MemoryLimit:        req.MemoryLimit,
		MinReplicas:        req.MinReplicas,
		MaxReplicas:        req.MaxReplicas,
		AutoscaleEnabled:   req.AutoscaleEnabled,
		DeployWorkflowID:   req.DeployWorkflowID,
		PromoteWorkflowID:  req.PromoteWorkflowID,
		RollbackWorkflowID: req.RollbackWorkflowID,
		DeploymentPlugin:   req.DeploymentPlugin,
		MonitoringPlugin:   req.MonitoringPlugin,
		SecurityPlugin:     req.SecurityPlugin,
		Labels:             req.Labels,
		Annotations:        req.Annotations,
	}

	if err := s.db.Create(deployable).Error; err != nil {
		return nil, fmt.Errorf("failed to create deployable: %w", err)
	}

	s.logger.Info("Deployable created",
		logging.String("deployableId", deployable.ID),
		logging.String("name", deployable.Name),
		logging.String("type", string(deployable.Type)),
		logging.String("projectId", deployable.ProjectID),
		logging.String("userId", userID),
	)

	return deployable, nil
}

// GetDeployable retrieves a deployable by ID
func (s *DeployableService) GetDeployable(ctx context.Context, id, projectID string, includeChildren, includeMetrics bool) (*models.Deployable, error) {
	var deployable models.Deployable
	query := s.db.Where("id = ? AND project_id = ?", id, projectID)

	if includeChildren {
		query = query.Preload("Children")
	}

	if includeMetrics {
		query = query.Preload("Metrics")
	}

	query = query.Preload("Parent").Preload("Versions")

	if err := query.First(&deployable).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("deployable not found")
		}
		return nil, fmt.Errorf("failed to get deployable: %w", err)
	}

	return &deployable, nil
}

// ListDeployablesOptions represents options for listing deployables
type ListDeployablesOptions struct {
	ProjectID       string                 `json:"projectId"`
	ParentID        *string                `json:"parentId,omitempty"`
	Type            *models.DeployableType `json:"type,omitempty"`
	IsActive        *bool                  `json:"isActive,omitempty"`
	IncludeChildren bool                   `json:"includeChildren"`
	IncludeMetrics  bool                   `json:"includeMetrics"`
	Page            int                    `json:"page"`
	PageSize        int                    `json:"pageSize"`
}

// ListDeployables retrieves deployables based on filters
func (s *DeployableService) ListDeployables(ctx context.Context, opts *ListDeployablesOptions) ([]models.Deployable, int64, error) {
	query := s.db.Model(&models.Deployable{})

	// Apply filters
	if opts.ProjectID != "" {
		query = query.Where("project_id = ?", opts.ProjectID)
	}

	if opts.ParentID != nil {
		if *opts.ParentID == "" {
			query = query.Where("parent_id IS NULL")
		} else {
			query = query.Where("parent_id = ?", *opts.ParentID)
		}
	}

	if opts.Type != nil {
		query = query.Where("type = ?", *opts.Type)
	}

	if opts.IsActive != nil {
		query = query.Where("is_active = ?", *opts.IsActive)
	}

	// Count total records
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count deployables: %w", err)
	}

	// Apply pagination
	if opts.Page > 0 && opts.PageSize > 0 {
		offset := (opts.Page - 1) * opts.PageSize
		query = query.Offset(offset).Limit(opts.PageSize)
	}

	// Load relationships
	query = query.Preload("Parent")
	if opts.IncludeChildren {
		query = query.Preload("Children")
	}
	if opts.IncludeMetrics {
		query = query.Preload("Metrics")
	}

	var deployables []models.Deployable
	if err := query.Find(&deployables).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list deployables: %w", err)
	}

	return deployables, total, nil
}

// DeployRequest represents a request to deploy a deployable
type DeployRequest struct {
	DeployableID         string                    `json:"deployableId" binding:"required"`
	EnvironmentID        string                    `json:"environmentId" binding:"required"`
	Version              string                    `json:"version" binding:"required"`
	Strategy             models.DeploymentStrategy `json:"strategy"`
	Configuration        string                    `json:"configuration"`
	EnvironmentOverrides string                    `json:"environmentOverrides"`
	TriggerWorkflow      bool                      `json:"triggerWorkflow"`
}

// DeployDeployable creates a new deployment for a deployable
func (s *DeployableService) DeployDeployable(ctx context.Context, req *DeployRequest, userID string) (*models.DeployableDeployment, error) {
	// Validate deployable exists
	var deployable models.Deployable
	if err := s.db.Where("id = ?", req.DeployableID).First(&deployable).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("deployable not found")
		}
		return nil, fmt.Errorf("failed to find deployable: %w", err)
	}

	// Set default strategy if not provided
	if req.Strategy == "" {
		req.Strategy = models.DeploymentStrategyRollingUpdate
	}

	deployment := &models.DeployableDeployment{
		DeployableID:         req.DeployableID,
		ProjectID:            deployable.ProjectID,
		EnvironmentID:        req.EnvironmentID,
		Version:              req.Version,
		Status:               models.DeploymentStatusPending,
		Strategy:             req.Strategy,
		Configuration:        req.Configuration,
		EnvironmentOverrides: req.EnvironmentOverrides,
		DeployedBy:           userID,
		HealthStatus:         models.HealthStatusUnknown,
		Replicas:             deployable.MinReplicas,
	}

	now := time.Now()
	deployment.DeployedAt = &now

	if err := s.db.Create(deployment).Error; err != nil {
		return nil, fmt.Errorf("failed to create deployment: %w", err)
	}

	s.logger.Info("Deployment created",
		logging.String("deploymentId", deployment.ID),
		logging.String("deployableId", req.DeployableID),
		logging.String("environmentId", req.EnvironmentID),
		logging.String("version", req.Version),
		logging.String("userId", userID),
	)

	return deployment, nil
}

// GetDeployments retrieves deployments for a deployable
func (s *DeployableService) GetDeployments(ctx context.Context, deployableID, projectID string, environmentID *string, limit int) ([]models.DeployableDeployment, error) {
	query := s.db.Where("deployable_id = ? AND project_id = ?", deployableID, projectID)

	if environmentID != nil {
		query = query.Where("environment_id = ?", *environmentID)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	query = query.Order("created_at DESC").Preload("Deployable")

	var deployments []models.DeployableDeployment
	if err := query.Find(&deployments).Error; err != nil {
		return nil, fmt.Errorf("failed to get deployments: %w", err)
	}

	return deployments, nil
}

// GetEnvironmentStatus retrieves the current status of deployables in environments
func (s *DeployableService) GetEnvironmentStatus(ctx context.Context, projectID string, deployableID, environmentID *string) ([]models.DeployableEnvironmentStatus, error) {
	query := s.db.Where("project_id = ?", projectID)

	if deployableID != nil {
		query = query.Where("deployable_id = ?", *deployableID)
	}

	if environmentID != nil {
		query = query.Where("environment_id = ?", *environmentID)
	}

	query = query.Preload("Deployable").Preload("LastDeployment")

	var statuses []models.DeployableEnvironmentStatus
	if err := query.Find(&statuses).Error; err != nil {
		return nil, fmt.Errorf("failed to get environment status: %w", err)
	}

	return statuses, nil
}

// GetMetrics retrieves metrics for deployables
func (s *DeployableService) GetMetrics(ctx context.Context, deployableID, projectID string, environmentID *string, metricType *string, from, to time.Time) ([]models.DeployableMetrics, error) {
	query := s.db.Where("deployable_id = ? AND project_id = ?", deployableID, projectID)

	if environmentID != nil {
		query = query.Where("environment_id = ?", *environmentID)
	}

	if metricType != nil {
		query = query.Where("metric_type = ?", *metricType)
	}

	if !from.IsZero() {
		query = query.Where("timestamp >= ?", from)
	}

	if !to.IsZero() {
		query = query.Where("timestamp <= ?", to)
	}

	query = query.Order("timestamp DESC")

	var metrics []models.DeployableMetrics
	if err := query.Find(&metrics).Error; err != nil {
		return nil, fmt.Errorf("failed to get metrics: %w", err)
	}

	return metrics, nil
}

// UpdateDeployableRequest represents a request to update a deployable
type UpdateDeployableRequest struct {
	Name        *string                `json:"name,omitempty"`
	Description *string                `json:"description,omitempty"`
	Type        *models.DeployableType `json:"type,omitempty"`

	// Repository information
	Repository *string  `json:"repository,omitempty"`
	Branch     *string  `json:"branch,omitempty"`
	Tags       []string `json:"tags,omitempty"`

	// Build configuration
	BuildCommand *string `json:"buildCommand,omitempty"`
	BuildPath    *string `json:"buildPath,omitempty"`
	Dockerfile   *string `json:"dockerfile,omitempty"`
	BuildArgs    *string `json:"buildArgs,omitempty"`

	// Runtime configuration
	StartCommand   *string `json:"startCommand,omitempty"`
	HealthEndpoint *string `json:"healthEndpoint,omitempty"`
	Port           *int    `json:"port,omitempty"`
	Image          *string `json:"image,omitempty"`
	ImageTag       *string `json:"imageTag,omitempty"`

	// Environment configuration
	EnvironmentConfig *string  `json:"environmentConfig,omitempty"`
	ConfigTemplate    *string  `json:"configTemplate,omitempty"`
	SecretNames       []string `json:"secretNames,omitempty"`

	// Resource requirements
	CPULimit      *string `json:"cpuLimit,omitempty"`
	MemoryLimit   *string `json:"memoryLimit,omitempty"`
	CPURequest    *string `json:"cpuRequest,omitempty"`
	MemoryRequest *string `json:"memoryRequest,omitempty"`
	Replicas      *int    `json:"replicas,omitempty"`

	// Plugin configuration
	PluginConfigs []string `json:"pluginConfigs,omitempty"`

	// Workflow integration
	WorkflowSteps []string `json:"workflowSteps,omitempty"`

	// Environment overrides
	EnvironmentOverrides []string `json:"environmentOverrides,omitempty"`

	IsActive *bool `json:"isActive,omitempty"`
}

// PromoteRequest represents a request to promote a deployable to another environment
type PromoteRequest struct {
	DeployableID      string                    `json:"deployableId" binding:"required"`
	FromEnvironmentID string                    `json:"fromEnvironmentId" binding:"required"`
	ToEnvironmentID   string                    `json:"toEnvironmentId" binding:"required"`
	Version           string                    `json:"version,omitempty"`
	Strategy          models.DeploymentStrategy `json:"strategy"`
	TriggerWorkflow   bool                      `json:"triggerWorkflow"`
}

// RollbackRequest represents a request to rollback a deployable
type RollbackRequest struct {
	DeployableID    string `json:"deployableId" binding:"required"`
	EnvironmentID   string `json:"environmentId" binding:"required"`
	Version         string `json:"version,omitempty"`
	ToVersion       string `json:"toVersion" binding:"required"`
	TriggerWorkflow bool   `json:"triggerWorkflow"`
}

// ScaleRequest represents a request to scale a deployable
type ScaleRequest struct {
	DeployableID  string `json:"deployableId" binding:"required"`
	EnvironmentID string `json:"environmentId" binding:"required"`
	Replicas      int    `json:"replicas" binding:"required,min=0"`
}

// UpdateDeployable updates an existing deployable entity
func (s *DeployableService) UpdateDeployable(ctx context.Context, id, projectID string, req *UpdateDeployableRequest, userID string) (*models.Deployable, error) {
	// Get the existing deployable
	deployable := &models.Deployable{}
	if err := s.db.First(deployable, "id = ? AND project_id = ?", id, projectID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("deployable with ID %s not found", id)
		}
		return nil, fmt.Errorf("failed to find deployable: %w", err)
	}

	// Update fields if provided in the request
	if req.Name != nil {
		deployable.Name = *req.Name
	}
	if req.Description != nil {
		deployable.Description = *req.Description
	}
	// Only update type if provided and different
	if req.Type != nil && *req.Type != deployable.Type {
		deployable.Type = *req.Type
	}

	// Repository information
	if req.Repository != nil {
		deployable.Repository = *req.Repository
	}
	if req.Branch != nil {
		deployable.Branch = *req.Branch
	}
	if len(req.Tags) > 0 {
		deployable.Tags = req.Tags
	}

	// Build configuration
	if req.BuildCommand != nil {
		deployable.BuildCommand = *req.BuildCommand
	}
	if req.BuildPath != nil {
		deployable.BuildPath = *req.BuildPath
	}
	if req.Dockerfile != nil {
		deployable.Dockerfile = *req.Dockerfile
	}
	if req.BuildArgs != nil {
		deployable.BuildArgs = *req.BuildArgs
	}

	// Runtime configuration
	if req.StartCommand != nil {
		deployable.StartCommand = *req.StartCommand
	}
	if req.HealthEndpoint != nil {
		deployable.HealthEndpoint = *req.HealthEndpoint
	}
	if req.Port != nil {
		deployable.Port = *req.Port
	}
	if req.Image != nil {
		deployable.Image = *req.Image
	}
	if req.ImageTag != nil {
		deployable.ImageTag = *req.ImageTag
	}

	// Environment configuration
	if req.EnvironmentConfig != nil {
		deployable.EnvironmentConfig = *req.EnvironmentConfig
	}
	if req.ConfigTemplate != nil {
		deployable.ConfigTemplate = *req.ConfigTemplate
	}
	if len(req.SecretNames) > 0 {
		deployable.SecretNames = req.SecretNames
	}

	// Resource requirements
	if req.CPULimit != nil {
		deployable.CPULimit = *req.CPULimit
	}
	if req.MemoryLimit != nil {
		deployable.MemoryLimit = *req.MemoryLimit
	}
	if req.CPURequest != nil {
		deployable.CPURequest = *req.CPURequest
	}
	if req.MemoryRequest != nil {
		deployable.MemoryRequest = *req.MemoryRequest
	}
	if req.Replicas != nil {
		deployable.MinReplicas = *req.Replicas
	}

	// Plugin configuration
	if len(req.PluginConfigs) > 0 {
		// Since there are no direct plugin fields in the UpdateDeployableRequest,
		// we would need to parse the plugin configs or handle them differently
		// For now, we'll just leave a placeholder for future implementation
	}

	// Workflow integration
	if len(req.WorkflowSteps) > 0 {
		// Handle workflow steps appropriately
		// Since there's no direct field in the model, this requires custom handling
	}

	// Environment overrides
	if len(req.EnvironmentOverrides) > 0 {
		// Handle environment overrides appropriately
		// Since there's no direct field in the model, this requires custom handling
	}

	// Active status
	if req.IsActive != nil {
		deployable.IsActive = *req.IsActive
	}

	// Update the deployable in the database
	if err := s.db.Save(deployable).Error; err != nil {
		return nil, fmt.Errorf("failed to update deployable: %w", err)
	}

	s.logger.Info("Deployable updated",
		logging.String("deployableId", deployable.ID),
		logging.String("name", deployable.Name),
		logging.String("projectId", deployable.ProjectID),
		logging.String("userId", userID),
	)

	return deployable, nil
}

// DeleteDeployable marks a deployable as inactive
func (s *DeployableService) DeleteDeployable(ctx context.Context, id, projectID, userID string) error {
	// Get the existing deployable
	deployable := &models.Deployable{}
	if err := s.db.First(deployable, "id = ? AND project_id = ?", id, projectID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("deployable with ID %s not found", id)
		}
		return fmt.Errorf("failed to find deployable: %w", err)
	}

	// Soft delete by marking as inactive
	deployable.IsActive = false
	if err := s.db.Save(deployable).Error; err != nil {
		return fmt.Errorf("failed to mark deployable as inactive: %w", err)
	}

	s.logger.Info("Deployable marked as inactive",
		logging.String("deployableId", deployable.ID),
		logging.String("name", deployable.Name),
		logging.String("projectId", deployable.ProjectID),
		logging.String("userId", userID),
	)

	return nil
}
