package services

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// MockWorkflowExecutor is a mock implementation of WorkflowExecutor
type MockWorkflowExecutor struct {
	mock.Mock
}

func (m *MockWorkflowExecutor) ExecuteWorkflow(ctx context.Context, req ExecutionRequest) (*ExecutionResult, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*ExecutionResult), args.Error(1)
}

// MockNotificationService is a mock implementation of NotificationService
type MockNotificationService struct {
	mock.Mock
}

func (m *MockNotificationService) SendDeploymentNotification(ctx context.Context, event NotificationEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func setupTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}

	// Migrate the schema
	db.AutoMigrate(&Deployment{}, &EnvironmentVersion{}, &ApplicationArtifact{}, &DeploymentConfiguration{})

	return db
}

func TestDeploymentManager_CreateDeployment(t *testing.T) {
	db := setupTestDB()
	mockWorkflowExecutor := new(MockWorkflowExecutor)
	mockNotificationService := new(MockNotificationService)

	manager := NewDeploymentManager(db, mockWorkflowExecutor, mockNotificationService)

	tests := []struct {
		name    string
		request CreateDeploymentRequest
		wantErr bool
	}{
		{
			name: "successful deployment creation",
			request: CreateDeploymentRequest{
				ProjectID:      "project1",
				ApplicationIDs: []string{"app1"},
				EnvironmentID:  "env1",
				Version:        "v1.0.0",
				WorkflowID:     "workflow1",
				ProviderType:   "helm",
				Parameters:     map[string]interface{}{"replicas": 3},
				SecretMappings: map[string]string{"DB_PASSWORD": "prod-db-secret"},
				Configuration:  map[string]interface{}{"strategy": "rolling"},
			},
			wantErr: false,
		},
		{
			name: "missing project ID",
			request: CreateDeploymentRequest{
				ApplicationIDs: []string{"app1"},
				EnvironmentID:  "env1",
				Version:        "v1.0.0",
				WorkflowID:     "workflow1",
				ProviderType:   "helm",
			},
			wantErr: true,
		},
		{
			name: "missing applications and components",
			request: CreateDeploymentRequest{
				ProjectID:     "project1",
				EnvironmentID: "env1",
				Version:       "v1.0.0",
				WorkflowID:    "workflow1",
				ProviderType:  "helm",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks for successful cases
			if !tt.wantErr {
				mockWorkflowExecutor.On("ExecuteWorkflow", mock.Anything, mock.Anything).Return(
					&ExecutionResult{
						ExecutionID: "execution1",
						Status:      "pending",
					}, nil)

				mockNotificationService.On("SendDeploymentNotification", mock.Anything, mock.Anything).Return(nil)
			}

			deployment, err := manager.CreateDeployment(context.Background(), tt.request)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, deployment)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, deployment)
				assert.Equal(t, tt.request.ProjectID, deployment.ProjectID)
				assert.Equal(t, tt.request.EnvironmentID, deployment.EnvironmentID)
				assert.Equal(t, tt.request.Version, deployment.Version)
				assert.Equal(t, "pending", deployment.Status)
			}

			// Reset mocks
			mockWorkflowExecutor.ExpectedCalls = nil
			mockNotificationService.ExpectedCalls = nil
		})
	}
}

func TestDeploymentManager_GetEnvironmentVersions(t *testing.T) {
	db := setupTestDB()
	manager := NewDeploymentManager(db, nil, nil)

	// Create test data
	testVersions := []EnvironmentVersion{
		{
			ID:            "version1",
			ProjectID:     "project1",
			EnvironmentID: "env1",
			ApplicationID: stringPtr("app1"),
			Version:       "v1.0.0",
			Status:        "active",
			DeploymentID:  "deployment1",
			DeployedAt:    time.Now(),
			DeployedBy:    "user1",
		},
		{
			ID:            "version2",
			ProjectID:     "project1",
			EnvironmentID: "env2",
			ApplicationID: stringPtr("app1"),
			Version:       "v0.9.0",
			Status:        "active",
			DeploymentID:  "deployment2",
			DeployedAt:    time.Now().Add(-24 * time.Hour),
			DeployedBy:    "user1",
		},
		{
			ID:            "version3",
			ProjectID:     "project2",
			EnvironmentID: "env1",
			ApplicationID: stringPtr("app2"),
			Version:       "v2.0.0",
			Status:        "active",
			DeploymentID:  "deployment3",
			DeployedAt:    time.Now(),
			DeployedBy:    "user2",
		},
	}

	for _, version := range testVersions {
		db.Create(&version)
	}

	tests := []struct {
		name           string
		projectID      string
		environmentIDs []string
		expectedCount  int
	}{
		{
			name:           "get all versions for project",
			projectID:      "project1",
			environmentIDs: nil,
			expectedCount:  2,
		},
		{
			name:           "get versions for specific environment",
			projectID:      "project1",
			environmentIDs: []string{"env1"},
			expectedCount:  1,
		},
		{
			name:           "get versions for multiple environments",
			projectID:      "project1",
			environmentIDs: []string{"env1", "env2"},
			expectedCount:  2,
		},
		{
			name:           "no versions for non-existent project",
			projectID:      "project3",
			environmentIDs: nil,
			expectedCount:  0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			versions, total, err := manager.GetEnvironmentVersions(tt.projectID, tt.environmentIDs)

			assert.NoError(t, err)
			assert.Equal(t, int64(tt.expectedCount), total)
			assert.Len(t, versions, tt.expectedCount)

			// Verify all returned versions belong to the correct project
			for _, version := range versions {
				assert.Equal(t, tt.projectID, version.ProjectID)
				if len(tt.environmentIDs) > 0 {
					assert.Contains(t, tt.environmentIDs, version.EnvironmentID)
				}
			}
		})
	}
}

func TestDeploymentManager_GetVersionMatrix(t *testing.T) {
	db := setupTestDB()
	manager := NewDeploymentManager(db, nil, nil)

	// Create test data with applications and components
	testVersions := []EnvironmentVersion{
		{
			ID:            "version1",
			ProjectID:     "project1",
			EnvironmentID: "env1",
			ApplicationID: stringPtr("app1"),
			Version:       "v1.0.0",
			Status:        "active",
			DeploymentID:  "deployment1",
			DeployedAt:    time.Now(),
			DeployedBy:    "user1",
		},
		{
			ID:            "version2",
			ProjectID:     "project1",
			EnvironmentID: "env2",
			ApplicationID: stringPtr("app1"),
			Version:       "v0.9.0",
			Status:        "active",
			DeploymentID:  "deployment2",
			DeployedAt:    time.Now(),
			DeployedBy:    "user1",
		},
		{
			ID:            "version3",
			ProjectID:     "project1",
			EnvironmentID: "env1",
			ComponentID:   stringPtr("comp1"),
			Version:       "v2.0.0",
			Status:        "active",
			DeploymentID:  "deployment3",
			DeployedAt:    time.Now(),
			DeployedBy:    "user1",
		},
	}

	for _, version := range testVersions {
		db.Create(&version)
	}

	matrix, err := manager.GetVersionMatrix("project1")

	assert.NoError(t, err)
	assert.NotNil(t, matrix)

	// Check that the matrix contains the expected entries
	assert.Contains(t, matrix, "app1")
	assert.Contains(t, matrix, "comp1")

	// Check version mappings
	assert.Equal(t, "v1.0.0", matrix["app1"]["env1"])
	assert.Equal(t, "v0.9.0", matrix["app1"]["env2"])
	assert.Equal(t, "v2.0.0", matrix["comp1"]["env1"])
}

func TestDeploymentManager_UpdateDeploymentStatus(t *testing.T) {
	db := setupTestDB()
	manager := NewDeploymentManager(db, nil, nil)

	// Create test deployment
	deployment := &Deployment{
		ID:            "deployment1",
		ProjectID:     "project1",
		EnvironmentID: "env1",
		Version:       "v1.0.0",
		Status:        "pending",
		WorkflowID:    "workflow1",
		ProviderType:  "helm",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
	db.Create(deployment)

	tests := []struct {
		name         string
		deploymentID string
		status       string
		wantErr      bool
	}{
		{
			name:         "successful status update",
			deploymentID: "deployment1",
			status:       "succeeded",
			wantErr:      false,
		},
		{
			name:         "update non-existent deployment",
			deploymentID: "deployment999",
			status:       "succeeded",
			wantErr:      true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := manager.UpdateDeploymentStatus(tt.deploymentID, tt.status)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)

				// Verify the status was updated
				var updatedDeployment Deployment
				db.First(&updatedDeployment, "id = ?", tt.deploymentID)
				assert.Equal(t, tt.status, updatedDeployment.Status)
			}
		})
	}
}

func TestDeploymentManager_validateDeploymentRequest(t *testing.T) {
	manager := &DeploymentManager{}

	tests := []struct {
		name    string
		request CreateDeploymentRequest
		wantErr bool
	}{
		{
			name: "valid request with applications",
			request: CreateDeploymentRequest{
				ProjectID:      "project1",
				ApplicationIDs: []string{"app1"},
				EnvironmentID:  "env1",
				Version:        "v1.0.0",
				WorkflowID:     "workflow1",
				ProviderType:   "helm",
			},
			wantErr: false,
		},
		{
			name: "valid request with components",
			request: CreateDeploymentRequest{
				ProjectID:     "project1",
				ComponentIDs:  []string{"comp1"},
				EnvironmentID: "env1",
				Version:       "v1.0.0",
				WorkflowID:    "workflow1",
				ProviderType:  "helm",
			},
			wantErr: false,
		},
		{
			name: "missing project ID",
			request: CreateDeploymentRequest{
				ApplicationIDs: []string{"app1"},
				EnvironmentID:  "env1",
				Version:        "v1.0.0",
				WorkflowID:     "workflow1",
				ProviderType:   "helm",
			},
			wantErr: true,
		},
		{
			name: "missing applications and components",
			request: CreateDeploymentRequest{
				ProjectID:     "project1",
				EnvironmentID: "env1",
				Version:       "v1.0.0",
				WorkflowID:    "workflow1",
				ProviderType:  "helm",
			},
			wantErr: true,
		},
		{
			name: "missing environment ID",
			request: CreateDeploymentRequest{
				ProjectID:      "project1",
				ApplicationIDs: []string{"app1"},
				Version:        "v1.0.0",
				WorkflowID:     "workflow1",
				ProviderType:   "helm",
			},
			wantErr: true,
		},
		{
			name: "missing version",
			request: CreateDeploymentRequest{
				ProjectID:      "project1",
				ApplicationIDs: []string{"app1"},
				EnvironmentID:  "env1",
				WorkflowID:     "workflow1",
				ProviderType:   "helm",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := manager.validateDeploymentRequest(tt.request)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Helper function to create string pointers
func stringPtr(s string) *string {
	return &s
}
