package services

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/logging"
)

// ProjectValidationError represents an error with HTTP status code
type ProjectValidationError struct {
	StatusCode int
	Message    string
	Cause      error
}

func (e *ProjectValidationError) Error() string {
	return e.Message
}

func (e *ProjectValidationError) Unwrap() error {
	return e.Cause
}

// NewProjectValidationError creates a new project validation error with status code
func NewProjectValidationError(statusCode int, message string, cause error) *ProjectValidationError {
	return &ProjectValidationError{
		StatusCode: statusCode,
		Message:    message,
		Cause:      cause,
	}
}

// ProjectValidator handles project business logic validation via admin-service API
// Authentication and access control are handled by the shared auth middleware
// This service only validates business rules like project existence and active status
type ProjectValidator struct {
	adminServiceURL  string
	httpClient       *http.Client
	logger           logging.Logger
	bypassValidation bool
}

// Project represents a project from admin-service
type Project struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	IsActive    bool   `json:"isActive"`
	CreatedAt   string `json:"createdAt"`
	UpdatedAt   string `json:"updatedAt"`
}

// NewProjectValidator creates a new project validator
func NewProjectValidator(adminServiceURL string, logger logging.Logger, bypassValidation bool) *ProjectValidator {
	return &ProjectValidator{
		adminServiceURL: adminServiceURL,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
		logger:           logger,
		bypassValidation: bypassValidation,
	}
}

// ValidateProject checks if a project exists and is active (business logic validation only)
// Authentication and access control are handled by the shared auth middleware
func (pv *ProjectValidator) ValidateProject(ctx context.Context, projectID string, authToken string) error {
	if projectID == "" {
		return fmt.Errorf("project ID cannot be empty")
	}

	// Skip validation if authentication is disabled
	if pv.bypassValidation {
		pv.logger.Info("Project validation bypassed (authentication disabled)",
			logging.String("projectId", projectID),
		)
		return nil
	}

	// Check if project exists and is active
	project, err := pv.getProject(ctx, projectID, authToken)
	if err != nil {
		return fmt.Errorf("failed to validate project: %w", err)
	}

	// Check if project is active (business rule)
	if !project.IsActive {
		return fmt.Errorf("project %s is not active", projectID)
	}

	pv.logger.Info("Project validation successful",
		logging.String("projectId", projectID),
		logging.String("projectName", project.Name),
		logging.Bool("isActive", project.IsActive),
	)

	return nil
}

// getProject retrieves project details from admin-service
func (pv *ProjectValidator) getProject(ctx context.Context, projectID, authToken string) (*Project, error) {
	url := fmt.Sprintf("%s/api/v1/admin-service/projects/%s", pv.adminServiceURL, projectID)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	if authToken != "" {
		req.Header.Set("Authorization", "Bearer "+authToken)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := pv.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request to admin-service: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusUnauthorized {
		return nil, NewProjectValidationError(http.StatusUnauthorized, "Authentication required", fmt.Errorf("admin-service returned 401"))
	}

	if resp.StatusCode == http.StatusForbidden {
		return nil, NewProjectValidationError(http.StatusForbidden, "Access denied to project "+projectID, fmt.Errorf("admin-service returned 403"))
	}

	if resp.StatusCode == http.StatusNotFound {
		return nil, NewProjectValidationError(http.StatusNotFound, "Project "+projectID+" not found", fmt.Errorf("admin-service returned 404"))
	}

	if resp.StatusCode != http.StatusOK {
		return nil, NewProjectValidationError(http.StatusInternalServerError, fmt.Sprintf("Admin service error (status %d)", resp.StatusCode), fmt.Errorf("admin-service returned status %d", resp.StatusCode))
	}

	var project Project
	if err := json.NewDecoder(resp.Body).Decode(&project); err != nil {
		return nil, fmt.Errorf("failed to decode project response: %w", err)
	}

	return &project, nil
}
