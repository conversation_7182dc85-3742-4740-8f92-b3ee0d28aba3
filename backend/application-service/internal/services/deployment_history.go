package services

import (
	"database/sql"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// DeploymentHistoryService manages deployment history and analytics
type DeploymentHistoryService struct {
	db *gorm.DB
}

// DeploymentHistoryEntry represents a deployment history entry
type DeploymentHistoryEntry struct {
	ID            string                 `json:"id" gorm:"primaryKey"`
	ProjectID     string                 `json:"projectId" gorm:"not null"`
	EnvironmentID string                 `json:"environmentId" gorm:"not null"`
	ApplicationID *string                `json:"applicationId"`
	ComponentID   *string                `json:"componentId"`
	DeploymentID  string                 `json:"deploymentId" gorm:"not null"`
	Version       string                 `json:"version" gorm:"not null"`
	Status        string                 `json:"status" gorm:"not null"`
	DeployedBy    string                 `json:"deployedBy" gorm:"not null"`
	DeployedAt    time.Time              `json:"deployedAt" gorm:"not null"`
	Duration      *int64                 `json:"duration"` // Duration in seconds
	WorkflowID    string                 `json:"workflowId"`
	ProviderType  string                 `json:"providerType"`
	EventType     string                 `json:"eventType"`    // deployment, rollback, promotion
	SourceEnvID   *string                `json:"sourceEnvId"`  // For promotions
	RollbackFrom  *string                `json:"rollbackFrom"` // For rollbacks
	Metadata      map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
	CreatedAt     time.Time              `json:"createdAt"`
}

// DeploymentStats represents deployment statistics
type DeploymentStats struct {
	TotalDeployments      int64   `json:"totalDeployments"`
	SuccessfulDeployments int64   `json:"successfulDeployments"`
	FailedDeployments     int64   `json:"failedDeployments"`
	SuccessRate           float64 `json:"successRate"`
	AverageDuration       float64 `json:"averageDuration"`
	DeploymentsToday      int64   `json:"deploymentsToday"`
	DeploymentsThisWeek   int64   `json:"deploymentsThisWeek"`
	DeploymentsThisMonth  int64   `json:"deploymentsThisMonth"`
}

// DeploymentTrend represents deployment trends over time
type DeploymentTrend struct {
	Date        string `json:"date"`
	Deployments int64  `json:"deployments"`
	Successes   int64  `json:"successes"`
	Failures    int64  `json:"failures"`
}

// EnvironmentStats represents statistics for a specific environment
type EnvironmentStats struct {
	EnvironmentID         string     `json:"environmentId"`
	EnvironmentName       string     `json:"environmentName"`
	TotalDeployments      int64      `json:"totalDeployments"`
	SuccessfulDeployments int64      `json:"successfulDeployments"`
	FailedDeployments     int64      `json:"failedDeployments"`
	SuccessRate           float64    `json:"successRate"`
	LastDeployment        *time.Time `json:"lastDeployment"`
	AverageDuration       float64    `json:"averageDuration"`
}

// DeploymentHistoryFilter represents filters for deployment history
type DeploymentHistoryFilter struct {
	ProjectID     string     `json:"projectId"`
	EnvironmentID string     `json:"environmentId"`
	ApplicationID string     `json:"applicationId"`
	ComponentID   string     `json:"componentId"`
	Status        []string   `json:"status"`
	EventType     []string   `json:"eventType"`
	DeployedBy    string     `json:"deployedBy"`
	FromDate      *time.Time `json:"fromDate"`
	ToDate        *time.Time `json:"toDate"`
	Limit         int        `json:"limit"`
	Offset        int        `json:"offset"`
}

// NewDeploymentHistoryService creates a new deployment history service
func NewDeploymentHistoryService(db *gorm.DB) *DeploymentHistoryService {
	return &DeploymentHistoryService{db: db}
}

// RecordDeployment records a deployment in history
func (dhs *DeploymentHistoryService) RecordDeployment(entry DeploymentHistoryEntry) error {
	entry.CreatedAt = time.Now()
	if err := dhs.db.Create(&entry).Error; err != nil {
		return fmt.Errorf("failed to record deployment history: %w", err)
	}
	return nil
}

// GetDeploymentHistory retrieves deployment history based on filter
func (dhs *DeploymentHistoryService) GetDeploymentHistory(filter DeploymentHistoryFilter) ([]DeploymentHistoryEntry, int64, error) {
	var entries []DeploymentHistoryEntry
	var total int64

	query := dhs.db.Model(&DeploymentHistoryEntry{})

	// Apply filters
	if filter.ProjectID != "" {
		query = query.Where("project_id = ?", filter.ProjectID)
	}
	if filter.EnvironmentID != "" {
		query = query.Where("environment_id = ?", filter.EnvironmentID)
	}
	if filter.ApplicationID != "" {
		query = query.Where("application_id = ?", filter.ApplicationID)
	}
	if filter.ComponentID != "" {
		query = query.Where("component_id = ?", filter.ComponentID)
	}
	if len(filter.Status) > 0 {
		query = query.Where("status IN ?", filter.Status)
	}
	if len(filter.EventType) > 0 {
		query = query.Where("event_type IN ?", filter.EventType)
	}
	if filter.DeployedBy != "" {
		query = query.Where("deployed_by = ?", filter.DeployedBy)
	}
	if filter.FromDate != nil {
		query = query.Where("deployed_at >= ?", *filter.FromDate)
	}
	if filter.ToDate != nil {
		query = query.Where("deployed_at <= ?", *filter.ToDate)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count deployment history: %w", err)
	}

	// Apply pagination
	if filter.Limit > 0 {
		query = query.Limit(filter.Limit)
	}
	if filter.Offset > 0 {
		query = query.Offset(filter.Offset)
	}

	// Get entries
	if err := query.Order("deployed_at DESC").Find(&entries).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get deployment history: %w", err)
	}

	return entries, total, nil
}

// GetDeploymentStats retrieves deployment statistics
func (dhs *DeploymentHistoryService) GetDeploymentStats(projectID string, environmentID string, fromDate, toDate *time.Time) (*DeploymentStats, error) {
	query := dhs.db.Model(&DeploymentHistoryEntry{}).Where("project_id = ?", projectID)

	if environmentID != "" {
		query = query.Where("environment_id = ?", environmentID)
	}
	if fromDate != nil {
		query = query.Where("deployed_at >= ?", *fromDate)
	}
	if toDate != nil {
		query = query.Where("deployed_at <= ?", *toDate)
	}

	var stats DeploymentStats

	// Total deployments
	if err := query.Count(&stats.TotalDeployments).Error; err != nil {
		return nil, fmt.Errorf("failed to count total deployments: %w", err)
	}

	// Successful deployments
	if err := query.Where("status = ?", "succeeded").Count(&stats.SuccessfulDeployments).Error; err != nil {
		return nil, fmt.Errorf("failed to count successful deployments: %w", err)
	}

	// Failed deployments
	if err := query.Where("status = ?", "failed").Count(&stats.FailedDeployments).Error; err != nil {
		return nil, fmt.Errorf("failed to count failed deployments: %w", err)
	}

	// Calculate success rate
	if stats.TotalDeployments > 0 {
		stats.SuccessRate = float64(stats.SuccessfulDeployments) / float64(stats.TotalDeployments) * 100
	}

	// Average duration
	var avgDuration sql.NullFloat64
	if err := query.Where("duration IS NOT NULL").Select("AVG(duration)").Scan(&avgDuration).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average duration: %w", err)
	}
	if avgDuration.Valid {
		stats.AverageDuration = avgDuration.Float64
	}

	// Deployments today
	today := time.Now().Truncate(24 * time.Hour)
	if err := query.Where("deployed_at >= ?", today).Count(&stats.DeploymentsToday).Error; err != nil {
		return nil, fmt.Errorf("failed to count deployments today: %w", err)
	}

	// Deployments this week
	weekStart := today.AddDate(0, 0, -int(today.Weekday()))
	if err := query.Where("deployed_at >= ?", weekStart).Count(&stats.DeploymentsThisWeek).Error; err != nil {
		return nil, fmt.Errorf("failed to count deployments this week: %w", err)
	}

	// Deployments this month
	monthStart := time.Date(today.Year(), today.Month(), 1, 0, 0, 0, 0, today.Location())
	if err := query.Where("deployed_at >= ?", monthStart).Count(&stats.DeploymentsThisMonth).Error; err != nil {
		return nil, fmt.Errorf("failed to count deployments this month: %w", err)
	}

	return &stats, nil
}

// GetDeploymentTrends retrieves deployment trends over time
func (dhs *DeploymentHistoryService) GetDeploymentTrends(projectID string, environmentID string, days int) ([]DeploymentTrend, error) {
	fromDate := time.Now().AddDate(0, 0, -days).Truncate(24 * time.Hour)

	query := `
		SELECT 
			DATE(deployed_at) as date,
			COUNT(*) as deployments,
			COUNT(CASE WHEN status = 'succeeded' THEN 1 END) as successes,
			COUNT(CASE WHEN status = 'failed' THEN 1 END) as failures
		FROM deployment_history_entries 
		WHERE project_id = ? AND deployed_at >= ?
	`

	args := []interface{}{projectID, fromDate}

	if environmentID != "" {
		query += " AND environment_id = ?"
		args = append(args, environmentID)
	}

	query += " GROUP BY DATE(deployed_at) ORDER BY date"

	var trends []DeploymentTrend
	if err := dhs.db.Raw(query, args...).Scan(&trends).Error; err != nil {
		return nil, fmt.Errorf("failed to get deployment trends: %w", err)
	}

	return trends, nil
}

// GetEnvironmentStats retrieves statistics for all environments
func (dhs *DeploymentHistoryService) GetEnvironmentStats(projectID string) ([]EnvironmentStats, error) {
	query := `
		SELECT 
			environment_id,
			COUNT(*) as total_deployments,
			COUNT(CASE WHEN status = 'succeeded' THEN 1 END) as successful_deployments,
			COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_deployments,
			MAX(deployed_at) as last_deployment,
			AVG(CASE WHEN duration IS NOT NULL THEN duration END) as average_duration
		FROM deployment_history_entries 
		WHERE project_id = ?
		GROUP BY environment_id
		ORDER BY environment_id
	`

	var stats []EnvironmentStats
	if err := dhs.db.Raw(query, projectID).Scan(&stats).Error; err != nil {
		return nil, fmt.Errorf("failed to get environment stats: %w", err)
	}

	// Calculate success rates
	for i := range stats {
		if stats[i].TotalDeployments > 0 {
			stats[i].SuccessRate = float64(stats[i].SuccessfulDeployments) / float64(stats[i].TotalDeployments) * 100
		}
	}

	return stats, nil
}

// GetFrequentlyDeployedApplications retrieves most frequently deployed applications
func (dhs *DeploymentHistoryService) GetFrequentlyDeployedApplications(projectID string, limit int) ([]struct {
	ApplicationID string `json:"applicationId"`
	Deployments   int64  `json:"deployments"`
}, error) {
	query := `
		SELECT 
			application_id,
			COUNT(*) as deployments
		FROM deployment_history_entries 
		WHERE project_id = ? AND application_id IS NOT NULL
		GROUP BY application_id
		ORDER BY deployments DESC
		LIMIT ?
	`

	var results []struct {
		ApplicationID string `json:"applicationId"`
		Deployments   int64  `json:"deployments"`
	}

	if err := dhs.db.Raw(query, projectID, limit).Scan(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get frequently deployed applications: %w", err)
	}

	return results, nil
}

// UpdateDeploymentDuration updates the duration of a deployment
func (dhs *DeploymentHistoryService) UpdateDeploymentDuration(deploymentID string, duration int64) error {
	if err := dhs.db.Model(&DeploymentHistoryEntry{}).
		Where("deployment_id = ?", deploymentID).
		Update("duration", duration).Error; err != nil {
		return fmt.Errorf("failed to update deployment duration: %w", err)
	}
	return nil
}

// UpdateDeploymentStatus updates the status of a deployment in history
func (dhs *DeploymentHistoryService) UpdateDeploymentStatus(deploymentID, status string) error {
	if err := dhs.db.Model(&DeploymentHistoryEntry{}).
		Where("deployment_id = ?", deploymentID).
		Update("status", status).Error; err != nil {
		return fmt.Errorf("failed to update deployment status: %w", err)
	}
	return nil
}
