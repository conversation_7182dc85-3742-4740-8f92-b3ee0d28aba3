package services

import (
	"context"
	"fmt"
	"log"
	"os/exec"
	"sync"
	"time"
)

// PluginManager manages deployment plugins
type PluginManager struct {
	plugins map[string]Plugin
	mutex   sync.RWMutex
}

// Plugin represents a deployment plugin
type Plugin interface {
	Name() string
	Version() string
	Execute(ctx context.Context, config map[string]interface{}) (map[string]interface{}, error)
	Validate(config map[string]interface{}) error
}

// PluginMetadata contains plugin information
type PluginMetadata struct {
	Name         string            `json:"name"`
	Version      string            `json:"version"`
	Description  string            `json:"description"`
	Type         string            `json:"type"`
	Capabilities []string          `json:"capabilities"`
	ConfigSchema map[string]interface{} `json:"configSchema"`
}

// NewPluginManager creates a new plugin manager
func NewPluginManager() *PluginManager {
	pm := &PluginManager{
		plugins: make(map[string]Plugin),
	}
	
	// Register built-in plugins
	pm.registerBuiltinPlugins()
	
	return pm
}

// registerBuiltinPlugins registers the built-in deployment plugins
func (pm *PluginManager) registerBuiltinPlugins() {
	pm.RegisterPlugin(&HelmPlugin{})
	pm.RegisterPlugin(&OpenShiftPlugin{})
	pm.RegisterPlugin(&KubernetesPlugin{})
	pm.RegisterPlugin(&DockerPlugin{})
}

// RegisterPlugin registers a new plugin
func (pm *PluginManager) RegisterPlugin(plugin Plugin) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	
	pm.plugins[plugin.Name()] = plugin
	log.Printf("Registered plugin: %s v%s", plugin.Name(), plugin.Version())
}

// GetPlugin retrieves a plugin by name
func (pm *PluginManager) GetPlugin(name string) (Plugin, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	
	plugin, exists := pm.plugins[name]
	if !exists {
		return nil, fmt.Errorf("plugin not found: %s", name)
	}
	
	return plugin, nil
}

// ListPlugins returns all registered plugins
func (pm *PluginManager) ListPlugins() []PluginMetadata {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	
	var plugins []PluginMetadata
	for _, plugin := range pm.plugins {
		metadata := PluginMetadata{
			Name:    plugin.Name(),
			Version: plugin.Version(),
			Type:    "deployment",
		}
		
		// Add plugin-specific metadata
		switch p := plugin.(type) {
		case *HelmPlugin:
			metadata.Description = "Helm chart deployment plugin"
			metadata.Capabilities = []string{"deploy", "upgrade", "rollback", "uninstall"}
		case *OpenShiftPlugin:
			metadata.Description = "OpenShift deployment plugin"
			metadata.Capabilities = []string{"deploy", "scale", "rollback"}
		case *KubernetesPlugin:
			metadata.Description = "Kubernetes deployment plugin"
			metadata.Capabilities = []string{"deploy", "scale", "rollback"}
		case *DockerPlugin:
			metadata.Description = "Docker container deployment plugin"
			metadata.Capabilities = []string{"deploy", "stop", "restart"}
		}
		
		plugins = append(plugins, metadata)
	}
	
	return plugins
}

// ExecutePlugin executes a plugin with the given configuration
func (pm *PluginManager) ExecutePlugin(ctx context.Context, name string, config map[string]interface{}) (map[string]interface{}, error) {
	plugin, err := pm.GetPlugin(name)
	if err != nil {
		return nil, err
	}
	
	// Validate configuration
	if err := plugin.Validate(config); err != nil {
		return nil, fmt.Errorf("plugin configuration validation failed: %w", err)
	}
	
	// Execute plugin
	return plugin.Execute(ctx, config)
}

// HelmPlugin implements Helm deployment functionality
type HelmPlugin struct{}

func (p *HelmPlugin) Name() string { return "helm" }
func (p *HelmPlugin) Version() string { return "1.0.0" }

func (p *HelmPlugin) Validate(config map[string]interface{}) error {
	required := []string{"chart", "release", "namespace"}
	for _, field := range required {
		if _, exists := config[field]; !exists {
			return fmt.Errorf("required field missing: %s", field)
		}
	}
	return nil
}

func (p *HelmPlugin) Execute(ctx context.Context, config map[string]interface{}) (map[string]interface{}, error) {
	chart := config["chart"].(string)
	release := config["release"].(string)
	namespace := config["namespace"].(string)
	
	log.Printf("Executing Helm deployment: chart=%s, release=%s, namespace=%s", chart, release, namespace)
	
	// Build helm command
	args := []string{"upgrade", "--install", release, chart, "--namespace", namespace, "--create-namespace"}
	
	// Add values if provided
	if values, exists := config["values"]; exists {
		if valuesMap, ok := values.(map[string]interface{}); ok {
			for key, value := range valuesMap {
				args = append(args, "--set", fmt.Sprintf("%s=%v", key, value))
			}
		}
	}
	
	// Execute helm command
	cmd := exec.CommandContext(ctx, "helm", args...)
	output, err := cmd.CombinedOutput()
	
	result := map[string]interface{}{
		"command": fmt.Sprintf("helm %v", args),
		"output":  string(output),
		"timestamp": time.Now(),
	}
	
	if err != nil {
		result["error"] = err.Error()
		return result, fmt.Errorf("helm command failed: %w", err)
	}
	
	result["status"] = "success"
	return result, nil
}

// OpenShiftPlugin implements OpenShift deployment functionality
type OpenShiftPlugin struct{}

func (p *OpenShiftPlugin) Name() string { return "openshift" }
func (p *OpenShiftPlugin) Version() string { return "1.0.0" }

func (p *OpenShiftPlugin) Validate(config map[string]interface{}) error {
	required := []string{"template", "project"}
	for _, field := range required {
		if _, exists := config[field]; !exists {
			return fmt.Errorf("required field missing: %s", field)
		}
	}
	return nil
}

func (p *OpenShiftPlugin) Execute(ctx context.Context, config map[string]interface{}) (map[string]interface{}, error) {
	template := config["template"].(string)
	project := config["project"].(string)
	
	log.Printf("Executing OpenShift deployment: template=%s, project=%s", template, project)
	
	// Build oc command
	args := []string{"process", "-f", template, "-n", project}
	
	// Add parameters if provided
	if params, exists := config["parameters"]; exists {
		if paramsMap, ok := params.(map[string]interface{}); ok {
			for key, value := range paramsMap {
				args = append(args, "-p", fmt.Sprintf("%s=%v", key, value))
			}
		}
	}
	
	args = append(args, "|", "oc", "apply", "-f", "-", "-n", project)
	
	// Execute oc command
	cmd := exec.CommandContext(ctx, "sh", "-c", fmt.Sprintf("oc %s", fmt.Sprintf("%v", args)))
	output, err := cmd.CombinedOutput()
	
	result := map[string]interface{}{
		"command": fmt.Sprintf("oc %v", args),
		"output":  string(output),
		"timestamp": time.Now(),
	}
	
	if err != nil {
		result["error"] = err.Error()
		return result, fmt.Errorf("oc command failed: %w", err)
	}
	
	result["status"] = "success"
	return result, nil
}

// KubernetesPlugin implements Kubernetes deployment functionality
type KubernetesPlugin struct{}

func (p *KubernetesPlugin) Name() string { return "kubernetes" }
func (p *KubernetesPlugin) Version() string { return "1.0.0" }

func (p *KubernetesPlugin) Validate(config map[string]interface{}) error {
	required := []string{"manifest", "namespace"}
	for _, field := range required {
		if _, exists := config[field]; !exists {
			return fmt.Errorf("required field missing: %s", field)
		}
	}
	return nil
}

func (p *KubernetesPlugin) Execute(ctx context.Context, config map[string]interface{}) (map[string]interface{}, error) {
	manifest := config["manifest"].(string)
	namespace := config["namespace"].(string)
	
	log.Printf("Executing Kubernetes deployment: manifest=%s, namespace=%s", manifest, namespace)
	
	// Build kubectl command
	args := []string{"apply", "-f", manifest, "-n", namespace}
	
	// Execute kubectl command
	cmd := exec.CommandContext(ctx, "kubectl", args...)
	output, err := cmd.CombinedOutput()
	
	result := map[string]interface{}{
		"command": fmt.Sprintf("kubectl %v", args),
		"output":  string(output),
		"timestamp": time.Now(),
	}
	
	if err != nil {
		result["error"] = err.Error()
		return result, fmt.Errorf("kubectl command failed: %w", err)
	}
	
	result["status"] = "success"
	return result, nil
}

// DockerPlugin implements Docker deployment functionality
type DockerPlugin struct{}

func (p *DockerPlugin) Name() string { return "docker" }
func (p *DockerPlugin) Version() string { return "1.0.0" }

func (p *DockerPlugin) Validate(config map[string]interface{}) error {
	required := []string{"image", "container"}
	for _, field := range required {
		if _, exists := config[field]; !exists {
			return fmt.Errorf("required field missing: %s", field)
		}
	}
	return nil
}

func (p *DockerPlugin) Execute(ctx context.Context, config map[string]interface{}) (map[string]interface{}, error) {
	image := config["image"].(string)
	container := config["container"].(string)
	
	log.Printf("Executing Docker deployment: image=%s, container=%s", image, container)
	
	// Build docker command
	args := []string{"run", "-d", "--name", container, image}
	
	// Add ports if provided
	if ports, exists := config["ports"]; exists {
		if portsSlice, ok := ports.([]interface{}); ok {
			for _, port := range portsSlice {
				args = append(args, "-p", fmt.Sprintf("%v", port))
			}
		}
	}
	
	// Add environment variables if provided
	if env, exists := config["environment"]; exists {
		if envMap, ok := env.(map[string]interface{}); ok {
			for key, value := range envMap {
				args = append(args, "-e", fmt.Sprintf("%s=%v", key, value))
			}
		}
	}
	
	// Execute docker command
	cmd := exec.CommandContext(ctx, "docker", args...)
	output, err := cmd.CombinedOutput()
	
	result := map[string]interface{}{
		"command": fmt.Sprintf("docker %v", args),
		"output":  string(output),
		"timestamp": time.Now(),
	}
	
	if err != nil {
		result["error"] = err.Error()
		return result, fmt.Errorf("docker command failed: %w", err)
	}
	
	result["status"] = "success"
	return result, nil
}
