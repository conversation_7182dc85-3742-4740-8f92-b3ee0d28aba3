package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"gorm.io/gorm"
)

// NotificationService handles deployment notifications
type NotificationService struct {
	db *gorm.DB
}

// NotificationChannel represents a notification channel
type NotificationChannel struct {
	ID           string                 `json:"id" gorm:"primaryKey"`
	ProjectID    string                 `json:"projectId" gorm:"not null"`
	Name         string                 `json:"name" gorm:"not null"`
	Type         string                 `json:"type" gorm:"not null"` // slack, email, webhook, teams
	Configuration map[string]interface{} `json:"configuration" gorm:"type:jsonb"`
	Enabled      bool                   `json:"enabled" gorm:"default:true"`
	Events       []string               `json:"events" gorm:"type:jsonb"` // deployment_started, deployment_completed, deployment_failed, etc.
	CreatedAt    time.Time              `json:"createdAt"`
	UpdatedAt    time.Time              `json:"updatedAt"`
}

// NotificationEvent represents a notification event
type NotificationEvent struct {
	ID          string                 `json:"id" gorm:"primaryKey"`
	ProjectID   string                 `json:"projectId" gorm:"not null"`
	EventType   string                 `json:"eventType" gorm:"not null"`
	EntityType  string                 `json:"entityType"` // deployment, promotion, rollback
	EntityID    string                 `json:"entityId"`
	Title       string                 `json:"title"`
	Message     string                 `json:"message"`
	Severity    string                 `json:"severity"` // info, warning, error, success
	Metadata    map[string]interface{} `json:"metadata" gorm:"type:jsonb"`
	SentAt      *time.Time             `json:"sentAt"`
	Status      string                 `json:"status"` // pending, sent, failed
	Channels    []string               `json:"channels" gorm:"type:jsonb"`
	CreatedAt   time.Time              `json:"createdAt"`
	UpdatedAt   time.Time              `json:"updatedAt"`
}

// NotificationTemplate represents a notification template
type NotificationTemplate struct {
	ID        string    `json:"id" gorm:"primaryKey"`
	ProjectID string    `json:"projectId" gorm:"not null"`
	EventType string    `json:"eventType" gorm:"not null"`
	Channel   string    `json:"channel" gorm:"not null"`
	Subject   string    `json:"subject"`
	Body      string    `json:"body"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

// SlackMessage represents a Slack message
type SlackMessage struct {
	Text        string            `json:"text"`
	Channel     string            `json:"channel,omitempty"`
	Username    string            `json:"username,omitempty"`
	IconEmoji   string            `json:"icon_emoji,omitempty"`
	Attachments []SlackAttachment `json:"attachments,omitempty"`
}

// SlackAttachment represents a Slack attachment
type SlackAttachment struct {
	Color     string       `json:"color,omitempty"`
	Title     string       `json:"title,omitempty"`
	Text      string       `json:"text,omitempty"`
	Fields    []SlackField `json:"fields,omitempty"`
	Timestamp int64        `json:"ts,omitempty"`
}

// SlackField represents a Slack field
type SlackField struct {
	Title string `json:"title"`
	Value string `json:"value"`
	Short bool   `json:"short"`
}

// NewNotificationService creates a new notification service
func NewNotificationService(db *gorm.DB) *NotificationService {
	return &NotificationService{db: db}
}

// SendDeploymentNotification sends a deployment notification
func (ns *NotificationService) SendDeploymentNotification(ctx context.Context, event NotificationEvent) error {
	// Create notification event record
	event.CreatedAt = time.Now()
	event.UpdatedAt = time.Now()
	event.Status = "pending"
	
	if err := ns.db.Create(&event).Error; err != nil {
		return fmt.Errorf("failed to create notification event: %w", err)
	}

	// Get notification channels for the project and event type
	channels, err := ns.getNotificationChannels(event.ProjectID, event.EventType)
	if err != nil {
		return fmt.Errorf("failed to get notification channels: %w", err)
	}

	// Send notifications to each channel
	var sentChannels []string
	for _, channel := range channels {
		if err := ns.sendToChannel(ctx, channel, event); err != nil {
			log.Printf("Failed to send notification to channel %s: %v", channel.Name, err)
			continue
		}
		sentChannels = append(sentChannels, channel.ID)
	}

	// Update event status
	event.Channels = sentChannels
	if len(sentChannels) > 0 {
		event.Status = "sent"
		now := time.Now()
		event.SentAt = &now
	} else {
		event.Status = "failed"
	}
	event.UpdatedAt = time.Now()
	
	if err := ns.db.Save(&event).Error; err != nil {
		log.Printf("Failed to update notification event: %v", err)
	}

	return nil
}

// getNotificationChannels retrieves notification channels for a project and event type
func (ns *NotificationService) getNotificationChannels(projectID, eventType string) ([]NotificationChannel, error) {
	var channels []NotificationChannel
	
	query := ns.db.Where("project_id = ? AND enabled = ?", projectID, true)
	
	// Use raw SQL to check if event type is in the events array
	query = query.Where("? = ANY(events)", eventType)
	
	if err := query.Find(&channels).Error; err != nil {
		return nil, err
	}
	
	return channels, nil
}

// sendToChannel sends a notification to a specific channel
func (ns *NotificationService) sendToChannel(ctx context.Context, channel NotificationChannel, event NotificationEvent) error {
	switch channel.Type {
	case "slack":
		return ns.sendSlackNotification(ctx, channel, event)
	case "webhook":
		return ns.sendWebhookNotification(ctx, channel, event)
	case "email":
		return ns.sendEmailNotification(ctx, channel, event)
	case "teams":
		return ns.sendTeamsNotification(ctx, channel, event)
	default:
		return fmt.Errorf("unsupported channel type: %s", channel.Type)
	}
}

// sendSlackNotification sends a notification to Slack
func (ns *NotificationService) sendSlackNotification(ctx context.Context, channel NotificationChannel, event NotificationEvent) error {
	webhookURL, ok := channel.Configuration["webhook_url"].(string)
	if !ok {
		return fmt.Errorf("slack webhook URL not configured")
	}

	// Build Slack message
	message := SlackMessage{
		Text:     event.Title,
		Username: "Deploy Orchestrator",
		IconEmoji: ":rocket:",
		Attachments: []SlackAttachment{
			{
				Color: ns.getSlackColor(event.Severity),
				Title: event.Title,
				Text:  event.Message,
				Fields: []SlackField{
					{Title: "Project", Value: event.ProjectID, Short: true},
					{Title: "Event Type", Value: event.EventType, Short: true},
					{Title: "Severity", Value: event.Severity, Short: true},
					{Title: "Time", Value: event.CreatedAt.Format(time.RFC3339), Short: true},
				},
				Timestamp: event.CreatedAt.Unix(),
			},
		},
	}

	// Add metadata fields
	for key, value := range event.Metadata {
		message.Attachments[0].Fields = append(message.Attachments[0].Fields, SlackField{
			Title: key,
			Value: fmt.Sprintf("%v", value),
			Short: true,
		})
	}

	// Send to Slack
	payload, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal slack message: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", webhookURL, bytes.NewBuffer(payload))
	if err != nil {
		return fmt.Errorf("failed to create slack request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send slack notification: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("slack API returned status: %d", resp.StatusCode)
	}

	return nil
}

// sendWebhookNotification sends a notification to a webhook
func (ns *NotificationService) sendWebhookNotification(ctx context.Context, channel NotificationChannel, event NotificationEvent) error {
	webhookURL, ok := channel.Configuration["url"].(string)
	if !ok {
		return fmt.Errorf("webhook URL not configured")
	}

	// Build webhook payload
	payload := map[string]interface{}{
		"event":     event,
		"timestamp": time.Now().Unix(),
		"source":    "deploy-orchestrator",
	}

	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal webhook payload: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", webhookURL, bytes.NewBuffer(jsonPayload))
	if err != nil {
		return fmt.Errorf("failed to create webhook request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// Add custom headers if configured
	if headers, ok := channel.Configuration["headers"].(map[string]interface{}); ok {
		for key, value := range headers {
			req.Header.Set(key, fmt.Sprintf("%v", value))
		}
	}

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send webhook notification: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("webhook returned status: %d", resp.StatusCode)
	}

	return nil
}

// sendEmailNotification sends an email notification (placeholder)
func (ns *NotificationService) sendEmailNotification(ctx context.Context, channel NotificationChannel, event NotificationEvent) error {
	// This would integrate with an email service like SendGrid, SES, etc.
	log.Printf("Email notification would be sent: %s", event.Title)
	return nil
}

// sendTeamsNotification sends a notification to Microsoft Teams (placeholder)
func (ns *NotificationService) sendTeamsNotification(ctx context.Context, channel NotificationChannel, event NotificationEvent) error {
	// This would integrate with Microsoft Teams webhook
	log.Printf("Teams notification would be sent: %s", event.Title)
	return nil
}

// getSlackColor returns the appropriate color for Slack attachments based on severity
func (ns *NotificationService) getSlackColor(severity string) string {
	switch severity {
	case "success":
		return "good"
	case "warning":
		return "warning"
	case "error":
		return "danger"
	default:
		return "#36a64f" // Default blue
	}
}

// CreateNotificationChannel creates a new notification channel
func (ns *NotificationService) CreateNotificationChannel(channel NotificationChannel) error {
	channel.CreatedAt = time.Now()
	channel.UpdatedAt = time.Now()
	
	if err := ns.db.Create(&channel).Error; err != nil {
		return fmt.Errorf("failed to create notification channel: %w", err)
	}
	
	return nil
}

// GetNotificationChannels retrieves notification channels for a project
func (ns *NotificationService) GetNotificationChannels(projectID string) ([]NotificationChannel, error) {
	var channels []NotificationChannel
	
	if err := ns.db.Where("project_id = ?", projectID).Find(&channels).Error; err != nil {
		return nil, fmt.Errorf("failed to get notification channels: %w", err)
	}
	
	return channels, nil
}

// UpdateNotificationChannel updates a notification channel
func (ns *NotificationService) UpdateNotificationChannel(channel NotificationChannel) error {
	channel.UpdatedAt = time.Now()
	
	if err := ns.db.Save(&channel).Error; err != nil {
		return fmt.Errorf("failed to update notification channel: %w", err)
	}
	
	return nil
}

// DeleteNotificationChannel deletes a notification channel
func (ns *NotificationService) DeleteNotificationChannel(channelID string) error {
	if err := ns.db.Delete(&NotificationChannel{}, "id = ?", channelID).Error; err != nil {
		return fmt.Errorf("failed to delete notification channel: %w", err)
	}
	
	return nil
}
