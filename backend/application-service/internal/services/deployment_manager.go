package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/claudio/deploy-orchestrator/application-service/internal/models"
	"github.com/claudio/deploy-orchestrator/shared/logging"
	"gorm.io/gorm"
)

// DeploymentManager handles deployment operations
type DeploymentManager struct {
	db     *gorm.DB
	logger logging.Logger
}

// NewDeploymentManager creates a new deployment manager
func NewDeploymentManager(db *gorm.DB, logger logging.Logger) *DeploymentManager {
	return &DeploymentManager{
		db:     db,
		logger: logger,
	}
}

// BulkDeploymentRequest represents a bulk deployment request
type BulkDeploymentRequest struct {
	ApplicationIDs []string               `json:"applicationIds,omitempty"`
	ComponentIDs   []string               `json:"componentIds,omitempty"`
	ProjectID      string                 `json:"projectId"`
	EnvironmentID  string                 `json:"environmentId"`
	Version        string                 `json:"version"`
	WorkflowID     string                 `json:"workflowId"`
	ProviderType   string                 `json:"providerType"`
	Parameters     map[string]interface{} `json:"parameters"`
	SecretMappings map[string]string      `json:"secretMappings"`
	DeployedBy     string                 `json:"deployedBy"`
	Strategy       string                 `json:"strategy,omitempty"`
	Configuration  map[string]interface{} `json:"configuration,omitempty"`
}

// DeploymentResponse represents a deployment response
type DeploymentResponse struct {
	DeploymentID      string            `json:"deploymentId"`
	WorkflowExecution string            `json:"workflowExecution"`
	Status            string            `json:"status"`
	Applications      []ApplicationInfo `json:"applications,omitempty"`
	Components        []ComponentInfo   `json:"components,omitempty"`
	CreatedAt         time.Time         `json:"createdAt"`
}

// ApplicationInfo represents application information in deployment
type ApplicationInfo struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Version string `json:"version"`
	Status  string `json:"status"`
}

// ComponentInfo represents component information in deployment
type ComponentInfo struct {
	ID            string `json:"id"`
	Name          string `json:"name"`
	ApplicationID string `json:"applicationId"`
	Version       string `json:"version"`
	Status        string `json:"status"`
}

// CreateDeployment creates a new deployment
func (s *DeploymentManager) CreateDeployment(ctx context.Context, req *BulkDeploymentRequest) (*DeploymentResponse, error) {
	s.logger.Info("Creating deployment", logging.Any("request", req))

	// Validate request
	if err := s.validateDeploymentRequest(req); err != nil {
		return nil, fmt.Errorf("invalid deployment request: %w", err)
	}

	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var deployments []models.ApplicationDeployment
	var response DeploymentResponse

	// Handle application deployments
	if len(req.ApplicationIDs) > 0 {
		for _, appID := range req.ApplicationIDs {
			deployment, err := s.createApplicationDeployment(tx, appID, req)
			if err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("failed to create application deployment: %w", err)
			}
			deployments = append(deployments, *deployment)

			// Add to response
			var app models.Application
			if err := tx.First(&app, "id = ?", appID).Error; err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("failed to find application: %w", err)
			}
			response.Applications = append(response.Applications, ApplicationInfo{
				ID:      app.ID,
				Name:    app.Name,
				Version: req.Version,
				Status:  string(deployment.Status),
			})
		}
	}

	// Handle component deployments
	if len(req.ComponentIDs) > 0 {
		for _, compID := range req.ComponentIDs {
			deployment, err := s.createComponentDeployment(tx, compID, req)
			if err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("failed to create component deployment: %w", err)
			}
			deployments = append(deployments, *deployment)

			// Add to response
			var comp models.ApplicationComponent
			if err := tx.First(&comp, "id = ?", compID).Error; err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("failed to find component: %w", err)
			}
			response.Components = append(response.Components, ComponentInfo{
				ID:            comp.ID,
				Name:          comp.Name,
				ApplicationID: comp.ApplicationID,
				Version:       req.Version,
				Status:        string(deployment.Status),
			})
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("failed to commit deployment transaction: %w", err)
	}

	// Set response metadata
	if len(deployments) > 0 {
		response.DeploymentID = deployments[0].ID
		response.Status = string(deployments[0].Status)
		response.CreatedAt = deployments[0].CreatedAt
	}

	s.logger.Info("Deployment created successfully", logging.String("deploymentId", response.DeploymentID))
	return &response, nil
}

// createApplicationDeployment creates a deployment for an application
func (s *DeploymentManager) createApplicationDeployment(tx *gorm.DB, appID string, req *BulkDeploymentRequest) (*models.ApplicationDeployment, error) {
	// Serialize configuration
	configJSON, err := json.Marshal(req.Configuration)
	if err != nil {
		return nil, fmt.Errorf("failed to serialize configuration: %w", err)
	}

	deployment := &models.ApplicationDeployment{
		ApplicationID: &appID,
		ProjectID:     req.ProjectID,
		EnvironmentID: req.EnvironmentID,
		Version:       req.Version,
		Status:        models.DeploymentStatusPending,
		Strategy:      models.DeploymentStrategy(req.Strategy),
		Configuration: string(configJSON),
		DeployedBy:    req.DeployedBy,
	}

	if err := tx.Create(deployment).Error; err != nil {
		return nil, fmt.Errorf("failed to create application deployment: %w", err)
	}

	// Create environment version tracking
	envVersion := &models.EnvironmentVersion{
		ApplicationID: &appID,
		ProjectID:     req.ProjectID,
		EnvironmentID: req.EnvironmentID,
		Version:       req.Version,
		DeploymentID:  deployment.ID,
		Status:        "deploying",
		DeployedAt:    time.Now(),
		DeployedBy:    req.DeployedBy,
	}

	if err := tx.Create(envVersion).Error; err != nil {
		return nil, fmt.Errorf("failed to create environment version: %w", err)
	}

	return deployment, nil
}

// createComponentDeployment creates a deployment for a component
func (s *DeploymentManager) createComponentDeployment(tx *gorm.DB, compID string, req *BulkDeploymentRequest) (*models.ApplicationDeployment, error) {
	// Serialize configuration
	configJSON, err := json.Marshal(req.Configuration)
	if err != nil {
		return nil, fmt.Errorf("failed to serialize configuration: %w", err)
	}

	deployment := &models.ApplicationDeployment{
		ComponentID:   &compID,
		ProjectID:     req.ProjectID,
		EnvironmentID: req.EnvironmentID,
		Version:       req.Version,
		Status:        models.DeploymentStatusPending,
		Strategy:      models.DeploymentStrategy(req.Strategy),
		Configuration: string(configJSON),
		DeployedBy:    req.DeployedBy,
	}

	if err := tx.Create(deployment).Error; err != nil {
		return nil, fmt.Errorf("failed to create component deployment: %w", err)
	}

	// Create environment version tracking
	envVersion := &models.EnvironmentVersion{
		ComponentID:   &compID,
		ProjectID:     req.ProjectID,
		EnvironmentID: req.EnvironmentID,
		Version:       req.Version,
		DeploymentID:  deployment.ID,
		Status:        "deploying",
		DeployedAt:    time.Now(),
		DeployedBy:    req.DeployedBy,
	}

	if err := tx.Create(envVersion).Error; err != nil {
		return nil, fmt.Errorf("failed to create environment version: %w", err)
	}

	return deployment, nil
}

// validateDeploymentRequest validates a deployment request
func (s *DeploymentManager) validateDeploymentRequest(req *BulkDeploymentRequest) error {
	if req.ProjectID == "" {
		return fmt.Errorf("project ID is required")
	}
	if req.EnvironmentID == "" {
		return fmt.Errorf("environment ID is required")
	}
	if req.Version == "" {
		return fmt.Errorf("version is required")
	}
	if req.WorkflowID == "" {
		return fmt.Errorf("workflow ID is required")
	}
	if req.ProviderType == "" {
		return fmt.Errorf("provider type is required")
	}
	if req.DeployedBy == "" {
		return fmt.Errorf("deployed by is required")
	}
	if len(req.ApplicationIDs) == 0 && len(req.ComponentIDs) == 0 {
		return fmt.Errorf("at least one application or component ID is required")
	}
	return nil
}

// GetEnvironmentVersions gets version information for environments
func (s *DeploymentManager) GetEnvironmentVersions(ctx context.Context, projectID string, environmentIDs []string) ([]models.EnvironmentVersion, error) {
	var versions []models.EnvironmentVersion

	query := s.db.Where("project_id = ?", projectID)
	if len(environmentIDs) > 0 {
		query = query.Where("environment_id IN ?", environmentIDs)
	}

	if err := query.Preload("Application").Preload("Component").Find(&versions).Error; err != nil {
		return nil, fmt.Errorf("failed to get environment versions: %w", err)
	}

	return versions, nil
}

// GetVersionMatrix gets a matrix of versions deployed across environments
func (s *DeploymentManager) GetVersionMatrix(ctx context.Context, projectID string) (map[string]map[string]string, error) {
	var versions []models.EnvironmentVersion

	if err := s.db.Where("project_id = ? AND status = ?", projectID, "active").
		Preload("Application").Preload("Component").Find(&versions).Error; err != nil {
		return nil, fmt.Errorf("failed to get version matrix: %w", err)
	}

	matrix := make(map[string]map[string]string)

	for _, version := range versions {
		var entityName string
		if version.Application != nil {
			entityName = version.Application.Name
		} else if version.Component != nil {
			entityName = version.Component.Name
		} else {
			continue
		}

		if matrix[entityName] == nil {
			matrix[entityName] = make(map[string]string)
		}
		matrix[entityName][version.EnvironmentID] = version.Version
	}

	return matrix, nil
}

// UpdateDeploymentStatus updates the status of a deployment
func (s *DeploymentManager) UpdateDeploymentStatus(ctx context.Context, deploymentID string, status models.DeploymentStatus) error {
	if err := s.db.Model(&models.ApplicationDeployment{}).
		Where("id = ?", deploymentID).
		Update("status", status).Error; err != nil {
		return fmt.Errorf("failed to update deployment status: %w", err)
	}

	// Update environment version status
	envStatus := "active"
	if status == models.DeploymentStatusFailed {
		envStatus = "failed"
	} else if status == models.DeploymentStatusRolledBack {
		envStatus = "rolled_back"
	}

	if err := s.db.Model(&models.EnvironmentVersion{}).
		Where("deployment_id = ?", deploymentID).
		Update("status", envStatus).Error; err != nil {
		s.logger.Warn("Failed to update environment version status", logging.Error(err))
	}

	return nil
}
