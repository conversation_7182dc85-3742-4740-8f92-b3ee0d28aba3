package services

import (
	"context"
	"fmt"
	"time"

	"github.com/claudio/deploy-orchestrator/application-service/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PromotionManager handles version promotions between environments
type PromotionManager struct {
	db                *gorm.DB
	deploymentManager *DeploymentManager
	workflowExecutor  *WorkflowExecutor
}

// Promotion represents a version promotion between environments
type Promotion struct {
	ID                  string                 `json:"id" gorm:"primaryKey"`
	ProjectID           string                 `json:"projectId" gorm:"not null"`
	ApplicationID       *string                `json:"applicationId"`
	ComponentID         *string                `json:"componentId"`
	Version             string                 `json:"version" gorm:"not null"`
	SourceEnvironmentID string                 `json:"sourceEnvironmentId" gorm:"not null"`
	TargetEnvironmentID string                 `json:"targetEnvironmentId" gorm:"not null"`
	WorkflowID          string                 `json:"workflowId" gorm:"not null"`
	ProviderType        string                 `json:"providerType" gorm:"not null"`
	Status              string                 `json:"status" gorm:"not null"` // pending, approved, rejected, in_progress, completed, failed
	DeploymentID        *string                `json:"deploymentId"`
	WorkflowExecutionID *string                `json:"workflowExecutionId"`
	RequestedBy         string                 `json:"requestedBy" gorm:"not null"`
	ApprovedBy          *string                `json:"approvedBy"`
	ApprovedAt          *time.Time             `json:"approvedAt"`
	CompletedAt         *time.Time             `json:"completedAt"`
	Parameters          map[string]interface{} `json:"parameters" gorm:"type:jsonb"`
	SecretMappings      map[string]string      `json:"secretMappings" gorm:"type:jsonb"`
	Configuration       map[string]interface{} `json:"configuration" gorm:"type:jsonb"`
	ApprovalRequired    bool                   `json:"approvalRequired" gorm:"default:true"`
	ApprovalReason      string                 `json:"approvalReason"`
	RejectionReason     string                 `json:"rejectionReason"`
	CreatedAt           time.Time              `json:"createdAt"`
	UpdatedAt           time.Time              `json:"updatedAt"`
}

// PromotionRequest represents a promotion request
type PromotionRequest struct {
	ProjectID           string                 `json:"projectId"`
	ApplicationID       *string                `json:"applicationId"`
	ComponentID         *string                `json:"componentId"`
	Version             string                 `json:"version"`
	SourceEnvironmentID string                 `json:"sourceEnvironmentId"`
	TargetEnvironmentID string                 `json:"targetEnvironmentId"`
	WorkflowID          string                 `json:"workflowId"`
	ProviderType        string                 `json:"providerType"`
	Parameters          map[string]interface{} `json:"parameters"`
	SecretMappings      map[string]string      `json:"secretMappings"`
	Configuration       map[string]interface{} `json:"configuration"`
	RequestedBy         string                 `json:"requestedBy"`
	ApprovalRequired    bool                   `json:"approvalRequired"`
}

// PromotionApproval represents a promotion approval/rejection
type PromotionApproval struct {
	PromotionID string `json:"promotionId"`
	Action      string `json:"action"` // approve, reject
	Reason      string `json:"reason"`
	ApprovedBy  string `json:"approvedBy"`
}

// PromotionFilter represents filters for querying promotions
type PromotionFilter struct {
	ProjectID     string   `json:"projectId"`
	Status        []string `json:"status"`
	RequestedBy   string   `json:"requestedBy"`
	EnvironmentID string   `json:"environmentId"`
	Limit         int      `json:"limit"`
	Offset        int      `json:"offset"`
}

// NewPromotionManager creates a new promotion manager
func NewPromotionManager(db *gorm.DB, deploymentManager *DeploymentManager, workflowExecutor *WorkflowExecutor) *PromotionManager {
	return &PromotionManager{
		db:                db,
		deploymentManager: deploymentManager,
		workflowExecutor:  workflowExecutor,
	}
}

// CreatePromotion creates a new promotion request
func (pm *PromotionManager) CreatePromotion(ctx context.Context, req PromotionRequest) (*Promotion, error) {
	// Validate the promotion request
	if err := pm.validatePromotionRequest(req); err != nil {
		return nil, fmt.Errorf("invalid promotion request: %w", err)
	}

	// Check if source version exists and is active
	sourceVersion, err := pm.getEnvironmentVersion(req.ProjectID, req.SourceEnvironmentID, req.Version, req.ApplicationID, req.ComponentID)
	if err != nil {
		return nil, fmt.Errorf("source version not found: %w", err)
	}

	if sourceVersion.Status != "active" {
		return nil, fmt.Errorf("source version is not active: %s", sourceVersion.Status)
	}

	// Create promotion record
	promotion := &Promotion{
		ID:                  uuid.New().String(),
		ProjectID:           req.ProjectID,
		ApplicationID:       req.ApplicationID,
		ComponentID:         req.ComponentID,
		Version:             req.Version,
		SourceEnvironmentID: req.SourceEnvironmentID,
		TargetEnvironmentID: req.TargetEnvironmentID,
		WorkflowID:          req.WorkflowID,
		ProviderType:        req.ProviderType,
		Status:              "pending",
		RequestedBy:         req.RequestedBy,
		Parameters:          req.Parameters,
		SecretMappings:      req.SecretMappings,
		Configuration:       req.Configuration,
		ApprovalRequired:    req.ApprovalRequired,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	}

	if err := pm.db.Create(promotion).Error; err != nil {
		return nil, fmt.Errorf("failed to create promotion: %w", err)
	}

	// If no approval required, automatically approve and execute
	if !req.ApprovalRequired {
		if err := pm.executePromotion(ctx, promotion); err != nil {
			return nil, fmt.Errorf("failed to execute promotion: %w", err)
		}
	}

	return promotion, nil
}

// ApprovePromotion approves or rejects a promotion
func (pm *PromotionManager) ApprovePromotion(ctx context.Context, approval PromotionApproval) error {
	var promotion Promotion
	if err := pm.db.First(&promotion, "id = ?", approval.PromotionID).Error; err != nil {
		return fmt.Errorf("promotion not found: %w", err)
	}

	if promotion.Status != "pending" {
		return fmt.Errorf("promotion is not in pending status: %s", promotion.Status)
	}

	now := time.Now()
	promotion.ApprovedBy = &approval.ApprovedBy
	promotion.ApprovedAt = &now
	promotion.UpdatedAt = now

	switch approval.Action {
	case "approve":
		promotion.Status = "approved"
		promotion.ApprovalReason = approval.Reason

		// Execute the promotion
		if err := pm.executePromotion(ctx, &promotion); err != nil {
			return fmt.Errorf("failed to execute promotion: %w", err)
		}

	case "reject":
		promotion.Status = "rejected"
		promotion.RejectionReason = approval.Reason

	default:
		return fmt.Errorf("invalid approval action: %s", approval.Action)
	}

	if err := pm.db.Save(&promotion).Error; err != nil {
		return fmt.Errorf("failed to update promotion: %w", err)
	}

	return nil
}

// executePromotion executes an approved promotion
func (pm *PromotionManager) executePromotion(ctx context.Context, promotion *Promotion) error {
	// Create deployment for the target environment
	deploymentReq := BulkDeploymentRequest{
		ProjectID:      promotion.ProjectID,
		EnvironmentID:  promotion.TargetEnvironmentID,
		Version:        promotion.Version,
		WorkflowID:     promotion.WorkflowID,
		ProviderType:   promotion.ProviderType,
		Parameters:     promotion.Parameters,
		SecretMappings: promotion.SecretMappings,
		Configuration:  promotion.Configuration,
	}

	if promotion.ApplicationID != nil {
		deploymentReq.ApplicationIDs = []string{*promotion.ApplicationID}
	}
	if promotion.ComponentID != nil {
		deploymentReq.ComponentIDs = []string{*promotion.ComponentID}
	}

	deployment, err := pm.deploymentManager.CreateDeployment(ctx, &deploymentReq)
	if err != nil {
		promotion.Status = "failed"
		pm.db.Save(promotion)
		return fmt.Errorf("failed to create deployment: %w", err)
	}

	// Update promotion with deployment ID
	promotion.DeploymentID = &deployment.DeploymentID
	promotion.Status = "in_progress"
	promotion.UpdatedAt = time.Now()
	pm.db.Save(promotion)

	// Execute workflow if workflow executor is available
	if pm.workflowExecutor != nil {
		execReq := ExecutionRequest{
			WorkflowID:   promotion.WorkflowID,
			DeploymentID: deployment.DeploymentID,
			Parameters:   promotion.Parameters,
			Context: map[string]interface{}{
				"promotion_id": promotion.ID,
				"source_env":   promotion.SourceEnvironmentID,
				"target_env":   promotion.TargetEnvironmentID,
			},
		}

		execResult, err := pm.workflowExecutor.ExecuteWorkflow(ctx, execReq)
		if err != nil {
			promotion.Status = "failed"
			pm.db.Save(promotion)
			return fmt.Errorf("failed to execute workflow: %w", err)
		}

		promotion.WorkflowExecutionID = &execResult.ExecutionID
		pm.db.Save(promotion)
	}

	return nil
}

// GetPromotions retrieves promotions based on filter
func (pm *PromotionManager) GetPromotions(filter PromotionFilter) ([]Promotion, int64, error) {
	var promotions []Promotion
	var total int64

	query := pm.db.Model(&Promotion{})

	// Apply filters
	if filter.ProjectID != "" {
		query = query.Where("project_id = ?", filter.ProjectID)
	}
	if len(filter.Status) > 0 {
		query = query.Where("status IN ?", filter.Status)
	}
	if filter.RequestedBy != "" {
		query = query.Where("requested_by = ?", filter.RequestedBy)
	}
	if filter.EnvironmentID != "" {
		query = query.Where("source_environment_id = ? OR target_environment_id = ?", filter.EnvironmentID, filter.EnvironmentID)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count promotions: %w", err)
	}

	// Apply pagination
	if filter.Limit > 0 {
		query = query.Limit(filter.Limit)
	}
	if filter.Offset > 0 {
		query = query.Offset(filter.Offset)
	}

	// Get promotions
	if err := query.Order("created_at DESC").Find(&promotions).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get promotions: %w", err)
	}

	return promotions, total, nil
}

// GetPromotion retrieves a specific promotion
func (pm *PromotionManager) GetPromotion(promotionID string) (*Promotion, error) {
	var promotion Promotion
	if err := pm.db.First(&promotion, "id = ?", promotionID).Error; err != nil {
		return nil, fmt.Errorf("promotion not found: %w", err)
	}
	return &promotion, nil
}

// validatePromotionRequest validates a promotion request
func (pm *PromotionManager) validatePromotionRequest(req PromotionRequest) error {
	if req.ProjectID == "" {
		return fmt.Errorf("project ID is required")
	}
	if req.ApplicationID == nil && req.ComponentID == nil {
		return fmt.Errorf("either application ID or component ID is required")
	}
	if req.Version == "" {
		return fmt.Errorf("version is required")
	}
	if req.SourceEnvironmentID == "" {
		return fmt.Errorf("source environment ID is required")
	}
	if req.TargetEnvironmentID == "" {
		return fmt.Errorf("target environment ID is required")
	}
	if req.SourceEnvironmentID == req.TargetEnvironmentID {
		return fmt.Errorf("source and target environments cannot be the same")
	}
	if req.WorkflowID == "" {
		return fmt.Errorf("workflow ID is required")
	}
	if req.ProviderType == "" {
		return fmt.Errorf("provider type is required")
	}
	if req.RequestedBy == "" {
		return fmt.Errorf("requested by is required")
	}
	return nil
}

// getEnvironmentVersion retrieves an environment version
func (pm *PromotionManager) getEnvironmentVersion(projectID, environmentID, version string, applicationID, componentID *string) (*models.EnvironmentVersion, error) {
	var envVersion models.EnvironmentVersion
	query := pm.db.Where("project_id = ? AND environment_id = ? AND version = ?", projectID, environmentID, version)

	if applicationID != nil {
		query = query.Where("application_id = ?", *applicationID)
	}
	if componentID != nil {
		query = query.Where("component_id = ?", *componentID)
	}

	if err := query.First(&envVersion).Error; err != nil {
		return nil, err
	}

	return &envVersion, nil
}
