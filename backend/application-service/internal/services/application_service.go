package services

import (
	"context"
	"fmt"
	"time"

	"github.com/claudio/deploy-orchestrator/application-service/internal/models"
	"github.com/claudio/deploy-orchestrator/shared/logging"
	"gorm.io/gorm"
)

// ApplicationService handles application-related operations
type ApplicationService struct {
	db               *gorm.DB
	projectValidator *ProjectValidator
	logger           logging.Logger
}

// CreateApplicationGroupRequest represents a request to create an application group
type CreateApplicationGroupRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	ProjectID   string `json:"projectId" binding:"required"`
}

// CreateApplicationRequest represents a request to create an application
type CreateApplicationRequest struct {
	Name           string `json:"name" binding:"required"`
	Description    string `json:"description"`
	GroupID        string `json:"groupId" binding:"required"`
	Repository     string `json:"repository"`
	Branch         string `json:"branch"`
	BuildCommand   string `json:"buildCommand"`
	StartCommand   string `json:"startCommand"`
	HealthEndpoint string `json:"healthEndpoint"`
	Port           int    `json:"port"`
}

// CreateComponentRequest represents a request to create an application component
type CreateComponentRequest struct {
	Name           string `json:"name" binding:"required"`
	Description    string `json:"description"`
	ApplicationID  string `json:"applicationId" binding:"required"`
	Type           string `json:"type" binding:"required"`
	Image          string `json:"image"`
	Version        string `json:"version"`
	Port           int    `json:"port"`
	HealthEndpoint string `json:"healthEndpoint"`
	ConfigTemplate string `json:"configTemplate"`
}

// DeploymentRequest represents a request to deploy an application or component
type DeploymentRequest struct {
	ApplicationID *string                   `json:"applicationId,omitempty"`
	ComponentID   *string                   `json:"componentId,omitempty"`
	EnvironmentID string                    `json:"environmentId" binding:"required"`
	Version       string                    `json:"version" binding:"required"`
	Strategy      models.DeploymentStrategy `json:"strategy"`
	Configuration string                    `json:"configuration"`
}

// NewApplicationService creates a new application service
func NewApplicationService(db *gorm.DB, projectValidator *ProjectValidator, logger logging.Logger) *ApplicationService {
	return &ApplicationService{
		db:               db,
		projectValidator: projectValidator,
		logger:           logger,
	}
}

// CreateApplicationGroup creates a new application group
func (s *ApplicationService) CreateApplicationGroup(ctx context.Context, req *CreateApplicationGroupRequest, userID, authToken string) (*models.ApplicationGroup, error) {
	// Validate project exists and is active (permission middleware already handled access control)
	if err := s.projectValidator.ValidateProject(ctx, req.ProjectID, authToken); err != nil {
		return nil, fmt.Errorf("project validation failed: %w", err)
	}

	// Check if group name already exists in project
	var existing models.ApplicationGroup
	if err := s.db.Where("name = ? AND project_id = ?", req.Name, req.ProjectID).First(&existing).Error; err == nil {
		return nil, fmt.Errorf("application group '%s' already exists in project", req.Name)
	}

	group := &models.ApplicationGroup{
		Name:        req.Name,
		Description: req.Description,
		ProjectID:   req.ProjectID,
		IsActive:    true,
	}

	if err := s.db.Create(group).Error; err != nil {
		return nil, fmt.Errorf("failed to create application group: %w", err)
	}

	s.logger.Info("Application group created",
		logging.String("groupId", group.ID),
		logging.String("name", group.Name),
		logging.String("projectId", group.ProjectID),
		logging.String("userId", userID),
	)

	return group, nil
}

// GetApplicationGroups retrieves application groups for accessible projects
func (s *ApplicationService) GetApplicationGroups(ctx context.Context, userID, authToken string, projectID *string) ([]models.ApplicationGroup, error) {
	// Since permission middleware already filtered accessible projects, get all active groups
	// The permission middleware ensures users can only access groups from their authorized projects
	query := s.db.Preload("Applications").Where("is_active = ?", true)

	if projectID != nil {
		query = query.Where("project_id = ?", *projectID)
	}

	var groups []models.ApplicationGroup
	if err := query.Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve application groups: %w", err)
	}

	return groups, nil
}

// GetApplicationGroup retrieves a specific application group
func (s *ApplicationService) GetApplicationGroup(ctx context.Context, groupID, userID, authToken string) (*models.ApplicationGroup, error) {
	var group models.ApplicationGroup
	if err := s.db.Preload("Applications.Components").First(&group, "id = ?", groupID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("application group not found")
		}
		return nil, fmt.Errorf("failed to retrieve application group: %w", err)
	}

	// Validate project exists and is active (permission middleware already handled access control)
	if err := s.projectValidator.ValidateProject(ctx, group.ProjectID, authToken); err != nil {
		return nil, fmt.Errorf("project validation failed: %w", err)
	}

	return &group, nil
}

// CreateApplication creates a new application
func (s *ApplicationService) CreateApplication(ctx context.Context, req *CreateApplicationRequest, userID, authToken string) (*models.Application, error) {
	// Get the application group to validate project access
	var group models.ApplicationGroup
	if err := s.db.First(&group, "id = ?", req.GroupID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("application group not found")
		}
		return nil, fmt.Errorf("failed to retrieve application group: %w", err)
	}

	// Validate project exists and is active (permission middleware already handled access control)
	if err := s.projectValidator.ValidateProject(ctx, group.ProjectID, authToken); err != nil {
		return nil, fmt.Errorf("project validation failed: %w", err)
	}

	// Check if application name already exists in group
	var existing models.Application
	if err := s.db.Where("name = ? AND group_id = ?", req.Name, req.GroupID).First(&existing).Error; err == nil {
		return nil, fmt.Errorf("application '%s' already exists in group", req.Name)
	}

	// Set default branch if not provided
	branch := req.Branch
	if branch == "" {
		branch = "main"
	}

	application := &models.Application{
		Name:           req.Name,
		Description:    req.Description,
		GroupID:        req.GroupID,
		ProjectID:      group.ProjectID,
		Repository:     req.Repository,
		Branch:         branch,
		BuildCommand:   req.BuildCommand,
		StartCommand:   req.StartCommand,
		HealthEndpoint: req.HealthEndpoint,
		Port:           req.Port,
		IsActive:       true,
	}

	if err := s.db.Create(application).Error; err != nil {
		return nil, fmt.Errorf("failed to create application: %w", err)
	}

	s.logger.Info("Application created",
		logging.String("applicationId", application.ID),
		logging.String("name", application.Name),
		logging.String("groupId", application.GroupID),
		logging.String("projectId", application.ProjectID),
		logging.String("userId", userID),
	)

	return application, nil
}

// GetApplications retrieves applications for accessible projects
func (s *ApplicationService) GetApplications(ctx context.Context, userID, authToken string, groupID *string) ([]models.Application, error) {
	// Since permission middleware already filtered accessible projects, get all active applications
	// The permission middleware ensures users can only access applications from their authorized projects
	query := s.db.Preload("Group").Preload("Components").Where("is_active = ?", true)

	if groupID != nil {
		query = query.Where("group_id = ?", *groupID)
	}

	var applications []models.Application
	if err := query.Find(&applications).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve applications: %w", err)
	}

	return applications, nil
}

// GetApplication retrieves a specific application
func (s *ApplicationService) GetApplication(ctx context.Context, applicationID, userID, authToken string) (*models.Application, error) {
	var application models.Application
	if err := s.db.Preload("Group").Preload("Components").Preload("Deployments").
		First(&application, "id = ?", applicationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("application not found")
		}
		return nil, fmt.Errorf("failed to retrieve application: %w", err)
	}

	// Validate project exists and is active (permission middleware already handled access control)
	if err := s.projectValidator.ValidateProject(ctx, application.ProjectID, authToken); err != nil {
		return nil, fmt.Errorf("project validation failed: %w", err)
	}

	return &application, nil
}

// CreateComponent creates a new application component
func (s *ApplicationService) CreateComponent(ctx context.Context, req *CreateComponentRequest, userID, authToken string) (*models.ApplicationComponent, error) {
	// Get the application to validate project access
	var application models.Application
	if err := s.db.First(&application, "id = ?", req.ApplicationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("application not found")
		}
		return nil, fmt.Errorf("failed to retrieve application: %w", err)
	}

	// Validate project exists and is active (permission middleware already handled access control)
	if err := s.projectValidator.ValidateProject(ctx, application.ProjectID, authToken); err != nil {
		return nil, fmt.Errorf("project validation failed: %w", err)
	}

	// Check if component name already exists in application
	var existing models.ApplicationComponent
	if err := s.db.Where("name = ? AND application_id = ?", req.Name, req.ApplicationID).First(&existing).Error; err == nil {
		return nil, fmt.Errorf("component '%s' already exists in application", req.Name)
	}

	component := &models.ApplicationComponent{
		Name:           req.Name,
		Description:    req.Description,
		ApplicationID:  req.ApplicationID,
		ProjectID:      application.ProjectID,
		Type:           req.Type,
		Image:          req.Image,
		Version:        req.Version,
		Port:           req.Port,
		HealthEndpoint: req.HealthEndpoint,
		ConfigTemplate: req.ConfigTemplate,
		IsActive:       true,
	}

	if err := s.db.Create(component).Error; err != nil {
		return nil, fmt.Errorf("failed to create application component: %w", err)
	}

	s.logger.Info("Application component created",
		logging.String("componentId", component.ID),
		logging.String("name", component.Name),
		logging.String("applicationId", component.ApplicationID),
		logging.String("projectId", component.ProjectID),
		logging.String("userId", userID),
	)

	return component, nil
}

// DeployApplication deploys an application or component
func (s *ApplicationService) DeployApplication(ctx context.Context, req *DeploymentRequest, userID, authToken string) (*models.ApplicationDeployment, error) {
	var projectID string

	// Validate request - must have either applicationID or componentID
	if req.ApplicationID == nil && req.ComponentID == nil {
		return nil, fmt.Errorf("either applicationId or componentId must be provided")
	}

	if req.ApplicationID != nil && req.ComponentID != nil {
		return nil, fmt.Errorf("cannot deploy both application and component in the same request")
	}

	// Get project ID and validate access
	if req.ApplicationID != nil {
		var application models.Application
		if err := s.db.First(&application, "id = ?", *req.ApplicationID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, fmt.Errorf("application not found")
			}
			return nil, fmt.Errorf("failed to retrieve application: %w", err)
		}
		projectID = application.ProjectID
	} else {
		var component models.ApplicationComponent
		if err := s.db.First(&component, "id = ?", *req.ComponentID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, fmt.Errorf("component not found")
			}
			return nil, fmt.Errorf("failed to retrieve component: %w", err)
		}
		projectID = component.ProjectID
	}

	// Validate project exists and is active (permission middleware already handled access control)
	if err := s.projectValidator.ValidateProject(ctx, projectID, authToken); err != nil {
		return nil, fmt.Errorf("project validation failed: %w", err)
	}

	// Set default strategy if not provided
	strategy := req.Strategy
	if strategy == "" {
		strategy = models.DeploymentStrategyRollingUpdate
	}

	deployment := &models.ApplicationDeployment{
		ApplicationID: req.ApplicationID,
		ComponentID:   req.ComponentID,
		ProjectID:     projectID,
		EnvironmentID: req.EnvironmentID,
		Version:       req.Version,
		Status:        models.DeploymentStatusPending,
		Strategy:      strategy,
		Configuration: req.Configuration,
		DeployedBy:    userID,
		HealthStatus:  models.HealthStatusUnknown,
	}

	if err := s.db.Create(deployment).Error; err != nil {
		return nil, fmt.Errorf("failed to create deployment: %w", err)
	}

	s.logger.Info("Deployment created",
		logging.String("deploymentId", deployment.ID),
		logging.String("projectId", deployment.ProjectID),
		logging.String("environmentId", deployment.EnvironmentID),
		logging.String("version", deployment.Version),
		logging.String("userId", userID),
	)

	// TODO: Trigger actual deployment process asynchronously
	// This would involve calling the appropriate provider/plugin

	return deployment, nil
}

// GetDeployments retrieves deployments for accessible projects
func (s *ApplicationService) GetDeployments(ctx context.Context, userID, authToken string, applicationID, componentID *string) ([]models.ApplicationDeployment, error) {
	// Since permission middleware already filtered accessible projects, get all deployments
	// The permission middleware ensures users can only access deployments from their authorized projects
	query := s.db.Preload("Application").Preload("Component")

	if applicationID != nil {
		query = query.Where("application_id = ?", *applicationID)
	}

	if componentID != nil {
		query = query.Where("component_id = ?", *componentID)
	}

	var deployments []models.ApplicationDeployment
	if err := query.Order("created_at DESC").Find(&deployments).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve deployments: %w", err)
	}

	return deployments, nil
}

// UpdateDeploymentStatus updates the status of a deployment
func (s *ApplicationService) UpdateDeploymentStatus(ctx context.Context, deploymentID string, status models.DeploymentStatus, healthStatus models.HealthStatus, logs string, userID, authToken string) error {
	var deployment models.ApplicationDeployment
	if err := s.db.First(&deployment, "id = ?", deploymentID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("deployment not found")
		}
		return fmt.Errorf("failed to retrieve deployment: %w", err)
	}

	// Validate project exists and is active (permission middleware already handled access control)
	if err := s.projectValidator.ValidateProject(ctx, deployment.ProjectID, authToken); err != nil {
		return fmt.Errorf("project validation failed: %w", err)
	}

	updates := map[string]interface{}{
		"status":            status,
		"health_status":     healthStatus,
		"logs":              logs,
		"last_health_check": time.Now(),
	}

	if status == models.DeploymentStatusSucceeded {
		now := time.Now()
		updates["deployed_at"] = &now
	}

	if err := s.db.Model(&deployment).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update deployment status: %w", err)
	}

	s.logger.Info("Deployment status updated",
		logging.String("deploymentId", deploymentID),
		logging.String("status", string(status)),
		logging.String("healthStatus", string(healthStatus)),
		logging.String("userId", userID),
	)

	return nil
}
