package services

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// RollbackManager handles deployment rollbacks
type RollbackManager struct {
	db                *gorm.DB
	deploymentManager *DeploymentManager
	workflowExecutor  *WorkflowExecutor
}

// Rollback represents a deployment rollback
type Rollback struct {
	ID                  string                 `json:"id" gorm:"primaryKey"`
	ProjectID           string                 `json:"projectId" gorm:"not null"`
	EnvironmentID       string                 `json:"environmentId" gorm:"not null"`
	ApplicationID       *string                `json:"applicationId"`
	ComponentID         *string                `json:"componentId"`
	FromVersion         string                 `json:"fromVersion" gorm:"not null"`
	ToVersion           string                 `json:"toVersion" gorm:"not null"`
	FromDeploymentID    string                 `json:"fromDeploymentId" gorm:"not null"`
	ToDeploymentID      *string                `json:"toDeploymentId"`
	WorkflowID          string                 `json:"workflowId" gorm:"not null"`
	ProviderType        string                 `json:"providerType" gorm:"not null"`
	Status              string                 `json:"status" gorm:"not null"` // pending, in_progress, completed, failed
	WorkflowExecutionID *string                `json:"workflowExecutionId"`
	RequestedBy         string                 `json:"requestedBy" gorm:"not null"`
	Reason              string                 `json:"reason"`
	CompletedAt         *time.Time             `json:"completedAt"`
	Parameters          map[string]interface{} `json:"parameters" gorm:"type:jsonb"`
	Configuration       map[string]interface{} `json:"configuration" gorm:"type:jsonb"`
	CreatedAt           time.Time              `json:"createdAt"`
	UpdatedAt           time.Time              `json:"updatedAt"`
}

// DeploymentRollbackRequest represents a rollback request
type DeploymentRollbackRequest struct {
	ProjectID     string                 `json:"projectId"`
	EnvironmentID string                 `json:"environmentId"`
	ApplicationID *string                `json:"applicationId"`
	ComponentID   *string                `json:"componentId"`
	ToVersion     string                 `json:"toVersion"`
	WorkflowID    string                 `json:"workflowId"`
	ProviderType  string                 `json:"providerType"`
	RequestedBy   string                 `json:"requestedBy"`
	Reason        string                 `json:"reason"`
	Parameters    map[string]interface{} `json:"parameters"`
	Configuration map[string]interface{} `json:"configuration"`
}

// RollbackFilter represents filters for querying rollbacks
type RollbackFilter struct {
	ProjectID     string   `json:"projectId"`
	EnvironmentID string   `json:"environmentId"`
	Status        []string `json:"status"`
	RequestedBy   string   `json:"requestedBy"`
	Limit         int      `json:"limit"`
	Offset        int      `json:"offset"`
}

// RollbackOption represents a rollback option
type RollbackOption struct {
	Version      string    `json:"version"`
	DeploymentID string    `json:"deploymentId"`
	DeployedAt   time.Time `json:"deployedAt"`
	DeployedBy   string    `json:"deployedBy"`
	Status       string    `json:"status"`
}

// NewRollbackManager creates a new rollback manager
func NewRollbackManager(db *gorm.DB, deploymentManager *DeploymentManager, workflowExecutor *WorkflowExecutor) *RollbackManager {
	return &RollbackManager{
		db:                db,
		deploymentManager: deploymentManager,
		workflowExecutor:  workflowExecutor,
	}
}

// GetRollbackOptions retrieves available rollback options for an environment
func (rm *RollbackManager) GetRollbackOptions(projectID, environmentID string, applicationID, componentID *string) ([]RollbackOption, error) {
	var envVersions []EnvironmentVersion
	query := rm.db.Where("project_id = ? AND environment_id = ? AND status = ?", projectID, environmentID, "active")

	if applicationID != nil {
		query = query.Where("application_id = ?", *applicationID)
	}
	if componentID != nil {
		query = query.Where("component_id = ?", *componentID)
	}

	if err := query.Order("deployed_at DESC").Limit(10).Find(&envVersions).Error; err != nil {
		return nil, fmt.Errorf("failed to get environment versions: %w", err)
	}

	var options []RollbackOption
	for _, version := range envVersions {
		options = append(options, RollbackOption{
			Version:      version.Version,
			DeploymentID: version.DeploymentID,
			DeployedAt:   version.DeployedAt,
			DeployedBy:   version.DeployedBy,
			Status:       version.Status,
		})
	}

	return options, nil
}

// CreateRollback creates a new rollback
func (rm *RollbackManager) CreateRollback(ctx context.Context, req DeploymentRollbackRequest) (*Rollback, error) {
	// Validate the rollback request
	if err := rm.validateRollbackRequest(req); err != nil {
		return nil, fmt.Errorf("invalid rollback request: %w", err)
	}

	// Get current version
	currentVersion, err := rm.getCurrentVersion(req.ProjectID, req.EnvironmentID, req.ApplicationID, req.ComponentID)
	if err != nil {
		return nil, fmt.Errorf("failed to get current version: %w", err)
	}

	// Validate target version exists
	targetVersion, err := rm.getTargetVersion(req.ProjectID, req.EnvironmentID, req.ToVersion, req.ApplicationID, req.ComponentID)
	if err != nil {
		return nil, fmt.Errorf("target version not found: %w", err)
	}

	// Check if rollback is necessary
	if currentVersion.Version == req.ToVersion {
		return nil, fmt.Errorf("already at target version: %s", req.ToVersion)
	}

	// Create rollback record
	rollback := &Rollback{
		ID:               uuid.New().String(),
		ProjectID:        req.ProjectID,
		EnvironmentID:    req.EnvironmentID,
		ApplicationID:    req.ApplicationID,
		ComponentID:      req.ComponentID,
		FromVersion:      currentVersion.Version,
		ToVersion:        req.ToVersion,
		FromDeploymentID: currentVersion.DeploymentID,
		WorkflowID:       req.WorkflowID,
		ProviderType:     req.ProviderType,
		Status:           "pending",
		RequestedBy:      req.RequestedBy,
		Reason:           req.Reason,
		Parameters:       req.Parameters,
		Configuration:    req.Configuration,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	if err := rm.db.Create(rollback).Error; err != nil {
		return nil, fmt.Errorf("failed to create rollback: %w", err)
	}

	// Execute rollback
	if err := rm.executeRollback(ctx, rollback, targetVersion); err != nil {
		return nil, fmt.Errorf("failed to execute rollback: %w", err)
	}

	return rollback, nil
}

// executeRollback executes a rollback
func (rm *RollbackManager) executeRollback(ctx context.Context, rollback *Rollback, targetVersion *EnvironmentVersion) error {
	// Create deployment for rollback
	deploymentReq := CreateDeploymentRequest{
		ProjectID:     rollback.ProjectID,
		EnvironmentID: rollback.EnvironmentID,
		Version:       rollback.ToVersion,
		WorkflowID:    rollback.WorkflowID,
		ProviderType:  rollback.ProviderType,
		Parameters:    rollback.Parameters,
		Configuration: rollback.Configuration,
	}

	if rollback.ApplicationID != nil {
		deploymentReq.ApplicationIDs = []string{*rollback.ApplicationID}
	}
	if rollback.ComponentID != nil {
		deploymentReq.ComponentIDs = []string{*rollback.ComponentID}
	}

	deployment, err := rm.deploymentManager.CreateDeployment(ctx, deploymentReq)
	if err != nil {
		rollback.Status = "failed"
		rm.db.Save(rollback)
		return fmt.Errorf("failed to create rollback deployment: %w", err)
	}

	// Update rollback with deployment ID
	rollback.ToDeploymentID = &deployment.ID
	rollback.Status = "in_progress"
	rollback.UpdatedAt = time.Now()
	rm.db.Save(rollback)

	// Execute workflow if workflow executor is available
	if rm.workflowExecutor != nil {
		execReq := ExecutionRequest{
			WorkflowID:   rollback.WorkflowID,
			DeploymentID: deployment.ID,
			Parameters:   rollback.Parameters,
			Context: map[string]interface{}{
				"rollback_id":     rollback.ID,
				"from_version":    rollback.FromVersion,
				"to_version":      rollback.ToVersion,
				"rollback_reason": rollback.Reason,
			},
		}

		execResult, err := rm.workflowExecutor.ExecuteWorkflow(ctx, execReq)
		if err != nil {
			rollback.Status = "failed"
			rm.db.Save(rollback)
			return fmt.Errorf("failed to execute rollback workflow: %w", err)
		}

		rollback.WorkflowExecutionID = &execResult.ExecutionID
		rm.db.Save(rollback)
	}

	return nil
}

// GetRollbacks retrieves rollbacks based on filter
func (rm *RollbackManager) GetRollbacks(filter RollbackFilter) ([]Rollback, int64, error) {
	var rollbacks []Rollback
	var total int64

	query := rm.db.Model(&Rollback{})

	// Apply filters
	if filter.ProjectID != "" {
		query = query.Where("project_id = ?", filter.ProjectID)
	}
	if filter.EnvironmentID != "" {
		query = query.Where("environment_id = ?", filter.EnvironmentID)
	}
	if len(filter.Status) > 0 {
		query = query.Where("status IN ?", filter.Status)
	}
	if filter.RequestedBy != "" {
		query = query.Where("requested_by = ?", filter.RequestedBy)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count rollbacks: %w", err)
	}

	// Apply pagination
	if filter.Limit > 0 {
		query = query.Limit(filter.Limit)
	}
	if filter.Offset > 0 {
		query = query.Offset(filter.Offset)
	}

	// Get rollbacks
	if err := query.Order("created_at DESC").Find(&rollbacks).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get rollbacks: %w", err)
	}

	return rollbacks, total, nil
}

// GetRollback retrieves a specific rollback
func (rm *RollbackManager) GetRollback(rollbackID string) (*Rollback, error) {
	var rollback Rollback
	if err := rm.db.First(&rollback, "id = ?", rollbackID).Error; err != nil {
		return nil, fmt.Errorf("rollback not found: %w", err)
	}
	return &rollback, nil
}

// UpdateRollbackStatus updates the status of a rollback
func (rm *RollbackManager) UpdateRollbackStatus(rollbackID, status string) error {
	rollback := &Rollback{}
	if err := rm.db.First(rollback, "id = ?", rollbackID).Error; err != nil {
		return fmt.Errorf("rollback not found: %w", err)
	}

	rollback.Status = status
	rollback.UpdatedAt = time.Now()

	if status == "completed" || status == "failed" {
		now := time.Now()
		rollback.CompletedAt = &now
	}

	if err := rm.db.Save(rollback).Error; err != nil {
		return fmt.Errorf("failed to update rollback status: %w", err)
	}

	return nil
}

// validateRollbackRequest validates a rollback request
func (rm *RollbackManager) validateRollbackRequest(req DeploymentRollbackRequest) error {
	if req.ProjectID == "" {
		return fmt.Errorf("project ID is required")
	}
	if req.EnvironmentID == "" {
		return fmt.Errorf("environment ID is required")
	}
	if req.ApplicationID == nil && req.ComponentID == nil {
		return fmt.Errorf("either application ID or component ID is required")
	}
	if req.ToVersion == "" {
		return fmt.Errorf("target version is required")
	}
	if req.WorkflowID == "" {
		return fmt.Errorf("workflow ID is required")
	}
	if req.ProviderType == "" {
		return fmt.Errorf("provider type is required")
	}
	if req.RequestedBy == "" {
		return fmt.Errorf("requested by is required")
	}
	return nil
}

// getCurrentVersion gets the current version deployed in an environment
func (rm *RollbackManager) getCurrentVersion(projectID, environmentID string, applicationID, componentID *string) (*EnvironmentVersion, error) {
	var envVersion EnvironmentVersion
	query := rm.db.Where("project_id = ? AND environment_id = ? AND status = ?", projectID, environmentID, "active")

	if applicationID != nil {
		query = query.Where("application_id = ?", *applicationID)
	}
	if componentID != nil {
		query = query.Where("component_id = ?", *componentID)
	}

	if err := query.Order("deployed_at DESC").First(&envVersion).Error; err != nil {
		return nil, err
	}

	return &envVersion, nil
}

// getTargetVersion gets the target version for rollback
func (rm *RollbackManager) getTargetVersion(projectID, environmentID, version string, applicationID, componentID *string) (*EnvironmentVersion, error) {
	var envVersion EnvironmentVersion
	query := rm.db.Where("project_id = ? AND environment_id = ? AND version = ?", projectID, environmentID, version)

	if applicationID != nil {
		query = query.Where("application_id = ?", *applicationID)
	}
	if componentID != nil {
		query = query.Where("component_id = ?", *componentID)
	}

	if err := query.First(&envVersion).Error; err != nil {
		return nil, err
	}

	return &envVersion, nil
}
