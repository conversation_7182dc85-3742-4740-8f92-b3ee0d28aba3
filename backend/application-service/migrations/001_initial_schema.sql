-- +migrate Up
-- Create application groups table
CREATE TABLE IF NOT EXISTS application_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    project_id UUID NOT NULL,
    is_active B<PERSON><PERSON>EAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(name, project_id)
);

-- Create applications table
CREATE TABLE IF NOT EXISTS applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    group_id UUID NOT NULL REFERENCES application_groups(id),
    project_id UUID NOT NULL,
    repository VARCHAR(500),
    branch VARCHAR(100) DEFAULT 'main',
    build_command TEXT,
    start_command TEXT,
    health_endpoint VARCHAR(255),
    port INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(name, group_id)
);

-- Create application components table
CREATE TABLE IF NOT EXISTS application_components (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    application_id UUID NOT NULL REFERENCES applications(id),
    project_id UUID NOT NULL,
    type VARCHAR(100) NOT NULL,
    image VARCHAR(500),
    version VARCHAR(100),
    port INTEGER,
    health_endpoint VARCHAR(255),
    config_template TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(name, application_id)
);

-- Create deployments table
CREATE TABLE IF NOT EXISTS deployments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    application_id UUID REFERENCES applications(id),
    component_id UUID REFERENCES application_components(id),
    project_id UUID NOT NULL,
    environment_id UUID NOT NULL,
    version VARCHAR(100) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    strategy VARCHAR(20) DEFAULT 'rolling',
    configuration TEXT,
    deployed_by UUID NOT NULL,
    deployed_at TIMESTAMP WITH TIME ZONE,
    rollback_to UUID,
    health_status VARCHAR(20) DEFAULT 'unknown',
    last_health_check TIMESTAMP WITH TIME ZONE,
    logs TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT check_deployment_target CHECK (
        (application_id IS NOT NULL AND component_id IS NULL) OR
        (application_id IS NULL AND component_id IS NOT NULL)
    )
);

-- Create application metrics table
CREATE TABLE IF NOT EXISTS application_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    application_id UUID NOT NULL REFERENCES applications(id),
    component_id UUID REFERENCES application_components(id),
    deployment_id UUID NOT NULL REFERENCES deployments(id),
    project_id UUID NOT NULL,
    environment_id UUID NOT NULL,
    metric_type VARCHAR(100) NOT NULL,
    value DECIMAL(15,4),
    unit VARCHAR(50),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_application_groups_project_id ON application_groups(project_id);
CREATE INDEX IF NOT EXISTS idx_application_groups_name_project ON application_groups(name, project_id);

CREATE INDEX IF NOT EXISTS idx_applications_group_id ON applications(group_id);
CREATE INDEX IF NOT EXISTS idx_applications_project_id ON applications(project_id);
CREATE INDEX IF NOT EXISTS idx_applications_name_group ON applications(name, group_id);

CREATE INDEX IF NOT EXISTS idx_application_components_application_id ON application_components(application_id);
CREATE INDEX IF NOT EXISTS idx_application_components_project_id ON application_components(project_id);
CREATE INDEX IF NOT EXISTS idx_application_components_name_app ON application_components(name, application_id);

CREATE INDEX IF NOT EXISTS idx_deployments_application_id ON deployments(application_id);
CREATE INDEX IF NOT EXISTS idx_deployments_component_id ON deployments(component_id);
CREATE INDEX IF NOT EXISTS idx_deployments_project_id ON deployments(project_id);
CREATE INDEX IF NOT EXISTS idx_deployments_environment_id ON deployments(environment_id);
CREATE INDEX IF NOT EXISTS idx_deployments_status ON deployments(status);
CREATE INDEX IF NOT EXISTS idx_deployments_created_at ON deployments(created_at);

CREATE INDEX IF NOT EXISTS idx_application_metrics_application_id ON application_metrics(application_id);
CREATE INDEX IF NOT EXISTS idx_application_metrics_component_id ON application_metrics(component_id);
CREATE INDEX IF NOT EXISTS idx_application_metrics_deployment_id ON application_metrics(deployment_id);
CREATE INDEX IF NOT EXISTS idx_application_metrics_project_id ON application_metrics(project_id);
CREATE INDEX IF NOT EXISTS idx_application_metrics_environment_id ON application_metrics(environment_id);
CREATE INDEX IF NOT EXISTS idx_application_metrics_timestamp ON application_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_application_metrics_type_timestamp ON application_metrics(metric_type, timestamp);

-- +migrate Down
DROP TABLE IF EXISTS application_metrics;
DROP TABLE IF EXISTS deployments;
DROP TABLE IF EXISTS application_components;
DROP TABLE IF EXISTS applications;
DROP TABLE IF EXISTS application_groups;
