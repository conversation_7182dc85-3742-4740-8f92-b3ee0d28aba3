package storage

import (
	"fmt"
	"time"

	"github.com/claudio/deploy-orchestrator/application-service/config"
	"github.com/claudio/deploy-orchestrator/application-service/internal/models"
	"github.com/claudio/deploy-orchestrator/shared/db"
	"gorm.io/gorm"
)

func InitDatabase(cfg *config.Config) (*gorm.DB, error) {
	// Use shared database initialization
	dbConfig := db.Config{
		URL:           cfg.Database.URL,
		MaxRetries:    cfg.Database.MaxRetries,
		RetryInterval: time.Duration(cfg.Database.RetryInterval) * time.Second,
		LogLevel:      cfg.Database.LogLevel,
	}

	database, err := db.Connect(dbConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize database: %v", err)
	}

	// Run migrations
	if err := autoMigrate(database); err != nil {
		return nil, fmt.Errorf("failed to run migrations: %v", err)
	}

	// Create indexes (non-critical, log errors but don't fail)
	return database, nil
}

// autoMigrate runs database migrations
func autoMigrate(db *gorm.DB) error {
	// Define the order of migrations to handle foreign key dependencies
	// Using both legacy and new unified models for compatibility
	models := []any{
		// Legacy models for compatibility
		&models.ApplicationGroup{},
		&models.Application{},
		&models.ApplicationVersion{},
		&models.ApplicationComponent{},
		&models.ApplicationDeployment{},
		&models.ApplicationMetrics{},
		&models.ApplicationArtifact{},
		&models.DeploymentConfiguration{},
		&models.EnvironmentVersion{},

		// New unified Deployable models
		&models.Deployable{},
		&models.DeployableVersion{},
		&models.DeployableDeployment{},
		&models.DeployableMetrics{},
		&models.DeployableEnvironmentStatus{},
	}

	for _, model := range models {
		if err := db.AutoMigrate(model); err != nil {
			return fmt.Errorf("failed to migrate %T: %w", model, err)
		}
	}

	return nil
}
