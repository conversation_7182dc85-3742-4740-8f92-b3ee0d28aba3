#!/bin/bash

# Color codes for output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Go mod tidy across all services...${NC}"

SERVICES=(
  "deployment-service"
  "integration-service"
  "notification-service"
  "scheduling-service"
  "audit-service"
  "admin-service"
)


for service in "${SERVICES[@]}"; do
  echo -e "\n${BLUE}Go mod tidy $service ${NC}"
  
  cd "$service" && go mod tidy
  cd ..
done
