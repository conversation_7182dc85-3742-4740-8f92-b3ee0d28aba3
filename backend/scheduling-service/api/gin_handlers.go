package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/claudio/deploy-orchestrator/scheduling-service/storage"
	"github.com/claudio/deploy-orchestrator/shared/auth"
	"github.com/claudio/deploy-orchestrator/shared/models"
)

// ScheduleHandler handles HTTP requests for schedules
type ScheduleHandler struct {
	db         *storage.Database
	authHelper *auth.AuthHelper
}

// NewScheduleHandler creates a new ScheduleHandler
func NewScheduleHandler(db *storage.Database) *ScheduleHandler {
	return &ScheduleHandler{
		db:         db,
		authHelper: auth.NewAuthHelper(),
	}
}

// GetSchedules retrieves all schedules
func (h *ScheduleHandler) GetSchedules(c *gin.Context) {
	// Check if filtering by deployment is requested
	deploymentID := c.Query("deploymentId")

	// Check if filtering by active status is requested
	activeOnly := c.Query("active") == "true"

	// Check user authentication using centralized auth helper
	authContext := h.authHelper.RequireAuthentication(c)
	if authContext == nil {
		// RequireAuthentication already sent the 401 response
		return
	}

	// Check if user is admin
	isAdmin := authContext.IsAdmin()

	var schedules []models.Schedule
	var err error

	if deploymentID != "" {
		schedules, err = h.db.GetSchedulesForDeployment(c.Request.Context(), deploymentID)
	} else if activeOnly {
		schedules, err = h.db.GetActiveSchedules(c.Request.Context())
	} else {
		schedules, err = h.db.GetSchedules(c.Request.Context())
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve schedules"})
		return
	}

	// If user is not admin, filter schedules by project access
	if !isAdmin {
		// TODO: In a real implementation, we would check if the user has access to the deployment's project
		// For now, we'll just return all schedules
		// This would involve:
		// 1. Getting the deployment ID for each schedule
		// 2. Getting the project ID for each deployment
		// 3. Checking if the user has access to the project
	}

	c.JSON(http.StatusOK, schedules)
}

// GetSchedule retrieves a schedule by ID
func (h *ScheduleHandler) GetSchedule(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Schedule ID is required"})
		return
	}

	schedule, err := h.db.GetScheduleByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve schedule"})
		return
	}

	if schedule == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Schedule not found"})
		return
	}

	// Check user authentication using centralized auth helper
	authContext := h.authHelper.RequireAuthentication(c)
	if authContext == nil {
		// RequireAuthentication already sent the 401 response
		return
	}

	// Check if user is admin
	isAdmin := authContext.IsAdmin()

	// If user is not admin, check if they have access to the schedule's deployment project
	if !isAdmin {
		// TODO: In a real implementation, we would check if the user has access to the deployment's project
		// For now, we'll just allow access to all schedules
		// This would involve:
		// 1. Getting the deployment ID for the schedule
		// 2. Getting the project ID for the deployment
		// 3. Checking if the user has access to the project
	}

	c.JSON(http.StatusOK, schedule)
}

// CreateSchedule creates a new schedule
func (h *ScheduleHandler) CreateSchedule(c *gin.Context) {
	var schedule models.Schedule
	if err := c.ShouldBindJSON(&schedule); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid schedule data"})
		return
	}

	// Generate UUID if not provided
	if schedule.ID == "" {
		schedule.ID = uuid.New().String()
	}

	if err := h.db.CreateSchedule(c.Request.Context(), &schedule); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create schedule"})
		return
	}

	c.JSON(http.StatusCreated, schedule)
}

// UpdateSchedule updates an existing schedule
func (h *ScheduleHandler) UpdateSchedule(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Schedule ID is required"})
		return
	}

	var schedule models.Schedule
	if err := c.ShouldBindJSON(&schedule); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid schedule data"})
		return
	}

	// Make sure ID in URL matches ID in body
	schedule.ID = id

	if err := h.db.UpdateSchedule(c.Request.Context(), &schedule); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update schedule"})
		return
	}

	c.JSON(http.StatusOK, schedule)
}

// DeleteSchedule deletes a schedule
func (h *ScheduleHandler) DeleteSchedule(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Schedule ID is required"})
		return
	}

	if err := h.db.DeleteSchedule(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete schedule"})
		return
	}

	c.Status(http.StatusNoContent)
}

// ToggleSchedule activates or deactivates a schedule
func (h *ScheduleHandler) ToggleSchedule(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Schedule ID is required"})
		return
	}

	schedule, err := h.db.GetScheduleByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve schedule"})
		return
	}

	if schedule == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Schedule not found"})
		return
	}

	// Toggle active status
	schedule.Active = !schedule.Active

	if err := h.db.UpdateSchedule(c.Request.Context(), schedule); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update schedule"})
		return
	}

	c.JSON(http.StatusOK, schedule)
}

// ActivateSchedule activates a schedule
func (h *ScheduleHandler) ActivateSchedule(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Schedule ID is required"})
		return
	}

	schedule, err := h.db.GetScheduleByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve schedule"})
		return
	}

	if schedule == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Schedule not found"})
		return
	}

	// Set active status to true
	schedule.Active = true

	if err := h.db.UpdateSchedule(c.Request.Context(), schedule); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to activate schedule"})
		return
	}

	c.JSON(http.StatusOK, schedule)
}

// DeactivateSchedule deactivates a schedule
func (h *ScheduleHandler) DeactivateSchedule(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Schedule ID is required"})
		return
	}

	schedule, err := h.db.GetScheduleByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve schedule"})
		return
	}

	if schedule == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Schedule not found"})
		return
	}

	// Set active status to false
	schedule.Active = false

	if err := h.db.UpdateSchedule(c.Request.Context(), schedule); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to deactivate schedule"})
		return
	}

	c.JSON(http.StatusOK, schedule)
}
