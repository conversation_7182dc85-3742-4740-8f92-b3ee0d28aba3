# Scheduling Service Migration to Standardized Patterns

This PR updates the scheduling-service to follow the standardized patterns established for the deploy-orchestrator project:

## Changes:

1. **Shared Database Connection Pattern**
   - Created new `storage` package to replace the existing `database` package
   - Implemented database connection using shared connection pattern from `shared/db`

2. **Standardized Health Check Endpoint**
   - Replaced custom health check implementation with shared handler from `shared/handlers`
   - Added service version information to health check response

3. **Formal Database Migration Support**
   - Added proper migrations using GORM AutoMigrate in dedicated migrations.go file
   - Separated database connection from business logic for cleaner architecture

4. **Added Unit Tests**
   - Created main_test.go with health endpoint test
   - Set up test router configuration for consistent testing

5. **Improved Schedule Management**
   - Enhanced database operations for schedules
   - Added proper error handling for all operations

## Notes:
- The in-memory storage fallback is preserved for development purposes
- All API endpoints remain the same for backward compatibility
- Health check endpoint now includes version information
