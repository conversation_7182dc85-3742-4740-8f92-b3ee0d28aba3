package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/claudio/deploy-orchestrator/scheduling-service/api"
	"github.com/claudio/deploy-orchestrator/scheduling-service/config"
	"github.com/claudio/deploy-orchestrator/scheduling-service/middleware"
	"github.com/claudio/deploy-orchestrator/scheduling-service/storage"
	"github.com/claudio/deploy-orchestrator/shared/auth"
	"github.com/claudio/deploy-orchestrator/shared/gateway"
	"github.com/claudio/deploy-orchestrator/shared/monitoring"
)

const (
	serviceName = "scheduling-service"
	version     = "1.0.0"
)

func main() {
	// Load configuration from YAML and environment variables
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Create default config file if it doesn't exist
	if err := config.SaveDefaultConfig(); err != nil {
		log.Printf("Warning: Failed to save default config: %v", err)
	}

	// Initialize shared authentication manager from config
	authManager, err := auth.NewAuthManagerFromSharedConfig(&cfg.Auth)
	if err != nil {
		log.Fatalf("Failed to create auth manager: %v", err)
	}

	// Initialize appropriate Gin mode based on logging level
	if cfg.Logging.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// Initialize database connection using config
	var db *storage.Database
	var inMemoryDb *storage.InMemoryDatabase
	var useInMemory bool

	if cfg.Database.URL != "" {
		db, err = storage.NewDatabase(cfg.Database.URL)
		if err != nil {
			log.Printf("Failed to connect to database: %v, falling back to in-memory storage", err)
			useInMemory = true
		} else {
			log.Println("Successfully connected to the database")
		}
	} else {
		log.Println("Database URL not configured, using in-memory storage")
		useInMemory = true
	}

	if useInMemory {
		// Use in-memory database if no PostgreSQL URL provided or connection failed
		inMemoryDb = storage.NewInMemoryDatabase()
		db = &storage.Database{
			DB:         nil,
			InMemoryDB: inMemoryDb,
		}
		log.Println("Using in-memory database as fallback")
	}

	// Initialize monitoring system
	monitoringManager := monitoring.NewMonitoringManager("scheduling-service", version, "development")
	if !useInMemory && db != nil && db.DB != nil {
		monitoringManager.AddDatabase(db.DB, "postgres")
	}
	log.Println("Monitoring system initialized")

	// Initialize gateway registration
	zapLogger, _ := zap.NewProduction()
	gatewayClient := gateway.NewClientFromSharedConfig("workflow-service", cfg.Server.Port, &cfg.Gateway, zapLogger)
	gatewayClient.SafeRegister()
	log.Println("Gateway registration attempted")

	// Setup router
	router := gin.New()

	// Setup monitoring routes and middleware
	monitoringManager.SetupRoutes(router)
	monitoringManager.SetupMiddleware(router)

	// Use middleware
	router.Use(middleware.Logger())
	router.Use(middleware.ErrorHandler())
	router.Use(authManager.CORSMiddleware()) // Use shared CORS middleware
	router.Use(gin.Recovery())

	// Initialize handlers with database connection
	scheduleHandler := api.NewScheduleHandler(db)

	// Initialize permission middleware
	permissionMiddleware := authManager.PermissionMiddleware()

	// API routes
	v1 := router.Group("/api/v1")
	{
		// Add authentication middleware to protected routes
		if !cfg.Auth.DisableAuth {
			v1.Use(authManager.AuthMiddleware())
		} else {
			log.Println("Warning: Authentication is disabled")
		}

		// Create a group for schedules
		schedules := v1.Group("/schedules")

		// Routes that all authenticated users can access
		schedules.GET("", scheduleHandler.GetSchedules)    // Will be filtered by user's projects
		schedules.GET("/:id", scheduleHandler.GetSchedule) // Will check access in the handler

		// Routes that require specific permissions
		if !cfg.Auth.DisableAuth {
			schedules.POST("",
				permissionMiddleware.RequirePermission("schedule:create", auth.ProjectIDFromJSON("projectId")),
				scheduleHandler.CreateSchedule)
			schedules.PUT("/:id",
				permissionMiddleware.RequirePermission("schedule:update", auth.ProjectIDFromJSON("projectId")),
				scheduleHandler.UpdateSchedule)
			schedules.DELETE("/:id",
				permissionMiddleware.RequirePermission("schedule:delete", auth.ProjectIDFromJSON("projectId")),
				scheduleHandler.DeleteSchedule)
			schedules.POST("/:id/activate",
				permissionMiddleware.RequirePermission("schedule:manage", auth.ProjectIDFromJSON("projectId")),
				scheduleHandler.ActivateSchedule)
			schedules.POST("/:id/deactivate",
				permissionMiddleware.RequirePermission("schedule:manage", auth.ProjectIDFromJSON("projectId")),
				scheduleHandler.DeactivateSchedule)
			schedules.PUT("/:id/toggle",
				permissionMiddleware.RequirePermission("schedule:manage", auth.ProjectIDFromJSON("projectId")),
				scheduleHandler.ToggleSchedule)
		} else {
			// No auth - allow all operations
			schedules.POST("", scheduleHandler.CreateSchedule)
			schedules.PUT("/:id", scheduleHandler.UpdateSchedule)
			schedules.DELETE("/:id", scheduleHandler.DeleteSchedule)
			schedules.POST("/:id/activate", scheduleHandler.ActivateSchedule)
			schedules.POST("/:id/deactivate", scheduleHandler.DeactivateSchedule)
			schedules.PUT("/:id/toggle", scheduleHandler.ToggleSchedule)
		}
	}

	// Create an HTTP server with configuration
	serverAddr := fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port)
	srv := &http.Server{
		Addr:         serverAddr,
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Start the server in a goroutine
	go func() {
		log.Printf("Starting %s server on %s", serviceName, serverAddr)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutdown signal received, shutting down server...")

	// Deregister from gateway
	gatewayClient.SafeDeregister()
	log.Println("Deregistered from gateway")

	// Create a deadline for the shutdown
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(cfg.Server.ShutdownTimeout)*time.Second)
	defer cancel()

	// Shutdown the server
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	// Shutdown monitoring system
	if err := monitoringManager.Shutdown(ctx); err != nil {
		log.Printf("Error shutting down monitoring system: %v", err)
	}

	// Close database connection if it exists
	if db != nil {
		if err := db.Close(); err != nil {
			log.Printf("Error closing database connection: %v", err)
		}
	}

	log.Println("Server gracefully stopped")
}
