package config

import (
	"fmt"

	sharedConfig "github.com/claudio/deploy-orchestrator/shared/config"
)

// SchedulingServiceConfig holds the configuration for the scheduling service
type SchedulingServiceConfig struct {
	// Common configuration sections
	Database sharedConfig.DBConfig      `mapstructure:"database" yaml:"database"`
	Server   sharedConfig.ServerConfig  `mapstructure:"server" yaml:"server"`
	Logging  sharedConfig.LoggingConfig `mapstructure:"logging" yaml:"logging"`
	Tracing  sharedConfig.TracingConfig `mapstructure:"tracing" yaml:"tracing"`
	Auth     sharedConfig.AuthConfig    `mapstructure:"auth" yaml:"auth"`
	Gateway  sharedConfig.GatewayConfig `mapstructure:"gateway" yaml:"gateway"`

	// Scheduling service specific configuration
	SchedulerIntervalSec   int    `mapstructure:"scheduler_interval_sec" yaml:"scheduler_interval_sec" env:"SCHEDULER_INTERVAL_SEC" default:"60"`
	SchedulerLockTimeout   int    `mapstructure:"scheduler_lock_timeout" yaml:"scheduler_lock_timeout" env:"SCHEDULER_LOCK_TIMEOUT" default:"300"`
	MaxConcurrentJobs      int    `mapstructure:"max_concurrent_jobs" yaml:"max_concurrent_jobs" env:"MAX_CONCURRENT_JOBS" default:"10"`
	DeploymentServiceURL   string `mapstructure:"deployment_service_url" yaml:"deployment_service_url" env:"DEPLOYMENT_SERVICE_URL" default:"http://localhost:8082"`
	AdminServiceURL        string `mapstructure:"admin_service_url" yaml:"admin_service_url" env:"ADMIN_SERVICE_URL" default:"http://localhost:8080"`
	NotificationServiceURL string `mapstructure:"notification_service_url" yaml:"notification_service_url" env:"NOTIFICATION_SERVICE_URL" default:"http://localhost:8083"`
	DefaultRetryCount      int    `mapstructure:"default_retry_count" yaml:"default_retry_count" env:"DEFAULT_RETRY_COUNT" default:"3"`
	DefaultRetrySec        int    `mapstructure:"default_retry_sec" yaml:"default_retry_sec" env:"DEFAULT_RETRY_SEC" default:"300"`
}

// LoadConfig loads the scheduling service configuration from file and environment variables
func LoadConfig() (*SchedulingServiceConfig, error) {
	config := &SchedulingServiceConfig{
		Server: sharedConfig.ServerConfig{
			Port: 8084, // Default port for scheduling service
		},
		Tracing: sharedConfig.TracingConfig{
			ServiceName: "scheduling-service",
		},
	}

	if err := sharedConfig.LoadConfig(config, "scheduling-service"); err != nil {
		return nil, fmt.Errorf("failed to load scheduling service config: %w", err)
	}

	return config, nil
}

// SaveDefaultConfig saves a default configuration file if one doesn't exist
func SaveDefaultConfig() error {
	config := &SchedulingServiceConfig{
		Server: sharedConfig.ServerConfig{
			Port: 8084,
		},
		Tracing: sharedConfig.TracingConfig{
			ServiceName: "scheduling-service",
		},
	}

	return sharedConfig.SaveDefaultConfig(config, "scheduling-service")
}
