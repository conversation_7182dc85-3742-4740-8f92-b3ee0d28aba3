package storage

import (
	"context"
	"errors"
	"fmt"

	"github.com/claudio/deploy-orchestrator/shared/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// CreateSchedule creates a new schedule
func (db *Database) CreateSchedule(ctx context.Context, schedule *models.Schedule) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.CreateSchedule(ctx, schedule)
	}

	if schedule.ID == "" {
		schedule.ID = uuid.New().String()
	}

	return db.DB.WithContext(ctx).Create(schedule).Error
}

// GetSchedules retrieves all schedules
func (db *Database) GetSchedules(ctx context.Context) ([]models.Schedule, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetSchedules(ctx)
	}

	var schedules []models.Schedule

	if err := db.DB.WithContext(ctx).Find(&schedules).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve schedules: %w", err)
	}

	return schedules, nil
}

// GetActiveSchedules retrieves all active schedules
func (db *Database) GetActiveSchedules(ctx context.Context) ([]models.Schedule, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetActiveSchedules(ctx)
	}

	var schedules []models.Schedule

	if err := db.DB.WithContext(ctx).Where("active = ?", true).Find(&schedules).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve active schedules: %w", err)
	}

	return schedules, nil
}

// GetSchedulesForDeployment retrieves all schedules for a specific deployment
func (db *Database) GetSchedulesForDeployment(ctx context.Context, deploymentID string) ([]models.Schedule, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetSchedulesForDeployment(ctx, deploymentID)
	}

	var schedules []models.Schedule

	if err := db.DB.WithContext(ctx).Where("deployment_id = ?", deploymentID).Find(&schedules).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve schedules for deployment: %w", err)
	}

	return schedules, nil
}

// GetScheduleByID retrieves a specific schedule by ID
func (db *Database) GetScheduleByID(ctx context.Context, id string) (*models.Schedule, error) {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.GetScheduleByID(ctx, id)
	}

	var schedule models.Schedule
	if err := db.DB.WithContext(ctx).Where("id = ?", id).First(&schedule).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // Schedule not found
		}
		return nil, fmt.Errorf("schedule not found: %w", err)
	}

	return &schedule, nil
}

// UpdateSchedule updates an existing schedule
func (db *Database) UpdateSchedule(ctx context.Context, schedule *models.Schedule) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.UpdateSchedule(ctx, schedule)
	}

	return db.DB.WithContext(ctx).Save(schedule).Error
}

// ToggleScheduleActive toggles the active status of a schedule
func (db *Database) ToggleScheduleActive(ctx context.Context, id string, active bool) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.ToggleScheduleActive(ctx, id, active)
	}

	return db.DB.WithContext(ctx).Model(&models.Schedule{}).Where("id = ?", id).Update("active", active).Error
}

// DeleteSchedule soft-deletes a schedule
func (db *Database) DeleteSchedule(ctx context.Context, id string) error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.DeleteSchedule(ctx, id)
	}

	return db.DB.WithContext(ctx).Delete(&models.Schedule{ID: id}).Error
}

// TestConnection tests the database connection
func (db *Database) TestConnection() error {
	// Check if using in-memory database
	if db.InMemoryDB != nil {
		return db.InMemoryDB.TestConnection()
	}

	sqlDB, err := db.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}
