package storage

import (
	"context"
	"errors"
	"log"
	"sync"

	"github.com/claudio/deploy-orchestrator/shared/models"
	"github.com/google/uuid"
)

// InMemoryDatabase provides a simple in-memory implementation for testing
type InMemoryDatabase struct {
	schedules map[string]models.Schedule
	mu        sync.RWMutex // For thread-safety
}

// NewInMemoryDatabase creates a new in-memory database
func NewInMemoryDatabase() *InMemoryDatabase {
	log.Println("Using in-memory database for scheduling-service")
	return &InMemoryDatabase{
		schedules: make(map[string]models.Schedule),
	}
}

// CreateSchedule creates a new schedule in memory
func (db *InMemoryDatabase) CreateSchedule(ctx context.Context, schedule *models.Schedule) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if schedule.ID == "" {
		schedule.ID = uuid.New().String()
	}

	db.schedules[schedule.ID] = *schedule
	return nil
}

// GetSchedules retrieves all schedules from memory
func (db *InMemoryDatabase) GetSchedules(ctx context.Context) ([]models.Schedule, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	schedules := make([]models.Schedule, 0, len(db.schedules))
	for _, schedule := range db.schedules {
		schedules = append(schedules, schedule)
	}

	return schedules, nil
}

// GetActiveSchedules retrieves all active schedules from memory
func (db *InMemoryDatabase) GetActiveSchedules(ctx context.Context) ([]models.Schedule, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	var activeSchedules []models.Schedule
	for _, schedule := range db.schedules {
		if schedule.Active {
			activeSchedules = append(activeSchedules, schedule)
		}
	}

	return activeSchedules, nil
}

// GetSchedulesForDeployment retrieves all schedules for a specific deployment from memory
func (db *InMemoryDatabase) GetSchedulesForDeployment(ctx context.Context, deploymentID string) ([]models.Schedule, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	var deploymentSchedules []models.Schedule
	for _, schedule := range db.schedules {
		if schedule.DeploymentID == deploymentID {
			deploymentSchedules = append(deploymentSchedules, schedule)
		}
	}

	return deploymentSchedules, nil
}

// GetScheduleByID retrieves a specific schedule by ID from memory
func (db *InMemoryDatabase) GetScheduleByID(ctx context.Context, id string) (*models.Schedule, error) {
	db.mu.RLock()
	defer db.mu.RUnlock()

	schedule, exists := db.schedules[id]
	if !exists {
		return nil, nil // Schedule not found
	}

	return &schedule, nil
}

// UpdateSchedule updates an existing schedule in memory
func (db *InMemoryDatabase) UpdateSchedule(ctx context.Context, schedule *models.Schedule) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if _, exists := db.schedules[schedule.ID]; !exists {
		return errors.New("schedule not found")
	}

	db.schedules[schedule.ID] = *schedule
	return nil
}

// ToggleScheduleActive toggles the active status of a schedule in memory
func (db *InMemoryDatabase) ToggleScheduleActive(ctx context.Context, id string, active bool) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	schedule, exists := db.schedules[id]
	if !exists {
		return errors.New("schedule not found")
	}

	schedule.Active = active
	db.schedules[id] = schedule
	return nil
}

// DeleteSchedule removes a schedule from memory
func (db *InMemoryDatabase) DeleteSchedule(ctx context.Context, id string) error {
	db.mu.Lock()
	defer db.mu.Unlock()

	if _, exists := db.schedules[id]; !exists {
		return errors.New("schedule not found")
	}

	delete(db.schedules, id)
	return nil
}

// TestConnection for in-memory database always returns nil
func (db *InMemoryDatabase) TestConnection() error {
	return nil
}

// Close does nothing for in-memory database but exists for interface compatibility
func (db *InMemoryDatabase) Close() error {
	log.Println("Closing in-memory database (no-op)")
	return nil
}
