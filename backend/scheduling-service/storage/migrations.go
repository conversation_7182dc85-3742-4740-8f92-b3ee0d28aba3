package storage

import (
	"log"

	"github.com/claudio/deploy-orchestrator/shared/db"
	sharedModels "github.com/claudio/deploy-orchestrator/shared/models"
	"gorm.io/gorm"
)

// Database represents a connection to the database using GORM
type Database struct {
	DB         *gorm.DB
	InMemoryDB *InMemoryDatabase
}

// NewDatabase creates a new database connection using GORM
func NewDatabase(postgresURL string) (*Database, error) {
	config := db.Config{
		URL:           postgresURL,
		MaxRetries:    5,
		RetryInterval: 2,
	}

	gormDB, err := db.Connect(config)
	if err != nil {
		return nil, err
	}

	// Run migrations immediately
	if err := RunMigrations(gormDB); err != nil {
		return nil, err
	}

	return &Database{
		DB:         gormDB,
		InMemoryDB: nil,
	}, nil
}

// Close closes the database connection
func (d *Database) Close() error {
	// If using in-memory database, no need to close anything
	if d.InMemoryDB != nil {
		return nil
	}

	// Close the real database connection
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// RunMigrations performs all necessary database migrations
func RunMigrations(grm *gorm.DB) error {
	log.Println("Running scheduling-service database migrations...")

	// Use the shared RunMigrations function to run migrations for all models
	return db.RunMigrations(
		grm,
		&sharedModels.Schedule{},
	)
}

// SeedTestData populates the database with initial test data
// Only for testing purposes
func SeedTestData(db *gorm.DB) error {
	log.Println("Seeding scheduling database with test data...")

	// Create sample schedules
	testSchedules := []sharedModels.Schedule{
		{
			ID:             "schedule-test-1",
			Name:           "Test Schedule 1",
			DeploymentID:   "deploy-test-1",
			CronExpression: "0 0 * * *",
			Active:         true,
		},
		{
			ID:             "schedule-test-2",
			Name:           "Test Schedule 2",
			DeploymentID:   "deploy-test-2",
			CronExpression: "0 12 * * *",
			Active:         false,
		},
	}

	// Add the test schedules to the database
	for _, schedule := range testSchedules {
		if result := db.Create(&schedule); result.Error != nil {
			return result.Error
		}
	}

	return nil
}
