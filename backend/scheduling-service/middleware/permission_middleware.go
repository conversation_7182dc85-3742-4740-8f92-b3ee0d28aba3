package middleware

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// ProjectAccessChecker is an interface for checking if a user has access to a project
type ProjectAccessChecker interface {
	CheckUserHasProjectAccess(userID, projectID string) (bool, error)
	GetDeploymentProjectID(deploymentID string) (string, error)
}

// HTTPProjectAccessChecker implements ProjectAccessChecker by making HTTP requests to the admin service
type HTTPProjectAccessChecker struct {
	AdminServiceURL   string
	DeploymentService string
	Client            *http.Client
}

// NewHTTPProjectAccessC<PERSON><PERSON> creates a new HTTP-based project access checker
func NewHTTPProjectAccessChecker(adminServiceURL, deploymentServiceURL string) *HTTPProjectAccessChecker {
	return &HTTPProjectAccessChecker{
		AdminServiceURL:   adminServiceURL,
		DeploymentService: deploymentServiceURL,
		Client:            &http.Client{},
	}
}

// CheckUserHasProjectAccess checks if a user has access to a project by making an HTTP request to the admin service
func (c *HTTPProjectAccessChecker) CheckUserHasProjectAccess(userID, projectID string) (bool, error) {
	url := fmt.Sprintf("%s/api/v1/users/%s/projects/%s/access", c.AdminServiceURL, userID, projectID)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return false, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.Client.Do(req)
	if err != nil {
		return false, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		return true, nil
	} else if resp.StatusCode == http.StatusForbidden {
		return false, nil
	}

	return false, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
}

// GetDeploymentProjectID gets the project ID for a deployment by making an HTTP request to the deployment service
func (c *HTTPProjectAccessChecker) GetDeploymentProjectID(deploymentID string) (string, error) {
	url := fmt.Sprintf("%s/api/v1/deployments/%s", c.DeploymentService, deploymentID)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.Client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var deployment struct {
		ProjectID string `json:"projectId"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&deployment); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	return deployment.ProjectID, nil
}

// ScheduleAccessMiddleware creates a middleware that checks if a user has access to a schedule
// by checking if they have access to the project associated with the deployment
func ScheduleAccessMiddleware(checker ProjectAccessChecker) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from context (set by auth middleware)
		userID, exists := c.Get("userID")
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			return
		}

		// Check if user is admin (admins bypass access checks)
		roles, exists := c.Get("roles")
		if exists {
			rolesList, ok := roles.([]string)
			if ok {
				for _, role := range rolesList {
					if role == "admin" {
						c.Next()
						return
					}
				}
			}
		}

		// Get deployment ID from request
		var deploymentID string

		// First try to get from URL parameter for schedule endpoints
		scheduleID := c.Param("id")
		if scheduleID != "" {
			// TODO: Get the deployment ID from the schedule ID
			// This would require a database lookup
			// For now, we'll assume the schedule ID is the deployment ID
			deploymentID = scheduleID
		}

		// If not found, try to get from query parameter
		if deploymentID == "" {
			deploymentID = c.Query("deploymentId")
		}

		// If still not found, try to get from request body for POST/PUT requests
		if deploymentID == "" && (c.Request.Method == "POST" || c.Request.Method == "PUT") {
			// Read the request body
			var requestBody map[string]interface{}
			if err := c.ShouldBindJSON(&requestBody); err == nil {
				// Check if deploymentId is in the request body
				if id, ok := requestBody["deploymentId"].(string); ok {
					deploymentID = id
				}
				// Reset the request body for later use
				bodyBytes, _ := json.Marshal(requestBody)
				c.Request.Body = &readCloser{strings.NewReader(string(bodyBytes))}
			}
		}

		// If no deployment ID found, let the handler deal with it
		if deploymentID == "" {
			c.Next()
			return
		}

		// Get the project ID for the deployment
		projectID, err := checker.GetDeploymentProjectID(deploymentID)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to get project ID for deployment",
				"details": err.Error(),
			})
			return
		}

		// If no project ID found, let the handler deal with it
		if projectID == "" {
			c.Next()
			return
		}

		// Check if user has access to the project
		hasAccess, err := checker.CheckUserHasProjectAccess(userID.(string), projectID)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to check project access",
				"details": err.Error(),
			})
			return
		}

		if !hasAccess {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"error":     "Access denied",
				"projectId": projectID,
			})
			return
		}

		c.Next()
	}
}

// readCloser is a helper type that implements io.ReadCloser
type readCloser struct {
	*strings.Reader
}

func (r *readCloser) Close() error {
	return nil
}
