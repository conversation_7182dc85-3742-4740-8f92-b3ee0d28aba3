# Foreign Key Constraint Analysis

## Issue Description

Environment creation is failing with:
```
ERROR: insert or update on table "environment_configs" violates foreign key constraint "fk_environment_configs_project" (SQLSTATE 23503)
```

## Current Payload

The API request payload is now correctly structured:

```json
{
    "projectId": "f0768aa2-aec0-4864-87bc-62e8f39921de",
    "name": "uat",
    "type": "kubernetes",
    "provider": {
        "type": "Red Hat OpenShift container platform provider",
        "config": {
            "authMethod": "username_password",
            "project": "jxp-email-sender",
            "credentials": {
                "password": "{{secret:sec_1748345528575882000:value}}",
                "username": "c325005"
            },
            "extra": {
                "api_url": "https://api.jxp1-qa-z2.unicreditgroup.eu:6443",
                "auth_method": "username_password",
                "enable_routes": true,
                "timeout": 30
            }
        }
    },
    ...
}
```

## Root Cause Analysis

The foreign key constraint error indicates that the `projectId` "f0768aa2-aec0-4864-87bc-62e8f39921de" does not exist in the `projects` table, or there's a database relationship issue.

### Possible Causes

#### 1. **Project Does Not Exist**
- The project ID is not present in the `projects` table
- Project was deleted but still appears in the UI cache
- Project ID format mismatch

#### 2. **Database Schema Issues**
- Foreign key constraint is incorrectly configured
- Different database connections between services
- Table name or column name mismatch

#### 3. **Soft Delete Issues**
- Project exists but is soft-deleted (`deleted_at` is not null)
- Foreign key constraint doesn't account for soft deletes

#### 4. **Service Communication Issues**
- Admin service and environment service using different databases
- Database synchronization problems
- Transaction isolation issues

## Debugging Steps

### Step 1: Run Debug Script

Copy and paste the content of `debug_foreign_key_issue.js` into the browser console on the environment configuration page. This will:

1. ✅ Check current project selection
2. ✅ Validate project exists via API
3. ✅ List all available projects
4. ✅ Check environment service health
5. ✅ Provide detailed analysis

### Step 2: Manual API Testing

#### Check if project exists:
```bash
curl -X GET "http://localhost:8080/api/v1/admin-service/projects/f0768aa2-aec0-4864-87bc-62e8f39921de" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### List all projects:
```bash
curl -X GET "http://localhost:8080/api/v1/admin-service/projects" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Check project access:
```bash
curl -X GET "http://localhost:8080/api/v1/admin-service/users/USER_ID/projects/f0768aa2-aec0-4864-87bc-62e8f39921de/access" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Step 3: Database Verification

If you have database access, check:

```sql
-- Check if project exists
SELECT id, name, description, is_active, created_at, deleted_at 
FROM projects 
WHERE id = 'f0768aa2-aec0-4864-87bc-62e8f39921de';

-- Check foreign key constraint
SELECT 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_name = 'environment_configs'
  AND tc.constraint_name = 'fk_environment_configs_project';

-- Check all projects
SELECT id, name, is_active, deleted_at FROM projects ORDER BY created_at DESC;
```

## Potential Solutions

### Solution 1: Project Selection Issue

**If the project doesn't exist:**

1. **Select a valid project** in the header project selector
2. **Refresh the project list** to ensure it's up to date
3. **Check project permissions** - user might not have access

**Implementation:**
```javascript
// In browser console
const projectService = angular.element(document.querySelector('app-environment-config')).componentInstance.projectService;
projectService.refreshProjects();
```

### Solution 2: Database Inconsistency

**If project exists in admin service but not in environment service:**

1. **Check database connections** - ensure both services use the same database
2. **Verify table schemas** - ensure foreign key constraints are correct
3. **Check for soft deletes** - project might be soft-deleted

**Backend fix needed:**
```go
// In environment service, ensure proper foreign key handling
type EnvironmentConfig struct {
    ID        string  `gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
    ProjectID string  `gorm:"type:uuid;not null;constraint:OnDelete:CASCADE"`
    Project   Project `gorm:"foreignKey:ProjectID;references:ID"`
    // ... other fields
}
```

### Solution 3: Frontend Validation

**Add project validation before submission:**

The frontend now includes project validation that will:
1. Check if project is selected
2. Validate project exists via API
3. Verify user has access to project
4. Show clear error messages

### Solution 4: Backend Error Handling

**Improve backend error messages:**

```go
// In environment service
if err := db.Create(&environmentConfig).Error; err != nil {
    if strings.Contains(err.Error(), "fk_environment_configs_project") {
        return fmt.Errorf("project with ID %s does not exist or is not accessible", req.ProjectID)
    }
    return fmt.Errorf("failed to create environment: %w", err)
}
```

## Testing Instructions

### 1. **Run Debug Script**
1. Navigate to environment configuration page
2. Open browser console (F12)
3. Copy and paste `debug_foreign_key_issue.js`
4. Review the output for specific issues

### 2. **Test Project Selection**
1. Click on project selector in header
2. Select a different project
3. Try creating environment again
4. Check console for project validation messages

### 3. **Verify API Responses**
1. Check network tab for API calls
2. Verify project API returns 200 status
3. Confirm project ID matches between frontend and backend

## Expected Resolution

### If Project Doesn't Exist:
- Debug script will show "PROJECT NOT FOUND"
- Solution: Select a valid project from the available list
- Frontend validation will prevent submission

### If Database Issue:
- Debug script will show project exists but constraint fails
- Solution: Backend database schema fix needed
- Contact backend developer for database investigation

### If Permission Issue:
- Debug script will show access denied
- Solution: Contact administrator for project permissions
- User needs to be added to project group

## Files Modified

1. **`frontend/deploy-orchestrator/src/app/components/environment-config/environment-config.component.ts`**
   - Added project validation before environment creation
   - Enhanced error handling and debugging
   - Improved project ID management

2. **`debug_foreign_key_issue.js`**
   - Comprehensive debugging script for browser console
   - Tests project existence, access, and API health
   - Provides detailed analysis and solutions

The foreign key constraint issue should be resolved by ensuring the project ID exists and the user has proper access to it.
