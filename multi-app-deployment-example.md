# Multi-App Deployment API Usage

## Create Multi-App Deployment

```bash
curl -X POST "http://localhost:8085/api/v1/deployments/multi-app" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <valid-jwt-token>" \
  -d '{
    "projectId": "my-project",
    "environmentId": "production",
    "applications": [
      {
        "applicationId": "frontend-app",
        "version": "1.2.0",
        "configuration": {
          "replicas": 3,
          "resources": {
            "cpu": "500m",
            "memory": "512Mi"
          }
        },
        "parameters": {
          "env": "production",
          "debug": false
        }
      },
      {
        "applicationId": "backend-api",
        "version": "2.1.0",
        "configuration": {
          "replicas": 2,
          "resources": {
            "cpu": "1000m",
            "memory": "1Gi"
          }
        },
        "parameters": {
          "env": "production",
          "database_url": "postgres://prod-db:5432/myapp"
        }
      }
    ],
    "strategy": "parallel",
    "workflowId": "standard-deployment-workflow"
  }'
```

## Get Multi-App Deployment Status

```bash
curl -X GET "http://localhost:8085/api/v1/deployments/multi-app/{deployment-id}" \
  -H "Authorization: Bearer <valid-jwt-token>"
```

## Start Multi-App Deployment

```bash
curl -X POST "http://localhost:8085/api/v1/deployments/multi-app/{deployment-id}/start" \
  -H "Authorization: Bearer <valid-jwt-token>"
```

## Response Format

```json
{
  "id": "uuid",
  "projectId": "my-project",
  "environmentId": "production",
  "strategy": "parallel",
  "status": "pending",
  "workflowId": "standard-deployment-workflow",
  "executionId": "workflow-execution-uuid",
  "applications": [
    {
      "id": "uuid",
      "applicationId": "frontend-app",
      "version": "1.2.0",
      "status": "pending",
      "configuration": {...},
      "parameters": {...}
    }
  ],
  "createdAt": "2025-06-07T20:57:27Z",
  "updatedAt": "2025-06-07T20:57:27Z"
}
```

## Deployment Strategies

- **parallel**: Deploy all applications simultaneously
- **sequential**: Deploy applications one after another

## Status Values

- **pending**: Deployment created but not started
- **running**: Deployment in progress
- **completed**: All applications deployed successfully
- **failed**: One or more applications failed to deploy
- **cancelled**: Deployment was cancelled
