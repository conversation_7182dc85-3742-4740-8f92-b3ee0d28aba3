version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: deploy-orchestrator-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: deployorchestrator
    volumes:
      - postgres-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  deployment-service:
    build:
      context: ./backend
      dockerfile: deployment-service/Dockerfile
    container_name: deployment-service
    environment:
      - PORT=8081
      - POSTGRES_URL=********************************************/deployorchestrator?sslmode=disable
      - GIN_MODE=release
    ports:
      - "8081:8081"
    depends_on:
      postgres:
        condition: service_healthy

  scheduling-service:
    build:
      context: ./backend
      dockerfile: scheduling-service/Dockerfile
    container_name: scheduling-service
    environment:
      - PORT=8082
      - POSTGRES_URL=********************************************/deployorchestrator?sslmode=disable
      - GIN_MODE=release
    ports:
      - "8082:8082"
    depends_on:
      postgres:
        condition: service_healthy

  notification-service:
    build:
      context: ./backend
      dockerfile: notification-service/Dockerfile
    container_name: notification-service
    environment:
      - PORT=8083
      - POSTGRES_URL=********************************************/deployorchestrator?sslmode=disable
      - GIN_MODE=release
    ports:
      - "8083:8083"
    depends_on:
      postgres:
        condition: service_healthy

  integration-service:
    build:
      context: ./backend
      dockerfile: integration-service/Dockerfile
    container_name: integration-service
    environment:
      - PORT=8084
      - POSTGRES_URL=********************************************/deployorchestrator?sslmode=disable
      - GIN_MODE=release
    ports:
      - "8084:8084"
    depends_on:
      postgres:
        condition: service_healthy

  audit-service:
    build:
      context: ./backend
      dockerfile: audit-service/Dockerfile
    container_name: audit-service
    environment:
      - PORT=8085
      - POSTGRES_URL=********************************************/deployorchestrator?sslmode=disable
      - GIN_MODE=release
    ports:
      - "8085:8085"
    depends_on:
      postgres:
        condition: service_healthy

volumes:
  postgres-data:
