global:
  smtp_smarthost: 'smtp.example.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password_file: '/etc/alertmanager/smtp_password'
  
  # Slack webhook URL for general alerts
  slack_api_url_file: '/etc/alertmanager/slack_webhook_url'

# Templates for alert formatting
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Routing configuration
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  
  routes:
    # Critical security alerts - immediate notification
    - match:
        severity: critical
        category: security
      receiver: 'security-team'
      group_wait: 0s
      repeat_interval: 5m
      
    # Secret mapping specific alerts
    - match:
        component: secrets
      receiver: 'secrets-team'
      group_interval: 5m
      repeat_interval: 30m
      
    # Performance alerts
    - match:
        category: performance
      receiver: 'performance-team'
      group_interval: 2m
      repeat_interval: 15m
      
    # Database alerts
    - match:
        component: database
      receiver: 'database-team'
      group_interval: 1m
      repeat_interval: 10m
      
    # Business logic alerts
    - match:
        category: business
      receiver: 'business-team'
      group_interval: 10m
      repeat_interval: 2h

# Inhibition rules to prevent alert spam
inhibit_rules:
  # Inhibit any warning if the same alert is already critical
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'cluster', 'service']
    
  # Inhibit secret mapping errors if service is down
  - source_match:
      alertname: 'ServiceDown'
    target_match:
      component: 'secrets'
    equal: ['service']

# Receiver configurations
receivers:
  # Default receiver
  - name: 'default'
    email_configs:
      - to: '<EMAIL>'
        subject: '[Deploy Orchestrator] {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          {{ end }}
    
    slack_configs:
      - channel: '#deploy-orchestrator-alerts'
        title: '[{{ .Status | toUpper }}] {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Service:* {{ .Labels.service }}
          {{ if .Annotations.runbook_url }}*Runbook:* {{ .Annotations.runbook_url }}{{ end }}
          {{ end }}
        send_resolved: true

  # Security team - critical security alerts
  - name: 'security-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '[SECURITY ALERT] {{ .GroupLabels.alertname }}'
        body: |
          🚨 SECURITY ALERT 🚨
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Time: {{ .StartsAt }}
          
          Labels:
          {{ range .Labels.SortedPairs }}  {{ .Name }}: {{ .Value }}
          {{ end }}
          
          {{ if .Annotations.runbook_url }}Runbook: {{ .Annotations.runbook_url }}{{ end }}
          {{ end }}
        headers:
          Priority: 'high'
    
    slack_configs:
      - channel: '#security-alerts'
        title: '🚨 [SECURITY] {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *User:* {{ .Labels.user_id }}
          *Project:* {{ .Labels.project_id }}
          *Source IP:* {{ .Labels.source_ip }}
          {{ if .Annotations.runbook_url }}*Runbook:* {{ .Annotations.runbook_url }}{{ end }}
          {{ end }}
        send_resolved: true
        color: 'danger'

  # Secrets team - secret mapping related alerts
  - name: 'secrets-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '[Secrets] {{ .GroupLabels.alertname }}'
        body: |
          Secret Management Alert
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Project: {{ .Labels.project_id }}
          Template: {{ .Labels.template_id }}
          
          {{ if .Annotations.runbook_url }}Runbook: {{ .Annotations.runbook_url }}{{ end }}
          {{ end }}
    
    slack_configs:
      - channel: '#secrets-alerts'
        title: '🔐 [Secrets] {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Project:* {{ .Labels.project_id }}
          {{ if .Labels.template_id }}*Template:* {{ .Labels.template_id }}{{ end }}
          {{ if .Annotations.runbook_url }}*Runbook:* {{ .Annotations.runbook_url }}{{ end }}
          {{ end }}
        send_resolved: true

  # Performance team
  - name: 'performance-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '[Performance] {{ .GroupLabels.alertname }}'
        body: |
          Performance Alert
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.service }}
          
          {{ if .Annotations.runbook_url }}Runbook: {{ .Annotations.runbook_url }}{{ end }}
          {{ end }}
    
    slack_configs:
      - channel: '#performance-alerts'
        title: '⚡ [Performance] {{ .GroupLabels.alertname }}'
        send_resolved: true

  # Database team
  - name: 'database-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '[Database] {{ .GroupLabels.alertname }}'
        body: |
          Database Alert
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Database: {{ .Labels.database }}
          
          {{ if .Annotations.runbook_url }}Runbook: {{ .Annotations.runbook_url }}{{ end }}
          {{ end }}
    
    slack_configs:
      - channel: '#database-alerts'
        title: '🗄️ [Database] {{ .GroupLabels.alertname }}'
        send_resolved: true

  # Business team
  - name: 'business-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '[Business Metrics] {{ .GroupLabels.alertname }}'
        body: |
          Business Metrics Alert
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          
          {{ if .Annotations.runbook_url }}Runbook: {{ .Annotations.runbook_url }}{{ end }}
          {{ end }}
    
    slack_configs:
      - channel: '#business-metrics'
        title: '📊 [Business] {{ .GroupLabels.alertname }}'
        send_resolved: true

  # PagerDuty integration for critical alerts
  - name: 'pagerduty'
    pagerduty_configs:
      - service_key_file: '/etc/alertmanager/pagerduty_service_key'
        description: '{{ .GroupLabels.alertname }}: {{ .Annotations.summary }}'
        details:
          firing: '{{ .Alerts.Firing | len }}'
          resolved: '{{ .Alerts.Resolved | len }}'
          service: '{{ .GroupLabels.service }}'
          severity: '{{ .GroupLabels.severity }}'
