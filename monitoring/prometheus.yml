global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Deploy Orchestrator Services
  - job_name: 'deploy-orchestrator-admin'
    static_configs:
      - targets: ['admin-service:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'deploy-orchestrator-deployment'
    static_configs:
      - targets: ['deployment-service:8081']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'deploy-orchestrator-scheduling'
    static_configs:
      - targets: ['scheduling-service:8082']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'deploy-orchestrator-integration'
    static_configs:
      - targets: ['integration-service:8083']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'deploy-orchestrator-notification'
    static_configs:
      - targets: ['notification-service:8084']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'deploy-orchestrator-audit'
    static_configs:
      - targets: ['audit-service:8085']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Monitoring Dashboard
  - job_name: 'monitoring-dashboard'
    static_configs:
      - targets: ['monitoring-dashboard:9090']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s
