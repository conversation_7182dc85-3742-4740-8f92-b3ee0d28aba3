{"dashboard": {"id": null, "title": "Secret Mapping Monitoring", "tags": ["deploy-orchestrator", "secrets", "security"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Secret Retrieval Rate", "type": "stat", "targets": [{"expr": "sum(rate(secret_retrieval_total[5m]))", "legendFormat": "Retrievals/sec"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Secret Retrieval Success Rate", "type": "stat", "targets": [{"expr": "sum(rate(secret_retrieval_total{status=\"success\"}[5m])) / sum(rate(secret_retrieval_total[5m])) * 100", "legendFormat": "Success Rate"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}, "unit": "percent", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Unauthorized Access Attempts", "type": "stat", "targets": [{"expr": "sum(increase(unauthorized_secret_access_attempts_total[1h]))", "legendFormat": "Attempts"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Secret Mapping Validation Rate", "type": "stat", "targets": [{"expr": "sum(rate(secret_mapping_validation_total[5m]))", "legendFormat": "Validations/sec"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 20}]}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "Secret Retrieval Duration", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, sum(rate(secret_retrieval_duration_seconds_bucket[5m])) by (le))", "legendFormat": "50th percentile"}, {"expr": "histogram_quantile(0.95, sum(rate(secret_retrieval_duration_seconds_bucket[5m])) by (le))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.99, sum(rate(secret_retrieval_duration_seconds_bucket[5m])) by (le))", "legendFormat": "99th percentile"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 6, "title": "Secret Mapping Errors by Type", "type": "timeseries", "targets": [{"expr": "sum(rate(secret_mapping_errors_total[5m])) by (error_type)", "legendFormat": "{{error_type}}"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 7, "title": "Workflow Secret Injections", "type": "timeseries", "targets": [{"expr": "sum(rate(workflow_secret_injections_total{status=\"success\"}[5m]))", "legendFormat": "Successful Injections"}, {"expr": "sum(rate(workflow_secret_injections_total{status=\"error\"}[5m]))", "legendFormat": "Failed Injections"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps", "min": 0}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 8, "title": "Secret Usage by Project", "type": "piechart", "targets": [{"expr": "sum(increase(secret_retrieval_total[1h])) by (project_id)", "legendFormat": "{{project_id}}"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 9, "title": "Security Events Timeline", "type": "timeseries", "targets": [{"expr": "increase(unauthorized_secret_access_attempts_total[5m])", "legendFormat": "Unauthorized Access"}, {"expr": "increase(cross_project_access_attempts_total[5m])", "legendFormat": "Cross-Project Access"}, {"expr": "increase(expired_secret_access_attempts_total[5m])", "legendFormat": "Expired Secret Access"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short", "min": 0}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}, {"id": 10, "title": "Template Executions with Secrets", "type": "table", "targets": [{"expr": "sum(increase(template_executions_with_secrets_total[1h])) by (template_id, project_id, environment)", "format": "table", "instant": true}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}}], "templating": {"list": [{"name": "project", "type": "query", "query": "label_values(secret_retrieval_total, project_id)", "refresh": 1, "includeAll": true, "allValue": ".*"}, {"name": "service", "type": "query", "query": "label_values(secret_retrieval_total, service)", "refresh": 1, "includeAll": true, "allValue": ".*"}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "Prometheus", "expr": "changes(app_info[5m]) > 0", "titleFormat": "Deployment", "textFormat": "Service {{service}} version {{version}} deployed"}]}}}