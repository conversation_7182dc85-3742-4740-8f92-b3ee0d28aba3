# Prometheus configuration for Deploy Orchestrator
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'deploy-orchestrator'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

# Scrape configuration
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Secrets Service
  - job_name: 'secrets-service'
    static_configs:
      - targets: ['secrets-service:8080']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s
    params:
      format: ['prometheus']

  # Workflow Service
  - job_name: 'workflow-service'
    static_configs:
      - targets: ['workflow-service:8081']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # Admin Service
  - job_name: 'admin-service'
    static_configs:
      - targets: ['admin-service:8082']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # Gateway Service
  - job_name: 'gateway-service'
    static_configs:
      - targets: ['gateway-service:8083']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # Integration Service
  - job_name: 'integration-service'
    static_configs:
      - targets: ['integration-service:8084']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # Frontend (if serving metrics)
  - job_name: 'frontend'
    static_configs:
      - targets: ['frontend:4200']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Node Exporter (system metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

  # PostgreSQL Exporter
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 15s

  # Redis Exporter (if using Redis for caching)
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s

  # Nginx Exporter (if using Nginx as reverse proxy)
  - job_name: 'nginx-exporter'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 15s

  # Blackbox Exporter (for endpoint monitoring)
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://deploy-orchestrator.example.com/health
        - https://deploy-orchestrator.example.com/api/v1/health
        - https://deploy-orchestrator.example.com/api/v1/secrets/health
        - https://deploy-orchestrator.example.com/api/v1/workflows/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # Kubernetes pods (if running on Kubernetes)
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)
      - source_labels: [__meta_kubernetes_namespace]
        action: replace
        target_label: kubernetes_namespace
      - source_labels: [__meta_kubernetes_pod_name]
        action: replace
        target_label: kubernetes_pod_name

# Remote write configuration (for long-term storage)
remote_write:
  - url: "https://prometheus-remote-write.example.com/api/v1/write"
    basic_auth:
      username: "deploy-orchestrator"
      password_file: "/etc/prometheus/remote-write-password"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500

# Storage configuration
storage:
  tsdb:
    retention.time: 30d
    retention.size: 50GB
    wal-compression: true
