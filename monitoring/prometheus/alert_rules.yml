groups:
  # Secret Mapping Security Alerts
  - name: secret_mapping_security
    rules:
      - alert: UnauthorizedSecretAccess
        expr: increase(unauthorized_secret_access_attempts_total[5m]) > 0
        for: 0m
        labels:
          severity: critical
          component: secrets
          category: security
        annotations:
          summary: "Unauthorized secret access attempt detected"
          description: "User {{ $labels.user_id }} attempted unauthorized access to secrets in project {{ $labels.project_id }} from IP {{ $labels.source_ip }}"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/security/unauthorized-access"

      - alert: CrossProjectAccessAttempt
        expr: increase(cross_project_access_attempts_total[5m]) > 0
        for: 0m
        labels:
          severity: critical
          component: secrets
          category: security
        annotations:
          summary: "Cross-project secret access attempt detected"
          description: "User {{ $labels.user_id }} from project {{ $labels.source_project }} attempted to access secrets in project {{ $labels.target_project }}"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/security/cross-project-access"

      - alert: HighSecretMappingErrorRate
        expr: rate(secret_mapping_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          component: secrets
          category: performance
        annotations:
          summary: "High secret mapping error rate"
          description: "Secret mapping error rate is {{ $value }} errors/sec for project {{ $labels.project_id }}"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/performance/secret-mapping-errors"

      - alert: ExpiredSecretAccessAttempts
        expr: increase(expired_secret_access_attempts_total[10m]) > 5
        for: 1m
        labels:
          severity: warning
          component: secrets
          category: security
        annotations:
          summary: "Multiple expired secret access attempts"
          description: "{{ $value }} attempts to access expired secrets in project {{ $labels.project_id }}"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/security/expired-secrets"

  # Secret Mapping Performance Alerts
  - name: secret_mapping_performance
    rules:
      - alert: SlowSecretRetrieval
        expr: histogram_quantile(0.95, rate(secret_retrieval_duration_seconds_bucket[5m])) > 1.0
        for: 3m
        labels:
          severity: warning
          component: secrets
          category: performance
        annotations:
          summary: "Slow secret retrieval performance"
          description: "95th percentile secret retrieval time is {{ $value }}s for project {{ $labels.project_id }}"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/performance/slow-secret-retrieval"

      - alert: SecretRetrievalFailureRate
        expr: rate(secret_retrieval_total{status="error"}[5m]) / rate(secret_retrieval_total[5m]) > 0.05
        for: 2m
        labels:
          severity: critical
          component: secrets
          category: reliability
        annotations:
          summary: "High secret retrieval failure rate"
          description: "Secret retrieval failure rate is {{ $value | humanizePercentage }} for project {{ $labels.project_id }}"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/reliability/secret-retrieval-failures"

      - alert: WorkflowSecretInjectionFailures
        expr: increase(workflow_secret_injections_total{status="error"}[10m]) > 3
        for: 1m
        labels:
          severity: warning
          component: workflows
          category: reliability
        annotations:
          summary: "Workflow secret injection failures"
          description: "{{ $value }} secret injection failures for workflow {{ $labels.workflow_id }} in project {{ $labels.project_id }}"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/reliability/workflow-secret-injection"

  # Service Health Alerts
  - name: service_health
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
          category: availability
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} has been down for more than 1 minute"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/availability/service-down"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status_code=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 3m
        labels:
          severity: warning
          category: reliability
        annotations:
          summary: "High error rate for {{ $labels.service }}"
          description: "Error rate is {{ $value | humanizePercentage }} for service {{ $labels.service }}"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/reliability/high-error-rate"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2.0
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "High response time for {{ $labels.service }}"
          description: "95th percentile response time is {{ $value }}s for service {{ $labels.service }}"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/performance/high-response-time"

  # Database Alerts
  - name: database_health
    rules:
      - alert: DatabaseConnectionPoolExhausted
        expr: db_connections_in_use / db_connections_open > 0.9
        for: 2m
        labels:
          severity: critical
          component: database
          category: performance
        annotations:
          summary: "Database connection pool nearly exhausted"
          description: "Database connection pool usage is {{ $value | humanizePercentage }} for {{ $labels.service }}"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/database/connection-pool"

      - alert: SlowDatabaseQueries
        expr: histogram_quantile(0.95, rate(db_query_duration_seconds_bucket[5m])) > 5.0
        for: 3m
        labels:
          severity: warning
          component: database
          category: performance
        annotations:
          summary: "Slow database queries detected"
          description: "95th percentile query time is {{ $value }}s for {{ $labels.operation }} on {{ $labels.database }}"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/database/slow-queries"

      - alert: DatabaseQueryFailureRate
        expr: rate(db_queries_total{status="error"}[5m]) / rate(db_queries_total[5m]) > 0.02
        for: 2m
        labels:
          severity: critical
          component: database
          category: reliability
        annotations:
          summary: "High database query failure rate"
          description: "Database query failure rate is {{ $value | humanizePercentage }} for {{ $labels.service }}"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/database/query-failures"

  # Resource Alerts
  - name: resource_usage
    rules:
      - alert: HighMemoryUsage
        expr: app_memory_usage_bytes{type="alloc"} / (1024*1024*1024) > 2.0
        for: 5m
        labels:
          severity: warning
          category: resources
        annotations:
          summary: "High memory usage for {{ $labels.service }}"
          description: "Memory usage is {{ $value }}GB for service {{ $labels.service }}"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/resources/high-memory"

      - alert: HighGoroutineCount
        expr: app_goroutines > 10000
        for: 5m
        labels:
          severity: warning
          category: resources
        annotations:
          summary: "High goroutine count for {{ $labels.service }}"
          description: "Goroutine count is {{ $value }} for service {{ $labels.service }}"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/resources/high-goroutines"

  # Business Logic Alerts
  - name: business_metrics
    rules:
      - alert: LowSecretMappingValidationSuccess
        expr: rate(secret_mapping_validation_total{status="success"}[10m]) / rate(secret_mapping_validation_total[10m]) < 0.95
        for: 5m
        labels:
          severity: warning
          component: secrets
          category: business
        annotations:
          summary: "Low secret mapping validation success rate"
          description: "Secret mapping validation success rate is {{ $value | humanizePercentage }} for template {{ $labels.template_id }}"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/business/low-validation-success"

      - alert: NoWorkflowExecutionsWithSecrets
        expr: increase(template_executions_with_secrets_total[1h]) == 0
        for: 30m
        labels:
          severity: info
          component: workflows
          category: business
        annotations:
          summary: "No workflow executions with secrets in the last hour"
          description: "No template executions using secret mapping have occurred in the last hour"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/business/no-secret-executions"

      - alert: HighSecretRotationRate
        expr: increase(secret_rotation_events_total[1h]) > 50
        for: 0m
        labels:
          severity: info
          component: secrets
          category: business
        annotations:
          summary: "High secret rotation activity"
          description: "{{ $value }} secret rotation events in the last hour for project {{ $labels.project_id }}"
          runbook_url: "https://docs.deploy-orchestrator.com/runbooks/business/high-rotation-rate"
