'-': 0s
access_token_expiry_minutes: 0
auth:
    jwt_secret: default-secret-key-change-in-production
    jwt_expiration_minutes: 1440
    access_token_expiry: ""
    refresh_token_expiry: ""
    admin_service_url: ""
    api_key_header: ""
    disable: false
database:
    db_url: ""
    db_max_retries: 0
    db_retry_interval: 0
    db_max_open_conns: 0
    db_max_idle_conns: 0
    db_query_timeout: 0
    db_log_level: ""
default_admin:
    username: ""
    password: ""
    email: ""
identity_providers:
    oidc:
        enabled: false
        issuer_url: https://accounts.google.com
        client_id: your-client-id
        client_secret: your-client-secret
        redirect_url: http://localhost:8080/api/auth/callback
        scopes: openid profile email
        groups_claim: groups
        username_claim: preferred_username
        email_claim: email
    saml:
        enabled: false
        entity_id: http://localhost:8080/saml/metadata
        metadata_url: ""
        acs_url: http://localhost:8080/saml/acs
        sp_certificate: ""
        sp_private_key: ""
        groups_attribute: groups
    ldap:
        enabled: false
        url: ldap://localhost:389
        bind_dn: cn=admin,dc=example,dc=org
        bind_password: admin
        base_dn: dc=example,dc=org
        user_filter: (uid=%s)
        group_filter: (member=%s)
        groups_attr: memberOf
        use_ssl: false
        start_tls: false
        insecure_skip: false
logging:
    log_level: ""
    log_format: ""
    log_output: ""
    log_time_format: ""
refresh_token_expiry_minutes: 0
server:
    server_port: 8080
    server_host: 0.0.0.0
    server_read_timeout: 0
    server_write_timeout: 0
    server_shutdown_timeout: 0
    server_trusted_proxies: ""
    server_tls_cert_file: ""
    server_tls_key_file: ""
service:
    name: admin-service
    version: 1.0.0
tracing:
    tracing_enabled: false
    tracing_service_name: admin-service
    tracing_endpoint: ""
