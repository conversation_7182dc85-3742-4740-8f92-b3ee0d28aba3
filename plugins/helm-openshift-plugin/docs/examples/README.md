# Examples Directory

This directory contains practical examples and templates for using the Helm OpenShift Deploy Plugin.

## 📁 Directory Structure

```
examples/
├── README.md                           # This file
├── workflow-template.json              # Complete workflow example
├── environment-promotion.json          # Environment promotion workflow
├── basic-deployment/                   # Basic deployment examples
│   ├── simple-app.yaml                # Simple application deployment
│   ├── microservice.yaml              # Microservice deployment
│   └── database-app.yaml              # Application with database
├── advanced-patterns/                 # Advanced deployment patterns
│   ├── blue-green.yaml                # Blue-green deployment
│   ├── canary.yaml                    # Canary deployment
│   ├── multi-tenant.yaml              # Multi-tenant deployment
│   └── disaster-recovery.yaml         # Disaster recovery
├── helm-charts/                       # Example Helm charts
│   ├── simple-app/                    # Basic application chart
│   ├── microservice/                  # Microservice chart
│   └── monitoring/                    # Monitoring stack chart
└── configurations/                    # Configuration examples
    ├── dev-config.json                # Development configuration
    ├── qa-config.json                 # QA configuration
    ├── prod-config.json               # Production configuration
    └── multi-env-config.json          # Multi-environment configuration
```

## 🚀 Quick Examples

### Basic Deployment
```json
{
  "operation": "deploy:helm",
  "parameters": {
    "openshift_api_url": "https://api.cluster.com:6443",
    "openshift_project": "my-app-dev",
    "username": "developer",
    "password": "secret123",
    "bitbucket_repo_url": "https://bitbucket.org/org/charts.git",
    "chart_path": "charts/my-app",
    "values_path": "values-dev.yaml"
  }
}
```

### Dry Run Validation
```json
{
  "operation": "deploy:helm",
  "parameters": {
    "openshift_api_url": "https://api.cluster.com:6443",
    "openshift_project": "my-app-dev",
    "username": "developer",
    "password": "secret123",
    "bitbucket_repo_url": "https://bitbucket.org/org/charts.git",
    "chart_path": "charts/my-app",
    "values_path": "values-dev.yaml",
    "dry_run": true
  }
}
```

### Status Check
```json
{
  "operation": "helm:status",
  "parameters": {
    "release_name": "my-app-dev",
    "namespace": "my-app-dev"
  }
}
```

### Rollback
```json
{
  "operation": "helm:rollback",
  "parameters": {
    "release_name": "my-app-dev",
    "namespace": "my-app-dev",
    "revision": 1
  }
}
```

## 📚 Example Categories

### 1. Basic Deployments
- Simple web application
- Microservice with database
- Static website
- API service

### 2. Advanced Patterns
- Blue-green deployments
- Canary releases
- A/B testing
- Multi-tenant applications

### 3. Enterprise Scenarios
- Multi-environment pipelines
- Compliance workflows
- Disaster recovery
- Monitoring and observability

### 4. Integration Examples
- GitOps workflows
- CI/CD pipelines
- Monitoring integration
- Secret management

## 🔧 Using the Examples

1. **Copy the example** that matches your use case
2. **Modify the parameters** to match your environment
3. **Test with dry run** first
4. **Execute the deployment**

## 📖 Related Documentation

- [Quick Start Guide](../QUICK_START.md) - Get started quickly
- [Deployment Guide](../DEPLOYMENT.md) - Comprehensive deployment patterns
- [Configuration Reference](../CONFIGURATION.md) - All configuration options
- [Best Practices](../BEST_PRACTICES.md) - Production recommendations

---

**Need Help?** Check the [Troubleshooting Guide](../TROUBLESHOOTING.md) or review the specific example documentation.
