# Helm OpenShift Deploy Plugin Documentation

Welcome to the comprehensive documentation for the Helm OpenShift Deploy Plugin. This plugin enables automated deployment of Helm charts to OpenShift clusters with Bitbucket integration.

## 📖 Documentation Index

### Getting Started
- [Installation Guide](./INSTALLATION.md) - How to install and configure the plugin
- [Quick Start](./QUICK_START.md) - Get up and running in 5 minutes
- [Configuration Reference](./CONFIGURATION.md) - Complete configuration options

### User Guides
- [Deployment Guide](./DEPLOYMENT.md) - How to deploy applications using the plugin
- [Environment Management](./ENVIRONMENTS.md) - Managing different environments (dev/qa/prod)
- [Troubleshooting](./TROUBLESHOOTING.md) - Common issues and solutions

### Developer Resources
- [API Reference](./API.md) - Complete API documentation
- [Plugin Development](./DEVELOPMENT.md) - Extending and customizing the plugin
- [Integration Guide](./INTEGRATION.md) - Integrating with other systems

### Examples
- [Workflow Examples](./examples/) - Sample workflows and configurations
- [Use Cases](./USE_CASES.md) - Real-world deployment scenarios
- [Best Practices](./BEST_PRACTICES.md) - Recommended practices and patterns

## 🚀 Overview

The Helm OpenShift Deploy Plugin provides:

- **OpenShift Integration**: Native OpenShift authentication and project management
- **Bitbucket Integration**: Clone Helm charts directly from Bitbucket repositories
- **Environment-Specific Deployments**: Support for different values files per environment
- **Smart Install/Upgrade**: Automatically detects and performs install or upgrade operations
- **Comprehensive Validation**: Dry run support and parameter validation
- **Monitoring & Logging**: Detailed deployment tracking and audit logs

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Deploy        │    │   Helm          │    │   OpenShift     │
│   Orchestrator  │◄──►│   OpenShift     │◄──►│   Cluster       │
│                 │    │   Plugin        │    │                 │
│ - Workflows     │    │                 │    │ - Projects      │
│ - Environments  │    │ ✅ Install      │    │ - Deployments   │
│ - Promotions    │    │ ✅ Upgrade      │    │ - Services      │
│ - Monitoring    │    │ ✅ Rollback     │    │ - Routes        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Bitbucket     │
                       │   Repository    │
                       │                 │
                       │ - Helm Charts   │
                       │ - Values Files  │
                       │ - Templates     │
                       └─────────────────┘
```

## 🎯 Key Features

### Deployment Operations
- **`deploy:helm`** - Main deployment operation (install/upgrade)
- **`helm:status`** - Get deployment status and information
- **`helm:rollback`** - Rollback to previous version
- **`deploy:validate`** - Validate configuration without deploying

### Configuration Management
- **Environment-specific values** - Different configurations per environment
- **Variable override** - Runtime parameter customization
- **Secret integration** - Secure credential management
- **Template support** - Dynamic configuration generation

### Monitoring & Observability
- **Deployment tracking** - Real-time deployment status
- **Resource monitoring** - Kubernetes resource health
- **Audit logging** - Complete operation history
- **Error reporting** - Detailed error messages and troubleshooting

## 🔧 Prerequisites

### Required Tools
- **Helm CLI** (v3.x) - Kubernetes package manager
- **OpenShift CLI** (oc) - OpenShift command-line interface
- **Git CLI** - For repository operations

### Access Requirements
- **OpenShift Cluster** - Valid cluster access with appropriate permissions
- **Bitbucket Repository** - Repository containing Helm charts
- **Deploy Orchestrator** - Running instance with plugin support

### Permissions
- **OpenShift RBAC** - Appropriate permissions in target projects
- **Plugin Permissions** - Deploy Orchestrator plugin access rights
- **Repository Access** - Read access to Bitbucket repositories

## 📊 Supported Environments

| Environment | Description | Values File | Typical Use |
|-------------|-------------|-------------|-------------|
| **Development** | Development environment | `values-dev.yaml` | Feature development and testing |
| **QA/Staging** | Quality assurance | `values-qa.yaml` | Integration testing and validation |
| **Production** | Production environment | `values-prod.yaml` | Live application deployment |
| **Custom** | Custom environments | `values-{env}.yaml` | Special purpose deployments |

## 🔄 Deployment Flow

1. **Repository Clone** - Clone Helm charts from Bitbucket
2. **Authentication** - Login to OpenShift cluster
3. **Project Selection** - Switch to target project/namespace
4. **Release Detection** - Check if Helm release exists
5. **Deployment** - Perform install or upgrade operation
6. **Validation** - Verify deployment success
7. **Monitoring** - Track deployment status and health

## 🛡️ Security Features

- **Credential Management** - Secure storage and handling of sensitive data
- **RBAC Integration** - Role-based access control
- **Audit Logging** - Complete operation audit trail
- **TLS Support** - Secure communication with OpenShift clusters
- **Secret Injection** - Runtime secret injection from secrets service

## 📈 Performance & Scalability

- **Concurrent Deployments** - Support for multiple simultaneous deployments
- **Resource Optimization** - Efficient resource usage and cleanup
- **Timeout Management** - Configurable operation timeouts
- **Retry Logic** - Automatic retry for transient failures
- **Hot Reload** - Plugin updates without service restart

## 🔗 Integration Points

### Deploy Orchestrator Integration
- **Workflow Engine** - Seamless workflow integration
- **Environment Service** - Environment configuration management
- **Secrets Service** - Secure credential injection
- **Monitoring Service** - Deployment tracking and alerting
- **Audit Service** - Operation logging and compliance

### External Integrations
- **Bitbucket** - Source code and chart repository
- **OpenShift** - Target deployment platform
- **Helm** - Package management and deployment
- **Kubernetes** - Container orchestration platform

## 📞 Support & Community

- **Documentation** - Comprehensive guides and references
- **Examples** - Real-world use cases and templates
- **Troubleshooting** - Common issues and solutions
- **Best Practices** - Recommended patterns and approaches

## 🚀 Getting Started

Ready to get started? Check out our [Quick Start Guide](./QUICK_START.md) to deploy your first application in minutes!

For detailed installation instructions, see the [Installation Guide](./INSTALLATION.md).

## 📝 License

This plugin is part of the Deploy Orchestrator project and follows the same licensing terms.

---

**Next Steps:**
- [Install the Plugin](./INSTALLATION.md)
- [Configure Your First Deployment](./QUICK_START.md)
- [Explore Advanced Features](./CONFIGURATION.md)
