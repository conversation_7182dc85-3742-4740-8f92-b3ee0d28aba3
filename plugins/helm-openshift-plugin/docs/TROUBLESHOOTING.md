# Troubleshooting Guide

Comprehensive troubleshooting guide for the Helm OpenShift Deploy Plugin.

## 🔍 Quick Diagnostics

### Health Check Commands
```bash
# Check plugin status
curl -H "Authorization: Bearer TOKEN" \
     http://localhost:8080/api/v1/plugins/helm-openshift-deploy/status

# Validate configuration
curl -X POST http://localhost:8080/api/v1/plugins/helm-openshift-deploy/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer TOKEN" \
  -d '{"operation": "deploy:validate", "parameters": {...}}'

# Check plugin logs
curl -H "Authorization: Bearer TOKEN" \
     http://localhost:8080/api/v1/plugins/helm-openshift-deploy/logs?lines=50
```

### System Requirements Check
```bash
# Check required tools
helm version --short
oc version --client
git --version

# Check plugin installation
ls -la ../../backend/workflow-service/plugins/helm-openshift-deploy/

# Check workflow service
curl http://localhost:8080/health
```

## 🚨 Common Issues

### 1. Plugin Not Found

**Symptoms:**
- Plugin not listed in UI
- API returns 404 for plugin endpoints
- "Plugin not found" errors

**Diagnosis:**
```bash
# Check plugin directory
ls -la ../../backend/workflow-service/plugins/

# Check plugin manifest
cat ../../backend/workflow-service/plugins/helm-openshift-deploy/plugin.yaml

# Check workflow service logs
curl http://localhost:8080/api/v1/plugins
```

**Solutions:**
```bash
# Reinstall plugin
cd plugins/helm-openshift-plugin
make clean
make build
make install

# Restart workflow service
cd ../../backend/workflow-service
go run main.go

# Or reload plugins
curl -X POST http://localhost:8080/api/v1/plugins/reload
```

### 2. Authentication Failures

**Symptoms:**
- "oc login failed" errors
- "Authorization header required"
- "Invalid credentials" messages

**Diagnosis:**
```bash
# Test OpenShift login manually
oc login https://api.your-cluster.com:6443 -u username -p password

# Check API token
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8080/api/v1/plugins
```

**Solutions:**
```bash
# Update credentials in plugin configuration
{
  "username": "correct-username",
  "password": "correct-password"
}

# Use secrets for sensitive data
{
  "username": "{{secret:openshift-username}}",
  "password": "{{secret:openshift-password}}"
}

# Check OpenShift cluster accessibility
ping api.your-cluster.com
curl -k https://api.your-cluster.com:6443/version
```

### 3. Repository Access Issues

**Symptoms:**
- "git clone failed" errors
- "Repository not found"
- "Permission denied" for Bitbucket

**Diagnosis:**
```bash
# Test repository access manually
git clone https://bitbucket.org/yourorg/helm-charts.git

# Check repository URL format
echo "https://bitbucket.org/yourorg/helm-charts.git"

# Verify chart path exists
ls -la helm-charts/charts/your-app/
```

**Solutions:**
```bash
# Correct repository URL format
{
  "bitbucket_repo_url": "https://bitbucket.org/yourorg/helm-charts.git"
}

# Add authentication for private repos
{
  "bitbucket_repo_url": "https://username:<EMAIL>/yourorg/helm-charts.git"
}

# Use SSH if HTTPS fails
{
  "bitbucket_repo_url": "*****************:yourorg/helm-charts.git"
}
```

### 4. Helm Chart Issues

**Symptoms:**
- "Chart.yaml not found"
- "values file does not exist"
- "helm install/upgrade failed"

**Diagnosis:**
```bash
# Check chart structure
git clone https://bitbucket.org/yourorg/helm-charts.git
ls -la helm-charts/charts/your-app/
cat helm-charts/charts/your-app/Chart.yaml

# Validate chart syntax
helm lint helm-charts/charts/your-app/

# Check values file
cat helm-charts/values-dev.yaml
```

**Solutions:**
```bash
# Ensure proper chart structure
charts/
└── your-app/
    ├── Chart.yaml
    ├── values.yaml
    └── templates/
        ├── deployment.yaml
        ├── service.yaml
        └── ingress.yaml

# Correct paths in configuration
{
  "chart_path": "charts/your-app",
  "values_path": "values-dev.yaml"
}

# Validate chart before deployment
helm template charts/your-app/ -f values-dev.yaml
```

### 5. OpenShift Permission Issues

**Symptoms:**
- "Forbidden" errors during deployment
- "User cannot create resources"
- "Project not found" errors

**Diagnosis:**
```bash
# Check user permissions
oc auth can-i create deployments -n your-project
oc auth can-i create services -n your-project
oc auth can-i create routes -n your-project

# Check project existence
oc get projects | grep your-project
oc project your-project
```

**Solutions:**
```bash
# Grant necessary permissions
oc adm policy add-role-to-user edit username -n your-project

# Create project if it doesn't exist
oc new-project your-project

# Use service account with proper permissions
oc create serviceaccount helm-deployer -n your-project
oc adm policy add-role-to-user edit system:serviceaccount:your-project:helm-deployer
```

### 6. Timeout Issues

**Symptoms:**
- "context deadline exceeded"
- "helm timeout" errors
- Deployments hanging

**Diagnosis:**
```bash
# Check current timeout setting
{
  "helm_timeout": "300s"  # Current setting
}

# Monitor deployment progress
oc get pods -n your-project -w
helm status your-release -n your-project
```

**Solutions:**
```bash
# Increase timeout for complex deployments
{
  "helm_timeout": "900s"  # 15 minutes
}

# Use appropriate timeouts per environment
{
  "dev": {"helm_timeout": "300s"},
  "qa": {"helm_timeout": "600s"},
  "prod": {"helm_timeout": "900s"}
}

# Check resource constraints
kubectl describe pod failing-pod -n your-project
```

## 🔧 Advanced Troubleshooting

### Debug Mode
Enable debug logging for detailed troubleshooting:

```json
{
  "debug": true,
  "log_level": "debug",
  "verbose_output": true
}
```

### Dry Run Testing
Test deployments without actual execution:

```json
{
  "operation": "deploy:helm",
  "parameters": {
    "dry_run": true,
    // ... other parameters
  }
}
```

### Step-by-Step Debugging

1. **Validate Configuration:**
   ```bash
   curl -X POST .../execute -d '{"operation": "deploy:validate", ...}'
   ```

2. **Test Repository Access:**
   ```bash
   git clone https://bitbucket.org/yourorg/helm-charts.git /tmp/test-clone
   ```

3. **Test OpenShift Login:**
   ```bash
   oc login https://api.cluster.com:6443 -u username -p password
   ```

4. **Test Helm Chart:**
   ```bash
   helm template charts/your-app/ -f values-dev.yaml
   ```

5. **Dry Run Deployment:**
   ```bash
   helm install test-release charts/your-app/ -f values-dev.yaml --dry-run
   ```

### Log Analysis

#### Plugin Logs
```bash
# Get recent plugin logs
curl -H "Authorization: Bearer TOKEN" \
     http://localhost:8080/api/v1/plugins/helm-openshift-deploy/logs?lines=100

# Follow logs in real-time
curl -H "Authorization: Bearer TOKEN" \
     http://localhost:8080/api/v1/plugins/helm-openshift-deploy/logs?follow=true
```

#### Workflow Service Logs
```bash
# Check workflow service logs
docker logs workflow-service

# Or if running locally
tail -f workflow-service.log
```

#### OpenShift Logs
```bash
# Check pod logs
oc logs deployment/your-app -n your-project

# Check events
oc get events -n your-project --sort-by='.lastTimestamp'
```

## 📊 Performance Issues

### Slow Deployments

**Diagnosis:**
- Check resource constraints
- Monitor network connectivity
- Analyze Helm chart complexity

**Solutions:**
```bash
# Optimize resource requests
resources:
  requests:
    memory: "256Mi"
    cpu: "250m"

# Use image pull policy optimization
image:
  pullPolicy: "IfNotPresent"

# Parallel deployment strategies
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 2
    maxUnavailable: 1
```

### Memory Issues

**Symptoms:**
- Plugin crashes
- Out of memory errors
- Slow response times

**Solutions:**
```bash
# Increase plugin memory limits
resources:
  limits:
    memory: "1Gi"
  requests:
    memory: "512Mi"

# Optimize chart size
# Remove unnecessary files from charts
# Use .helmignore file
```

## 🔄 Recovery Procedures

### Rollback Failed Deployment
```bash
# Check release history
helm history your-release -n your-project

# Rollback to previous version
curl -X POST .../execute -d '{
  "operation": "helm:rollback",
  "parameters": {
    "release_name": "your-release",
    "namespace": "your-project",
    "revision": 1
  }
}'
```

### Clean Up Failed Resources
```bash
# Delete failed release
helm uninstall your-release -n your-project

# Clean up stuck resources
oc delete all -l app=your-app -n your-project

# Force delete stuck pods
oc delete pod stuck-pod -n your-project --force --grace-period=0
```

### Plugin Recovery
```bash
# Restart plugin
curl -X POST http://localhost:8080/api/v1/plugins/helm-openshift-deploy/reload

# Reinstall plugin
cd plugins/helm-openshift-plugin
make clean install

# Reset plugin configuration
# Remove and reconfigure through UI
```

## 📞 Getting Help

### Information to Collect
When seeking help, provide:

1. **Plugin Version:**
   ```bash
   cat plugins/helm-openshift-plugin/plugin.yaml | grep version
   ```

2. **Error Messages:**
   ```bash
   curl -H "Authorization: Bearer TOKEN" \
        http://localhost:8080/api/v1/plugins/helm-openshift-deploy/logs?lines=50
   ```

3. **Configuration:**
   ```json
   {
     "openshift_api_url": "https://api.cluster.com:6443",
     "openshift_project": "my-app-dev",
     // ... (sanitized configuration)
   }
   ```

4. **Environment Details:**
   ```bash
   helm version
   oc version
   kubectl version
   ```

### Support Channels
- **Documentation:** Check [Configuration Reference](./CONFIGURATION.md)
- **API Reference:** Review [API Documentation](./API.md)
- **Examples:** See [Use Cases](./USE_CASES.md)

---

**Still Having Issues?** Check our [Configuration Reference](./CONFIGURATION.md) or review the [API Documentation](./API.md) for detailed parameter information.
