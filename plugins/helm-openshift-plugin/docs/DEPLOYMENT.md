# Deployment Guide

Comprehensive guide for deploying applications using the Helm OpenShift Deploy Plugin.

## 🎯 Deployment Overview

The Helm OpenShift Deploy Plugin supports various deployment patterns:
- **Single Environment Deployments** - Deploy to one environment
- **Multi-Environment Deployments** - Deploy across dev/qa/prod
- **Blue-Green Deployments** - Zero-downtime deployments
- **Canary Deployments** - Gradual rollout strategies
- **Environment Promotions** - Promote releases between environments

## 🚀 Basic Deployment Workflow

### 1. Prepare Your Helm Chart

Ensure your repository has the proper structure:

```
helm-charts/
├── charts/
│   └── my-application/
│       ├── Chart.yaml
│       ├── values.yaml
│       └── templates/
│           ├── deployment.yaml
│           ├── service.yaml
│           ├── ingress.yaml
│           └── configmap.yaml
├── values-dev.yaml
├── values-qa.yaml
└── values-prod.yaml
```

**Example Chart.yaml:**
```yaml
apiVersion: v2
name: my-application
description: A Helm chart for my application
type: application
version: 1.0.0
appVersion: "1.0.0"
```

**Example values-dev.yaml:**
```yaml
replicaCount: 1
image:
  repository: my-app
  tag: "latest"
  pullPolicy: Always
service:
  type: ClusterIP
  port: 8080
ingress:
  enabled: true
  host: my-app-dev.apps.cluster.com
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi
```

### 2. Create Deployment Workflow

#### Via UI (Recommended)
1. Navigate to **Workflows** → **Designer**
2. Create new workflow: "Deploy My Application"
3. Add workflow steps:

```yaml
name: "Deploy My Application"
description: "Deploy application to OpenShift using Helm"
parameters:
  - name: ENVIRONMENT
    type: string
    default: "dev"
    description: "Target environment (dev/qa/prod)"
  - name: IMAGE_TAG
    type: string
    required: true
    description: "Docker image tag to deploy"
  - name: DRY_RUN
    type: boolean
    default: false
    description: "Perform dry run without actual deployment"

steps:
  - name: validate-config
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:validate
    parameters:
      openshift_api_url: "{{.OPENSHIFT_API_URL}}"
      openshift_project: "my-app-{{.ENVIRONMENT}}"
      username: "{{secret:openshift-username}}"
      password: "{{secret:openshift-password}}"
      bitbucket_repo_url: "https://bitbucket.org/myorg/helm-charts.git"
      chart_path: "charts/my-application"
      values_path: "values-{{.ENVIRONMENT}}.yaml"

  - name: deploy-application
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      openshift_api_url: "{{.OPENSHIFT_API_URL}}"
      openshift_project: "my-app-{{.ENVIRONMENT}}"
      username: "{{secret:openshift-username}}"
      password: "{{secret:openshift-password}}"
      bitbucket_repo_url: "https://bitbucket.org/myorg/helm-charts.git"
      chart_path: "charts/my-application"
      values_path: "values-{{.ENVIRONMENT}}.yaml"
      release_name: "my-app-{{.ENVIRONMENT}}"
      dry_run: "{{.DRY_RUN}}"
      extra_values:
        image.tag: "{{.IMAGE_TAG}}"
        environment: "{{.ENVIRONMENT}}"

  - name: verify-deployment
    type: plugin
    plugin: helm-openshift-deploy
    operation: helm:status
    parameters:
      release_name: "my-app-{{.ENVIRONMENT}}"
      namespace: "my-app-{{.ENVIRONMENT}}"
```

### 3. Execute Deployment

#### Development Deployment
```json
{
  "workflow": "deploy-my-application",
  "parameters": {
    "ENVIRONMENT": "dev",
    "IMAGE_TAG": "v1.2.3",
    "DRY_RUN": false
  }
}
```

#### Production Deployment
```json
{
  "workflow": "deploy-my-application",
  "parameters": {
    "ENVIRONMENT": "prod",
    "IMAGE_TAG": "v1.2.3",
    "DRY_RUN": false
  }
}
```

## 🌍 Multi-Environment Deployments

### Environment-Specific Configuration

Create separate values files for each environment:

**values-dev.yaml:**
```yaml
replicaCount: 1
image:
  tag: "latest"
  pullPolicy: Always
resources:
  requests:
    memory: "256Mi"
    cpu: "250m"
ingress:
  host: my-app-dev.apps.cluster.com
database:
  host: dev-db.internal
  name: myapp_dev
```

**values-qa.yaml:**
```yaml
replicaCount: 2
image:
  tag: "stable"
  pullPolicy: IfNotPresent
resources:
  requests:
    memory: "512Mi"
    cpu: "500m"
ingress:
  host: my-app-qa.apps.cluster.com
database:
  host: qa-db.internal
  name: myapp_qa
```

**values-prod.yaml:**
```yaml
replicaCount: 3
image:
  tag: "v1.0.0"
  pullPolicy: IfNotPresent
resources:
  requests:
    memory: "1Gi"
    cpu: "1000m"
  limits:
    memory: "2Gi"
    cpu: "2000m"
ingress:
  host: my-app.company.com
database:
  host: prod-db.internal
  name: myapp_prod
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
```

### Environment Promotion Workflow

```yaml
name: "Environment Promotion"
description: "Promote application between environments"
parameters:
  - name: SOURCE_ENV
    type: string
    required: true
    description: "Source environment (dev/qa)"
  - name: TARGET_ENV
    type: string
    required: true
    description: "Target environment (qa/prod)"
  - name: VERSION
    type: string
    required: true
    description: "Version to promote"

steps:
  - name: validate-promotion
    type: condition
    condition: |
      ({{.SOURCE_ENV}} == "dev" && {{.TARGET_ENV}} == "qa") ||
      ({{.SOURCE_ENV}} == "qa" && {{.TARGET_ENV}} == "prod")
    onFailure: abort

  - name: get-source-status
    type: plugin
    plugin: helm-openshift-deploy
    operation: helm:status
    parameters:
      release_name: "my-app-{{.SOURCE_ENV}}"
      namespace: "my-app-{{.SOURCE_ENV}}"

  - name: approval-gate
    type: manual-approval
    condition: "{{.TARGET_ENV}} == 'prod'"
    approvers: ["release-manager", "devops-team"]
    message: "Approve promotion to production: {{.VERSION}}"

  - name: deploy-to-target
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      openshift_project: "my-app-{{.TARGET_ENV}}"
      values_path: "values-{{.TARGET_ENV}}.yaml"
      release_name: "my-app-{{.TARGET_ENV}}"
      extra_values:
        image.tag: "{{.VERSION}}"
        promotion.source: "{{.SOURCE_ENV}}"
        promotion.timestamp: "{{.TIMESTAMP}}"

  - name: smoke-tests
    type: script
    script: |
      #!/bin/bash
      echo "Running smoke tests for {{.TARGET_ENV}}"
      # Add your smoke test commands here
      curl -f http://my-app-{{.TARGET_ENV}}.apps.cluster.com/health
```

## 🔄 Advanced Deployment Patterns

### Blue-Green Deployment

```yaml
name: "Blue-Green Deployment"
steps:
  - name: deploy-green
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      release_name: "my-app-green"
      extra_values:
        service.selector.version: "green"
        image.tag: "{{.NEW_VERSION}}"

  - name: test-green
    type: script
    script: |
      # Test green deployment
      curl -f http://my-app-green.internal/health

  - name: switch-traffic
    type: script
    script: |
      # Switch traffic to green
      oc patch service my-app -p '{"spec":{"selector":{"version":"green"}}}'

  - name: cleanup-blue
    type: plugin
    plugin: helm-openshift-deploy
    operation: helm:rollback
    parameters:
      release_name: "my-app-blue"
```

### Canary Deployment

```yaml
name: "Canary Deployment"
steps:
  - name: deploy-canary
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      release_name: "my-app-canary"
      extra_values:
        replicaCount: 1
        service.weight: 10  # 10% traffic
        image.tag: "{{.NEW_VERSION}}"

  - name: monitor-canary
    type: script
    script: |
      # Monitor canary metrics for 10 minutes
      sleep 600

  - name: promote-canary
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      release_name: "my-app-main"
      extra_values:
        image.tag: "{{.NEW_VERSION}}"
```

## 📊 Monitoring and Validation

### Health Checks

Add health check steps to your deployment workflows:

```yaml
- name: health-check
  type: script
  script: |
    #!/bin/bash
    echo "Checking application health..."
    
    # Wait for deployment to be ready
    oc rollout status deployment/my-app -n my-app-{{.ENVIRONMENT}} --timeout=300s
    
    # Check pod status
    READY_PODS=$(oc get pods -n my-app-{{.ENVIRONMENT}} -l app=my-app --field-selector=status.phase=Running --no-headers | wc -l)
    EXPECTED_PODS={{.REPLICA_COUNT}}
    
    if [ "$READY_PODS" -eq "$EXPECTED_PODS" ]; then
      echo "✅ Health check passed: $READY_PODS/$EXPECTED_PODS pods running"
    else
      echo "❌ Health check failed: $READY_PODS/$EXPECTED_PODS pods running"
      exit 1
    fi
    
    # Test application endpoint
    if curl -f http://my-app-{{.ENVIRONMENT}}.apps.cluster.com/health; then
      echo "✅ Application endpoint healthy"
    else
      echo "❌ Application endpoint unhealthy"
      exit 1
    fi
```

### Rollback on Failure

```yaml
onFailure:
  - name: rollback-deployment
    type: plugin
    plugin: helm-openshift-deploy
    operation: helm:rollback
    parameters:
      release_name: "my-app-{{.ENVIRONMENT}}"
      namespace: "my-app-{{.ENVIRONMENT}}"
  
  - name: notify-failure
    type: notification
    message: "❌ Deployment failed and rolled back: {{.IMAGE_TAG}} to {{.ENVIRONMENT}}"
    channels: ["slack", "email"]
```

## 🔐 Security Considerations

### Secret Management

```yaml
# Use secrets for sensitive data
parameters:
  username: "{{secret:openshift-username}}"
  password: "{{secret:openshift-password}}"
  database_password: "{{secret:db-password}}"
```

### RBAC Configuration

Ensure proper OpenShift permissions:

```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: helm-deployer
  namespace: my-app-dev
subjects:
- kind: User
  name: deploy-user
roleRef:
  kind: ClusterRole
  name: edit
  apiGroup: rbac.authorization.k8s.io
```

## 📈 Performance Optimization

### Resource Management

```yaml
# Optimize resource allocation
resources:
  requests:
    memory: "256Mi"
    cpu: "250m"
  limits:
    memory: "512Mi"
    cpu: "500m"

# Use horizontal pod autoscaling
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
```

### Image Optimization

```yaml
# Use specific image tags
image:
  tag: "v1.2.3"  # Not "latest"
  pullPolicy: "IfNotPresent"

# Use image pull secrets for private registries
imagePullSecrets:
  - name: registry-secret
```

## 🎯 Best Practices

1. **Always use dry run first** for production deployments
2. **Implement proper health checks** and readiness probes
3. **Use environment-specific configurations** via values files
4. **Implement rollback strategies** for failed deployments
5. **Monitor deployment metrics** and set up alerts
6. **Use semantic versioning** for image tags
7. **Implement approval gates** for production deployments
8. **Test deployments** in lower environments first

---

**Next Steps:**
- [Set Up Environment Management](./ENVIRONMENTS.md)
- [Explore Advanced Configuration](./CONFIGURATION.md)
- [Learn Best Practices](./BEST_PRACTICES.md)
