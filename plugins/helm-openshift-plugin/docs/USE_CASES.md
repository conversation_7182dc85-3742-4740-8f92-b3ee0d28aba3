# Use Cases Guide

Real-world use cases and examples for the Helm OpenShift Deploy Plugin.

## 🎯 Common Use Cases

### 1. Microservices Deployment
Deploy multiple microservices with dependencies and service mesh integration.

### 2. Multi-Environment CI/CD
Automated deployment pipeline across development, QA, and production environments.

### 3. Blue-Green Deployments
Zero-downtime deployments using blue-green deployment strategy.

### 4. Canary Releases
Gradual rollout of new versions with traffic splitting.

### 5. Environment Promotion
Promote tested releases from lower to higher environments.

### 6. Disaster Recovery
Automated backup and restore procedures for applications.

## 🏢 Enterprise Scenarios

### Scenario 1: E-commerce Platform Deployment

**Context:** Large e-commerce platform with multiple microservices requiring coordinated deployment.

**Requirements:**
- Deploy 15+ microservices
- Database migrations
- Cache warming
- Health checks
- Rollback capability

**Solution:**
```yaml
name: "E-commerce Platform Deployment"
description: "Deploy complete e-commerce platform"

parameters:
  - name: RELEASE_VERSION
    type: string
    required: true
  - name: ENVIRONMENT
    type: string
    default: "staging"
  - name: ENABLE_MIGRATIONS
    type: boolean
    default: true

steps:
  # Pre-deployment checks
  - name: validate-environment
    type: script
    script: |
      oc get namespace ecommerce-{{.ENVIRONMENT}} || oc create namespace ecommerce-{{.ENVIRONMENT}}

  # Database migrations
  - name: run-migrations
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    condition: "{{.ENABLE_MIGRATIONS}}"
    parameters:
      chart_path: "charts/database-migrations"
      release_name: "migrations-{{.RELEASE_VERSION}}"
      extra_values:
        version: "{{.RELEASE_VERSION}}"

  # Core services deployment
  - name: deploy-user-service
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      chart_path: "charts/user-service"
      release_name: "user-service"
      extra_values:
        image.tag: "{{.RELEASE_VERSION}}"

  - name: deploy-product-service
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      chart_path: "charts/product-service"
      release_name: "product-service"
      extra_values:
        image.tag: "{{.RELEASE_VERSION}}"

  - name: deploy-order-service
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      chart_path: "charts/order-service"
      release_name: "order-service"
      extra_values:
        image.tag: "{{.RELEASE_VERSION}}"

  # Frontend deployment
  - name: deploy-web-frontend
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      chart_path: "charts/web-frontend"
      release_name: "web-frontend"
      extra_values:
        image.tag: "{{.RELEASE_VERSION}}"
        config.apiUrl: "https://api-{{.ENVIRONMENT}}.ecommerce.com"

  # Post-deployment validation
  - name: health-check-all-services
    type: script
    script: |
      services=("user-service" "product-service" "order-service" "web-frontend")
      for service in "${services[@]}"; do
        echo "Checking $service health..."
        oc rollout status deployment/$service -n ecommerce-{{.ENVIRONMENT}} --timeout=300s
      done

  - name: integration-tests
    type: script
    script: |
      # Run integration tests
      kubectl run integration-tests --image=ecommerce/integration-tests:{{.RELEASE_VERSION}} \
        --env="BASE_URL=https://{{.ENVIRONMENT}}.ecommerce.com" \
        --restart=Never -n ecommerce-{{.ENVIRONMENT}}
      kubectl wait --for=condition=complete job/integration-tests --timeout=600s

onFailure:
  - name: rollback-all-services
    type: script
    script: |
      services=("web-frontend" "order-service" "product-service" "user-service")
      for service in "${services[@]}"; do
        helm rollback $service -n ecommerce-{{.ENVIRONMENT}}
      done
```

### Scenario 2: Financial Services Compliance Deployment

**Context:** Financial application requiring strict compliance, audit trails, and approval workflows.

**Requirements:**
- Multi-level approvals
- Compliance validation
- Audit logging
- Encrypted secrets
- Rollback procedures

**Solution:**
```yaml
name: "Financial Services Deployment"
description: "Compliant deployment for financial applications"

parameters:
  - name: APPLICATION_VERSION
    type: string
    required: true
  - name: ENVIRONMENT
    type: string
    enum: ["uat", "prod"]
    required: true
  - name: COMPLIANCE_APPROVED
    type: boolean
    required: true

steps:
  # Compliance validation
  - name: compliance-check
    type: condition
    condition: "{{.COMPLIANCE_APPROVED}} == true"
    onFailure: abort
    errorMessage: "Deployment requires compliance approval"

  # Security scan
  - name: security-scan
    type: script
    script: |
      echo "Running security scan for version {{.APPLICATION_VERSION}}"
      # Integrate with security scanning tools
      trivy image financial-app:{{.APPLICATION_VERSION}}

  # Multi-level approval
  - name: security-approval
    type: manual-approval
    approvers: ["security-team"]
    message: "Security approval for {{.APPLICATION_VERSION}} to {{.ENVIRONMENT}}"
    timeout: "24h"

  - name: compliance-approval
    type: manual-approval
    condition: "{{.ENVIRONMENT}} == 'prod'"
    approvers: ["compliance-officer", "risk-manager"]
    message: "Compliance approval for production deployment"
    timeout: "48h"

  # Backup current state
  - name: backup-current-state
    type: script
    script: |
      echo "Creating backup of current state"
      helm get values financial-app -n financial-{{.ENVIRONMENT}} > backup-values-$(date +%Y%m%d-%H%M%S).yaml
      oc get all -n financial-{{.ENVIRONMENT}} -o yaml > backup-resources-$(date +%Y%m%d-%H%M%S).yaml

  # Deploy with audit logging
  - name: deploy-application
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      chart_path: "charts/financial-app"
      release_name: "financial-app"
      extra_values:
        image.tag: "{{.APPLICATION_VERSION}}"
        security.encryptionEnabled: true
        audit.enabled: true
        compliance.environment: "{{.ENVIRONMENT}}"

  # Compliance validation
  - name: post-deployment-compliance
    type: script
    script: |
      echo "Running post-deployment compliance checks"
      # Check encryption at rest
      # Verify audit logging
      # Validate security policies

  # Audit trail
  - name: create-audit-record
    type: script
    script: |
      echo "Creating audit record"
      cat << EOF > audit-record.json
      {
        "deployment_id": "{{.EXECUTION_ID}}",
        "application": "financial-app",
        "version": "{{.APPLICATION_VERSION}}",
        "environment": "{{.ENVIRONMENT}}",
        "timestamp": "{{.TIMESTAMP}}",
        "approvers": ["security-team", "compliance-officer"],
        "compliance_status": "approved"
      }
      EOF
      # Send to audit system
```

### Scenario 3: SaaS Multi-Tenant Deployment

**Context:** SaaS platform requiring tenant-specific configurations and isolated deployments.

**Requirements:**
- Tenant isolation
- Custom configurations per tenant
- Scaling based on tenant size
- Monitoring per tenant

**Solution:**
```yaml
name: "SaaS Multi-Tenant Deployment"
description: "Deploy SaaS application for multiple tenants"

parameters:
  - name: TENANT_ID
    type: string
    required: true
  - name: TENANT_TIER
    type: string
    enum: ["basic", "premium", "enterprise"]
    required: true
  - name: APPLICATION_VERSION
    type: string
    required: true

steps:
  # Tenant-specific configuration
  - name: prepare-tenant-config
    type: script
    script: |
      case "{{.TENANT_TIER}}" in
        "basic")
          export REPLICA_COUNT=1
          export RESOURCE_MEMORY="512Mi"
          export RESOURCE_CPU="500m"
          ;;
        "premium")
          export REPLICA_COUNT=3
          export RESOURCE_MEMORY="1Gi"
          export RESOURCE_CPU="1000m"
          ;;
        "enterprise")
          export REPLICA_COUNT=5
          export RESOURCE_MEMORY="2Gi"
          export RESOURCE_CPU="2000m"
          ;;
      esac

  # Create tenant namespace
  - name: create-tenant-namespace
    type: script
    script: |
      oc create namespace tenant-{{.TENANT_ID}} --dry-run=client -o yaml | oc apply -f -
      oc label namespace tenant-{{.TENANT_ID}} tenant-id={{.TENANT_ID}} tenant-tier={{.TENANT_TIER}}

  # Deploy tenant-specific application
  - name: deploy-tenant-application
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      openshift_project: "tenant-{{.TENANT_ID}}"
      chart_path: "charts/saas-application"
      release_name: "saas-app-{{.TENANT_ID}}"
      extra_values:
        tenant.id: "{{.TENANT_ID}}"
        tenant.tier: "{{.TENANT_TIER}}"
        image.tag: "{{.APPLICATION_VERSION}}"
        replicaCount: "{{.REPLICA_COUNT}}"
        resources.requests.memory: "{{.RESOURCE_MEMORY}}"
        resources.requests.cpu: "{{.RESOURCE_CPU}}"
        ingress.host: "{{.TENANT_ID}}.saas-platform.com"

  # Configure tenant monitoring
  - name: setup-tenant-monitoring
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      chart_path: "charts/tenant-monitoring"
      release_name: "monitoring-{{.TENANT_ID}}"
      extra_values:
        tenant.id: "{{.TENANT_ID}}"
        grafana.dashboard.tenant: "{{.TENANT_ID}}"

  # Tenant health check
  - name: tenant-health-check
    type: script
    script: |
      echo "Checking tenant {{.TENANT_ID}} health"
      curl -f https://{{.TENANT_ID}}.saas-platform.com/health
      curl -f https://{{.TENANT_ID}}.saas-platform.com/api/tenant/{{.TENANT_ID}}/status
```

## 🔄 DevOps Automation Scenarios

### Scenario 4: GitOps Integration

**Context:** Implementing GitOps workflow with automatic deployments triggered by Git commits.

```yaml
name: "GitOps Deployment Pipeline"
description: "Automated deployment triggered by Git events"

triggers:
  - type: git-webhook
    repository: "https://bitbucket.org/myorg/helm-charts.git"
    branch: "main"
    path: "charts/**"

parameters:
  - name: GIT_COMMIT_SHA
    type: string
    source: "trigger.commit.sha"
  - name: CHANGED_CHARTS
    type: array
    source: "trigger.changed_files"

steps:
  # Detect changed charts
  - name: detect-changes
    type: script
    script: |
      echo "Detecting changed charts from commit {{.GIT_COMMIT_SHA}}"
      for file in {{.CHANGED_CHARTS}}; do
        if [[ $file == charts/* ]]; then
          chart_name=$(echo $file | cut -d'/' -f2)
          echo "Chart changed: $chart_name"
          echo "CHANGED_CHART=$chart_name" >> $GITHUB_ENV
        fi
      done

  # Validate chart changes
  - name: validate-chart
    type: script
    script: |
      helm lint charts/$CHANGED_CHART/
      helm template charts/$CHANGED_CHART/ --validate

  # Deploy to development
  - name: deploy-to-dev
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      chart_path: "charts/$CHANGED_CHART"
      values_path: "values-dev.yaml"
      openshift_project: "$CHANGED_CHART-dev"
      extra_values:
        git.commit: "{{.GIT_COMMIT_SHA}}"

  # Run automated tests
  - name: run-tests
    type: script
    script: |
      # Run chart tests
      helm test $CHANGED_CHART -n $CHANGED_CHART-dev
      
      # Run application tests
      kubectl run test-runner --image=test-suite:latest \
        --env="APP_URL=https://$CHANGED_CHART-dev.apps.cluster.com" \
        --restart=Never

  # Promote to QA on success
  - name: promote-to-qa
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    condition: "success"
    parameters:
      chart_path: "charts/$CHANGED_CHART"
      values_path: "values-qa.yaml"
      openshift_project: "$CHANGED_CHART-qa"
```

### Scenario 5: Disaster Recovery Automation

**Context:** Automated disaster recovery with backup, restore, and failover capabilities.

```yaml
name: "Disaster Recovery Deployment"
description: "Automated disaster recovery procedures"

parameters:
  - name: RECOVERY_TYPE
    type: string
    enum: ["backup", "restore", "failover"]
    required: true
  - name: BACKUP_ID
    type: string
    required: false
  - name: TARGET_CLUSTER
    type: string
    required: false

steps:
  # Backup procedure
  - name: create-backup
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    condition: "{{.RECOVERY_TYPE}} == 'backup'"
    parameters:
      chart_path: "charts/backup-job"
      release_name: "backup-{{.TIMESTAMP}}"
      extra_values:
        backup.timestamp: "{{.TIMESTAMP}}"
        backup.retention: "30d"

  # Restore procedure
  - name: restore-from-backup
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    condition: "{{.RECOVERY_TYPE}} == 'restore'"
    parameters:
      chart_path: "charts/restore-job"
      release_name: "restore-{{.BACKUP_ID}}"
      extra_values:
        restore.backupId: "{{.BACKUP_ID}}"

  # Failover procedure
  - name: failover-to-dr-site
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    condition: "{{.RECOVERY_TYPE}} == 'failover'"
    parameters:
      openshift_api_url: "{{.TARGET_CLUSTER}}"
      chart_path: "charts/application"
      release_name: "app-dr"
      extra_values:
        deployment.mode: "disaster-recovery"
        database.readOnly: false
```

## 📊 Monitoring and Observability

### Scenario 6: Comprehensive Monitoring Setup

**Context:** Deploy monitoring stack with application-specific dashboards and alerts.

```yaml
name: "Monitoring Stack Deployment"
description: "Deploy comprehensive monitoring for applications"

steps:
  # Deploy Prometheus
  - name: deploy-prometheus
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      chart_path: "charts/prometheus"
      release_name: "prometheus"
      extra_values:
        retention: "30d"
        storage.size: "100Gi"

  # Deploy Grafana
  - name: deploy-grafana
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      chart_path: "charts/grafana"
      release_name: "grafana"
      extra_values:
        dashboards.enabled: true
        datasources.prometheus.url: "http://prometheus:9090"

  # Deploy AlertManager
  - name: deploy-alertmanager
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      chart_path: "charts/alertmanager"
      release_name: "alertmanager"
      extra_values:
        slack.webhook: "{{secret:slack-webhook}}"
        email.smtp: "{{secret:smtp-config}}"
```

---

**Explore More:** These use cases demonstrate the flexibility and power of the Helm OpenShift Deploy Plugin. Adapt these examples to your specific requirements and organizational needs!
