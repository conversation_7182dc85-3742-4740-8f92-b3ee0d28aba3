# Quick Start Guide

Get up and running with the Helm OpenShift Deploy Plugin in 5 minutes! This guide will walk you through your first deployment.

## 🎯 What You'll Accomplish

By the end of this guide, you'll have:
- ✅ Configured the Helm OpenShift plugin
- ✅ Created a simple deployment workflow
- ✅ Deployed an application to OpenShift
- ✅ Verified the deployment status

## 📋 Before You Start

Ensure you have:
- [ ] Plugin installed (see [Installation Guide](./INSTALLATION.md))
- [ ] OpenShift cluster access
- [ ] Bitbucket repository with a Helm chart
- [ ] Deploy Orchestrator UI access

## 🚀 Step 1: Configure the Plugin (2 minutes)

### Access Plugin Configuration
1. Open Deploy Orchestrator UI: `http://localhost:4200`
2. Navigate to **Settings** → **Plugin Management**
3. Find `helm-openshift-deploy` plugin
4. Click **Config** button

### Basic Configuration
Enter your configuration details:

```json
{
  "openshift_api_url": "https://api.your-cluster.com:6443",
  "openshift_project": "my-app-dev",
  "username": "your-username",
  "password": "your-password",
  "bitbucket_repo_url": "https://bitbucket.org/yourorg/helm-charts.git",
  "chart_path": "charts/my-app",
  "values_path": "values-dev.yaml",
  "helm_timeout": "300s"
}
```

**Replace with your values:**
- `openshift_api_url`: Your OpenShift cluster API endpoint
- `openshift_project`: Target namespace/project
- `username/password`: Your OpenShift credentials
- `bitbucket_repo_url`: Repository containing your Helm charts
- `chart_path`: Path to your Helm chart directory
- `values_path`: Environment-specific values file

### Save Configuration
Click **Update** to save your configuration.

## 🔧 Step 2: Prepare Your Helm Chart (1 minute)

Ensure your Bitbucket repository has this structure:

```
your-helm-repo/
├── charts/
│   └── my-app/
│       ├── Chart.yaml
│       ├── values.yaml
│       └── templates/
│           ├── deployment.yaml
│           ├── service.yaml
│           └── ingress.yaml
├── values-dev.yaml
├── values-qa.yaml
└── values-prod.yaml
```

**Example `values-dev.yaml`:**
```yaml
replicaCount: 1

image:
  repository: my-app
  tag: "latest"
  pullPolicy: Always

service:
  type: ClusterIP
  port: 8080

ingress:
  enabled: true
  host: my-app-dev.apps.cluster.com

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi
```

## 🎯 Step 3: Test with Validation (1 minute)

Before deploying, let's validate our configuration:

### Via UI (Recommended)
1. Go to **Workflows** → **Designer**
2. Create a new workflow: "Test Helm Deployment"
3. Add a **Plugin Step**:
   - Plugin: `helm-openshift-deploy`
   - Operation: `deploy:validate`
   - Parameters: Use your configuration values
4. **Execute** the workflow

### Via API (Alternative)
```bash
curl -X POST http://localhost:8080/api/v1/plugins/helm-openshift-deploy/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "operation": "deploy:validate",
    "parameters": {
      "openshift_api_url": "https://api.your-cluster.com:6443",
      "openshift_project": "my-app-dev",
      "username": "your-username",
      "password": "your-password",
      "bitbucket_repo_url": "https://bitbucket.org/yourorg/helm-charts.git",
      "chart_path": "charts/my-app",
      "values_path": "values-dev.yaml"
    }
  }'
```

**Expected Response:**
```json
{
  "valid": true,
  "request": {
    "openshift_api_url": "https://api.your-cluster.com:6443",
    "openshift_project": "my-app-dev",
    "chart_path": "charts/my-app",
    "values_path": "values-dev.yaml"
  }
}
```

## 🚀 Step 4: Deploy Your Application (1 minute)

### Dry Run First (Recommended)
Test the deployment without actually deploying:

```json
{
  "operation": "deploy:helm",
  "parameters": {
    "openshift_api_url": "https://api.your-cluster.com:6443",
    "openshift_project": "my-app-dev",
    "username": "your-username",
    "password": "your-password",
    "bitbucket_repo_url": "https://bitbucket.org/yourorg/helm-charts.git",
    "chart_path": "charts/my-app",
    "values_path": "values-dev.yaml",
    "dry_run": true
  }
}
```

### Actual Deployment
Remove `"dry_run": true` and execute:

```json
{
  "operation": "deploy:helm",
  "parameters": {
    "openshift_api_url": "https://api.your-cluster.com:6443",
    "openshift_project": "my-app-dev",
    "username": "your-username",
    "password": "your-password",
    "bitbucket_repo_url": "https://bitbucket.org/yourorg/helm-charts.git",
    "chart_path": "charts/my-app",
    "values_path": "values-dev.yaml",
    "release_name": "my-app-dev"
  }
}
```

**Expected Response:**
```json
{
  "deployment_result": {
    "success": true,
    "release_name": "my-app-dev",
    "namespace": "my-app-dev",
    "status": "deployed",
    "revision": 1,
    "last_deployed": "2024-01-15T10:30:00Z",
    "execution_time": "45.2s",
    "resources": [
      {
        "kind": "Deployment",
        "name": "my-app",
        "namespace": "my-app-dev",
        "status": "deployed"
      },
      {
        "kind": "Service",
        "name": "my-app",
        "namespace": "my-app-dev",
        "status": "deployed"
      }
    ]
  }
}
```

## ✅ Step 5: Verify Deployment

### Check Deployment Status
```json
{
  "operation": "helm:status",
  "parameters": {
    "release_name": "my-app-dev",
    "namespace": "my-app-dev"
  }
}
```

### Verify in OpenShift Console
1. Login to OpenShift Console
2. Navigate to your project: `my-app-dev`
3. Check **Workloads** → **Deployments**
4. Verify your application is running

### Test Application Access
```bash
# Get route URL
oc get route my-app -n my-app-dev

# Test application
curl http://my-app-dev.apps.cluster.com
```

## 🎉 Congratulations!

You've successfully deployed your first application using the Helm OpenShift plugin! 

## 🔄 Next Steps

### Environment Promotion
Set up promotion from dev to QA:

1. **Create QA Configuration:**
   ```json
   {
     "openshift_project": "my-app-qa",
     "values_path": "values-qa.yaml"
   }
   ```

2. **Create Promotion Workflow:**
   - Source: `my-app-dev`
   - Target: `my-app-qa`
   - Use the same plugin with QA configuration

### Advanced Features
Explore these advanced capabilities:

- **[Environment Management](./ENVIRONMENTS.md)** - Multi-environment deployments
- **[Workflow Integration](./DEPLOYMENT.md)** - Complex deployment workflows
- **[Monitoring & Rollback](./TROUBLESHOOTING.md)** - Deployment monitoring and rollback

### Common Workflow Patterns

#### 1. Simple Deployment Workflow
```yaml
name: "Deploy to Development"
steps:
  - name: "validate"
    plugin: "helm-openshift-deploy"
    operation: "deploy:validate"
  - name: "deploy"
    plugin: "helm-openshift-deploy"
    operation: "deploy:helm"
  - name: "verify"
    plugin: "helm-openshift-deploy"
    operation: "helm:status"
```

#### 2. Environment Promotion Workflow
```yaml
name: "Promote to QA"
steps:
  - name: "approval"
    type: "manual-approval"
    approvers: ["qa-team"]
  - name: "deploy-qa"
    plugin: "helm-openshift-deploy"
    operation: "deploy:helm"
    parameters:
      values_path: "values-qa.yaml"
      openshift_project: "my-app-qa"
```

#### 3. Rollback Workflow
```yaml
name: "Emergency Rollback"
steps:
  - name: "rollback"
    plugin: "helm-openshift-deploy"
    operation: "helm:rollback"
    parameters:
      release_name: "my-app-prod"
      revision: "previous"
```

## 🐛 Quick Troubleshooting

### Common Issues

**Plugin Not Found:**
```bash
# Check plugin installation
curl http://localhost:8080/api/v1/plugins | grep helm-openshift
```

**Authentication Failed:**
```bash
# Test OpenShift login
oc login https://api.your-cluster.com:6443 -u your-username
```

**Chart Not Found:**
```bash
# Verify repository access
git clone https://bitbucket.org/yourorg/helm-charts.git
ls -la helm-charts/charts/my-app/
```

**Deployment Failed:**
- Check OpenShift project permissions
- Verify Helm chart syntax: `helm lint charts/my-app`
- Review plugin logs in UI

## 📚 Learn More

- **[Configuration Reference](./CONFIGURATION.md)** - Complete configuration options
- **[API Documentation](./API.md)** - Full API reference
- **[Best Practices](./BEST_PRACTICES.md)** - Recommended patterns
- **[Troubleshooting](./TROUBLESHOOTING.md)** - Detailed troubleshooting guide

---

**Ready for Production?** Check out our [Best Practices Guide](./BEST_PRACTICES.md) for production deployment recommendations!
