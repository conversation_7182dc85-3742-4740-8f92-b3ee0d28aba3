# Installation Guide

This guide walks you through installing and configuring the Helm OpenShift Deploy Plugin.

## 📋 Prerequisites

### System Requirements
- **Operating System**: Linux, macOS, or Windows
- **Go**: Version 1.21 or later
- **Make**: Build automation tool
- **Git**: Version control system

### Required Tools
Before installing the plugin, ensure these tools are available:

```bash
# Check tool availability
helm version --short
oc version --client
git --version
```

**Tool Installation:**
- **Helm**: [Install Helm](https://helm.sh/docs/intro/install/)
- **OpenShift CLI**: [Install oc](https://docs.openshift.com/container-platform/latest/cli_reference/openshift_cli/getting-started-cli.html)
- **Git**: [Install Git](https://git-scm.com/downloads)

### Access Requirements
- **OpenShift Cluster**: Valid cluster access with deployment permissions
- **Bitbucket Repository**: Repository containing <PERSON><PERSON> charts
- **Deploy Orchestrator**: Running instance (v1.0.0 or later)

## 🚀 Quick Installation

### Automated Installation
Use the provided installation script for the fastest setup:

```bash
# Navigate to plugin directory
cd plugins/helm-openshift-plugin

# Run installation script
./install-and-test.sh
```

This script will:
1. Check prerequisites
2. Build the plugin
3. Install to workflow service
4. Validate installation
5. Run basic tests

### Manual Installation

#### Step 1: Build the Plugin
```bash
# Navigate to plugin directory
cd plugins/helm-openshift-plugin

# Build the plugin binary
make build
```

#### Step 2: Install to Workflow Service
```bash
# Install plugin files
make install
```

This copies the plugin to: `../../backend/workflow-service/plugins/helm-openshift-deploy/`

#### Step 3: Register with Workflow Service
```bash
# Option 1: Restart workflow service (automatic discovery)
cd ../../backend/workflow-service
go run main.go

# Option 2: Hot reload (if service is running)
curl -X POST http://localhost:8080/api/v1/plugins/reload
```

## 🔧 Configuration

### Basic Configuration
Configure the plugin through the Deploy Orchestrator UI:

1. **Access Plugin Management**:
   - Navigate to: `http://localhost:4200/plugin-management`
   - Login with your credentials

2. **Configure Plugin**:
   - Find `helm-openshift-deploy` in the installed plugins
   - Click **Config** button
   - Enter your configuration (see example below)

### Configuration Example
```json
{
  "openshift_api_url": "https://api.cluster.example.com:6443",
  "openshift_project": "my-app-dev",
  "username": "developer",
  "password": "your-password",
  "bitbucket_repo_url": "https://bitbucket.org/myorg/helm-charts.git",
  "chart_path": "charts/my-application",
  "values_path": "values-dev.yaml",
  "helm_timeout": "300s"
}
```

### Environment-Specific Configuration
For multiple environments, create separate configurations:

```json
{
  "environments": {
    "dev": {
      "openshift_api_url": "https://api.dev-cluster.com:6443",
      "openshift_project": "myapp-dev",
      "values_path": "values-dev.yaml"
    },
    "qa": {
      "openshift_api_url": "https://api.qa-cluster.com:6443",
      "openshift_project": "myapp-qa",
      "values_path": "values-qa.yaml"
    },
    "prod": {
      "openshift_api_url": "https://api.prod-cluster.com:6443",
      "openshift_project": "myapp-prod",
      "values_path": "values-prod.yaml"
    }
  }
}
```

## 🔐 Security Configuration

### Credential Management
Store sensitive credentials securely:

1. **Using Secrets Service**:
   ```json
   {
     "openshift_api_url": "https://api.cluster.com:6443",
     "openshift_project": "myapp-prod",
     "username": "{{secret:openshift-username}}",
     "password": "{{secret:openshift-password}}",
     "bitbucket_repo_url": "https://bitbucket.org/myorg/charts.git"
   }
   ```

2. **Environment Variables**:
   ```json
   {
     "username": "{{env:OPENSHIFT_USERNAME}}",
     "password": "{{env:OPENSHIFT_PASSWORD}}"
   }
   ```

### RBAC Configuration
Ensure proper permissions are configured:

1. **OpenShift RBAC**:
   ```yaml
   apiVersion: rbac.authorization.k8s.io/v1
   kind: RoleBinding
   metadata:
     name: helm-deployer
     namespace: myapp-dev
   subjects:
   - kind: User
     name: developer
   roleRef:
     kind: ClusterRole
     name: edit
   ```

2. **Plugin Permissions**:
   - Configure user permissions in Deploy Orchestrator
   - Assign appropriate roles for plugin access

## ✅ Verification

### Installation Verification
```bash
# Check plugin files
ls -la ../../backend/workflow-service/plugins/helm-openshift-deploy/

# Expected files:
# - helm-openshift-plugin (binary)
# - plugin.yaml (manifest)
# - README.md (documentation)
```

### API Verification
```bash
# Check plugin registration (requires auth token)
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8080/api/v1/plugins/helm-openshift-deploy
```

### UI Verification
1. Open Deploy Orchestrator UI
2. Navigate to Plugin Management
3. Verify `helm-openshift-deploy` appears in installed plugins
4. Check plugin status is "running"

## 🧪 Testing Installation

### Basic Validation Test
```bash
# Test plugin validation (safe - no actual deployment)
curl -X POST http://localhost:8080/api/v1/plugins/helm-openshift-deploy/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "operation": "deploy:validate",
    "parameters": {
      "openshift_api_url": "https://api.your-cluster.com:6443",
      "openshift_project": "test-project",
      "username": "test-user",
      "password": "test-password",
      "bitbucket_repo_url": "https://bitbucket.org/yourorg/charts.git",
      "chart_path": "charts/test-app",
      "values_path": "values-dev.yaml"
    }
  }'
```

### Dry Run Test
```bash
# Test deployment with dry run (safe - no actual deployment)
curl -X POST http://localhost:8080/api/v1/plugins/helm-openshift-deploy/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "operation": "deploy:helm",
    "parameters": {
      "openshift_api_url": "https://api.your-cluster.com:6443",
      "openshift_project": "test-project",
      "username": "test-user",
      "password": "test-password",
      "bitbucket_repo_url": "https://bitbucket.org/yourorg/charts.git",
      "chart_path": "charts/test-app",
      "values_path": "values-dev.yaml",
      "dry_run": true
    }
  }'
```

## 🔄 Updates and Maintenance

### Plugin Updates
```bash
# Update plugin code
git pull origin main

# Rebuild and reinstall
make clean
make build
make install

# Reload plugin (if service is running)
curl -X POST http://localhost:8080/api/v1/plugins/helm-openshift-deploy/reload
```

### Health Monitoring
Monitor plugin health through:
- **UI**: Plugin Management → Status
- **API**: `/api/v1/plugins/helm-openshift-deploy/status`
- **Logs**: `/api/v1/plugins/helm-openshift-deploy/logs`

## 🐛 Troubleshooting

### Common Issues

#### Plugin Not Found
```bash
# Check plugin directory
ls -la ../../backend/workflow-service/plugins/

# Verify plugin manifest
cat ../../backend/workflow-service/plugins/helm-openshift-deploy/plugin.yaml
```

#### Build Failures
```bash
# Check Go version
go version

# Clean and rebuild
make clean
make build
```

#### Permission Errors
```bash
# Check file permissions
chmod +x ../../backend/workflow-service/plugins/helm-openshift-deploy/helm-openshift-plugin

# Check directory permissions
chmod -R 755 ../../backend/workflow-service/plugins/helm-openshift-deploy/
```

#### Service Connection Issues
```bash
# Check workflow service status
curl http://localhost:8080/health

# Check plugin endpoint
curl http://localhost:8080/api/v1/plugins
```

### Getting Help
- Check [Troubleshooting Guide](./TROUBLESHOOTING.md)
- Review [Configuration Reference](./CONFIGURATION.md)
- Examine plugin logs in UI or via API

## 📚 Next Steps

After successful installation:
1. [Configure Your First Deployment](./QUICK_START.md)
2. [Set Up Environment-Specific Configurations](./ENVIRONMENTS.md)
3. [Create Deployment Workflows](./DEPLOYMENT.md)
4. [Explore Advanced Features](./CONFIGURATION.md)

---

**Need Help?** Check our [Troubleshooting Guide](./TROUBLESHOOTING.md) or review the [API Documentation](./API.md).
