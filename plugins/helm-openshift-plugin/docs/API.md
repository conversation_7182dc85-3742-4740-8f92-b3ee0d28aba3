# API Reference

Complete API reference for the Helm OpenShift Deploy Plugin.

## 🔗 Base URL

All API endpoints are relative to the workflow service base URL:
```
http://localhost:8080/api/v1/plugins/helm-openshift-deploy
```

## 🔐 Authentication

All API calls require authentication via Bearer token:
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8080/api/v1/plugins/helm-openshift-deploy/execute
```

## 📋 Operations Overview

| Operation | Description | Use Case |
|-----------|-------------|----------|
| `deploy:helm` | Main deployment operation | Install or upgrade Helm releases |
| `helm:status` | Get release status | Check deployment status |
| `helm:rollback` | Rollback release | Revert to previous version |
| `deploy:validate` | Validate parameters | Test configuration before deployment |

## 🚀 deploy:helm

Main deployment operation that performs Helm install or upgrade.

### Endpoint
```
POST /api/v1/plugins/helm-openshift-deploy/execute
```

### Request Body
```json
{
  "operation": "deploy:helm",
  "parameters": {
    "openshift_api_url": "https://api.cluster.com:6443",
    "openshift_project": "my-app-dev",
    "username": "developer",
    "password": "secret123",
    "bitbucket_repo_url": "https://bitbucket.org/org/charts.git",
    "chart_path": "charts/my-app",
    "values_path": "values-dev.yaml",
    "release_name": "my-app-dev",
    "helm_timeout": "300s",
    "dry_run": false,
    "extra_values": {
      "image.tag": "v1.2.3",
      "replicaCount": 2
    }
  }
}
```

### Parameters

#### Required Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `openshift_api_url` | string | OpenShift cluster API URL |
| `openshift_project` | string | Target namespace/project |
| `username` | string | OpenShift username |
| `password` | string | OpenShift password |
| `bitbucket_repo_url` | string | Repository URL containing Helm charts |
| `chart_path` | string | Path to chart from repository root |
| `values_path` | string | Path to values file |

#### Optional Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `release_name` | string | chart name | Helm release name |
| `helm_timeout` | string | `300s` | Operation timeout |
| `dry_run` | boolean | `false` | Perform dry run |
| `force_upgrade` | boolean | `false` | Force upgrade |
| `extra_values` | object | `{}` | Additional Helm values |

### Response

#### Success Response (200)
```json
{
  "deployment_result": {
    "success": true,
    "release_name": "my-app-dev",
    "namespace": "my-app-dev",
    "status": "deployed",
    "revision": 2,
    "last_deployed": "2024-01-15T10:30:00Z",
    "helm_output": "Release \"my-app-dev\" has been upgraded...",
    "execution_time": "45.2s",
    "resources": [
      {
        "kind": "Deployment",
        "name": "my-app",
        "namespace": "my-app-dev",
        "status": "deployed"
      },
      {
        "kind": "Service",
        "name": "my-app",
        "namespace": "my-app-dev",
        "status": "deployed"
      }
    ]
  }
}
```

#### Error Response (400/500)
```json
{
  "deployment_result": {
    "success": false,
    "release_name": "my-app-dev",
    "namespace": "my-app-dev",
    "error": "helm upgrade failed: context deadline exceeded",
    "execution_time": "300.0s"
  }
}
```

### Example Usage

#### Basic Deployment
```bash
curl -X POST http://localhost:8080/api/v1/plugins/helm-openshift-deploy/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "operation": "deploy:helm",
    "parameters": {
      "openshift_api_url": "https://api.cluster.com:6443",
      "openshift_project": "my-app-dev",
      "username": "developer",
      "password": "secret123",
      "bitbucket_repo_url": "https://bitbucket.org/org/charts.git",
      "chart_path": "charts/my-app",
      "values_path": "values-dev.yaml"
    }
  }'
```

#### Dry Run Deployment
```bash
curl -X POST http://localhost:8080/api/v1/plugins/helm-openshift-deploy/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "operation": "deploy:helm",
    "parameters": {
      "openshift_api_url": "https://api.cluster.com:6443",
      "openshift_project": "my-app-dev",
      "username": "developer",
      "password": "secret123",
      "bitbucket_repo_url": "https://bitbucket.org/org/charts.git",
      "chart_path": "charts/my-app",
      "values_path": "values-dev.yaml",
      "dry_run": true
    }
  }'
```

## 📊 helm:status

Get the current status of a Helm release.

### Request Body
```json
{
  "operation": "helm:status",
  "parameters": {
    "release_name": "my-app-dev",
    "namespace": "my-app-dev"
  }
}
```

### Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `release_name` | string | Yes | Name of the Helm release |
| `namespace` | string | No | Kubernetes namespace (uses plugin config if not provided) |

### Response

#### Success Response (200)
```json
{
  "exists": true,
  "status": {
    "name": "my-app-dev",
    "info": {
      "first_deployed": "2024-01-15T09:00:00Z",
      "last_deployed": "2024-01-15T10:30:00Z",
      "deleted": "",
      "description": "Upgrade complete",
      "status": "deployed"
    },
    "chart": {
      "metadata": {
        "name": "my-app",
        "version": "1.0.0"
      }
    },
    "config": {
      "image": {
        "tag": "v1.2.3"
      },
      "replicaCount": 2
    },
    "version": 2,
    "namespace": "my-app-dev"
  }
}
```

#### Release Not Found (200)
```json
{
  "exists": false,
  "error": "release: not found"
}
```

### Example Usage
```bash
curl -X POST http://localhost:8080/api/v1/plugins/helm-openshift-deploy/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "operation": "helm:status",
    "parameters": {
      "release_name": "my-app-dev",
      "namespace": "my-app-dev"
    }
  }'
```

## ⏪ helm:rollback

Rollback a Helm release to a previous version.

### Request Body
```json
{
  "operation": "helm:rollback",
  "parameters": {
    "release_name": "my-app-dev",
    "namespace": "my-app-dev",
    "revision": 1
  }
}
```

### Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `release_name` | string | Yes | Name of the Helm release |
| `namespace` | string | No | Kubernetes namespace |
| `revision` | integer | No | Specific revision to rollback to (defaults to previous) |

### Response

#### Success Response (200)
```json
{
  "success": true,
  "release_name": "my-app-dev",
  "namespace": "my-app-dev",
  "output": "Rollback was a success! Happy Helming!"
}
```

#### Error Response (400/500)
```json
{
  "success": false,
  "release_name": "my-app-dev",
  "namespace": "my-app-dev",
  "error": "rollback failed: revision 5 not found"
}
```

### Example Usage
```bash
curl -X POST http://localhost:8080/api/v1/plugins/helm-openshift-deploy/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "operation": "helm:rollback",
    "parameters": {
      "release_name": "my-app-dev",
      "namespace": "my-app-dev",
      "revision": 1
    }
  }'
```

## ✅ deploy:validate

Validate deployment parameters without executing the deployment.

### Request Body
```json
{
  "operation": "deploy:validate",
  "parameters": {
    "openshift_api_url": "https://api.cluster.com:6443",
    "openshift_project": "my-app-dev",
    "username": "developer",
    "password": "secret123",
    "bitbucket_repo_url": "https://bitbucket.org/org/charts.git",
    "chart_path": "charts/my-app",
    "values_path": "values-dev.yaml"
  }
}
```

### Parameters
Same as `deploy:helm` operation parameters.

### Response

#### Valid Configuration (200)
```json
{
  "valid": true,
  "request": {
    "openshift_api_url": "https://api.cluster.com:6443",
    "openshift_project": "my-app-dev",
    "username": "developer",
    "bitbucket_repo_url": "https://bitbucket.org/org/charts.git",
    "chart_path": "charts/my-app",
    "values_path": "values-dev.yaml",
    "release_name": "my-app",
    "helm_timeout": "300s"
  }
}
```

#### Invalid Configuration (200)
```json
{
  "valid": false,
  "errors": [
    "OpenShift API URL must start with http:// or https://",
    "Helm timeout must end with s, m, or h",
    "Required tool 'helm' not found in PATH"
  ],
  "request": {
    "openshift_api_url": "invalid-url",
    "helm_timeout": "invalid-timeout"
  }
}
```

### Example Usage
```bash
curl -X POST http://localhost:8080/api/v1/plugins/helm-openshift-deploy/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "operation": "deploy:validate",
    "parameters": {
      "openshift_api_url": "https://api.cluster.com:6443",
      "openshift_project": "my-app-dev",
      "username": "developer",
      "password": "secret123",
      "bitbucket_repo_url": "https://bitbucket.org/org/charts.git",
      "chart_path": "charts/my-app",
      "values_path": "values-dev.yaml"
    }
  }'
```

## 🔧 Plugin Management APIs

### Get Plugin Status
```bash
GET /api/v1/plugins/helm-openshift-deploy/status
```

### Get Plugin Logs
```bash
GET /api/v1/plugins/helm-openshift-deploy/logs?lines=100&follow=false
```

### Reload Plugin
```bash
POST /api/v1/plugins/helm-openshift-deploy/reload
```

## 🐛 Error Codes

| HTTP Code | Error Type | Description |
|-----------|------------|-------------|
| 400 | Bad Request | Invalid parameters or configuration |
| 401 | Unauthorized | Missing or invalid authentication |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Plugin or resource not found |
| 500 | Internal Error | Plugin execution error |
| 503 | Service Unavailable | Plugin not available or disabled |

## 📝 Response Schemas

### DeploymentResult Schema
```json
{
  "type": "object",
  "properties": {
    "success": {"type": "boolean"},
    "release_name": {"type": "string"},
    "namespace": {"type": "string"},
    "status": {"type": "string"},
    "revision": {"type": "integer"},
    "last_deployed": {"type": "string", "format": "date-time"},
    "helm_output": {"type": "string"},
    "error": {"type": "string"},
    "resources": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "kind": {"type": "string"},
          "name": {"type": "string"},
          "namespace": {"type": "string"},
          "status": {"type": "string"}
        }
      }
    },
    "execution_time": {"type": "string"}
  }
}
```

---

**Next Steps:**
- [Try the Quick Start Guide](./QUICK_START.md)
- [Explore Configuration Options](./CONFIGURATION.md)
- [Learn Deployment Patterns](./DEPLOYMENT.md)
