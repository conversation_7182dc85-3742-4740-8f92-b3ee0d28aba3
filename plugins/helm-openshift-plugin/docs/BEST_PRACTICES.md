# Best Practices Guide

Production-ready best practices for the Helm OpenShift Deploy Plugin.

## 🎯 Core Principles

### 1. Security First
- **Never store credentials in plain text**
- **Use secrets management for sensitive data**
- **Implement proper RBAC controls**
- **Regular credential rotation**
- **Audit all deployment activities**

### 2. Reliability & Resilience
- **Always test with dry runs first**
- **Implement comprehensive rollback strategies**
- **Use health checks and readiness probes**
- **Set appropriate timeouts**
- **Monitor deployment metrics**

### 3. Consistency & Standardization
- **Use standardized naming conventions**
- **Implement consistent environment configurations**
- **Follow semantic versioning**
- **Standardize Helm chart structures**
- **Document all configurations**

## 🔐 Security Best Practices

### Credential Management

**✅ DO:**
```json
{
  "username": "{{secret:openshift-username}}",
  "password": "{{secret:openshift-password}}",
  "database_url": "{{secret:database-connection-string}}"
}
```

**❌ DON'T:**
```json
{
  "username": "admin",
  "password": "password123",
  "database_url": "********************************/db"
}
```

### RBAC Configuration

**Principle of Least Privilege:**
```yaml
# Create dedicated service accounts
apiVersion: v1
kind: ServiceAccount
metadata:
  name: helm-deployer
  namespace: my-app-dev

---
# Grant minimal required permissions
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: helm-deployer-binding
  namespace: my-app-dev
subjects:
- kind: ServiceAccount
  name: helm-deployer
  namespace: my-app-dev
roleRef:
  kind: ClusterRole
  name: edit  # Or create custom role with specific permissions
  apiGroup: rbac.authorization.k8s.io
```

### Secret Rotation

**Implement automated credential rotation:**
```yaml
# Workflow for credential rotation
name: "Rotate OpenShift Credentials"
schedule: "0 2 * * 0"  # Weekly at 2 AM Sunday
steps:
  - name: generate-new-credentials
    type: script
    script: |
      # Generate new service account token
      oc create token helm-deployer --duration=168h
  
  - name: update-secrets
    type: plugin
    plugin: secrets-service
    operation: update-secret
    parameters:
      secret_name: "openshift-token"
      secret_value: "{{.NEW_TOKEN}}"
  
  - name: test-new-credentials
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:validate
```

## 🏗️ Chart Development Best Practices

### Chart Structure

**Recommended structure:**
```
charts/my-application/
├── Chart.yaml
├── values.yaml
├── values.schema.json
├── README.md
├── .helmignore
├── templates/
│   ├── deployment.yaml
│   ├── service.yaml
│   ├── ingress.yaml
│   ├── configmap.yaml
│   ├── secret.yaml
│   ├── serviceaccount.yaml
│   ├── rbac.yaml
│   ├── hpa.yaml
│   ├── pdb.yaml
│   ├── networkpolicy.yaml
│   └── tests/
│       └── test-connection.yaml
└── crds/  # If using custom resources
```

### Values File Organization

**Environment-specific values:**
```yaml
# values-base.yaml (common values)
nameOverride: ""
fullnameOverride: ""

image:
  repository: my-app
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 8080

# values-dev.yaml
replicaCount: 1
image:
  tag: "latest"
  pullPolicy: Always
resources:
  requests:
    memory: "256Mi"
    cpu: "250m"

# values-prod.yaml
replicaCount: 3
image:
  tag: "v1.0.0"
  pullPolicy: IfNotPresent
resources:
  requests:
    memory: "1Gi"
    cpu: "1000m"
  limits:
    memory: "2Gi"
    cpu: "2000m"
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
```

### Template Best Practices

**Use proper labels and selectors:**
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "myapp.fullname" . }}
  labels:
    {{- include "myapp.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "myapp.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
      labels:
        {{- include "myapp.selectorLabels" . | nindent 8 }}
```

**Implement health checks:**
```yaml
# deployment.yaml
containers:
- name: {{ .Chart.Name }}
  image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
  ports:
  - name: http
    containerPort: 8080
    protocol: TCP
  livenessProbe:
    httpGet:
      path: /health
      port: http
    initialDelaySeconds: 30
    periodSeconds: 10
  readinessProbe:
    httpGet:
      path: /ready
      port: http
    initialDelaySeconds: 5
    periodSeconds: 5
```

## 🚀 Deployment Best Practices

### Pre-Deployment Validation

**Always validate before deploying:**
```yaml
steps:
  - name: validate-chart
    type: script
    script: |
      helm lint charts/my-application/
      helm template charts/my-application/ -f values-{{.ENVIRONMENT}}.yaml --validate

  - name: validate-config
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:validate

  - name: dry-run-deployment
    type: plugin
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      dry_run: true
```

### Deployment Strategies

**Rolling Updates (Default):**
```yaml
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 25%
    maxSurge: 25%
```

**Blue-Green Deployments:**
```yaml
# Use separate releases for blue/green
- name: deploy-green
  parameters:
    release_name: "my-app-green"
    extra_values:
      service.selector.version: "green"

- name: switch-traffic
  type: script
  script: |
    oc patch service my-app -p '{"spec":{"selector":{"version":"green"}}}'
```

### Monitoring and Observability

**Implement comprehensive monitoring:**
```yaml
# Add monitoring annotations
metadata:
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
    prometheus.io/path: "/metrics"

# Include monitoring resources
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "myapp.fullname" . }}
spec:
  selector:
    matchLabels:
      {{- include "myapp.selectorLabels" . | nindent 6 }}
  endpoints:
  - port: http
    path: /metrics
```

## 🔄 Environment Management

### Environment Promotion Pipeline

**Structured promotion flow:**
```yaml
# Development → QA → Production
environments:
  dev:
    auto_deploy: true
    approval_required: false
    rollback_on_failure: true
  
  qa:
    auto_deploy: false
    approval_required: true
    approvers: ["qa-team"]
    rollback_on_failure: true
  
  prod:
    auto_deploy: false
    approval_required: true
    approvers: ["release-manager", "devops-lead"]
    rollback_on_failure: true
    maintenance_window: "02:00-04:00 UTC"
```

### Configuration Management

**Environment-specific configurations:**
```yaml
# Use ConfigMaps for non-sensitive config
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "myapp.fullname" . }}-config
data:
  database_host: {{ .Values.database.host }}
  log_level: {{ .Values.logging.level }}
  feature_flags: {{ .Values.features | toJson }}

# Use Secrets for sensitive data
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "myapp.fullname" . }}-secrets
type: Opaque
data:
  database_password: {{ .Values.database.password | b64enc }}
  api_key: {{ .Values.external.apiKey | b64enc }}
```

## 📊 Performance Optimization

### Resource Management

**Right-size your resources:**
```yaml
# Development
resources:
  requests:
    memory: "256Mi"
    cpu: "250m"
  limits:
    memory: "512Mi"
    cpu: "500m"

# Production
resources:
  requests:
    memory: "1Gi"
    cpu: "1000m"
  limits:
    memory: "2Gi"
    cpu: "2000m"
```

**Implement autoscaling:**
```yaml
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80
```

### Image Management

**Use specific image tags:**
```yaml
# ✅ Good
image:
  tag: "v1.2.3"
  pullPolicy: "IfNotPresent"

# ❌ Bad
image:
  tag: "latest"
  pullPolicy: "Always"
```

**Optimize image pulls:**
```yaml
# Use image pull secrets for private registries
imagePullSecrets:
  - name: registry-secret

# Use node affinity for image locality
affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      preference:
        matchExpressions:
        - key: node.kubernetes.io/instance-type
          operator: In
          values: ["m5.large", "m5.xlarge"]
```

## 🔍 Monitoring and Alerting

### Health Checks

**Implement proper health endpoints:**
```yaml
# Application health endpoints
livenessProbe:
  httpGet:
    path: /health/live
    port: 8080
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /health/ready
    port: 8080
  initialDelaySeconds: 5
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 3
```

### Deployment Monitoring

**Monitor deployment progress:**
```yaml
- name: monitor-deployment
  type: script
  script: |
    #!/bin/bash
    echo "Monitoring deployment progress..."
    
    # Wait for rollout to complete
    oc rollout status deployment/my-app -n {{.NAMESPACE}} --timeout=600s
    
    # Check pod readiness
    kubectl wait --for=condition=ready pod -l app=my-app -n {{.NAMESPACE}} --timeout=300s
    
    # Verify service endpoints
    kubectl get endpoints my-app -n {{.NAMESPACE}}
```

## 📝 Documentation Standards

### Chart Documentation

**Include comprehensive README:**
```markdown
# My Application Helm Chart

## Prerequisites
- Kubernetes 1.19+
- Helm 3.0+
- OpenShift 4.6+

## Installation
```bash
helm install my-app ./charts/my-application -f values-prod.yaml
```

## Configuration
| Parameter | Description | Default |
|-----------|-------------|---------|
| `replicaCount` | Number of replicas | `1` |
| `image.repository` | Image repository | `my-app` |
```

### Deployment Runbooks

**Create operational runbooks:**
```markdown
# Deployment Runbook

## Pre-deployment Checklist
- [ ] Validate chart syntax
- [ ] Test with dry run
- [ ] Verify resource quotas
- [ ] Check maintenance windows

## Deployment Steps
1. Execute validation workflow
2. Deploy to staging
3. Run smoke tests
4. Get approval for production
5. Deploy to production
6. Monitor deployment

## Rollback Procedure
1. Check deployment status
2. Identify last known good version
3. Execute rollback workflow
4. Verify rollback success
```

---

**Ready for Production?** Follow these best practices to ensure reliable, secure, and maintainable deployments with the Helm OpenShift Deploy Plugin!
