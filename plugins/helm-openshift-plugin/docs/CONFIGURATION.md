# Configuration Reference

Complete reference for configuring the Helm OpenShift Deploy Plugin.

## 📋 Configuration Overview

The plugin supports multiple configuration methods:
- **Static Configuration** - Set in plugin configuration
- **Runtime Parameters** - Override via workflow parameters
- **Environment Variables** - System environment variables
- **Secrets Integration** - Secure credential injection

## 🔧 Core Configuration Parameters

### Required Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `openshift_api_url` | string | OpenShift cluster API URL | `https://api.cluster.com:6443` |
| `openshift_project` | string | Target OpenShift project/namespace | `my-app-dev` |
| `username` | string | OpenShift username | `developer` |
| `password` | string | OpenShift password | `secret123` |
| `bitbucket_repo_url` | string | Bitbucket repository URL | `https://bitbucket.org/org/charts.git` |
| `chart_path` | string | Path to Helm chart from repo root | `charts/my-application` |
| `values_path` | string | Path to values file | `values-dev.yaml` |

### Optional Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `release_name` | string | chart name | Helm release name |
| `helm_timeout` | string | `300s` | Helm operation timeout |
| `dry_run` | boolean | `false` | Perform dry run without deploying |
| `force_upgrade` | boolean | `false` | Force upgrade even if no changes |
| `extra_values` | object | `{}` | Additional Helm values to override |

## 🏗️ Configuration Examples

### Basic Configuration
```json
{
  "openshift_api_url": "https://api.dev-cluster.com:6443",
  "openshift_project": "my-app-dev",
  "username": "developer",
  "password": "dev-password",
  "bitbucket_repo_url": "https://bitbucket.org/myorg/helm-charts.git",
  "chart_path": "charts/my-application",
  "values_path": "values-dev.yaml"
}
```

### Advanced Configuration
```json
{
  "openshift_api_url": "https://api.prod-cluster.com:6443",
  "openshift_project": "my-app-prod",
  "username": "prod-deployer",
  "password": "{{secret:openshift-prod-password}}",
  "bitbucket_repo_url": "https://bitbucket.org/myorg/helm-charts.git",
  "chart_path": "charts/my-application",
  "values_path": "values-prod.yaml",
  "release_name": "my-app-production",
  "helm_timeout": "600s",
  "extra_values": {
    "image.tag": "v1.2.3",
    "replicaCount": 3,
    "resources.limits.memory": "1Gi"
  }
}
```

### Multi-Environment Configuration
```json
{
  "environments": {
    "dev": {
      "openshift_api_url": "https://api.dev-cluster.com:6443",
      "openshift_project": "my-app-dev",
      "values_path": "values-dev.yaml",
      "helm_timeout": "300s",
      "extra_values": {
        "replicaCount": 1,
        "resources.requests.memory": "256Mi"
      }
    },
    "qa": {
      "openshift_api_url": "https://api.qa-cluster.com:6443",
      "openshift_project": "my-app-qa",
      "values_path": "values-qa.yaml",
      "helm_timeout": "450s",
      "extra_values": {
        "replicaCount": 2,
        "resources.requests.memory": "512Mi"
      }
    },
    "prod": {
      "openshift_api_url": "https://api.prod-cluster.com:6443",
      "openshift_project": "my-app-prod",
      "values_path": "values-prod.yaml",
      "helm_timeout": "600s",
      "extra_values": {
        "replicaCount": 3,
        "resources.requests.memory": "1Gi"
      }
    }
  },
  "common": {
    "username": "{{secret:openshift-username}}",
    "password": "{{secret:openshift-password}}",
    "bitbucket_repo_url": "https://bitbucket.org/myorg/helm-charts.git",
    "chart_path": "charts/my-application"
  }
}
```

## 🔐 Security Configuration

### Secret Integration
Use the Deploy Orchestrator secrets service for sensitive data:

```json
{
  "username": "{{secret:openshift-username}}",
  "password": "{{secret:openshift-password}}",
  "bitbucket_token": "{{secret:bitbucket-access-token}}"
}
```

### Environment Variables
Reference system environment variables:

```json
{
  "username": "{{env:OPENSHIFT_USERNAME}}",
  "password": "{{env:OPENSHIFT_PASSWORD}}",
  "openshift_api_url": "{{env:OPENSHIFT_API_URL}}"
}
```

### Credential Rotation
Support for credential rotation:

```json
{
  "credentials": {
    "rotation_enabled": true,
    "rotation_interval": "24h",
    "backup_credentials": {
      "username": "{{secret:openshift-username-backup}}",
      "password": "{{secret:openshift-password-backup}}"
    }
  }
}
```

## 🎯 Parameter Validation

### URL Validation
```json
{
  "openshift_api_url": {
    "pattern": "^https?://.*",
    "required": true,
    "description": "Must be a valid HTTP/HTTPS URL"
  }
}
```

### Project Name Validation
```json
{
  "openshift_project": {
    "pattern": "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$",
    "required": true,
    "description": "Must be a valid Kubernetes namespace name"
  }
}
```

### Timeout Validation
```json
{
  "helm_timeout": {
    "pattern": "^[0-9]+(s|m|h)$",
    "default": "300s",
    "description": "Must be a valid duration (e.g., 300s, 5m, 1h)"
  }
}
```

## 🔄 Runtime Parameter Override

### Workflow Parameter Override
Override configuration at runtime through workflow parameters:

```yaml
# Workflow definition
parameters:
  - name: TARGET_ENVIRONMENT
    type: string
    default: "dev"
  - name: IMAGE_TAG
    type: string
    required: true
  - name: REPLICA_COUNT
    type: number
    default: 1

steps:
  - name: deploy
    plugin: helm-openshift-deploy
    operation: deploy:helm
    parameters:
      openshift_project: "my-app-{{.TARGET_ENVIRONMENT}}"
      values_path: "values-{{.TARGET_ENVIRONMENT}}.yaml"
      extra_values:
        image.tag: "{{.IMAGE_TAG}}"
        replicaCount: "{{.REPLICA_COUNT}}"
```

### Dynamic Configuration
Generate configuration dynamically:

```json
{
  "config_template": {
    "openshift_api_url": "https://api.{{.CLUSTER_NAME}}.com:6443",
    "openshift_project": "{{.APP_NAME}}-{{.ENVIRONMENT}}",
    "values_path": "values-{{.ENVIRONMENT}}.yaml",
    "release_name": "{{.APP_NAME}}-{{.ENVIRONMENT}}"
  }
}
```

## 📊 Configuration Profiles

### Development Profile
```json
{
  "profile": "development",
  "config": {
    "helm_timeout": "300s",
    "dry_run": false,
    "force_upgrade": true,
    "extra_values": {
      "replicaCount": 1,
      "image.pullPolicy": "Always",
      "resources.requests.memory": "256Mi"
    }
  }
}
```

### Production Profile
```json
{
  "profile": "production",
  "config": {
    "helm_timeout": "900s",
    "dry_run": false,
    "force_upgrade": false,
    "extra_values": {
      "replicaCount": 3,
      "image.pullPolicy": "IfNotPresent",
      "resources.requests.memory": "1Gi",
      "resources.limits.memory": "2Gi"
    }
  }
}
```

## 🔧 Advanced Configuration

### Custom Helm Values
Override specific Helm values:

```json
{
  "extra_values": {
    "image.repository": "my-registry.com/my-app",
    "image.tag": "v1.2.3",
    "service.type": "LoadBalancer",
    "ingress.enabled": true,
    "ingress.hosts[0].host": "my-app.example.com",
    "resources.limits.cpu": "1000m",
    "resources.limits.memory": "2Gi",
    "autoscaling.enabled": true,
    "autoscaling.minReplicas": 2,
    "autoscaling.maxReplicas": 10
  }
}
```

### Repository Configuration
Advanced repository settings:

```json
{
  "repository": {
    "url": "https://bitbucket.org/myorg/helm-charts.git",
    "branch": "main",
    "depth": 1,
    "auth": {
      "type": "token",
      "token": "{{secret:bitbucket-token}}"
    },
    "cache": {
      "enabled": true,
      "ttl": "1h"
    }
  }
}
```

### Deployment Strategy
Configure deployment strategies:

```json
{
  "deployment_strategy": {
    "type": "rolling",
    "rolling": {
      "max_unavailable": "25%",
      "max_surge": "25%"
    },
    "hooks": {
      "pre_deploy": ["helm test"],
      "post_deploy": ["kubectl rollout status"]
    }
  }
}
```

## 📝 Configuration Validation

### Schema Validation
The plugin validates configuration against a JSON schema:

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "required": [
    "openshift_api_url",
    "openshift_project",
    "username",
    "password",
    "bitbucket_repo_url",
    "chart_path",
    "values_path"
  ],
  "properties": {
    "openshift_api_url": {
      "type": "string",
      "pattern": "^https?://.*"
    },
    "helm_timeout": {
      "type": "string",
      "pattern": "^[0-9]+(s|m|h)$"
    }
  }
}
```

### Configuration Testing
Test configuration before deployment:

```bash
# Validate configuration
curl -X POST /api/v1/plugins/helm-openshift-deploy/execute \
  -d '{"operation": "deploy:validate", "parameters": {...}}'
```

## 🔗 Integration Configuration

### Monitoring Integration
```json
{
  "monitoring": {
    "enabled": true,
    "metrics_endpoint": "/metrics",
    "health_check_interval": "30s",
    "alerts": {
      "deployment_failure": true,
      "timeout_warning": true
    }
  }
}
```

### Notification Integration
```json
{
  "notifications": {
    "slack": {
      "webhook_url": "{{secret:slack-webhook}}",
      "channel": "#deployments",
      "events": ["success", "failure"]
    },
    "email": {
      "smtp_server": "smtp.company.com",
      "recipients": ["<EMAIL>"]
    }
  }
}
```

---

**Next Steps:**
- [Deploy Your First Application](./QUICK_START.md)
- [Explore Deployment Patterns](./DEPLOYMENT.md)
- [Set Up Environment Management](./ENVIRONMENTS.md)
