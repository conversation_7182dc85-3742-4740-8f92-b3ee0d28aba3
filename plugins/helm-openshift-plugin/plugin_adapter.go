package main

import (
	"context"

	"github.com/claudio/deploy-orchestrator/shared/models"
	"go.uber.org/zap"
)

// ProviderExecutor interface (simplified version for plugin compatibility)
type ProviderExecutor interface {
	GetProviderType() models.ProviderType
	GetCapabilities() []string
	ValidateConfig(config *models.ProviderConfig) error
	Execute(ctx context.Context, request *ExecutionRequest) (*ExecutionResult, error)
}

// ExecutionRequest represents a deployment execution request
type ExecutionRequest struct {
	Operation   string                 `json:"operation"`
	Parameters  map[string]interface{} `json:"parameters"`
	ProjectID   string                 `json:"project_id"`
	Environment string                 `json:"environment"`
}

// ExecutionResult represents the result of a deployment execution
type ExecutionResult struct {
	Success   bool                   `json:"success"`
	Message   string                 `json:"message"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Error     string                 `json:"error,omitempty"`
	Resources []string               `json:"resources,omitempty"`
}

// PluginInterface defines the interface that all plugins must implement
type PluginInterface interface {
	GetExecutor() ProviderExecutor
	Initialize(ctx context.Context) error
	Shutdown(ctx context.Context) error
	Health(ctx context.Context) error
	GetInfo() PluginInfo
}

// PluginInfo represents plugin information
type PluginInfo struct {
	Name        string `json:"name"`
	Version     string `json:"version"`
	Type        string `json:"type"`
	Description string `json:"description"`
}

// HelmOpenShiftPluginWrapper wraps our existing plugin for the Go plugin system
type HelmOpenShiftPluginWrapper struct {
	plugin *HelmOpenShiftPlugin
	logger *zap.Logger
}

// HelmOpenShiftProviderExecutor implements ProviderExecutor for our plugin
type HelmOpenShiftProviderExecutor struct {
	plugin *HelmOpenShiftPlugin
	logger *zap.Logger
}

// NewPlugin creates a new plugin instance (required by Go plugin system)
func NewPlugin(config map[string]interface{}, logger *zap.Logger) (PluginInterface, error) {
	plugin := NewHelmOpenShiftPlugin()

	// Initialize with config if provided
	if len(config) > 0 {
		if err := plugin.Initialize(config); err != nil {
			logger.Warn("Failed to initialize plugin with config", zap.Error(err))
		}
	}

	return &HelmOpenShiftPluginWrapper{
		plugin: plugin,
		logger: logger,
	}, nil
}

// GetExecutor returns the provider executor
func (p *HelmOpenShiftPluginWrapper) GetExecutor() ProviderExecutor {
	return &HelmOpenShiftProviderExecutor{
		plugin: p.plugin,
		logger: p.logger,
	}
}

// ProviderExecutor implementation
func (e *HelmOpenShiftProviderExecutor) GetProviderType() models.ProviderType {
	return models.ProviderType("helm-openshift-deploy")
}

func (e *HelmOpenShiftProviderExecutor) GetCapabilities() []string {
	return []string{
		"deploy:helm",
		"deploy:openshift",
		"deploy:kubernetes",
		"git:clone",
		"helm:install",
		"helm:upgrade",
		"helm:status",
		"helm:rollback",
	}
}

func (e *HelmOpenShiftProviderExecutor) ValidateConfig(config *models.ProviderConfig) error {
	// Basic validation - could be enhanced
	return nil
}

func (e *HelmOpenShiftProviderExecutor) Execute(ctx context.Context, request *ExecutionRequest) (*ExecutionResult, error) {
	// Delegate to our plugin
	result, err := e.plugin.Execute(ctx, request.Operation, request.Parameters)
	if err != nil {
		return &ExecutionResult{
			Success: false,
			Error:   err.Error(),
		}, err
	}

	return &ExecutionResult{
		Success: true,
		Data:    result,
	}, nil
}

// Initialize initializes the plugin
func (p *HelmOpenShiftPluginWrapper) Initialize(ctx context.Context) error {
	p.logger.Info("Initializing Helm OpenShift plugin")
	return nil
}

// Shutdown shuts down the plugin
func (p *HelmOpenShiftPluginWrapper) Shutdown(ctx context.Context) error {
	p.logger.Info("Shutting down Helm OpenShift plugin")
	return nil
}

// Health checks plugin health
func (p *HelmOpenShiftPluginWrapper) Health(ctx context.Context) error {
	// Basic health check - could be enhanced to check dependencies
	return nil
}

// GetInfo returns plugin information
func (p *HelmOpenShiftPluginWrapper) GetInfo() PluginInfo {
	return PluginInfo{
		Name:        "helm-openshift-deploy",
		Version:     "1.0.0",
		Type:        "deployment-provider",
		Description: "Helm OpenShift deployment provider with Bitbucket integration",
	}
}
