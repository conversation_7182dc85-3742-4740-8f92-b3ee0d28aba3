#!/bin/bash

# Helm OpenShift Plugin Test Script
set -e

PLUGIN_NAME="helm-openshift-deploy"
WORKFLOW_SERVICE_URL="http://localhost:8085"

echo "🧪 Helm OpenShift Plugin Test Suite"
echo "===================================="

# Function to check if a command exists
check_command() {
    if ! command -v "$1" &> /dev/null; then
        echo "❌ Error: $1 is not installed or not in PATH"
        echo "Please install $1 and try again"
        exit 1
    fi
    echo "✅ $1 is available"
}

# Function to check if service is running
check_service() {
    local url=$1
    local service_name=$2
    
    if curl -s -f "$url/health" > /dev/null 2>&1; then
        echo "✅ $service_name is running"
        return 0
    else
        echo "❌ $service_name is not running at $url"
        return 1
    fi
}

echo ""
echo "📋 Step 1: Checking Prerequisites"
echo "================================="

# Check required tools
echo "Checking required tools..."
check_command "go"
check_command "make"
check_command "curl"
check_command "jq"

# Check OpenShift/Kubernetes tools
echo ""
echo "Checking OpenShift/Kubernetes tools:"
if command -v oc &> /dev/null; then
    echo "✅ oc (OpenShift CLI) is available ($(oc version --client --short 2>/dev/null || echo 'version unknown'))"
    OC_AVAILABLE=true
else
    echo "⚠️  oc (OpenShift CLI) is not available"
    OC_AVAILABLE=false
fi

if command -v helm &> /dev/null; then
    echo "✅ helm is available ($(helm version --short 2>/dev/null || echo 'version unknown'))"
    HELM_AVAILABLE=true
else
    echo "⚠️  helm is not available"
    HELM_AVAILABLE=false
fi

if command -v kubectl &> /dev/null; then
    echo "✅ kubectl is available ($(kubectl version --client --short 2>/dev/null || echo 'version unknown'))"
    KUBECTL_AVAILABLE=true
else
    echo "⚠️  kubectl is not available"
    KUBECTL_AVAILABLE=false
fi

echo ""
echo "📦 Step 2: Building Plugin"
echo "=========================="

echo "Building Helm OpenShift plugin..."
if make build; then
    echo "✅ Plugin built successfully"
else
    echo "❌ Failed to build plugin"
    exit 1
fi

echo ""
echo "📁 Step 3: Installing Plugin"
echo "============================"

echo "Installing plugin to workflow service..."
if make install; then
    echo "✅ Plugin installed successfully"
else
    echo "❌ Failed to install plugin"
    exit 1
fi

echo ""
echo "🔌 Step 4: Checking Workflow Service"
echo "===================================="

echo "Checking if workflow service is running..."
if check_service "$WORKFLOW_SERVICE_URL" "Workflow Service"; then
    SERVICE_RUNNING=true
else
    SERVICE_RUNNING=false
    echo ""
    echo "ℹ️  Workflow service is not running. You can:"
    echo "   1. Start the workflow service: cd ../../backend/workflow-service && go run main.go"
    echo "   2. The plugin will be automatically loaded when the service starts"
fi

echo ""
echo "🧪 Step 5: Plugin Validation Tests"
echo "=================================="

# Test 1: Plugin binary execution
echo ""
echo "Test 1: Plugin binary execution"
if timeout 5s ./build/helm-openshift-plugin --help 2>/dev/null || true; then
    echo "✅ Plugin binary is executable"
else
    echo "⚠️  Plugin binary test inconclusive (this is normal for plugin binaries)"
fi

# Test 2: Plugin manifest validation
echo ""
echo "Test 2: Plugin manifest validation"
if command -v yq &> /dev/null; then
    if yq eval plugin.yaml > /dev/null 2>&1; then
        echo "✅ Plugin manifest is valid YAML"
        
        # Show plugin info
        PLUGIN_VERSION=$(yq eval '.metadata.version' plugin.yaml 2>/dev/null || echo "unknown")
        PLUGIN_TYPE=$(yq eval '.spec.type' plugin.yaml 2>/dev/null || echo "unknown")
        echo "   Plugin Version: $PLUGIN_VERSION"
        echo "   Plugin Type: $PLUGIN_TYPE"
    else
        echo "❌ Plugin manifest is invalid YAML"
    fi
else
    echo "⚠️  yq not available, skipping YAML validation"
fi

# Test 3: API tests (if service is running)
if [ "$SERVICE_RUNNING" = true ]; then
    echo ""
    echo "Test 3: API Integration Tests"
    
    # List plugins
    echo "Testing plugin listing..."
    PLUGINS_RESPONSE=$(curl -s "$WORKFLOW_SERVICE_URL/api/v1/plugins" || echo "")
    if [ -n "$PLUGINS_RESPONSE" ]; then
        echo "✅ Plugin listing API works"
        
        # Check if our plugin is listed
        if echo "$PLUGINS_RESPONSE" | jq -r '.plugins[].name' 2>/dev/null | grep -q "$PLUGIN_NAME"; then
            echo "✅ Helm OpenShift plugin is registered"
        else
            echo "⚠️  Helm OpenShift plugin not found in plugin list"
        fi
    else
        echo "❌ Plugin listing API failed"
    fi
    
    # Get specific plugin
    echo ""
    echo "Testing plugin details..."
    PLUGIN_RESPONSE=$(curl -s "$WORKFLOW_SERVICE_URL/api/v1/plugins/$PLUGIN_NAME" || echo "")
    if [ -n "$PLUGIN_RESPONSE" ]; then
        echo "✅ Plugin details API works"
        
        # Show plugin status
        PLUGIN_STATUS=$(echo "$PLUGIN_RESPONSE" | jq -r '.status' 2>/dev/null || echo "unknown")
        PLUGIN_HEALTH=$(echo "$PLUGIN_RESPONSE" | jq -r '.health' 2>/dev/null || echo "unknown")
        echo "   Status: $PLUGIN_STATUS"
        echo "   Health: $PLUGIN_HEALTH"
    else
        echo "⚠️  Plugin details API failed"
    fi
    
    # Test plugin validation
    echo ""
    echo "Testing plugin validation..."
    VALIDATION_RESPONSE=$(curl -s -X POST "$WORKFLOW_SERVICE_URL/api/v1/plugins/$PLUGIN_NAME/execute" \
        -H "Content-Type: application/json" \
        -d '{
            "operation": "deploy:validate",
            "parameters": {
                "openshift_api_url": "https://api.test.example.com:6443",
                "openshift_project": "test-project",
                "username": "test-user",
                "password": "test-password",
                "release_name": "test-release",
                "bitbucket_repo_url": "https://bitbucket.org/test/repo.git",
                "chart_path": "charts/app",
                "values_path": "values-dev.yaml"
            }
        }' || echo "")
    
    if [ -n "$VALIDATION_RESPONSE" ]; then
        echo "✅ Plugin validation API works"
        echo "$VALIDATION_RESPONSE" | jq '.' 2>/dev/null || echo "$VALIDATION_RESPONSE"
    else
        echo "⚠️  Plugin validation API failed"
    fi
fi

echo ""
echo "🎯 Step 6: Environment Requirements"
echo "=================================="

echo "To test the plugin with actual deployments, you'll need:"
echo ""
echo "Required Environment:"
echo "  ✅ OpenShift cluster access"
echo "  ✅ Valid OpenShift credentials"
echo "  ✅ Bitbucket repository with Helm charts"
echo "  ✅ Helm charts in the repository"
echo ""

if [ "$OC_AVAILABLE" = true ] && [ "$HELM_AVAILABLE" = true ]; then
    echo "✅ OpenShift CLI and Helm are available"
    echo ""
    echo "Example deployment test:"
    echo ""
    
    cat << 'EOF'
# Test deployment (requires actual OpenShift cluster)
curl -X POST http://localhost:8085/api/v1/plugins/helm-openshift-deploy/execute \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "deploy:helm",
    "parameters": {
      "openshift_api_url": "https://your-openshift-cluster:6443",
      "openshift_project": "your-project",
      "username": "your-username",
      "password": "your-password",
      "release_name": "my-app",
      "bitbucket_repo_url": "https://bitbucket.org/your-org/your-repo.git",
      "chart_path": "charts/myapp",
      "values_path": "values-dev.yaml",
      "dry_run": true
    }
  }'

# Check deployment status
curl -X POST http://localhost:8085/api/v1/plugins/helm-openshift-deploy/execute \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "helm:status",
    "parameters": {
      "release_name": "my-app",
      "namespace": "your-project"
    }
  }'
EOF
else
    echo "⚠️  OpenShift CLI or Helm not available"
    echo "   Install oc and helm for full deployment testing"
fi

echo ""
echo "📋 Test Summary"
echo "==============="
echo "Plugin Name: $PLUGIN_NAME"
echo "Workflow Service: $WORKFLOW_SERVICE_URL"
echo "Service Running: $SERVICE_RUNNING"
echo "OpenShift CLI: $OC_AVAILABLE"
echo "Helm CLI: $HELM_AVAILABLE"
echo ""

if [ "$SERVICE_RUNNING" = true ] && [ "$OC_AVAILABLE" = true ] && [ "$HELM_AVAILABLE" = true ]; then
    echo "✅ Plugin is ready for deployment testing!"
    echo ""
    echo "Next steps:"
    echo "1. Configure the plugin with your OpenShift cluster details"
    echo "2. Test with a dry run deployment"
    echo "3. Perform actual deployments to your cluster"
    echo "4. Integrate with workflows for automated deployments"
elif [ "$SERVICE_RUNNING" = true ]; then
    echo "⚠️  Plugin is registered but missing deployment tools"
    echo "   Install oc and helm for deployment testing"
else
    echo "⚠️  Start the workflow service to test the plugin:"
    echo "   cd ../../backend/workflow-service && go run main.go"
fi

echo ""
echo "🔗 Useful URLs:"
echo "  Plugin API: $WORKFLOW_SERVICE_URL/api/v1/plugins/$PLUGIN_NAME"
echo "  All Plugins: $WORKFLOW_SERVICE_URL/api/v1/plugins"
echo "  Plugin Docs: $(pwd)/README.md"
echo ""
echo "Test complete! 🎉"
