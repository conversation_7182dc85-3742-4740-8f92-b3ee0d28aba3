{"name": "helm-openshift-deployment", "version": "1.0.0", "description": "Deploy applications to OpenShift using Helm charts from Bitbucket", "author": "Deploy Orchestrator Team", "category": "deployment", "tags": ["helm", "openshift", "kubernetes", "deployment"], "variables": [{"name": "ENVIRONMENT", "type": "string", "description": "Target environment (dev, qa, prod)", "required": true, "validation": {"enum": ["dev", "qa", "prod"]}}, {"name": "APPLICATION_NAME", "type": "string", "description": "Name of the application to deploy", "required": true}, {"name": "IMAGE_TAG", "type": "string", "description": "Docker image tag to deploy", "required": true}, {"name": "REPLICAS", "type": "number", "description": "Number of application replicas", "default": 1, "validation": {"minimum": 1, "maximum": 10}}, {"name": "DRY_RUN", "type": "boolean", "description": "Perform dry run without actual deployment", "default": false}], "steps": [{"name": "validate-environment", "type": "condition", "condition": "{{.ENVIRONMENT}} in ['dev', 'qa', 'prod']", "onFailure": "abort"}, {"name": "set-environment-config", "type": "script", "script": {"language": "bash", "content": ["#!/bin/bash", "case \"{{.ENVIRONMENT}}\" in", "  dev)", "    export OPENSHIFT_API_URL=\"https://api.dev-cluster.example.com:6443\"", "    export OPENSHIFT_PROJECT=\"{{.APPLICATION_NAME}}-dev\"", "    export VALUES_FILE=\"values-dev.yaml\"", "    ;;", "  qa)", "    export OPENSHIFT_API_URL=\"https://api.qa-cluster.example.com:6443\"", "    export OPENSHIFT_PROJECT=\"{{.APPLICATION_NAME}}-qa\"", "    export VALUES_FILE=\"values-qa.yaml\"", "    ;;", "  prod)", "    export OPENSHIFT_API_URL=\"https://api.prod-cluster.example.com:6443\"", "    export OPENSHIFT_PROJECT=\"{{.APPLICATION_NAME}}-prod\"", "    export VALUES_FILE=\"values-prod.yaml\"", "    ;;", "esac", "echo \"Environment configured for {{.ENVIRONMENT}}\""]}}, {"name": "validate-deployment", "type": "plugin", "plugin": "helm-openshift-deploy", "operation": "deploy:validate", "parameters": {"openshift_api_url": "{{.OPENSHIFT_API_URL}}", "openshift_project": "{{.OPENSHIFT_PROJECT}}", "username": "{{.OPENSHIFT_USERNAME}}", "password": "{{.OPENSHIFT_PASSWORD}}", "bitbucket_repo_url": "https://bitbucket.org/myorg/helm-charts.git", "chart_path": "charts/{{.APPLICATION_NAME}}", "values_path": "{{.VALUES_FILE}}"}, "onFailure": "abort"}, {"name": "deploy-application", "type": "plugin", "plugin": "helm-openshift-deploy", "operation": "deploy:helm", "parameters": {"openshift_api_url": "{{.OPENSHIFT_API_URL}}", "openshift_project": "{{.OPENSHIFT_PROJECT}}", "username": "{{.OPENSHIFT_USERNAME}}", "password": "{{.OPENSHIFT_PASSWORD}}", "bitbucket_repo_url": "https://bitbucket.org/myorg/helm-charts.git", "chart_path": "charts/{{.APPLICATION_NAME}}", "values_path": "{{.VALUES_FILE}}", "release_name": "{{.APPLICATION_NAME}}-{{.ENVIRONMENT}}", "helm_timeout": "600s", "dry_run": "{{.DRY_RUN}}", "extra_values": {"image.tag": "{{.IMAGE_TAG}}", "replicaCount": "{{.REPLICAS}}", "environment": "{{.ENVIRONMENT}}"}}, "timeout": "900s"}, {"name": "verify-deployment", "type": "plugin", "plugin": "helm-openshift-deploy", "operation": "helm:status", "parameters": {"release_name": "{{.APPLICATION_NAME}}-{{.ENVIRONMENT}}", "namespace": "{{.OPENSHIFT_PROJECT}}"}, "condition": "{{.DRY_RUN}} == false"}, {"name": "deployment-summary", "type": "script", "script": {"language": "bash", "content": ["#!/bin/bash", "echo \"=== Deployment Summary ===\"", "echo \"Application: {{.APPLICATION_NAME}}\"", "echo \"Environment: {{.ENVIRONMENT}}\"", "echo \"Image Tag: {{.IMAGE_TAG}}\"", "echo \"Replicas: {{.REPLICAS}}\"", "echo \"Dry Run: {{.DRY_RUN}}\"", "echo \"Release: {{.APPLICATION_NAME}}-{{.ENVIRONMENT}}\"", "echo \"Namespace: {{.OPENSHIFT_PROJECT}}\"", "echo \"=========================\""]}}], "onSuccess": [{"name": "success-notification", "type": "notification", "message": "✅ Successfully deployed {{.APPLICATION_NAME}} v{{.IMAGE_TAG}} to {{.ENVIRONMENT}}", "channels": ["slack", "email"]}], "onFailure": [{"name": "failure-notification", "type": "notification", "message": "❌ Failed to deploy {{.APPLICATION_NAME}} v{{.IMAGE_TAG}} to {{.ENVIRONMENT}}", "channels": ["slack", "email"]}, {"name": "rollback-on-failure", "type": "plugin", "plugin": "helm-openshift-deploy", "operation": "helm:rollback", "parameters": {"release_name": "{{.APPLICATION_NAME}}-{{.ENVIRONMENT}}", "namespace": "{{.OPENSHIFT_PROJECT}}"}, "condition": "{{.ENVIRONMENT}} == 'prod'"}], "permissions": {"required": ["deployment:create", "deployment:update", "plugin:execute"], "scope": "project"}, "metadata": {"documentation": "https://docs.example.com/helm-openshift-deployment", "support": "<EMAIL>", "version_history": [{"version": "1.0.0", "date": "2024-01-15", "changes": ["Initial release with Helm OpenShift deployment support"]}]}}