{"name": "helm-environment-promotion", "version": "1.0.0", "description": "Promote applications between environments using Helm OpenShift plugin", "author": "Deploy Orchestrator Team", "category": "promotion", "tags": ["helm", "openshift", "promotion", "environment"], "variables": [{"name": "APPLICATION_NAME", "type": "string", "description": "Name of the application to promote", "required": true}, {"name": "SOURCE_ENVIRONMENT", "type": "string", "description": "Source environment (dev, qa)", "required": true, "validation": {"enum": ["dev", "qa"]}}, {"name": "TARGET_ENVIRONMENT", "type": "string", "description": "Target environment (qa, prod)", "required": true, "validation": {"enum": ["qa", "prod"]}}, {"name": "VERSION_TAG", "type": "string", "description": "Specific version to promote", "required": true}, {"name": "APPROVAL_REQUIRED", "type": "boolean", "description": "Require manual approval for promotion", "default": true}], "steps": [{"name": "validate-promotion-path", "type": "condition", "condition": "({{.SOURCE_ENVIRONMENT}} == 'dev' && {{.TARGET_ENVIRONMENT}} == 'qa') || ({{.SOURCE_ENVIRONMENT}} == 'qa' && {{.TARGET_ENVIRONMENT}} == 'prod')", "onFailure": "abort", "errorMessage": "Invalid promotion path. Only dev->qa and qa->prod are allowed."}, {"name": "get-source-deployment-status", "type": "plugin", "plugin": "helm-openshift-deploy", "operation": "helm:status", "parameters": {"release_name": "{{.APPLICATION_NAME}}-{{.SOURCE_ENVIRONMENT}}", "namespace": "{{.APPLICATION_NAME}}-{{.SOURCE_ENVIRONMENT}}"}}, {"name": "validate-source-deployment", "type": "condition", "condition": "{{.get-source-deployment-status.exists}} == true", "onFailure": "abort", "errorMessage": "Source deployment not found in {{.SOURCE_ENVIRONMENT}} environment"}, {"name": "manual-approval", "type": "approval", "condition": "{{.APPROVAL_REQUIRED}} == true", "approvers": ["devops-team", "release-manager"], "message": "Approve promotion of {{.APPLICATION_NAME}} v{{.VERSION_TAG}} from {{.SOURCE_ENVIRONMENT}} to {{.TARGET_ENVIRONMENT}}", "timeout": "24h"}, {"name": "set-target-environment-config", "type": "script", "script": {"language": "bash", "content": ["#!/bin/bash", "case \"{{.TARGET_ENVIRONMENT}}\" in", "  qa)", "    export TARGET_API_URL=\"https://api.qa-cluster.example.com:6443\"", "    export TARGET_PROJECT=\"{{.APPLICATION_NAME}}-qa\"", "    export TARGET_VALUES=\"values-qa.yaml\"", "    export TARGET_REPLICAS=2", "    ;;", "  prod)", "    export TARGET_API_URL=\"https://api.prod-cluster.example.com:6443\"", "    export TARGET_PROJECT=\"{{.APPLICATION_NAME}}-prod\"", "    export TARGET_VALUES=\"values-prod.yaml\"", "    export TARGET_REPLICAS=3", "    ;;", "esac", "echo \"Target environment configured: {{.TARGET_ENVIRONMENT}}\""]}}, {"name": "backup-current-target", "type": "plugin", "plugin": "helm-openshift-deploy", "operation": "helm:status", "parameters": {"release_name": "{{.APPLICATION_NAME}}-{{.TARGET_ENVIRONMENT}}", "namespace": "{{.TARGET_PROJECT}}"}, "continueOnError": true}, {"name": "deploy-to-target", "type": "plugin", "plugin": "helm-openshift-deploy", "operation": "deploy:helm", "parameters": {"openshift_api_url": "{{.TARGET_API_URL}}", "openshift_project": "{{.TARGET_PROJECT}}", "username": "{{.OPENSHIFT_USERNAME}}", "password": "{{.OPENSHIFT_PASSWORD}}", "bitbucket_repo_url": "https://bitbucket.org/myorg/helm-charts.git", "chart_path": "charts/{{.APPLICATION_NAME}}", "values_path": "{{.TARGET_VALUES}}", "release_name": "{{.APPLICATION_NAME}}-{{.TARGET_ENVIRONMENT}}", "helm_timeout": "900s", "extra_values": {"image.tag": "{{.VERSION_TAG}}", "replicaCount": "{{.TARGET_REPLICAS}}", "environment": "{{.TARGET_ENVIRONMENT}}", "promotion.source": "{{.SOURCE_ENVIRONMENT}}", "promotion.timestamp": "{{.TIM<PERSON><PERSON><PERSON>}}", "promotion.version": "{{.VERSION_TAG}}"}}, "timeout": "1200s"}, {"name": "verify-target-deployment", "type": "plugin", "plugin": "helm-openshift-deploy", "operation": "helm:status", "parameters": {"release_name": "{{.APPLICATION_NAME}}-{{.TARGET_ENVIRONMENT}}", "namespace": "{{.TARGET_PROJECT}}"}}, {"name": "health-check", "type": "script", "script": {"language": "bash", "content": ["#!/bin/bash", "echo \"Performing health check for {{.APPLICATION_NAME}} in {{.TARGET_ENVIRONMENT}}\"", "", "# Wait for deployment to be ready", "oc rollout status deployment/{{.APPLICATION_NAME}} -n {{.TARGET_PROJECT}} --timeout=300s", "", "# Check if pods are running", "READY_PODS=$(oc get pods -n {{.TARGET_PROJECT}} -l app={{.APPLICATION_NAME}} --field-selector=status.phase=Running --no-headers | wc -l)", "EXPECTED_PODS={{.TARGET_REPLICAS}}", "", "if [ \"$READY_PODS\" -eq \"$EXPECTED_PODS\" ]; then", "  echo \"✅ Health check passed: $READY_PODS/$EXPECTED_PODS pods running\"", "  exit 0", "else", "  echo \"❌ Health check failed: $READY_PODS/$EXPECTED_PODS pods running\"", "  exit 1", "fi"]}, "timeout": "600s"}, {"name": "smoke-tests", "type": "script", "script": {"language": "bash", "content": ["#!/bin/bash", "echo \"Running smoke tests for {{.APPLICATION_NAME}} in {{.TARGET_ENVIRONMENT}}\"", "", "# Get service URL", "SERVICE_URL=$(oc get route {{.APPLICATION_NAME}} -n {{.TARGET_PROJECT}} -o jsonpath='{.spec.host}' 2>/dev/null || echo \"\")", "", "if [ -n \"$SERVICE_URL\" ]; then", "  echo \"Testing application at: https://$SERVICE_URL\"", "  ", "  # Basic connectivity test", "  if curl -f -s \"https://$SERVICE_URL/health\" > /dev/null; then", "    echo \"✅ Health endpoint accessible\"", "  else", "    echo \"❌ Health endpoint not accessible\"", "    exit 1", "  fi", "  ", "  # Version verification", "  DEPLOYED_VERSION=$(curl -s \"https://$SERVICE_URL/version\" | jq -r '.version' 2>/dev/null || echo \"unknown\")", "  if [ \"$DEPLOYED_VERSION\" = \"{{.VERSION_TAG}}\" ]; then", "    echo \"✅ Version verification passed: $DEPLOYED_VERSION\"", "  else", "    echo \"⚠️  Version mismatch: expected {{.VERSION_TAG}}, got $DEPLOYED_VERSION\"", "  fi", "else", "  echo \"⚠️  No route found, skipping external tests\"", "fi", "", "echo \"Smoke tests completed\""]}, "timeout": "300s", "continueOnError": true}, {"name": "promotion-summary", "type": "script", "script": {"language": "bash", "content": ["#!/bin/bash", "echo \"=== Promotion Summary ===\"", "echo \"Application: {{.APPLICATION_NAME}}\"", "echo \"Version: {{.VERSION_TAG}}\"", "echo \"Source: {{.SOURCE_ENVIRONMENT}}\"", "echo \"Target: {{.TARGET_ENVIRONMENT}}\"", "echo \"Timestamp: {{.TIMESTAMP}}\"", "echo \"Status: SUCCESS\"", "echo \"=========================\""]}}], "onSuccess": [{"name": "success-notification", "type": "notification", "message": "🚀 Successfully promoted {{.APPLICATION_NAME}} v{{.VERSION_TAG}} from {{.SOURCE_ENVIRONMENT}} to {{.TARGET_ENVIRONMENT}}", "channels": ["slack", "email"], "metadata": {"application": "{{.APPLICATION_NAME}}", "version": "{{.VERSION_TAG}}", "source": "{{.SOURCE_ENVIRONMENT}}", "target": "{{.TARGET_ENVIRONMENT}}", "timestamp": "{{.TIM<PERSON><PERSON><PERSON>}}"}}, {"name": "update-deployment-registry", "type": "script", "script": {"language": "bash", "content": ["#!/bin/bash", "# Update deployment registry with promotion information", "echo \"Recording promotion in deployment registry...\"", "# This could integrate with your deployment tracking system"]}}], "onFailure": [{"name": "failure-notification", "type": "notification", "message": "❌ Failed to promote {{.APPLICATION_NAME}} v{{.VERSION_TAG}} from {{.SOURCE_ENVIRONMENT}} to {{.TARGET_ENVIRONMENT}}", "channels": ["slack", "email"]}, {"name": "rollback-on-failure", "type": "plugin", "plugin": "helm-openshift-deploy", "operation": "helm:rollback", "parameters": {"release_name": "{{.APPLICATION_NAME}}-{{.TARGET_ENVIRONMENT}}", "namespace": "{{.TARGET_PROJECT}}"}, "condition": "{{.backup-current-target.exists}} == true"}], "permissions": {"required": ["deployment:create", "deployment:update", "environment:promote", "plugin:execute"], "scope": "project"}, "metadata": {"documentation": "https://docs.example.com/environment-promotion", "support": "<EMAIL>"}}