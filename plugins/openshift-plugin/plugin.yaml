apiVersion: v1
kind: Plugin
metadata:
  name: openshift-plugin
  version: 1.0.0
  description: "OpenShift deployment provider plugin with advanced features"
  author: "Deploy Orchestrator Team <<EMAIL>>"
  license: "MIT"
  
spec:
  type: provider
  runtime: go
  entrypoint: main.go
  
  # Provider-specific configuration
  provider:
    type: openshift
    capabilities:
      - deploy
      - rollback
      - scaling
      - health-checks
      - logs
      - metrics
      - secrets
      - config-maps
      - load-balancing
      - auto-scaling
      - blue-green
      - canary
      - multi-region
      - backup
      - monitoring
      - networking
      - storage
      - service-mesh
      - image-scanning
      - compliance
      - routes
      - builds
      - image-streams
      - operators
    
  # Configuration schema
  configSchema:
    type: object
    properties:
      clusterUrl:
        type: string
        description: "OpenShift cluster URL"
        required: true
        example: "https://api.cluster.openshift.com:6443"
      token:
        type: string
        description: "Service account token or OAuth token"
        required: true
        sensitive: true
      namespace:
        type: string
        description: "Default namespace for deployments"
        default: "default"
      insecureSkipTLSVerify:
        type: boolean
        description: "Skip TLS certificate verification"
        default: false
      timeout:
        type: string
        description: "Default timeout for operations"
        default: "300s"
      registry:
        type: object
        description: "Container registry configuration"
        properties:
          url:
            type: string
            description: "Registry URL"
          username:
            type: string
            description: "Registry username"
          password:
            type: string
            description: "Registry password"
            sensitive: true
      buildConfig:
        type: object
        description: "Build configuration"
        properties:
          strategy:
            type: string
            description: "Build strategy (Source, Docker, Custom)"
            default: "Source"
          sourceSecret:
            type: string
            description: "Secret for source code access"
          outputSecret:
            type: string
            description: "Secret for output image push"
      routeConfig:
        type: object
        description: "Route configuration"
        properties:
          domain:
            type: string
            description: "Default domain for routes"
          tlsTermination:
            type: string
            description: "TLS termination type (edge, passthrough, reencrypt)"
            default: "edge"
          
  # Dependencies
  dependencies:
    - name: kubernetes
      version: ">=1.20.0"
    - name: openshift-client
      version: ">=4.0.0"
      
  # Resource requirements
  resources:
    memory: "256Mi"
    cpu: "200m"
    
  # Hot reload configuration
  hotReload:
    enabled: true
    watchPaths:
      - "*.go"
      - "templates/*.yaml"
      - "manifests/*.yaml"
    excludePaths:
      - "tests/*"
      - "*.test"
      - ".git/*"
    debounceInterval: "2s"
    maxReloadAttempts: 3

  # Plugin metadata
  tags:
    - openshift
    - kubernetes
    - containers
    - red-hat
    - enterprise
  
  categories:
    - cloud-provider
    - container-orchestration
  
  documentation:
    readme: "README.md"
    examples: "examples/"
    api: "docs/api.md"
  
  support:
    issues: "https://github.com/deploy-orchestrator/openshift-plugin/issues"
    documentation: "https://docs.deploy-orchestrator.com/plugins/openshift"
    community: "https://community.deploy-orchestrator.com"
