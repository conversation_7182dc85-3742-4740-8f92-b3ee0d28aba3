apiVersion: v1
kind: WorkflowTemplate
metadata:
  name: openshift-basic-deploy
  description: "Basic deployment to OpenShift"
  provider: openshift
  category: deployment
  tags:
    - openshift
    - basic
    - deployment

spec:
  parameters:
    - name: appName
      type: string
      description: "Application name"
      required: true
    - name: imageTag
      type: string
      description: "Container image tag"
      required: true
    - name: namespace
      type: string
      description: "Target namespace"
      default: "default"
    - name: replicas
      type: integer
      description: "Number of replicas"
      default: 1
    - name: port
      type: integer
      description: "Application port"
      default: 8080

  steps:
    - name: authenticate-openshift
      type: openshift_auth
      config:
        clusterUrl: "{{.environment.clusterUrl}}"
        namespace: "{{.namespace}}"
        
    - name: validate-manifests
      type: openshift_validate
      dependsOn: ["authenticate-openshift"]
      config:
        manifests:
          - "deployment.yaml"
          - "service.yaml"
        namespace: "{{.namespace}}"
        
    - name: deploy-application
      type: openshift_deploy
      dependsOn: ["validate-manifests"]
      config:
        manifests:
          - "deployment.yaml"
          - "service.yaml"
        namespace: "{{.namespace}}"
        variables:
          appName: "{{.appName}}"
          imageTag: "{{.imageTag}}"
          replicas: "{{.replicas}}"
          port: "{{.port}}"
          
    - name: verify-deployment
      type: openshift_health_check
      dependsOn: ["deploy-application"]
      config:
        namespace: "{{.namespace}}"
        timeout: "300s"
        
    - name: notify-success
      type: notification
      dependsOn: ["verify-deployment"]
      config:
        type: slack
        message: "✅ Successfully deployed {{.appName}} v{{.imageTag}} to OpenShift namespace {{.namespace}}"

  manifests:
    deployment.yaml: |
      apiVersion: apps/v1
      kind: Deployment
      metadata:
        name: {{.appName}}
        namespace: {{.namespace}}
        labels:
          app: {{.appName}}
      spec:
        replicas: {{.replicas}}
        selector:
          matchLabels:
            app: {{.appName}}
        template:
          metadata:
            labels:
              app: {{.appName}}
          spec:
            containers:
            - name: {{.appName}}
              image: {{.imageTag}}
              ports:
              - containerPort: {{.port}}
              env:
              - name: PORT
                value: "{{.port}}"
              resources:
                requests:
                  memory: "128Mi"
                  cpu: "100m"
                limits:
                  memory: "256Mi"
                  cpu: "200m"
              livenessProbe:
                httpGet:
                  path: /health
                  port: {{.port}}
                initialDelaySeconds: 30
                periodSeconds: 10
              readinessProbe:
                httpGet:
                  path: /ready
                  port: {{.port}}
                initialDelaySeconds: 5
                periodSeconds: 5

    service.yaml: |
      apiVersion: v1
      kind: Service
      metadata:
        name: {{.appName}}-service
        namespace: {{.namespace}}
        labels:
          app: {{.appName}}
      spec:
        selector:
          app: {{.appName}}
        ports:
        - protocol: TCP
          port: 80
          targetPort: {{.port}}
        type: ClusterIP
