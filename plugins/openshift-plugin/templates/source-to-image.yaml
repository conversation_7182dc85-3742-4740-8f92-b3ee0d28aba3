apiVersion: v1
kind: WorkflowTemplate
metadata:
  name: openshift-s2i-deploy
  description: "Source-to-Image deployment on OpenShift"
  provider: openshift
  category: build-and-deploy
  tags:
    - openshift
    - s2i
    - source-to-image
    - build

spec:
  parameters:
    - name: appName
      type: string
      description: "Application name"
      required: true
    - name: sourceUrl
      type: string
      description: "Git repository URL"
      required: true
    - name: sourceBranch
      type: string
      description: "Git branch"
      default: "main"
    - name: builderImage
      type: string
      description: "Builder image"
      default: "registry.redhat.io/ubi8/nodejs-16"
    - name: namespace
      type: string
      description: "Target namespace"
      default: "default"
    - name: replicas
      type: integer
      description: "Number of replicas"
      default: 1
    - name: routeDomain
      type: string
      description: "Route domain"
      required: false

  steps:
    - name: authenticate-openshift
      type: openshift_auth
      config:
        clusterUrl: "{{.environment.clusterUrl}}"
        namespace: "{{.namespace}}"
        
    - name: validate-source
      type: openshift_validate
      dependsOn: ["authenticate-openshift"]
      config:
        manifests:
          - "imagestream.yaml"
          - "buildconfig.yaml"
          - "deploymentconfig.yaml"
          - "service.yaml"
        namespace: "{{.namespace}}"
        
    - name: create-imagestream
      type: openshift_deploy
      dependsOn: ["validate-source"]
      config:
        manifests:
          - "imagestream.yaml"
        namespace: "{{.namespace}}"
        variables:
          appName: "{{.appName}}"
          
    - name: build-image
      type: openshift_build
      dependsOn: ["create-imagestream"]
      config:
        strategy: "Source"
        sourceUrl: "{{.sourceUrl}}"
        sourceBranch: "{{.sourceBranch}}"
        builderImage: "{{.builderImage}}"
        namespace: "{{.namespace}}"
        appName: "{{.appName}}"
        
    - name: deploy-application
      type: openshift_deploy
      dependsOn: ["build-image"]
      config:
        manifests:
          - "deploymentconfig.yaml"
          - "service.yaml"
        namespace: "{{.namespace}}"
        variables:
          appName: "{{.appName}}"
          replicas: "{{.replicas}}"
          
    - name: create-route
      type: openshift_route
      dependsOn: ["deploy-application"]
      condition: "{{.routeDomain != ''}}"
      config:
        domain: "{{.routeDomain}}"
        tlsTermination: "edge"
        namespace: "{{.namespace}}"
        appName: "{{.appName}}"
        
    - name: verify-deployment
      type: openshift_health_check
      dependsOn: ["deploy-application"]
      config:
        namespace: "{{.namespace}}"
        timeout: "600s"
        
    - name: notify-success
      type: notification
      dependsOn: ["verify-deployment"]
      config:
        type: slack
        message: "🚀 Successfully built and deployed {{.appName}} from {{.sourceUrl}} to OpenShift namespace {{.namespace}}"

  manifests:
    imagestream.yaml: |
      apiVersion: image.openshift.io/v1
      kind: ImageStream
      metadata:
        name: {{.appName}}
        namespace: {{.namespace}}
        labels:
          app: {{.appName}}
      spec:
        lookupPolicy:
          local: false

    buildconfig.yaml: |
      apiVersion: build.openshift.io/v1
      kind: BuildConfig
      metadata:
        name: {{.appName}}
        namespace: {{.namespace}}
        labels:
          app: {{.appName}}
      spec:
        source:
          type: Git
          git:
            uri: {{.sourceUrl}}
            ref: {{.sourceBranch}}
        strategy:
          type: Source
          sourceStrategy:
            from:
              kind: DockerImage
              name: {{.builderImage}}
        output:
          to:
            kind: ImageStreamTag
            name: {{.appName}}:latest
        triggers:
        - type: ConfigChange
        - type: ImageChange
          imageChange: {}

    deploymentconfig.yaml: |
      apiVersion: apps.openshift.io/v1
      kind: DeploymentConfig
      metadata:
        name: {{.appName}}
        namespace: {{.namespace}}
        labels:
          app: {{.appName}}
      spec:
        replicas: {{.replicas}}
        selector:
          app: {{.appName}}
        template:
          metadata:
            labels:
              app: {{.appName}}
          spec:
            containers:
            - name: {{.appName}}
              image: {{.appName}}:latest
              ports:
              - containerPort: 8080
              env:
              - name: NODE_ENV
                value: "production"
              resources:
                requests:
                  memory: "128Mi"
                  cpu: "100m"
                limits:
                  memory: "512Mi"
                  cpu: "500m"
              livenessProbe:
                httpGet:
                  path: /health
                  port: 8080
                initialDelaySeconds: 30
                periodSeconds: 10
              readinessProbe:
                httpGet:
                  path: /ready
                  port: 8080
                initialDelaySeconds: 5
                periodSeconds: 5
        triggers:
        - type: ConfigChange
        - type: ImageChange
          imageChangeParams:
            automatic: true
            containerNames:
            - {{.appName}}
            from:
              kind: ImageStreamTag
              name: {{.appName}}:latest

    service.yaml: |
      apiVersion: v1
      kind: Service
      metadata:
        name: {{.appName}}-service
        namespace: {{.namespace}}
        labels:
          app: {{.appName}}
      spec:
        selector:
          app: {{.appName}}
        ports:
        - protocol: TCP
          port: 80
          targetPort: 8080
        type: ClusterIP

    route.yaml: |
      apiVersion: route.openshift.io/v1
      kind: Route
      metadata:
        name: {{.appName}}-route
        namespace: {{.namespace}}
        labels:
          app: {{.appName}}
      spec:
        host: {{.appName}}.{{.routeDomain}}
        to:
          kind: Service
          name: {{.appName}}-service
        port:
          targetPort: 8080
        tls:
          termination: edge
          insecureEdgeTerminationPolicy: Redirect
