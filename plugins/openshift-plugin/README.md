# OpenShift Plugin for Deploy Orchestrator

## 🎯 **Overview**

The OpenShift Plugin provides comprehensive deployment capabilities for Red Hat OpenShift Container Platform. It supports both traditional container deployments and OpenShift's Source-to-Image (S2I) builds, along with advanced OpenShift features like Routes, BuildConfigs, and DeploymentConfigs.

## ✨ **Features**

### **Core Capabilities**
- ✅ **Container Deployments** - Deploy pre-built container images
- ✅ **Source-to-Image (S2I)** - Build and deploy from source code
- ✅ **OpenShift Routes** - External traffic routing with TLS
- ✅ **BuildConfigs** - Automated builds from Git repositories
- ✅ **DeploymentConfigs** - OpenShift-native deployment management
- ✅ **ImageStreams** - Container image management
- ✅ **Health Checks** - Comprehensive deployment verification
- ✅ **Rollback Support** - Easy rollback to previous versions
- ✅ **Hot Reload** - Plugin updates without service restart

### **Advanced Features**
- 🔄 **Blue-Green Deployments** - Zero-downtime deployments
- 🐤 **Canary Deployments** - Gradual rollout strategies
- 📊 **Metrics & Monitoring** - Integration with OpenShift monitoring
- 🔐 **Security Scanning** - Container image vulnerability scanning
- 🏗️ **Multi-Stage Builds** - Complex build pipelines
- 🌐 **Multi-Region Support** - Deploy across multiple clusters
- 📦 **Operator Integration** - Deploy and manage operators

## 🚀 **Quick Start**

### **1. Installation**

```bash
# Install the OpenShift plugin
deploy-orchestrator plugin install openshift-plugin \
  --source ./plugins/openshift-plugin \
  --config openshift-config.yaml
```

### **2. Configuration**

```yaml
# openshift-config.yaml
clusterUrl: "https://api.cluster.openshift.com:6443"
token: "your-service-account-token"
namespace: "my-app"
insecureSkipTLSVerify: false
timeout: "300s"

# Optional: Container registry configuration
registry:
  url: "registry.redhat.io"
  username: "your-username"
  password: "your-password"

# Optional: Build configuration
buildConfig:
  strategy: "Source"
  sourceSecret: "git-secret"
  outputSecret: "registry-secret"

# Optional: Route configuration
routeConfig:
  domain: "apps.cluster.openshift.com"
  tlsTermination: "edge"
```

### **3. Basic Deployment**

```yaml
# workflow.yaml
apiVersion: v1
kind: Workflow
metadata:
  name: deploy-to-openshift
spec:
  template: openshift-basic-deploy
  parameters:
    appName: "my-app"
    imageTag: "registry.redhat.io/my-app:v1.0.0"
    namespace: "production"
    replicas: 3
    port: 8080
```

### **4. Source-to-Image Deployment**

```yaml
# s2i-workflow.yaml
apiVersion: v1
kind: Workflow
metadata:
  name: s2i-deploy
spec:
  template: openshift-s2i-deploy
  parameters:
    appName: "my-node-app"
    sourceUrl: "https://github.com/user/my-node-app.git"
    sourceBranch: "main"
    builderImage: "registry.redhat.io/ubi8/nodejs-16"
    namespace: "development"
    routeDomain: "apps.cluster.openshift.com"
```

## 📋 **Configuration Reference**

### **Required Configuration**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `clusterUrl` | string | OpenShift cluster API URL | `https://api.cluster.openshift.com:6443` |
| `token` | string | Service account token or OAuth token | `sha256~abc123...` |

### **Optional Configuration**

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `namespace` | string | `default` | Default namespace for deployments |
| `insecureSkipTLSVerify` | boolean | `false` | Skip TLS certificate verification |
| `timeout` | string | `300s` | Default timeout for operations |

### **Registry Configuration**

```yaml
registry:
  url: "registry.redhat.io"           # Registry URL
  username: "your-username"           # Registry username
  password: "your-password"           # Registry password (sensitive)
```

### **Build Configuration**

```yaml
buildConfig:
  strategy: "Source"                  # Build strategy (Source, Docker, Custom)
  sourceSecret: "git-secret"          # Secret for source code access
  outputSecret: "registry-secret"     # Secret for output image push
```

### **Route Configuration**

```yaml
routeConfig:
  domain: "apps.cluster.openshift.com"  # Default domain for routes
  tlsTermination: "edge"                # TLS termination (edge, passthrough, reencrypt)
```

## 🏗️ **Workflow Templates**

### **1. Basic Deployment Template**

Deploy pre-built container images to OpenShift.

```bash
deploy-orchestrator workflow create \
  --template openshift-basic-deploy \
  --param appName=my-app \
  --param imageTag=my-registry/my-app:v1.0.0 \
  --param namespace=production
```

### **2. Source-to-Image Template**

Build and deploy applications from source code.

```bash
deploy-orchestrator workflow create \
  --template openshift-s2i-deploy \
  --param appName=my-node-app \
  --param sourceUrl=https://github.com/user/my-app.git \
  --param builderImage=registry.redhat.io/ubi8/nodejs-16
```

## 🔧 **Advanced Usage**

### **Custom Build Strategies**

```yaml
# Custom Docker build
buildConfig:
  strategy: "Docker"
  dockerfilePath: "Dockerfile.prod"
  buildArgs:
    - name: "NODE_ENV"
      value: "production"
```

### **Multi-Environment Deployments**

```yaml
# Deploy to multiple environments
environments:
  - name: "development"
    namespace: "my-app-dev"
    replicas: 1
  - name: "staging"
    namespace: "my-app-staging"
    replicas: 2
  - name: "production"
    namespace: "my-app-prod"
    replicas: 5
```

### **Blue-Green Deployment**

```yaml
# Blue-green deployment configuration
deployment:
  strategy: "blue-green"
  blueGreen:
    activeService: "my-app-active"
    previewService: "my-app-preview"
    autoPromote: false
    scaleDownDelay: "30s"
```

## 🔐 **Security**

### **Service Account Setup**

```bash
# Create service account
oc create serviceaccount deploy-orchestrator

# Grant necessary permissions
oc adm policy add-cluster-role-to-user edit \
  system:serviceaccount:default:deploy-orchestrator

# Get service account token
oc serviceaccounts get-token deploy-orchestrator
```

### **Secret Management**

```yaml
# Git repository secret
apiVersion: v1
kind: Secret
metadata:
  name: git-secret
type: kubernetes.io/basic-auth
stringData:
  username: "git-username"
  password: "git-token"

# Registry secret
apiVersion: v1
kind: Secret
metadata:
  name: registry-secret
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: <base64-encoded-docker-config>
```

## 📊 **Monitoring & Observability**

### **Health Checks**

The plugin performs comprehensive health checks:

- **Deployment Status** - Verify all pods are ready
- **Service Connectivity** - Test service endpoints
- **Route Accessibility** - Verify external routes respond
- **Resource Usage** - Monitor CPU and memory usage

### **Metrics Collection**

```yaml
# Enable metrics collection
metrics:
  enabled: true
  interval: "30s"
  endpoints:
    - "/metrics"
    - "/health"
```

## 🐛 **Troubleshooting**

### **Common Issues**

1. **Authentication Failures**
   ```bash
   # Verify token is valid
   oc whoami --show-token
   
   # Check cluster connectivity
   oc cluster-info
   ```

2. **Build Failures**
   ```bash
   # Check build logs
   oc logs bc/my-app
   
   # Describe build config
   oc describe bc/my-app
   ```

3. **Route Issues**
   ```bash
   # Check route status
   oc get routes
   
   # Test route connectivity
   curl -I https://my-app.apps.cluster.openshift.com
   ```

### **Debug Mode**

```yaml
# Enable debug logging
debug:
  enabled: true
  level: "debug"
  logRequests: true
  logResponses: true
```

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 **License**

MIT License - see [LICENSE](LICENSE) file for details.

## 🆘 **Support**

- **Documentation**: [OpenShift Plugin Docs](https://docs.deploy-orchestrator.com/plugins/openshift)
- **Issues**: [GitHub Issues](https://github.com/deploy-orchestrator/openshift-plugin/issues)
- **Community**: [Deploy Orchestrator Community](https://community.deploy-orchestrator.com)
- **Red Hat Support**: For enterprise support, contact Red Hat

---

## 🎉 **Get Started Today!**

The OpenShift Plugin brings enterprise-grade deployment capabilities to your Deploy Orchestrator setup. With support for S2I builds, advanced routing, and comprehensive monitoring, you can deploy applications to OpenShift with confidence.

```bash
# Install and start using the OpenShift plugin
deploy-orchestrator plugin install openshift-plugin
deploy-orchestrator workflow create --template openshift-basic-deploy
```
