# GKE Environment Provider

A standalone environment provider for Google Kubernetes Engine (GKE) that integrates with the Deploy Orchestrator platform.

## Overview

This provider enables the Deploy Orchestrator to manage GKE clusters as deployment environments. It supports both standard GKE clusters and GKE Autopilot, with advanced features like Workload Identity, Istio service mesh, and Binary Authorization.

## Features

- ✅ **Multiple Authentication Methods**: Service Account, OAuth, gcloud CLI
- ✅ **Cluster Types**: Standard GKE and GKE Autopilot support
- ✅ **Advanced Features**: Workload Identity, Istio, Binary Authorization
- ✅ **Full Lifecycle Management**: Create, update, delete, monitor environments
- ✅ **Health Monitoring**: Comprehensive health checks and metrics
- ✅ **Resource Management**: Namespace and node pool management
- ✅ **Scaling Support**: Auto-scaling and manual scaling operations

## Quick Start

### Prerequisites

- Go 1.21 or later
- Google Cloud SDK (optional)
- kubectl (optional)
- Valid Google Cloud credentials

### Installation

1. **Clone and build:**
   ```bash
   git clone <repository>
   cd plugins/gke-environment-provider
   make build-local
   ```

2. **Run the provider:**
   ```bash
   make run
   ```

3. **Test the provider:**
   ```bash
   make test-api
   ```

### Configuration

The provider requires the following configuration:

```json
{
  "project_id": "my-gcp-project",
  "cluster_name": "my-gke-cluster",
  "zone_or_region": "us-central1-a",
  "namespace": "default",
  "auth_method": "service_account",
  "service_account_key": "base64-encoded-key-json"
}
```

## API Endpoints

The provider exposes the following REST API endpoints:

### Provider Information
- `GET /metadata` - Get provider metadata
- `GET /schema` - Get configuration schema
- `GET /health` - Health check

### Configuration
- `POST /validate` - Validate configuration
- `POST /initialize` - Initialize provider with configuration

### Environment Management
- `POST /environments` - Create environment
- `PUT /environments/{id}` - Update environment
- `DELETE /environments/{id}` - Delete environment
- `GET /environments/{id}/status` - Get environment status
- `GET /environments/{id}/resources` - Get environment resources
- `POST /environments/{id}/scale` - Scale environment resources
- `GET /environments/{id}/health` - Environment health check
- `GET /environments/{id}/metrics` - Get environment metrics

## Authentication Methods

### Service Account (Recommended)
```json
{
  "auth_method": "service_account",
  "service_account_key": "base64-encoded-service-account-json"
}
```

### OAuth Token
```json
{
  "auth_method": "oauth",
  "oauth_token": "ya29...."
}
```

### gcloud CLI
```json
{
  "auth_method": "gcloud_auth"
}
```

## Advanced Features

### Workload Identity
Enable Workload Identity for secure access to Google Cloud services:
```json
{
  "enable_workload_identity": true
}
```

### Istio Service Mesh
Enable Istio for advanced traffic management:
```json
{
  "enable_istio": true
}
```

### Binary Authorization
Enable Binary Authorization for container image security:
```json
{
  "enable_binary_authorization": true
}
```

### GKE Autopilot
Use GKE Autopilot for serverless Kubernetes:
```json
{
  "cluster_type": "autopilot"
}
```

## Development

### Build Commands

```bash
# Install dependencies
make deps

# Run tests
make test

# Build for all platforms
make build

# Build for current platform
make build-local

# Run locally
make run

# Create distribution packages
make dist

# Install to environment service
make install
```

### Testing

```bash
# Test the provider API
make test-api

# Run unit tests
make test
```

## Integration with Environment Service

To integrate this provider with the environment service:

1. **Build and install:**
   ```bash
   make install
   ```

2. **Register with environment service:**
   The environment service will automatically discover and load the provider.

3. **Configure in UI:**
   The provider will appear in the environment configuration UI with dynamic forms.

## Configuration Schema

The provider uses JSON Schema for configuration validation:

```json
{
  "type": "object",
  "required": ["project_id", "cluster_name", "zone_or_region"],
  "properties": {
    "project_id": {
      "type": "string",
      "title": "Google Cloud Project ID"
    },
    "cluster_name": {
      "type": "string", 
      "title": "GKE Cluster Name"
    },
    "zone_or_region": {
      "type": "string",
      "title": "Zone or Region"
    }
  }
}
```

## Monitoring and Observability

The provider includes comprehensive monitoring:

### Health Checks
- GKE cluster status
- Node pool health
- Workload Identity configuration
- Network connectivity

### Metrics
- Cluster status and node count
- Pod counts and resource utilization
- CPU, memory, and disk usage
- Request latency and error rates

## Security

### Credential Management
- Sensitive data is marked in schema
- Credentials are encrypted in transit
- Support for multiple authentication methods

### Network Security
- TLS encryption for all API calls
- Support for private GKE clusters
- VPC-native networking support

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Verify service account has required permissions
   - Check credential format and encoding
   - Ensure project ID is correct

2. **Cluster Access Issues**
   - Verify cluster exists and is running
   - Check zone/region specification
   - Ensure network connectivity

3. **Permission Errors**
   - Verify service account has GKE permissions
   - Check IAM roles and bindings
   - Ensure cluster admin access

### Logs

The provider logs to stdout with structured logging:
```bash
# View logs
docker logs <container-id>

# Set log level
export LOG_LEVEL=debug
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
