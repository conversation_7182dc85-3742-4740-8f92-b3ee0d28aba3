# GKE Environment Provider Makefile

# Build variables
BINARY_NAME=gke-environment-provider
VERSION?=1.0.0
BUILD_TIME=$(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT=$(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
LDFLAGS=-ldflags "-X main.version=$(VERSION) -X main.buildTime=$(BUILD_TIME) -X main.gitCommit=$(GIT_COMMIT)"

# Go variables
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# Directories
BUILD_DIR=build
DIST_DIR=dist

# Default target
.PHONY: all
all: clean deps test build

# Install dependencies
.PHONY: deps
deps:
	@echo "📦 Installing dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# Run tests
.PHONY: test
test:
	@echo "🧪 Running tests..."
	$(GOTEST) -v ./...

# Build the binary
.PHONY: build
build:
	@echo "🔨 Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 .
	CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-amd64 .
	CGO_ENABLED=0 GOOS=darwin GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-arm64 .
	CGO_ENABLED=0 GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-windows-amd64.exe .

# Build for current platform only
.PHONY: build-local
build-local:
	@echo "🔨 Building $(BINARY_NAME) for current platform..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) .

# Run the provider locally
.PHONY: run
run: build-local
	@echo "🚀 Starting $(BINARY_NAME)..."
	./$(BUILD_DIR)/$(BINARY_NAME)

# Clean build artifacts
.PHONY: clean
clean:
	@echo "🧹 Cleaning..."
	$(GOCLEAN)
	rm -rf $(BUILD_DIR)
	rm -rf $(DIST_DIR)

# Create distribution package
.PHONY: dist
dist: build
	@echo "📦 Creating distribution package..."
	@mkdir -p $(DIST_DIR)
	@for binary in $(BUILD_DIR)/*; do \
		if [ -f "$$binary" ]; then \
			filename=$$(basename "$$binary"); \
			platform=$$(echo "$$filename" | sed 's/$(BINARY_NAME)-//'); \
			mkdir -p "$(DIST_DIR)/$$platform"; \
			cp "$$binary" "$(DIST_DIR)/$$platform/$(BINARY_NAME)"; \
			cp README.md "$(DIST_DIR)/$$platform/" 2>/dev/null || true; \
			cp plugin.yaml "$(DIST_DIR)/$$platform/" 2>/dev/null || true; \
			cd $(DIST_DIR) && tar -czf "$(BINARY_NAME)-$$platform.tar.gz" "$$platform/"; \
			cd ..; \
		fi \
	done

# Install to environment service
.PHONY: install
install: build-local
	@echo "📥 Installing to environment service..."
	@if [ ! -d "../../backend/environment-service/plugins" ]; then \
		mkdir -p ../../backend/environment-service/plugins; \
	fi
	cp $(BUILD_DIR)/$(BINARY_NAME) ../../backend/environment-service/plugins/
	@echo "✅ Plugin installed successfully"

# Uninstall from environment service
.PHONY: uninstall
uninstall:
	@echo "🗑️  Uninstalling from environment service..."
	rm -f ../../backend/environment-service/plugins/$(BINARY_NAME)
	@echo "✅ Plugin uninstalled successfully"

# Test the plugin API
.PHONY: test-api
test-api:
	@echo "🧪 Testing plugin API..."
	@if ! pgrep -f "$(BINARY_NAME)" > /dev/null; then \
		echo "❌ Plugin is not running. Start it with 'make run'"; \
		exit 1; \
	fi
	@echo "Testing metadata endpoint..."
	curl -s http://localhost:8090/metadata | jq .
	@echo "\nTesting schema endpoint..."
	curl -s http://localhost:8090/schema | jq .
	@echo "\nTesting health endpoint..."
	curl -s http://localhost:8090/health | jq .

# Development workflow
.PHONY: dev
dev: clean deps test build-local
	@echo "🎯 Development build complete"

# Production workflow
.PHONY: prod
prod: clean deps test build dist
	@echo "🚀 Production build complete"

# Help
.PHONY: help
help:
	@echo "GKE Environment Provider Build System"
	@echo ""
	@echo "Available targets:"
	@echo "  all         - Clean, install deps, test, and build"
	@echo "  deps        - Install Go dependencies"
	@echo "  test        - Run tests"
	@echo "  build       - Build for all platforms"
	@echo "  build-local - Build for current platform only"
	@echo "  run         - Build and run locally"
	@echo "  clean       - Clean build artifacts"
	@echo "  dist        - Create distribution packages"
	@echo "  install     - Install to environment service"
	@echo "  uninstall   - Remove from environment service"
	@echo "  test-api    - Test plugin API endpoints"
	@echo "  dev         - Development workflow"
	@echo "  prod        - Production workflow"
	@echo "  help        - Show this help"
