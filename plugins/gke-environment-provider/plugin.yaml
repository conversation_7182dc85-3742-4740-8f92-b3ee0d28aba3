apiVersion: v1
kind: EnvironmentProvider
metadata:
  name: gke
  version: 1.0.0
  description: Google Kubernetes Engine (GKE) managed Kubernetes service
  author: Deploy Orchestrator Team
  license: MIT
  homepage: https://github.com/claudio/deploy-orchestrator
  repository: https://github.com/claudio/deploy-orchestrator/tree/main/plugins/gke-environment-provider
  documentation: https://cloud.google.com/kubernetes-engine/docs
  
spec:
  type: managed-kubernetes
  category: google-cloud
  
  # Provider capabilities
  capabilities:
    - deploy
    - scale
    - monitor
    - logs
    - health-check
    - auto-scaling
    - auto-upgrade
    - workload-identity
    - istio-service-mesh
    - binary-authorization
  
  # Supported platforms
  platforms:
    - linux/amd64
    - linux/arm64
    - darwin/amd64
    - darwin/arm64
    - windows/amd64
  
  # Runtime requirements
  runtime:
    type: standalone
    port: 8090
    healthEndpoint: /health
    
  # Configuration schema
  configSchema:
    type: object
    required:
      - project_id
      - cluster_name
      - zone_or_region
    properties:
      project_id:
        type: string
        title: Google Cloud Project ID
        description: Google Cloud project ID where the GKE cluster is located
      cluster_name:
        type: string
        title: GKE Cluster Name
        description: Name of the GKE cluster
      zone_or_region:
        type: string
        title: Zone or Region
        description: GCP zone (for zonal clusters) or region (for regional clusters)
        examples:
          - us-central1-a
          - us-central1
          - europe-west1-b
      namespace:
        type: string
        title: Kubernetes Namespace
        description: Target namespace for deployments
        default: default
      auth_method:
        type: string
        title: Authentication Method
        description: Method for authenticating with GKE
        enum:
          - service_account
          - oauth
          - gcloud_auth
        default: service_account
      service_account_key:
        type: string
        title: Service Account Key
        description: Base64 encoded service account key JSON
        sensitive: true
      oauth_token:
        type: string
        title: OAuth Access Token
        description: Google Cloud OAuth access token
        sensitive: true
      cluster_type:
        type: string
        title: Cluster Type
        description: Type of GKE cluster
        enum:
          - standard
          - autopilot
        default: standard
      enable_workload_identity:
        type: boolean
        title: Enable Workload Identity
        description: Enable Workload Identity for secure access to Google Cloud services
        default: false
      enable_istio:
        type: boolean
        title: Enable Istio Service Mesh
        description: Enable Istio service mesh for advanced traffic management
        default: false
      enable_binary_authorization:
        type: boolean
        title: Enable Binary Authorization
        description: Enable Binary Authorization for container image security
        default: false
      node_pool:
        type: string
        title: Node Pool
        description: Target node pool for deployments
        default: default-pool
  
  # API endpoints
  endpoints:
    metadata: GET /metadata
    schema: GET /schema
    validate: POST /validate
    initialize: POST /initialize
    createEnvironment: POST /environments
    updateEnvironment: PUT /environments/{id}
    deleteEnvironment: DELETE /environments/{id}
    getEnvironmentStatus: GET /environments/{id}/status
    getResources: GET /environments/{id}/resources
    scaleResources: POST /environments/{id}/scale
    healthCheck: GET /environments/{id}/health
    getMetrics: GET /environments/{id}/metrics
    health: GET /health
  
  # Tags for discovery
  tags:
    - gke
    - kubernetes
    - google-cloud
    - managed
    - container-orchestration
  
  # Provider metadata
  metadata:
    supported_versions:
      - "1.24"
      - "1.25"
      - "1.26"
      - "1.27"
      - "1.28"
    node_types:
      - standard
      - autopilot
    regions:
      - us-central1
      - us-east1
      - europe-west1
      - asia-southeast1
    features:
      - workload-identity
      - istio
      - binary-authorization
      - gke-autopilot
    
  # Installation instructions
  installation:
    requirements:
      - Google Cloud SDK (optional)
      - kubectl (optional)
      - Valid Google Cloud credentials
    
    steps:
      - Download the appropriate binary for your platform
      - Make the binary executable (chmod +x gke-environment-provider)
      - Run the provider (./gke-environment-provider)
      - Register with environment service
    
    environment_variables:
      - name: PORT
        description: Port to run the provider on
        default: "8090"
      - name: LOG_LEVEL
        description: Log level (debug, info, warn, error)
        default: info
      - name: GOOGLE_APPLICATION_CREDENTIALS
        description: Path to Google Cloud service account key file
        required: false
