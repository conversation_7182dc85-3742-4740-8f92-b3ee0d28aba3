# GKE Environment Provider - Quick Start Guide

This guide will help you get the GKE Environment Provider up and running in 5 minutes.

## Prerequisites

- Go 1.21 or later
- Google Cloud SDK (optional)
- Valid Google Cloud credentials
- GKE cluster (for testing)

## Step 1: Build the Provider

```bash
# Navigate to the provider directory
cd plugins/gke-environment-provider

# Install dependencies and build
make deps
make build-local
```

## Step 2: Configure Credentials

### Option A: Service Account Key (Recommended)

1. **Create a service account:**
   ```bash
   gcloud iam service-accounts create gke-provider-sa \
     --description="GKE Environment Provider Service Account" \
     --display-name="GKE Provider SA"
   ```

2. **Grant necessary permissions:**
   ```bash
   gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
     --member="serviceAccount:gke-provider-sa@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
     --role="roles/container.developer"
   ```

3. **Create and download key:**
   ```bash
   gcloud iam service-accounts keys create gke-sa-key.json \
     --iam-account=gke-provider-sa@YOUR_PROJECT_ID.iam.gserviceaccount.com
   ```

4. **Encode the key:**
   ```bash
   base64 -i gke-sa-key.json > gke-sa-key-base64.txt
   ```

### Option B: gcloud CLI Authentication

```bash
# Authenticate with gcloud
gcloud auth login
gcloud config set project YOUR_PROJECT_ID
```

## Step 3: Run the Provider

```bash
# Start the provider service
make run

# Or run directly
./build/gke-environment-provider
```

The provider will start on port 8090 by default.

## Step 4: Test the Provider

### Test Basic Endpoints

```bash
# Check health
curl http://localhost:8090/health

# Get provider metadata
curl http://localhost:8090/metadata | jq .

# Get configuration schema
curl http://localhost:8090/schema | jq .
```

### Test Configuration Validation

```bash
# Test valid configuration
curl -X POST http://localhost:8090/validate \
  -H "Content-Type: application/json" \
  -d '{
    "project_id": "your-project-id",
    "cluster_name": "your-cluster-name",
    "zone_or_region": "us-central1-a",
    "auth_method": "service_account",
    "service_account_key": "base64-encoded-key"
  }'
```

### Test Environment Creation

```bash
# Create a test environment
curl -X POST http://localhost:8090/environments \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test-env",
    "type": "gke",
    "provider": "gke",
    "config": {
      "project_id": "your-project-id",
      "cluster_name": "your-cluster-name",
      "zone_or_region": "us-central1-a",
      "namespace": "default",
      "auth_method": "service_account",
      "service_account_key": "base64-encoded-key"
    }
  }'
```

## Step 5: Integration with Environment Service

### Option A: Manual Registration

1. **Start the environment service** (if not already running)

2. **Register the provider** by adding it to the provider registry or using the discovery mechanism

### Option B: Automatic Discovery

The environment service can automatically discover external providers if configured with service discovery.

## Configuration Examples

### Standard GKE Cluster

```json
{
  "project_id": "my-gcp-project",
  "cluster_name": "my-gke-cluster",
  "zone_or_region": "us-central1-a",
  "namespace": "production",
  "auth_method": "service_account",
  "service_account_key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "cluster_type": "standard",
  "enable_workload_identity": true,
  "node_pool": "default-pool"
}
```

### GKE Autopilot

```json
{
  "project_id": "my-gcp-project",
  "cluster_name": "my-autopilot-cluster",
  "zone_or_region": "us-central1",
  "namespace": "default",
  "auth_method": "service_account",
  "service_account_key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "cluster_type": "autopilot",
  "enable_workload_identity": true,
  "enable_istio": true
}
```

### With Advanced Features

```json
{
  "project_id": "my-gcp-project",
  "cluster_name": "my-secure-cluster",
  "zone_or_region": "us-central1-a",
  "namespace": "secure-apps",
  "auth_method": "service_account",
  "service_account_key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "cluster_type": "standard",
  "enable_workload_identity": true,
  "enable_istio": true,
  "enable_binary_authorization": true,
  "node_pool": "secure-pool"
}
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   ```
   Error: failed to authenticate with Google Cloud
   ```
   - Verify service account key is valid and base64 encoded
   - Check service account has required permissions
   - Ensure project ID is correct

2. **Cluster Not Found**
   ```
   Error: cluster not found
   ```
   - Verify cluster name and zone/region
   - Check cluster exists and is running
   - Ensure service account has access to the cluster

3. **Permission Denied**
   ```
   Error: permission denied
   ```
   - Add `roles/container.developer` role to service account
   - For Autopilot: add `roles/container.admin` role
   - Check project-level IAM permissions

### Debug Commands

```bash
# Check if cluster exists
gcloud container clusters list --project=YOUR_PROJECT_ID

# Get cluster credentials
gcloud container clusters get-credentials CLUSTER_NAME \
  --zone=ZONE_OR_REGION --project=YOUR_PROJECT_ID

# Test kubectl access
kubectl get nodes

# Check service account permissions
gcloud projects get-iam-policy YOUR_PROJECT_ID \
  --flatten="bindings[].members" \
  --format="table(bindings.role)" \
  --filter="bindings.members:gke-provider-sa@YOUR_PROJECT_ID.iam.gserviceaccount.com"
```

### Logs

```bash
# View provider logs
docker logs <container-id>

# Enable debug logging
export LOG_LEVEL=debug
./build/gke-environment-provider
```

## Next Steps

1. **Integrate with Environment Service**: Configure the environment service to discover and use this provider

2. **Create Environment Templates**: Define reusable environment configurations

3. **Set up Monitoring**: Configure monitoring and alerting for the provider

4. **Scale the Provider**: Deploy multiple instances for high availability

5. **Customize Configuration**: Add custom fields and validation rules

## Production Deployment

### Docker Deployment

```dockerfile
FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY gke-environment-provider .
CMD ["./gke-environment-provider"]
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gke-environment-provider
spec:
  replicas: 2
  selector:
    matchLabels:
      app: gke-environment-provider
  template:
    metadata:
      labels:
        app: gke-environment-provider
    spec:
      containers:
      - name: provider
        image: gke-environment-provider:latest
        ports:
        - containerPort: 8090
        env:
        - name: PORT
          value: "8090"
        - name: LOG_LEVEL
          value: "info"
```

### Service Configuration

```yaml
apiVersion: v1
kind: Service
metadata:
  name: gke-environment-provider
spec:
  selector:
    app: gke-environment-provider
  ports:
  - port: 8090
    targetPort: 8090
  type: ClusterIP
```

## Support

- **Documentation**: See [README.md](README.md) for detailed documentation
- **Issues**: Report issues on the project repository
- **Examples**: Check the `examples/` directory for more configuration examples
