#!/bin/bash

# Bitbucket Plugin Installation and Test Script
set -e

PLUGIN_NAME="bitbucket-git"
WORKFLOW_SERVICE_URL="http://localhost:8085"
PLUGIN_DIR="../../backend/workflow-service/plugins/${PLUGIN_NAME}"

echo "🚀 Bitbucket Plugin Installation and Test"
echo "=========================================="

# Function to check if a command exists
check_command() {
    if ! command -v "$1" &> /dev/null; then
        echo "❌ Error: $1 is not installed or not in PATH"
        echo "Please install $1 and try again"
        exit 1
    fi
    echo "✅ $1 is available"
}

# Function to check if service is running
check_service() {
    local url=$1
    local service_name=$2
    
    if curl -s -f "$url/health" > /dev/null 2>&1; then
        echo "✅ $service_name is running"
        return 0
    else
        echo "❌ $service_name is not running at $url"
        return 1
    fi
}

# Function to wait for user input
wait_for_input() {
    echo ""
    echo "Press Enter to continue or Ctrl+C to exit..."
    read -r
}

echo ""
echo "📋 Step 1: Checking Prerequisites"
echo "================================="

# Check required tools
echo "Checking required tools..."
check_command "go"
check_command "make"
check_command "curl"
check_command "jq"

# Check optional tools
echo ""
echo "Checking optional tools:"
if command -v git &> /dev/null; then
    echo "✅ git is available ($(git --version))"
    GIT_AVAILABLE=true
else
    echo "⚠️  git is not available (required for actual Git operations)"
    GIT_AVAILABLE=false
fi

echo ""
echo "📦 Step 2: Building Plugin"
echo "=========================="

echo "Building Bitbucket plugin..."
if make build; then
    echo "✅ Plugin built successfully"
else
    echo "❌ Failed to build plugin"
    exit 1
fi

echo ""
echo "📁 Step 3: Installing Plugin"
echo "============================"

echo "Installing plugin to workflow service..."
if make install; then
    echo "✅ Plugin installed successfully"
    echo "Plugin location: $PLUGIN_DIR"
else
    echo "❌ Failed to install plugin"
    exit 1
fi

# Verify installation
if [ -f "$PLUGIN_DIR/plugin.yaml" ]; then
    echo "✅ Plugin manifest found"
else
    echo "❌ Plugin manifest not found"
    exit 1
fi

if [ -f "$PLUGIN_DIR/bitbucket-plugin" ]; then
    echo "✅ Plugin binary found"
else
    echo "❌ Plugin binary not found"
    exit 1
fi

echo ""
echo "🔌 Step 4: Checking Workflow Service"
echo "===================================="

echo "Checking if workflow service is running..."
if check_service "$WORKFLOW_SERVICE_URL" "Workflow Service"; then
    SERVICE_RUNNING=true
    
    echo ""
    echo "Attempting to register plugin with workflow service..."
    
    # Try to install plugin via API
    INSTALL_RESPONSE=$(curl -s -X POST "$WORKFLOW_SERVICE_URL/api/v1/plugins" \
        -H "Content-Type: application/json" \
        -d "{
            \"name\": \"$PLUGIN_NAME\",
            \"source\": \"$PLUGIN_DIR\",
            \"enabled\": true,
            \"config\": {}
        }" || echo "")
    
    if [ -n "$INSTALL_RESPONSE" ]; then
        echo "✅ Plugin registration response received"
        echo "$INSTALL_RESPONSE" | jq '.' 2>/dev/null || echo "$INSTALL_RESPONSE"
    else
        echo "⚠️  No response from plugin registration API"
    fi
    
else
    SERVICE_RUNNING=false
    echo ""
    echo "ℹ️  Workflow service is not running. You can:"
    echo "   1. Start the workflow service: cd ../../backend/workflow-service && go run main.go"
    echo "   2. The plugin will be automatically loaded when the service starts"
fi

echo ""
echo "🧪 Step 5: Plugin Validation Tests"
echo "=================================="

echo "Running plugin validation tests..."

# Test 1: Plugin binary execution
echo ""
echo "Test 1: Plugin binary execution"
if timeout 5s "$PLUGIN_DIR/bitbucket-plugin" --help 2>/dev/null || true; then
    echo "✅ Plugin binary is executable"
else
    echo "⚠️  Plugin binary test inconclusive (this is normal for plugin binaries)"
fi

# Test 2: Plugin manifest validation
echo ""
echo "Test 2: Plugin manifest validation"
if command -v yq &> /dev/null; then
    if yq eval "$PLUGIN_DIR/plugin.yaml" > /dev/null 2>&1; then
        echo "✅ Plugin manifest is valid YAML"
        
        # Show plugin info
        PLUGIN_VERSION=$(yq eval '.metadata.version' "$PLUGIN_DIR/plugin.yaml" 2>/dev/null || echo "unknown")
        PLUGIN_TYPE=$(yq eval '.spec.type' "$PLUGIN_DIR/plugin.yaml" 2>/dev/null || echo "unknown")
        echo "   Plugin Version: $PLUGIN_VERSION"
        echo "   Plugin Type: $PLUGIN_TYPE"
    else
        echo "❌ Plugin manifest is invalid YAML"
    fi
else
    echo "⚠️  yq not available, skipping YAML validation"
fi

# Test 3: API tests (if service is running)
if [ "$SERVICE_RUNNING" = true ]; then
    echo ""
    echo "Test 3: API Integration Tests"
    
    # List plugins
    echo "Testing plugin listing..."
    PLUGINS_RESPONSE=$(curl -s "$WORKFLOW_SERVICE_URL/api/v1/plugins" || echo "")
    if [ -n "$PLUGINS_RESPONSE" ]; then
        echo "✅ Plugin listing API works"
        
        # Check if our plugin is listed
        if echo "$PLUGINS_RESPONSE" | jq -r '.plugins[].name' 2>/dev/null | grep -q "$PLUGIN_NAME"; then
            echo "✅ Bitbucket plugin is registered"
        else
            echo "⚠️  Bitbucket plugin not found in plugin list"
        fi
    else
        echo "❌ Plugin listing API failed"
    fi
    
    # Get specific plugin
    echo ""
    echo "Testing plugin details..."
    PLUGIN_RESPONSE=$(curl -s "$WORKFLOW_SERVICE_URL/api/v1/plugins/$PLUGIN_NAME" || echo "")
    if [ -n "$PLUGIN_RESPONSE" ]; then
        echo "✅ Plugin details API works"
        
        # Show plugin status
        PLUGIN_STATUS=$(echo "$PLUGIN_RESPONSE" | jq -r '.status' 2>/dev/null || echo "unknown")
        PLUGIN_HEALTH=$(echo "$PLUGIN_RESPONSE" | jq -r '.health' 2>/dev/null || echo "unknown")
        echo "   Status: $PLUGIN_STATUS"
        echo "   Health: $PLUGIN_HEALTH"
    else
        echo "⚠️  Plugin details API failed"
    fi
fi

echo ""
echo "🎯 Step 6: Usage Examples"
echo "========================="

echo "To test the plugin with actual Bitbucket repositories, you'll need:"
echo ""
echo "Required Configuration:"
echo "  ✅ Bitbucket Cloud or Data Center access"
echo "  ✅ Valid Bitbucket credentials (app password or access token)"
echo "  ✅ Repository access permissions"
echo ""

if [ "$GIT_AVAILABLE" = true ]; then
    echo "✅ Git is available for repository operations"
    echo ""
    echo "Example API calls:"
    echo ""
    
    # Bitbucket Cloud example
    cat << 'EOF'
# Validate repository access (Bitbucket Cloud)
curl -X POST http://localhost:8085/api/v1/plugins/bitbucket-git/execute \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "git:validate-repository",
    "parameters": {
      "repository": {
        "owner": "your-workspace",
        "name": "your-repo"
      }
    }
  }'

# Get commit information
curl -X POST http://localhost:8085/api/v1/plugins/bitbucket-git/execute \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "git:commit-info",
    "parameters": {
      "repository": {
        "owner": "your-workspace",
        "name": "your-repo"
      },
      "commit_sha": "abc123def456"
    }
  }'

# List branches
curl -X POST http://localhost:8085/api/v1/plugins/bitbucket-git/execute \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "git:list-branches",
    "parameters": {
      "repository": {
        "owner": "your-workspace",
        "name": "your-repo"
      }
    }
  }'
EOF
else
    echo "⚠️  Git is not available"
    echo "   Install git for full repository operations"
fi

echo ""
echo "📋 Installation Summary"
echo "======================"
echo "Plugin Name: $PLUGIN_NAME"
echo "Plugin Location: $PLUGIN_DIR"
echo "Workflow Service: $WORKFLOW_SERVICE_URL"
echo "Service Running: $SERVICE_RUNNING"
echo ""

if [ "$SERVICE_RUNNING" = true ]; then
    echo "✅ Plugin is ready for use!"
    echo ""
    echo "Next steps:"
    echo "1. Configure the plugin with your Bitbucket credentials"
    echo "2. Test repository validation with your repositories"
    echo "3. Integrate with workflows for Git operations"
    echo "4. Set up webhooks for automated deployments"
else
    echo "⚠️  Start the workflow service to use the plugin:"
    echo "   cd ../../backend/workflow-service && go run main.go"
fi

echo ""
echo "🔗 Useful URLs:"
echo "  Plugin API: $WORKFLOW_SERVICE_URL/api/v1/plugins/$PLUGIN_NAME"
echo "  All Plugins: $WORKFLOW_SERVICE_URL/api/v1/plugins"
echo "  Plugin Docs: $(pwd)/README.md"
echo ""
echo "Installation complete! 🎉"
