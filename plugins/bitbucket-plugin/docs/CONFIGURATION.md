# Bitbucket Plugin Configuration Guide

This guide covers how to configure the Bitbucket Git plugin for different environments and use cases.

## Overview

The Bitbucket plugin supports both Bitbucket Cloud and Bitbucket Data Center with flexible authentication options.

## Configuration Parameters

### Required Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `username` | string | Bitbucket username or service account |
| `server_type` | string | Server type: "cloud" or "datacenter" |

### Optional Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `base_url` | string | `https://api.bitbucket.org/2.0` | Bitbucket API base URL |
| `api_version` | string | `2.0` | API version (2.0 for Cloud, 1.0 for Data Center) |
| `access_token` | string | - | Personal access token or OAuth token |
| `app_password` | string | - | Bitbucket app password (Cloud only) |

## Bitbucket Cloud Configuration

### Method 1: App Password (Recommended)

App passwords are the recommended authentication method for Bitbucket Cloud.

**Step 1: Create App Password**
1. Go to Bitbucket Settings → App passwords
2. Click "Create app password"
3. Select required permissions:
   - Repositories: Read, Write
   - Pull requests: Read, Write
   - Webhooks: Read, Write
4. Copy the generated password

**Step 2: Configure Plugin**
```json
{
  "name": "bitbucket-git",
  "config": {
    "server_type": "cloud",
    "username": "your-username",
    "app_password": "your-app-password"
  }
}
```

### Method 2: OAuth Access Token

For OAuth-based authentication:

```json
{
  "name": "bitbucket-git",
  "config": {
    "server_type": "cloud",
    "username": "your-username",
    "access_token": "your-oauth-token"
  }
}
```

## Bitbucket Data Center Configuration

### Method 1: Personal Access Token (Recommended)

**Step 1: Create Personal Access Token**
1. Go to Personal settings → Personal access tokens
2. Click "Create token"
3. Select required permissions:
   - Repository: Read, Write
   - Project: Read (if needed)
4. Copy the generated token

**Step 2: Configure Plugin**
```json
{
  "name": "bitbucket-git",
  "config": {
    "server_type": "datacenter",
    "base_url": "https://bitbucket.company.com/rest/api/1.0",
    "username": "service-account",
    "access_token": "your-personal-access-token"
  }
}
```

### Method 2: Username/Password

For basic authentication (not recommended for production):

```json
{
  "name": "bitbucket-git",
  "config": {
    "server_type": "datacenter",
    "base_url": "https://bitbucket.company.com/rest/api/1.0",
    "username": "your-username",
    "app_password": "your-password"
  }
}
```

## Environment Variables

The plugin supports environment variables for workflow integration:

```json
{
  "variables": {
    "BITBUCKET_WORKSPACE": "your-workspace-or-project",
    "BITBUCKET_REPO": "repository-name",
    "BITBUCKET_BRANCH": "main",
    "CLONE_DEPTH": 1,
    "WEBHOOK_SECRET": "webhook-validation-secret"
  }
}
```

### Variable Descriptions

| Variable | Description | Default |
|----------|-------------|---------|
| `BITBUCKET_WORKSPACE` | Workspace name (Cloud) or Project key (Data Center) | - |
| `BITBUCKET_REPO` | Repository name | - |
| `BITBUCKET_BRANCH` | Default branch for operations | "main" |
| `CLONE_DEPTH` | Git clone depth for shallow clones | 1 |
| `WEBHOOK_SECRET` | Secret for webhook validation | - |

## Security Best Practices

### 1. Use Service Accounts

Create dedicated service accounts for automation:

**Bitbucket Cloud:**
- Create a dedicated user account for automation
- Use app passwords instead of regular passwords
- Limit permissions to only what's needed

**Bitbucket Data Center:**
- Create service accounts with minimal required permissions
- Use personal access tokens with expiration dates
- Regularly rotate tokens

### 2. Secure Credential Storage

- Store credentials in secure secret management systems
- Use environment variables or secret providers
- Never commit credentials to version control
- Regularly rotate access tokens and passwords

### 3. Network Security

- Use HTTPS for all API communications
- Implement proper firewall rules
- Consider VPN or private network access for Data Center

### 4. Permission Management

**Minimum Required Permissions:**

**Bitbucket Cloud App Password:**
- Repositories: Read (for read operations), Write (for webhooks/PRs)
- Pull requests: Read, Write (if using PR operations)
- Webhooks: Read, Write (if using webhook operations)

**Bitbucket Data Center Token:**
- Repository: Read, Write
- Project: Read (for project-level operations)

## Configuration Examples

### Development Environment

```json
{
  "name": "bitbucket-git",
  "config": {
    "server_type": "cloud",
    "username": "dev-user",
    "app_password": "dev-app-password"
  },
  "variables": {
    "BITBUCKET_WORKSPACE": "dev-workspace",
    "BITBUCKET_REPO": "test-repo",
    "BITBUCKET_BRANCH": "develop",
    "CLONE_DEPTH": 5
  }
}
```

### Production Environment

```json
{
  "name": "bitbucket-git",
  "config": {
    "server_type": "datacenter",
    "base_url": "https://bitbucket.company.com/rest/api/1.0",
    "username": "deploy-service",
    "access_token": "{{secret:bitbucket-token}}"
  },
  "variables": {
    "BITBUCKET_WORKSPACE": "PROD",
    "BITBUCKET_REPO": "{{workflow.repository}}",
    "BITBUCKET_BRANCH": "{{workflow.branch}}",
    "CLONE_DEPTH": 1,
    "WEBHOOK_SECRET": "{{secret:webhook-secret}}"
  }
}
```

### Multi-Repository Setup

For managing multiple repositories:

```json
{
  "name": "bitbucket-git",
  "config": {
    "server_type": "cloud",
    "username": "automation-user",
    "app_password": "{{secret:bitbucket-app-password}}"
  },
  "variables": {
    "BITBUCKET_WORKSPACE": "myorg",
    "FRONTEND_REPO": "frontend-app",
    "BACKEND_REPO": "backend-api",
    "DOCS_REPO": "documentation",
    "BITBUCKET_BRANCH": "main",
    "CLONE_DEPTH": 1
  }
}
```

## Troubleshooting Configuration

### Common Issues

**1. Authentication Failures**
- Verify credentials are correct
- Check token/app password hasn't expired
- Ensure sufficient permissions are granted

**2. API URL Issues (Data Center)**
- Verify base_url is correct
- Check API version (usually 1.0 for Data Center)
- Ensure network connectivity to the server

**3. Repository Access Issues**
- Verify repository exists and is accessible
- Check workspace/project name is correct
- Ensure user has access to the repository

### Testing Configuration

Use the validation operation to test your configuration:

```bash
curl -X POST http://localhost:8085/api/v1/plugins/bitbucket-git/execute \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "git:validate-repository",
    "parameters": {
      "repository": {
        "owner": "your-workspace",
        "name": "your-repo"
      }
    }
  }'
```

### Debug Mode

Enable debug logging by setting environment variable:

```bash
export BITBUCKET_PLUGIN_DEBUG=true
```

This will provide detailed logging of API requests and responses for troubleshooting.
