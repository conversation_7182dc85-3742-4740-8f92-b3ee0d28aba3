# Bitbucket Plugin API Reference

This document provides detailed API reference for the Bitbucket Git plugin operations.

## Base URL

All plugin operations are accessed through the workflow service:

```
POST http://localhost:8085/api/v1/plugins/bitbucket-git/execute
```

## Authentication

The plugin supports multiple authentication methods:

### Bitbucket Cloud
- **App Password**: Username + App Password (recommended)
- **Access Token**: OAuth access token

### Bitbucket Data Center
- **Personal Access Token**: Username + Personal Access Token (recommended)
- **Username/Password**: Basic authentication

## Operations

### git:commit-info

Get detailed information about a specific commit.

**Parameters:**
- `repository` (object, required): Repository information
  - `owner` (string): Repository owner/workspace
  - `name` (string): Repository name
- `commit_sha` (string, required): Commit SHA hash

**Example Request:**
```json
{
  "operation": "git:commit-info",
  "parameters": {
    "repository": {
      "owner": "myworkspace",
      "name": "myrepo"
    },
    "commit_sha": "abc123def456789"
  }
}
```

**Example Response:**
```json
{
  "commit": {
    "sha": "abc123def456789",
    "message": "Fix authentication bug",
    "author": "John Doe",
    "email": "<EMAIL>",
    "date": "2024-01-15T10:30:00Z",
    "url": "https://bitbucket.org/myworkspace/myrepo/commits/abc123def456789"
  }
}
```

### git:branch-info

Get information about a specific branch.

**Parameters:**
- `repository` (object, required): Repository information
- `branch` (string, required): Branch name

**Example Request:**
```json
{
  "operation": "git:branch-info",
  "parameters": {
    "repository": {
      "owner": "myworkspace",
      "name": "myrepo"
    },
    "branch": "main"
  }
}
```

**Example Response:**
```json
{
  "branch": {
    "name": "main",
    "sha": "abc123def456789",
    "protected": false,
    "default": true
  }
}
```

### git:tag-info

Get information about a specific tag.

**Parameters:**
- `repository` (object, required): Repository information
- `tag` (string, required): Tag name

**Example Request:**
```json
{
  "operation": "git:tag-info",
  "parameters": {
    "repository": {
      "owner": "myworkspace",
      "name": "myrepo"
    },
    "tag": "v1.0.0"
  }
}
```

**Example Response:**
```json
{
  "tag": {
    "name": "v1.0.0",
    "sha": "abc123def456789",
    "message": "Release version 1.0.0",
    "date": "2024-01-15T10:30:00Z"
  }
}
```

### git:list-branches

List all branches in the repository.

**Parameters:**
- `repository` (object, required): Repository information

**Example Request:**
```json
{
  "operation": "git:list-branches",
  "parameters": {
    "repository": {
      "owner": "myworkspace",
      "name": "myrepo"
    }
  }
}
```

**Example Response:**
```json
{
  "branches": [
    {
      "name": "main",
      "sha": "abc123def456789",
      "default": true
    },
    {
      "name": "develop",
      "sha": "def456abc123789",
      "default": false
    }
  ]
}
```

### git:list-tags

List all tags in the repository.

**Parameters:**
- `repository` (object, required): Repository information

**Example Request:**
```json
{
  "operation": "git:list-tags",
  "parameters": {
    "repository": {
      "owner": "myworkspace",
      "name": "myrepo"
    }
  }
}
```

**Example Response:**
```json
{
  "tags": [
    {
      "name": "v1.0.0",
      "sha": "abc123def456789",
      "date": "2024-01-15T10:30:00Z"
    },
    {
      "name": "v0.9.0",
      "sha": "def456abc123789",
      "date": "2024-01-10T15:20:00Z"
    }
  ]
}
```

### git:create-webhook

Create a webhook for the repository.

**Parameters:**
- `repository` (object, required): Repository information
- `webhook_url` (string, required): Webhook endpoint URL
- `events` (array, optional): List of events to trigger webhook
- `secret` (string, optional): Webhook secret for validation

**Example Request:**
```json
{
  "operation": "git:create-webhook",
  "parameters": {
    "repository": {
      "owner": "myworkspace",
      "name": "myrepo"
    },
    "webhook_url": "https://my-server.com/webhook",
    "events": ["repo:push", "pullrequest:created"],
    "secret": "my-webhook-secret"
  }
}
```

**Example Response:**
```json
{
  "webhook": {
    "id": "webhook-123",
    "url": "https://my-server.com/webhook",
    "events": ["repo:push", "pullrequest:created"],
    "active": true
  },
  "status": "created"
}
```

### git:validate-repository

Validate repository access and credentials.

**Parameters:**
- `repository` (object, required): Repository information

**Example Request:**
```json
{
  "operation": "git:validate-repository",
  "parameters": {
    "repository": {
      "owner": "myworkspace",
      "name": "myrepo"
    }
  }
}
```

**Example Response (Success):**
```json
{
  "valid": true,
  "message": "Repository access validated successfully"
}
```

**Example Response (Error):**
```json
{
  "valid": false,
  "error": "Repository not found or access denied"
}
```

### git:parse-version

Parse and categorize version strings.

**Parameters:**
- `version` (string, required): Version string to parse

**Example Request:**
```json
{
  "operation": "git:parse-version",
  "parameters": {
    "version": "v1.0.0"
  }
}
```

**Example Response:**
```json
{
  "version_info": {
    "version": "v1.0.0",
    "type": "tag",
    "git_tag": "v1.0.0"
  }
}
```

### git:clone

Prepare repository clone operation.

**Parameters:**
- `repository` (object, required): Repository information
- `target_dir` (string, required): Target directory for clone
- `branch` (string, optional): Branch to clone (default: "main")
- `depth` (number, optional): Clone depth (default: 1)

**Example Request:**
```json
{
  "operation": "git:clone",
  "parameters": {
    "repository": {
      "owner": "myworkspace",
      "name": "myrepo"
    },
    "target_dir": "/tmp/checkout",
    "branch": "main",
    "depth": 1
  }
}
```

**Example Response:**
```json
{
  "clone_url": "https://bitbucket.org/myworkspace/myrepo.git",
  "target_dir": "/tmp/checkout",
  "branch": "main",
  "depth": 1,
  "status": "ready_to_clone",
  "message": "Clone parameters prepared. Execute git clone externally."
}
```

### git:pull-request

Create a pull request.

**Parameters:**
- `repository` (object, required): Repository information
- `title` (string, required): Pull request title
- `source_branch` (string, required): Source branch
- `target_branch` (string, optional): Target branch (default: "main")
- `description` (string, optional): Pull request description

**Example Request:**
```json
{
  "operation": "git:pull-request",
  "parameters": {
    "repository": {
      "owner": "myworkspace",
      "name": "myrepo"
    },
    "title": "Add new feature",
    "source_branch": "feature/new-feature",
    "target_branch": "main",
    "description": "This PR adds a new feature to the application"
  }
}
```

**Example Response:**
```json
{
  "pull_request": {
    "id": 123,
    "title": "Add new feature",
    "state": "OPEN",
    "source": {
      "branch": {
        "name": "feature/new-feature"
      }
    },
    "destination": {
      "branch": {
        "name": "main"
      }
    }
  },
  "status": "created"
}
```

## Error Handling

All operations return standard error responses when something goes wrong:

```json
{
  "error": "Error message describing what went wrong",
  "code": "ERROR_CODE",
  "details": {
    "additional": "error details"
  }
}
```

Common error codes:
- `INVALID_REPOSITORY`: Repository parameter is invalid
- `AUTHENTICATION_FAILED`: Invalid credentials
- `REPOSITORY_NOT_FOUND`: Repository doesn't exist or access denied
- `COMMIT_NOT_FOUND`: Specified commit doesn't exist
- `BRANCH_NOT_FOUND`: Specified branch doesn't exist
- `TAG_NOT_FOUND`: Specified tag doesn't exist
- `API_ERROR`: Bitbucket API returned an error
- `NETWORK_ERROR`: Network connectivity issues
