{"workflow": {"name": "bitbucket-integration-example", "description": "Example workflow using Bitbucket plugin for Git operations", "steps": [{"name": "validate-repository", "type": "plugin", "plugin": "bitbucket-git", "operation": "git:validate-repository", "parameters": {"repository": {"owner": "{{BITBUCKET_WORKSPACE}}", "name": "{{BITBUCKET_REPO}}"}}}, {"name": "get-latest-commit", "type": "plugin", "plugin": "bitbucket-git", "operation": "git:branch-info", "parameters": {"repository": {"owner": "{{BITBUCKET_WORKSPACE}}", "name": "{{BITBUCKET_REPO}}"}, "branch": "{{BITBUCKET_BRANCH}}"}, "depends_on": ["validate-repository"]}, {"name": "prepare-clone", "type": "plugin", "plugin": "bitbucket-git", "operation": "git:clone", "parameters": {"repository": {"owner": "{{BITBUCKET_WORKSPACE}}", "name": "{{BITBUCKET_REPO}}"}, "target_dir": "/tmp/checkout", "branch": "{{BITBUCKET_BRANCH}}", "depth": "{{CLONE_DEPTH}}"}, "depends_on": ["get-latest-commit"]}, {"name": "list-branches", "type": "plugin", "plugin": "bitbucket-git", "operation": "git:list-branches", "parameters": {"repository": {"owner": "{{BITBUCKET_WORKSPACE}}", "name": "{{BITBUCKET_REPO}}"}}, "depends_on": ["validate-repository"]}, {"name": "create-webhook", "type": "plugin", "plugin": "bitbucket-git", "operation": "git:create-webhook", "parameters": {"repository": {"owner": "{{BITBUCKET_WORKSPACE}}", "name": "{{BITBUCKET_REPO}}"}, "webhook_url": "https://your-deploy-orchestrator.com/webhook/bitbucket", "events": ["repo:push", "pullrequest:created", "pullrequest:updated"], "secret": "{{WEBHOOK_SECRET}}"}, "depends_on": ["validate-repository"], "optional": true}], "variables": [{"name": "BITBUCKET_WORKSPACE", "description": "Bitbucket workspace or project key", "required": true}, {"name": "BITBUCKET_REPO", "description": "Repository name", "required": true}, {"name": "BITBUCKET_BRANCH", "description": "Branch to work with", "default": "main"}, {"name": "CLONE_DEPTH", "description": "Git clone depth", "default": 1}, {"name": "WEBHOOK_SECRET", "description": "Webhook secret for validation", "sensitive": true, "required": false}]}}