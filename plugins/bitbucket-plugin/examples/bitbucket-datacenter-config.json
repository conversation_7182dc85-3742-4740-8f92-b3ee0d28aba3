{"name": "bitbucket-git", "config": {"server_type": "datacenter", "base_url": "https://bitbucket.company.com/rest/api/1.0", "api_version": "1.0", "username": "service-account", "access_token": "your-personal-access-token"}, "variables": {"BITBUCKET_WORKSPACE": "PROJECT_KEY", "BITBUCKET_REPO": "repository-name", "BITBUCKET_BRANCH": "develop", "CLONE_DEPTH": 5}, "description": "Bitbucket Data Center configuration example with personal access token"}