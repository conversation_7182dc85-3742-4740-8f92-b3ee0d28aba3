package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/claudio/deploy-orchestrator/shared/plugin"
)

// BitbucketPlugin implements Git repository integration for Bitbucket
type BitbucketPlugin struct {
	config *BitbucketConfig
	client *http.Client
}

// BitbucketConfig represents Bitbucket plugin configuration
type BitbucketConfig struct {
	BaseURL     string `json:"base_url"`
	AccessToken string `json:"access_token"`
	Username    string `json:"username"`
	AppPassword string `json:"app_password"`
	ServerType  string `json:"server_type"` // "cloud" or "datacenter"
	APIVersion  string `json:"api_version"` // "2.0" for cloud, "1.0" for datacenter
}

// CommitInfo represents Git commit information
type CommitInfo struct {
	SHA     string    `json:"sha"`
	Message string    `json:"message"`
	Author  string    `json:"author"`
	Email   string    `json:"email"`
	Date    time.Time `json:"date"`
	URL     string    `json:"url"`
	Branch  string    `json:"branch,omitempty"`
	Tags    []string  `json:"tags,omitempty"`
}

// BranchInfo represents Git branch information
type BranchInfo struct {
	Name       string      `json:"name"`
	SHA        string      `json:"sha"`
	Protected  bool        `json:"protected"`
	Default    bool        `json:"default"`
	LastCommit *CommitInfo `json:"last_commit,omitempty"`
}

// TagInfo represents Git tag information
type TagInfo struct {
	Name    string      `json:"name"`
	SHA     string      `json:"sha"`
	Message string      `json:"message,omitempty"`
	Date    time.Time   `json:"date"`
	Commit  *CommitInfo `json:"commit,omitempty"`
}

// Repository represents a Git repository
type Repository struct {
	Owner string `json:"owner"`
	Name  string `json:"name"`
	URL   string `json:"url"`
}

// NewBitbucketPlugin creates a new Bitbucket plugin instance
func NewBitbucketPlugin() *BitbucketPlugin {
	return &BitbucketPlugin{
		client: &http.Client{Timeout: 30 * time.Second},
	}
}

// GetMetadata returns plugin metadata
func (p *BitbucketPlugin) GetMetadata() plugin.Metadata {
	return plugin.Metadata{
		Name:        "bitbucket-git",
		Version:     "2.0.0",
		Description: "Bitbucket Git repository integration plugin (Cloud & Data Center)",
		Author:      "Deploy Orchestrator Team",
		Type:        "git-provider",
		Capabilities: []string{
			"git:commit-info",
			"git:branch-info",
			"git:tag-info",
			"git:list-branches",
			"git:list-tags",
			"git:webhook",
			"git:repository-validation",
			"git:parse-version",
			"git:clone",
			"git:pull-request",
		},
		ConfigSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"base_url": map[string]interface{}{
					"type":        "string",
					"default":     "https://api.bitbucket.org/2.0",
					"description": "Bitbucket API base URL (Cloud: https://api.bitbucket.org/2.0, Data Center: https://your-server/rest/api/1.0)",
				},
				"server_type": map[string]interface{}{
					"type":        "string",
					"default":     "cloud",
					"description": "Bitbucket server type",
					"enum":        []string{"cloud", "datacenter"},
				},
				"api_version": map[string]interface{}{
					"type":        "string",
					"default":     "2.0",
					"description": "API version (2.0 for Cloud, 1.0 for Data Center)",
				},
				"access_token": map[string]interface{}{
					"type":        "string",
					"description": "Bitbucket access token (for Cloud) or Personal Access Token (for Data Center)",
					"sensitive":   true,
				},
				"username": map[string]interface{}{
					"type":        "string",
					"description": "Bitbucket username",
				},
				"app_password": map[string]interface{}{
					"type":        "string",
					"description": "Bitbucket app password (for Cloud)",
					"sensitive":   true,
				},
			},
			"required": []string{"username", "server_type"},
		},
		Variables: []plugin.VariableDefinition{
			{
				Name:        "BITBUCKET_WORKSPACE",
				Type:        "string",
				Description: "Bitbucket workspace/organization name",
				Required:    true,
			},
			{
				Name:        "BITBUCKET_REPO",
				Type:        "string",
				Description: "Repository name",
				Required:    true,
			},
			{
				Name:         "BITBUCKET_BRANCH",
				Type:         "string",
				Description:  "Default branch name",
				DefaultValue: "main",
				Required:     false,
			},
			{
				Name:         "CLONE_DEPTH",
				Type:         "number",
				Description:  "Git clone depth for shallow clones",
				DefaultValue: 1,
				Required:     false,
				Validation: &plugin.Validation{
					Min: func() *float64 { v := 1.0; return &v }(),
					Max: func() *float64 { v := 1000.0; return &v }(),
				},
			},
			{
				Name:        "WEBHOOK_SECRET",
				Type:        "string",
				Description: "Secret for webhook validation",
				Required:    false,
				Sensitive:   true,
			},
		},
	}
}

// Initialize initializes the plugin with configuration
func (p *BitbucketPlugin) Initialize(config map[string]interface{}) error {
	configBytes, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	var bitbucketConfig BitbucketConfig
	if err := json.Unmarshal(configBytes, &bitbucketConfig); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Set defaults based on server type
	if bitbucketConfig.ServerType == "" {
		bitbucketConfig.ServerType = "cloud"
	}

	if bitbucketConfig.BaseURL == "" {
		if bitbucketConfig.ServerType == "datacenter" {
			return fmt.Errorf("base_url is required for Bitbucket Data Center")
		}
		bitbucketConfig.BaseURL = "https://api.bitbucket.org/2.0"
	}

	if bitbucketConfig.APIVersion == "" {
		if bitbucketConfig.ServerType == "datacenter" {
			bitbucketConfig.APIVersion = "1.0"
		} else {
			bitbucketConfig.APIVersion = "2.0"
		}
	}

	p.config = &bitbucketConfig
	log.Printf("Bitbucket plugin initialized for %s server, user: %s",
		p.config.ServerType, p.config.Username)
	return nil
}

// Execute executes a plugin operation
func (p *BitbucketPlugin) Execute(ctx context.Context, operation string, params map[string]interface{}) (map[string]interface{}, error) {
	switch operation {
	case "git:commit-info":
		return p.getCommitInfo(ctx, params)
	case "git:branch-info":
		return p.getBranchInfo(ctx, params)
	case "git:tag-info":
		return p.getTagInfo(ctx, params)
	case "git:list-branches":
		return p.listBranches(ctx, params)
	case "git:list-tags":
		return p.listTags(ctx, params)
	case "git:create-webhook":
		return p.createWebhook(ctx, params)
	case "git:validate-repository":
		return p.validateRepository(ctx, params)
	case "git:parse-version":
		return p.parseVersion(ctx, params)
	case "git:clone":
		return p.cloneRepository(ctx, params)
	case "git:pull-request":
		return p.createPullRequest(ctx, params)
	default:
		return nil, fmt.Errorf("unsupported operation: %s", operation)
	}
}

// getCommitInfo retrieves commit information
func (p *BitbucketPlugin) getCommitInfo(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error) {
	repo, err := p.extractRepository(params)
	if err != nil {
		return nil, err
	}

	commitSHA, ok := params["commit_sha"].(string)
	if !ok {
		return nil, fmt.Errorf("commit_sha parameter required")
	}

	var url string
	if p.config.ServerType == "datacenter" {
		url = fmt.Sprintf("%s/projects/%s/repos/%s/commits/%s", p.config.BaseURL, repo.Owner, repo.Name, commitSHA)
	} else {
		url = fmt.Sprintf("%s/repositories/%s/%s/commit/%s", p.config.BaseURL, repo.Owner, repo.Name, commitSHA)
	}

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	if p.config.AccessToken != "" {
		req.Header.Set("Authorization", "Bearer "+p.config.AccessToken)
	} else if p.config.Username != "" && p.config.AppPassword != "" {
		req.SetBasicAuth(p.config.Username, p.config.AppPassword)
	}

	resp, err := p.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Bitbucket API returned status %d", resp.StatusCode)
	}

	var commitInfo CommitInfo

	if p.config.ServerType == "datacenter" {
		// Data Center API format
		var dcCommit struct {
			ID        string `json:"id"`
			Message   string `json:"message"`
			Timestamp int64  `json:"authorTimestamp"`
			Author    struct {
				Name         string `json:"name"`
				EmailAddress string `json:"emailAddress"`
			} `json:"author"`
		}

		if err := json.NewDecoder(resp.Body).Decode(&dcCommit); err != nil {
			return nil, fmt.Errorf("failed to decode response: %w", err)
		}

		commitInfo = CommitInfo{
			SHA:     dcCommit.ID,
			Message: dcCommit.Message,
			Author:  dcCommit.Author.Name,
			Email:   dcCommit.Author.EmailAddress,
			Date:    time.Unix(dcCommit.Timestamp/1000, 0),
			URL:     fmt.Sprintf("%s/projects/%s/repos/%s/commits/%s", p.config.BaseURL, repo.Owner, repo.Name, dcCommit.ID),
		}
	} else {
		// Cloud API format
		var cloudCommit struct {
			Hash    string `json:"hash"`
			Message string `json:"message"`
			Date    string `json:"date"`
			Author  struct {
				Raw  string `json:"raw"`
				User struct {
					DisplayName string `json:"display_name"`
				} `json:"user"`
			} `json:"author"`
			Links struct {
				HTML struct {
					Href string `json:"href"`
				} `json:"html"`
			} `json:"links"`
		}

		if err := json.NewDecoder(resp.Body).Decode(&cloudCommit); err != nil {
			return nil, fmt.Errorf("failed to decode response: %w", err)
		}

		// Parse date
		date, err := time.Parse(time.RFC3339, cloudCommit.Date)
		if err != nil {
			log.Printf("Failed to parse commit date: %v", err)
			date = time.Now()
		}

		commitInfo = CommitInfo{
			SHA:     cloudCommit.Hash,
			Message: cloudCommit.Message,
			Author:  cloudCommit.Author.User.DisplayName,
			Email:   cloudCommit.Author.Raw,
			Date:    date,
			URL:     cloudCommit.Links.HTML.Href,
		}
	}

	result := map[string]interface{}{
		"commit": commitInfo,
	}

	return result, nil
}

// getBranchInfo retrieves branch information
func (p *BitbucketPlugin) getBranchInfo(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error) {
	repo, err := p.extractRepository(params)
	if err != nil {
		return nil, err
	}

	branch, ok := params["branch"].(string)
	if !ok {
		return nil, fmt.Errorf("branch parameter required")
	}

	url := fmt.Sprintf("%s/repositories/%s/%s/refs/branches/%s", p.config.BaseURL, repo.Owner, repo.Name, branch)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	if p.config.AccessToken != "" {
		req.Header.Set("Authorization", "Bearer "+p.config.AccessToken)
	} else if p.config.Username != "" && p.config.AppPassword != "" {
		req.SetBasicAuth(p.config.Username, p.config.AppPassword)
	}

	resp, err := p.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Bitbucket API returned status %d", resp.StatusCode)
	}

	var bitbucketBranch struct {
		Name   string `json:"name"`
		Target struct {
			Hash string `json:"hash"`
		} `json:"target"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&bitbucketBranch); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	branchInfo := BranchInfo{
		Name: bitbucketBranch.Name,
		SHA:  bitbucketBranch.Target.Hash,
	}

	result := map[string]interface{}{
		"branch": branchInfo,
	}

	return result, nil
}

// getTagInfo retrieves tag information
func (p *BitbucketPlugin) getTagInfo(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error) {
	repo, err := p.extractRepository(params)
	if err != nil {
		return nil, err
	}

	tag, ok := params["tag"].(string)
	if !ok {
		return nil, fmt.Errorf("tag parameter required")
	}

	var url string
	if p.config.ServerType == "datacenter" {
		url = fmt.Sprintf("%s/projects/%s/repos/%s/tags/%s", p.config.BaseURL, repo.Owner, repo.Name, tag)
	} else {
		url = fmt.Sprintf("%s/repositories/%s/%s/refs/tags/%s", p.config.BaseURL, repo.Owner, repo.Name, tag)
	}

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	if p.config.AccessToken != "" {
		req.Header.Set("Authorization", "Bearer "+p.config.AccessToken)
	} else if p.config.Username != "" && p.config.AppPassword != "" {
		req.SetBasicAuth(p.config.Username, p.config.AppPassword)
	}

	resp, err := p.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Bitbucket API returned status %d", resp.StatusCode)
	}

	var tagInfo TagInfo

	if p.config.ServerType == "datacenter" {
		// Data Center API format
		var dcTag struct {
			ID           string `json:"id"`
			DisplayID    string `json:"displayId"`
			LatestCommit string `json:"latestCommit"`
		}

		if err := json.NewDecoder(resp.Body).Decode(&dcTag); err != nil {
			return nil, fmt.Errorf("failed to decode response: %w", err)
		}

		tagInfo = TagInfo{
			Name: dcTag.DisplayID,
			SHA:  dcTag.LatestCommit,
			Date: time.Now(), // Data Center doesn't provide tag date directly
		}
	} else {
		// Cloud API format
		var cloudTag struct {
			Name   string `json:"name"`
			Target struct {
				Hash string `json:"hash"`
				Date string `json:"date"`
			} `json:"target"`
		}

		if err := json.NewDecoder(resp.Body).Decode(&cloudTag); err != nil {
			return nil, fmt.Errorf("failed to decode response: %w", err)
		}

		// Parse date
		date, err := time.Parse(time.RFC3339, cloudTag.Target.Date)
		if err != nil {
			log.Printf("Failed to parse tag date: %v", err)
			date = time.Now()
		}

		tagInfo = TagInfo{
			Name: cloudTag.Name,
			SHA:  cloudTag.Target.Hash,
			Date: date,
		}
	}

	result := map[string]interface{}{
		"tag": tagInfo,
	}

	return result, nil
}

// listBranches lists all branches
func (p *BitbucketPlugin) listBranches(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error) {
	repo, err := p.extractRepository(params)
	if err != nil {
		return nil, err
	}

	var url string
	if p.config.ServerType == "datacenter" {
		url = fmt.Sprintf("%s/projects/%s/repos/%s/branches", p.config.BaseURL, repo.Owner, repo.Name)
	} else {
		url = fmt.Sprintf("%s/repositories/%s/%s/refs/branches", p.config.BaseURL, repo.Owner, repo.Name)
	}

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	if p.config.AccessToken != "" {
		req.Header.Set("Authorization", "Bearer "+p.config.AccessToken)
	} else if p.config.Username != "" && p.config.AppPassword != "" {
		req.SetBasicAuth(p.config.Username, p.config.AppPassword)
	}

	resp, err := p.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Bitbucket API returned status %d", resp.StatusCode)
	}

	var branches []BranchInfo

	if p.config.ServerType == "datacenter" {
		// Data Center API format
		var dcResponse struct {
			Values []struct {
				ID           string `json:"id"`
				DisplayID    string `json:"displayId"`
				LatestCommit string `json:"latestCommit"`
				IsDefault    bool   `json:"isDefault"`
			} `json:"values"`
		}

		if err := json.NewDecoder(resp.Body).Decode(&dcResponse); err != nil {
			return nil, fmt.Errorf("failed to decode response: %w", err)
		}

		for _, branch := range dcResponse.Values {
			branches = append(branches, BranchInfo{
				Name:    branch.DisplayID,
				SHA:     branch.LatestCommit,
				Default: branch.IsDefault,
			})
		}
	} else {
		// Cloud API format
		var cloudResponse struct {
			Values []struct {
				Name   string `json:"name"`
				Target struct {
					Hash string `json:"hash"`
				} `json:"target"`
			} `json:"values"`
		}

		if err := json.NewDecoder(resp.Body).Decode(&cloudResponse); err != nil {
			return nil, fmt.Errorf("failed to decode response: %w", err)
		}

		for _, branch := range cloudResponse.Values {
			branches = append(branches, BranchInfo{
				Name: branch.Name,
				SHA:  branch.Target.Hash,
			})
		}
	}

	result := map[string]interface{}{
		"branches": branches,
	}

	return result, nil
}

// listTags lists all tags
func (p *BitbucketPlugin) listTags(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error) {
	repo, err := p.extractRepository(params)
	if err != nil {
		return nil, err
	}

	var url string
	if p.config.ServerType == "datacenter" {
		url = fmt.Sprintf("%s/projects/%s/repos/%s/tags", p.config.BaseURL, repo.Owner, repo.Name)
	} else {
		url = fmt.Sprintf("%s/repositories/%s/%s/refs/tags", p.config.BaseURL, repo.Owner, repo.Name)
	}

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	if p.config.AccessToken != "" {
		req.Header.Set("Authorization", "Bearer "+p.config.AccessToken)
	} else if p.config.Username != "" && p.config.AppPassword != "" {
		req.SetBasicAuth(p.config.Username, p.config.AppPassword)
	}

	resp, err := p.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Bitbucket API returned status %d", resp.StatusCode)
	}

	var tags []TagInfo

	if p.config.ServerType == "datacenter" {
		// Data Center API format
		var dcResponse struct {
			Values []struct {
				ID           string `json:"id"`
				DisplayID    string `json:"displayId"`
				LatestCommit string `json:"latestCommit"`
			} `json:"values"`
		}

		if err := json.NewDecoder(resp.Body).Decode(&dcResponse); err != nil {
			return nil, fmt.Errorf("failed to decode response: %w", err)
		}

		for _, tag := range dcResponse.Values {
			tags = append(tags, TagInfo{
				Name: tag.DisplayID,
				SHA:  tag.LatestCommit,
				Date: time.Now(), // Data Center doesn't provide tag date directly
			})
		}
	} else {
		// Cloud API format
		var cloudResponse struct {
			Values []struct {
				Name   string `json:"name"`
				Target struct {
					Hash string `json:"hash"`
					Date string `json:"date"`
				} `json:"target"`
			} `json:"values"`
		}

		if err := json.NewDecoder(resp.Body).Decode(&cloudResponse); err != nil {
			return nil, fmt.Errorf("failed to decode response: %w", err)
		}

		for _, tag := range cloudResponse.Values {
			// Parse date
			date, err := time.Parse(time.RFC3339, tag.Target.Date)
			if err != nil {
				log.Printf("Failed to parse tag date: %v", err)
				date = time.Now()
			}

			tags = append(tags, TagInfo{
				Name: tag.Name,
				SHA:  tag.Target.Hash,
				Date: date,
			})
		}
	}

	result := map[string]interface{}{
		"tags": tags,
	}

	return result, nil
}

// createWebhook creates a webhook
func (p *BitbucketPlugin) createWebhook(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error) {
	repo, err := p.extractRepository(params)
	if err != nil {
		return nil, err
	}

	webhookURL, ok := params["webhook_url"].(string)
	if !ok {
		return nil, fmt.Errorf("webhook_url parameter required")
	}

	events, _ := params["events"].([]string)
	if len(events) == 0 {
		events = []string{"repo:push", "pullrequest:created", "pullrequest:updated"}
	}

	secret, _ := params["secret"].(string)

	var url string
	var requestBody map[string]interface{}

	if p.config.ServerType == "datacenter" {
		url = fmt.Sprintf("%s/projects/%s/repos/%s/webhooks", p.config.BaseURL, repo.Owner, repo.Name)
		requestBody = map[string]interface{}{
			"name":   "Deploy Orchestrator Webhook",
			"url":    webhookURL,
			"events": events,
			"active": true,
		}
		if secret != "" {
			requestBody["secret"] = secret
		}
	} else {
		url = fmt.Sprintf("%s/repositories/%s/%s/hooks", p.config.BaseURL, repo.Owner, repo.Name)
		requestBody = map[string]interface{}{
			"description": "Deploy Orchestrator Webhook",
			"url":         webhookURL,
			"events":      events,
			"active":      true,
		}
		if secret != "" {
			requestBody["secret"] = secret
		}
	}

	bodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewReader(bodyBytes))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if p.config.AccessToken != "" {
		req.Header.Set("Authorization", "Bearer "+p.config.AccessToken)
	} else if p.config.Username != "" && p.config.AppPassword != "" {
		req.SetBasicAuth(p.config.Username, p.config.AppPassword)
	}

	resp, err := p.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Bitbucket API returned status %d", resp.StatusCode)
	}

	var webhookResponse map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&webhookResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	result := map[string]interface{}{
		"webhook": webhookResponse,
		"status":  "created",
	}

	return result, nil
}

// validateRepository validates repository access
func (p *BitbucketPlugin) validateRepository(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error) {
	repo, err := p.extractRepository(params)
	if err != nil {
		return nil, err
	}

	var url string
	if p.config.ServerType == "datacenter" {
		url = fmt.Sprintf("%s/projects/%s/repos/%s", p.config.BaseURL, repo.Owner, repo.Name)
	} else {
		url = fmt.Sprintf("%s/repositories/%s/%s", p.config.BaseURL, repo.Owner, repo.Name)
	}

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	if p.config.AccessToken != "" {
		req.Header.Set("Authorization", "Bearer "+p.config.AccessToken)
	} else if p.config.Username != "" && p.config.AppPassword != "" {
		req.SetBasicAuth(p.config.Username, p.config.AppPassword)
	}

	resp, err := p.client.Do(req)
	if err != nil {
		return map[string]interface{}{
			"valid": false,
			"error": fmt.Sprintf("Failed to connect to repository: %v", err),
		}, nil
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		return map[string]interface{}{
			"valid":   true,
			"message": "Repository access validated successfully",
		}, nil
	} else if resp.StatusCode == http.StatusNotFound {
		return map[string]interface{}{
			"valid": false,
			"error": "Repository not found or access denied",
		}, nil
	} else if resp.StatusCode == http.StatusUnauthorized {
		return map[string]interface{}{
			"valid": false,
			"error": "Authentication failed - check credentials",
		}, nil
	} else {
		return map[string]interface{}{
			"valid": false,
			"error": fmt.Sprintf("Bitbucket API returned status %d", resp.StatusCode),
		}, nil
	}
}

// parseVersion parses version information and enriches with Git data
func (p *BitbucketPlugin) parseVersion(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error) {
	version, ok := params["version"].(string)
	if !ok {
		return nil, fmt.Errorf("version parameter required")
	}

	// Determine if version is commit SHA, branch, or tag
	versionInfo := map[string]interface{}{
		"version": version,
	}

	if p.isCommitSHA(version) {
		versionInfo["type"] = "commit"
		versionInfo["git_commit"] = version
	} else if p.isTag(version) {
		versionInfo["type"] = "tag"
		versionInfo["git_tag"] = version
	} else {
		versionInfo["type"] = "branch"
		versionInfo["git_branch"] = version
	}

	return map[string]interface{}{
		"version_info": versionInfo,
	}, nil
}

// cloneRepository handles git clone operations
func (p *BitbucketPlugin) cloneRepository(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error) {
	repo, err := p.extractRepository(params)
	if err != nil {
		return nil, err
	}

	targetDir, ok := params["target_dir"].(string)
	if !ok {
		return nil, fmt.Errorf("target_dir parameter required")
	}

	branch, _ := params["branch"].(string)
	if branch == "" {
		branch = "main"
	}

	depth, _ := params["depth"].(float64)
	if depth == 0 {
		depth = 1
	}

	// Construct clone URL
	var cloneURL string
	if p.config.ServerType == "datacenter" {
		cloneURL = fmt.Sprintf("%s/scm/%s/%s.git",
			p.config.BaseURL, repo.Owner, repo.Name)
	} else {
		cloneURL = fmt.Sprintf("https://bitbucket.org/%s/%s.git",
			repo.Owner, repo.Name)
	}

	result := map[string]interface{}{
		"clone_url":  cloneURL,
		"target_dir": targetDir,
		"branch":     branch,
		"depth":      int(depth),
		"status":     "ready_to_clone",
		"message":    "Clone parameters prepared. Execute git clone externally.",
	}

	return result, nil
}

// createPullRequest creates a pull request
func (p *BitbucketPlugin) createPullRequest(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error) {
	repo, err := p.extractRepository(params)
	if err != nil {
		return nil, err
	}

	title, ok := params["title"].(string)
	if !ok {
		return nil, fmt.Errorf("title parameter required")
	}

	sourceBranch, ok := params["source_branch"].(string)
	if !ok {
		return nil, fmt.Errorf("source_branch parameter required")
	}

	targetBranch, ok := params["target_branch"].(string)
	if !ok {
		targetBranch = "main"
	}

	description, _ := params["description"].(string)

	var url string
	var requestBody map[string]interface{}

	if p.config.ServerType == "datacenter" {
		url = fmt.Sprintf("%s/projects/%s/repos/%s/pull-requests",
			p.config.BaseURL, repo.Owner, repo.Name)
		requestBody = map[string]interface{}{
			"title":       title,
			"description": description,
			"fromRef": map[string]interface{}{
				"id": "refs/heads/" + sourceBranch,
				"repository": map[string]interface{}{
					"slug": repo.Name,
					"project": map[string]interface{}{
						"key": repo.Owner,
					},
				},
			},
			"toRef": map[string]interface{}{
				"id": "refs/heads/" + targetBranch,
				"repository": map[string]interface{}{
					"slug": repo.Name,
					"project": map[string]interface{}{
						"key": repo.Owner,
					},
				},
			},
		}
	} else {
		url = fmt.Sprintf("%s/repositories/%s/%s/pullrequests",
			p.config.BaseURL, repo.Owner, repo.Name)
		requestBody = map[string]interface{}{
			"title":       title,
			"description": description,
			"source": map[string]interface{}{
				"branch": map[string]interface{}{
					"name": sourceBranch,
				},
			},
			"destination": map[string]interface{}{
				"branch": map[string]interface{}{
					"name": targetBranch,
				},
			},
		}
	}

	bodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url,
		bytes.NewReader(bodyBytes))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if p.config.AccessToken != "" {
		req.Header.Set("Authorization", "Bearer "+p.config.AccessToken)
	} else if p.config.Username != "" && p.config.AppPassword != "" {
		req.SetBasicAuth(p.config.Username, p.config.AppPassword)
	}

	resp, err := p.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("Bitbucket API returned status %d", resp.StatusCode)
	}

	var prResponse map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&prResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	result := map[string]interface{}{
		"pull_request": prResponse,
		"status":       "created",
	}

	return result, nil
}

// Helper methods

func (p *BitbucketPlugin) extractRepository(params map[string]interface{}) (*Repository, error) {
	repoData, ok := params["repository"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("repository parameter required")
	}

	owner, ok := repoData["owner"].(string)
	if !ok {
		return nil, fmt.Errorf("repository.owner required")
	}

	name, ok := repoData["name"].(string)
	if !ok {
		return nil, fmt.Errorf("repository.name required")
	}

	url, _ := repoData["url"].(string)

	return &Repository{
		Owner: owner,
		Name:  name,
		URL:   url,
	}, nil
}

func (p *BitbucketPlugin) isCommitSHA(version string) bool {
	if len(version) != 40 && len(version) != 64 {
		return false
	}

	for _, char := range version {
		if !((char >= '0' && char <= '9') || (char >= 'a' && char <= 'f') || (char >= 'A' && char <= 'F')) {
			return false
		}
	}

	return true
}

func (p *BitbucketPlugin) isTag(version string) bool {
	// Common tag patterns
	if len(version) > 0 && version[0] == 'v' {
		return true
	}

	// Check for semantic version pattern (x.y.z)
	// Simplified check - could be more sophisticated
	return false
}

// Cleanup cleans up plugin resources
func (p *BitbucketPlugin) Cleanup() error {
	log.Println("Bitbucket plugin cleanup completed")
	return nil
}

// Health returns plugin health status
func (p *BitbucketPlugin) Health() plugin.HealthStatus {
	if p.config == nil {
		return plugin.HealthStatus{
			Status:  "unhealthy",
			Message: "Plugin not initialized",
		}
	}

	return plugin.HealthStatus{
		Status:  "healthy",
		Message: "Bitbucket plugin is operational",
	}
}

// Plugin entry point
func main() {
	plugin.Serve(&BitbucketPlugin{})
}

// Ensure BitbucketPlugin implements the Plugin interface
var _ plugin.Plugin = (*BitbucketPlugin)(nil)
