# Bitbucket Plugin Makefile

PLUGIN_NAME = bitbucket-git
PLUGIN_VERSION = 2.0.0
BINARY_NAME = bitbucket-plugin
BUILD_DIR = build
DIST_DIR = dist

# Go build settings
GOOS ?= $(shell go env GOOS)
GOARCH ?= $(shell go env GOARCH)
CGO_ENABLED = 0

# Build flags
LDFLAGS = -w -s -X main.version=$(PLUGIN_VERSION)
BUILD_FLAGS = -ldflags "$(LDFLAGS)" -trimpath

.PHONY: all build clean test install package help

# Default target
all: clean build

# Build the plugin binary
build:
	@echo "Building $(PLUGIN_NAME) v$(PLUGIN_VERSION) for $(GOOS)/$(GOARCH)..."
	@mkdir -p $(BUILD_DIR)

	# Build as standalone binary
	CGO_ENABLED=$(CGO_ENABLED) GOOS=$(GOOS) GOARCH=$(GOARCH) \
		go build $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) .

	@echo "Build complete: $(BUILD_DIR)/$(BINARY_NAME)"

# Build for multiple platforms
build-all:
	@echo "Building for multiple platforms..."
	@mkdir -p $(BUILD_DIR)

	# Linux AMD64
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 \
		go build $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 .

	# Linux ARM64
	CGO_ENABLED=0 GOOS=linux GOARCH=arm64 \
		go build $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-arm64 .

	# macOS AMD64
	CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 \
		go build $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-amd64 .

	# macOS ARM64
	CGO_ENABLED=0 GOOS=darwin GOARCH=arm64 \
		go build $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-arm64 .

	# Windows AMD64
	CGO_ENABLED=0 GOOS=windows GOARCH=amd64 \
		go build $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-windows-amd64.exe .

	@echo "Multi-platform build complete"

# Run tests
test:
	@echo "Running tests..."
	go test -v ./...

# Run tests with coverage
test-coverage:
	@echo "Running tests with coverage..."
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(BUILD_DIR) $(DIST_DIR) coverage.out coverage.html

# Install dependencies
deps:
	@echo "Installing dependencies..."
	go mod download
	go mod tidy

# Format code
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Lint code
lint:
	@echo "Linting code..."
	golangci-lint run

# Package the plugin for distribution
package: build
	@echo "Packaging plugin..."
	@mkdir -p $(DIST_DIR)

	# Create plugin package
	@tar -czf $(DIST_DIR)/$(PLUGIN_NAME)-$(PLUGIN_VERSION)-$(GOOS)-$(GOARCH).tar.gz \
		-C $(BUILD_DIR) $(BINARY_NAME) \
		-C .. plugin.yaml README.md

	@echo "Package created: $(DIST_DIR)/$(PLUGIN_NAME)-$(PLUGIN_VERSION)-$(GOOS)-$(GOARCH).tar.gz"

# Package for all platforms
package-all: build-all
	@echo "Packaging for all platforms..."
	@mkdir -p $(DIST_DIR)

	# Linux AMD64
	@tar -czf $(DIST_DIR)/$(PLUGIN_NAME)-$(PLUGIN_VERSION)-linux-amd64.tar.gz \
		-C $(BUILD_DIR) $(BINARY_NAME)-linux-amd64 \
		-C .. plugin.yaml README.md

	# Linux ARM64
	@tar -czf $(DIST_DIR)/$(PLUGIN_NAME)-$(PLUGIN_VERSION)-linux-arm64.tar.gz \
		-C $(BUILD_DIR) $(BINARY_NAME)-linux-arm64 \
		-C .. plugin.yaml README.md

	# macOS AMD64
	@tar -czf $(DIST_DIR)/$(PLUGIN_NAME)-$(PLUGIN_VERSION)-darwin-amd64.tar.gz \
		-C $(BUILD_DIR) $(BINARY_NAME)-darwin-amd64 \
		-C .. plugin.yaml README.md

	# macOS ARM64
	@tar -czf $(DIST_DIR)/$(PLUGIN_NAME)-$(PLUGIN_VERSION)-darwin-arm64.tar.gz \
		-C $(BUILD_DIR) $(BINARY_NAME)-darwin-arm64 \
		-C .. plugin.yaml README.md

	# Windows AMD64
	@zip -j $(DIST_DIR)/$(PLUGIN_NAME)-$(PLUGIN_VERSION)-windows-amd64.zip \
		$(BUILD_DIR)/$(BINARY_NAME)-windows-amd64.exe plugin.yaml README.md

	@echo "All packages created in $(DIST_DIR)/"

# Install plugin to local workflow service
install: build
	@echo "Installing plugin to workflow service..."
	@if [ ! -d "../../backend/workflow-service/plugins/$(PLUGIN_NAME)" ]; then \
		mkdir -p ../../backend/workflow-service/plugins/$(PLUGIN_NAME); \
	fi

	# Copy binary
	cp $(BUILD_DIR)/$(BINARY_NAME) ../../backend/workflow-service/plugins/$(PLUGIN_NAME)/

	# Copy source files
	cp main.go ../../backend/workflow-service/plugins/$(PLUGIN_NAME)/
	# Create go.mod with correct paths for plugin directory
	sed 's|../../backend/shared|../../../shared|g; s|../../backend/workflow-service|../../../workflow-service|g' go.mod > ../../backend/workflow-service/plugins/$(PLUGIN_NAME)/go.mod
	cp go.sum ../../backend/workflow-service/plugins/$(PLUGIN_NAME)/ 2>/dev/null || true

	# Copy configuration and documentation
	cp plugin.yaml ../../backend/workflow-service/plugins/$(PLUGIN_NAME)/
	cp README.md ../../backend/workflow-service/plugins/$(PLUGIN_NAME)/

	@echo "Plugin installed to workflow service"
	@echo "Plugin directory: ../../backend/workflow-service/plugins/$(PLUGIN_NAME)"
	@echo ""
	@echo "To register the plugin, restart the workflow service or call:"
	@echo "curl -X POST http://localhost:8085/api/v1/plugins/reload"

# Validate plugin configuration
validate:
	@echo "Validating plugin configuration..."
	@if command -v yq >/dev/null 2>&1; then \
		yq eval plugin.yaml > /dev/null && echo "plugin.yaml is valid"; \
	else \
		echo "yq not found, skipping YAML validation"; \
	fi

# Check required tools
check-tools:
	@echo "Checking required tools..."
	@command -v git >/dev/null 2>&1 || { echo "git is required but not installed"; exit 1; }
	@command -v curl >/dev/null 2>&1 || { echo "curl is required but not installed"; exit 1; }
	@echo "All required tools are available"

# Development setup
dev-setup: deps check-tools
	@echo "Setting up development environment..."
	@if ! command -v golangci-lint >/dev/null 2>&1; then \
		echo "Installing golangci-lint..."; \
		go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest; \
	fi
	@echo "Development environment ready"

# Run the plugin in development mode
dev-run: build
	@echo "Running plugin in development mode..."
	./$(BUILD_DIR)/$(BINARY_NAME)

# Generate plugin documentation
docs:
	@echo "Generating plugin documentation..."
	@echo "# Plugin Documentation" > PLUGIN_DOCS.md
	@echo "" >> PLUGIN_DOCS.md
	@echo "Generated on: $$(date)" >> PLUGIN_DOCS.md
	@echo "" >> PLUGIN_DOCS.md
	@cat README.md >> PLUGIN_DOCS.md

# Show help
help:
	@echo "Available targets:"
	@echo "  build         - Build the plugin binary"
	@echo "  build-all     - Build for multiple platforms"
	@echo "  test          - Run tests"
	@echo "  test-coverage - Run tests with coverage"
	@echo "  clean         - Clean build artifacts"
	@echo "  deps          - Install dependencies"
	@echo "  fmt           - Format code"
	@echo "  lint          - Lint code"
	@echo "  package       - Package plugin for distribution"
	@echo "  package-all   - Package for all platforms"
	@echo "  install       - Install plugin to workflow service"
	@echo "  validate      - Validate plugin configuration"
	@echo "  check-tools   - Check required tools"
	@echo "  dev-setup     - Setup development environment"
	@echo "  dev-run       - Run plugin in development mode"
	@echo "  docs          - Generate documentation"
	@echo "  help          - Show this help"

# Version information
version:
	@echo "Plugin: $(PLUGIN_NAME)"
	@echo "Version: $(PLUGIN_VERSION)"
	@echo "Go version: $$(go version)"
	@echo "Build target: $(GOOS)/$(GOARCH)"
