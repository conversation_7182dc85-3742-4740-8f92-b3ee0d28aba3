#!/bin/bash

# Script to run the foreign key removal migration
# This removes the problematic foreign key constraint that references projects table

echo "🔧 Running Foreign Key Removal Migration"
echo "========================================"

# Check if migration file exists
if [ ! -f "migrations/remove_project_fk.sql" ]; then
    echo "❌ Migration file not found: migrations/remove_project_fk.sql"
    exit 1
fi

# Database connection parameters (adjust as needed)
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-environment_service}"
DB_USER="${DB_USER:-deploy}"

echo "📋 Migration Details:"
echo "  Host: $DB_HOST"
echo "  Port: $DB_PORT"
echo "  Database: $DB_NAME"
echo "  User: $DB_USER"
echo ""

# Check if psql is available
if ! command -v psql &> /dev/null; then
    echo "❌ psql command not found. Please install PostgreSQL client."
    exit 1
fi

echo "🔍 Checking current foreign key constraints..."

# Check current constraints
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
SELECT 
    tc.constraint_name, 
    tc.table_name, 
    ccu.table_name AS foreign_table_name
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_name = 'environment_configs'
  AND tc.table_schema = current_schema();
"

echo ""
echo "🚀 Running migration..."

# Run the migration
if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f migrations/remove_project_fk.sql; then
    echo ""
    echo "✅ Migration completed successfully!"
    
    echo ""
    echo "🔍 Verifying migration results..."
    
    # Verify no foreign key constraints remain
    CONSTRAINT_COUNT=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
    SELECT COUNT(*) 
    FROM information_schema.table_constraints 
    WHERE constraint_type = 'FOREIGN KEY' 
      AND table_name = 'environment_configs'
      AND constraint_name LIKE '%project%';
    ")
    
    if [ "$CONSTRAINT_COUNT" -eq 0 ]; then
        echo "✅ No project-related foreign key constraints found"
    else
        echo "⚠️  Warning: $CONSTRAINT_COUNT project-related foreign key constraints still exist"
    fi
    
    # Check if index was created
    INDEX_EXISTS=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
    SELECT COUNT(*) 
    FROM pg_indexes 
    WHERE tablename = 'environment_configs' 
      AND indexname = 'idx_environment_configs_project_id';
    ")
    
    if [ "$INDEX_EXISTS" -eq 1 ]; then
        echo "✅ Index idx_environment_configs_project_id exists"
    else
        echo "⚠️  Warning: Index idx_environment_configs_project_id not found"
    fi
    
    echo ""
    echo "🎉 Migration Summary:"
    echo "  ✅ Foreign key constraints removed"
    echo "  ✅ Performance index created"
    echo "  ✅ Column documentation added"
    echo "  ✅ Environment service can now create environments"
    echo ""
    echo "💡 Next steps:"
    echo "  1. Restart the environment service"
    echo "  2. Test environment creation"
    echo "  3. Monitor logs for project validation"
    
else
    echo ""
    echo "❌ Migration failed!"
    echo "Please check the error messages above and fix any issues."
    exit 1
fi
