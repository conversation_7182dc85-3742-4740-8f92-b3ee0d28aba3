services:
  gateway-service:
    build:
      context: ./backend/gateway-service
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - JWT_SECRET_KEY=your-jwt-secret-key-change-in-production
      - ADMIN_SERVICE_URL=http://admin-service:8080
    volumes:
      - ./backend/gateway-service/config:/app/config
    depends_on:
      - admin-service
      - application-service
      - environment-service
      - scheduling-service
      - notification-service
      - integration-service
      - audit-service
      - workflow-service
      - secrets-service
    restart: unless-stopped

  environment-service:
    build:
      context: ./backend/environment-service
      dockerfile: Dockerfile
    ports:
      - "8083:8083"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - GATEWAY_URL=http://gateway-service:8000
      - GATEWAY_AUTH_TOKEN=${SERVICE_AUTH_TOKEN:-environment-service-token}
      - SERVICE_VERSION=1.0.0
      - ENVIRONMENT=development
      - JWT_SECRET_KEY=your-jwt-secret-key-change-in-production
      - ADMIN_SERVICE_URL=http://admin-service:8086
      - DATABASE_URL=******************************************/deploy_orchestrator?sslmode=disable
    volumes:
      - ./backend/environment-service/config:/app/config
    depends_on:
      - postgres
    restart: unless-stopped

  secrets-service:
    build:
      context: ./backend/secrets-service
      dockerfile: Dockerfile
    ports:
      - "8087:8087"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - GATEWAY_URL=http://gateway-service:8000
      - GATEWAY_AUTH_TOKEN=${SERVICE_AUTH_TOKEN:-secrets-service-token}
      - SERVICE_VERSION=1.0.0
      - ENVIRONMENT=development
      - JWT_SECRET_KEY=your-jwt-secret-key-change-in-production
      - ADMIN_SERVICE_URL=http://admin-service:8086
      - DATABASE_URL=******************************************/deploy_orchestrator?sslmode=disable
    volumes:
      - ./backend/secrets-service/config:/app/config
    depends_on:
      - postgres
    restart: unless-stopped

  scheduling-service:
    build:
      context: ./backend/scheduling-service
      dockerfile: Dockerfile
    ports:
      - "8081:8080"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - GATEWAY_URL=http://gateway-service:8000
      - GATEWAY_AUTH_TOKEN=${SERVICE_AUTH_TOKEN:-scheduling-service-token}
      - SERVICE_VERSION=1.0.0
      - ENVIRONMENT=development
    volumes:
      - ./backend/scheduling-service/config:/app/config
    depends_on:
      - postgres
      - kafka
    restart: unless-stopped

  notification-service:
    build:
      context: ./backend/notification-service
      dockerfile: Dockerfile
    ports:
      - "8082:8080"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - GATEWAY_URL=http://gateway-service:8000
      - GATEWAY_AUTH_TOKEN=${SERVICE_AUTH_TOKEN:-notification-service-token}
      - SERVICE_VERSION=1.0.0
      - ENVIRONMENT=development
    volumes:
      - ./backend/notification-service/config:/app/config
    depends_on:
      - postgres
      - kafka
    restart: unless-stopped

  integration-service:
    build:
      context: ./backend/integration-service
      dockerfile: Dockerfile
    ports:
      - "8088:8080"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - GATEWAY_URL=http://gateway-service:8000
      - GATEWAY_AUTH_TOKEN=${SERVICE_AUTH_TOKEN:-integration-service-token}
      - SERVICE_VERSION=1.0.0
      - ENVIRONMENT=development
    volumes:
      - ./backend/integration-service/config:/app/config
    depends_on:
      - postgres
      - kafka
    restart: unless-stopped

  audit-service:
    build:
      context: ./backend/audit-service
      dockerfile: Dockerfile
    ports:
      - "8084:8080"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - GATEWAY_URL=http://gateway-service:8000
      - GATEWAY_AUTH_TOKEN=${SERVICE_AUTH_TOKEN:-audit-service-token}
      - SERVICE_VERSION=1.0.0
      - ENVIRONMENT=development
    volumes:
      - ./backend/audit-service/config:/app/config
    depends_on:
      - postgres
      - kafka
    restart: unless-stopped

  workflow-service:
    build:
      context: ./backend
      dockerfile: workflow-service/Dockerfile
    ports:
      - "8088:8088"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - GATEWAY_URL=http://gateway-service:8000
      - GATEWAY_AUTH_TOKEN=${SERVICE_AUTH_TOKEN:-workflow-service-token}
      - SERVICE_VERSION=1.0.0
      - ENVIRONMENT=development
      - AUTH_DISABLE=false
      - JWT_SECRET_KEY=your-jwt-secret-key-change-in-production
      - ACCESS_TOKEN_EXPIRY=3600
      - REFRESH_TOKEN_EXPIRY=86400
    volumes:
      - ./backend/workflow-service/config:/app/config
    depends_on:
      - postgres
      - kafka
    restart: unless-stopped

  admin-service:
    build:
      context: ./backend/admin-service
      dockerfile: Dockerfile
    ports:
      - "8086:8080"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - GATEWAY_URL=http://gateway-service:8000
      - GATEWAY_AUTH_TOKEN=${SERVICE_AUTH_TOKEN:-admin-service-token}
      - SERVICE_VERSION=1.0.0
      - ENVIRONMENT=development
    volumes:
      - ./backend/admin-service/config:/app/config
    depends_on:
      - postgres
      - kafka
    restart: unless-stopped

  application-service:
    build:
      context: ./backend/application-service
      dockerfile: Dockerfile
    ports:
      - "8085:8085"
    environment:
      - CONFIG_PATH=/app/config/config.yaml
      - GATEWAY_URL=http://gateway-service:8000
      - GATEWAY_AUTH_TOKEN=${SERVICE_AUTH_TOKEN:-application-service-token}
      - SERVICE_VERSION=1.0.0
      - ENVIRONMENT=development
      - JWT_SECRET_KEY=your-jwt-secret-key-change-in-production
      - ADMIN_SERVICE_URL=http://admin-service:8086
      - DATABASE_URL=******************************************/deploy_orchestrator?sslmode=disable
    volumes:
      - ./backend/application-service/config:/app/config
    depends_on:
      - postgres
      - admin-service
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "4200:80"
    depends_on:
      - admin-service
      - application-service
      - environment-service
      - secrets-service
      - scheduling-service
      - notification-service
      - integration-service
      - audit-service
      - workflow-service
    restart: unless-stopped

  postgres:
    image: postgres:14-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=deploy_orchestrator
    volumes:
      - postgres-data:/var/lib/postgresql/data
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:7.0.0
    ports:
      - "9092:9092"
    environment:
      - KAFKA_BROKER_ID=1
      - KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092
      - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
    depends_on:
      - zookeeper
    restart: unless-stopped

  zookeeper:
    image: confluentinc/cp-zookeeper:7.0.0
    ports:
      - "2181:2181"
    environment:
      - ZOOKEEPER_CLIENT_PORT=2181
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data
    restart: unless-stopped

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped

volumes:
  postgres-data:
  zookeeper-data:
  redis-data:
