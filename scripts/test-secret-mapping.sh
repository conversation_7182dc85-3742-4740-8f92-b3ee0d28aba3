#!/bin/bash

# Test Secret Mapping Implementation
# This script runs all tests related to secret mapping functionality

set -e

echo "🧪 Running Secret Mapping Tests"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v go &> /dev/null; then
        print_error "Go is not installed"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    print_success "All dependencies are available"
}

# Run backend unit tests
run_backend_unit_tests() {
    print_status "Running backend unit tests..."
    
    cd backend/workflow-service
    
    # Install dependencies
    go mod tidy
    
    # Run unit tests for secret mapping
    if go test -v ./tests/unit/secret_mapping_test.go; then
        print_success "Backend unit tests passed"
    else
        print_error "Backend unit tests failed"
        return 1
    fi
    
    cd ../..
}

# Run backend integration tests
run_backend_integration_tests() {
    print_status "Running backend integration tests..."
    
    cd backend/workflow-service
    
    # Run integration tests
    if go test -v ./tests/integration/secret_mapping_integration_test.go; then
        print_success "Backend integration tests passed"
    else
        print_error "Backend integration tests failed"
        return 1
    fi
    
    cd ../..
}

# Run security tests
run_security_tests() {
    print_status "Running security tests..."
    
    cd backend/secrets-service
    
    # Install dependencies
    go mod tidy
    
    # Run security tests
    if go test -v ./tests/security/secret_isolation_test.go; then
        print_success "Security tests passed"
    else
        print_error "Security tests failed"
        return 1
    fi
    
    cd ../..
}

# Run frontend unit tests
run_frontend_unit_tests() {
    print_status "Running frontend unit tests..."
    
    cd frontend/deploy-orchestrator
    
    # Install dependencies
    npm install
    
    # Run specific tests for secret mapping components
    if npm run test -- --watch=false --browsers=ChromeHeadless --include="**/secret-mapping.component.spec.ts"; then
        print_success "Frontend unit tests passed"
    else
        print_error "Frontend unit tests failed"
        return 1
    fi
    
    cd ../..
}

# Run end-to-end tests
run_e2e_tests() {
    print_status "Running end-to-end tests..."
    
    # Start services in background
    print_status "Starting test services..."
    
    # Start secrets service
    cd backend/secrets-service
    go run main.go &
    SECRETS_PID=$!
    cd ../..
    
    # Start workflow service
    cd backend/workflow-service
    go run main.go &
    WORKFLOW_PID=$!
    cd ../..
    
    # Start frontend
    cd frontend/deploy-orchestrator
    npm start &
    FRONTEND_PID=$!
    cd ../..
    
    # Wait for services to start
    sleep 10
    
    # Run e2e tests
    cd frontend/deploy-orchestrator
    if npm run e2e -- --suite=secret-mapping; then
        print_success "End-to-end tests passed"
        E2E_RESULT=0
    else
        print_error "End-to-end tests failed"
        E2E_RESULT=1
    fi
    cd ../..
    
    # Cleanup
    print_status "Cleaning up test services..."
    kill $SECRETS_PID $WORKFLOW_PID $FRONTEND_PID 2>/dev/null || true
    
    return $E2E_RESULT
}

# Run performance tests
run_performance_tests() {
    print_status "Running performance tests..."
    
    cd backend/workflow-service
    
    # Run performance benchmarks
    if go test -bench=. -benchmem ./tests/performance/secret_mapping_bench_test.go; then
        print_success "Performance tests completed"
    else
        print_warning "Performance tests had issues (non-critical)"
    fi
    
    cd ../..
}

# Generate test coverage report
generate_coverage_report() {
    print_status "Generating test coverage report..."
    
    # Backend coverage
    cd backend/workflow-service
    go test -coverprofile=coverage.out ./...
    go tool cover -html=coverage.out -o coverage.html
    print_success "Backend coverage report generated: backend/workflow-service/coverage.html"
    cd ../..
    
    cd backend/secrets-service
    go test -coverprofile=coverage.out ./...
    go tool cover -html=coverage.out -o coverage.html
    print_success "Secrets service coverage report generated: backend/secrets-service/coverage.html"
    cd ../..
    
    # Frontend coverage
    cd frontend/deploy-orchestrator
    npm run test -- --watch=false --browsers=ChromeHeadless --code-coverage
    print_success "Frontend coverage report generated: frontend/deploy-orchestrator/coverage/"
    cd ../..
}

# Main test execution
main() {
    echo "Starting secret mapping test suite..."
    echo "====================================="
    
    # Parse command line arguments
    RUN_UNIT=true
    RUN_INTEGRATION=true
    RUN_SECURITY=true
    RUN_FRONTEND=true
    RUN_E2E=false
    RUN_PERFORMANCE=false
    GENERATE_COVERAGE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --unit-only)
                RUN_INTEGRATION=false
                RUN_SECURITY=false
                RUN_FRONTEND=false
                shift
                ;;
            --integration-only)
                RUN_UNIT=false
                RUN_SECURITY=false
                RUN_FRONTEND=false
                shift
                ;;
            --security-only)
                RUN_UNIT=false
                RUN_INTEGRATION=false
                RUN_FRONTEND=false
                shift
                ;;
            --frontend-only)
                RUN_UNIT=false
                RUN_INTEGRATION=false
                RUN_SECURITY=false
                shift
                ;;
            --e2e)
                RUN_E2E=true
                shift
                ;;
            --performance)
                RUN_PERFORMANCE=true
                shift
                ;;
            --coverage)
                GENERATE_COVERAGE=true
                shift
                ;;
            --all)
                RUN_E2E=true
                RUN_PERFORMANCE=true
                GENERATE_COVERAGE=true
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                echo "Usage: $0 [--unit-only|--integration-only|--security-only|--frontend-only] [--e2e] [--performance] [--coverage] [--all]"
                exit 1
                ;;
        esac
    done
    
    # Check dependencies
    check_dependencies
    
    # Track test results
    FAILED_TESTS=()
    
    # Run tests based on flags
    if [ "$RUN_UNIT" = true ]; then
        if ! run_backend_unit_tests; then
            FAILED_TESTS+=("Backend Unit Tests")
        fi
    fi
    
    if [ "$RUN_INTEGRATION" = true ]; then
        if ! run_backend_integration_tests; then
            FAILED_TESTS+=("Backend Integration Tests")
        fi
    fi
    
    if [ "$RUN_SECURITY" = true ]; then
        if ! run_security_tests; then
            FAILED_TESTS+=("Security Tests")
        fi
    fi
    
    if [ "$RUN_FRONTEND" = true ]; then
        if ! run_frontend_unit_tests; then
            FAILED_TESTS+=("Frontend Unit Tests")
        fi
    fi
    
    if [ "$RUN_E2E" = true ]; then
        if ! run_e2e_tests; then
            FAILED_TESTS+=("End-to-End Tests")
        fi
    fi
    
    if [ "$RUN_PERFORMANCE" = true ]; then
        run_performance_tests
    fi
    
    if [ "$GENERATE_COVERAGE" = true ]; then
        generate_coverage_report
    fi
    
    # Print final results
    echo ""
    echo "🏁 Test Results Summary"
    echo "======================"
    
    if [ ${#FAILED_TESTS[@]} -eq 0 ]; then
        print_success "All tests passed! ✅"
        echo ""
        echo "Secret mapping implementation is ready for production! 🚀"
    else
        print_error "Some tests failed:"
        for test in "${FAILED_TESTS[@]}"; do
            echo "  ❌ $test"
        done
        echo ""
        echo "Please fix the failing tests before proceeding."
        exit 1
    fi
}

# Run main function
main "$@"
