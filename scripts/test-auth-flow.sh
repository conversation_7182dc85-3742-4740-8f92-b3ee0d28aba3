#!/bin/bash

# Test script to diagnose authentication flow issues
# This script tests login and subsequent authenticated requests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ADMIN_SERVICE_URL="http://localhost:8086"
USERNAME="admin"
PASSWORD="admin123"

echo -e "${BLUE}🔍 Testing Authentication Flow${NC}"
echo "=================================="

# Function to make authenticated request
test_authenticated_request() {
    local endpoint=$1
    local token=$2
    local description=$3
    
    echo -e "\n${YELLOW}Testing: ${description}${NC}"
    echo "Endpoint: ${endpoint}"
    echo "Token: ${token:0:20}..."
    
    response=$(curl -s -w "\n%{http_code}" \
        -H "Authorization: Bearer ${token}" \
        -H "Content-Type: application/json" \
        "${ADMIN_SERVICE_URL}${endpoint}")
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    echo "HTTP Status: ${http_code}"
    
    if [[ $http_code == "200" ]]; then
        echo -e "${GREEN}✅ Success${NC}"
        echo "Response: ${body}" | jq . 2>/dev/null || echo "Response: ${body}"
        return 0
    else
        echo -e "${RED}❌ Failed${NC}"
        echo "Response: ${body}"
        return 1
    fi
}

# Step 1: Test login
echo -e "\n${YELLOW}Step 1: Testing Login${NC}"
echo "Username: ${USERNAME}"
echo "Password: ${PASSWORD}"

login_response=$(curl -s -w "\n%{http_code}" \
    -X POST \
    -H "Content-Type: application/json" \
    -d "{\"username\":\"${USERNAME}\",\"password\":\"${PASSWORD}\"}" \
    "${ADMIN_SERVICE_URL}/api/v1/auth/login")

login_http_code=$(echo "$login_response" | tail -n1)
login_body=$(echo "$login_response" | head -n -1)

echo "Login HTTP Status: ${login_http_code}"

if [[ $login_http_code == "200" ]]; then
    echo -e "${GREEN}✅ Login Successful${NC}"
    
    # Extract token from response
    access_token=$(echo "$login_body" | jq -r '.token // .accessToken // .access_token // empty' 2>/dev/null)
    
    if [[ -z "$access_token" || "$access_token" == "null" ]]; then
        echo -e "${RED}❌ No access token found in login response${NC}"
        echo "Login response: ${login_body}"
        exit 1
    fi
    
    echo "Access Token: ${access_token:0:50}..."
    
    # Step 2: Test authenticated requests
    echo -e "\n${YELLOW}Step 2: Testing Authenticated Requests${NC}"
    
    # Test projects endpoint (the one causing issues)
    test_authenticated_request "/api/v1/projects" "$access_token" "Get Projects"
    
    # Test users endpoint
    test_authenticated_request "/api/v1/users" "$access_token" "Get Users"
    
    # Test health endpoint (should work without auth)
    echo -e "\n${YELLOW}Step 3: Testing Health Endpoint (No Auth)${NC}"
    health_response=$(curl -s -w "\n%{http_code}" "${ADMIN_SERVICE_URL}/health")
    health_http_code=$(echo "$health_response" | tail -n1)
    
    if [[ $health_http_code == "200" ]]; then
        echo -e "${GREEN}✅ Health endpoint working${NC}"
    else
        echo -e "${RED}❌ Health endpoint failed${NC}"
    fi
    
else
    echo -e "${RED}❌ Login Failed${NC}"
    echo "Login response: ${login_body}"
    
    # Check if admin service is running
    echo -e "\n${YELLOW}Checking if admin service is running...${NC}"
    if curl -s "${ADMIN_SERVICE_URL}/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Admin service is running${NC}"
        echo -e "${RED}❌ But login failed - check credentials or auth configuration${NC}"
    else
        echo -e "${RED}❌ Admin service is not running or not accessible${NC}"
        echo "Please start the admin service: cd backend/admin-service && go run ."
    fi
    exit 1
fi

echo -e "\n${BLUE}🎯 Authentication Flow Test Complete${NC}"
