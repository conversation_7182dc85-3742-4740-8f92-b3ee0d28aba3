#!/bin/bash

# Test script to verify monitoring integration across all services
# This script tests that all services have the required monitoring endpoints

set -e

echo "🔍 Testing Monitoring Integration Across All Services"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Service configurations
declare -A SERVICES=(
    ["admin-service"]="8086"
    ["deployment-service"]="8080"
    ["scheduling-service"]="8081"
    ["integration-service"]="8083"
    ["notification-service"]="8082"
    ["audit-service"]="8084"
)

# Function to test if a service endpoint responds
test_endpoint() {
    local service_name=$1
    local port=$2
    local endpoint=$3
    local expected_status=${4:-200}
    
    echo -n "  Testing ${endpoint}... "
    
    if curl -s -o /dev/null -w "%{http_code}" "http://localhost:${port}${endpoint}" | grep -q "${expected_status}"; then
        echo -e "${GREEN}✅ OK${NC}"
        return 0
    else
        echo -e "${RED}❌ FAILED${NC}"
        return 1
    fi
}

# Function to test service monitoring endpoints
test_service_monitoring() {
    local service_name=$1
    local port=$2
    
    echo -e "\n📊 Testing ${YELLOW}${service_name}${NC} (port ${port})"
    echo "----------------------------------------"
    
    local failed=0
    
    # Test health endpoint
    test_endpoint "$service_name" "$port" "/health" || failed=1
    
    # Test readiness endpoint
    test_endpoint "$service_name" "$port" "/health/ready" || failed=1
    
    # Test liveness endpoint
    test_endpoint "$service_name" "$port" "/health/live" || failed=1
    
    # Test metrics endpoint
    test_endpoint "$service_name" "$port" "/metrics" || failed=1
    
    if [ $failed -eq 0 ]; then
        echo -e "  ${GREEN}✅ All monitoring endpoints working${NC}"
    else
        echo -e "  ${RED}❌ Some endpoints failed${NC}"
    fi
    
    return $failed
}

# Function to check if service is running
check_service_running() {
    local service_name=$1
    local port=$2
    
    if curl -s -f "http://localhost:${port}/health" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Main testing logic
echo -e "\n🚀 Starting monitoring integration tests..."

total_services=0
running_services=0
passed_services=0

for service_name in "${!SERVICES[@]}"; do
    port=${SERVICES[$service_name]}
    total_services=$((total_services + 1))
    
    echo -e "\n🔍 Checking if ${service_name} is running on port ${port}..."
    
    if check_service_running "$service_name" "$port"; then
        running_services=$((running_services + 1))
        echo -e "${GREEN}✅ Service is running${NC}"
        
        if test_service_monitoring "$service_name" "$port"; then
            passed_services=$((passed_services + 1))
        fi
    else
        echo -e "${YELLOW}⚠️  Service not running (this is expected if not started)${NC}"
        echo "   To start the service, run: cd backend/${service_name} && go run ."
    fi
done

# Summary
echo -e "\n📋 Test Summary"
echo "==============="
echo -e "Total services: ${total_services}"
echo -e "Running services: ${running_services}"
echo -e "Services with working monitoring: ${passed_services}"

if [ $running_services -eq 0 ]; then
    echo -e "\n${YELLOW}ℹ️  No services are currently running.${NC}"
    echo "To test the monitoring integration:"
    echo "1. Start the services you want to test"
    echo "2. Run this script again"
    echo ""
    echo "Example to start admin-service:"
    echo "  cd backend/admin-service && go run ."
elif [ $passed_services -eq $running_services ]; then
    echo -e "\n${GREEN}🎉 All running services have working monitoring integration!${NC}"
else
    echo -e "\n${RED}⚠️  Some running services have monitoring issues.${NC}"
    exit 1
fi

echo -e "\n✨ Monitoring integration test completed!"
