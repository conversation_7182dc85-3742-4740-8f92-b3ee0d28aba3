#!/bin/bash

# Comprehensive test script to verify authentication fixes for all services
# Tests projects, deployments, schedules, and other authenticated endpoints

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
ADMIN_SERVICE_URL="http://localhost:8086"
DEPLOYMENT_SERVICE_URL="http://localhost:8081"
SCHEDULE_SERVICE_URL="http://localhost:8082"
USERNAME="admin"
PASSWORD="admin123"

echo -e "${BLUE}🔍 Testing Authentication Flow for All Services${NC}"
echo "=================================================="

# Function to make authenticated request
test_authenticated_request() {
    local service_name=$1
    local endpoint=$2
    local token=$3
    local description=$4
    
    echo -e "\n${YELLOW}Testing: ${description}${NC}"
    echo "Service: ${service_name}"
    echo "Endpoint: ${endpoint}"
    echo "Token: ${token:0:20}..."
    
    response=$(curl -s -w "\n%{http_code}" \
        -H "Authorization: Bearer ${token}" \
        -H "Content-Type: application/json" \
        "${endpoint}")
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    echo "HTTP Status: ${http_code}"
    
    if [[ $http_code == "200" ]]; then
        echo -e "${GREEN}✅ Success${NC}"
        echo "Response: ${body}" | jq . 2>/dev/null || echo "Response: ${body}"
        return 0
    else
        echo -e "${RED}❌ Failed${NC}"
        echo "Response: ${body}"
        return 1
    fi
}

# Step 1: Test login
echo -e "\n${PURPLE}Step 1: Testing Login${NC}"
echo "Username: ${USERNAME}"
echo "Password: ${PASSWORD}"

login_response=$(curl -s -w "\n%{http_code}" \
    -X POST \
    -H "Content-Type: application/json" \
    -d "{\"username\":\"${USERNAME}\",\"password\":\"${PASSWORD}\"}" \
    "${ADMIN_SERVICE_URL}/api/v1/auth/login")

login_http_code=$(echo "$login_response" | tail -n1)
login_body=$(echo "$login_response" | head -n -1)

echo "Login HTTP Status: ${login_http_code}"

if [[ $login_http_code == "200" ]]; then
    echo -e "${GREEN}✅ Login Successful${NC}"
    
    # Extract token from response
    access_token=$(echo "$login_body" | jq -r '.token // .accessToken // .access_token // empty' 2>/dev/null)
    
    if [[ -z "$access_token" || "$access_token" == "null" ]]; then
        echo -e "${RED}❌ No access token found in login response${NC}"
        echo "Login response: ${login_body}"
        exit 1
    fi
    
    echo "Access Token: ${access_token:0:50}..."
    
    # Step 2: Test all authenticated endpoints
    echo -e "\n${PURPLE}Step 2: Testing All Service Endpoints${NC}"
    
    # Test admin service endpoints
    echo -e "\n${BLUE}--- Admin Service Tests ---${NC}"
    test_authenticated_request "admin-service" "${ADMIN_SERVICE_URL}/api/v1/projects" "$access_token" "Get Projects"
    test_authenticated_request "admin-service" "${ADMIN_SERVICE_URL}/api/v1/users" "$access_token" "Get Users"
    test_authenticated_request "admin-service" "${ADMIN_SERVICE_URL}/api/v1/roles" "$access_token" "Get Roles"
    test_authenticated_request "admin-service" "${ADMIN_SERVICE_URL}/api/v1/groups" "$access_token" "Get Groups"
    
    # Test deployment service endpoints (if running)
    echo -e "\n${BLUE}--- Deployment Service Tests ---${NC}"
    if curl -s "${DEPLOYMENT_SERVICE_URL}/health" > /dev/null 2>&1; then
        test_authenticated_request "deployment-service" "${DEPLOYMENT_SERVICE_URL}/api/v1/deployments" "$access_token" "Get Deployments"
    else
        echo -e "${YELLOW}⚠️  Deployment service not running, skipping tests${NC}"
    fi
    
    # Test schedule service endpoints (if running)
    echo -e "\n${BLUE}--- Schedule Service Tests ---${NC}"
    if curl -s "${SCHEDULE_SERVICE_URL}/health" > /dev/null 2>&1; then
        test_authenticated_request "schedule-service" "${SCHEDULE_SERVICE_URL}/api/v1/schedules" "$access_token" "Get Schedules"
    else
        echo -e "${YELLOW}⚠️  Schedule service not running, skipping tests${NC}"
    fi
    
    # Step 3: Test unauthenticated requests (should fail)
    echo -e "\n${PURPLE}Step 3: Testing Unauthenticated Requests (Should Fail)${NC}"
    
    echo -e "\n${YELLOW}Testing: Unauthenticated Projects Request${NC}"
    unauth_response=$(curl -s -w "\n%{http_code}" \
        -H "Content-Type: application/json" \
        "${ADMIN_SERVICE_URL}/api/v1/projects")
    
    unauth_http_code=$(echo "$unauth_response" | tail -n1)
    
    if [[ $unauth_http_code == "401" ]]; then
        echo -e "${GREEN}✅ Correctly rejected unauthenticated request${NC}"
    else
        echo -e "${RED}❌ Unauthenticated request should have been rejected${NC}"
        echo "HTTP Status: ${unauth_http_code}"
    fi
    
    # Step 4: Test invalid token (should fail)
    echo -e "\n${PURPLE}Step 4: Testing Invalid Token (Should Fail)${NC}"
    
    echo -e "\n${YELLOW}Testing: Invalid Token Request${NC}"
    invalid_response=$(curl -s -w "\n%{http_code}" \
        -H "Authorization: Bearer invalid-token-12345" \
        -H "Content-Type: application/json" \
        "${ADMIN_SERVICE_URL}/api/v1/projects")
    
    invalid_http_code=$(echo "$invalid_response" | tail -n1)
    
    if [[ $invalid_http_code == "401" ]]; then
        echo -e "${GREEN}✅ Correctly rejected invalid token${NC}"
    else
        echo -e "${RED}❌ Invalid token should have been rejected${NC}"
        echo "HTTP Status: ${invalid_http_code}"
    fi
    
    # Step 5: Test health endpoints (should work without auth)
    echo -e "\n${PURPLE}Step 5: Testing Health Endpoints (No Auth Required)${NC}"
    
    services=("admin-service:${ADMIN_SERVICE_URL}" "deployment-service:${DEPLOYMENT_SERVICE_URL}" "schedule-service:${SCHEDULE_SERVICE_URL}")
    
    for service_info in "${services[@]}"; do
        service_name=$(echo "$service_info" | cut -d: -f1)
        service_url=$(echo "$service_info" | cut -d: -f2-)
        
        echo -e "\n${YELLOW}Testing: ${service_name} health endpoint${NC}"
        health_response=$(curl -s -w "\n%{http_code}" "${service_url}/health")
        health_http_code=$(echo "$health_response" | tail -n1)
        
        if [[ $health_http_code == "200" ]]; then
            echo -e "${GREEN}✅ ${service_name} health endpoint working${NC}"
        else
            echo -e "${YELLOW}⚠️  ${service_name} not running or health endpoint failed${NC}"
        fi
    done
    
else
    echo -e "${RED}❌ Login Failed${NC}"
    echo "Login response: ${login_body}"
    
    # Check if admin service is running
    echo -e "\n${YELLOW}Checking if admin service is running...${NC}"
    if curl -s "${ADMIN_SERVICE_URL}/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Admin service is running${NC}"
        echo -e "${RED}❌ But login failed - check credentials or auth configuration${NC}"
    else
        echo -e "${RED}❌ Admin service is not running or not accessible${NC}"
        echo "Please start the admin service: cd backend/admin-service && go run ."
    fi
    exit 1
fi

echo -e "\n${BLUE}🎯 Complete Authentication Flow Test Finished${NC}"
echo -e "${GREEN}✅ All authentication fixes verified successfully!${NC}"
