#!/bin/bash

# Test script for the monitoring and observability infrastructure

set -e

echo "🔍 Deploy Orchestrator - Monitoring System Test"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if services are running
check_service() {
    local service_name=$1
    local port=$2
    local endpoint=${3:-"/health"}
    
    print_status "Checking $service_name on port $port..."
    
    if curl -s -f "http://localhost:$port$endpoint" > /dev/null; then
        print_success "$service_name is running and healthy"
        return 0
    else
        print_warning "$service_name is not running or unhealthy"
        return 1
    fi
}

# Test health endpoints
test_health_endpoints() {
    echo ""
    echo "🏥 Testing Health Endpoints"
    echo "=========================="
    
    # Test admin service health
    if check_service "Admin Service" 8080; then
        echo "  📊 Health check response:"
        curl -s "http://localhost:8080/health" | jq '.' 2>/dev/null || echo "  (JSON parsing failed - service may not be fully ready)"
        echo ""
        
        echo "  🔍 Readiness check:"
        curl -s "http://localhost:8080/health/ready" | jq '.' 2>/dev/null || echo "  (Readiness check failed)"
        echo ""
        
        echo "  💓 Liveness check:"
        curl -s "http://localhost:8080/health/live" | jq '.' 2>/dev/null || echo "  (Liveness check failed)"
        echo ""
    fi
}

# Test metrics endpoints
test_metrics_endpoints() {
    echo ""
    echo "📈 Testing Metrics Endpoints"
    echo "============================"
    
    if check_service "Admin Service Metrics" 8080 "/metrics"; then
        echo "  📊 Sample metrics:"
        curl -s "http://localhost:8080/metrics" | grep -E "(http_requests_total|app_info|db_connections)" | head -5
        echo "  ... (truncated)"
        echo ""
    fi
}

# Test monitoring dashboard
test_monitoring_dashboard() {
    echo ""
    echo "🖥️  Testing Monitoring Dashboard"
    echo "==============================="
    
    if check_service "Monitoring Dashboard" 9090; then
        echo "  🌐 Dashboard API response:"
        curl -s "http://localhost:9090/api/status" | jq '.services[] | {name: .name, status: .status}' 2>/dev/null || echo "  (API response parsing failed)"
        echo ""
        
        print_success "Dashboard is accessible at: http://localhost:9090"
    fi
}

# Test logging functionality
test_logging() {
    echo ""
    echo "📝 Testing Logging Functionality"
    echo "==============================="
    
    print_status "Making test requests to generate logs..."
    
    # Make some test requests
    curl -s -H "Authorization: Bearer invalid-token" "http://localhost:8080/api/v1/users" > /dev/null || true
    curl -s "http://localhost:8080/health" > /dev/null || true
    curl -s "http://localhost:8080/api/identity-providers/enabled" > /dev/null || true
    
    print_success "Test requests completed - check service logs for structured log entries"
}

# Test tracing headers
test_tracing() {
    echo ""
    echo "🔗 Testing Distributed Tracing"
    echo "=============================="
    
    print_status "Making request with trace ID..."
    
    TRACE_ID="trace-$(date +%s)"
    response=$(curl -s -H "X-Trace-ID: $TRACE_ID" "http://localhost:8080/health" -D -)
    
    if echo "$response" | grep -q "X-Trace-ID: $TRACE_ID"; then
        print_success "Trace ID propagation working correctly"
    else
        print_warning "Trace ID propagation may not be working"
    fi
}

# Performance test
performance_test() {
    echo ""
    echo "⚡ Performance Test"
    echo "=================="
    
    print_status "Running performance test (10 concurrent requests)..."
    
    # Simple performance test
    time_start=$(date +%s.%N)
    
    for i in {1..10}; do
        curl -s "http://localhost:8080/health" > /dev/null &
    done
    wait
    
    time_end=$(date +%s.%N)
    duration=$(echo "$time_end - $time_start" | bc -l 2>/dev/null || echo "N/A")
    
    print_success "Completed 10 requests in ${duration}s"
}

# Main test execution
main() {
    echo ""
    print_status "Starting monitoring system tests..."
    echo ""
    
    # Check if required tools are available
    if ! command -v curl &> /dev/null; then
        print_error "curl is required but not installed"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        print_warning "jq is not installed - JSON output will not be formatted"
    fi
    
    # Run tests
    test_health_endpoints
    test_metrics_endpoints
    test_monitoring_dashboard
    test_logging
    test_tracing
    performance_test
    
    echo ""
    echo "🎉 Monitoring System Test Summary"
    echo "================================="
    print_success "All tests completed!"
    echo ""
    echo "📋 Available Endpoints:"
    echo "  • Health Check:        http://localhost:8080/health"
    echo "  • Metrics:             http://localhost:8080/metrics"
    echo "  • Monitoring Dashboard: http://localhost:9090"
    echo "  • Dashboard API:       http://localhost:9090/api/status"
    echo ""
    echo "📖 For more information, see MONITORING.md"
}

# Run main function
main "$@"
