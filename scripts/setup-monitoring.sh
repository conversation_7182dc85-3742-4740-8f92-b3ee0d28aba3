#!/bin/bash

# Setup Monitoring and Alerting for Deploy Orchestrator
# This script sets up the complete monitoring stack

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
MONITORING_DIR="monitoring"
ENVIRONMENT=${ENVIRONMENT:-production}
DOMAIN=${DOMAIN:-deploy-orchestrator.example.com}

print_status "Setting up monitoring and alerting for Deploy Orchestrator"
echo "Environment: $ENVIRONMENT"
echo "Domain: $DOMAIN"
echo "=================================="

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Create monitoring directories
create_directories() {
    print_status "Creating monitoring directories..."
    
    mkdir -p $MONITORING_DIR/{prometheus,grafana,alertmanager,loki,promtail,blackbox,secrets}
    mkdir -p $MONITORING_DIR/grafana/{provisioning/datasources,provisioning/dashboards,dashboards}
    mkdir -p $MONITORING_DIR/alertmanager/templates
    mkdir -p logs/deploy-orchestrator
    
    print_success "Directories created"
}

# Setup secrets
setup_secrets() {
    print_status "Setting up secrets..."
    
    # Create secret files if they don't exist
    if [ ! -f "$MONITORING_DIR/secrets/smtp_password" ]; then
        read -s -p "Enter SMTP password: " smtp_password
        echo "$smtp_password" > "$MONITORING_DIR/secrets/smtp_password"
        chmod 600 "$MONITORING_DIR/secrets/smtp_password"
        echo
    fi
    
    if [ ! -f "$MONITORING_DIR/secrets/slack_webhook_url" ]; then
        read -p "Enter Slack webhook URL: " slack_webhook
        echo "$slack_webhook" > "$MONITORING_DIR/secrets/slack_webhook_url"
        chmod 600 "$MONITORING_DIR/secrets/slack_webhook_url"
    fi
    
    if [ ! -f "$MONITORING_DIR/secrets/pagerduty_service_key" ]; then
        read -p "Enter PagerDuty service key (optional): " pagerduty_key
        if [ ! -z "$pagerduty_key" ]; then
            echo "$pagerduty_key" > "$MONITORING_DIR/secrets/pagerduty_service_key"
            chmod 600 "$MONITORING_DIR/secrets/pagerduty_service_key"
        fi
    fi
    
    print_success "Secrets configured"
}

# Setup Grafana datasources
setup_grafana_datasources() {
    print_status "Setting up Grafana datasources..."
    
    cat > "$MONITORING_DIR/grafana/provisioning/datasources/datasources.yml" << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true

  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    editable: true

  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    editable: true
EOF

    print_success "Grafana datasources configured"
}

# Setup Grafana dashboard provisioning
setup_grafana_dashboards() {
    print_status "Setting up Grafana dashboard provisioning..."
    
    cat > "$MONITORING_DIR/grafana/provisioning/dashboards/dashboards.yml" << EOF
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards
EOF

    print_success "Grafana dashboard provisioning configured"
}

# Setup Loki configuration
setup_loki_config() {
    print_status "Setting up Loki configuration..."
    
    cat > "$MONITORING_DIR/loki/loki.yml" << EOF
auth_enabled: false

server:
  http_listen_port: 3100
  grpc_listen_port: 9096

common:
  path_prefix: /loki
  storage:
    filesystem:
      chunks_directory: /loki/chunks
      rules_directory: /loki/rules
  replication_factor: 1
  ring:
    instance_addr: 127.0.0.1
    kvstore:
      store: inmemory

query_range:
  results_cache:
    cache:
      embedded_cache:
        enabled: true
        max_size_mb: 100

schema_config:
  configs:
    - from: 2020-10-24
      store: boltdb-shipper
      object_store: filesystem
      schema: v11
      index:
        prefix: index_
        period: 24h

ruler:
  alertmanager_url: http://alertmanager:9093

limits_config:
  reject_old_samples: true
  reject_old_samples_max_age: 168h

chunk_store_config:
  max_look_back_period: 0s

table_manager:
  retention_deletes_enabled: false
  retention_period: 0s

compactor:
  working_directory: /loki/boltdb-shipper-compactor
  shared_store: filesystem
EOF

    print_success "Loki configuration created"
}

# Setup Promtail configuration
setup_promtail_config() {
    print_status "Setting up Promtail configuration..."
    
    cat > "$MONITORING_DIR/promtail/promtail.yml" << EOF
server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: containers
    static_configs:
      - targets:
          - localhost
        labels:
          job: containerlogs
          __path__: /var/lib/docker/containers/*/*log

    pipeline_stages:
      - json:
          expressions:
            output: log
            stream: stream
            attrs:
      - json:
          expressions:
            tag:
          source: attrs
      - regex:
          expression: (?P<container_name>(?:[^|]*))\|
          source: tag
      - timestamp:
          format: RFC3339Nano
          source: time
      - labels:
          stream:
          container_name:
      - output:
          source: output

  - job_name: deploy-orchestrator-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: deploy-orchestrator
          __path__: /var/log/deploy-orchestrator/*.log

    pipeline_stages:
      - json:
          expressions:
            level: level
            message: msg
            timestamp: ts
            service: service
      - timestamp:
          format: Unix
          source: timestamp
      - labels:
          level:
          service:
EOF

    print_success "Promtail configuration created"
}

# Setup Blackbox Exporter configuration
setup_blackbox_config() {
    print_status "Setting up Blackbox Exporter configuration..."
    
    cat > "$MONITORING_DIR/blackbox/blackbox.yml" << EOF
modules:
  http_2xx:
    prober: http
    timeout: 5s
    http:
      valid_http_versions: ["HTTP/1.1", "HTTP/2.0"]
      valid_status_codes: []
      method: GET
      headers:
        Host: $DOMAIN
        Accept-Language: en-US
      no_follow_redirects: false
      fail_if_ssl: false
      fail_if_not_ssl: false
      tls_config:
        insecure_skip_verify: false
      preferred_ip_protocol: "ip4"

  http_post_2xx:
    prober: http
    timeout: 5s
    http:
      method: POST
      headers:
        Content-Type: application/json
      body: '{}'

  tcp_connect:
    prober: tcp
    timeout: 5s

  pop3s_banner:
    prober: tcp
    timeout: 5s
    tcp:
      query_response:
        - expect: "^+OK"
      tls: true
      tls_config:
        insecure_skip_verify: false

  ssh_banner:
    prober: tcp
    timeout: 5s
    tcp:
      query_response:
        - expect: "^SSH-2.0-"

  irc_banner:
    prober: tcp
    timeout: 5s
    tcp:
      query_response:
        - send: "NICK prober"
        - send: "USER prober prober prober :prober"
        - expect: "PING :([^ ]+)"
          send: "PONG :\${1}"
        - expect: "^:[^ ]+ 001"

  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: "ip4"
      source_ip_address: "127.0.0.1"
EOF

    print_success "Blackbox Exporter configuration created"
}

# Setup recording rules
setup_recording_rules() {
    print_status "Setting up Prometheus recording rules..."
    
    cat > "$MONITORING_DIR/prometheus/recording_rules.yml" << EOF
groups:
  - name: secret_mapping_recording_rules
    interval: 30s
    rules:
      - record: secret_retrieval_rate_5m
        expr: rate(secret_retrieval_total[5m])
      
      - record: secret_retrieval_success_rate_5m
        expr: rate(secret_retrieval_total{status="success"}[5m]) / rate(secret_retrieval_total[5m])
      
      - record: secret_mapping_error_rate_5m
        expr: rate(secret_mapping_errors_total[5m])
      
      - record: workflow_secret_injection_rate_5m
        expr: rate(workflow_secret_injections_total[5m])
      
      - record: unauthorized_access_rate_1h
        expr: increase(unauthorized_secret_access_attempts_total[1h])

  - name: service_recording_rules
    interval: 30s
    rules:
      - record: http_request_rate_5m
        expr: rate(http_requests_total[5m])
      
      - record: http_error_rate_5m
        expr: rate(http_requests_total{status_code=~"5.."}[5m]) / rate(http_requests_total[5m])
      
      - record: http_request_duration_p95_5m
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))
EOF

    print_success "Recording rules created"
}

# Start monitoring stack
start_monitoring_stack() {
    print_status "Starting monitoring stack..."
    
    cd $MONITORING_DIR
    docker-compose up -d
    cd ..
    
    print_success "Monitoring stack started"
}

# Verify monitoring stack
verify_monitoring_stack() {
    print_status "Verifying monitoring stack..."
    
    # Wait for services to start
    sleep 30
    
    # Check Prometheus
    if curl -s http://localhost:9090/-/healthy > /dev/null; then
        print_success "Prometheus is healthy"
    else
        print_error "Prometheus is not responding"
    fi
    
    # Check Grafana
    if curl -s http://localhost:3000/api/health > /dev/null; then
        print_success "Grafana is healthy"
    else
        print_error "Grafana is not responding"
    fi
    
    # Check Alertmanager
    if curl -s http://localhost:9093/-/healthy > /dev/null; then
        print_success "Alertmanager is healthy"
    else
        print_error "Alertmanager is not responding"
    fi
}

# Display access information
display_access_info() {
    print_status "Monitoring stack is ready!"
    echo ""
    echo "Access URLs:"
    echo "  Prometheus: http://localhost:9090"
    echo "  Grafana: http://localhost:3000 (admin/admin123)"
    echo "  Alertmanager: http://localhost:9093"
    echo ""
    echo "Log files:"
    echo "  Application logs: logs/deploy-orchestrator/"
    echo "  Secret audit logs: logs/deploy-orchestrator/secret-audit.log"
    echo ""
    echo "Next steps:"
    echo "  1. Import Grafana dashboards"
    echo "  2. Configure notification channels"
    echo "  3. Test alerting rules"
    echo "  4. Set up log rotation"
}

# Main execution
main() {
    check_prerequisites
    create_directories
    setup_secrets
    setup_grafana_datasources
    setup_grafana_dashboards
    setup_loki_config
    setup_promtail_config
    setup_blackbox_config
    setup_recording_rules
    start_monitoring_stack
    verify_monitoring_stack
    display_access_info
}

# Run main function
main "$@"
